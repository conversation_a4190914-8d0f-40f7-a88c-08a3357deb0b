package com.reon.ehr.api.sys.dubbo.service.rpc.file;

import com.reon.hr.api.file.exception.FileSystemException;

/**
 * <AUTHOR> on 2023/7/26.
 */
public interface IEpFileSystemWrapperService {
    /**
     * 文件上传
     * @param bytes 文件字节数组
     * @param fileExtName 文件名 eg: xxx.jpg,xxx.txt,xxx.doc
     * @return 文件系统ID
     * @throws FileSystemException
     */
    public String uploadFile(byte[] bytes,String fileExtName)throws FileSystemException;
    /**
     * 删除文件
     * @param fileId 文件ID
     * @return true:删除成功，false:删除失败
     * @throws FileSystemException
     */
    public boolean deleteFile(String fileId)throws FileSystemException;

    /**
     * 根据文件系统ID获取原文件名
     * @param fileId 文件系统ID
     * @return 原文件名
     * @throws FileSystemException
     */
    String getOriginalFilename(String fileId)throws FileSystemException;
}
