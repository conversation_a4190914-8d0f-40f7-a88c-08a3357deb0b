var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
         var layer = parent.layer === undefined ? layui.layer : parent.layer;

         // 用于判断是哪个 支付查询  socialPaymentApplyQuery 和 paymentApplyQuery
         var type;

    //查出属于哪个公司 TODO(未完成)
    $(document).ready(function () {
        // $.ajax({
        //     type:"GET",
        //     url:ctx+"/bill/paymentApply/findOrgById",
        //     dataType:'json',
        //     param:"",
        //     success:function (data) {
        //         org = data.data;
        //     }
        // });
        getCityAndOrgCodeMapByLoginName()
        type = $('#type').val();
        // 两种请求 区分

        var payComList = [];
        $.ajax({
            type:"GET",
            url:ctx + "/customer/contract/orgList",
            dataType:'json',
            success: function(data){
                payComList = [];
                payComList =data.data;
                // $.each(payComList,function(i,item){
                //     $("#orgCode").append($("<option/>").text(item.orgName).attr("value",item.orgCode));
                // });
                form.render('select');
            },
            error: function(data){
                console.log("error");
            }
        });
    });


    /*监听document的回车操作*/
    $(document).bind('keypress', function (event) {
        if (event.keyCode === 13) {
            reloadTable();
        }
    });

    //日期范围
    var startApplyTime = laydate.render({
        elem: '#startApplyTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endApplyTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endApplyTime = laydate.render({
        elem: '#endApplyTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startApplyTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    //日期范围
    var startPassTime = laydate.render({
        elem: '#startPassTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endPassTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endPassTime = laydate.render({
        elem: '#endPassTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startPassTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });





    //日期点击事件
    var initYear;
    laydate.render({
        elem: '#payMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#payMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });


    form.on('submit(btnQuery)', function (data) {
        type = $("#type").val();
        if (type == "shebao"){
            // shebao为社保查询  仅为sociaPaymentApplyQuery.jsp 使用
            // data.field['payCom'] = $('#orgCode').val();
            // delete data.field.orgCode;
            table.reload('paymentApplyGrid', {
                where: data.field,
                page: {curr: 1} //重新从第 1 页开始
            });
            return ;
        }
        table.reload('paymentApplyGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    //查询支付管理数据
    function getPaymentApplyInfo(){
    table.render({
        id: 'paymentApplyGrid',
        elem: '#paymentApplyQueryGrid',
        url: ML.contextPath + '/bill/paymentApply/getPaymentApplyListPage',
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: '600',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%',fixed: 'left'},
            {field: 'appStatus', title: '审批状态', width: '15%', align: 'center',fixed: 'left',templet:function(d){
                if(d.appStatus){
                return ML.dictFormatter('APPLY_APPR_STATUS',d.appStatus);
                }else{return "无数据";}
                }},
            // {field: 'payKind', title: '支付大类', width: '15%', align: 'center',templet: function (d) {
            //     if(d.payKind){
            //         return ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", d.payKind);
            //     }else{
            //         return "无数据";
            //     }}},
            {
                field: 'payType', title: '支付类型', width: '15%', align: 'center', templet: function (d) {
                if(d.payType){
                return ML.dictFormatter("PAYMENT_SUBCLASS",d.payType);
                }else{
                    return "无数据";
                }}},
            {field: 'payMethod', title: '支付方式', width: '7%', align: 'center',templet:function(d){
                    if(d.payMethod){
                    return ML.dictFormatter('MODE_OF_PAYMENT',d.payMethod);
                    }else{
                     return "无数据"}}},
            {field: 'payee', title: '收款方', width: '15%', align: 'center'},
            {field: 'bankType', title: '收款银行', width: '10%', align: 'center',templet:function(d){
                    if(d.bankType){
                    return ML.dictFormatter('BANK',d.bankType);
                    }else{return "无数据"}}},
            {field: 'bankNo', title: '银行账号/支票号', width: '10%', align: 'center'},
            {field: 'depoist', title: '开户银行', width: '10%', align: 'center'},
            {field: 'payAmt', title: '应付金额', width: '10%', align: 'center'},
            {field: 'applyAmt', title: '申请金额', width: '10%', align: 'center'},
            {field: 'payMonth', title: '支付所属年月', width: '10%', align: 'center'},
            {field: 'lastDate', title: '最晚支付时间', width: '10%', align: 'center'},
            {field: 'applicant', title: '申请人', width: '10%', align: 'center',templet:function (d) {
                    if(d.applicant){
                        return ML.loginNameFormater(d.applicant)
                    }else{
                        return "无数据"

                    }
                }},
            {field: 'applyTime', title: '申请时间', width: '10%', align: 'center'},
            {field: 'appComName', title: '支付地', width: '10%', align: 'center'},
            // 福利办理方 就是 支付抬头 公司字段
            {field: 'payCom', title: '福利办理方', width: '10%', align: 'center'},
            {field: 'passTime', title: '审批通过时间', width: '10%', align: 'center',fixed: 'right'},
            {field: 'id', type: 'hidden'},
            {field: 'fileId', type: 'hidden'},
            {field: 'fileName', type: 'hidden'},
            // {field: 'appCom', title: '申请人分公司', width: '10%', align: 'center'}
        ]],
        done: function (res) {
            ML.hideNoAuth();
            $("[data-field='id']").css('display','none');
            $("[data-field='fileId']").css('display','none');
            $("[data-field='fileName']").css('display','none');
            $("[data-field='appCom']").css('display','none');
            for (var i = 0; i < res.data.length; i++) {
                if (res.data[i].appStatus == 1) {
                    $("tr").eq(i + 1).css("color", "red");
                    $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                }
            }
        }
    });
    }

    //查询社保支付数据
    function getSocialPaymentApplyInfo(params){
        table.render({
            id: 'paymentApplyGrid',
            elem: '#paymentApplyQueryGrid',
            url: ML.contextPath + '/bill/paymentApply/getSocialPaymentApplyListPage',
            where: params,
            method: 'get',
            page: true, //默认为不开启
            limits: [50, 100, 200],
            defaultToolbar: [],
            height: '600',
            toolbar: '#toolbarDemo',
            limit: 50,
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'checkbox', width: '3%',fixed: 'left'},
                {field: 'appStatus', title: '审批状态', width: '15%', align: 'center',fixed: 'left',templet:function(d){
                        if(d.appStatus){
                            return ML.dictFormatter('APPLY_APPR_STATUS',d.appStatus);
                        }else{return "无数据";}
                    }},
                // {field: 'payKind', title: '支付大类', width: '15%', align: 'center',templet: function (d) {
                //     if(d.payKind){
                //         return ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", d.payKind);
                //     }else{
                //         return "无数据";
                //     }}},
                {field: 'packName', title: '福利包名称', width: '10%', align: 'center'},
                {
                    field: 'payType', title: '支付类型', width: '15%', align: 'center', templet: function (d) {
                        if(d.payType){
                            // return ML.dictFormatter("PAYMENT_SUBCLASS",d.payType);
                            //实做中的支付类型被写死，字典表中有多个地方使用，不能直接修改字典name
                            return "社保公积金";
                        }else{
                            return "无数据";
                        }}},
                {field: 'payMethod', title: '支付方式', width: '7%', align: 'center',templet:function(d){
                        if(d.payMethod){
                            return ML.dictFormatter('MODE_OF_PAYMENT',d.payMethod);
                        }else{
                            return "无数据"}}},
                {field: 'payee', title: '收款方', width: '15%', align: 'center'},
                {field: 'bankType', title: '收款银行', width: '10%', align: 'center',templet:function(d){
                        if(d.bankType){
                            return ML.dictFormatter('BANK',d.bankType);
                        }else{return "无数据"}}},
                {field: 'bankNo', title: '银行账号/支票号', width: '10%', align: 'center'},
                {field: 'depoist', title: '开户银行', width: '10%', align: 'center'},
                {field: 'payAmt', title: '应付金额', width: '10%', align: 'center'},
                {field: 'applyAmt', title: '申请金额', width: '10%', align: 'center'},
                {field: 'payMonth', title: '支付所属年月', width: '10%', align: 'center'},
                {field: 'lastDate', title: '最晚支付时间', width: '10%', align: 'center'},
                {field: 'applicant', title: '申请人', width: '10%', align: 'center',templet:function (d) {
                        if(d.applicant){
                            return ML.loginNameFormater(d.applicant)
                        }else{
                            return "无数据"

                        }
                    }},
                {field: 'applyTime', title: '申请时间', width: '10%', align: 'center'},
                {field: 'appComName', title: '支付地', width: '10%', align: 'center'},
                {field: 'payCom', title: '福利办理方', width: '10%', align: 'center'},
                {field: 'passTime', title: '审批通过时间', width: '10%', align: 'center',fixed: 'right'},
                {field: 'id', type: 'hidden'},
                {field: 'fileId', type: 'hidden'},
                {field: 'fileName', type: 'hidden'},
                // {field: 'appCom', title: '申请人分公司', width: '10%', align: 'center'}
            ]],
            done: function (res) {
                ML.hideNoAuth();
                $("[data-field='id']").css('display','none');
                $("[data-field='fileId']").css('display','none');
                $("[data-field='fileName']").css('display','none');
                $("[data-field='appCom']").css('display','none');
                for (var i = 0; i < res.data.length; i++) {
                    if (res.data[i].appStatus == 1) {
                        $("tr").eq(i + 1).css("color", "red");
                        $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                    }
                }
            }
        });
    }


    //监听支付管理表格上方按钮
    table.on('toolbar(paymentApplyQueryGridFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            //新增
            case 'add':
                open("新增支付管理", "add", ['65%', '82%'], checkStatus.data[0]);
                break;
            //编辑
            case 'edit':
                if (obj.event == 'edit' && checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                // if (checkStatus.data[0].applicant != window.top['currentLoginName']) {
                //     return layer.msg("支付申请只能由创建人自己修改");
                // }
                if (checkStatus.data[0].appStatus == 2 || checkStatus.data[0].appStatus == 5 || checkStatus.data[0].appStatus == 3) {
                    return layer.msg("【审批中、支付完成、终止】状态的支付申请不能修改");
                }
                var loginName = window.top['currentLoginName'];
                var orgCode = checkStatus.data[0].orgCode;
                $.ajax({
                    url: ML.contextPath + '/customer/personOrderQuery/getLaterServiceList?orgCode=' + orgCode ,
                    type: 'GET',
                    dataType: 'json',
                    contentType: 'application/json',
                    success: function (result) {
                        let flag = false;
                        for (let i = 0; i < result.length; i++) {
                            if(result[i].loginName == loginName){
                                flag = true;
                            }
                        }

                        if(!flag){
                            layer.msg("您不是福利办理方下面的后道客服，无法修改！")
                            return
                        }

                        open("编辑支付管理", "edit", ['65%', '85%'], checkStatus.data[0].id);
                    }, error: function (result) {
                        layer.msg("错误!");
                    }
                })
                break;
            //终止
            case 'termination':
                if (obj.event == 'termination' && checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                if (checkStatus.data[0].appStatus == 3 ) {
                    return layer.msg("已经终止的申请不可以再终止");
                }

                if (ML.isEmpty(checkStatus.data[0].orgCode)) {
                    return layer.msg("福利办理方不能为空");
                }
                if (checkStatus.data[0].appStatus == 5 || checkStatus.data[0].appStatus == 2 ) {
                    return layer.msg("【审批中、支付完成】状态的支付申请不能终止");
                }
                var loginName = window.top['currentLoginName'];
                var orgCode = checkStatus.data[0].orgCode;
                // if (checkStatus.data[0].applicant != window.top['currentLoginName']) {
                //     return layer.msg("支付申请只能由创建人自己终止");
                // }
                $.ajax({
                    url: ML.contextPath + '/customer/personOrderQuery/getLaterServiceList?orgCode=' + orgCode ,
                    type: 'GET',
                    dataType: 'json',
                    contentType: 'application/json',
                    success: function (result) {
                        let flag = false;
                        for (let i = 0; i < result.length; i++) {
                            if(result[i].loginName == loginName){
                                flag = true;
                            }
                        }

                        if(!flag){
                            layer.msg("您不是福利办理方下面的后道客服，无法修改！")
                            return
                        }

                        layer.prompt({
                            formType: 2,
                            title: '终止原因',
                            area: ['260px', '100px'] //自定义文本域宽高
                        }, function (value, index, elem) {
                            layer.close(index);
                            var param = {"id":checkStatus.data[0].id,"pid": checkStatus.data[0].pid};
                            if(value){
                                param.overReason = value;
                            }
                            $.ajax({
                                url: ML.contextPath + "/bill/paymentApply/termination",
                                type: 'POST',
                                dataType: 'json',
                                data: JSON.stringify(param),
                                contentType: "application/json;charset=UTF-8",
                                success: function (result) {
                                    layer.close(index);
                                    reloadTable();
                                    layer.msg(result.msg);
                                },
                                error: function (data) {
                                    layer.msg("系统繁忙，请稍后重试!");
                                    // ML.layuiButtonDisabled($('#' + type), 'true');
                                }
                            });
                        });

                    }, error: function (result) {
                        layer.msg("错误!");
                    }
                })
                break;

            case 'addWhiteList':
                addWhiteList();
                break;

        }
    });


    //csType 1:项目(派单)2:接单
    function getServiceList(orgCode,csType,loginName) {
        $.ajax({
            url: ML.contextPath + '/customer/personOrderQuery/getServiceList?orgCode=' + orgCode+'&csType='+csType ,
            type: 'GET',
            dataType: 'json',
            contentType: 'application/json',
            success: function (result) {
                let flag = false;
                for (let i = 0; i < result.length; i++) {
                    if(result[i].loginName == loginName){
                        flag = true;
                    }
                }

            }, error: function (result) {
                layer.msg("错误!");
            }
        })
    }

    function addWhiteList() {
        let area = ['50%', '50%'];
        let url = '/customer/insurancePractice/societyInsurance/gotoAddAppWhiteList?type='+2;
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: "添加白名单",
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + url
            , success: function (layero, index) {
            },
            end: function () {
            }
        });
    }

    // 监听表格行双击事件
    table.on('rowDouble(paymentApplyQueryGridFilter)', function (obj) {
        open("查看支付管理", "check", ['58%', '82%'], obj.data.id);
        return false;
    });





    //打开窗口
    function open(title, optType, area, data) {
        var url;
        if (optType == "add") {
            // if (window.top['userOrgPosPool'].length > 1) {
            //     area = ['380px', '250px'];
            //     url = "/bill/paymentApply/gotoSelectOptView?type=paymentApply";
            //     title = "选择岗位";
            // } else {
            //     url = "/bill/paymentApply/addPaymentApplyPage?posCode=" + window.top['userOrgPosPool'][0].posCode + ',' + window.top['userOrgPosPool'][0].orgCode;
            // }
            url = "/bill/practicePayment/goToSocialSecurityPaymentPage";
            title = "社保支付";
        }
        if (optType == "edit") {
            url = "/bill/paymentApply/editPaymentApplyPage?id=" + data;
        }
        if (optType == "check") {
            url = "/bill/paymentApply/checkPaymentApplyPage?id=" + data;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        });
    }


    //重载数据
    function reloadTable() {
        table.reload('paymentApplyGrid', {
            where: serialize("searchForm"),
        });
    }

    // $("#packName").on('click', function () {
    //     let appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="packName" id="packName" placeholder="福利包名称" autocomplete="off" class="layui-input">' +
    //         '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" id="orgName" placeholder="福利办理方" autocomplete="off" class="layui-input">';
    //     tableSelect.render({
    //         elem: '#packName',
    //         checkedKey: 'id',
    //         appd: appd,
    //         table: {
    //             url: ctx + '/base/insurancePack/getInsurancePackByPage',
    //             cols: [[
    //                 {type: 'radio'}
    //                 , {field: 'packCode', title: '福利包编号', align: 'center'}
    //                 , {field: 'packName', title: '福利包名称', align: 'center'}
    //                 , {field: 'singleFlag', title: '是否单立户', align: 'center',templet: function (d) {
    //                         return getSingleFlagStr(d.singleFlag);
    //                     }}
    //                 , {field: 'custName', title: '客户名称', align: 'center'}
    //                 , {
    //                     field: 'orgName', title: '福利办理方名称', align: 'center'
    //                 }
    //             ]]
    //         },
    //         done: function (elem, data) {
    //             let NEWJSON = [];
    //             let packCode = '';
    //             layui.each(data.data, function (index, item) {
    //                 NEWJSON.push(item.packName);
    //                 packCode = item.packCode;
    //             });
    //             // 回填值
    //             elem.val(NEWJSON.join(","));
    //             $("#packCode").val(packCode);
    //         }
    //     });
    // });


    // function getSingleFlagStr(singleFlag) {
    //     if(singleFlag == 1){
    //         return '大户'
    //     } else if(singleFlag == 2){
    //         return '单立户'
    //     }else{
    //         return singleFlag;
    //     }
    // }


    let orgCodes = [];
    //用于过滤是否单利户
    let packArr = [];
    //用于过滤是否单利户
    let packFilterCust = [];
    let orgCodeAndPackNameListMap = {}
    let orgCodeArray = [];
    function getCityAndOrgCodeMapByLoginName() {
        let statusStr = $('#statusStr').val();
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/insurancePractice/societyInsurance/getOrgCodeAndPackName?statusStr="+statusStr,
            dataType: 'json',
            success: function (data) {
                orgCodeAndPackNameListMap = data.orgCodeAndPackNameListMap;
                orgCodeArray = data.orgVoList;
                if (ML.isNotEmpty(orgCodeArray)) {
                    initOrgCode(orgCodeArray);
                }
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    function initOrgCode(orgCodeArray) {
        $("#payCom").append($("<option/>"));
        $.each(orgCodeArray, function (i, item) {
            $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
            orgCodes.push(item.orgCode)
        });
        if (type == 'shebao'){
            let params = serialize("searchForm");
            // params['payCom'] = params['orgCode'];
            // delete params.orgCode;
            getSocialPaymentApplyInfo(params);

        }else {
            getPaymentApplyInfo();
        }
        initPackName(orgCodeArray[0].orgCode);
        form.render("select")
    }




    form.on("select(payComFilter)", function (data) {
        initPackName(data.value);
    });


    function renderPack(packs) {
        let singleFlag = $('#singleFlag').val();
        $.each(packs, function (i, item) {
            let name = item.packName;
            if(item.status == 2){
                name = name+"（已暂停）";
            }
            if(ML.isNotEmpty(singleFlag)){
                if(item.singleFlag == singleFlag){
                    $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
                }
            }else {
                $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
            }
        });
        form.render("select")
    }

    function initPackName(value) {
        $("#packCode").empty();
        clearCustomerAndSingleFlag();
        for (let orgCode in orgCodeAndPackNameListMap) {
            if (value === orgCode) {
                dataGrop = orgCodeAndPackNameListMap[value];
                packArr = dataGrop;
                packFilterCust = dataGrop;
                $("#packCode").append($("<option/>"));
                renderPack(dataGrop);
            }
        }
        $("#packCode").val('');
        form.render("select")
    }

    form.on("select(singleFlagFilter)",function(data) {
        $("#packCode").find("option:selected").text("");
        $("#packCode").empty();
        let targetPack = packArr.filter(pack => pack.singleFlag+"" === data.value);
        renderPack(targetPack);
        $("#custName").removeAttr("disabled");

    })

    //选择福利包如果是单立户带出客户
    form.on("select(packCodeFilter)", function (data) {
        if (ML.isEmpty(data.value)) {
            clearCustomerAndSingleFlag();
            form.render('select');
            return;
        }
        let singleFlag;
        let findPack = dataGrop.filter(item => item.packCode === data.value);
        if (findPack[0] && findPack[0].singleFlag === 2) {
            $('#custName').val(findPack[0].custName);
            $('#custId').val(findPack[0].custId);
            singleFlag = 2;
        }
        $('#singleFlag').find("option[value='"+singleFlag+"']").prop('selected',true);//选中value="2"的option
        form.render('select');
    })

    function clearCustomerAndSingleFlag() {
        // $('#singleFlag').val("");
        $("#custName").val("");
        $("#custId").val("");
    }






});