#is dev mode
system.devMode=false

elastic.job.zookeeperUrl=192.168.101.236:2184,192.168.101.237:2184,192.168.101.238:2184

dubbo.default.protocol=dubbo
#dubbo.direct.customer.url=dubbo://127.0.0.1:20885
#dubbo.direct.base.url=dubbo://127.0.0.1:20882
#dubbo.direct.admin.url=dubbo://127.0.0.1:20881
#dubbo.direct.file.url=dubbo://127.0.0.1:20883
#dubbo.direct.bill.url=dubbo://127.0.0.1:20886
#dubbo.direct.report.url=dubbo://127.0.0.1:20887
#dubbo.direct.ehr.url=dubbo://127.0.0.1:20889

#dubbo.registry.url=zookeeper://127.0.0.1:2181
#dubbo.registry.url=multicast://*********:1234
dubbo.registry.url=zookeeper://127.0.0.1:2181

#dubbo.rest.port=36004

#dubbo.rest.contextpath=reon-customer-sp

dubbo.default.timeout=5000

#dubbo.rest.servertype=tomcat
#dubbo.rest.threadcount=50
#dubbo.rest.acceptcount=50
#dubbo.rest.protocalextension=com.alibaba.dubbo.rpc.protocol.rest.support.LoggingFilter


#\u90ae\u7bb1\u670d\u52a1
reon.email.username=<EMAIL>
reon.email.password=Ds202409
reon.email.smtpHostName=smtp.exmail.qq.com
