var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        upload = layui.upload,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        element = layui.element,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    //    保存
    form.on('submit(save)', function (data) {
        console.log(data)
        if (uploadIds.length < 1) {
            return layer.msg("请上传文件再提交");
        }

        data.field.fileIdList = uploadIds;
        data.field.signStatus = 3;
        delete data.field.file
        $.ajax({
            url: ML.contextPath + "/customer/laborContract/stopLaborContract",
            type: 'POST',
            dataType: 'json',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(data.field),
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error: function (data) {
                layer.msg("系统保存失败，请稍后重试!");
                ML.layuiButtonDisabled($('#' + type), 'true');
            }
        });

    });

    $(document).on("click", "#close", function () {
        layer.closeAll('iframe')
    });
    //    关闭
    form.on('submit(close)', function () {
        layer.closeAll();
    });


    var uploadIds = [];
    var delFileList = [];
    var fileType = '';
    var fileName = '';
    //上传
    upload.render({
        elem: '#employContractUpload' //绑定元素
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: {contentType: false, processData: false}
        , method: 'POST'
        , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
        , field: 'file'

        , before: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", {icon: 2});
                    return false;
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push( res.data.fileId);
                $('#upload').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag">' +
                    '<a href="' + ML.fileServerUrl + res.data.fileId + '"  target="_blank" id="gethref">' + fileName + '</a>' +
                    '<a href="javascript:void(0)" class="deleteFile"  }" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', {icon: 1});
            }
        }
        , error: function () {
            //请求异常回调
            layer.msg('上传失败', {icon: 5});
        }
    });
    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        var delIndex;
        for (var i = 0; i < uploadIds.length; i++) {
            if (uploadIds[i].fileId == fileId) {
                delIndex = i;
            }
        }
        uploadIds.splice(delIndex, 1);
        if ($("#optType").val() == "edit") {
            delFileList.push(fileId);
        }
        $(this).parent()[0].remove();
    });
// 页面加载函数
    $(document).ready(function (data) {
        console.log(document.getElementById("orderVo").value)
        form.render('select');
    })

});
