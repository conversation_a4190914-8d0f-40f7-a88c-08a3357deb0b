<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.BillCheckDetailMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">

    </resultMap>
    <sql id="Base_Column_List">
        id
        , check_sub_id, bill_detail_id, check_amt, check_val_tax_rate, creator, updater, create_time,
    update_time, del_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_check_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bill_check_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <update id="deleteByIds">
        update bill_check_detail set del_flag = 'Y'
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insert" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">
        insert into bill_check_detail (id, check_sub_id, bill_detail_id,
                                       check_amt, check_val_tax_rate, creator,
                                       updater, create_time, update_time,
                                       del_flag)
        values (#{id,jdbcType=BIGINT}, #{check_sub_id,jdbcType=BIGINT}, #{billDetailId,jdbcType=BIGINT},
                #{checkAmt,jdbcType=DECIMAL}, #{checkValTaxRate,jdbcType=DECIMAL}, #{creator,jdbcType=VARCHAR},
                #{updater,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{delFlag,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">
        insert into bill_check_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="checkSubId != null">
                check_sub_id,
            </if>
            <if test="billDetailId != null">
                bill_detail_id,
            </if>
            <if test="checkAmt != null">
                check_amt,
            </if>
            <if test="checkValTaxRate != null">
                check_val_tax_rate,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="checkSubId != null">
                #{checkSubId,jdbcType=BIGINT},
            </if>
            <if test="billDetailId != null">
                #{billDetailId,jdbcType=BIGINT},
            </if>
            <if test="checkAmt != null">
                #{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkValTaxRate != null">
                #{checkValTaxRate,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">
        update bill_check_detail
        <set>
            <if test="checkSubId != null">
                check_sub_id = #{checkSubId,jdbcType=BIGINT},
            </if>
            <if test="billDetailId != null">
                bill_detail_id = #{billDetailId,jdbcType=BIGINT},
            </if>
            <if test="checkAmt != null">
                check_amt = #{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkValTaxRate != null">
                check_val_tax_rate = #{checkValTaxRate,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">
        update bill_check_detail
        set check_sub_id       = #{checkSubId,jdbcType=BIGINT},
            bill_detail_id     = #{billDetailId,jdbcType=BIGINT},
            check_amt          = #{checkAmt,jdbcType=DECIMAL},
            check_val_tax_rate = #{checkValTaxRate,jdbcType=DECIMAL},
            creator            = #{creator,jdbcType=VARCHAR},
            updater            = #{updater,jdbcType=VARCHAR},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            del_flag           = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="getByCheckId" resultType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from bill_check_detail
        where check_sub_id = #{billSubCheckId}
    </select>
    <select id="selectBySubCheckIds" resultType="com.reon.hr.sp.bill.entity.bill.BillCheckDetail">
        select
        id,
        check_sub_id,
        bill_detail_id,
        ifnull(check_amt,0) check_amt,
        ifnull( check_val_tax_rate,0) check_val_tax_rate,
        creator, updater, create_time,
        update_time, del_flag
        from `reon-billdb`.bill_check_detail
        where del_flag = 'N' and check_sub_id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <insert id="batchInsert" parameterType="com.reon.hr.api.bill.vo.check.BillCheckDetailVo">
        insert into bill_check_detail (id, check_sub_id, bill_detail_id,
        check_amt, check_val_tax_rate, creator)
        values
        <foreach collection="record" item="record" separator=",">
            (#{record.id,jdbcType=BIGINT}, #{record.checkSubId,jdbcType=BIGINT}, #{record.billDetailId,jdbcType=BIGINT},
            #{record.checkAmt,jdbcType=DECIMAL}, #{record.checkValTax,jdbcType=DECIMAL},
            #{record.creator,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="record" item="record">
            update bill_check_detail
            SET
            check_amt = #{record.checkAmt},
            check_val_tax_rate = #{record.checkValTax},
            updater =#{record.updater},
            update_time =now()
            where id=#{record.id};
        </foreach>
    </update>
</mapper>