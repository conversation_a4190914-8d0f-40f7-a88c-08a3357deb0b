<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.salary.ExcessCalculationAmountDetailMapper">
  <sql id="Base_Column_List">
    id, withholding_agent_no,valid_from,valid_to,excess_calculation_amount,excess_calculation_type, creator, create_time,
    updater, update_time, del_flag
  </sql>
    <insert id="insertVoList">
      insert into excess_calculation_amount_detail
      (withholding_agent_no,valid_from,valid_to,excess_calculation_amount,excess_calculation_type,creator)
      values
      <foreach collection="list" item="item" separator=",">
        (#{item.withholdingAgentNo},#{item.validFrom},#{item.validTo},#{item.excessCalculationAmount},
         #{item.excessCalculationType},#{item.creator})
      </foreach>
    </insert>
  <insert id="saveExcessAmountProvidentFund">
    insert into excess_calculation_amount_detail
    (withholding_agent_no,valid_from,valid_to,excess_calculation_amount,excess_calculation_type,creator)
    value
    (
      #{vo.withholdingAgentNo},#{item.validFrom},#{item.validTo},#{item.excessCalculationAmount},#{item.excessCalculationType},#{vo.creator}
    )
  </insert>
  <update id="updateExcessAmountProvidentFund">
    update excess_calculation_amount_detail
    set excess_calculation_amount=#{vo.excessCalculationAmount},valid_from=#{vo.validFrom},valid_to=#{vo.validTo},updater=#{vo.updater}
    where id=#{vo.id}
  </update>
  <update id="updateVoList">
    update excess_calculation_amount_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="valid_from = case" suffix="end,">
        <foreach collection="list" item="item">
          when id = #{item.id} then #{item.validFrom}
        </foreach>
      </trim>
      <trim prefix="valid_to = case" suffix="end,">
        <foreach collection="list" item="item">
          when id = #{item.id} then #{item.validTo}
        </foreach>
      </trim>
      <trim prefix="excess_calculation_amount = case" suffix="end,">
        <foreach collection="list" item="item">
          when id = #{item.id} then #{item.excessCalculationAmount}
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" item="item">
          when id = #{item.id} then #{item.updater}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <delete id="deleteExcessAmountProvidentFund">
    update excess_calculation_amount_detail
    set del_flag='Y',updater=#{loginName}
    where id=#{id}
  </delete>

  <select id="getByWithholdingAgentNoList" resultType="com.reon.hr.api.customer.vo.salary.ExcessCalculationAmountDetailVo">
    select id,withholding_agent_no,valid_from,valid_to,excess_calculation_amount,excess_calculation_type, creator, create_time,
    updater, update_time, del_flag
    from excess_calculation_amount_detail
    where del_flag='N' and withholding_agent_no in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>
</mapper>
