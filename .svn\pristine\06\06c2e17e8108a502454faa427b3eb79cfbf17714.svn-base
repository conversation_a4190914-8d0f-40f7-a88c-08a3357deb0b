package com.reon.hr.sp.customer.service.supplierPractice;

import com.google.common.collect.Lists;
import com.reon.hr.api.customer.vo.employee.OneTimeCharge;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierOneChargeVo;
import com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice.SupplierOneCharge;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplierOneChargeService {
    List<SupplierOneChargeVo> getSupplierOneChargeVosByOrderNo(List<String> orderNos);
    List<SupplierOneChargeVo> getSupplierOneChargeVosByOrderNo(List<String> orderNos,Integer billMonth);
    void deletSupplierOneChargeByOrderNo(String orderNo);

    int insertOneTimeCharge(List<SupplierOneCharge> oneTimeChargVos);
}
