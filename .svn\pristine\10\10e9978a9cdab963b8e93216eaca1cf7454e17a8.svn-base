<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>商保绑定账单模板查询页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="合同编号">合同编号：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="contractNo" name="contractNo" placeholder="请输入" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="合同编号">合同名称：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="contractName" name="contractName" placeholder="请输入" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="客户名称">客户名称：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" id="custName" readonly  lay-filter="custNameFilter"
                           autocomplete="off" class="layui-input" lay-verType="tips"  placeholder="请选择"  style="padding-left: 10px;width: 190px;"/>
                    <input type="text" name="custId" id="custId" style="display: none;"  value=""/>
                </div>
            </div>
            <div class="layui-inline">
                <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                   lay-submit="">查询</a>
            </div>
        </div>
    </form>
</div>

<div class="layui-card-body">
    <table class="layui-hide" id="contractTempletQueryGrid" lay-filter="contractTempletQueryGridFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <input type="button" class="layui-btn layui-btn-sm" id="bindBillTemplate" lay-event="bindBillTemplate"
               authURI="/customer/contractTemplet/gotoBindContractTempletView" value="绑定账单模板">
    </script>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/commInsurOrder/contractTempletQuery.js?v=${publishVersion}"></script>
</body>
</html>
