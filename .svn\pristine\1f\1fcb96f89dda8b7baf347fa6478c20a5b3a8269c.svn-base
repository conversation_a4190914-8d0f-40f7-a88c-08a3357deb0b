package com.reon.hr.api.customer.dubbo.service.rpc.customer;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.ComAcctInfoVo;

import java.util.List;

public interface CusComAcctInfoWrapperService {
    Page<ComAcctInfoVo> getListPage(Integer page,Integer limit,String paramDate);
    ComAcctInfoVo getComAcctInfo(String groupCode,String orgCode,Byte AcctType);
    ComAcctInfoVo getComAcctInfoById(long id);

    Boolean addComAcctInfo(ComAcctInfoVo comAcctInfoVo,String loginName);

    boolean editComAcctInfo(ComAcctInfoVo comAcctInfoVo, String loginName);

    Boolean delList(List<Long> ids,String loginName);

    ComAcctInfoVo getComAcctInfoByOrgCodeAndGroupCode(String orgCode, String groupCode);
}
