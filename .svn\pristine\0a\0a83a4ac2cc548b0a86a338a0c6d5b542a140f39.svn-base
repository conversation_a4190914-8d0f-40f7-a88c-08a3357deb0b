package com.reon.hr.sp.bill.rabbitmq.listener;

import com.google.common.collect.Lists;
import com.reon.hr.api.base.enums.ValidFlagEnum;
import com.reon.hr.api.bill.constant.BillEnum;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IBillInvoiceWrapperService;
import com.reon.hr.api.bill.enums.VirtualRedStatusEnum;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.InvoiceBillVo;
import com.reon.hr.api.bill.vo.PerBillInfoVo;
import com.reon.hr.api.bill.vo.bill.PerCommSupplierCostDeleteVo;
import com.reon.hr.api.bill.vo.perCommerceItemVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.context.MqContext;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ConsumerScopeTypeBill;
import com.reon.hr.rabbitmq.enums.bill.ProducerScopeTypeBill;
import com.reon.hr.sp.bill.dao.bill.BillInvoiceFromMapper;
import com.reon.hr.sp.bill.dao.bill.PerBillInfoMapper;
import com.reon.hr.sp.bill.dao.bill.perCommerceItemMapper;
import com.reon.hr.sp.bill.dao.cus.CommEmpPeriodMapper;
import com.reon.hr.sp.bill.dao.cus.PerCommSupplierCostMapper;
import com.reon.hr.sp.bill.entity.bill.InsuranceBill;
import com.reon.hr.sp.bill.entity.bill.InsuranceBillLog;
import com.reon.hr.sp.bill.entity.cus.CommEmpPeriod;
import com.reon.hr.sp.bill.entity.cus.PerCommSupplierCost;
import com.reon.hr.sp.bill.service.bill.ICommercialInsuranceBillService;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillLogService;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillService;
import com.reon.hr.sp.bill.service.bill.ISalaryInsuranceBillService;
import com.reon.hr.sp.bill.utils.GetTableName;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class CommercialBillGenerateCompletedListener extends AbstractConsumerListener {

	private final static String BILL_QUEUE_NAME = "bill.generate.commercial.queue";

  private final static Logger logger = LoggerFactory.getLogger(CommercialBillGenerateCompletedListener.class);

  @Autowired
  private MqContext mqContext;
  @Autowired
  private IInsuranceBillService insuranceBillService;
  @Autowired
  private IInsuranceBillLogService insuranceBillLogService;
  @Autowired
  private ICommercialInsuranceBillService commercialInsuranceBillService;
  @Autowired
  private ISalaryInsuranceBillService salaryInsuranceBillService;
  @Autowired
  private MqMessageSender mqMessageSender;
  @Autowired
  private perCommerceItemMapper perCommerceItemMapper;
  @Autowired
  private PerCommSupplierCostMapper perCommSupplierCostMapper;
  @Autowired
  private PerBillInfoMapper perBillInfoMapper;
  @Autowired
  private CommEmpPeriodMapper commEmpPeriodMapper;
  @Autowired
  private BillInvoiceFromMapper billInvoiceFromMapper;
  @Autowired
  private IBillInvoiceWrapperService billInvoiceWrapperService;

  private  static final int SERVICE_CHARGE_PRODUCTCODE = 20;// 商保产品code

  @Override
  protected void doWork(String message) {
    logger.info("bill list<id> :{}", message);
    String errInfo = "";
    Integer generateStatus = null;
    List<Long> billIds = Lists.newArrayList();
    List<InsuranceBillVo> firstBillList = new ArrayList<>();
    try {
      //先判断是商保还是普通账单
      Map<String, String> map = (Map<String, String>) JSONObject.fromObject(message);
      billIds = JsonUtil.jsonToList(map.get("billIds"), Long.class);
        logger.info("=======MQ执行商保流程=======");
        List<InsuranceBillVo> billVos = commercialInsuranceBillService.getEmployInformation(billIds,Integer.parseInt(map.get("billMonth")));
        for (InsuranceBillVo billVo : billVos) {
          InsuranceBillLog billLog = new InsuranceBillLog();
          if (billVo != null) {
            BeanUtils.copyProperties(billVo, billLog);
          }
          billLog.setId(null);
          billLog.setBillId(billVo.getId());
          try {
            Integer billMonth = Integer.parseInt(map.get("billMonth"));
            Long custId = Long.parseLong(map.get("custId"));
            String creator=map.get("creator");
            String table = GetTableName.getTableNamePerCommerceItem(billMonth);
            logger.info(table);
            // 删除关联的所有的从表中的数据  //GetTableName.getTableName(billMonth,"COM_ITEM")
            List<perCommerceItemVo> perCommerceItemVos = perCommerceItemMapper.getPerCommerceItemByBillId(billVos.get(0).getId(),table );
            long start = System.currentTimeMillis();
            logger.info("-------开始删除--------"+System.currentTimeMillis());
            List<Long> perCommerceItemVoIdList = perCommerceItemVos.stream().map(perCommerceItemVo::getId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(perCommerceItemVoIdList)){
              perCommSupplierCostMapper.deleteByPrimaryKeyList(perCommerceItemVoIdList, GetTableName.getTableNamePerCommSupplierCost(billMonth));
              perCommerceItemMapper.deleteByPrimaryKeyList(perCommerceItemVoIdList,table);//删除数据
            }
            List<String> perCommerceItemVoOrderNoList = perCommerceItemVos.stream().map(perCommerceItemVo::getOrderNo).distinct().collect(Collectors.toList());
            List<PerBillInfoVo> perBillInfoVoList =new ArrayList<>();
            if(CollectionUtils.isNotEmpty(perCommerceItemVoOrderNoList)){
              perBillInfoVoList=perBillInfoMapper.getByCustIdAndOrderNoList(custId, perCommerceItemVoOrderNoList, SERVICE_CHARGE_PRODUCTCODE);
            }
            Map<String, List<PerBillInfoVo>> perBillInfoVoMap = perBillInfoVoList.stream().collect(Collectors.groupingBy(PerBillInfoVo::getOrderNo));
            List<Long> perBillInfoVoIdList=new ArrayList<>();
            List<CommEmpPeriod> commEmpPeriodSelectList =new ArrayList<>();
            List<PerCommSupplierCost> supplierCosts=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(perCommerceItemVoOrderNoList)){
              commEmpPeriodSelectList=commEmpPeriodMapper.selectByPrimaryKeyList(perCommerceItemVoOrderNoList);
              Map<Integer,List<String>> currentMonthMap=new HashMap<>();
              for (CommEmpPeriod commEmpPeriodSelect:commEmpPeriodSelectList) {
                if(commEmpPeriodSelect !=null &&Objects.equals(commEmpPeriodSelect.getCurrentMonth(),billMonth)){
                  int count = DateUtil.getStartMonthAndEndMonthSub(commEmpPeriodSelect.getFirstMonth(),commEmpPeriodSelect.getNextMonth());
                  Integer currentMonth = DateUtil.getYearMonthByCount(commEmpPeriodSelect.getCurrentMonth(), -count);//负数
                  if(currentMonth<=commEmpPeriodSelect.getFirstMonth()){
                    currentMonth =commEmpPeriodSelect.getFirstMonth();
                  }
                  if(currentMonthMap.containsKey(currentMonth)){
                    List<String> orderNoList = currentMonthMap.get(currentMonth);
                    orderNoList.add(commEmpPeriodSelect.getOrderNo());
                    currentMonthMap.put(currentMonth, orderNoList);
                  }else {
                    List<String> orderNoList = new ArrayList<>();
                    orderNoList.add(commEmpPeriodSelect.getOrderNo());
                    currentMonthMap.put(currentMonth, orderNoList);
                  }
                }
              }
              for (Integer key:currentMonthMap.keySet()) {
                List<String> orderNoList = currentMonthMap.get(key);
                supplierCosts.addAll(perCommSupplierCostMapper.selectSupplerCancleByOrderNoList(orderNoList,GetTableName.getTableNamePerCommerceItem(key),GetTableName.getTableNamePerCommSupplierCost(key)));
              }
            }
            Map<String, List<PerCommSupplierCost>> supplierCostListMap = supplierCosts.stream().collect(Collectors.groupingBy(PerCommSupplierCost::getOrderNo));
            Map<String, CommEmpPeriod> commEmpPeriodSelectMap = commEmpPeriodSelectList.stream().collect(Collectors.toMap(CommEmpPeriod::getOrderNo, Function.identity(), (key1, key2) -> key2));
            List<CommEmpPeriod> updateCommEmpPeriodList=new ArrayList<>();
            List<PerCommSupplierCostDeleteVo> perCommSupplierCostDeleteVoList=new ArrayList<>();
            List<PerCommSupplierCost> updatePerCommSupplierCostList=new ArrayList<>();
            for (perCommerceItemVo perCommerceItemVo : perCommerceItemVos) {
              List<PerBillInfoVo> perBillInfos = perBillInfoVoMap.get(perCommerceItemVo.getOrderNo());
              boolean isFirst =false;
              if (CollectionUtils.isNotEmpty(perBillInfos)) {
                PerBillInfoVo perBillInfoVo = perBillInfos.get(0);
                if(Objects.equals(perBillInfoVo.getFirstBillMonth(),billMonth)) {
                  perBillInfoVoIdList.add(perBillInfoVo.getId());
                  isFirst =true;
                }
              }
              //logger.info("---------------------供应商成本回退开始------------------------------");
              //恢复账单周期，和判断生成当月信息是不是有供应商成本回写，如果有，把他撤回
              if(!isFirst&&Objects.equals(perCommerceItemVo.getItemType(),BillEnum.itemType.PAY_TYPE.getCode())) {//正常账单才进行会写
                cancleEmpBill(perCommerceItemVo, billMonth, creator,commEmpPeriodSelectMap,updateCommEmpPeriodList,perCommSupplierCostDeleteVoList,updatePerCommSupplierCostList,supplierCostListMap);
              }
              //logger.info("---------------------供应商成本回退结束------------------------------");
            }
            if(CollectionUtils.isNotEmpty(perBillInfoVoIdList)){
              perBillInfoMapper.deleteByPrimaryKeyList(perBillInfoVoIdList);
            }
//            if(CollectionUtils.isNotEmpty(updateCommEmpPeriodList)){
//              commEmpPeriodMapper.updateByPrimaryKeySelectiveList(updateCommEmpPeriodList);
//            }
            if(CollectionUtils.isNotEmpty(perCommSupplierCostDeleteVoList)){
              perCommSupplierCostMapper.deleteByIdKeyList(perCommSupplierCostDeleteVoList);
            }
            if(CollectionUtils.isNotEmpty(updatePerCommSupplierCostList)){
              perCommSupplierCostMapper.updateByPrimaryKeyList(updatePerCommSupplierCostList);
            }
            long end = System.currentTimeMillis();
            logger.info("-------删除结束--------"+System.currentTimeMillis());
            logger.info("-------删除时间--------"+(start-end));
            //删除首版账单表数据

            commercialInsuranceBillService.savePerCommercialInsurance(billVo,custId);
            generateStatus = BillEnum.GenerateBillStatus.SUCCESSED.getCode();
            List<InsuranceBillVo> existBill = insuranceBillService.getByContractNoAndLessThanBillMonth(billVo.getContractNo(), billVo.getBillMonth());
            if (CollectionUtils.isEmpty(existBill)){
              InsuranceBillVo firstbillVo = new InsuranceBillVo();
              firstbillVo.setContractNo(billVo.getContractNo());
              firstbillVo.setBillMonth(billVo.getBillMonth());
              firstBillList.add(firstbillVo);
            }
          } catch (Exception e) {
            e.printStackTrace();
            errInfo = e.getMessage();
            generateStatus = BillEnum.GenerateBillStatus.FAILED.getCode();
            billLog.setReceiveAmt(BigDecimal.ZERO);
            billLog.setGenStatus(generateStatus);
          } finally {
            // 新增日志表
            billLog.setErrorInfo(errInfo);
            InsuranceBillVo insuranceBillVo = insuranceBillService.getOneById(billVo.getId());
            InsuranceBill insuranceBill = new InsuranceBill();
            insuranceBill.setId(billVo.getId());
            insuranceBill.setGenStatus(generateStatus);
            if (generateStatus == BillEnum.GenerateBillStatus.SUCCESSED.getCode()) {
              billLog.setReceiveAmt(insuranceBillVo.getReceiveAmt());
            } else {
              insuranceBill.setReceiveAmt(BigDecimal.ZERO);
              insuranceBill.setEmployeeNum(0);
            }
            billLog.setGenStatus(generateStatus);
            insuranceBillLogService.insertSelective(billLog);
            List<InvoiceBillVo> virtualRedStatusList = billInvoiceFromMapper.getInvoiceVoByBillId(billVo.getId());
            if (virtualRedStatusList.size() == 1) {
              InvoiceBillVo invoiceBillVo = virtualRedStatusList.get(0);
              if (invoiceBillVo.getVirtualRedStatus() == VirtualRedStatusEnum.PREPARE.getCode() || invoiceBillVo.getVirtualRedStatus() == VirtualRedStatusEnum.REGENERATE.getCode()) {
                billInvoiceWrapperService.updateInvoiceGenFlag(Long.valueOf(invoiceBillVo.getId()), ValidFlagEnum.VALID.getCode());
              }
            }
            // 更改账单表中的生成状态
            insuranceBillService.updateByPrimaryKeySelective(insuranceBill);
          }
        }
    } catch (BeansException e) {
      e.printStackTrace();
      //      logger.error("get message exception :{}", billIdsStr, e);
    } catch (Exception e) {
      for (Long billId : billIds) {

        InsuranceBillVo billVo = insuranceBillService.getOneById(billId);
        InsuranceBill insuranceBill = new InsuranceBill();
        insuranceBill.setGenStatus(BillEnum.GenerateBillStatus.FAILED.getCode());
        insuranceBill.setId(billId);
        insuranceBill.setEmployeeNum(0);
        insuranceBill.setReceiveAmt(BigDecimal.ZERO);
        // 修改账单表的状态等
        insuranceBillService.updateByPrimaryKeySelective(insuranceBill);
        InsuranceBillLog billLog = new InsuranceBillLog();
        if (billVo != null) {
          BeanUtils.copyProperties(billVo, billLog);
        }
        billLog.setId(null);
        billLog.setBillId(billId);
        billLog.setGenStatus(BillEnum.GenerateBillStatus.FAILED.getCode());
        billLog.setReceiveAmt(BigDecimal.ZERO);
        billLog.setErrorInfo(e.getMessage());
        // 插入bill_log表
        insuranceBillLogService.insertSelective(billLog);
      }
      //e.printStackTrace();

      logger.error("date format exception :{}", message, e);
    }
    if (CollectionUtils.isNotEmpty(firstBillList)){
      firstBillList = firstBillList.stream().collect(
              Collectors.collectingAndThen(
                      Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InsuranceBillVo::getContractNo))), ArrayList::new));
      mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeBill.REON_CONTRACT_FIRST_BILL,JsonUtil.beanToJson(firstBillList));
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    super.init(ModuleType.REON_BILL, ConsumerScopeTypeBill.REON_BILL_GENERATED_COMMERCIAL_COMPLETED, mqContext, BILL_QUEUE_NAME);
  }
  /*** 重复生成账单，把账单信息还原*/
  private void cancleEmpBill(perCommerceItemVo perCommerceItemVo,Integer billMonth,String creator,Map<String, CommEmpPeriod> commEmpPeriodSelectMap
          ,List<CommEmpPeriod> updateCommEmpPeriodList,List<PerCommSupplierCostDeleteVo> perCommSupplierCostDeleteVoList,List<PerCommSupplierCost> updatePerCommSupplierCostList,Map<String, List<PerCommSupplierCost>> supplierCostListMap) {
    CommEmpPeriod commEmpPeriodSelect = commEmpPeriodSelectMap.get(perCommerceItemVo.getOrderNo());
    CommEmpPeriod commEmpPeriod  =new CommEmpPeriod();
    if(commEmpPeriodSelect !=null &&Objects.equals(commEmpPeriodSelect.getCurrentMonth(),billMonth)){
      int count = DateUtil.getStartMonthAndEndMonthSub(commEmpPeriodSelect.getFirstMonth(),commEmpPeriodSelect.getNextMonth());
      Integer currentMonth = DateUtil.getYearMonthByCount(commEmpPeriodSelect.getCurrentMonth(), -count);//负数
      if(currentMonth<=commEmpPeriodSelect.getFirstMonth()){
        currentMonth =commEmpPeriodSelect.getFirstMonth();
      }
      commEmpPeriod.setOrderNo(perCommerceItemVo.getOrderNo());
      commEmpPeriod.setEmpId(commEmpPeriodSelect.getEmpId());
      commEmpPeriod.setFirstMonth(commEmpPeriodSelect.getFirstMonth());
      logger.info("回退的下个出账周期"+commEmpPeriodSelect.getCurrentMonth());
      commEmpPeriod.setNextMonth(commEmpPeriodSelect.getCurrentMonth());
      commEmpPeriod.setCurrentMonth(currentMonth);
      commEmpPeriod.setUpdater(creator);
      commEmpPeriod.setUpdateTime(new Date());
      updateCommEmpPeriodList.add(commEmpPeriod);
      List<PerCommSupplierCost> supplierCosts = supplierCostListMap.get(perCommerceItemVo.getOrderNo());
      Map<Integer, List<PerCommSupplierCost>> supplierCostMap = supplierCosts.stream().collect(Collectors.groupingBy(PerCommSupplierCost::getStatus));
      //判断是否有撤销状态的数据
      if(supplierCostMap.containsKey(BillEnum.SupplierCostStatus.CANCLE_STATUS.getCode())){
        PerCommSupplierCost cancleSupplierCost =supplierCostMap.get(BillEnum.SupplierCostStatus.CANCLE_STATUS.getCode()).get(0);
        //获取实收数据
        List<PerCommSupplierCost> paySupplierCost=supplierCostMap.get(BillEnum.SupplierCostStatus.PAY_STATUS.getCode());
        Map<Integer, List<PerCommSupplierCost>> paySupplierCostMap = paySupplierCost.stream().collect(Collectors.groupingBy(PerCommSupplierCost::getStartMonth));
        if(paySupplierCostMap.containsKey(cancleSupplierCost.getStartMonth())){
          PerCommSupplierCost beforeCost =paySupplierCostMap.get(cancleSupplierCost.getStartMonth()).get(0);
          PerCommSupplierCostDeleteVo perCommSupplierCostDeleteVo = new PerCommSupplierCostDeleteVo();
          perCommSupplierCostDeleteVo.setId(beforeCost.getId());
          perCommSupplierCostDeleteVo.setTableName(GetTableName.getTableNamePerCommSupplierCost(currentMonth));
          perCommSupplierCostDeleteVoList.add(perCommSupplierCostDeleteVo);
          cancleSupplierCost.setStatus(BillEnum.SupplierCostStatus.BEFORE_STATUS.getCode());
          cancleSupplierCost.setTableName(GetTableName.getTableNamePerCommSupplierCost(currentMonth));
          updatePerCommSupplierCostList.add(cancleSupplierCost);
        }
      }


    }

  }

}
