<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!DOCTYPE html>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
  <meta charset="utf-8">
  <title>测试招商银企直连支付</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">

  <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
  <link rel="stylesheet" href="${ctx}/css/user.css?v=${publishVersion}" media="all"/>
  <link rel="stylesheet" href="${ctx}/layui_ext/dtree/dtree.css?v=${publishVersion}"/>
  <link rel="stylesheet" href="${ctx}/layui_ext/dtree/font/dtreefont.css?v=${publishVersion}"/>
</head>
<body class="childrenBody">
<div class="layui-fluid">
  <div class="layui-card">
    <%--startQuery--%>
    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm" action="" method="get">
      <%--表示社保--%>
      <input type="hidden" name="kind" id="kind" value="2">
      <div class="layui-form-item">
        <div class="layui-inline queryTable">
          <div class="layui-inline">
            <label class="layui-form-label layui-elip" title="业务参考号" style="font-weight:800"><i
                    style="color: red">*</i>业务参考号</label>
            <div class="layui-input-inline">
              <input type="text" id="yurRef" name="yurRef" placeholder="请输入"
                     class="layui-input" autocomplete="off">
            </div>
          </div>


          <div class="layui-inline" style="float: right;padding:10px ;">
            <button class="layui-btn" id="btnQuery" type="button">查询</button>
            <%--            <a class="layui-btn" id="more">显示更多…</a>--%>
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline queryTable">
          <div class="layui-inline">
            <label class="layui-form-label layui-elip" title="业务参考号" style="font-weight:800"><i
                    style="color: red">*</i>业务参考号</label>
            <div class="layui-input-inline">
              <h2 id="msg110"> </h2>
            </div>
          </div>



        </div>
      </div>
    </form>
  </div>
</div>

<%--startTable--%>
<table id="QueryGridTable" lay-filter="QueryGridTable"></table>
<table id="QueryGridTable2" lay-filter="QueryGridTable2"></table>
<table id="QueryGridTable3" lay-filter="QueryGridTable3"></table>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/sha256.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/formverify.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/cmb/comFormat.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/thirdPart/queryPayrollBatchAndDetailPage.js?v=${publishVersion}"></script>

</body>
</html>
