package com.reon.hr.sp.base.entity.sys;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InsuranceRatio {

    private String insuranceRatioCode;

    private Integer cityCode;

    private String cityName;

    private Integer productCode;

    private String ratioName;

    private BigDecimal comRatio;

    private BigDecimal comAdd;

    private BigDecimal indRatio;

    private BigDecimal indlAdd;

    private Integer comExactVal;

    private Integer comCalcMode;

    private Integer indExactVal;

    private Integer indCalcMode;

    private Integer chargeFreq;

    private Integer payCalcOrder;

    private Short payMonth;

    private BigDecimal comMonthlyFee;

    private BigDecimal indMonthlyFee;

    private Integer staus;
    private Integer refFlag;

    private String processInstanceId;

    private String suspendMan;

    private Date suspendTime;

    private Date createTime;

    private String creator;

    private Date updateTime;

    private String updater;

    private String delFlag;

    private Integer specialFlag;
    private Integer accordingRatio;


}