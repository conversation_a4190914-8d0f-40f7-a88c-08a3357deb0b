<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://dubbo.apache.org/schema/dubbo
		http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:annotation package="com.reon.hr.api.workflow.dubbo.service,com.reon.hr.sp.workflow.dubbo.service" />

    <dubbo:application name="reon-workflow-sp" owner="dingshan" organization="dingshan" logger="slf4j" >
        <dubbo:parameter key="qos.enable" value="false"/>
    </dubbo:application>
    <!-- 注册中心 -->
    <dubbo:registry address="${dubbo.registry.url}" />
     <!-- 服务监控 -->
    <!--<dubbo:monitor protocol="registry" />-->

    <!-- 服务提供者缺省值配置，该标签为<dubbo:service>和<dubbo:protocol>标签的缺省值设置 -->
    <dubbo:provider timeout="${dubbo.default.timeout}" protocol="${dubbo.default.protocol}"/>
    <!-- 服务消费者缺省值配置，该标签为<dubbo:reference>标签的缺省值设置 -->
    <dubbo:consumer timeout="${dubbo.default.timeout}" check="false"/>

    <!-- 多协议 -->
    <dubbo:protocol name="dubbo" serialization="hessian2" port="-1" />
<!--    <dubbo:protocol name="rest" port="${dubbo.rest.port}" threads="${dubbo.rest.threadcount}" contextpath="${dubbo.rest.contextpath}"
                    server="${dubbo.rest.servertype}" accepts="${dubbo.rest.acceptcount}" extension="${dubbo.rest.protocalextension}" />
    -->
    <!--  服务者 -->
    <dubbo:reference id="userDubboService" interface="com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService" check="false"/>
    <dubbo:reference id="userOrgPosDubboService" interface="com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService" check="false"/>
    <dubbo:reference id="positionDubboService" interface="com.reon.hr.api.dubbo.service.rpc.sys.IPositionWrapperService"  check="false"/>
    <dubbo:reference id="paymentApplyWrapperService" interface="com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService"  check="false"/>
    <dubbo:reference id="customerWrapperService" interface="com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService"  check="false"/>
    <dubbo:reference id="qysContractTableWrapperService" interface="com.reon.hr.api.customer.dubbo.service.rpc.qiyuesuo.IQysContractTableWrapperService" check="false"/>
    <dubbo:service  ref="workflowDubboService" interface="com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService"/>
    <!-- <dubbo:service ref="server4ChannelService" interface="com.reon.hr.api.dubbo.service.rest.IServer4ChannelService" protocol="rest"/> -->


    <dubbo:reference id="salaryPaymentApplyDubboService" interface="com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.ISalaryPaymentApplyWrapperService" check="false"/>
</beans>
