/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/10
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ServiceSiteCfgVo
 *
 * @date 2020/6/10 9:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceSiteCfgVo implements Serializable {

    private Long id;

    private Integer districtCode;

    private Integer cityCode;

    private String cityName;

    private String serviceSiteCode;

    private Integer orgType;

    private String serviceSiteName;

    /**
     * 使用状态
     */
    private Integer useStatusType;
    private List<Integer> useStatusTypes;

    /**
     * 使用状态说明
     */
    private String useState;
    private String noUseState;
    private Integer noUseStatusType;

    /**
     * 接单客服
     */
    private String receivingMan;

    private Integer applyInsurFreq;

    private Integer applyFundFreq;

    /**
     * 大病申报频率
     * 1:月收
     * 2:年收(不足年按月)
     * 3:年收(不足年按年)
     */
    private Integer applyIllnessFreq;

    private Integer billFeeRule;

    /**
     * 是否可以落地发薪和报税
     */
    private Integer paymentAndDeclare;
    /**
     * 残障金政策标准
     */
    private String disFundPolicyNorm;
    /**
     * 政策链接
     */
    private String policyLink;
    /**
     * 残障金我司收取标准
     */
    private String companyCollectNorm;
    /**
     * 缴纳基本方式
     */
    private String payBaseMethod;

    private String bigAccountArea;

    private String singleAccountCounty;

    private String addInfo;

    private String downInfo;

    private String appendInfo;

    private Integer additionFlag;



    private String additionStartMonth;

    private Integer receptionFlag;
    private String injuryEnjoy;

    private String additionRule;

    private Integer insurAddDay;

    private Integer insurSubDay;

    private Integer crfAddDay;

    private Integer crfSubDay;

    private Date createTime;

    private String creator;

    private Date updateTime;

    /**
     * 特殊注意事项
     */
    private String specialConsiderations;
    /**
     * 客户端特殊注意事项
     */
    private String ehrSpecialConsiderations;

    private String updater;
//用于选择是 add  还是  update  还是 delete
    private String optType;

    private String delFlag;

    //人员类型在表中的string字段
    private String categoryCode;
    //人员类型在前端用到的展示字段 是数字 需要在服务员中查出来
    private Integer indTypeCode;
    private Integer peopleNum;
    /**
     * 是否必须落地发薪
     */
    private Integer mustPayment;

    private List<SscUpdateLogVo> sscUpdateLogs;
}
