package com.reon.hr.modules.base.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.InsuTempCfgRecordWrapperService;
import com.reon.hr.api.base.vo.InsuTempCfgRecordVo;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

@RestController
@RequestMapping("/base/InsuTempCfgRecord/")
public class InsuTempCfgRecordController extends BaseController {

    @Autowired
    private InsuTempCfgRecordWrapperService insuTempCfgRecordWrapperService;

    @GetMapping("/gotoInsuTempCfgRecordPage")
    public ModelAndView gotoInsuTempCfgRecordPage() {
        return new ModelAndView("/base/insuTempCfgRecord/insuTempCfgRecordPage");
    }

    @GetMapping("/gotoEditInsuTempCfgRecordPage")
    public ModelAndView gotoEditInsuTempCfgRecordPage(Model model, String optType,Long id) {
        model.addAttribute("optType",optType);
        if(!"add".equals(optType)){
            InsuTempCfgRecordVo recordById = insuTempCfgRecordWrapperService.getInsuTempCfgRecordById(id);
            model.addAttribute("json", JsonUtil.beanToJson(recordById));
        }
        return new ModelAndView("/base/insuTempCfgRecord/editInsuTempCfgRecordPage");
    }


    @GetMapping("/gotoViewFilePage")
    public ModelAndView gotoViewFilePage() {
        return new ModelAndView("/base/insuTempCfgRecord/viewFile");
    }


    @RepeatSubmit
    @GetMapping("/getInsuTempCfgRecordPage")
    public LayuiReplay getInsuTempCfgRecordPage(InsuTempCfgRecordVo vo) {
        Page<InsuTempCfgRecordVo> data = insuTempCfgRecordWrapperService.getInsuTempCfgRecordPage(vo);
        return new LayuiReplay(LayuiReplay.success().getCode(), LayuiReplay.success().getMsg(), data.getTotal(), data.getRecords());
    }


    @RepeatSubmit
    @PostMapping("/saveOrUpdateInsuTempCfgRecord")
    public LayuiReplay saveOrUpdateInsuTempCfgRecord(@RequestBody InsuTempCfgRecordVo vo) {
        if (vo.getCustId() == null) {
            return LayuiReplay.error("请选择客户！");
        }
        if (vo.getCityCode() == null) {
            return LayuiReplay.error("请选择城市！");
        }
        if (StringUtils.isBlank(vo.getReceiving())) {
            return LayuiReplay.error("请选择接单方！");
        }
        if (StringUtils.isBlank(vo.getContractNo())) {
            return LayuiReplay.error("请选择合同！");
        }

        if (CollectionUtils.isEmpty(vo.getFileIdList())) {
            return LayuiReplay.error("请上传文件！");
        }
        String loginName = getSessionUser().getLoginName();
        vo.setCreator(loginName);
        vo.setUpdater(loginName);
        int i = insuTempCfgRecordWrapperService.saveOrUpdateInsuTempCfgRecord(vo);
        return new LayuiReplay(LayuiReplay.success().getCode(), LayuiReplay.success().getMsg());
    }
}
