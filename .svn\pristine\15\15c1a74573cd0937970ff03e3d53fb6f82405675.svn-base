package com.reon.hr.sp.customer.dao.salaryImport;

import com.reon.hr.api.customer.vo.salary.PayEmpBaseVo;
import com.reon.hr.sp.customer.entity.salaryImport.PayEmpBase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PayEmpBaseMapper {
    int insert(@Param("list") List<PayEmpBase> payEmpBaseList);
    int deletePayEmpBase(Long id);
    int deletePayEmpBaseByPayIdList(@Param("payIdList")List<Long> payIdList);

    List<PayEmpBaseVo> getByPayId(Long payId);

    List<PayEmpBaseVo> selectPayEmpBaseListByPayIdList(@Param("list") List<Long> payIdList);

    Long getAllDataByPayIdAndWithholdingAgentNo(@Param("payId") String payId, @Param("withholdingAgentNo") String withholdingAgentNo);

    int insertPay(@Param("item") PayEmpBase payEmpBase);

    int getCountByCertNo(@Param("certNo") String certNo,@Param("withholdingAgentNo") String withholdingAgentNo,@Param("year")String year,@Param("taxMonth") Integer taxMonth
            ,@Param("taxListId") Long taxListId);
    PayEmpBaseVo getDataByCertNo(@Param("certNo") String certNo,@Param("withholdingAgentNo") String withholdingAgentNo,@Param("year")String year
            ,@Param("taxListId") Long taxListId);
    int getNormalCountByCertNo(@Param("certNo") String certNo,@Param("withholdingAgentNo") String withholdingAgentNo,@Param("year")String year,@Param("taxMonth") Integer taxMonth);

    List<Long> getBySalaryInfoIdList(@Param("salaryInfoIdList")List<Long> salaryInfoIdList);

    void deleteByIdList(@Param("pebIdList")List<Long> pebIdList);
}
