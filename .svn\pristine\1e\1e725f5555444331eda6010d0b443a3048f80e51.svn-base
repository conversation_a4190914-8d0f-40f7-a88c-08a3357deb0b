package com.reon.hr.sp.change.entity.change;

import lombok.Data;

import java.util.Date;

@Data
public class CollectImportResult {
    private Long id;

    private Long chgSetId;

    private Integer successCnt;

    private Integer failureCnt;

    private Integer cancelCnt;

    private Integer total;

    private Date importTime;

    private String importFile;

    private String detailTxt;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;


}