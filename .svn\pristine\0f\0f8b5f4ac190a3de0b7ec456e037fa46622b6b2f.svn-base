<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/22
  Time: 14:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>查看员工合同</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-onlySelf {
            width: 125px;
        }

        .layui-tab-item {
            position: unset
        }

        .layui-btn layui-btn-sm tableSelect_btn_select reset {
            display: none !important;
        }

        .dis {
            pointer-events: none
        }
        #addContract{
            width: 350px;
            height: 100px;
        }
        #editContract{
            width: 350px;
            height: 100px;
        }
        #stopContract{
            width: 350px;
            height: 100px;
        }
        #renewContract{
            width: 350px;
            height: 100px;
        }

    </style>
</head>
<body>

<div class="layui-tab-item layui-show" style="margin-top: 5px">
    <div class="layui-fluid">
        <div class="layui-card">
            <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
                <%--隐藏域--%>
                <input type="hidden" id="empContractNo" name="empContractNo"/>
                <input type="hidden" id="orderNo" name="orderNo"/>
                <input type="hidden" id="optType" value="${optType}"/>
                <input type="hidden" id="fileIdCache"/>
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label title="证件号码:" class="layui-form-label  layui-onlySelf"><i
                                style="color: red">*</i>证件号码:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="certNo" readonly
                                   name="certNo" lay-filter="certNoFilter" autocomplete="off" placeholder=""
                                   lay-verify="required">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="证件类型:" class="layui-form-label  layui-onlySelf">证件类型:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="certType1"
                                   lay-filter="certTypeFilter" autocomplete="off" readonly
                                   placeholder="">
                            <input type="hidden" id="certType" name="certType">
                        </div>

                    </div>

                    <div class="layui-inline">
                        <label title="雇员姓名:" class="layui-form-label  layui-onlySelf">雇员姓名:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="name"
                                   name="name" lay-filter="nameFilter" autocomplete="off" placeholder="" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="唯一号:" class="layui-form-label  layui-onlySelf">唯一号:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="employeeNo"
                                   name="employeeNo" lay-filter="employeeNoFilter" autocomplete="off"
                                   placeholder="请选择" readonly>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="客户编号:" class="layui-form-label  layui-onlySelf">客户编号:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="custNo" readonly
                                   name="custNo" lay-filter="custNoFilter" autocomplete="off" placeholder="请选择">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="客户名称:" class="layui-form-label  layui-onlySelf">客户名称:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="custName" readonly
                                   name="custName" lay-filter="custNameFilter" autocomplete="off"
                                   placeholder="">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="正式工资:" class="layui-form-label  layui-onlySelf"><i
                                style="color: red">*</i>正式工资:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input layui-input-disposable dis" id="formalSalary"
                                   lay-verify="required"
                                   name="formalSalary" lay-filter="formalSalaryFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="是否外呼:" class="layui-form-label  layui-onlySelf">是否外呼:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis" lay-filter="callFlagFilter" name="callFlag"
                                    id="callFlag" placeholder="请选择" readonly disabled
                                    DICT_TYPE="BOOLEAN_TYPE">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="签署状态:" class="layui-form-label  layui-onlySelf">签署状态:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis" name="signStatus" DICT_TYPE="SIGN_STATUS"
                                    id="signStatus" placeholder="无法选择" readonly disabled>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="签署日期:" class="layui-form-label  layui-onlySelf">签署日期:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="signDate"
                                   name="signDate" lay-filter="signDateFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="工作制:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>工作制:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis"  lay-verify="required" name="workMethod" id="workMethod" DICT_TYPE="WORKING_SYSTEM" placeholder="请选择" >
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同类别:" class="layui-form-label  layui-onlySelf"><i
                                style="color: red">*</i>合同类别:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis" lay-verify="required" lay-filter="empContractType"
                                    name="empContractType" DICT_TYPE="LABOR_CONTRACT_KIND"
                                    id="empContractType" placeholder="请选择">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="派遣期限起:" class="layui-form-label  layui-onlySelf"><i style="color: red" class ="layui-hide disStart">*</i>派遣期限起:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis dispatch" id="dispatchStart"
                                   lay-verify="required"
                                   name="dispatchStart" lay-filter="dispatchStartFilter" autocomplete="off"
                                   placeholder="请选择" disabled>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="派遣期限止:" class="layui-form-label  layui-onlySelf"><i style="color: red" class ="layui-hide disStart">*</i>派遣期限止:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis dispatch" id="dispatchEnd"
                                   lay-verify="required" disabled name="dispatchEnd" lay-filter="dispatchEndFilter"
                                   autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="劳动合同起:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>劳动合同起:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="startDate"
                                   lay-verify="required"
                                   name="startDate" lay-filter="startDateFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="劳动合同止:" class="layui-form-label  layui-onlySelf"><i class="layui-hide redEmpContractType" style="color: red">*</i>劳动合同止:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis requiredEmpContractType" id="endDate"
                                   name="endDate" lay-filter="endDateFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label title="重新签署开始时间:" class="layui-form-label  layui-onlySelf"><i class="layui-hide redEmpContractType" style="color: red">*</i>重新签署开始时间:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="reSignDate"
                                   name="reSignDate" lay-filter="reSignDateFilter" autocomplete="off" lay-verify="" disabled readonly
                                   placeholder="请选择">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label title="是否有试用期:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>是否有试用期:</label>
                        <div class="layui-input-inline">
                            <select type="text" class="layui-select dis" id="probationFlag" DICT_TYPE="BOOLEAN_TYPE"
                                    name="probationFlag" lay-filter="probationFlagFilter"
                                    autocomplete="off" placeholder="请选择" readonly lay-verify="required">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="试用时间起:" class="layui-form-label layui-onlySelf"><i style="color: red" class="layui-hide probaStart">*</i>试用时间起:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable proba dis" id="probaStart"
                                   name="probaStart" lay-filter="probaStartFilter" autocomplete="off"
                                   placeholder="请选择" disabled>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="试用期月数:" class="layui-form-label  layui-onlySelf">试用期月数:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input layui-input-disposable proba dis" id="probationMonths"
                                   name="probationMonths" lay-filter="probationMonthsFilter" autocomplete="off"
                                   placeholder="请输入">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="试用时间止:" class="layui-form-label  layui-onlySelf"><i style="color: red" class="layui-hide probaStart">*</i>试用时间止:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable proba dis" id="probaEnd"
                                   name="probaEnd" lay-filter="probaEndFilter" autocomplete="off"
                                   placeholder="请选择" disabled>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="试用期工资:" class="layui-form-label  layui-onlySelf"><i style="color: red" class="layui-hide probaStart">*</i>试用期工资:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable proba dis" id="probaSalary"
                                   name="probaSalary" lay-filter="probaSalaryFilter" autocomplete="off"
                                   placeholder="请选择" disabled>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同签订地:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同签订地:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="signPlace1" readonly
                                   lay-filter="signPlace1Filter" autocomplete="off"
                                   placeholder="请选择" lay-verify="required">
                            <input type="hidden" id="signPlace" name="signPlace">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同版本地:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同版本地:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="tempPlace1" readonly
                                   lay-filter="tempPlace1Filter" autocomplete="off"
                                   placeholder="请选择" lay-verify="required">
                            <input type="hidden" id="tempPlace" name="tempPlace">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同版本:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同版本:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis" id="tempType" DICT_TYPE="LABOR_CONTRACT_TEMP"
                                    name="tempType" lay-filter="tempTypeFilter" autocomplete="off"
                                    placeholder="请选择" lay-verify="required">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同原则:" class="layui-form-label  layui-onlySelf">合同原则:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="principle"
                                   name="principle" lay-filter="principleFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="试用时间止:" class="layui-form-label  layui-onlySelf">录入时间:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable proba" id="entryTime"
                                   name="entryTime" lay-filter="entryTimeFilter" autocomplete="off" readonly
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="续签中间时间:" class="layui-form-label  layui-onlySelf">续签中间时间:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable proba" id="middleTime"
                                   name="middleTime" lay-filter="middleTimeFilter" autocomplete="off" readonly
                                   placeholder="请选择">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label title="用工单位:" class="layui-form-label  layui-onlySelf">用工单位:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="employingUnit"
                                   name="employingUnit" lay-filter="employingUnitFilter" autocomplete="off"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="用工单位:" class="layui-form-label  layui-onlySelf">工作地（省/市）:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="workPlace"
                                   name="workPlace" autocomplete="off"
                                   placeholder="请输入">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label title="用工单位:" class="layui-form-label  layui-onlySelf">工作岗位:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="jobPosition"
                                   name="jobPosition"  autocomplete="off"
                                   placeholder="请输入">
                        </div>
                    </div>
                    <div></div>
                        <tr>
                            <td align="right">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp新增操作记录</td>
                            <td colspan="3"><textarea class="layui-textarea" readonly>${employeeContractOperationLog.saveOperation}</textarea></td>
                            <td align="right">&nbsp&nbsp修改操作记录</td>
                            <td colspan="3"><textarea class="layui-textarea" readonly>${employeeContractOperationLog.updateOperation}</textarea></td>
                        </tr>
                        <tr>
                            <td align="right">&nbsp&nbsp续签操作记录</td>
                            <td colspan="3"><textarea class="layui-textarea" readonly>${employeeContractOperationLog.renewOperation}</textarea></td>
                            <td align="right">&nbsp&nbsp终止操作记录</td>
                            <td colspan="3"><textarea class="layui-textarea" readonly>${employeeContractOperationLog.stopOperation}</textarea></td>
                        </tr>
                    <tr>
                        <td align="right">&nbsp&nbsp重新签署操作记录</td>
                        <td colspan="3"><textarea class="layui-textarea" readonly>${employeeContractOperationLog.reSignOperation}</textarea></td>
                    </tr>

                </div>
                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>文件上传</legend>
                    </fieldset>
                    <div class="layui-upload">
                        <div class="layui-input-block" style="width: 1166px;">
                            <button type="button" id="employContractUpload" class="layui-btn layui-btn-normal">选择文件
                            </button>
                            <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                                预览图：
                                <div class="layui-upload-list" id="upload"></div>
                            </blockquote>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>备注</legend>
                    </fieldset>
                    <div class="layui-input-block" style="width: 1166px;">
                        <textarea placeholder="请输入内容" name="remark" id="remark" class="layui-textarea queryDis"
                                  style="min-width: 55px"></textarea>
                    </div>

                </div>

        </div>
        </form>
    </div>
</div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/employeeContract/employeeContractEdit.js?v=${publishVersion}"></script>
</body>
</html>
