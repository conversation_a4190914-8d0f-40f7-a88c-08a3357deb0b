package com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class NaturalWagesDetailImportDto implements Serializable {

    private Long employeeId;
    private String withholdingAgentNo;
    private String withholdingAgentName;
    private String withholdingAgentType;
    private Integer taxMonth;
    private String salaryJsonInfo;

    @Excel(name = "客户名称",width = 26)
    private String custName;

    @Excel(name = "工号",width = 10)
    private String staffNo;

    /**
     * 雇员名称
     */
    @Excel(name = "*姓名",width = 10)
    private String employeeName;

    /**
     * 证件类型
     */
    @Excel(name = "*证件类型",width = 10, readConverterExp = "1=居民身份证,2=其他,3=港澳居民来往内地通行证,4=台湾居民来往大陆通行证,5=中国护照,6=其他")
    private String certType;

    /**
     * 证件号码
     */
    @Excel(name = "*证件号码",width = 22)
    private String certNo;

    @Excel(name = "本期收入",width = 10)
    private BigDecimal currentIncome;
    @Excel(name = "本期公积金超限计税金额",width = 10)
    private BigDecimal currentAccumulationFundExcessTaxAmount;

    @Excel(name = "本期免税收入",width = 10)
    private BigDecimal currentTaxFreeIncome;

    @Excel(name = "基本养老保险费",width = 10)
    private BigDecimal basicPensionPremium;

    @Excel(name = "基本医疗保险费",width = 10)
    private BigDecimal basicMedicalInsurancePremium;

    @Excel(name = "失业保险费",width = 10)
    private BigDecimal unemploymentInsuranceExpense;

    @Excel(name = "住房公积金",width = 10)
    private BigDecimal housingFund;

    @Excel(name = "累计子女教育",width = 10)
    private BigDecimal cumulativeChildEducation;

    @Excel(name = "累计继续教育",width = 10)
    private BigDecimal cumulativeContinuingEducation;

    @Excel(name = "累计住房贷款利息",width = 10)
    private BigDecimal accumulatedInterestHousingLoans;

    @Excel(name = "累计住房租金",width = 10)
    private BigDecimal accumulatedHousingRent;

    @Excel(name = "累计赡养老人",width = 10)
    private BigDecimal supportElderlyCumulatively;

    @Excel(name = "累计3岁以下婴幼儿照护",width = 10)
    private BigDecimal accuBabyCare;

    @Excel(name = "累计个人养老金",width = 10)
    private BigDecimal accuPersonalPension;

    @Excel(name = "企业(职业)年金",width = 10)
    private BigDecimal enterpriseOccupationalAnnuity;

    @Excel(name = "商业健康保险",width = 10)
    private BigDecimal commercialHealthInsurance;

    @Excel(name = "税延养老保险",width = 10)
    private BigDecimal taxDeferredPensionInsurance;

    @Excel(name = "其他",width = 10)
    private BigDecimal other;

    @Excel(name = "准予扣除的捐赠额",width = 10)
    private BigDecimal deductionAllowedDonationsAmount;

    @Excel(name = "减免税额",width = 10)
    private BigDecimal taxDeduction;

    @Excel(name = "备注",width = 10)
    private String remark;

    private Long salaryId;
    @Excel(name = "工资支付日期",width = 10)
    private String lastDate;
    @Excel(name = "是否退票重发",width = 10)
    private String anewPayFlagStr;
    @Excel(name = "本期公积金最高限额是否变更",width = 10,richText = "与客服计算时的数据相比")
    private String currentAccumulationFundExcessTaxAmountChangeFlag;
    @Excel(name = "本期公积金最高限额",width = 10)
    private BigDecimal currentAccumulationFundTopAmount;

    private String otherName;

    private String otherCertNo;


}
