package com.reon.hr.sp.base.entity.sys;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class CompanyAddrList {
	private Long id;

	private String comCode;

	private String comTel;

	private String comAddr;

	private String comEmail;
    private String taxerIdentNo;

    private String delFlag;

    private String postcode;
    /** 邮编 */
    private String faxNumber;
    /** 传真 */
    private String legalRep;
    /** 法人 */

	private String fileId;
	private String creator;

	private Date createTime;

	private String updater;

	private Date updateTime;


}