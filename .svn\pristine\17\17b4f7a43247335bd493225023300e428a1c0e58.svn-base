/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/5/18 0018
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.batchImport;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SuppliPayCfgVo
 *
 * @date 2021/5/18 0018 14:00
 */
@Data
public class SuppliPayCfgVo implements Serializable {

    private static final long serialVersionUID = -7839169557940907705L;
    /**
     * '编号',
     */
    private String importNo;
    /**
     *  '导入名称',
     */
    private String importName;
    /**
     *  '补缴导入类型(1:全额导入，2：补差导入)',
     */
    private Integer importType;
    /**
     *   '补差方式(1:不允许退费补差，2：允许退费补差)',
     */
    private Integer diffMethod;
    /**
     *
     *    '福利办理方',
     */
    private String orgCode;
    /**
     *    '辅料code',
     */
    private String packCode;
    /**
     *   '办理供应商',
     */
    private String handleCorp;


    /**
     *
     *    '客户ID',
     */
    private Integer custId;
    /**
     * 
     *    '补缴发生月',
     */
    private Integer happenMonth;
    /**
     *  
     *    '补缴导入种类（1:补缴导入，2:补缴滞纳金导入）',
     */
    private Integer suppliPayKind;
    /**
     * 
     *    '创建人',
     */
    private String creator;
    /**
     * 
     *   '创建时间',
     */
    private String createTime;
    /**
     *  
     *  '修改人',
     */
    private String updater;
    /**
     *  '修改时间',
     */
    private String updateTime;
    /**
     *  '删除标识(Y:已删除，N:未删除)',
     */
    private String delFlag;

    /**
     *  滞纳金发生月份 1：当月 2：上月
     */
    private Integer letFeeType;

}
