<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.salary.TaxComparisonFeedbackRecordMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.salary.TaxComparisonFeedbackRecord">
        <result column="salary_batch_detail_ids" jdbcType="BIGINT" property="salaryBatchDetailIds"/>
        <result column="emp_id" jdbcType="BIGINT" property="empId"/>
        <result column="offline_data_ids" jdbcType="BIGINT" property="offlineDataIds"/>
        <result column="salary_comparison_type" jdbcType="INTEGER" property="salaryComparisonType"/>
        <result column="difference" jdbcType="DECIMAL" property="difference"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="payment_date" jdbcType="INTEGER" property="paymentDate"/>
        <result column="tax_month" jdbcType="INTEGER" property="taxMonth"/>
        <result column="emp_excel_add" jdbcType="VARCHAR" property="empExcelAdd"/>
        <result column="ind_tax_apply_info" jdbcType="VARCHAR" property="indTaxApplyInfo"/>
        <result column="tax_comparison_type" jdbcType="INTEGER" property="taxComparisonType"/>
        <result column="feedback_status" jdbcType="INTEGER" property="feedbackStatus"/>
        <result column="commissioner_feedback_remark" jdbcType="VARCHAR" property="commissionerFeedbackRemark"/>
        <result column="commissioner_feedback_time" jdbcType="TIMESTAMP" property="commissionerFeedbackTime"/>
        <result column="finance_feedback_remark" jdbcType="VARCHAR" property="financeFeedbackRemark"/>
        <result column="finance_feedback_time" jdbcType="TIMESTAMP" property="financeFeedbackTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        salary_batch_detail_ids,emp_id,offline_data_ids,salary_comparison_type,difference,remark,payment_date,tax_month,
        emp_excel_add,ind_tax_apply_info,tax_comparison_type,feedback_status,
        commissioner_feedback_remark,commissioner_feedback_time,finance_feedback_remark,finance_feedback_time,
        withholding_agent_no,creator, create_time, updater, update_time, del_flag
    </sql>
    <insert id="insertByVoList">
        insert into tax_comparison_feedback_record
        (salary_batch_detail_ids,emp_id,offline_data_ids,salary_comparison_type,difference,remark,payment_date,tax_month,
        emp_excel_add,ind_tax_apply_info,tax_comparison_type,feedback_status,
        commissioner_feedback_remark,commissioner_feedback_time,finance_feedback_remark,finance_feedback_time,
        withholding_agent_no,creator,updater)
        values
        <foreach collection="list" item="item" separator=",">
            (
             #{item.salaryBatchDetailIds},#{item.empId},#{item.offlineDataIds},#{item.salaryComparisonType},
             #{item.difference},#{item.remark},#{item.paymentDate},#{item.taxMonth},
             #{item.empExcelAdd},#{item.indTaxApplyInfo},#{item.taxComparisonType},#{item.feedbackStatus},
             #{item.commissionerFeedbackRemark},#{item.commissionerFeedbackTime},#{item.financeFeedbackRemark},#{item.financeFeedbackTime},
             #{item.withholdingAgentNo},#{item.creator},#{item.updater}
            )
        </foreach>
    </insert>
    <delete id="deleteByPaymentDate">
        delete from tax_comparison_feedback_record
               where payment_date=#{paymentDate} and salary_comparison_type=1
    </delete>
    <delete id="deleteByTaxMonth">
        delete from tax_comparison_feedback_record
        where tax_month=#{taxMonth} and tax_comparison_type=#{taxComparisonType} and salary_comparison_type=2
        <if test="withholdingAgentNo!=null and withholdingAgentNo!=''">
            and withholding_agent_no = #{withholdingAgentNo}
        </if>
        <if test="certNoList != null and certNoList.size > 0">
            and (
                TRIM(REPLACE(IFNULL(JSON_EXTRACT(ind_tax_apply_info,'$.certNo'),''),'"','')) in
                <foreach collection="certNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                or TRIM(REPLACE(IFNULL(JSON_EXTRACT(emp_excel_add,'$.certNo'),''),'"','')) in
                <foreach collection="certNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            )
        </if>
        <if test="staffNameList != null and staffNameList.size > 0">
            and (
            TRIM(REPLACE(IFNULL(JSON_EXTRACT(ind_tax_apply_info,'$.staffName'),''),'"','')) in
            <foreach collection="staffNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            or TRIM(REPLACE(IFNULL(JSON_EXTRACT(emp_excel_add,'$.name'),''),'"','')) in
            <foreach collection="staffNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </delete>
    <select id="getTaxComparisonFeedbackRecordList"
            resultType="com.reon.hr.api.customer.vo.export.salary.TaxComparisonFeedbackRecordExportVo">
        SELECT
        e.`name` emp_name,e.cert_no,tcfr.emp_id,tcfr.difference,tcfr.offline_data_ids,tcfr.salary_batch_detail_ids,
        tcfr.salary_comparison_type,tcfr.remark,tcfr.emp_excel_add,tcfr.ind_tax_apply_info,tcfr.tax_comparison_type,tcfr.feedback_status,
        tcfr.commissioner_feedback_remark,tcfr.commissioner_feedback_time,tcfr.finance_feedback_remark,tcfr.finance_feedback_time,
        tcfr.payment_date,tcfr.tax_month,wa.withholding_agent_name,wa.withholding_agent_type
        FROM
        tax_comparison_feedback_record tcfr
        LEFT JOIN employee e on tcfr.emp_id=e.id
        left join withholding_agent wa on wa.withholding_agent_no=tcfr.withholding_agent_no
        <where>
            <if test="vo.empName != null and vo.empName !='' ">
                and e.`name` = #{vo.empName}
            </if>
            <if test="vo.certNo != null and vo.certNo !='' ">
                and e.cert_no = #{vo.certNo}
            </if>
            <if test="vo.paymentDate != null and vo.paymentDate !='' ">
                and tcfr.payment_date = #{vo.paymentDate}
            </if>
            <if test="vo.withholdingAgentNo != null and vo.withholdingAgentNo !='' ">
                and tcfr.withholding_agent_no = #{vo.withholdingAgentNo}
            </if>
            <if test="vo.salaryComparisonType != null and vo.salaryComparisonType !='' ">
                and tcfr.salary_comparison_type = #{vo.salaryComparisonType}
            </if>
            <if test="vo.taxComparisonType != null and vo.taxComparisonType !='' ">
                and tcfr.tax_comparison_type = #{vo.taxComparisonType}
            </if>
        </where>
    </select>
    <select id="getTaxComparisonFeedbackRecordTaxList"
            resultType="com.reon.hr.api.customer.dto.customer.salary.IncomeTaxDifferencesImportDto">
        SELECT
        e.employee_no,e.`name` employeeName,e.cert_no,tcfr.emp_id,tcfr.difference,tcfr.offline_data_ids,tcfr.salary_batch_detail_ids,
        tcfr.salary_comparison_type,tcfr.remark,tcfr.emp_excel_add,tcfr.ind_tax_apply_info,tcfr.tax_comparison_type,tcfr.feedback_status,
        tcfr.commissioner_feedback_remark,tcfr.commissioner_feedback_time,tcfr.finance_feedback_remark,tcfr.finance_feedback_time,
        tcfr.payment_date,tcfr.tax_month,wa.withholding_agent_name,wa.withholding_agent_type,
        si.creator salaryCreator,c.contract_no,c.contract_name,c.dist_com,c.commissioner dist_com_man,c.salary_commissioner,sp.pay_name salary_pay_name,
        cust.cust_no,cust.cust_name,sc.category_name salary_category_name,e.cert_type,sp.bill_month,sp.salary_month
        FROM
        tax_comparison_feedback_record tcfr
        LEFT JOIN employee e on tcfr.emp_id=e.id
        left join withholding_agent wa on wa.withholding_agent_no=tcfr.withholding_agent_no
        left join salary_pay sp on sp.id=IFNULL(JSON_EXTRACT(tcfr.emp_excel_add,'$.payId'),0)
        left join salary_category sc on sp.salary_category_id=sc.id
        left join salary_info si on si.emp_id=e.id and si.pay_id=sp.id
        LEFT JOIN contract c on c.contract_no=sc.contract_no
        LEFT JOIN customer cust on cust.id=c.cust_id
        <where>
            tcfr.salary_comparison_type=2
            <if test="vo.employeeName != null and vo.employeeName !='' ">
                and (e.`name` = #{vo.employeeName} or REPLACE(IFNULL(JSON_EXTRACT(tcfr.ind_tax_apply_info,'$.staffName'),''),'"','')=#{vo.employeeName})
            </if>
            <if test="vo.certNo != null and vo.certNo !='' ">
                and (e.cert_no = #{vo.certNo} or REPLACE(IFNULL(JSON_EXTRACT(tcfr.ind_tax_apply_info,'$.certNo'),''),'"','')=#{vo.certNo})
            </if>
            <if test="vo.paymentDate != null and vo.paymentDate !='' ">
                and tcfr.payment_date = #{vo.paymentDate}
            </if>
            <if test="vo.remark != null and vo.remark !='' ">
                and tcfr.remark like concat('%',#{vo.remark},'%')
            </if>
            <if test="vo.taxMonth != null and vo.taxMonth !='' ">
                and tcfr.tax_month = #{vo.taxMonth}
            </if>
            <if test="vo.withholdingAgentNo != null and vo.withholdingAgentNo !='' ">
                and tcfr.withholding_agent_no = #{vo.withholdingAgentNo}
            </if>
            <if test="vo.custName != null and vo.custName != '' ">
                and cust.cust_name like concat(#{vo.custName},'%')
            </if>
            <if test="vo.custNo != null and vo.custNo != '' ">
                and cust.cust_no = #{vo.custNo}
            </if>
            <if test="vo.contractNo != null and vo.contractNo != '' ">
                and c.contract_no = #{vo.contractNo}
            </if>
            <if test="vo.contractName != null and vo.contractName != '' ">
                and c.contract_name like concat(#{vo.contractName},'%')
            </if>
            <if test="vo.distCom != null and vo.distCom != '' ">
                and c.dist_com = #{vo.distCom}
            </if>
            <if test="vo.distComMan != null and vo.distComMan != '' ">
                and c.commissioner = #{vo.distComMan}
            </if>
            <if test="vo.salaryCommissioner != null and vo.salaryCommissioner != '' ">
                and c.salary_commissioner = #{vo.salaryCommissioner}
            </if>
            and ( c.contract_no is null
            <if test="vo.userOrgPositionDtoList != null and vo.userOrgPositionDtoList.size > 0">
                or
                <foreach collection="vo.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                         close=")">
                    (
                    (
                    ( c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( c.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.salary_comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                    )
                    <if test="userOrgPositionDto.loginName != null">
                        and (
                        ( c.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                        )
                    </if>
                    )
                </foreach>
            </if>
            )
        </where>
    </select>
    <select id="getTaxComparisonFeedbackRecord"
            resultType="com.reon.hr.api.customer.dto.customer.salary.TaxComparisonFeedbackRecordImportDto">
        SELECT
        e.employee_no,e.`name` employeeName,e.cert_no,tcfr.emp_id,tcfr.difference,tcfr.offline_data_ids,tcfr.salary_batch_detail_ids,
        tcfr.salary_comparison_type,tcfr.remark,tcfr.emp_excel_add,tcfr.ind_tax_apply_info,tcfr.tax_comparison_type,tcfr.feedback_status,
        tcfr.commissioner_feedback_remark,tcfr.commissioner_feedback_time,tcfr.finance_feedback_remark,tcfr.finance_feedback_time,
        tcfr.payment_date,tcfr.tax_month,wa.withholding_agent_name,wa.withholding_agent_type,
        tcfr.withholding_agent_no,tcfr.id,
        si.creator salaryCreator,c.contract_no,c.contract_name,c.dist_com,c.commissioner dist_com_man,c.salary_commissioner,sp.pay_name salary_pay_name,
        cust.cust_no,cust.cust_name,sc.category_name salary_category_name,e.cert_type,sp.bill_month,sp.salary_month
        FROM
        tax_comparison_feedback_record tcfr
        LEFT JOIN employee e on tcfr.emp_id=e.id
        left join withholding_agent wa on wa.withholding_agent_no=tcfr.withholding_agent_no
        left join salary_pay sp on sp.id=IFNULL(JSON_EXTRACT(tcfr.emp_excel_add,'$.payId'),0)
        left join salary_category sc on sp.salary_category_id=sc.id
        left join salary_info si on si.emp_id=e.id and si.pay_id=sp.id
        LEFT JOIN contract c on c.contract_no=sc.contract_no
        LEFT JOIN customer cust on cust.id=c.cust_id
        <where>
            tcfr.salary_comparison_type=2
            <if test="vo.employeeName != null and vo.employeeName !='' ">
                and (e.`name` = #{vo.employeeName} or REPLACE(IFNULL(JSON_EXTRACT(tcfr.ind_tax_apply_info,'$.staffName'),''),'"','')=#{vo.employeeName})
            </if>
            <if test="vo.certNo != null and vo.certNo !='' ">
                and (e.cert_no = #{vo.certNo} or REPLACE(IFNULL(JSON_EXTRACT(tcfr.ind_tax_apply_info,'$.certNo'),''),'"','')=#{vo.certNo})
            </if>
            <if test="vo.paymentDate != null and vo.paymentDate !='' ">
                and tcfr.payment_date = #{vo.paymentDate}
            </if>
            <if test="vo.remark != null and vo.remark !='' ">
                and tcfr.remark like concat('%',#{vo.remark},'%')
            </if>
            <if test="vo.taxMonth != null and vo.taxMonth !='' ">
                and tcfr.tax_month = #{vo.taxMonth}
            </if>
            <if test="vo.withholdingAgentNo != null and vo.withholdingAgentNo !='' ">
                and tcfr.withholding_agent_no = #{vo.withholdingAgentNo}
            </if>
            <if test="vo.feedbackStatus != null and vo.feedbackStatus !='' ">
                and tcfr.feedback_status = #{vo.feedbackStatus}
            </if>
            <if test="vo.custName != null and vo.custName != '' ">
                and cust.cust_name like concat(#{vo.custName},'%')
            </if>
            <if test="vo.custNo != null and vo.custNo != '' ">
                and cust.cust_no = #{vo.custNo}
            </if>
            <if test="vo.contractNo != null and vo.contractNo != '' ">
                and c.contract_no = #{vo.contractNo}
            </if>
            <if test="vo.contractName != null and vo.contractName != '' ">
                and c.contract_name like concat(#{vo.contractName},'%')
            </if>
            <if test="vo.distCom != null and vo.distCom != '' ">
                and c.dist_com = #{vo.distCom}
            </if>
            <if test="vo.distComMan != null and vo.distComMan != '' ">
                and c.commissioner = #{vo.distComMan}
            </if>
            <if test="vo.salaryCommissioner != null and vo.salaryCommissioner != '' ">
                and c.salary_commissioner = #{vo.salaryCommissioner}
            </if>
            and ( c.contract_no is null
            <if test="vo.userOrgPositionDtoList != null and vo.userOrgPositionDtoList.size > 0">
                or
                <foreach collection="vo.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                         close=")">
                    (
                    (
                    ( c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( c.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.salary_comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( '300400' like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and wa.org_code like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                    )
                    <if test="userOrgPositionDto.loginName != null">
                        and (
                        ( c.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                        )
                    </if>
                    )
                </foreach>
            </if>
            )
        </where>
    </select>
    <update id="updateFeedbackStatusByFinance">
        update tax_comparison_feedback_record
        set feedback_status = #{vo.feedbackStatus},updater=#{vo.updater},finance_feedback_remark=#{vo.financeFeedbackRemark},
        finance_feedback_time=#{vo.financeFeedbackTime}
        where
        <foreach collection="list" item="item" separator=" or " open="(" close=")">
            (
            <if test="item.salaryBatchDetailIds != null and item.salaryBatchDetailIds != '' ">
                salary_batch_detail_ids = #{item.salaryBatchDetailIds} and
            </if>
            <if test="item.empId != null and item.empId != '' ">
                emp_id = #{item.empId} and
            </if>
            <if test="item.offlineDataIds != null and item.offlineDataIds != '' ">
                offline_data_ids = #{item.offlineDataIds} and
            </if>
            salary_comparison_type=#{item.salaryComparisonType} and payment_date=#{item.paymentDate} and tax_month=#{item.taxMonth} and
            withholding_agent_no=#{item.withholdingAgentNo} and tax_comparison_type=#{item.taxComparisonType}
            )
        </foreach>
    </update>
    <update id="updateFinanceFeedbackStatusByIdList">
        update tax_comparison_feedback_record
        set feedback_status = #{vo.feedbackStatus},updater=#{vo.updater},finance_feedback_remark=#{vo.financeFeedbackRemark},
        finance_feedback_time=#{vo.financeFeedbackTime}
        where id in
        <foreach collection="list" item="item" separator=" , " open="(" close=")">
            #{item}
        </foreach>
    </update>
</mapper>
