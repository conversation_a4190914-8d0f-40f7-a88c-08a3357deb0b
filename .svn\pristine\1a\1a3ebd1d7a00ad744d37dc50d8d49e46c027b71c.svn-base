var ctx = ML.contextPath;
layui.config({
    base : ctx+"/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
        ML.ajax("/sys/org/findAllCompanys",null,function (res) {
            if (res.data.length>0){
                $(".oldGenBill").remove();
                $(".oldreceiver").remove();
                res.data.forEach(function (item) {
                    $("#compNo").append('<option class="compNo" value="'+item.orgCode+'">'+item.orgName+'</option>');
                });

                form.render('select');
            }
        },"GET");

    //获取税率表数据
    table.render({
        id: 'invoiceGrid',
        elem: '#invoiceGrid',
        url: ML.contextPath + '/company/selectCompBank',
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        limit: 50,
        toolbar: '#toolDemo',
        height: 600,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            { type: 'checkbox', width: '3%', fixed: 'left'},
            { title: '序号',type: 'numbers', width: '3%', fixed: 'left'},
            {
                field: 'compName', title: '公司名称', align: 'center', width: "18%", fixed: 'left',
            },
            {
                field: 'bankType', title: '银行', align: 'center', width: "6%",templet:function(d){
                    return ML.dictFormatter('BANK',d.bankType);
                }
            },
            {field: 'bankName', title: '开户行', align: 'center', width: "20%",
            },
            {field: 'openingPlace', title: '开户地', align: 'center', width: "7%",
            },
            {field: 'bankNo', title: '银行账户', align: 'center', width: "10%"},
            {field: 'accountName', title: '账户全称', align: 'center', width: "20%"},
            {field: 'type', title: '类型', align: 'center', width: "5%",templet(d){
                    if (d.type==1){
                        return "通用";
                    }else {
                        return "特殊";
                    }
                }},
            {field: 'custName', title: '客户名称', align: 'center', width: "15%"},
        ]],
        done: function (res) {
            ML.hideNoAuth();
            table.on('toolbar(invoiceFilter)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id), checkData = checkStatus.data;
                switch (obj.event) {
                    case 'delete':
                        //删除
                        if (checkData.length === 0)
                            layer.msg('请选择一行');
                        else {
                           // deleteOneOrMore(checkData);
                        }
                        break;
                    case 'add':
                        editPage("新增",'add',null);
                        break;
                    case 'update':
                        //锁定
                        if (checkData.length === 0)
                            layer.msg('请选择一行');
                        else if (checkData[0].type==1) {
                            editPage("新增",'update',checkData);
                        } else if (checkData[0].type==2) {
                            layer.msg('你选择是特殊类型的银行数据,请点击修改公司特殊银行信息按钮进行修改!');
                        }
                        break;
                    case 'addSpecial':
                        editPage("新增公司特殊银行信息",'addSpecial',null);
                        break;
                    case 'updateSpecial':
                        if (checkData.length === 0)
                            layer.msg('请选择一行');
                        else if (checkData[0].type==2) {
                            editPage("修改公司特殊银行信息",'updateSpecial',checkData);
                        } else if (checkData[0].type==1) {
                            layer.msg('你选择是通用类型的银行数据,请点击修改按钮进行修改!');
                        }
                        break;
                   /* case 'query':
                        //查询
                        if (checkData.length === 0) return layer.msg('请选择一行');
                        else {}
                        break;*/
                }
            });
        }
    });
    table.on('rowDouble(invoiceGrid)', function(obj){
        //obj 同上
        var  url ="/company/lookCompBankPage?id="+obj.data.id+"?optype='look'";
        editOne("查看账户详情",url);
    });
    function editOne(title, url) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['60%', '80%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        });

    }
    function editPage(title, type,data) {
        var url ='';
        switch (type) {
            case 'add':
                url="/company/lookCompBankPage?id=0&optype="+type;
                break;
            case 'update':
                url="/company/lookCompBankPage?id="+data[0].id+"&optype="+type;
                break;
            case 'addSpecial':
                url="/company/gotoAddSpecialCompanyBankPage";
                break;
            case 'updateSpecial':
                url="/company/gotoUpdateSpecialCompanyBankPage?id="+data[0].id;
                break;

        }

        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['55%', '70%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        });

    }
    $("#btnQuery").on('click',function () {
        //alert("666");
        reloadTable();
        return false;
    });
    function reloadTable() {
        param =  serialize("searchForm");
        param.limit = 50;
        param.page = 1;
        table.reload('invoiceGrid', {
            where: param,
            page: { curr: 1 } //重新从第 1 页开始
        });
    }

});