package com.reon.hr.api.customer.vo.salary;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TaxMonthWhiteListVo implements Serializable {
    private Long id;

    private String withholdingAgentNo;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private Integer limit;
    private Integer  page;

    @Excel(name = "客户名称",width = 34)
    private String custName;

    @Excel(name = "合同编号",width = 20)
    private String contractNo;
    private String contractName;
    private Long custId;
    @Excel(name = "扣缴义务人名称",width = 34)
    private String withholdingAgentName;
    @Excel(name = "扣缴义务人类型")
    private String withholdingAgentTypeStr;
    private Integer withholdingAgentType;
    @Excel(name = "工资发放地")
    private String payPlaceStr;
    private String payPlace;
}

