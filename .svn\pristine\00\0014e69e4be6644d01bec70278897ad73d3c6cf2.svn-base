<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../../common/taglibs.jsp" %>
<html>
<head>
  <meta charset="utf-8">
  <title>上传导入数据</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">

  <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>

</head>
<body class="childrenBody">
<form class="layui-form">
  <input type="hidden" name="id" id="templetId" value="${id}">
  <input type="hidden" name="optType" id="optType" value="${optType}">
  <input type="hidden" name="custId" id="custId">


  <div class="layui-form-item layui-form-text">
    <label class="layui-form-label">备注：</label>
    <div class="layui-input-block">
      <textarea id="remark" placeholder="请输入内容" class="layui-textarea"></textarea>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">上传文件：</label>
    <div class="layui-input-block">
      <div class="layui-upload">
        <input type="text" name="title" id="selectFile" lay-verify="title" autocomplete="off" placeholder="请选择文件"
               class="layui-input">
      </div>
    </div>
  </div>

  <div id="uploadLoadingDiv">

  </div>
  <div class="layui-row">
    <div class="layui-col-xs3">
      <div class="grid-demo">
        <button type="button" class="layui-btn" id="upload">开始上传</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
      </div>
    </div>
  </div>

</form>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/cumulativeCalculation/import/batchImportSalary.js?v=${publishVersion}"></script>
</body>
</html>