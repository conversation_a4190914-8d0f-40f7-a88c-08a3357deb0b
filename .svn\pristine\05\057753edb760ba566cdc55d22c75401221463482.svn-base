<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../../common/taglibs.jsp" %>
<html>
<head>
    <title>申请办理</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .layui-table-cell {
            overflow: visible;
        }

        .layui-form-select {
            position: initial;
        }

        .layui-table-box {
            overflow: visible;
        }

        .layui-table-body {
            overflow: visible;
        }

        /* 设置下拉框的高度与表格单元相同 */
        td .layui-form-select {
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
            z-index: 50000;
        }

        .input {
            border: 0px;
        }
    </style>
    <script>
        var billTemplet = [];
    </script>
</head>
<body class="childrenBody">
<div class="layui-tab layui-tab-card" style="margin-top: 0px">
    <input type="hidden" id="practiceId"/>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <form class="layui-form" method="post">
                <div class="layui-form-item" style="margin-top: 10px">
                    <%--隐藏域--%>
                    <input type="hidden" name="id" id="insurancePracticeIdList">
                    <%--表单元素--%>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i style="color: red">*</i>备注：</label>
                        <div class="layui-input-block" style="width: 1166px;">
                        <textarea placeholder="请输入内容" name="firstRemark" id="firstRemark" class="layui-textarea"
                                  style="min-width: 55px" lay-verify="required"></textarea>
                        </div>
                    </div>

                </div>

                <div class="layui-inline" style="margin-left:85%;margin-bottom: 10px;">
                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="save" id="save" type="button">保存
                    </button>
                    <button class="layui-btn layui-btn-primary cancel" type="button" id="cancel">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript"
        src="${ctx}/js/modules/customer/insurancePractice/societyInsurance/societyInsuranceHandle/noRequiredHandle.js?v=${publishVersion}"></script>


</body>
</html>
