/****************************************
 * Copyright (c) 2017 ML.
 * All rights reserved.
 * Created on 2017年6月26日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.common.exception;



public class QysBusinessException extends Exception {
	private static final long serialVersionUID = 1L;

	public QysBusinessException() {
		super();
	}

	public QysBusinessException(String message) {
		super(message);
	}
	
	public QysBusinessException(String message, Throwable cause) {
		super(message, cause);
	}

	public QysBusinessException(Throwable cause) {
		super(cause);
	}

	protected QysBusinessException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
