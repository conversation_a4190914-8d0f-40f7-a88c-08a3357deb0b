package com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee;

import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.salary.SalaryPureAgentVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 纯代发人员信息查询
 * @datetime 2022/9/26 0026 13:36
 * @version: 1.0
 */
@Service
public interface ISalaryEmployeePersonnelInformationWrapperService {

    /**
     * 查询接口
     * @param page
     * @param limit
     * @param paramData
     * @return
     */
    Page<SalaryPureAgentVo> selectSalaryPersonnel(Integer page, Integer limit,String custName,String billMonth,String commissioner,
                                                  String salaryCommissioner,List<OrgPositionDto> userOrgPositionDtoList);

    List<SalaryPureAgentVo> exportSalaryPersonnel( String custName, String billMonth,String commissioner,
                                                   String salaryCommissioner,List<OrgPositionDto> userOrgPositionDtoList);
}
