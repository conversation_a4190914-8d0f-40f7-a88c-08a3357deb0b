/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/4/23 0023
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.ehr.sp.sys.dubbo.rpc.impl.employee;

import com.reon.ehr.api.sys.dubbo.service.rpc.employee.IEpEmpRelativeFileWrapperService;
import com.reon.ehr.api.sys.vo.employee.EhrEmpRelativeFileVo;
import com.reon.ehr.sp.sys.service.order.IEpEmpRelativeFileService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IEpEmpRelativeFileWrapperServiceImpl
 *
 * @date 2021/4/23 0023 16:06
 */
@Service("epEmpRelativeFileWrapperService")
public class IEpEmpRelativeFileWrapperServiceImpl implements IEpEmpRelativeFileWrapperService {
    @Resource
    private IEpEmpRelativeFileService iEpEmpRelativeFileService;
    @Override
    public Boolean addEhrEmpRelativeFile(EhrEmpRelativeFileVo empRelativeFileVo) {
       return iEpEmpRelativeFileService.addEhrEmpRelativeFile(empRelativeFileVo);
    }

    @Override
    public boolean deleteEhrEmpRelativeFile(EhrEmpRelativeFileVo empRelativeFileVo) {
        return iEpEmpRelativeFileService.deleteEhrEmpRelativeFile(empRelativeFileVo);
    }
}
