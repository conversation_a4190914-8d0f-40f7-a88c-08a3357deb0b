layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
  var table = layui.table,
      $ = layui.$,
      form = layui.form,
      layer = layui.layer,
      layer = parent.layer === undefined ? layui.layer : parent.layer;

  // 取消按钮
  $(document).on('click', '#cancel', function () {
    layer.closeAll('iframe');
  });
  setTimeout(function () {
    form.render('select');
  }, 100);
  // 保存按钮
  form.on("submit(save)", function (data) {
    saveForm('save', data);
    return false;
  });


  //手机号和座机号校验
  var checkTel = (value) => {
    // console.log(value);
    if (!value) {
      return false;
    } else {
      const isPhone = /^([0-9]{3,4}-)?[0-9]{7,8}$/; // 0571-86295197
      const isPhone02 = /^\d{3,4}-\d{3,4}-\d{3,4}$/; // 4001-550-520
      const isMob = /^1[0-9]{10}$/;
      // const phone02 = /^0\d{2,3}-?\d{7,8}$/;
      const valuePhone = value.trim();
      if (isMob.test(valuePhone) || isPhone.test(valuePhone) || isPhone02.test(valuePhone)) { // 正则验证
        return true;
      } else {
        return false;
      }
    }
  };

  // 保存数据方法
  function saveForm(type, obj) {
    if (obj.field.employeeName === "") {
      layer.msg("姓名不能为空");
      return false;
    }
    if (ML.isEmpty(obj.field.tel) && ML.isEmpty(obj.field.mobilePhone)) {
      layer.msg("电话和手机号要有一个不为空!");
      return false;
    }


    if (ML.isNotEmpty(obj.field.tel)) {
      if (!checkTel(obj.field.tel)) {
        layer.msg("电话格式不正确!");
        return false;
      }

    }

    if (ML.isNotEmpty(obj.field.mobilePhone)) {
      var phone = /^(0|86|17951)?(11[0-9]|12[0-9]|13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])[0-9]{8}$/;
      if (!(phone.test(obj.field.mobilePhone))) {
        layer.msg("手机号格式不正确!");
        return false;
      }
    }


    $.ajax({
      url: ML.contextPath + "/customer/personOrderQuery/updateEmployeeName",
      type: 'POST',
      dataType: 'json',
      contentType: 'application/json',
      data: JSON.stringify(obj.field),
      success: function (result) {
        if (result) {
          layer.msg(result.msg);
          layer.closeAll('iframe');//关闭弹窗

        } else {
          layer.msg('修改失败！');
        }
      },
      error: function () {
        layer.alert('系统发生异常，请重试！');
      }
    });
  }


});