<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.dao.sys.RoleResourceDao">

  <resultMap id="BaseResultMap" type="com.reon.hr.sp.entity.sys.RoleResource">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ROLE_ID" jdbcType="INTEGER" property="roleId" />
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, ROLE_ID, RESOURCE_ID
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_role_resource
    where ID = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from common_role_resource
    where ID = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.reon.hr.sp.entity.sys.RoleResource">
    insert into common_role_resource (ID, ROLE_ID, RESOURCE_ID
      )
    values (#{id,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}, #{resourceId,jdbcType=VARCHAR}
      )
  </insert>

  <insert id="insertSelective" parameterType="com.reon.hr.sp.entity.sys.RoleResource">
    insert into common_role_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="roleId != null">
        ROLE_ID,
      </if>
      <if test="resourceId != null">
        RESOURCE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="insertList">
    insert into common_role_resource (ROLE_ID, RESOURCE_ID
    )values
    <foreach collection="resourceNoList" item="item" separator=",">
      (#{roleId,jdbcType=INTEGER}, #{item,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.entity.sys.RoleResource">
    update common_role_resource
    <set>
      <if test="roleId != null">
        ROLE_ID = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="resourceId != null">
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.entity.sys.RoleResource">
    update common_role_resource
    set ROLE_ID = #{roleId,jdbcType=INTEGER},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>

</mapper>