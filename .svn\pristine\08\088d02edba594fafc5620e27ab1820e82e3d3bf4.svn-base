package com.reon.hr.sp.bill.service.bill.supplierPractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.vo.bill.SupplierPracticeBillVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.bill.service.bill.supplierPractice
 * @ClassName: ISupplierPracticeBillServiceImpl
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/5 10:50
 * @Version: 1.0
 */
public interface ISupplierPracticeBillService {
    /**
     * 新增供应商实做
     * @param vo
     */
     void insertSupplierReport(SupplierPracticeBillVo vo);

    /**
     * 生成供应商报表
     * @param billId
     */
    void handleGenSupplierBill(Long billId) throws Exception;

    Page<SupplierPracticeBillVo> searchSupplierBill(SupplierPracticeBillVo vo);
    SupplierPracticeBillVo searchSupplierBillByArgs(SupplierPracticeBillVo vo);
    List<SupplierPracticeBillVo> searchSupplierBillGlMonth(SupplierPracticeBillVo vo);

    List<SupplierPracticeBillVo> getByIds(List<Long> id);

    void updateReportToLockByIds(List<Long> id, String loginName, Byte code);


    List<SupplierPracticeBillVo> getSupplierBillBySupIdAndTemId(List<String> tempIdArgs, List<Integer> monthBetween,Integer lockStatus,Integer payStatus);


    List<SupplierPracticeBillVo> getSupplierPracticeBillLock(Integer month,Long supplierId);

    Long getBillIdByBillMonthAndBillMonth(Integer billMonth, Long supplierId,  Long templetId);
}
