package com.reon.hr.sp.bill.dubbo.service.rpc.impl.financial;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.bill.dto.ImportDataDto;
import com.reon.hr.api.bill.dto.PaymentCustomerDto;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.financial.INetSilverWrapperService;
import com.reon.hr.api.bill.enums.PaymentOperationType;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.check.BillCheckApprovalVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.OrgTypeEnum;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.sp.bill.entity.bill.BankInfoAttachment;
import com.reon.hr.sp.bill.entity.bill.PaymentCustomer;
import com.reon.hr.sp.bill.entity.bill.PaymentCustomerLog;
import com.reon.hr.sp.bill.service.bill.financial.INetSilverService;
import com.reon.hr.sp.bill.service.bill.financial.PaymentCustomerLogService;
import com.reon.hr.sp.bill.service.bill.salary.IRecBankRelativeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service("inetSilverWrapperService")
@Slf4j
public class NetSilverWrapperServiceImpl implements INetSilverWrapperService {
    @Autowired
    private INetSilverService iNetSilverService;
    @Resource
    private IUserWrapperService iUserWrapperService;

    @Resource
    private IRecBankRelativeService iRecBankRelativeService;

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Resource
    private PaymentCustomerLogService paymentCustomerLogService;
    @Override
    public Page<BankInfoAttachmentVo> getNetSilverUploadListPage(ParameterVo parameterVo,
                                                                 Integer page, Integer limit) {
        Map<String, Object> map = Maps.newHashMap ();
        map.put ("uploaderName", parameterVo.getUploaderName ());
        map.put ("orgCode", parameterVo.getOrgCode());
        if (StringUtils.isNotEmpty (parameterVo.getStartCreateTime ())) {
            map.put ("startCreateTime", parameterVo.getStartCreateTime () + " 00:00:00");
        }
        if (StringUtils.isNotEmpty (parameterVo.getEndCreateTime ())) {
            map.put ("endCreateTime", parameterVo.getEndCreateTime () + " 23:00:00");
        }
        Page<BankInfoAttachmentVo> pages = new Page<> (page, limit);
        List<BankInfoAttachmentVo> bankInfoAttachmentListPage = iNetSilverService.getBankInfoAttachmentListPage (pages, map);
        pages.setRecords (bankInfoAttachmentListPage);
        return pages;
    }

    @Override
    public Page<NetSilverQueryVo> getNetSilverQueryListPage(ParameterVo parameterVo, Integer page, Integer limit) {
        Page<NetSilverQueryVo> pages = new Page<> (page, limit);
        Integer companyFlag = parameterVo.getCompanyFlag();
        if(companyFlag != null){
            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionByOrgType(OrgTypeEnum.PART_COMPANY_DT.getCode());
            List<String> orgNames = orgVos.stream().map(OrgVo::getOrgName).distinct().collect(Collectors.toList());
            parameterVo.setOrgNames(orgNames);
        }
        List<NetSilverQueryVo> netSilverQueryListPage = iNetSilverService.getNetSilverQueryListPage (pages, parameterVo);
        List<NetSilverQueryVo> netSilverQueryVos = setValue(netSilverQueryListPage);
        pages.setRecords (netSilverQueryVos);
        return pages;
    }
    @Override
    public List<NetSilverQueryVo> getNetSilverQueryList(ParameterVo parameterVo) {
        Map<String, Object> map = Maps.newHashMap ();
        map.put ("orgName", parameterVo.getOrgName ());
        map.put ("payCustName", parameterVo.getPayCustName ());
        if (StringUtils.isNotEmpty (parameterVo.getStartPayTime ())) {
            map.put ("startPayTime", parameterVo.getStartPayTime () + " 00:00:00");
        }
        if (StringUtils.isNotEmpty (parameterVo.getEndPayTime ())) {
            map.put ("endPayTime", parameterVo.getEndPayTime () + " 23:00:00");
        }
        map.put ("startPayAmt", parameterVo.getStartPayAmt ());
        map.put ("endPayAmt", parameterVo.getEndPayAmt ());
        map.put ("startBalance", parameterVo.getStartBalance ());
        map.put ("endBalance", parameterVo.getEndBalance ());
        map.put ("startUncheckAmt", parameterVo.getStartUncheckAmt ());
        map.put ("endUncheckAmt", parameterVo.getEndUncheckAmt ());
        if (StringUtils.isNotEmpty (parameterVo.getStartCreateTime ())) {
            map.put ("startCreateTime", parameterVo.getStartCreateTime () + " 00:00:00");
        }
        if (StringUtils.isNotEmpty (parameterVo.getEndCreateTime ())) {
            map.put ("endCreateTime", parameterVo.getEndCreateTime () + " 23:00:00");
        }
        map.put ("status", parameterVo.getStatus ());
        map.put ("freezeFlag", parameterVo.getFreezeFlag ());
//        if(!parameterVo.getCityCodes ().isEmpty ()){
//            map.put ("cityCodes", parameterVo.getCityCodes ());
//        }
        Integer companyFlag = parameterVo.getCompanyFlag();
        if(companyFlag != null){
            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionByOrgType(OrgTypeEnum.PART_COMPANY_DT.getCode());
            List<String> orgNames = orgVos.stream().map(OrgVo::getOrgName).distinct().collect(Collectors.toList());
            parameterVo.setOrgNames(orgNames);
        }
        List<NetSilverQueryVo> netSilverQueryList = iNetSilverService.getNetSilverQueryList(parameterVo);
        List<NetSilverQueryVo> netSilverQueryVos = setValue(netSilverQueryList);
        Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
        for (NetSilverQueryVo netSilverQueryVo : netSilverQueryVos) {
            netSilverQueryVo.setUploaderName(allUserName.get(netSilverQueryVo.getUploaderName()));
        }
        return netSilverQueryVos;
    }

    public List<NetSilverQueryVo> setValue(List<NetSilverQueryVo> netSilverQueryList){
        if (CollectionUtils.isNotEmpty(netSilverQueryList)){
            Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
            List<Long> payCustIdList = netSilverQueryList.stream().map(NetSilverQueryVo::getId).collect(Collectors.toList());
            List<BillCheckApprovalVo> netSilverCheckedQueryListByPayCustIdList = iNetSilverService.getNetSilverCheckedQueryListByPayCustIdList(payCustIdList);
            if (CollectionUtils.isNotEmpty(netSilverCheckedQueryListByPayCustIdList)){
                Map<Long, List<BillCheckApprovalVo>> payCustIdMap = netSilverCheckedQueryListByPayCustIdList.stream().collect(Collectors.groupingBy(BillCheckApprovalVo::getCheckId));
                for (NetSilverQueryVo netSilverQueryVo : netSilverQueryList) {
                    List<BillCheckApprovalVo> billCheckApprovalVos = payCustIdMap.get(netSilverQueryVo.getId());
                    if (CollectionUtils.isNotEmpty(billCheckApprovalVos)){
                        BillCheckApprovalVo billCheckApprovalVo = billCheckApprovalVos.stream().max(Comparator.comparing(BillCheckApprovalVo::getCheckDate)).get();
                        netSilverQueryVo.setFinance(allUserName.get(billCheckApprovalVo.getFinance()));
                    }
                }
            }
        }
        return netSilverQueryList;
    }

    @Override
    public int saveBankInfoAttachment(BankInfoAttachmentVo record) {
        BankInfoAttachment bankInfoAttachment = new BankInfoAttachment ();
        BeanUtils.copyProperties (record, bankInfoAttachment);
        return iNetSilverService.insertSelective (bankInfoAttachment);
    }

    @Override
    public int batchPaymentCustomerVo(List<PaymentCustomerVo> record) {
        List<PaymentCustomer> list = Lists.newArrayList ();
        record.forEach (r -> {
            PaymentCustomer paymentCustomer = new PaymentCustomer ();
            BeanUtils.copyProperties (r, paymentCustomer);
            list.add (paymentCustomer);
        });
        return iNetSilverService.batchInsert (list);
    }

    @Override
    public boolean saveRemark(PaymentCustomerVo paymentCustomerVo) {
        return iNetSilverService.saveRemark (paymentCustomerVo);
    }


    @Override
    public boolean updateStatus(String ids, Integer freezeFlag, Integer status, String loginName, String remark) {
        List<PaymentCustomerVo> paymentCustomerVos = Lists.newArrayList ();
        List<Long> list = JSON.parseArray (ids, Long.class);
        list.forEach (id -> {
            //添加到款客户信息
            PaymentCustomerVo paymentCustomerVo = new PaymentCustomerVo ();
            paymentCustomerVo.setId (id);
            paymentCustomerVo.setFreezeFlag (freezeFlag);
            paymentCustomerVo.setStatus (status);
            paymentCustomerVo.setUpdater (loginName);
            //添加到款客户日志信息

            PaymentCustomer paymentCustomer = iNetSilverService.selectById (id);
            if (paymentCustomer == null) {
                paymentCustomerVos.add (paymentCustomerVo);
            } else {
                if (paymentCustomer.getFreezeFlag () != freezeFlag && status == null) {
                    paymentCustomerVos.add (paymentCustomerVo);
                }
                if (paymentCustomer.getStatus () != status && freezeFlag == null) {
                    paymentCustomerVos.add (paymentCustomerVo);

                }
            }
        });
        if (!paymentCustomerVos.isEmpty ()) {
             List<PaymentCustomerLog> paymentCustomerLogList = Lists.newArrayList();
            iNetSilverService.updateStatus (paymentCustomerVos);
            try{
            for (PaymentCustomerVo paymentCustomerVo : paymentCustomerVos) {
                PaymentCustomerLog paymentCustomerLog = new PaymentCustomerLog();
                paymentCustomerLog.setPayCustId(paymentCustomerVo.getId());
                if (freezeFlag != null) {
                    if (freezeFlag == 2 && status == null) {
                        // 冻结操作：freezeFlag=2 且 status为空
                        paymentCustomerLog.setOprType(PaymentOperationType.FREEZE.getCode());
                        paymentCustomerLog.setFreezeRemark(remark); // 冻结操作时，将备注设置到freezeRemark字段
                    } else if (freezeFlag == 1 && status == null) {
                        // 解冻操作：freezeFlag=1 且 status为空
                        paymentCustomerLog.setOprType(PaymentOperationType.UNFREEZE.getCode());
                        paymentCustomerLog.setRemark(remark); // 解冻操作时，将备注设置到remark字段
                    }
                } else if (status != null) {
                    if (status == 2) {
                        // 作废操作：status=2 且 freezeFlag为空
                        paymentCustomerLog.setOprType(PaymentOperationType.INVALIDATE.getCode());
                        paymentCustomerLog.setRemark(remark); // 作废操作时，将备注设置到remark字段
                    } else if (status == 1) {
                        // 恢复有效操作：status=1 且 freezeFlag为空
                        paymentCustomerLog.setOprType(PaymentOperationType.RESTORE.getCode());
                        paymentCustomerLog.setRemark(remark); // 恢复有效操作时，将备注设置到remark字段
                    }
                }
                paymentCustomerLog.setCreator(loginName);
                paymentCustomerLogList.add(paymentCustomerLog);
            }
            if (CollectionUtils.isNotEmpty(paymentCustomerLogList)) paymentCustomerLogService.insertBatch (paymentCustomerLogList);
            }catch (Exception e){
                log.error("到款 操作保存日志失败:{}",paymentCustomerVos.toString());
            }
        }
        return true;
    }

    @Override
    public Page<PaymentCustomerLogVo> getNetSilverOperatingHistoryLogListPage(Long payCustId, String loginName, Integer page, Integer limit) {
        Map<String, Object> map = Maps.newHashMap ();
        map.put ("payCustId", payCustId);
        map.put ("loginName", loginName);
        Page<PaymentCustomerLogVo> pages = new Page<> (page, limit);
        List<PaymentCustomerLogVo> netSilverQueryListPage = iNetSilverService.getNetSilverOperatingHistoryLogList (pages, map);
        Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
        for (PaymentCustomerLogVo paymentCustomerLogVo : netSilverQueryListPage) {
            paymentCustomerLogVo.setCreator(allUserName.getOrDefault(paymentCustomerLogVo.getCreator(),paymentCustomerLogVo.getCreator()));
        }
        pages.setRecords (netSilverQueryListPage);
        return pages;
    }

    @Override
    public Page<BillCheckApprovalVo> getNetSilverCheckedQueryListPage(String prjCsLoginName, String financelLoginName,Integer payCustId, Integer page, Integer limit) {
        Map<String, Object> map = Maps.newHashMap ();
        map.put ("prjCsLoginName", prjCsLoginName);
        map.put ("financelLoginName", financelLoginName);
        map.put ("payCustId", payCustId);
        Page<BillCheckApprovalVo> pages = new Page<> (page, limit);
        List<BillCheckApprovalVo> netSilverQueryListPage = iNetSilverService.getNetSilverCheckedQueryListPage (pages, map);
        pages.setRecords (netSilverQueryListPage);
        return pages;
    }

    @Override
    public ImportDataDto<PaymentCustomerDto> batchInsertPaymentCustomer(ImportDataDto<PaymentCustomerDto> importDataDto, String orgCode) throws Exception {
      return  iNetSilverService.batchInsertPaymentCustomer(importDataDto,orgCode);
    }

    @Override
    public void handleSyncBankLogs(String searchTime,String hasSearch) {
        iNetSilverService.handleSyncBankLogs(searchTime,hasSearch);
    }

    @Override
    public void handlePayCustomerMatchSalrayRece(String loginName) {
        iNetSilverService.handlePayCustomerMatchSalrayRece(loginName);
    }

}
