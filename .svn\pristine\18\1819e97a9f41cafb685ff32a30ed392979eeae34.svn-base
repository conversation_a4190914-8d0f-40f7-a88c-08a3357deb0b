<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /*去掉type=number时的上下加减按钮*/
        /* 谷歌 */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }

        /* 火狐 */
        input {
            -moz-appearance: textfield;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post" id="searchForm">
    <table class="layui-table" lay-skin="nob" style="width: 100%;">
        <tr>
            <th style=" font-size: 1.5em;"><b>回调地址(${environment})</b></th>
            <th style=" font-size: 1.3em;"><b>${url}</b></th>
        </tr>
        <br>
        <tr>
            <th style=" font-size: 1.5em;"><b>契约锁字段(用于填充到模板)</b></th>
            <th style=" font-size: 1.5em;"><b>备注</b></th>
            <th style=" font-size: 1.5em;"><b>所属库表</b></th>
        </tr>
        <c:forEach var="data" items="${dataList}">
            <tr>
                <th width="20%" style="background: #F5F5F5;font-size: 1.3em;"><strong>{{${data.qysFileField}}}</strong>
                </th>
                <th width="50%">${data.commentForField}</th>
                <th width="30%">${data.belongDatabaseTable} </th>
            </tr>
        </c:forEach>
    </table>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
</body>
</html>