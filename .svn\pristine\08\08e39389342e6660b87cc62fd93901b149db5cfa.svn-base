package com.reon.hr.modules.sys.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.ehr.api.sys.dubbo.service.rpc.IEpEmployeeOrderWrapperService;
import com.reon.ehr.api.sys.enums.order.StaffingState;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IMessageWrapperService;
import com.reon.hr.api.base.vo.MessageVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillDealWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillDisposableWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.salary.ISupplierSalaryBillWrapperService;
import com.reon.hr.api.bill.enums.CommonBooleanTypeEnum;
import com.reon.hr.api.bill.enums.SupplierVerificationStatus;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo;
import com.reon.hr.api.common.ParamInterface;
import com.reon.hr.api.customer.constant.ProcessStatus;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.salary.TaxComparisonFeedbackRecordImportDto;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAssignResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IQuotationResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.salaryPayment.ITaxComparisonWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IPersonOrderQueryWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.qiyuesuo.IQysContractTableWrapperService;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.salary.FeedbackStatus;
import com.reon.hr.api.customer.vo.ContractAssignVo;
import com.reon.hr.api.customer.vo.EditContractInfoWorkflowVo;
import com.reon.hr.api.customer.vo.employee.CompleteOrderViewVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.PersonOrderEditVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysContractApprovalVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.ListUtils;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dto.TaskQueryDTO;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.api.workflow.vo.WorkFlowCountVo;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.core.utils.StringUtil;
import com.reon.hr.modules.common.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年03月11日
 * @Version 1.0
 */
@RestController
@RequestMapping("/sys/workflowCount")
@Slf4j
public class WorkFlowCountController extends BaseController {

    private static final String QUOTATION = "quotation";
    private static final String CONTRACT_PROCESS = "contract_process";
    private static final String SUPPLIER_CONTRACT_PROCESS = "supplier_contract_process";

    @Autowired
    private ISupplierWrapperService supplierWrapperService;
    @Autowired
    IInsuranceBillDealWrapperService iInsuranceBillDealWrapperService;
    @Resource(name = "workflowDubboService")
    private IWorkflowWrapperService workflowWrapperService;

    @Resource(name = "userOrgPosDubboService")
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource(name = "quotationDubboService")
    private IQuotationResourceWrapperService quotationResourceWrapperService;

    @Resource(name = "contractDubboService")
    private IContractResourceWrapperService contractResourceWrapperService;

    @Resource(name = "paymentApplyDubboService")
    private IPaymentApplyWrapperService iPaymentApplyWrapperService;
    @Resource
    private ICustomerWrapperService customerWrapperService;
    @Resource(name = "contractAssignDubboService")
    private IContractAssignResourceWrapperService contractAssignResourceWrapperService;

    @Resource(name = "personOrderQueryDubboService")
    private IPersonOrderQueryWrapperService personOrderQueryWrapperService;
    @Autowired
    private IEmployeeOrderWrapperService employeeOrderService;

    @Autowired
    private ISalaryPayWrapperService iSalaryPayWrapperService;
    @Autowired
    private ITaxComparisonWrapperService taxComparisonWrapperService;

    @Autowired
    private IUserWrapperService iUserWrapperService;
    @Autowired
    private ISupplierSalaryBillWrapperService supplierSalaryBillWrapperService;
    @Autowired
    private IEpEmployeeOrderWrapperService epEmployeeOrderWrapperService;
    @Autowired

    private IInsuranceBillDisposableWrapperService insuranceBillDisposableWrapperService;
    @Resource
    private IQysContractTableWrapperService iQysContractTableWrapperService;

    @RequestMapping("/getAllTaskCount")
    public LayuiReplay getAllTaskCount(HttpSession session){
        List<String> urlByLoginName = iUserWrapperService.getUrlByLoginName(getSessionUser().getLoginName());
        Map<String, String> keyAndTextMap = keyAndTextMap();
        Map<String, ParamInterface> paramInterfaceMap = initWorkflowTypeAndMapperMap();
        int count =0;
        List<WorkFlowCountVo> workFlowCountVos = new ArrayList<>();
        for (ReonWorkflowType value : ReonWorkflowType.values()) {
            WorkFlowCountVo workFlowCountVo = new WorkFlowCountVo();
            List<TaskVo> taskListWithoutPageConditionForLoginName = getTaskVos(value.getCode());
            List<TaskVo> taskVoList = new ArrayList<>();
            Map<String, TaskVo> taskVoMap = Maps.newHashMap();
            List<String> pidList = Lists.newArrayList();
            taskListWithoutPageConditionForLoginName.forEach(taskVo -> {
                pidList.add(taskVo.getProcessInstanceId());
                taskVoMap.put(taskVo.getProcessInstanceId(), taskVo);
            });
            ParamInterface paramInterface = paramInterfaceMap.get(value.getCode());
            if (paramInterface != null&&CollectionUtils.isNotEmpty(pidList)) {
                List<String> pidListCount = paramInterface.myMethod(pidList);
                if (CollectionUtils.isNotEmpty(pidListCount)){
                    workFlowCountVo.setNum(pidListCount.size());
                    workFlowCountVo.setKey(value.getCode());
                    workFlowCountVo.setKeyName(keyAndTextMap.get(value.getCode()));
                    count = count+pidListCount.size();
                }

            } else if (Objects.equals(ReonWorkflowType.PAYMENT_APPLY.getCode(), value.getCode())) {
                List<PaymentApplyVo> practIcePaymentApplyList = iPaymentApplyWrapperService.getPractIcePaymentApplyListByAppStatus();
                practIcePaymentApplyList.forEach(item -> {
                    if (taskVoMap.containsKey(item.getPid())) {
                        TaskVo taskVo = taskVoMap.get(item.getPid());
                        taskVoList.add(taskVo);
                    }
                });
                if (CollectionUtils.isNotEmpty(taskVoList)){
                    workFlowCountVo.setKey(value.getCode());
                    workFlowCountVo.setKeyName(keyAndTextMap.get(value.getCode()));
                    workFlowCountVo.setNum(taskVoList.size());
                    count = count+taskVoList.size();
                }
            }else if (value.getCode().equals(ReonWorkflowType.SALARY_PAYMENT_APPLY.getCode())&&CollectionUtils.isNotEmpty(pidList)){
                if (!urlByLoginName.contains(keyAndTextMap.get(value.getCode()))){
                    continue;
                }
                List<PaymentApplyVo> paymentApplyList = iPaymentApplyWrapperService.getPaymentApplyListByPidList (pidList);
                if (CollectionUtils.isNotEmpty(paymentApplyList)){
                    count = count+paymentApplyList.size();
                    workFlowCountVo.setKey(value.getCode());
                    workFlowCountVo.setKeyName(keyAndTextMap.get(value.getCode()));
                    workFlowCountVo.setNum(paymentApplyList.size());
                }
            }
            workFlowCountVos.add(workFlowCountVo);
        }
        List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>)session.getAttribute(Constants.SESSION_ORG_POSITION);
        ContractAssignVo contractAssignVo = new ContractAssignVo();
        List<Integer> list = Lists.newArrayList(ContractType.SOCIAL_SALARY_LIST);
        list.addAll(ContractType.DISPOSABLE_SERVICE_LIST_NOT_EXAMINATION);
        contractAssignVo.setContractTypeList(list);
        contractAssignVo.setUserOrgPositionDtoList(userOrgPositionDtoList);
        contractAssignVo.setIfNoCommercialInsurance("是");
        contractAssignVo.setDistributionFlag(0);
        List<OrgPositionDto> userOrgPosList = ListUtils.copyListBean(userOrgPositionDtoList, OrgPositionDto.class);
        List<String> posCodeList = userOrgPosList.stream().map(OrgPositionDto::getPosCode).collect(Collectors.toList());

        if (!posCodeList.contains(PositionEnum.PROJECT_VIRTUAL_VP.getCode())&&!posCodeList.contains(PositionEnum.PROJECT_VP.getCode())){
            if (urlByLoginName.contains("分配小合同")){
                List<ContractAssignVo> contractAreaWorkFlow = contractAssignResourceWrapperService.getContractAreaWorkFlow(contractAssignVo);
                if (CollectionUtils.isNotEmpty(contractAreaWorkFlow)){
                    if (CollectionUtils.isNotEmpty(contractAreaWorkFlow)){
                        WorkFlowCountVo contractAssignWorkFlowCountVo = new WorkFlowCountVo();
                        contractAssignWorkFlowCountVo.setKey("10");
                        contractAssignWorkFlowCountVo.setKeyName("分配小合同");
                        contractAssignWorkFlowCountVo.setNum(contractAreaWorkFlow.size());
                        workFlowCountVos.add(contractAssignWorkFlowCountVo);
                        count = count+contractAreaWorkFlow.size();
                    }
                }
            }
            if (urlByLoginName.contains("分配大合同")){
                List<ContractAssignVo> contractWorkFlow = contractAssignResourceWrapperService.getContractWorkFlow(contractAssignVo);
                if (CollectionUtils.isNotEmpty(contractWorkFlow)){
                    WorkFlowCountVo contractWorkFlowCountVo = new WorkFlowCountVo();
                    contractWorkFlowCountVo.setKey("11");
                    contractWorkFlowCountVo.setKeyName("分配大合同");
                    contractWorkFlowCountVo.setNum(contractWorkFlow.size());
                    workFlowCountVos.add(contractWorkFlowCountVo);
                    count = count+contractWorkFlow.size();
                }
            }
            if (urlByLoginName.contains("合同分配薪资客服")){
                contractAssignVo.setSalaryDispatchFlag(0);
                List<ContractAssignVo> contractWorkFlow = contractAssignResourceWrapperService.getContractSalaryWorkFlow(contractAssignVo);
                if (CollectionUtils.isNotEmpty(contractWorkFlow)){
                    WorkFlowCountVo contractWorkFlowCountVo = new WorkFlowCountVo();
                    contractWorkFlowCountVo.setKey("27");
                    contractWorkFlowCountVo.setKeyName("合同分配薪资客服");
                    contractWorkFlowCountVo.setNum(contractWorkFlow.size());
                    workFlowCountVos.add(contractWorkFlowCountVo);
                    count = count+contractWorkFlow.size();
                }
            }
        }

        if (urlByLoginName.contains("变更订单确认")){
            PersonOrderEditVo personOrderEditVo = new PersonOrderEditVo();
            personOrderEditVo.setUserOrgPositionDtoList(userOrgPositionDtoList);
            List<PersonOrderQueryDto> orderQueryDtoList = personOrderQueryWrapperService.getListChangeOrderConfirmationPage(personOrderEditVo, getSessionUser().getLoginName());
            if (CollectionUtils.isNotEmpty(orderQueryDtoList)){
                WorkFlowCountVo orderChangeWorkFlowCountVo = new WorkFlowCountVo();
                orderChangeWorkFlowCountVo.setKey("12");
                orderChangeWorkFlowCountVo.setKeyName("变更订单确认");
                orderChangeWorkFlowCountVo.setNum(orderQueryDtoList.size());
                workFlowCountVos.add(orderChangeWorkFlowCountVo);
                count = count+orderQueryDtoList.size();
            }
        }
        CompleteOrderViewVo completeOrderViewVo = new CompleteOrderViewVo();
        completeOrderViewVo.setOrderFlag(3);
        completeOrderViewVo.setLoginName(getSessionUser().getLoginName());
        completeOrderViewVo.setUserOrgPositionDtoList(userOrgPositionDtoList);
        if (urlByLoginName.contains("完善个人订单")){
            List<CompleteOrderViewVo> completeOrderListWorkFow = employeeOrderService.getCompleteOrderListWorkFow(completeOrderViewVo);
            if (CollectionUtils.isNotEmpty(completeOrderListWorkFow)){
                WorkFlowCountVo orderCompleteWorkFlowCountVo = new WorkFlowCountVo();
                orderCompleteWorkFlowCountVo.setKey("13");
                orderCompleteWorkFlowCountVo.setKeyName("完善个人订单");
                orderCompleteWorkFlowCountVo.setNum(completeOrderListWorkFow.size());
                workFlowCountVos.add(orderCompleteWorkFlowCountVo);
                count = count+completeOrderListWorkFow.size();
            }
        }

        completeOrderViewVo.setOrderFlag(5);
        if (urlByLoginName.contains("确认个人订单")){
            List<CompleteOrderViewVo> completeOrderListWorkFow1 = employeeOrderService.getCompleteOrderListWorkFow(completeOrderViewVo);
            if (CollectionUtils.isNotEmpty(completeOrderListWorkFow1)){
                WorkFlowCountVo orderCompleteWorkFlowCountVo = new WorkFlowCountVo();
                orderCompleteWorkFlowCountVo.setKey("14");
                orderCompleteWorkFlowCountVo.setKeyName("确认个人订单");
                orderCompleteWorkFlowCountVo.setNum(completeOrderListWorkFow1.size());
                workFlowCountVos.add(orderCompleteWorkFlowCountVo);
                count = count+completeOrderListWorkFow1.size();
            }
        }

        if (urlByLoginName.contains("退票录入信息查询")){
            int i = iSalaryPayWrapperService.selectSalaryPaymentRefundInputCount(getSessionUser().getLoginName());
            if (i!=0){
                WorkFlowCountVo salaryWorkFlowCountVo = new WorkFlowCountVo();
                salaryWorkFlowCountVo.setKey("15");
                salaryWorkFlowCountVo.setKeyName("退票录入信息查询");
                salaryWorkFlowCountVo.setNum(i);
                workFlowCountVos.add(salaryWorkFlowCountVo);
                count = count+i;
            }
        }
        int i = employeeOrderService.getCountByCommissionerOrReceivingMan(getSessionUser().getLoginName(), 7);
        if (i!=0){
            WorkFlowCountVo salaryWorkFlowCountVo = new WorkFlowCountVo();
            salaryWorkFlowCountVo.setKey("16");
            salaryWorkFlowCountVo.setKeyName("接单确认员工离职");
            salaryWorkFlowCountVo.setNum(i);
            workFlowCountVos.add(salaryWorkFlowCountVo);
            count = count+i;
        }
        int countByCommissionerOrReceivingMan = employeeOrderService.getCountByCommissionerOrReceivingMan(getSessionUser().getLoginName(), 8);
        if (countByCommissionerOrReceivingMan!=0){
            WorkFlowCountVo salaryWorkFlowCountVo = new WorkFlowCountVo();
            salaryWorkFlowCountVo.setKey("17");
            salaryWorkFlowCountVo.setKeyName("项目确认员工离职");
            salaryWorkFlowCountVo.setNum(countByCommissionerOrReceivingMan);
            workFlowCountVos.add(salaryWorkFlowCountVo);
            count = count+countByCommissionerOrReceivingMan;
        }

        if (urlByLoginName.contains("供应商工资账单管理")){
            String loginName = getSessionUser().getLoginName();
            Map<String, Object> queryParams = supplierSalaryBillWrapperService.getQueryParams(null, null,null, SupplierVerificationStatus.PUSHED.getCode(), loginName,new ArrayList<>());
            queryParams.put("supplierCommissioner",loginName);
            List<SupplierSalaryBillVo> supplierSalaryBillVoList = supplierSalaryBillWrapperService.getListByParams(queryParams);
            if (CollectionUtils.isNotEmpty(supplierSalaryBillVoList)){
                WorkFlowCountVo salaryWorkFlowCountVo = new WorkFlowCountVo();
                salaryWorkFlowCountVo.setKey("18");
                salaryWorkFlowCountVo.setKeyName("供应商工资账单管理");
                salaryWorkFlowCountVo.setNum(supplierSalaryBillVoList.size());
                workFlowCountVos.add(salaryWorkFlowCountVo);
                count = count+supplierSalaryBillVoList.size();
            }
        }
            Map<String, Object> conditionMap = Maps.newHashMap();
            conditionMap.put("receiver",getSessionUser().getLoginName());
            conditionMap.put("readFlag","1");
            conditionMap.put("msgType","12");
            List<MessageVo> messageVoList = iMessageWrapperService.getListMessageByMap(conditionMap);
            if (CollectionUtils.isNotEmpty(messageVoList)){
                WorkFlowCountVo orderCompleteWorkFlowCountVo = new WorkFlowCountVo();
                orderCompleteWorkFlowCountVo.setKey("19");
                orderCompleteWorkFlowCountVo.setKeyName("合同分配后自动新建ehr账号提醒");
                orderCompleteWorkFlowCountVo.setNum(messageVoList.size());
                workFlowCountVos.add(orderCompleteWorkFlowCountVo);
                count = count+messageVoList.size();
            }

        int rejectOrderCount = employeeOrderService.getRejectOrderCountByLoginName(getSessionUser().getLoginName());
        if (rejectOrderCount!=0){
            WorkFlowCountVo salaryWorkFlowCountVo = new WorkFlowCountVo();
            salaryWorkFlowCountVo.setKey("20");
            salaryWorkFlowCountVo.setKeyName("生成个人订单");
            salaryWorkFlowCountVo.setNum(rejectOrderCount);
            workFlowCountVos.add(salaryWorkFlowCountVo);
            count = count+rejectOrderCount;
        }


        Integer ehrAddCount = epEmployeeOrderWrapperService.selectOrderResult(getSessionUser().getLoginName(),Lists.newArrayList(StaffingState.SUBMITTED.getCode()));
        if(ehrAddCount!=0){
            WorkFlowCountVo ehrAdd = new WorkFlowCountVo();
            ehrAdd.setKey("21");
            ehrAdd.setKeyName("ehr增员待处理");
            ehrAdd.setNum(ehrAddCount);
            workFlowCountVos.add(ehrAdd);
            count = count+ehrAddCount;
        }
        Integer ehrLeaveCount = epEmployeeOrderWrapperService.selectOrderResult(getSessionUser().getLoginName(),Lists.newArrayList(StaffingState.REDUCTION_SUBMIT.getCode()));
        if(ehrLeaveCount>0){
            WorkFlowCountVo ehrLeave = new WorkFlowCountVo();
            ehrLeave.setKey("22");
            ehrLeave.setKeyName("ehr减员待处理");
            ehrLeave.setNum(ehrLeaveCount);
            workFlowCountVos.add(ehrLeave);
            count = count+ehrLeaveCount;
        }
        /** 账单一次性审批 23 */
        List<OrgPositionDto> listData = (List<OrgPositionDto>) session.getAttribute(Constants.SESSION_ORIGIN_ORG_POSITION);
        List<OrgPositionDto> userOrgPositionDtoListAddVir   = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(listData)){userOrgPositionDtoListAddVir.addAll(listData);}
//        List<OrgPositionDto> virtualPos = (List) session.getAttribute(Constants.SESSION_VIRTUAL_ORG_POSITION);
//        if(CollectionUtils.isNotEmpty(virtualPos)){
//            userOrgPositionDtoListAddVir.addAll(virtualPos);
//        }
        Integer billApproCount = insuranceBillDisposableWrapperService.getAllBillDisposableApprovalByUOP(userOrgPositionDtoListAddVir);
        if (billApproCount > 0) {
            WorkFlowCountVo billAppro = new WorkFlowCountVo();
            billAppro.setKey("23");
            billAppro.setKeyName("账单一次性审批");
            billAppro.setNum(billApproCount);
            workFlowCountVos.add(billAppro);
            count = count + billApproCount;
        }
        /** 账单解锁审批 24 */

        Set<String> approvalSet = Sets.newHashSet();
        for (String posCode : getSessionUser().getPosAndCityMap().keySet()) {
            List<String> orgAndPosCodeList = getSessionUser().getOrgPosVos().stream().filter(item -> item.getPosCode().equals(posCode)).map(item -> item.getOrgCode() + "," + item.getPosCode()).collect(toList());
            approvalSet.addAll(orgAndPosCodeList);
        }
       Integer billBlockCount = iInsuranceBillDealWrapperService.getContractTaskList(Maps.newHashMap(), approvalSet, 1, 10000).getRecords().size();
        if (billBlockCount > 0) {
            WorkFlowCountVo billBlock = new WorkFlowCountVo();
            billBlock.setKey("24");
            billBlock.setKeyName("账单解锁审批");
            billBlock.setNum(billBlockCount);
            workFlowCountVos.add(billBlock);
            count = count + billBlockCount;
        }

        /** 合同特殊审批 25 */
        Map<String, Object> stringObjectMap = Maps.newHashMap();
        List<TaskVo> taskListWithoutPageConditionForLoginName = getTaskVos(ReonWorkflowType.CHANGE_CONTRACT_ITEM_FLOW.getCode());
        List<TaskVo> taskVoList = new ArrayList<TaskVo>();
        Map<String, TaskVo> taskVoMap = Maps.newHashMap();
        ArrayList<String> pidList = Lists.newArrayList();
        taskListWithoutPageConditionForLoginName.forEach(taskVo -> {
            taskVo.setAssignee(getLoginName(taskVo.getAssignee()));
            pidList.add(taskVo.getProcessInstanceId());
            taskVoMap.put(taskVo.getProcessInstanceId(), taskVo);
        });
        stringObjectMap.put("pidList", pidList);

        List<EditContractInfoWorkflowVo> contractList = contractResourceWrapperService.getSpecialContractListByPid(stringObjectMap);
        contractList.forEach(conVo -> {
            if (taskVoMap.containsKey(conVo.getPid())) {
                TaskVo taskVo = taskVoMap.get(conVo.getPid());
                taskVo.setName(conVo.getName());
                taskVo.setType(conVo.getType());
                taskVo.setApprovalStatus(conVo.getApprovalStatus());
                taskVo.setECIWId(conVo.getId());
                taskVo.setCreator(conVo.getCreator());
                taskVoList.add(taskVo);
            }else if(conVo.getApprovalStatus()==ProcessStatus.REJECTED.getCode()&&getSessionUser().getLoginName().equals(conVo.getCreator())){
                TaskQueryDTO taskQueryDTO = new TaskQueryDTO(ReonWorkflowType.CHANGE_CONTRACT_ITEM_FLOW.getDefineKey(), ReonWorkflowType.CHANGE_CONTRACT_ITEM_FLOW.getBussinessKey(), null, conVo.getPid());
                TaskVo task = workflowWrapperService.getOneTask(taskQueryDTO);
                if(task!=null){
                    task.setName(conVo.getName());
                    task.setType(conVo.getType());
                    task.setApprovalStatus(conVo.getApprovalStatus());
                    task.setECIWId(conVo.getId());
                    task.setCreator(conVo.getCreator());
                    taskVoList.add(task);
                }
            }
        });
        Integer contractSpecialCount = taskVoList.size();
        if (contractSpecialCount > 0) {
            WorkFlowCountVo contractSpecial = new WorkFlowCountVo();
            contractSpecial.setKey("25");
            contractSpecial.setKeyName("合同特殊审批");
            contractSpecial.setNum(contractSpecialCount);
            workFlowCountVos.add(contractSpecial);
            count = count + contractSpecialCount;
        }

        /** 契约锁审批 26 */
        Map<String,Object> qysConMap = Maps.newHashMap();
        qysConMap.put("loginName", getSessionUser().getLoginName());
        Page<QysContractApprovalVo> result = iQysContractTableWrapperService.getQysTaskList(qysConMap);
        Integer qysCount = result.getRecords().size();
        if (qysCount > 0) {
            WorkFlowCountVo qysWorkflow = new WorkFlowCountVo();
            qysWorkflow.setKey("26");
            qysWorkflow.setKeyName("契约锁审批");
            qysWorkflow.setNum(qysCount);
            workFlowCountVos.add(qysWorkflow);
            count = count + qysCount;
        }

        /** 代理员工离职资料 未上传 */
        Map<String, Object> agentEmployeeDimInformationMap = Maps.newHashMap();
        agentEmployeeDimInformationMap.put("loginName", getSessionUser().getLoginName());
        List<EmployeeOrderVo> agentEmployeeDimInformationList =personOrderQueryWrapperService.getAgentEmployeeDimInformation(agentEmployeeDimInformationMap);
        if (agentEmployeeDimInformationList!=null&&agentEmployeeDimInformationList.size() > 0) {
            WorkFlowCountVo agentEmployeeWorkflow = new WorkFlowCountVo();
            agentEmployeeWorkflow.setKey("28");
            agentEmployeeWorkflow.setKeyName("代理员工离职资料未上传");
            agentEmployeeWorkflow.setNum(agentEmployeeDimInformationList.size());
            workFlowCountVos.add(agentEmployeeWorkflow);
            count = count + agentEmployeeDimInformationList.size();
        }
        if (urlByLoginName.contains("个税差异反馈")){
            if(posCodeList.contains(PositionEnum.TREASURY_TAX_ATTACHE.getCode())){
                TaxComparisonFeedbackRecordImportDto dto=new TaxComparisonFeedbackRecordImportDto();
                dto.setUserOrgPositionDtoList(userOrgPosList);
                dto.setFeedbackStatus(FeedbackStatus.COMMISSIONER_FEEDBACK.getCode());
                Integer currYearMonth = DateUtil.getCurrentYearMonth();
                Integer taxMonth = DateUtil.getYearMonthByCount(currYearMonth, -1);
                dto.setTaxMonth(taxMonth);
                int taxComparisonFeedbackRecordCount=taxComparisonWrapperService.getTaxComparisonFeedbackRecordCount(dto);
                if (taxComparisonFeedbackRecordCount!=0){
                    WorkFlowCountVo workFlowCountVo = new WorkFlowCountVo();
                    workFlowCountVo.setKey("29");
                    workFlowCountVo.setKeyName("个税差异反馈");
                    workFlowCountVo.setNum(taxComparisonFeedbackRecordCount);
                    workFlowCountVos.add(workFlowCountVo);
                    count = count+taxComparisonFeedbackRecordCount;
                }
            }
        }
        if (urlByLoginName.contains("工资个税差异")) {
            if (posCodeList.contains(PositionEnum.SALARY_SPECIALIST.getCode())) {
                TaxComparisonFeedbackRecordImportDto dto = new TaxComparisonFeedbackRecordImportDto();
                dto.setUserOrgPositionDtoList(userOrgPosList);
                dto.setFeedbackStatus(FeedbackStatus.NO_FEEDBACK.getCode());
                dto.setMustFeedbackFlag(CommonBooleanTypeEnum.Yes.getIndex());
                Integer currYearMonth = DateUtil.getCurrentYearMonth();
                Integer taxMonth = DateUtil.getYearMonthByCount(currYearMonth, -1);
                dto.setTaxMonth(taxMonth);
                int taxComparisonFeedbackRecordCount = taxComparisonWrapperService.getTaxComparisonFeedbackRecordCount(dto);
                if (taxComparisonFeedbackRecordCount != 0) {
                    WorkFlowCountVo workFlowCountVo = new WorkFlowCountVo();
                    workFlowCountVo.setKey("40");
                    workFlowCountVo.setKeyName("工资个税差异必须反馈");
                    workFlowCountVo.setNum(taxComparisonFeedbackRecordCount);
                    workFlowCountVos.add(workFlowCountVo);
                    count = count + taxComparisonFeedbackRecordCount;
                }
            }
        }
        /** 人事合同归档确认 30 */

        Integer  ccrCount =  contractResourceWrapperService.getContractCompletionReminderCountByLoginName(getSessionUser().getLoginName());
        if (ccrCount > 0) {
            WorkFlowCountVo ccrWorkflow = new WorkFlowCountVo();
            ccrWorkflow.setKey("30");
            ccrWorkflow.setKeyName("人事合同归档确认");
            ccrWorkflow.setNum(ccrCount);
            workFlowCountVos.add(ccrWorkflow);
            count = count + ccrCount;
        }


        workFlowCountVos = workFlowCountVos.stream().filter(vo ->StringUtil.isNotEmpty(vo.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(workFlowCountVos)){
            workFlowCountVos.get(0).setTotal(count);
        }
        return LayuiReplay.success(workFlowCountVos);
    }

@Autowired
private IMessageWrapperService iMessageWrapperService;

    private List<TaskVo> getTaskVos(String processDefinitionKey) {

        TaskQueryDTO taskQueryDTOForLoginName = getTaskQueryDTO(processDefinitionKey, getSessionUser().getLoginName());
        List<TaskVo> taskListWithoutPageConditionForLoginName;
        taskListWithoutPageConditionForLoginName = workflowWrapperService.getTaskListWithoutPageCondition(taskQueryDTOForLoginName);
        List<UserOrgPosVo> userOrgPosVoList = iUserOrgPosWrapperService.getAllOrgCodeAndPosCode(getSessionUser().getLoginName());
        Set<String> sets = Sets.newHashSet();
        if (processDefinitionKey.equals(ReonWorkflowType.PAYMENT_APPLY.getCode())){
            UserOrgPosVo userOrgPosVo = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(getSessionUser().getLoginName());
            String orgAndPosCode = new StringBuilder().append(userOrgPosVo.getOrgCode()).append(",").append(userOrgPosVo.getPosCode()).toString();
            TaskQueryDTO taskQueryDTOForPosAndOrgCode = getTaskQueryDTO(processDefinitionKey, orgAndPosCode);
            List<TaskVo> taskListWithoutPageConditionForPosOrgCode = workflowWrapperService.getTaskListWithoutPageCondition(taskQueryDTOForPosAndOrgCode);
            taskListWithoutPageConditionForLoginName.addAll(taskListWithoutPageConditionForPosOrgCode);
        }else {
            userOrgPosVoList.forEach(item -> {
                String orgAndPosCode = item.getOrgCode() + "," + item.getPosCode();
                if (!sets.contains(orgAndPosCode)) {
                    TaskQueryDTO taskQueryDTOForPosAndOrgCode = getTaskQueryDTO(processDefinitionKey, orgAndPosCode);
                    List<TaskVo> taskListWithoutPageConditionForPosOrgCode = workflowWrapperService.getTaskListWithoutPageCondition(taskQueryDTOForPosAndOrgCode);
                    taskListWithoutPageConditionForLoginName.addAll(taskListWithoutPageConditionForPosOrgCode);
                    sets.add(orgAndPosCode);
                }
            });
        }
        //根据编号从流中获取所有数据
        if (CollectionUtils.isEmpty(taskListWithoutPageConditionForLoginName)) {
            return Lists.newArrayList();
        }
        return taskListWithoutPageConditionForLoginName;
    }



    /**
     * 获取流程查询条件DTO
     */
    private TaskQueryDTO getTaskQueryDTO(String processDefinitionKey, String userId) {
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO();
        if (StringUtil.isNotEmpty(processDefinitionKey)) {
            ReonWorkflowType type = ReonWorkflowType.fromCode(processDefinitionKey);
            taskQueryDTO.setProcessDefinitionKey(type.getDefineKey());
        }
        taskQueryDTO.setUserId(userId);
        return taskQueryDTO;
    }


    private Map<String, ParamInterface> initWorkflowTypeAndMapperMap() {
        Map<String, ParamInterface> workflowTypeAndMapperMap = new HashMap<>();
        // 报价单
        workflowTypeAndMapperMap.put(ReonWorkflowType.QUOTATION.getCode(), (pidList) -> {
            return customerWrapperService.getPidByPidListAndTableName(pidList,null,QUOTATION,"N");
        });
        //合同
        workflowTypeAndMapperMap.put(ReonWorkflowType.CONTRACT.getCode(), (pidList) -> {
            return  customerWrapperService.getPidByPidListAndTableName(pidList,"add",CONTRACT_PROCESS,null);
        });
        //合同续签
        workflowTypeAndMapperMap.put(ReonWorkflowType.CONTRACT_RENEW.getCode(), (pidList) -> {
            return  customerWrapperService.getPidByPidListAndTableName(pidList,"renew",CONTRACT_PROCESS,null);
        });
        //合同补充协议流程
        workflowTypeAndMapperMap.put(ReonWorkflowType.ADDITION_CONTRACT_FLOW.getCode(), (pidList) -> {
            return  customerWrapperService.getPidByPidListAndTableName(pidList,"iation",CONTRACT_PROCESS,null);
        });
        //供应商合同流程
        workflowTypeAndMapperMap.put(ReonWorkflowType.SUPPLIER_CONTRACT_FLOW.getCode(), (pidList) -> {
            return customerWrapperService.getPidByPidListAndTableName(pidList, "1",SUPPLIER_CONTRACT_PROCESS,null);
        });
        //供应商续签流程
        workflowTypeAndMapperMap.put(ReonWorkflowType.RENEW_SUPPLIER_CONTRACT_FLOW.getCode(), (pidList) -> {
            return customerWrapperService.getPidByPidListAndTableName(pidList, "2",SUPPLIER_CONTRACT_PROCESS,null);
        });
        //供应商补充流程
        workflowTypeAndMapperMap.put(ReonWorkflowType.AGREEMENT_SUPPLIER_CONTRACT_FLOW.getCode(), (pidList) -> {
            return customerWrapperService.getPidByPidListAndTableName(pidList, "3",SUPPLIER_CONTRACT_PROCESS,null);
        });


        return workflowTypeAndMapperMap;
    }

    private String getLoginName(String orgPosCode) {
        String loginName = "";
        /** orgPosCode 为 , 隔开 */
        try {
            if (orgPosCode.contains(",")) {
                String[] split = orgPosCode.split(",");
                UserOrgPosVo leaderByOrgCodeAndPosCode = iUserOrgPosWrapperService.getLeaderByOrgCodeAndPosCode(split[0], split[1]);
                if (leaderByOrgCodeAndPosCode != null) {
                    loginName = leaderByOrgCodeAndPosCode.getLoginName();
                }
            } else {
                loginName = orgPosCode;
            }
        } catch (Exception e) {
            String message = e.getMessage();
            //一岗多人
            if (!message.contains("selectOne(),")) {
                log.info(message);
            }
        }
        return loginName;
    }


    private Map<String, String> keyAndTextMap() {
        Map<String, String> keyAndTextMap = new HashMap<>();
        // 报价单
        keyAndTextMap.put(ReonWorkflowType.QUOTATION.getCode(), "报价单流程审批");
        //合同
        keyAndTextMap.put(ReonWorkflowType.CONTRACT.getCode(),"合同流程审批");
        //合同续签
        keyAndTextMap.put(ReonWorkflowType.CONTRACT_RENEW.getCode(), "合同续签流程审批");
        //合同补充协议流程
        keyAndTextMap.put(ReonWorkflowType.ADDITION_CONTRACT_FLOW.getCode(),"合同补充/关联协议审批");
        //供应商合同流程
        keyAndTextMap.put(ReonWorkflowType.SUPPLIER_CONTRACT_FLOW.getCode(), "供应商合同审批流程");
        //供应商续签流程
        keyAndTextMap.put(ReonWorkflowType.RENEW_SUPPLIER_CONTRACT_FLOW.getCode(), "供应商合同续签审批流程");
        //供应商补充流程
        keyAndTextMap.put(ReonWorkflowType.AGREEMENT_SUPPLIER_CONTRACT_FLOW.getCode(),"供应商合同补充协议审批流程");

        keyAndTextMap.put(ReonWorkflowType.PAYMENT_APPLY.getCode(),"支付流程审批");

        keyAndTextMap.put(ReonWorkflowType.SALARY_PAYMENT_APPLY.getCode(),"工资支付流程审批");


        return keyAndTextMap;
    }
}
