package com.reon.hr.api.customer.vo;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ContractAssignVo implements Serializable {

    private static final long serialVersionUID = -8648374680575959912L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分配类型
     */
    @TableField(value = "assign_type")
    private Integer assignType;

    /**
     * 大合同/小合同编号
     */
    @TableField(value = "relative_no")
    private String relativeNo;

    /**
     * 客服类型
     */
    @TableField(value = "cs_type")
    private Integer csType;

    /**
     * 客服专员
     */
    @TableField(value = "commissioner")
    private String commissioner;

    /**
     * 生效时间
     */
    @TableField(value = "valid_date")
    private Date validDate;

    /**
     * 状态（0:未生效，1：已生效，2，已失效）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /*合同*/
    /**
     * 合同名称
     */
    @TableField(value = "contract_name")
    private String contractName;
    private String ifNoCommercialInsurance;//是否查合同类型不为商保的
    private Integer contractType;//合同类型
    private String contractNo;//合同编号
    /**
     * 派单分公司
     */
    @TableField(value = "dist_com")
    private String distCom;

    /**
     * 签单分公司（分公司20190617）
     */
    @TableField(value = "sign_com")
    private String signCom;

    /**
     * 派单客服
     */
    @TableField(value = "disCommissioner")
    private String disCommissioner;

    /*客户*/

    /**
     * 客户名称
     */
    @TableField(value = "cust_name")
    private String custName;
    private String custNo;//客户编号

    /*小合同*/

    /**
     * 小合同名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 接单方客服
     */
    @TableField(value = "receiving_man")
    private String receivingMan;

    /**
     * 接单方
     */
    @TableField(value = "receiving")
    private String receiving;

    /**
     * 派单客服经理
     */
    @TableField(value = "comm_manager")
    private String commManager;

    /**
     * 接单方客服经理
     */
    @TableField(value = "rev_mgr")
    private String revMgr;

    /**
     * 接单方客服经理
     */
    @TableField(value = "pos_code")
    private String posCode;

    /**
     * 项目客服
     */
    @TableField(value = "later_pos_code")
    private String laterPosCode;


    /*自定义搜索条件*/

    /*
     * 替换权限级别
     * */
    private String replaceAuth;
    /**
     * 大区
     */
    private List<String> userLargeArea;

    private List<Integer> contractTypeList;

    private Date startDate;//合同生效时间
    private Date endDate;//合同终止时间
    private Integer distributionFlag;//接单是否分配
    private Integer laterDisFlag;//后道是否分配
    /***
     * 后道客服
     */
    private String laterMan;

    private List<OrgPositionDto> userOrgPositionDtoList;

    private Integer accountFlag;
    private Integer salaryDispatchFlag;//薪资是否分配
    private String salaryCommissioner;

    /**
     * 附件ID
     */
    @TableField(value = "attachment_id")
    private String attachmentId;
}
