
var ctx = ML.contextPath;
layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload'], function () {
    var table = layui.table, $ = layui.$, form = layui.form, laydate = layui.laydate, layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer, upload = layui.upload;
    var layerIndex = layer.index;
    var uploadIds = [];
    var delFileList = [];
    var dataGrop = [];

    var iStatus = '';
    var closeInterval = '';

    $(document).ready(function () {
        ML.ajax("/sys/policy/findAllCreator", null, function (res) {
            if (res.data.length > 0) {
                res.data.forEach(function (item) {
                    $("#creater").append("<option value='" + item.creater + "'>" + item.loginName + "</option>");

                });
                form.render('select');
            }
        }, "GET");
    });
    //开始日期范围
    var startDate = laydate.render({
        elem: '#startOprTime', max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate.config.min = {
                    year: date.year, month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    //结束日期范围
    var endDate = laydate.render({
        elem: '#endOprTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate.config.max = {
                    year: date.year, month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });

    //查询导入统计数据
    table.render({
        id: 'batchImportGrid',
        elem: '#batchImportGrid',
        url: ML.contextPath + '/sys/policy/getImportFileListPage',
        page: true,
        toolbar: '#topbtn',
        height: 650,
        where: {"paramData": JSON.stringify(serialize("searchForm"))},
        defaultToolbar: [],
        limit: 50,
        method: 'GET',
        limits: [50, 100, 200],
        text: {
            none: '暂无数据' //无数据时展示
        }

        ,
        cols: [[{field: '', type: 'checkbox', width: '50', fixed: 'left'}, {
            field: 'id',
            title: 'id',
            width: '500',
            align: 'center',
            fixed: 'left',
            hide: true
        }, {field: 'fileId', title: '文件编号', width: '500', align: 'center', fixed: 'left'}, {
            field: 'fileName',
            title: '文件名称',
            width: '500',
            align: 'center',
            fixed: 'left'
        },  {field: 'remark', title: '文件描述', width: '300', align: 'center', fixed: 'left'}, {
            field: 'creater',
            title: '上传人',
            width: '150',
            align: 'center',
            fixed: 'left'
        }, {field: 'createTime', title: '上传时间', width: '185', align: 'center', fixed: 'left'},
            {field: 'updater', title: '修改人', width: '150', align: 'center', fixed: 'left'},
            {field: 'updateTime', title: '修改时间', width: '185', align: 'center', fixed: 'left'},




        ]],
        done: function (res, curr, count) {
            ML.hideNoAuth();
            table.on('toolbar(batchImportGridTable)', function (obj) {
                var data = obj.data; //获得当前行数据
                var checkStatus = table.checkStatus(obj.config.id), checkData = checkStatus.data;
                var fileId = '';
                var id = '';
                for (var i = 0; i < checkData.length; i++) {
                    fileId = checkData[i].fileId
                    id = checkData[i].id
                }
                switch (obj.event) {
                    case 'import':
                        customerImport("上传文件", 'import', ['40%', '40%'], data)
                        break;
                    case 'delete':
                        //删除
                        if (checkData.length === 0) layer.msg('请选择一行'); else if (checkData.length > 1) {
                            layer.msg('请选择一行');

                        } else {
                            optOneOrMany("删除", 'delete', fileId,id);
                        }
                        break;
                    case 'edit':
                        //修改
                        if (checkData.length === 0) layer.msg('请选择一行'); else if (checkData.length > 1) {
                            layer.msg('请选择一行');

                        } else {
                            editImport("修改", 'edit', ['40%', '40%'], id);
                        }
                        break;

                }
            });
        }
    });

    ///逻辑删除
    function optOneOrMany(title, optType, params,id) {
        layer.confirm("确认要" + title + "吗？", {title: title + "确认"}, function (index) {
            //layer.close(index);
            var url = "";
            if (optType == 'delete') {
                url = "/sys/policy/delByFileId?fileId=" + params+"&optType="+optType+"&id="+id;
            }
            ML.ajax(url, params, function (result) {
                layer.msg(result.msg);
                reloadTable();
            });
        });
    }

    //打开窗口
    function customerImport(title, optType, area, data) {
        var url = "";
        if (optType == 'import') {
            url = "/sys/policy/gotoPolicyUploadFileView"
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#uploadType").val('textType');

            },
            end: function () {
                reloadTable();
            }
        });
    }

    //打开修改窗口
    function editImport(title, optType, area, id) {
        var url = "";
        if (optType == 'edit') {
            url = "/sys/policy/gotoPolicyEditFileView?id=" + id
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#editUploadType").val('textType');

            },
            end: function () {
                reloadTable();
            }
        });
    }

    //初始化表单数据
    form.on('submit(btnQueryFilter)', function (data) {
        console.log(data.field)
        table.reload('batchImportGrid', {
            where: {paramData: JSON.stringify(data.field)}, page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });
    var fileType = '';
    var fileName = '';
    //上传
    upload.render({
        elem: '#quotationUpload' //绑定元素
        ,
        url: ML.contextPath + '/sys/file/upload' //上传接口
        ,
        accept: 'file',
        headers: {contentType: false, processData: false},
        method: 'POST',
        exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb',
        field: 'file',
        auto: false,
        choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                var tip = true;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", {icon: 2});
                    return;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        },
        done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push({'fileId': res.data.fileId});
                $('#uploads').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag">' +
                    '<a href="' + ML.fileServerUrl + res.data.fileId + '"  target="_blank" id="gethref">' + fileName + '</a>' +
                    '<a href="javascript:void(0)" class="deleteFile"  }" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', {icon: 1});

            }
        },
        error: function () {
            //请求异常回调

        }
    });
////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        var delIndex;
        for (var i = 0; i < uploadIds.length; i++) {
            if (uploadIds[i].fileId == fileId) {
                delIndex = i;
            }
        }
        uploadIds.splice(delIndex, 1);
        if ($("#optType").val() == "edit") {
            delFileList.push(fileId);
        } else {
            ML.ajax("/sys/policy/delByFileId?fileId=" + fileId+"&optType="+'edit' + "&id=" + '0', {}, function (result) {
                if (result.code == 0) {
                    layer.msg("删除文件成功！")
                }
            }, 'POST');
        }
        $(this).parent()[0].remove();
    });

    //重载数据
    function reloadTable() {
        table.reload('batchImportGrid', {
            where: {paramData: JSON.stringify(serialize("searchForm"))}, curr: 1 //重新从第 1 页开始
        });
    }

    var fileType = '';
    var cust = '';
    var sale = '';
    var remark = '';
    var ids = '';
    var id = '';
    form.on("submit(uploadFilter)", function (data) {
        console.log(data.field)
        fileType = data.field.fileType
        cust = data.field.cust
        sale = data.field.sale
        remark = data.field.remark
        if (fileType == 2 && !cust) {
            layer.msg("制度类文件必须选择客服可见")
            return false;
        }
        if (!cust) {
            cust = '';
        }
        if (!sale) {
            sale = '';
        }
        if (!remark) {
            remark = '';
        }
        /*上传id*/
        if (uploadIds.length>1){
            layer.msg("只允许上传一个文件,请删除多余的文件")
            return false;
        }else {
            ids = uploadIds[0].fileId ;
        }

        $.ajax({
            url: ML.contextPath + "/sys/policy/save",
            type: 'POST',
            dataType: 'json',
            data: {'ids': ids,'cust':cust,'sale':sale,'fileType':fileType,'remark':remark,'optType':'add','id':id},
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
                reloadTable();

            },
            error: function (result) {
                layer.msg(result.msg);
            }
        });
    });


    //关闭弹窗
    $("#cancelBtn").click(function () {
        layer.closeAll('iframe');
    });


});
