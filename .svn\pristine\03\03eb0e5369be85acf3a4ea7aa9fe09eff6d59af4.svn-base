/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/5/14 0014
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.doIt;

import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RepayTheImportInformationVo
 *
 * @date 2021/5/14 0014 16:08
 */
@Data
public class RepayTheImportInformationVo implements Serializable {
    private static final long serialVersionUID = 5620431212113832809L;

    private String dataType;
    private Integer page;
    private Integer limit;
    private String groupCode;
    /**
     * 福利包名称
     */
    private String packCode;
    /**
     * 福利办理方
     */
    private String orgCode;
    private String importName;
    /**
     * 导入类型
     */
    private Integer importType;
    /**
     * 补差方式
     */
    private Integer compensationMethod;
    private String dealingWithSuppliers;
    private String welfareTransaction;
    private Integer custId;
    /**
     * 发生月
     */
    private String happenMonth;
    private String importStatus;
    /**
     * 产品类型code
     */
    private Integer[] prodCode;
    /**
     * 比例code
     */
    private String[] ratioCode;
    /**
     * 产品类型序号
     */
    private Integer[] seqNum;
    /**
     * int comment '补缴导入种类（1:补缴导入，2:补缴滞纳金导入）',
     */
    private Integer suppliPayKind;

    private String importNo;
    private String loginName;
    /**
     *  滞纳金发生月份 1：当月 2：上月
     */
    private Integer letFeeType;

}
