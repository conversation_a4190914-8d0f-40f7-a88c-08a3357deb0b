var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        laydate = layui.laydate,
        form = layui.form;
    layer = parent.layer === undefined ? layui.layer : parent.layer,tableSelect = layui.tableSelect;


    //日期范围
    var startApplyTime = laydate.render({
        elem: '#startApplyTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endApplyTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endApplyTime = laydate.render({
        elem: '#endApplyTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startApplyTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    var lastDate = laydate.render({
        elem: '#lastDate',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var paymentDateStr = laydate.render({
        elem: '#paymentDateStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });

    var createTime = laydate.render({
        elem: '#createTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var reviewTimeStr = laydate.render({
        elem: '#reviewTimeStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var ownReviewTimeStr = laydate.render({
        elem: '#ownReviewTimeStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    form.on('submit(btnQueryFilter)', function (data) {
        // renderGird()
        var field = data.field;
        field['processType'] = 5;
        table.reload('paymentApplyTable', {
            url: ML.contextPath + '/bill/payBatch/selectSalaryPaymentApplyAndCmbLog',
            where: field,
            page: {curr: 1} //重新从第 1 页开始
        });

        return false;
    });
    var tableView = '';
    var resDataLength = 0;
    renderGird();
    function renderGird() {
        table.render({
            id: 'paymentApplyTable',
            elem: '#paymentApplyTable',
            // url: ML.contextPath + '/bill/payBatch/selectSalaryPaymentApplyAndCmbLog',
            // where: {'processType': 5},
            data: [],
            method: 'get',
            toolbar: '#toolbarDemo',
            page: true, //默认为不开启
            limits: [20, 50, 100,200],
            limit: 20,
            height: 650,
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'checkbox', fixed: 'left', align: 'center'},
                {type:'numbers',align:'center', fixed: 'left'},
                {field: 'custName', title: '客户名称',align:'center',width: '10%', fixed: 'left', sort: 'true'},
                {field: 'revComName', title: '收款方',align:'center',width: '10%', fixed: 'left', sort: 'true'},

                {field: 'payAmt', title: '总计',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                        return formatCurrency(d.payAmt);
                    }},
                {field: 'singlePayAmt', title: '支付总计',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                    if(d.hasYurref == 1){
                        return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoCheckLogDetails'>" + formatCurrency(d.singlePayAmt) + "</a>";
                    }
                       return formatCurrency(d.singlePayAmt);
                    }},

                {field: 'payRollAmt', title: '代发总计',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                        if(d.hasYurref == 1){
                            return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoCheckLogDetails'>" + formatCurrency(d.payRollAmt) + "</a>";;
                        }
                        return formatCurrency(d.payRollAmt);
                    }},
                {field: 'hasYurref', title: '是否存在银企直连业务号',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                       if(d.hasYurref == 1){
                           return '是';
                       }
                       return '否';
                    }},
                // {field: 'contractTypeName', title: '产品方案',align:'center',width: '7%', sort: 'true'},
                // {field: 'documentStatusStr', title: '制单状态',align:'center',width: '7%', sort: 'true'},
                {field: 'billCheckAmt', title: '核销金额',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                    return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoCheckAmtDetailPage'>" + formatCurrency(d.billCheckAmt) + "</a>";;
                    }},
                {field: 'matchAmt', title: '匹配金额',align:'center',width: '6%', fixed: 'left', sort: 'true',templet: function (d) {
                    return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoCheckAmtDetailPage'>" + formatCurrency(d.matchAmt) + "</a>";;
                    }},
                {field: 'createTime', title: '创建时间',align:'center',width: '7%',  sort: 'true'},
                {field: 'yurref', title: '业务参考号',align:'center',width: '7%',  sort: 'true'},
                {field: 'appStatusStr', title: '审批状态',align:'center',width: '7%',  sort: 'true'},
                {field: 'payComName', title: '出款公司名称',align:'center',width: '10%',  sort: 'true'},
                {field: 'lastDate', title: '工资支付日期',align:'center',minWidth:110,  sort: 'true'},
                {field: 'payMonth', title: '支付所属年月',align:'center',width: '7%', sort: 'true'},
                // {field: 'totalActApy', title: '实付工资款',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalActApy);
                //     }},
                // {field: 'totalTax', title: '个税款',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalTax);
                //     }},
                // {field: 'totalCompensation', title: '补偿金',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalCompensation);
                //     }},
                // {field: 'totalCompensationTax', title: '补偿金个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalCompensationTax);
                //     }},
                // {field: 'totalAnnualBonus', title: '年终奖',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalAnnualBonus);
                //     }},
                // {field: 'totalAnnualBonusTax', title: '年终奖个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalAnnualBonusTax);
                //     }},
                // {field: 'totalLaborWages', title: '劳务工资',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalLaborWages);
                //     }},
                // {field: 'totalLaborWagesTax', title: '劳务工资个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalLaborWagesTax);
                //     }},
                // {field: 'totalSalaryFee', title: '服务费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalSalaryFee);
                //     }},
                // {field: 'totalSupplierDisFund', title: '残障金',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalSupplierDisFund);
                //     }},
                // {field: 'totalSupplierCrossBankHandlingFees', title: '跨行手续费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalSupplierCrossBankHandlingFees);
                //     }},
                // {field: 'totalSupplierUnionFees', title: '工会费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                //         return formatCurrency(d.totalSupplierUnionFees);
                //     }},
                {field: 'totalSupplierSalarySaleTax', title: '税金合计',align:'center',width: '15%', sort: 'true',templet: function (d) {
                        return formatCurrency(d.totalSupplierSalarySaleTax);
                    }},
                {field: 'reviewTime', title: '复核时间',align:'center',width: '10%', sort: 'true'},
                {field: 'ownReviewTime', title: '自有工资复核时间',align:'center',width: '10%', sort: 'true'},
                {field: 'applicant', title: '申请人',align:'center',width: '15%',  sort: 'true'},
                {field: 'payTypeStr', title: '支付类型',align:'center', width: '13%', sort: 'true'},
                {field: 'anewPayFlagStr', title: '是否退票重发',align:'center',width: '7%', sort: 'true'},
                {field: 'paymentDate', title: '供应商发薪时间',align:'center',width: '7%', sort: 'true'},
                {field: 'payAssociatedComName', title: '支付关联抬头',align:'center',width: '7%', sort: 'true'},
                {field: 'paymentApplyId', title: '支付ID',align:'center',width: '5%', sort: 'true'},
                {title:'审批信息',toolbar: '#toolDemo', width: '8%', align: 'center',fixed: 'right'},
                {title:'查看人员明细',toolbar: '#toolDemo2', width: '8%', align: 'center',fixed: 'right'},
            ]],
            done: function (res) {
                ML.hideNoAuth();
                tableView = this.elem.next();
                resDataLength = res.data.length;
                for (var i = 0; i < resDataLength; i++) {
                    if (res.data[i].appStatus === 1 || res.data[i].appStatus === 6) {
                        $("tr").eq(i + 1).css("color", "red");
                        $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                    }
                }
            }
        });
    }
    //收款方(分公司)
    var appd3 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#revComName',
        checkedKey: 'orgCode',
        appd:appd3,
        table: {
            url:ML.contextPath+'/sys/org/getCompanyByName',
            cols: [[
                { type: 'radio' },
                {type:'numbers',title:'序号',align:'center'},
                {field: 'orgName',title:'分公司名称',align:'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#revCom").val($("#revComName").attr("ts-selected"));
        }
    });
    //支付关联抬头
    var appd4 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#payAssociatedComName',
        checkedKey: 'orgCode',
        appd:appd4,
        table: {
            url:ML.contextPath+'/sys/org/getCompanyByName',
            cols: [[
                { type: 'radio' },
                {type:'numbers',title:'序号',align:'center'},
                {field: 'orgName',title:'分公司名称',align:'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#payAssociatedCom").val($("#payAssociatedComName").attr("ts-selected"));
        }
    });
    //监听工具条
    table.on('tool(paymentApplyFilter)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
            case 'priceEvent'://查看支付详情
                openCurrPage("查看支付详情" ,"priceEvent",data);
                break;
            case 'personnelReview'://查看支付详情
                openCurrPage("查看人员详情" ,"personnelReview",data);
                break;
            case 'gotoCheckLogDetails':
                layer.open({
                    type: 2, //1 直接弹出content内容 2发送请求
                    title: "查看日志",
                    area: ['65%', '80%'],
                    maxmin: true,
                    offset: 'auto',
                    shade: [0.8, '#393D49'],
                    content: ctx + '/thirdPart/cmb/checkRequestMsg',
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        let params = {"transAcctInfos": data.transAcctInfos,
                            "detailInfoList":data.detailInfoList,"payrollBatchInfos":data.payrollBatchInfos};
                        body.find("#reqMsg").val(JSON.stringify(params));
                        body.find("#serviceCode").val("checkPayLog");
                    },
                    end: function () {

                    }
                })
                break;
                case 'gotoCheckAmtDetailPage':
                layer.open({
                    type: 2, //1 直接弹出content内容 2发送请求
                    title: "查看日志",
                    area: ['65%', '80%'],
                    maxmin: true,
                    offset: 'auto',
                    shade: [0.8, '#393D49'],
                    content: ctx + '/thirdPart/cmb/gotoCheckAmtDetailPage',
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        let params = {"payCustAndRecVos": data.payCustAndRecVos,
                            "billCheckVos":data.billCheckVos};
                        body.find("#reqMsg").val(JSON.stringify(params));
                    },
                    end: function () {

                    }
                })
                break;
        }
    });


    function openCurrPage(title,optType,data){
        var  url="",area=['85%', '80%'];
        switch (optType) {
            case 'priceEvent':
                url="/bill/payBatch/lookSalaryPage?batchId="+data.id+"&payMentId="+data.paymentApplyId;
                area=['75%', '85%'];
                break;
            case 'personnelReview':
                url="/bill/payBatch/lookSalaryEmpByPayment?paymentApplyId="+data.paymentApplyId;
                break;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        })

    }
    // 监听表格上方的按钮
    table.on('toolbar(paymentApplyFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        let paymentApplyIds=[];
        switch (obj.event) {
            case 'export':
                window.open(ctx + '/bill/payBatch/exportSalaryPaymentApplyQuery?paramData='
                    +JSON.stringify(serialize("searchForm")));
                break;
            case 'net':
                if(data.length<1){
                    return layer.msg("最少选择一条数据!");
                }
                for (let i = 0; i < data.length; i++) {
                    if(data[i].payType==3||data[i].payType==18){
                        return layer.msg("所选数据存在不是自有工资、异地供应商类型的！");
                    }
                    paymentApplyIds.push(data[i].paymentApplyId);
                }
                window.open(  ML.contextPath + "/bill/payBatch/getOnlineBank?paymentApplyIds=" + JSON.stringify(paymentApplyIds)+"&documentStatus=3");
                break;
            case 'payNet':
                if(data.length<1){
                    return layer.msg("最少选择一条数据!");
                }
                for (let i = 0; i < data.length; i++) {
                    paymentApplyIds.push(data[i].paymentApplyId);
                }
                window.open(  ML.contextPath + "/bill/payBatch/getOnlineBank?paymentApplyIds=" + JSON.stringify(paymentApplyIds)+"&documentStatus=1");
                break;


        }
    })

    table.on('checkbox(paymentApplyFilter)', function (obj) {
        var checked = obj.checked; //获取checkbox的选中状态，true或者false
        var tr = obj.tr; //获取到当前行的DOM对象
        setTr(checked, tr);
    });
    form.on('checkbox(layTableAllChoose)', function (res) {
        var checked = res.elem.checked;
        for (let i = 0; i < resDataLength; i++) {
            var tr = tableView.find('tr[data-index=' + i + ']');
            setTr(checked, tr);
        }
    });
    function setTr(checked, tr) {
        var style = tr.attr('style');
        var redFlag = style && style.indexOf('red') > 0;
        if (checked) {
            // 设置选中行的背景颜色
            tr.css('background-color', '#2257c4');
            if (!redFlag) {
                tr.css('color', '#fff');
            }
            tr.css('font-weight', '600');
        } else {
            // 取消选中行的背景颜色
            tr.css('background-color', '');
            if (!redFlag) {
                tr.css('color', '');
            }
            tr.css('font-weight', '');
        }
    }


    var customerList;
    //获取客户
    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var id = '';
            var name = '';
            customerList=data.data
            layui.each(data.data, function (index, item) {
                id = item.id;
                name = item.custName;
            });
            // 回填值
            $("#custId").val(id);
            $("#custName").val(name);
        }
    });
    $(document).ready(function () {
        $("#payType option").each(function () {
            var value = $(this).val();
            if(value&&value!=3&&value!=12&&value!=18&&value!=19){
                $(this).remove();
            }
        })
        var info = [];
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/workflow/salaryPaymentApplyAllUserList",
            data:{"processType":5},
            dataType: 'json',
            success: function (data) {
                info = [];
                info = data.data;
                $.each(info, function (i, item) {
                    $("#applicant").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    });

    //重载数据
    function reloadTable() {
        table.reload('paymentApplyTable', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }
    $("#reset").click(function () {
        $("#custId").val(null);
        $("#distCom").val(null);
        $("#revCom").val(null);
        $("#revComName").removeAttr("ts-selected");
        $("#payAssociatedCom").val(null);
        $("#payAssociatedComName").removeAttr("ts-selected");
        startApplyTime.config.min = {year: 2010, month:0, date:1};
        startApplyTime.config.max = {year: 2099, month:11, date:12};
        endApplyTime.config.min = {year: 2010, month:0, date:1};
        endApplyTime.config.max = {year: 2099, month:11, date:12};
        form.render();
    });
});