<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/6
  Time: 16:46
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>查看修改历史</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-onlySelf {
            width: 125px;
        }

        /*出现滚动条*/
        td {
            overflow: auto;
        }

        .layui-table th {
            text-align: center;
        }

        .noteNewline {
            white-space: nowrap
        }
        .layui-card-header.layuiadmin-card-header-auto {
            padding-top: unset!important;
            padding-bottom: unset!important;

        }
    </style>
</head>
<body>
<input type="hidden" id="empContractNo" name="empContractNo" value="">
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
            </div>
        </form>
    </div>

    <div class="layui-card-body">
        <table class="layui-hide" id="checkEditSotryTable" lay-filter="checkEditSotryFilter"></table>
    </div>
</div>
<%--<div class="layui-card-body">--%>
<%--    <script type="text/jsp" id="toolbarDemo">--%>
<%--        <input type="button" class="layui-btn layui-btn-sm" id="add" lay-event="selectCategory" value="选中">--%>
<%--    </script>--%>
<%--</div>--%>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/employeeContract/checkEmployeeContractStory.js?v=${publishVersion}"></script>

</body>
</html>
