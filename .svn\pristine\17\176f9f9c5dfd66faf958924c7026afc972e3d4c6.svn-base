package com.reon.hr.sp.customer.entity.cus;


import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

@Data
@TableName(value = "contract_assign_log")
public class ContractAssignLog {
    /**
     * 主键ID
     */
     @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分配类型
     */
    @TableField(value = "assign_type")
    private Integer assignType;

    /**
     * 大合同/小合同编号
     */
    @TableField(value = "relative_no")
    private String relativeNo;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 合同名称
     */
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 小合同名称
     */
    @TableField(value = "contract_area_name")
    private String contractAreaName;

    /**
     * 客服类型
     */
    @TableField(value = "cs_type")
    private Integer csType;

    /**
     * 客服专员
     */
    @TableField(value = "commissioner")
    private String commissioner;

    /**
     * 生效日期
     */
    @TableField(value = "valid_date")
    private Date validDate;

    /**
     * 失效日期
     */
    @TableField(value = "invalid_date")
    private Date invalidDate;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;

    public static final String COL_ASSIGN_TYPE = "assign_type";

    public static final String COL_RELATIVE_NO = "relative_no";

    public static final String COL_CUSTOMER_NAME = "customer_name";

    public static final String COL_CONTRACT_NAME = "contract_name";

    public static final String COL_CONTRACT_AREA_NAME = "contract_area_name";

    public static final String COL_CS_TYPE = "cs_type";

    public static final String COL_COMMISSIONER = "commissioner";

    public static final String COL_VALID_DATE = "valid_date";

    public static final String COL_INVALID_DATE = "invalid_date";

    public static final String COL_CREATOR = "creator";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATER = "updater";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_DEL_FLAG = "del_flag";
}
