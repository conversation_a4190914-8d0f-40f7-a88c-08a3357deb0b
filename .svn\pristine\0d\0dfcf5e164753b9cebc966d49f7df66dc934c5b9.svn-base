package com.reon.hr.sp.report.dao.report;





import com.reon.hr.sp.report.entity.InvoiceCheckIntermidiate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvoiceCheckIntermidiateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(InvoiceCheckIntermidiate record);

    int insertSelective(InvoiceCheckIntermidiate record);

    InvoiceCheckIntermidiate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceCheckIntermidiate record);

    int updateByPrimaryKey(InvoiceCheckIntermidiate record);


    Integer updateStatus(@Param("invoiceCheckIntermidiate") InvoiceCheckIntermidiate invoiceCheckIntermidiate);

    Integer deleteByCheckIds(@Param("list") List<Long> checkIds);

	Integer deleteByCheckSubIds(@Param("list")List<Long> subCheckList);

    Integer deleteByCheckIdAndBillIds(@Param("checkId") Long checkId, @Param("billIds") List<Long> billIds);
}