package com.reon.hr.api.bill.vo.supplierPractice;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SupplierBillCompareVo extends BaseImportDto implements Serializable {
    @ExcelProperty("证件号*")
    private String certNo;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    private BigDecimal serviceFeeDiff;

    @ExcelProperty("类型*")
    private String adjustType;

    @ExcelProperty("服务开始月*")
    private Integer startMonth;

    @ExcelProperty("服务费截止月(汇缴不用填写)")
    private Integer endMonth;

    private String remark;

    private Map<String, SupplierBillCompareDetailVo> prodMap = Maps.newHashMap();
}
