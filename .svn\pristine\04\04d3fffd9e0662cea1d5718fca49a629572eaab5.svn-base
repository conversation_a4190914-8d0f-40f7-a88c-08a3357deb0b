var ctx = ML.contextPath;
layui.use(['form', 'layer', 'table', 'upload', 'laydate',], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate;
    layer = parent.layer === undefined ? layui.layer : parent.layer;

    $("#billCheckFlag").val($("#type").val());
    $("#commissioner").val(ML.loginNameFormater($("#commissionerS").val()));

    form.render('select')

    // 表单保存
    form.on("submit(saveFilter)", function (data) {
        $.ajax({
            url: ML.contextPath + "/report/actual/updateActualPaymentAdvanceReport",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data.field),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                layer.msg(result.msg);
                if(result.code == 0){
                    layer.closeAll('iframe');


                }
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
        return false;
    });

    //关闭弹窗
    $("#cancelBtn").click(function () {
        layer.closeAll('iframe');
    });

});
