package com.reon.hr.core.thread;

import org.apache.commons.collections4.CollectionUtils;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderStatus;
import com.reon.hr.api.customer.utils.BatchImportExcelCommonUtil;
import com.reon.hr.api.customer.utils.BatchResignationApplicationImportUtil;
import com.reon.hr.api.customer.vo.batchImport.*;
import com.reon.hr.api.customer.vo.employee.CompleteOrderViewVo;
import com.reon.hr.core.vo.ServiceCommonVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.RecursiveAction;

public class BatchResignationApplicationImportTask extends RecursiveAction {
    private static final Logger logger = LoggerFactory.getLogger (BatchResignationApplicationImportTask.class);

    MultipartFile file;
    Integer importDateTypeId;
    String loginName;
    String remark;
    String fileId;
    IBatchImportDataWrapperService iBatchImportDataService;
    String importNo;
    BatchResignationApplicationImportUtil batchResignationApplicationImportUtil;
    IEmployeeOrderWrapperService employeeOrderService;
    BatchCommonVo batchCommonVo;
    BatchVo batchVo;

    @Override
    protected void compute() {
        try {
            //
            ResignationApplicationExcelDataVo raxdv = new ResignationApplicationExcelDataVo ();
            raxdv.setImportNo (importNo);
            batchVo.setResignationApplicationExcelDataVo (raxdv);

            batchResignationApplicationImportUtil.analyzeExcelData (raxdv, batchCommonVo);
            parsingResignationApplicationExcelData (raxdv);

            iBatchImportDataService.updateByImportDataNo (importNo, BatchImportExcelCommonUtil.PROCESSED);
            ImportDataVo byId = iBatchImportDataService.getById (importNo);
            if (byId != null) {
                if (byId.getStatus () == 2 && importDateTypeId == 2) {
                    IBatchImportDataWrapperService.resignationApplicationHashMap.remove (loginName);
                }
            }
        } catch (Exception e) {
            iBatchImportDataService.updateByImportDataNo (importNo, BatchImportExcelCommonUtil.PROCESSED);
            IBatchImportDataWrapperService.resignationApplicationHashMap.remove (loginName);
            e.printStackTrace ();
            logger.error ("批量离职申请导入解析或者入库异常：" + e.getMessage ());
        }
    }

    /**
     * 效验excel批量离职表格的数据是否正确
     *
     * @param raedv
     * @throws Exception
     */
    private void parsingResignationApplicationExcelData(ResignationApplicationExcelDataVo raedv) throws Exception {
        logger.info ("============批量离职申请导入日志=============");
        List<CompleteOrderViewVo> completeOrderViewVos = new ArrayList<> ();
        String cn = IBatchImportDataWrapperService.CONTRACTAREA_NO;
        String mcontractareaNo = cn.substring (0, 5);
        String en = IBatchImportDataWrapperService.EMPLOYEE_NAME;
        String memployeeName = en.substring (2, 4);
        String rk = IBatchImportDataWrapperService.ENTRY_REMARK;
        String remark = rk.substring (2, 4);
        String certno = IBatchImportDataWrapperService.CERT_NO;
        String sfz = certno.substring (0, 4);
        int is = 0;
        for (Map<String, Object> map : raedv.getList ()) {
            Integer rowNum = (Integer) map.get (IBatchImportDataWrapperService.ROWNUM);
            CompleteOrderViewVo orderViewVo = new CompleteOrderViewVo ();
            orderViewVo.setOrderFlag (6);
            orderViewVo.setEmployeeName ((String) map.get (memployeeName));
            orderViewVo.setContractAreaNo ((String) map.get (mcontractareaNo));
            orderViewVo.setCertNo ((String) map.get (sfz));
            List<CompleteOrderViewVo> completeOrderLists = iBatchImportDataService.getCompleteOrderList (orderViewVo, loginName);
            if (CollectionUtils.isNotEmpty(completeOrderLists)) {
                for (CompleteOrderViewVo completeOrderList : completeOrderLists) {
                    //新增等待接单方完善和挂起 两个状态可以减员
                    if (completeOrderList.getOrderStatus () == EmployeeOrderStatus.ADD_FINISHED.getCode() ||
                        completeOrderList.getOrderStatus () == EmployeeOrderStatus.NEED_RECEIVING_COMPLETED.getCode() ||
                        completeOrderList.getOrderStatus () == EmployeeOrderStatus.SUSPEND.getCode() ) {
                        //验证有效性
                        validationResignationApplication (completeOrderViewVos, map, rowNum, completeOrderList, remark, raedv);
                    } else {
                        is++;
                    }
                }
                if (is == completeOrderLists.size ()) {
                    raedv.getDataJson ().put (IBatchImportDataWrapperService.ERROR_DESCRIPTION, "小合同编号或者姓名或者身份证号不存在或者员工是离职完成状态不能再次离职!");
                    batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
                }
                raedv.getDataJson ().clear ();
                is = 0;
            } else {
                raedv.getDataJson ().put (IBatchImportDataWrapperService.ERROR_DESCRIPTION, "小合同编号或者姓名或者身份证号不存在或者员工是离职完成状态不能再次离职!");
                batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
            }
        }
        BatchImportExcelCommonUtil.addAndupdateImportData (batchVo,iBatchImportDataService);
        logger.info ("批量离职申请导入开始修改离职信息");
        if(!completeOrderViewVos.isEmpty ()){
            employeeOrderService.updateBatchEmployeeLeave (completeOrderViewVos, loginName);
        }
        logger.info ("批量离职申请导入结束修改离职信息");
    }

    /**
     * 验证批量导入离职字段有效性
     *
     * @param completeOrderViewVos
     * @param map
     * @param rowNum
     * @param completeOrderList
     * @param raedv
     */
    private void validationResignationApplication(List<CompleteOrderViewVo> completeOrderViewVos,
                                                  Map<String, Object> map, Integer rowNum,
                                                  CompleteOrderViewVo completeOrderList,
                                                  String remark,
                                                  ResignationApplicationExcelDataVo raedv) {
        String departureDate = (String) map.get (IBatchImportDataWrapperService.DEPARTURE_DATE);
        if (!StringUtils.isNotEmpty (departureDate)) {
            raedv.getDataJson ().put (IBatchImportDataWrapperService.DEPARTURE_DATE, "离职日期不能空!");
            batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
        } else {
            try {
                if (!BatchImportExcelCommonUtil.isValidDate (departureDate,null)) {
                    raedv.getDataJson ().put (IBatchImportDataWrapperService.DEPARTURE_DATE, "离职日期格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以！(注意：有可能日不在当前月里)");
                    batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
                } else {
                    Date dt = BatchImportExcelCommonUtil.getDate (departureDate);
                    Date ed = BatchImportExcelCommonUtil.getDate (completeOrderList.getEntryDate ());
                    String defautlString = BatchImportExcelCommonUtil.getDefautlString (dt);
                    if (dt.getTime () >= ed.getTime ()) {
                        completeOrderList.setDimissionDate (defautlString);
                    } else {
                        raedv.getDataJson ().put (IBatchImportDataWrapperService.DEPARTURE_DATE, "离职日期必须大于等于入职日期!");
                        batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
                    }
                }
            } catch (Exception e) {
                logger.error (e.getMessage ());
                e.printStackTrace ();
            }
        }
        String closingMonth = (String) map.get (IBatchImportDataWrapperService.CLOSING_MONTH);
        if (!StringUtils.isNotEmpty (closingMonth)) {
            raedv.getDataJson ().put (IBatchImportDataWrapperService.CLOSING_MONTH, "收费截止月不能为空!");
            batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
        } else {
            if (!BatchImportExcelCommonUtil.isValidDate (closingMonth,"type")) {
                raedv.getDataJson ().put (IBatchImportDataWrapperService.CLOSING_MONTH, "收费截止月格式错误，必须是yyyyMM格式，年月后面不能带-，/(注意：有可能月不在当前年里)");
                batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
            } else {
                completeOrderList.setExpiredMonth (Integer.valueOf (closingMonth));
            }
        }
        String dimissionReason = (String) map.get (IBatchImportDataWrapperService.LEAVING_REASON);
        if (!StringUtils.isNotEmpty (dimissionReason)) {
            raedv.getDataJson ().put (IBatchImportDataWrapperService.LEAVING_REASON, "离职原因不能为空!");
            batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
        }
        Integer dimissionReasonTypeIf = BatchImportExcelCommonUtil.isDimissionReasonTypeIf (dimissionReason);
        completeOrderList.setDimissionReason (dimissionReasonTypeIf);
        String reduceReason = (String) map.get (IBatchImportDataWrapperService.ATTRITION_REASON);
        if (!StringUtils.isNotEmpty (reduceReason)) {
            raedv.getDataJson ().put (IBatchImportDataWrapperService.ATTRITION_REASON, "减员原因不能为空!");
            batchResignationApplicationImportUtil.updateError (rowNum, batchVo);
        }
        Integer reduceReasonTypeIf = BatchImportExcelCommonUtil.isReduceReasonTypeIf (reduceReason);
        completeOrderList.setReduceReason (reduceReasonTypeIf);
        String dimissionRemark = (String) map.get (remark);
        completeOrderList.setDimissionRemark (dimissionRemark);
        if (raedv.getErrorDesc ().get (rowNum) == null) {
            completeOrderViewVos.add (completeOrderList);
        }
    }

    public BatchResignationApplicationImportTask(ServiceCommonVo serviceCommonVo) {
        this.file = serviceCommonVo.getFile ();
        this.importDateTypeId = serviceCommonVo.getImportDateTypeId ();
        this.loginName = serviceCommonVo.getLoginName ();
        this.remark = serviceCommonVo.getRemark ();
        this.fileId = serviceCommonVo.getFileId ();
        this.iBatchImportDataService = serviceCommonVo.getIBatchImportDataService ();
        this.importNo = serviceCommonVo.getImportNo ();
        this.batchResignationApplicationImportUtil = serviceCommonVo.getBatchResignationApplicationImportUtil ();
        this.employeeOrderService = serviceCommonVo.getEmployeeOrderService ();
        this.batchCommonVo = serviceCommonVo.getBatchCommonVo ();
        this.batchVo = serviceCommonVo.getBatchVo ();
    }
}
