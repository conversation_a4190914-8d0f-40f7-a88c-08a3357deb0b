package com.reon.hr.api.bill.utils;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.bill.enums.InsuranceIRatioProductCode;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeReportPaymentDetailVo;
import com.reon.hr.api.bill.vo.insurancePractice.ProdPayAmtDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.math.BigDecimal;
import java.util.*;

public class ExportPracticeCollectReportUtil {

    public static final String[] RECENTLY_HEAD = {"序号", "福利办理方","城市","是否单立户","单立户名称","大区","报表年月","锁定时间"};
    public static final String[] RECENTLY_FIELD = {"index", "orgName","cityName","singFlagStr","singFlagName","areaName","billReportMonth","lockTime"};
    public static final Integer[] RECENTLY_HEAD_COLUMN_WIDTH = {8, 24, 24,12,12,12,10,10,10};
    public static final String[] RECENTLY_DETAIL_HEAD = {"金额", "人数"};
    public static final String[] RECENTLY_DETAIL_FIELD = {"totalAmt", "empNum"};
    public static final Integer[] RECENTLY_DETAIL_HEAD_COLUMN_WIDTH = {8, 8};

    public static SXSSFWorkbook getPracticeReportWorkBook(List<PracticeReportPaymentDetailVo> exportPracticeReportVoList, String fileName, String[] heads, String[] feilds, Integer[] columnWidth) {
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        CellStyle headCellStyle = ExportPracticeReportUtil.getFieldCellStyle(sxssfWorkbook, true);
        CellStyle dataCellStyle = ExportPracticeReportUtil.getFieldCellStyle(sxssfWorkbook, false);
        // 创建表并设置标题与打印时间
        SXSSFSheet sheet = ExportPracticeReportUtil.createSheetAndSetTitleAndTime(sxssfWorkbook, "", fileName);
        // 获取详细产品数量及编码
        Set<Byte> prodCodeList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(exportPracticeReportVoList)) {
            for (PracticeReportPaymentDetailVo exportPracticeReportVo : exportPracticeReportVoList) {
                if(Objects.nonNull(exportPracticeReportVo.getProdPayAmtDtos())){
                    prodCodeList.addAll(exportPracticeReportVo.getProdPayAmtDtos().keySet());
                }

            }
        }

        // 设置请求头与列宽
        SXSSFRow headRow = sheet.createRow(5);
        SXSSFRow headRow2 = sheet.createRow(6);
        headRow.setHeight((short) 400);
        // 设置基础表头信息
        for (int i = 0; i < heads.length; i++) {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(5, 6, i + 1, i + 1);
            sheet.addMergedRegion(cellRangeAddress);
            ExportPracticeReportUtil.regionStyle(cellRangeAddress, sheet);
            SXSSFCell cell = headRow.createCell(i + 1);
            cell.setCellValue(heads[i]);
            cell.setCellStyle(headCellStyle);
            sheet.setColumnWidth(i + 1, columnWidth[i] * 400);
        }

        // 设置 明细表头
        int head1CellIndex = heads.length;
        int head2CellIndex = heads.length;

        for (Byte prodCode : prodCodeList) {
            String name = InsuranceIRatioProductCode.getName(prodCode.intValue());
            int startIndex = head1CellIndex + 1;
            int endIndex = startIndex + RECENTLY_DETAIL_FIELD.length - 1;
            head1CellIndex = endIndex;
            CellRangeAddress cellRangeAddress = new CellRangeAddress(5, 5, startIndex, endIndex);
            ExportPracticeReportUtil.regionStyle(cellRangeAddress, sheet);
            sheet.addMergedRegion(cellRangeAddress);
            SXSSFCell cell1 = headRow.createCell(startIndex);
            cell1.setCellStyle(headCellStyle);
            cell1.setCellValue(name);
            for (int i = 0; i < RECENTLY_DETAIL_HEAD.length; i++) {
                SXSSFCell cell = headRow2.createCell(++head2CellIndex);
                cell.setCellValue(RECENTLY_DETAIL_HEAD[i]);
                cell.setCellStyle(headCellStyle);
                sheet.setColumnWidth(head2CellIndex, RECENTLY_DETAIL_HEAD_COLUMN_WIDTH[i] * 400);
            }
        }
        // 设置合计列
        SXSSFCell headTotalcell = headRow.createCell(++head2CellIndex);
        CellRangeAddress cellRangeAddress = new CellRangeAddress(5, 6, head2CellIndex, head2CellIndex);
        sheet.addMergedRegion(cellRangeAddress);
        ExportPracticeReportUtil.regionStyle(cellRangeAddress, sheet);
        headTotalcell.setCellValue("合计");
        headTotalcell.setCellStyle(headCellStyle);
        sheet.setColumnWidth(head2CellIndex, 8 * 400);

        BigDecimal totalAmount = BigDecimal.ZERO;
        // 设置详细信息
        if (CollectionUtils.isNotEmpty(exportPracticeReportVoList)) {
            int index = 0;
            for (int i = 0; i < exportPracticeReportVoList.size(); i++) {
                SXSSFRow row = sheet.createRow(i + 7);
                row.setHeight((short) 400);
                PracticeReportPaymentDetailVo exportPracticeReportVo = exportPracticeReportVoList.get(i);
                // 填入基本信息
                for (int i1 = 0; i1 < feilds.length; i1++) {
                    SXSSFCell cell = row.createCell(i1 + 1);
                    cell.setCellStyle(dataCellStyle);
                    if (i1 == 0) {
                        cell.setCellValue(++index);
                    } else {
                        Object value = ExportPracticeReportUtil.getGetMethod(exportPracticeReportVo, RECENTLY_FIELD[i1]);
                        if (value instanceof Date) {
                            cell.setCellValue(DateUtil.getDateString((Date) value));
                        } else {
                            cell.setCellValue(value == null ? "" : value.toString());
                        }
                    }
                }
                int detailCellIndex = RECENTLY_HEAD.length;

                // 填入产品明细信息
                for (Byte prodCode : prodCodeList) {
                    Map<Byte, ProdPayAmtDto> prodPayAmtDtos = Optional.ofNullable(exportPracticeReportVo.getProdPayAmtDtos()).orElse(Maps.newHashMap());
                    ProdPayAmtDto practiceReportDetailVo = prodPayAmtDtos.getOrDefault(prodCode, new ProdPayAmtDto());
                    for (String fieldName : RECENTLY_DETAIL_FIELD) {
                        SXSSFCell cell = row.createCell(++detailCellIndex);
                        cell.setCellStyle(dataCellStyle);
                        Object value = ExportPracticeReportUtil.getGetMethod(practiceReportDetailVo, fieldName);
                        if (value instanceof Date) {
                            cell.setCellValue(DateUtil.getDateString((Date) value));
                        } else {
                            cell.setCellValue(value == null ? "" : value.toString());
                        }
                    }
                }

                // 填入合计金额
                SXSSFCell totalCell = row.createCell(++detailCellIndex);
                totalCell.setCellStyle(dataCellStyle);
                Object value = ExportPracticeReportUtil.getGetMethod(exportPracticeReportVo, "totalAmt");
                totalCell.setCellValue(value == null ? "" : value.toString());
                BigDecimal target = value == null ? totalAmount.add(BigDecimal.ZERO) : (BigDecimal) value;
                totalAmount = totalAmount.add(target);
            }



            //计算总金额
            SXSSFRow row = sheet.createRow(exportPracticeReportVoList.size() + 7);
            row.setHeight((short) 400);
            int index2 = prodCodeList.size() * RECENTLY_DETAIL_HEAD.length + RECENTLY_HEAD.length;
            SXSSFCell cell = row.createCell(index2);
            cell.setCellValue("总金额");
            cell.setCellStyle(headCellStyle);
            SXSSFCell cellMount = row.createCell(index2 + 1);
            cellMount.setCellValue(totalAmount.toString());
            cellMount.setCellStyle(headCellStyle);
        }


        return sxssfWorkbook;
    }

}
