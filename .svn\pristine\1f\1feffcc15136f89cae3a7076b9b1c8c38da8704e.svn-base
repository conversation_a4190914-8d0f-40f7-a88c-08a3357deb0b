package com.reon.hr.api.report.utils;

import com.reon.hr.api.report.vo.BillContractReport;
import com.reon.hr.api.report.vo.CostReport;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * create guoqian
 * 20200623*/
public class CostReportExcle {
    private  static Logger logger = LoggerFactory.getLogger (CostReportExcle.class);
    private static String   FIELD_ONE ="合同编号";
    private static String   FIELD_TWO ="合同名称";
    private static String   FIELD_THREE ="接单方";
    private static String   FIELD_FOUR ="接单方类型";
    private static String   FIELD_FIVE ="客户编号";
    private static String   FIELD_SIX ="客户名称";
    private static String   FIELD_SEVEN ="费用总计（个人总计+总服务费）";
    private static String   FIELD_EIGHT ="总服务费";
    private static String   FIELD_NINE ="供应商服务费";
    private static String   FIELD_TEN="服务人次";
    private static String   FIELD_ELEVEN ="产品类型";
    private static String   FIELD_TWELVE ="产品名称";
    private static String   FIELD_THIRTEEN  ="是否含税";
    private static String   FIELD_FOURTEEN ="税率值";
    private static String   FIELD_FIFTEEN ="人数分布";
    //合同生成详情导出方法
    //合同编号
    //合同名称
    //客户编号
    //客户名称
    private  static String  FIELD_BILL_MONTH ="账单年月";
    private  static  String FIELD_BILL_TRUE ="是否出账单";


    //成本导入的方法
   public  static <T> void export(HSSFWorkbook wb, String name,  List<T> list ){
       //在webbook中添加一个sheet,对应Excel文件中的sheet
       HSSFSheet sheet = wb.createSheet(name);
       //sheet.setDisplayGridlines (false);//去除网格线

      //创建第一行hu
       HSSFRow row = sheet.createRow(0);
       String[] headSub ={
               FIELD_ONE,
               FIELD_TWO,
               FIELD_THREE,
               FIELD_FOUR,
               FIELD_FIVE,
               FIELD_SIX,
               FIELD_SEVEN,
               FIELD_EIGHT,
               FIELD_NINE,
               FIELD_TEN,
               FIELD_ELEVEN,
               FIELD_TWELVE,
               FIELD_THIRTEEN,
               FIELD_FOURTEEN,
               FIELD_FIFTEEN,
       };
       logger.info("输出表头"+headSub+"输出第一列"+headSub[0]);

       //表头循环
       for( int i=0;i<headSub.length;i++){
           row.createCell(i).setCellValue(headSub[i]);
       }

       //子项数据填充
     for (int i =0 ;i<list.size();i++) {
         CostReport costReport = (CostReport) list.get(i);
         String[] objects={
                 costReport.getContractNo(),
                 costReport.getContractName(),
                 costReport.getReceivingName(),
                 costReport.getReceivingTypeName(),
                 costReport.getCustId(),
                 costReport.getCustName(),
                 //个人统计
                 bigDecimalToString(costReport.getIndTotalSum()),
                 //总服务费 serviceFeeSum
                 bigDecimalToString(costReport.getServiceFeeSum()),
                 //接单方服务总价格 servicePriceSum
                 bigDecimalToString(costReport.getServicePriceSum()),
                 //服务人次 serviceCount
                 String.valueOf(costReport.getServiceCount()),
                 //产品类型
                 costReport.getProdTypeName(),
                 //产品子类型 subType
                 costReport.getSubTypeName(),
                 //是否含税 taxFlag
                 costReport.getTaxFlagName(),
                 //税率值 taxRatio
//                 String.valueOf(costReport.getTaxRatio()),
                 bigDecimalToString(costReport.getTaxRatio()),
                 //人数分布 distributeType
                 costReport.getDistributeTypeName()
         };
         HSSFRow rowMain = sheet.createRow(i+1);
         //数据循环
         for( int j=0;j<objects.length;j++){
             if (j==6||j==7||j==8||j==13){
                 String object = objects[j];
                 BigDecimal bigDecimal = new BigDecimal(object);
                 rowMain.createCell(j).setCellValue(bigDecimal.doubleValue());
             }else {
                 rowMain.createCell(j).setCellValue(objects[j]);
             }
         }
     }
       //设置样式
       useCellStyle(sheet,wb);

       }
       //合同生成详情导出方法
       public  static <T> void exportContractBill(HSSFWorkbook wb, String name,  List<T> list ) {
           //在webbook中添加一个sheet,对应Excel文件中的sheet
           HSSFSheet sheet = wb.createSheet(name);
           //sheet.setDisplayGridlines (false);//去除网格线

           //创建第一行hu
           HSSFRow row = sheet.createRow(0);
           String[] headSub ={
                   FIELD_ONE,//合同编号
                   FIELD_TWO,//合同名称
                   FIELD_FIVE,
                   FIELD_SIX,
                   FIELD_BILL_MONTH,
                   FIELD_BILL_TRUE

           };
           //表头样式
           HSSFCellStyle style =  setCellStyle(wb, false,  "5", null, BorderStyle.THIN);
           HSSFCellStyle styleTwo = setCellStyle(wb,  false, "8", null, BorderStyle.THIN);//数据样式
           //表头循环
           for( int i=0;i<headSub.length;i++){
               row.createCell(i).setCellValue(headSub[i]);
               row.getCell(i).setCellStyle(style);//设置样式字体
           }
           //子项数据填充
           for (int i =0 ;i<list.size();i++) {
               BillContractReport billContractReport = (BillContractReport) list.get(i);
               String[] objects={
                       billContractReport.getContractNo(),
                       billContractReport.getContractName(),
                       billContractReport.getCustNo(),
                       billContractReport.getCustName(),
                       String.valueOf(billContractReport.getBillMonth()),
                       billContractReport.getIsTrueBillType()==1?"是":"否",
               };
               HSSFRow rowMain = sheet.createRow(i+1);
               //数据循环
               for( int j=0;j<objects.length;j++){
                   rowMain.createCell(j).setCellValue(objects[j]);
                   rowMain.getCell(j).setCellStyle(styleTwo);//设置样式字体
               }
           }
           //设置样式
          // useCellStyle(sheet,wb);
           setSizeColumn(sheet,6,sheet.getLastRowNum()+1);//自适应宽度长度不明显
           //设置宽度
           for (int i =0;i<6;i++){
               sheet.setColumnWidth(i , 7000);
           }
           for (int i =0;i<sheet.getLastRowNum()+1;i++){
               sheet.getRow(i).setHeight((short) 460);
           }


       }

       /**
        * row 第几行
        * */
//设置样式
    public  static HSSFCellStyle setCellStyle(HSSFWorkbook wb, boolean isBold,  String type, CellRangeAddress address,BorderStyle i){
        HSSFCellStyle style = wb.createCellStyle();
        HSSFSheet sheet = wb.getSheet("社保公积金一览单个城市");
       //设置样式
        HSSFFont font = wb.createFont();//创建一个字体
        font.setFontHeightInPoints((short) 10);
        font.setFontName("SimSun");
        //font.setItalic(false); 字体是否斜体

        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBottomBorderColor (HSSFColor.BLACK.index);
        style.setLeftBorderColor (HSSFColor.BLACK.index);
        style.setRightBorderColor (HSSFColor.BLACK.index);
        style.setTopBorderColor (HSSFColor.BLACK.index);
        style.setBorderBottom (BorderStyle.THIN);//下边框
        style.setBorderLeft (BorderStyle.THIN);//左边框
        style.setBorderRight (BorderStyle.THIN);//右边框
        style.setBorderTop (BorderStyle.THIN); //上边框
        font.setBold (true);//设置是否加粗

        switch (type) {
            case "1":
                font.setColor(HSSFColor.WHITE.index);
                style.setFillForegroundColor(IndexedColors.INDIGO.getIndex());//填充单元格颜色
                break;
            case "2":
                font.setBold (true);//设置是否加粗
                font.setColor(HSSFColor.BLACK.index);
                break;
            case "3":
                 font.setColor(HSSFColor.RED.index);
                break;
            case "4":
                font.setColor(HSSFColor.DARK_TEAL.index);
                style.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());//填充单元格颜色
                style.setAlignment (HorizontalAlignment.CENTER);//水平居中
                style.setVerticalAlignment (VerticalAlignment.CENTER);//垂直居中
                RegionUtil.setBorderBottom (i, address, sheet); // 下边框
                RegionUtil.setBorderLeft (i, address, sheet); // 左边框
                RegionUtil.setBorderRight (i, address, sheet); // 有边框
                RegionUtil.setBorderTop (i, address, sheet); // 上边框
                RegionUtil.setBottomBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setLeftBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setRightBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setTopBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                break;
            case "5":
                font.setColor(HSSFColor.DARK_TEAL.index);
                style.setAlignment (HorizontalAlignment.CENTER);//水平居中
                style.setVerticalAlignment (VerticalAlignment.CENTER);//垂直居中
                break;
            case "6":
                font.setColor(HSSFColor.BLACK.index);
                font.setBold (false);//设置是否加粗
                style.setAlignment (HorizontalAlignment.LEFT);//水平居中
                style.setVerticalAlignment (VerticalAlignment.CENTER);//垂直居中
                RegionUtil.setBorderBottom (i, address, sheet); // 下边框
                RegionUtil.setBorderLeft (i, address, sheet); // 左边框
                RegionUtil.setBorderRight (i, address, sheet); // 有边框
                RegionUtil.setBorderTop (i, address, sheet); // 上边框
                RegionUtil.setBottomBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setLeftBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setRightBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                RegionUtil.setTopBorderColor (HSSFColor.DARK_BLUE.index, address, sheet);
                break;
            case "7":
                font.setColor(HSSFColor.BLACK.index);
                font.setBold (false);//设置是否加粗
                style.setAlignment (HorizontalAlignment.LEFT);//水平靠左
                style.setVerticalAlignment (VerticalAlignment.CENTER);//垂直居中
                break;
            case "8":
                font.setColor(HSSFColor.BLACK.index);
                style.setAlignment (HorizontalAlignment.CENTER);//水平居中
                style.setVerticalAlignment (VerticalAlignment.CENTER);//垂直居中
                font.setBold (false);//设置是否加粗
                break;

        }
        style.setFont(font);
        return  style;
       //



    }
//具体格式套用
public  static void useCellStyle(HSSFSheet sheet,HSSFWorkbook wb){
    //设置样式
    HSSFCellStyle style =  setCellStyle(wb, false,  "5", null, BorderStyle.THIN);
    for (int j=0;j<15;j++) {
        //logger.info("循环++++++"+i +"J+++++"+j);
        sheet.getRow(0).getCell(j).setCellStyle(style);
    }
    HSSFCellStyle styleTwo = setCellStyle(wb,  false, "8", null, BorderStyle.THIN);;
    for (int i=1;i<sheet.getLastRowNum()+1;i++){
         //logger.info("循环+++++++i"+sheet.getLastRowNum());
        for (int j=0;j<15;j++) {
            sheet.getRow(i).getCell(j).setCellStyle(styleTwo);

        }

    }
    //自适应宽度跟长度
    setSizeColumn(sheet,15,sheet.getLastRowNum()+1);//自适应宽度长度不明显
    //设置宽度
    for (int i =0;i<6;i++){
        sheet.setColumnWidth(i , 7000);
    }
    for (int i =6;i<15;i++){
        sheet.setColumnWidth(i , 4500);
    }
    for (int i =0;i<sheet.getLastRowNum()+1;i++){
        sheet.getRow(i).setHeight((short) 460);
    }
}
//设置自适应宽度
    private static void setSizeColumn(HSSFSheet sheet, int size, int isSize) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth (columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum (); rowNum++) {
                HSSFRow currentRow;
                //当前行未被使用过
                if (sheet.getRow (rowNum) == null) {
                    currentRow = sheet.createRow (rowNum);
                } else {
                    currentRow = sheet.getRow (rowNum);
                }
                if (isSize != 0) {
                    currentRow.setHeight ((short) 460);
                }
                if (currentRow.getCell (columnNum) != null) {
                    HSSFCell currentCell = currentRow.getCell (columnNum);
                    if (currentCell.getCellType () == HSSFCell.CELL_TYPE_STRING) {
                        int length = (currentCell.getStringCellValue ().getBytes ().length + size) / 4;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth (columnNum, columnWidth * 256);
        }
    }

    private static String bigDecimalToString(BigDecimal bigDecimal){
        return String.valueOf(Optional.ofNullable(bigDecimal).orElse(BigDecimal.ZERO));
    }

}
