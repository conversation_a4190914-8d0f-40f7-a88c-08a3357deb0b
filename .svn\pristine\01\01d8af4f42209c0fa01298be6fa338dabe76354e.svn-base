package com.reon.hr.sp.base.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.SscUpdateLogVo;
import com.reon.hr.sp.base.dao.sys.SscUpdateLogMapper;
import com.reon.hr.sp.base.service.sys.SscUpdateLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (SscUpdateLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-20 10:04:22
 */
@Service("sscUpdateLogService")
public class SscUpdateLogServiceImpl implements SscUpdateLogService {
    @Resource
    private SscUpdateLogMapper sscUpdateLogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public SscUpdateLogVo queryById(Long id) {
        return sscUpdateLogMapper.queryById(id);
    }

    @Override
    public List<SscUpdateLogVo> queryBySscId(Long sscId) {
        return sscUpdateLogMapper.queryBySscId(sscId);
    }

    /**
     * 分页查询
     *
     * @param sscUpdateLog 筛选条件
     * @param page                页面
     * @param limit               限制
     * @return 查询结果
     */
    @Override
    public Page<SscUpdateLogVo> queryByPage(Integer page, Integer limit, SscUpdateLogVo sscUpdateLog) {
        Page<SscUpdateLogVo> pageRequest = new Page<>(page, limit);
        pageRequest.setRecords(sscUpdateLogMapper.queryAllByLimit(sscUpdateLog, pageRequest));
        return pageRequest;
    }

    /**
     * 新增数据
     *
     * @param sscUpdateLogVo 实例对象
     * @return 实例对象
     */
    @Override
    public SscUpdateLogVo insert(SscUpdateLogVo sscUpdateLogVo) {
        sscUpdateLogMapper.insert(sscUpdateLogVo);
        return sscUpdateLogVo;
    }

    @Override
    public int insertBatch(List<SscUpdateLogVo> entities) {
        return sscUpdateLogMapper.insertBatch(entities);
    }

    /**
     * 修改数据
     *
     * @param sscUpdateLogVo 实例对象
     * @return 实例对象
     */
    @Override
    public SscUpdateLogVo update(SscUpdateLogVo sscUpdateLogVo) {
        sscUpdateLogMapper.update(sscUpdateLogVo);
        return this.queryById(sscUpdateLogVo.getId());
    }
    
    @Override
    public int updateBatch(List<SscUpdateLogVo> entities) {
        return sscUpdateLogMapper.updateBatch(entities);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return sscUpdateLogMapper.deleteById(id) > 0;
    }
}
