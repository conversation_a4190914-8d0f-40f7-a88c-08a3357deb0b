<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2020/6/11
  Time: 13:31
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>

<html>
<head>
    <title></title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">


    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>

    <style>
        .layui-elip {
            width: 130px;
        }

        .layui-table th {
            text-align: center;
        }

        .layui-inline {
            margin: 10px 10px 10px 10px;
        }

        .layui-form-item {
            margin: 0px 0px 0px 0px;
        }
        .inlineLong{
            width: 750px;
        }
        .textareaLong{
            width:570px;
        }
        .inlineLongTwo{
            width: 720px;
            /*text-align:left;*/
        }
        form .layui-inline {
            padding-bottom: 10px;
        }
        .tableSelect_btn_search {
            visibility: hidden;
        }
    </style>
</head>
<body>
<div class="layui-tab layui-tab-card" style="height: 70%">
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <div class="layui-fluid">
                <div class="layui-card">
                    <input type="hidden" name="loginName" id="loginName">
                    <form class="layui-form" id="searchForm">
                        <input type="hidden" name="id" id="id">
                        <input type="hidden" name="optType" id="optType">
                        <table class="layui-table table-mini" id="insuranceRatioTable" lay-skin="nob" style="width: 70%;margin: 0 auto;">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label layui-elip" title="所属城市"><i style="color: red;">*</i>所属城市：</label>
                                    <div class="layui-input-inline">
                                        <select name="cityCode" id="cityCode" lay-filter="cityCodeFilter" lay-search
                                                AREA_TYPE
                                                lay-verify="required" lay-verType="tips" autocomplete="off" disabled>
                                            <option value=""></option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label layui-elip" title="服务网点"><i style="color: red">*</i>服务网点：</label>
                                    <div class="layui-input-inline">
                                        <input class="layui-select" name="serviceSiteName" id="serviceSiteName"  disabled>
                                    </div>
                                </div>
                            </div>
                        </table>
                        <div class="layui-table-mini">
                            <table class="layui-hide" id="insuranceBaseId" lay-filter="insuranceBaseFilter"></table>
                        </div>

                        <div class="layui-inline" style="float: right;padding: 10px;" id="myDiv">
                            <button type="submit" class="layui-btn layuiadmin-btn-list" lay-submit=""
                                    lay-filter="submitFilter" id="submit">立即提交
                            </button>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/numberCheck.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/socialWagsMaintain/updateSocialWagsMaintainPage.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js"></script>
<script type="text/javascript" src="${ctx}/js/tableDate.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js"></script>
<script type="text/jsp" id="toolDemo">
    <a href="javascript:void(0)" title="新增" lay-event="add"><b class="layui-btn layui-btn-sm">新增</b></a>
    <a href="javascript:void(0)" title="删除" lay-event="delete"><b class="layui-btn layui-btn-sm">删除</b></a>
</script>
</html>
