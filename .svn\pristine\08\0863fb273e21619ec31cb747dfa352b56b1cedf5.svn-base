<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .layui-table-cell {overflow: visible;}
        /* 使得下拉框与单元格刚好合适 */
        .layui-table-box {
            overflow: visible;
        }
        .layui-table-body {
            overflow: visible;
        }
        td .layui-form-select{
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
        }

    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post">
    <input type="hidden" name="id" id="supplierId" value="${supplierId}">
    <input type="hidden" name="contractId" id="contractId">
    <input type="hidden" name="optType" id="optType" value="${optType}">
    <table class="layui-table" lay-skin="nob" style="width: 95%;margin: 0 auto;">
        <tr>
            <td align="right" width="11%"><i style="color: red; font-weight: bolder;">*</i>供应商名称：</td>
            <td colspan="3"><input class="layui-input" type="text" name="supplierName" id="supplierName" maxlength="30" autocomplete="off" lay-verify="required"></td>
            <td width="11%" align="right"><i style="color: red; font-weight: bolder;">*</i>联系人：</td>
            <td width="14%"><input class="layui-input" type="text" name="contactor" id="contactor" maxlength="10" autocomplete="off" lay-verify="required"></td>
            <td width="8.5%" align="right"><i style="color: red; font-weight: bolder;">*</i>联系方式</td>
            <td width="14%"><input class="layui-input" type="tel" maxlength="15" minlength="7" autocomplete="off" name="tel" id="tel" lay-verify="required"></td>
        </tr>
        <tr>
            <td width="10%" align="right"><i style="color: red; font-weight: bolder;">*</i>邮箱：</td>
            <td width="14%"><input class="layui-input" type="email" name="email" autocomplete="off" maxlength="50" id="email" lay-verify="required|email"></td>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>供应商地址：</td>
            <td colspan="3"><input class="layui-input" type="text" name="addr" autocomplete="off" maxlength="50" id="addr" lay-verify="required"></td>
        </tr>
    </table>
    <hr class="layui-bg-green">
    <table class="layui-table" lay-skin="nob" style="width: 95%;margin: 0 auto;">
<%--        <tr>--%>
<%--            <td width="14%" align="right"><i style="color: red;font-weight: bolder; ">*</i>合同类型：</td>--%>
<%--            <td width="12.5%">--%>
<%--                <select class="layui-select" name="contractType" id="contractType" DICT_TYPE="CONTRACT_TYPE" lay-verify="required">--%>
<%--                    <option value=""></option>--%>
<%--                </select></td>--%>
<%--            <td width="10%" align="right"><i style="color: red; font-weight: bolder;">*</i>产品类型：</td>--%>
<%--            <td colspan="5"><select class="layui-select" name="prodType" id="prodType" DICT_TYPE="CONTRACT_PRDO_TYPE" lay-verify="required" >--%>
<%--                <option value=""></option>--%>
<%--            </select>--%>
<%--            </td>--%>
<%--        </tr>--%>
<%--        <tr>--%>
<%--            <td align="right"><i style="color: red; font-weight: bolder;">*</i>服务费（含税）：</td>--%>
<%--            <td><input class="layui-input" type="number" name="fee" id="fee2" autocomplete="off" maxlength="10" lay-verify="required|number" autocomplete="off"></td>--%>
<%--            <td width="9.5%" align="right"><i style="color: red; font-weight: bolder;">*</i>起止日期：</td>--%>
<%--            <td width="20%">--%>
<%--                <div class="layui-inline">--%>
<%--                    <input type="text" class="layui-input" id="startTime" name="startTime" lay-verify="required|date" placeholder="yyyy-MM-dd" size="8" readonly>--%>
<%--                </div>--%>
<%--                <div class="layui-inline">--%>
<%--                    <input type="text" class="layui-input" id="endTime" name="endTime" lay-verify="required|date" placeholder="yyyy-MM-dd" size="8"--%>
<%--                           readonly>--%>
<%--                </div>--%>
<%--            </td>--%>
<%--            <td width="8%" align="right"><i style="color: red; font-weight: bolder;">*</i>账单日：</td>--%>
<%--            <td width="9%"><input class="layui-input" type="tel" maxlength="2" oninput = "value=value.replace(/[^\d]/g,'')"  autocomplete="off" name="billDate" id="billDate"--%>
<%--                                  lay-verify="required|number"></td>--%>
<%--            <td width="8%" align="right"><i style="color: red; font-weight: bolder;">*</i>付款日：</td>--%>
<%--            <td width="9%"><input class="layui-input" type="tel" name="payDate" autocomplete="off" maxlength="2"  oninput = "value=value.replace(/[^\d]/g,'')" id="payDate"--%>
<%--                                  lay-verify="required|number"></td>--%>
<%--        </tr>--%>
<%--        <tr>--%>
<%--            <td align="right"><i style="color: red; font-weight: bolder;">*</i>附件：</td>--%>
<%--            <td><a href="javascript:void(0);"  id="uploadBtn"><i class="layui-icon layui-icon-upload"></i>上传文件</a> </td>--%>
<%--            <td colspan="5"><div id="fileListId"></div></td>--%>
<%--            &lt;%&ndash;<td><a href="javascript:void(0);" id="uploadListAction">开始上传</a></td>&ndash;%&gt;--%>
<%--        </tr>--%>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>服务城市：</td>
            <td colspan="7">
                <table class="layui-hide" id="areaGrid" lay-filter="areaGridFilter"></table>
            </td>
        </tr>
<%--        <tr>--%>
<%--            <td align="right">备注：</td>--%>
<%--            <td colspan="5">--%>
<%--                <textarea name="remark" id="remark" autocomplete="off" placeholder="请输入内容" class="layui-textarea"></textarea>--%>
<%--            </td>--%>
<%--            <td></td>--%>
<%--            <td></td>--%>
<%--        </tr>--%>
    </table>
    <div style="float: right; margin-right: 100px;">
        <%--<button class="layui-btn" lay-submit lay-filter="submitFilter" id="submitBtn">提交</button>--%>
        <button class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn" authURI="/customer/supplier/save">保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
        <button class="layui-btn" type="button" id="closeBtn" style="display: none;">关闭</button>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/supplier/editSupplier.js?v=${publishVersion}"></script>
<script type="text/jsp" id="provinceId">
    <select class="layui-select layui-form-select" dataId="{{d.provinceCode}}" name="provinceCode" lay-search class="provinceCode" lay-verify="required" lay-filter="provinceFilter">
        <option value=""></option>
        {{# layui.each(window.top['province'], function(index, item){
        var code=(item.code+"").substring(0,2);
        }}
        <option value={{code}} {{ d.provinceCode==code?'selected':'' }}  >{{ item.name }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/jsp" id="cityId">
    <select class="layui-select layui-form-select" dataId="{{d.cityCode}}" name="cityCode" lay-search class="cityCode" lay-verify="required" CITYS lay-filter="cityFilter">
        <option value=""></option>
        {{# layui.each(window.top['province'], function(index, item){
           var code=(item.code+"").substring(0,2);
           if(code == d.provinceCode){
        layui.each(item.children,function(index2,i){
        var cityCode=(i.code+"").substring(2,6);
        }}
        <option value={{cityCode}} {{ d.cityCode==cityCode?'selected':'' }}  >{{ i.name }}</option>
        {{#  });  }
        }); }}
    </select>
</script>
<script type="text/jsp" id="enableId">
    <select class="layui-select" dataId="{{d.enable}}" name="enable" id="enable" lay-filter="enableFilter">
        <option value=""></option>
        {{# layui.each(window.top['dictCachePool']['BOOLEAN_TYPE'], function(index, item){ }}
        <option value={{item.code}} {{ d.enable==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/jsp" id="priorityFlagId">
    <select class="layui-select" dataId="{{d.priorityFlag}}" name="priorityFlag" id="priorityFlag" lay-filter="priorityFilter">
        <option value=""></option>
        {{# layui.each(window.top['dictCachePool']['BOOLEAN_TYPE'], function(index, item){ }}
        <option value={{item.code}} {{ d.priorityFlag==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/jsp" id="toolDemo">
    <a href="javascript:void(0)" title="新增" lay-event="add"><i class="layui-icon layui-icon-add-1"></i></a>
    <a href="javascript:void(0)" title="删除" lay-event="delete"><i class="layui-icon layui-icon-delete"></i></a>
</script>


</body>
</html>