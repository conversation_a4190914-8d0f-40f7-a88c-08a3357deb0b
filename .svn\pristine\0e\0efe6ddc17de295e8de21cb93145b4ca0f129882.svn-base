var ctx = ML.contextPath;

layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'upload', 'tableSelect', 'table'], function () {
    var $ = layui.$,
        form = layui.form,
        layer = layui.layer, table = layui.table,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    let quotationVoMap;
    $(document).ready(function () {
        var quotationVoMapStr = $('#quotationVoMap').val();
        if (ML.isNotEmpty(quotationVoMapStr)) {
            quotationVoMap = JSON.parse(quotationVoMapStr);
            renderQuotation();
        }
    });

    function renderQuotation() {
        let oldQuoteNo = $('#oldQuoteNo').val();
        for (let quoteNoAndName in quotationVoMap) {
            let split = quoteNoAndName.split('_');
            let quoteNo = split[0];
            if(quoteNo == oldQuoteNo){
                let quoteObj = quotationVoMap[quoteNoAndName];
                $("#oldQuoteNo").append("<option/>").append($("<option/>").text(split[1]).attr("value", quoteNo));;
                $('#oldAmount').val(quoteObj.serviceFee);
                $("#oldQuoteType").find("option[value = '" + quoteObj.serviceFeeType + "']").attr("selected", "selected");
                if(ML.isNotEmpty(quoteObj.cityCode)){
                    var cityCodes = quoteObj.cityCode.split(",");
                    renderCityName(cityCodes,"oldCity");
                }
            } else {
                $("#newQuoteNo2").append("<option/>").append($("<option/>").text(split[1]).attr("value", split[0]));
            }
        }

        form.render("select");
    }

    function renderCityName(cityCodes,id) {
        let cityNames = ""
        for (let j = 0; j < cityCodes.length; j++) {
            layui.each(window.top['area'], function (index, item) {
                if (cityCodes[j] ==item.code) {
                    cityNames += item.name + ",";
                }
            });
        }
        if(ML.isNotEmpty(cityNames)){
            cityNames = cityNames.slice(0,cityNames.length-1);
        }
        $("#"+id).val(cityNames);
    }

    form.on('select(newQuoteNo2Filter)', function (data) {
        $("#bindRevTempletId").empty().val('');
        let quoteName = data.elem.selectedOptions[0].text;
        let quoteNo = data.elem.selectedOptions[0].value;
        let quote = quotationVoMap[quoteNo + '_' + quoteName.trim()];
        if (ML.isNotEmpty(quote)) {
            $('#newQuoteNo').val(quoteNo);
            $('#newAmount').val(quote.serviceFee);
            $("#newQuoteType").find("option[value = '" + quote.serviceFeeType + "']").attr("selected", "selected");
            if(ML.isNotEmpty(quote.cityCode)){
                var cityCodes = quote.cityCode.split(",");
                renderCityName(cityCodes,"newCity");
            }
        }
        form.render("select");
    });


    $(document).on('click', '#save', function () {
        let supplierId = $('#supplierId').val();
        let oldQuoteNo = $('#oldQuoteNo').val();
        let newQuoteNo = $('#newQuoteNo').val();
        let revStartMonth = $('#startMonth').val();
        let contractAreaNosStr = $('#contractAreaNos').val();
        if(oldQuoteNo == newQuoteNo){
            layer.msg("新旧报价单一直，无需变更！");
            return
        }
        let params = {
            supplierId: supplierId,
            quoteNo: newQuoteNo,
            revStartMonth: revStartMonth,
            contractAreaNoStr: contractAreaNosStr,
            operateType: 2,
            relativeType: 2
        };
        $.ajax({
            url: ctx + "/customer/supplierConArea/handleSupContractAreaBusiness",
            type: 'POST',
            contentType: "application/json",
            data: JSON.stringify(params),
            success: function (data) {
                if (data.code == 0) {
                    layer.msg("绑定成功！");
                    layer.closeAll();
                } else {
                    return layer.msg(data.msg)
                }
            },
        });
    });

    laydate.render({
        elem: '#startMonth'
        , trigger: 'click'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , type: 'month'
        , theme: 'grid'
        , calendar: true
        , format: 'yyyyMM'
        , showBottom: true
        , done: function (value, date, endDate) {

        }
    });

    $(document).on('click', '#cancel', function () {
        layer.closeAll();
    });
})
