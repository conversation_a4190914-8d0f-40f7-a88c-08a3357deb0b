package com.reon.hr.sp.customer.service.supplierPractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierSyncCfgVo;
import com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice.SupplierSyncCfg;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplierSyncCfgService {

    Page<SupplierSyncCfgVo> getSupplierSyncCfgVoPage(SupplierSyncCfgVo vo, Integer page , Integer limit);

    void deleteSupplierSyncCfgByIds(List<Long> ids);

    void addSupplierSyncCfgVo(SupplierSyncCfgVo supplierSyncCfgVo);

    void editSupplierSyncCfgVo(SupplierSyncCfgVo supplierSyncCfgVo);

    Integer getSupplierSyncCfgVoCount(SupplierSyncCfgVo vo);

    List<SupplierSyncCfgVo> getSupplierSyncCfgVoBySupIdAndCityCode(List<String> strings);

    List<SupplierSyncCfg> getSupplierSyncCfgVo(SupplierSyncCfgVo vo);
}
