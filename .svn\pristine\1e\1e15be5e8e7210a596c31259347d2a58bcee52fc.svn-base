<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../common/taglibs.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>合同流程审批</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <div class="layui-inline queryTable">
        <form class="layui-form" id="searchForm" action="" method="post">
            <div class="layui-input-inline" style="
    width: 250px;
">
                <label class="layui-form-label"
                       style="font-weight:800;width: 50px;padding-right: 5px;">提交人</label>
                <div class="layui-input-block" style="
    margin-left: 90px;
">
                    <select name="seller" id="seller" lay-search>
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-input-inline" style="width: 240px;">
                <label class="layui-form-label" style="font-weight:800;width: 40px;padding-left: 5px;">客户</label>
                <div class="layui-input-block" style=" margin-left: 60px;">
                    <input type="text" id="custName" value="" placeholder="请输入" class="layui-input"
                           autocomplete="off" style="
    width: 172px;
">
                    <input type="hidden" id="custId" value="" name="custId" value="">
                    <input type="hidden" id="processDefinitionKey" value="2" name="processDefinitionKey">
                </div>
            </div>

            <div class="layui-input-inline" style="  width: 260px;">
                <label class="layui-form-label" style="font-weight:800;width: 70px;padding-left: 0px;">合同名称</label>
                <div class="layui-input-block" style="   margin-left: 90px;">
                    <input type="text" id="contractName" name="contractName" placeholder="请输入" class="layui-input"
                           autocomplete="off" style="   width: 182px;">
                </div>
            </div>
            <div class="layui-input-inline" style="   width: 260px;">
                <label class="layui-form-label" style="font-weight:800;width: 70px;padding-left: 0px;">合同编号</label>
                <div class="layui-input-block" style=" margin-left: 90px;">
                    <input type="text" id="contractNo" name="contractNo" placeholder="请输入" class="layui-input"
                           autocomplete="off" style=" width: 182px;">
                </div>
            </div>

            <div class="layui-input-inline" style="   width: 290px;">
                <label class="layui-form-label" style="font-weight:800;width: 90px;">创建时间起始</label>
                <div class="layui-input-block" style="   margin-left: 110px;">
                    <input type="text" id="dateS" name="dateS" placeholder="请输入" class="layui-input"
                           autocomplete="off" style="  padding-left: 10px;   width: 162px;">
                </div>
            </div>

            <div class="layui-input-inline" style="   width: 280px;">
                <label class="layui-form-label"
                       style="font-weight:800;width: 90px;padding-right: 0px;padding-left: 0px;">创建时间截止</label>
                <div class="layui-input-block">
                    <input type="text" id="dateE" name="dateE" placeholder="请输入" class="layui-input"
                           autocomplete="off" style="    width: 162px;">
                </div>
            </div>

            <button class="layui-btn" lay-submit="" id="btnQuery" lay-filter="btnQueryFilter" type="button"
                    authuri="/workflow/getTaskList">查询
            </button>
            <button class="layui-btn" id="reset" type="reset" lay-filter="resetFilter" lay-event="resetEvent">重置
            </button>
        </form>
    </div>


</blockquote>

<table class="layui-hide" id="taskGrid" lay-filter="taskFilter"></table>

<script type="text/jsp" id="tooltask">
    {{# if(d.name != "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="taskApproval" lay-event="approval">审批</a>
    {{# } }}


    {{# if(d.name == "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="updateFinalFile" lay-event="updateFinalFile">上传最终合同</a>
    {{# } }}
</script>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/workflow/taskListPageContract.js?v=${publishVersion}"></script>
</body>
</html>
