/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/12/23
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.salary;

import com.reon.hr.api.customer.anno.Excel;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalaryEmployeeBankCardVo
 *
 * @date 2020/12/23 14:34
 */
@Data
public class SalaryEmployeeBankCardVo implements Serializable {
    private static final long serialVersionUID = 1985831121829421040L;

    private Long id;
    private Long empId;
    private Long cardId;

    @Excel(name = "唯一号")
    private String employeeNo;

    @Excel(name = "雇员姓名")
    private String name;

    @Excel(name = "证件号码")
    private String certNo;

    @Excel(name = "银行卡号")
    private String cardNo;

    @Excel(name = "开户人姓名")
    private String acctName;

    @Excel(name = "开户分支行")
    private String subBank;

    @Excel(name = "开户银行", readConverterExp = "1=工商银行,2=农业银行,3=中国银行,4=建设银行,5=交通银行,6=招商银行,7=浦发银行,8=其它银行")
    private Integer bankName;

    @Excel(name = "其他银行")
    private String otherBank;

    @Excel(name = "开户行省份")
    private String openProvince;

    @Excel(name = "开户行城市")
    private String openingPlace;

    @Excel(name = "是否有效", readConverterExp = "1=否,2=是")
    private Integer validity;

    @Excel(name = "业务类型", readConverterExp = "1=代发工资,2=商保,3=非纯代发工资")
    private Integer businessType;

    @Excel(name = "备注")
    private String remark;

    private Date createTime;

    private String custNo;

    private String contractNo;

    private String contractAreaNo;

    private String email;

    private String creator;

    private String loginName;

    private String updater;

    private Integer certType;

    private String mobile;

    private List<OrgPositionDto> userOrgPositionDtoList;

    private String custName;

    private boolean nameFlag = true;
    private Integer competitionFlag;

}
