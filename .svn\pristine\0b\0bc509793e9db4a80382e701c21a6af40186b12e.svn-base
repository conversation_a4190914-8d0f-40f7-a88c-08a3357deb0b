<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.employee.EmployeeTransferInfoMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.employee.EmpOrderTransferInfo">
    </resultMap>
    <sql id="baseColumn">
        chg_no,chg_name,recceiving_type,old_contract_area_no,new_contract_area_no,new_quote_no,effective_month
            ,status,failure_reason,remark,creator,create_time,updater,update_time,del_flag
    </sql>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="chg_no">
        insert into employee_order_transfer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="chgNo != null">
                chg_no,
            </if>
            <if test="chgName != null">
                chg_name,
            </if>
            <if test="recceivingType != null">
                recceiving_type,
            </if>
            <if test="oldContractAreaNo != null">
                old_contract_area_no,
            </if>
            <if test="newContractAreaNo != null">
                new_contract_area_no,
            </if>
            <if test="effectiveMonth != null">
                effective_month,
            </if>
            <if test="newQuoteNo != null">
                new_quote_no,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chgNo != null">
                #{chgNo},
            </if>
            <if test="chgName != null">
                #{chgName},
            </if>
            <if test="recceivingType != null">
                #{recceivingType},
            </if>
            <if test="oldContractAreaNo != null">
                #{oldContractAreaNo},
            </if>
            <if test="newContractAreaNo != null">
                #{newContractAreaNo},
            </if>
            <if test="effectiveMonth != null">
                #{effectiveMonth},
            </if>
            <if test="newQuoteNo != null">
                #{newQuoteNo},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>


    <select id="selectEmpOrderTransferInfoByPage"
            resultType="com.reon.hr.api.customer.vo.employee.EmpOrderTransferInfoVo">
        select
        <include refid="baseColumn"/>,
        (select count(1) as num
        from employee_order_transfer_detail t2
        where t2.chg_no = t1.chg_no and t2.status = 0)                                                               as successNum,
        (select count(1) as num
        from employee_order_transfer_detail t2
        where t2.chg_no = t1.chg_no
        and t2.status = 1)                                                                                         as failNum
        from employee_order_transfer_info t1
        <where>
            <if test="chgNo != null and chgNo != ''">
                and chg_no like concat('%',#{chgNo},'%')
            </if>
            <if test="chgName != null and chgName != ''">
                and chg_name like concat('%',#{chgName},'%')
            </if>
            <if test="oldContractAreaNo != null and oldContractAreaNo != ''">
                and old_contract_area_no like concat('%',#{oldContractAreaNo},'%')
            </if>
            <if test="newContractAreaNo != null and newContractAreaNo != ''">
                and new_contract_area_no like concat('%',#{newContractAreaNo},'%')
            </if>
            <if test="effectiveMonth != null">
                and effective_month = #{effectiveMonth}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectEmpOrderTransferInfoByChgNo"
            resultType="com.reon.hr.api.customer.vo.employee.EmpOrderTransferInfoVo">
        select
        <include refid="baseColumn"/>
        from employee_order_transfer_info where chg_no = #{chgNo}
    </select>


    <select id="getSuccessEmpOrderTransferInfo" resultType="com.reon.hr.api.customer.vo.employee.EmpOrderTransferInfoVo">
        select distinct  t1.chg_no, t1.old_contract_area_no, t1.new_contract_area_no
        from employee_order_transfer_info t1
                 left join employee_order_transfer_detail t2 on t1.chg_no = t2.chg_no
        where t2.status = 0
          and t1.del_flag = 'N'
          and t2.del_flag = 'N'
    </select>


</mapper>
