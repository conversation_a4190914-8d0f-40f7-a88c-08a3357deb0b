<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.reon.hr.sp.customer.dao.supplierPractice.OrderChangeTaskDetailMapper">



    <insert id="insertOrderChangeTaskDetailVo" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.OrderChangeTaskDetailVo">
        INSERT INTO order_change_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != taskInfoId and '' != taskInfoId">
                task_info_id,
            </if>
            <if test="null != beforeData and '' != beforeData">
                before_data,
            </if>
            <if test="null != afterData and '' != afterData">
                after_data,
            </if>
            <if test="null != creator and '' != creator">
                creator
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != taskInfoId and '' != taskInfoId">
                #{taskInfoId},
            </if>
            <if test="null != beforeData and '' != beforeData">
                #{beforeData},
            </if>
            <if test="null != afterData and '' != afterData">
                #{afterData},
            </if>
            <if test="null != creator and '' != creator">
                #{creator}
            </if>
        </trim>
    </insert>

    <update id="updateAfterJsonById">
        update order_change_task_detail set after_data = #{json} where id = #{id};
    </update>


</mapper>