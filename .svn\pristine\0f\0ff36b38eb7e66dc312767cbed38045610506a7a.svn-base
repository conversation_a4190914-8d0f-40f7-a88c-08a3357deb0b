/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/11/2
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.cmb;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.common.constant.CMBPayConstant;
import com.reon.hr.common.constant.InitCMBPayConstant;
import com.reon.hr.common.enums.ServiceNameType;


import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaySingleReqInfo
 * @description TODO
 * @date 2023/11/2 11:03
 */
public class PaySingleReqInfo extends ReqInfoBuilder{

    private Map<String,Object> bodyMap;

    public PaySingleReqInfo(String uId,TransAcctInfo transAcctInfo){
        super(uId);
        this.bodyMap = Maps.newHashMap();
        Map<String,String> modMap = Maps.newHashMap();
        //暂时写死，后续可能作为参数，供选择
        List<String> modSet = InitCMBPayConstant.CMB_FUN_MAP.get(this.getFunCode()).get(CMBPayConstant.PAY_SINGLE_BUS_CODE);
        modMap.put(CMBPayConstant.BUS_MODE_KEY, modSet.get(0));
        modMap.put(CMBPayConstant.BUS_CODE_KEY,CMBPayConstant.PAY_SINGLE_BUS_CODE);
        //模式信息
        bodyMap.put(CMBPayConstant.PAY_SINGLE_MODE_INFO_KEY, Lists.newArrayList(modMap));
        //支付明细信息
        bodyMap.put(CMBPayConstant.PAY_SINGLE_DETAIL_INFO_KEY,Lists.newArrayList(transAcctInfo));
    }

    @Override
    public Map<String, Object> getBodyMap() {
        return this.bodyMap;
    }

    @Override
    public String getFunCode() {
        return ServiceNameType.BB1PAYOP.getServiceCode();
    }


}
