<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.InsurancePracticePayBankConfigMapper">

    <resultMap id="BaseResultMap" type="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo" >
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="ratio_flag" property="ratioFlag" />
        <result column="ratio_pay_flag" property="ratioPayFlag" />
        <result column="ratio_receipt_flag" property="ratioReceiptFlag" />
        <result column="social_pay_flag" property="socialPayFlag" />
        <result column="social_practice_pay_flag" property="socialPracticePayFlag" />
        <result column="social_practice_pay_detail_flag" property="socialPracticePayDetailFlag" />
        <result column="provident_practice_pay_flag" property="providentPracticePayFlag" />
        <result column="provident_practice_pay_detail_flag" property="providentPracticePayDetailFlag" />
        <result column="dispatch_bank_no" property="dispatchBankNo" />
        <result column="dispatch_bank_name" property="dispatchBankName" />
        <result column="dispatch_bank_type" property="dispatchBankType" />
        <result column="receiving_bank_no" property="receivingBankNo" />
        <result column="receiving_bank_name" property="receivingBankName" />
        <result column="receiving_bank_type" property="receivingBankType" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
       id, org_code,
                org_name,
                ratio_flag,
                ratio_pay_flag,
                ratio_receipt_flag,
                social_pay_flag,
                social_practice_pay_flag,
                social_practice_pay_detail_flag,
                provident_practice_pay_flag,
                provident_practice_pay_detail_flag,
                dispatch_bank_no,
                dispatch_bank_name,
                dispatch_bank_type,
                receiving_bank_no,
                receiving_bank_name,
                receiving_bank_type,
                creator,
                create_time,
                updater,
                update_time
    </sql>

    <insert id="insertInsurancePracticePayBankConfig" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        INSERT INTO insurance_practice_pay_bank_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != orgCode and '' != orgCode">
                org_code,
            </if>
            <if test="null != orgName and '' != orgName">
                org_name,
            </if>
            <if test="null != ratioFlag ">
                ratio_flag,
            </if>
            <if test="null != ratioPayFlag ">
                ratio_pay_flag,
            </if>
            <if test="null != ratioReceiptFlag ">
                ratio_receipt_flag,
            </if>
            <if test="null != socialPayFlag ">
                social_pay_flag,
            </if>
            <if test="null != socialPracticePayFlag ">
                social_practice_pay_flag,
            </if>
            <if test="null != socialPracticePayDetailFlag ">
                social_practice_pay_detail_flag,
            </if>
            <if test="null != providentPracticePayFlag ">
                provident_practice_pay_flag,
            </if>
            <if test="null != providentPracticePayDetailFlag ">
                provident_practice_pay_detail_flag,
            </if>
            <if test="null != dispatchBankNo and '' != dispatchBankNo">
                dispatch_bank_no,
            </if>
            <if test="null != dispatchBankName and '' != dispatchBankName">
                dispatch_bank_name,
            </if>
            <if test="null != dispatchBankType ">
                dispatch_bank_type,
            </if>
            <if test="null != receivingBankNo and '' != receivingBankNo">
                receiving_bank_no,
            </if>
            <if test="null != receivingBankName and '' != receivingBankName">
                receiving_bank_name,
            </if>
            <if test="null != receivingBankType ">
                receiving_bank_type,
            </if>
            <if test="null != creator and '' != creator">
                creator,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updater and '' != updater">
                updater,
            </if>
            <if test="null != updateTime ">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != orgCode and '' != orgCode">
                #{orgCode},
            </if>
            <if test="null != orgName and '' != orgName">
                #{orgName},
            </if>
            <if test="null != ratioFlag ">
                #{ratioFlag},
            </if>
            <if test="null != ratioPayFlag ">
                #{ratioPayFlag},
            </if>
            <if test="null != ratioReceiptFlag ">
                #{ratioReceiptFlag},
            </if>
            <if test="null != socialPayFlag ">
                #{socialPayFlag},
            </if>
            <if test="null != socialPracticePayFlag ">
                #{socialPracticePayFlag},
            </if>
            <if test="null != socialPracticePayDetailFlag ">
                #{socialPracticePayDetailFlag},
            </if>
            <if test="null != providentPracticePayFlag ">
                #{providentPracticePayFlag},
            </if>
            <if test="null != providentPracticePayDetailFlag ">
                #{providentPracticePayDetailFlag},
            </if>
            <if test="null != dispatchBankNo and '' != dispatchBankNo">
                #{dispatchBankNo},
            </if>
            <if test="null != dispatchBankName and '' != dispatchBankName">
                #{dispatchBankName},
            </if>
            <if test="null != dispatchBankType ">
                #{dispatchBankType},
            </if>
            <if test="null != receivingBankNo and '' != receivingBankNo">
                #{receivingBankNo},
            </if>
            <if test="null != receivingBankName and '' != receivingBankName">
                #{receivingBankName},
            </if>
            <if test="null != receivingBankType ">
                #{receivingBankType},
            </if>
            <if test="null != creator and '' != creator">
                #{creator},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updater and '' != updater">
                #{updater},
            </if>
            <if test="null != updateTime ">
                #{updateTime}
            </if>
        </trim>
    </insert>



    <update id="updateInsurancePracticePayBankConfig" parameterType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        UPDATE insurance_practice_pay_bank_config
        <set>
            <if test="null != ratioFlag ">ratio_flag = #{ratioFlag},</if>
            <if test="null != ratioPayFlag ">ratio_pay_flag = #{ratioPayFlag},</if>
            <if test="null != ratioReceiptFlag ">ratio_receipt_flag = #{ratioReceiptFlag},</if>
            <if test="null != socialPayFlag ">social_pay_flag = #{socialPayFlag},</if>
            <if test="null != socialPracticePayFlag ">social_practice_pay_flag = #{socialPracticePayFlag},</if>
            <if test="null != socialPracticePayDetailFlag ">social_practice_pay_detail_flag = #{socialPracticePayDetailFlag},</if>
            <if test="null != providentPracticePayFlag ">provident_practice_pay_flag = #{providentPracticePayFlag},</if>
            <if test="null != providentPracticePayDetailFlag ">provident_practice_pay_detail_flag = #{providentPracticePayDetailFlag},</if>
            <if test="null != dispatchBankNo and '' != dispatchBankNo">dispatch_bank_no = #{dispatchBankNo},</if>
            <if test="null != dispatchBankName and '' != dispatchBankName">dispatch_bank_name = #{dispatchBankName},</if>
            <if test="null != dispatchBankType ">dispatch_bank_type = #{dispatchBankType},</if>
            <if test="null != receivingBankNo and '' != receivingBankNo">receiving_bank_no = #{receivingBankNo},</if>
            <if test="null != receivingBankName and '' != receivingBankName">receiving_bank_name = #{receivingBankName},</if>
            <if test="null != receivingBankType ">receiving_bank_type = #{receivingBankType},</if>
            <if test="null != creator and '' != creator">creator = #{creator},</if>
            <if test="null != createTime ">create_time = #{createTime},</if>
            <if test="null != updater and '' != updater">updater = #{updater},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="getInsurancePracticePayBankConfigList" resultType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        SELECT *
        FROM insurance_practice_pay_bank_config
       <where>
        <if test="vo.orgCode != null and vo.orgCode != ''">
            and org_code =#{vo.orgCode}
        </if>
        <if test="vo.ratioFlag != null and vo.ratioFlag != ''">
            and ratio_flag =#{vo.ratioFlag}
        </if>
        </where>
        order by update_time desc
    </select>


    <select id="getInsurancePracticePayBankConfigCountByOrgCode" resultType="int">
        select count(1) from insurance_practice_pay_bank_config where org_code = #{orgCode}
    </select>

    <select id="getInsurancePracticePayBankConfigById" resultType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        SELECT *
        FROM insurance_practice_pay_bank_config where id =#{id}
    </select>


    <select id="getInsurancePracticePayBankConfigByOrgCodeList" resultType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        select *
        from insurance_practice_pay_bank_config
        where org_code in
        <foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getInsurancePracticePayBankConfigByOrgCode" resultType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        SELECT *
        FROM insurance_practice_pay_bank_config where org_code = #{orgCode}
    </select>

    <select id="getAllInsurancePracticePayBankConfig" resultType="com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo">
        select *
        from insurance_practice_pay_bank_config
    </select>

</mapper>