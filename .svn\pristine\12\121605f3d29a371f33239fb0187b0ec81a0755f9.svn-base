package com.reon.hr.api.enums;

import java.util.Arrays;
import java.util.List;

public enum OrgTypeEnum {

    PART_COMPANY_DT("1", "分公司"),
    SALE_DT("2", "销售部"),
    CS_DT("3", "客服部"),
    FINANCIAL_DT("4", "财务部"),
    HR_ASSISTANT_DT("5", "人事部"),
    LEGAL_DT("6", "法务部"),
    SUPPLIER_DT("7", "供应商管理部"),
    BIG_AREA("8", "大区"),
    CITY("9", "城市"),
    BUSINESS_INSURANCE("10", "商保部");

    private String code;

    private String name;
    //正常部门
    public static final List<String> normalDepartmentList = Arrays.asList(
            SALE_DT.code,CS_DT.code
    );
    private OrgTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
