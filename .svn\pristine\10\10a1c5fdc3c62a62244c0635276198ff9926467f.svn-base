<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.BillSubCheckMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bill_check_id" jdbcType="BIGINT" property="billCheckId"/>
        <result column="bill_id" jdbcType="BIGINT" property="billId"/>
        <result column="check_amt" jdbcType="DECIMAL" property="checkAmt"/>
        <result column="adjust_flag" jdbcType="INTEGER" property="adjustFlag"/>
        <result column="adjust_amt" jdbcType="DECIMAL" property="adjustAmt"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , bill_check_id, bill_id, IFNULL(check_amt, 0.00) AS check_amt,  adjust_flag, IFNULL(adjust_amt, 0.00) as
        adjust_amt,creator, updater,
    create_time, update_time, del_flag, version
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_sub_check bsc
        where id = #{id,jdbcType=BIGINT} and bsc.del_flag = 'N'
    </select>
    <select id="selectListByCheckIdList" resultType="com.reon.hr.api.bill.vo.check.BillSubCheckVo">
        select
        id, bill_check_id, bill_id, IFNULL(check_amt, 0.00) AS checkAmt, adjust_flag, IFNULL(adjust_amt, 0.00) as
        adjustAmt, creator, updater,
        create_time, update_time, del_flag, version
        from bill_sub_check bsc
        where bill_check_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and bsc.del_flag = 'N'
    </select>
    <select id="getCheckAmtBySubCheckId" resultType="java.math.BigDecimal">
        select IFNULL(SUM(check_amt), 0.00)+IFNULL(SUM(adjust_amt), 0.00)
        from bill_sub_check bsc
        where id = #{subCheckId}
          and del_flag = 'N';
    </select>
    <select id="getAllCheckAmtByBillId" resultType="java.math.BigDecimal">
        select IFNULL(SUM(check_amt), 0.00)
        from bill_sub_check
        where bill_id = #{billId}
          and del_flag = 'N';
    </select>
    <select id="getAllBIllIdByCheckId" resultType="java.lang.Long">
        select bill_id
        from bill_sub_check
        where bill_check_id = #{checkId}
          and del_flag = 'N';
    </select>
    <select id="selectBIllSubCheckId" resultType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        select
        <include refid="Base_Column_List"/>
        from bill_sub_check bsc where 1=1 and bsc.del_flag = 'N' and bill_check_id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="getBillCheckSubData" resultType="com.reon.hr.api.bill.vo.check.BillSubCheckVo">
        select
        bsc.id,
        bsc.bill_check_id,
        bsc.bill_id,
        IFNULL(bsc.check_amt, 0.00) AS checkAmt,
        adjust_flag, IFNULL(bsc.adjust_amt, 0.00) as adjustAmt,
        bsc.creator,
        bsc.updater,
        bsc.create_time,
        bsc.update_time,
        bsc.del_flag,
        bsc.version
        from insurance_bill ib left join bill_sub_check bsc on ib.id = bsc.bill_id
        where
         bsc.del_flag = 'N' and ib.del_flag = 'N'
        <if test="contractNo !=null and contractNo != '' and templetId != null and billMonth != null">
            and ib.contract_no = #{contractNo} and ib.templet_id = #{templetId} and ib.bill_month= #{billMonth}
        </if>
        <if test="startDate != null and endDate != null">
            and bsc.create_time  <![CDATA[ >= ]]>  #{startDate} and bsc.create_time <![CDATA[ <= ]]>  #{endDate}
        </if>
        order by bsc.create_time
    </select>
    <select id="selectBillIdAndMaxBillCheckIdListByBillIdList"
            resultType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        select bill_id,max(bill_check_id) as bill_check_id from bill_sub_check where bill_id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        group by bill_id order by create_time asc;
    </select>
    <select id="getMaxBillCheckIdByBillId" resultType="java.lang.Long">
        select max(bill_check_id) as bill_check_id from bill_sub_check where bill_id =#{billId};
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bill_sub_check bsc
        where id = #{id,jdbcType=BIGINT} and bsc.del_flag = 'N'
    </delete>
    <update id="deleteByIds">
        update bill_sub_check set del_flag = 'Y'
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insert" parameterType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        insert into bill_sub_check (id, bill_check_id, bill_id,
                                    check_amt, adjust_flag, adjust_amt,
                                    creator, updater, create_time,
                                    update_time, del_flag, version)
        values (#{id,jdbcType=BIGINT}, #{billCheckId,jdbcType=BIGINT}, #{billId,jdbcType=BIGINT},
                #{checkAmt,jdbcType=DECIMAL}, #{adjustFlag,jdbcType=INTEGER}, #{adjustAmt,jdbcType=DECIMAL},
                #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{version,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        insert into bill_sub_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="billCheckId != null">
                bill_check_id,
            </if>
            <if test="billId != null">
                bill_id,
            </if>
            <if test="checkAmt != null">
                check_amt,
            </if>
            <if test="adjustFlag != null">
                adjust_flag,
            </if>
            <if test="adjustAmt != null">
                adjust_amt,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="version != null">
                version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="billCheckId != null">
                #{billCheckId,jdbcType=BIGINT},
            </if>
            <if test="billId != null">
                #{billId,jdbcType=BIGINT},
            </if>
            <if test="checkAmt != null">
                #{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="adjustFlag != null">
                #{adjustFlag,jdbcType=INTEGER},
            </if>
            <if test="adjustAmt != null">
                #{adjustAmt,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        update bill_sub_check
        <set>
            <if test="billCheckId != null">
                bill_check_id = #{billCheckId,jdbcType=BIGINT},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=BIGINT},
            </if>
            <if test="checkAmt != null">
                check_amt = #{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="adjustFlag != null">
                adjust_flag = #{adjustFlag,jdbcType=INTEGER},
            </if>
            <if test="adjustAmt != null">
                adjust_amt = #{adjustAmt,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.bill.entity.bill.BillSubCheck">
        update bill_sub_check bsc
        set bill_check_id = #{billCheckId,jdbcType=BIGINT},
            bill_id       = #{billId,jdbcType=BIGINT},
            check_amt     = #{checkAmt,jdbcType=DECIMAL},
            adjust_flag   = #{adjustFlag,jdbcType=INTEGER},
            adjust_amt    = #{adjustAmt,jdbcType=DECIMAL},
            creator       = #{creator,jdbcType=VARCHAR},
            updater       = #{updater,jdbcType=VARCHAR},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            del_flag      = #{delFlag,jdbcType=CHAR},
            version       = #{version,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}  and bsc.del_flag = 'N'
    </update>

    <insert id="batchInsertListAndCheckId" keyProperty="id" useGeneratedKeys="true">
        insert into bill_sub_check (id, bill_check_id, bill_id,
        check_amt, adjust_flag, adjust_amt,
        creator
        ) values
        <foreach collection="list" separator="," item="item">
            ( #{item.id,jdbcType=BIGINT}, #{checkId,jdbcType=BIGINT}, #{item.billId,jdbcType=BIGINT},
            #{item.checkAmt,jdbcType=DECIMAL}, #{item.adjustFlag,jdbcType=INTEGER}, #{item.adjustAmt,jdbcType=DECIMAL},
            #{item.creator,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="insertListAndCheckId" keyProperty="id" useGeneratedKeys="true">
        insert into `reon-billdb`.bill_sub_check (  bill_check_id, bill_id,
        check_amt, adjust_flag, adjust_amt,
        creator
        ) values
          ( #{billCheckId,jdbcType=BIGINT}, #{billId,jdbcType=BIGINT},
            #{checkAmt,jdbcType=DECIMAL}, #{adjustFlag,jdbcType=INTEGER}, #{adjustAmt,jdbcType=DECIMAL},
            #{creator,jdbcType=VARCHAR})

    </insert>

    <select id="selectDataByCheckIdAndBillIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `reon-billdb`.bill_sub_check bsc
        where del_flag = 'N'
          and bsc.bill_check_id = #{checkId}
          and bsc.bill_id in
        <foreach collection="billIdList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getMaxIdBillSubCheckVoByBillId" resultType="com.reon.hr.api.bill.vo.check.BillSubCheckVo">
        SELECT *
        FROM bill_sub_check
        WHERE del_flag = 'N' and  bill_id = #{billId}
        ORDER BY bill_check_id DESC
            LIMIT 1
    </select>

    <select id="getBillSubCheckByBillIdListAndAdjustFlag" resultType="com.reon.hr.api.bill.vo.check.BillSubCheckVo">
        select bsc.bill_id, bsc.adjust_flag, sum(ifnull(bsc.adjust_amt, 0))
        from `reon-billdb`.bill_sub_check bsc
        where bsc.bill_id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        and bsc.adjust_flag = #{adjustFlag}
        and bsc.del_flag = 'N'
        group by bsc.bill_id;
    </select>
</mapper>