<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>工伤</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>

    <style type="text/css">
        .layui-body{overflow-y: scroll;}
        .layui-table td{
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }
        .layui-input{
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm" method="post">
        <table class="layui-table" lay-skin="nob">
            <tr>
                <td width="5%" align="right" style="font-weight:800">订单编号</td>
                <td width="9%"><input type="text" class="layui-input" name="orderNo" autocomplete="off"></td>
                <td width="4%" align="right" style="font-weight:800">员工姓名</td>
                <td width="9%"><input type="text" class="layui-input" name="employeeName" autocomplete="off"></td>
                <td width="4%" align="right" style="font-weight:800">客户编号</td>
                <td width="9%"><input type="text" class="layui-input" name="custNo" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">客户名称</td>
                <td width="10%"><input type="text" class="layui-input" name="custName" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">证件号码</td>
                <td width="9%"><input type="text" class="layui-input" name="certNo" autocomplete="off"></td>
            </tr>
            <tr>
                <td align="right" style="font-weight:800">接单方</td>
                <td>
                    <input type="text" class="layui-input" id="receivingName" readonly>
                    <input type="hidden" class="layui-input" name="receiving" id="receiving">
                </td>
                <td align="right" style="font-weight:800">接单方客服</td>
                <td><input type="text" class="layui-input" id="receivingMan" autocomplete="off" >
                    <input type="hidden" name="receivingMan" id="receivingMan2">
                </td>
<%--                <td align="right" style="font-weight:800">派单方客服</td>--%>
<%--                <td><input type="text" class="layui-input" id="distCom" name="commissioner" autocomplete="off">--%>
<%--                    <input type="hidden" name="commissioner" id="commissioner2">--%>
<%--                </td>--%>

                <td align="right" style="font-weight:800">业务大类</td>
                <td>
                    <select name="businessType" id="businessType"  lay-search DICT_TYPE="INJURY_TYPE" lay-filter="businessType" >
                        <option value=""></option>
                    </select>
                </td>

                <td align="right" style="font-weight:800">业务小类</td>
                <td>
                    <select name="businessSubType"  lay-search DICT_TYPE="INJURY_SMALL_TYPE_1" lay-filter="businessSubType" >
                        <option value=""></option>
                    </select>
                </td>

            </tr>

            <tr>
                <td align="right" style="font-weight:800">具体业务</td>
                <td>
                    <select class="layui-select" name="definiteBusiness" lay-Filter="definiteBusiness"
                            id="definiteBusiness"  autocomplete="off" placeholder="请选择">
                        <option value=""></option>
                    </select>
                </td>
                <td align="right" style="font-weight:800">业务进度</td>
                <td>
                    <select name="status"  lay-search DICT_TYPE="WORK_INJURY_STATUS" lay-verify="" >
                        <option value=""></option>
                    </select>
                </td>
                <td colspan="2" align="center">
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" >查询</button>
                    <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button></td>
            </tr>
        </table>
        <input type="hidden" id="selectedGroup">
    </form>
</blockquote>
<table class="layui-hide" id="workInjuryGrid" lay-filter="workInjuryGridFilter"></table>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/workInjury/workInjury.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="addOne" lay-event="add" authURI="/customer/workInjury/gotoSaveWorkInjuryPage" >新增</button>
    <button class="layui-btn layui-btn-sm" id="update" lay-event="update" authURI="/customer/workInjury/gotoUpdateWorkInjuryPage">修改</button>
    <button class="layui-btn layui-btn-sm" id="update" lay-event="delete" authURI="/customer/workInjury/delete">删除</button>
    <button class="layui-btn layui-btn-sm" id="commit" lay-event="commit" authURI="/customer/workInjury/batchCommit">提交</button>
    <button class="layui-btn layui-btn-sm" id="cancel" lay-event="cancel" authURI="/customer/workInjury/batchCommit">取消办理</button>
    <button class="layui-btn layui-btn-sm" id="updateCfg" lay-event="updateCfg" authURI="/customer/workInjury/gotoWorkInjuryByIdPage">查看详情</button>
     <a class="layui-btn layui-btn-sm" id="import"  lay-event="import" authURI="/customer/workInjury/exportWorkInjury">导出</a>
</script>

</body>
</html>