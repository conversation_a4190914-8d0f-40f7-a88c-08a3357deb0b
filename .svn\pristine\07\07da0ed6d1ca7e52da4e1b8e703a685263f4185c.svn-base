package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.org.OrgAndSupplierVo;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.customer.vo.supplier.SupplierSearchVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.sp.customer.entity.cus.Supplier;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierMapper {

    int insertSelective(Supplier record);

    Supplier selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Supplier record);

    int updateByPrimaryKey(Supplier record);

    /**
     *条件 分页查询
     * @param page
     * @return
     */
    List<SupplierVo> getSupplierListPage(Page page, SupplierSearchVo sup1plierSearchVo);
    List<SupplierVo> getSupplierList(SupplierSearchVo sup1plierSearchVo);


    List<OrgAndSupplierVo> getSupplierListByArea(@Param("provinceCode")String provinceCode, @Param("cityCode")String cityCode,@Param("priorityFlag")Integer priorityFlag);
    List<OrgAndSupplierVo> getSuppliersByArea(@Param("provinceCode")String provinceCode, @Param("cityCode")String cityCode,@Param("priorityFlag")Integer priorityFlag);

    /**
     * 根据供应商集合和优选标识查询
     *
     * @param priorityFlag 优选标识
     * @param supplierVos 供应商集合
     * @return {@link List}<{@link OrgAndSupplierVo}>
     */
    List<OrgAndSupplierVo> getSupplierByCityCodeAndPriorityFlag(@Param("supplierVos") List<OrgAndSupplierVo> supplierVos,@Param("priorityFlag") Integer priorityFlag);

    /**
     * 根据id查询供应商 及供应商合同、服务城市、附件
     * @param id
     * @return
     */
    SupplierVo getSupplierInfoById(Long id);

    /**
     * 逻辑删除多个
     * @param ids
     * @return
     */
    int deleteById(@Param("ids") List<Long> ids,@Param("updater")String updater);


    List<OrgVo> getSupplierByNameAndCityCode(@Param("keyword") String keyword, @Param("cityCode") Integer cityCode);


    List<SupplierVo> getAllSuppliers(@Param("supplierName") String supplierName);

    List<SupplierVo> selectAllEnableSupplier();

    List<SupplierVo> selectAllEnableSupplierByType(@Param("list") List<Integer> typeList);
    /**
     * 根据供应商ID查询供应商名称**/
    List<SupplierVo>  getSupplierName(@Param("id")Long id);


    List<SupplierVo> getAllSupplierByType();
    List<SupplierVo> getAllSupplier();
    List<SupplierVo> getSupplierByIds(@Param("Ids") List<Long> ids);

    List<SupplierVo> getSalarySupplier(@Param("userOrgPositionDtoList") List<OrgPositionDto> userOrgPositionDtoList);

    List<SupplierAreaVo> getAllAggregationCommissioner();

    List<Long> getSupplierIdByCommissioner(String loginName);
}
