var ctx = ML.contextPath;
layui.config({
    base : ctx+"/js/"
}).use(['form', 'layer', 'table', 'upload', 'laydate','tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, upload = layui.upload,tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;
    form.render('select');
    var custData = [];
    var optType = $("#optType").val();
    var corpKind ;
    var invoiceId = $("#invoiceId").val();
    if (optType == 'update' || optType == 'query') {
        ML.ajax("/customer/invoice/getOneById", {'id': invoiceId}, function (result) {
            var data = result.data;
            console.log(data);
            if (data) {
                $("#custId").val(data.custId);
                $("#corpKind").val(data.corpKind);
                editRequired(data.corpKind)
                form.render('select');
                $("#custName").val(data.custName);
                $("#taxNo").val(data.taxNo);
                $("#acctNo").val(data.acctNo);
                $("#title").val(data.title);
                $("#acctName").val(data.acctName);
                $("#bankName").val(data.bankName);
                $("#tel").val(data.tel);
                $("#addr").val(data.addr);
                $("#remark").val(data.remark);
                $("#invoiceMethod").val(data.invoiceMethod);
                $("#email").val(data.email);
                $("#pushMode").val(data.pushMode);
            }
            form.render("select");
        },"GET");
        if (optType == 'query') {
            $('select').attr("disabled", "disabled");
            form.render('select');
            $("input").attr("disabled", "disabled");
            $("button").hide();
            $("textarea").attr("disabled", "disabled");
        }
    }

    form.on("select(invoiceMethodFilter)", function (data) {
        const invoiceMethod = data.value;
        if (invoiceMethod == 0) {
            $("#email").removeAttr("lay-verify","email");
            clearInput("#acctNo");
            clearInput("#acctName");
            clearInput("#bankName");
            clearInput("#tel");
            clearInput("#addr");
            clearInput("#email");
        }
        if (invoiceMethod == 1) {
            $("#email").attr("lay-verify","email");
        }
        if (invoiceMethod == 2) {
            $("#email").removeAttr("lay-verify","email");
        }
    })

    function clearInput(selector) {
        if (!$(selector).val()) {
            $(selector).val("/");
        }
    }


    function editRequired(corpKind) {
        if (corpKind == 2) {
            $("#acctNo").attr("lay-verify","required");
            $("#acctNoI").html("*")
            $("#acctName").attr("lay-verify","required");
            $("#acctNameI").html("*")
            $("#bankName").attr("lay-verify","required");
            $("#bankNameI").html("*")
            $("#tel").attr("lay-verify","required");
            $("#telI").html("*")
            $("#addr").attr("lay-verify","required");
            $("#addrI").html("*")
        }else if (corpKind == 1) {
            $("#acctNo").attr("lay-verify","");
            $("#acctNoI").html("")
            $("#acctName").attr("lay-verify","");
            $("#acctNameI").html("")
            $("#bankName").attr("lay-verify","");
            $("#bankNameI").html("")
            $("#tel").attr("lay-verify","");
            $("#telI").html("")
            $("#addr").attr("lay-verify","");
            $("#addrI").html("")
        }
    }





    form.on("submit(saveFilter)", function (data) {
        var field = data.field;
        if (field.invoiceMethod == 1) {
            if (!field.email) {
                layer.msg("发票方式为电子发票时,邮箱必填")
                return false;
            }
        }
        if (field.email && !verifyEmail(field.email) && field.invoiceMethod != 0) {
            layer.msg("邮箱格式不正确")
            return false;
        }
        ML.layuiButtonDisabled($('#saveBtn'));// 禁用
        data.field['custId']=$("#custName").attr("ts-selected");
        $.ajax({
            url: ctx + "/customer/invoice/save",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    layer.closeAll('iframe');//关闭弹窗
                } else if (result.code == -1) {
                    ML.layuiButtonDisabled($('#' + btnId + ''), true);// 禁用
                }
            }
        });
        return false;
    });

    $("#cancelBtn").click(function () {
        layer.closeAll('iframe'); //关闭弹窗
    });

    if (optType=='add'){
        //下拉数据表格
        var appd = '<input style="display:inline-block;width:220px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号/合同名称/编号" autocomplete="off" class="layui-input">';
        tableSelect.render({
            elem: '#custName',
            checkedKey: 'custId',
            appd:appd,
            table: {
                url:ctx+'/customer/contract/getCustomerPageByName',
                cols: [[
                    { type: 'radio', width: '50' },
                    {field: 'custNo',title:'客户编号',align:'center',width:'200'},
                    {field: 'custName',title:'客户名称',align:'center',width: '150'},
                    {field: 'contractNo',title:'合同编号',align:'center',width:'220'},
                    {field:'contractName',title:'合同名称',align:'center',width:'200'},
                ]]
            },
            done: function (elem, data) {
                var NEWJSON = [];
                layui.each(data.data, function (index, item) {
                    NEWJSON.push(item.custName)
                })
                elem.val(NEWJSON.join(","))
            }
        });
    }

});