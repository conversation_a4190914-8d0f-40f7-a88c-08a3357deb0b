var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    $('#btnQuery').on('click', function () {
        table.reload('queryBusinessModalsPage', {where: serialize("searchForm")});
    });
    $(document).ready(function () {
        setTimeout(function () {
            let notifyMsg = $('#notifyMsg').val();
            let serviceCode = $('#serviceCode').val();
            if (ML.isNotEmpty(notifyMsg)) {
                let msg = JSON.parse(notifyMsg);
                // var notdatStr = msg.notdat;
                // let notifyData = JSON.parse(notdatStr);
                //支付完成
                if (serviceCode === "YQN02030_FINS") {
                    let dataArr = [];
                    if (ML.isNotEmpty(msg.trsInfo)) {
                        dataArr.push(msg.trsInfo);
                    }
                    trsInfoFun(dataArr)
                    //支付退票
                } else if (serviceCode === "YQN02030_FINB") {
                    let dataArr = [];
                    if (ML.isNotEmpty(msg.backInfo)) {
                        dataArr.push(msg.backInfo);
                    }
                    backInfoFun(dataArr);
                    //代发完成
                } else if (serviceCode === "YQN03010_FIN") {
                    let agcInfoArr = [];
                    let detailInfoArr = [];
                    var notdatStr = msg.notdat;
                    let notifyData = JSON.parse(notdatStr);
                    if (ML.isNotEmpty(msg)) {
                        detailInfoArr = ML.isNotEmpty(notifyData.msgdat.detailInfo) ? notifyData.msgdat.detailInfo : [];
                        if (ML.isNotEmpty(notifyData.msgdat.agcInfo)) {
                            agcInfoArr.push(notifyData.msgdat.agcInfo);
                        }
                    }
                    //代发经办
                    agcInfoFun(agcInfoArr, detailInfoArr);
                    //代发退票
                } else if (serviceCode === "YQN03010_RFD") {
                    var notdatStr = msg.notdat;
                    let notifyData = JSON.parse(notdatStr);
                    let detailInfoArr = [];
                    if (ML.isNotEmpty(notifyData.msgdat)) {
                        detailInfoArr.push(notifyData.msgdat.detailInfo);
                    }
                    detailInfoFun(detailInfoArr);
                }
                //代发明细对账单结果通知
                else if (serviceCode === "YQF01010" || serviceCode === "YQF03010") {
                    let data = [];
                    if (ML.isNotEmpty(msg.notdat)) {
                        data.push(JSON.parse(msg.notdat));
                    }
                    //账户交易信息查询
                    YQF01010Arr(data);
                }

                let arr = [];
                arr.push({
                    "notdat": msg.notdat,
                    "notkey": msg.notkey,
                    "notnbr": msg.notnbr,
                    "nottyp": msg.nottyp,
                    "sigdat": msg.sigdat,
                    "sigtim": msg.sigtim,
                    "usrnbr": msg.usrnbr
                })
                table.reload('headGird', {data: arr});
            }
        }, 200)
    })


    table.render({
        id: 'headGird',
        elem: '#QueryGridTable'
        , data: []
        , height: '150px'
        , page: false
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , method: 'POST'
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            // , {field: 'notdat', title: '通知内容', align: 'center', width: '150'}
            , {field: 'notkey', title: '通知键值', align: 'center', width: '150'}
            , {field: 'notnbr', title: '通知编号', align: 'center', width: '150'}
            , {field: 'nottyp', title: '通知类型', align: 'center', width: '150'}
            , {field: 'sigdat', title: '签名内容', align: 'center', width: '150'}
            , {field: 'sigtim', title: '签名时间', align: 'center', width: '150'}
            , {field: 'usrnbr', title: '用户编号', align: 'center', width: '150'}
        ]]
    });

    function trsInfoFun(dataArr) {
        //单笔支付查询
        table.render({
            id: 'trsInfoGird',
            elem: '#QueryGridTable1'
            , data: dataArr
            , height: 'full-200'
            , page: true
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , limit: 50
            , method: 'GET'
            , limits: [50, 100, 200]
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                var that = this.elem.next();
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'yurRef', title: '参考号', width: 150, align: 'center'}
                , {field: 'usrNam', title: '用户姓名', width: 150, align: 'center'}
                , {field: 'trsAmt', title: '交易金额', width: 150, align: 'center'}
                , {
                    field: 'ccyNbr', title: '币种', width: 150, align: 'center', templet: function (d) {
                        if (d.ccyNbr == 10) {
                            return "人民币"
                        }
                        return d.ccyNbr;
                    }
                }
                , {field: 'crtAcc', title: '收方帐号', width: 150, align: 'center'}
                , {field: 'crtAdr', title: '收方行地址', width: 150, align: 'center'}
                , {field: 'crtBbk', title: '收方分行号', width: 150, align: 'center'}
                , {field: 'crtBnk', title: '收方行名称', width: 150, align: 'center'}
                , {field: 'crtNam', title: '收方名称', width: 150, align: 'center'}
                , {field: 'dbtAcc', title: '付方帐号', width: 150, align: 'center'}
                , {field: 'dbtBbk', title: '转出分行号', width: 150, align: 'center'}
                , {field: 'dbtNam', title: '付方帐户名', width: 150, align: 'center'}
                , {field: 'dmaNbr', title: '付方记账子单元编号', width: 150, align: 'center'}
                , {field: 'eptDat', title: '期望日', width: 150, align: 'center'}
                , {field: 'eptTim', title: '期望时间', width: 150, align: 'center'}
                , {field: 'lgnNam', title: '用户名', width: 150, align: 'center'}
                , {field: 'ntfCh1', title: '通知方式一', width: 150, align: 'center'}
                , {field: 'ntfCh2', title: '通知方式二', width: 150, align: 'center'}
                , {field: 'nusAge', title: '用途', width: 150, align: 'center'}
                , {field: 'oprAls', title: '操作别名', width: 150, align: 'center'}
                , {field: 'oprDat', title: '经办日期', width: 150, align: 'center'}
                , {field: 'oprSqn', title: '待处理操作序列', width: 150, align: 'center'}
                , {field: 'rcvBrd', title: '收方大额行号', width: 150, align: 'center'}
                , {field: 'reqNbr', title: '流程实例号', width: 150, align: 'center'}
                , {
                    field: 'reqSts', title: '请求状态', width: 150, align: 'center', templet: function (d) {
                        return formatReqStsEn(d.reqSts);
                    }
                }
                , {
                    field: 'rtnFlg', title: '业务处理结果', width: 150, align: 'center', templet: function (d) {
                        return formatRtnFlgEn(d.rtnFlg);
                    }
                }
                , {field: 'rtnNar', title: '失败原因', width: 150, align: 'center'}
                , {
                    field: 'stlChn', title: '结算通路', width: 150, align: 'center', templet: function (d) {
                        return formatStlChnEn(d.stlChn);
                    }
                }
                , {
                    field: 'trsTyp', title: '业务种类', width: 150, align: 'center', templet: function (d) {
                        return formatTrsTypEn(d.trsTyp);
                    }
                }
                , {field: 'trxSeq', title: '账务流水', width: 150, align: 'center'}
                , {field: 'trxSet', title: '账务套号', width: 150, align: 'center'}
                , {
                    field: 'athFlg', title: '是否有附件信息', width: 150, align: 'center', templet: function (d) {
                        switch (d.athFlg) {
                            case "Y" :
                                return "是";
                            case  "N" :
                                return "否";
                            default  :
                                return d.athFlg;
                        }
                    }
                }
                , {
                    field: 'bnkFlg', title: '系统内外标志', width: 150, align: 'center', templet: function (d) {
                        switch (d.bnkFlg) {
                            case "Y" :
                                return "系统内";
                            case  "N" :
                                return "系统外";
                            default  :
                                return d.bnkFlg;
                        }
                    }
                }
                , {field: 'busCod', title: '业务编码', width: 150, align: 'center'}
                , {field: 'busMod', title: '业务模式', width: 150, align: 'center'}
                , {field: 'busNar', title: '业务摘要', width: 150, align: 'center'}


            ]]
        });

    }

    function backInfoFun(bodyArr) {
        table.render({
            id: 'backInfoGird',
            elem: '#QueryGridTable2'
            , data: bodyArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'reqNbr', title: '流程实例号', align: 'center', width: '150'}
                , {field: 'yurRef', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'busNbr', title: '汇款编号', align: 'center', width: '150'}
                , {field: 'outTyp', title: '汇款方式', align: 'center', width: '150'}
                , {field: 'busTyp', title: '转账汇款种类', align: 'center', width: '150'}
                , {field: 'busLvl', title: '汇款优先级', align: 'center', width: '150'}
                , {field: 'busSts', title: '汇款业务状态', align: 'center', width: '150'}
                , {field: 'sndClt', title: '付方客户号', align: 'center', width: '150'}
                , {field: 'clrSts', title: '清算状态', align: 'center', width: '150'}
                , {field: 'isuCnl', title: '汇款发起通道', align: 'center', width: '150'}
                , {field: 'isuDat', title: '发起日期', align: 'center', width: '150'}
                , {field: 'trsBbk', title: '处理分行', align: 'center', width: '150'}
                , {field: 'trsBrn', title: '处理机构', align: 'center', width: '150'}
                , {field: 'ccyNbr', title: '交易货币', align: 'center', width: '150'}
                , {field: 'trsAmt', title: '金额', align: 'center', width: '150'}
                , {field: 'dbtAcc', title: '付方户口号', align: 'center', width: '150'}
                , {field: 'dbtNam', title: '付方户名', align: 'center', width: '150'}
                , {field: 'sndBrn', title: '付方开户机构', align: 'center', width: '150'}
                , {field: 'crtAcc', title: '收方户口号', align: 'center', width: '150'}
                , {field: 'crtNam', title: '收方户名', align: 'center', width: '150'}
                , {field: 'crtBnk', title: '收方开户行', align: 'center', width: '150'}
                , {field: 'rcvEaa', title: '收方开户地', align: 'center', width: '150'}
                , {field: 'narTxt', title: '摘要', align: 'center', width: '150'}
                , {field: 'feeAmt', title: '费用总额', align: 'center', width: '150'}
                , {field: 'feeCcy', title: '币种', align: 'center', width: '150'}
                , {field: 'psbTyp', title: '提出凭证种类', align: 'center', width: '150'}
                , {field: 'psbNbr', title: '凭证号码', align: 'center', width: '150'}
                , {field: 'ctyFlg', title: '同城异地标志', align: 'center', width: '150'}
                , {field: 'sysFlg', title: '系统内外标志', align: 'center', width: '150', templet: function (d) {
                        switch (d.sysFlg) {
                            case "Y" :
                                return "系统内";
                            case  "N" :
                                return "系统外";
                            default  :
                                return d.sysFlg;
                        }
                    }}
                , {field: 'rcvTyp', title: '收方公私标志', align: 'center', width: '150'}
                , {field: 'watRcn', title: '资金停留原因', align: 'center', width: '150'}
                , {field: 'watTrs', title: '资金停留流水', align: 'center', width: '150'}
                , {field: 'updDat', title: '更新日期', align: 'center', width: '150'}
                , {field: 'rtnCod', title: '退票理由代码', align: 'center', width: '150'}
                , {field: 'rtnNar', title: '退票原因', align: 'center', width: '150'}
                , {field: 'rcdSts', title: '记录状态', align: 'center', width: '150'}

            ]]
        });
    }


    function agcInfoFun(agcInfoArr, detailInfoArr) {
        table.render({
            id: 'agcInfoArrGird',
            elem: '#QueryGridTable3'
            , data: agcInfoArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'yurref', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'ntbnbr', title: '企银编号', align: 'center', width: '150'}
                , {field: 'reqnbr', title: '流程实例号', align: 'center', width: '150'}
                , {field: 'buscod', title: '业务代码', align: 'center', width: '150'}
                , {field: 'bchnbr', title: '批次号码', align: 'center', width: '150'}
                , {field: 'sntflg', title: '超网标志', align: 'center', width: '150', templet: function (d) {
                        switch (d.sntflg) {
                            case "Y" :
                                return "是";
                            case  "N" :
                                return "否";
                            default  :
                                return d.sntflg;
                        }
                    }}
                , {field: 'accnbr', title: '账号 ', align: 'center', width: '150'}
                , {field: 'oprdat', title: '经办日期', align: 'center', width: '150'}
                , {field: 'rtnflg', title: '请求结果', align: 'center', width: '150', templet: function (d) {
                        return formatRtnFlgEn(d.rtnflg);
                    }}
                , {field: 'totamt', title: '总金额', align: 'center', width: '150'}
                , {field: 'trsnum', title: '交易笔数', align: 'center', width: '150'}
                , {field: 'sucamt', title: '成功金额', align: 'center', width: '150'}
                , {field: 'sucnum', title: '成功笔数', align: 'center', width: '150'}
                , {field: 'oprusr', title: '经办用户', align: 'center', width: '150'}
                , {field: 'trsreq', title: '交易流水', align: 'center', width: '150'}
                , {field: 'trsset', title: '交易套号', align: 'center', width: '150'}

            ]]
        });


        table.render({
            id: 'detailInfoArrGird',
            elem: '#QueryGridTable4'
            , data: detailInfoArr
            , height: 'full-200'
            , page: true
            , limit: 50
            , limits: [50, 100, 200]
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'trxseq', title: '交易序号', align: 'center', width: '150'}
                , {field: 'accnbr', title: '账号', align: 'center', width: '150'}
                , {field: 'accnam', title: '户名', align: 'center', width: '150'}
                , {field: 'trsamt', title: '交易金额', align: 'center', width: '150'}
                , {field: 'stscod', title: '交易状态', align: 'center', width: '150', templet: function (d) {
                        return formatbb6bpdqyz2StscodEn(d.stscod);
                    }}

            ]]
        });
    }


    function detailInfoFun(detailInfoArr) {
        table.render({
            id: 'detailInfo2Gird',
            elem: '#QueryGridTable5'
            , data: detailInfoArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'reqnbr', title: '流程实例号', align: 'center', width: '150'}
                , {field: 'yurref', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'ntbnbr', title: '企银编号', align: 'center', width: '150'}
                , {field: 'dtlseq', title: '明细序号', align: 'center', width: '150'}
                , {field: 'chntyp', title: '通知渠道', align: 'center', width: '150'}
                , {field: 'trynum', title: '调用次数', align: 'center', width: '150'}
                , {field: 'ntfsta', title: '成功标志', align: 'center', width: '150', templet: function (d) {
                        return formatNtfsta(d.ntfsta);
                    }}
                , {field: 'bchnbr', title: '批次号 ', align: 'center', width: '150'}
                , {field: 'payacc', title: '付款账号', align: 'center', width: '150'}
                , {field: 'accnbr', title: '账号', align: 'center', width: '150'}
                , {field: 'ttlamt', title: '明细退票金额', align: 'center', width: '150'}
                , {field: 'trasqn', title: '退票交易流水', align: 'center', width: '150'}
                , {field: 'traset', title: '退票交易套号', align: 'center', width: '150'}
                , {field: 'traarn', title: '退票受理回单实例号', align: 'center', width: '150'}
                , {field: 'tradat', title: '退票日期', align: 'center', width: '150'}
                , {field: 'tratim', title: '退票时间', align: 'center', width: '150'}
            ]]
        });
    }



    function YQF01010Arr(data) {
        table.render({
            id: 'YQF01010ArrId',
            elem: '#QueryGridTable13'
            , data: data
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'retcod', title: '响应码', align: 'center', width: '150'}
                , {field: 'taskid', title: '打印ID', align: 'center', width: '150'}
                , {field: 'returl', title: '下载路径', align: 'center', width: '150'}
            ]]
        });
    }

});