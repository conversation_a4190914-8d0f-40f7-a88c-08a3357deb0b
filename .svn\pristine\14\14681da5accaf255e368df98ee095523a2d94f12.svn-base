package com.reon.hr.sp.customer.rabbitmq.listener;

import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.salary.ISalaryPayBatchWrapperService;
import com.reon.hr.api.bill.enums.PaymentApplyProcessStatus;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.customer.enums.salary.SalaryInfoStatus;
import com.reon.hr.api.customer.vo.salary.pay.EmpDelaySearchVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.context.MqContext;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.customer.ConsumerScopeTypeCustomer;
import com.reon.hr.sp.customer.service.employee.salary.pay.ISalaryBatchDetailService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class SalaryPaymentApplyProcessCompletedListener extends AbstractConsumerListener {

    private final static String QUEUE_NAME = "salary.payment.apply.process.completed";

    private final static Logger logger = LoggerFactory.getLogger(SalaryPaymentApplyProcessCompletedListener.class);

    @Autowired
    private MqContext mqContext;

    @Autowired
    private ISalaryBatchDetailService salaryBatchDetailService;
    @Autowired
    private IPaymentApplyWrapperService iPaymentApplyWrapperService;
    @Autowired
    private ISalaryPayBatchWrapperService salaryPayBatchWrapperService;

    @Override
    protected void doWork(String message) {
        //此日志在 AbstractConsumerListener类中84行有打印
      //  logger.info("processInstance ID :{}",message);
        String pid =message;
        try{
            List<String> pidList = Collections.singletonList(pid);
            iPaymentApplyWrapperService.updateDocumentStatus(pidList,null, PaymentApplyProcessStatus.FINISHED.getCode());
        }catch (Exception e){
            logger.error("modify biz process id {} 'finished' status failure",pid,e);
            e.printStackTrace();
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        super.init(ModuleType.REON_CUSTOMER,ConsumerScopeTypeCustomer.REON_SALARY_PAYMENT_APPLY_PROCESS_COMPLETED,mqContext,QUEUE_NAME);
    }
}
