<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>小合同设置白名单</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        html, body {
            overflow: hidden;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline ">
                <label class="layui-form-label layui-elip" title="小合同名称" style="font-weight:800">小合同名称</label>
                <div class="layui-input-inline">
                    <input type="text"   maxlength="20" name="name" placeholder="请输入" class="layui-input" autocomplete="off">
                </div>
            </div>

            <div class="layui-input-inline ">
                <label class="layui-form-label layui-elip" title="小合同编号" style="font-weight:800">小合同编号</label>
                <div class="layui-input-inline">
                    <input type="text"   maxlength="20" name="contractAreaNo" placeholder="请输入" class="layui-input" autocomplete="off">
                </div>
            </div>

            <a class="layui-btn" id="btnQuery" data-type="reload" >检索</a>
            <button class="layui-btn" id="resetBtn" type="reset" >重置</button>
        </div>
    </form>
    <%--endQuery--%>
</blockquote>
<%--startTable--%>
<table id="ContractAreaWhiteListTable" lay-filter="ContractAreaWhiteListTableFilter"  ></table>
<%--endTable--%>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="add"  lay-event="add" authURI="/customer/contractArea/gotoAddContractAreaWhiteListPage" >新增</button>
        <button class="layui-btn layui-btn-sm" id="edit"  lay-event="edit" authURI="/customer/contractArea/gotoEditContractAreaWhiteListPage">修改</button>
        <button class="layui-btn layui-btn-sm" id="export"  lay-event="export" authURI="/customer/contractArea/exportCAWL">导出</button>
    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/contractArea/contractAreaWhiteList.js?v=${publishVersion}"></script>
</body>
</html>
