package com.reon.ehr.api.sys.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.vo.customer.CustomerVisitRecordVo;

import java.util.List;

/**
 * 客户拜访会议纪要 服务层
 */
public interface IEpCustomerVisitWrapperService
{
    /**
     * 查询客户拜访会议纪要数据
     *
     * @param customerVisitRecordVo 客户拜访会议纪要
     * @return 客户拜访会议纪要集合
     */
    public Page selectCustomerVisitPage(Integer page, Integer limit, CustomerVisitRecordVo customerVisitRecordVo);

}
