package com.reon.hr.sp.bill.entity.bill;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillInvoiceDetail {
    private Long id;

    private Long invoiceFromId;

    private Long billDetailId;

    private BigDecimal amt;

    private Integer peopleNum;

    private BigDecimal indAmt;

    private BigDecimal comAmt;

    private String creator;

    private String updater;

    private Date createTime;

    private Date updateTime;

    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInvoiceFromId() {
        return invoiceFromId;
    }

    public void setInvoiceFromId(Long invoiceFromId) {
        this.invoiceFromId = invoiceFromId;
    }

    public Long getBillDetailId() {
        return billDetailId;
    }

    public void setBillDetailId(Long billDetailId) {
        this.billDetailId = billDetailId;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public Integer getPeopleNum() {
        return peopleNum;
    }

    public void setPeopleNum(Integer peopleNum) {
        this.peopleNum = peopleNum;
    }

    public BigDecimal getIndAmt() {
        return indAmt;
    }

    public void setIndAmt(BigDecimal indAmt) {
        this.indAmt = indAmt;
    }

    public BigDecimal getComAmt() {
        return comAmt;
    }

    public void setComAmt(BigDecimal comAmt) {
        this.comAmt = comAmt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater == null ? null : updater.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }
}