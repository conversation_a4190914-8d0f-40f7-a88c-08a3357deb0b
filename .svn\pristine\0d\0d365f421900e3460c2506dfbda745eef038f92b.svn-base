package com.reon.hr.sp.change.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.change.vo.AdjustFailEmpVo;
import com.reon.hr.api.change.vo.CollectImportResultVo;
import com.reon.hr.api.customer.utils.TotalDataPageUtil;
import com.reon.hr.api.file.dubbo.service.rpc.IFileSystemService;
import com.reon.hr.api.file.exception.FileSystemException;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.sp.change.dao.change.CollectImportResultMapper;
import com.reon.hr.sp.change.entity.change.CollectImportResult;
import com.reon.hr.sp.change.service.change.ICollectImportResultService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CollectImportResultServiceImpl implements ICollectImportResultService {
    private static final Logger log = LoggerFactory.getLogger(CollectImportResultServiceImpl.class);
    @Autowired
    private CollectImportResultMapper importResultMapper;
    @Autowired
    private IFileSystemService fileSystemService;

    @Override
    public Page<CollectImportResultVo> getPageByCollectSetId(Long collectSetId, Integer page, Integer size) {
        Page<CollectImportResultVo> importResultVoPage = new Page<>(page, size);
        List<CollectImportResultVo> resultVos = importResultMapper.getByCollectSetId(collectSetId, importResultVoPage);
        for (CollectImportResultVo resultVo : resultVos) {
            String fileName = "文件";
            try {
                fileName = fileSystemService.getOriginalFilename(resultVo.getImportFile());
            } catch (FileSystemException e) {
                log.error("get fileName error from fileSystem,fileId={}", resultVo.getImportFile(), e);
            }
            resultVo.setFileName(fileName);
        }
        importResultVoPage.setRecords(resultVos);
        return importResultVoPage;
    }

    @Override
    public Map<String, Object> getImportDetailById(Long id, Integer page, Integer size) {
        Map<String, Object> map = new HashMap<>();
        CollectImportResult importResult = importResultMapper.selectByPrimaryKey(id);
        if (importResult != null && StringUtils.isNotEmpty(importResult.getDetailTxt())) {
            List<AdjustFailEmpVo> failEmpVos = JsonUtil.jsonToList(importResult.getDetailTxt(), AdjustFailEmpVo.class);
            map = TotalDataPageUtil.getPage(failEmpVos, page, size);
        }
        return map;
    }
}
