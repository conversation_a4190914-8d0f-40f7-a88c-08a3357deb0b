/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/1/22
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.dubbo.service.rpc.impl;


import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.dto.importData.AddEmployeeContractImportDto;
import com.reon.hr.api.customer.dto.importData.AddTransferEmpContractImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dto.qys.QysMappingFieldDto;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeContractWrapperService;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.employeeContract.WorkMethodEnum;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.EmpContractLogVo;
import com.reon.hr.api.customer.vo.EmployeeContractVo;
import com.reon.hr.api.customer.vo.employee.EmployeeContractOperationLogVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderContractFileVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgPositionWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.sp.customer.dao.employee.EmployeeContractOperationLogMapper;
import com.reon.hr.sp.customer.service.employee.IEmployeeContractService;
import com.reon.hr.sp.customer.service.qiyuesuo.QysContractTableService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EmployeeContractWrapperServiceImpl
 *
 * @date 2021/1/22 11:37
 */
@Service("employeeContractDubboService")
public class EmployeeContractWrapperServiceImpl implements IEmployeeContractWrapperService {
	@Autowired
	private IUserWrapperService dsUserService;
	@Autowired
	IEmployeeContractService iEmployeeContractService;
    @Autowired
    private QysContractTableService qysContractTableService;
	@Autowired
	private IOrgnizationResourceWrapperService iOrgnizationResourceWrapperService;
	@Autowired
	private IOrgPositionWrapperService iOrgPositionWrapperService;

	@Autowired
	private EmployeeContractOperationLogMapper employeeContractOperationLogMapper;

	private static final String OPERATIONDATE = "操作时间";
	private static final String OPERATIONNAME = "操作人";
	@Override
	public Integer saveEmployeeContract(EmployeeContractVo employeeContractVo) {
		return iEmployeeContractService.saveEmployeeContract(employeeContractVo);
	}

	@Override
	public Integer editEmployeeContract(EmployeeContractVo employeeContractVo) {
		return iEmployeeContractService.editEmployeeContract(employeeContractVo);
	}

	@Override
	public void updateFileIdByOrderNo(List<EmployeeContractVo> records,String loginName) {
		iEmployeeContractService.updateFileIdByOrderNo(records,loginName);
	}

	@Override
	public void updateFileIdByOrderNo(EmployeeContractVo record) {
		iEmployeeContractService.updateFileId(record);
	}
	@Override
	public boolean getByOrderNoList(List<String> orderNos) {
		return iEmployeeContractService.getByOrderNoList(orderNos).size() == orderNos.size();
	}

	@Override
	public int getPerContractCountByEmpId(List<String> certNoList) {
		return iEmployeeContractService.getPerContractCountByEmpId(certNoList);
	}

	@Override
	public int getPerContractCountByEmpIdAndContractNo(Long empId, String contractNo) {
		return iEmployeeContractService.getPerContractCountByEmpIdAndContractNo(empId, contractNo);
	}

	@Override
	public Page<EmployeeContractVo> getEmployeeContractPage(Integer page, Integer limit, EmployeeContractVo employeeContractVo) {
		return iEmployeeContractService.getEmployeeContractPage(page,limit,employeeContractVo);
	}

	@Override
	public Page<EmployeeContractVo> getEmployeeContractPageForEhr(Integer page, Integer limit, EmployeeContractVo employeeContractVo) {
		return iEmployeeContractService.getEmployeeContractPageForEhr(page,limit,employeeContractVo);
	}

	@Override
	public EmployeeContractVo getDataByContractNo(String empContractNo) {
		return iEmployeeContractService.getDataByContractNo(empContractNo);
	}

	@Override
	public Integer delEmployeeContractByNo(List delNoList) {
		return iEmployeeContractService.delEmployeeContractByNo(delNoList);
	}

	@Override
	public Integer updateSignStatustByNo(List updateNoList) {
		return iEmployeeContractService.updateSignStatustByNo(updateNoList);
	}

	@Override
	public Integer commitEmployeeContractByBatchNo(List<String> commitNoList) {
		return iEmployeeContractService.commitEmployeeContractByBatchNo(commitNoList);
	}

	@Override
	public Integer stopEmployeeContract(EmployeeContractVo employeeContractVo) {
		return iEmployeeContractService.stopEmployeeContract(employeeContractVo);
	}

	@Override
	public Page<EmpContractLogVo> getEmployeeContractLogPage(Integer page, Integer limit, EmpContractLogVo empContractLogVo) {
		Map<String, String> listSignTitle = dsUserService.getAllUserMap();
		Page<EmpContractLogVo> empContractLogVoPage = iEmployeeContractService.getEmployeeContractLogPage(page, limit, empContractLogVo);
		List<EmpContractLogVo> records = empContractLogVoPage.getRecords();
		records.forEach(empContractLogVoCache -> {
			empContractLogVoCache.setCreator(listSignTitle.get(empContractLogVoCache.getCreator()));
			empContractLogVoCache.setUpdater(listSignTitle.get(empContractLogVoCache.getUpdater()));
		});
		empContractLogVoPage.setRecords(records);
		return empContractLogVoPage;
	}

	@Override
	public List<EmployeeContractVo> getEmployeeContractList(EmployeeContractVo employeeContractList) {
		List<EmployeeContractVo> employeeContractVoList = iEmployeeContractService.getEmployeeContractList(employeeContractList);
		List<String> empContractNoList = employeeContractVoList.stream().map(EmployeeContractVo::getEmpContractNo).collect(Collectors.toList());
		List<EmployeeContractOperationLogVo> employeeContractOperationLogByEmpContractNoList = employeeContractOperationLogMapper.getEmployeeContractOperationLogByEmpContractNoList(empContractNoList);
		Map<String, List<EmployeeContractOperationLogVo>> employeeContractOperationLogByEmpContractNoMap = Maps.newHashMap();
		if (CollectionUtils.isNotEmpty(employeeContractOperationLogByEmpContractNoList)){
			employeeContractOperationLogByEmpContractNoMap =employeeContractOperationLogByEmpContractNoList.stream().collect(Collectors.groupingBy(EmployeeContractOperationLogVo::getEmpContractNo));
		}
		DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//		获取所有登录人姓名
		Map<String, String> listSignTitle = dsUserService.getAllUserMap();
		//  获取所有公司
		Map<String, String> signTitle = iOrgPositionWrapperService.getAllOrgName(null);

        List<String> orderNoList = employeeContractVoList.stream().map(EmployeeContractVo::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, String> orderNoAndSuccessContractMap = qysContractTableService.getBusNameByOrderNoList(orderNoList);

		for (EmployeeContractVo employeeContractVo : employeeContractVoList) {
            Optional.ofNullable(orderNoAndSuccessContractMap.get(employeeContractVo.getOrderNo())).ifPresent(employeeContractVo::setAlreadyQYS);
			if (!Objects.isNull(employeeContractVo.getReSignDate())){
				employeeContractVo.setStartDate(employeeContractVo.getReSignDate());
			}
			// 终止
			StringBuilder stopOperation = new StringBuilder ();
			List<EmployeeContractOperationLogVo> employeeContractOperationLogVos = employeeContractOperationLogByEmpContractNoMap.get(employeeContractVo.getEmpContractNo());
			if (CollectionUtils.isNotEmpty(employeeContractOperationLogVos)){
				for (EmployeeContractOperationLogVo employeeContractOperationLogVo : employeeContractOperationLogVos) {
					Date createTime = employeeContractOperationLogVo.getCreateTime();
					String format = dateformat.format(createTime);
					String operation = OPERATIONNAME+":" +listSignTitle.get(employeeContractOperationLogVo.getCreator())+","+OPERATIONDATE +":"+ format+";";
					stopOperation.append(operation);
				}
				employeeContractVo.setOprRemark(stopOperation.toString());
			}
			setEmpContractStatus(employeeContractVo);
			employeeContractVo.setCreator(listSignTitle.get(employeeContractVo.getCreator()));
			employeeContractVo.setDispatchMan(listSignTitle.get(employeeContractVo.getDispatchMan()));
			employeeContractVo.setReceivingMan(listSignTitle.get(employeeContractVo.getReceivingMan()));
			employeeContractVo.setSignPlace(signTitle.get(employeeContractVo.getSignPlace()));
			employeeContractVo.setTempPlace(signTitle.get(employeeContractVo.getTempPlace()));
			employeeContractVo.setContractTypeStr(ContractType.getNameByCode(employeeContractVo.getContractType()));
			if (StringUtils.isNotEmpty(employeeContractVo.getWorkMethod())) {
				employeeContractVo.setWorkMethod(EnumsUtil.getNameByCode(Integer.parseInt(employeeContractVo.getWorkMethod()), WorkMethodEnum.class));
			}
			if (null != employeeContractVo.getEntryTime()) {
				employeeContractVo.setEntryTimeEx(employeeContractVo.getEntryTime());
			}
		}
		return employeeContractVoList;
	}

	public static void setEmpContractStatus(EmployeeContractVo employeeContractVo) {
		employeeContractVo.setEmpContractStatus(
				null != employeeContractVo.getSignStatus() && null != employeeContractVo.getEntryDimissionStatus() && null != employeeContractVo.getEndDate() ?
						(employeeContractVo.getSignStatus() == 3 || employeeContractVo.getEntryDimissionStatus() == 3) ?
								"终止" : employeeContractVo.getEndDate().compareTo(new Date()) < 0 ?
								"过期" : "正常" : ""
		);
	}

	@Override
	public Integer batchSave(ArrayList<EmployeeContractVo> employeeContractVoList) {
		return iEmployeeContractService.batchSave(employeeContractVoList);
	}

	@Override
	public List<EmployeeContractVo> getExpiredEmployeeContractList() {
		return iEmployeeContractService.getExpiredEmployeeContractList();
	}

	@Override
	public List<EmployeeContractVo> getExpiredMonthLaterEmployeeContractList() {
		return iEmployeeContractService.getExpiredMonthLaterEmployeeContractList();
	}

	@Override
	public List<String> getAllOrderNo() {
		return iEmployeeContractService.getAllOrderNo();
	}

	@Override
	public Long getEmpIdByEmpNo(String empNo) {
		return iEmployeeContractService.getEmpIdByEmpNo(empNo);
	}

	@Override
	public Page<EmployeeOrderVo> getContractDataByCertNo(Integer limit, Integer page, EmployeeOrderVo employeeOrderVo) {
		return iEmployeeContractService.getContractDataByCertNo(limit, page, employeeOrderVo);
	}

	@Override
	public int deleteFileId(String fileId,String loginName) {
		return iEmployeeContractService.deleteFileId(fileId,loginName);
	}

	@Override
	public void updateEmployeeContractByTransfer(List<String> orderNos) {
		 iEmployeeContractService.updateEmployeeContractByTransfer(orderNos);
	}



	@Override
	public void reSignEmployeeContract(EmployeeContractVo employeeContractVo) {
		iEmployeeContractService.reSignEmployeeContract(employeeContractVo);
	}

	@Override
	public int insertAgentContractFile(OrderContractFileVo orderContractFileVo) {
		return iEmployeeContractService.insertAgentContractFile(orderContractFileVo);
	}

	@Override
	public List<OrderContractFileVo> getFileByEmpId(Long empId, Integer fileType) {
		return iEmployeeContractService.getFileByEmpId(empId,fileType);
	}

	@Override
	public List<OrderContractFileVo> getFileByOrderNo(String orderNo, Integer fileType) {
		return iEmployeeContractService.getFileByOrderNo(orderNo, fileType);
	}

    @Override
    public QysMappingFieldDto getQysMappingFieldDtoData(String orderNo, String orgCode, Map<String, Object> renewConditionMap) {
        return iEmployeeContractService.getQysMappingFieldDtoData(orderNo,orgCode,renewConditionMap);
    }



    @Override
	public void batchAddEmployeeContractImport(ImportDataDto<AddEmployeeContractImportDto> employeeContractImportDataDto) {
		iEmployeeContractService.batchAddEmployeeContractImport(employeeContractImportDataDto);
	}


	@Override
	public List<String> batchImportTransferEmpContractInfo(ImportDataDto<AddTransferEmpContractImportDto> importDataDto) {
	return	iEmployeeContractService.batchImportTransferEmpContractInfo(importDataDto);
	}

	@Override
	public Page<EmployeeContractVo> getEmployeeContractAllPage (Integer page, Integer limit, EmployeeContractVo employeeContractVo) throws ParseException {
		return iEmployeeContractService.getEmployeeContractAllPage(page,limit,employeeContractVo);
	}

	@Override
	public int getCountByFileId(String orderNo, String fileId) {
		return iEmployeeContractService.getCountByFileId(orderNo, fileId);
	}

	@Override
	public EmployeeContractOperationLogVo getEmployeeContractOperationLogByEmpContractNo(String empContractNo) {
		return iEmployeeContractService.getEmployeeContractOperationLogByEmpContractNo(empContractNo);
	}

	@Override
	public List<EmployeeContractVo> getEmployeeContractAllList(EmployeeContractVo employeeContract) {
		List<EmployeeContractVo> employeeContractVoList = iEmployeeContractService.getEmployeeContractAllList(employeeContract);
		//		获取所有登录人姓名
		Map<String, String> listSignTitle = dsUserService.getAllUserMap();
		//  获取所有公司
		Map<String, String> signTitle = iOrgPositionWrapperService.getAllOrgName(null);
		employeeContractVoList.forEach(employeeContractVo -> {
			employeeContractVo.setCreator(listSignTitle.get(employeeContractVo.getCreator()));
			employeeContractVo.setDispatchMan(listSignTitle.get(employeeContractVo.getDispatchMan()));
			employeeContractVo.setReceivingMan(listSignTitle.get(employeeContractVo.getReceivingMan()));
			employeeContractVo.setSignPlace(signTitle.get(employeeContractVo.getSignPlace()));
			employeeContractVo.setTempPlace(signTitle.get(employeeContractVo.getTempPlace()));
			if (null != employeeContractVo.getEntryTime()) {
				employeeContractVo.setEntryTimeEx(employeeContractVo.getEntryTime());
			}
		});
		return employeeContractVoList;
	}

	@Override
	public List<EmployeeContractVo> getNeedUploadFileList() {
		return iEmployeeContractService.getNeedUploadFileList();
	}

	@Override
	public List<OrderContractFileVo> getPersonalContractFileByEmpId(Long empId, Integer fileType,String contractNo) {
		return iEmployeeContractService.getPersonalContractFileByEmpId(empId, fileType,contractNo);
	}


	@Override
	public void deleteTransferEmpContractNo() {
		iEmployeeContractService.deleteTransferEmpContractNo();
	}


}
