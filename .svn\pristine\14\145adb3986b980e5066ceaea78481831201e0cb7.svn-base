package com.reon.hr.api.customer.vo;


import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import com.reon.hr.api.base.dto.sys.ServiceSiteCfgDto;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractAreaVo implements Serializable {
    private static final long serialVersionUID = 3989773298330535363L;
    /**
     小合同编号
     */
    @TableId(value = "contract_area_no", type = IdType.INPUT)
    private String contractAreaNo;
    private String oldContractAreaNo;
    /**
     合同编号
     */
    @TableField(value = "contract_no")
    private String contractNo;

    private String quoteNo;// 报价单编码

    private String quoteName;// 报价单名称
    private Integer contractStatus;
    /**
     小合同名称
     */
    @TableField(value = "name")
    private String name;

    /**
     账单模板ID
     */
    @TableField(value = "templet_id")
    private Long templetId;

    private String templetStr;// 客户对应的账单模板

    /**
     派单类型
     */
    @TableField(value = "dispatch_type")
    private Integer dispatchType;

    /**
     小合同状态
     */
    @TableField(value = "status")
    private Integer status;

    /*
     * 城市code
     *
     * */
    @TableField(value = "city_code")
    private Integer cityCode;

    /*
     * 接单方
     * */
    @TableField(value = "receiving")
    private String receiving;

    /**
     接单方类型(0,自有公司，1、供应商公司)
     */
    @TableField(value = "recceiving_type")
    private Integer recceivingType;

    /**
     接单方客服
     */
    @TableField(value = "receiving_man")
    private String receivingMan;

    /**
     接单方客服主管
     */
    @TableField(value = "receiving_supervisor")
    private String receivingSupervisor;

    /**
     特殊说明
     */
    @TableField(value = "remark")
    private String remark;

    /**
     是否需要劳动合同
     */
    @TableField(value = "has_labor_contract")
    private Integer hasLaborContract;

    /**
     劳动合同版本
     */
    @TableField(value = "templet_type")
    private Integer templetType;

    /**
     是否存档
     */
    @TableField(value = "archive_flag")
    private Integer archiveFlag;

    /**
     是否外呼
     */
    @TableField(value = "tel_flag")
    private Integer telFlag;

    /**
     是否单立户
     */
    @TableField(value = "account_flag")
    private Integer accountFlag;

    /**
     单立户Id
     */
    @TableField(exist = false)
    private Long sinAccId;
    /**
     单立户Id
     */
    @TableField(exist = false)
    private String sinAccName;

    /**
     是否优选
     */
    @TableField(value = "first_flag")
    private Integer firstFlag;

    /**
     派单人
     */
    @TableField(value = "dispatch_man")
    private String dispatchMan;

    /**
     创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /*合同*/

    /**
     客服主管
     */
    @TableField(value = "comm_supervisor")
    private String commSupervisor;

    /**
     销售所属公司
     */
    @TableField(value = "belong_company")
    private String belongCompany;

    /**
     合同名称
     */
    @TableField(value = "contract_name")
    private String contractName;


    /**
     签单分公司
     */
    @TableField(value = "sign_com")
    private String signCom;

    /**
     派单分公司
     */
    @TableField(value = "dist_com")
    private String distCom;

    /**
     合同类型
     */
    @TableField(value = "contract_type")
    private Integer contractType;
    private List<Integer> contractTypeList;
    private String contractTypeListStr;

    /**
     客服专员
     */
    @TableField(value = "commissioner")
    private String commissioner;

    /**
     客服机构
     */
    private String commOrg;

    /**
     客服岗位
     */
    private String commPos;

    /*合同模板*/

    /**
     收款方
     */
    @TableField(value = "receiver_id")
    private String receiverId;

    /**
     回款频率(1:当月回款,2:次月回款)
     */
    private Integer receiveFreq;

    /**
     付款方
     */
    @TableField(value = "payment_id")
    private String paymentId;

    /**
     支付日期
     */
    private Integer payDate;

    /**
     账单方
     */
    @TableField(value = "gen_bill")
    private String genBill;

    /**
     模板名称
     */
    @TableField(value = "templet_name")
    private String templetName;

    /**
     附件ID
     */
    private String attachmentId;

    /*报价单*/

    /**
     人数
     */
    @TableField(value = "num")
    private Integer num;

    private BigDecimal taxRatio;// 税率值
    private Integer taxFlag;//是否含税

    private BigDecimal price;// 报价金额（不含税）

    private BigDecimal feeRatio; //服务比例(外包二使用)
    private Integer prodType;   //产品类型
    private Integer subType;    // 产品子类
    /**
     人数分布
     */
    @TableField(value = "distribute_type")
    private Integer distributeType;

    /*客户*/
    /**
     客户编号
     */
    @TableField(value = "cust_no")
    private String custNo;

    private Long custId;

    /**
     客户名称
     */
    @TableField(value = "cust_name")
    private String custName;

    /*搜索参数*/
    private Date stopDateS;

    private Date stopDateE;

    /*合同开始、结束时间*/

    /**
     合同起始日
     */
    @TableField(value = "start_date")
    private Date startDate;

    /**
     合同到期日
     */
    @TableField(value = "end_date")
    private Date endDate;


    /*list*/
    private List<ContractAreaVo> contractAreaList;

    /*中间字段*/
    private String receivingId;

    private String distComName;// 派单方名称

    private String receivingName;// 接单方名称

    /**tip 在小合同修改页面使用这个字段，但是不知道啥原因被注释
     * 接单客服经理
     */
//    private String revMgr;

    /**
     分配ID
     */
    private String distributId;


    /**
     * 大区接单总监
     */
//    @TableField(value = "receiving_district_manager")
//    private String receivingDistrictManager;
    /**
     分配标识
     */
    private Integer dispatchFlag;
    /**
     厚道分配标识
     */
    private Integer laterDisFlag;
    /**
     后道客服
     */
    private String laterMan;
    /**
     后道客服姓名
     */
    private String laterManName;
    /**
     后道客服岗位
     */
    private String laterManPos;


    private Long empId;
    /**
     大区
     */
    private List<String> userLargeArea;

    /**
     接单客服机构
     */
    @TableField(value = "receiving_man_org")
    private String receivingManOrg;


    /**
     接单客服岗位
     */
    @TableField(value = "receiving_man_pos")
    private String receivingManPos;

    /**
     是否单工伤
     */
    @TableField(value = "injury_flag")
    private Integer injuryFlag;

    /**
     服务网点里的 截点
     */
    private Integer addSubPoint;

    private ServiceSiteCfgDto serviceSiteCfgDto;

    private List<OrgPositionDto> userOrgPositionDtoList;

    private Integer numberOfEmployes;
    private String orderNo;

}
