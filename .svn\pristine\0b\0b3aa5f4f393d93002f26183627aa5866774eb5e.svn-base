<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.TaxSpreadsheetMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tax_tab_id" jdbcType="BIGINT" property="taxTabId" />
    <result column="tax_level" jdbcType="TINYINT" property="taxLevel" />
    <result column="min_val" jdbcType="INTEGER" property="minVal" />
    <result column="max_val" jdbcType="INTEGER" property="maxVal" />
    <result column="tax_ratio" jdbcType="INTEGER" property="taxRatio" />
    <result column="deduction" jdbcType="INTEGER" property="deduction" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tax_tab_id, tax_level, min_val, max_val, tax_ratio, deduction, creator, create_time, 
    updater, update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tax_spreadsheet
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByTaxTalId" resultType="com.reon.hr.api.base.vo.TaxSpreadsheetVo">
    select
     id, tax_tab_id, tax_level, min_val, max_val, tax_ratio, deduction, creator, create_time,
    updater, update_time
    from tax_spreadsheet
    where del_flag='N'
    and tax_tab_id = #{taxTabId,jdbcType=BIGINT}
    order by tax_level
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tax_spreadsheet
    where tax_tab_id = #{taxTabId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
    insert into tax_spreadsheet (id, tax_tab_id, tax_level, 
      min_val, max_val, tax_ratio, 
      deduction, creator, create_time, 
      updater, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{taxTabId,jdbcType=BIGINT}, #{taxLevel,jdbcType=TINYINT}, 
      #{minVal,jdbcType=INTEGER}, #{maxVal,jdbcType=INTEGER}, #{taxRatio,jdbcType=INTEGER}, 
      #{deduction,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, now(),
      #{updater,jdbcType=VARCHAR}, now()
      )
  </insert>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
    insert into tax_spreadsheet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taxTabId != null">
        tax_tab_id,
      </if>
      <if test="taxLevel != null">
        tax_level,
      </if>
      <if test="minVal != null">
        min_val,
      </if>
      <if test="maxVal != null">
        max_val,
      </if>
      <if test="taxRatio != null">
        tax_ratio,
      </if>
      <if test="deduction != null">
        deduction,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      create_time,
      update_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taxTabId != null">
        #{taxTabId,jdbcType=BIGINT},
      </if>
      <if test="taxLevel != null">
        #{taxLevel,jdbcType=TINYINT},
      </if>
      <if test="minVal != null">
        #{minVal,jdbcType=INTEGER},
      </if>
      <if test="maxVal != null">
        #{maxVal,jdbcType=INTEGER},
      </if>
      <if test="taxRatio != null">
        #{taxRatio,jdbcType=INTEGER},
      </if>
      <if test="deduction != null">
        #{deduction,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      now(),
      now()
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
    update tax_spreadsheet
    <set>
      <if test="taxTabId != null">
        tax_tab_id = #{taxTabId,jdbcType=BIGINT},
      </if>
      <if test="taxLevel != null">
        tax_level = #{taxLevel,jdbcType=TINYINT},
      </if>
      <if test="minVal != null">
        min_val = #{minVal,jdbcType=INTEGER},
      </if>
      <if test="maxVal != null">
        max_val = #{maxVal,jdbcType=INTEGER},
      </if>
      <if test="taxRatio != null">
        tax_ratio = #{taxRatio,jdbcType=INTEGER},
      </if>
      <if test="deduction != null">
        deduction = #{deduction,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
        update_time = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
    update tax_spreadsheet
    set tax_tab_id = #{taxTabId,jdbcType=BIGINT},
      tax_level = #{taxLevel,jdbcType=TINYINT},
      min_val = #{minVal,jdbcType=INTEGER},
      max_val = #{maxVal,jdbcType=INTEGER},
      tax_ratio = #{taxRatio,jdbcType=INTEGER},
      deduction = #{deduction,jdbcType=INTEGER},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = now(),
      del_flag = #{delFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="deleteTaxSpreadsheetById" parameterType="com.reon.hr.sp.base.entity.sys.taxSpreadsheet">
 update tax_spreadsheet
 set del_flag = 'Y',
 where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>