/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/11/21
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.cmb;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseNoticeInfo
 * @description TODO
 * @date 2023/11/21 16:31
 */
@Data
public class BaseNoticeInfo {

    /**
     * 签名时间 格式为年月日时分秒
     */
    private String sigtim;

    /**
     * 签名内容
     */
    private String sigdat;
    /**
     * 通知键值
     */
    private String notkey;
    /**
     * 通知内容 通知具体内容，见各通知具体接口说明，格式为字符串，可以格式化为json对象
     */
    private String notdat;

    /**
     * 用户编号
     */
    private String usrnbr;

    /**
     *通知编号
     */
    private String notnbr;

    /**
     * 通知类型 (有很多种，目前我们只用到 YQN02030：支付结果通知，YQN03010：代发结果通知)
     */
    private String nottyp;
}
