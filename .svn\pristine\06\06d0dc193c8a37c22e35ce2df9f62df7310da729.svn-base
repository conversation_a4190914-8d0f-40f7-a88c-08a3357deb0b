<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>上传网银记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <input type="hidden" id="orgCode" name="orgCode">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="分公司">分公司：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="orgName" maxlength="20" name="orgName" placeholder="请选择"
                               class="layui-input layui-input-disposable" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="上传人">上传人：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="uploaderName" maxlength="20" name="uploaderName" placeholder="请输入"
                               class="layui-input layui-input-disposable" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="上传开始时间">上传开始时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                               name="startCreateTime"
                               id="startCreateTime" placeholder="请选择" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="上传结束时间">上传结束时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                               name="endCreateTime"
                               id="endCreateTime" placeholder="请选择" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                       lay-submit="">检索</a>
                    <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card-body">
    <table class="layui-hide" id="netSilverUploadTable" lay-filter="netSilverUploadTableFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <button class="layui-btn layui-btn-sm" lay-event="downloadTemplate" authURI="/bill/financial/downloadTemplate">
            模板下载
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="upload" authURI="/bill/financial/upload">上传</button>
    </script>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/financial/netSilverUpload.js?v=${publishVersion}"></script>
</body>
</html>
