package com.reon.hr.common.enums.log;

import com.reon.hr.common.enums.BaseIEnum;

/**
 * 业务处理状态(1:初始化、2:成功、3:部分成功、4:失败)
 */
public enum BusHandleStatus implements BaseIEnum {
    INIT(1,"初始化"),//请求发送之前
    SUCCESS(2,"成功"),
    PARTIAL_SUCCESS(3,"部分成功"),
    FAIL(4,"失败");

    private Integer code;
    private String name;
    @Override
    public String getName() {
        return name;
    }

    @Override
    public int getCode() {
        return code;
    }

    BusHandleStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
