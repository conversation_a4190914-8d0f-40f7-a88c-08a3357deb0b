package com.reon.hr.sp.customer.service.impl.qiyuesuo;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysMappingFieldVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysSecretKeyVo;
import com.reon.hr.api.thirdpart.dubbo.service.rpc.qys.IQYSNotifyWrapperService;
import com.reon.hr.sp.customer.dao.cus.CustomerMapper;
import com.reon.hr.sp.customer.dao.qiyuesuo.QysMappingFieldMapper;
import com.reon.hr.sp.customer.dao.qiyuesuo.QysSecretKeyMapper;
import com.reon.hr.sp.customer.service.qiyuesuo.QysSecretKeyService;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 <AUTHOR>
 @Description
 @Date 2024年05月07日
 @Version 1.0 */
@Service
public class QysSecretKeyServiceImpl implements QysSecretKeyService {

    @Resource
    private QysSecretKeyMapper qysSecretKeyMapper;

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private QysMappingFieldMapper qysMappingFieldMapper;

    @Override
    public List<String> getAllSecretKey() {
        return qysSecretKeyMapper.getAllSecretKey();
    }

    @Override
    public Page<QysSecretKeyVo> getAllCallBackKey(QysSecretKeyVo qysSecretKeyVo) {
        Page<QysSecretKeyVo> qysSecretKeyVoPage = new Page<>(qysSecretKeyVo.getPage(), qysSecretKeyVo.getLimit());
        List<QysSecretKeyVo> allCallBackKey = qysSecretKeyMapper.getAllCallBackKey(qysSecretKeyVoPage, qysSecretKeyVo);
        if (CollectionUtils.isNotEmpty(allCallBackKey)) {
            List<CustomerVo> allCustomer = customerMapper.getAllCustomerAndGroupName(null);
            Map<Long, CustomerVo> custIdMap = allCustomer.stream().collect(Collectors.toMap(CustomerVo::getId, Function.identity()));
            for (QysSecretKeyVo secretKeyVo : allCallBackKey) {
                if (secretKeyVo.getCustId() != null) {
                    CustomerVo customerVo = custIdMap.get(secretKeyVo.getCustId());
                    secretKeyVo.setCustName(customerVo.getCustName());
                    if (StringUtils.isNotEmpty(customerVo.getGroupName())) {
                        secretKeyVo.setGroupName(customerVo.getGroupName());
                    }
                }
            }
        }
        return qysSecretKeyVoPage.setRecords(allCallBackKey);
    }


    @Override
    public QysSecretKeyVo getCallBackKeyById(Long id) {
        QysSecretKeyVo callBackKeyById = qysSecretKeyMapper.getCallBackKeyById(id);
        if (callBackKeyById.getCustId() != null) {
            List<CustomerVo> allCustomerAndGroupName = customerMapper.getAllCustomerAndGroupName(callBackKeyById.getCustId());
            callBackKeyById.setCustName(allCustomerAndGroupName.get(0).getCustName());
            callBackKeyById.setGroupName(allCustomerAndGroupName.get(0).getGroupName());
        }

        return callBackKeyById;
    }

    @Override
    public int addCallBackKey(QysSecretKeyVo qysSecretKeyVo) {
        return qysSecretKeyMapper.addCallBackKey(qysSecretKeyVo);
    }

    @Override
    public int updateCallBackKey(QysSecretKeyVo qysSecretKeyVo) {
        return qysSecretKeyMapper.updateByPrimaryKeySelective(qysSecretKeyVo);
    }

    @Override
    public QysSecretKeyVo getQysSecretKeyVoByBuClasId(Long buClasId) {
        return qysSecretKeyMapper.getQysCategoryData(buClasId);
    }

    @Override
    public List<QysMappingFieldVo> getCheckTemplateFields() {
        List<QysMappingFieldVo> dataList = qysMappingFieldMapper.getQysMappingField();
        if (dataList == null) return Lists.newArrayList();
        return dataList;
    }

    @Override
    public List<QysSecretKeyVo> getAllQysSecretKeyVo() {
        return qysSecretKeyMapper.getAllQysSecretKeyVo();
    }

    @Override
    public void batchUpdateByBusClasId(List<QysSecretKeyVo> qysSecretKeyVos) {
        qysSecretKeyMapper.batchUpdateByBusClasId(qysSecretKeyVos);
    }


    @Override
    public QysSecretKeyVo getQysSecretKeyVoBySecretKey(String secretKey) {
        return qysSecretKeyMapper.getQysSecretKeyVoBySecretKey(secretKey);
    }
}
