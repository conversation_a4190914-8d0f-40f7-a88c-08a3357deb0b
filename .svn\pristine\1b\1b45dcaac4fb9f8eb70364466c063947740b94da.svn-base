package com.reon.hr.sp.bill.dao.bill;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.reon.hr.sp.bill.entity.bill.BeforeOrderServiceMonth;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BeforeOrderServiceMonthMapper extends BaseMapper<BeforeOrderServiceMonth> {
    int deleteByPrimaryKey(Long id);

    Integer insert(BeforeOrderServiceMonth record);

    int insertSelective(BeforeOrderServiceMonth record);

    BeforeOrderServiceMonth selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BeforeOrderServiceMonth record);

    int updateByPrimaryKey( BeforeOrderServiceMonth record);

    int deleteAll();

    int insertList(@Param("list") List<BeforeOrderServiceMonth> subList);

    Integer batchUpdateByList(@Param("list")List<BeforeOrderServiceMonth> updateList);

    List<Long> selectAllIdList();

    List<BeforeOrderServiceMonth> selectBeforeOrderServiceMonthVos(@Param("updateTime") String updateTime,@Param("orderNos") List<String> orderNos);
    List<BeforeOrderServiceMonth> getBeforeServiceMonthByOrderNo(@Param("orderNo") String orderNo);

	int batchUpdateNoDealMonthByOrderNoAndProductCode(@Param("list") List<BeforeOrderServiceMonth> beforeOrderServiceMonthUpdates);

    int updateServiceMonthCacheByIdList(@Param("list") List<BeforeOrderServiceMonth> item);
}