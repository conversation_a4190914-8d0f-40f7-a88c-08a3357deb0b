<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="Gradle : com.reon.hr : reon-admin-web-1.0-SNAPSHOT.war (exploded)" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="304a78c6-1ebf-404a-b029-4103d21aa510" name="更改" comment="实做支付查询页调整新增、修改、查看字段label显示不全问题">
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/java/com/reon/hr/modules/report/controller/OrderFeesController.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/java/com/reon/hr/modules/report/controller/OrderFeesController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/contractArea/contractAreaListPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/contractArea/contractAreaListPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/personOrder/personOrderQuery.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/personOrder/personOrderQuery.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/supplier/supplierListPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/customer/supplier/supplierListPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/report/orderFeesReport.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/WEB-INF/views/report/orderFeesReport.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/contractArea/contractArea.js" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/contractArea/contractArea.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/personOrder/personOrderQuery.js" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/personOrder/personOrderQuery.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/salary/employee/salaryPersonnelSelect.js" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/customer/salary/employee/salaryPersonnelSelect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/report/orderFeesReport.js" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-web/src/main/webapp/js/modules/report/orderFeesReport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/vo/OrderFeeParamVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/vo/OrderFeeParamVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/vo/employee/PersonOrderQueryVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/vo/employee/PersonOrderQueryVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/cus/ContractAreaMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/cus/ContractAreaMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/EmployeeOrderMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/EmployeeOrderMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/OrderInsuanceCfgMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/OrderInsuanceCfgMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/PersonOrderQueryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/mysql/employee/PersonOrderQueryMapper.xml" afterDir="false" />
    </list>
    <list id="37766218-ece0-4cd2-b50d-990443872e41" name="config" comment="">
      <change beforePath="$PROJECT_DIR$/reon-admin-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-admin-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-base-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-base-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-bill-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-bill-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/utils/ServerUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-api/src/main/java/com/reon/hr/api/customer/utils/ServerUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-customer-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-ehr-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-ehr-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/ModuleType.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/ModuleType.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/report/ConsumerScopeTypeReport.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/report/ConsumerScopeTypeReport.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/report/ProducerScopeTypeReport.java" beforeDir="false" afterPath="$PROJECT_DIR$/reon-rabbitmq-component/src/main/java/com/reon/hr/rabbitmq/enums/report/ProducerScopeTypeReport.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-report-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-report-sp/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/reon-workflow-sp/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/reon-workflow-sp/src/main/resources/jdbc.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CommittedChangesCache">
    <option name="refreshEnabled" value="true" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/reon-bill-sp">
          <activation />
        </task>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
                <item name="reon-bill-sp" type="2d1252cf:ModuleNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
                <item name="reon-bill-sp" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="reon" type="f1a62948:ProjectNode" />
                <item name="reon-bill-sp" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="JavaScript File" />
        <option value="CSS File" />
      </list>
    </option>
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yiZG9uat1uFqV19Y9j9aRzTGZ0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;Gradle.reon [build].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.reon [clean].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.reon:reon-admin-sp [:reon-admin-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-base-sp [:reon-base-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-bill-sp [:reon-bill-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-bill-sp [clean].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.reon:reon-change-base-sp [:reon-change-base-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-customer-api [:reon-customer-api:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-customer-sp [:reon-customer-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-ehr-sp [:reon-ehr-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-file-sp [:reon-file-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-report-sp [:reon-report-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-thirdpart-sp [:reon-thirdpart-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.reon:reon-workflow-sp [:reon-workflow-sp:org.apache.dubbo.container.Main.main()].executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.下载源代码.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.构建 reon.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TestContractServiceImpl.testContractBillWrapperService.executor&quot;: &quot;Debug&quot;,
    &quot;JavaScript 调试.test-attachment-ui.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.test-vue.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.text.html.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ReonEhrApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ReonThirdpartApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Tomcat 服务器.Tomcat 8.5.executor&quot;: &quot;Debug&quot;,
    &quot;Tomcat 服务器.welfareUpload.jsp.executor&quot;: &quot;Debug&quot;,
    &quot;Tomcat 服务器.未命名.executor&quot;: &quot;Debug&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;grid.search.filter.rows&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Users/<USER>/Desktop/case/reon/reon-admin-web/src/main/webapp/WEB-INF/views/serviceSiteCfg&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;CSS&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\java\\idea\\IntelliJ IDEA 2023.3.6\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.admin.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.base.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.bill.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.customer.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.ehr.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.file.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.report.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.thirdpart.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.workflow.executor&quot;: &quot;Debug&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Users\as\Desktop\case\reon\reon-admin-web\src\main\webapp\WEB-INF\views\serviceSiteCfg" />
      <recent name="D:\Users\as\Desktop\case\reon\reon-admin-web\src\main\webapp\js\modules\customer\batchImport" />
      <recent name="D:\Users\as\Desktop\case\reon\reon-admin-web\src\main\webapp\layui" />
      <recent name="D:\Users\as\Desktop\case\reon\reon-admin-web\src\main\webapp\layui\lay" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Users\as\Desktop\case\reon\reon-admin-web\src\main\webapp\layui" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="http-client &lt;request&gt;" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Gradle.reon [build]">
    <configuration default="true" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-admin-sp.main" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="admin" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-admin-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="base" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-base-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="bill" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-bill-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="customer" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-customer-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ehr" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-ehr-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="file" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-file-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="report" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-report-sp.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="thirdpart" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-thirdpart-sp.main" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="workflow" type="Application" factoryName="Application">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="org.apache.dubbo.container.Main" />
      <module name="reon.reon-workflow-sp.main" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="GradleRunConfiguration" factoryName="Gradle">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list />
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="reon [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="TestContractServiceImpl.testContractBillWrapperService" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="reon.reon-customer-sp.test" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.reon.hr.sp.customer.service.cus.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.reon.hr.sp.customer.service.cus" />
      <option name="MAIN_CLASS_NAME" value="com.reon.hr.sp.customer.service.cus.TestContractServiceImpl" />
      <option name="METHOD_NAME" value="testContractBillWrapperService" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="test-vue.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/reon/reon.reon-admin-web.main/webapp/test-vue.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="text.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/reon/reon.reon-admin-web.main/webapp/text.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="ReonEhrApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="reon.reon-ehr-gateway.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.reon.ehr.ReonEhrApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ReonThirdpartApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="reon.reon-thirdpart-gateway.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.reon.thirdpart.ReonThirdpartApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SPRING_BOOT_MAIN_CLASS" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat 8.5" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 8.5" ALTERNATIVE_JRE_ENABLED="false">
      <option name="UPDATE_ON_FRAME_DEACTIVATION" value="true" />
      <option name="UPDATING_POLICY" value="update-resources" />
      <deployment>
        <artifact name="Gradle : com.reon.hr : reon-admin-web-1.0-SNAPSHOT.war (exploded)">
          <settings>
            <option name="CONTEXT_PATH" value="" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="6ce45c10-d0c0-40f5-b4c7-a205b0c63d6c" />
        <option name="HTTP_PORT" value="8082" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="53491" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="Gradle : com.reon.hr : reon-admin-web-1.0-SNAPSHOT.war (exploded)" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="Gradle.reon [build]" />
      <item itemvalue="JavaScript 调试.text.html" />
      <item itemvalue="JavaScript 调试.test-vue.html" />
      <item itemvalue="JUnit.TestContractServiceImpl.testContractBillWrapperService" />
      <item itemvalue="Spring Boot.ReonEhrApplication" />
      <item itemvalue="Spring Boot.ReonThirdpartApplication" />
      <item itemvalue="Tomcat 服务器.Tomcat 8.5" />
      <item itemvalue="应用程序.admin" />
      <item itemvalue="应用程序.base" />
      <item itemvalue="应用程序.bill" />
      <item itemvalue="应用程序.ehr" />
      <item itemvalue="应用程序.customer" />
      <item itemvalue="应用程序.file" />
      <item itemvalue="应用程序.report" />
      <item itemvalue="应用程序.workflow" />
      <item itemvalue="应用程序.thirdpart" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JavaScript 调试.text.html" />
        <item itemvalue="JavaScript 调试.test-vue.html" />
        <item itemvalue="JUnit.TestContractServiceImpl.testContractBillWrapperService" />
        <item itemvalue="Gradle.reon [build]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Users\as\Desktop\case\reon" />
          <option name="myCopyRoot" value="D:\Users\as\Desktop\case\reon" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Users\as\Desktop\case\reon" />
          <option name="myCopyRoot" value="D:\Users\as\Desktop\case\reon" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="304a78c6-1ebf-404a-b029-4103d21aa510" name="更改" comment="" />
      <changelist id="37766218-ece0-4cd2-b50d-990443872e41" name="config" comment="" />
      <created>1750052275713</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750052275713</updated>
      <workItem from="1750319256283" duration="45000" />
      <workItem from="1750319357073" duration="11000" />
      <workItem from="1750319389405" duration="1365000" />
      <workItem from="1750320788258" duration="701000" />
      <workItem from="1750321502859" duration="8252000" />
      <workItem from="1750380557495" duration="22449000" />
      <workItem from="1750641086547" duration="27356000" />
      <workItem from="1750726201709" duration="18678000" />
      <workItem from="1750813856820" duration="160000" />
      <workItem from="1750814036486" duration="66000" />
      <workItem from="1750814142993" duration="16722000" />
      <workItem from="1750898774579" duration="12866000" />
      <workItem from="1750920786248" duration="11452000" />
      <workItem from="1750985201697" duration="27414000" />
      <workItem from="1751019778471" duration="830000" />
      <workItem from="1751244403367" duration="24328000" />
      <workItem from="1751330693273" duration="432000" />
      <workItem from="1751331146072" duration="8672000" />
      <workItem from="1751359728162" duration="60000" />
      <workItem from="1751359804074" duration="79000" />
      <workItem from="1751359940616" duration="350000" />
      <workItem from="1751360810588" duration="5126000" />
      <workItem from="1751417196028" duration="26926000" />
      <workItem from="1751503625747" duration="28302000" />
      <workItem from="1751589916867" duration="347000" />
      <workItem from="1751590742728" duration="30369000" />
      <workItem from="1751849113630" duration="27126000" />
      <workItem from="1751935551878" duration="30668000" />
      <workItem from="1752022205464" duration="29701000" />
      <workItem from="1752108324858" duration="31900000" />
      <workItem from="1752194740592" duration="28607000" />
      <workItem from="1752454035166" duration="620000" />
      <workItem from="1752454893583" duration="27388000" />
      <workItem from="1752540597642" duration="106000" />
      <workItem from="1752540783317" duration="6968000" />
      <workItem from="1752547926754" duration="994000" />
      <workItem from="1752549005127" duration="21830000" />
      <workItem from="1752574132700" duration="15000" />
      <workItem from="1752626751878" duration="3508000" />
      <workItem from="1752630703390" duration="3349000" />
      <workItem from="1752634694732" duration="31000" />
      <workItem from="1752634796145" duration="14280000" />
      <workItem from="1752713180573" duration="27771000" />
      <workItem from="1752799700694" duration="347000" />
      <workItem from="1752800069621" duration="28882000" />
      <workItem from="1753058839500" duration="125000" />
      <workItem from="1753058978978" duration="29481000" />
      <workItem from="1753145177189" duration="30201000" />
      <workItem from="1753231829375" duration="26678000" />
      <workItem from="1753262434217" duration="2974000" />
      <workItem from="1753318061555" duration="24493000" />
      <workItem from="1753347528648" duration="6295000" />
      <workItem from="1753404410361" duration="5904000" />
      <workItem from="1753410905494" duration="11832000" />
      <workItem from="1753427609673" duration="152000" />
      <workItem from="1753427773272" duration="10400000" />
      <workItem from="1753663512368" duration="346000" />
      <workItem from="1753663889729" duration="1984000" />
      <workItem from="1753665964882" duration="17805000" />
      <workItem from="1753688989583" duration="104000" />
      <workItem from="1753689106330" duration="3122000" />
      <workItem from="1753692264969" duration="16000" />
      <workItem from="1753692342521" duration="299000" />
      <workItem from="1753692660210" duration="133000" />
      <workItem from="1753692880997" duration="282000" />
      <workItem from="1753693188734" duration="32000" />
      <workItem from="1753693243203" duration="1018000" />
      <workItem from="1753694320209" duration="2758000" />
      <workItem from="1753750285642" duration="3061000" />
      <workItem from="1753753691198" duration="269000" />
      <workItem from="1753753979264" duration="5072000" />
    </task>
    <task id="LOCAL-00001" summary="小合同上传附件 在合同类型一致的情况下比较 派单方跟城市code，三方一致，需要上传附近，反之 ；分配小合同表格增加附近字段">
      <option name="closed" value="true" />
      <created>1750757948355</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750757948355</updated>
    </task>
    <task id="LOCAL-00002" summary="调整小合同附件上传不提示问题  小合同特殊附件编辑不刷新">
      <option name="closed" value="true" />
      <created>1750843383159</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750843383159</updated>
    </task>
    <task id="LOCAL-00003" summary="小合同附件对旧数据加入对比">
      <option name="closed" value="true" />
      <created>1750845047924</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750845047924</updated>
    </task>
    <task id="LOCAL-00004" summary="计税月限制白名单加扣缴义务人类型筛选">
      <option name="closed" value="true" />
      <created>1750921142442</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750921142442</updated>
    </task>
    <task id="LOCAL-00005" summary="计税月限制白名单.jsp 标签没闭合">
      <option name="closed" value="true" />
      <created>1750923263829</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750923263829</updated>
    </task>
    <task id="LOCAL-00006" summary="服务网点信息表格新增客户端特殊注意事项字段，并在导出中添加显示">
      <option name="closed" value="true" />
      <created>1751003861678</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751003861678</updated>
    </task>
    <task id="LOCAL-00007" summary="1.生成个人订单增员时，对派遣、外包1、外包2类型进行正式工工资验证，不得低于接单方和劳动起始时间在服务网点起始日到截止日范围内的最低工资&#10;2.批量客户导入对导入的文件进行解析正式工资的校验，并增加失败记录数，在查看中体现&#10;3.调整个人订单查询中表格 是否外呼 字段查询的数据库，以详情内是否外呼查询的数据库为准">
      <option name="closed" value="true" />
      <created>1751511797284</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751511797284</updated>
    </task>
    <task id="LOCAL-00008" summary="生成个人订单增员时，对派遣、外包1、外包2类型进行社保工资和试用工资资验证，不得低于接单方和劳动起始时间在服务网点起始日到截止日范围内的最低工资">
      <option name="closed" value="true" />
      <created>1751521507539</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751521507539</updated>
    </task>
    <task id="LOCAL-00009" summary="批量客户导入对因为正式工资验证不正确的，不添加到个人订单查询列表中">
      <option name="closed" value="true" />
      <created>1751536128332</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1751536128332</updated>
    </task>
    <task id="LOCAL-00010" summary="注释增员提交时不符合最低工资与正式工资比较的验证方法">
      <option name="closed" value="true" />
      <created>1751596306088</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1751596306088</updated>
    </task>
    <task id="LOCAL-00011" summary="生成个人订单增员 劳动合同开始时间超过套餐时间范围，提示信息">
      <option name="closed" value="true" />
      <created>1751608984379</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1751608984379</updated>
    </task>
    <task id="LOCAL-00012" summary="生成个人订单增员 提示信息修改">
      <option name="closed" value="true" />
      <created>1751609173960</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1751609173960</updated>
    </task>
    <task id="LOCAL-00013" summary="一次性毛利计算样式调整 改为vue框架">
      <option name="closed" value="true" />
      <created>1751618373880</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1751618373880</updated>
    </task>
    <task id="LOCAL-00014" summary="一次性毛利计算 vue框架改为本地路径+element-plus组件库">
      <option name="closed" value="true" />
      <created>1751865984662</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1751865984662</updated>
    </task>
    <task id="LOCAL-00015" summary="一次性毛利html标签闭合">
      <option name="closed" value="true" />
      <created>1751879810339</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1751879810339</updated>
    </task>
    <task id="LOCAL-00016" summary="社保公积金单个城市、社保公积金一览、社保公积金试算调整逻辑中操作问题，更新页面框架，重构页面调整ui">
      <option name="closed" value="true" />
      <created>1752142922575</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752142922575</updated>
    </task>
    <task id="LOCAL-00017" summary="社保公积金单个城市、社保公积金一览、社保公积金试算  去掉.trim() 验证方法">
      <option name="closed" value="true" />
      <created>1752143327579</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752143327579</updated>
    </task>
    <task id="LOCAL-00018" summary="社保公积金一览 客户表格加溢出滑动">
      <option name="closed" value="true" />
      <created>1752143672761</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752143672761</updated>
    </task>
    <task id="LOCAL-00019" summary="社保公积金试算 调整在添加两个城市计算后再删除一个城市计算报错的问题">
      <option name="closed" value="true" />
      <created>1752199234954</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752199234954</updated>
    </task>
    <task id="LOCAL-00020" summary="社保公积金试算 增加计算前必填验证提醒">
      <option name="closed" value="true" />
      <created>1752200045652</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752200045652</updated>
    </task>
    <task id="LOCAL-00021" summary="社保公积金试算 去掉trim验证">
      <option name="closed" value="true" />
      <created>1752201900711</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1752201900711</updated>
    </task>
    <task id="LOCAL-00022" summary="社保公积金试算 删除逻辑调整">
      <option name="closed" value="true" />
      <created>1752202184295</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1752202184295</updated>
    </task>
    <task id="LOCAL-00023" summary="社保公积金试算 批量设置提醒详情">
      <option name="closed" value="true" />
      <created>1752203039363</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1752203039363</updated>
    </task>
    <task id="LOCAL-00024" summary="社保公积金一览 根据客户导出没有选择客户 阻止生成文件">
      <option name="closed" value="true" />
      <created>1752203611931</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752203611931</updated>
    </task>
    <task id="LOCAL-00025" summary="社保公积金一览 添加全部生成文件 优选">
      <option name="closed" value="true" />
      <created>1752459637285</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752459637285</updated>
    </task>
    <task id="LOCAL-00026" summary="社保公积金试算 调整选择公积金比例计算结果没变问题">
      <option name="closed" value="true" />
      <created>1752470191763</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1752470191763</updated>
    </task>
    <task id="LOCAL-00027" summary="1.小合同管理数据导出增加后道客服字段显示&#10;2.财务-上传网银到款界面查询操作历史日志时增加展示列冻结时候填的备注">
      <option name="closed" value="true" />
      <created>1752569233203</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1752569233205</updated>
    </task>
    <task id="LOCAL-00028" summary="社保公积金单个城市 社保申报频率'公积金申报频率 显示不对问题">
      <option name="closed" value="true" />
      <created>1752573878384</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1752573878384</updated>
    </task>
    <task id="LOCAL-00029" summary="修复小合同导出后道客服不显示问题&#10;财务-上传网银到款界面查询操作历史日志时增加展示列冻结时候填的备注&#10;报表管理：1.增员报表增加根据大集团筛选功能并可导出 2.减员报表增加根据大集团筛选功能并可导出&#10;实收开票查询界面增加筛选项：账单月，可选择账单月后直接进行查询导出，目前是必须选提票起始日期，需将此日期设置为非必填项">
      <option name="closed" value="true" />
      <created>1752803749543</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1752803749543</updated>
    </task>
    <task id="LOCAL-00030" summary="实收开票查询  调整财务应收月起数据查询不对问题">
      <option name="closed" value="true" />
      <created>1752818994712</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1752818994712</updated>
    </task>
    <task id="LOCAL-00031" summary="工资管理，薪资结果查询页面，客户账单月起止时间，工资计税月起止时间，工资所属月起止时间，优化能选到同一个月">
      <option name="closed" value="true" />
      <created>1752831446185</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1752831446185</updated>
    </task>
    <task id="LOCAL-00032" summary="调整供应商合同审批流程 审批窗口内 按钮被遮挡和 审批意见覆盖在表格之上问题">
      <option name="closed" value="true" />
      <created>1753078160152</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753078160152</updated>
    </task>
    <task id="LOCAL-00033" summary="调整供应商合同续签审批流程、供应商合同补充协议审批流程 审批窗口内 按钮被遮挡和 审批意见覆盖在表格之上问题">
      <option name="closed" value="true" />
      <created>1753088193611</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753088193611</updated>
    </task>
    <task id="LOCAL-00034" summary="订单管理——&gt;生成个人订单 验证如果为试用期，则必须填写试用工资">
      <option name="closed" value="true" />
      <created>1753150072099</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753150072099</updated>
    </task>
    <task id="LOCAL-00035" summary="分配大合同时，不应该查到已终止的未分配的大合同。">
      <option name="closed" value="true" />
      <created>1753324315786</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753324315786</updated>
    </task>
    <task id="LOCAL-00036" summary="消息中心，调整分页总数查询不对问题">
      <option name="closed" value="true" />
      <created>1753349075436</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753349075436</updated>
    </task>
    <task id="LOCAL-00037" summary="首页样式调整">
      <option name="closed" value="true" />
      <created>1753350957398</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753350957398</updated>
    </task>
    <task id="LOCAL-00038" summary="设置全局样式，减少代码沉余">
      <option name="closed" value="true" />
      <created>1753420794478</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753420794478</updated>
    </task>
    <task id="LOCAL-00039" summary="实做支付查询页调整新增、修改、查看字段label显示不全问题">
      <option name="closed" value="true" />
      <created>1753695153087</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753695153087</updated>
    </task>
    <option name="localTasksCounter" value="40" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="一次性毛利html标签闭合" />
    <MESSAGE value="社保公积金单个城市、社保公积金一览、社保公积金试算调整逻辑中操作问题，更新页面框架，重构页面调整ui" />
    <MESSAGE value="社保公积金单个城市、社保公积金一览、社保公积金试算  去掉.trim() 验证方法" />
    <MESSAGE value="社保公积金一览 客户表格加溢出滑动" />
    <MESSAGE value="社保公积金试算 调整在添加两个城市计算后再删除一个城市计算报错的问题" />
    <MESSAGE value="社保公积金试算 增加计算前必填验证提醒" />
    <MESSAGE value="社保公积金试算 去掉trim验证" />
    <MESSAGE value="社保公积金试算 删除逻辑调整" />
    <MESSAGE value="社保公积金试算 批量设置提醒详情" />
    <MESSAGE value="社保公积金一览 根据客户导出没有选择客户 阻止生成文件" />
    <MESSAGE value="社保公积金一览 添加全部生成文件 优选" />
    <MESSAGE value="社保公积金试算 调整选择公积金比例计算结果没变问题" />
    <MESSAGE value="1.小合同管理数据导出增加后道客服字段显示&#10;2.财务-上传网银到款界面查询操作历史日志时增加展示列冻结时候填的备注" />
    <MESSAGE value="社保公积金单个城市 社保申报频率'公积金申报频率 显示不对问题" />
    <MESSAGE value="修复小合同导出后道客服不显示问题&#10;财务-上传网银到款界面查询操作历史日志时增加展示列冻结时候填的备注&#10;报表管理：1.增员报表增加根据大集团筛选功能并可导出 2.减员报表增加根据大集团筛选功能并可导出&#10;实收开票查询界面增加筛选项：账单月，可选择账单月后直接进行查询导出，目前是必须选提票起始日期，需将此日期设置为非必填项" />
    <MESSAGE value="实收开票查询  调整财务应收月起数据查询不对问题" />
    <MESSAGE value="工资管理，薪资结果查询页面，客户账单月起止时间，工资计税月起止时间，工资所属月起止时间，优化能选到同一个月" />
    <MESSAGE value="调整供应商合同审批流程 审批窗口内 按钮被遮挡和 审批意见覆盖在表格之上问题" />
    <MESSAGE value="调整供应商合同续签审批流程、供应商合同补充协议审批流程 审批窗口内 按钮被遮挡和 审批意见覆盖在表格之上问题" />
    <MESSAGE value="订单管理——&gt;生成个人订单 验证如果为试用期，则必须填写试用工资" />
    <MESSAGE value="分配大合同时，不应该查到已终止的未分配的大合同。" />
    <MESSAGE value="消息中心，调整分页总数查询不对问题" />
    <MESSAGE value="首页样式调整" />
    <MESSAGE value="设置全局样式，减少代码沉余" />
    <MESSAGE value="实做支付查询页调整新增、修改、查看字段label显示不全问题" />
    <option name="LAST_COMMIT_MESSAGE" value="实做支付查询页调整新增、修改、查看字段label显示不全问题" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/reon-customer-sp/src/main/java/com/reon/hr/sp/customer/dubbo/service/rpc/impl/CustomerBatchDownLoadMqServiceImpl.java</url>
          <line>111</line>
          <properties class="com.reon.hr.sp.customer.dubbo.service.rpc.impl.CustomerBatchDownLoadMqServiceImpl" method="generateDownLoadCustomerOrderFeeTask">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.util.HashMap$Node" memberName="value" />
        <PinnedItemInfo parentTag="org.apache.catalina.connector.Response" memberName="writer" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>