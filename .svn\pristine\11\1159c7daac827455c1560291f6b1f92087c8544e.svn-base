package com.reon.hr.sp.base.dubbo.rpc.sys.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.SocialOverviewOneCityService;
import com.reon.hr.api.base.vo.IndCategoryVo;
import com.reon.hr.api.base.vo.SocialCfgOneCityVo;
import com.reon.hr.api.base.vo.SocialGfgOneCitySubVo;
import com.reon.hr.sp.base.service.sys.SocialOverviewOneCitySpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("socialOverviewOneCityService")
public class SocialOverviewOneCityServiceImpl implements SocialOverviewOneCityService {
    @Autowired
     private SocialOverviewOneCitySpService SocialOverviewOneCitySpService;
    @Override
    public SocialCfgOneCityVo getCfgMain(SocialCfgOneCityVo vo) {
        return SocialOverviewOneCitySpService.getCfgMain(vo);
    }

    @Override
    public Page<SocialGfgOneCitySubVo> getSubMain(SocialCfgOneCityVo vo, Integer page, Integer limit) {
        return SocialOverviewOneCitySpService.getSub(vo,page,limit);
    }

    @Override
    public List<IndCategoryVo> getindTypeCode(Integer cityCode) {
        return SocialOverviewOneCitySpService.getindTypeCode(cityCode);
    }
}
