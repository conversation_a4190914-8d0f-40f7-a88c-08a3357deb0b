var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'table', 'tableSelectWithoutSearch', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        upload = layui.upload,
        tableSelectWithoutSearch = layui.tableSelectWithoutSearch;
    layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var uploadIds = [];
    var delFileList = [];
    var fileType = '';
    var fileName = '';

    var appd3 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelectWithoutSearch.render({
        delFlg: ["comName", "comCode"],
        elem: '#comName',
        checkedKey: 'orgCode',
        appd: appd3,
        table: {
            url: ctx + '/sys/org/getCompanyByName',
            cols: [[
                {type: 'radio'},
                {type: 'numbers', title: '序号'},
                {field: 'orgName', title: '分公司名称'}
            ]]
        },
        done: function (elem, data) {
            if (data.data.length > 0) {
                $('#comName').val(data.data[0].orgName);
                $('#comCode').val(data.data[0].orgCode);
            }
        }
    });

    //上传
    upload.render({
        elem: '#companyAddrListUpload' //绑定元素
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: {contentType: false, processData: false}
        , method: 'POST'
        , exts: 'jpg|png|gif|bmp|jpeg'
        , field: 'file'

        , before: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", {icon: 2});
                    return false;
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push({'fileId': res.data.fileId});
                $('#upload').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag">' +
                    '<a href="' + ML.fileServerUrl + res.data.fileId + '"  target="_blank" id="gethref">' + fileName + '</a>' +
                    '<a href="javascript:void(0)" class="deleteFile"  }" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', {icon: 1});
            }
        }
        , error: function () {
            //请求异常回调
            layer.msg('上传失败', {icon: 5});
        }
    });

    $(document).on("click","#close",function () {
        layer.closeAll();
    });

    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        var delIndex;
        for (var i = 0; i < uploadIds.length; i++) {
            if (uploadIds[i].fileId == fileId) {
                delIndex = i;
            }
        }
        uploadIds.splice(delIndex, 1);
        if ($("#optType").val() == "edit") {
            delFileList.push(fileId);
        } else {
            ML.ajax("/customer/quotation/delByFileId?fileId=" + fileId, {}, function (result) {
                    if (result.code == 0) {
                        layer.msg("删除文件成功！")
                    }
                },
                'POST');
        }
        $(this).parent()[0].remove();
    });

    function delFile(fileId) {
        uploadIds.forEach(function (value, index) {
            if (value == fileId) {
                uploadIds.splice(index,1)
            }
        })
        ML.ajax("/customer/quotation/delByFileId?fileId=" + fileId, {}, function (result) {
            },
            'POST');
    };


    var mobile = /^1[3|4|5|7|8]\d{9}$/, phone = /^0\d{2,3}-?\d{7,8}$/;
    form.verify({
        tellphone: function (value) {
            var flag = mobile.test(value) || phone.test(value);
            if (!flag) {
                return '请输入正确座机号码或手机号';
            }
        }
    });

    $("#comTel").blur(function(){
        if($("#comTel").val()){
            $("#comTel").attr("lay-verify", "tellphone");
        }else {
            $("#comTel").removeAttr("lay-verify", "tellphone");
        }
    });

    //监听邮箱输入事件
    $("#comEmail").blur(function(){
        if($("#comEmail").val()){
            $("#comEmail").attr("lay-verify", "email");
        }else {
            $("#comEmail").removeAttr("lay-verify", "email");
        }
    });


    form.on('submit(save)', function (data) {
        saveForm('save', data);
    });

    function saveForm(type, data) {
        //对上传数据进行验证
        if (uploadIds.length < 1) {
            return layer.msg("请上传文件再提交");
        }
        delFileList.forEach(function (value, index) {
            delFile(value)
        });

        var fileIdCache = '';

        for (var i = 0; i < uploadIds.length; i++) {
            if (i == 0) {
                fileIdCache = uploadIds[i].fileId
            } else if (i > 0) {
                fileIdCache = fileIdCache + ',' + uploadIds[i].fileId;
            }
        }

        data.field.fileId = fileIdCache;
        ML.layuiButtonDisabled($('#' + type));
        $.ajax({
            url: ML.contextPath + "/companyAddrList/saveCompanyAddrList",
            type: 'POST',
            dataType: 'json',
            data: {'paramData': JSON.stringify(data.field)},
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error: function (data) {
                layer.msg("系统保存失败，请稍后重试!");
                ML.layuiButtonDisabled($('#' + type), 'true');
            }
        });
    }
});