package com.reon.hr.api.bill.exception;

public class PaymentCustomerException extends RuntimeException {
    public final static String ERROR_MSG = "财务上传异常";
    public final static String DUPLICATE_DATA = "有一行或多行数据重复,请勿重复上传!";

    public PaymentCustomerException() {
        super(ERROR_MSG);
    }

    public PaymentCustomerException(String message) {
        super(message);
    }

    public PaymentCustomerException(String message, Throwable cause) {
        super(message, cause);
    }
}
