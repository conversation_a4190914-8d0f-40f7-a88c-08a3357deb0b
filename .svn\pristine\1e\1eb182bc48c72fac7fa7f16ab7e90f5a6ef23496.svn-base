/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2017年4月16日
 *
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.rabbitmq.enums.bill;


import com.reon.hr.rabbitmq.enums.BaseProducerScopeType;

/**
 * 交易渠道（队列的路由的依据）
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2017年4月16日
 */
public enum ProducerScopeTypeBill implements BaseProducerScopeType {
    REON_BILL_GENERATED_COMPLETED("bill_id", "reon.bill.generated.completed", "生成账单"),
    REON_INCOME_COUNT_TABLE("income_count_table", "reon.income.count.table.completed", "开票或核销后中间表插入"),
    REON_INSURANCE_PRACTICE_LOCK_GENERATE_COMPLETED("insurance_practice_lock_id", "reon.insurance.practice.lock.generate.completed", "生成实做报表锁定"),
    REON_SUPPLIER_PRACTICE_LOCK_GENERATE_COMPLETED("supplier_practice_lock_id", "reon.supplier.practice.lock.generate.completed", "生成供应商实做报表锁定"),
    REON_CONTRACT_FIRST_BILL("contract_first_bill", "reon.contract.first.bill.completed", "合同生成首版账单"),
    REON_SALARY_BILL_UNLOCK("salary_bill_unlock", "reon.salary.bill.unlock", "工资账单解锁"),
    REON_CHECK_APPROVAL("bill_check_approval", "reon.bill.check.approval", "核销后直接成功"),
    REON_BILL_GENERATED_SALARY_COMPLETED("generated_salary_bill", "reon.bill.generated.salary.completed", "生成工资账单"),
    REON_BILL_GENERATED_SUPPLIER_SALARY_COMPLETED("generated_supplier_salary_bill", "reon.bill.generated.supplier.salary.completed", "生成供应商工资账单"),
    REON_BILL_GENERATED_COMMERCIAL_COMPLETED("generated_commercial_bill", "reon.bill.generated.commercial.completed", "生成商保账单"),
    ;

    private String code;
    private String routingKey;
    private String name;

    ProducerScopeTypeBill(String code, String routingKey, String name) {
        this.code = code;
        this.routingKey = routingKey;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public String getName() {
        return name;
    }

    public static ProducerScopeTypeBill from(String code) {
        if (code == null) {
            return null;
        }
        ProducerScopeTypeBill[] vs = ProducerScopeTypeBill.values();
        for (ProducerScopeTypeBill e : vs) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
