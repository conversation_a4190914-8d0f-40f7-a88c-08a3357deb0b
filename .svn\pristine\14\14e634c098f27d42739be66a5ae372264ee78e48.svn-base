package com.reon.hr.sp.dao.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.vo.sys.*;
import com.reon.hr.sp.entity.sys.OrgPosition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface OrgPositionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrgPosition record);

    boolean insertSelective(OrgPositionVo record);

    OrgPosition selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrgPosition record);

    int updateByPrimaryKey(OrgPosition record);

    /**
     * 获取定岗信息，若该用户含有的岗位为选中
     *
     * @param page
     * @param userId
     * @return
     */
    List<UserOrgPosVo> getPageByUserId(Page page, @Param("userId") Long userId, @Param("position") String position, @Param("loginName") String loginName);

    boolean updateByPrimaryKey(OrgPositionVo record);

    List<String> getLargeAreaByUserId(@Param("userId") Long userId);

    List<OrgPositionVo> getOrgPositionQueryList(Page page, Map<String, Object> map);

    List<OrgPositionVo> getUserOrgPosListPage(Page page, Map<String, Object> map);

    List<OrgPositionVo> getUserOrgPosList(Map<String, Object> map);

    List<OrgPositionVo> findLoginNameByPosCodeAndOrgCode(@Param("loginName") String loginName);

    OrgPositionVo findOrgPositionOrgAndPos(Map<String, Object> map);
    String  getOrgName(@Param("orgCode") String orgCode);
    List<OrgUserVo> getAllOrgName(@Param("orgCode") String orgCode);

	List<OrgUserVo> getAllOrganization();
	List<OrgPositionVo> getAllOrgPositionVo();
    List<OrgUserVo> getAllReceivingMan();
    boolean updateOrgPositionLargeArea(@Param("id") Integer id,@Param("largeArea") String largeArea);
    String getOrgPositionLargeArea(@Param("id")Integer id);

    String getOrgNameByOrgCode(Integer orgCode);

    List<OrgPositionVo> getUserOrgPosListByBeforeOrgCodeAndBeforePosCodeAndLowPos(@Param("orgCode") String orgCode, @Param("posCode") String posCode);

    List<OrgPositionVo> getOrgPositionListLikeOrgCode(@Param("orgCode") String source);

	int insertList(@Param("list") List<OrgPosition> orgPositions);

	List<OrgPosition> getAllOrgPosList();

	int getOrgPosNumByOrgCode(@Param("orgCode") String target);

    List<OrgPositionVo> getOrgPositionListByOrgCode(@Param("orgCode")  String source);

    OrgPositionVo getOrgPosById(Long id);

    List<OrgPositionDto> getOrgPositionByUserId(Long userId);

    String  getAllCityName(Long owerCity);


}
