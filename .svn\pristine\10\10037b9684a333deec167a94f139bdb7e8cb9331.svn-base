package com.reon.hr.sp.base.dao.sys;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.base.vo.CustomerCostReportVo;
import com.reon.hr.api.base.vo.DicVo;
import com.reon.hr.api.base.vo.DictVo;
import com.reon.hr.api.base.vo.DictVoS;
import com.reon.hr.sp.base.entity.sys.Dictionary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    int insertSelective(Dictionary record);

    List<Dictionary> findAllDictionary();

    List<DicVo> findAll();

    String getNameByCodeTypeAndCode(@Param("codeType") String codeType, @Param("code") Integer code);
    String getNameByDescriptionTypeAndCode(@Param("codeType") String codeType,@Param("parentCode") Integer parentCode, @Param("code") Integer code);
    List<CustomerCostReportVo> getNameByDescriptionTypeAndCodeList(@Param("list") List<CustomerCostReportVo> code);
    List<DicVo> getAllNameByCodeTypeAndCode(@Param("codeType") String codeType, @Param("code") Integer code);

	List<DicVo> getAllByCodeType(@Param("codeType")String codeType);

    List<DictVo> getAllByParentCodeType(@Param("parentCodeType")String parentCodeType);

    List<DictVoS> getDictVoSByCodeType(@Param("codeType")String codeType);
}
