package com.reon.hr.sp.customer.service.salary;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo;

import java.util.List;

/**
 * <AUTHOR> on 2021/10/11.
 */
public interface SalaryActualInfoService {
    List<SalaryActualInfoVo> getSalaryActualInfoVoListPage(Page<SalaryActualInfoVo> page,SalaryActualInfoVo salaryActualInfoVo);
    List<SalaryActualInfoVo> getSalaryActualInfoVoList(SalaryActualInfoVo salaryActualInfoVo);
    boolean saveSalaryActualInfoVoList(List<SalaryActualInfoVo> salaryActualInfoVoList);


    List<SalaryActualInfoVo> findByDataMonthAndEmpIds(Integer billMonth,List<Long> empIds,List<String> withholdingAgentNoList);

    int deleteByList(List<SalaryActualInfoVo> salaryActualInfoVoList);
}
