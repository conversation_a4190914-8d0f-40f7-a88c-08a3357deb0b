var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect', 'tableSelectWithoutSearch'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        tableSelect = layui.tableSelect,
        tableSelectWithoutSearch = layui.tableSelectWithoutSearch,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    $(document).ready(function () {
        document.getElementById("orderNo").focus();
    });

    /*监听document的回车操作*/
    $(document).bind('keypress', function (event) {
        if (event.keyCode === 13) {
            reloadTable();
        }
    });

        setTimeout(function (){
            table.render({
                id: 'socialSecurityStopHandleId',
                elem: '#stopHandleGridTable'
                ,
                url: ML.contextPath + '/customer/insurancePractice/societyInsuranceStopHandle/getSocietyInsuranceStopHandleList'
                ,
                data: []
                ,
                height: 'full-200'
                ,
                page: true
                ,
                toolbar: '#topbtn'
                ,
                defaultToolbar: []
                ,
                where: {"paramData": JSON.stringify(serialize("searchForm"))}
                ,
                limit: 50
                ,
                limit: 50
                ,
                method: 'GET'
                ,
                limits: [50, 100, 200]
                ,
                text: {
                    none: '暂无数据' //无数据时展示
                }
                ,
                done: function (res, curr, count) {
                    ML.hideNoAuth();
                    if (res.data ) {
                        for (var i = 0; i < res.data.length; i++) {
                            if (res.data[i].handleStatus === 9) {
                                $("tr").eq(i + 1).css("color", "LightGrey");
                                $(".layui-table-fixed").find("div").find("table").find("table").eq(i + 1).css("color", "LightGrey");
                            }
                        }
                        $(".layui-table-fixed-l").find("div").find("thead").find("tr").css("color", "");
                    }
                }
                ,
                cols: [[
                    {field: '', type: 'checkbox', width: '50', fixed: 'left'},
                    {field: '', type: 'numbers', width: '50', fixed: 'left', title: '序号'}
                    , {field: 'empName', title: '雇员姓名', align: 'center', width: '150'}
                    , {
                        field: 'certNo', title: '证件号码', align: 'center', width: '150'
                    }
                    , {
                        field: 'mobile', title: '手机号码', align: 'center', width: '100'
                    }
                    , {field: 'orderNo', title: '订单编号', align: 'center', width: '150'}
                    , {
                        field: 'orgCode', title: '福利办理方', align: 'center', width: '150', templet: function (d) {
                            if (ML.isNotEmpty(d.orgCode)) {
                                return CN.getCompanyName(d.orgCode)
                            }
                        }
                    }
                    , {field: 'packName', title: '福利包名称', align: 'center', width: '150'}
                    , {field: 'packCode', title: '福利包编号', align: 'center', width: '150'}
                    , {field: 'custName', title: '客户名称', width: '150', align: 'center'}
                    , {field: 'custNo', title: '客户编号', width: '150', align: 'center'}
                    , {
                        field: 'dispatchType', title: '服务区域类型', width: '100', align: 'center', templet: function (d) {
                            if (d.dispatchType) {
                                return ML.dictFormatter("EMP_DISTRIBUTE_TYPE", d.dispatchType)
                            } else {
                                return ""
                            }
                        }
                    }
                    , {field: 'contractAreaName', title: '小合同', width: '100', align: 'center'}
                    , {field: 'signComName', title: '派单方', width: '100', align: 'center'}
                    , {field: 'distComName', title: '接单方', align: 'center', width: '100'}
                    , {field: 'commissioner', title: '派单方客服', align: 'center', width: '110'}
                    , {field: 'receivingMan', title: '接单方客服', align: 'center', width: '110'}
                    // , {field: 'acctNo', title: '账号', align: 'center', width: '150'}
                    , {field: 'ratioName', title: '比例名称', align: 'center', width: '150'}
                    , {
                        field: 'entryDate', title: '入职日期', align: 'center', width: '150', templet: function (d) {
                            if (d.entryDate) {
                                return d.entryDate.split(" ")[0]
                            } else {
                                return ""
                            }
                        }
                    }
                    , {
                        field: 'handler', title: '办理人', align: 'center', width: '150', templet: function (d) {
                            return ML.loginNameFormater(d.handler)
                        }
                    }
                    , {
                        field: 'procTime', title: '办理时间', align: 'center', width: '150', templet: function (d) {
                            if (d.procTime) {
                                return d.procTime.split(" ")[0]
                            } else {
                                return ""
                            }
                        }
                    }
                    ,
                    {
                        field: 'handleStatus', title: '状态', width: '100', align: 'center', templet: function (d) {
                            // if (d.handleStatus) {
                            //     return ML.dictFormatter("HANDLE_STATUS", d.handleStatus)
                            // } else {
                            //     return ""
                            // }
                            return "待停办";
                        }
                    }
                    , {field: 'startMonth', title: '福利起始月', align: 'center', width: '150'}
                    , {
                        field: 'dimissionDate', title: '离职日期', align: 'center', width: '150', templet: function (d) {
                            if (d.dimissionDate) {
                                return d.dimissionDate.split(" ")[0]
                            } else {
                                return ""
                            }
                        }
                    }
                    , {field: 'expiredMonth', title: '收费截止月', align: 'center', width: '150'}
                    ,{
                        field: 'dimissionReason', title: '离职原因', width: 160, align: 'center', templet: function (d) {
                            return ML.dictFormatter("LEAVE_REASON", d.dimissionReason);
                        }
                    }
                ]]
            });
        },500)




    //日期范围
    var addDateS = laydate.render({
        elem: '#addDateS',
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                addDateE.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            } else {
                addDateE.config.min = {
                    date: 1,
                    hours: 0,
                    minutes: 0,
                    month: 0,
                    seconds: 0,
                    year: 1900
                };
            }
        }
    });

    var addDateE = laydate.render({
        elem: '#addDateE',//选择器结束时间
        min: "1970-01-01",//设置min默认最小值
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        done: function (value, date) {
            if (null != value && '' != value) {
                addDateS.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            } else {
                addDateS.config.max = {
                    date: 31,
                    hours: 0,
                    minutes: 0,
                    month: 11,
                    seconds: 0,
                    year: 2099
                }
            }
        }
    });



  laydate.render({
        elem: '#expiredMonth',//选择器结束时间
        btns: ['clear', 'confirm'],
        format: 'yyyyMM'
        , type: 'month',
        done: function (value, date) {
        }
    });

    // 离职日期
    var dimissionDateS = laydate.render({
        elem: '#dimissionDateS',
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                dimissionDateE.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            } else {
                dimissionDateE.config.min = {
                    date: 1,
                    hours: 0,
                    minutes: 0,
                    month: 0,
                    seconds: 0,
                    year: 1900
                };
            }
        }
    });

    var dimissionDateE = laydate.render({
        elem: '#dimissionDateE',//选择器结束时间
        min: "1970-01-01",//设置min默认最小值
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        done: function (value, date) {
            if (null != value && '' != value) {
                dimissionDateS.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            } else {
                dimissionDateS.config.max = {
                    date: 31,
                    hours: 0,
                    minutes: 0,
                    month: 11,
                    seconds: 0,
                    year: 2099
                }
            }
        }
    });


    // 办理日期
    var procTimeS = laydate.render({
        elem: '#procTimeS',
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                procTimeE.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            } else {
                procTimeE.config.min = {
                    date: 1,
                    hours: 0,
                    minutes: 0,
                    month: 0,
                    seconds: 0,
                    year: 1900
                };
            }
        }
    });

    var procTimeE = laydate.render({
        elem: '#procTimeE',//选择器结束时间
        min: "1970-01-01",//设置min默认最小值
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        done: function (value, date) {
            if (null != value && '' != value) {
                procTimeS.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            } else {
                procTimeS.config.max = {
                    date: 31,
                    hours: 0,
                    minutes: 0,
                    month: 11,
                    seconds: 0,
                    year: 2099
                }
            }
        }
    });






    //监听行双击事件
    table.on('rowDouble(stopHandleGridTableFilter)', function (obj) {
        var url = $("#detail").attr("authURI");
        if (ML.isAuth(url)) {
            check(obj.data);
        }
        return false;
    });
    table.on('toolbar(stopHandleGridTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'queryEmployeeOrder':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                addView("查看个人订单", "query", checkStatus.data[0]);
                break;
            case 'stopHandle':
                if (checkStatus.data.length < 1) {
                    return layer.msg("请选择数据!");
                }
                var selected = [];
                let orgCodeList = [];
                let packCodeList = [];
                for (var i = 0; i < checkStatus.data.length; i++) {
                    selected.push(checkStatus.data[i].id)
                    orgCodeList.push(checkStatus.data[i].orgCode);
                    packCodeList.push(checkStatus.data[i].packCode);
                }
                stopHandle("stopHandle", "停办", selected,orgCodeList,packCodeList);
                break;
            case 'check':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                check("noRequired", "查看", checkStatus.data[0]);
                break;
            case 'export':
                if ($("#kind").val() == 1) {
                    layer.msg("社保停办数据生成");
                } else if ($("#kind").val() == 2) {
                    layer.msg("公积金停办数据生成");
                }
                var url = ML.contextPath + "/customer/insurancePractice/societyInsuranceStopHandle/exportStopHandle?1=1";
                var paramData = JSON.stringify(serialize("searchForm"));
                if (paramData != null && paramData !== '') {
                    console.log(paramData);
                    url += "&paramData=" + paramData
                }
                window.location.href = url;
                break;
        }
    });

    var active = {
        reload: function () {
            table.reload('socialSecurityStopHandleId', {
                where: {
                    paramData: JSON.stringify(serialize("searchForm")),
                    // page: 1 //重新从第 1 页开始
                }
            });
        }
    };
    $('#btnQuery').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });


    /**/
    function stopHandle(oprType, title, selected,orgCodeList,packCodeList) {
        var url;
        var area;
        area = ['1000px', '600px'];
        url = '/customer/insurancePractice/societyInsuranceStopHandle/gotoStopHandle';
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + url
            , success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#insurancePracticeIdList").val(JSON.stringify(selected));
                body.find("#orgCodeList").val(JSON.stringify(orgCodeList));
                body.find("#packCodeList").val(JSON.stringify(packCodeList));
                reloadTable();
            },
            end: function () {
                reloadTable();
            }
        });
    }

    /*查看*/
    function check(oprType, title, data) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['80%', '70%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/customer/insurancePractice/societyInsuranceStopHandle/gotoCheckDetail'
            , success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#insurancePracticeId").val(data.id);
                reloadTable();
            },
            end: function(){
                reloadTable();
            }
        });
    }

    // $(document).ready(function () {
    //
    //     // initSelect();
    //     var info = [];
    //     $.ajax({
    //         type: "GET",
    //         url: ML.contextPath + "/customer/quotation/sellerList",
    //         //data: {roleId: $('#roleId').val()},
    //         dataType: 'json',
    //         success: function (data) {
    //             info = [];
    //             info = data.data;
    //             $.each(info, function (i, item) {
    //                 $("#sale").append($("<option/>").text(item.userName).attr("value", item.loginName));
    //             });
    //             form.render('select');
    //         },
    //         error: function (data) {
    //             layer.msg(data);
    //             console.log("error")
    //         }
    //     });
    // });



    $("#reset").click(
        function () {
            $(".reset").val("");
            clearDate();
        }
    );

    //查看个人订单
    function addView(title, optType, data) {
        var url = "";
        if (optType == 'query') {
            url = "/customer/personOrder/gotoQueryPersonOrderPage?orderNo=" + data.orderNo + "&optType=" + optType;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['80%', '90%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        })
    }


    //重载数据
    function reloadTable() {
        table.reload('socialSecurityStopHandleId', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }


    // function initSelect() {
    //     if ($("#kind").val() == 1) {
    //         $.ajax({
    //             url: ctx + "/sys/InsuranceGroup/getEmployeeManageSocialSecurityGroup",
    //             type: 'get',
    //             success: function (data) {
    //                 var optionStr = "";
    //                 layui.each(data, function (i, item) {
    //                     optionStr += "<option value='" + item.groupCode + "'>" + item.groupName + "</option>"
    //                 })
    //                 $("#accumulationFundGroupId").append(optionStr);
    //                 form.render('select');
    //             }
    //         })
    //     } else if ($("#kind").val() == 2) {
    //         $.ajax({
    //             url: ctx + "/sys/InsuranceGroup/getEmployeeManageAccumulationFundGroup",
    //             type: 'get',
    //             success: function (data) {
    //                 var optionStr = "";
    //                 layui.each(data, function (i, item) {
    //                     optionStr += "<option value='" + item.groupCode + "'>" + item.groupName + "</option>"
    //                 })
    //                 $("#accumulationFundGroupId").append(optionStr);
    //                 form.render('select');
    //             }
    //         })
    //     }
    //
    //     $.ajax({
    //         url: ctx + "/sys/org/getWelfareTransaction",
    //         type: 'get',
    //         success: function (data) {
    //             var optionStr = "";
    //             layui.each(data, function (i, item) {
    //                 optionStr += "<option value='" + item.orgCode + "'>" + item.orgName + "</option>"
    //             })
    //             $("#handleParty").append(optionStr);
    //             form.render('select');
    //         }
    //     })
    // }


    function clearDate() {
        //在此添加需要清空的对象
        let dateArray = [addDateS, addDateE,dimissionDateS, dimissionDateE, procTimeS, procTimeE];
        let max = {date: 31, hours: 0, minutes: 0, month: 11, seconds: 0, year: 2099};
        let min = {date: 1, hours: 0, minutes: 0, month: 0, seconds: 0, year: 1900};
        if (dateArray.length > 0) {
            dateArray.forEach(function (item) {
                item.config.min = min;
                item.config.max = max;
            })
        }
        $("#packCode").html('');
    }


    form.on('select(packCodeFilter)', function (data) {
        $.ajax({
            url: ML.contextPath + "/customer/insurancePractice/societyInsuranceHandle/getRatioCode?packCode=" + data.value,
            type: "GET",
            dataType: 'json',
            success: function (result) {
                if (result.code === 0) {
                    $.each(result.data, function (i, item){
                       $("#insuranceRatioCode").append($("<option/>").text(item.ratioName).attr("value", item.insuranceRatioCode));
                    });
                    form.render('select');
                }
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
            }
        })
    });

    // 办理人
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="userName" placeholder="姓名" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelectWithoutSearch.render({
        elem: '#handler2',
        checkedKey: 'userName',
        delFlg: ["handler2", "handler"],
        appd: appd,
        table: {
            url: ML.contextPath + '/sys/user/getAllUserForTableSelect',
            cols: [[
                {type: 'radio'}
                // , {field: 'id', title: 'ID', align: 'center'}
                , {field: 'userName', title: '姓名', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                $("#handler2").val(item.userName);
                $("#handler").val(item.loginName);
            });
            // 回填值
        }
    });


});

