layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'laypage'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        laypage = layui.laypage,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 代收代付表格数据源
    let collectionCostList = [];
    // 代收代付金额
    let collectionAmt = 0;

    const collectionGridTable = 'collectionGridTable';

    // 一次性表格数据源
    let disposableItemApprovalVoList = [];
    // 一次性金额
    let disposableItemAmt = 0;

    const disposableGridTable = 'disposableGridTable';

    // 服务费数据源
    let serviceCostList = [];
    // 服务费金额
    let serviceCostAmt = 0;

    const serviceChangeGridTable = 'serviceChangeGridTable';


    // 渲染服务费数据
    table.render({
        id: serviceChangeGridTable,
        elem: '#serviceChangeGridTable',
        page: false,
        data: serviceCostList,
        toolbar: false,
        defaultToolbar: [],
        limit: Number.MAX_VALUE, // 默认显示最大值
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            {
                field: 'productType', title: '产品类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PRODUCT_KIND", d.productType);
                }
            },
            {field: 'billMonth', title: '账单年月', width: '120', align: 'center'},
            {field: 'receivableMonth', title: '财务应收账单年月', width: '150', align: 'center'},
            {field: 'templetName', title: '客户帐套', width: '180', align: 'center'},
            {field: 'contractNo', title: '合同编号', width: '180', align: 'center'},
            {field: 'receiveAmt', title: '总金额', width: '100', align: 'center', totalRow: true},
            // {field: 'unTaxAmt', title: '金额', width: '100', align: 'center'},
            {field: 'receiveValTax', title: '增值税金额', width: '100', align: 'center'},
            {field: 'valTaxRate', title: '增值税税率', width: '100', align: 'center'},
            {field: 'number', title: '人数', width: '80', align: 'center'},
            {
                field: 'contractType', title: '大合同类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            }
        ]]
    })

    // 渲染代收代付表格
    table.render({
        id: collectionGridTable,
        elem: '#collectionGridTable',
        page: false,
        data: collectionCostList,
        toolbar: false,
        defaultToolbar: [],
        totalRow: true, // 开启合计行
        limit: Number.MAX_VALUE, // 默认显示最大值
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            {field: 'billMonth', title: '账单年月', width: '120', align: 'center', totalRowText: '合计'},
            {field: 'receivableMonth', title: '财务应收账单年月', width: '150', align: 'center'},
            {field: 'templetName', title: '客户帐套', width: '180', align: 'center'},
            {field: 'contractNo', title: '合同编号', width: '180', align: 'center'},
            {field: 'receiveAmt', title: '金额', width: '120', align: 'center', totalRow: true},
            // {field: 'indAmt', title: '个人金额', width: '120', align: 'center', totalRow: true},
            // {field: 'comAmt', title: '企业金额', width: '120', align: 'center', totalRow: true},
            {
                field: 'productType', title: '类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PRODUCT_KIND", d.productType);
                }
            },
            {
                field: 'contractType', title: '大合同类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            }
        ]]
    })


    // 渲染一次性收费表格
    table.render({
        id: disposableGridTable,
        elem: '#disposableGridTable',
        page: false,
        data: disposableItemApprovalVoList,
        toolbar: false,
        defaultToolbar: [], // 默认显示最大值
        limit: Number.MAX_VALUE,
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            {
                field: 'prodType',
                title: '产品类型',
                width: '150',
                align: 'center',
                fixed: 'left',
                templet: function (d) {
                    return ML.dictFormatter("DISPOSAL_TYPE", d.prodType);
                }
            },
            {
                field: 'prodKind', title: '产品', width: '150', align: 'center', fixed: 'left', templet: function (d) {
                    return ML.getDictSubByParentCodeAndSelfCode(d.prodType, "DISPOSAL_TYPE", d.prodKind);
                }
            },
            {field: 'amount', title: '总金额', width: '150', align: 'center'},
            {field: 'taxFreeAmt', title: '金额', width: '150', align: 'center'},
            {field: 'tax', title: '增值税金额', width: '150', align: 'center'},
            {
                field: 'disposableTaxRatio', title: '增值税税率', width: '150', align: 'center', templet: function (d) {
                    if (ML.isEmpty(d.disposableTaxRatio))
                        return "0%";
                    else
                        return d.disposableTaxRatio + "%"
                }
            },
            {field: 'peopleNum', title: '人数', width: '150', align: 'center'},
            {field: 'remark', title: '备注', width: '150', align: 'center'}
        ]]
    })

    // 页面初始化函数
    $(document).ready(function () {
        getTableData();
    })


    // 获取表格数据
    function getTableData() {
        let invoiceId = $("#invoiceId").val();
        if (!invoiceId) {
            layer.msg("发票信息为空,请检查");
            return false;
        }

        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/invoice/getConfirmRedInvoiceData?invoiceId=" + invoiceId,
            dataType: 'json',
            success: function (data) {
                layer.closeAll('loading');
                if (data.code == 0) {
                    console.log(data)
                    serviceCostList = data.data.serviceCostList;
                    collectionCostList = data.data.collectionCostList;
                    disposableItemApprovalVoList = data.data.disposableItemApprovalVoList;
                    resetServiceData();
                    resetColData();
                    resetDisItemData();
                } else {
                    return layer.msg(data.msg);
                }
            },
            error: function (data) {
                layer.closeAll('loading');
                layer.msg(data);
                console.log("error")
            }
        });
    }

    function reload(tableId, data) {
        table.reload(tableId, {data: data});
    }


    /**
     * 汇总服务费总金额
     */
    function resetServiceData() {
        if (serviceCostList.length > 0) {
            serviceCostAmt = 0;
            serviceCostList.forEach(item => {
                serviceCostAmt = serviceCostAmt + Number(item.receiveAmt);
            });
            reload(serviceChangeGridTable, serviceCostList);
        }
    }

    /**
     * 汇总代收代付总金额
     */
    function resetColData() {
        if (collectionCostList.length > 0) {
            collectionAmt = 0;
            collectionCostList.forEach(item => {
                collectionAmt = collectionAmt + Number(item.receiveAmt);
            });
            reload(collectionGridTable, collectionCostList);
        }
    }

    /**
     * 汇总一次性收费总金额
     */
    function resetDisItemData() {
        if (disposableItemApprovalVoList.length > 0) {
            disposableItemAmt = 0;
            disposableItemApprovalVoList.forEach(item => {
                disposableItemAmt = disposableItemAmt + Number(item.amount);
            });
            reload(disposableGridTable, disposableItemApprovalVoList);
        }
    }


    // 监听保存按钮
    $("#invoiceSubmit").on('click', function () {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/invoice/confirmRedInvoice?invoiceId=" + $("#invoiceId").val(),
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    layer.msg(data.msg);
                } else {
                    return layer.msg(data.msg);
                }
            }
        });
        $("#invoiceSubmit").attr("disabled", true);
        // ajax get请求

    });

    // 取消按钮
    $(document).on('click', '#invoiceCancel', function () {
        layer.closeAll('iframe');
    });
})
