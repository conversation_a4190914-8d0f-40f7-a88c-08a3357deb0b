//福利办理方
var CN = CN || {};
CN.data = CN.data || {};// 用于存放临时的数据或者对象

var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer','tableSelect'], function () {
    var $ = layui.$, form = layui.form,tableSelect = layui.tableSelect;;

    let orgCodes = [];
    //用于过滤是否单利户
    let packArr = [];
    //用于过滤是否单利户
    let packFilterCust = [];
    let orgCodeAndPackNameListMap = {}
    let orgCodeArray = [];
// 页面 加载事件
    $(document).ready(function () {
        getCityAndOrgCodeMapByLoginName()
    });

    function getCityAndOrgCodeMapByLoginName() {
        let statusStr = $('#statusStr').val();
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/insurancePractice/societyInsurance/getOrgCodeAndPackName?statusStr="+statusStr,
            dataType: 'json',
            success: function (data) {
                orgCodeAndPackNameListMap = data.orgCodeAndPackNameListMap;
                 orgCodeArray = data.orgVoList;
                if (ML.isNotEmpty(orgCodeArray)) {
                    initOrgCode(orgCodeArray);
                }
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    function initOrgCode(orgCodeArray) {
        $("#orgCode").append($("<option/>"));
        $.each(orgCodeArray, function (i, item) {
            $("#orgCode").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
            orgCodes.push(item.orgCode)
        });
        // initPackName(orgCodeArray[0].orgCode);
        form.render("select")
    }




    form.on("select(orgCodeFilter)", function (data) {
        initPackName(data.value);
    });


    function renderPack(packs) {
        let singleFlag = $('#singleFlag').val();
        $.each(packs, function (i, item) {
            let name = item.packName;
            if(item.status == 2){
                name = name+"（已暂停）";
            }
            if(ML.isNotEmpty(singleFlag)){
                if(item.singleFlag == singleFlag){
                    $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
                }
            }else {
                $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
            }
        });
        form.render("select")
    }

    function initPackName(value) {
        $("#packCode").empty();
        clearCustomerAndSingleFlag();
        for (let orgCode in orgCodeAndPackNameListMap) {
            if (value === orgCode) {
                dataGrop = orgCodeAndPackNameListMap[value];
                packArr = dataGrop;
                packFilterCust = dataGrop;
                $("#packCode").append($("<option/>"));
                renderPack(dataGrop);
            }
        }
        $("#packCode").val('');
        form.render("select")
    }

    form.on("select(singleFlagFilter)",function(data) {
        $("#packCode").find("option:selected").text("");
        $("#packCode").empty();
        let targetPack = packArr.filter(pack => pack.singleFlag+"" === data.value);
        renderPack(targetPack);
        $("#custName").removeAttr("disabled");

    })

    //选择福利包如果是单立户带出客户
    form.on("select(packCodeFilter)", function (data) {
        if (ML.isEmpty(data.value)) {
            clearCustomerAndSingleFlag();
            form.render('select');
            return;
        }
        let singleFlag;
        let findPack = dataGrop.filter(item => item.packCode === data.value);
        if (findPack[0] && findPack[0].singleFlag === 2) {
            $('#custName').val(findPack[0].custName);
            $('#custId').val(findPack[0].custId);
            singleFlag = 2;
            if (ML.isNotEmpty(findPack[0].custId)){
                document.getElementById("sinAccName").options.length = 0
                $.ajax({
                    type: "GET",
                    url: ML.contextPath + "/prac/singleAccount/getSingleAccountNameByCustId?custId="+findPack[0].custId,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        $.each(data.data, function (i, item) {
                            $("#sinAccName").append($("<option/>").text(item.name).attr("value", item.id));
                        });

                    },
                    error: function (data) {
                        console.log("error")
                    }
                });
            }else {
                document.getElementById("sinAccName").options.length = 1;
            }
            form.render('select');
        }
        $('#singleFlag').find("option[value='"+singleFlag+"']").prop('selected',true);//选中value="2"的option
        form.render('select');
    })

    function clearCustomerAndSingleFlag() {
        // $('#singleFlag').val("");
        $("#custName").val("");
        $("#custId").val("");
        document.getElementById("sinAccName").options.length = 0;
    }

    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ctx + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            $("#packCode").empty();
            let singleFlag = $('#singleFlag').val();
            if (singleFlag == "2"){
                let packs = packFilterCust.filter(pack => pack.custId === id);
                renderPack(packs);
            }else {
                let packs = packArr.filter(pack => pack.singleFlag === 1);
                renderPack(packs);
            }
            elem.val(NEWJSON.join(","));
            if (ML.isNotEmpty(id)){
                document.getElementById("sinAccName").options.length = 0
                $("#sinAccName").append("<option value=''></option>");
                $.ajax({
                    type: "GET",
                    url: ML.contextPath + "/prac/singleAccount/getSingleAccountNameByCustId?custId="+id,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        $.each(data.data, function (i, item) {
                            $("#sinAccName").append($("<option/>").text(item.name).attr("value", item.id));
                        });

                    },
                    error: function (data) {
                        console.log("error")
                    }
                });
            }else {
                let selectElement = document.getElementById("sinAccName");
                selectElement.options.length = 1;
            }
            form.render('select');
            $("#custId").val(id);
        }
    });

    CN.getCompanyName = function (data){
        for (var i in orgCodeArray) {
            var obj = orgCodeArray[i];
            if (data == obj.orgCode) {
                return obj.orgName;
            }
        }
        return ""
    }

    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="福利办理方编号/名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#orgName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ctx + '/customer/insurancePractice/societyInsurance/getOrgCodeByLoginName',
            cols: [[
                {type: 'radio'}
                , {field: 'orgCode', title: '福利办理方编号', align: 'center'}
                , {field: 'orgName', title: '福利办理方名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            let orgCode;
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName);
                orgCode = item.orgCode;
                id = item.id;
            });
            elem.val(NEWJSON.join(","));
            $("#orgCode").val(orgCode);
            initPackName(orgCode);
        }
    });



});