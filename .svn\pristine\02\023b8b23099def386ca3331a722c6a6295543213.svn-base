<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input{
            padding-right: 30px;!important;
        }
        .layui-table-cell{
            padding: 0px;
        }
        #inp{
            padding-top:15px;

        }

    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" title="供应商名称" style="font-weight:800"><i style="color: red"></i>&nbsp;供应商名称</label>
                <div class="layui-input-inline">
                    <select name="supplierId" id="supplierId" lay-verType="tips" lay-search
                            lay-Filter="supplierFilter" autocomplete="off">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="参保城市">参保城市：</label>
                <div class="layui-input-inline">
                    <select name="cityCode" id="cityCode" lay-filter="cityCodeFilter" lay-search
                            AREA_TYPE
                            lay-verify="" lay-verType="tips" autocomplete="off">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="产品类型">产品类型：</label>
                <div class="layui-input-inline">
                    <select name="prodCode" id="prodCode" lay-filter="prodCode" lay-search DICT_TYPE="PRODUCT_IND_TYPE"
                            lay-verify="" lay-verType="tips" autocomplete="off">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" title="类型" style="font-weight:800">类型</label>
                <div class="layui-input-block">
<%--                  disabled  readonly=""--%>
                    <select name="syncType" id="syncType"  DICT_TYPE="SUPPLIER_SYNC_TYPE"
                            lay-verify="">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div style="float: right;margin-right: 10%; margin-top: 30px">
                <button type="button" class="layui-btn" id="confirm">确认</button>
                <button type="button" class="layui-btn" id="cancel">关闭</button>
            </div>
        </div>
    </form>
</blockquote>

<table class="layui-hide" id="billTempletGrid" lay-filter="billTempletFilter" ></table>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/supplier/practice/syncCfg/addSyncCfgPage.js?v=${publishVersion}"></script>
</body>
</html>
