layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var checkCustName = false;

    // 监听保存按钮
    form.on("submit(save)", function (data) {
        if (checkCustName) {
            layer.msg("用户名已重复!", {icon: 5});
        } else {
            if (ValidateValue(data.field.custName)) {
                layer.msg("客户名称非法");
            }else {
                ML.layuiButtonDisabled($('#save'));// 禁用
                saveForm(data);
            }
        }
        return false;
    });

    function ValidateValue(str) {
        const IllegalString = "[`~!#$^&*()=|{}':;',\\[\\].<>/?~！#￥……&*——|{}【】@%+《》‘；：”“'。，、？]‘'";
        for (let i = 0; i < str.length; i++) {
            if (IllegalString.indexOf(str[i]) !== -1) {
                return true;
            }
        }
        return false;
    }

    // 关闭弹窗
    $(document).on('click', '#cancelBtn', function () {
        layer.closeAll('iframe');
    });

    function saveForm(data) {
        $.ajax({
            url: ML.contextPath + "/customer/customer/save",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                //console.log(window.parent[2].document.getElementById("btnQuery"));
                //window.parent[2].document.getElementById("btnQuery").click();

                layer.msg(result.msg);
                layer.closeAll('iframe');

            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });
    }

    function classShow() {
        $(".industryAdd").removeClass("layui-hide");
        $(".industryAdd").addClass("layui-show");
    }

    function classHide() {
        $(".industryAdd").removeClass("layui-show");
        $(".industryAdd").addClass("layui-hide");
    }

    $("#custName").on('change', function () {
        var custName = $("#custName").val();
        $.ajax({
            url: ML.contextPath + "/customer/customer/checkCustomerName?custName=" + custName.trim(),
            type: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.code == 0) {
                    if (result.data) {
                        checkCustName = false;
                    } else {
                        checkCustName = true;
                        $("#custName").val('');
                        layer.msg("用户名已重复!", {icon: 5});
                    }
                } else {
                    layer.msg(result.msg, {icon: 5});
                }
            }
        });
    })

    // 页面加载函数
    $(document).ready(function () {
        var type = $("#type").val();
        setTimeout(function () {
            if (type == "update") {
                $("#custName").attr("disabled", true);
                $("#industryType").attr("disabled", true);
                if ($("#creator").val() != window.top['currentLoginName']) {
                    $("#bizType").attr("disabled", true);
                }
                $("#industryType").val($("#editIndustryType").val())
                //$("#corpKind").val($("#editCorpKind").val())
                $("#cityCode").val($("#editCityCode").val())
                classShow();
                form.render('select');
            }
        }, 100);
        if (type != "update") {
            classHide();
        }
        form.render('select');
    })
})