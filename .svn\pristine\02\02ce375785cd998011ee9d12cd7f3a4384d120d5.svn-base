package com.reon.hr.sp.customer.service.cus;

import com.reon.hr.api.customer.vo.CommInsuQuotationItemVo;
import com.reon.hr.api.customer.vo.QuotationItemVo;
import com.reon.hr.sp.customer.entity.cus.CommInsuQuotationItem;

import java.util.List;

public interface CommInsuQuotationItemService {
	void insert(List<CommInsuQuotationItem> commInsuQuotationItems);

	List<CommInsuQuotationItemVo> selectByQuoteNo(String quoteNo);

	void deleteByQuoteNo(String quoteNo);

	List<CommInsuQuotationItemVo> selectSolutionNoReferenced(String soluNo);

	List<CommInsuQuotationItemVo> getQuotationItemByQuoteNo(String quoteNo);

	List<QuotationItemVo> getSoluNoByQuoteNo(String quoteNo);
}
