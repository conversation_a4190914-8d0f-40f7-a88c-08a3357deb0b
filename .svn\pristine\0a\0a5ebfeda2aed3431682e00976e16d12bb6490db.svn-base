package com.reon.hr.modules.sys.controller;

import com.reon.hr.api.common.ResponseModel;
import com.reon.hr.api.dubbo.service.rpc.CaptchaService;
import com.reon.hr.api.dubbo.service.rpc.sys.*;
import com.reon.hr.api.vo.CaptchaVO;
import com.reon.hr.api.vo.JsonResult;
import com.reon.hr.api.vo.sys.*;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.core.utils.IpUtil;
import com.reon.hr.core.utils.VerifyCodeUtil;
import com.reon.hr.modules.common.BaseController;
import com.reon.hr.modules.common.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Controller
public class LoginController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final static int NO_LOGIN_SESSION_LIVE_TIME_SECONDS = (int) TimeUnit.MINUTES.toSeconds(5);//未登录的session超时时间

    @Resource(name = "userDubboService")
    private IUserWrapperService dsUserService;
    @Resource(name = "sysLogDubboService")
    private IUserOpLogWrapperService userOpLogWrapperService;
    @Resource(name = "positionDubboService")
    private IPositionWrapperService positionWrapperService;
    @Resource(name = "orgDubboService")
    private IOrgnizationResourceWrapperService orgnizationWrapperService;
    @Autowired
    private CaptchaService captchaService;
    @Resource
    private RedisTemplate redisTemplate;


    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @SuppressWarnings("unchecked")
    public String login(@RequestParam("acctName") String loginName, String loginPwd, String verifyCode, HttpSession session, HttpServletRequest request) throws Exception {
        String lang = "zh_CN";
        try {



            String sessionCode = (String) session.getAttribute(Constants.SSESSION_VERIFY_CODE);
            if (StringUtils.isEmpty(loginName)) {
                request.setAttribute("errMsg", "用户名或密码为空");
                return "/login";
            } else if (StringUtils.isEmpty(loginPwd)) {
                request.setAttribute("errMsg", "用户名或密码为空");
                return "/login";
            }
            //效验验证码
            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setCaptchaVerification(verifyCode);
            ResponseModel response = captchaService.verification(captchaVO);
            if(!response.isSuccess()){
                //验证码校验失败，返回信息告诉前端
                //repCode  0000  无异常，代表成功
                //repCode  9999  服务器内部异常
                //repCode  0011  参数不能为空
                //repCode  6110  验证码已失效，请重新获取
                //repCode  6111  验证失败
                //repCode  6112  获取验证码失败,请联系管理员
                request.setAttribute("errMsg", response.getRepMsg ());
				return "/login";
            }
            String ip = IpUtil.getIp(getRequest());
            JsonResult<Object> result = dsUserService.doUserLogin(loginName, loginPwd, lang, ip);
            if (!result.isSuccess()) {
                throw new BusinessException(result.getMsg());
            }
            Map<String, Object> data = (Map<String, Object>) result.getData();
            CommonUserVo userVO = (CommonUserVo) data.get("userVo");
            Map<String, String> authResourcesMap = (Map<String, String>) data.get("authResourcesMap");
            Map<String, String> editResourcesMap = (Map<String, String>) data.get("editResourcesMap");
            List<ResourceVO> authResourcesList = (List<ResourceVO>) data.get("authResourcesList");
            List<OrgPositionDto> userVirtualPosList = (List<OrgPositionDto>)data.get("userVirtualPosList");
            List<OrgPositionDto> userOrgPositionList = (List<OrgPositionDto>)data.get("userOrgPositionList");
            List<OrgPositionDto> originUserOrgPositionList = (List<OrgPositionDto>)data.get("originUserOrgPositionList");
            List<Integer> userCityCodeList = (List<Integer>)data.get("userCityCodeList");
            // session
            loginSession(userVO, authResourcesMap, editResourcesMap, authResourcesList,userCityCodeList,userOrgPositionList,userVirtualPosList,originUserOrgPositionList);
            addThemeCookie(null);
            //return "redirect:/user/main";// 实际上:字符串是views名称对应的jsp，而forward:、redirect:是重定向到controller-main
            return "redirect:/index";
        } catch (BusinessException e) {
            request.setAttribute("errMsg", e.getMessage());
        } catch (Exception e) {
            logger.error("登陆模块异常", e);
            request.setAttribute("errMsg", "服务器繁忙，请稍后再试！");
        }
        if (!StringUtils.isEmpty(loginName)) {
            request.setAttribute("loginName", loginName);
        }
        return "/login";
    }

    /**
     * get访问登录页
     */
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public String login() {
        //已登录的情况自动转到主页
        try {
            CommonUserVo userVO = getSessionUser();
            if (userVO != null) {
                return "redirect:/index";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //未登录状态，转到登录页面
        return "login";
    }

    /**
     * get访问登录页
     */
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public String index() {
        //已登录的情况自动转到主页
        try {
            CommonUserVo userVO = getSessionUser();
            if (userVO != null) {
                return "/index";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //未登录状态，转到登录页面
        return "redirect:/login";
    }

    @RequestMapping("/logout")
    public String logout() {
        try {

            HttpSession session = getSession();
            if (session != null) {
                CommonUserVo userVO = (CommonUserVo) session.getAttribute(Constants.SESSION_USER_KEY);
                if (userVO != null) {
                    logger.info("用户主动退出：[userName={}, sid={}]", userVO.getLoginName(), session.getId());
                    session.invalidate();
                    // 记录log
                    String ip = IpUtil.getIp(getRequest());
                    userOpLogWrapperService.doAddOpLog(userVO.getId(), userVO.getUserName(), new Date(), ip, "/user/logout", null);
                }
                // session.removeAttribute(Constants.SESSION_USER_KEY);
                // session.removeAttribute(Constants.SESSION_LIST_RESOURCE_KEY);
                // session.removeAttribute(Constants.SESSION_MAP_RESOURCE_KEY);
                // session.removeAttribute(Constants.SESSION_IS_SA);
                // session.removeAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:/login";
    }


    @RequestMapping("/main")
    public String gotoMain() {
        return "main";
    }

    @RequestMapping(value = "/denied")
    public String denied() {
        return "denied";
    }

    @RequestMapping(value = "/error")
    public String error() {
        return "error";
    }

    @ResponseBody
    @RequestMapping("/verifyCode")
    public void verifyCode(HttpServletRequest req, HttpServletResponse resp) {
        try {
            String code = VerifyCodeUtil.generateVerifyCode(4);
            // 将四位数字的验证码保存到Session中。
            HttpSession session = getSession()==null?req.getSession():getSession();
            session.setAttribute(Constants.SSESSION_VERIFY_CODE, code);
            session.setMaxInactiveInterval(NO_LOGIN_SESSION_LIVE_TIME_SECONDS);//当前会话的有效期（单位:秒）
            // 禁止图像缓存。
            resp.setHeader("Pragma", "no-cache");
            resp.setHeader("Cache-Control", "no-cache");
            resp.setDateHeader("Expires", 0);
            resp.setContentType("image/jpeg");
            ServletOutputStream sos = resp.getOutputStream();
            // 调用工具类生成的验证码和验证码图片
            VerifyCodeUtil.outputVerifyImage(90, 30, sos, code);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
