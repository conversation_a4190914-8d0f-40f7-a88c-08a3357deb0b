<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.BillTempletMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.cus.BillTemplet">
    </resultMap>
    <sql id="templetMap">
        b.id
        ,b.cust_id,b.templet_no,b.templet_type,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,
	b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
	b.row_flag,b.view_items,b.sub_types,b.contract_no
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select
        b.id, b.cust_id,b.templet_no,b.templet_type, b.templet_name,b.contract_no, b.payment_id, b.gen_bill,
        b.receiver_id, b.lock_date,
        b.salary_date, b.insurancel_flag, b.reserve_flag,
        b.salary_flag,b.ind_tax_flag, b.row_flag, b.view_items, b.creator, b.create_time, b.updater, b.update_time,
        b.del_flag,
        c.cust_no,c.cust_name,ci.customer_invoice_no as paymentNo,ci.title as
        paymentName,b.templet_type,con.contract_name,con.contract_type,b.sub_types,
        b.gen_date,b.pay_date,b.deadline,b.income_month_type,con.commissioner as prjCs,con.salary_commissioner as salary_commissioner,
        b.pay_method,b.holiday_method
        FROM
        `reon-customerdb`.bill_templet b
        LEFT JOIN customer c ON b.cust_id = c.id
        LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        LEFT JOIN contract con on b.contract_no = con.contract_no
        LEFT JOIN contract_content_info cci on con.contract_no = cci.contract_no
        WHERE b.id = #{id,jdbcType=BIGINT}
        <if test="contractNo !=null and contractNo !=''">
            and con.contract_no = #{contractNo}
        </if>
        and b.del_flag='N' ORDER BY b.create_time desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bill_templet
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.reon.hr.sp.customer.entity.cus.BillTemplet">
        insert into bill_templet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            cust_id,
            templet_no,
            templet_type,
            contract_no,
            sub_types,
            <if test="templetName != null">
                templet_name,
            </if>
            <if test="paymentId != null">
                payment_id,
            </if>
            <if test="payMethod != null and payMethod != ''">
                pay_method,
            </if>
            <if test="holidayMethod != null and holidayMethod != ''">
                holiday_method,
            </if>
            <if test="genBill != null">
                gen_bill,
            </if>
            <if test="receiverId != null">
                receiver_id,
            </if>
            <if test="genDate != null">
                gen_date,
            </if>
            <if test="lockDate != null">
                lock_date,
            </if>
            <if test="deadline != null">
                deadline,
            </if>
            <if test="payDate != null">
                pay_date,
            </if>
            <if test="salaryDate != null">
                salary_date,
            </if>
            <if test="incomeMonthType != null">
                income_month_type,
            </if>
            <if test="insurancelFlag != null">
                insurancel_flag,
            </if>
            <if test="reserveFlag != null">
                reserve_flag,
            </if>
            <if test="salaryFlag != null">
                salary_flag,
            </if>
            <if test="indTaxFlag!=null">
                ind_tax_flag,
            </if>
            <if test="rowFlag != null">
                row_flag,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            view_items
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{custId,jdbcType=BIGINT},
            #{templetNo,jdbcType=VARCHAR},
            #{templetType,jdbcType=INTEGER},
            #{contractNo,jdbcType=VARCHAR},
            #{subTypes,jdbcType=INTEGER},
            <if test="templetName != null">
                #{templetName,jdbcType=VARCHAR},
            </if>
            <if test="paymentId != null">
                #{paymentId,jdbcType=BIGINT},
            </if>
            <if test="payMethod != null and payMethod != ''">
                #{payMethod},
            </if>
            <if test="holidayMethod != null and holidayMethod != ''">
                #{holidayMethod},
            </if>
            <if test="genBill != null">
                #{genBill,jdbcType=VARCHAR},
            </if>
            <if test="receiverId != null">
                #{receiverId,jdbcType=VARCHAR},
            </if>
            <if test="genDate != null">
                #{genDate,jdbcType=INTEGER},
            </if>
            <if test="lockDate != null">
                #{lockDate,jdbcType=INTEGER},
            </if>
            <if test="deadline != null">
                #{deadline,jdbcType=INTEGER},
            </if>
            <if test="payDate != null">
                #{payDate,jdbcType=INTEGER},
            </if>
            <if test="salaryDate != null">
                #{salaryDate,jdbcType=INTEGER},
            </if>
            <if test="incomeMonthType != null">
                #{incomeMonthType,jdbcType=INTEGER},
            </if>
            <if test="insurancelFlag != null">
                #{insurancelFlag,jdbcType=INTEGER},
            </if>
            <if test="reserveFlag != null">
                #{reserveFlag,jdbcType=INTEGER},
            </if>
            <if test="salaryFlag != null">
                #{salaryFlag,jdbcType=INTEGER},
            </if>
            <if test="indTaxFlag!=null">
                #{indTaxFlag,jdbcType=INTEGER},
            </if>
            <if test="rowFlag != null">
                #{rowFlag,jdbcType=INTEGER},
            </if>

            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            "1,2,3,4,5,6,7,8"
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.customer.entity.cus.BillTemplet">
        update bill_templet
        <set>
            <if test="custId != null">
                cust_id = #{custId,jdbcType=BIGINT},
            </if>
            <if test="templetNo != null">
                templet_no = #{templetNo,jdbcType=VARCHAR},
            </if>
            <if test="templetType!=null">
                templet_type=#{templetType,jdbcType=INTEGER},
            </if>
            <if test="templetName != null">
                templet_name = #{templetName,jdbcType=VARCHAR},
            </if>
            <if test="paymentId != null">
                payment_id = #{paymentId,jdbcType=BIGINT},
            </if>
            <if test="genBill != null">
                gen_bill = #{genBill,jdbcType=VARCHAR},
            </if>
            <if test="receiverId != null">
                receiver_id = #{receiverId,jdbcType=VARCHAR},
            </if>
            <if test="genDate != null">
                gen_date = #{genDate,jdbcType=INTEGER},
            </if>
            <if test="lockDate != null">
                lock_date = #{lockDate,jdbcType=INTEGER},
            </if>
            <if test="deadline != null">
                deadline = #{deadline,jdbcType=INTEGER},
            </if>
            <if test="payDate != null">
                pay_date = #{payDate,jdbcType=INTEGER},
            </if>
            <if test="salaryDate != null">
                salary_date = #{salaryDate,jdbcType=INTEGER},
            </if>
            <if test="incomeMonthType != null">
                income_month_type = #{incomeMonthType,jdbcType=INTEGER},
            </if>
            <if test="insurancelFlag != null">
                insurancel_flag = #{insurancelFlag,jdbcType=INTEGER},
            </if>
            <if test="reserveFlag != null">
                reserve_flag = #{reserveFlag,jdbcType=INTEGER},
            </if>
            <if test="salaryFlag != null">
                salary_flag = #{salaryFlag,jdbcType=INTEGER},
            </if>
            <if test="indTaxFlag!=null">
                ind_tax_flag=#{indTaxFlag,jdbcType=INTEGER},
            </if>
            <if test="rowFlag != null">
                row_flag = #{rowFlag,jdbcType=INTEGER},
            </if>
            <if test="viewItems != null">
                view_items = #{viewItems,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="getListPage" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT DISTINCT b.id,
        b.cust_id,
        b.templet_no,
        b.templet_type,
        b.templet_name,
        b.contract_no,
        b.payment_id,
        b.gen_bill,
        b.receiver_id,
        b.gen_date,
        b.lock_date,
        b.deadline,
        b.pay_date,
        b.salary_date,
        b.income_month_type,
        b.insurancel_flag,
        b.reserve_flag,
        b.salary_flag,
        b.ind_tax_flag,
        b.row_flag,
        b.view_items,
        b.creator,
        b.create_time,
        b.updater,
        b.update_time,
        b.del_flag,
        c.cust_no,
        c.cust_name,
        ci.customer_invoice_no AS paymentNo,
        ci.title AS paymentName,
        con.contract_name,
        con.commissioner
        FROM `reon-customerdb`.bill_templet b
        LEFT JOIN `reon-customerdb`.customer_invoice ci ON b.payment_id = ci.id
        LEFT JOIN `reon-customerdb`.contract con ON b.contract_no = con.contract_no
        LEFT JOIN `reon-customerdb`.customer c ON con.cust_id = c.id
        WHERE b.del_flag = 'N'
        and con.dispatch_flag = 1
        <if test="userOrgPositionDtoList != null and userOrgPositionDtoList.size > 0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                     close=")">
                (

                ( con.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                ( con.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.salary_comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                )
                <if test="userOrgPositionDto.loginName != null">
                    and (
                    ( con.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                    ( con.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})

                    )
                </if>
            </foreach>
        </if>
        <if test="contractNo!=null and contractNo!=''">
            and con.contract_no=#{contractNo}
        </if>
        <if test="contractName !=null and contractName!=''">
            and con.contract_name like concat('%',#{contractName},'%')
        </if>
        <if test="templetNo!=null and templetNo!=''">
            and b.templet_no like concat('%',#{templetNo},'%')
        </if>
        <if test="templetName!=null and templetName!='' ">
            and b.templet_name like concat('%',#{templetName},'%')
        </if>
        <if test="paymentName!=null and paymentName!=''">
            and ci.title like concat('%',#{paymentName},'%')
        </if>
        <if test="paymentNo!=null and paymentNo!=''">
            AND ci.customer_invoice_no like concat('%',#{paymentNo},'%')
        </if>
        ORDER BY
        b.create_time DESC
    </select>
    <select id="getListBillTemplet" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT DISTINCT b.id,
        b.cust_id,
        b.templet_no,
        b.templet_name,
        b.contract_no,
        con.contract_name,
        b.payment_id,
        b.gen_bill,
        b.receiver_id,
        b.lock_date,
        b.salary_date,
        b.templet_type,
        b.insurancel_flag,
        b.reserve_flag,
        b.salary_flag,
        b.ind_tax_flag,
        b.row_flag,
        b.view_items,
        b.creator,
        b.create_time,
        b.updater,
        b.update_time,
        b.del_flag,
        c.cust_no,
        c.cust_name,
        ci.customer_invoice_no AS paymentNo,
        ci.title AS paymentName,
        con.commissioner AS prjCs,
        con.salary_commissioner,
        b.gen_date as genDate,b.pay_date as payDate,con.add_sub_point as deadline,con.receive_freq as
        incomeMonthType
        FROM `reon-customerdb`.bill_templet b
        LEFT JOIN `reon-customerdb`.customer_invoice ci ON b.payment_id = ci.id
        LEFT JOIN `reon-customerdb`.contract con on b.contract_no = con.contract_no
        LEFT JOIN `reon-customerdb`.customer c ON con.cust_id = c.id
        WHERE b.del_flag = 'N'
        AND con.dispatch_flag = 1
        <if test="userOrgPositionDtoList != null and userOrgPositionDtoList.size > 0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                     close=")">
                (
                ( con.seller_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.seller_org like
                concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                or
                ( con.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                ( con.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.salary_comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                )
                <if test="userOrgPositionDto.loginName != null">
                    and (
                    con.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}
                    or
                    b.creator = #{userOrgPositionDto.loginName,jdbcType=VARCHAR} or
                    con.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}
                    )
                </if>
            </foreach>
        </if>
        order by b.create_time desc
    </select>
    <update id="deleteById">
        update bill_templet
        set del_flag='Y',
            updater=#{updater}
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey">
        update bill_templet
        <set>
            <if test="templetNo != null">
                templet_no = #{templetNo,jdbcType=VARCHAR},
            </if>
            <if test="templetType!=null">
                templet_type=#{templetType,jdbcType=INTEGER},
            </if>
            <if test="templetName != null">
                templet_name = #{templetName,jdbcType=VARCHAR},
            </if>
            <if test="paymentId != null">
                payment_id = #{paymentId,jdbcType=BIGINT},
            </if>
            <if test="payMethod != null and payMethod != ''">
                pay_method = #{payMethod},
            </if>
            <if test="holidayMethod != null and holidayMethod != ''">
                holiday_method = #{holidayMethod},
            </if>
            <if test="genBill != null">
                gen_bill = #{genBill,jdbcType=VARCHAR},
            </if>
            <if test="receiverId != null">
                receiver_id = #{receiverId,jdbcType=VARCHAR},
            </if>
            <if test="genDate != null">
                gen_date = #{genDate,jdbcType=INTEGER},
            </if>
            <if test="lockDate != null">
                lock_date = #{lockDate,jdbcType=INTEGER},
            </if>
            <if test="deadline != null">
                deadline = #{deadline,jdbcType=INTEGER},
            </if>
            <if test="payDate != null">
                pay_date = #{payDate,jdbcType=INTEGER},
            </if>
            salary_date = #{salaryDate,jdbcType=INTEGER},
            income_month_type = #{incomeMonthType,jdbcType=INTEGER},
            <if test="insurancelFlag != null">
                insurancel_flag = #{insurancelFlag,jdbcType=INTEGER},
            </if>
            <if test="reserveFlag != null">
                reserve_flag = #{reserveFlag,jdbcType=INTEGER},
            </if>
            salary_flag = #{salaryFlag,jdbcType=INTEGER},
            ind_tax_flag=#{indTaxFlag,jdbcType=INTEGER},
            row_flag = #{rowFlag,jdbcType=INTEGER},
            <if test="viewItems != null">
                view_items = #{viewItems,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findBillTempletList" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT
        b.id,
        b.cust_id,
        b.templet_no,
        b.templet_name,
        b.payment_id,
        b.gen_bill,
        b.receiver_id,
        b.gen_date,
        b.lock_date,
        b.deadline,
        b.pay_date,
        b.salary_date,
        b.income_month_type,
        b.insurancel_flag,
        b.reserve_flag,
        b.salary_flag,
        b.ind_tax_flag,
        b.row_flag,
        b.view_items,
        b.creator,
        b.create_time,
        b.updater,
        b.update_time,
        b.del_flag
        FROM
        bill_templet b
        WHERE
        b.del_flag = 'N'
        and b.templet_type = #{templetType}
        <if test="contractNo!=null and contractNo!=''">
            and b.contract_no= #{contractNo}
        </if>
        ORDER BY
        b.create_time desc
    </select>
    <select id="getListByCustIdAndTempletId" parameterType="java.lang.Long"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select DISTINCT
        b.id,b.cust_id,c.cust_name,ci.corp_kind as
        corpKind,b.templet_no,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.lock_date,
        b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
        b.row_flag,b.view_items, ci.title as paymentName,b.templet_type,con.bill_date as genDate
        FROM
        bill_templet b
        LEFT JOIN contract con on b.contract_no = con.contract_no
        LEFT JOIN customer c ON con.cust_id = c.id
        LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        WHERE b.del_flag = 'N' and b.cust_id=#{custId}
        <if test="billTempletId!=null">
            and b.id=#{billTempletId}
        </if>
        <if test="templetType!=null and templetType !=''">
            and b.templet_type =#{templetType}
        </if>
        <if test="contractNo!=null and contractNo !=''">
            and b.contract_no =#{contractNo}
        </if>
        order by b.create_time desc
    </select>
    <select id="getOneByTempletNo"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT
        <include refid="templetMap"/>
        FROM bill_templet b
        WHERE b.templet_no=#{templetNo} and b.del_flag = 'N'
        <if test="contractNo !=null and contractNo !=''">
            and b.contract_no=#{contractNo}
        </if>
    </select>

    <select id="searchByGenDate" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT bt.id, bt.gen_date, bt.cust_id
        FROM bill_templet bt
        WHERE bt.del_flag = 'N'
          AND bt.gen_date = DAY (curdate())
          and bt.templet_type = 1
    </select>

    <select id="searchCommBillByGenDate" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT bt.id, bt.gen_date, bt.cust_id, bt.contract_no
        FROM bill_templet bt
        WHERE bt.del_flag = 'N'
          AND bt.gen_date = DAY (curdate())
          and bt.templet_type = 3
    </select>

    <select id="searchByPaymentId" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT id,
               templet_name
        FROM bill_templet
        WHERE del_flag = 'N'
          AND payment_id = #{paymentId}
    </select>


    <select id="getBillTempletList" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT DISTINCT b.id,
                        b.cust_id,
                        b.templet_no,
                        b.templet_name,
                        b.payment_id,
                        b.gen_bill,
                        b.receiver_id,
                        b.gen_date,
                        b.lock_date,
                        b.deadline,
                        b.pay_date,
                        b.salary_date,
                        b.income_month_type,
                        b.insurancel_flag,
                        b.reserve_flag,
                        b.salary_flag,
                        b.ind_tax_flag,
                        b.row_flag,
                        b.view_items,
                        b.creator,
                        b.create_time,
                        b.updater,
                        b.update_time,
                        b.del_flag
        FROM bill_templet b
                 LEFT JOIN customer c ON b.cust_id = c.id
                 LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        where b.del_flag = 'N'
          and b.cust_id = #{custId}
          and b.templet_type = #{templetType}
        ORDER BY b.create_time DESC
    </select>
    <select id="getTempletByContractNoAndCustId" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT t.id,
               t.templet_name,
               t.templet_no,
               c.bill_date as genDate,
               t.lock_date,
               t.templet_type
        FROM bill_templet t
                 LEFT JOIN contract c on t.contract_no = c.contract_no
        WHERE t.id IN
              (SELECT DISTINCT bt.id
               FROM bill_templet bt
               WHERE bt.contract_no = #{contractNo})
          and t.del_flag = 'N'
        <if test="templetIdList != null and templetIdList.size > 0">
            order by CASE t.id
            <foreach item="item" index="index" collection="templetIdList">
                WHEN #{item} THEN #{index}
            </foreach>
            ELSE
                999999
            END;
        </if>
    </select>
    <select id="searchByCurrGenDate" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT bt.id, bt.gen_date, bt.cust_id, co.contract_no
        FROM bill_templet bt
                 LEFT JOIN contract_templet co on bt.id = co.temp_id
        WHERE bt.del_flag = 'N'
          AND co.del_flag = 'N'
          AND bt.gen_date = DAY (curdate())
          and exists (
        select t.order_no
        from comm_insur_order t
        where t.contract_no = co.contract_no
          and t.del_flag = 'N')
          and exists (
        select ct.contract_no
        from contract ct
        where ct.contract_type = 6 and ct.status = 1)
        GROUP BY bt.id, bt.gen_date, bt.cust_id, co.contract_no

    </select>
    <select id="getListPageByCustId" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select
        b.id, b.cust_id,b.templet_no, b.templet_name, b.payment_id, b.gen_bill, b.receiver_id, b.gen_date, b.lock_date,
        b.deadline, b.pay_date, b.salary_date, b.income_month_type, b.insurancel_flag, b.reserve_flag,
        b.salary_flag,b.ind_tax_flag, b.row_flag, b.view_items, b.creator, b.create_time, b.updater, b.update_time,
        b.del_flag
        from bill_templet b
        where b.del_flag ='N'
        <choose>
            <when test="billTempletDTO.custId != null">
                and b.cust_id = #{billTempletDTO.custId}
            </when>
            <otherwise>
                and b.cust_id = -1
            </otherwise>
        </choose>
        <if test="billTempletDTO.contractNo != null">
            and b.contract_no = #{billTempletDTO.contractNo}
        </if>
        <if test="billTempletDTO.templetType != null">
            and b.templet_type = #{billTempletDTO.templetType}
        </if>
        <if test='billTempletDTO.templetName != null and billTempletDTO.templetName !=""'>
            and (b.templet_name like concat('%',#{billTempletDTO.templetName},'%') or b.templet_no =
            #{billTempletDTO.templetName})
        </if>
        ORDER BY
        b.create_time desc
    </select>
    <select id="getListTempletByContractNo" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT
        t.id,
        t.templet_name,
        t.templet_no,
        con.bill_date as gen_date,
        t.lock_date,
        t.templet_type,
        c.cust_name,
        c.id as custId,
        t.payment_id,
        ci.title as paymentName,
        t.pay_date
        from `reon-customerdb`.bill_templet t
        left join `reon-customerdb`.contract con on t.contract_no = con.contract_no
        LEFT JOIN `reon-customerdb`.customer c ON con.cust_id = c.id
        LEFT JOIN `reon-customerdb`.customer_invoice ci ON t.payment_id = ci.id
        where t.del_flag ='N' and
        t.contract_no=#{contractNo}
        <if test="billTempletId!=null">
            and t.id=#{billTempletId}
        </if>
        <if test="templetType!=null">
            and t.templet_type=#{templetType}
        </if>
        group by t.id ,t.cust_id
    </select>
    <select
            id="checkTemplateName"
            parameterType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo"
    >
        SELECT
        b.id,b.templet_name
        FROM
        bill_templet b
        WHERE
        b.del_flag = 'N'
        <if test="templetName != null and templetName != ''">
            and b.templet_name =#{templetName}
        </if>
        <if test="custId != null">
            and b.cust_id =#{custId}
        </if>
    </select>

    <select id="checkTemplet" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT DISTINCT
        b.id,b.cust_id,b.templet_no,b.templet_type,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,b.deadline,b.pay_date,b.salary_date,
        b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,b.row_flag,b.view_items,b.creator,b.create_time,b.updater,b.update_time,b.del_flag,
        c.cust_no,c.cust_name,ci.customer_invoice_no AS paymentNo,ci.title AS paymentName
        FROM
        bill_templet b
        LEFT JOIN customer c ON b.cust_id = c.id
        LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        LEFT JOIN contract con on b.cust_id = con.cust_id
        WHERE
        b.del_flag = 'N'
        and b.id =#{id}
        <if test="loginName != null and loginName != ''">
            and con.commissioner =#{loginName}
        </if>

    </select>
    <select id="getBillTemplateName" resultType="java.lang.String">
        select templet_name
        FROM bill_templet b
        where b.id = #{templetId}
    </select>
    <select id="getTempletVoByTempletIdList"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select
        b.id,b.cust_id,b.templet_no,b.templet_type,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,
        b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
        b.row_flag,b.view_items,c.sign_com_title,c.contract_no,c.contract_type,b.sub_types,cust.cust_no,c.dist_com,c.commissioner
        from `reon-customerdb`.bill_templet b
        left join `reon-customerdb`.contract c on b.contract_no = c.contract_no
        left join `reon-customerdb`.customer cust on b.cust_id = cust.id
        where
        b.id in
        <foreach collection="list" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and b.del_flag = 'N';
    </select>
    <select id="checkTempletInUse" resultType="java.lang.Integer">
        <if test="ids.size()>0">
            select count(a.contract_no) from (
            select distinct ca.contract_no from bill_templet bt
            left join contract_area ca on bt.id=ca.templet_id
            where ca.contract_no is not null
            and bt.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
            union
            select distinct ct.contract_no from bill_templet bt
            left join contract_templet ct on bt.id=ct.temp_id
            where ct.contract_no is not null
            and bt.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
            union
            select distinct sc.contract_no from bill_templet bt
            left join salary_category sc on bt.id=sc.templet_id
            where sc.contract_no is not null
            and bt.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
            union
            select distinct eo.contract_no from bill_templet bt
            left join order_insuance_cfg oic on bt.id=oic.templet_id
            LEFT JOIN employee_order eo on eo.order_no=oic.order_no
            where eo.contract_no is not null
            and bt.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
            )a
        </if>
    </select>
    <select id="getBillReportByTempletIdList" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select b.id,b.income_month_type,b.title,b.fee_name,b.pay_date,b.gen_date,b.lock_date FROM (
        select bt.id,bt.income_month_type,ci.title,btfc.fee_name,bt.pay_date,bt.gen_date,bt.lock_date
        from `reon-customerdb`.bill_templet bt
        LEFT JOIN `reon-customerdb`.customer_invoice ci on bt.payment_id = ci.id
        LEFT JOIN `reon-customerdb`.bill_templet_fee_cfg btfc on bt.id=btfc.templet_id
        where bt.del_flag = 'N' and ci.del_flag = 'N' and btfc.del_flag='N' and bt.id in (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        ) ORDER BY btfc.default_flag desc LIMIT 10000000
        )b GROUP BY b.id
    </select>
    <select id="getListByCustIds" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select DISTINCT
        b.id,b.cust_id,c.cust_name,b.templet_no,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,
        b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
        b.row_flag,b.view_items, ci.title as paymentName,b.templet_type
        FROM
        bill_templet b
        LEFT JOIN customer c ON b.cust_id = c.id
        LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        WHERE b.del_flag = 'N' and b.cust_id in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        order by b.create_time desc
    </select>
    <select id="getAllTempletByGenDate" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select b.id,
               b.cust_id,
               b.templet_no,
               b.templet_type,
               b.templet_name,
               b.payment_id,
               b.gen_bill,
               b.receiver_id,
               b.gen_date,
               b.lock_date,
               b.deadline,
               b.pay_date,
               b.salary_date,
               b.income_month_type,
               b.insurancel_flag,
               b.reserve_flag,
               b.salary_flag,
               b.ind_tax_flag,
               b.row_flag
        from bill_templet b
        where b.gen_date = #{genDate}
          and b.del_flag = 'N';
    </select>
    <select id="getTempletByContractNoandCustIdIgnoreLoginName"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT t.id,
               t.templet_name,
               t.templet_no,
               t.gen_date,
               t.lock_date,
               t.templet_type
        FROM bill_templet t
        WHERE t.id IN
              (SELECT DISTINCT c.templet_id
               FROM employee_order eo,
                    order_insuance_cfg c
               WHERE eo.order_no = c.order_no
                 AND eo.contract_no = #{contractNo})
          and t.del_flag = 'N'
        UNION
        SELECT t.id,
               t.templet_name,
               t.templet_no,
               t.gen_date,
               t.lock_date,
               t.templet_type
        from contract_area a
                 left JOIN bill_templet t
                           on t.id = a.templet_id
        where a.del_flag = 'N'
          and t.del_flag = 'N'
          and a.contract_no = #{contractNo}
          and t.cust_id = #{custId}
        UNION
        SELECT t.id,
               t.templet_name,
               t.templet_no,
               t.gen_date,
               t.lock_date,
               t.templet_type
        FROM contract_templet c
                 left join bill_templet t
                           on c.temp_id = t.id
        WHERE c.del_flag = 'N'
          and t.del_flag = 'N'
          and c.contract_no = #{contractNo}
          and t.cust_id = #{custId}
        UNION
        select t.id,
               t.templet_name,
               t.templet_no,
               t.gen_date,
               t.lock_date,
               t.templet_type
        from salary_category sa
                 left join bill_templet t
                           on sa.templet_id = t.id
        WHERE sa.del_flag = 'N'
          and t.del_flag = 'N'
          and sa.contract_no = #{contractNo}
          and t.cust_id = #{custId}
    </select>
    <select id="getBillTempletListByContractNoAndBillType"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select DISTINCT b.id,
                        b.cust_id,
                        c.cust_name,
                        b.templet_no,
                        b.templet_name,
                        b.payment_id,
                        b.gen_bill,
                        b.receiver_id,
                        b.gen_date,
                        b.lock_date,
                        b.deadline,
                        b.pay_date,
                        b.salary_date,
                        b.income_month_type,
                        b.insurancel_flag,
                        b.reserve_flag,
                        b.salary_flag,
                        b.ind_tax_flag,
                        b.row_flag,
                        b.view_items,
                        ci.title as paymentName,
                        b.templet_type
        FROM bill_templet b
                 LEFT JOIN customer c ON b.cust_id = c.id
                 LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        WHERE b.del_flag = 'N'
          and b.contract_no = #{contractNo}
          and b.templet_type = #{templetType}
        order by b.create_time desc
    </select>
    <select id="getListByContractNoList" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select DISTINCT
        b.id,b.cust_id,c.cust_name,b.templet_no,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,
        b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
        b.row_flag,b.view_items, ci.title as paymentName,b.templet_type,b.contract_no
        FROM
        bill_templet b
        LEFT JOIN customer c ON b.cust_id = c.id
        LEFT JOIN customer_invoice ci ON b.payment_id = ci.id
        WHERE b.del_flag = 'N' and b.templet_type = #{templetType} and b.contract_no in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        order by b.create_time desc
    </select>
    <select id="getBillTempletNameByTempIdList"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select id,templet_name,salary_date,sub_types,templet_no from bill_templet where id in
        <foreach collection="list" item="item" close=")" separator="," open="(">
            #{item}
        </foreach>
        and del_flag='N'
    </select>
    <select id="getTempletVoByTempletId" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select
        b.id
        ,b.cust_id,b.templet_no,b.templet_type,b.templet_name,b.payment_id,b.gen_bill,b.receiver_id,b.gen_date,b.lock_date,
        b.deadline,b.pay_date,b.salary_date,b.income_month_type,b.insurancel_flag,b.reserve_flag,b.salary_flag,b.ind_tax_flag,
        b.row_flag,b.view_items,b.sub_types,b.contract_no
        from `reon-customerdb`.bill_templet b where b.id = #{templetId}
    </select>
    <select id="getTempletSubTypesMapByTempletIds" resultType="com.reon.hr.sp.customer.entity.cus.BillTemplet">
        select id,sub_types from bill_templet where del_flag = "N" and id in
        <foreach collection="list" item="item" close=")" separator="," open="(">
            #{item}
        </foreach>
    </select>
    <select id="getBillTempletVoByBillTempletList"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT
        t.id,
        t.payment_id,
        t.sub_types,
        t.pay_date,
        c.cust_name,
        c1.contract_type,
        c1.sign_com_title,
        c.id as custId,
        ci.title as paymentName,
        c1.start_date
        from bill_templet t
        LEFT JOIN contract c1 on t.contract_no = c1.contract_no
        LEFT JOIN customer_invoice ci ON t.payment_id = ci.id
        LEFT JOIN customer c ON ci.cust_id = c.id
        where t.del_flag ='N'
        and t.id in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getListTempletByContractNoForDispos"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        SELECT t.id,
               t.templet_name,
               t.templet_no,
               t.lock_date,
               t.gen_date,
               t.templet_type,
               t.pay_date,
               t.payment_id,
               c.cust_name,
               c.id     as custId,
               ci.title as paymentName
        from `reon-customerdb`.bill_templet t
                 LEFT JOIN `reon-customerdb`.customer_invoice ci ON t.payment_id = ci.id
                 LEFT JOIN `reon-customerdb`.customer c ON t.cust_id = c.id
        where t.del_flag = 'N'
          and t.id = #{templetId}
          and t.templet_type = #{templetType}
    </select>

    <select id="getCustomerInvoiceNameByTempletNameList"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select bt.templet_name,ci.title from bill_templet bt left join customer_invoice ci on ci.id = bt.payment_id
        where bt.templet_name in
        <foreach collection="templetNameList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getContractNoAndTempletIdByGroupIdAndBillType"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select bt.contract_no,bt.id,contract_type,c.id custId from `reon-customerdb`.cust_group cg
        left join `reon-customerdb`.customer c on cg.id = c.cust_group_id
        left join `reon-customerdb`.bill_templet bt on c.id = bt.cust_id
        left join `reon-customerdb`.contract con on bt.contract_no = con.contract_no
        where con.status = 1 -- 只能查询到新签的合同,终止的合同无法查询到
        and cg.del_flag = 'N' and bt.del_flag = 'N' and con.del_flag='N'
        and cg.id = #{custGroup} and bt.templet_type =#{billType}
        <if test="contractNo != null and contractNo != ''">
            and con.contract_no =#{contractNo}
        </if>
        <if test="templetId != null">
            and bt.id =#{templetId}
        </if>
        <if test="custId != null">
            and c.id =#{custId}
        </if>
        <if test="userOrgPositionDtoList != null and userOrgPositionDtoList.size > 0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                     close=")">
                (
                ( con.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                )
                <if test="userOrgPositionDto.loginName != null">
                    or (
                    ( con.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                    <!--                    or-->
                    <!--                    ( bt.rev_cs = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})-->
                    )
                </if>
            </foreach>
        </if>
        group by bt.contract_no, bt.id

    </select>

    <select id="getCustomerInvoiceNameByTempletIdList" resultType="java.lang.String">
        select distinct ci.title from customer_invoice ci left join bill_templet bt on ci.id = bt.payment_id where bt.id
        in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getDataById" resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletVo">
        select
        <include refid="templetMap"/>
        from `reon-customerdb`.bill_templet b where id = #{id} ;
    </select>


    <select id="getInsuranceTemCfgReport"
            resultType="com.reon.hr.api.customer.vo.billTemplet.BillTempletReportVo">
        select distinct  c.cust_no,
        c.cust_name,
        cg.group_name,
        con.contract_no,
        con.contract_name,
        con.contract_type,
        con.sub_types,
        con.dist_place,
        con.dist_com,
        ca.account_flag,
        con.commissioner,
        e.name,
        e.cert_no,
        ca.city_code,
        ca.receiving,
        ca.recceiving_type,
        oic.product_code,
        bt.templet_no,
        bt.templet_name,
        fc.fee_no,
        fc.fee_name,
        fc.before_months,
        eo.category_code
        from employee_order eo
        left join employee_entry_dimission eed on eo.order_no = eed.order_no
        left join order_insuance_cfg oic on eo.order_no = oic.order_no
        left join contract con on con.contract_no = eo.contract_no
        left join contract_area ca on ca.contract_area_no = eo.contract_area_no
        left join bill_templet bt on bt.id = oic.templet_id
        left join bill_templet_fee_cfg fc on fc.id = oic.rev_temp_id
        left join customer c on c.id = eo.cust_id
        left join employee e on eo.employee_id = e.id
        left join cust_group cg on c.cust_group_id = cg.id
        where 1=1
            <if test="entryDimissionStatus != null and entryDimissionStatus != ''">
                and eed.status = #{entryDimissionStatus}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and ca.city_code = #{cityCode}
            </if>
            <if test="contractNo != null and contractNo != ''">
                and con.contract_no = #{contractNo}
            </if>
            <if test="custId != null and custId != ''">
                and eo.cust_id = #{custId}
            </if>
            <if test="receiving != null and receiving != ''">
                and ca.receiving = #{receiving}
            </if>

            <if test="accountFlag != null and accountFlag != ''">
                and ca.account_flag = #{accountFlag}
            </if>

            <if test="beforeMonths != null and beforeMonths != ''">
                and fc.before_months = #{beforeMonths}
            </if>

        order by eo.create_time desc
    </select>


    <select id="getInsuranceTemCfgExportReport"
            resultType="com.reon.hr.api.customer.vo.billTemplet.ExportBillTempletReportVo">
        select distinct
            ca.city_code,
            ca.receiving,
            ca.recceiving_type,
            oic.product_code,
            oic.templet_id,
            oic.rev_temp_id,
            eo.cust_id,
            eo.employee_id,
            ca.account_flag,
            eo.contract_no,
            ca.contract_area_no,
            ca.name as contractAreaName,
            eo.category_code
        from employee_order eo
                 left join employee_entry_dimission eed on eo.order_no = eed.order_no
                 left join order_insuance_cfg oic on eo.order_no = oic.order_no
                 left join contract_area ca on ca.contract_area_no = eo.contract_area_no
        where 1 = 1
        <if test="entryDimissionStatus != null and entryDimissionStatus != ''">
            and eed.status = #{entryDimissionStatus}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and ca.city_code = #{cityCode}
        </if>
        <if test="contractNo != null and contractNo != ''">
            and ca.contract_no = #{contractNo}
        </if>
        <if test="custId != null and custId != ''">
            and eo.cust_id = #{custId}
        </if>
        <if test="receiving != null and receiving != ''">
            and ca.receiving = #{receiving}
        </if>
        <if test="accountFlag != null and accountFlag != ''">
            and ca.account_flag = #{accountFlag}
        </if>
        order by eo.create_time desc
    </select>

    <select id="getBillTempletNameNotIntTempletIdList" resultMap="BaseResultMap">
        select id,templet_name,salary_date,sub_types,templet_no from `reon-customerdb`.bill_templet bt where bt.contract_no = #{contractNo}
        <if test="list != null and list.size() != 0">
        and id not in
        <foreach collection="list" item="item" close=")" separator="," open="(">
            #{item}
        </foreach>
        </if>
        and del_flag='N' order by id desc;
    </select>



</mapper>
