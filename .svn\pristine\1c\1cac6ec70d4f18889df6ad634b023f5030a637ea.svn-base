<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.employee.OrderContractFileMapper">


    <sql id="Base_Column_List">
        id
        ,
        order_no,
        file_id,
        creator,
        create_time,
        updater,
        update_time,
        file_type,
        del_flag,
        emp_id,
        qys_file_type,
        per_contract_file_type
    </sql>

    <select id="getAgentContractFileByOrderNo" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where file_type = #{fileType}
            and  order_no =#{orderNo} and del_flag='N'
    </select>
    <select id="getAgentContractFileByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where file_type = #{fileType} and del_flag='N'
        and order_no in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>

    </select>

    <select id="getQysContractFileByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where qys_file_type is not null and del_flag='N'
        and order_no in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="getContractFileCount" resultType="int">
        select
        count(1)
        from order_contract_file where file_type = #{fileType}
        and  order_no =#{orderNo} and del_flag='N' and file_id=#{fileId}
    </select>
    <update id="updateAgentContractFileByOrderNo">
        update order_contract_file
        set file_id=concat(if(file_id is not null, file_id, ''), if(file_id is not null, ',', ''), #{item.fileId})

        where order_no = #{item.orderNo}
    </update>


    <insert id="insertAgentContractFile" parameterType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO order_contract_file (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">
                order_no,
            </if>
            <if test="fileId != null and fileId != ''">
                file_id,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            create_time,
            <if test="updater != null and updater != ''">
                updater,
            </if>
            <if test="fileType != null and fileType != ''">
                file_type,
            </if>
            <if test="empId != null and empId != ''">
                emp_id,
            </if>
            <if test="qysFileType != null and qysFileType != ''">
                qys_file_type,
            </if>
            <if test="perContractFileType != null and perContractFileType != ''">
                per_contract_file_type,
            </if>
            update_time
        </trim>
        ) VALUES (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="fileId != null and fileId != ''">
                #{fileId},
            </if>
            <if test="creator != null and creator != ''">
                #{creator},
            </if>
            #{createTime},
            <if test="updater != null and updater != ''">
                #{updater},
            </if>
            <if test="fileType != null and fileType != ''">
                #{fileType},
            </if>
            <if test="empId != null and empId != ''">
                #{empId},
            </if>
            <if test="qysFileType != null and qysFileType != ''">
                #{qysFileType},
            </if>
            <if test="perContractFileType != null and perContractFileType != ''">
                #{perContractFileType},
            </if>
            #{updateTime}
        </trim>
        )
    </insert>

    <select id="getOrderContractFileId" resultType="long">
        SELECT id FROM order_contract_file
        WHERE qys_file_type = #{vo.qysFileType} and order_no = #{vo.orderNo}
        ORDER BY id DESC LIMIT 1;
    </select>

    <update id="deleteFileId">
        update order_contract_file set del_flag ='Y',updater = #{updater},update_time =now() WHERE file_id =#{fileId}
    </update>

    <select id="getFileIdsByOrderNos" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select file_id,order_no,file_type from order_contract_file where  del_flag='N' and  order_no in

        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>


    <delete id="delOrderContractFileVo">
        <foreach collection="noList" item="item">
            update order_contract_file set del_flag = 'Y' where order_no = #{item};
        </foreach>
    </delete>


    <select id="getFileIdsByOrderNo" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where file_type in (2,3,4) and del_flag='N'
        and order_no =
            #{orderNo}

    </select>

    <select id="getOrderContractFileVoByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where  del_flag='N'
        and order_no in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>

    </select>

    <select id="getFileIdsByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select
        <include refid="Base_Column_List"/>
        from order_contract_file where file_type in (2,3,4)   and del_flag='N'
        and order_no in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="getFileIdByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select  <include refid="Base_Column_List"/>
            from order_contract_file where emp_id in ( select
        emp_id
        from order_contract_file where file_type in (2,3,4)   and del_flag='N'
        and order_no in
        <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>)
    </select>

    <delete id="deleteAllFileIdByOrderNo">
        delete  from order_contract_file where order_no = #{orderNo} and file_type= #{fileType}
    </delete>


    <update id="updateFileIdByOrderNo">
        update order_contract_file set file_id =#{fileId} ,update_time=now(), updater =#{updater}  where order_no=#{orderNo} and file_type=#{fileType}
    </update>

    <delete id="deleteFileIdByOrderNo" >
        delete from order_contract_file where order_no=#{orderNo} and file_type =3
    </delete>

    <select id="getFileByEmpId" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where emp_id =#{empId} and file_type =#{fileType} and del_flag='N'
    </select>

    <select id="getFileByEmpIdList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where emp_id in
        <foreach collection="empIdList" item="empId" close=")" open="(" separator=",">
            #{empId}
        </foreach>
        <if test="fileType != null and fileType != ''">
            and file_type =#{fileType}
        </if>
        and del_flag='N'
    </select>

    <select id="getFileByOrderNoList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where order_no in
        <foreach collection="orderNoList" item="orderNo" close=")" open="(" separator=",">
        #{orderNo}
        </foreach>
        and file_type =#{fileType} and del_flag='N'
    </select>

    <select id="getQysContractFileByOrderNoAndFileType" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where order_no = #{orderNo}
        and qys_file_type =#{qysFileType} and del_flag='N'
    </select>


    <select id="getPeronalContractFileByEmpIdList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where emp_id in
        <foreach collection="empIdList" item="empId" close=")" open="(" separator=",">
            #{empId}
        </foreach>
        and per_contract_file_type =#{fileType} and del_flag='N'
    </select>

    <select id="getPersonalContractFileByFileTypeListAndEmpIdList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where emp_id in
        <foreach collection="empIdList" item="empId" close=")" open="(" separator=",">
            #{empId}
        </foreach>
        and per_contract_file_type  in
        <foreach collection="fileTypeList" item="fileType" close=")" open="(" separator=",">
            #{fileType}
        </foreach>    and del_flag='N'
    </select>

    <delete id="deletePerSonContractFile">
        delete from order_contract_file where emp_id =#{empId} and per_contract_file_type is not null
    </delete>

    <select id="getPersonalContractFileByEmpIdListAndCOntractNo" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select * from order_contract_file where emp_id =#{empId}
        and per_contract_file_type =#{fileType} and order_no = #{contractNo} and del_flag='N'
    </select>

    <select id="getOrderContractFileByIdList" resultType="com.reon.hr.api.customer.vo.employee.OrderContractFileVo">
        select file_id,id from order_contract_file where id in
        <foreach collection="idList" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
