var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


        table.render({
            id: 'salaryItemGrid',
            elem: '#salaryItmeTable',
            url: ML.contextPath + '/customer/salary/salaryItem/getSalaryItemListPage',
            where: {salaryCategoryId: $("#salaryCategoryId").val()},
            method: 'GET',
            defaultToolbar: [],
            height: 'auto',
            page: true, //默认不开启
            limits: [50, 100, 200],
            limit: 50,
            text: {
                none: '暂无数据'
            },
            cols: [[
                {field: 'itemName', title: '薪资项目名称', width: '25%', align: 'center'},
                {field: 'itemNo', title: '薪资项目编号', width: '25%', align: 'center'},
                {field: 'engName', title: '英文名称', width: '25%', align: 'center'},
                {
                    field: 'status', title: '审批状态', width: '25%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("ITEM_STATUS", d.status)
                    }
                }
            ]],
            done: function (res) {
            }
        });


});