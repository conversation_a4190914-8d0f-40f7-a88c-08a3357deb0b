package com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.bill.vo.insurancePractice.InsurancePracticeDifferenceImportVo;
import com.reon.hr.api.customer.vo.batchImport.ImportDataVo;
import org.apache.ibatis.annotations.One;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年06月24日
 * @Version 1.0
 */
public interface IInsurancePracticeOneFeeWrapperService {

    void importDifferences(Long paymentId, ImportDataDto<InsurancePracticeDifferenceImportVo> importVoImportDataDto);

    InsurancePracticeOneFeeVo getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(Long paymentId,List<Integer> approveStatusList);

    Page<ImportDataVo> getImportDifferencesPage(Long paymentId,String loginName,Integer page, Integer limit);

    void submitApprove(Long id);

    List<InsurancePracticeOneFeeVo> getInsurancePracticeOneFeeMessageRemind();

    List<InsurancePracticeOneFeeVo> getApproveDifferencesPage(InsurancePracticeOneFeeVo vo);

    void approveDifferences(InsurancePracticeOneFeeVo vo);

    List<InsurancePracticeOneFeeVo> getAllApproveDifferencesPage(InsurancePracticeOneFeeDetailVo vo);

    List<InsurancePracticeOneFeeBalanceVo> getDeductionAmtByCustIdList(String payCom, String disCom, List<Long> custIdSet);

    void updateInsurancePracticeOneFeeBalanceAndRecordVo(DeductionBalanceCacheVo vo);

    List<OneFeeDiffDataExportVo> printBalanceDiff(Long payId,Integer type);

    /**
     * 驳回同步回退抵扣数据
     * @param paymentId
     */
    void rollbackBalanceByPaymentId(Long paymentId,String loginName);


}
