package com.reon.hr.modules.sys.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceBaseWrapperService;
import com.reon.hr.api.base.vo.InsuranceBaseVo;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.vo.LayuiReplay;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/sys/insuranceBase/")
public class InsuranceBaseController {
    private final static Logger log = LoggerFactory.getLogger(InsuranceBaseController.class);
    @Autowired
    private IInsuranceBaseWrapperService baseWrapperService;

    @RequestMapping(value = "getInsuranceBaseList")
    public LayuiReplay<InsuranceBaseVo> getInsuranceBaseList(String insuraceRatioCode, Integer page, Integer limit) {
        log.info("当前页{}，每页数{}", page, limit);
        Page<InsuranceBaseVo> insuranceBasePage = baseWrapperService.findInsuranceBasePage(insuraceRatioCode, page, limit);
        return new LayuiReplay<InsuranceBaseVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), insuranceBasePage.getTotal(), insuranceBasePage.getRecords());
    }

    @RequestMapping("getInsuranceBasesByRatioCode")
    public LayuiReplay<InsuranceBaseVo> getInsuranceBasesByRatioCode(String insuranceRatioCode) {
        List<InsuranceBaseVo> insuranceRatioVoList = Lists.newArrayList();
        if (StringUtils.isNotBlank(insuranceRatioCode)) {
            insuranceRatioVoList = baseWrapperService.findInsuranceBaseByCode(insuranceRatioCode);
        }
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), insuranceRatioVoList);
    }


}
