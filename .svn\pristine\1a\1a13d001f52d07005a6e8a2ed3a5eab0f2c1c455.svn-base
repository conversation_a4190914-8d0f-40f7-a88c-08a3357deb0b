package com.reon.hr.sp.customer.service.cus;

import java.util.List;

import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.sp.customer.entity.cus.QysRenewCache;

public interface QysRenewCacheService extends IService<QysRenewCache> {


    int updateBatch(List<QysRenewCache> list);

    int batchInsert(List<QysRenewCache> list);



    void insertRestartQysCache(Long oldContractId, Long newContractId, String creator);
}
