<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.BillCheckAbolishForIncomeMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.bill.BillCheckAbolishForIncome">
    <!--@mbg.generated-->
    <!--@Table `reon-billdb`.bill_check_abolish_for_income-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bili_id" jdbcType="BIGINT" property="biliId" />
    <result column="old_check_date" jdbcType="TIMESTAMP" property="oldCheckDate" />
    <result column="abolish_date" jdbcType="TIMESTAMP" property="abolishDate" />
    <result column="old_bill_amt" jdbcType="DECIMAL" property="oldBillAmt" />
    <result column="old_check_amt" jdbcType="DECIMAL" property="oldCheckAmt" />
    <result column="new_check_date" jdbcType="TIMESTAMP" property="newCheckDate" />
    <result column="new_bill_amt" jdbcType="DECIMAL" property="newBillAmt" />
    <result column="new_check_amt" jdbcType="DECIMAL" property="newCheckAmt" />
    <result column="check_month_flag" jdbcType="INTEGER" property="checkMonthFlag" />
    <result column="check_flag" jdbcType="INTEGER" property="checkFlag" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bili_id, old_check_date, abolish_date, old_bill_amt, old_check_amt, new_check_date, 
    new_bill_amt, new_check_amt, check_month_flag, check_flag, creator, create_time, 
    updater, update_time, del_flag
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `reon-billdb`.bill_check_abolish_for_income
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bili_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.biliId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="old_check_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="abolish_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.abolishDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="old_bill_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldBillAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="old_check_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="new_check_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.newCheckDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="new_bill_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.newBillAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="new_check_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.newCheckAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="check_month_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.checkMonthFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="check_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.checkFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="del_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-billdb`.bill_check_abolish_for_income
    (bili_id, old_check_date, abolish_date, old_bill_amt, old_check_amt, new_check_date,
      new_bill_amt, new_check_amt,   creator  )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.biliId,jdbcType=BIGINT}, #{item.oldCheckDate,jdbcType=TIMESTAMP}, #{item.abolishDate,jdbcType=TIMESTAMP},
        #{item.oldBillAmt,jdbcType=DECIMAL}, #{item.oldCheckAmt,jdbcType=DECIMAL}, #{item.newCheckDate,jdbcType=TIMESTAMP},
        #{item.newBillAmt,jdbcType=DECIMAL}, #{item.newCheckAmt,jdbcType=DECIMAL},   #{item.creator,jdbcType=VARCHAR} )
    </foreach>
  </insert>

  <select id="selectDataByMap" resultType="com.reon.hr.api.bill.vo.check.BillCheckAbolishForIncomeVo">
      select
      <include refid="Base_Column_List"/>
      from `reon-billdb`.bill_check_abolish_for_income where del_flag = 'N' and
      bili_id = #{map.billId} and check_flag = #{map.checkFlag} ;

    </select>

  <select id="getBillCheckAbolishForIncomeReportByBillId"
            resultType="com.reon.hr.api.bill.dto.BillCheckAbolishForIncomeReportDto">
      select id,
             bili_id,
             old_check_date,
             abolish_date,
             old_bill_amt,
             old_check_amt,
             new_check_date,
             new_bill_amt,
             new_check_amt,
             check_month_flag,
             check_flag,
             creator
      from `reon-billdb`.bill_check_abolish_for_income where del_flag = 'N'
      and  bili_id in
      <foreach collection="list" open="(" close=")" separator="," item="item">
          #{item}
      </foreach>
      order by bili_id, id;
    </select>
</mapper>