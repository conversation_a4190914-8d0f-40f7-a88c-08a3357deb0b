package com.reon.hr.sp.customer.dao.salary;

import com.reon.hr.api.customer.vo.salary.pay.*;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.sp.customer.entity.salary.SupplierSalaryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierSalaryInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SupplierSalaryInfo record);

    int insertSelective(SupplierSalaryInfo record);

    boolean updateByPrimaryKeySelective(SupplierSalaryInfo record);

    List<SupplierSalaryBillVo> getSupplierEmpBill(SalaryBillSearchVo vo);
    List<WithholdingAgentVo> checkWithholdingAgent(SalaryBillSearchVo vo);

    List<SupplierSalaryInfoVo> getByQueryVoList(@Param("list") List<SupplierSalaryInfoVo> queryVoList);

    List<SupplierSalaryBillVo> getBySupplierSalaryBillVoList(@Param("list") List<SupplierSalaryBillVo> queryVoList);

    List<SupplierSalaryInfoVo> getSalaryInfoVoList(@Param("salaryInfoVoList")List<SupplierSalaryInfoVo> salaryInfoVoList);

    List<SupplierSalaryInfoVo> getSalaryInfoVoListByImport(@Param("salaryInfoVoList")List<SupplierSalaryInfoVo> queryVoList);

    int updateByList(@Param("list") List<SupplierSalaryInfoVo> updateVoList);

    int insertByList(@Param("list")List<SupplierSalaryInfoVo> addVoList);

    List<Integer> getPayStatusBySalaryBillVoList(@Param("list")List<SalaryBillVo> salaryBillVoAllList);

    void updateSupplierConfirmFlag(@Param("list")List<SalaryBillVo> salaryBillVoList,@Param("vo") SupplierSalaryInfoVo supplierSalaryInfoVo);

    void deleteByVoList(@Param("list")List<SupplierSalaryInfoVo> supplierSalaryInfoVoList);
}
