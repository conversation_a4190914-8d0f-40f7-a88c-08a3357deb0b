package com.reon.hr.common.enums;

import lombok.Getter;

@Getter
public enum ThirdPartKeyEn implements BaseSEnum {
    CMD("cmb", "招商银行", "CMBConfig"),
    QI_YUE_SUO("qiyuesuo", "契约锁", "QYSConfig"),
    NUO_NUO("nuonuo", "诺诺", "NNConfigList");

    private final String code; //配置key 的前缀
    private final String desc;
    private final String beanName;


    ThirdPartKeyEn(String code, String desc, String beanName) {
        this.code = code;
        this.desc = desc;
        this.beanName = beanName;
    }

    public static String getBeanNameByCode(String code) {
        for (ThirdPartKeyEn value : ThirdPartKeyEn.values()) {
            if (value.getCode().equals(code)) {
                return value.beanName;
            }
        }
        return null;
    }

    // 根据code获取枚举
    public static ThirdPartKeyEn fromCode(String code) {
        for (ThirdPartKeyEn value : ThirdPartKeyEn.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}