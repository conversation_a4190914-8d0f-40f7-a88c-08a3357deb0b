package com.reon.hr.api.customer.vo.sales;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class TelRecordExportVo implements Serializable {

    private Long id;

    @Excel(name = "客户名称")
    private String companyName;

    @Excel(name = "联系人")
    private String custName;

    @Excel(name = "联系人电话")
    private String tel;

    @Excel(name = "联系人邮箱")
    private String email;

    @Excel(name = "公司地址")
    private String address;

    @Excel(name = "是否接通" ,readConverterExp="1=否,2=是")
    private Integer flag;

    @Excel(name = "是否需要" ,readConverterExp="1=否,2=是")
    private Integer needFlag;

    @Excel(name = "产品" ,readConverterExp="1=外包服务,2=代理,3=员工福利,4=人才招聘,5=人才培训,6=商务服务,7=定睛背调,9=派遣,10=商保")
    private Integer productType;

    @Excel(name = "日期" ,dateFormat = "yyyy-MM-dd")
    private Date callDate;

    @Excel(name = "人数")
    private Integer num;

    @Excel(name = "价格")
    private BigDecimal price;

    @Excel(name = "预计签约日期" ,dateFormat = "yyyy-MM-dd")
    private Date appointDate;

    @Excel(name = "销售" )
    private String saleMan;

    @Excel(name = "备注")
    private String remark;
}
