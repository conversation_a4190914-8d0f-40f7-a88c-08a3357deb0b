package com.reon.ehr.web.controller.base;

import com.reon.ehr.api.sys.dubbo.service.rpc.IEpInsuranceSetWrapperService;
import com.reon.ehr.api.sys.dubbo.service.rpc.base.IEpServiceSiteCfgWrapperService;
import com.reon.ehr.api.sys.model.AjaxResult;
import com.reon.ehr.web.controller.BaseController;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> on 2023/7/17.
 */
@RestController
@RequestMapping("/serviceSiteCfg")
public class ServiceSiteCfgController extends BaseController {
    @DubboReference
    private IEpServiceSiteCfgWrapperService iEpServiceSiteCfgWrapperService;
    @DubboReference
    private IEpInsuranceSetWrapperService insuranceSetWrapperService;

    /**
     * 获取城市地图
     */
    @GetMapping("/selectGroupByCityCode")
    public AjaxResult selectGroupByCityCode() {
        return AjaxResult.success(iEpServiceSiteCfgWrapperService.selectGroupByCityCode());
    }

    /**
     * 获取城市
     * 如果personTypeFlag为1则证明需要隐藏没有社保套餐的人员类型
     */
    @GetMapping("/findCityCodeByPersonType")
    public AjaxResult findCityCodeByPersonType(Integer personTypeFlag) {
        return AjaxResult.success(insuranceSetWrapperService.findCityCodeByPersonType(personTypeFlag));
    }
}
