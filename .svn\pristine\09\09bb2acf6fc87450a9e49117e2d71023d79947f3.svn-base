var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','tableDate'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        tableDate = layui.tableDate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    var sendStatus = '';
    var closeInterval = '';

    lay('#salaryMonth').each(function (i) {
        tableDate.render({
            dateElem: 'salaryMonth',
            row: i,
            object: this,
        });
    });

    //查询薪资单发送数据
    table.render({
        id: 'salarySendQueryGrid',
        elem: '#salarySendQueryGrid',
        url: ML.contextPath + '/customer/salary/send/getSendSalaryInfoMailVoListPage',
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: 'full-200',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: 'custNo', title: '客户编号', width: '13%', align: 'center', fixed: 'left'},
            {field: 'categoryNo', title: '薪资类别编号', width: '10%', align: 'center'},
            {field: 'payName', title: '发放名称', width: '10%', align: 'center'},
            {field: 'name', title: '雇员姓名', width: '10%', align: 'center'},
            {field: 'employeeNo', title: '唯一号', width: '14%', align: 'center'},
            {field: 'listFormat', title: '工资单格式', width: '20%', align: 'center', templet: function (d) {
                    return ML.dictFormatter('LIST_FORMAT', d.listFormat);
                }},
            {field: 'email', title: '电子邮件', width: '10%', align: 'center'},
            {field: 'salaryMonth', title: '工资所属年月', width: '14%', align: 'center'},
            {field: 'taxMonth', title: '工资计税年月', width: '20%', align: 'center'},
            {field: 'billMonth', title: '客户账单年月', width: '10%', align: 'center'},
            {field: 'sendStatus', title: '是否已发送邮件', width: '14%', align: 'center', templet: function (d) {
                    return ML.dictFormatter('BOOLEAN_TYPE', d.sendStatus);
                }},
            {field: 'sendCnt', title: '发送邮件次数', width: '20%', align: 'center'},
            {field: 'withCombined', title: '收入合计', width: '10%', align: 'center'},
            {field: 'deductionsCombined', title: '扣款合计', width: '14%', align: 'center'},
            {field: 'actPay', title: '实发合计', width: '20%', align: 'center'},
            {field: 'taxableSalary', title: '应税工资', width: '10%', align: 'center'},
            {field: 'thisTax', title: '本次扣税', width: '10%', align: 'center', fixed: 'right'}
        ]],
        done: function (res) {
            ML.hideNoAuth();
            if (res.data[0]) {
                sendStatus = res.data[0].sendStatus;
            }
        }
    });


    table.on('toolbar(salarySendQueryGridFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'batchSend':
                var sendSalaryInfoMailVos = [];
                if (checkStatus.data.length == 0) {
                    return layer.msg("请至少选择一条数据！");
                }
                checkStatus.data.forEach(function (value) {
                    //拼装数据
                    var scd = {};
                    scd["salaryCategoryId"] = value.salaryCategoryId;
                    scd["id"] = value.id;
                    scd["name"] = value.name;
                    scd["sendCnt"] = value.sendCnt;
                    scd["email"] = value.email;
                    scd["salMonth"] = value.salMonth;
                    sendSalaryInfoMailVos.push(scd);
                });
                var parameter = {
                    "sendSalaryInfoMailVos":JSON.stringify(sendSalaryInfoMailVos)
                };
                $.ajax({
                    url: ML.contextPath + "/customer/salary/send/batchSend",
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify(parameter),
                    contentType: "application/json;charset=UTF-8",
                    success: function (result) {
                        layer.msg(result.data);
                        reloadTable();
                        closeInterval = setInterval('reloadView()', 5000);
                    },
                    error: function () {
                        layer.msg("系统繁忙，请稍后重试!");
                    }
                });
                break;
        }
    });

    //重载数据
    function reloadTable() {
        table.reload('salarySendQueryGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    //初始化表单数据
    form.on('submit(btnQuery)', function (data) {
        table.reload('salarySendQueryGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
                id = item.custNo;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            $("#custNo").val(id);
        }
    });


    //轮询当前数据表格
    window.reloadView = function () {
        reloadTable();
        if(sendStatus == 2){
            clearInterval(closeInterval);
        }
    }
});
