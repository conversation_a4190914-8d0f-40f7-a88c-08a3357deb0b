package com.reon.hr.api.customer.utils;

import com.reon.hr.api.base.vo.TaxSpreadsheetVo;
import com.reon.hr.api.customer.enums.SalaryPayEnum;
import com.reon.hr.api.customer.vo.salary.pay.SalaryEmpTitleVo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> guoqian
 * @date 2021/1/7 0007 14:48
 * @title
 * @modify
 */
public class PayRollUtils {
    /**
     * 复制map对象
     *
     * @param paramsMap 被拷贝对象
     * @param resultMap 拷贝后的对象
     * @explain 将paramsMap中的键值对全部拷贝到resultMap中；
     * paramsMap中的内容不会影响到resultMap（深拷贝）
     */
    public static void mapCopy(Map paramsMap, Map resultMap) {
        if (resultMap == null) resultMap = new HashMap();
        if (paramsMap == null) return;

        Iterator it = paramsMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            Object key = entry.getKey();
            resultMap.put(key, paramsMap.get(key) != null ? paramsMap.get(key) : "");

        }
    }

    /**
     * 个人所得税
     *
     * @param taxSpreadsheetVos 税率区间
     * @param taxableSalary     扣税工资
     * @param S010              累计扣税不包含当月
     */
    public static BigDecimal S006(List<TaxSpreadsheetVo> taxSpreadsheetVos, BigDecimal taxableSalary, BigDecimal S010) {
        BigDecimal S006 = BigDecimal.ZERO;
        //区间
        for (TaxSpreadsheetVo taxSpreadsheetVo : taxSpreadsheetVos) {
            if ((taxableSalary.compareTo(taxSpreadsheetVo.getMinVal()) == 1 || taxableSalary.compareTo(taxSpreadsheetVo.getMinVal()) == 0)
                    && (taxableSalary.compareTo(taxSpreadsheetVo.getMaxVal()) == -1 || taxableSalary.compareTo(taxSpreadsheetVo.getMaxVal()) == 0)) {
                S006 = ((taxableSalary.multiply(taxSpreadsheetVo.getTaxRatio()).divide(new BigDecimal("100"))).subtract(taxSpreadsheetVo.getDeduction())).subtract(S010);
            }

        }
        if(S006.compareTo(BigDecimal.ZERO) < 0){
            S006=BigDecimal.ZERO;
        }
        return S006;
    }

    /**
     * 保留小数位数
     */
    public static BigDecimal keepBigDecimal(Integer decimalCnt, Object number) {
        BigDecimal result = BigDecimal.ZERO;
        if (StringUtils.isBlank(String.valueOf(number))||number==null) {
            return result;
        } else {
            result = new BigDecimal(String.valueOf(number));
        }
        if (Objects.equals(decimalCnt, SalaryPayEnum.decimalCntEnum.ONE.getIndex())) {
            return new BigDecimal(String.valueOf(number)).setScale(1, BigDecimal.ROUND_HALF_UP);
        }
        if (Objects.equals(decimalCnt, SalaryPayEnum.decimalCntEnum.TWO.getIndex())) {
            return new BigDecimal(String.valueOf(number)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return result;
    }

    /**
     * 系统保留小数
     */
    public static BigDecimal cutBigDecimal(String itemNo, Map<String, List<SalaryEmpTitleVo>> EmpTitleMap, BigDecimal number) {
        if (Objects.equals(EmpTitleMap.get(itemNo).get(0).getTxtFlag(), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex())) {
            BigDecimal result = PayRollUtils.keepBigDecimal(EmpTitleMap.get(itemNo).get(0).getDecimalCnt(), number);
            return result;
        }
        return number;
    }

    public static BigDecimal AnnualBonus(List<TaxSpreadsheetVo> taxSpreadsheetVos, BigDecimal annualBonus) {
        //年终奖除以12再跟年终奖税率表比对，得出对应税率，年终奖乘以对应税率再减去速算扣除出
        BigDecimal tax=annualBonus.divide(new BigDecimal(12),2,BigDecimal.ROUND_HALF_UP);
        BigDecimal S006 = BigDecimal.ZERO;
        //区间
        for (TaxSpreadsheetVo taxSpreadsheetVo : taxSpreadsheetVos) {
            if ((tax.compareTo(taxSpreadsheetVo.getMinVal()) == 1 || tax.compareTo(taxSpreadsheetVo.getMinVal()) == 0)
                    && (tax.compareTo(taxSpreadsheetVo.getMaxVal()) == -1 || tax.compareTo(taxSpreadsheetVo.getMaxVal()) == 0)) {
                S006 = ((annualBonus.multiply(taxSpreadsheetVo.getTaxRatio()).divide(new BigDecimal("100"))).subtract(taxSpreadsheetVo.getDeduction()));
            }

        }
        if(S006.compareTo(BigDecimal.ZERO) < 0){
            S006=BigDecimal.ZERO;
        }
        return S006;
    }
}
