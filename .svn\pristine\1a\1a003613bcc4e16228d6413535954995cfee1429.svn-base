<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>支付审批流程</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <!--add guoqian  20200904-->
    <style>
        .layui-table th {
            text-align: center;
        }
    </style>
    <!--end guoqian 20200904-->
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">

<%--                <div class="layui-inline">--%>
<%--                    <label class="layui-form-label layui-elip" title="支付大类">支付大类:</label>--%>
<%--                    <div class="layui-input-inline">--%>
<%--                        <select class="layui-select" name="payKind" id="payKind" DICT_TYPE="PAYMENT_BROAD_CATEGORIES">--%>
<%--                            <option value=""></option>--%>
<%--                        </select>--%>
<%--                    </div>--%>
<%--                </div>--%>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="支付子类">支付类型:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="payType" id="payType" DICT_TYPE="PAYMENT_SUBCLASS" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="提交人">提交人:</label>
                    <div class="layui-input-inline">
                        <select name="applicant" id="applicant" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="申请支付时间>=">申请支付时间>=</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                               name="startApplyTime"
                               id="startApplyTime" placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="申请支付时间<=">申请支付时间<=</label>
                    <div class="layui-input-inline">
                        <input type="text" id="endApplyTime" maxlength="20" name="endApplyTime" placeholder="请选择"
                               class="layui-input layui-input-disposable" autocomplete="off" readonly>
                    </div>
                    <div class="layui-inline">
                        <a class="layui-btn" id="btnQueryFilter" data-type="reload" lay-filter="btnQueryFilter"
                           lay-submit="">查询</a>
                        <button class="layui-btn" id="reset" type="reset">重置</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="layui-card-body">
        <table class="layui-hide" id="paymentApplyTable" lay-filter="paymentApplyFilter"></table>
    </div>
</div>
<script type="text/jsp" id="topbtn">
    <button class="layui-btn layui-btn-sm" id="add" lay-event="batchApproval" authURI="/workflow/batchSubmitTaskPayment">批量审批</button>
</script>
<script type="text/jsp" id="tooltask">
    {{# if(d.name != "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="taskApproval" lay-event="approval">审批</a>
    {{# } }}

    {{# if(d.name == "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="updateFinalFile" lay-event="updateFinalFile">上传最终合同</a>
    {{# } }}
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/workflow/paymentApply.js?v=${publishVersion}"></script>
</body>
</html>
