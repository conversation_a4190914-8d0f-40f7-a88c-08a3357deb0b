package com.reon.hr.sp.report.dubbo.service.rpc.impl;

import com.reon.hr.api.base.dubbo.service.rpc.sys.IDictionaryWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.bill.vo.FinancialArrearsReportDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgPositionWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.report.dubbo.service.rpc.IFinancialArrearsReportWrapperService;
import com.reon.hr.api.report.vo.FinancialArrearsReportVo;
import com.reon.hr.api.vo.sys.OrgVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("financialArrearsReportDubboService")
public class FinancialArrearsReportWrapperServiceImpl implements IFinancialArrearsReportWrapperService {

    @Autowired
    private IInsuranceBillWrapperService insuranceBillWrapperService;

    @Autowired
    private IContractResourceWrapperService contractResourceWrapperService;

    @Autowired
    private IUserWrapperService userWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Autowired
    private IDictionaryWrapperService dictionaryWrapperService;
    @Autowired
    private IOrgPositionWrapperService iOrgPositionWrapperService;
    @Autowired
    private ICustomerWrapperService customerWrapperService;

    @Override
    public List<FinancialArrearsReportVo> financialArrearsReportByBill(Map<String, Object> map) {
        // 获取打印数据
        List<FinancialArrearsReportDto> financialArrearsReportDtoList = insuranceBillWrapperService.financialArrearsReportByBill(map);
        // 获取所有用户loginName和userName
        Map<String, String> userMap = userWrapperService.getAllUserMap();

         List<FinancialArrearsReportVo> financialArrearsReportVoList = new ArrayList<>();
        // 获取所有的分公司
        List<OrgVo> allCompanies = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> companyMap = allCompanies.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        // 获取所有客户
        List<CustomerVo> allCustomers = customerWrapperService.findAllCustomer();

        Map<Long, String> customerMap = allCustomers.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustNo));
        for (FinancialArrearsReportDto financialArrearsReportDto : financialArrearsReportDtoList) {
            FinancialArrearsReportVo financialArrearsReportVo = new FinancialArrearsReportVo();
            BeanUtils.copyProperties(financialArrearsReportDto, financialArrearsReportVo);
            // 获取客户编号
            financialArrearsReportVo.setCustNo(customerMap.get(financialArrearsReportDto.getCustId()));
            financialArrearsReportVo.setBillPrjCs(userMap.get(financialArrearsReportDto.getBillPrjCs()));
            financialArrearsReportVo.setSeller(userMap.get(financialArrearsReportDto.getSeller()));
            // 转换公司抬头数据
            String formatSignComTitle = userMap.get(financialArrearsReportDto.getSignComTitleId());
            financialArrearsReportVo.setSignComTitle(formatSignComTitle);
            // 获取分公司数据
            financialArrearsReportVo.setAcctCompany(companyMap.get(financialArrearsReportDto.getAcctCompany()));
            financialArrearsReportVoList.add(financialArrearsReportVo);
        }

        return financialArrearsReportVoList;
    }
}
