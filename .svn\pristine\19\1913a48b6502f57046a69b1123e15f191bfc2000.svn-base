package com.reon.hr.sp.bill.dao.bill;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.reon.hr.sp.bill.entity.bill.CustomerLocalRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2022/12/8.
 */
public interface CustomerLocalRecordMapper extends BaseMapper<CustomerLocalRecord> {
    List<CustomerLocalRecord> getByBillMonth(Integer billMonth);

    int insertCustomerLocalRecordList(@Param("insertCustomerLocalRecordList")List<CustomerLocalRecord> insertCustomerLocalRecordList);

    int updaterCustomerLocalRecordList(@Param("updaterCustomerLocalRecordList")List<CustomerLocalRecord> updaterCustomerLocalRecordList);

	List<CustomerLocalRecord> getLocalFlagByCustIdAndBillMonthList(@Param("list") List<String> custIdAndBillIdList);



    List<CustomerLocalRecord> getLocalFlagByBillMonthAndCustId(@Param("list") List<Long> custId,@Param("yearMonth") int preYearMonth);
}
