var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    $('#btnQuery').on('click', function () {
        table.reload('queryBusinessModalsPage', {where: serialize("searchForm")});
    });


    let serviceCode;
    $(document).ready(function () {
        setTimeout(function () {
            let reqMsgJson = $('#reqMsg').val();
            serviceCode = $('#serviceCode').val();
            if(serviceCode == 'checkPayLog'){
                var data = JSON.parse(reqMsgJson);
                $('#id1').hide();
                $('#id2').hide();

                singlePay(data.transAcctInfos,[] )
                //代发经办
                payrollFun(data.payrollBatchInfos, data.detailInfoList);
                return;
            }
            if (ML.isNotEmpty(reqMsgJson)) {
                let reqMsgData = JSON.parse(reqMsgJson);
                let headArr = [];
                let signatureArr = [];
                let bodyArr = [];

                if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.head)) {
                    headArr.push(reqMsgData.request.head)
                }

                if (ML.isNotEmpty(reqMsgData.signature)) {
                    signatureArr.push(reqMsgData.signature)
                }

                if (serviceCode === "BB1PAYOP") {
                    let bb1payopx1Arr = [];
                    let bb1paybmx1Arr = [];
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        bb1payopx1Arr = ML.isNotEmpty(reqMsgData.request.body.bb1payopx1) ? reqMsgData.request.body.bb1payopx1 : [];
                        bb1paybmx1Arr = ML.isNotEmpty(reqMsgData.request.body.bb1paybmx1) ? reqMsgData.request.body.bb1paybmx1 : [];
                    }
                    singlePay(bb1payopx1Arr, bb1paybmx1Arr)
                } else if (serviceCode === "DCLISMOD") {
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        bodyArr.push(reqMsgData.request.body)
                    }
                    // 业务经办查询
                    DCLISMODGirdFun(bodyArr);
                } else if (serviceCode === "BB6BTHHL") {
                    var refNoIndex = $('#refNoIndex').val();
                    var split = refNoIndex.split("_");
                    let bb6cdcbhx1Arr = [];
                    let bb6cdcdlx1Arr = [];
                    if(split.length == 2 && split[1] == 0){
                        bb6cdcbhx1Arr.push(reqMsgData.batchInfo);
                        bb6cdcdlx1Arr = reqMsgData.detailInfoList;
                    } else {
                        if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                            bb6cdcbhx1Arr = ML.isNotEmpty(reqMsgData.request.body.bb6cdcbhx1) ? reqMsgData.request.body.bb6cdcbhx1 : [];
                            bb6cdcdlx1Arr = ML.isNotEmpty(reqMsgData.request.body.bb6cdcdlx1) ? reqMsgData.request.body.bb6cdcdlx1 : [];
                        }
                    }


                    //代发经办
                    payrollFun(bb6cdcbhx1Arr, bb6cdcdlx1Arr);
                } else if (serviceCode === "BB6BPDQY") {
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        bodyArr = ML.isNotEmpty(reqMsgData.request.body.bb6bpdqyy1) ? reqMsgData.request.body.bb6bpdqyy1 : [];
                    }
                    //代发批次与明细查询
                    payrollQueryFun(bodyArr);
                } else if (serviceCode === "BB1PAYQR") {
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        bodyArr = ML.isNotEmpty(reqMsgData.request.body.bb1payqrx1) ? reqMsgData.request.body.bb1payqrx1 : [];
                    }
                    //企银支付业务查询
                    singlePayQueryFun(bodyArr);
                }                //电子回单
                else if (serviceCode === "ASYCALHD") {
                    let data = [];
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        data.push(reqMsgData.request.body)
                    }
                    ASYCALHDArr(data);
                }

                else if (serviceCode === "DCAGPPDF") {
                    let data = [];
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        data.push(reqMsgData.request.body)
                    }
                    //代发明细对账单查询
                    DCAGPPDFArr(data);
                }

                else if (serviceCode === "NTQACINF") {
                    let data = [];
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        data =  ML.isNotEmpty(reqMsgData.request.body.ntqacinfx) ? reqMsgData.request.body.ntqacinfx : [];
                    }
                    //代发明细对账单查询
                    NTQACINFArr(data);
                }

                else if (serviceCode === "trsQryByBreakPoint") {
                    let data = [];
                    if (ML.isNotEmpty(reqMsgData.request) && ML.isNotEmpty(reqMsgData.request.body)) {
                        data.push(reqMsgData.request.body.TRANSQUERYBYBREAKPOINT_X1);
                    }
                    //账户交易信息查询
                    trsQryByBreakPointArr(data);
                }
                if(serviceCode != 'checkPayLog'){
                    table.reload('headGird', {data: headArr});
                    table.reload('signatureGird', {data: signatureArr});
                }
            }
        }, 300)
    })


    table.render({
        id: 'headGird',
        elem: '#QueryGridTable'
        , data: []
        , height: '150px'
        , page: false
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , method: 'POST'
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'funcode', title: '接口名称', align: 'center', width: '150'}
            , {field: 'userid', title: '用户Id', align: 'center', width: '150'}
            , {field: 'reqid', title: '请求Id', align: 'center', width: '150'}
        ]]
    });


    table.render({
        id: 'signatureGird',
        elem: '#QueryGridTable2'
        , data: []
        , height: '150px'
        , page: false
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , method: 'POST'
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'sigdat', title: '签名内容', align: 'center', width: '150'}
            , {field: 'sigtim', title: '签名时间', align: 'center', width: '150'}
        ]]
    });


    function DCLISMODGirdFun(bodyArr) {
        table.render({
            id: 'DCLISMODGird',
            elem: '#QueryGridTable3'
            , data: bodyArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'buscod', title: '业务模式', align: 'center', width: '150'}
            ]]
        });
    }


    function singlePay(bb1payopx1Arr, bb1paybmx1Arr) {
        if(serviceCode != 'checkPayLog'){
            table.render({
                id: 'bb1paybmx1Gird',
                elem: '#QueryGridTable5'
                , data: bb1paybmx1Arr
                , height: '150px'
                , page: false
                , toolbar: '#topbtn'
                , defaultToolbar: []
                , method: 'POST'
                , text: {
                    none: '暂无数据' //无数据时展示
                }
                , done: function (res, curr, count) {
                    //监听行双击事件
                    table.on('rowDouble(QueryGridTable)', function (obj) {

                    });
                    ML.hideNoAuth();
                    if (res.data) {

                    }

                }, error: function (res, msg) {
                    // layer.msg(msg);
                }
                , cols: [[
                    {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                    , {field: 'busMod', title: '业务模式编号', align: 'center', width: '150'}
                    , {field: 'busCod', title: '业务类别', align: 'center', width: '150'}
                ]]
            });
        }
        //单笔支付查询
        table.render({
            id: 'bb1payopx1Gird',
            elem: '#QueryGridTable4'
            , data: bb1payopx1Arr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'yurRef', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'dbtAcc', title: '转出账号', align: 'center', width: '150'}
                , {field: 'crtAcc', title: '收方帐号', align: 'center', width: '150'}
                , {field: 'crtNam', title: '收方户名', align: 'center', width: '150'}
                , {field: 'ccyNbr', title: '币种', align: 'center', width: '150'}
                , {field: 'trsAmt', title: '交易金额', align: 'center', width: '150'}
                , {field: 'nusAge', title: '用途', align: 'center', width: '150'}
            ]]
        });


    }


    function payrollFun(bb6cdcbhx1Arr, bb6cdcdlx1Arr) {
        table.render({
            id: 'bb6cdcbhx1Gird',
            elem: '#QueryGridTable6'
            , data: bb6cdcbhx1Arr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'yurref', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'begtag', title: '批次开始标志', align: 'center', width: '150'}
                , {field: 'endtag', title: '批次结束标志', align: 'center', width: '150'}
                , {field: 'reqnbr', title: '续传次数流程实例号', align: 'center', width: '150'}
                , {field: 'accnbr', title: '账号', align: 'center', width: '150'}
                , {field: 'accnam', title: '户名', align: 'center', width: '150'}
                , {field: 'ttlamt', title: '总金额', align: 'center', width: '150'}
                , {field: 'ttlcnt', title: '总笔数', align: 'center', width: '150'}
                , {field: 'curamt', title: '本次金额', align: 'center', width: '150'}
                , {field: 'curcnt', title: '本次笔数', align: 'center', width: '150'}
                , {field: 'ccynbr', title: '币种', align: 'center', width: '150'}
                , {field: 'trstyp', title: '交易类型', align: 'center', width: '150'}
                , {field: 'nusage', title: '用途', align: 'center', width: '150'}
                , {field: 'eptdat', title: '期望日期', align: 'center', width: '150'}
                , {field: 'chlflg', title: '结算通道', align: 'center', width: '150'}


            ]]
        });


        table.render({
            id: 'bb6cdcdlx1Gird',
            elem: '#QueryGridTable7'
            , data: bb6cdcdlx1Arr
            , height: 'full-200'
            , page: true
            , limit: 50
            , limits: [50, 100, 200]
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'trxseq', title: '交易序号', align: 'center', width: '150'}
                , {field: 'accnbr', title: '账号', align: 'center', width: '150'}
                , {field: 'accnam', title: '户名', align: 'center', width: '150'}
                , {field: 'trsamt', title: '交易金额', align: 'center', width: '150'}
                , {field: 'eacbnk', title: '他行户口开户行', align: 'center', width: '150'}
                , {field: 'eaccty', title: '他行户口开户地', align: 'center', width: '150'}

            ]]
        });
    }


    function payrollQueryFun(bodyArr) {
        table.render({
            id: 'bb6bpdqyy1Grid',
            elem: '#QueryGridTable8'
            , data: bodyArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'busCod', title: '业务模式', align: 'center', width: '150'}
                , {field: 'yurRef', title: '业务参考号', align: 'center', width: '150'}


            ]]
        });
    }


    function singlePayQueryFun(bodyArr) {
        table.render({
            id: 'bb6bpdqyy1Grid',
            elem: '#QueryGridTable8'
            , data: bodyArr
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'busCod', title: '业务模式', align: 'center', width: '150'}
                , {field: 'yurRef', title: '业务参考号', align: 'center', width: '150'}


            ]]
        });
    }


    function ASYCALHDArr(data) {
        table.render({
            id: 'bb6bpdqyy1Grid',
            elem: '#QueryGridTable9'
            , data: data
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'eacnbr', title: '账户', align: 'center', width: '150'}
                , {field: 'begdat', title: '开始时间', align: 'center', width: '150'}
                , {field: 'enddat', title: '结束时间', align: 'center', width: '150'}
                , {field: 'primod', title: '文件格式', align: 'center', width: '150'}
                , {field: 'rrccod', title: '回单代码', align: 'center', width: '150'}
                , {field: 'rrcflg', title: '打印标志', align: 'center', width: '150'}


            ]]
        });
    }
    function DCAGPPDFArr(data) {
        table.render({
            id: 'DCAGPPDFArrGirdId',
            elem: '#QueryGridTable10'
            , data: data
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'begdat', title: '开始时间', align: 'center', width: '150'}
                , {field: 'enddat', title: '结束日期', align: 'center', width: '150'}
                , {field: 'begidx', title: '查询标记', align: 'center', width: '150'}
                , {field: 'buscod', title: '业务类型', align: 'center', width: '150'}
                , {field: 'busmod', title: '业务模式', align: 'center', width: '150'}
                , {field: 'eacnam', title: '收方户名', align: 'center', width: '150'}
                , {field: 'maxamt', title: '最大金额', align: 'center', width: '150'}
                , {field: 'minamt', title: '最小金额    ', align: 'center', width: '150'}
                , {field: 'payeac', title: '付款账户', align: 'center', width: '150'}
                , {field: 'prtmod', title: '打印模式', align: 'center', width: '150', templet: function (d) {
                        return formatPrtmod(d.prtmod);
                    }}
                , {field: 'ptyref', title: '业务参考号', align: 'center', width: '150'}
                , {field: 'gwEnable', title: '地址类型', align: 'center', width: '150'}


            ]]
        });
    }
    function NTQACINFArr(data) {
        table.render({
            id: 'NTQACINFArrId',
            elem: '#QueryGridTable11'
            , data: data
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }

            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'accnbr', title: '账号', align: 'center', width: '150'}
                , {field: 'bbknbr', title: '分行号', align: 'center', width: '150', templet: function (d) {
                        if(ML.isNotEmpty(d.bbknbr)){
                            for (let key in BankBranchEn) {
                                if(BankBranchEn[key] == d.bbknbr){
                                    return key;
                                }
                            }
                        }
                        return "";
                    }}

            ]]
        });
    }
    function trsQryByBreakPointArr(data) {
        table.render({
            id: 'trsQryByBreakPointArrId',
            elem: '#QueryGridTable12'
            , data: data
            , height: '150px'
            , page: false
            , toolbar: '#topbtn'
            , defaultToolbar: []
            , method: 'POST'
            , text: {
                none: '暂无数据' //无数据时展示
            }
            , done: function (res, curr, count) {
                //监听行双击事件
                table.on('rowDouble(QueryGridTable)', function (obj) {

                });
                ML.hideNoAuth();
                if (res.data) {

                }

            }, error: function (res, msg) {
                // layer.msg(msg);
            }
            , cols: [[
                {field: '', type: 'checkbox', width: '50', fixed: 'left'}
                , {field: 'cardNbr', title: '户口号', align: 'center', width: '150'}
                , {field: 'beginDate', title: '开始日期', align: 'center', width: '150'}
                , {field: 'endDate', title: '结束日期', align: 'center', width: '150'}
                , {field: 'loanCode', title: '借贷码', align: 'center', width: '150', templet: function (d) {
                        return LoanCodeEn[d.loanCode];
                    }}
                , {field: 'currencyCode', title: '币种', align: 'center', width: '150'}
            ]]
        });
    }



});
