/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/9
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.base.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.reon.hr.api.base.enums.SocialSecurityFundEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 <AUTHOR>
 @version 1.0
 @className SocialSecurityFundExportVo
 @date 2020/6/9 15:23 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialSecurityFundExportVo implements Serializable {
    private static final long serialVersionUID = 1L;


    private String receptionFlag;
    private String injuryEnjoy;
    private String mustPayment;

    private String insuranceRatioCode;

    //格式化导出字段
    private String strApplyInsurFreq;

    private String strApplyFundFreq;

    private String strApplyIllnessFreq;

    private String strBillFeeRule;

    private String strAddCrfFlag;

    private String strInsurRemitFlag;

    private String strCrfRemitFlag;

    private String strAdditionFlag;

    private Date createTime;


    private Integer comExactVal;//企业精确值

    private Integer comCalcMode;//企业计算方式

    private Integer indExactVal;//个人精确值

    private Integer indCalcMode;//个人计算方式

    //组装数据字段
    private Integer orgType;//服务网点类型
    private String orgTypeStr;//服务网点类型
    /**
     特殊注意事项
     */
    private String specialConsiderations;
    private String ehrSpecialConsiderations;
    private String setCode;//社保套餐编号

    private String setName;//社保套餐名称

    private Integer currentDate;//当前日期

    private String categoryCode;//人员类型

    private String serviceSiteCode;//服务网点(自有公司或供应商)

    private String serviceSiteName;//服务网点(自有公司或供应商)

    private Integer useStatusType;//使用状态
    private String useStatusTypeStr;//使用状态
    private String useState;//使用状态说明

    private String additionStartMonth;//离职补差起始月

    private String receivingMan;//接单客户

    private Integer productCode;//产品名称

    private String province;//省

    private String cityCode;//城市编码

    private String cityName;//缴费地市/县区（简称）

    private String bigAccountArea;//大户所在区

    private String singleAccountCounty;//单立户可操作区县

    private Integer applyInsurFreq;//社保申报频率

    private Integer applyFundFreq;//公积金申报频率

    /**
     大病申报频率
     1:月收
     2:年收(不足年按月)
     3:年收(不足年按年)
     */
    private Integer applyIllnessFreq;

    private Integer billFeeRule;//账单收费规则

    private String paymentAndDeclare;//是否可以落地发薪和报税

    private String disFundPolicyNorm;//残障金政策标准

    private String policyLink;//政策链接

    private String companyCollectNorm;//残障金我司收取标准

    private String payBaseMethod;//缴纳基本方式

    private String indTypeCode;//"人员类别户籍性质+缴费方案）"

    private Integer insurAddDay;//社保增员截止日

    private Integer insurSubDay;//社保减员截止日

    private Integer crfAddDay;//公积金增员截止日

    private Integer crfSubDay;//公积金减员截止日

    private Integer addCrfFlag;//是否有补充公积金

    private Integer insurRemitFlag;//是否社保必缴

    private Integer crfRemitFlag;//是否公积金必缴

    private String addInfo;//增员材料

    private String downInfo;//减员材料

    private String appendInfo;//补缴材料

    private Integer additionFlag;//是否离职补差

    private String ratioName;//比例名称

    private String additionRule;//离职补差规则

    private BigDecimal lowBaseCom;//单位基数下限

    private BigDecimal highBaseCom;//单位基数上限

    private BigDecimal comRatio;//单位比例

    private BigDecimal comAdd;//单位定值

    private BigDecimal lowBaseInd;//个人基数下限

    private BigDecimal highBaseInd;//个人基数上限

    private BigDecimal indRatio; //个人比例

    private BigDecimal indlAdd;//个人定值

    private BigDecimal complementMo = BigDecimal.ZERO;//补缴月数
    private String incumbencyFlag = "";//在职是否补差
    private String incumbencyStartMonth = "";//每年基数调整


    private BigDecimal lowBaseComAA1;//单位基数下限

    private BigDecimal highBaseComAA1;//单位基数上限

    private BigDecimal comRatioAA1;//单位比例

    private BigDecimal comAddAA1;//单位定值

    private BigDecimal lowBaseIndAA1;//个人基数下限

    private BigDecimal highBaseIndAA1;//个人基数上限

    private BigDecimal indRatioAA1; //个人比例

    private BigDecimal indlAddAA1;//个人定值

    private BigDecimal complementMoAA1;//补缴月数

    private String incumbencyFlagAA1;//在职是否补差

    private String incumbencyStartMonthAA1;//每年基数调整


    private BigDecimal lowBaseComAA2;//单位基数下限

    private BigDecimal highBaseComAA2;//单位基数上限

    private BigDecimal comRatioAA2;//单位比例

    private BigDecimal comAddAA2;//单位定值

    private BigDecimal lowBaseIndAA2;//个人基数下限

    private BigDecimal highBaseIndAA2;//个人基数上限

    private BigDecimal indRatioAA2; //个人比例

    private BigDecimal indlAddAA2;//个人定值

    private BigDecimal complementMoAA2;//补缴月数
    private String incumbencyFlagAA2;//在职是否补差

    private String incumbencyStartMonthAA2;//每年基数调整


    private BigDecimal lowBaseComAA3;//单位基数下限

    private BigDecimal highBaseComAA3;//单位基数上限

    private BigDecimal comRatioAA3;//单位比例

    private BigDecimal comAddAA3;//单位定值

    private BigDecimal lowBaseIndAA3;//个人基数下限

    private BigDecimal highBaseIndAA3;//个人基数上限

    private BigDecimal indRatioAA3; //个人比例

    private BigDecimal indlAddAA3;//个人定值

    private BigDecimal complementMoAA3;//补缴月数
    private String incumbencyFlagAA3;//在职是否补差

    private String incumbencyStartMonthAA3;//每年基数调整


    private BigDecimal lowBaseComAA4;//单位基数下限

    private BigDecimal highBaseComAA4;//单位基数上限

    private BigDecimal comRatioAA4;//单位比例

    private BigDecimal comAddAA4;//单位定值

    private BigDecimal lowBaseIndAA4;//个人基数下限

    private BigDecimal highBaseIndAA4;//个人基数上限

    private BigDecimal indRatioAA4; //个人比例

    private BigDecimal indlAddAA4;//个人定值

    private BigDecimal complementMoAA4;//补缴月数


    private String incumbencyFlagAA4;//在职是否补差

    private String incumbencyStartMonthAA4;//每年基数调整


    private BigDecimal lowBaseComAA5;//单位基数下限

    private BigDecimal highBaseComAA5;//单位基数上限

    private BigDecimal comRatioAA5;//单位比例

    private BigDecimal comAddAA5;//单位定值

    private BigDecimal lowBaseIndAA5;//个人基数下限

    private BigDecimal highBaseIndAA5;//个人基数上限

    private BigDecimal indRatioAA5; //个人比例

    private BigDecimal indlAddAA5;//个人定值

    private BigDecimal complementMoAA5;//补缴月数
    private String incumbencyFlagAA5;//在职是否补差

    private String incumbencyStartMonthAA5;//每年基数调整


    private BigDecimal lowBaseComAA6;//单位基数下限

    private BigDecimal highBaseComAA6;//单位基数上限

    private BigDecimal comRatioAA6;//单位比例

    private BigDecimal comAddAA6;//单位定值

    private BigDecimal lowBaseIndAA6;//个人基数下限

    private BigDecimal highBaseIndAA6;//个人基数上限

    private BigDecimal indRatioAA6; //个人比例

    private BigDecimal indlAddAA6;//个人定值

    private BigDecimal complementMoAA6;//补缴月数
    private String incumbencyFlagAA6;//在职是否补差

    private String incumbencyStartMonthAA6;//每年基数调整


    private BigDecimal lowBaseComAA7;//单位基数下限

    private BigDecimal highBaseComAA7;//单位基数上限

    private BigDecimal comRatioAA7;//单位比例

    private BigDecimal comAddAA7;//单位定值

    private BigDecimal lowBaseIndAA7;//个人基数下限

    private BigDecimal highBaseIndAA7;//个人基数上限

    private BigDecimal indRatioAA7; //个人比例

    private BigDecimal indlAddAA7;//个人定值

    private BigDecimal complementMoAA7;//补缴月数

    private String incumbencyFlagAA7;//在职是否补差

    private String incumbencyStartMonthAA7;//每年基数调整


    private BigDecimal lowBaseComAA8;//单位基数下限

    private BigDecimal highBaseComAA8;//单位基数上限

    private BigDecimal comRatioAA8;//单位比例

    private BigDecimal comAddAA8;//单位定值

    private BigDecimal lowBaseIndAA8;//个人基数下限

    private BigDecimal highBaseIndAA8;//个人基数上限

    private BigDecimal indRatioAA8; //个人比例

    private BigDecimal indlAddAA8;//个人定值

    private BigDecimal complementMoAA8;//补缴月数
    private String incumbencyFlagAA8;//在职是否补差

    private String incumbencyStartMonthAA8;//每年基数调整


    private BigDecimal lowBaseComAA9;//单位基数下限

    private BigDecimal highBaseComAA9;//单位基数上限

    private BigDecimal comRatioAA9;//单位比例

    private BigDecimal comAddAA9;//单位定值

    private BigDecimal lowBaseIndAA9;//个人基数下限

    private BigDecimal highBaseIndAA9;//个人基数上限

    private BigDecimal indRatioAA9; //个人比例

    private BigDecimal indlAddAA9;//个人定值

    private BigDecimal complementMoAA9;//补缴月数

    private String incumbencyFlagAA9;//在职是否补差

    private String incumbencyStartMonthAA9;//每年基数调整


    private BigDecimal lowBaseComAA10;//单位基数下限

    private BigDecimal highBaseComAA10;//单位基数上限

    private BigDecimal comRatioAA10;//单位比例

    private BigDecimal comAddAA10;//单位定值

    private BigDecimal lowBaseIndAA10;//个人基数下限

    private BigDecimal highBaseIndAA10;//个人基数上限

    private BigDecimal indRatioAA10; //个人比例

    private BigDecimal indlAddAA10;//个人定值

    private BigDecimal complementMoAA10;//补缴月数

    private String incumbencyFlagAA10;//在职是否补差

    private String incumbencyStartMonthAA10;//每年基数调整


    private BigDecimal lowBaseComAA11;//单位基数下限

    private BigDecimal highBaseComAA11;//单位基数上限

    private BigDecimal comRatioAA11;//单位比例

    private BigDecimal comAddAA11;//单位定值

    private BigDecimal lowBaseIndAA11;//个人基数下限

    private BigDecimal highBaseIndAA11;//个人基数上限

    private BigDecimal indRatioAA11; //个人比例

    private BigDecimal indlAddAA11;//个人定值

    private BigDecimal complementMoAA11;//补缴月数

    private String incumbencyFlagAA11;//在职是否补差

    private String incumbencyStartMonthAA11;//每年基数调整


    private BigDecimal lowBaseComAA12;//单位基数下限

    private BigDecimal highBaseComAA12;//单位基数上限

    private BigDecimal comRatioAA12;//单位比例

    private BigDecimal comAddAA12;//单位定值

    private BigDecimal lowBaseIndAA12;//个人基数下限

    private BigDecimal highBaseIndAA12;//个人基数上限

    private BigDecimal indRatioAA12; //个人比例

    private BigDecimal indlAddAA12;//个人定值

    private BigDecimal complementMoAA12;//补缴月数


    private String incumbencyFlagAA12;//在职是否补差

    private String incumbencyStartMonthAA12;//每年基数调整


    private BigDecimal lowBaseComAA13;//单位基数下限

    private BigDecimal highBaseComAA13;//单位基数上限

    private BigDecimal comRatioAA13;//单位比例

    private BigDecimal comAddAA13;//单位定值

    private BigDecimal lowBaseIndAA13;//个人基数下限

    private BigDecimal highBaseIndAA13;//个人基数上限

    private BigDecimal indRatioAA13; //个人比例

    private BigDecimal indlAddAA13;//个人定值

    private BigDecimal complementMoAA13;//补缴月数

    private String incumbencyFlagAA13;//在职是否补差

    private String incumbencyStartMonthAA13;//每年基数调整


    private BigDecimal lowBaseComAA14;//单位基数下限

    private BigDecimal highBaseComAA14;//单位基数上限

    private BigDecimal comRatioAA14;//单位比例

    private BigDecimal comAddAA14;//单位定值

    private BigDecimal lowBaseIndAA14;//个人基数下限

    private BigDecimal highBaseIndAA14;//个人基数上限

    private BigDecimal indRatioAA14; //个人比例

    private BigDecimal indlAddAA14;//个人定值

    private BigDecimal complementMoAA14;//补缴月数


    private String incumbencyFlagAA14;//在职是否补差

    private String incumbencyStartMonthAA14;//每年基数调整


    private BigDecimal lowBaseComAA15;//单位基数下限

    private BigDecimal highBaseComAA15;//单位基数上限

    private BigDecimal comRatioAA15;//单位比例

    private BigDecimal comAddAA15;//单位定值

    private BigDecimal lowBaseIndAA15;//个人基数下限

    private BigDecimal highBaseIndAA15;//个人基数上限

    private BigDecimal indRatioAA15; //个人比例

    private BigDecimal indlAddAA15;//个人定值

    private BigDecimal complementMoAA15;//补缴月数


    private String incumbencyFlagAA15;//在职是否补差

    private String incumbencyStartMonthAA15;//每年基数调整


    private BigDecimal lowBaseComAA16;//单位基数下限

    private BigDecimal highBaseComAA16;//单位基数上限

    private BigDecimal comRatioAA16;//单位比例

    private BigDecimal comAddAA16;//单位定值

    private BigDecimal lowBaseIndAA16;//个人基数下限

    private BigDecimal highBaseIndAA16;//个人基数上限

    private BigDecimal indRatioAA16; //个人比例

    private BigDecimal indlAddAA16;//个人定值

    private BigDecimal complementMoAA16;//补缴月数


    private String incumbencyFlagAA16;//在职是否补差

    private String incumbencyStartMonthAA16;//每年基数调整


    private BigDecimal lowBaseComAA17;//单位基数下限

    private BigDecimal highBaseComAA17;//单位基数上限

    private BigDecimal comRatioAA17;//单位比例

    private BigDecimal comAddAA17;//单位定值

    private BigDecimal lowBaseIndAA17;//个人基数下限

    private BigDecimal highBaseIndAA17;//个人基数上限

    private BigDecimal indRatioAA17; //个人比例

    private BigDecimal indlAddAA17;//个人定值

    private BigDecimal complementMoAA17;//补缴月数


    private String incumbencyFlagAA17;//在职是否补差

    private String incumbencyStartMonthAA17;//每年基数调整

    private BigDecimal lowBaseComAA18;//单位基数下限

    private BigDecimal highBaseComAA18;//单位基数上限

    private BigDecimal comRatioAA18;//单位比例

    private BigDecimal comAddAA18;//单位定值

    private BigDecimal lowBaseIndAA18;//个人基数下限

    private BigDecimal highBaseIndAA18;//个人基数上限

    private BigDecimal indRatioAA18; //个人比例

    private BigDecimal indlAddAA18;//个人定值

    private BigDecimal complementMoAA18;//补缴月数


    private String incumbencyFlagAA18;//在职是否补差

    private String incumbencyStartMonthAA18;//每年基数调整


    private BigDecimal lowBaseComAA20;//单位基数下限

    private BigDecimal highBaseComAA20;//单位基数上限

    private BigDecimal comRatioAA20;//单位比例

    private BigDecimal comAddAA20;//单位定值

    private BigDecimal lowBaseIndAA20;//个人基数下限

    private BigDecimal highBaseIndAA20;//个人基数上限

    private BigDecimal indRatioAA20; //个人比例

    private BigDecimal indlAddAA20;//个人定值

    private BigDecimal complementMoAA20;//补缴月数

    private String incumbencyFlagAA20;//在职是否补差

    private String incumbencyStartMonthAA20;//每年基数调整

    /**
     开始时间
     */
    private Integer startTime;

    /**
     结束时间
     */
    private Integer endTime;

    /**
     最低工资
     */
    private BigDecimal minWage;

    /**
     年度市平均工资
     */
    private BigDecimal annualAverageCityWage;

    /**
     年度省平均工资
     */
    private BigDecimal annualProvincialAverageWage;

    private Integer specialFlag;
    private Integer chargeFreq;// 收费频率

    public String getStrApplyInsurFreq() {
        if (this.applyInsurFreq != null) {
            this.strApplyInsurFreq = SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(this.applyInsurFreq);
        }
        return strApplyInsurFreq;
    }

    public String getStrApplyFundFreq() {
        if (this.applyFundFreq != null) {
            this.strApplyFundFreq = SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(this.applyFundFreq);
        }
        return strApplyFundFreq;
    }

    public String getStrApplyIllnessFreq() {
        if (this.applyIllnessFreq != null) {
            this.strApplyIllnessFreq = SocialSecurityFundEnum.IsApplyIllnessFreqEnum.getName(this.applyIllnessFreq);
        }
        return strApplyIllnessFreq;
    }


    public String getStrBillFeeRule() {
        if (this.billFeeRule != null) {
            this.strBillFeeRule = SocialSecurityFundEnum.IsBillFeeRuleEnum.getName(this.billFeeRule);
        }
        return strBillFeeRule;
    }

    public String getStrAddCrfFlag() {
        if (this.addCrfFlag != null) {
            this.strAddCrfFlag = SocialSecurityFundEnum.IsBooleanTypeEnum.getName(this.addCrfFlag);
        }
        return strAddCrfFlag;
    }

    public String getStrInsurRemitFlag() {
        if (this.insurRemitFlag != null) {
            this.strInsurRemitFlag = SocialSecurityFundEnum.IsBooleanTypeEnum.getName(this.insurRemitFlag);
        }
        return strInsurRemitFlag;
    }

    public String getStrCrfRemitFlag() {
        if (this.crfRemitFlag != null) {
            this.strCrfRemitFlag = SocialSecurityFundEnum.IsBooleanTypeEnum.getName(this.crfRemitFlag);
        }
        return strCrfRemitFlag;
    }

    public String getStrAdditionFlag() {
        if (this.additionFlag != null) {
            this.strAdditionFlag = SocialSecurityFundEnum.IsBooleanTypeEnum.getName(this.additionFlag);
        }
        return strAdditionFlag;
    }

    /**
     避免多重set
     */
    public void setOtherData(SocialSecurityFundExportVo vo) {
        this.strApplyInsurFreq = vo.getStrApplyInsurFreq();
        this.strApplyFundFreq = vo.getStrApplyFundFreq();
        this.strApplyIllnessFreq = vo.getStrApplyIllnessFreq();
        this.strBillFeeRule = vo.getStrBillFeeRule();
        this.strAdditionFlag = vo.getStrAdditionFlag();
        this.orgType = vo.getAddCrfFlag();
        this.specialConsiderations = vo.getSpecialConsiderations();
        this.ehrSpecialConsiderations = vo.getEhrSpecialConsiderations();
        this.categoryCode = vo.getCategoryCode();
        this.serviceSiteName = vo.getServiceSiteName();
        this.bigAccountArea = vo.getBigAccountArea();
        this.singleAccountCounty = vo.getSingleAccountCounty();
        this.applyInsurFreq = vo.getApplyInsurFreq();
        this.applyFundFreq = vo.getApplyFundFreq();
        this.billFeeRule = vo.getBillFeeRule();
        this.insurAddDay = vo.getInsurAddDay();
        this.insurSubDay = vo.getInsurSubDay();
        this.crfAddDay = vo.getCrfAddDay();
        this.crfSubDay = vo.getCrfSubDay();
        this.addInfo = vo.getAddInfo();
        this.appendInfo = vo.getAppendInfo();
        this.downInfo = vo.getDownInfo();
        this.additionFlag = vo.getAdditionFlag();
        this.additionRule = vo.getAdditionRule();
        this.cityCode = vo.getCityCode();
        this.paymentAndDeclare = vo.getPaymentAndDeclare();
    }

    /**
     基本数据集
     @param vo 签证官
     */
    public void setBasicData(SocialSecurityFundExportVo vo) {
        this.setName = vo.getSetName();
        this.ratioName = vo.getRatioName();
        this.strAddCrfFlag = vo.getStrAddCrfFlag();
        this.strInsurRemitFlag = vo.getStrInsurRemitFlag();
        this.strCrfRemitFlag = vo.getStrCrfRemitFlag();
        this.serviceSiteCode = vo.getServiceSiteCode();
        this.indTypeCode = vo.getIndTypeCode();
        this.addCrfFlag = vo.getAddCrfFlag();
        this.insurRemitFlag = vo.getInsurRemitFlag();
        this.crfRemitFlag = vo.getCrfRemitFlag();
        this.productCode = vo.getProductCode();
        this.cityName = vo.getCityName();
    }
}
