package com.reon.hr.api.base.dubbo.service.rpc.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dto.sys.ServiceSiteCfgDto;
import com.reon.hr.api.base.vo.OrgAndSupplierVo;
import com.reon.hr.api.base.vo.ServiceSiteCfgImportVo;
import com.reon.hr.api.base.vo.ServiceSiteCfgVo;

import java.util.List;
import java.util.Map;

public interface IServiceSiteCfgWrapperService {
    Page<ServiceSiteCfgVo> findServiceSiteCfgSetPage(ServiceSiteCfgVo serviceSiteCfgVo, Integer page, Integer limit);
    List<ServiceSiteCfgVo> findServiceSiteCfgSetPage(ServiceSiteCfgVo serviceSiteCfgVo);

    List<ServiceSiteCfgVo> findByCityCodeAndServiceCodeAndIndType(List<ServiceSiteCfgVo> serviceSiteCfgVos);
    ServiceSiteCfgVo findBySpecialField(ServiceSiteCfgVo serviceSiteCfgVos);

    String CITY = "city";
    String SERVER = "server";
    String IND_TYPE = "indType";
    String USE_STATUS = "useStatus";
    // 社保或公积金申报频率(选择)
    String APPLY_INSUR_OR_FUND_FREQ = "applyInsurFreqName";
    // 大病申报频率(选择)
    String APPLY_ILLNESS_FREQ = "applyIllnessFreqName";
    // 账单收费规则(选择)
    String BILL_FEE_RULE = "billFeeRuleName";
    // 是否
    String VALID_FLAG = "validFlagMap";

    /**
     * 获取字段对应显示值和真实值map
     * @return {@link Map}<{@link String}, {@link Map}<{@link Object}, {@link String}>>
     */
    Map<String, Map<String, Object>> getSelectMap();

    /**
     * 获取去重的城市
     * @return {@link List}<{@link ServiceSiteCfgVo}>
     */
    List<ServiceSiteCfgVo> getDistinctCity();

    ServiceSiteCfgVo saveOrUpdate(ServiceSiteCfgVo serviceSiteCfgVo) throws Exception;

    /**
     * 更新供应商
     *
     * @param serviceSiteCfgVo 服务网站cfg签证官
     * @return {@link ServiceSiteCfgVo}
     * @throws Exception 异常
     */
    int updateBySupplier(ServiceSiteCfgVo serviceSiteCfgVo);

    Integer findCityCodeAndServiceSiteCodeAndCategoryCode(Integer cityCode,String serviceSiteCode,String categoryCode) ;

	ServiceSiteCfgVo getIncreaseAndDecrease(ServiceSiteCfgVo serviceSiteCfgVo);

    void saveServiceSiteCfgImportVo(List<ServiceSiteCfgImportVo> serviceSiteCfgImportVoList);

    Integer selectServiceSiteByCityOrSiteName(ServiceSiteCfgVo serviceSiteCfgVo);

    void BatchUpdateServiceByCityOrSiteCode(List<ServiceSiteCfgVo> serviceSiteCfgVo);

    /**
     * 批量修改
     * @param dataList
     * @return {@link Boolean}
     */
    Boolean BatchUpdate(List<ServiceSiteCfgVo> dataList);

    List<ServiceSiteCfgDto> getInsurAddDay(List<ServiceSiteCfgDto> serviceSiteCfgDtoList);
    List<ServiceSiteCfgDto> getInsurAddDayByArgs(List<String> args);
    List<ServiceSiteCfgDto> getInsurAddDayByCondit(List<String> args);


    /**
     * 根据去重标识来动态查询,1否2是
     * 1否的时候不去重,比如客户端主页要查询到所有相关的服务网点
     * 2是的时候去重,比如根据城市查询到的服务网点只需要查服务网点,一个城市下的多个不同人员类型相同服务网点保留一个即可
     * @param serviceSiteCfgDtoList
     * @param distinctFlag
     * @return
     */
    List<ServiceSiteCfgDto> getInsurAddDay1(List<ServiceSiteCfgDto> serviceSiteCfgDtoList, Integer distinctFlag);

    List<ServiceSiteCfgDto> getNormalData();

    Map<String, List<ServiceSiteCfgVo>> selectGroupByCityCode();

    List<OrgAndSupplierVo> getSupplierOrCompanyByType(Integer cityCode, List<Integer> useStatusTypes);

    ServiceSiteCfgVo getDataByCANo(Integer cityCode, String receiving, String categoryCode);
}
