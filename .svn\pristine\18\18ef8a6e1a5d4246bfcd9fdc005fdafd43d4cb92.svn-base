package com.reon.hr.api.report.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.report.vo.IncomeCountTableReportVo;
import com.reon.hr.api.report.vo.InvoiceCheckIntermidiateVo;
import com.reon.hr.api.report.vo.MonthlyIncomeReportVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IncomeCountTableWrapperService {
	Map<String, Object> getIncomeCountTableListPageFromReport(IncomeCountTableReportVo vo);

	/**
	 * 月度收入报告
	 *
	 * @param vo 签证官
	 * @return {@link List}<{@link IncomeCountTableReportVo}>
	 */
	List<MonthlyIncomeReportVo> getMonthlyIncomeFromReport(MonthlyIncomeReportVo vo);

	Page<IncomeCountTableReportVo> getIncomeCountTableListPage(Integer limit, Integer page, IncomeCountTableReportVo incomeCountTableReportVo);


//	List<Long> getAllCheckIdListByInvoiceId(Long item);

	List<Long> getAllInvoiceIdListByCheckId(Long item);

	List<Long> getAllInvoiceIdListByCheckIdFromIncomeCount(Long item);


	Integer insertSerlective(IncomeCountTableReportVo incomeCountTableReportVo);

	Long insertInvoiceCheckIntermidiate(InvoiceCheckIntermidiateVo invoiceCheckIntermidiateVo);

	Integer getCountTableDataByCheckIdAndInvoiceId(Long checkId, Long invoiceId);



	Integer updateStatus(InvoiceCheckIntermidiateVo invoiceCheckIntermidiateVo);

	List<Long> getAllCheckIdList();

	BigDecimal getAmountDepositedByBillId(Long billId);

	List<Long> getAllInvoiceList();

	List<Long> getAllCheckSubIdList(List<Long> checkSubIdSelectList);

    void deleteIncomeCountTableAndIntermidiate(List<Long> checkIds);

	Boolean getIdListBillId(Long billId);

	void restoreData(String contractNo, Long templetId, Integer billMonth, Date startDate, Date endDate);

	Integer deleteIncomeCountTableAndIntermidiateByDelDate(String delDate);

    void deleteIncomeCountTableAndIntermidiateByCheckIdAndBillIds(Long checkId, List<Long> billIds);
}
