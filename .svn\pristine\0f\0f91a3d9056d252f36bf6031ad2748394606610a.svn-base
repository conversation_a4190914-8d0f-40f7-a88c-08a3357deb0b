/**
 * 
 */
package com.reon.hr.sp.base.service.impl.sequence;

import com.reon.hr.sp.base.dao.SequenceMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.reon.hr.sp.base.service.sequence.ISequenceInnerService;


@Service
public class SequenceInnerServiceImpl implements ISequenceInnerService {

    @Autowired
    private SequenceMapper seqDao;

    @Override
    public long generateSeqValue(String seqKey) {
        String seqStr = seqDao.getSeqNextvalByKey(seqKey);
        if (StringUtils.isAllEmpty(seqStr)) {
            throw new RuntimeException("not found seqkey:" + seqKey);
        }
        return Long.parseLong(seqStr.split(",")[0]);
    }

    @Override
    public long[] getSeqValueAndStep(String seqKey) {
        String seqStr = seqDao.getSeqNextvalByKey(seqKey);
        if (StringUtils.isAllEmpty(seqStr)) {
            throw new RuntimeException("not found seqkey:" + seqKey);
        }
        long[] arr = new long[2];
        arr[0] = Long.parseLong(seqStr.split(",")[0]);
        arr[1] = Long.parseLong(seqStr.split(",")[1]);

        return arr;
    }

}
