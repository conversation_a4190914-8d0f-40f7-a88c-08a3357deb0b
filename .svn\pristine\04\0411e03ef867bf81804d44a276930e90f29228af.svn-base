package com.reon.hr.api.change.enums;

import lombok.Getter;

@Getter
public enum ExcelOprTypeEnum {
    DOWNLOAD_DATA(1),// 下载数据
    EXPORT_PAGE(2),// 导出页面数据
    EXPORT_DECLARE(3),// 导出申报
    DOWNLOAD_EXPORT_LIST(4),// 下载导入名单
    DECLARE_IMPORT(5),// 申报导入
    ADJUST_IMPORT(6),// 调整导入
    DECLARE_COLLECT(7),// 基数收集
    COLLECT_IMPORT(8),// 收集调整
    CUSTOMER_DOWNLOAD_DATA(9),// 客户下载数据
    EXPORT_COLLECT(10),// 导出数据-客服
    CUSTOMER_EXPORT_COLLECT(11),// 客户导出数据-客服
    ;

    private Integer code;

    ExcelOprTypeEnum(Integer code) {
        this.code = code;
    }
}
