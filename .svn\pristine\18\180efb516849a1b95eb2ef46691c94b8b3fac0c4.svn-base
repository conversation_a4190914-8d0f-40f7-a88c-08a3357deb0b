<%--
  Created by IntelliJ IDEA.
  User: guoqian
  Date: 2020/6/17 0017
  Time: 17:34
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>

    <title>社保公积金一览(单个城市)</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <!-- import CSS -->
    <link rel="stylesheet" href="${ctx}/layui/vue/general.css?v=${publishVersion}" media="all">
    <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
    <!-- import JavaScript -->
    <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/icon.js?v=${publishVersion}"></script>

    <style>
        /* 页面整体样式 */
        body {
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }

        #app {
            max-width: 1600px;
            margin: 0 auto;
        }

        /* 查询表单样式 */
        .el-form {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 20px;
            margin-bottom: 20px;
        }

        /* 标签页样式 */
        .el-tabs--border-card > .el-tabs__content {
            padding: 24px;
        }

        /* 基础信息表单样式 */
        .basic-info-form .el-form-item {
            margin-bottom: 20px;
        }

        .basic-info-form .el-form-item__label {
            font-weight: 500;
            color: #606266;
        }

        /* 表格样式 */
        .el-table {
            font-size: 13px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #e4e7ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .el-table tr:hover td {
            background: #f5f7fa;
        }

        .el-table tr:nth-child(even) td {
            background: #fafbfc;
        }

        .el-table tr:nth-child(even):hover td {
            background: #f0f2f5;
        }

        /* 表格第一列样式（产品名称列） */
        .el-table td:first-child,
        .el-table th:first-child {
            text-align: left;
            padding-left: 16px;
            font-weight: 500;
        }

        /* 数字列样式 */
        .el-table td:not(:first-child):not(:last-child) {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-weight: 500;
        }

        /* 操作列样式 */
        .el-table td:last-child,
        .el-table th:last-child {
            text-align: center;
            min-width: 80px;
        }

        /* 表格容器样式 */
        .table-container {
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 20px;
        }

        /* 表格标题样式 */
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e4e7ed;
        }

        /* 合计行样式 */
        .el-table .total-row td {
            background: #f0f9ff !important;
            font-weight: 600;
            color: #1f2937;
            border-top: 2px solid #3b82f6;
        }

        .el-table .total-row:hover td {
            background: #e0f2fe !important;
        }

        .el-select, .el-input {
            border-radius: 4px;
        }

        .el-select .el-input__inner,
        .el-select .el-input__inner:focus,

            /* 按钮样式 */
        .el-button {
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button:hover {
            transform: translateY(-1px);
        }

        .el-button--primary {
            background: #409eff;
            border-color: #409eff;
        }

        .el-button--success {
            background: #67c23a;
            border-color: #67c23a;
        }

        /* 表单项间距 */
        .el-form-item {
            margin-bottom: 18px;
        }

        /* 行间距 */
        .el-row {
            margin-bottom: 16px;
        }

        .el-row:last-child {
            margin-bottom: 0;
        }

        /* 卡片样式 */
        .el-card {
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 文本域样式 */
        .el-textarea__inner {
            border-radius: 4px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;
        }

        .el-textarea__inner:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .el-select__wrapper.is-disabled {
            background: #FFFFFF;
        }

        .el-select__wrapper.is-disabled .el-select__selected-item {
            color: #000000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .el-form {
                padding: 16px;
            }

            .el-tabs--border-card > .el-tabs__content {
                padding: 16px;
            }

            .el-col {
                margin-bottom: 16px;
            }
        }
    </style>
</head>
<body>
<div id="app">
    <!-- 查询条件 -->
    <el-form :model="obj.queryParams" inline label-width="auto">
        <el-form-item label="所属城市:">
            <el-select class="width220" filterable v-model="obj.queryParams.city" placeholder="请选择"
                       @change="handleCityChange">
                <el-option v-for="item in obj.cityOptions" :key="item.cityCode" :label="item.cityName"
                           :value="item.cityCode +'-'+item.cityName"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="人员类别:">
            <el-select class="width220" filterable v-model="obj.queryParams.personType" placeholder="请选择"
                       @change="handlePersonTypeChange">
                <el-option v-for="item in obj.personTypeOptions" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="服务网点:">
            <el-select style="width: 260px" filterable v-model="obj.queryParams.servicePoint" placeholder="请选择"
                       @change="handleServicePointChange">
                <el-option v-for="item in obj.servicePointOptions" :key="item.value" :label="item.serviceSiteName"
                           :value="item.categoryCode+'-'+item.cityCode+'-'+item.serviceSiteCode"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="Download" @click="handleExport">导出</el-button>
        </el-form-item>
    </el-form>
    <el-tabs v-model="obj.activeTab" type="border-card">
        <el-tab-pane label="基础信息" name="basic">
            <el-form :model="obj.basicForm" class="basic-info-form" label-width="auto">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="使用状态:">
                            <el-select disabled class="width-full" filterable v-model="obj.basicForm.useStatusType"
                                       placeholder="">
                                <el-option value="1" label="正常使用"></el-option>
                                <el-option value="2" label="停止使用"></el-option>
                                <el-option value="3" label="特殊使用"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="使用状态说明:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.useState"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="社保增员截点:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.insurAddDay"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="社保减员截点:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.insurSubDay"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公积金增员截点:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.crfAddDay"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="公积金减员截点:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.crfSubDay"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="社保申报频率:">
                            <el-select disabled class="width-full" filterable v-model="obj.basicForm.applyInsurFreqName"
                                       placeholder="">
                                <el-option value="1" label="当月增当月,当月减当月"></el-option>
                                <el-option value="2" label="当月增次月，当月减次月"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="公积金申报频率:">
                            <el-select disabled class="width-full" filterable v-model="obj.basicForm.applyFundFreqName"
                                       placeholder="">
                                <el-option value="1" label="当月增当月,当月减当月"></el-option>
                                <el-option value="2" label="当月增次月，当月减次月"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="账单收费规则:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.billFeeRuleName"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="大病申报频率:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.strApplyIllnessFreq"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="大户所在区:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.bigAccountArea"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="当地单立户可操作性区县:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.singleAccountCounty"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="增员材料:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.addInfo"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否必须落地发薪:">
                            <el-select disabled class="width-full" filterable v-model="obj.basicForm.mustPayment"
                                       placeholder="">
                                <el-option value="0" label="是"></el-option>
                                <el-option value="1" label="否"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="减员材料:">
                            <el-input readonly type="textarea" class="width-full" v-model="obj.basicForm.downInfo"
                                      :rows="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="补缴材料:">
                            <el-input readonly type="textarea" class="width-full" v-model="obj.basicForm.appendInfo"
                                      :rows="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="特殊注意事项:">
                            <el-input readonly type="textarea" class="width-full"
                                      v-model="obj.basicForm.specialConsiderations"
                                      :rows="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="是否离职补差:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.additionFlagName"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="离职补差起始月:">
                            <el-input readonly class="width-full" v-model="obj.basicForm.additionStartMonth"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-tab-pane>
        <el-tab-pane label="基数比例一览" name="ratio">
            <!-- 基数比例表格 -->
            <div class="table-container">
                <el-table :data="obj.tableData" border style="width: 100%;">
                    <el-table-column prop="productName" label="产品名称" min-width="120"></el-table-column>
                    <el-table-column prop="lowBaseCom" label="单位基数下限" min-width="140" sortable></el-table-column>
                    <el-table-column prop="highBaseCom" label="单位基数上限" min-width="120"></el-table-column>
                    <el-table-column prop="comRatio" label="单位比例" min-width="100"></el-table-column>
                    <el-table-column prop="comAdd" label="单位定值" min-width="120" sortable></el-table-column>
                    <el-table-column prop="lowBaseInd" label="个人基数下限" min-width="140" sortable></el-table-column>
                    <el-table-column prop="highBaseInd" label="个人基数上限" min-width="120"></el-table-column>
                    <el-table-column prop="indRatio" label="个人比例" min-width="120" sortable></el-table-column>
                    <el-table-column prop="indlAdd" label="个人定值" min-width="120" sortable></el-table-column>
                    <el-table-column prop="lowBaseComRatio" label="单位下限金额" min-width="140"
                                     sortable></el-table-column>
                    <el-table-column prop="lowBaseIndAdd" label="个人下限金额" min-width="140"
                                     sortable></el-table-column>
                    <el-table-column prop="totalLow" label="单位+个人下限金额总计" min-width="200"
                                     sortable></el-table-column>
                    <el-table-column prop="highBaseComRatio" label="单位上限金额" min-width="140"
                                     sortable></el-table-column>
                    <el-table-column prop="highBaseIndAdd" label="个人上限金额" min-width="140"
                                     sortable></el-table-column>
                    <el-table-column prop="totalHigh" label="单位+个人上限金额总计" min-width="200"
                                     sortable></el-table-column>
                    <el-table-column prop="complementMo" label="补缴月份" min-width="120" sortable></el-table-column>
                    <el-table-column prop="spanYearElag" label="是否可跨年补缴" min-width="140" sortable>
                        <template #default="scope">
                            <div> {{ scope.row.spanYearElagName }}</div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination v-model:current-page="obj.queryParams.pageNum"
                               v-model:page-size="obj.queryParams.pageSize"
                               :page-sizes="[50,100]"
                               layout="total, sizes, prev, pager, next, jumper" :total="obj.total"
                               @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
            </div>

            <!-- 最低工资表格 -->
            <div class="table-container mt20">
                <el-table :data="obj.wageData" border style="width: 100%;">
                    <el-table-column prop="cityName" label="城市名称"></el-table-column>
                    <el-table-column prop="serviceSiteName" label="服务网点"></el-table-column>
                    <el-table-column prop="startTime" label="开始时间"></el-table-column>
                    <el-table-column prop="endTime" label="结束时间"></el-table-column>
                    <el-table-column prop="minWage" label="最低工资"></el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination v-model:current-page="obj.queryParams.pageNum"
                               v-model:page-size="obj.queryParams.pageSize"
                               :page-sizes="[50,100]"
                               layout="total, sizes, prev, pager, next, jumper" :total="obj.wageTotal"
                               @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
            </div>
        </el-tab-pane>
    </el-tabs>
</div>
</body>
<script>
    const {createApp, reactive, ref, onMounted} = Vue
    const {ElMessage} = ElementPlus

    const app = createApp({
        setup() {

            // 响应式数据
            const obj = reactive({
                // 页面状态
                activeTab: 'basic',

                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 50,
                },
                cityOptions: [],// 城市下拉框数据
                personTypeOptions: [],// 人员类型下拉框数据
                waitData: [],
                servicePointOptions: [],// 服务网点下拉框数据

                cityChange: '',// 城市选中数据
                personTypeChange: '',// 人员类型选中数据
                servicePointChange: '',// 服务网点选中数据

                paramsList: [],
                // 基础信息表单
                basicForm: {},

                // 基数比例相关
                tableData: [],
                total: 0,

                // 最低工资相关
                wageTotal: 0,
                wageData: [],

            })

            function getCodeName(type, val) {
                let arr = window.top['dictCachePool'][type];
                if (!arr) return "";
                for (let i in arr) {
                    let obj = arr[i];
                    if (val == obj.code) {
                        return obj.name;
                    }
                }
                return "";
            }

            //获取城市列表
            function cityList() {
                SocialSecurityCityConfigAPI.getCityList().then(res => {
                    if (res.data.code === 0) {
                        obj.cityOptions = res.data.data
                    }
                })
            }

            //城市选择
            function handleCityChange(val) {
                obj.cityChange = val
                if (val) {
                    let arr = val.split('-')
                    SocialSecurityCityConfigAPI.getServiceSiteList({
                        cityCode: arr[0],
                    }).then(res => {
                        if (res.data.code === 0) {
                            obj.personTypeOptions = []
                            obj.waitData = []
                            obj.servicePointOptions = []
                            obj.paramsList = []
                            obj.queryParams.personType = ''
                            obj.queryParams.servicePoint = ''
                            obj.personTypeChange = ''
                            obj.servicePointChange = ''

                            obj.waitData = res.data.data
                            if (obj.waitData && obj.waitData.length > 0) {
                                obj.waitData.forEach(item => {
                                    item.serviceSiteName = item.serviceSiteName + '(' + (item.useStatusType == 1 ? '正常使用' : item.useStatusType == 3 ? '特殊使用' : '') + ')'
                                })
                                obj.personTypeOptions.push({
                                    code: arr[0],
                                    name: arr[1] + '-' + getCodeName('PEOPLE_IND_TYPE', obj.waitData[0].indTypeCode)
                                })
                            }
                        }
                    })
                }
            }

            //服务人员类型 选择
            function handlePersonTypeChange(val) {
                obj.personTypeChange = val
                if (val) {
                    obj.servicePointOptions = obj.waitData
                }
            }

            //服务网点 选择
            function handleServicePointChange(val) {
                obj.servicePointChange = val
                if (val) {
                    obj.paramsList = val.split('-')
                }
            }

            //查询
            function handleQuery() {
                if (!obj.cityChange) {
                    ElMessage.warning('请选择所属城市!')
                    return false
                }
                if (!obj.personTypeChange) {
                    ElMessage.warning('请选择人员类别!')
                    return false
                }
                if (!obj.servicePointChange) {
                    ElMessage.warning('请选择服务网点!')
                    return false
                }
                getBasicInfo()
                getFundRatio()
                getFundRatio2()
            }

            // 重置
            function resetQuery() {
                obj.personTypeOptions = []
                obj.waitData = []
                obj.servicePointOptions = []
                obj.paramsList = []
                obj.queryParams.personType = ''
                obj.queryParams.servicePoint = ''
                obj.queryParams.city = ''
                obj.queryParams.pageNum = 1
                obj.queryParams.pageSize = 50

                obj.cityChange = ''
                obj.personTypeChange = ''
                obj.servicePointChange = ''
            }

            // 导出
            function handleExport() {
                if (!obj.cityChange) {
                    ElMessage.warning('请选择所属城市!')
                    return false
                }
                if (!obj.personTypeChange) {
                    ElMessage.warning('请选择人员类别!')
                    return false
                }
                if (!obj.servicePointChange) {
                    ElMessage.warning('请选择服务网点!')
                    return false
                }
                let params = {
                    cityCode: obj.paramsList[1],
                    indTypeCode: obj.paramsList[0],
                    serviceSiteCode: obj.paramsList[2]
                }
                let vot = JSON.stringify(params);
                window.location.href = ML.contextPath + '/serviceSiteCfg/socialOneCity/exportFile?vot=' + vot;
            }

            //获取基础信息
            function getBasicInfo() {
                let params = {
                    cityCode: obj.paramsList[1],
                    indTypeCode: obj.paramsList[0],
                    serviceSiteCode: obj.paramsList[2]
                }
                SocialSecurityCityConfigAPI.getBaseInfo(params).then(res => {
                    if (res.data.code === 0) {
                        obj.basicForm = res.data.data
                        if (obj.basicForm.useStatusType) {
                            obj.basicForm.useStatusType = obj.basicForm.useStatusType.toString()
                        }
                        if (obj.basicForm.mustPayment) {
                            obj.basicForm.mustPayment = obj.basicForm.mustPayment.toString()
                        } else {
                            obj.basicForm.mustPayment = "0"
                        }
                    }
                })
            }

            //获取基金比例
            function getFundRatio() {
                let params = {
                    page: obj.queryParams.pageNum,
                    limit: obj.queryParams.pageSize,
                    cityCode: obj.paramsList[1],
                    indTypeCode: obj.paramsList[0],
                    serviceSiteCode: obj.paramsList[2],
                    currentTimeTip: true
                }
                SocialSecurityCityConfigAPI.getBaseRadio(params).then(res => {
                    if (res.data.code === 0) {
                        obj.tableData = res.data.data
                        obj.total = res.data.count
                    }
                })
            }

            //获取基金比例2
            function getFundRatio2() {
                let params = {
                    page: obj.queryParams.pageNum,
                    limit: obj.queryParams.pageSize,
                    cityCode: obj.paramsList[1],
                    indTypeCode: obj.paramsList[0],
                    serviceSiteCode: obj.paramsList[2],
                    currentTimeTip: true
                }
                SocialSecurityCityConfigAPI.getBaseRadio2(params).then(res => {
                    if (res.data.code === 0) {
                        obj.wageData = res.data.data
                        obj.wageTotal = res.data.count
                    }
                })
            }

            //分页
            function handleSizeChange(size) {
                obj.queryParams.pageSize = size
                getFundRatio()
                getFundRatio2()
            }

            function handleCurrentChange(page) {
                obj.queryParams.pageNum = page
                getFundRatio()
                getFundRatio2()
            }

            onMounted(() => {
                cityList()
            })

            // 加载图标
            if (typeof ElementPlusIconsVue !== 'undefined') {
                // console.log('ElementPlusIconsVue 已加载，可用图标：', Object.keys(ElementPlusIconsVue))
                for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                    app.component(key, component)
                }
            } else {
                console.error('ElementPlusIconsVue 未定义，请检查图标文件是否正确加载')
                // 尝试从 window 对象获取
                if (window.ElementPlusIconsVue) {
                    // console.log('从 window 对象找到 ElementPlusIconsVue')
                    for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
                        app.component(key, component)
                    }
                }
            }
            return {
                obj,

                handleCityChange,
                handlePersonTypeChange,
                handleServicePointChange,
                handleQuery,
                resetQuery,
                handleExport,
                handleSizeChange,
                handleCurrentChange,
            }
        }
    })
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount('#app')
</script>
</html>
