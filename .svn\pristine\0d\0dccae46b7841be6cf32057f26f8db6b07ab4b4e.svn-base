package com.reon.ehr.web.controller.enterprise;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.ehr.api.sys.dubbo.service.rpc.IEpBillWrapperService;
import com.reon.ehr.api.sys.model.AjaxResult;
import com.reon.ehr.api.sys.vo.EhrBillExportConfig;
import com.reon.ehr.api.sys.vo.EhrBillExportVo;
import com.reon.ehr.api.sys.vo.InsuranceBillVo;
import com.reon.ehr.api.sys.vo.SysUserVo;
import com.reon.ehr.web.controller.BaseController;
import com.reon.ehr.web.domain.TableDataInfo;
import com.reon.hr.api.bill.dto.BillPrintDto;
import com.reon.hr.api.bill.utils.BillSalaryPrintExcelUtil;
import com.reon.hr.api.bill.utils.CommerceBillPrintExcelUtil;
import com.reon.hr.api.bill.utils.NewBillPrintExcelUtil;
import com.reon.hr.api.bill.vo.bill.BillSalaryPrintParameterVo;
import com.reon.hr.api.bill.vo.bill.BillSalaryPrintVo;
import com.reon.hr.api.bill.vo.bill.CommerceBillPrintVo;
import com.reon.hr.api.bill.vo.perCommerceItemVo;
import com.reon.hr.api.customer.enums.EmployeeReportEnum;
import com.reon.hr.api.customer.enums.billTemplet.BillTempletType;
import com.reon.hr.api.customer.vo.ContractPageVo;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.customer.vo.employee.ehr.BillInfoVo;
import com.reon.hr.api.report.enums.ContractBillEnum;
import com.reon.hr.common.enums.BillEnum;
import com.reon.hr.common.enums.IsMergeEnum;
import com.reon.hr.common.enums.PrintTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static java.util.stream.Collectors.*;

/**
 * 企业账单控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/enterprise/bill")
public class BillController extends BaseController {

    @DubboReference
    private IEpBillWrapperService ehrBillExportConfigWrapperService;

    private static final Logger log = LoggerFactory.getLogger(BillController.class);

    @PreAuthorize("@ss.hasPermi('enterprise:bill:list')")
    @GetMapping("/list")
    public TableDataInfo list(Integer pageNum, Integer pageSize, InsuranceBillVo insuranceBillVo) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("billMonth", insuranceBillVo.getBillMonth());
        params.put("billType", insuranceBillVo.getBillType());
        params.put("invoiceStatus", insuranceBillVo.getInvoiceStatus());
        params.put("customerDtoList", getLoginUser().getUser().getCustomerDtoList());
        params.put("status", BillEnum.BillStatus.LOCK_STATUS.getCode());
        return getDataTable(ehrBillExportConfigWrapperService.getListByParams(pageNum, pageSize, params));
    }

    @PreAuthorize("@ss.hasPermi('enterprise:bill:billExport')")
    @PostMapping("exportBillDynamically")
    public void exportBillDynamically(HttpServletResponse response, EhrBillExportVo ehrBillExportVo) throws IOException {
        Integer configId = ehrBillExportVo.getConfigId();
        List<Integer> collect = Lists.newArrayList();
        if (configId != null) {
            EhrBillExportConfig ehrBillExportConfig = ehrBillExportConfigWrapperService.queryById(configId);
            collect = ehrBillExportConfig.getConfigColumns().stream().map(Integer::valueOf).collect(toList());
        }
        List<Long> templetIdList = ehrBillExportVo.getTempletIdList();
        String contractNo = ehrBillExportVo.getContractNo();
        Integer billMonth = ehrBillExportVo.getBillMonth();
        Integer isMerge = ehrBillExportVo.getIsMerge();
        Integer printType = ehrBillExportVo.getPrintType();
        Long custId = ehrBillExportVo.getCustId();
        Integer sortType = ehrBillExportVo.getSortType();

        StringBuilder sb = new StringBuilder();
        /* 首先获取所有账单模板信息 看是否是同一个合同下有多个类型账单模板 */
        if (CollectionUtils.isEmpty(templetIdList)) {
            log.info("当前没有账单模板,请检查!!");
            sb.append(" 当前没有账单模板");
        }
        if (StringUtils.isBlank(contractNo)) {
            log.info("当前没有选择合同,请检查!!");
            sb.append(" 当前没有选择合同 ");
        }
        if (null == billMonth) {
            log.info("当前没有选择账单月,请检查!!");
            sb.append(" 当前没有选择账单月 ");
        }
        List<BillTempletVo> templetList = ehrBillExportConfigWrapperService.getTempletVoByTempletIdList(templetIdList);
        Set<Long> templetIdSet = ehrBillExportConfigWrapperService.getTempletIdSetByContractAndBillMonth(contractNo, billMonth);
        Map<Integer, List<Long>> templetTypeAndTempletMap = templetList.stream().filter(item -> templetIdSet.contains(item.getId())).collect(groupingBy(BillTempletVo::getTempletType, mapping(BillTempletVo::getId, toList())));
        /* 合同确定 根据账单模板还有账单月可以导出账单 */
        Set<Integer> billTypeSet = templetTypeAndTempletMap.keySet();
        OutputStream out = response.getOutputStream();

        /*
         判断
         如果类型有多种 那么一定打印压缩包
         如果只有一种类型 社保一定打印excel
         工资和商保 要判断是否合并 和是否有多个账单模板
         不合并 有多个账单模板 压缩包
         不合并 一个账单模板 excel
         合并 多个账单模板 excel
         合并 一个账单模板 excel
         */
        boolean zipTip = true;  // 预设为true 打印压缩包不会报错
        if (billTypeSet.isEmpty()) {
            log.info("当前选项没有账单模板,请检查!!");
            sb.append(" 当前选项没有账单");
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
            NewBillPrintExcelUtil.closeInfo(response, sxssfWorkbook, sb.toString());
        } else {
            if (billTypeSet.size() == 1) {
                Set<Long> templetIdListForReMove = templetTypeAndTempletMap.values()
                        .stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());

                if (isMerge.equals(IsMergeEnum.YES.getCode())) {
                    zipTip = false;
                } else {
                    if (templetIdListForReMove.size() <= 1) {
                        zipTip = false;
                    }
                }

                if (billTypeSet.contains(ContractBillEnum.BillTypeEnum.Bill_Type_Social.getIndex())) {
                    zipTip = false;
                }
            }

            if (!zipTip && printType.equals(PrintTypeEnum.COMPRESS_PRINT.getCode())) {
                zipTip = true;
            }

            ZipOutputStream zipOutputStream = null;
            if (zipTip) {
                zipOutputStream = new ZipOutputStream(out);
            }
            // 注意：在代码片段中，我将不同账单类型的处理提取到单独的部分中，以便更好地组织和可读性。这使得代码更易于理解和维护。
            processBillTypes(
                    billTypeSet,
                    templetTypeAndTempletMap,
                    isMerge,
                    custId,
                    billMonth,
                    contractNo,
                    zipTip,
                    out,
                    zipOutputStream,
                    response,
                    sortType,
                    templetList,
                    collect
            );
        }
    }

    /**
     * 分页查询
     *
     * @param ehrBillExportConfig 筛选条件
     * @param page                页面
     * @param limit               限制
     * @return 查询结果
     */
    @PreAuthorize("@ss.hasPermi('enterprise:bill:exportConfigList')")
    @GetMapping("queryByPage")
    public TableDataInfo queryByPage(Integer page, Integer limit, EhrBillExportConfig ehrBillExportConfig) {
        return getDataTable(ehrBillExportConfigWrapperService.queryByPage(page, limit, ehrBillExportConfig));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @PreAuthorize("@ss.hasPermi('enterprise:bill:exportConfigQuery')")
    @GetMapping("{id}")
    public AjaxResult queryById(@PathVariable("id") Integer id) {
        return AjaxResult.success(ehrBillExportConfigWrapperService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param ehrBillExportConfig 实体
     * @return 新增结果
     */
    @PreAuthorize("@ss.hasPermi('enterprise:bill:exportConfigAdd')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EhrBillExportConfig ehrBillExportConfig) {
        if (CollectionUtils.isEmpty(ehrBillExportConfig.getConfigColumns())) {
            return AjaxResult.error("数据为空!");
        }
        return AjaxResult.success(ehrBillExportConfigWrapperService.insert(ehrBillExportConfig));
    }

    /**
     * 编辑数据
     *
     * @param ehrBillExportConfig 实体
     * @return 编辑结果
     */
    @PreAuthorize("@ss.hasPermi('enterprise:bill:exportConfigEdit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EhrBillExportConfig ehrBillExportConfig) {
        return AjaxResult.success(ehrBillExportConfigWrapperService.update(ehrBillExportConfig));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @PreAuthorize("@ss.hasPermi('enterprise:bill:exportConfigDelete')")
    @DeleteMapping("/{id}")
    public AjaxResult deleteById(@PathVariable("id") Integer id) {
        return AjaxResult.success(ehrBillExportConfigWrapperService.deleteById(id));
    }

    /**
     * 处理账单类型
     */
    private void processBillTypes(
            Set<Integer> billTypeSet,
            Map<Integer, List<Long>> templetTypeAndTempletMap,
            Integer isMerge,
            Long custId,
            Integer billMonth,
            String contractNo,
            boolean zipTip,
            OutputStream out,
            ZipOutputStream zipOutputStream,
            HttpServletResponse response,
            Integer sortType,
            List<BillTempletVo> templetList,
            List<Integer> columns
    ) {
        try {
            // Set response headers
            response.setContentType("application/octet-stream");
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Connection", "close");
            response.setCharacterEncoding("UTF-8");

            String zipName = "账单压缩包.zip";
            String encodedZipName = new String(zipName.getBytes("GB2312"), "ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedZipName);
            for (Integer typeOfBill : billTypeSet) {
                List<Long> tempIdList = templetTypeAndTempletMap.get(typeOfBill);

                if (typeOfBill == ContractBillEnum.BillTypeEnum.Bill_Type_Social.getIndex()) {
                    // Handle social bill
                    handleSocialBill(typeOfBill, isMerge, contractNo, tempIdList, billMonth, sortType, zipOutputStream, custId, zipTip, response, templetList, columns);
                } else if (typeOfBill == ContractBillEnum.BillTypeEnum.Bill_Type_Salary.getIndex()) {
                    // Handle salary bill
                    handleSalaryBill(typeOfBill, isMerge, custId, contractNo, tempIdList, billMonth, zipTip, response, zipOutputStream);
                } else if (typeOfBill == ContractBillEnum.BillTypeEnum.Bill_Type_Commercial.getIndex()) {
                    // Handle commercial bill
                    handleCommercialBill(typeOfBill, isMerge, contractNo, tempIdList, billMonth, custId, zipTip, response, zipOutputStream);
                } else if (typeOfBill == ContractBillEnum.BillTypeEnum.DISPOSE_INSURANCE.getIndex()) {
                    // Handle dispose insurance bill
                    handleDisposeInsuranceBill(typeOfBill, contractNo, tempIdList, billMonth, custId, zipTip, response, zipOutputStream, isMerge);
                }
            }
        } catch (Exception e) {
            log.error("账单打印错误", e);
        } finally {
            try {
                if (zipTip) {
                    zipOutputStream.close();
                }
                out.close();
            } catch (IOException e) {
                log.error("账单打印错误", e);
            }
        }
    }

    /**
     * 处理社保账单
     */
    private void handleSocialBill(
            Integer typeOfBill,
            Integer isMerge,
            String contractNo,
            List<Long> tempIdList,
            Integer billMonth,
            Integer sortType,
            ZipOutputStream zipOutputStream,
            Long custId,
            boolean zipTip,
            HttpServletResponse response,
            List<BillTempletVo> templetList,
            List<Integer> columns
    ) throws IOException {
        // ... logic for handling social bill
        Map<Integer, Object> billPrintDtoMap = ehrBillExportConfigWrapperService.getBillPrintDataNewForAll(
                contractNo,
                tempIdList,
                billMonth,
                sortType,
                isMerge
        );
        Object billPrintObject = billPrintDtoMap.get(isMerge);
        SXSSFWorkbook billPrintWorkBookForSocial;
        if (isMerge != null && isMerge.equals(IsMergeEnum.YES.getCode())) {
            BillPrintDto reportDto = (BillPrintDto) billPrintObject;
            String templetNameJoin = "账单模板名称";
            if (CollectionUtils.isNotEmpty(templetList)) {
                templetNameJoin = templetList.stream()
                        .map(BillTempletVo::getTempletName)
                        .collect(Collectors.joining("-"));
            }
            reportDto.setTempletName(templetNameJoin);
            billPrintWorkBookForSocial = NewBillPrintExcelUtil.getBillPrintWorkBook(reportDto);
        } else {
            List<BillPrintDto> resultList = (List<BillPrintDto>) billPrintObject;
            billPrintWorkBookForSocial = NewBillPrintExcelUtil.getBillPrintWorkBookByList(resultList, columns);
        }
        String fileName = getFileNameForXlsx(custId, billMonth, contractNo, typeOfBill);
        if (zipTip) {
            flushFile(zipOutputStream, billPrintWorkBookForSocial, fileName);
        } else {
            NewBillPrintExcelUtil.closeInfo(response, billPrintWorkBookForSocial, fileName);
        }
    }

    /**
     * 处理工资账单
     */
    private void handleSalaryBill(
            Integer typeOfBill,
            Integer isMerge,
            Long custId,
            String contractNo,
            List<Long> tempIdList,
            Integer billMonth,
            boolean zipTip,
            HttpServletResponse response,
            ZipOutputStream zipOutputStream
    ) throws Exception {
        // ... logic for handling salary bill
        CustomerVo customerVo = ehrBillExportConfigWrapperService.findById(custId);
        ContractPageVo contractData = ehrBillExportConfigWrapperService.getDataByContractNo(contractNo);

        String[] sheets = {"付款通知书", "薪资明细"};
        int tip = 1;

        for (Long templetId : tempIdList) {
            SXSSFWorkbook wb = new SXSSFWorkbook();
            BillSalaryPrintParameterVo billSalaryPrintParameterVo = new BillSalaryPrintParameterVo();
            billSalaryPrintParameterVo
                    .setTempletId(templetId)
                    .setBillMonth(billMonth)
                    .setCustId(custId)
                    .setContractNo(contractNo)
                    .setCustName(customerVo.getCustName())
                    .setContractType(contractData.getContractType());

            BillSalaryPrintVo billSalaryPrintData = (BillSalaryPrintVo) ehrBillExportConfigWrapperService.getBillSalaryPrintData(billSalaryPrintParameterVo);

            for (String sheet : sheets) {
                BillSalaryPrintExcelUtil.export(
                        billSalaryPrintData,
                        billSalaryPrintParameterVo,
                        sheet + tip,
                        wb,
                        contractData.getContractType()
                );
            }

            String filePath = getFileNameForXlsx(custId, billMonth, contractNo, typeOfBill);
            if (isMerge != null && isMerge.equals(IsMergeEnum.YES.getCode())) {
                tip++;
                if (zipTip) {
                    flushFile(zipOutputStream, wb, filePath);
                } else {
                    NewBillPrintExcelUtil.closeInfo(response, wb, filePath);
                }
            } else {
                filePath = filePath.split(".xlsx")[0] + tip++ + ".xlsx";
                if (zipTip) {
                    flushFile(zipOutputStream, wb, filePath);
                } else {
                    NewBillPrintExcelUtil.closeInfo(response, wb, filePath);
                }
            }
        }
    }

    /**
     * 处理商业账单
     */
    private void handleCommercialBill(
            Integer typeOfBill,
            Integer isMerge,
            String contractNo,
            List<Long> tempIdList,
            Integer billMonth,
            Long custId,
            boolean zipTip,
            HttpServletResponse response,
            ZipOutputStream zipOutputStream
    ) throws IOException {
        // ... logic for handling commercial bill
        String filePath = getFileNameForXlsx(custId, billMonth, contractNo, typeOfBill);
        int tip = 1;

        for (Long templetId : tempIdList) {
            CommerceBillPrintVo printVo = new CommerceBillPrintVo();
            printVo.setBillMonth(billMonth)
                    .setTempletId(templetId)
                    .setContractNo(contractNo)
                    .setCustId(custId);

            List<CommerceBillPrintVo> commerceBillPrintVoOne = ehrBillExportConfigWrapperService.getCommerceBillPrint(printVo);
            List<CommerceBillPrintVo> commerceBillPrintVos = new ArrayList<>();

            if (isMerge != null && isMerge.equals(IsMergeEnum.YES.getCode())) {
                commerceBillPrintVos.addAll(commerceBillPrintVoOne);
            } else {
                for (CommerceBillPrintVo commerceBillPrintVo : commerceBillPrintVoOne) {
                    CommerceBillPrintVo commerceBillPrintVo1 = new CommerceBillPrintVo();
                    BeanUtils.copyProperties(commerceBillPrintVo, commerceBillPrintVo1);
                    List<perCommerceItemVo> collect = commerceBillPrintVo.getPerCommerceItemVos()
                            .stream()
                            .filter(x -> x.getPrice().compareTo(BigDecimal.ZERO) != 0)
                            .collect(toList());
                    commerceBillPrintVo1.setPerCommerceItemVos(collect);
                    commerceBillPrintVos.add(commerceBillPrintVo1);
                }
            }

            SXSSFWorkbook wb = new SXSSFWorkbook();
            CommerceBillPrintExcelUtil.exportContractBill(wb, commerceBillPrintVos);

            if (isMerge == null || isMerge.equals(IsMergeEnum.NO.getCode())) {
                filePath = filePath.split(".xlsx")[0] + (tip++) + ".xlsx";
            }

            if (zipTip) {
                flushFile(zipOutputStream, wb, filePath);
            } else {
                NewBillPrintExcelUtil.closeInfo(response, wb, filePath);
            }
        }
    }

    /**
     * 处理一次性账单
     */
    private void handleDisposeInsuranceBill(
            Integer typeOfBill,
            String contractNo,
            List<Long> tempIdList,
            Integer billMonth,
            Long custId,
            boolean zipTip,
            HttpServletResponse response,
            ZipOutputStream zipOutputStream,
            Integer isMerge) throws IOException {
        // ... logic for handling dispose insurance bill
        List<BillPrintDto> reportDtoList = ehrBillExportConfigWrapperService.getDisposeReportData(contractNo, tempIdList, billMonth);
        int tip = 1;

        for (BillPrintDto billPrintDto : reportDtoList) {
            SXSSFWorkbook billPrintWorkBookForSocial = NewBillPrintExcelUtil.getBillPrintWorkBook(billPrintDto);
            String fileName = getFileNameForXlsx(custId, billMonth, contractNo, typeOfBill);

            if (isMerge != null && isMerge.equals(IsMergeEnum.YES.getCode())) {
                if (tip == 1) {
                    fileName = getFileNameForXlsx(custId, billMonth, contractNo, typeOfBill);
                    if (zipTip) {
                        flushFile(zipOutputStream, billPrintWorkBookForSocial, fileName);
                    } else {
                        NewBillPrintExcelUtil.closeInfo(response, billPrintWorkBookForSocial, fileName);
                    }
                }
            } else {
                fileName = fileName.split(".xlsx")[0] + (tip++) + ".xlsx";
                if (zipTip) {
                    flushFile(zipOutputStream, billPrintWorkBookForSocial, fileName);
                } else {
                    NewBillPrintExcelUtil.closeInfo(response, billPrintWorkBookForSocial, fileName);
                }
            }
        }
    }

    private String getFileNameForXlsx(Long custId, Integer billMonth, String contractNo, int templetType) {
        String fileName; /* 客户名称 合同类型 账单年月 账单类型 */
        CustomerVo customerVo = ehrBillExportConfigWrapperService.findById(custId);
        String custName = customerVo.getCustName();
        ContractPageVo contractData = ehrBillExportConfigWrapperService.getContractData(contractNo);
        Integer contractType = contractData.getContractType();
        String contractTypeName = EmployeeReportEnum.ContractTypeEnum.getName(contractType);
        String templetTypeName = BillTempletType.getNameByCode(templetType);
        fileName = custName + "_" + contractTypeName + "_" + billMonth + "_" + templetTypeName + ".xlsx";
        return fileName;
    }

    private void flushFile(ZipOutputStream zipOutputStream, SXSSFWorkbook sxssfWorkbook, String fileName) throws IOException {
        zipOutputStream.putNextEntry(new ZipEntry(fileName));
        ByteArrayOutputStream byteArrayOutputStreamForSocial = new ByteArrayOutputStream();
        sxssfWorkbook.write(byteArrayOutputStreamForSocial);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStreamForSocial.toByteArray());
        int len;
        byte[] buffer = new byte[1024];
        while ((len = byteArrayInputStream.read(buffer)) != -1) {
            zipOutputStream.write(buffer, 0, len);
        }
        zipOutputStream.flush();
        zipOutputStream.closeEntry();
        byteArrayOutputStreamForSocial.close();
        byteArrayInputStream.close();
    }

    /**
     * 获取账单信息List
     */
    @GetMapping("/getBillInfoList")
    public AjaxResult getBillInfoList(BillInfoVo vo)
    {
        SysUserVo userInfo = getLoginUser().getUser();
        vo.setCustomerDtoList(userInfo.getDefaultCustomerDtoList());
        return AjaxResult.success(ehrBillExportConfigWrapperService.getBillInfoList(vo));
    }
    /**
     * 获取人员数量信息List
     */
    @GetMapping("/getBillInfoVo")
    public AjaxResult getBillInfoVo(BillInfoVo vo)
    {
        SysUserVo userInfo = getLoginUser().getUser();
        vo.setCustomerDtoList(userInfo.getDefaultCustomerDtoList());
        return AjaxResult.success(ehrBillExportConfigWrapperService.getBillInfoVo(vo));
    }
}
