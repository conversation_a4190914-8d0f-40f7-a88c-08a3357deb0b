package com.reon.hr.sp.report.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "contract_bill")
public class ContractBill {
    /**
     * 主键id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private long id;
    /**
     * 合同编号
     */
    @TableField(value = "contract_no")
    private String contractNo;
    /**
     * 合同名称
     */
    @TableField(value = "contract_name")
    private String contractName;
    /**
     * 合同起始日
     */
    @TableField(value = "start_date")
    private Date startDate;
    /**
     * 合同到期日
     */
    @TableField(value = "end_date")
    private Date endDate;
    /**
     * 合同类型
     */
    @TableField(value = "contract_type")
    private Integer contractType;
    /**
     * 客户编号
     */
    @TableField(value = "cust_no")
    private String custNo;
    /**
     * 客户名称
     */
    @TableField(value = "cust_name")
    private String custName;
    /**
     * 报价单编号
     */
    @TableField(value = "quotation_no_list")
    private String quotationNoList;
    /**
     * 签单城市
     */
    @TableField(value = "sign_place")
    private Integer signPlace;
    /**
     * 销售
     */
    @TableField(value = "seller")
    private String seller;
    /**
     * 客服
     */
    @TableField(value = "commissioner")
    private String commissioner;
    /**
     * 账单月
     */
    @TableField(value = "bill_month")
    private Integer billMonth;
    /**
     * 账单类型
     */
    @TableField(value = "bill_type")
    private Integer billType;
    /**
     * 服务费
     */
    @TableField(value = "fee")
    private BigDecimal fee;
    /**
     * 总金额
     */
    @TableField(value = "amt")
    private BigDecimal amt;
    /**
     * 账单人数
     */
    @TableField(value = "people_num")
    private Integer peopleNum;
    /**
     * 实际账单锁定日
     */
    @TableField(value = "act_lock_time")
    private Date actLockTime;
    /**
     * 约定到款时间
     */
    @TableField(value = "pay_date")
    private Integer payDate;
    /**
     * 到款所属月
     */
    @TableField(value = "income_month_type")
    private Integer incomeMonthType;
    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;
    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
