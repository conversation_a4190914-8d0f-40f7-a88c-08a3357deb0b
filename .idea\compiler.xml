<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../../../java/gradle-8.0.2/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.26/8f8cf0372abf564913e9796623aac4c8ea44025a/lombok-1.18.26.jar" />
        </processorPath>
        <module name="reon.reon-base-sp.main" />
        <module name="reon.reon-ehr-sp.main" />
        <module name="reon.reon-admin-web.main" />
        <module name="reon.reon-change-base-api.main" />
        <module name="reon.reon-file-api.main" />
        <module name="reon.reon-file-sp.main" />
        <module name="reon.reon-thirdpart-common.main" />
        <module name="reon.reon-report-api.main" />
        <module name="reon.reon-bill-sp.main" />
        <module name="reon.reon-base-api.main" />
        <module name="reon.reon-admin-sp.main" />
        <module name="reon.reon-customer-sp.main" />
        <module name="reon.reon-admin-api.main" />
        <module name="reon.reon-report-sp.main" />
        <module name="reon.reon-thirdpart-api.main" />
        <module name="reon.reon-workflow-sp.main" />
        <module name="reon.reon-thirdpart-gateway.main" />
        <module name="reon.reon-workflow-api.main" />
        <module name="reon.reon-common-utils.main" />
        <module name="reon.reon-customer-api.main" />
        <module name="reon.reon-rabbitmq-component.main" />
        <module name="reon.reon-thirdpart-sp.main" />
        <module name="reon.reon-ehr-api.main" />
        <module name="reon.reon-bill-api.main" />
        <module name="reon.reon-change-base-sp.main" />
      </profile>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../../../java/gradle-8.0.2/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.26/8f8cf0372abf564913e9796623aac4c8ea44025a/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../../../java/gradle-8.0.2/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-processor/2.7.10/2efce9b613cf87d0b5669db136950be15f8da7b9/spring-boot-configuration-processor-2.7.10.jar" />
        </processorPath>
        <module name="reon.reon-ehr-gateway.main" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="1.8" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="reon.reon-ehr-gateway" options="-parameters" />
      <module name="reon.reon-ehr-gateway.main" options="-parameters" />
      <module name="reon.reon-ehr-gateway.test" options="-parameters" />
      <module name="reon.reon-thirdpart-gateway" options="-parameters" />
      <module name="reon.reon-thirdpart-gateway.main" options="-parameters" />
      <module name="reon.reon-thirdpart-gateway.test" options="-parameters" />
    </option>
  </component>
</project>