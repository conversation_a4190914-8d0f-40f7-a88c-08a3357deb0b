package com.reon.hr.sp.report.service.impl;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.reon.hr.sp.report.dao.report.ServiceNumPeopleReportMapper;
import com.reon.hr.sp.report.entity.ServiceNumPeopleReport;
import com.reon.hr.sp.report.service.report.ServiceNumPeopleReportService;

import java.util.List;
import java.util.Map;

@Service
public class ServiceNumPeopleReportServiceImpl implements ServiceNumPeopleReportService {

    @Resource
    private ServiceNumPeopleReportMapper serviceNumPeopleReportMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return serviceNumPeopleReportMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ServiceNumPeopleReport record) {
        return serviceNumPeopleReportMapper.insert(record);
    }

    @Override
    public int insertSelective(ServiceNumPeopleReport record) {
        return serviceNumPeopleReportMapper.insertSelective(record);
    }

    @Override
    public ServiceNumPeopleReport selectByPrimaryKey(Long id) {
        return serviceNumPeopleReportMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ServiceNumPeopleReport record) {
        return serviceNumPeopleReportMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ServiceNumPeopleReport record) {
        return serviceNumPeopleReportMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ServiceNumPeopleReport> list) {
        return serviceNumPeopleReportMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<ServiceNumPeopleReport> list) {
        return serviceNumPeopleReportMapper.batchInsert(list);
    }

    @Override
    public List<ServiceNumPeopleReport> getDataByYearMonth(Integer yearMonthS, Integer yearMonthE, Map<String, Object> conditonMap) {
        return serviceNumPeopleReportMapper.getDataByYearMonth(yearMonthS,yearMonthE,conditonMap);
    }

    @Override
    public Integer deleteCurMonthDataByMonth(Integer currYearMonth) {
        return serviceNumPeopleReportMapper.deleteCurMonthDataByMonth(currYearMonth);
    }
}

