package com.reon.hr.sp.customer.service.impl.employee;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.ehr.api.sys.dubbo.service.rpc.IEpEmployeeOrderWrapperService;
import com.reon.ehr.api.sys.enums.order.ServiceNature;
import com.reon.ehr.api.sys.enums.order.StaffingState;
import com.reon.ehr.api.sys.vo.employee.EhrEmpRelativeFileVo;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeOrderVo;
import com.reon.hr.api.base.dto.sys.ServiceSiteCfgDto;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.enums.SocialSecurityFundEnum;
import com.reon.hr.api.base.utils.ListPageUtil;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.enums.CommonBooleanTypeEnum;
import com.reon.hr.api.customer.dto.EmployeeChangeReportExportDto;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.QuotationDTO;
import com.reon.hr.api.customer.dto.employee.EmployeeOrderDto;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;
import com.reon.hr.api.customer.dto.exportData.ServiceNumPeopleReportDto;
import com.reon.hr.api.customer.dto.qys.QysEmpConCusAndEedDto;
import com.reon.hr.api.customer.dto.report.EmployeeReportDto;
import com.reon.hr.api.customer.dubbo.service.rpc.IBillTempletWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.CustomerGroupWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IInsurancePracticeWrapperService;
import com.reon.hr.api.customer.enums.*;
import com.reon.hr.api.customer.enums.contract.ContractAreaRecceivingType;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderStatus;
import com.reon.hr.api.customer.exception.OrderDimissionException;
import com.reon.hr.api.customer.exception.OrderException;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.utils.ServiceMonthUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletFeeCfgVo;
import com.reon.hr.api.customer.vo.employee.RatioBaseVo;
import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.report.vo.ServiceNumPeopleReportVo;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.util.ListUtils;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.VoUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.sp.customer.dao.cus.ContractRelativeQuotationMapper;
import com.reon.hr.sp.customer.dao.employee.*;
import com.reon.hr.sp.customer.entity.cus.Contract;
import com.reon.hr.sp.customer.entity.cus.Customer;
import com.reon.hr.sp.customer.entity.cus.SocialSysMandatoryInfo;
import com.reon.hr.sp.customer.entity.employee.EmployeeContract;
import com.reon.hr.sp.customer.entity.employee.*;
import com.reon.hr.sp.customer.service.GeneratorSequenceService;
import com.reon.hr.sp.customer.service.cus.*;
import com.reon.hr.sp.customer.service.employee.*;
import com.reon.hr.sp.customer.service.supplierPractice.ISupplierPracticeService;
import com.reon.hr.sp.customer.util.SequenceUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@Service
public class EmployeeOrderServiceImpl implements IEmployeeOrderService {

    @Autowired
    private IInsuranceBaseWrapperService baseWrapperService;
    @Autowired
    private IBillTempletFeeCfgService feeCfgService;
    @Autowired
    private IInsuranceRatioWrapperService ratioWrapperService;
    @Autowired
    ISupplierWrapperService iSupplierWrapperService;
    @Autowired
    IUserWrapperService iUserWrapperService;
    @Autowired
    IAreaResourceWrapperService iAreaResourceWrapperService;
    @Autowired
    private EmployeeOrderMapper employeeOrderMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private EmployeeContractMapper employeeContractMapper;
    @Autowired
    private OrderServiceChargeMapper chargeMapper;
    @Autowired
    private IBillTempletService templetService;
    @Autowired
    private OrderInsuranceCfgMapper insuanceCfgMapper;
    @Autowired
    private EmployeeEntryDimissionMapper entryDimissionMapper;
    @Autowired
    private EmployeeRemarkMapper remarkMapper;
    @Autowired
    private EmployeeOrderLogMapper orderLogMapper;
    @Autowired
    private GeneratorSequenceService generatorSequenceService;
    @Autowired
    private IEmployeeOrderLogService employeeOrderLogService;
    @Autowired
    private IOrgnizationResourceWrapperService orgnizationWrapperService;
    @Autowired
    private IIndCategoryWrapperService categoryWrapperService;
    @Autowired
    private ISupplierService supplierService;
    @Autowired
    private IInsurancePracticeWrapperService iInsurancePracticeWrapperService;
    @Autowired
    private ISequenceService iSequenceService;
    @Autowired
    private IOrderServiceChargeService serviceChargeService;
    @Autowired
    private EmployeeOrderLogMapper employeeOrderLogMapper;
    @Autowired
    private PersonOrderEditMapper employeeOrderChangeMapper;
    @Autowired
    private ContractService contractService;
    @Autowired
    private ContractAreaService contractAreaService;
    @Autowired
    private IUserWrapperService userWrapperService;
    @Autowired
    private IinsuranceGroupWrapperService groupWrapperService;
    @Autowired
    private IDictionaryWrapperService dictionaryService;
    @Autowired
    private MqMessageSender mqMessageSender;
    @Autowired
    private OrderOneChargeMapper orderOneChargeMapper;
    @Autowired
    private IOrderInsuranceCfgService insuranceCfgService;
    @Autowired
    private ContractRelativeQuotationMapper contractRelativeQuotationMapper;
    @Autowired
    private IBillTempletWrapperService templetWrapperService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private IOrgnizationResourceWrapperService organizationService;
    @Autowired
    private IEpEmployeeOrderWrapperService epEmployeeOrderWrapperService;

    @Autowired
    private OrderInsuranceCfgMapper orderInsuanceCfgMapper;

    @Autowired
    private OrderContractFileMapper orderContractFileMapper;

    @Autowired
    private ISupplierPracticeService iSupplierPracticeService;

    @Resource(name = "serviceSiteDubboCfgService")
    private IServiceSiteCfgWrapperService serviceSiteCfgService;

    @Autowired
    private IEmployeeTransferService iEmployeeTransferService;

    @Autowired
    private IMessageWrapperService iMessageWrapperService;

    @Autowired
    private PersonOrderQueryMapper personOrderQueryMapper;

    @Autowired
    private QuotationService quotationService;
    @Autowired
    private EmpRelativeFileMapper empRelativeFileMapper;

    @Autowired
    private ChangeTempletFeeIdSublistMapper changeTempletFeeIdSublistMapper;


    private static final String EMPLOYEE_PREFIX = "GY";

    private static final String EMPLOYEE_ORDER_PREFIX = "YD-";

    private static final String EMPLOYEE_CONTRACT_PREFIX = "YH-";

    private static final int FLOWCODE_LENGTH = 6;


    private Logger log = LoggerFactory.getLogger(getClass());


    @Override
    public String getOrderNo(String areaNo, String employeeId) {
        return employeeOrderMapper.getOrderNo(areaNo, employeeId);
    }

    @Override
    public Page<EmployeeOrderViewVo> getListPage(Integer page, Integer limit, EmployeeOrderVo orderVo) {
        Page<EmployeeOrderViewVo> employeeOrderPage = new Page<EmployeeOrderViewVo>(page, limit);
        List<EmployeeOrderViewVo> record = employeeOrderMapper.getListPage(employeeOrderPage, orderVo);
        employeeOrderPage.setRecords(record);
        return employeeOrderPage;
    }

    @Override
    public EmployeeOrderVo getCommissionerAndReceivingManByOrderNo(String orderNo) {
        return employeeOrderMapper.getCommissionerAndReceivingManByOrderNo(orderNo);
    }

    @Override
    public Long selectCountByOrderNo(String orderNo) {
        return employeeOrderMapper.selectCountByOrderNo(orderNo);
    }

    @Override
    public List<EmployeeOrderVo> getRemark1() {
        return employeeOrderMapper.getRemark1();
    }

    @Override
    public EmployeeOrderVo getOneById(String... orderNos) {
//        EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo);
        String orderNo = orderNos[0];
        EmployeeOrderVo orderVo = selectEmployeeOrder(orderNos);
        List<OrderContractFileVo> agentContractFileByOrderNoList = orderContractFileMapper.getFileIdsByOrderNo(orderNo);
        List<String> uploadContFileIdList = agentContractFileByOrderNoList.stream().map(OrderContractFileVo::getFileId).collect(Collectors.toList());
        String fileIds = uploadContFileIdList.stream().collect(Collectors.joining(","));
        orderVo.setFileId(fileIds);
        //入职、离职、变更过程
        getEmployeeOrderVo(orderNo, orderVo);
        ServiceSiteCfgDto serviceSiteCfgDto = new ServiceSiteCfgDto(Integer.valueOf(orderVo.getCityCode()), orderVo.getReceiving());
        //只有一个
        List<ServiceSiteCfgDto> serviceSiteCfgDtoList = serviceSiteCfgService.getInsurAddDay(Collections.singletonList(serviceSiteCfgDto));
        if (serviceSiteCfgDtoList.size() > 0) {
            serviceSiteCfgDto = serviceSiteCfgDtoList.get(0);
            transformMsg(serviceSiteCfgDto);
            orderVo.setServiceSiteCfgDto(serviceSiteCfgDto);

        }
        setTransferFlag(orderVo);
        return orderVo;
    }

    @Override
    public List<EmployeeOrderVo> getOneByOrderNo(String orderNo, String certNo, String receivingName, String custNo) {
        return employeeOrderMapper.getOneByOrderNo(orderNo, certNo, receivingName, custNo, DateUtil.getCurrYearMonth());
    }

    @Override
    public List<EmployeeOrderVo> getEmpDataForAdvancePayment(List<String> areaNoList) {
        return employeeOrderMapper.getEmpDataForAdvancePayment(areaNoList);
    }

    @Override
    public EmployeeOrderVo getOneConfirmGetOneById(String orderNo) {
        Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
        EmployeeOrderVo orderVo = employeeOrderMapper.getOneConfirmGetOneById(orderNo, DateUtil.getCurrYearMonth());
        String remark = null;
        if (StringUtils.isNotBlank(orderVo.getDimissionRemark())) {
            remark = orderVo.getDimissionRemark();
        }
        EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(
                orderNo,
                EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
        String chgContent = employeeOrderChange.getChgContent();
        EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
        EmployeeEntryDimission newEmployeeEntryDimission = empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
        orderVo.setReduceReason(newEmployeeEntryDimission.getReduceReason());
        orderVo.setReduceDetailReason(newEmployeeEntryDimission.getReduceDetailReason());
        orderVo.setDimissionReason(newEmployeeEntryDimission.getDimissionReason());
        orderVo.setDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getDimissionDate(), "yyyy-MM-dd"));
        orderVo.setDimissionRemark(newEmployeeEntryDimission.getDimissionRemark());
        orderVo.setApplyDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getApplyDimissionDate(), "yyyy-MM-dd"));
        orderVo.setLeaveCallFlag(newEmployeeEntryDimission.getCallFlag());
        orderVo.setDimissionRemark(remark);
        String name = allUserMap.get(newEmployeeEntryDimission.getApplyDimissionMan());
        if (name == null) {
            name = newEmployeeEntryDimission.getApplyDimissionMan();
        }
        orderVo.setApplyDimissionMan(name);
        getEmployeeOrderVo(orderNo, orderVo);
        // 获取服务网点 截点
        ServiceSiteCfgDto serviceSiteCfgDto = new ServiceSiteCfgDto(Integer.valueOf(orderVo.getCityCode()), orderVo.getReceiving());
        //只有一个
        List<ServiceSiteCfgDto> serviceSiteCfgDtoList = serviceSiteCfgService.getInsurAddDay(Collections.singletonList(serviceSiteCfgDto));
        if (CollectionUtils.isNotEmpty(serviceSiteCfgDtoList)) {
            serviceSiteCfgDto = serviceSiteCfgDtoList.get(0);
            transformMsg(serviceSiteCfgDto);
            orderVo.setServiceSiteCfgDto(serviceSiteCfgDto);
        }
        return orderVo;
    }

    /**
     转化字典值
     */
    private void transformMsg(ServiceSiteCfgDto serviceSiteCfgDto) {
        serviceSiteCfgDto.setApplyInsurMsg(SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(serviceSiteCfgDto.getApplyInsurFreq()));
        serviceSiteCfgDto.setApplyFundMsg(SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(serviceSiteCfgDto.getApplyFundFreq()));
    }

    private void getEmployeeOrderVo(String orderNo, EmployeeOrderVo orderVo) {
        //入职、离职、变更过程
        String entryProcess = employeeOrderLogService.getListByOrderNoAndOptType(orderNo, PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
        orderVo.setEntryProcess(entryProcess);
        String dimissionProcess = employeeOrderLogService.getListByOrderNoAndOptType(orderNo, PersonOrderEnum.OrderLogOprType.DIMISSION.getCode());
        orderVo.setDimissionProcess(dimissionProcess);
        String changeProcess = employeeOrderLogService.getListByOrderNoAndOptType(orderNo, Lists.newArrayList(PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), PersonOrderEnum.OrderLogOprType.SSMI_EDIT.getCode()));
        orderVo.setChangeProcess(changeProcess);
        String modifyRemark = employeeOrderLogService.getListByOrderNoAndOptType(orderNo, PersonOrderEnum.OrderLogOprType.MODIFY_REMARK.getCode());
        orderVo.setModifyRemark(modifyRemark);
        String dimissionRemark = employeeOrderLogService.getListByOrderNoAndOptType(orderNo, PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode());
        if (StringUtils.isBlank(dimissionRemark)) {
            dimissionRemark = orderVo.getDimissionRemark();
        }
        orderVo.setDimissionRemark(dimissionRemark);
        OrgVo orgVo = orgnizationWrapperService.findOrgById(orderVo.getDistCom());
        if (orgVo != null) {
            orderVo.setDistComName(orgVo.getOrgName());
        }
        OrgVo orgVo1 = orgnizationWrapperService.findOrgById(orderVo.getSignDistCom());
        if (orgVo1 != null) {
            orderVo.setSignDistComName(orgVo1.getOrgName());
        }
        IndCategoryVo categoryVo = categoryWrapperService.findOne(orderVo.getCategoryCode());
        if (categoryVo != null) {
            orderVo.setIndTypeCode(categoryVo.getIndTypeCode());
            orderVo.setCityCode(categoryVo.getCityCode().toString());
        }
        List<OneTimeCharge> oneTimeChargeList = orderOneChargeMapper.getOneTimeChargeByOrderNo(orderNo);
        orderVo.setOneTimeChargVos(oneTimeChargeList);
    }

    @Override
    public String findCertNameByCertNo(String certNo) {
        return employeeMapper.findCertNameByCertNo(certNo);
    }

    @Autowired
    private SocialSysMandatoryInfoService socialSysMandatoryInfoService;

    @Override
    public void saveOrUpdate(EmployeeOrderVo orderVo) {

        if (StringUtils.isNotBlank(orderVo.getFileId()) && StringUtils.isNotBlank(orderVo.getOrderNo())) {
            List<String> fileIdList = Arrays.asList(orderVo.getFileId().split(","));
            for (String fileId : fileIdList) {
                OrderContractFileVo orderContractFileVo = new OrderContractFileVo();
                orderContractFileVo.setFileId(fileId);
                orderContractFileVo.setCreator(orderVo.getUpdater());
                orderContractFileVo.setFileType(OrderContractFileEnum.RENEW_CONTRACT.getCode());
                orderContractFileMapper.insertAgentContractFile(orderContractFileVo);
            }
        }
        //orderInsurance  --> List
        if (CollectionUtils.isNotEmpty(orderVo.getInsurances())) {
            for (int i = 0; i < orderVo.getInsurances().size(); i++) {
                OrderInsuranceCfgVo orderInsuranceVo = orderVo.getInsurances().get(i);
                if (orderInsuranceVo != null && StringUtils.isNotEmpty(orderInsuranceVo.getGroupCode()) && StringUtils.isNotEmpty(orderInsuranceVo.getRatioCode())) {
                    //according to 社保组code 和 社保比例code  得到社保组比例
                    InsuranceGroupRatioVo groupRatioVo = groupWrapperService.getOneByParams(orderInsuranceVo.getGroupCode(), null, orderInsuranceVo.getRatioCode());
                    //设置 社保组比例
                    //add guoqian 精度丢失问题，需要重新计算
                    getSocialSecurityRatioBaseAmount(orderInsuranceVo, groupRatioVo);
                    //精度丢失问题
                    orderInsuranceVo.setGroupRatioId(groupRatioVo.getId());
                }
            }
        }

        ////完善订单中的挂起
        if (orderVo.getOptType().equalsIgnoreCase("completeUpdate") && orderVo.getOrderStatus() == EmployeeOrderStatus.SUSPEND.getCode()) {
            //获取所有的挂起原因
            List<DictVo> dictVos = dictionaryService.findAllDictList().get("ORDER_SUSPEND_REASON");
            // list --> map
            Map<Integer, String> dicMap = dictVos.stream().collect(toMap(DictVo::getCode, DictVo::getName));
            if (orderVo.getSuspendReason() != null) {
                //得到暂停的原因
                orderVo.setSuspendReasonStr(dicMap.get(orderVo.getSuspendReason()));
            }
        }

        String currentDate = DateUtil.getString(new Date(), "yyyyMMdd");
        EmployeeOrderDto orderDto = orderVoToOrderDto(orderVo);


        if ("add".equalsIgnoreCase(orderVo.getOptType())) {

            EhrEmployeeOrderVo oldEhrEmployeeOrderVo = new EhrEmployeeOrderVo();
            if (orderVo.getEhrEmployeeOrderId() != null) {
                oldEhrEmployeeOrderVo = epEmployeeOrderWrapperService.selectOrderById(orderVo.getEhrEmployeeOrderId());
                StaffingState staffingStateByCode = StaffingState.getStaffingStateByCode(oldEhrEmployeeOrderVo.getStaffingState());
                switch (Objects.requireNonNull(staffingStateByCode)) {
                    case CANCELLATION_OF_ADDITIONAL_PERSONNEL:
                        throw new OrderException("该员工增员已被企业端取消");
                    case THE_INCREASE_HAS_BEEN_ACCEPTED:
                        throw new OrderException("该员工已增员受理");
                }
            }
            // 根据身份证号判断当前小合同中是否已经存在该员工
            int certNoCount = employeeOrderMapper.getCountByContractAreaAndCertNo(orderVo.getContractAreaNo(), orderVo.getCertNo());
            if (certNoCount >= 1) {
                throw new OrderException("该小合同中已经存在此员工");
            }
            // 判断合同是否存在
//            orderDto = setUserName(orderDto);
            Employee employee = new Employee();
            BeanUtils.copyProperties(orderDto, employee);
            Contract contract = contractService.findbyContractNo(orderVo.getContractNo());
            if (contract == null) {
                log.error("该合同不存在或已被删除");
            }

            ContractAreaVo contractAreaVo = contractAreaService.fingByContractAreaNo(orderVo.getContractAreaNo());
            // 生成员工empNo流水号
            Long employeeNum = generatorSequenceService.getIncrementNum("get_employee_next_sequence_" + "date_" + currentDate);
            String employeeFlowCode = SequenceUtils.getSequence(employeeNum, FLOWCODE_LENGTH);
            employee.setEmployeeNo(EMPLOYEE_PREFIX + currentDate + employeeFlowCode);
            employee.setName(orderDto.getEmployeeName());
            /**
             * 2021/8/27
             * 调整增员逻辑:如果在一个客户下存在当前插入员工(用身份证来判断),则直接放弃在员工表里新插入该员工,
             *              直接拿身份证对应的员工id进行后续其他表的插入操作
             *      修改处理逻辑，雇员信息表中证件号唯一
             */
            Employee emp = employeeOrderMapper.getEmployeeByCertNo(orderVo.getCertNo());
            if (emp == null) {
                //存入员工
                if (employee.getName() != null && !employee.getName().matches("^[a-zA-Z0-9 ]*$"))
                    employee.setName(employee.getName().replace(" ", "").replace("\t", ""));
                employeeMapper.insertSelective(employee);
            } else {
                if (emp.getDelFlag().equals(DEL_FLAG_Y)) {
                    employeeMapper.updateDelFlagById(emp.getId(), DEL_FLAG_N);
                }
                if (employee.getName().trim().equals(emp.getName().trim())) {
                    employee.setId(emp.getId());
                    /** 当身份证 身份证类型 姓名相同时更新 */
                    int count = employeeMapper.updateEmployeeByCertNoAndCertTypeAndEmpName(employee);
                } else {
                    throw new OrderException("该证件号已存在,姓名不符");
                }
            }
            EmployeeRemark remark = new EmployeeRemark();
            BeanUtils.copyProperties(orderDto, remark);
            if (remark != null && (StringUtils.isNotBlank(remark.getRemark1()) || StringUtils.isNotBlank(remark.getRemark2()) ||
                    StringUtils.isNotBlank(remark.getRemark3()) || StringUtils.isNotBlank(remark.getRemark4()) ||
                    StringUtils.isNotBlank(remark.getRemark5()) || StringUtils.isNotBlank(remark.getReceivingRemark()) || StringUtils.isNotBlank(remark.getAccuAcctNo()))) {
                remark.setCustomerId(orderDto.getCustId());
                remark.setEmployeeId(employee.getId());
                remarkMapper.insertSelective(remark);
            }
            EmployeeOrder employeeOrder = new EmployeeOrder();
            BeanUtils.copyProperties(orderDto, employeeOrder);
            if (employeeOrder != null) {
                employeeOrder.setStatus(orderDto.getOrderStatus());
                employeeOrder.setEmployeeId(employee.getId());
                // 新增员工订单
                int saveEmployeeOrderCount = saveEmployeeOrder(employeeOrder, contractAreaVo, contract, currentDate);
                orderVo.setOrderNo(employeeOrder.getOrderNo());
                //remark表绑定订单号
                remarkMapper.updateById(remark.getId(), employeeOrder.getOrderNo());
                if (StringUtils.isNotBlank(orderVo.getFileId())) {
                    List<String> fileIdList = Arrays.asList(orderVo.getFileId().split(","));
                    for (String fileId : fileIdList) {
                        OrderContractFileVo orderContractFileVo = new OrderContractFileVo();
                        orderContractFileVo.setFileType(OrderContractFileEnum.RENEW_CONTRACT.getCode());
                        orderContractFileVo.setFileId(fileId);
                        orderContractFileVo.setCreateTime(new Date());
                        orderContractFileVo.setCreator(orderVo.getUpdater());
                        orderContractFileVo.setOrderNo(employeeOrder.getOrderNo());
                        orderContractFileMapper.insertAgentContractFile(orderContractFileVo);
                    }
                }
                if (saveEmployeeOrderCount > 0) {
                    EmployeeEntryDimission entryDimission = new EmployeeEntryDimission();
                    BeanUtils.copyProperties(orderDto, entryDimission);
                    if (orderDto.getCertType().equals(CertType.ID_CARD.getCode()) && StringUtils.isNotBlank(orderDto.getCertNo())) {
                        Integer retirementDate = DateUtil.getRetirementDate(orderDto.getCertNo());
                        String retirementRemark = RetirementTypeEnum.getNameByCode(retirementDate);
                        if (StringUtils.isNotEmpty(retirementRemark)) {
                            if (StringUtils.isNotBlank(orderDto.getEntryRemark())) {
                                entryDimission.setEntryRemark(entryDimission.getEntryRemark() + ";" + retirementRemark);
                            } else {
                                entryDimission.setEntryRemark(retirementRemark);
                            }
                        }
                    }
                    if (entryDimission != null) {
                        entryDimission.setOrderNo(employeeOrder.getOrderNo());
                        entryDimission.setApplyEntryMan(entryDimission.getCreator());
                        entryDimission.setApplyEntryTime(new Date());
                        entryDimission.setEntryRemark(entryDimission.getEntryRemark());
                        entryDimissionMapper.insertSelective(entryDimission);
                    }

                    EmployeeContract employeeContract = new EmployeeContract();
                    BeanUtils.copyProperties(orderDto, employeeContract);
                    if (employeeContract != null && orderDto.getSignFlag() == 2) {
                        employeeContract.setOrderNo(employeeOrder.getOrderNo());
                        Long employeeContractNum = generatorSequenceService.getIncrementNum("get_employeeContract_next_sequence_" + "date_" + currentDate);
                        String employeeContractFlowCode = SequenceUtils.getSequence(employeeContractNum, FLOWCODE_LENGTH);
                        employeeContract.setEmpContractNo(EMPLOYEE_CONTRACT_PREFIX + currentDate + employeeContractFlowCode);
                        employeeContract.setSignStatus((byte) 1);
                        employeeContractMapper.insertSelective(employeeContract);
                    }

                    if (CollectionUtils.isNotEmpty(orderDto.getInsurances())) {

                        for (int i = 0; i < orderDto.getInsurances().size(); i++) {
                            //精度回拉
                            OrderInsuranceCfg insuanceCfg = new OrderInsuranceCfg();
                            BeanUtils.copyProperties(orderDto.getInsurances().get(i), insuanceCfg);
                            insuanceCfg.setOrderNo(employeeOrder.getOrderNo());
                            insuanceCfg.setCreator(orderDto.getCreator());
                            insuanceCfg.setCreator(orderDto.getCreator());
                            insuanceCfgMapper.insertSelective(insuanceCfg);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(orderDto.getServiceCharges())) {
                        List<OrderServiceChargeVo> serviceCharges = orderDto.getServiceCharges();
                        for (OrderServiceChargeVo vo : serviceCharges) {
                            OrderServiceCharge serviceCharge = new OrderServiceCharge();
                            BeanUtils.copyProperties(vo, serviceCharge);
                            serviceCharge.setOrderNo(employeeOrder.getOrderNo());
                            if (StringUtils.isBlank(serviceCharge.getQuotationNo())) {
                                serviceCharge.setQuotationNo(orderVo.getQuoteNo());// 保存报价单编号
                            }
                            QuotationDTO quotationData = quotationService.findQuotationDataAndQuotationItem(serviceCharge.getQuotationNo());
                            initPrice(quotationData, serviceCharge);
                            serviceCharge.setCreator(orderDto.getCreator());
                            serviceCharge.setUpdater(orderDto.getUpdater());
                            chargeMapper.insertSelective(serviceCharge);
                        }

                    }
                    EmployeeOrderLog orderLog = new EmployeeOrderLog();
                    BeanUtils.copyProperties(orderDto, orderLog);
                    orderLog.setRemark("派单方生成个人订单：" + userWrapperService.findUserByLoginName(orderVo.getCreator()).getUserName() + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                    orderLog.setEmployeeId(employee.getId());
                    orderLog.setOrderNo(employeeOrder.getOrderNo());
                    orderLog.setOprType(PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
                    orderLogMapper.insertSelective(orderLog);
                    if (orderVo.getEhrEmployeeOrderId() != null) {
                        EhrEmployeeOrderVo ehrEmployeeOrderVo = new EhrEmployeeOrderVo();
                        ehrEmployeeOrderVo.setId(orderVo.getEhrEmployeeOrderId());
                        ehrEmployeeOrderVo.setEmployeeId(employee.getId());
                        ehrEmployeeOrderVo.setOrderNo(employeeOrder.getOrderNo());
                        ehrEmployeeOrderVo.setStaffingState(StaffingState.THE_INCREASE_HAS_BEEN_ACCEPTED.getCode());
                        ehrEmployeeOrderVo.setEmpContractNo(employeeContract.getEmpContractNo());
                        ehrEmployeeOrderVo.setUpdater(employeeOrder.getCreator());
                        epEmployeeOrderWrapperService.updateOrderNoByEhrEmployeeOrderVo(ehrEmployeeOrderVo);
                        List<EhrEmpRelativeFileVo> ehrEmpRelativeFileVoList = oldEhrEmployeeOrderVo.getEhrEmpRelativeFileVoList();
                        if (CollectionUtils.isNotEmpty(ehrEmpRelativeFileVoList)) {
                            List<EmpRelativeFileVo> empRelativeFileVos = VoUtil.copyProperties(ehrEmpRelativeFileVoList, EmpRelativeFileVo.class);
                            for (EmpRelativeFileVo empRelativeFileVo : empRelativeFileVos) {
                                OrderContractFileVo orderContractFileVo = new OrderContractFileVo();
                                orderContractFileVo.setFileId(empRelativeFileVo.getFileId());
                                orderContractFileVo.setOrderNo(employeeOrder.getOrderNo());
                                orderContractFileVo.setEmpId(employee.getId());
                                orderContractFileVo.setFileType(OrderContractFileEnum.CERT_TYPE.getCode());
                                orderContractFileVo.setCreator(employeeOrder.getCreator());
                                orderContractFileMapper.insertAgentContractFile(orderContractFileVo);
                                //empRelativeFileMapper.insertSelective(empRelativeFileVo);
                            }
                            entryDimissionMapper.updataIdStatus(Collections.singletonList(employeeOrder.getOrderNo()));
                        }
                    }
                }
            }
        } else {
            /** 添加日志 */
            String orderNo = orderDto.getOrderNo();
            Employee emp = employeeOrderMapper.getEmployeeByCertNo(orderVo.getCertNo());
            if (emp.getDelFlag().equals(DEL_FLAG_Y)) {
                employeeMapper.updateDelFlagById(emp.getId(), DEL_FLAG_N);
            }
            EmployeeOrderVo employeeOrderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
            Integer orderStatus = employeeOrderVo.getOrderStatus();
            /** orderStatus = 10  orderDto.getOrderStatus = 3 才记日志*/
            if (orderStatus != null && EmployeeOrderStatus.RECEIVING_REJECT.getCode() == orderStatus && orderDto.getOrderStatus() == EmployeeOrderStatus.NEED_RECEIVING_COMPLETED.getCode()) {
                StringBuffer remarkStrReject = new StringBuffer();
                remarkStrReject.append("增员派单方被接单驳回修改后提交：" + userWrapperService.findUserByLoginName(orderDto.getUpdater()).getUserName() + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                EmployeeOrderLog orderLog = new EmployeeOrderLog();
                orderLog.setRemark(remarkStrReject.toString());
                orderLog.setOprType(PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
                orderLog.setOrderNo(orderNo);
                orderLog.setEmployeeId(orderDto.getEmployeeId());
                orderLog.setCreator(orderDto.getUpdater());
                orderLogMapper.insertSelective(orderLog);
                entryDimissionMapper.updateApplyEntryTimeByOrderNo(orderNo);
            }
            Employee employee = new Employee();
            BeanUtils.copyProperties(orderDto, employee);
            employee.setId(orderDto.getEmployeeId());
            employee.setName(orderDto.getEmployeeName());
            if (employee.getName() != null && !employee.getName().matches("^[a-zA-Z0-9 ]*$"))
                employee.setName(employee.getName().replace(" ", "").replace("\t", ""));
            int count = employeeMapper.updateByPrimaryKeySelective(employee);
            if (count <= 0) {
                return;
            }
            EmployeeRemark remark = new EmployeeRemark();
            BeanUtils.copyProperties(orderDto, remark);
            if (remark == null) {
                return;
            }
            if (remarkMapper.getByEmployeeId(orderDto.getOrderNo()) == null) {
                if (StringUtils.isNotBlank(remark.getRemark1()) || StringUtils.isNotEmpty(remark.getRemark2()) || StringUtils.isNotEmpty(remark.getRemark3())
                        || StringUtils.isNotEmpty(remark.getRemark4()) || StringUtils.isNotEmpty(remark.getRemark5()) || StringUtils.isNotEmpty(remark.getReceivingRemark()) || StringUtils.isNotBlank(remark.getAccuAcctNo())) {
                    remark.setCreator(orderVo.getUpdater());
                    remark.setCustomerId(orderVo.getCustId());
                    remark.setOrderNo(orderVo.getOrderNo());
                    remarkMapper.insertSelective(remark);
                }
            } else {
                remarkMapper.updateByEmployeeIdSelective(remark);
            }

            EmployeeOrder employeeOrder = new EmployeeOrder();
            BeanUtils.copyProperties(orderDto, employeeOrder);
            if (employeeOrder != null) {
                /** orderStatus = 10  orderDto.getOrderStatus = 1 不需要改变状态*/
                if (orderStatus != null && orderStatus == EmployeeOrderStatus.RECEIVING_REJECT.getCode() && orderDto.getOrderStatus() == EmployeeOrderStatus.DRAFT.getCode()) {
                    employeeOrder.setStatus(orderStatus);// 订单状态
                } else {
                    employeeOrder.setStatus(orderDto.getOrderStatus());// 订单状态
                }
                int num = employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
                if (num > 0) {
                    EmployeeEntryDimission entryDimission = new EmployeeEntryDimission();
                    BeanUtils.copyProperties(orderDto, entryDimission);
                    entryDimission.setUpdater(orderVo.getUpdater());
                    if (entryDimission != null) {
                        if ("confirmUpdate".equalsIgnoreCase(orderVo.getOptType())) {
                            entryDimission.setStatus(PersonOrderEnum.EntryDimissionStatus.IN_JOB.getCode());
                            entryDimission.setEntryConfirmTime(new Date());
                        }
                        entryDimission.setApplyEntryMan(null);
                        entryDimissionMapper.updateByOrderNoSelective(entryDimission);
                    }
                    EmployeeContract employeeContract = new EmployeeContract();
                    BeanUtils.copyProperties(orderDto, employeeContract);
                    if (employeeContract != null && orderDto.getSignFlag() == 2) {
                        if (employeeContractMapper.getByOrderNo(employeeOrder.getOrderNo()) == null) {
                            employeeContract.setCreator(orderVo.getUpdater());
                            Long employeeContractNum = generatorSequenceService.getIncrementNum("get_employeeContract_next_sequence_" + "date_" + currentDate);
                            String employeeContractFlowCode = SequenceUtils.getSequence(employeeContractNum, FLOWCODE_LENGTH);
                            employeeContract.setEmpContractNo(EMPLOYEE_CONTRACT_PREFIX + currentDate + employeeContractFlowCode);
                            employeeContractMapper.insertSelective(employeeContract);
                        } else {
                            employeeContractMapper.updateByPrimaryKeySelective(employeeContract);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(orderDto.getInsurances())) {
                        List<OrderInsuranceCfgVo> insurances = insuanceCfgMapper.getListByOrderNo(orderDto.getOrderNo());
                        if (CollectionUtils.isNotEmpty(insurances)) {
                            insurances.forEach(insuranceVo -> insuanceCfgMapper.deleteByPrimaryKey(insuranceVo.getId()));
                        }
                        for (int i = 0; i < orderDto.getInsurances().size(); i++) {
                            OrderInsuranceCfg insuanceCfg = new OrderInsuranceCfg();
                            BeanUtils.copyProperties(orderDto.getInsurances().get(i), insuanceCfg);
                            insuanceCfg.setCreator(orderVo.getUpdater());
                            insuanceCfg.setUpdater(orderVo.getUpdater());
                            insuanceCfg.setOrderNo(orderDto.getOrderNo());

                            //如果截止月不为空，lastMonth = expiredMonth
//                            if (insuanceCfg.getExpiredMonth() != null) {
                            insuanceCfg.setLastMonth(insuanceCfg.getExpiredMonth());
//                            }
                            insuanceCfgMapper.insertSelective(insuanceCfg);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(orderDto.getServiceCharges())) {
                        List<OrderServiceChargeVo> serviceCharges = orderDto.getServiceCharges();
                        for (OrderServiceChargeVo vo : serviceCharges) {
                            OrderServiceCharge serviceCharge = new OrderServiceCharge();
                            BeanUtils.copyProperties(vo, serviceCharge);
                            /**完善时候amount 展示的是报价单中的价格可能是不含税的*/
                            serviceCharge.setAmount(Optional.ofNullable(serviceCharge.getTaxfreeAmt()).orElse(BigDecimal.ZERO)
                                    .add(Optional.ofNullable(serviceCharge.getValTax()).orElse(BigDecimal.ZERO)));
                            chargeMapper.updateByPrimaryKeySelective(serviceCharge);
                            /** 修复bug 个人订单驳回,再提交时,截止月不能置为null */
                            if (serviceCharge.getRevEndMonth() == null) {
                                chargeMapper.updateSpecialEndMonthAllById(serviceCharge.getId());
                            }
                        }
                    }

                    if (orderVo.getSave() == null && !"update".equalsIgnoreCase(orderVo.getOptType())) {
                        // 提交
                        EmployeeOrderLog orderLog = new EmployeeOrderLog();
                        BeanUtils.copyProperties(orderDto, orderLog);
                        orderLog.setCreator(orderDto.getUpdater());
                        StringBuffer remarkStr = new StringBuffer();
                        if ("completeUpdate".equalsIgnoreCase(orderVo.getOptType())) {
                            // 完善订单
                            if (orderVo.getOrderStatus() == EmployeeOrderStatus.SUSPEND.getCode()) {
                                remarkStr.append("增员接单方挂起：");
                                if (StringUtils.isNotBlank(orderDto.getSuspendRemark())) {
                                    remarkStr.append(orderDto.getSuspendRemark()).append(" ");
                                }
                                remarkStr.append(userWrapperService.findUserByLoginName(orderDto.getUpdater()).getUserName()).append(" ").append(DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                            } else {
                                remarkStr.append("增员接单方完善：" + userWrapperService.findUserByLoginName(orderDto.getUpdater()).getUserName() + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                                /** 接单完善后 进入实做 */
                                iInsurancePracticeWrapperService.startInsurancePractice(orderDto.getOrderNo(), employeeOrder.getUpdater());
                            }
                        }
                        if ("confirmUpdate".equalsIgnoreCase(orderVo.getOptType())) {
                            // 确认订单
                            List<String> orderNoList = new ArrayList<>();
                            if (orderLog != null) {
                                remarkStr.append("增员派单方最终确认：" + userWrapperService.findUserByLoginName(orderDto.getUpdater()).getUserName() + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                                orderNoList.add(orderVo.getOrderNo());
                            }
                            iSupplierPracticeService.updateBillStartMonthByOrderNo(orderVo.getOrderNo(), orderDto.getInsurances().get(0).getBillStartMonth());
                            /**将账单开始月填入供应商实做*/
                            log.info("确认订单完成 orderNoList:{}", orderNoList);
                        }
                        orderLog.setRemark(remarkStr.toString());
                        orderLog.setOprType(PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
                        orderLogMapper.insertSelective(orderLog);
                    }
                }
            }
        }
        SocialSysMandatoryInfo socialSysMandatoryInfo = new SocialSysMandatoryInfo();
        BeanUtils.copyProperties(orderVo, socialSysMandatoryInfo);
        SocialSysMandatoryInfo socialSysMandatoryInfoS = socialSysMandatoryInfoService.selectOne(new EntityWrapper<SocialSysMandatoryInfo>().eq(SocialSysMandatoryInfo.COL_ORDER_NO, orderVo.getOrderNo()));

        String dealUserName = "";
        try {
            CommonUserVo userByLoginName = userWrapperService.findUserByLoginName(StringUtils.isNotBlank(orderDto.getUpdater()) ? orderDto.getUpdater() : orderDto.getCreator());
            if(userByLoginName != null){
                dealUserName = userByLoginName.getUserName();
            }
        } catch (Exception e) {
            log.info("社保必填信息获取姓名失败! orderDto.creator:{} orderDto.updater:{}",orderDto.getCreator(),orderDto.getUpdater());
            e.printStackTrace();
        }
        try {
            if (socialSysMandatoryInfoS != null && socialSysMandatoryInfoS.getId() != null) {
                if (SocialSysMandatoryInfo.notEmpty(socialSysMandatoryInfo)) {
                    if (DEL_FLAG_Y.equals(socialSysMandatoryInfoS.getDelFlag())) socialSysMandatoryInfo.setDelFlag("N");
                    socialSysMandatoryInfo.setId(socialSysMandatoryInfoS.getId());
                    socialSysMandatoryInfo.setUpdater(orderVo.getUpdater());
                    /** 如果全部属性相同则不修改 */
                    if (!SocialSysMandatoryInfo.isAllEquals(socialSysMandatoryInfoS, socialSysMandatoryInfo)) {
                        EmployeeOrderLog orderLog = new EmployeeOrderLog();
                        BeanUtils.copyProperties(orderDto, orderLog);
                        orderLog.setCreator(orderDto.getUpdater());
                        orderLog.setRemark(PersonOrderEnum.OrderLogOprType.SSMI_EDIT.getName() + " " + dealUserName + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                        orderLog.setOprType(PersonOrderEnum.OrderLogOprType.SSMI_EDIT.getCode());
                        Map<String, Object> logMap = Maps.newHashMap();
                        logMap.put("before", socialSysMandatoryInfoS);
                        logMap.put("current", socialSysMandatoryInfo);
                        orderLog.setLogData(new ObjectMapper().writeValueAsString(logMap));
                        socialSysMandatoryInfoService.updateById(socialSysMandatoryInfo);
                        orderLog.setOrderNo(socialSysMandatoryInfo.getOrderNo());
                        orderLogMapper.insertSelective(orderLog);
                    }
                }
            } else {
                EmployeeOrderLog orderLog = new EmployeeOrderLog();
                BeanUtils.copyProperties(orderDto, orderLog);
                orderLog.setCreator(orderDto.getUpdater());
                orderLog.setRemark(PersonOrderEnum.OrderLogOprType.SSMI_INSERT.getName() + " " + dealUserName + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
                orderLog.setOprType(PersonOrderEnum.OrderLogOprType.SSMI_INSERT.getCode());
                socialSysMandatoryInfo.setCreator(orderVo.getCreator());
                Map<String, Object> logMap = Maps.newHashMap();
                logMap.put("current", socialSysMandatoryInfo);
                orderLog.setLogData(new ObjectMapper().writeValueAsString(logMap));
                if (SocialSysMandatoryInfo.notEmpty(socialSysMandatoryInfo)) {
                    socialSysMandatoryInfoService.insert(socialSysMandatoryInfo);
                    orderLog.setOrderNo(socialSysMandatoryInfo.getOrderNo());
                    orderLogMapper.insertSelective(orderLog);
                }
            }
        } catch (Exception e) {
            log.info("社保必填数据保存失败");
            e.printStackTrace();
        }
    }

    @Override
    public void innerRolledBackSaveOrUpdate(EmployeeOrderVo orderVo) {
        saveOrUpdate(orderVo);
    }

    /**
     新增员工订单表
     @return
     */
    private Integer saveEmployeeOrder(EmployeeOrder employeeOrder, ContractAreaVo contractAreaVo, Contract contract, String currentDate) {
        employeeOrder.setRevCs(contractAreaVo.getReceivingMan());
        employeeOrder.setPrjCs(contract.getCommissioner());
        //set大区信息,截取前三位即可
        employeeOrder.setLargeArea(contract.getDistCom().substring(0, 3));
        if (contractAreaVo.getReceiving().length() > 2) {
            employeeOrder.setRevLargeArea(contractAreaVo.getReceiving().substring(0, 3));
        }
        Long employeeOrderNum = generatorSequenceService.getIncrementNum("get_employeeOrder_next_sequence_" + "date_" + currentDate);
        String employeeOrderFlowCode = SequenceUtils.getSequence(employeeOrderNum, FLOWCODE_LENGTH);
        employeeOrder.setOrderNo(EMPLOYEE_ORDER_PREFIX + currentDate + employeeOrderFlowCode);
        int saveEmployeeOrderCount = employeeOrderMapper.insertSelective(employeeOrder);
        return saveEmployeeOrderCount;
    }


    @Override
    public void deleteByOrderNos(List<String> orderNos, String updater) {
        // 删除员工入离职
        entryDimissionMapper.deleteByOrderNos(orderNos, updater);

        for (int i = 0; i < orderNos.size(); i++) {
            EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNos.get(i), DateUtil.getCurrYearMonth());
            if (orderVo != null) {
                // 删除员工订单
                employeeOrderMapper.deleteByOrderNos(Lists.newArrayList(orderVo.getOrderNo()), updater);
                // 删除员工
                Integer size = employeeOrderMapper.getSizeByEmpIdAndNotDel(orderVo.getEmployeeId());
                if (size == null || size == 0) {
                    employeeMapper.deleteById(orderVo.getEmployeeId(), updater);
                }
                // 删除机动分类表
                if (remarkMapper.getByEmployeeId(orderVo.getOrderNo()) != null) {
                    remarkMapper.deleteByEmployeeId(orderVo.getOrderNo(), updater);
                }
                // 删除员工合同
                if (employeeContractMapper.getByOrderNo(orderNos.get(i)) != null) {
                    employeeContractMapper.deleteByOrderNo(orderNos.get(i), updater);
                }
                // 删除社保公积金
                List<OrderInsuranceCfgVo> insuranceCfgs = insuanceCfgMapper.getListByOrderNo(orderNos.get(i));
                if (CollectionUtils.isNotEmpty(insuranceCfgs)) {
                    insuanceCfgMapper.deleteByOrderNo(orderNos.get(i), updater);
                }
                // 删除服务费
                if (chargeMapper.getByOrderNo(orderNos.get(i)) != null) {
                    chargeMapper.deleteByOrderNo(orderNos.get(i), updater);
                }
                // 删除日志
                if (CollectionUtils.isNotEmpty(orderLogMapper.getListByOrderNo(orderNos.get(i), null))) {
                    orderLogMapper.deleteByOrderNo(orderNos.get(i), updater);
                }
            }
        }
    }

    @Override
    public List<EmployeeOrderDto> getChangeTempleteList(ChangeTempletFindParaVo changeTempletFindParaVo) {
        if (changeTempletFindParaVo != null) {
            return employeeOrderMapper.getChangeTempleteList(changeTempletFindParaVo);
        }
        return null;
    }

    @Override
    public List<CompleteOrderViewVo> getCompleteOrderListWorkFow(CompleteOrderViewVo orderViewVo) {
        return employeeOrderMapper.getCompleteOrderListWorkFow(orderViewVo);
    }

    @Override
    public Page<CompleteOrderViewVo> getCompleteOrderListPage(Integer page, Integer limit, CompleteOrderViewVo orderViewVo) {
        Page<CompleteOrderViewVo> orderPage = new Page<>(page, limit);
//        List<CompleteOrderViewVo> orderList = employeeOrderMapper.getCompleteOrderListPage(orderPage, orderViewVo);
        List<CompleteOrderViewVo> orderList = employeeOrderMapper.getEmployeeDimissionListPage(orderPage, orderViewVo);
        Map<String, String> orderNoAndChgContentMap = getOrderChangeMap(orderList);
        Map<String, String> orgMap = getOrgMap();
        Map<Long, String> supplierMap = getSupplierMap();
        for (int i = 0; i < orderList.size(); i++) {
            CompleteOrderViewVo completeOrderViewVoByPage = orderList.get(i);
            String orderNo = completeOrderViewVoByPage.getOrderNo();
            if (orderNoAndChgContentMap.containsKey(orderNo)) {
                EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(orderNoAndChgContentMap.get(orderNo), EmpOrderEntryDimissionsInsCfg.class);
                EmployeeEntryDimission newEmployeeEntryDimission = empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
                completeOrderViewVoByPage.setDimMaterial(newEmployeeEntryDimission.getDimMaterial());
                completeOrderViewVoByPage.setReduceReason(newEmployeeEntryDimission.getReduceReason());
                completeOrderViewVoByPage.setReduceDetailReason(newEmployeeEntryDimission.getReduceDetailReason());
                completeOrderViewVoByPage.setDimissionReason(newEmployeeEntryDimission.getDimissionReason());
                completeOrderViewVoByPage.setDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getDimissionDate(), "yyyy-MM-dd"));
                completeOrderViewVoByPage.setApplyDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getApplyDimissionDate(), "yyyy-MM-dd"));
                completeOrderViewVoByPage.setDimissionRemark(newEmployeeEntryDimission.getDimissionRemark());
                completeOrderViewVoByPage.setLeaveCallFlag(newEmployeeEntryDimission.getCallFlag());
                completeOrderViewVoByPage.setExpiredMonth(newEmployeeEntryDimission.getExpiredMonth());
            }
            if (orderViewVo.getOrderFlag() != EmployeeOrderStatus.NEED_DISTDOM_CONFIRM.getCode()) {
                String remarkStr = employeeOrderLogService.getListByOrderNoAndOptType(orderList.get(i).getOrderNo(), PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
                orderList.get(i).setEntryProcess(remarkStr);
            }
            if (StringUtils.isNotEmpty(orgMap.get(orderList.get(i).getDistCom()))) {
                orderList.get(i).setDistComName(orgMap.get(orderList.get(i).getDistCom()));
            }
            Integer recceivingType = orderList.get(i).getRecceivingType();
            if (recceivingType == 0) {
                // 自有公司
                orderList.get(i).setReceivingName(orgMap.get(orderList.get(i).getReceiving()));
            } else {
                orderList.get(i).setReceivingName(supplierMap.get(Long.valueOf(orderList.get(i).getReceiving())));
            }
            if (orderList.get(i).getCertType().equals(CertType.ID_CARD.getCode()) && StringUtils.isNotBlank(orderList.get(i).getCertNo())) {
                try {
                    Integer retirementDate = DateUtil.getRetirementDate(orderList.get(i).getCertNo());
                    completeOrderViewVoByPage.setRetireType(retirementDate);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        setInsurAddDay(orderList);

        orderPage.setRecords(orderList);
        return orderPage;
    }

    private Map<String, String> getOrderChangeMap(List<CompleteOrderViewVo> orderList) {
        Set<String> orderNoSet = orderList.stream().map(CompleteOrderViewVo::getOrderNo).collect(Collectors.toSet());
        if (orderNoSet.size() > 0) {
            List<EmployeeOrderChange> employeeOrderChanges = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrders(orderNoSet);
            return employeeOrderChanges.stream().filter(item -> item.getChgStatus()
                            .equals(EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode())
                            && item.getChgMethod().equals(EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode())
                            && item.getChgType().equals(EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode()))
                    .collect(toMap(EmployeeOrderChange::getOrderNo, EmployeeOrderChange::getChgContent));
        } else {
            return Maps.newHashMap();
        }
    }

    @Override
    public Page<CompleteOrderViewVo> getLeaveDataListPage(Integer page, Integer limit, CompleteOrderViewVo completeOrderViewVo) {
        Page<CompleteOrderViewVo> orderPage = new Page<>(page, limit);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<CompleteOrderViewVo> orderList = employeeOrderMapper.getLeaveDataListPage(orderPage, completeOrderViewVo);
        stopWatch.stop();
        System.out.println("查询执行了" + stopWatch.getTotalTimeSeconds());
        Map<Long, String> supplierMap = getSupplierMap();
        Map<String, String> orgMap = getOrgMap();
        Map<String, String> orderNoAndChgContentMap = getOrderChangeMap(orderList);

        for (int i = 0; i < orderList.size(); i++) {
            CompleteOrderViewVo completeOrderViewVoByPage = orderList.get(i);
            String orderNo = completeOrderViewVoByPage.getOrderNo();
            if (orderNoAndChgContentMap.containsKey(orderNo)) {
                EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(orderNoAndChgContentMap.get(orderNo), EmpOrderEntryDimissionsInsCfg.class);
                EmployeeEntryDimission newEmployeeEntryDimission = empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
                completeOrderViewVoByPage.setDimMaterial(newEmployeeEntryDimission.getDimMaterial());
                completeOrderViewVoByPage.setReduceReason(newEmployeeEntryDimission.getReduceReason());
                completeOrderViewVoByPage.setReduceDetailReason(newEmployeeEntryDimission.getReduceDetailReason());
                completeOrderViewVoByPage.setDimissionRemark(newEmployeeEntryDimission.getDimissionRemark());
                completeOrderViewVoByPage.setLeaveCallFlag(newEmployeeEntryDimission.getCallFlag());
                completeOrderViewVoByPage.setExpiredMonth(newEmployeeEntryDimission.getExpiredMonth());
                completeOrderViewVoByPage.setDimissionReason(newEmployeeEntryDimission.getDimissionReason());
                completeOrderViewVoByPage.setDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getDimissionDate(), "yyyy-MM-dd"));
                completeOrderViewVoByPage.setApplyDimissionDate(DateUtil.formatDateToString(newEmployeeEntryDimission.getApplyDimissionDate(), "yyyy-MM-dd"));
            }
            Integer receivingType = orderList.get(i).getRecceivingType();
            if (receivingType == ContractAreaRecceivingType.MY_COMPANY.getCode()) {
                // 自有公司
                orderList.get(i).setReceivingName(orgMap.get(orderList.get(i).getReceiving()));
            } else if (receivingType == ContractAreaRecceivingType.SUPPLIER_COMPANY.getCode()) {
                orderList.get(i).setReceivingName(supplierMap.get(Long.valueOf(orderList.get(i).getReceiving())));
            }
            if (StringUtils.isNotEmpty(orgMap.get(orderList.get(i).getDistCom()))) {
                orderList.get(i).setDistComName(orgMap.get(orderList.get(i).getDistCom()));
            }
        }

        StopWatch stopWatchs = new StopWatch();
        stopWatchs.start();
        setInsurAddDay(orderList);
        stopWatchs.stop();
        System.out.println("代码块执行l" + stopWatchs.getTotalTimeSeconds());
        orderPage.setRecords(orderList);
        return orderPage;
    }

    private void setInsurAddDay(List<CompleteOrderViewVo> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<ServiceSiteCfgDto> serviceSiteCfgDtoList = new ArrayList<>();
        orderList.forEach(v -> {
            ServiceSiteCfgDto serviceSiteCfgDto = new ServiceSiteCfgDto();
            serviceSiteCfgDto.setServiceSiteCode(v.getReceiving());
            serviceSiteCfgDto.setCityCode(v.getCityCode());
            serviceSiteCfgDtoList.add(serviceSiteCfgDto);
        });
        List<ServiceSiteCfgDto> serviceSiteCfgList = serviceSiteCfgService.getInsurAddDay(serviceSiteCfgDtoList);

        orderList.forEach(v -> {
            serviceSiteCfgList.forEach(serviceSiteCfg -> {
                if (v.getReceiving().equals(serviceSiteCfg.getServiceSiteCode()) && v.getCityCode().equals(serviceSiteCfg.getCityCode())) {
                    transformMsg(serviceSiteCfg);
                    v.setServiceSiteCfgDto(serviceSiteCfg);
                }
            });
        });
    }

    private Map<String, String> getOrgMap() {
        List<OrgVo> companys = orgnizationWrapperService.findAllCompany();
        Map<String, String> companyMap = companys.stream().collect(toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        return companyMap;
    }

    private Map<Long, String> getSupplierMap() {
        List<SupplierVo> suppliers = supplierService.getAllSuppliers();
        Map<Long, String> supplierMap = suppliers.stream().collect(toMap(SupplierVo::getId, SupplierVo::getSupplierName));
        return supplierMap;
    }


    @Override
    public boolean updateStatusByOrderNo(String orderNo, Integer status, String rejectReason, String updater) {
        EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
        String remark = "";
        if (EmployeeOrderStatus.NEED_DISTDOM_CONFIRM.getCode() == orderVo.getOrderStatus()) {
            remark = "增员派单方驳回：" + rejectReason + "  ";
            /** 增员派单方进行驳回时需要删除实做表信息 */
            iInsurancePracticeWrapperService.deleteInsurancePracticeByOrderNo(orderNo);
        } else {
            throw new RuntimeException("当前非派单方驳回,请检查是否有数据错误");
        }
        EmployeeOrder employeeOrder = new EmployeeOrder();
        BeanUtils.copyProperties(orderVo, employeeOrder);
        employeeOrder.setRejectReason(rejectReason);
        employeeOrder.setStatus(status);
        employeeOrder.setUpdater(updater);
        int count = employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
        if (count > 0) {

            // 新增入职操作日志
            saveEmployeeOrderLog(updater, orderNo, orderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode(), remark);
        }
        return count > 0;
    }


    private List<OrderInsuranceCfgVo> getInsuranceCfgsByOrderNo(String orderNo) {
        List<OrderInsuranceCfgVo> insuranceCfgVos = insuranceCfgService.getListByOrderNo(orderNo);
        setInsuranceCfg(insuranceCfgVos);
        return insuranceCfgVos;
    }


    private void setInsuranceCfg(List<OrderInsuranceCfgVo> insuranceCfgVos) {
        if (CollectionUtils.isNotEmpty(insuranceCfgVos)) {
            for (OrderInsuranceCfgVo insuranceCfgVo : insuranceCfgVos) {
                InsuranceGroupRatioVo groupAndRatio = groupWrapperService.getGroupAndRatioById(insuranceCfgVo.getGroupRatioId());
                if (groupAndRatio != null) {
                    InsuranceRatioVo insuranceRatioVo = ratioWrapperService.findIinsuranceRatio(groupAndRatio.getInsuranceRatioCode());
                    if (null != insuranceRatioVo) {
                        insuranceCfgVo.setGroupCode(groupAndRatio.getInsuranceGroupCode());
                        insuranceCfgVo.setCityCode(insuranceRatioVo.getCityCode());
                        insuranceCfgVo.setRatioCode(groupAndRatio.getInsuranceRatioCode());
                        insuranceCfgVo.setProductCode(insuranceRatioVo.getProductCode());
                        insuranceCfgVo.setCityName(insuranceRatioVo.getCityName());
                        insuranceCfgVo.setComAdd(insuranceRatioVo.getComAdd());
                        insuranceCfgVo.setIndExactVal(insuranceRatioVo.getIndExactVal());
                        insuranceCfgVo.setComExactVal(insuranceRatioVo.getComExactVal());
                        insuranceCfgVo.setIndCalcMode(insuranceRatioVo.getIndCalcMode());
                        insuranceCfgVo.setComCalcMode(insuranceRatioVo.getComCalcMode());
                        insuranceCfgVo.setIndRatio(insuranceRatioVo.getIndRatio());
                        insuranceCfgVo.setIndlAdd(insuranceRatioVo.getIndlAdd());
                        insuranceCfgVo.setComRatio(insuranceRatioVo.getComRatio());
                        insuranceCfgVo.setRatioName(insuranceRatioVo.getRatioName());
                    }
                    InsuranceGroupVo groupVo = groupWrapperService.selectByPrimaryKey(groupAndRatio.getInsuranceGroupCode());
                    if (groupVo != null) {
                        insuranceCfgVo.setGroupName(groupVo.getGroupName());
                    }
                }
                if (StringUtils.isNotEmpty(insuranceCfgVo.getRatioCode())) {
                    List<InsuranceBaseVo> baseVoLists = baseWrapperService.findInsuranceBaseByCode(insuranceCfgVo.getRatioCode());
                    List<RatioBaseVo> ratioBaseVoList = new ArrayList<>();
                    baseVoLists.forEach(insuranceBaseVo -> {
                        RatioBaseVo baseVo = new RatioBaseVo();
                        BeanUtils.copyProperties(insuranceBaseVo, baseVo);
                        baseVo.setRatioCode(insuranceBaseVo.getInsuraceRatioCode());
                        ratioBaseVoList.add(baseVo);
                    });
                    insuranceCfgVo.setBaseList(ratioBaseVoList);
                }
            }
        }
    }


    private void setEmployeeOrder(EmployeeOrderVo employeeOrderVo, List<OrderInsuranceCfgVo> orderInsuranceCfgVoList) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderInsuranceCfgVoList)) {
            Set<OrderInsuranceCfgVo> orderInsuranceCfgVos = new HashSet<>(orderInsuranceCfgVoList);//去重
            //add guoqian 2020/11/18 0018 16:43 对产品类型进行排序
            List<OrderInsuranceCfgVo> result = new ArrayList<>(orderInsuranceCfgVos);
            // 排序
            Collections.sort(result, new Comparator<OrderInsuranceCfgVo>() {
                @Override
                //从小到大排序
                public int compare(OrderInsuranceCfgVo o1, OrderInsuranceCfgVo o2) {
                    return o1.getProductCode().compareTo(o2.getProductCode());
                }
            });
            employeeOrderVo.setInsurances(orderInsuranceCfgVoList);
            employeeOrderVo.setInsurancesStr(JsonUtil.beanToJson(result));
        }
        List<OrderServiceChargeVo> serviceChargeVo = serviceChargeService.getOneByOrderNo(employeeOrderVo.getOrderNo());
        if (null != serviceChargeVo) {
            employeeOrderVo.setServiceChargeStr(JsonUtil.beanToJson(serviceChargeVo));
            employeeOrderVo.setServiceCharges(serviceChargeVo);
        }
//        employeeOrderVo.setTempletStr(getBillTemplate(employeeOrderVo.getCustId()));
    }

//    private String getBillTemplate(Long custId) {
//        List<BillTempletVo> billTemplateVoList = templetService.getListByCustIdAndTempletId(custId, null, null);
//        List<BillTempletVo> billTempletVos = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(billTemplateVoList)) {
//            billTemplateVoList.forEach(templateVo -> {
//                List<BillTempletFeeCfgVo> feeCfgVoList = feeCfgService.getListByTempletId(templateVo.getId());
//                templateVo.setFees(feeCfgVoList);
//                billTempletVos.add(templateVo);
//            });
//            return JsonUtil.beanToJson(billTempletVos);
//        }
//        return null;
//    }


    @Override
    public int getUseQuotationEmpOrderCount(String quotationNo) {
        return employeeOrderMapper.getUseQuotationEmpOrderCount(quotationNo);
    }

    @Override
    public void updateStatusBatch(List<String> orderNos, Integer status, String updater) {
        ArrayList<EmployeeOrderVo> orderVoList = new ArrayList<>();
        List<EhrEmployeeOrderVo> ehrEmployeeOrderVoList = epEmployeeOrderWrapperService.selectListByOrderNoList(orderNos);
        Map<String, EhrEmployeeOrderVo> ehrEmployeeOrderVoMap = ehrEmployeeOrderVoList.stream().collect(toMap(EhrEmployeeOrderVo::getOrderNo, Function.identity(), (e1, e2) -> e2));

        for (String orderNo : orderNos) {
            EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
            if (orderVo != null) {
                /** 只有在status为6 也就是employee_order status的状态为6时才会在里面插入 */
                if (status == EmployeeOrderStatus.ADD_FINISHED.getCode()) {
                    List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = getInsuranceCfgsByOrderNo(orderNo);
                    setEmployeeOrder(orderVo, orderInsuranceCfgVoList);
                    orderVo.setUpdater(updater);
                    orderVoList.add(orderVo);
                }
                Integer oprType = PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode();
                StringBuilder remark = new StringBuilder();
                if (status == EmployeeOrderStatus.NEED_DISTDOM_CONFIRM.getCode() || status == EmployeeOrderStatus.ADD_FINISHED.getCode()
                        || status == EmployeeOrderStatus.SUB_FINISHED.getCode() || status == EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode()) {
                    if (status == EmployeeOrderStatus.NEED_DISTDOM_CONFIRM.getCode()) {
                        remark.append("增员接单方完善：");

                    } else if (status == EmployeeOrderStatus.ADD_FINISHED.getCode()) {
                        remark.append("增员派单方最终确认：");
                    } else if (status == EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode()) {
                        oprType = PersonOrderEnum.OrderLogOprType.DIMISSION.getCode();
                        /** 批量确认的时候,需要在项目最终确认的时候显示 returnMonth 所以在批量里处理一下 */
                        EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(
                                orderNo,
                                EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                                EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                                EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
                        String chgContent = employeeOrderChange.getChgContent();
                        EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
                        List<OrderInsuranceCfg> newOrderInsuranceCfgList = empOrderEntryDimissionsInsCfg.getNewOrderInsuranceCfgList();
                        Long revTempId = newOrderInsuranceCfgList.get(0).getRevTempId();
                        BillTempletFeeCfgVo feeTemplate = templetWrapperService.getFeeTempletById(revTempId);
                        for (OrderInsuranceCfg orderInsuranceCfg : newOrderInsuranceCfgList) {
                            if (orderInsuranceCfg.getLastMonth() == null) {
                                orderInsuranceCfg.setLastMonth(orderInsuranceCfg.getExpiredMonth());
                            }
                            if (orderInsuranceCfg.getReturnMonth() == null) {
                                Integer returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), orderInsuranceCfg.getExpiredMonth());
                                if (orderInsuranceCfg.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), orderInsuranceCfg.getReturnMonth()) && orderInsuranceCfg.getReturnMonth() > returnMonth) {
                                    /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                                    returnMonth = orderInsuranceCfg.getReturnMonth();
                                }
                                orderInsuranceCfg.setReturnMonth(returnMonth);
                            }
                        }
                        empOrderEntryDimissionsInsCfg.setNewOrderInsuranceCfgList(newOrderInsuranceCfgList);
                        String newCacheCfg = JsonUtil.beanToJson(empOrderEntryDimissionsInsCfg);
                        employeeOrderChange.setChgContent(newCacheCfg);
                        employeeOrderChangeMapper.updateChgContentById(employeeOrderChange);
                        remark.append("减员接单方确认：");
                    } else if (status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                        oprType = PersonOrderEnum.OrderLogOprType.DIMISSION.getCode();
                        remark.append("减员派单方最终确认：");
                    }
                    // 新增入职操作日志
                    saveEmployeeOrderLog(updater, orderVo.getOrderNo(), orderVo.getEmployeeId(), oprType, remark.toString());
                }
                EmployeeEntryDimission entryDimission = entryDimissionMapper.selectByOrderNo(orderVo.getOrderNo());
                if (entryDimission != null && status == EmployeeOrderStatus.ADD_FINISHED.getCode()) {
                    entryDimission.setEntryConfirmTime(new Date());
                    entryDimission.setUpdater(updater);
                    entryDimission.setStatus(PersonOrderEnum.EntryDimissionStatus.IN_JOB.getCode());
                    entryDimissionMapper.updateByOrderNoSelective(entryDimission);
                }
                if (entryDimission != null && status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                    /** 最后落地 */
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String format = sdf.format(new Date());
                    try {
                        Date parse = sdf.parse(format);
                        entryDimissionMapper.updateApplyConfirmTimeByOrderNo(parse, orderVo.getOrderNo());
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
                // 离职成功或者完善个人订单时，将员工的社保产品的lastMonth=expiredMonth
                if (entryDimission != null && (status == EmployeeOrderStatus.SUB_FINISHED.getCode()
                        || status == EmployeeOrderStatus.NEED_RECEIVING_COMPLETED.getCode())) {
                    /** 最后加入逻辑 */
                    List<OrderInsuranceCfg> insuranceCfgList = Lists.newArrayList();
                    OrderServiceChargeVo newChargeVo = new OrderServiceChargeVo();
                    Integer endMonth = null;
                    if (status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                        EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(
                                orderNo,
                                EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                                EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                                EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
                        String chgContent = employeeOrderChange.getChgContent();
                        EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
                        insuranceCfgList = empOrderEntryDimissionsInsCfg.getNewOrderInsuranceCfgList();
                        newChargeVo = empOrderEntryDimissionsInsCfg.getNewOrderServiceChargeVo();
                        EmployeeEntryDimission entryDimissionByDimission = empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
                        entryDimissionByDimission.setUpdater(updater);
                        entryDimissionByDimission.setStatus(PersonOrderEnum.EntryDimissionStatus.DIMISSION.getCode());
                        endMonth = entryDimissionByDimission.getExpiredMonth();
                        entryDimissionMapper.updateByOrderNoSelective(entryDimissionByDimission);
                        int i1 = employeeOrderChangeMapper.updateChgStatusById(employeeOrderChange.getId(), EmployeeOrderChangeEnum.ChgStatus.CHANGE_SUCCESS.getCode());
                    } else {
                        List<OrderInsuranceCfgVo> insuranceCfgVoList = insuanceCfgMapper.getListByOrderNo(orderNo);
                        insuranceCfgList = insuranceCfgVoList.stream().map(orderInsuranceCfgVo -> {
                            OrderInsuranceCfg orderInsuranceCfg = new OrderInsuranceCfg();
                            BeanUtils.copyProperties(orderInsuranceCfgVo, orderInsuranceCfg);
                            return orderInsuranceCfg;
                        }).collect(Collectors.toList());
                    }
                    Long revTempId = insuranceCfgList.get(0).getRevTempId();
                    BillTempletFeeCfgVo feeTemplate = templetWrapperService.getFeeTempletById(revTempId);
                    //                    Integer currMonth = DateUtil.gerCurrYearMonth();
                    List<OrderInsuranceCfg> oldCfgData = Lists.newArrayList();
                    if (status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                        oldCfgData = insuanceCfgMapper.getInsuranceCfgsByOrderNo(orderNo);
                        insuranceCfgList.forEach(item -> {
                            Integer returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), item.getExpiredMonth());
                            if (item.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), item.getReturnMonth()) && item.getReturnMonth() > returnMonth) {
                                /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                                returnMonth = item.getReturnMonth();
                            }
                            item.setLastMonth(item.getExpiredMonth());
                            OrderInsuranceCfg insuranceCfg = new OrderInsuranceCfg();
                            BeanUtils.copyProperties(item, insuranceCfg);
                            // 自动填写退费年月
                            insuranceCfg.setReturnMonth(returnMonth);
                            insuanceCfgMapper.updateByPrimaryKeySelective(insuranceCfg);
                        });
                        /**给服务费添加截止月*/
                        updateCharges(orderVo.getOrderNo(), newChargeVo, endMonth);

                    } else {
                        insuranceCfgList.forEach(e -> {
                            // 如果lastMonth为空或=0
                            if ((null == e.getLastMonth() || 0 == e.getLastMonth()) && e.getExpiredMonth() != null) {
                                Integer returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), e.getExpiredMonth());
                                if (e.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), e.getReturnMonth()) && e.getReturnMonth() > returnMonth) {
                                    /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                                    returnMonth = e.getReturnMonth();
                                }
                                e.setLastMonth(e.getExpiredMonth());
                                OrderInsuranceCfg insuranceCfg = new OrderInsuranceCfg();
                                BeanUtils.copyProperties(e, insuranceCfg);
                                // 自动填写退费年月
                                if (status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                                    insuranceCfg.setReturnMonth(returnMonth);
                                }
                                insuanceCfgMapper.updateByPrimaryKeySelective(insuranceCfg);
                            }
                        });
                    }
                    if (status == EmployeeOrderStatus.SUB_FINISHED.getCode()) {

                        iSupplierPracticeService.updateSupplierPractice(orderNo, updater, insuranceCfgList, oldCfgData);
                        if (ehrEmployeeOrderVoMap.containsKey(orderNo)) {
                            EhrEmployeeOrderVo ehrEmployeeOrderVo = ehrEmployeeOrderVoMap.get(orderNo);
                            ehrEmployeeOrderVo.setStaffingState(StaffingState.REDUCTION_PROCESSED.getCode());
                            ehrEmployeeOrderVo.setUpdater(updater);
                            epEmployeeOrderWrapperService.updateStaffingState(ehrEmployeeOrderVo);
                        }
                    }
                }
            }

            if (status == EmployeeOrderStatus.NEED_DISTDOM_CONFIRM.getCode()) {
                /** 接单完善后 进入实做 */
                iInsurancePracticeWrapperService.startInsurancePractice(orderNo, updater);

            }
        }
        employeeOrderMapper.updateStatusBatch(orderNos, status, updater);

    }

    private void updateCharges(String orderNo, OrderServiceChargeVo newChargeVo, Integer revEndMonth) {
        /**没有newChargeVo 应该是给服务费截至之前就申请离职的数据，直接使用订单的截至月*/
        Integer endMonth = Objects.isNull(newChargeVo) ? revEndMonth : newChargeVo.getRevEndMonth();
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        List<OrderServiceChargeVo> chargeServiceList = serviceChargeService.getListByOrderNos(Lists.newArrayList(orderNo));
        List<Long> editIds = chargeServiceList.stream()
                .filter(vo -> vo.getRevEndMonth() == null || (vo.getRevEndMonth() >= vo.getRevStartMonth() && vo.getRevEndMonth() >= endMonth))
                .map(OrderServiceChargeVo::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(editIds)) {
            OrderServiceCharge condition = new OrderServiceCharge();
            condition.setOrderServiceChargeIds(editIds);
            condition.setRevEndMonth(endMonth);
            serviceChargeService.updateOrderServiceChargeByIds(condition);
        }
    }

    @Override
    public void updateSuspendOrder(List<String> orderNos, Integer suspendReason, String suspendRemark, String updater) {
//        String suspendReasonStr = "";
//        if (suspendReason != null) {
//            List<DictVo> dictVos = dictionaryService.findAllDictList().get("ORDER_SUSPEND_REASON");
//            for (int i = 0; i < dictVos.size(); i++) {
//                if (dictVos.get(i).getCode() == suspendReason) {
//                    suspendReasonStr = dictVos.get(i).getName();
//                    break;
//                }
//            }
//        }
        for (String orderNo : orderNos) {
            EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
            if (orderVo != null) {
                EmployeeOrder order = new EmployeeOrder();
                order.setOrderNo(orderNo);
                order.setStatus(EmployeeOrderStatus.SUSPEND.getCode());
                order.setUpdater(updater);
                employeeOrderMapper.updateByPrimaryKeySelective(order);

                EmployeeEntryDimission entryDimission = entryDimissionMapper.selectByOrderNo(orderNo);
                if (entryDimission != null) {
                    entryDimission.setSuspendReason(suspendReason);
                    entryDimission.setSuspendRemark(suspendRemark);
                    entryDimissionMapper.updateByOrderNoSelective(entryDimission);
                }

                // 新增入职操作日志
                saveEmployeeOrderLog(updater, orderNo, orderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode(), "增员接单方挂起：" + suspendRemark + "  ");
            }
        }

    }

    @Override
    public void updateRejectOrder(String orderNo, String rejectRemark, String loginName) {
        EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
        if (orderVo != null) {
            EmployeeOrder order = new EmployeeOrder();
            order.setUpdater(loginName);
            order.setStatus(EmployeeOrderStatus.RECEIVING_REJECT.getCode());
            order.setOrderNo(orderNo);
            //            order.setReceivingRejectRemark(rejectRemark);
            employeeOrderMapper.updateByPrimaryKeySelective(order);

            // 新增入职接单方操作日志
            saveEmployeeOrderLog(loginName, orderNo, orderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode(), "增员接单方驳回：" + rejectRemark + "  ");
        }
    }


    @Override
    public Page<NationwideEmployeeViewVo> getNationwideEmployee(Integer page, Integer limit, EmployeeOrderVo employeeOrderVo) {
        Page<NationwideEmployeeViewVo> nationwidePage = new Page<>(page, limit);
        List<NationwideEmployeeViewVo> nationwideEmployeeList = employeeOrderMapper.getNationwideEmployee(nationwidePage, employeeOrderVo);

        List<String> contractNoList = nationwideEmployeeList.stream().map(NationwideEmployeeViewVo::getContractNo).collect(Collectors.toList());
        List<String> contractAreaNoList = nationwideEmployeeList.stream().map(NationwideEmployeeViewVo::getContractAreaNo).collect(Collectors.toList());

        Map<String, ContractPageVo> contractNoAndContractMap = new HashMap<>();
        Map<String, ContractAreaVo> contractAreaNoAndContractAreaMap = new HashMap<>();
        //      获取客户姓名
        //	获取contract  --> 派单方
        if (contractNoList.size() > 0) {
            List<ContractPageVo> contractListByContractNoList = contractService.getContractListByContractNoList(contractNoList);
            contractNoAndContractMap = contractListByContractNoList.stream().collect(toMap(ContractPageVo::getContractNo, Function.identity()));
        }
        // 获取contract_area --> 接单方
        if (contractAreaNoList.size() > 0) {
            List<ContractAreaVo> contractAreaListByContractNoList = contractAreaService.selectContractAreaListByContractNoList(contractAreaNoList);
            contractAreaNoAndContractAreaMap = contractAreaListByContractNoList.stream().collect(toMap(ContractAreaVo::getContractAreaNo, Function.identity()));
        }
        //获取公司名称
        Map<String, OrgVo> allCompanyToMap = organizationService.getAllCompanyToMap();
        Map<Long, SupplierVo> allSupplierNameToMap = supplierService.getAllSupplierNameToMap();

        for (NationwideEmployeeViewVo item : nationwideEmployeeList) {
            ContractPageVo contractPageVo = contractNoAndContractMap.get(item.getContractNo());
            item.setDistComName(allCompanyToMap.get(contractPageVo.getDistCom()).getOrgName());

            ContractAreaVo contractAreaVo = contractAreaNoAndContractAreaMap.get(item.getContractAreaNo());
            item.setContractAreaName(contractAreaVo.getName());
            item.setReceivingMan(contractAreaVo.getReceivingMan());
            item.setDistComMan(contractPageVo.getCommissioner());
            if (contractAreaVo.getRecceivingType() == ContractAreaRecceivingType.MY_COMPANY.getCode()) {
                item.setReceivingName(allCompanyToMap.get(contractAreaVo.getReceiving()).getOrgName());
            } else if (contractAreaVo.getRecceivingType() == ContractAreaRecceivingType.SUPPLIER_COMPANY.getCode()) {
                item.setReceivingName(allSupplierNameToMap.get(Long.valueOf(contractAreaVo.getReceiving())).getSupplierName());
            } else {
                throw new RuntimeException("派单方,接单方类型出现第三种");
            }
            String processData = employeeOrderLogService.getProcessData(item.getOrderNo());
            item.setProcessData(processData);
        }

        nationwidePage.setRecords(nationwideEmployeeList);
        return nationwidePage;
    }

    @Override
    public List<OneTimeCharge> getUnusedOneTimeFeeByEmployeeIdAndHappenMonth(String orderNo, Integer happenMonth) {
        return orderOneChargeMapper.getUnusedOneTimeFeeByEmployeeIdAndHappenMonth(orderNo, happenMonth);
    }

    @Override
    public void updateOrderOneChargeBillIdByIds(List<Integer> orderOneChargeIds, Long billId) {
        orderOneChargeMapper.updateBillIdByIds(orderOneChargeIds, billId);
    }

    @Override
    public void updateOrderOneChargeUnusedByBillIds(Set<Long> billIds) {
        orderOneChargeMapper.updateOrderOneChargeUnusedByBillIds(billIds);
    }

    @Override
    public List<OneTimeCharge> getOrderOneChargeByBillId(Long billId) {
        return orderOneChargeMapper.getOrderOneChargeByBillId(billId);
    }

    @Override
    public List<OneTimeCharge> getIndFeeAndOneFee(List<String> orderNoList) {
        return orderOneChargeMapper.getIndFeeAndOneFee(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderByOrderNoList(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList))
            return Lists.newArrayList();
        return employeeOrderMapper.getEmployeeOrderByOrderNoList(orderNoList);
    }

    @Override
    public void rejectedLeave(List<String> orderNos, String loginName, String dimReason) {
        List<EmployeeOrder> employeeOrderList = employeeOrderMapper.selectEmployeeOrderByOrderNoList(orderNos);
        Map<String, Long> orderNoAndEmployeeIdMap = employeeOrderList.stream().collect(toMap(EmployeeOrder::getOrderNo, EmployeeOrder::getEmployeeId));
        /** 驳回离职请求改变 order_status 为 11   EmployeeOrderStatus  */
        for (String orderNo : orderNos) {
            saveEmployeeOrderLog(loginName, orderNo, orderNoAndEmployeeIdMap.get(orderNo), PersonOrderEnum.OrderLogOprType.DIMISSION.getCode(), "接单方离职驳回：原因为 " + dimReason + " ");
            saveEmployeeOrderLog(loginName, orderNo, orderNoAndEmployeeIdMap.get(orderNo), PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode(), "接单方离职驳回备注:" + dimReason + " ");
        }
        employeeOrderMapper.rejectedLeave(orderNos, loginName);
    }

    @Override
    public int cancelDimission(List<CompleteOrderViewVo> orders, String loginName) {
        /** 取消减员 修改两张表
         1. 将老数据还原
         2. 还原后删除change表中数据
         3. 清除入离职表中的申请离职日期
         */
        Set<String> orderNoSet = orders.stream().map(CompleteOrderViewVo::getOrderNo).collect(Collectors.toSet());
        /** 拿出change表中数据 */
        List<EmployeeOrderChange> employeeOrderChanges = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrders(orderNoSet);
        /**
         获取原本的 employeeOrder status
         先过滤   ->  再在map中获取到 老的oldEmployeeOrder status  然后变为map 返回
         如果 下面的处理,是不允许出现 key出现多条的*/
        Map<String, Integer> orderNoAndEmployeeOrderChangeMap = employeeOrderChanges.stream()
                .filter(item -> item.getChgStatus()
                        .equals(EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode())
                        && item.getChgMethod().equals(EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode())
                        && item.getChgType().equals(EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode()))
                .collect(toMap(EmployeeOrderChange::getOrderNo, item -> {
                    EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(item.getChgContent(), EmpOrderEntryDimissionsInsCfg.class);
                    return empOrderEntryDimissionsInsCfg.getOldEmployeeOrder().getOrderStatus();
                }, (a, b) -> b));
        List<EmployeeOrder> employeeOrders = Lists.newArrayList();
        /** 获取到数据 orderNo status 然后update进去 */
        orderNoSet.forEach(item -> {
            if (orderNoAndEmployeeOrderChangeMap.containsKey(item)) {
                EmployeeOrder employeeOrder = new EmployeeOrder();
                employeeOrder.setOrderNo(item);
                employeeOrder.setStatus(orderNoAndEmployeeOrderChangeMap.get(item));
                employeeOrders.add(employeeOrder);
            }
        });
        int removeNum = entryDimissionMapper.removeApplyDimissionDateByOrderNoSet(orderNoSet);
        int num = employeeOrderMapper.updateOrderStatusByOrderNo(employeeOrders);
        /** 最后删去缓存change中的缓存
         chg_tyoe = 2  chg_method = 0  chg_status = 0
         */
        for (CompleteOrderViewVo order : orders) {
            saveEmployeeOrderLog(loginName, order.getOrderNo(), order.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION.getCode(), "项目客服取消减员:");
        }
        employeeOrderChangeMapper.deleteDimissionData(orderNoSet, EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode(), EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(), EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode());

        return num;
    }

    @Override
    public Set<Integer> getEmployeeStatus(String certNo) {
        Set<Integer> set = new HashSet<>();
        List<EmployeeEntryDimission> employeeStatus = employeeOrderMapper.getEmployeeStatus(certNo);
        employeeStatus.forEach(employeeEntryDimission -> {
            set.add(employeeEntryDimission.getStatus());
        });
        return set;
    }

    @Override
    public int updateBatchEmployeeLeave(List<CompleteOrderViewVo> orderViewVos, String updater) {
        List<String> orderNos = Lists.newArrayList();
        List<EmployeeOrderChange> employeeOrderChanges = Lists.newArrayList();
        orderViewVos.forEach(orderViewVo -> orderNos.add(orderViewVo.getOrderNo()));
        List<EmployeeEntryDimission> entryDimissions = Lists.newArrayList();
        List<String> orderNoList = orderViewVos.stream().map(CompleteOrderViewVo::getOrderNo).distinct().collect(Collectors.toList());
        List<OrderInsuranceCfg> orderInsuranceCfgs = insuanceCfgMapper.getInsuranceCfgsByOrderList(orderNoList);
        Map<String, List<OrderInsuranceCfg>> orderNoAndOrdInsCfgMap = orderInsuranceCfgs.stream().collect(Collectors.groupingBy(OrderInsuranceCfg::getOrderNo));
        /**服务费*/
        List<OrderServiceChargeVo> serviceChargeVos = serviceChargeService.getListByOrderNoSet(Sets.newHashSet(orderNoList));
        Map<String, List<OrderServiceChargeVo>> orderNoAndChargeMap = serviceChargeVos.stream().collect(Collectors.groupingBy(OrderServiceChargeVo::getOrderNo));

        Wrapper<EmployeeOrder> wrapper = new EntityWrapper<>();
        wrapper.setSqlSelect("order_no")
                .in("order_no", orderNos)
                .eq("del_flag", "N")
                .in("status", Lists.newArrayList(EmployeeOrderStatus.SUB_NEED_RECEIVING_CONFIRM.getCode(),
                        EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode(),
                        EmployeeOrderStatus.SUB_FINISHED.getCode(),
                        EmployeeOrderStatus.DIMISSION_RECEIVING_REJECT.getCode()));
        List<EmployeeOrder> employeeOrders = employeeOrderMapper.selectList(wrapper);
        Set<String> orderNoSet = employeeOrders.stream().map(EmployeeOrder::getOrderNo).collect(Collectors.toSet());
        if (orderNoSet.size() > 0) {
            throw new OrderDimissionException("申请离职的人中有已经处于离职的人,请刷新重试!");
        }
        /** 过滤已经有  减员等待接单方确认 减员等待派单方确认 离职完成 减员接单方驳回 这四种状态的人*/
        orderViewVos = orderViewVos.stream().filter(item -> !orderNoSet.contains(item.getOrderNo())).collect(Collectors.toList());
        orderViewVos.forEach(orderViewVo -> {
            if (orderViewVo.getEhrEmployeeOrderId() != null) {
                EhrEmployeeOrderVo ehrEmployeeOrderVo = epEmployeeOrderWrapperService.selectOrderById(orderViewVo.getEhrEmployeeOrderId());
                StaffingState staffingStateByCode = StaffingState.getStaffingStateByCode(ehrEmployeeOrderVo.getStaffingState());
                switch (Objects.requireNonNull(staffingStateByCode)) {
                    case REDUCTION_CANCEL:
                        throw new OrderDimissionException("该员工减员已被企业端取消");
                    case REDUCTION_HANDLE:
                        throw new OrderDimissionException("该员工减员受理中");
                    case REDUCTION_PROCESSED:
                        throw new OrderDimissionException("该员工减员已受理");
                }
                ehrEmployeeOrderVo.setId(orderViewVo.getEhrEmployeeOrderId());
                ehrEmployeeOrderVo.setStaffingState(StaffingState.REDUCTION_HANDLE.getCode());
                ehrEmployeeOrderVo.setUpdater(updater);
                epEmployeeOrderWrapperService.updateStaffingState(ehrEmployeeOrderVo);
            }
            String orderNo = orderViewVo.getOrderNo();
            /** 将entryDimission数据放入 */
            EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = new EmpOrderEntryDimissionsInsCfg();
            EmployeeEntryDimission employeeEntryDimission = entryDimissionMapper.selectByOrderNo(orderNo);
            empOrderEntryDimissionsInsCfg.setOldEmployeeEntryDimission(employeeEntryDimission);
            /** 入离职表只改变申请时间,其它由落地时改变 */
            employeeEntryDimission.setApplyDimissionDate(new Date());
            entryDimissions.add(employeeEntryDimission);
            EmployeeEntryDimission entryDimission = getEmployeeEntryDimission(updater, orderViewVo);
            /** 将新entryDimission数据放入,最后确认之后落地 */
            empOrderEntryDimissionsInsCfg.setNewEmployeeEntryDimission(entryDimission);

            /** ----将原来的数据放入 -----*/
            if (orderNoAndOrdInsCfgMap.containsKey(orderNo)) {
                empOrderEntryDimissionsInsCfg.setOldOrderInsuranceCfgList(orderNoAndOrdInsCfgMap.get(orderNo));
            }
            /** ----将原来的数据放入 -----*/
            if (orderNoAndChargeMap.containsKey(orderNo)) {
                empOrderEntryDimissionsInsCfg.setOldOrderServiceChargeVoList(orderNoAndChargeMap.get(orderNo));
            }
            /** 处理产品 */
            setNewCfg(orderNoAndOrdInsCfgMap, orderViewVo, orderNo, empOrderEntryDimissionsInsCfg);
            /**记录新的服务费*/
            setNewChargeFee(orderNoAndChargeMap, orderViewVo, orderNo, empOrderEntryDimissionsInsCfg);

            // 新增离职操作日志
            saveEmployeeOrderLog(updater, orderNo, orderViewVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION.getCode(), "减员派单方申请：");
            if (StringUtils.isNotBlank(entryDimission.getDimissionRemark())) {
                saveEmployeeOrderLog(updater, orderNo, orderViewVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode(), "减员派单方申请备注：" + entryDimission.getDimissionRemark().replaceAll("\n", "  ") + " ");
            }
            /** ----将原来的数据放入 -----*/
            EmployeeOrderVo employeeOrderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
            empOrderEntryDimissionsInsCfg.setOldEmployeeOrder(employeeOrderVo);
            empOrderEntryDimissionsInsCfg.setOrderNo(orderNo);
            EmployeeOrderChange employeeOrderChange = new EmployeeOrderChange();
            employeeOrderChange.setOrderNo(orderNo);
            employeeOrderChange.setChgContent(JsonUtil.beanToJson(empOrderEntryDimissionsInsCfg));
            /**
             变更方式   chg_method
             1	非公积金变更
             2	变更社保公积金
             3	变更账单月
             变更状态   chg_status
             1	未变更
             2	变更等待派单方确认
             3	变更完成	订单
             变更类型   chg_type
             1   个人订单变更
             2   离职驳回缓存
             */
            /** chgmethod= 0 chgstatus = 0 表示离职缓存刚存入,库中离职时间还未落地 */
            employeeOrderChange.setChgMethod(EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode());
            employeeOrderChange.setChgStatus(EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode());
            employeeOrderChange.setChgType(EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
            employeeOrderChange.setCreator(updater);
            employeeOrderChanges.add(employeeOrderChange);
        });
        int updateNum = entryDimissionMapper.updateApplyDimissionDateByOrderNo(entryDimissions, updater);
        int count = employeeOrderMapper.updateStatusBatch(orderNos, EmployeeOrderStatus.SUB_NEED_RECEIVING_CONFIRM.getCode(), updater);
        /**将老数据放入 employeeOrderChange中方便后面调出还原 */
        List<String> orderNoS = employeeOrderChanges.stream().map(EmployeeOrderChange::getOrderNo).distinct().collect(Collectors.toList());
        List<String> tableHavingOrderNo = employeeOrderChangeMapper.selectOrderNoByOrderNoAndChgMethodAndChgType(orderNoS, EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(), EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode());
        for (int i = employeeOrderChanges.size() - 1; i >= 0; i--) {
            if (tableHavingOrderNo.contains(employeeOrderChanges.get(i).getOrderNo())) {
                employeeOrderChanges.remove(i);
            }
        }
        List<EmployeeOrderChange> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeOrderChanges)) {
            insertList = employeeOrderChanges.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmployeeOrderChange::getOrderNo))), ArrayList::new));
            employeeOrderChangeMapper.batchSavePersonOrderChange(insertList);
        }

        return count;
    }

    private void setNewChargeFee(Map<String, List<OrderServiceChargeVo>> orderNoAndChargeMap, CompleteOrderViewVo orderView, String orderNo, EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg) {
        OrderServiceChargeVo chargeVo = orderView.getChargeVo();
        if (Objects.nonNull(chargeVo)) {
            OrderServiceChargeVo charge = new OrderServiceChargeVo();
            BeanUtils.copyProperties(chargeVo, charge);
            if (charge.getRevEndMonth() == null) {
                // 如果产品的收费截止月为空 则等于员工的收费截止月
                charge.setRevEndMonth(chargeVo.getRevEndMonth());
            }
            empOrderEntryDimissionsInsCfg.setNewOrderServiceChargeVo(charge);
        } else {
            // 如为空  则产品的收费截止月等于员工的收费截止月
            List<OrderServiceChargeVo> chargeVoList = orderNoAndChargeMap.get(orderNo);
            if (CollectionUtils.isNotEmpty(chargeVoList)) {
                OrderServiceChargeVo vo = chargeVoList.stream().max(Comparator.comparingInt(OrderServiceChargeVo::getRevStartMonth)).get();
                OrderServiceChargeVo cache = new OrderServiceChargeVo();
                BeanUtils.copyProperties(vo, cache);
                cache.setRevEndMonth(orderView.getExpiredMonth());
                /** ----将原来的数据放入 -----*/
                empOrderEntryDimissionsInsCfg.setNewOrderServiceChargeVo(cache);
            }
        }
    }

    @Override
    public int editRejectDimission(List<CompleteOrderViewVo> orders, String loginName) {
        /** 获取change数据 */
        Set<String> orderNoSet = orders.stream().map(CompleteOrderViewVo::getOrderNo).collect(Collectors.toSet());
        List<EmployeeOrderChange> employeeOrderChanges = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrders(orderNoSet);
        List<EmployeeEntryDimission> entryDimissions = Lists.newArrayList();
        Map<String, String> orderNoAndChgContentMap = employeeOrderChanges.stream().filter(item -> item.getChgStatus()
                        .equals(EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode())
                        && item.getChgMethod().equals(EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode())
                        && item.getChgType().equals(EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode()))
                .collect(toMap(EmployeeOrderChange::getOrderNo, EmployeeOrderChange::getChgContent));
        List<OrderInsuranceCfg> orderInsuranceCfgs = insuanceCfgMapper.getInsuranceCfgsByOrderList(new ArrayList<>(orderNoSet));
        Map<String, List<OrderInsuranceCfg>> orderNoAndOrdInsCfgMap = orderInsuranceCfgs.stream().collect(Collectors.groupingBy(OrderInsuranceCfg::getOrderNo));
        List<EmployeeOrderChange> editEmployeeOrderChanges = Lists.newArrayList();
        for (CompleteOrderViewVo orderView : orders) {
            String orderNo = orderView.getOrderNo();
            EmployeeOrderChange employeeOrderChange = new EmployeeOrderChange();
            employeeOrderChange.setOrderNo(orderNo);
            String empOrderEntryDimissionsInsCfgJson = null;
            if (orderNoAndChgContentMap.containsKey(orderNo)) {
                /** 这里的代码是更新 entryDimission表中的applyDimissionDate */
                EmployeeEntryDimission employeeEntryDimission = new EmployeeEntryDimission();
                employeeEntryDimission.setOrderNo(orderNo);
                Date nowDate = new Date();
                employeeEntryDimission.setApplyDimissionDate(nowDate);
                entryDimissions.add(employeeEntryDimission);
                /** 下面代码是放进缓存表里 */
                String chgContent = orderNoAndChgContentMap.get(orderNo);
                EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
                EmployeeEntryDimission entryDimission = getEmployeeEntryDimission(loginName, orderView);
                entryDimission.setApplyDimissionDate(nowDate);
                empOrderEntryDimissionsInsCfg.setNewEmployeeEntryDimission(entryDimission);
                setNewCfg(orderNoAndOrdInsCfgMap, orderView, orderNo, empOrderEntryDimissionsInsCfg);
                empOrderEntryDimissionsInsCfgJson = JsonUtil.beanToJson(empOrderEntryDimissionsInsCfg);
                employeeOrderChange.setChgContent(empOrderEntryDimissionsInsCfgJson);
                editEmployeeOrderChanges.add(employeeOrderChange);
            }
            saveEmployeeOrderLog(loginName, orderNo, orderView.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION.getCode(), "派单方修改离职驳回信息提交：");
        }
        int updateNum = entryDimissionMapper.updateApplyDimissionDateByOrderNo(entryDimissions, loginName);

        int count = employeeOrderChangeMapper.batchUpdateDataByStatusAndOrderNo(editEmployeeOrderChanges);
        int employeeOrderUpdateNum = employeeOrderMapper.updateStatusBatch(new ArrayList<>(orderNoSet), EmployeeOrderStatus.SUB_NEED_RECEIVING_CONFIRM.getCode(), loginName);
        return count;
    }

    private void setNewCfg(Map<String, List<OrderInsuranceCfg>> orderNoAndOrdInsCfgMap, CompleteOrderViewVo orderView, String orderNo, EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg) {
        List<OrderInsuranceCfgVo> oldInsuranceList = orderView.getInsuranceList();
        if (CollectionUtils.isNotEmpty(oldInsuranceList)) {
            List<OrderInsuranceCfg> insuranceCfgs = Lists.newArrayList();
            oldInsuranceList.forEach(insuranceCfgVo -> {
                OrderInsuranceCfg insuranceCfg = new OrderInsuranceCfg();
                BeanUtils.copyProperties(insuranceCfgVo, insuranceCfg);
                if (insuranceCfg.getExpiredMonth() == null) {
                    // 如果产品的收费截止月为空 则等于员工的收费截止月
                    insuranceCfg.setExpiredMonth(orderView.getExpiredMonth());
                }
                insuranceCfgs.add(insuranceCfg);
            });
            empOrderEntryDimissionsInsCfg.setNewOrderInsuranceCfgList(insuranceCfgs);
        } else {
            // 如为空  则产品的收费截止月等于员工的收费截止月
            List<OrderInsuranceCfg> insuranceCfgs = orderNoAndOrdInsCfgMap.get(orderNo);
            List<OrderInsuranceCfg> insuranceCfgsCache = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(insuranceCfgs)) {
                for (OrderInsuranceCfg insuranceCfg : insuranceCfgs) {
                    OrderInsuranceCfg insuranceCfgCache = new OrderInsuranceCfg();
                    BeanUtils.copyProperties(insuranceCfg, insuranceCfgCache);
                    if (null == insuranceCfg.getExpiredMonth() || insuranceCfg.getExpiredMonth() > orderView.getExpiredMonth()) {
                        insuranceCfgCache.setExpiredMonth(orderView.getExpiredMonth());
                    }
                    insuranceCfgsCache.add(insuranceCfgCache);
                }
                /** ----将原来的数据放入 -----*/
                empOrderEntryDimissionsInsCfg.setNewOrderInsuranceCfgList(insuranceCfgsCache);
            }
        }
    }

    private EmployeeEntryDimission getEmployeeEntryDimission(String loginName, CompleteOrderViewVo orderView) {
        EmployeeEntryDimission entryDimission = new EmployeeEntryDimission();
        BeanUtils.copyProperties(orderView, entryDimission);
        entryDimission.setStatus(orderView.getEedStatus());
        entryDimission.setApplyDimissionDate(new Date());
        entryDimission.setCallFlag(orderView.getLeaveCallFlag());
        entryDimission.setDimissionDate(DateUtil.getAppointDate(orderView.getDimissionDate()));
        entryDimission.setApplyDimissionMan(loginName);
        entryDimission.setUpdater(loginName);
        return entryDimission;
    }

    @Override
    public int updateBatchByOrderNoAndGroupRatioId(ChangeTempletInfoVo changeTempletInfoVo, String updater, Map<String, Long> orderNoAndCtoIdMap) {
        /**
         * 改造后的逻辑 每个产品对应不同的收费模板
         */
        List<Integer> productList = changeTempletInfoVo.getNewList().stream().map(OrderInsuranceCfgVo::getProductCode).collect(Collectors.toList());
        Map<Integer, OrderInsuranceCfgVo> productCodeAndOrderInsuranceCfgVoMap = changeTempletInfoVo.getNewList().stream().collect(Collectors.toMap(OrderInsuranceCfgVo::getProductCode, Function.identity()));
        /**
         * 这些就是要更改的数据
         * 给每条订单加上服务费
         *
         */
        List<OrderInsuranceCfgVo> orderServiceChargeVoByStartMonthAndProductCode = insuanceCfgMapper.getOrderServiceChargeVoByProductCode(changeTempletInfoVo.getOrderNos(), productList);
        if (CollectionUtils.isNotEmpty(orderServiceChargeVoByStartMonthAndProductCode)) {
            Map<String, List<OrderInsuranceCfgVo>> orderNoAndOrderInsuranceCfgVoMap = orderServiceChargeVoByStartMonthAndProductCode.stream().collect(groupingBy(OrderInsuranceCfgVo::getOrderNo));
            for (String orderNo : orderNoAndOrderInsuranceCfgVoMap.keySet()) {
                List<OrderInsuranceCfgVo> orderInsuranceCfgVos = orderNoAndOrderInsuranceCfgVoMap.get(orderNo);
                OrderInsuranceCfgVo orderInsuranceCfgVo = new OrderInsuranceCfgVo();
                BeanUtils.copyProperties(orderInsuranceCfgVos.get(0), orderInsuranceCfgVo);
                orderInsuranceCfgVo.setProductCode(-1);
                orderInsuranceCfgVos.add(orderInsuranceCfgVo);
            }
            // 将更新后的列表赋值给原始列表
            orderServiceChargeVoByStartMonthAndProductCode = orderNoAndOrderInsuranceCfgVoMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }

        //先更改order_insuance_cfg表
        for (OrderInsuranceCfgVo orderInsuranceCfgVo : orderServiceChargeVoByStartMonthAndProductCode) {
            OrderInsuranceCfgVo orderInsuranceCfgVoByMap = productCodeAndOrderInsuranceCfgVoMap.get(orderInsuranceCfgVo.getProductCode());
            insuanceCfgMapper.updateTempletAndFeeIdByid(orderInsuranceCfgVoByMap.getTempletId(), orderInsuranceCfgVoByMap.getRevTempId(), orderInsuranceCfgVo.getOrderNo(), updater, orderInsuranceCfgVo.getProductCode());
            OrderInsuranceCfgVo serviceFeeVo = productCodeAndOrderInsuranceCfgVoMap.get(-1);
            //更改order_service_change
            chargeMapper.updateByOrderNo(serviceFeeVo.getTempletId(), serviceFeeVo.getRevTempId(), orderInsuranceCfgVo.getOrderNo(), updater);
            Long ctoId = orderNoAndCtoIdMap.get(orderInsuranceCfgVo.getOrderNo());
            ChangeTempletFeeIdSublist changeTempletFeeIdSublist = new ChangeTempletFeeIdSublist();
            changeTempletFeeIdSublist.setProdCode(orderInsuranceCfgVo.getProductCode());
            changeTempletFeeIdSublist.setOldFeeId(orderInsuranceCfgVo.getRevTempId());
            changeTempletFeeIdSublist.setNewFeeId(orderInsuranceCfgVoByMap.getRevTempId());
            changeTempletFeeIdSublist.setCtoId(String.valueOf(ctoId));
            changeTempletFeeIdSublistMapper.insert(changeTempletFeeIdSublist);
        }

        return 1;

    }

    @Override
    public void updateConfirmLeave(CompleteOrderViewVo orderViewVo) {
        String orderNo = orderViewVo.getOrderNo();
        EmployeeOrderVo orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
        if (orderVo != null) {
            String remark = "减员派单方最终确认：";
            String dimissionRemark = "减员派单方最终确认备注：";
            if (orderViewVo.getOrderStatus() == EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode()) {
                remark = "减员接单方确认：";
                dimissionRemark = "减员接单方确认备注：";
                if (CollectionUtils.isNotEmpty(orderViewVo.getInsuranceList())) {
                    /** 获取到退费年月 */
                    Long revTempId = orderViewVo.getInsuranceList().get(0).getRevTempId();
                    BillTempletFeeCfgVo feeTemplate = templetWrapperService.getFeeTempletById(revTempId);
                    /** 获取到收费截止月和退费账单月  然后作为newCfg 存入employeeOrderChange表*/
                    List<OrderInsuranceCfgVo> insuranceCfgForPageList = orderViewVo.getInsuranceList();
                    List<OrderInsuranceCfg> newOrderInsuranceCfgList = Lists.newArrayList();
                    insuranceCfgForPageList.forEach(item -> {
                        OrderInsuranceCfg orderInsuranceCfg = new OrderInsuranceCfg();
                        BeanUtils.copyProperties(item, orderInsuranceCfg);
                        orderInsuranceCfg.setLastMonth(orderInsuranceCfg.getExpiredMonth());
                        /** 前台未填写则自动填写  如果填写了要判断是否是出账单月*/

                        Integer returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), item.getExpiredMonth());
                        if (item.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), item.getReturnMonth()) && item.getReturnMonth() > returnMonth) {
                            /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                            returnMonth = item.getReturnMonth();
                        }
                        orderInsuranceCfg.setReturnMonth(returnMonth);
                        newOrderInsuranceCfgList.add(orderInsuranceCfg);
                    });
                    /** 根据orderNo获取change表数据 然后将cfg缓存放入,下一步落地*/
                    EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(
                            orderNo,
                            EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                            EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                            EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
                    String chgContent = employeeOrderChange.getChgContent();
                    EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
                    empOrderEntryDimissionsInsCfg.setNewOrderInsuranceCfgList(newOrderInsuranceCfgList);
                    empOrderEntryDimissionsInsCfg.setNewOrderServiceChargeVo(orderViewVo.getChargeVo());
                    chgContent = JsonUtil.beanToJson(empOrderEntryDimissionsInsCfg);
                    employeeOrderChange.setChgContent(chgContent);
                    employeeOrderChangeMapper.updateChgContentById(employeeOrderChange);
                }
            } else if (orderViewVo.getOrderStatus() == EmployeeOrderStatus.SUB_FINISHED.getCode()) {
                /** 最后落地 */
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = sdf.format(new Date());
                try {
                    Date parse = sdf.parse(format);
                    entryDimissionMapper.updateApplyConfirmTimeByOrderNo(parse, orderNo);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(
                        orderNo,
                        EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                        EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                        EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
                String chgContent = employeeOrderChange.getChgContent();
                EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(chgContent, EmpOrderEntryDimissionsInsCfg.class);
                EmployeeEntryDimission entryDimission = empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
                OrderServiceChargeVo newChargeVo = empOrderEntryDimissionsInsCfg.getNewOrderServiceChargeVo();
                entryDimission.setStatus(PersonOrderEnum.EntryDimissionStatus.DIMISSION.getCode());
                entryDimissionMapper.updateByOrderNoSelective(entryDimission);
                /** 将cfg数据落地 */
                List<OrderInsuranceCfg> orderInsuranceCfgs = empOrderEntryDimissionsInsCfg.getNewOrderInsuranceCfgList();
                Long revTempId = orderInsuranceCfgs.get(0).getRevTempId();
                BillTempletFeeCfgVo feeTemplate = templetWrapperService.getFeeTempletById(revTempId);
                List<OrderInsuranceCfgVo> insuranceCfgForPageList = orderViewVo.getInsuranceList();
                Map<Long, Integer> insCfgIdAndReturnMonthMap = new HashMap<>();
                for (OrderInsuranceCfgVo orderInsuranceCfgVo : insuranceCfgForPageList) {
                    insCfgIdAndReturnMonthMap.put(orderInsuranceCfgVo.getId(), orderInsuranceCfgVo.getReturnMonth());
                }
                for (OrderInsuranceCfg orderInsuranceCfg : orderInsuranceCfgs) {
                    /** 后面增加需求  需要项目确认的时候可以改退费月 */
                    if (insCfgIdAndReturnMonthMap.containsKey(orderInsuranceCfg.getId()) && insCfgIdAndReturnMonthMap.get(orderInsuranceCfg.getId()) != null) {
                        orderInsuranceCfg.setReturnMonth(insCfgIdAndReturnMonthMap.get(orderInsuranceCfg.getId()));
                    }
                    Integer returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), orderInsuranceCfg.getExpiredMonth());
                    if (orderInsuranceCfg.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), orderInsuranceCfg.getReturnMonth()) && orderInsuranceCfg.getReturnMonth() > returnMonth) {
                        /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                        returnMonth = orderInsuranceCfg.getReturnMonth();
                    }
                    orderInsuranceCfg.setReturnMonth(returnMonth);
                    orderInsuranceCfg.setLastMonth(orderInsuranceCfg.getExpiredMonth());
                    /** 前台未填写则自动填写  如果填写了要判断是否是出账单月*/
                }
                List<OrderInsuranceCfg> oldCfgData = insuanceCfgMapper.getInsuranceCfgsByOrderNo(orderNo);
                insuanceCfgMapper.updateBatchById(orderInsuranceCfgs);
                /**修改服务费*/
                updateCharges(orderVo.getOrderNo(), newChargeVo, entryDimission.getExpiredMonth());
                /** 修改change表数据  如果为离职缓存 最终数据为 chg_method = 0 chg_status = 3  chg_type = 2*/
                employeeOrderChangeMapper.updateChgStatusById(employeeOrderChange.getId(), EmployeeOrderChangeEnum.ChgStatus.CHANGE_SUCCESS.getCode());
                iSupplierPracticeService.updateSupplierPractice(orderNo, orderViewVo.getLoginName(), orderInsuranceCfgs, oldCfgData);
                EhrEmployeeOrderVo ehrEmployeeOrderVo = epEmployeeOrderWrapperService.getOrder(orderNo);
                if (ehrEmployeeOrderVo != null) {
                    ehrEmployeeOrderVo.setStaffingState(StaffingState.REDUCTION_PROCESSED.getCode());
                    ehrEmployeeOrderVo.setUpdater(orderViewVo.getLoginName());
                    epEmployeeOrderWrapperService.updateStaffingState(ehrEmployeeOrderVo);
                }

            }
            // 新增离职操作日志
            saveEmployeeOrderLog(orderViewVo.getLoginName(), orderNo, orderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION.getCode(), remark);
            if (StringUtils.isNotBlank(orderViewVo.getOrderLogRemark())) {
                dimissionRemark += orderViewVo.getOrderLogRemark();
                saveEmployeeOrderLog(orderViewVo.getLoginName(), orderNo, orderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode(), dimissionRemark.replaceAll("\n", "  ") + " ");
            }
            //更改状态
            EmployeeOrder employeeOrder = new EmployeeOrder();
            employeeOrder.setOrderNo(orderNo);
            employeeOrder.setStatus(orderViewVo.getOrderStatus());
            employeeOrder.setUpdater(orderViewVo.getLoginName());
            employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
        }
    }

    @Override
    public void updateByOrderNo(PersonOrderQueryVo vo) {
        // 修改订单数据
        EmployeeOrder employeeOrder = new EmployeeOrder();
        employeeOrder.setOrderNo(vo.getOrderNo());
        boolean ifEdit = false;
        if (StringUtils.isNotEmpty(vo.getCardFlag())) {
            employeeOrder.setCardFlag(Integer.valueOf(vo.getCardFlag()));
            ifEdit = true;
        }
        if (StringUtils.isNotEmpty(vo.getStorageFlag())) {
            employeeOrder.setStorageFlag(Integer.valueOf(vo.getStorageFlag()));
            ifEdit = true;
        }
        if (StringUtils.isNotEmpty(vo.getMedicalFlag())) {
            employeeOrder.setMedicalFlag(Integer.valueOf(vo.getMedicalFlag()));
            ifEdit = true;
        }
        if (StringUtils.isNotBlank(vo.getHousehold())) {
            employeeOrder.setHousehold(vo.getHousehold());
            ifEdit = true;
        }
        if (ifEdit && StringUtils.isNotEmpty(vo.getOrderNo())) {
            employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
        }
        // 新增或修改订单机动分类
        saveOrEditOrderRemark(vo);
        // 新增变更日志
        saveEmployeeOrderLog(vo.getOperator(), vo.getOrderNo(), Long.valueOf(vo.getEmployeeId()), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), "派单方订单变更：");
    }

    @Override
    public void updateByOrderNoList(List<EmployeeOrder> employeeOrders) {
        employeeOrderMapper.updateByOrderNoList(employeeOrders);
    }

    // 新增或修改订单机动分类
    private void saveOrEditOrderRemark(PersonOrderQueryVo vo) {
        // 查询remark数据是否存在
        EmployeeRemark remark = new EmployeeRemark();
        remark.setContractNo(vo.getContractNo());
        if (StringUtils.isNotEmpty(vo.getEmployeeId())) {
            if (StringUtils.isNotEmpty(vo.getCustId())) {
                remark.setCustomerId(Long.valueOf(vo.getCustId()));
            }
            remark.setEmployeeId(Long.valueOf(vo.getEmployeeId()));
            remark.setOrderNo(vo.getOrderNo());
            remark.setAccuAcctNo(vo.getAccuAcctNo());
            // 判断新增或者修改
            EmployeeRemark employeeRemark = remarkMapper.getByEmployeeId(vo.getOrderNo());
            remark.setRemark2(vo.getRemark2());
            remark.setRemark3(vo.getRemark3());
            remark.setRemark4(vo.getRemark4());
            remark.setRemark5(vo.getRemark5());
            if (null != employeeRemark) {
                remark.setRemark1(getRemark(vo.getRemark1(), employeeRemark.getRemark1()));
                remark.setUpdater(vo.getOperator());
                remarkMapper.updateByEmployeeIdSelective(remark);
            } else {
                remark.setRemark1(vo.getRemark1());
                remark.setCreator(vo.getOperator());
                remarkMapper.insertSelective(remark);
            }
        }
    }

    @Override
    public void insOrUpByRemark(List<PersonOrderQueryVo> vos, List<EmployeeRemark> employeeRemarks, List<EmployeeRemark> employeeRemarkList, String type) {
        boolean update = "1".equals(type);
        boolean insert = "2".equals(type);
        List<EmployeeRemark> remarksInsOrUp = Lists.newArrayList();
        List<EmployeeOrderLog> employeeOrderLogs = Lists.newArrayList();
        List<String> userNames = vos.stream().map(PersonOrderQueryVo::getOperator).collect(Collectors.toList());
        List<CommonUserVo> usersByLoginNames = userWrapperService.findUsersByLoginNames(userNames);
        List<String> loginNames = usersByLoginNames.stream().map(CommonUserVo::getLoginName).collect(Collectors.toList());

        for (int i = 0; i < vos.size(); i++) {
            PersonOrderQueryVo vo = vos.get(i);
            EmployeeRemark remark = employeeRemarkList.get(i);
            if (update) {
                EmployeeRemark employeeRemark = employeeRemarks.get(i);
                remark.setRemark1(getRemark(vo.getRemark1(), employeeRemark.getRemark1()));
                remark.setUpdater(vo.getOperator());
            }
            if (insert) {
                remark.setRemark1(vo.getRemark1());
                remark.setCreator(vo.getOperator());
            }
            remark.setRemark2(vo.getRemark2());
            remark.setRemark3(vo.getRemark3());
            remark.setRemark4(vo.getRemark4());
            remark.setRemark5(vo.getRemark5());
            Date currentDate = new Date();
            String remark1 = "派单方订单变更：";

            String userName = userNames.get(i);
            //若userName在查询出来的数据中,返回此usersByLoginName
            String userByLoginName = loginNames.contains(userName) ? usersByLoginNames.get(loginNames.indexOf(userName)).getUserName() + " " : " ";
            //当前时间字符串
            String currentDateForString = DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG);

            employeeOrderLogs.add(
                    EmployeeOrderLog.builder()
                            .delFlag("N")
                            .oprType(PersonOrderEnum.OrderLogOprType.MODIFY.getCode())
                            .creator(vo.getOperator())
                            .createTime(currentDate)
                            .updater(vo.getOperator())
                            .updateTime(currentDate)
                            .orderNo(vo.getOrderNo())
                            .employeeId(Long.valueOf(vo.getEmployeeId()))
                            .remark(remark1 + userByLoginName + currentDateForString)
                            .build()
            );
            remarksInsOrUp.add(remark);
        }
        if (update) {
            remarkMapper.updateRemarkByBatch(remarksInsOrUp);
        }
        if (insert) {
            remarkMapper.insertRemarkByBatch(remarksInsOrUp);
        }
        try {
            employeeOrderLogService.insertEmployeeOrderLogs(employeeOrderLogs);
        } catch (Exception e) {
            log.info("*****批量更新雇员订单信息日志失败*****");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Override
    public void updateRemark(List<EmployeeRemark> remarkUp) {
        remarkMapper.updateRemarkByBatch(remarkUp);
    }

    @Override
    public void insertRemark(List<EmployeeRemark> remarkIns) {
        remarkMapper.insertRemarkByBatch(remarkIns);
    }

    @Override
    public List<EmployeeRemark> getEmployeeRemarkList(List<String> orderNoList) {
        return remarkMapper.getEmployeeRemarkList(orderNoList);
    }


    /**
     将要新增的摘要数据和数据库中已存在的摘要数据通过 | 拼接到一起
     @param remark       要新增的摘要数据
     @param remarkReturn 数据库中已存在的摘要数据
     @return
     */
    private String getRemark(String remark, String remarkReturn) {
        if (StringUtil.isNotBlank(remark) && StringUtil.isBlank(remarkReturn)) {
            return remark;
        }
        if (StringUtil.isBlank(remark) && StringUtil.isNotBlank(remarkReturn)) {
            return remarkReturn;
        }
        if (StringUtil.isNotBlank(remark) && StringUtil.isNotBlank(remarkReturn)) {
            return remarkReturn + " | " + remark;
        }
        return "";
    }

    @Override
    public void saveEmployeeOrderLog(String loginName, String orderNo, Long employeeId, Integer status, String remark) {
        EmployeeOrderLog employeeOrderLog = new EmployeeOrderLog();
        employeeOrderLog.setDelFlag("N");
        employeeOrderLog.setOprType(status);
        employeeOrderLog.setCreator(loginName);
        employeeOrderLog.setCreateTime(new Date());
        employeeOrderLog.setUpdater(loginName);
        employeeOrderLog.setUpdateTime(new Date());
        employeeOrderLog.setOrderNo(orderNo);
        employeeOrderLog.setEmployeeId(employeeId);
        employeeOrderLog.setRemark(remark + userWrapperService.findUserByLoginName(loginName).getUserName() + " " + DateUtil.getString(new Date(), DateUtil.DATE_FORMAT_LONG));
        employeeOrderLogMapper.insertSelective(employeeOrderLog);
    }

    @Override
    public List<EmployeeReportDto> getDimReportByApplyDimDateAndCust(String applyDateS, String applyDateE, String custName, Integer distPlace, Integer cityCode, String receiving, String commissioner, Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList) {
        List<EmployeeReportDto> employeeReportDtos = employeeOrderMapper.getDimReportByApplyDimDateAndCust(applyDateS, applyDateE, custName, distPlace, cityCode, receiving, commissioner, accountFlag, groupName, userOrgPositionDtoList);
        Map<Long, String> allGroupRatioIdAndRatioNameMap = groupWrapperService.getAllGroupRatioIdAndRatioNameMap();
        List<EmpOrderTransferDetailVo> detailVos = iEmployeeTransferService.selectEmpOrderTransferDetails();
        Map<String, EmpOrderTransferDetailVo> transferDetailVoMap = detailVos.stream().collect(Collectors.toMap(EmpOrderTransferDetailVo::getOrderNo, Function.identity()));
        StringBuilder sb = new StringBuilder();
        List<String> orderNoList = employeeReportDtos.stream().map(EmployeeReportDto::getOrderNo).collect(Collectors.toList());
        /** chgMethod = 0 chgType = 2  chgStatus 无需设置值 */
        List<EmployeeOrderChange> employeeOrderChange = Lists.newArrayList();
        if (orderNoList.size() > 0) {
            employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrderNosAndChgTypeAndChgMethod(
                    orderNoList,
                    EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                    EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
        }
        Map<String, EmployeeEntryDimission> orderNoAndEntryDimissionMap = employeeOrderChange.stream().collect(toMap(EmployeeOrderChange::getOrderNo, item -> {
            EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(item.getChgContent(), EmpOrderEntryDimissionsInsCfg.class);
            return empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission();
        }));
        /** status == 7,8 从缓存中取出 */
        Map<String, List<OrderInsuranceCfg>> orderNoAndCfgMap = employeeOrderChange.stream().collect(toMap(EmployeeOrderChange::getOrderNo, item -> {
            EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(item.getChgContent(), EmpOrderEntryDimissionsInsCfg.class);
            return empOrderEntryDimissionsInsCfg.getNewOrderInsuranceCfgList();
        }));
        /**  status == 9 从库中查出来*/
        List<String> orderNoListForSearch = employeeReportDtos.stream().filter(item -> item.getOrderStatus() == 9).map(EmployeeReportDto::getOrderNo).collect(Collectors.toList());
        Map<String, List<OrderInsuranceCfgVo>> orderNoAndCfgMapForSearch = new HashMap<>();
        if (orderNoListForSearch.size() > 0) {
            List<OrderInsuranceCfgVo> insuranceCfgVos = insuranceCfgService.getInsuranceCfgsMapByOrderList(orderNoListForSearch);
            orderNoAndCfgMapForSearch = insuranceCfgVos.stream().collect(Collectors.groupingBy(OrderInsuranceCfgVo::getOrderNo));
        }
        Set<String> socialSecuritySet = new HashSet<>();
        Set<String> accumulationFundSet = new HashSet<>();
        for (EmployeeReportDto employeeReportDto : employeeReportDtos) {
            employeeReportDto.setAccountFlagName(CommonBooleanTypeEnum.getName(employeeReportDto.getAccountFlag()));
            List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = new ArrayList<>();
            if (employeeReportDto.getOrderStatus() == EmployeeOrderStatus.SUB_NEED_RECEIVING_CONFIRM.getCode() || employeeReportDto.getOrderStatus() == EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode()) {
                EmployeeEntryDimission employeeEntryDimission = orderNoAndEntryDimissionMap.get(employeeReportDto.getOrderNo());
                employeeReportDto.setExpiredMonth(employeeEntryDimission.getExpiredMonth());
                employeeReportDto.setDimissionDate(DateUtil.formatDateToString(employeeEntryDimission.getDimissionDate(), "yyyy-MM-dd"));
                employeeReportDto.setDimissionReason(employeeEntryDimission.getDimissionReason());
                employeeReportDto.setApplyDimissionMan(employeeEntryDimission.getApplyDimissionMan());
                if (StringUtils.isBlank(employeeReportDto.getDimissionRemark())) {
                    employeeReportDto.setDimissionRemark(employeeEntryDimission.getDimissionRemark());
                }
                employeeReportDto.setReduceDetailReason(employeeEntryDimission.getReduceDetailReason());
                orderInsuranceCfgVoList = ListUtils.copyListBeanProperties(orderNoAndCfgMap.get(employeeReportDto.getOrderNo()), OrderInsuranceCfgVo.class);
            } else {
                orderInsuranceCfgVoList = orderNoAndCfgMapForSearch.get(employeeReportDto.getOrderNo());
            }
            if (CollectionUtils.isNotEmpty(orderInsuranceCfgVoList)) {
//                if (filterNonFee(orderNos, conditions, employeeReportDto, orderInsuranceCfgVoList)) continue;
                Map<String, Object> flexibleDateMap = new HashMap<>();
                Map<Integer, List<OrderInsuranceCfgVo>> socialSecurityProdListByProductMap = orderInsuranceCfgVoList.stream().collect(Collectors.groupingBy(OrderInsuranceCfgVo::getProductCode));
                for (Integer productCode : socialSecurityProdListByProductMap.keySet()) {
                    String name = InsuranceIRatioProductCodeEnum.getName(productCode) + "最大收费截止月";
                    List<OrderInsuranceCfgVo> orderInsuranceCfgVos = socialSecurityProdListByProductMap.get(productCode);
                    if (productCode.equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())) {
                        String ratioName = allGroupRatioIdAndRatioNameMap.get(orderInsuranceCfgVos.get(0).getGroupRatioId());
                        employeeReportDto.setRatioName(ratioName);
                    }
                    int maxExpireMonth = orderInsuranceCfgVos.stream().mapToInt(OrderInsuranceCfgVo::getExpiredMonth).max().getAsInt();
                    flexibleDateMap.put(name, maxExpireMonth);
                    if (InsuranceIRatioProductCodeEnum.isSocialSecurityProd(productCode)) {
                        socialSecuritySet.add(name);
                    } else {
                        accumulationFundSet.add(name);
                    }
                }
                employeeReportDto.setFlexibleDateMap(flexibleDateMap);
                List<OrderInsuranceCfgVo> socialSecurityProdList = orderInsuranceCfgVoList.stream().filter(c -> InsuranceIRatioProductCodeEnum.isSocialSecurityProd(c.getProductCode())).collect(Collectors.toList());
                List<OrderInsuranceCfgVo> accumulationFundProdList = orderInsuranceCfgVoList.stream().filter(c -> InsuranceIRatioProductCodeEnum.isAccumulationFundProd(c.getProductCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(socialSecurityProdList)) {
                    employeeReportDto.setSocialSecurityExpiredMonth(socialSecurityProdList.stream().mapToInt(OrderInsuranceCfgVo::getExpiredMonth).max().getAsInt());
                }
                if (CollectionUtils.isNotEmpty(accumulationFundProdList)) {
                    employeeReportDto.setAccumulationFundExpiredMonth(accumulationFundProdList.stream().mapToInt(OrderInsuranceCfgVo::getExpiredMonth).max().getAsInt());
                }
            }

            EmpOrderTransferDetailVo vo = transferDetailVoMap.get(employeeReportDto.getOrderNo());
            if (Objects.nonNull(vo)) {
                sb.append("申报转移：").append(vo.getRemark()).append(";");
                employeeReportDto.setTransferRemark(sb.toString());
                sb.setLength(0);
            }

        }
        socialSecuritySet.addAll(accumulationFundSet);
        if (CollectionUtils.isNotEmpty(employeeReportDtos)) {
            employeeReportDtos.get(0).setFlexibleDateTitle(socialSecuritySet);
        }

        return employeeReportDtos;
    }

    /**
     过滤正常的无费用减员订单
     */
    private boolean filterNonFee(List<String> orderNos, List<String> conditions, EmployeeReportDto employeeReportDto, List<OrderInsuranceCfgVo> orderInsuranceCfgVoList) {
        String orderNo = employeeReportDto.getOrderNo();
        if (!orderNos.contains(orderNo)) {
            boolean flag = true;
            Map<Integer, List<OrderInsuranceCfgVo>> prodAndCfgMap = orderInsuranceCfgVoList.stream().collect(Collectors.groupingBy(OrderInsuranceCfgVo::getProductCode));
            for (Integer prodCode : prodAndCfgMap.keySet()) {
                List<OrderInsuranceCfgVo> isNotNullMonthCfgList = prodAndCfgMap.get(prodCode);
                boolean hasNullExpiredMonth = isNotNullMonthCfgList.stream().anyMatch(item -> item.getExpiredMonth() == null);
                if (hasNullExpiredMonth) {
                    flag = false;
                    break;
                }
                OrderInsuranceCfgVo maxExpiredMonth = isNotNullMonthCfgList.stream().max(Comparator.comparing(OrderInsuranceCfgVo::getExpiredMonth)).get();
                OrderInsuranceCfgVo minRevStartMonth = isNotNullMonthCfgList.stream().min(Comparator.comparing(OrderInsuranceCfgVo::getRevStartMonth)).get();
                if (maxExpiredMonth.getExpiredMonth() >= minRevStartMonth.getRevStartMonth()) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                conditions.add(orderNo);
                return true;
            }
        }
        return false;
    }

    @Override
    public List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving) {
        return employeeOrderMapper.getEntryReportByApplyEntryDateAndCust(applyEntryDateS, applyEntryDateE, custName, distPlace, cityCode, receiving);
    }

    @Override
    public List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust1(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving, String commissioner, Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList) {
        List<EmployeeReportDto> employeeReportDtoList = employeeOrderMapper.getEntryReportByApplyEntryDateAndCust1(applyEntryDateS, applyEntryDateE, custName, distPlace, cityCode, receiving, commissioner, accountFlag, groupName, userOrgPositionDtoList);
        return CollectionUtils.isEmpty(employeeReportDtoList) ? Lists.newArrayList() : filterTransferOrder(employeeReportDtoList);
    }

    @Override
    public List<OrderInsuranceCfgVo> getComAmtAndIndAmt(String contractNo) {
        return employeeOrderMapper.getComAmtAndIndAmt(contractNo);
    }

    @Override
    public Page<SummaryOrderVo> summaryOrder(Integer page, Integer limit, String customerId, String signCom) {
        Page<SummaryOrderVo> employeeOrderPage = new Page<>(page, limit);
        List<SummaryOrderVo> record = employeeOrderMapper.summaryOrder(employeeOrderPage, customerId, signCom);
        employeeOrderPage.setRecords(record);
        return employeeOrderPage;
    }

    @Override
    public List<CollectQueryEmployeeVo> collectQueryEmployeeInfo(CollectQueryEmployeeVo collectQueryEmployeeVo, List<String> groupRatioIdList) {
        return employeeOrderMapper.collectQueryEmployeeInfo(collectQueryEmployeeVo, groupRatioIdList);
    }

    @Override
    public boolean updateByPrimaryKeySelective(List<EmployeeOrderVo> record) {
        for (EmployeeOrderVo e : record) {
            if (e.getEmployeeName() != null && !e.getEmployeeName().matches("^[a-zA-Z0-9 ]*$"))
                e.setEmployeeName(e.getEmployeeName().replace(" ", "").replace("\t", ""));
        }
        return employeeMapper.updateEmployee(record);
    }

    @Override
    public int updateCfgAmt(EmployeeOrderVo order) {
        // Page<EmployeeOrderViewVo> employeeOrderPage = new Page<EmployeeOrderViewVo>(page, limit);
        //根据条件拉取数据
        List<EmployeeOrderViewVo> records = employeeOrderMapper.getListPageBysystem(order);
        //add guoqian 2021/1/8 0008 13:27
        //查询所有社保数据
        //List<InsuranceGroupRatioVo> listRatio = groupWrapperService.getGroupAndRatiosByFund();
        // Map<Long,List<InsuranceGroupRatioVo>> mapRatio =listRatio.stream().collect(Collectors.groupingBy(InsuranceGroupRatioVo::getId));
        for (EmployeeOrderViewVo record : records) {
            //查询员工对应的产品
            List<OrderInsuranceCfgVo> listCfg = insuanceCfgMapper.getListByOrderNo(record.getOrderNo());
            for (OrderInsuranceCfgVo orderInsuranceCfgVo : listCfg) {
                InsuranceGroupRatioVo GroupAndRatio = groupWrapperService.getGroupAndRatioById(orderInsuranceCfgVo.getGroupRatioId());
                //if(mapRatio.containsKey(orderInsuranceCfgVo.getGroupRatioId())){
                // InsuranceGroupRatioVo GroupAndRatio =  mapRatio.get(orderInsuranceCfgVo.getGroupRatioId()).get(0);
                if (GroupAndRatio != null) {
                    getSocialSecurityRatioBaseAmount(orderInsuranceCfgVo, GroupAndRatio);
                    OrderInsuranceCfg orderInsuranceCfg = new OrderInsuranceCfg();
                    BeanUtils.copyProperties(orderInsuranceCfgVo, orderInsuranceCfg);
                    insuanceCfgMapper.updateByPrimaryKeySelective(orderInsuranceCfg);
                } else {
                    log.info("该条社保比例系统中找不到GroupRatioId()" + orderInsuranceCfgVo.getGroupRatioId());
                }
                //}
            }
        }

        return 0;
    }

    @Override
    public Page<EmployeeOrderVo> getEmployeeDataForEmployeeContract(EmployeeOrderVo employeeOrderVo, Integer page, Integer limit) {
        Page<EmployeeOrderVo> employDataPage = new Page<>(page, limit);
        List<EmployeeOrderVo> employeeOrderVoList = employeeOrderMapper.getEmployeeDataForEmployeeContract(employDataPage, employeeOrderVo);
        employeeOrderVoList = employeeOrderVoList.stream().sorted(Comparator.comparing(EmployeeOrderVo::getEmployeeName)).collect(Collectors.toList());
        employDataPage.setRecords(employeeOrderVoList);
        return employDataPage;
    }

    @Override
    public EmployeeOrderVo getEmployeeDataForEmployeeContractByProperty(EmployeeOrderVo employeeOrderVo) {
        return employeeOrderMapper.getEmployeeDataForEmployeeContractByProperty(employeeOrderVo);
    }

    @Override
    public List<String> getEmployeeNoList() {
        return employeeMapper.getEmployeeNoList();
    }

    @Override
    public List<String> getAllEmployeeNoAndContractAreaNoList() {
        return employeeOrderMapper.getAllEmployeeNoAndContractAreaNoList();
    }

    @Override
    public List<String> getOrderNoByCustIdAndcontractAreaNo(String employeeNo, String contractAreaNo) {
        return employeeOrderMapper.getOrderNoByCustIdAndcontractAreaNo(employeeNo, contractAreaNo);
    }

    @Override
    public Integer findGroupRatioId(Long groupRatioId) {
        return insuanceCfgMapper.findGroupRatioId(groupRatioId);
    }

    @Override
    public List<Long> getemployeeId(String certNo) {
        return employeeMapper.selectId(certNo);
    }

    @Override
    public BigDecimal getOneTimeFeeByEmployeeIdAndHappenMonth(Long employeeId, Integer happenMonth) {
        return orderOneChargeMapper.getOneTimeFeeByEmployeeIdAndHappenMonth(employeeId, happenMonth);
    }


    @Override
    public List<StaffOnActiveDutyCustomerVo> getAllStaffOnActiveDutyByCityCode(String cityCode, Integer nowMonth, String receiving, String custNo, Integer accountFlag, String groupName, String receivingMan, String commissioner) {
        List<StaffOnActiveDutyCustomerVo> staffOnActiveDutyExportVos = employeeOrderMapper.getAllStaffOnActiveDutyByCityCode(cityCode, nowMonth, receiving, custNo, accountFlag, groupName, receivingMan, commissioner);
        return staffOnActiveDutyExportVos;
    }

    @Override
    public List<EmployeeOrderVo> getSocietyInsuranceApplicationDataByOrderNo(List<String> orderNoList) {
        return employeeOrderMapper.getSocietyInsuranceApplicationDataByOrderNo(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVosByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getEmployeeOrderVosByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getSocietyInsuranceDataByOrderNo(List<String> orderNoList) {
        List<EmployeeOrderVo> employeeOrderVos = employeeOrderMapper.getSocietyInsuranceApplicationDataByOrderNo(orderNoList);
        List<String> contractAreaNos = employeeOrderVos.stream().map(EmployeeOrderVo::getContractAreaNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(contractAreaNos)) {
            List<com.reon.hr.api.customer.vo.EmployeeContract> contractAreaNoList = contractAreaService.getContractAreaByContractAreaNoList(contractAreaNos);
            Map<String, String> map = contractAreaNoList.stream().collect(toMap(k -> k.getContractAreaNo(), v -> v.getReceiving()));
            for (EmployeeOrderVo employeeOrderVo : employeeOrderVos) {
                String receiving = map.getOrDefault(employeeOrderVo.getContractAreaNo(), "");
                employeeOrderVo.setReceiving(receiving);
            }
        }
        return employeeOrderVos;
    }

    @Override
    public int insertSelective(EmployeeOrder record) {
        return employeeOrderMapper.insertSelective(record);
    }


    @Override
    public List<EmployeeOrderVo> findAll() {
        return employeeOrderMapper.findAll();
    }

    @Override
    public List<String> getOrderNoList() {
        return employeeOrderMapper.getOrderNoList();
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderList() {
        return employeeOrderMapper.getEmployeeOrderList();
    }

    @Override
    public List<EmployeeOrderVo> getAllEmployeeOrderPartData() {
        return employeeOrderMapper.getAllEmployeeOrderPartData();
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderByOrderNos(List<String> orderNos) {
        return employeeOrderMapper.getEmployeeOrderByOrderNos(orderNos);
    }


    @Override
    public Map<String, List<OrderInsuranceCfgVo>> getComAmtAndIndAmtListMap(List<String> contractNoList) {
        List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = employeeOrderMapper.getComAmtAndIndAmtListMap(contractNoList);
        Map<String, List<OrderInsuranceCfgVo>> collect = orderInsuranceCfgVoList.stream().collect(toMap(OrderInsuranceCfgVo::getContractNo, OrderInsuranceCfgVo::getOrderInsuranceCfgVos));
        return collect;
    }

    @Override
    public Object searchEmployeeByOrderNo(List<String> orderNoList) {
        Map<String, Object> map = new HashMap<>();
        List<PersonOrderEditVo> list = employeeMapper.searchEmployeeByOrderNo(orderNoList);
//        OrderServiceChargeVo orderServiceChargeVo = chargeMapper.searchOrderChangeByOrderNo(orderNoList.get(0));
        OrderServiceChargeVo orderServiceChargeVo = selectOrderServiceChargeVo(orderNoList.get(0));
        /**amount: 展示为含税金额，查询出来的是报价单中金额，重新根据不含税 和增值税计算一下*/
        orderServiceChargeVo.setAmount(Optional.ofNullable(orderServiceChargeVo.getTaxfreeAmt()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(orderServiceChargeVo.getValTax()).orElse(BigDecimal.ZERO)));
        List<QuotationItemVo> quotationItemVoList = contractRelativeQuotationMapper.getQuteNoByOrderNo(orderNoList.get(0));
        map.put("personOrderList", list);
        map.put("notInsuranceList", orderServiceChargeVo);
        map.put("quotationList", quotationItemVoList);
        return map;
    }

    @Override
    public List<PersonOrderEditVo> getEmployeeByOrderNo(List<String> orderNoList) {
        return employeeMapper.searchEmployeeByOrderNo(orderNoList);
    }

    @Override
    public List<PerInsuranceBillVo> getListByCustIdAndTempletId(String contractNo, Long templetId, Set<String> orderNosFromChangeTempletInfo) {
        // 获取在职的员工 或者 离职
        log.info("contractNo" + contractNo + "templetId" + templetId);
        List<PerInsuranceBillVo> list = employeeOrderMapper.getListByCustIdAndTempletId(contractNo, templetId, orderNosFromChangeTempletInfo);
        return list;
    }

    private EmployeeOrderDto orderVoToOrderDto(EmployeeOrderVo orderVo) {
        EmployeeOrderDto orderDto = new EmployeeOrderDto();
        BeanUtils.copyProperties(orderVo, orderDto);
        if (orderVo != null) {
            if (StringUtils.isNotBlank(orderVo.getEntryDate())) {
                orderDto.setEntryDate(DateUtil.getAppointDate(orderVo.getEntryDate()));
            }
            if (StringUtils.isNotBlank(orderVo.getSignDate())) {
                orderDto.setSignDate(DateUtil.getAppointDate(orderVo.getSignDate()));
            }
            if (StringUtils.isNotBlank(orderVo.getStartDate())) {
                orderDto.setStartDate(DateUtil.getAppointDate(orderVo.getStartDate()));
            }
            if (StringUtils.isNotBlank(orderVo.getEndDate())) {
                orderDto.setEndDate(DateUtil.getAppointDate(orderVo.getEndDate()));
            }
            if (StringUtils.isNotBlank(orderVo.getProbaStart())) {
                orderDto.setProbaStart(DateUtil.getAppointDate(orderVo.getProbaStart()));
            }
            if (StringUtils.isNotBlank(orderVo.getProbaEnd())) {
                orderDto.setProbaEnd(DateUtil.getAppointDate(orderVo.getProbaEnd()));
            }
            if (StringUtils.isNotBlank(orderVo.getDispatchStart())) {
                orderDto.setDispatchStart(DateUtil.getAppointDate(orderVo.getDispatchStart()));
            }
            if (StringUtils.isNotBlank(orderVo.getDispatchEnd())) {
                orderDto.setDispatchEnd(DateUtil.getAppointDate(orderVo.getDispatchEnd()));
            }
//            if (StringUtils.isNotBlank(orderVo.getTotalFeeTime())) {
//                orderDto.setTotalFeeTime(DateUtil.getAppointDate(orderVo.getTotalFeeTime()));
//            }
            if (StringUtils.isNotBlank(orderVo.getDimissionDate())) {
                orderDto.setDimissionDate(DateUtil.getAppointDate(orderVo.getDimissionDate()));
            }
            if (StringUtils.isNotBlank(orderVo.getApplyDimissionDate())) {
                orderDto.setApplyDimissionDate(DateUtil.getAppointDate(orderVo.getApplyDimissionDate()));
            }
            //新增的收指挥存在一条服务费
            orderDto.setServiceCharges(orderVo.getServiceCharges());
        }
        return orderDto;
    }

    /**
     获取最近的下一个账单月
     @return
     */
    private static int getMaxMonth(int[] arr, int yearMonth) {
        int previousMonth = Integer.valueOf(String.valueOf(yearMonth).substring(4, 6));
        int month = previousMonth;//默认原值
        for (int j = 0; j < arr.length; j++) {
            if (arr[j] >= previousMonth) {//大于基本值
                if (month >= arr[j] || previousMonth == month) {
                    month = arr[j];
                    if (String.valueOf(month).length() == 1) {
                        yearMonth = Integer.valueOf(String.valueOf(yearMonth).substring(0, 4) + "0" + month);
                    } else {
                        yearMonth = Integer.valueOf(String.valueOf(yearMonth).substring(0, 4) + month);
                    }
                    break;
                }
            }
        }
        if (month > arr[arr.length - 1]) {
            month = arr[0];
            yearMonth = Integer.valueOf((Integer.valueOf(String.valueOf(yearMonth).substring(0, 4)) + 1) + "0" + month);
        }
        return yearMonth;
    }

//    private EmployeeOrderDto setUserName(EmployeeOrderDto orderDto) {
//        // 判断订单新增的角色
//        CommonUserVo currUser = userWrapperService.findUserByLoginName(orderDto.getCreator());
//        switch (currUser.getPosition()) {
//            // 项目客服经理
//            case "200":
//                orderDto.setPrjMangr(orderDto.getCreator());
//                break;
//            // 项目客服主管
//            case "200100":
//                orderDto.setPrjSupervisor(orderDto.getCreator());
//                orderDto.setPrjCs(orderDto.getCreator());
//                String prjMangr = userWrapperService.findSaleSupervisor(orderDto.getCreator());
//                if (StringUtils.isNotEmpty(prjMangr))
//                    orderDto.setPrjMangr(prjMangr);
//                break;
//            // 项目客服专员
//            case "200100100":
//                orderDto.setPrjCs(orderDto.getCreator());
//                String prjSupervisor = userWrapperService.findSaleSupervisor(orderDto.getCreator());
//                if (StringUtils.isNotEmpty(prjSupervisor))
//                    orderDto.setPrjSupervisor(prjSupervisor);
//
//                String mangr = userWrapperService.findSaleSupervisor(orderDto.getCreator());
//                if (StringUtils.isNotEmpty(mangr))
//                    orderDto.setPrjMangr(mangr);
//                break;
//            default:
//                break;
//        }
//        return orderDto;
//    }

    /**
     计算金额
     @param orderInsuranceCfgVo 员工产品信息
     @param oneByParams         社保信息
     */

    private void getSocialSecurityRatioBaseAmount(OrderInsuranceCfgVo orderInsuranceCfgVo,
                                                  InsuranceGroupRatioVo oneByParams) {
        // logger.info ("=======getSocialSecurityRatioBaseAmount()方法，批量订单导入日志=======");
        //计算个人订单个人金额
//        if (orderInsuranceCfgVo.getIndBase() != null) {
//            if (oneByParams.getIndlAdd() != null && oneByParams.getIndlAdd().compareTo(BigDecimal.ZERO) != 0) {
//                int i = oneByParams.getIndlAdd().compareTo(BigDecimal.ZERO);
//                //取值2位在四舍五入
//                if (orderInsuranceCfgVo.getIndAmt() != null && orderInsuranceCfgVo.getIndAmt().compareTo(BigDecimal.ZERO) != 0) {
//                    BigDecimal bigDecimal = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getIndBase(), oneByParams.getIndRatio(), oneByParams.getIndlAdd(), oneByParams.getIndCalcMode(), oneByParams.getIndExactVal());
//                    orderInsuranceCfgVo.setIndAmt(bigDecimal);
//                } else {
//                    orderInsuranceCfgVo.setIndAmt(BigDecimal.ZERO);
//                }
//            } else {
//                if (orderInsuranceCfgVo.getIndAmt() != null && orderInsuranceCfgVo.getIndAmt().compareTo(BigDecimal.ZERO) != 0) {
//                    BigDecimal bigDecimal = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getIndBase(), oneByParams.getIndRatio(), null, oneByParams.getIndCalcMode(), oneByParams.getIndExactVal());
//                    orderInsuranceCfgVo.setIndAmt(bigDecimal);
//                } else {
//                    //BigDecimal zero =new BigDecimal('0');
//                    orderInsuranceCfgVo.setIndAmt(BigDecimal.ZERO);
//                }
//            }
//        }
//        //计算个人订单企业金额
//        if (orderInsuranceCfgVo.getComBase() != null) {
//            if (oneByParams.getComAdd() != null && oneByParams.getComAdd().compareTo(BigDecimal.ZERO) != 0) {
//                //取值2位在四舍五入
//                if (orderInsuranceCfgVo.getComAmt() != null && orderInsuranceCfgVo.getComAmt().compareTo(BigDecimal.ZERO) != 0) {
//                    BigDecimal bigDecimal = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getComBase(), oneByParams.getComRatio(), oneByParams.getComAdd(), oneByParams.getComCalcMode(), oneByParams.getComExactVal());
//                    orderInsuranceCfgVo.setComAmt(bigDecimal);
//                } else {
//                    orderInsuranceCfgVo.setComAmt(BigDecimal.ZERO);
//                }
//            } else {
//                if (orderInsuranceCfgVo.getComAmt() != null && orderInsuranceCfgVo.getComAmt().compareTo(BigDecimal.ZERO) != 0) {
//                    BigDecimal bigDecimal = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getComBase(), oneByParams.getComRatio(), null, oneByParams.getComCalcMode(), oneByParams.getComExactVal());
//                    orderInsuranceCfgVo.setComAmt(bigDecimal);
//                } else {
//                    orderInsuranceCfgVo.setComAmt(BigDecimal.ZERO);
//                }
//            }
//        }
        CalculateArgs args = new CalculateArgs();
        args.setComArgs(orderInsuranceCfgVo.getComBase(), oneByParams.getComRatio(), oneByParams.getComAdd(), oneByParams.getComCalcMode(), oneByParams.getComExactVal());
        args.setIndArgs(orderInsuranceCfgVo.getIndBase(), oneByParams.getIndRatio(), oneByParams.getIndlAdd(), oneByParams.getIndCalcMode(), oneByParams.getIndExactVal());
        args.setSpecialFlag(oneByParams.getSpecialFlag());
        CalculateUtil.calculateAmt(args);
        if (!(orderInsuranceCfgVo.getIndAmt() != null && orderInsuranceCfgVo.getIndAmt().compareTo(BigDecimal.ZERO) != 0)) {
            args.setIndAmt(BigDecimal.ZERO);
        }
        if (!(orderInsuranceCfgVo.getComAmt() != null && orderInsuranceCfgVo.getComAmt().compareTo(BigDecimal.ZERO) != 0)) {
            args.setComAmt(BigDecimal.ZERO);
        }
        orderInsuranceCfgVo.setComAmt(args.getComAmt());
        orderInsuranceCfgVo.setIndAmt(args.getIndAmt());
        orderInsuranceCfgVo.setGroupRatioId(oneByParams.getId());
    }


    @Override
    public List<EmployeeOrderChangeVo> selectEmployeeOrderChangeByOrderNosAndChgTypeAndChgMethod(List<String> needGetForCacheNoList, Integer chgMethod, Integer chgType) {
        List<EmployeeOrderChangeVo> employeeOrderChangeVos = employeeOrderChangeMapper.selectEmployeeOrderChangeVoByOrderNosAndChgTypeAndChgMethod(
                needGetForCacheNoList,
                EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());

        return employeeOrderChangeVos;
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVoByCertNo(String no) {
        return employeeOrderMapper.getEmployeeOrderVoByCertNo(no);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVoByCertNoAndNonOrderNo(String certNo, String orderNo) {
        return employeeOrderMapper.getEmployeeOrderVoByCertNoAndNonOrderNo(certNo, orderNo);
    }

    @Override
    public List<EmployeeVo> getEmployeeListByEmployeeNoList(List<String> conditionEmployeeNoList) {
        return employeeMapper.getEmployeeListByEmployeeNoList(conditionEmployeeNoList);
    }

    @Override
    public List<String> getAllEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos(List<EmployeeOrderVo> employeeOrderVos) {
        List<String> employeeNoList = employeeOrderVos.stream().map(EmployeeOrderVo::getEmployeeNo).collect(Collectors.toList());
        List<EmployeeVo> employeeListByEmployeeNoList = employeeMapper.getEmployeeListByEmployeeNoList(employeeNoList);
        Map<String, Long> employeeNoAndIdMap = employeeListByEmployeeNoList.stream().collect(toMap(EmployeeVo::getEmployeeNo, EmployeeVo::getId));
        for (EmployeeOrderVo employeeOrderVo : employeeOrderVos) {
            employeeOrderVo.setEmployeeId(employeeNoAndIdMap.get(employeeOrderVo.getEmployeeNo()));
            employeeOrderVo.setEmployeeNo(null);
        }
        List<EmployeeOrderVo> allEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos = employeeOrderMapper.getAllEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos(employeeOrderVos);
        List<Long> employeeIdList = allEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos.stream().map(EmployeeOrderVo::getEmployeeId).collect(Collectors.toList());
        List<EmployeeVo> employeeListByEmployeeNoListTwo = Lists.newArrayList();
        Map<Long, String> employeeIdAndNoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(employeeIdList)) {
            employeeListByEmployeeNoListTwo = employeeMapper.getEmployeeListByEmployeeIdList(employeeIdList);
            employeeIdAndNoMap = employeeListByEmployeeNoListTwo.stream().collect(toMap(EmployeeVo::getId, EmployeeVo::getEmployeeNo));
        }
        List<String> employeeNoAndAreaNoList = Lists.newArrayList();
        for (EmployeeOrderVo item : allEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos) {
            item.setEmployeeNo(employeeIdAndNoMap.get(item.getEmployeeId()));
            item.setEmployeeId(null);
            employeeNoAndAreaNoList.add(item.getEmployeeNo() + "," + item.getContractAreaNo());
        }
        return employeeNoAndAreaNoList;
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVoByCustIdAndcontractAreaNo(String employeeNo, String contractAreaNo) {
        return employeeOrderMapper.getEmployeeOrderVoByCustIdAndcontractAreaNo(employeeNo, contractAreaNo);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeAndInsuranceCfgDataByOrderNos(List<String> orderNoList) {
        List<EmployeeOrderVo> employeeOrderByOrderNos = employeeOrderMapper.getEmployeeOrderByOrderNos(orderNoList);
        List<OrderInsuranceCfgVo> insuranceCfgsByOrderList = orderInsuanceCfgMapper.getInsuranceCfgVosByOrderList(orderNoList);
        Map<String, List<OrderInsuranceCfgVo>> orderNoAndCfgMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(insuranceCfgsByOrderList)) {
            orderNoAndCfgMap = insuranceCfgsByOrderList.stream().collect(Collectors.groupingBy(OrderInsuranceCfgVo::getOrderNo));
        }
        if (CollectionUtils.isNotEmpty(employeeOrderByOrderNos)) {
            for (EmployeeOrderVo employeeOrderByOrderNo : employeeOrderByOrderNos) {
                String orderNo = employeeOrderByOrderNo.getOrderNo();
                if (orderNoAndCfgMap.containsKey(orderNo)) {
                    List<OrderInsuranceCfgVo> orderInsuranceCfgs = orderNoAndCfgMap.get(orderNo);
                    employeeOrderByOrderNo.setInsurances(orderInsuranceCfgs);
                }
            }
        }
        return employeeOrderByOrderNos;
    }

    @Override
    public List<OneTimeCharge> getOneTimeChargeByOrderNos(List<String> orderNoList) {
        return orderOneChargeMapper.getOneTimeChargeByOrderNos(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getAllEmployeeOrderByCustIdList(List<Long> custIdList) {
        return employeeOrderMapper.getAllEmployeeOrderByCustIdList(custIdList);
    }

    @Override
    public List<EmployeeOrderVo> getAllContractNoAndTempletId(Integer genDate) {
        return employeeOrderMapper.getAllContractNoAndTempletId(genDate);
    }

    @Override
    public Map<String, String> getCertNoByOrderNoList(List<String> orderNoList) {

        List<EmployeeOrderVo> employeeOrderVoList = employeeOrderMapper.getCertNoByOrderNoList(orderNoList);
        Map<String, String> orderNoAndCertNoMap = employeeOrderVoList.stream().collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getCertNo));
        return orderNoAndCertNoMap;
    }

    @Override
    public Map<String, EmployeeOrderVo> getCertNoByOrderNos(List<String> orderNoList) {
        List<EmployeeOrderVo> employeeOrderVoList = employeeOrderMapper.getCertNoByOrderNoList(orderNoList);
        Map<String, EmployeeOrderVo> orderNoAndCertNoMap = employeeOrderVoList.stream().collect(toMap(EmployeeOrderVo::getOrderNo, Function.identity()));
        return orderNoAndCertNoMap;
    }

    @Override
    public Map<String, String> getNameByOrderNoList(List<String> orderNoList) {

        List<EmployeeOrderVo> employeeOrderVoList = employeeOrderMapper.getNameByOrderNoList(orderNoList);
        Map<String, String> orderNoAndCertNoMap = employeeOrderVoList.stream().collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getEmployeeName));
        return orderNoAndCertNoMap;
    }

    @Override
    public void updateByPrimaryKeySelective(Employee record) {
        if (record.getName() != null && !record.getName().matches("^[a-zA-Z0-9 ]*$"))
            record.setName(record.getName().replace(" ", "").replace("\t", ""));
        employeeMapper.updateByPrimaryKeySelective(record);
    }

    private final String DEL_FLAG_N = "N";
    private final String DEL_FLAG_Y = "Y";

    @Override
    public Map<String, Integer> getActiveEmployeeByContractNo(List<String> contractNoList) {
        Set<String> contractNoSet = Sets.newHashSet();
        contractNoSet.addAll(contractNoList);
        Map<String, Integer> contractNoAndActiveStaffNumMap = new HashMap<>();
        if (contractNoSet.size() > 0) {
            List<EmployeeOrderVo> employeeOrderVos = employeeOrderMapper.getActiveStaffByContractNo(contractNoSet);
            if (CollectionUtils.isNotEmpty(employeeOrderVos)) {
                contractNoAndActiveStaffNumMap = employeeOrderVos.stream().collect(toMap(EmployeeOrderVo::getContractNo, EmployeeOrderVo::getActiveStaffNum));
            }
        }

        return contractNoAndActiveStaffNumMap;
    }

    @Override
    public List<String> getAllOrderNoByEmpId(Long empId) {
        return employeeOrderMapper.getAllOrderNoByEmpId(empId);
    }

    @Override
    public List<EmployeeVo> getEmployeeListByEmployeeIdList(List<Long> empIdList) {
        return employeeMapper.getEmployeeListByEmployeeIdList(empIdList);
    }

    @Override
    public List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatus(int contractStatus, long start, long end) {
        return employeeOrderMapper.getAllEmployeeOrderNoByContractStatus(contractStatus, start, end);
    }

    @Override
    public Long getAllEmployeeOrderNoByContractStatusCount(int contractStatus) {
        return employeeOrderMapper.getAllEmployeeOrderNoByContractStatusCount(contractStatus);
    }

    @Override
    public List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatusAndOrderNoList(int contractStatus, List<String> dealOrderNos) {
        if (CollectionUtils.isEmpty(dealOrderNos)) {
            return Lists.newArrayList();
        }
        return employeeOrderMapper.getAllEmployeeOrderNoByContractStatusAndOrderNoList(contractStatus, dealOrderNos);
    }

    @Override
    public List<EmployeeOrderVo> getOrderNoAndCityCodeByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getOrderNoAndCityCodeByOrderNoList(orderNoList);
    }


    @Override
    public Page<EmployeeOrderVo> getAllOrderNo(Integer page, Integer limit, EmployeeOrderVo employeeOrderVo) {
        Page<EmployeeOrderVo> employeeOrderVoPage = new Page<>(page, limit);
        List<EmployeeOrderVo> allOrderNo = employeeOrderMapper.getAllOrderNo(employeeOrderVoPage, employeeOrderVo);
        // 分公司
        List<OrgVo> companys = orgnizationWrapperService.findAllCompany();
        Map<String, String> companysMap = companys.stream().collect(toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        // 供应商
        List<SupplierVo> suppliers = supplierService.getAllSuppliers();
        Map<Long, String> suppliersMap = suppliers.stream().collect(toMap(SupplierVo::getId, SupplierVo::getSupplierName));
        if (CollectionUtils.isNotEmpty(allOrderNo)) {
            for (EmployeeOrderVo orderVo : allOrderNo) {
                if (orderVo.getRecceivingType() == 0) {
                    orderVo.setReceivingName(companysMap.get(orderVo.getReceiving()));
                } else {
                    orderVo.setReceivingName(suppliersMap.get(Long.parseLong(orderVo.getReceiving())));
                }

            }

        }
        return employeeOrderVoPage.setRecords(allOrderNo);
    }

    @Override
    public List<EmployeeOrderVo> getRemarkByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getRemarkByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getOrderNoAndCertNo() {
        return employeeOrderMapper.getOrderNoAndCertNo();
    }

    @Override
    public List<EmployeeOrderVo> getAllEmployeeOrderByStatus() {
        return employeeOrderMapper.getAllEmployeeOrderByStatus();
    }

    @Override
    public List<String> getAllOrderNoByContractNoAndTempletId(String contractNo, Long templetId) {
        return employeeOrderMapper.getAllOrderNoByContractNoAndTempletId(contractNo, templetId);
    }


    @Override
    public List<EmployeeOrderVo> getAllEmpOrderByCustId(InsuranceBillAndPracDiffArgs args) {
        return employeeOrderMapper.getAllEmpOrderByCustId(args);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVoByMonthsRange(String preTime, Integer startMonth, Integer endMonth, List<String> orderNos) {
        return employeeOrderMapper.getEmployeeOrderVoByMonthsRange(preTime, startMonth, endMonth, orderNos);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderVoByCreateTime(String oneMonthBefore, String twoMonthBefore, String threeMonthBefore, String orderNo) {
        List<EmployeeOrderVo> employeeOrderVoByCreateTime = employeeOrderMapper.getEmployeeOrderVoByCreateTime(oneMonthBefore, twoMonthBefore, threeMonthBefore, orderNo);
        if (CollectionUtils.isNotEmpty(employeeOrderVoByCreateTime)) {
            List<String> orderNoList = employeeOrderVoByCreateTime.stream().map(EmployeeOrderVo::getOrderNo).distinct().collect(Collectors.toList());
            List<OrderInsuranceCfgVo> orderInsuranceCfgVos = insuanceCfgMapper.getOrderNoAndReceiveMonthTypeByOrderNoList(orderNoList);
            Map<String, Map<Integer, Integer>> orderNoAndProductCodeAndReceivewMonthTypeMap = orderInsuranceCfgVos.stream().collect(groupingBy(OrderInsuranceCfgVo::getOrderNo, toMap(OrderInsuranceCfgVo::getProductCode, OrderInsuranceCfgVo::getReceiveMonthType, (a, b) -> a)));
            for (EmployeeOrderVo item : employeeOrderVoByCreateTime) {
                item.setProductCodeAndReceiveMonthTypeMap(orderNoAndProductCodeAndReceivewMonthTypeMap.get(item.getOrderNo()));
            }
        }

        return employeeOrderVoByCreateTime;
    }

    @Override
    public Map<Long, CommInsurEmpVo> getTaxRatioByContractNoAndTemId(String contractNo, List<Long> templetId) {
        List<CommInsurEmpVo> taxRatios = employeeOrderMapper.getTaxRatioByContractNoAndTemId(contractNo, templetId);
        Map<Long, CommInsurEmpVo> map;
        try {
            map = taxRatios.stream().collect(toMap(CommInsurEmpVo::getTempletId, Function.identity()));
        } catch (Exception e) {
            log.error("合同号：{},账单模板Id：{}存在多种税率！！", contractNo, templetId.toString());
            throw new RuntimeException(e);
        }
        return map;
    }

    @Override
    public Map<Long, List<CommInsurEmpVo>> getAllTaxRatioByContractNoAndTemId(String contractNo, List<Long> templetId) {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.MONTH, -12);
        String date = DateUtil.formatDateToString(instance.getTime(), DateUtil.DATE_FORMAT_LONG);
        List<CommInsurEmpVo> taxRatios = employeeOrderMapper.getAllTaxRatioByContractNoAndTemId(contractNo, templetId, date);
        Map<Long, List<CommInsurEmpVo>> map;
        try {
            map = taxRatios.stream().collect(groupingBy(CommInsurEmpVo::getTempletId));
        } catch (Exception e) {
            log.error("合同号：{},账单模板Id：{}存在多种税率！！", contractNo, templetId.toString());
            throw new RuntimeException(e);
        }
        return map;
    }

    @Override
    public List<CommInsurEmpVo> getTaxRatioByOrderNos(List<String> orderNos) {
        return employeeOrderMapper.getTaxRatioByOrderNos(orderNos);
    }

    @Override
    public Map<Long, List<CommInsurEmpVo>> getTaxRatiosByContractNoAndTemId(String contractNo, List<Long> templetId) {
        List<CommInsurEmpVo> taxRatios = employeeOrderMapper.getTaxRatioByContractNoAndTemId(contractNo, templetId);
        return taxRatios.stream().collect(groupingBy(CommInsurEmpVo::getTempletId));
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeAndContractAreaByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getEmployeeAndContractAreaByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getEmployeeOrderByInsuranceSetNo(List<String> insuranceSetNo) {
        return employeeOrderMapper.getEmployeeOrderByInsuranceSetNo(insuranceSetNo);
    }

    @Override
    public List<PerInsuranceBillVo> getPibByOrderNoList(Set<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList))
            return Lists.newArrayList();
        return employeeOrderMapper.getPibByOrderNoList(orderNoList);
    }

    @Override
    public List<String> getOrderNosByContractAreaNos(List<String> contractAreaNos) {
        return employeeOrderMapper.getOrderNosByContractAreaNos(contractAreaNos);
    }

    @Override
    public Page<CompleteOrderViewVo> getReductionPage(Integer page, Integer limit, CompleteOrderViewVo vo) {
        Page<CompleteOrderViewVo> orderPage = new Page<>(page, limit);
        List<CompleteOrderViewVo> reductionList = getReductionPage(vo);
        orderPage.setTotal(reductionList.size());
        ListPageUtil<CompleteOrderViewVo> listPageUtil = new ListPageUtil<>(reductionList, limit);
        List<CompleteOrderViewVo> pagedList = listPageUtil.getPagedList(page);
        orderPage.setRecords(pagedList);
        return orderPage;
    }

    @Override
    public List<CompleteOrderViewVo> getReductionPage(CompleteOrderViewVo vo) {
        List<CompleteOrderViewVo> orderList = employeeOrderMapper.getReductionPage(vo);
        List<String> orderNoList = orderList.stream().map(CompleteOrderViewVo::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, EhrEmployeeOrderVo> ehrOrderVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            List<EhrEmployeeOrderVo> ehrEmployeeOrderVoList = epEmployeeOrderWrapperService.selectOnlyListByOrderNoList(orderNoList);
            Map<String, EhrEmployeeOrderVo> ehrEmployeeOrderVoMap = ehrEmployeeOrderVoList.stream().filter(
                            e -> StaffingState.REDUCE_LIST.contains(e.getStaffingState())).
                    collect(toMap(EhrEmployeeOrderVo::getOrderNo, Function.identity()));
            orderList.removeIf(c -> ehrEmployeeOrderVoMap.containsKey(c.getOrderNo()));
            if (vo.getStaffingState() != null) {
                List<String> orderNoListBySS = ehrEmployeeOrderVoList.stream().filter(e -> vo.getStaffingState().equals(e.getStaffingState()))
                        .map(EhrEmployeeOrderVo::getOrderNo).distinct().collect(Collectors.toList());
                orderList.removeIf(c -> !orderNoListBySS.contains(c.getOrderNo()));
            }
            ehrOrderVoMap = ehrEmployeeOrderVoList.stream().collect(toMap(EhrEmployeeOrderVo::getOrderNo, Function.identity()));
        }
        for (CompleteOrderViewVo completeOrderViewVo : orderList) {
            int contractType = Integer.parseInt(completeOrderViewVo.getContractType());
            completeOrderViewVo.setServiceNature(ServiceNature.getServiceNature(contractType));
            if (ehrOrderVoMap.containsKey(completeOrderViewVo.getOrderNo())) {
                EhrEmployeeOrderVo ehrEmployeeOrderVo = ehrOrderVoMap.get(completeOrderViewVo.getOrderNo());
                completeOrderViewVo.setStaffingState(ehrEmployeeOrderVo.getStaffingState());
            }
        }
        return orderList;
    }

    @Override
    public Map<Integer, Integer> getPeopleNumGroupByCityCode() {
        return employeeOrderMapper.getPeopleNumGroupByCityCode().stream().collect(Collectors.toMap(ServiceSiteCfgVo::getCityCode, ServiceSiteCfgVo::getPeopleNum));
    }

    @Override
    public List<OrderFeeVo> getOrderFee(OrderFeeParamVo orderFeeParamVo, Set<String> orderNo) {
        return employeeOrderMapper.getOrderFee(orderFeeParamVo, orderNo);
    }

    @Override
    public int getOrderFeeCount(OrderFeeVo vo) {
        return employeeOrderMapper.getOrderFeeCount(vo);
    }

    @Override
    public List<InsurancePracticeVo> getStartMonthAndEndMonthByCertNo(List<String> certNo) {
        return employeeOrderMapper.getStartMonthAndEndMonthByCertNo(certNo);
    }

    @Override
    public List<EmployeeOrderVo> findEmployeeIdByOrderNos(List<String> orderNoList) {
        return employeeOrderMapper.findEmployeeIdByOrderNos(orderNoList);
    }

    @Override
    public int updateFileStatusByOrderNo(String orderNo, String columnName) {
        return employeeOrderMapper.updateFileStatusByOrderNo(orderNo, columnName);
    }

    @Override
    public List<ServiceNumPeopleReportDto> getServiceNum(Map<String, Object> conditonMap) {
        List<Customer> customers = customerService.selectListByCustGroupId(conditonMap);
        if (CollectionUtils.isEmpty(customers))
            return Lists.newArrayList();
        Map<Long, Long> custIdAndGroupIdMap = customers.stream().filter(item -> item.getId() != null && item.getCustGroupId() != null).collect(toMap(Customer::getId, Customer::getCustGroupId, (a, b) -> b));
        Set<Long> custIdList = customers.stream().map(Customer::getId).collect(Collectors.toSet());
        conditonMap.put(ServiceNumPeopleReportVo.CUST_ID_LIST, Lists.newArrayList(custIdList));
        List<Contract> contractList = contractService.selectNoListByConditionMap(conditonMap);
        HashMap<String, Object> newConditionMap = Maps.newHashMap();
        newConditionMap.put("CONTRACT_NO_LIST", Lists.newArrayList(contractList.stream().map(Contract::getContractNo).collect(Collectors.toSet())));
        if (CollectionUtils.isEmpty(contractList))
            return Lists.newArrayList();
        List<ServiceNumPeopleReportDto> serviceNumList = employeeOrderMapper.getServiceNum(newConditionMap);
        List<String> contractNoList = serviceNumList.stream().map(ServiceNumPeopleReportDto::getContractNo).distinct().collect(Collectors.toList());
        Wrapper<Contract> wrapper = new EntityWrapper<>();
        wrapper.in("contract_no", contractNoList);
        wrapper.setSqlSelect("seller", "commissioner", "sign_place", "contract_type", "contract_no");
        List<Contract> contractLists = contractService.selectList(wrapper);
        Map<String, Contract> contractNoAndDataMap = contractLists.stream().collect(toMap(Contract::getContractNo, Function.identity(), (a, b) -> b));
        for (ServiceNumPeopleReportDto item : serviceNumList) {
            String contractNo = item.getContractNo();
            Long custId = item.getCustId();
            item.setCustGroupId(custIdAndGroupIdMap.get(custId))
                    .setSeller(contractNoAndDataMap.get(contractNo).getSeller())
                    .setCommissioner(contractNoAndDataMap.get(contractNo).getCommissioner())
                    .setSignPlace(contractNoAndDataMap.get(contractNo).getSignPlace().toString())
                    .setContractType(contractNoAndDataMap.get(contractNo).getContractType());
        }
        return serviceNumList;
    }

    @Override
    public EmployeeVo getEmployeeDataByOrderNo(String orderNo) {
        return employeeMapper.getEmployeeDataByOrderNo(orderNo);
    }

    @Override
    public QysEmpConCusAndEedDto getEmployeeAndContractAndCustomerAndEEDDataByOrder(String orderNo) {
        QysEmpConCusAndEedDto employeeAndContractAndCustomerAndEEDDataByOrder = employeeMapper.getEmployeeAndContractAndCustomerAndEEDDataByOrder(orderNo);
        if (employeeAndContractAndCustomerAndEEDDataByOrder != null) {
            EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(orderNo,
                    EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                    EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                    EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
            if (employeeOrderChange != null) {
                EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(employeeOrderChange.getChgContent(), EmpOrderEntryDimissionsInsCfg.class);
                employeeAndContractAndCustomerAndEEDDataByOrder.setDimissionReason(empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission().getDimissionReason());
            }
        }


        return employeeAndContractAndCustomerAndEEDDataByOrder;
    }

    @Override
    public List<EmployeeOrderVo> getReceivingManNameByOrderNos(List<String> orderNos) {
        return employeeOrderMapper.getReceivingManNameByOrderNos(orderNos);
    }

    @Override
    public String getCommissionerOrReceivingMan(String orderNo, String loginName) {
        EmployeeOrderVo commissionerOrReceivingMan = employeeOrderMapper.getCommissionerOrReceivingMan(orderNo);
        if (loginName.equals(commissionerOrReceivingMan.getCommissioner()) || loginName.equals(commissionerOrReceivingMan.getReceivingMan())) {
            Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
            return allUserName.get(loginName);
        }
        return null;
    }

    @Override
    public int getCountByCommissionerOrReceivingMan(String loginName, Integer status) {
        return employeeOrderMapper.getCountByCommissionerOrReceivingMan(loginName, status);
    }

    @Override
    public List<String> getReceivingByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getReceivingByOrderNoList(orderNoList);
    }

    @Override
    public Page<PersonOrderQueryDto> selectReonOrderPage(Map<String, Object> conditionMap) {
        Page<PersonOrderQueryDto> employeeOrderPage = new Page<PersonOrderQueryDto>((Integer) conditionMap.getOrDefault("pageNum", 1),
                (Integer) conditionMap.getOrDefault("pageSize", 10));
        if (CollectionUtils.isEmpty((List<Long>) conditionMap.getOrDefault("custIdList", Lists.newArrayList())))
            return new Page<PersonOrderQueryDto>();
        List<PersonOrderQueryDto> record = employeeOrderMapper.selectReonOrderPage(employeeOrderPage, conditionMap);
        employeeOrderPage.setRecords(record);
        return employeeOrderPage;

    }

    @Override
    public int getRejectOrderCountByLoginName(String loginName) {
        return employeeOrderMapper.getRejectOrderCountByLoginName(loginName);
    }


    @Override
    public Boolean judgmentCollectToTempletId(Long templetId, String contractAreaNo) {
        if (templetId == null || StringUtils.isBlank(contractAreaNo)) {
            throw new IllegalArgumentException("必填参数未填写!");
        }
        ContractAreaVo contractAreaVo = contractAreaService.fingByContractAreaNo(contractAreaNo);
        if (contractAreaVo == null) {
            // 如果找不到对应的合同区域信息，可以选择抛出异常或返回false
            throw new IllegalArgumentException("未找到对应的合同区域信息!");
        }
        // 如果当前小合同不是收集单,直接返回 false
        if (!(ExportPersonOrderEnum.IsSingleTypeTypeEnum.DISPATCH_TYPE3.getIndex() == contractAreaVo.getDispatchType())) {
            return false;
        }
        String currentReceivingMan = contractAreaVo.getReceivingMan(); // 当前接单人
        // 获取当前账单模板下的所有收集单小合同的接单人
        Set<String> collectReceiveManSet = contractAreaService.getCollectContractAreaByTempletId(templetId);
        if (collectReceiveManSet == null || collectReceiveManSet.isEmpty()) {
            // 如果没有收集单小合同，直接返回false
            return false;
        }
        // 如果接单人与收集单小合同的接单人相同，返回false
        return !collectReceiveManSet.contains(currentReceivingMan);
    }

    @Override
    public String selectReceivingManByOrderNo(String orderNo) {
        return employeeOrderMapper.selectReceivingManByOrderNo(orderNo);
    }

    @Override
    public List<OrderAndInsuranceDiffExportVo> getOrderFeeDetailByOrderNoSet(Set<String> orderNoList) {
        return employeeOrderMapper.getOrderFeeDetailByOrderNoSet(orderNoList);
    }

    @Override
    public List<String> getOrderNoListByReceivingList(List<String> contractAreaNoList) {
        return employeeOrderMapper.getOrderNoListByReceivingList(contractAreaNoList);
    }

    @Override
    public List<String> getAllOrderNoByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) return Lists.newArrayList();
        return employeeOrderMapper.getAllOrderNoByOrderNo(orderNo);
    }

    @Autowired
    private CustomerGroupWrapperService customerGroupWrapperService;
    // 预定义常量（类初始化时执行一次）
    private static final Map<String, Integer> EMPTY_STAT_MAP = Collections.emptyMap();

    @Override
    public List<EmployeeChangeReportExportVo> getEmployeeChangeReportData(String yearMonth) {
        List<EmployeeChangeReportExportVo> returnDataList = Lists.newArrayList();
        if (StringUtils.isBlank(yearMonth) || !yearMonth.matches("^\\d{4}-\\d{2}$")) return returnDataList;
        String[] split = yearMonth.split("-");
        List<EmployeeChangeReportExportDto> employeeChangeReportExportVos = employeeOrderMapper.getOrderNoByEmpIdHaveMutiOrderNo(split[0], split[1]);
        Map<Long, List<EmployeeChangeReportExportDto>> empIdAndEmpDataMap = employeeChangeReportExportVos.stream().collect(groupingBy(EmployeeChangeReportExportDto::getEmployeeId, Collectors.toList()));
        Set<Long> custIdSet = Sets.newHashSet();
        Set<String> contractNoSet = Sets.newHashSet();
        for (EmployeeChangeReportExportDto employeeChangeReportExportVo : employeeChangeReportExportVos) {
            custIdSet.add(employeeChangeReportExportVo.getCustId());
            contractNoSet.add(employeeChangeReportExportVo.getContractNo());
        }
        List<ContractVo> byContractNoList = contractService.getByContractNoList(Lists.newArrayList(contractNoSet));
        Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
        Map<Long, String> custIdAndNameMap = Maps.newHashMap();
        Map<String, String> conNoAndSellerMap = Maps.newHashMap();
        Map<String, String> contractAndContractTypeMap = Maps.newHashMap();
        Set<Long> groupIdSet = Sets.newHashSet();
        Map<String, Long> contractNoAndGroupIdMap = Maps.newHashMap();
        Map<String, String> contractNoAndCustNameMap = Maps.newHashMap();
        Map<String, Long> contractNoAndCustIdMap = Maps.newHashMap();
        byContractNoList.forEach(contract -> {
            Optional.ofNullable(contract).ifPresent(c -> {
                if (!custIdAndNameMap.containsKey(c.getCustId()) && c.getCustName() != null)
                    Optional.ofNullable(c.getCustId()).ifPresent(custId -> custIdAndNameMap.put(custId, c.getCustName()));
                if (!conNoAndSellerMap.containsKey(c.getContractNo()) && c.getSeller() != null)
                    Optional.ofNullable(c.getContractNo()).ifPresent(conNo -> {
                        conNoAndSellerMap.put(c.getContractNo(), allUserMap.getOrDefault(c.getSeller(), c.getSeller()));
                        contractNoAndGroupIdMap.put(c.getContractNo(), c.getGroupId());
                    });
                Optional.ofNullable(c.getContractType()).ifPresent(contractTypeCode -> {
                    contractAndContractTypeMap.put(c.getContractNo(), ContractType.getNameByCode(contractTypeCode));
                });
                Optional.ofNullable(c.getGroupId()).ifPresent(groupId -> groupIdSet.add(groupId));
                contractNoAndCustNameMap.putIfAbsent(c.getContractNo(), c.getCustName());
                contractNoAndCustIdMap.put(contract.getContractNo(), contract.getCustId());
            });
        });
        Map<Long, String> groupIdAndNameMapById = customerGroupWrapperService.getGroupIdAndNameMapById(Lists.newArrayList(groupIdSet));
        if (MapUtils.isEmpty(empIdAndEmpDataMap)) return Lists.newArrayList();
        /** 每个 员工id(employee_id) 下面 可能有多个 orderNo
         每个order_no 都有 entry_date 与 dimission_date
         需要找出 入职时间与离职时间一样的两条 订单
         一般 如果 同一个月 有多条离职订单 取 最早的那条  同一个月离职两次,应该是最早的那条有效
         有多条 入职订单(一个人有两条有效订单 这个一般不符合业务,但是可能是有的 )
         */
        Map<Long, Object> custIdAndDataMap = Maps.newHashMap();

        Map<String, Map<String, Integer>> contractNoAndTypeAndNumMap = Maps.newHashMap();
        List<EmployeeChangeReportExportDto> allCacheDto = Lists.newArrayList();

        Map<Long, Map<String, EmployeeChangeReportExportDto>> empIdAndFilterDataMap = Maps.newHashMap();
        empIdAndEmpDataMap.forEach((key, v) -> {
            boolean entryTip = false;
            boolean dimissionTip = false;
            Integer dimissionMonthCache = Integer.MAX_VALUE; // 月份获取
            Integer entryMonthCache = Integer.MIN_VALUE;

            EmployeeChangeReportExportDto dimDataCache = null; // 唯一的一条离职数据
            //  List<EmployeeChangeReportExportDto> entryDataListCache = Lists.newArrayList(); // 入职数据
            EmployeeChangeReportExportDto entryDataCache = null;
            String newCustomerName = "";
            String newSaleName = "";
            /** 处理同一个 员工id下的不同订单的数据
             为什么注释掉下面的代码, 本来是考虑到一个员工可能有多条入职订单,但是 需求报表的人说不管,一定要按照 入职的合同进行统计数据,所以原来的逻辑被改变
             */
            for (EmployeeChangeReportExportDto eceDto : v) {
                /** 同一订单状态不可能又在职,又离职 */
                /** 在职 */
                if (PersonOrderEnum.EntryDimissionStatus.IN_JOB.getCode() == eceDto.getEedStatus()
                        && eceDto.getEntryTip()) {  // 表示 入职时间 等于 yearMonth 已在sql中处理
                    if (eceDto.getEntryDate() > entryMonthCache) {    //取最新的入职数据
                        entryTip = true;
                        entryMonthCache = eceDto.getEntryDate();
                        entryDataCache = eceDto;
//                        entryDataListCache.add(eceDto); // 同一个月可能会入职两条不同的订单,一般情况下不会有
                        newSaleName = conNoAndSellerMap.get(eceDto.getContractNo());
                    }
                }
                /** 离职只有一条(多条暂不考虑),入职可能有多条 一般情况下为一条 */
                if (PersonOrderEnum.EntryDimissionStatus.DIMISSION.getCode() == eceDto.getEedStatus()
                        && eceDto.getDimissionTip()) {  // 表示离职时间等于 yearMonth 已在sql中处理
                    /** 获取离职日期最早那条 */
                    dimissionTip = true;
                    if (eceDto.getDimissionDate() < dimissionMonthCache) {
                        dimDataCache = eceDto;
                        dimissionMonthCache = eceDto.getDimissionDate();
                    }
                }
            }
            if (entryTip && dimissionTip) {
                allCacheDto.add(entryDataCache);
                allCacheDto.add(dimDataCache);
            }
        });

        // entryTip 表示 入职标记,上面代码已经过滤过了, 每个员工id下只有一条入职订单和一条离职订单
        //过滤出所有 入职的 合同号 与对应的 入职订单
        // 按照入职的订单进行切分   如果有两个合同的订单,入职到同一个合同, 需要进行切分
        Map<String, Set<Long>> contractNoAndEmpId = allCacheDto.stream().filter(item -> item.getEntryTip() && !item.getDimissionTip()).collect(groupingBy(EmployeeChangeReportExportDto::getContractNo, Collectors.mapping(EmployeeChangeReportExportDto::getEmployeeId, Collectors.toSet())));
        Set<Long> custIdBySearchInJobNum = allCacheDto.stream().map(EmployeeChangeReportExportDto::getCustId).collect(Collectors.toSet());
        List<EmployeeVo> employeeVosByInJobNum = employeeMapper.getInJobNumByCustList(custIdBySearchInJobNum);
        Map<Long, Integer> custIdAndInJobNumMap = employeeVosByInJobNum.stream().collect(toMap(EmployeeVo::getCustId, EmployeeVo::getInJobNum));
        contractNoAndEmpId.keySet().forEach(entryContractNo -> {
            Set<Long> empIdSet = contractNoAndEmpId.get(entryContractNo);
            List<EmployeeChangeReportExportDto> dimissionList = allCacheDto.stream().filter(item -> item.getDimissionTip() && empIdSet.contains(item.getEmployeeId())).collect(Collectors.toList());
            Map<String, List<EmployeeChangeReportExportDto>> dimissionContractNoAndDataListMap = dimissionList.stream().collect(groupingBy(EmployeeChangeReportExportDto::getContractNo));
            Set<String> dimmsionContractNoSet = dimissionList.stream().map(EmployeeChangeReportExportDto::getContractNo).collect(Collectors.toSet());

            dimmsionContractNoSet.forEach(dimissionContractNo -> {
                EmployeeChangeReportExportVo employeeChangeReportExportVo = new EmployeeChangeReportExportVo();
                employeeChangeReportExportVo.setOldCustomerName(contractNoAndCustNameMap.get(dimissionContractNo));
                employeeChangeReportExportVo.setOldSaleName(conNoAndSellerMap.get(dimissionContractNo));
                employeeChangeReportExportVo.setOldContractNo(dimissionContractNo);
                employeeChangeReportExportVo.setOldGroup(groupIdAndNameMapById.get(contractNoAndGroupIdMap.get(dimissionContractNo)));
                employeeChangeReportExportVo.setOldContractType(contractAndContractTypeMap.get(dimissionContractNo));
                employeeChangeReportExportVo.setNowMonthOutNum(dimissionContractNoAndDataListMap.get(dimissionContractNo).size());
                employeeChangeReportExportVo.setOldCustomerNum(custIdAndInJobNumMap.get(contractNoAndCustIdMap.get(dimissionContractNo)));

                employeeChangeReportExportVo.setNewCustomerName(contractNoAndCustNameMap.get(entryContractNo));
                employeeChangeReportExportVo.setNewSaleName(conNoAndSellerMap.get(entryContractNo));
                employeeChangeReportExportVo.setNewContractNo(entryContractNo);
                employeeChangeReportExportVo.setNewGroup(groupIdAndNameMapById.get(contractNoAndGroupIdMap.get(entryContractNo)));
                employeeChangeReportExportVo.setNewContractType(contractAndContractTypeMap.get(entryContractNo));
                employeeChangeReportExportVo.setNowMonthInNum(dimissionContractNoAndDataListMap.get(dimissionContractNo).size());  // 入职的数据量,就是应该根据离职的来的
                employeeChangeReportExportVo.setNewCustomerNum(custIdAndInJobNumMap.get(contractNoAndCustIdMap.get(entryContractNo)));
                returnDataList.add(employeeChangeReportExportVo);
            });
        });

        return returnDataList;
    }

    @Override
    public List<EmployeeOrderVo> getContractSignComTitleByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getContractSignComTitleByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> getContractTypeAndDisComByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.getContractTypeAndDisComByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> selectOrderByOrderNoList(Set<String> orderNoList) {
        return employeeOrderMapper.selectOrderByOrderNoList(orderNoList);
    }

    @Override
    public List<EmployeeOrderVo> selectContractTypeByOrderNoList(List<String> orderNoList) {
        return employeeOrderMapper.selectContractTypeByOrderNoList(orderNoList);
    }

    /***
     * @param employeeReports
     * @return
     */
    private List<EmployeeReportDto> filterTransferOrder(List<EmployeeReportDto> employeeReports) {
        List<EmpOrderTransferDetailVo> detailVos = iEmployeeTransferService.selectEmpOrderTransferDetails();
        Map<String, EmployeeReportDto> collect = employeeReports.stream().collect(Collectors.toMap(EmployeeReportDto::getOrderNo, Function.identity()));
        StringBuilder sb = new StringBuilder();
        detailVos.forEach(vo -> {
            if (collect.containsKey(vo.getNewOrderNo())) {
                EmployeeReportDto reportDto = collect.get(vo.getNewOrderNo());
                sb.append("申报转移：").append(vo.getRemark()).append(";");
                reportDto.setTransferRemark(sb.toString());
                sb.setLength(0);
            }
        });
        return employeeReports;
    }

    private EmployeeOrderVo selectEmployeeOrder(String... orderNos) {
        String orderNo = orderNos[0];
        String type = orderNos.length > 1 ? orderNos[1] : "";
        //如果是接单完善被驳回，此时只有一个报价单不需要进行时间过滤
        EmployeeOrderVo orderVo;
        if (type.equals("cancel")) {
            orderVo = employeeOrderMapper.selectOneByPrimaryKey(orderNo);
        } else {
            orderVo = employeeOrderMapper.selectByPrimaryKey(orderNo, DateUtil.getCurrYearMonth());
        }
        List<OrderServiceChargeVo> orderServiceChargeVos = serviceChargeService.searchOrderChangeByOrderNos(Lists.newArrayList(orderNo));
        for (OrderServiceChargeVo orderServiceChargeVo : orderServiceChargeVos) {
            orderServiceChargeVo.setContractNo(orderVo.getContractNo());
        }
        orderVo.setServiceCharges(orderServiceChargeVos);
        return orderVo;
    }

    private OrderServiceChargeVo selectOrderServiceChargeVo(String orderNo) {
        List<OrderServiceChargeVo> orderServiceChargeVos = chargeMapper.searchOrderChangeByOrderNos(Lists.newArrayList(orderNo));
        Integer currYearMonth = DateUtil.getCurrYearMonth();
        OrderServiceChargeVo orderServiceChargeVo = orderServiceChargeVos.stream().filter(
                vo -> vo.getRevStartMonth() <= currYearMonth && (vo.getRevEndMonth() == null || currYearMonth <= vo.getRevEndMonth())).findFirst().orElse(orderServiceChargeVos.get(0));
        return orderServiceChargeVo;
    }

    /**
     同一个人，当月报减且当月又报增人员，连续参保，只更换客户名称，增加是否连续转移标识
     */
    private void setTransferFlag(EmployeeOrderVo record) {
        List<PersonOrderQueryDto> personOrderQueryDtos = personOrderQueryMapper.getPersonOrderQueryDtoByCertNos(Lists.newArrayList(record.getCertNo()));
        Map<String, Integer> flagMap = new HashMap<>();
        if (personOrderQueryDtos.size() > 1) {
            /**身份证号相同 入职、离职时间间隔没有超过一个月 连续转移标识 设置为true apply_entry_time apply_dimission_date*/
            for (int i = 0; i < personOrderQueryDtos.size(); i++) {
                for (int j = 0; j < personOrderQueryDtos.size(); j++) {
                    if (i == j) {
                        continue;
                    }
                    PersonOrderQueryDto curr = personOrderQueryDtos.get(i);
                    PersonOrderQueryDto next = personOrderQueryDtos.get(j);
                    if (Objects.nonNull(curr.getQuitDate()) && Objects.nonNull(next.getApplyEntryDate())) {
                        Calendar quitDate = DateToCalendar(curr.getQuitDate());
                        Calendar applyEntryDate = DateToCalendar(next.getApplyEntryDate());
                        int currMonth = quitDate.get(Calendar.MONTH);
                        int nextMonth = applyEntryDate.get(Calendar.MONTH);
                        if (Math.abs(currMonth - nextMonth) <= 1) {
                            /**1：入职 2离职，暂时没用到*/
                            flagMap.put(curr.getOrderNo(), 1);
                            flagMap.put(next.getOrderNo(), 2);
                        }
                    }
                }
            }
        }
        record.setIsTransferFlag(flagMap.containsKey(record.getOrderNo()));
    }

    private Calendar DateToCalendar(Date applyEntryDate) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(applyEntryDate);
        return instance;
    }


    private void initPrice(QuotationDTO quotationItem, OrderServiceCharge newServiceCharge) {
        BigDecimal taxExclude;
        BigDecimal taxIncluded;
        BigDecimal ratio = BigDecimal.ONE.add(quotationItem.getTaxRatio());
        /***税前*/
        if (quotationItem.getTaxFlag().equals(QuotationEnum.TaxFlagEnum.BOOLEAN_TYPE2.getIndex())) {
            taxIncluded = quotationItem.getPrice();
            taxExclude = Optional.ofNullable(quotationItem.getPrice()).orElse(BigDecimal.ZERO).divide(ratio, 2, RoundingMode.HALF_UP);
        } else {
            taxExclude = quotationItem.getPrice();
            taxIncluded = Optional.ofNullable(quotationItem.getPrice()).orElse(BigDecimal.ZERO).multiply(ratio).setScale(2, RoundingMode.HALF_UP);
        }
        newServiceCharge.setAmount(taxIncluded);
        newServiceCharge.setTaxfreeAmt(taxExclude);
        newServiceCharge.setValTax(taxIncluded.subtract(taxExclude));
    }

    public static void main(String[] args) {
        BigDecimal taxExclude = new BigDecimal("64.94");
        BigDecimal taxIncluded = Optional.ofNullable(taxExclude).orElse(BigDecimal.ZERO).multiply(new BigDecimal("1.0683")).setScale(2, RoundingMode.HALF_UP);
        System.out.println(taxIncluded);
        System.out.println(taxExclude);
        System.out.println(taxIncluded.subtract(taxExclude));
    }


    @Override
    public List<EmployeeReportDto> getEntryOrderList() {
        Calendar calendar = Calendar.getInstance();
        // 获取上一个月的第一天
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String firstDayOfLastMonth = sdf.format(calendar.getTime());
        // 获取上个月的最后一天
        calendar.add(Calendar.MONTH, 1); // 设置回当前月
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 减一天即为上个月的最后一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        String lastDayOfLastMonth = sdf.format(calendar.getTime());
        List<String> orderNoList = iInsurancePracticeWrapperService.getOrderNoList(firstDayOfLastMonth, lastDayOfLastMonth);
        return employeeMapper.getEntryOrderList(firstDayOfLastMonth, lastDayOfLastMonth, orderNoList);
    }


    @Override
    public List<Long> getAllEmpIdByCommissionerOrReceivingMan(String loginName) {
        return employeeOrderMapper.getAllEmpIdByCommissionerOrReceivingMan(loginName);
    }
}



