package com.reon.hr.core.aspect;

import com.alibaba.fastjson.JSON;
import com.reon.hr.api.base.vo.MessageVo;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.anno.MessageReminder;
import com.reon.hr.core.strategy.factory.MessageFactory;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.web.ProducerScopeTypeWeb;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年06月06日
 * @Version 1.0
 */

@Aspect
@Component
public class MessageReminderAspect {

    @Autowired
    private MessageFactory messageFactory;
    @Autowired
    private MqMessageSender mqMessageSender;


    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @AfterReturning(value = "@annotation(com.reon.hr.core.anno.MessageReminder)", returning = "result")
    public void AfterMessageReminder(JoinPoint joinPoint, Object result) {

        /** 返回值为空或者返回失败不添加数据*/
        if (!Objects.isNull(result)) {
            LayuiReplay layuiReplay = JSON.parseObject(JSON.toJSONString(result), LayuiReplay.class);
            if (Objects.equals(layuiReplay.getCode(), 0)) {
                //获取方法签名
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                //获取注解
                MessageReminder annotation = signature.getMethod().getAnnotation(MessageReminder.class);
                //获取方法参数
                List<MessageVo> messageVoList = messageFactory.getMessageVoList(annotation.businessType().getCode(), joinPoint);
                if (CollectionUtils.isNotEmpty(messageVoList)){
                    mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BASE, ProducerScopeTypeWeb.REON_MESSAGE_INSERT, JsonUtil.beanToJson(messageVoList));
                    logger.info("rabbitmq发送消息:[lsit={}]",messageVoList);
                }

            }
        }

    }



}
