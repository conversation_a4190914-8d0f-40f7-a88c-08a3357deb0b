var ctx = ML.contextPath;
let use = layui.use(['form', 'layer', 'laydate', 'table'], function () {
        var table = layui.table;
        layer = parent.layer === undefined ? layui.layer : parent.layer;

        // 页面 加载事件
        $(document).ready(function () {
            var fileStr = $('#fileId').val();
            var fileArr = fileStr.split(",");
            for (let i = 0; i < fileArr.length; i++) {
                $.ajaxData.getCheckName(fileArr[i], 'check')
            }
        })


    })
;