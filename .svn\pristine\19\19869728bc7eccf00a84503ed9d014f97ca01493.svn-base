<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:reg="http://www.dangdang.com/schema/ddframe/reg" xmlns:job="http://www.dangdang.com/schema/ddframe/job"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.dangdang.com/schema/ddframe/reg
                        http://www.dangdang.com/schema/ddframe/reg/reg.xsd
                        http://www.dangdang.com/schema/ddframe/job
                        http://www.dangdang.com/schema/ddframe/job/job.xsd">
	<!--配置作业注册中心 -->
	<reg:zookeeper id="regCenter" server-lists="${elastic.job.zookeeperUrl}" namespace="reon-customer-job"
		base-sleep-time-milliseconds="10000" max-sleep-time-milliseconds="30000" max-retries="3" />

 	<job:simple id="contractAssignJob" class="com.reon.hr.sp.customer.job.customer.ContractAssignJob" registry-center-ref="regCenter"
		cron="0 40 23 * * ? *" description="分配客户Job" sharding-total-count="1" overwrite="true" />

	<job:simple id="customerSolutionUpgradeJob" class="com.reon.hr.sp.customer.job.customer.CustomerSolutionUpgradeJob" registry-center-ref="regCenter"
				cron="0 40 23 * * ? *" description="商保方案升级Job" sharding-total-count="1" overwrite="true" />

	<job:simple id="templetComparisonJob" class="com.reon.hr.sp.customer.job.customer.TempletComparisonJob" registry-center-ref="regCenter"
				cron="0 0 3 * * ? *" description="订单模板对比Job" sharding-total-count="1" overwrite="true" />

	<!--<job:simple id="orderDataCheckRecordJob" class="com.reon.hr.sp.customer.job.customer.OrderDataCheckRecordJob" registry-center-ref="regCenter"
				cron="0 0 1 * * ?" description="订单数据检查记录表Job" sharding-total-count="1" overwrite="true" />-->
	<job:simple id="quotationChgTaskJob" class="com.reon.hr.sp.customer.job.customer.QuotationChgTaskJob"
				registry-center-ref="regCenter"
				cron="0 0 23 * * ?" description="续签自动更新合同表默认报价单Job" sharding-total-count="1" overwrite="true"/>
	<!-- 每年的1月 1号 2号 3号 晚上11点50执行-->
	<job:simple id="ContractRefreshNewFlagJob" class="com.reon.hr.sp.customer.job.customer.ContractRefreshNewFlagJob"
				registry-center-ref="regCenter"
				cron="0 50 23 1,2,3 1 ? *" description="合同刷新新增标识" sharding-total-count="1" overwrite="true"/>
<!--	<job:simple id="custScaleStatusUpdateJob" class="com.reon.hr.sp.customer.job.customer.CustScaleStatusUpdateJob"-->
<!--				registry-center-ref="regCenter"-->
<!--				cron="0 0 10 L * ?" description="更新客户规模字段" sharding-total-count="1" overwrite="true"/>-->
	<job:simple id="ContractRefreshInitStartDateJob" class="com.reon.hr.sp.customer.job.customer.ContractRefreshInitStartDateJob"
				registry-center-ref="regCenter"
				cron="0 30 0 1 * ? *" description="合同刷新执行期" sharding-total-count="1" overwrite="true"/>

	<job:simple id="contractEndTimeAutoRefreshJob" class="com.reon.hr.sp.customer.job.customer.ContractEndTimeAutoRefreshJob"
				registry-center-ref="regCenter"
				cron="0 0 2 * * ? " description="合同自动顺延结束时间每晚2点执行" sharding-total-count="1" overwrite="true"/>

	<job:simple id="noEntryBillOrderJob" class="com.reon.hr.sp.customer.job.customer.NoEntryBillOrderJob"
				registry-center-ref="regCenter"
				cron="0 0 23 L * ?" description="每月最后一天11点生成未进入账单订单数据" sharding-total-count="1" overwrite="true"/>


	<job:simple id="salaryTaxComparisonJob" class="com.reon.hr.sp.customer.job.customer.SalaryTaxComparisonJob"
				registry-center-ref="regCenter"
				cron="0 0 1 * * ? " description="每天1点判断是否执行申报个税比对" sharding-total-count="1" overwrite="true"/>
	<job:simple id="taxDateRangeJob" class="com.reon.hr.sp.customer.job.customer.TaxDateRangeJob"
				registry-center-ref="regCenter"
				cron="0 0 1 * * ? " description="每天1点根据报税日历判断是否给财务发提醒" sharding-total-count="1" overwrite="true"/>

    <job:simple id="contractCompensationJob" class="com.reon.hr.sp.customer.job.customer.ContractCompletionReminderJob"
                registry-center-ref="regCenter" cron="0 0 1 * * ? " description="每天1点执行,合同未归档自动加1天" sharding-total-count="1" overwrite="true"/>

</beans>
