/* 全局样式 */
.app-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin: 15px;
    min-height: calc(100vh - 80px);
}

/* 宽度 */
.width-full {
    width: 100% !important;
}

.width80 {
    width: 80px !important;
}

.width120 {
    width: 120px !important;
}

.width170 {
    width: 170px !important;
}

.width180 {
    width: 180px !important;
}

.width220 {
    width: 220px !important;
}

.width320 {
    width: 320px !important;
}

.width420 {
    width: 420px !important;
}

.width620 {
    width: 620px !important;
}

/* 左margin */
.ml5 {
    margin-left: 5px;
}

.ml10 {
    margin-left: 10px;
}

.ml15 {
    margin-left: 15px;
}

.ml20 {
    margin-left: 20px;
}

/* 右margin */
.mr5 {
    margin-right: 5px;
}

.mr10 {
    margin-right: 10px;
}

.mr15 {
    margin-right: 15px;
}

.mr20 {
    margin-right: 20px;
}

/* 上margin */
.mt10 {
    margin-top: 10px;
}

.mt20 {
    margin-top: 20px;
}

.mt30 {
    margin-top: 30px;
}

/* 下margin */
.mb8{
    margin-bottom: 8px;
}

.mb10 {
    margin-bottom: 10px;
}

.mb20 {
    margin-bottom: 20px;
}

.mb30 {
    margin-bottom: 30px;
}

/* 左margin */
.pl5 {
    padding-left: 5px;
}

.pl10 {
    padding-left: 10px;
}

.pl15 {
    padding-left: 15px;
}

.padl20 {
    padding-left: 20px;
}

/* 右padding */
.pr5 {
    padding-right: 5px;
}

.pr10 {
    padding-right: 10px;
}

.pr15 {
    padding-right: 15px;
}

.pr20 {
    padding-right: 20px;
}

/* 上padding */
.pt10 {
    padding-top: 10px;
}

.pt20 {
    padding-top: 20px;
}

.pt30 {
    padding-top: 30px;
}

/* 下padding */
.pb10 {
    padding-bottom: 10px;
}

.pb20 {
    padding-bottom: 20px;
}

.pb30 {
    padding-bottom: 30px;
}

/* 超出显示省略号 */
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 超出2行显示省略号 */
.ellipsis2 {
    overflow: hidden;
    text-overflow: ellipsis;
    /* 将对象作为弹性伸缩盒子模型显示 */
    display: -webkit-box;
    /* 限制在一个块元素显示的文本的行数 */
    /* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
    -webkit-line-clamp: 2;
    line-clamp: 2;
    /* 设置或检索伸缩盒对象的子元素的排列方式 */
    -webkit-box-orient: vertical;
}

/* 字体宽度 */
.fw300 {
    font-weight: 300;
}

.fw600 {
    font-weight: 600;
}


/* 弹性盒子 */
.flex {
    display: flex;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}

.flex-around {
    display: flex;
    justify-content: space-around;
}

.flex-evenly {
    display: flex;
    justify-content: space-evenly;
}

.flex-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-around-center {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.flex-evenly-center {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.flex-center-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-center {
    display: flex;
    align-items: center;
}

.cursor-pointer {
    cursor: pointer;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}


/* form高度，溢出overflow */
.formHight {
    max-height: 75vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 10px;
}

/* 表格样式优化 */
.el-table th {
    font-size: 14px;
    background: linear-gradient(180deg, #f8f9fa 0%, #f1f3f4 100%) !important;
    color: #303133;
    font-weight: 600;
    padding: 12px 8px;
    border-bottom: 2px solid #e4e7ed;
    white-space: nowrap;
}

.el-table td {
    font-size: 14px;
    border-bottom: 1px solid #f0f2f5;
    padding: 10px 8px;
    vertical-align: middle;
}

.el-table th.el-table__cell {
    background: #F5F7FA;
}

.el-table.is-scrolling-left th.el-table-fixed-column--left {
    background: #F5F7FA;
}


/* 分页样式优化 */
.el-pagination {
    margin-top: 20px;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: flex-end;
}