<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.FinancialRequireMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.cus.FinancialRequire">
    <!--@mbg.generated-->
    <!--@Table `reon-customerdb`.financial_require-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cert_no, `month`, creator, create_time, updater, update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `reon-customerdb`.financial_require
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `reon-customerdb`.financial_require
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.customer.entity.cus.FinancialRequire" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-customerdb`.financial_require (cert_no, `month`, creator, 
      create_time, updater, update_time, 
      del_flag)
    values (#{certNo,jdbcType=VARCHAR}, #{month,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.customer.entity.cus.FinancialRequire" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-customerdb`.financial_require
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="month != null">
        `month`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="month != null">
        #{month,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.customer.entity.cus.FinancialRequire">
    <!--@mbg.generated-->
    update `reon-customerdb`.financial_require
    <set>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="month != null">
        `month` = #{month,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.customer.entity.cus.FinancialRequire">
    <!--@mbg.generated-->
    update `reon-customerdb`.financial_require
    set cert_no = #{certNo,jdbcType=VARCHAR},
      `month` = #{month,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `reon-customerdb`.financial_require
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="cert_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.certNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`month` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.month,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="del_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-customerdb`.financial_require
    (cert_no, `month`, creator )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.certNo,jdbcType=VARCHAR}, #{item.month,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectDataByCertNoAndMonth" resultType="java.lang.String">
      select concat(cert_no, '_', `month`) as cert_month from `reon-customerdb`.financial_require where
          del_flag = 'N' and (
          <foreach collection="list" separator="or" item="item" close=")" open="(">
              cert_no = #{item.certNo,jdbcType=VARCHAR} and `month` = #{item.month,jdbcType=INTEGER}
          </foreach>
      )
    </select>

  <select id="selectDataByYearMonth" resultType="java.lang.String">
  select cert_no from `reon-customerdb`.financial_require where del_flag = 'N' and month = #{yearMonth};
    </select>
</mapper>