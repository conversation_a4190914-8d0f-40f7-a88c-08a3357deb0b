package com.reon.hr.api.customer.dto.customer;

import com.alibaba.excel.annotation.ExcelProperty;
import com.reon.hr.api.customer.anno.Excel;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinancialRequireExportDto  implements Serializable {



    @Excel(name = "月份*",dataheight = 40,cellType = Excel.ColumnType.STRING)
    private Integer month;
    /**
     身份证
     */

    @Excel(name = "身份证*",dataheight = 140,cellType = Excel.ColumnType.STRING)
    private String certNo;

}
