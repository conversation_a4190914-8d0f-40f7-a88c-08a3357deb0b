var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','tableDate'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        tableDate = layui.tableDate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    //查询纯代发人员维护数据
    table.render({
        id: 'noSalaryPersonnelMaintainQueryGrid',
        elem: '#noSalaryPersonnelMaintainQueryGrid',
        url: ML.contextPath + '/customer/salary/employee/getNoSalaryEmployeeListPage',
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: 'full-200',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: 'employeeNo', title: '唯一号', width: '13%', align: 'center', fixed: 'left'},
            {field: 'name', title: '雇员姓名', width: '10%', align: 'center'},
            {field: 'certType', title: '证件类型', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter('CERT_TYPE', d.certType);
                }},
            {field: 'certNo', title: '证件号码', width: '10%', align: 'center'},
            {field: 'custNo', title: '客户编号', width: '14%', align: 'center'},
            {field: 'custName', title: '客户名称', width: '20%', align: 'center'},
            {field: 'commissioner', title: '派单客服', width: '10%', align: 'center', templet: function (d) {
                    return ML.loginNameFormater(d.commissioner)
                }},
            {field: 'salaryCommissioner', title: '薪资客服', width: '10%', align: 'center', templet: function (d) {
                    return ML.loginNameFormater(d.salaryCommissioner)
                }},
            {field: 'receivingMan', title: '接单客服', width: '10%', align: 'center', fixed: 'right', templet: function (d) {
                    return ML.loginNameFormater(d.receivingMan)
                }},
            {field: 'competitionFlag', title: '是否竞业员工', width: '10%', align: 'center', fixed: 'right', templet: function (d) {
                    return ML.dictFormatter('BOOLEAN_TYPE',d.competitionFlag)
                }}
        ]],
        done: function (res) {
            ML.hideNoAuth();
        }
    });

    //监听纯代发人员维护表格上方按钮
    table.on('toolbar(noSalaryPersonnelMaintainQueryGridFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            //修改
            case 'edit':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                open("修改", "edit", ['55%', '45%'],checkStatus.data[0].id);
                break;
            //查看更改历史
            case 'historyLog':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                open("查看更改历史", "historyLog", ['65%', '45%'], checkStatus.data[0].id);
                break;
        }
    });

    //打开窗口
    function open(title, optType, area, data) {
        var url;
        if (optType == "edit") {
            url = "/customer/salary/employee/gotoNoSalaryPersonnelMaintainEditView?employeeId="+data;
        }
        if (optType == "historyLog") {
            url = "/customer/salary/employee/gotoSalaryPersonnelMaintainQueryLogView?empId="+data;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                if($("#optType").val() === "empNo"){
                    body.find("#certType").attr("disabled","disabled");
                    body.find("#certNo").attr("disabled","disabled");
                }
                if($("#optType").val() === "certNo"){
                    body.find("#name").attr("disabled","disabled");
                    body.find("#mobile").attr("disabled","disabled");
                }
            },
            end: function () {
                reloadTable();
            }
        });
    }

    //重载数据
    function reloadTable() {
        table.reload('noSalaryPersonnelMaintainQueryGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    form.on('submit(btnQuery)', function (data) {
        table.reload('noSalaryPersonnelMaintainQueryGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
                id = item.custNo;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            $("#custNo").val(id);
        }
    });

});
