package com.reon.hr.api.customer.vo.employee;

import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PersonOrderEditVo implements Serializable {


    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户编号
     */
    private String custNo;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 雇员ID
     */
    private String employeeId;

    /**
     * 唯一号
     */
    private String employeeNo;

    /**
     * 雇员名称
     */
    private String employeeName;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 人员类型
     */
    private String categoryCode;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 合同名称
     */
    private String contractNo;
    /**
     * 小合同名称
     */
    private String contractAreaName;

    /**
     * 小合同编号
     */
    private String contractAreaNo;

    /**
     * 派单方
     */
    private String distComName;

    /**
     * 派单方客服
     */
    private String distComMan;

    /**
     * 接单方
     */
    private String receivingName;

    /**
     *
     */
    private String receiving;

    /**
     *
     */
    private String distCom;

    /**
     * 接单方客服
     */
    private String receivingMan;

    /**
     * 入职时间起
     */
    private String entryDateS;

    /**
     * 入职时间止
     */
    private String entryDateD;

    /**
     * 离职日期起
     */
    private String quitDateS;

    /**
     * 离职日期止
     */
    private String quitDateD;

    /**
     * 创建日期起
     */
    private String createTimeS;

    /**
     * 创建日期止
     */
    private String createTimeD;

    /**
     * 变更方式
     */
    private String chgMethod;

    /**
     * 客户类型
     */
    private String custType;

    /**
     * 申报入职日期
     */
    private Date applyEntryDate;

    /**
     * 数据确认状态
     */
    private String orderStart;

    /**
     * 入离职状态
     */
    private String eedStatus;

    /**
     * 申报离职日期
     */
    private Date applyQuitDate;

    /**
     * 离职日期
     */
    private Date quitDate;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 人员分布
     */
    private String distributeType;

    /**
     * 报价单编号
     */
    private String quoteNo;

    /**
     * 变更状态
     */
    private String chgState;

    /**
     * 用户大区
     */
    private List<String> userLargeArea;


    private String revCs;//接单客服
    private String revSupervisor;//接单客服主管
    private String revMangr;//接单客服经理
    private String prjCs;//项目客服
    private String prjSupervisor;//项目客服主管
    private String prjMangr;//项目客服经理
    private Integer accountFlag;

    private Long templetId;

    private Long revTempId;


    private List<OrgPositionDto> userOrgPositionDtoList;
}
