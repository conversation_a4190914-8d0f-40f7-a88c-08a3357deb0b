var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        upload = layui.upload,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer,
        tableSelect = layui.tableSelect;
    element = layui.element;

    /** 生成合同页面的数据,就是下一步那个页面的数据 */
    var generateNormParam = {};
    var uploadIds = [];
    var optType = $("#optType").val();
    var fileId = '';
    // 记录当前窗口的layer.index
    var layerIndex = layer.index;
    form.on("submit(commit)", function (data) {
        saveForm('commit', data);
        return false;
    });
    form.on("submit(save)", function (data) {
        saveForm('save', data);
        return false;
    });
    //待办任务流程使用
    element.on('tab(contractTabFilter)', function (data) {
        var len = $(".layui-tab-title").children("li").length;
        if (data.index == len - 1) {
            var pid = $('#pid').val();
            if (pid) {
                $("#workFlowImg").attr("src", "/workflow/workflowGraph?pid=" + pid);
                ML.ajax("/workflow/getWorkflowAuditLogList", {"pid": pid}, function (res) {
                    var commentData = res.data;
                    table.render({
                        id: 'contractFlowTable',
                        elem: '#contractFlowTable',
                        data: commentData,
                        cols: [[
                            {title: '序号', type: 'numbers'}
                            , {
                                field: 'userId', title: '处理人', align: 'center', templet: function (d) {
                                    return ML.loginNameFormater(d.userId);
                                }
                            }
                            , {
                                field: 'auditType', title: '处理类型', align: 'center', templet: function (d) {
                                    return ML.dictFormatter("AUDIT_TYPE", d.auditType);
                                }
                            }
                            , {field: 'comment', title: '审批意见', align: 'center'}
                            , {field: 'createTime', title: '审批时间', align: 'center'}
                        ]]
                    });
                }, 'GET');
                ML.ajax("/workflow/getCurrentApprover", {"pid": pid}, function (res) {
                    var curApp = res.data;
                    if (curApp) {
                        $("#currentItem").removeClass("layui-hide");
                        document.getElementById('currentApproverName').innerHTML = curApp.name;
                        $("#currentApproverAssignee").val(ML.loginNameFormater(curApp.assignee));
                    }
                }, 'GET')
            }
        }
    });


    //关闭弹窗
    $(document).on('click', '.cancel', function () {
        layer.close(layerIndex);
    });

    $(document).on('click', '#submitProc', function () {
        if (uploadIds1.length == 0) {
            layer.msg('请先上传最终文件!', {icon:5});
            return;
        }
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var param = {
            'taskId': taskId,
            'comment': comment,
            'bizType': 'agreement_supplier_contract',
            'fileList': uploadIds1,
            'id': $('#id').val()
        }
        $.ajax({
            url: ML.contextPath + "/customer/supplier/uploadFinalFile",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(param),
            success: function (result) {
                layer.msg(result.msg);
                layer.closeAll('iframe');
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });

    });

    $(document).on('click', '#rejectProc', function () {
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var pid = $('#pid').val();
        var bizId = $('#contractNo').val();
        var type = "3";

        if (ML.isNotEmpty(comment) && comment.length > 255) {
            layer.msg("审批意见过长,请缩短审批意见!");
        } else {
            ML.ajax('/workflow/rejectTask',
                {'pid': pid, 'bizId': bizId, 'taskId': taskId, 'comment': comment, 'bizType': 'agreement_supplier_contract', 'type': type}
                , function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layer.close(layerIndex);
                    }
                }, 'POST');
        }

    });
    $(document).on('click', '#rejectTaskPreviousNode', function () {
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var pid = $('#pid').val();
        var bizId = $('#contractNo').val();
        var type = "3";

        if (ML.isNotEmpty(comment) && comment.length > 255) {
            layer.msg("审批意见过长,请缩短审批意见!");
        } else {
            var paramDateList=[];
            var paramDate={
                'pid':pid,'bizId':bizId,'taskId':taskId,'bizType':'agreement_supplier_contract'
            }
            paramDateList.push(paramDate);
            ML.ajax('/workflow/rejectTaskPreviousNode',
                {'paramDataListStr':JSON.stringify(paramDateList)}
                , function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layer.close(layerIndex);
                    }
                }, 'POST');
        }

    });


    var fileType = '';
    var fileName = '';
    var uploadIds1 = [];
    var uploadIds = [];
    //上传
    upload.render({
        elem: '#contractUpload' //绑定元素
        , auto: false
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: { contentType: false, processData: false }
        , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
        , choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                var tip = true;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", { icon: 2 });
                    tip = false;
                    return;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds1.push(res.data.fileId);
                $('#upload1').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag"><a href="' + ML.fileServerUrl + res.data.fileId + '" target="_blank">' + fileName + '</a><a href="javascript:void(0)" class="deleteFile" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', { icon: 1 });
                $("#contractUpload").remove();
            }
        }
        , error: function () {
            //请求异常回调
            console.log("上传失败");
            // layer.msg('上传失败', { icon: 5 });
        }
    });





    //页面初始化
    $(document).ready(function () {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/supplier/getSupplierVoBySupplierContractId",
            data: {id: $('#id').val(),type:"",procType:3},
            dataType: 'json',
            success: function (data) {
                $.each(data.data.agreementFileIdList, function (i, item) {
                    $.ajaxData.getFileName(item, 'check');
                });
                $('#supplierContractNo').val(data.data.contractNo)
                $('#supplierName').val(data.data.supplierName)
                $('#prodType').val(data.data.prodType)
                $('#supplierType').val(data.data.supplierType)
                $('#contractType').val(data.data.contractType)
                $('#orgCode').val(data.data.orgCode)
                $('#startTime').val(data.data.startTime)
                $('#endTime').val(data.data.endTime)
                $('#billDate').val(data.data.billDate)
                $('#payDate').val(data.data.payDate)
                $('#remark').val(data.data.remark)
                $('#contractName').val(data.data.contractName)
                $('#contractNo').val(data.data.contractNo)
                $('#relatedCompFlag').val(data.data.relatedCompFlag)
                $('#postponeFlag').val(data.data.postponeFlag)
                form.render('select');
                // 获取页面显示数据
                $.each(data.data.relatedCompList, function (index, item) {
                    $("#jobTypeDiv").append('<input type="checkbox" checked="checked"  value="' + item + '" title="' + item + '">');

                });
                form.render();
            },
            error: function (data) {
                console.log("error")
            }
        });
    });



    table.render({
        id: 'bindingGrid',
        elem: '#bindingGridTable'
        , url: ML.contextPath + '/customer/supplier/getSupplierVoBySupplierContractId'
        , page: true
        // , toolbar: '#topbtn'
        , defaultToolbar: []
        , where: {id: $('#id').val(),type:"quo",procType:3}
        , limit: 50
        , height: '250px'
        , method: 'GET'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //过滤没有权限的内容
            ML.hideNoAuth();
        }
        , cols: [[
            {
                field: '', title: '序号', align: 'center', width: '100', templet: function (d) {
                    return (d.LAY_INDEX)
                }
            },
            {
                field: 'supQuotationNo', title: '报价编号', align: 'center', width: '230',
            }
            , {field: 'supQuotationName', title: '报价单名称', align: 'center', width: '250'}
            , {field: 'serviceFeeType', title: '服务费类型', align: 'center', width: '130', templet: function (d) {
                    if (d.serviceFeeType==1){
                        return '社保服务费';
                    }else if (d.serviceFeeType==2){
                        return '工资服务费';
                    }else if (d.serviceFeeType==3){
                        return '社保工资服务费';
                    }

                }}
            , {
                field: 'cityCode', title: '指定城市', align: 'center', width: '170', templet: function (d) {
                    var split = d.cityCode.split(',');
                    var areaFormatter='';
                    for (var i = 0; i < split.length; i++) {
                        areaFormatter += ML.areaFormatter(split[i])+',';
                    };
                    return  '{' +areaFormatter +'}'
                }
            }
            , {
                field: 'serviceFee', title: '服务费', align: 'center', width: '140'
            },
            {field: 'taxFlag', title: '是否含税', width: '160', align: 'center',
                templet: function (d) {
                    if (d.taxFlag==1){
                        return '否';
                    }else if (d.taxFlag==2){
                        return '是'
                    }
                }},
            {
                field: 'taxRate',
                title: '<i style="color: red;"></i>税率',
                width: '140',
                edit: 'number',
                align: 'center',

            },
            {
                field: 'defaultStatus', title: '是否默认', align: 'center', width: '160', templet: function (d) {
                    if (d.defaultStatus==1){
                        return '否';
                    }else if (d.defaultStatus==2){
                        return '是'
                    }
                }
            }

        ]]
    });















































    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        ML.ajax("/customer/contract/delByFileId?fileId=" + fileId, {}, function (result) {
                if (result.code == 0) {
                    layer.msg("删除文件成功！")
                }
            },
            'POST');
        uploadIds1.splice(uploadIds1.indexOf($(this).parent()[0].id.split('-')[1]), 1);
        $(this).parent()[0].remove();
    });














});
