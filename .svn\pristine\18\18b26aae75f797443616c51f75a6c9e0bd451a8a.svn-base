<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans.xsd
      http://www.springframework.org/schema/context
      http://www.springframework.org/schema/context/spring-context.xsd
      http://www.springframework.org/schema/aop
	  http://www.springframework.org/schema/aop/spring-aop.xsd
      http://www.springframework.org/schema/mvc
      http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <context:property-placeholder ignore-unresolvable="true"
                                  location="classpath:properties/*.properties" />
    <!-- 开启注解 -->
    <mvc:annotation-driven>
        <mvc:message-converters register-defaults="true">
<!--            <bean class="com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter">-->
<!--                <property name="supportedMediaTypes">-->
<!--                    <list>-->
<!--                        <value>application/json;charset=UTF-8</value>-->
<!--                    </list>-->
<!--                </property>-->
<!--                <property name="dateFormat" value="yyyy-MM-dd HH:mm:ss"/>-->
<!--            </bean>-->

            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg value="UTF-8" />
                <property name="supportedMediaTypes">
                    <list>
                        <bean class="org.springframework.http.MediaType">
                            <constructor-arg index="0" value="text" />
                            <constructor-arg index="1" value="plain" />
                            <constructor-arg index="2" value="UTF-8" />
                        </bean>
                        <bean class="org.springframework.http.MediaType">
                            <constructor-arg index="0" value="*" />
                            <constructor-arg index="1" value="*" />
                            <constructor-arg index="2" value="UTF-8" />
                        </bean>
                    </list>
                </property>
            </bean>

            <!-- 处理responseBody 里面日期类型 -->
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                <property name="objectMapper">
                    <bean class="com.fasterxml.jackson.databind.ObjectMapper">
                        <property name="dateFormat">
                            <bean class="java.text.SimpleDateFormat">
                                <constructor-arg type="java.lang.String" value="yyyy-MM-dd HH:mm:ss" />
                            </bean>
                        </property>
                    </bean>
                </property>
            </bean>

            <!-- 避免IE执行AJAX时,返回JSON出现下载文件 -->
            <bean id="fastJsonHttpMessageConverter" class="com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter">
                <property name="supportedMediaTypes">
                    <list>
                        <value>application/json;charset=UTF-8</value>
                    </list>
                </property>
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>
<aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 开启controller注解支持 -->
    <context:component-scan base-package="com.reon.hr.modules.*.controller,com.reon.hr.core.*,com.reon.hr.rabbitmq" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice" />
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Component"/>
    </context:component-scan>

    <mvc:default-servlet-handler />

    <!-- 静态资源映射-->
    <mvc:resources location="/css/" mapping="/css/**" />
    <mvc:resources location="/images/" mapping="/images/**" />
    <mvc:resources location="/js/" mapping="/js/**" />
    <mvc:resources location="/json/" mapping="/json/**" />
    <mvc:resources location="/layui/" mapping="/layui/**" />

    <!-- 视图解释类 -->
    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/"></property>
        <property name="suffix" value=".jsp"></property>
    </bean>

	<!-- 项目根路径访问页面配置 -->
    <mvc:view-controller path="/" view-name="/login"/>

    <!-- 项目完全启动回调 -->
    <bean class="com.reon.hr.modules.common.listener.WebStartInitListener" />
    <bean class="com.reon.hr.modules.common.listener.WebCloseInitListener" />

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
    <!-- 指定所上传文件的总大小不能超过5M-->
   <property name="maxUploadSize" value="10485760" />
    <!-- 小于这个值的文件存储在内存中5M-->
   <property name="maxInMemorySize" value="10485760" />

</bean>

    <!-- Spring MVC 拦截器定义 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <!-- 拦截所有请求 -->
            <mvc:mapping path="/**"/>
            <!-- 使用bean定义一个Interceptor -->
            <bean class="com.reon.hr.core.interceptor.RepeatSubmitInterceptor" />
        </mvc:interceptor>
    </mvc:interceptors>





    <!-- 支持上传文件 -->
    <!-- 配置MultipartResolver 用于文件上传 使用spring的CommosMultipartResolver -->
    <!--<bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver"
          p:defaultEncoding="UTF-8" p:maxUploadSize="${upload.max.size}" p:uploadTempDir="${upload.base.dir}/temp" />-->

    <!-- 以下 validator  http://blog.csdn.net/xiaojiesu/article/details/50555714 在使用 mvc:annotation-driven 会 自动注册-->
    <!--<bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
        <property name="providerClass" value="org.hibernate.validator.HibernateValidator"/>
        &lt;!&ndash; 如果不加默认到 使用classpath下的 ValidationMessages.properties &ndash;&gt;
        <property name="validationMessageSource" ref="messageSource"/>
    </bean>
    <bean id="localeResolver"
          class="org.springframework.web.servlet.i18n.SessionLocaleResolver" />-->

    <!-- 计划任务后台管理 -->
    <!--<bean id="schedulerFactoryBean" class="org.springframework.scheduling.quartz.SchedulerFactoryBean" />-->
    <!-- 计划任务启动监听器 -->
    <!--<bean class="cn.jeeweb.modules.task.listener.ScheduleJobInitListener" />-->
    <!--<import resource="spring-mvc-shiro.xml" />-->

</beans>
