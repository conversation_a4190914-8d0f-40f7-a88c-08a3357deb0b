package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.vo.InsurancePackVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.salary.SalaryCalculationImporDto;
import com.reon.hr.api.customer.dto.importData.*;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.vo.batchImport.*;
import com.reon.hr.api.customer.vo.doIt.PaySocialSecurityVo;
import com.reon.hr.api.customer.vo.doIt.RepayTheImportInformationVo;
import com.reon.hr.api.customer.vo.employee.CompleteOrderViewVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.EmployeeQueryVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.customer.vo.salary.PayRelativeImportVo;
import com.reon.hr.sp.customer.dao.employee.OrderInsuranceCfgMapper;
import com.reon.hr.sp.customer.entity.cus.ImportData;
import com.reon.hr.sp.customer.entity.cus.ImportDataLog;
import com.reon.hr.sp.customer.service.ImportService.*;
import com.reon.hr.sp.customer.service.cus.IBatchImportDataService;
import com.reon.hr.sp.customer.service.employee.IEmployeeOrderService;
import com.reon.hr.sp.customer.service.employee.IOrderInsuranceCfgService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service("batchImportDataDubboService")
public class BatchImportDataWrapperServiceImpl implements IBatchImportDataWrapperService {

    private static final Logger logger = LoggerFactory.getLogger(BatchImportDataWrapperServiceImpl.class);

    @Autowired
    private IBatchImportDataService iBatchImportDataService;

    @Autowired
    private IEmployeeOrderService employeeOrderService;

    @Autowired
    private IOrderInsuranceCfgService insuranceCfgService;

    @Autowired
    private BatchAddPersonImportService batchAddPersonImportService;

    @Autowired
    private BatchAddCommInsurOrderEmployeeImportService batchAddCommInsurOrderEmployeeImportService;
    @Autowired
    private BatchAddIndTaxApplyInfoImportService batchAddIndTaxApplyInfoImportService;
    @Autowired
    private BatchAddSalaryActualInfoImportService batchAddSalaryActualInfoImportService;
    @Autowired
    private BatchAddSalaryEmployeeBankCardInfoImportService batchAddSalaryEmployeeBankCardInfoImportService;
    @Autowired
    private BatchAddEmployeeDimissionImportService batchAddEmployeeDimissionImportService;
    @Autowired
    private BatchAddEhrEmployeeDimissionImportService batchAddEhrEmployeeDimissionImportService;
    @Autowired
    private BatchAddEmployeeAccountImportService batchAddEmployeeAccountImportService;
    @Autowired
    private BatchAddEmployeeOrderImportService batchAddEmployeeOrderImportService;
    @Autowired
    private BatchSalaryDeductionImportService batchSalaryDeductionImportService;
    @Autowired
    private BatchSalaryDelayedImportService batchSalaryDelayedImportService;
    @Autowired
    private BatchSalaryEmployeeImportService batchSalaryEmployeeImportService;
    @Autowired
    private BatchSalarySpecialEmployeeImportService batchSalarySpecialEmployeeImportService;
    @Autowired
    private BatchReplenishImport batchReplenishImport;
    @Autowired
    private BatchAddSalaryImportService batchAddSalaryImportService;
    @Autowired
    private BatchAddCommInsurOrderChangesBillTemplateService batchAddCommInsurOrderChangesBillTemplateService;
    @Autowired
    BatchAddCommInsurImportService batchAddCommInsurImportService;

    @Autowired
    OrderInsuranceCfgMapper orderInsuranceCfgMapper;

    @Autowired
    BatchAddTelRecordImportService batchAddTelRecordImportService;
    @Autowired
    private IInsurancePackResourceWrapperService insurancePackResourceWrapperService;
    @Autowired
    private BatchActualVarianceImportService batchActualVarianceImportService;
    @Autowired
    BatchAddReceivingManImportIService batchAddReceivingManImportIService;

    @Autowired
    BatchEditInsuranceBaseImportService batchEditInsuranceBaseImportService;
    @Autowired
    private BatchAddPersonalProductsImportService batchAddPersonalProductsImportService;

    @Autowired
    private BatchAddEhrEmployeeImportService batchAddEhrEmployeeImportService;

    @Autowired
    private BatchSalaryCalculationImportService batchSalaryCalculationImportService;

    @Autowired
    private BatchImportImplementFeedbackImportService batchImportImplementFeedbackImportService;
    @Autowired
    private BatchImportSupplierSalaryImportService batchImportSupplierSalaryImportService;

    @Override
    public List<CompleteOrderViewVo> getCompleteOrderList(CompleteOrderViewVo orderViewVo, String loginName) {
        if (StringUtils.isNotBlank(orderViewVo.getReceiving())) {
            String[] strings = orderViewVo.getReceiving().split("_");
            orderViewVo.setReceiving(strings[1]);
        }
        orderViewVo.setLoginName(loginName);
        List<CompleteOrderViewVo> orderListPage = iBatchImportDataService.getCompleteOrderListPage(orderViewVo);
        orderListPage.forEach(orderView -> {
            List<OrderInsuranceCfgVo> insuranceCfgVos = insuranceCfgService.getListByOrderNo(orderView.getOrderNo());
            if (CollectionUtils.isNotEmpty(insuranceCfgVos)) {
                orderView.setInsuranceList(insuranceCfgVos);
            }
        });
        return orderListPage;
    }

    @Override
    public ImportDataLogVo getById(String importNo, Integer rowNum) {
        return iBatchImportDataService.getById(importNo, rowNum);
    }

    @Override
    public void updateByImportRecordNumber(BatchVo vo) {
        switch (vo.getDataType()) {
            case 1:
                iBatchImportDataService.updateByImportRecordNumber(vo.getCustomerOrderExcelDataVo().getImportNo(),
                        vo.getCustomerOrderExcelDataVo().getSuccessNum(),
                        vo.getCustomerOrderExcelDataVo().getFailNum());
                break;
            case 2:
                iBatchImportDataService.updateByImportRecordNumber(vo.getResignationApplicationExcelDataVo().getImportNo(),
                        vo.getResignationApplicationExcelDataVo().getSuccessNum(),
                        vo.getResignationApplicationExcelDataVo().getFailNum());
                break;
            case 3:
                iBatchImportDataService.updateByImportRecordNumber(vo.getOrderBasicInformationExcelDataVo().getImportNo(),
                        vo.getOrderBasicInformationExcelDataVo().getSuccessNum(),
                        vo.getOrderBasicInformationExcelDataVo().getFailNum());
                break;
            case 4:
                iBatchImportDataService.updateByImportRecordNumber(vo.getCommInsurOrderExcelDataVo().getImportNo(),
                        vo.getCommInsurOrderExcelDataVo().getSuccessNum(),
                        vo.getCommInsurOrderExcelDataVo().getFailNum());
                break;
            case 5:
                iBatchImportDataService.updateByImportRecordNumber(vo.getSalaryEmployeeExcelDataVo().getImportNo(),
                        vo.getSalaryEmployeeExcelDataVo().getSuccessNum(),
                        vo.getSalaryEmployeeExcelDataVo().getFailNum());
                break;
            case 7:
                iBatchImportDataService.updateByImportRecordNumber(vo.getSalaryImportExcelDataVo().getImportNo(),
                        vo.getSalaryImportExcelDataVo().getSuccessNum(),
                        vo.getSalaryImportExcelDataVo().getFailNum());
                break;
            case 8:
                iBatchImportDataService.updateByImportRecordNumber(vo.getSalaryEmployeeBankCardExcelDataVo().getImportNo(),
                        vo.getSalaryEmployeeBankCardExcelDataVo().getSuccessNum(),
                        vo.getSalaryEmployeeBankCardExcelDataVo().getFailNum());
                break;
            case 9:
                iBatchImportDataService.updateByImportRecordNumber(vo.getEmployeeContractExcelDataVo().getImportNo(),
                        vo.getEmployeeContractExcelDataVo().getSuccessNum(),
                        vo.getEmployeeContractExcelDataVo().getFailNum());
                break;
            case 10:
                iBatchImportDataService.updateByImportRecordNumber(vo.getSalaryEmpExcelDataVo().getImportNo(),
                        vo.getSalaryEmpExcelDataVo().getSuccessNum(), vo.getSalaryEmpExcelDataVo().getFailNum());
                break;
            case 11:
                iBatchImportDataService.updateByImportRecordNumber(vo.getSocialSecurityExcelDataVo().getImportNo(),
                        vo.getSocialSecurityExcelDataVo().getSuccessNum(), vo.getSocialSecurityExcelDataVo().getFailNum());
                break;
        }
    }

    @Override
    public Page<ImportDataVo> getImportDataListByTaxComparisonTypePage(String acctType, String groupCode, String importNo, String oprMan, String startOprTime,
                                                                       String endOprTime, String loginUser, Integer dataType, Integer page, Integer limit) {
        page = null == page ? 1 : page;
        limit = null == limit ? 10 : limit;
        Map<String, Object> map = new HashMap<>();
        map.put("importNo", importNo);
        map.put("oprMan", oprMan);
        map.put("creator", loginUser);
        map.put("dataType", dataType);
        map.put("groupCode", groupCode);
        map.put("acctType", acctType);
        if (StringUtils.isNotBlank(startOprTime)) {
            map.put("startOprTime", startOprTime + " 00:00:00");
        }
        if (StringUtils.isNotBlank(endOprTime)) {
            map.put("endOprTime", endOprTime + " 23:59:59");
        }
        Page<ImportDataVo> importDataListPage = null;
        if (!map.isEmpty()) {
            importDataListPage = iBatchImportDataService.getImportDataListPage(page, limit, map);
        }
        return importDataListPage;
    }
    @Override
    public Page<ImportDataVo> getImportDataListByTaxComparisonTypePage(String importNo, String oprMan, String startOprTime,
                                                                       String endOprTime, String loginUser, Integer dataType, Integer page, Integer limit,
                                                                       Integer taxComparisonType, String taxComparisonTypeQueryListStr,
                                                                       Integer taxComparisonTypeQuery) {
        page = null == page ? 1 : page;
        limit = null == limit ? 10 : limit;
        Map<String, Object> map = new HashMap<>();
        map.put("importNo", importNo);
        map.put("oprMan", oprMan);
        map.put("creator", loginUser);
        map.put("dataType", dataType);
        map.put("taxComparisonType", taxComparisonType);
        map.put("taxComparisonTypeQuery", taxComparisonTypeQuery);
        List<Integer> taxComparisonTypeQueryListInt=new ArrayList<>();
        if(StringUtils.isNotBlank(taxComparisonTypeQueryListStr)){
            String[] taxComparisonTypeQueryList = taxComparisonTypeQueryListStr.split(",");
            taxComparisonTypeQueryListInt = Arrays.stream(taxComparisonTypeQueryList)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        map.put("taxComparisonTypeQueryList", taxComparisonTypeQueryListInt);
        if (StringUtils.isNotBlank(startOprTime)) {
            map.put("startOprTime", startOprTime + " 00:00:00");
        }
        if (StringUtils.isNotBlank(endOprTime)) {
            map.put("endOprTime", endOprTime + " 23:59:59");
        }
        Page<ImportDataVo> importDataListPage = null;
        if (!map.isEmpty()) {
            importDataListPage = iBatchImportDataService.getImportDataByTaxComparisonTypeListPage(page, limit, map);
        }
        return importDataListPage;
    }

    @Override
    public Page<ImportDataVo> getImportKeyCustomerDataListPage(String loginName, Integer dataType, Integer page, Integer limit) {
        page = null == page ? 1 : page;
        limit = null == limit ? 10 : limit;
        Map<String, Object> map = new HashMap<>();
        map.put("dataType", dataType);
        map.put("oprMan", loginName);
        return iBatchImportDataService.getImportKeyCustomerDataListPage(page, limit, map);
    }

    @Override
    public Page<ImportDataVo> getImportKeyBanlanceDataListPage(String loginName, Integer dataType, Integer page, Integer limit) {
        page = null == page ? 1 : page;
        limit = null == limit ? 10 : limit;
        Map<String, Object> map = new HashMap<>();
        map.put("dataType", dataType);
        map.put("oprMan", loginName);
        return iBatchImportDataService.getImportKeyBanlanceDataListPage(page, limit, map);
    }

    @Override
    public Page<EmployeeImportVo> getEmployeeImportDataListPage(EmployeeQueryVo queryVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("packCode", queryVo.getPackCode());
        map.put("importType", queryVo.getImportType());
        map.put("dataType", queryVo.getDataType());
        map.put("creator", queryVo.getCreator());
        map.put("orgCode", queryVo.getOrgCode());
        if (StringUtils.isNotBlank(queryVo.getStartOprTime())) {
            LocalDate startDate = LocalDate.parse(queryVo.getStartOprTime());
            map.put("startOprTime", String.valueOf(LocalDateTime.of(startDate, LocalTime.MIN)));
        }
        if (StringUtils.isNotBlank(queryVo.getEndOprTime())) {
            LocalDate endDate = LocalDate.parse(queryVo.getEndOprTime());
            map.put("endOprTime", String.valueOf(LocalDateTime.of(endDate, LocalTime.MAX)));
        }
        Page<EmployeeImportVo> importDataListPage = new Page<>();
        if (!map.isEmpty()) {
            importDataListPage = iBatchImportDataService.getEmployeeImportDataListPage(queryVo.getPage(), queryVo.getLimit(), map);
            List<EmployeeImportVo> importData = importDataListPage.getRecords();
            if (CollectionUtils.isNotEmpty(importData)) {
                List<String> packCodeList = importData.stream().map(EmployeeImportVo::getPackCode).distinct().collect(Collectors.toList());
                Map<String, InsurancePackVo> packNameByCode = insurancePackResourceWrapperService.getPackNameByCode(packCodeList);
                Optional.ofNullable(packNameByCode).ifPresent(packMap -> {
                    for (EmployeeImportVo item : importData) {
                        if (packNameByCode.containsKey(item.getPackCode())) {
                            item.setPackName(packMap.get(item.getPackCode()).getPackName());
                        }
                    }
                });
            }
        }
        return importDataListPage;
    }

    @Override
    public Page<ImportDataLogVo> getImportDataLogListPage(String importNo, Integer page, Integer limit) {
        page = null == page ? 1 : page;
        limit = null == limit ? 10 : limit;
        Page<ImportDataLogVo> importDataLogVoPage = iBatchImportDataService.getImportDataLogListPage(page, limit, importNo);
        return importDataLogVoPage;
    }


    @Override
    public int insertSelective(ImportDataVo importDataVo) {
        ImportData importData = new ImportData();
        BeanUtils.copyProperties(importDataVo, importData);
        return iBatchImportDataService.insertSelective(importData);
    }

    @Override
    public int insertSelectiveLog(List<ImportDataLogVo> record) {
        List<ImportDataLog> importDataLogs = Lists.newArrayList();
        record.forEach(r -> {
            ImportDataLog importDataLog = new ImportDataLog();
            BeanUtils.copyProperties(r, importDataLog);
            importDataLogs.add(importDataLog);
        });
        return iBatchImportDataService.insertSelective(importDataLogs);
    }

    @Override
    public Page<PaySocialSecurityVo> getImportDataPage(RepayTheImportInformationVo repayTheImportInformationVo) {
        return iBatchImportDataService.getImportDataPage(repayTheImportInformationVo);
    }

    @Override
    public void batchAddPersonImport(ImportDataDto<AddEmployeeImportDto> importDataDto) {
        batchAddPersonImportService.batchAddPersonImport(importDataDto);
    }

    @Override
    public void batchAddCommInsurOrderEmployeeImport(ImportDataDto<AddCommInsurOrderEmployeeImportDto> commInsurOrderEmployeeImportDtoImportDataDto) {
        batchAddCommInsurOrderEmployeeImportService.batchAddCommInsurOrderEmployeeImport(commInsurOrderEmployeeImportDtoImportDataDto);
    }

    @Override
    public ImportDataDto<AddIndTaxApplyInfoImportDto> batchAddIndTaxApplyInfoImport(ImportDataDto<AddIndTaxApplyInfoImportDto> importDataDto, String withholdingAgentNo,Integer taxComparisonType) {
        return batchAddIndTaxApplyInfoImportService.batchAddIndTaxApplyInfoImport(importDataDto, withholdingAgentNo,taxComparisonType);
    }
    @Override
    public ImportDataDto<AddSalaryActualInfoImportDto> batchAddSalaryActualInfoImport(ImportDataDto<AddSalaryActualInfoImportDto> importDataDto, String withholdingAgentNo) {
        return batchAddSalaryActualInfoImportService.batchAddSalaryActualInfoImport(importDataDto, withholdingAgentNo);
    }

    @Override
    public void addImportSupplierSalary(ImportDataDto<SupplierSalaryImportDto> importDataDto) {
        batchImportSupplierSalaryImportService.addImportSupplierSalary(importDataDto);
    }

    @Override
    public void batchAddSalaryEmployeeBankCardInfoImport(ImportDataDto<AddSalaryEmployeeBankCardInfoImportDto> salaryEmployeeBankCardInfoImportDtoImportDataDto) {
        batchAddSalaryEmployeeBankCardInfoImportService.batchAddSalaryEmployeeBankCardInfoImport(salaryEmployeeBankCardInfoImportDtoImportDataDto);
    }


    @Override
    public void batchAddEmployeeAccountImport(ImportDataDto<EmployeeAccountImportDto> importDataDto, EmployeeQueryVo queryVo) {
        batchAddEmployeeAccountImportService.batchAddEmployeeAccountImport(importDataDto, queryVo);
    }

    @Override
    public void batchAddEmployeeDimissionImport(ImportDataDto<AddEmployeeDimissionImportDto> addEmployeeDimissionImportDataDto) {
        batchAddEmployeeDimissionImportService.batchAddEmployeeDimissionImport(addEmployeeDimissionImportDataDto);
    }


    @Override
    public void batchSalaryDelayedImport(ImportDataDto<SalaryDelayedImportDto> salaryDelayedImportDataDto) {
        batchSalaryDelayedImportService.batchSalaryDelayedImport(salaryDelayedImportDataDto);
    }

    @Override
    public void batchAddEmployeeOrderImport(ImportDataDto<EmployeeOrderImportDto> importDataDto, List<OrgPositionDto> userOrgPositionDtoList) {
        batchAddEmployeeOrderImportService.batchAddEmployeeOrderImport(importDataDto, userOrgPositionDtoList);
    }

    @Override
    public void batchSalaryDeductionImport(ImportDataDto<SalaryDeductionImportDto> importDataDto, String withholdingAgentNo) {
        batchSalaryDeductionImportService.updateSalaryDeductionImport(importDataDto, withholdingAgentNo);
    }

    @Override
    public void batchAddTelRecordImport(ImportDataDto<TelRecordImportDto> importDataDto) {
        batchAddTelRecordImportService.batchAddTelRecordImport(importDataDto);
    }


    @Override
    public void batchSalaryEmployeeImport(ImportDataDto<SalaryEmployeeImportDto> importDataDto) {
        batchSalaryEmployeeImportService.batchSalaryEmployeeImport(importDataDto);
    }

    @Override
    public void batchSalarySpecialEmployeeImport(ImportDataDto<SalarySpecialEmployeeImportDto> importDataDto) {
        batchSalarySpecialEmployeeImportService.batchSalarySpecialEmployeeImport(importDataDto);
    }

    @Override
    public void batchReplenishImport(RepayTheImportInformationVo repayTheImportInformationVo, SocialSecurityPaymentExcelDataVo socialSecurityPaymentExcelDataVo) {
        batchReplenishImport.batchReplenishImport(repayTheImportInformationVo, socialSecurityPaymentExcelDataVo);
    }

    @Override
    public void batchReplenishImport(String importType, String compensationMethod, ImportDataDto<SocialSecurityPaymentImportVo> importDataDto) {
        batchReplenishImport.batchReplenishImport(importType, compensationMethod, importDataDto);
    }

    @Override
    public void addImportDataAndSuppliPay(RepayTheImportInformationVo repayTheImportInformationVo) {
        batchReplenishImport.addImportDataAndSuppliPay(repayTheImportInformationVo);
    }

    @Override
    public void batchAddSalaryImport(ImportDataDto<AddSalaryImportDto> importDataDto, PayRelativeImportVo payRelativeImportVo) {
        batchAddSalaryImportService.batchAddSalaryImport(importDataDto, payRelativeImportVo);
    }

    @Override
    public void batchAddCommInsurOrderChangesBillTemplateImport(ImportDataDto<AddCommerChgTempletImportDto> importDataDto) {
        batchAddCommInsurOrderChangesBillTemplateService.batchAddCommInsurOrderChangesBillTemplateService(importDataDto);
    }

    @Override
    public void addImportCommInsurReductionData(ImportDataDto<CommInsurBatchReductionImportDto> importDataDto) {
        batchAddCommInsurImportService.addImportCommInsurReductionData(importDataDto);
    }


    @Override
    public boolean updateByImportDataNo(String importNo, Integer status) {
        return iBatchImportDataService.updateByImportDataNo(importNo, status);
    }

    @Override
    public boolean updateByErrorDesc(List<ImportDataLogVo> record) {
        if (!record.isEmpty()) {
            return iBatchImportDataService.updateByErrorDesc(record);
        }
        return false;
    }

    @Override
    public boolean deleteByIds(String importNo) throws Exception {
        return iBatchImportDataService.updateByImportDataAndLogNo(importNo);
    }

    @Override
    public void getParsingExcelData(EmployeeOrderVo employeeOrderVo) throws Exception {
        employeeOrderService.saveOrUpdate(employeeOrderVo);
    }


    @Override
    public List<Map<String, Object>> getExportData(String importNo) {
        List<Map<String, Object>> list = new ArrayList();
        List<ImportDataLogVo> importDataLogList = iBatchImportDataService.getImportDataLogList(importNo);
        importDataLogList.forEach(a -> {
            String importTxt = a.getImportTxt();
            String remind = a.getRemind();
            Map<String, Object> map = (Map<String, Object>) JSONUtils.parse(importTxt);
            String errorDesc = a.getErrorDesc();
            if (remind != null) {
                Map<String, Object> remindMap = (Map<String, Object>) JSONUtils.parse(remind);
                Map<String, String> remindStringMap = new HashMap<>();
                for (String key:remindMap.keySet()) {
                    Object o = remindMap.get(key);
                    remindStringMap.put(key,JSONUtils.toJSONString(o));
                }
                map.putAll(remindStringMap);
            }
            map.put(IBatchImportDataWrapperService.ERROR_DESCRIPTION, errorDesc);

            list.add(map);
        });
        //放入 提醒信息 表头
//        getRemindData(list,importNo);
        return list;
    }

    @Override
    public ImportDataVo getById(String importNo) {
        return iBatchImportDataService.getById(importNo);
    }

    @Override
    public <T> void addImportData(ImportDataDto<T> importDataDto, Integer importDataType) {
        iBatchImportDataService.addImportData(importDataDto, importDataType);
    }

    @Override
    public void batchInnertActualVarianceImport(ImportDataDto<ActualVarianceImportDto> importDataDto, String remark, Integer dataType) {
        batchActualVarianceImportService.batchInnertActualVarianceImport(importDataDto, remark, dataType);
    }

    @Override
    public String addSuppliPayConfig(String importNo, String loginName) {
        return batchReplenishImport.addSuppliPayConfig(importNo, loginName);
    }

    @Override
    public List<OrderInsuranceCfgVo> geExpiredMonthByOrderNo(String orderNo, List<Integer> prodCodeList) {
        return orderInsuranceCfgMapper.geExpiredMonthByOrderNo(orderNo, prodCodeList);
    }

    @Override
    public void batchAddReceivingManImport(ImportDataDto<ReceivingManImportVo> importDataDto) {
        batchAddReceivingManImportIService.batchAddReceivingManImport(importDataDto);
    }

    @Override
    public void batchInnerAddPersonalProductsImport(ImportDataDto<AddPersonalProductsImportDto> importDataDto) {
        batchAddPersonalProductsImportService.batchInnerAddPersonalProductsImport(importDataDto);
    }

    @Override
    public void handleBatchEditInsuranceBase(ImportDataDto<InsuranceBaseImportVo> importDataDto) {
        batchEditInsuranceBaseImportService.handleBatchEditInsuranceBase(importDataDto);
    }

    @Override
    public void updateSuccAndFailNum(String importNo) {
        iBatchImportDataService.updateSuccAndFailNum(importNo);
    }

    @Override
    public void addBatchEhrEmployeeImport(ImportDataDto<AddEhrEmployeeImportDto> importDataDto) {
        batchAddEhrEmployeeImportService.addBatchEhrEmployeeImport(importDataDto);
    }

    @Override
    public void batchAddEhrEmployeeDimissionImport(ImportDataDto<AddEhrEmployeeDimissionImportDto> importDataDto) {
        batchAddEhrEmployeeDimissionImportService.batchAddEhrEmployeeDimissionImport(importDataDto);
    }

    @Override
    public void batchSalaryCalculationImport(ImportDataDto<SalaryCalculationImporDto> importDataDto) {
        batchSalaryCalculationImportService.batchSalaryCalculationImportService(importDataDto);
    }

//    @Override
//    public List<OrderInsuranceCfgVo> getOrderInsuranceCfgVoByProd(List<ProdHandleInfoVo> prodHandleInfoVos,Integer yearMonth) {
//        return orderInsuranceCfgMapper.getOrderInsuranceCfgVoByProd(prodHandleInfoVos,yearMonth);
//    }

    @Override
    public void addImportImplementFeedback(ImportDataDto<ImplementFeedbackImportDto> importDataDto) {
        batchImportImplementFeedbackImportService.addImportImplementFeedback(importDataDto);
    }

    @Override
    public <T> void addAndupdateImportDatas(ImportDataDto<T> importDataDto) {
        iBatchImportDataService.addAndupdateImportData(importDataDto);
    }

    @Override
    public ImportDataLogVo createImportDataLogVo(BaseImportDto importDto, String importNo, String loginName) {
        return iBatchImportDataService.createImportDataLogVo(importDto, importNo, loginName);
    }
}
