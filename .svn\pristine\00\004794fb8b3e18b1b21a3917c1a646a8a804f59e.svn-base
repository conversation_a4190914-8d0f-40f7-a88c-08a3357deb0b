package com.reon.hr.sp.base.dao.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.PolicyWorkInjuryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工伤福利待遇(PolicyWorkInjury)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-29 10:35:35
 */
public interface PolicyWorkInjuryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PolicyWorkInjuryVo queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param policyWorkInjuryVo 查询条件
     * @param page         分页对象
     * @return 对象列表
     */
    List<PolicyWorkInjuryVo> queryAllByLimit(PolicyWorkInjuryVo policyWorkInjuryVo, Page<PolicyWorkInjuryVo> page);

    /**
     * 查询列表
     *
     * @param policyWorkInjuryVo 保单工伤VO
     * @return {@link List}<{@link PolicyWorkInjuryVo}>
     */
    List<PolicyWorkInjuryVo> queryList(PolicyWorkInjuryVo policyWorkInjuryVo);

    /**
     * 查询列表
     *
     * @param policyWorkInjuryVo 保单工伤VO
     * @return {@link List}<{@link PolicyWorkInjuryVo}>
     */
    List<PolicyWorkInjuryVo> queryListByList(@Param("list") List<PolicyWorkInjuryVo> policyWorkInjuryVo);

    /**
     * 统计总行数
     *
     * @param policyWorkInjuryVo 查询条件
     * @return 总行数
     */
    long count(PolicyWorkInjuryVo policyWorkInjuryVo);

    /**
     * 新增数据
     *
     * @param policyWorkInjuryVo 实例对象
     * @return 影响行数
     */
    int insert(PolicyWorkInjuryVo policyWorkInjuryVo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PolicyWorkInjury> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PolicyWorkInjuryVo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PolicyWorkInjury> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PolicyWorkInjuryVo> entities);

    /**
     * 修改数据
     *
     * @param policyWorkInjuryVo 实例对象
     * @return 影响行数
     */
    int update(PolicyWorkInjuryVo policyWorkInjuryVo);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PolicyWorkInjury> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int updateBatch(@Param("entities") List<PolicyWorkInjuryVo> entities);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

