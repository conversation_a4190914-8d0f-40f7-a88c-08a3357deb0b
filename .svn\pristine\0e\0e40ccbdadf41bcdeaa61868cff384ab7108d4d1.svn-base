/*
 * Copyright (C), 2016-2018, 上海牛娃互联网金融信息服务有限公司
 * FileName: SequenceServiceImpl.java
 * Author:   Administrator
 * Date:     2018年6月28日 下午3:17:44
 * Description: //模块目的、功能描述
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.reon.hr.sp.base.dubbo.rpc.sys.impl;

import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.sp.base.service.sequence.ISequenceInnerService;
import com.reon.hr.sp.base.util.DateUtil;
import com.reon.hr.sp.base.util.SequenceConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 〈一句话功能简述〉<br>
 * 〈功能详细描述〉
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */

@Service("sequenceDubboService")
public class SequenceServiceImpl implements ISequenceService {

    private final int SEQ_LENGTH = 8;

    private final  int SEQ_CATEGORY_LENGTH = 4;

    private final int SEQ_FIFTH_LENGTH = 5;

    private final  int SEQ_QUATATION_LENGTH = 6;



    @Autowired
    private ISequenceInnerService innerSeqService;

    @Override
    public String getCorpNo() {
        return SequenceConstant.Head.DS.getCode()
        + getSurplusSequenceBySeqKey(SequenceConstant.Head.DS.getCode(), SEQ_LENGTH);
    }

    /**城市人员类型seq
     * @return
     */
    @Override
    public String getCategoryNo() {
        return SequenceConstant.Head.CATEGORY.getCode()
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.CATEGORY.getCode(),SEQ_CATEGORY_LENGTH);
    }

    @Override
    public String getInsuranceRatioNo() {
        return SequenceConstant.Head.INSURANCE_RATIO_CODE.getCode()
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.INSURANCE_RATIO_CODE.getCode(),SEQ_LENGTH);
    }

    /**社保组seq
     * @return
     */
    @Override
    public String getInsuranceGroupNo() {
        return SequenceConstant.Head.GROUPCODE.getCode()
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.GROUPCODE.getCode(),SEQ_CATEGORY_LENGTH);
    }

    @Override
    public String getSalaryCategoryNo() {
        String str = SequenceConstant.Head.SALARY_CATEGORY_NO.getCode() + DateUtil.getString(new Date(),"yyyyMMdd")+
        getSurplusSequenceBySeqKey(SequenceConstant.Head.SALARY_CATEGORY_NO.getCode(), SEQ_CATEGORY_LENGTH);
        return str;
    }

    @Override
    public String getSalaryItemNo() {
       String str = SequenceConstant.Head.SALARY_ITEM_NO.getCode() +
               getSurplusSequenceBySeqKey(SequenceConstant.Head.SALARY_ITEM_NO.getCode(), SEQ_QUATATION_LENGTH);
        return str;

    }

    @Override
    public String getQuotationNo() {
        return SequenceConstant.Head.QUOTATIONCODE.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.QUOTATIONCODE.getCode(),SEQ_QUATATION_LENGTH);
    }
    @Override
    public String getWithholdingAgentSuffixNo() {
        return SequenceConstant.Head.WITHHOLDING_AGENT_SUFFIX_NO.getCode()
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.WITHHOLDING_AGENT_SUFFIX_NO.getCode(),SEQ_FIFTH_LENGTH);
    }

    @Override
    public String getSSContractNo(Date date) {
        return SequenceConstant.Head.SOCIAL_SECURITY_VENDOR_CONTRACT_NUMBER.getCode()+"-"+ DateUtil.getString(date,"yyyyMMdd")
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.SOCIAL_SECURITY_VENDOR_CONTRACT_NUMBER.getCode(),SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getNOSSContractNo(Date date) {
        return SequenceConstant.Head.NO_SOCIAL_SECURITY_VENDOR_CONTRACT_NUMBER.getCode()+"-"+ DateUtil.getString(date,"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.NO_SOCIAL_SECURITY_VENDOR_CONTRACT_NUMBER.getCode(),SEQ_QUATATION_LENGTH);
    }


    @Override
    public String getInsuranceSetNo() {
        return SequenceConstant.Head.INSURANCE_SET_CODE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.INSURANCE_SET_CODE.getCode(),SEQ_LENGTH);
    }

    @Override
    public String getContractNo() {
        return SequenceConstant.Head.CONTRACTCODE.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTCODE.getCode(),SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCustomerNo() {
        return SequenceConstant.Head.CUSTOMERCODE.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CUSTOMERCODE.getCode(),SEQ_QUATATION_LENGTH);
    }

    /**商保方案seq
     * @return
     */
    @Override
    public String getCustomerSolutionNo() {
        return SequenceConstant.Head.CUSTOMER_SOLUTION_CODE.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.QUOTATIONCODE.getCode(),SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getInsurancePackNo() {
        return SequenceConstant.Head.INSURANCE_PACK.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")
                +getSurplusSequenceBySeqKey(SequenceConstant.Head.INSURANCE_PACK.getCode(),SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCMBSinglePayBusinessNo() {
        String str = SequenceConstant.Head.SINGLE_PAY_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.SINGLE_PAY_NO.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCMBPayrollBusinessNo() {
        String str = SequenceConstant.Head.PAYROLL_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.PAYROLL_NO.getCode (), SEQ_QUATATION_LENGTH);
        String dateCode = getDateCode(str, SEQ_QUATATION_LENGTH);
        return dateCode.replaceAll("-","");
    }

    @Override
    public String getCMBPayOtherBusinessNo() {
        String str = SequenceConstant.Head.PAY_OTHER_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.PAY_OTHER_NO.getCode (), SEQ_QUATATION_LENGTH);
        String dateCode = getDateCode(str, SEQ_QUATATION_LENGTH);
        return dateCode.replaceAll("-","");
    }

    @Override
    public String getCMBSalaryBusinessNo(Integer payRollType) {
        String yurref;
//        BusCodeEn.PAY_OTHERS.getCode()
        if(payRollType != null && payRollType.equals(1)){
            yurref = getCMBPayOtherBusinessNo();
        } else {
            yurref = getCMBPayrollBusinessNo();
        }
        return yurref;
    }

    @Override
    public String getCMBSalaryBusinessNo(String prefix) {
        String yurref;
//        BusCodeEn.PAY_OTHERS.getStartP()
        if(prefix.startsWith("PE")){
            yurref = getCMBPayOtherBusinessNo();
        } else {
            yurref = getCMBPayrollBusinessNo();
        }
        return yurref;
    }

    @Override
    public String getCMBQueryBusinessNo() {
        String str = SequenceConstant.Head.BUSINESS_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.BUSINESS_NO.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode(str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getNNInvoiceOrderNo() {
        String str = SequenceConstant.Head.INVOICE_ORDER_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.INVOICE_ORDER_NO.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode(str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getInvoiceNo() {
        String str = SequenceConstant.Head.INVOICE_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.INVOICE_NO.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode(str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getTaskNo() {
        String str = SequenceConstant.Head.TASK_NO.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.TASK_NO.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode(str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getContractAreaNo() {
      String str =  SequenceConstant.Head.CONTRACTAREACODE.getCode()+
              getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return  getDateCode(str,SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getChangeTempleteNo() {
        String str =  SequenceConstant.Head.CHANGETEMPLETECODE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CHANGETEMPLETECODE.getCode(),SEQ_QUATATION_LENGTH);
        return  getDateCode(str,SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getTransferTempleteNo() {
        String str =  SequenceConstant.Head.TRANSFERTEMPLETECODE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.TRANSFERTEMPLETECODE.getCode(),SEQ_QUATATION_LENGTH);
        return  getDateCode(str,SEQ_QUATATION_LENGTH);
    }

    /**
     * 获取剩余的编号
     *
     * @param seqKey 键值
     * @param length 编号长度
     * @return
     */
    private String getSurplusSequenceBySeqKey(String seqKey, int length){
        String currentSeq = String.valueOf(innerSeqService.generateSeqValue(seqKey));
        StringBuffer sb = new StringBuffer();
        int fillLength = length - currentSeq.length();
        if(fillLength > 0){ //小于指定长度则前面补0
            for(int i=0;i<fillLength;i++){
                sb.append("0");
            }
        }
        sb.append(currentSeq);
        return sb.toString();
    }



    /**为主键拼接日期标识
     * @param str 根据数据库函数生成的主键 ex:XHT000001
     * @return ex:XHT-20190606000001
     */
    private String getDateCode(String str,int length) {
        if (StringUtils.isNotBlank(str)){
            StringBuilder no = new StringBuilder(str);
            int len = str.length()-length;
            no.insert(len,"-");
            String date = new SimpleDateFormat("yyyyMMdd").format(new Date()).toString();
            no.insert(len+1,date);
            return no.toString();
        }
        return null;
    }

    @Override
    public String getBatchImportDataNo() {
        String str = SequenceConstant.Head.BATCHIMPORTDATAEACODE.getCode () +
                getSurplusSequenceBySeqKey (SequenceConstant.Head.BATCHIMPORTDATAEACODE.getCode (), SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCollectJobNo() {
        String str =  SequenceConstant.Head.COLLECTJOBCODE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getMsgNo() {
        String str =  SequenceConstant.Head.MESSAGECODE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCommercialInsuranceOrderPersonnelNo() {
        String str =  SequenceConstant.Head.COMMERCIAL_INSURANCE_ORDER_PERSONNEL.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getCommercialInsuranceOrderNo() {
        String str =  SequenceConstant.Head.COMMERCIAL_INSURANCE_ORDER.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    @Override
    public String getSalaryPayNo() {
        return SequenceConstant.Head.SAYLARY_PAY_NO.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.SAYLARY_PAY_NO.getCode(),SEQ_CATEGORY_LENGTH);
    }

    @Override
    public String getBatchNo() {
        return SequenceConstant.Head.PAY_BATCH_NO.getCode()+"-"+ DateUtil.getString(new Date(),"yyyyMMdd")+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.PAY_BATCH_NO.getCode(),SEQ_CATEGORY_LENGTH);
    }

    @Override
    public String getSalaryEmployeeNo() {
        String str =  SequenceConstant.Head.SALARY_EMPLOYEE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTAREACODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }

    /**
     如果需要获取无前缀的no 需要在这里添加key
     返回数据长度为 7 + length
     */
    private static final String UPLOAD_NO_KEY = "get_upload_no_sequence_date_";
    private static final String PAY_CUST_NO_KEY = "get_payment_cust_no_sequence_date_";

    @Override
    public String getUploadId() {
        Long no = getIncrementNum(UPLOAD_NO_KEY + DateUtil.getString(new Date(), "yyyyMMdd"));
        String code = getSequence(no, SEQ_FIFTH_LENGTH);
        return DateUtil.getString(new Date(), "yyyyMMdd") + code;
    }

    @Override
    public String getPaymentCustomerNo() {
        Long no = getIncrementNum(PAY_CUST_NO_KEY + DateUtil.getString(new Date(), "yyyyMMdd"));
        String code = getSequence(no, SEQ_QUATATION_LENGTH);
        return DateUtil.getString(new Date(), "yyyyMMdd") + code;
    }
    @Override
    public String getCommerChgTempletChgNo() {
        String str =  SequenceConstant.Head.COMMERCIAL_INSURANCE_ORDER_CHANGE_BILL_TEMPLATE.getCode()+
                getSurplusSequenceBySeqKey(SequenceConstant.Head.CONTRACTCODE.getCode(),SEQ_QUATATION_LENGTH);
        return getDateCode (str, SEQ_QUATATION_LENGTH);
    }
    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    private Long getIncrementNum(String key) {
        // 不存在准备创建 键值对
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        Long counter = entityIdCounter.incrementAndGet();
        if ((null == counter || counter.longValue() == 1)) {// 初始设置过期时间
            entityIdCounter.expire(1, TimeUnit.DAYS);// 单位天
        }
        return counter;
    }

    public static String getSequence(long seq, int length) {
        String str = String.valueOf(seq);
        int len = str.length();
        if (len >= length) {
            return str;
        }
        int rest = length - len;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < rest; i++) {
            sb.append('0');
        }
        sb.append(str);
        return sb.toString();
    }
}
