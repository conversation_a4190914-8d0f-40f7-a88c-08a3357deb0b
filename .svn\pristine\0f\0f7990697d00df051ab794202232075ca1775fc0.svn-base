var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        upload = layui.upload,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
    element = layui.element;
    var layerIndex = layer.index;
    var uploadIds = [];
    var delFileList = [];
    var dataGrop = [];
    //缓存
    var commInsCache = [];


    function delapprovalBtn() {
        $("#approval").hide();
    }

    function delEditBtn() {
        $(".topBtnClass").hide();
        $(".belowBtn").hide();
    }

  var  adjustHideOrShowProratedFee = function (prodType){
        if (prodType == 1) {
            hideOrShowProratedFee(2)
        } else {
            hideOrShowProratedFee(1)
        }
    }

    $(document).ready(function () {
        loadTable([]);
        $(".topBtnClass").hide();
        var optType = $("#optType").val();
        var optType2 = $("#optType2").val();
        var quoteNo = $("#quoteNo").val();
        var taskId = $("#taskId").val();
        var pid = $("#pid").val();
        //这是审批的标记
        var quoteTypeMark = $("#quoteTypeMark").val();

        if (optType === "" && optType2 === "") {
            delEditBtn();
            delapprovalBtn()
        }

        //如果是查看页面并且 pid 有值表示流程页面应该有显示  如果pid没有值,表示流程页面不应该显示
        if (optType == "check") {
            if (pid == null || pid == "" || typeof (pid) == undefined || pid == "null") {
                $("#workFlowImg").remove();
                $("#quotationFlowTable").remove();
            }

        }

        if (optType2 == "approval" || optType == "check") {
            $(".deleteFile").css('display', 'none');
            $("#quotationUpload").hide();
            $("#belowBtn").hide();
            $(".queryDis").attr("disabled", "disabled");
            $("#custId").attr("disabled", "disabled");
            $("#custName").attr("disabled", "disabled");
            $("#prodType + div div input").attr("disabled", "disabled");
            $("#receiveDate").attr("disabled", "disabled");
            $("#feeRatio").attr("disabled", "disabled");
            $("#taxRatio").attr("disabled", "disabled");


            form.render()
        }
        if (optType == "edit" && optType2 === "") {
            delapprovalBtn();
        }
        if ("edit" == optType || "approval" == optType2 || optType == "check") {
            //quoteTypeMark = 2 时表示  组合报价,也就是 商保报价
            //              == 1 时表示 普通报价单  -- > 需要特殊处理
            //获取所有当前页面数据
            //根据 报价单编号查询主表数据
            $.ajax({
                url: ML.contextPath + "/customer/quotation/getAllQuotationByQuoteNo",
                type: 'GET',
                dataType: 'json',
                data: {'quoteNo': quoteNo, 'quoteTypeMark': quoteTypeMark},
                success: function (result) {
                    setTimeout(function () {
                        var data = result.data;
                        $("#quoteName").val(data.quoteName);
                        $("#quoteType").val(data.quoteType);
                        $("#valKind").val(data.valKind);
                        $("#custId").val(data.custId);
                        $("#custName").val(data.custName);
                        $("#custName").text(data.custName);
                        $("#prodType").val(data.prodType);
                        $("#subType").val(data.subType);
                        dataGrop = data.dataGrop;
                        $("#distributeType").val(data.distributeType);
                        $("#taxFlag").val(data.taxFlag);
                        $("#proratedFee").val(data.proratedFee);
                        //$("#taxRatioType").val(data.taxRatioType);
                        $("#taxRatio").val(data.taxRatio);
                        $("#collectFreq").val(data.collectFreq);
                        $("#receiveDate").val(data.receiveDate);
                        $("#remark").val(data.remark);
                        var subType = data.subType;
                        $("#subType").empty();
                        var subPro = ML.getSubDictByParent('PROD_KIND', data.prodType);
                        if (null != data.prodType && "" != data.prodType) {
                            $.each(subPro, function (i, item) {
                                if (subType == item.code) {
                                    $("#subType").append($("<option/>").text(item.name).attr("value", item.code).attr("selected", "selected"));
                                } else {
                                    $("#subType").append($("<option/>").text(item.name).attr("value", item.code));
                                }
                            });
                        }
                        uploadIds = data.quotationAttachmentList;
                        if (uploadIds) {
                            for (var i = 0; i < uploadIds.length; i++) {
                                var fileId = uploadIds[i].fileId;
                                // 调用获取文件名的方法
                                if ($("#optType2").val() == "approval" || $("#optType").val() == "check") {
                                    $.ajaxData.getFileName(fileId, 'check')
                                } else {
                                    $.ajaxData.getFileName(fileId, 'edit')
                                }
                            }
                        }
                        if (!pid) {
                            $("#pid").val(data.pid);
                        }

                        adjustHideOrShowProratedFee($("#prodType").val());

                        hideOrShowFeeRatio($("#proratedFee").val());
                        $("#feeRatio").val((Number(data.feeRatio) * 100).toFixed(4));
                        loadTable(data.dataGrop);
                        if (optType2 == "approval" || optType == "check") {
                            $("#prodType + div div input").attr("disabled", "disabled");
                            $("#prodType + div div input").attr("readonly", "readonly");
                            $("#prodType").attr("disabled", "disabled");
                            form.render();
                        }
                        form.render();
                    }, 200);
                },
                error: function (result) {
                    layer.msg("数据错误，请稍后重试!");
                }
            });
            // }
        }
    });



    function loadTable(data) {
        var limitCount = data.length;
        table.render({
            id: 'addCommInsuQuotationGrid',
            elem: '#addCommInsuQuotationTable',
            page: false
            , limit: limitCount
            , defaultToolbar: []
            , toolbar: '#topbtn'
            , data: data
            , cols: [[
                {type: 'checkbox', fixed: 'left', align: 'center'}
                , {title: '序号', type: 'numbers'}
                , {
                    field: 'num', title: '<i style="color: red">*</i>人数', align: 'center', width: 200
                    , edit: 'text'
                    , event: 'numEvent'
                }
                , {
                    field: 'firstNum', title: '<i style="color: red">*</i>首批', align: 'center', width: 200
                    , edit: 'text'
                    , event: 'firstNumEvent'
                }
                , {
                    field: 'price', title: '<i style="color: red">*</i>报价', align: 'center', width: 200
                    , edit: 'text', event: 'priceEvent'
                }
                , {
                    field: 'productCost', title: '赠送产品成本', align: 'center', width: 200
                    , edit: 'text'
                    , event: 'productCostEvent'
                }
                , {
                    field: 'costRemark', title: '赠送产品成本备注', align: 'center', width: 200
                    , edit: 'text', event: 'costRemarkEvent'
                }
                , {field: 'guidePrice', title: '指导价', align: 'center', width: 200}
                , {
                    field: 'solutionName',
                    title: '<i style="color: red">*</i>方案',
                    align: 'center',
                    width: 500,
                    templet: function (d) {
                        return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='queryQuote'>" + d.solutionName + "</a>";
                    }
                }
                , {field: 'custSoluId'}
                , {field: 'soluNo'}
            ]]
            , done: function (res) {
                $("[data-field='custSoluId']").css('display', 'none');
                $("[data-field='soluNo']").css('display', 'none');
                $("#quoteType").attr("disabled", "disabled");
                $("#valKind").attr("disabled", "disabled");
                //10表示  产品类型  商保
                if ($("#prodType").val() == 10) {
                    $("#quoteType").val(2)
                    $("[data-field='solutionName']").removeAttr('style', 'display');
                    $("[data-field=guidePrice]").removeAttr('style', 'display');
                    $(".topBtnClass").show();
                } else {
                    $("#quoteType").val(1)
                    $("[data-field='solutionName']").css('display', 'none');
                    $("[data-field=guidePrice]").css('display', 'none');
                    $(".topBtnClass").hide();
                }
                dropDownBoxAdapts();
                form.render();

                //如果是审批或者查看隐藏选择方案按钮
                if ($("#optType2").val() == "approval" || $("#optType").val() == "check") {
                    $(".topBtnClass").hide();
                    $("[data-field='num']").removeAttr('data-edit');
                    $("[data-field='firstNum']").removeAttr('data-edit');
                    $("[data-field='price']").removeAttr('data-edit');
                    $("[data-field='prdouctCost']").removeAttr('data-edit');
                    $("[data-field='costRemark']").removeAttr('data-edit');
                    $("[data-field=guidePrice]").removeAttr('data-edit');
                    $("[data-field='solutionName']").removeAttr('data-edit');
                }

                if ($("#optType").val() == "check") {
                    delapprovalBtn();
                }


                function numberCheck(param, type) {
                    $(param).on("input propertychange", function () {
                        var val = $(this).val();
                        //先把非数字的都替换掉，除了数字和.
                        val = val.replace(/[^\d.]/g, "");
                        if (type === "number") {
                            //先把非数字的都替换掉，除了数字
                            val = val.replace(/[^\d]/g, "");
                        }
                        //必须保证第一个为数字而不是.
                        val = val.replace(/^\./g, "");

                        //保证只有出现一个.而没有多个.
                        val = val.replace(/\.{2,}/g, "");

                        //保证.只出现一次，而不能出现两次以上
                        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                        $(this).val(val);
                    })
                }

                //单击蓝色链接事件
                table.on('tool(quotationTableFilter)', function (obj) {
                    var trDom = obj.tr;
                    // num  人数
                    if (obj.event == "numEvent") {
                        $(trDom).find("[data-field=num] input").attr("id", "num").attr("type", "number");
                        $("#num").val(obj.data.num);
                        numberCheck("#num", "number")
                    }
                    //firstNum  首批
                    if (obj.event == "firstNumEvent") {
                        $(trDom).find("[data-field=firstNum] input").attr("id", "firstNum").attr("type", "number");
                        $("#firstNum").val(obj.data.firstNum);
                        numberCheck("#firstNum", "number")
                    }
                    //price  报价
                    if (obj.event == "priceEvent") {
                        $(trDom).find("[data-field=price] input").attr("id", "price");
                        // $(trDom).find("[data-field=price] input").attr("type", "number");
                        numberCheck("#price", "price");
                        $("#price").val(obj.data.price);
                    }
                    if (obj.event == "productCostEvent") {
                        $(trDom).find("[data-field=productCost] input").attr("id", "productCost");
                        $("#productCost").val(obj.data.productCost);
                        numberCheck("#productCost", "productCost");

                    }

                    if (obj.event == 'queryQuote') {
                        var customerSolutionNo = obj.data.soluNo;
                        layer.open({
                            type: 2, //为1时弹出页面直接展示content中内容,为2时会向后台发送请求,content内容为请求地址
                            title: "查看方案详情",
                            area: ['1800px', '800px'],
                            shade: [0.8, '#393D49'],
                            maxmin: true,
                            offset: 'auto',
                            content: ML.contextPath + "/customer/customerSolution/gotoCustomerSolutionQuery",
                            success: function (layero, index) {
                                var body = layer.getChildFrame('body', index);
                                body.find("#optType").val("quotationQuery");
                                body.find("#solutionNo").val(customerSolutionNo);
                            },
                            end: function () {
                                reloadTable();
                            }
                        })
                    }
                })
            }
        });
    }

    /*选择框的监听*/

    // 产品子类选择框监听
    form.on('select(proratedFeeFilter)', function (data) {
        // 产品为外包, 是否按照服务比例收取服务费为是的时候 显示服务比例
        if (data.value == 2 && $("#prodType").val() != 1) {
            $("#proratedFee").val(null);
            form.render("select");
            return layer.msg('只有外包类型 才能根据比例计算服务费!');
        }
        hideOrShowFeeRatio(data.value);
    });

    // pt prodType st subType
    function hideOrShowFeeRatio(pf) {
        if (pf == 2) {
            $(".feeRatioHideClass").removeClass("layui-hide");
            $(".feeRatioHideClass").addClass("layui-show")
            $("#feeRatio").attr("lay-verify", "required");
        } else {
            $("#feeRatio").removeAttr("lay-verify", "required");
            $(".feeRatioHideClass").removeClass("layui-show");
            $(".feeRatioHideClass").addClass("layui-hide")
            $("#feeRatio").val("");
        }
    }

    // 是否显示 按比例收费
    function hideOrShowProratedFee(pf) {
        if (pf == 2) {
            $(".proratedFeeHideClass").removeClass("layui-hide");
            $(".proratedFeeHideClass").addClass("layui-show")
            $("#proratedFee").attr("lay-verify", "required");
        } else {
            $("#proratedFee").removeAttr("lay-verify", "required");
            $(".proratedFeeHideClass").removeClass("layui-show");
            $(".proratedFeeHideClass").addClass("layui-hide")
            $("#proratedFee").val("");
            hideOrShowFeeRatio(1) // 如果是否按比例收费为否,那么 serviceRatio 也要隐藏
        }
    }


    form.on('select(prodType)', function (data) {
        $("#subType").empty();
        //选择产品类型时首先加载产品子类
        var subPro = ML.getSubDictByParent('PROD_KIND', data.value);
        if (null != data.value && "" != data.value) {
            $("#subType").append($("<option/>"));
            $.each(subPro, function (i, item) {
                $("#subType").append($("<option/>").text(item.name).attr("value", item.code));
            });
        }
        form.render();
        //然后判断  是否显示选择方案的按钮 如果是prodType = 10 表示是商保  才可以显示按钮
        //10表示  产品类型  商保
        if ($("#prodType").val() == 10) {
            dataGrop = [];
        } else {
            dataGrop = [{num: "", firstNum: "", price: ""}];
        }

        adjustHideOrShowProratedFee($("#prodType").val());

        if ($("#proratedFee").val() == 2 && $("#prodType").val() != 1) {
            $("#proratedFee").val(null);
            form.render("select");
            hideOrShowFeeRatio($("#proratedFee").val());
            return layer.msg('只有外包类型 才能根据比例计算服务费!');
        }
        loadTable(dataGrop)
    });


    //监听编辑框
    table.on("edit(quotationTableFilter)", function (obj) {
        var oldDate = table.cache["addCommInsuQuotationTable"];
        var trDom = obj.tr;
        var splitElement = trDom.selector.split("=");
        var splitElementElement = splitElement[1];
        var strings = splitElementElement.split("]");
        var string = strings[0];
        var strings1 = string.split('"');
        var number = parseInt(strings1[1]);
        $.extend(true, dataGrop, oldDate);
        //行选择数据
        var row = obj.tr.selector;

        if (obj.field === 'num') {
            var value = obj.value;
            dataGrop[number].num = value;
            obj.update({
                num: value
            });
        }
        if (obj.field === 'firstNum') {
            var value = obj.value;
            dataGrop[number].firstNum = value;
            obj.update({
                firstNum: value
            });
        }
        if (obj.field === 'price') {
            var value = obj.value;
            dataGrop[number].price = value;
            obj.update({
                price: value
            });
        }
        if (obj.field === 'productCost') {
            var value = obj.value;
            dataGrop[number].productCost = value;
            obj.update({
                productCost: value
            });
        }
        if (obj.field === 'costRemark') {
            var value = obj.value;
            dataGrop[number].costRemark = value;
            obj.update({
                costRemark: value
            });
        }
    });

    $(".example").on('click', function (data) {
        layer.msg("默认：0%</br>派遣、代理通常：5%</br>外包1、外包2通常：6.83%", {time: 7000});
    })

    $(".servicesExample").on('click', function (data) {
        layer.msg("默认：0%</br>外包2通常：20%、15%", {time: 7000});
    })

    $("#taxRatio").on("input propertychange", function () {
        var val = $(this).val();
        //小于100
        val = val.replace(/^[1-9]\d{2,}[\.]?\d*/g, "");
        $(this).val(val);
    })

    $("#feeRatio").on("input propertychange", function () {
        var val = $(this).val();
        val = val.replace(/^[1-9]\d{2,}[\.]?\d*/g, "");
        $(this).val(val);
    });

    laydate.render({
        elem: '#receiveDate',
        type: 'date',
        format: 'dd',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        zIndex: 99999999,
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
        }
    });


    /*监听编辑*/
    table.on('edit(addQuotationTable)', function (obj) {
        var re = '^[1-9]\\d*$';
        var number = '^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)|([0]))$'
        // var number = '(^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$)'
        //人数
        if (obj.field == 'num') {
            num = obj.value;
            if (!num.match(re)) {
                return layer.msg("只能输入数字,请重新输入")
            }
            var index = $(obj.tr).attr("data-index");
            b[index][obj.field] = num;
        }
        //首批
        if (obj.field == 'firstNum') {
            firstNum = obj.value;
            if (!firstNum.match(re)) {
                return layer.msg("只能输入数字,请重新输入")
            }
            var index = $(obj.tr).attr("data-index");
            b[index][obj.field] = firstNum;

        }
        //报价
        if (obj.field == 'price') {
            price = obj.value;
            if (!price.match(number)) {
                return layer.msg("只能输入数字,请重新输入")
            }
            var index = $(obj.tr).attr("data-index");
            b[index][obj.field] = price;
        }
        //报价
        if (obj.field == 'productCost') {
            productCost = obj.value;
            if (!productCost.match(number)) {
                return layer.msg("只能输入数字,请重新输入")
            }
            var index = $(obj.tr).attr("data-index");
            b[index][obj.field] = productCost;
        }
    });

    //动态表格监听操作

    /*监听表格*/
    table.on('toolbar(quotationTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id),
            data = checkStatus.data;
        switch (obj.event) {
            case 'selectSolution':
                if ($("#custId").val() != "" && $("#custName").val() != "") {
                    selectSolution();
                } else {
                    layer.msg("请选择客户再选择方案!");
                }
                break;
            case 'delSolution':
                if (data.length === 0) {
                    layer.msg('请选择一行');
                } else {
                    del()
                }
                break;
        }
    });


    function saveForm(type, data) {
        //对上传数据进行验证
        if (dataGrop == false || uploadIds == false) {
            return layer.msg('请添加表格内容和上传文件后再提交哦！');
        }
        if ($('#taxRatio').val() == 0) {
            return layer.msg('税率不可以为0!');
        }
        // 代理 税率只能为 5% 或者 6%
        if ($('#prodType').val() == 2 && (!($('#taxRatio').val() == 5 || $('#taxRatio').val() == 6))) {
            return layer.msg('代理税率只能为 5% 或 6% !');
        }

        if ($('#prodType').val() == 9 && !($('#taxRatio').val() == 5)) {
            return layer.msg('派遣税率只能为 5% !');
        }
        if ($('#prodType').val() != 1 && $('#proratedFee').val() == 2) {
            return layer.msg('只有外包类型 才能根据比例计算服务费!');
        }
        if ($("#proratedFee").val() != 2) {
            $("#feeRatio").val("");
        }

        /*新增表格信息*/
        var addList = [];
        for (var i = 0; i < dataGrop.length; i++) {
            var add = {};
            if (!dataGrop[i].num) {
                return layer.msg("人数必填")
            }
            if (!dataGrop[i].firstNum) {
                return layer.msg("首批必填")
            }
            if (!dataGrop[i].price) {
                return layer.msg("报价必填")
            }
            if (dataGrop[i].productCost && !dataGrop[i].costRemark) {
                return layer.msg("赠送产品成本备注必填")
            }
            if ($('#quoteType').val() == 2 && $("#prodType").val() == 10 && !dataGrop[i].solutionNo) {
                return layer.msg("方案必选")
            }
            add['firstNum'] = dataGrop[i].firstNum;
            add['num'] = dataGrop[i].num;
            add['price'] = dataGrop[i].price;
            add['soluNo'] = dataGrop[i].solutionNo;
            add['productCost'] = dataGrop[i].productCost;
            add['costRemark'] = dataGrop[i].costRemark;
            add['guidePrice'] = dataGrop[i].guidePrice;
            addList.push(add);
        }
        /*上传id*/
        var uploadIdList = [];
        uploadIds.forEach(function (obj) {
            var ids = {};
            ids['fileId'] = obj.fileId;
            uploadIdList.push(ids);
        });

        ML.layuiButtonDisabled($('#' + type));// 禁用
        //data.field['taxRatioType'] = $("#taxRatioType").val();
        data.field['taxRatio'] = $("#taxRatio").val() / 100;
        data.field['feeRatio'] = $("#feeRatio").val() / 100;
        data.field['addList'] = addList;
        data.field['uploadIdList'] = uploadIdList;
        // data.field['posCode'] = $("#posCode").val();
        //只要是修改页面都需要 quoteNo 报价单编号
        if ($("#optType").val() == "edit") {
            data.field['quoteNo'] = $("#quoteNo").val();
            data.field['pid'] = $("#pid").val();
            delFileList.forEach(function (obj) {
                    ML.ajax("/customer/quotation/delByFileId?fileId=" + obj.fileId, {}, function (result) {
                            if (result.code == 0) {
                                layer.msg("删除文件成功！")
                            }
                        },
                        'POST');
                }
            );
        }
        if ($("#taskId").val()) {
            data.field['taskId'] = $("#taskId").val();
        }
        // data.field['dataGrop'] = dataGrop;
        data.field['quoteName'] = $('#quoteName').val();
        data.field['quoteType'] = $('#quoteType').val();
        data.field['valKind'] = $('#valKind').val();
        data.field['custId'] = $('#custId').val();
        data.field['prodType'] = $('#prodType').val();
        data.field['subType'] = $('#subType').val();
        data.field['distributeType'] = $('#distributeType').val();
        data.field['taxFlag'] = $('#taxFlag').val();
        data.field['collectFreq'] = $('#collectFreq').val();
        data.field['receiveDate'] = $('#receiveDate').val();
        data.field['remark'] = $('#remark').val();
        data.field['proratedFee'] = $('#proratedFee').val();
        // 修改
        if ($("#optType").val() == "edit") {
            //修改页面
            //  save  commit
            $.ajax({
                url: ML.contextPath + "/customer/quotation/" + type + "Quotation" + "?optType=edit",
                type: 'POST',
                dataType: 'json',
                data: {'jsonQuotation': JSON.stringify(data.field)},
                success: function (result) {
                    layer.closeAll('iframe');
                    layer.msg(result.msg);
                },
                error: function (data) {
                    layer.msg("系统繁忙，请稍后重试!");
                    ML.layuiButtonDisabled($('#' + type), 'true');
                }
            });
        } else {
            //新增页面
            //  save  commit
            $.ajax({
                url: ML.contextPath + "/customer/quotation/" + type + "Quotation",
                type: 'POST',
                dataType: 'json',
                data: {'jsonQuotation': JSON.stringify(data.field)},
                success: function (result) {
                    layer.closeAll('iframe');
                    layer.msg(result.msg);
                },
                error: function (data) {
                    layer.msg("系统繁忙，请稍后重试!");
                    ML.layuiButtonDisabled($('#' + type), 'true');
                }
            });
        }
    }

    //提交
    form.on("submit(commit)", function (data) {
        saveForm('commit', data);
        return false;
    });
    //保存
    form.on("submit(save)", function (data) {
        saveForm('save', data);
        return false;
    });

    //关闭弹窗
    $(document).on('click', '.cancel', function () {
        layer.closeAll('iframe');
        location.reload();
    });


    //新增
    function selectSolution() {
        layer.open({
            type: 2,
            area: ['80%', '70%'],
            shade: [0.8, '#393D49'],
            maxmin: true,
            offset: 'auto',
            content: ML.contextPath + '/customer/quotation/gotoSelectCustSol',
            success: function (layero, index) {
                var body = layer.getChildFrame("body", index);
                var parentIndex = layer.getFrameIndex(window.name);
                body.find("#parentIframeIndex").val(parentIndex);
                body.find("#custId").val($("#custId").val());
                body.find("#custName").val($("#custName").val());
                //如果产品类型和产品子类没有值什么都不显示
                if ($("#prodType").val() == 10) {
                    body.find("#showMarks").val(true);
                } else {
                    body.find("#showMarks").val(false);
                }
            },
            end: function () {
                if ($("#CustSol").val() != null && $("#CustSol").val() != "") {
                    commInsCache.splice(0, commInsCache.length);
                    if ($("#CustSol").val()) {
                        commInsCache = JSON.parse($("#CustSol").val());
                    }
                    $("#CustSol").val([]);
                    // 在 方案中 cost 为成本价  price 为指导价
                    // 在 报价单中 price 为报价,而指导价是不提交的只作为观看使用,所以在使用时
                    //把从方案中查到的price指导价 转换为guidePrice 也就是报价单中的指导价 而报价单中的报价 price 才是要提交的
                    var peg = true;

                    $.each(dataGrop, function (i, dataGropItem) {
                        $.each(commInsCache, function (m, commInsCacheItem) {
                            if (dataGropItem.solutionNo == commInsCacheItem.solutionNo) {
                                peg = false;
                                layer.msg("请勿选择相同的方案!");
                                if (!peg) {
                                    return false;
                                }
                            }
                        })
                        if (!peg) {
                            return false;
                        }
                    });


                    if (peg) {
                        commInsCache.forEach(function (item) {
                            item["soluNo"] = item.solutionNo;
                            item["guidePrice"] = item.price;
                            item["price"] = null;
                            dataGrop.push(item);
                        });
                    }
                }
                loadTable(dataGrop);
                form.render();
            }
        })
    }

    //删除
    function del() {
        layer.confirm("你确定要删除么？", {btn: ['确定', '取消']}, function () {
            var tableData = table.cache["addCommInsuQuotationGrid"];
            for (var i = tableData.length - 1; i >= 0; i--) {
                if (tableData[i]['LAY_CHECKED']) {
                    dataGrop.splice(tableData[i]['LAY_TABLE_INDEX'], 1);
                }
            }
            layer.msg("删除成功", {time: 10}, function () {
                table.reload('addCommInsuQuotationGrid', {data: dataGrop});
            });
        });
    }


    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            //用于判断现在的值与之前的客户id是否相同,如果不相同那么方案也要清空

            if (id != $("#custId").val()) {
                //报价单类型,用来判断填入什么值是标准报价单,需要填入 三个空, 如果是2 则不需要
                var quoteType = $("#quoteType").val();
                if (quoteType == null || quoteType == "undefined" || quoteType == 2 || quoteType == "") {
                    dataGrop = [];
                } else if (quoteType == 1) {
                    dataGrop = [{num: "", firstNum: "", price: ""}];
                }
                loadTable(dataGrop);
            }
            $("#custId").val(id);
        }
    });

    function dropDownBoxAdapts() {
        //在动态创建table中显示被遮盖的下拉框
        $(" .layui-table-body, .layui-table-box, .layui-table-cell").css('overflow', 'visible');
        //设置下拉框的高度与表格单元相同
        $("td .layui-form-select").css({
            'margin-top': '-10px',
            'margin-left': '-15px',
            'margin-right': '-15px'
        });
    }

    //重载数据
    function reloadTable() {
        table.reload('QuotionGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }


    //驳回 提交
    element.on('tab(quotationTabFilter)', function (data) {
        if (data.index == 1) {
            var pid = $('#pid').val();
            if (pid != null && pid != "" && typeof (pid) != undefined) {
                //报价单里面查看报价单
                if (!$("#type").val()) {
                    $("#workFlowImg").attr("src", "/workflow/workflowGraph?pid=" + pid);
                    ML.ajax("/workflow/getWorkflowAuditLogList", {"pid": pid}, function (res) {
                        var commentData = res.data;
                        table.render({
                            id: 'quotationFlowTable',
                            elem: '#quotationFlowTable',
                            data: commentData,
                            cols: [[
                                {title: '序号', type: 'numbers'}
                                , {
                                    field: 'userId', title: '处理人', align: 'center', templet: function (d) {
                                        return ML.loginNameFormater(d.userId);
                                    }
                                }
                                , {
                                    field: 'auditType', title: '处理类型', align: 'center', templet: function (d) {
                                        return ML.dictFormatter("AUDIT_TYPE", d.auditType);
                                    }
                                }
                                , {field: 'comment', title: '审批意见', align: 'center'}
                                , {field: 'createTime', title: '审批时间', align: 'center'}
                            ]]
                        });
                    }, 'GET');
                    /** 显示 当前审批人 */
                    showNowApprover(pid)
                } else {
                    //个人订单查询里面查看报价单
                    $("#workFlowImg2").attr("src", "/workflow/workflowGraph?pid=" + pid);
                    ML.ajax("/workflow/getWorkflowAuditLogList", {"pid": pid}, function (res) {
                        var commentData = res.data;
                        table.render({
                            id: 'quotationFlowTable2',
                            elem: '#quotationFlowTable2',
                            data: commentData,
                            cols: [[
                                {title: '序号', type: 'numbers'}
                                , {
                                    field: 'userId', title: '处理人', align: 'center', templet: function (d) {
                                        return ML.loginNameFormater(d.userId);
                                    }
                                }
                                , {
                                    field: 'auditType', title: '处理类型', align: 'center', templet: function (d) {
                                        return ML.dictFormatter("AUDIT_TYPE", d.auditType);
                                    }
                                }
                                , {field: 'comment', title: '审批意见', align: 'center'}
                                , {field: 'createTime', title: '审批时间', align: 'center'}
                            ]]
                        });
                    }, 'GET');
                    /** 显示 当前审批人 */
                    showNowApprover(pid)
                }
            }

        }
    });

    /** 显示当前审批人 */
    function showNowApprover(pid) {
        ML.ajax("/workflow/getCurrentApprover", {"pid": pid}, function (res) {
            var curApp = res.data;
            if (curApp) {
                $("#currentItem").removeClass("layui-hide");
                document.getElementById('currentApproverName').innerHTML = curApp.name;
                $("#currentApproverAssignee").val(ML.loginNameFormater(curApp.assignee));
            }
        }, 'GET')
    }

    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.close(layerIndex); //它获取的始终是最新弹出的某个层，值是由layer内部动态递增计算的
    });

    $(document).on('click', '#submitProc', function () {
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        ML.ajax('/workflow/submitTask',
            {'taskId': taskId, 'comment': comment, 'bizType': 'quotation_flow'}
            , function (res) {
                layer.msg(res.msg);
                if (res.code == 0) {
                    layer.close(layerIndex);
                }
            }, 'POST');

    });

    $(document).on('click', '#rejectProc', function () {
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var pid = $('#pid').val();
        var bizId = $('#quoteNo').val();
        if (ML.isNotEmpty(comment) && comment.length > 255) {
            layer.msg("审批意见过长,请缩短审批意见!");
        } else {
            ML.ajax('/workflow/rejectTask',
                {
                    'pid': pid,
                    'bizId': bizId,
                    'taskId': taskId,
                    'comment': comment,
                    'bizType': 'quotation_flow',
                    'type': 'add'
                }
                , function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layer.close(layerIndex);
                    }
                }, 'POST');
        }
    });


    var fileType = '';
    var fileName = '';
    //上传
    upload.render({
        elem: '#quotationUpload' //绑定元素
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: {contentType: false, processData: false}
        , method: 'POST'
        , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
        , field: 'file'
        , auto: false
        , choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                var tip = true;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", {icon: 2});
                    tip = false;
                    return;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push({'fileId': res.data.fileId});
                $('#upload').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag">' +
                    '<a href="' + ML.fileServerUrl + res.data.fileId + '"  target="_blank" id="gethref">' + fileName + '</a>' +
                    '<a href="javascript:void(0)" class="deleteFile"  }" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', {icon: 1});
            }
        }
        , error: function () {
            //请求异常回调
            console.log("上传文件失败")
        }
    });

    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        var delIndex;
        for (var i = 0; i < uploadIds.length; i++) {
            if (uploadIds[i].fileId == fileId) {
                delIndex = i;
            }
        }
        uploadIds.splice(delIndex, 1);
        if ($("#optType").val() == "edit") {
            delFileList.push(fileId);
        } else {
            ML.ajax("/customer/quotation/delByFileId?fileId=" + fileId, {}, function (result) {
                    if (result.code == 0) {
                        layer.msg("删除文件成功！")
                    }
                },
                'POST');
        }
        $(this).parent()[0].remove();
    });


});
