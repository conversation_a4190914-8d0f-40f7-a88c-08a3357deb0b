<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.ehr.sp.sys.mapper.EhrBillExportConfigMapper">

    <resultMap type="com.reon.ehr.api.sys.vo.EhrBillExportConfig" id="EhrBillExportConfigMap">
        <result property="configId" column="config_id" jdbcType="INTEGER"/>
        <result property="configColumn" column="config_column" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="EhrBillExportConfigMap">
        select config_id, config_column, create_by, create_time, update_by, update_time, remark

        from ehr_bill_export_config
        where config_id = #{configId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="EhrBillExportConfigMap">
        select
        config_id, config_column, create_by, create_time, update_by, update_time, remark
        from ehr_bill_export_config
        <where>
            <if test="configId != null">
                and config_id = #{configId}
            </if>
            <if test="configColumn != null and configColumn != ''">
                and config_column = #{configColumn}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from ehr_bill_export_config
        <where>
            <if test="configId != null">
                and config_id = #{configId}
            </if>
            <if test="configColumn != null and configColumn != ''">
                and config_column = #{configColumn}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="configId" useGeneratedKeys="true">
        insert into ehr_bill_export_config
        (config_column, create_by, create_time, update_by, update_time, remark)
        values (#{configColumn}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark})
    </insert>

    <insert id="insertBatch" keyProperty="configId" useGeneratedKeys="true">
        insert into ehr_bill_export_config(config_column, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.configColumn}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="configId" useGeneratedKeys="true">
        insert into ehr_bill_export_config(config_column, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.configColumn}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime},
            #{entity.remark})
        </foreach>
        on duplicate key update
        config_column = values(config_column),
        create_by = values(create_by),
        create_time = values(create_time),
        update_by = values(update_by),
        update_time = values(update_time),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ehr_bill_export_config
        <set>
            <if test="configColumn != null and configColumn != ''">
                config_column = #{configColumn},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where config_id = #{configId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ehr_bill_export_config where config_id = #{configId}
    </delete>

</mapper>

