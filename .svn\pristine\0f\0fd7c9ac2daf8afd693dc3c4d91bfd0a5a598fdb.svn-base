/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/8/27
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.bill.controller.paymentApply;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.ehr.api.sys.dubbo.service.rpc.HolidaysWrapperService;
import com.reon.ehr.api.sys.vo.HolidaysVo;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.dto.SearchInsuranceLockDto;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice.IInsurancePracticeBillWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice.IInsurancePracticeOneFeeWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.enums.*;
import com.reon.hr.api.bill.utils.QueryUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.bill.vo.salary.PayServiceSerialLogVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.report.enums.BillReportEnum;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ManualAction;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dto.TaskQueryDTO;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.common.enums.salary.PayServiceSerialDetailType;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.core.utils.StringUtil;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentApplyController
 *
 * @date 2020/8/27 14:46
 */
@RestController
@RequestMapping("/bill/paymentApply")
public class PaymentApplyController extends BaseController {
	private static final Logger log = LoggerFactory.getLogger(PaymentApplyController.class);
    @Resource(name = "customerService")
    private ICustomerWrapperService customerWrapperService;

    @Autowired
    private IPaymentApplyWrapperService iPaymentApplyWrapperService;

    @Resource(name = "workflowDubboService")
    private IWorkflowWrapperService workflowWrapperService;

    @Resource(name = "userDubboService")
    private IUserWrapperService userWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService orgnizationService;
    @Resource(name = "orgDubboService")
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Autowired
    private IInsurancePracticeBillWrapperService insurancePracticeLockWrapperService;

    @Autowired
    IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Autowired
    IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;

    @Resource
    private RedisTemplate stringRedisTemplate;

    @Resource
    private IEmployeeOrderWrapperService employeeOrderWrapperService;

    @Resource
    private ISequenceService iSequenceService;

    @Resource
    private IContractResourceWrapperService contractResourceWrapperService;

    @Resource
    private IInsurancePracticeServiceConfigWrapperService insurancePracticeServiceConfigWrapperService;

    @Resource
    private ICompanyBankWrapperService companyBankWrapperService;

    @Resource
    private HolidaysWrapperService holidaysWrapperService;

    @Resource
    private InsurancePracticePayBankConfigWrapperService insurancePracticePayBankConfigWrapperService;

    @Resource
    private IInsurancePracticeOneFeeWrapperService iInsurancePracticeOneFeeWrapperService;
    /**
     * 查看支付管理页面
     *  没有使用
     * @return
     */
    @RequestMapping("gotoPaymentApplyQueryPage")
    public ModelAndView gotoPaymentApplyQueryPage() {
        return new ModelAndView ("bill/paymentApply/paymentApplyQuery");
    }

    /**
     * 新增支付管理页面
     *
     * @param id
     * @param pid
     * @param posCode
     * @param type
     * @param model
     * @return
     */
    @RequestMapping("addPaymentApplyPage")
    public ModelAndView addPaymentApplyPage(String id, String pid, String posCode, String type, Model model) {
        model.addAttribute ("id", id);
        if (StringUtil.isNotEmpty (pid)) {
            model.addAttribute ("pid", pid);
        }
        if (StringUtil.isNotEmpty (posCode)) {
            model.addAttribute ("posCode", posCode);
        }
        model.addAttribute ("type", type);
        return new ModelAndView ("bill/paymentApply/paymentApplyAdd");
    }

    /**
     * 编辑支付页面
     *
     * @param id
     * @param pid
     * @param
     * @param
     * @param model
     * @return
     */
    @RequestMapping(value = {"editPaymentApplyPage", "gotoEditPaymentApplyApprovalPage"})
    public ModelAndView editPaymentApplyPage(Long id, String taskId, String pid, Model model) {
        setModel (id, taskId, pid,model);
        return new ModelAndView ("bill/paymentApply/paymentApplyEdit");
    }

    @RequestMapping(value = {"checkPaymentApplyPage", "gotoPaymentApplyApprovalPage"})
    public ModelAndView checkPaymentApplyPage(Long id, String taskId, String pid, Model model) {
        setModel (id, taskId, pid, model);
        return new ModelAndView ("bill/paymentApply/paymentApplyCheck");
    }

    @RequestMapping("/gotoPaymentApplyApprovalUploadPage")
    public ModelAndView gotoPaymentApplyApprovalUploadPage(Long id, String taskId, String pid, Model model) {
        setModel (id, taskId, pid, model);
        return new ModelAndView ("bill/paymentApply/paymentApplyUploadFile");
    }

    //设置model返回属性方法
    public void setModel(Long id, String taskId, String pid, Model model) {
        PaymentApplyVo paymentApplyVo = iPaymentApplyWrapperService.selectByPrimaryKey (id);
        //福利办理方
        OrgVo orgVo = orgnizationService.findOrgById(paymentApplyVo.getOrgCode());
        //支付地
        OrgVo appCom = orgnizationService.findOrgById(paymentApplyVo.getAppCom());
        List<InsurancePackVo> insurancePackVo = iInsurancePackResourceWrapperService.getAllInsuPackByPackCodeList(paymentApplyVo.getPackCodes());
        List<InsurancePracticePayBankDetailConfigVo> providentDetails = insurancePracticeLockWrapperService.getProvidentDetails(paymentApplyVo.getOrgCode(), paymentApplyVo.getLockMonth(),paymentApplyVo.getPayDetailType(),id);
        if (paymentApplyVo != null) {
            if (CollectionUtils.isNotEmpty(providentDetails)){
                paymentApplyVo.setDetailConfigVos(providentDetails);
            }
            List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = iPaymentApplyWrapperService.getInsurancePracticeDisComListByPayId(id);
            if (CollectionUtils.isNotEmpty(insurancePracticeDisComListByPayId)){
                BigDecimal totalAmt = BigDecimal.ZERO;
                BigDecimal serviceTotalAmt = BigDecimal.ZERO;
                BigDecimal actPayAmtTotal = BigDecimal.ZERO;
                BigDecimal balanceAmtTotal = BigDecimal.ZERO;
                for (InsurancePracticeDisComPayVo item : insurancePracticeDisComListByPayId) {
                    item.setBalanceAmt((item.getPayAmt().add(item.getServiceAmt()).subtract(item.getActPayAmt())));
                    totalAmt = totalAmt.add(item.getPayAmt());
                    serviceTotalAmt = serviceTotalAmt.add(item.getServiceAmt());
                    actPayAmtTotal = actPayAmtTotal.add(item.getActPayAmt());
                    balanceAmtTotal = balanceAmtTotal.add(item.getBalanceAmt());
                    OrgVo disCom = orgnizationService.findOrgById(item.getDisCom());
                    item.setDisCom(disCom.getOrgName());
                    String[] disComApp = item.getDisComApp().split(",");
                    List<String> loginNameList= userWrapperService.getLongingNameByOrgCodeAndPosCode(disComApp[0], disComApp[1]);
                    String loginName = String.join(",", loginNameList);
                    item.setDisComApp(loginName);
                }
                InsurancePracticeDisComPayVo insurancePracticeDisComPayVo = new InsurancePracticeDisComPayVo();
                insurancePracticeDisComPayVo.setDisCom("总计");
                insurancePracticeDisComPayVo.setPayAmt(totalAmt);
                insurancePracticeDisComPayVo.setServiceAmt(serviceTotalAmt);
                insurancePracticeDisComPayVo.setActPayAmt(actPayAmtTotal);
                insurancePracticeDisComPayVo.setBalanceAmt(balanceAmtTotal);
                insurancePracticeDisComListByPayId.add(insurancePracticeDisComPayVo);


                paymentApplyVo.setInsurancePracticeDisComPayVoList(insurancePracticeDisComListByPayId);

            }
            InsurancePracticePayBankConfigVo bankConfigByOrgCode = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
            if (!Objects.isNull(bankConfigByOrgCode)){
                paymentApplyVo.setPayBankNo(bankConfigByOrgCode.getReceivingBankNo());
                paymentApplyVo.setPayBankName(bankConfigByOrgCode.getReceivingBankName());
            }
            List<PracticeLockInfoVo> lockInfoVos = insurancePracticeLockWrapperService.getLockInfosByPayId(id);
            getRequest ().setAttribute ("paymentApply", paymentApplyVo);
            String paymentApplyJson = JsonUtil.beanToJson(paymentApplyVo);
            model.addAttribute ("paymentApplyJson", paymentApplyJson);
            model.addAttribute ("lockInfoJson", JsonUtil.beanToJson(lockInfoVos));
        }
        model.addAttribute ("id", id);
        if (StringUtil.isNotEmpty (pid)) {
            model.addAttribute ("pid", pid);
        }
        if (StringUtil.isNotEmpty (taskId)) {
            model.addAttribute ("taskId", taskId);
        }
        if (orgVo != null){
            model.addAttribute("orgName",orgVo.getOrgName());
        }
        if (appCom != null){
            model.addAttribute("appComName",appCom.getOrgName());
        }
        if (CollectionUtils.isNotEmpty(insurancePackVo)){
            String packNames = insurancePackVo.stream().map(InsurancePackVo::getPackName).collect(Collectors.joining(","));
            model.addAttribute("packName",packNames);
        }
    }




    @RequestMapping(value = "/gotoSelectOptView", method = RequestMethod.GET)
    public ModelAndView gotoSelectOptView(Model model, String type) {
        model.addAttribute ("type", type);
        return new ModelAndView ("/bill/paymentApply/selectPos");
    }

//返回


    /**
     * 查询支付管理数据
     *
     * @param paymentApplyVo
     * @param page
     * @param limit
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getPaymentApplyListPage", method = RequestMethod.GET)
    public Object getPaymentApplyListPage(PaymentApplyVo paymentApplyVo,
                                             Integer page, Integer limit) {
        List<OrgVo> cityOrgVos = getSessionUser().getCityOrgVos();
        if(CollectionUtils.isNotEmpty(cityOrgVos)){
            cityOrgVos.forEach (orgVo -> {
                paymentApplyVo.getAppComs().add(orgVo.getOrgCode());
            } );
        }else {
            return  LayuiReplay.error (ResultEnum.POWER_ERR.getMsg ());
        }

        if (paymentApplyVo.getType() != null && paymentApplyVo.getType().equals("shebao")){
            paymentApplyVo.setPayType(PaymentApplyPayTypeEnum.SOCIAL_SECURITY.getCode());
        }
        Page<PaymentApplyVo> paymentApplyListPage = iPaymentApplyWrapperService.getPaymentApplyListPage (paymentApplyVo, page, limit);
        List<PaymentApplyVo> paymentApplyList = paymentApplyListPage.getRecords();
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        for (PaymentApplyVo applyVo : paymentApplyList) {
            if (StringUtil.isNotBlank(applyVo.getPayCom())) {
                applyVo.setPayCom(orgVoMap.get(applyVo.getPayCom()));
            }
            if (StringUtil.isNotBlank(applyVo.getAppCom())){
                applyVo.setAppComName(orgVoMap.get(applyVo.getAppCom()));
            }
        }
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), paymentApplyListPage.getTotal (), paymentApplyListPage.getRecords ());
    }

    /**
     *  与getPaymentApplyListPage 区别  只查出社保类型
     */
    @ResponseBody
    @RequestMapping(value = "/getSocialPaymentApplyListPage", method = RequestMethod.GET)
    public Object getSocialPaymentApplyListPage(PaymentApplyVo paymentApplyVo,
                                          Integer page, Integer limit) {
        List<OrgVo> cityOrgVos = getSessionUser().getCityOrgVos();
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        UserOrgPosVo defaultFlagOrgPos = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(getSessionUser().getLoginName());

        if(CollectionUtils.isNotEmpty(cityOrgVos)){
            cityOrgVos.forEach (orgVo -> {
                paymentApplyVo.getAppComs().add(orgVo.getOrgCode());
             } );
        }else if (defaultFlagOrgPos.getPosType().equals(Integer.valueOf(PositionEnum.PROJECT_VP.getCode()))){
            Set<String> orgCodeSet = allCompany.stream()
                    .map(OrgVo::getOrgCode)
                    .filter(Objects::nonNull)
                    .filter(orgCode -> orgCode.length() > 8)
                    .map(orgCode -> orgCode.substring(0, 8))
                    .collect(Collectors.toSet());

            Set<String> appComs = Optional.ofNullable(paymentApplyVo.getAppComs())
                    .orElse(new HashSet<>());
            appComs.addAll(orgCodeSet);
            paymentApplyVo.setAppComs(appComs);
        }else {
            return  LayuiReplay.error (ResultEnum.POWER_ERR.getMsg ());
        }

        // 1 社保类型
        paymentApplyVo.setPayType(PaymentApplyPayTypeEnum.SOCIAL_SECURITY.getCode());
        Page<PaymentApplyVo> paymentApplyListPage = iPaymentApplyWrapperService.getPaymentApplyListPage (paymentApplyVo, page, limit);
        List<PaymentApplyVo> paymentApplyList = paymentApplyListPage.getRecords();

        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        for (PaymentApplyVo applyVo : paymentApplyList) {
            if (StringUtil.isNotBlank(applyVo.getPayCom())) {
                applyVo.setPayCom(orgVoMap.get(applyVo.getPayCom()));
            }
            if (StringUtil.isNotBlank(applyVo.getAppCom())){
                applyVo.setAppComName(orgVoMap.get(applyVo.getAppCom()));
            }
        }
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), paymentApplyListPage.getTotal (), paymentApplyListPage.getRecords ());
    }



    /**
     * @Description:
     * @Author: chenxiang 对保存或者提交进行处理
     * @Params: * @param
     * @Returns:
     * @Since 2020/9/1 9:19
     */
    @PostMapping(value = "saveOrCommit")
    public Object saveOrCommit(@RequestBody PracPaymentApplyVo paymentApplyVo,HttpSession session) {
        if(paymentApplyVo.getListCnt() != null){
            if(paymentApplyVo.getListCnt()>128){
                return new LayuiReplay<PaymentApplyVo> (ResultEnum.ERR.getCode (), "单据数量不可以大于128");
            }else if(paymentApplyVo.getListCnt()<0){
                return new LayuiReplay<PaymentApplyVo> (ResultEnum.ERR.getCode (), "单据数量不可以小于0");
            }
        }
        if ("[]".equals(paymentApplyVo.getFileId())){
            return LayuiReplay.error("请上传文件！");
        }
        setFileId(paymentApplyVo.getFileId(),paymentApplyVo);

        List<InsurancePracticePayBankDetailRemarkVo> remarkVos = JsonUtil.jsonToList(paymentApplyVo.getRemarkVo(), InsurancePracticePayBankDetailRemarkVo.class);
        //支付地不需要更新  前端传的是公司名 所以重设为null
//        paymentApplyVo.setAppCom(null);
        /**
         * 3 表示工资类型
        */
      /*  if(paymentApplyVo.getPayType() == 3){
            return new LayuiReplay<PaymentApplyVo>(ResultEnum.ERR.getCode(),"无法新增工资类型数据");
        }*/
        //获取保存状态
//        int save = 0;
        //获取当前保存人的状态
        CommonUserVo commonUserVo = getSessionUser ();
        String loginName = commonUserVo.getLoginName ();
        paymentApplyVo.setCreator (loginName);
        paymentApplyVo.setApplicant (loginName);
        checkCompanyBank(paymentApplyVo);
        /**实做*/
        if(PaymentApplyPayTypeEnum.SOCIAL_SECURITY.getCode().equals(paymentApplyVo.getPayType())){
            if(paymentApplyVo.getIsSelectAmt() && StringUtil.isBlank(paymentApplyVo.getSelectPaymentDetails())){
                return LayuiReplay.error("请选择支付数据！");
            }
            if(StringUtil.isBlank(paymentApplyVo.getPid())){
                return LayuiReplay.error("缺少支付Id，请联系管理员！");
            }
            if(StringUtil.isBlank(paymentApplyVo.getLockInfoJson())){
                return LayuiReplay.error("缺少报表数据，请联系管理员！");
            }
            boolean ckeckHoliday = checkLastDateISHoliday(paymentApplyVo.getLastDate());
            if (!ckeckHoliday){
                return LayuiReplay.error("最晚支付日期不能是节假日");
            }
            boolean ckeck = checkLastDate(paymentApplyVo.getLastDate());
            if (!ckeck){
                return LayuiReplay.error("最晚支付日期要至少在两天后");
            }
            String lockInfoJson = paymentApplyVo.getLockInfoJson();
            List<PracticeLockInfoVo> perData = JsonUtil.jsonToList(lockInfoJson, PracticeLockInfoVo.class);
            Map<Long, Long> perDataMap = perData.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, PracticeLockInfoVo::getVersion));
            SearchInsuranceLockDto lockDto = paymentApplyVo.buildDto();
            List<PracticeLockInfoVo> currData = insurancePracticeLockWrapperService.getBySearch(lockDto);
            /**驳回直接提交的时候： 没有金额的lockInfo 是没有和申请id 绑定关系的，所以可能不在当前数据里面*/
            boolean anyMatch = currData.stream().anyMatch(vo -> perDataMap.containsKey(vo.getId()) && !Objects.equals(vo.getVersion(), perDataMap.get(vo.getId())));
            if(anyMatch){
                return LayuiReplay.error("当前数据不是最新，请重新选择支付金额！！");
            }   
            currData.forEach(vo ->vo.setVersion(vo.getVersion()+1));
            paymentApplyVo.setPracticeLockInfoVoList(currData);
            Long key = (Long)session.getAttribute(QueryUtil.PRAC_REHECT_SESSION_KEY);
            paymentApplyVo.setOperateTime(key);
            /**驳回提交*/
            insurancePracticeLockWrapperService.updatePracticePayDetails(paymentApplyVo);
            List<PracticePayDetailVo> practicePayDetailVos = iPaymentApplyWrapperService.selectDetailsByPayId(paymentApplyVo.getId());
            getServiceOrderSet(practicePayDetailVos,paymentApplyVo);
            paymentApplyVo.setDetailVos(practicePayDetailVos);
            /**提交的时候删除*/
            session.removeAttribute(QueryUtil.PRAC_REHECT_SESSION_KEY);
        }
        //获取当前对象的所属公司
//        String judgePosition = paymentApplyVo.getPosCode().split(",")[0].substring(0, 1);
//        UserOrgPosVo userOrgPosVo = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(loginName);
//        String posCode = userOrgPosVo.getPosCode();
//        if (StringUtil.isNotEmpty(posCode) && posCode.startsWith("3")) {
//            paymentApplyVo.setAppCom (posCode);
//            paymentApplyVo.setJudgePosition("3");
//        } else {
//            paymentApplyVo.setAppCom(posCode);
//        }


//        try {
//	        if ("commit".equals(paymentApplyVo.getType()) && StringUtil.isEmpty(paymentApplyVo.getPid())) {
//                //设置日期格式
//                Date utilDate = new Date ();
//                java.sql.Date date = new java.sql.Date (utilDate.getTime ());
//                paymentApplyVo.setApplyTime (date);
//                String pid = startPaymentApplyWorkflow (paymentApplyVo);
//                paymentApplyVo.setPid (pid);
//                paymentApplyVo.setAppStatus (5);
//                iPaymentApplyWrapperService.save (paymentApplyVo);
//	        } else if ("save".equals(paymentApplyVo.getType())) {
//		        if (paymentApplyVo.getAppStatus() != null && paymentApplyVo.getAppStatus() == 1) {
//			        paymentApplyVo.setAppStatus(1);
//		        } else {
//			        paymentApplyVo.setAppStatus(4);
//		        }
//		        iPaymentApplyWrapperService.save(paymentApplyVo);
//	        }
//	        else if ("commit".equals(paymentApplyVo.getType()) && StringUtil.isNotEmpty(paymentApplyVo.getPid())) {
		     try{
		     	String editPid = paymentApplyVo.getPid();
		     	TaskQueryDTO taskQueryDTO = new TaskQueryDTO(ReonWorkflowType.PAYMENT_APPLY.getDefineKey(), ReonWorkflowType.PAYMENT_APPLY.getBussinessKey(), loginName,editPid);
		        TaskVo task = workflowWrapperService.getOneTask(taskQueryDTO);
		        String taskId = task.getId();
                setApprovalProcessValues(paymentApplyVo);
		        workflowWrapperService.excuteTask(taskId,paymentApplyVo.getApprovalParamMap(),ManualAction.PASS,"驳回后提交");
		        paymentApplyVo.setAppStatus (PaymentApplyProcessStatus.PENDING_APPROVAL.getCode());
                 //更新接口
		         iPaymentApplyWrapperService.save (paymentApplyVo);
                 iPaymentApplyWrapperService.deleteInsurancePracticeDisComPayByPayId(paymentApplyVo.getId());
                 iPaymentApplyWrapperService.deletePayServiceLogByPayId(paymentApplyVo.getId());
                 insertInsurancePracticeDisComPayVoAndPayLog(paymentApplyVo.getId(),paymentApplyVo,loginName);
                 insertOrUpdateDetailRemark(remarkVos,getSessionUser().getLoginName(),paymentApplyVo.getId(),false);
		     }catch (Exception e){
			     log.error("驳回后提交错误");
                 return new LayuiReplay<String> (ResultEnum.ERR.getCode (), ResultEnum.ERR.getMsg ());
		     }
//	        }
//        } catch (Exception e) {
//            return new LayuiReplay<String> (ResultEnum.ERR.getCode (), ResultEnum.ERR.getMsg ());
//        }

        return new LayuiReplay<PaymentApplyVo> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
    }

    @RepeatSubmit
    @PostMapping("/applyInsurancePracticePayment")
    public LayuiReplay applyInsurancePracticePayment(@RequestParam("param")String param, HttpSession session){
        try {
            PracPaymentApplyVo paymentApplyVo = JsonUtil.jsonToBean(param, PracPaymentApplyVo.class);
            if ("[]".equals(paymentApplyVo.getFileId())){
                return LayuiReplay.error("请上传文件！");
            }
            setFileId(paymentApplyVo.getFileId(),paymentApplyVo);
            CommonUserVo commonUserVo = getSessionUser ();
            String loginName = commonUserVo.getLoginName ();
            paymentApplyVo.setCreator(loginName);
            String packCode = paymentApplyVo.getPackCode();
            SearchInsuranceLockDto lockDto = paymentApplyVo.buildDto();
            if(StringUtil.isNotBlank(packCode)){
                lockDto.setPackCodes(Arrays.stream(packCode.split(",")).collect(Collectors.toList()));
            }
            if(Objects.isNull(lockDto.getOrgCode()) || CollectionUtils.isEmpty(lockDto.getPackCodes())){
                return new LayuiReplay(LayuiReplay.error().getCode(), "缺少福利办理方，或者福利包");
            }
            boolean ckeckHoliday = checkLastDateISHoliday(paymentApplyVo.getLastDate());
            if (!ckeckHoliday){
                return LayuiReplay.error("最晚支付日期不能是节假日");
            }
            boolean ckeck = checkLastDate(paymentApplyVo.getLastDate());
            if (!ckeck){
                return LayuiReplay.error("最晚支付日期要至少在两天后");
            }
            List<InsurancePracticePayBankDetailRemarkVo> remarkVos = JsonUtil.jsonToList(paymentApplyVo.getRemarkVo(), InsurancePracticePayBankDetailRemarkVo.class);
            String lockInfoJson = paymentApplyVo.getLockInfoJson();
            List<PracticeLockInfoVo> perData = JsonUtil.jsonToList(lockInfoJson, PracticeLockInfoVo.class);
            Map<Long, Long> perDataMap = perData.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, PracticeLockInfoVo::getVersion));
            List<PracticeLockInfoVo> currData = insurancePracticeLockWrapperService.getBySearch(lockDto);
            /***/
            boolean anyMatch = currData.stream().anyMatch(vo -> perDataMap.containsKey(vo.getId()) && !Objects.equals(vo.getVersion(), perDataMap.get(vo.getId())));
            if(anyMatch){
                return LayuiReplay.error("当前数据不是最新，请重新选择支付金额！！");
            }



            currData.forEach(vo ->vo.setVersion(vo.getVersion()+1));
            paymentApplyVo.setPracticeLockInfoVoList(currData);
            Long operateTime = (Long)session.getAttribute(QueryUtil.PRAC_SESSION_KEY);
            lockDto.setOperateTime(operateTime);
            /**检验金额是否相等*/
            List<PracticePayDetailVo> detailVos = insurancePracticeLockWrapperService.checkPracPaymentAmt(lockDto,paymentApplyVo.getPayDetailType());
            if(Objects.isNull(detailVos)){
                return LayuiReplay.error("请联系管理员,支付金额有误！");
            }
            checkCompanyBank(paymentApplyVo);
            paymentApplyVo.setDetailVos(detailVos);
            getServiceOrderSet(detailVos,paymentApplyVo);
            setApprovalProcessValues (paymentApplyVo);
            String pid = workflowWrapperService.startProcessInstanceByKeyAndExcuteFirstNode(ReonWorkflowType.PAYMENT_APPLY.getDefineKey(), ReonWorkflowType.PAYMENT_APPLY.getBussinessKey(), paymentApplyVo.getApprovalParamMap());


//            List<Byte> feeTypeList;
//            if (PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT_AND_REPLENISH_PAYMENT.getCode().equals(paymentApplyVo.getPayDetailType())){
//                feeTypeList = Arrays.asList(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode(),PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode());
//            }else {
//                feeTypeList = Collections.singletonList(paymentApplyVo.getPayDetailType());
//            }
//            searchInsuranceLockDto.setOrgCode(paymentApplyVo.getWelfareTransaction());
//            searchInsuranceLockDto.setFeeTypeList(feeTypeList);
//            searchInsuranceLockDto.initCanPayStatus();
//            searchInsuranceLockDto.setGroupCode(paymentApplyVo.getInsuranceGroup());

            paymentApplyVo.setPid (pid);
            paymentApplyVo.setPracticeLockIds(currData.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList()));
            Long id = iPaymentApplyWrapperService.applyInsurancePracticePayment(paymentApplyVo);
            insertInsurancePracticeDisComPayVoAndPayLog(id,paymentApplyVo,loginName);
            insertOrUpdateDetailRemark(remarkVos,getSessionUser().getLoginName(),id,true);
            session.removeAttribute(QueryUtil.PRAC_SESSION_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            return LayuiReplay.error();
        }
        return LayuiReplay.success();
    }


    /**
     * 终止支付管理
     *
     * @param paymentApplyVo
     * @return
     */
    @PostMapping(value = "/termination")
    public Object terminationPaymentApply(@RequestBody PaymentApplyVo paymentApplyVo) {
//        CommonUserVo commonUserVo = getSessionUser ();
        paymentApplyVo.setAppStatus (3);
        if(Objects.isNull(paymentApplyVo.getPid())){
            return LayuiReplay.error();
        }
        /**根据支付申请id，修改实做报表数据 */
        String loginName = getSessionUser().getLoginName();
        paymentApplyVo.setCreator(loginName);
        boolean update = iPaymentApplyWrapperService.editPaymentApplyAndPrac(paymentApplyVo);
        workflowWrapperService.deleteByPidList(Lists.newArrayList(paymentApplyVo.getPid()));
        iPaymentApplyWrapperService.deleteInsurancePracticeDisComPayByPayId(paymentApplyVo.getId());
//        boolean update = iPaymentApplyWrapperService.updateAppStatus (paymentApplyVo);
        return new LayuiReplay<PaymentApplyVo> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), update);
    }

    /**
     * 设置审批流程节点值
     *
     * @param paymentApplyVo
     * @return
     */
    public void setApprovalProcessValues(PracPaymentApplyVo paymentApplyVo) throws Exception {
        List<PracticePayDetailVo> detailVos = paymentApplyVo.getDetailVos();
        Set<String> serviceOrderSet = paymentApplyVo.getServiceOrderSet();

        Map<String, Object> varMaps = Maps.newHashMap();

        String loginName = getSessionUser().getLoginName();
        UserOrgPosVo userOrgPosVo = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(loginName);
        UserOrgPosVo largeUserVo = userWrapperService.getLargeDefaultFlagOrgPosByOrgCode(userOrgPosVo.getOrgCode());
        if (largeUserVo==null){
            log.info("流程审批中组织人员缺失区域负责人岗位信息！");
            throw new Exception("流程审批中组织人员缺失区域负责人岗位信息！");
        }
        List<String> orderNoList = detailVos.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toList());
        List<EmployeeOrderVo> contractSignComTitleByOrderNoList = employeeOrderWrapperService.getContractSignComTitleByOrderNoList(orderNoList);

        //根据签约方抬头分组
        Map<String, EmployeeOrderVo> orderNoAndsignDistComMap = contractSignComTitleByOrderNoList.stream().collect(Collectors.toMap(EmployeeOrderVo::getOrderNo,Function.identity()));
        for (PracticePayDetailVo detailVo : detailVos) {
            if (orderNoAndsignDistComMap.get(detailVo.getOrderNo())!=null){
                EmployeeOrderVo employeeOrderVo = orderNoAndsignDistComMap.get(detailVo.getOrderNo());
                detailVo.setSignDistCom(employeeOrderVo.getSignDistCom());
                detailVo.setContractType(employeeOrderVo.getContractType());
            }else {
                log.info("流程审批中组织人员缺失签约方抬头信息！,订单号为{}",detailVo.getOrderNo());
            }

        }
        Map<String, List<PracticePayDetailVo>> siginMap = detailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getSignDistCom));
        Set<String> signDistComSet = new HashSet<>(siginMap.keySet());

        //审批节点中的派单地财务经理--机构为签约方抬头
        List<String> approveFinancialManagerList = new ArrayList<>();

        // 派单地（签约方抬头）及其对应的支付总额
        Map<String, BigDecimal> distComAndTotalAmtMap = new HashMap<>();
        Map<String, BigDecimal> serviceMap = new HashMap<>();
        Map<String, DeductionBalanceCacheVo> balanceCacheVoMap = new HashMap<>();
        List<InsurancePracticeCustPayDetailVo> insurancePracticeCustPayDetailVos = new ArrayList<>();

        if (CollectionUtils.isEmpty(signDistComSet)){
            log.info("流程审批中组织人员缺失派单地财务信息！");
            throw new Exception("流程审批中组织人员缺失派单地财务信息！");
        }
        if (paymentApplyVo.getApprovalFlag()){
            signDistComSet.remove(paymentApplyVo.getOrgCode());
        }
        List<InsurancePracticeServiceConfigVo> insurancePracticeServiceConfig = insurancePracticeServiceConfigWrapperService.getInsurancePracticeServiceConfig();
        Map<String, BigDecimal> serviceConfigMap = insurancePracticeServiceConfig.stream().collect(Collectors.toMap(InsurancePracticeServiceConfigVo::getOrgCode, InsurancePracticeServiceConfigVo::getServiceAmt));
        if (signDistComSet.isEmpty()){
            String orgCode = paymentApplyVo.getOrgCode();
            BigDecimal serviceAmt = serviceConfigMap.get(orgCode);
            List<InsurancePracticeCustPayDetailVo> payCustDetailVos = getPayCustDetailVos(siginMap.get(orgCode), paymentApplyVo.getCustIdAndServiceCountMap(), serviceAmt, orgCode, orgCode,BooleanTypeEnum.YES.getCode());
            insurancePracticeCustPayDetailVos.addAll(payCustDetailVos);
            varMaps.put("companyCounts", 1);
        }else {
            int size = signDistComSet.size();
            if (size ==1){
                size=2;
            }
            varMaps.put("companyCounts", size);
            


            for (String signDistComNo : signDistComSet) {
                List<PracticePayDetailVo> practicePayDetailVos = siginMap.get(signDistComNo);
                Set<String> orderNoSet = practicePayDetailVos.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
                orderNoSet.retainAll(serviceOrderSet);
                BigDecimal signDistTotalAmt = practicePayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                distComAndTotalAmtMap.put(signDistComNo, signDistTotalAmt);
                BigDecimal serviceAmt = serviceConfigMap.get(signDistComNo);
                BigDecimal serviceAmtTotal = serviceAmt.multiply(BigDecimal.valueOf(orderNoSet.size()));
                serviceMap.put(signDistComNo, serviceAmtTotal);
                List<InsurancePracticeCustPayDetailVo> payCustDetailVos = getPayCustDetailVos(practicePayDetailVos, paymentApplyVo.getCustIdAndServiceCountMap(), serviceAmt, signDistComNo, paymentApplyVo.getOrgCode(),BooleanTypeEnum.NO.getCode());
                DeductionBalanceCacheVo deductionBalanceCacheVo = insertBalance(practicePayDetailVos, signDistComNo, paymentApplyVo.getOrgCode(), loginName, signDistTotalAmt.add(serviceAmtTotal),payCustDetailVos);
                if (deductionBalanceCacheVo!=null){
                    balanceCacheVoMap.put(signDistComNo, deductionBalanceCacheVo);
                }
                insurancePracticeCustPayDetailVos.addAll(payCustDetailVos);
            }

        }


        signDistComSet.forEach(item ->{
            approveFinancialManagerList.add(item+","+ PositionEnum.TREASURY_RECHECK_ATTACHE.getCode());
        });
        varMaps.put("userList", approveFinancialManagerList);
        varMaps.put("financialOfficer", paymentApplyVo.getOrgCode()+","+ PositionEnum.TREASURY_RECHECK_ATTACHE.getCode());
        varMaps.put("starter", getSessionUser().getLoginName());
        varMaps.put("manager", largeUserVo.getOrgCode() + "," + largeUserVo.getPosCode());
        varMaps.put("isEqual", false);
        paymentApplyVo.setApprovalParamMap(varMaps);
        paymentApplyVo.setSignDistComAndCountMap(distComAndTotalAmtMap);
        paymentApplyVo.setServiceMap(serviceMap);
        paymentApplyVo.setBalanceMap(balanceCacheVoMap);
        paymentApplyVo.setPayDetailVos(insurancePracticeCustPayDetailVos);
    }

    public void insertInsurancePracticeDisComPayVoAndPayLog(Long payId,PracPaymentApplyVo paymentApplyVo,String loginName){
        List<PayServiceSerialLogVo> payServiceSerialLogVos = new ArrayList<>();
        List<InsurancePracticeDisComPayVo> insurancePracticeDisComPayVos = new ArrayList<>();
        List<InsurancePracticeOneFeeBalanceVo> balanceUpdates = new ArrayList<>();
        List<InsurancePracticeOneFeeChargeRecordVo> chargeRecords = new ArrayList<>();
        Map<String, BigDecimal> signDistComAndCountMap = paymentApplyVo.getSignDistComAndCountMap();
        signDistComAndCountMap.keySet().forEach(item ->{
            String cmbSinglePayBusinessNo = iSequenceService.getCMBSinglePayBusinessNo();
            InsurancePracticeDisComPayVo insurancePracticeDisComPayVo = new InsurancePracticeDisComPayVo();
            insurancePracticeDisComPayVo.setPayId(payId);
            insurancePracticeDisComPayVo.setYurref(cmbSinglePayBusinessNo);
            insurancePracticeDisComPayVo.setDisCom(item);
            insurancePracticeDisComPayVo.setDisComApp(item+","+ PositionEnum.TREASURY_RECHECK_ATTACHE.getCode());
            insurancePracticeDisComPayVo.setCreator(loginName);
            insurancePracticeDisComPayVo.setUpdater(loginName);
            insurancePracticeDisComPayVo.setPayAmt(signDistComAndCountMap.get(item));
            insurancePracticeDisComPayVo.setServiceAmt(paymentApplyVo.getServiceMap().get(item));
            insurancePracticeDisComPayVo.setActPayAmt(signDistComAndCountMap.get(item).add(paymentApplyVo.getServiceMap().get(item)));
            DeductionBalanceCacheVo deductionBalanceCacheVo = paymentApplyVo.getBalanceMap().get(item);
            if (deductionBalanceCacheVo!=null&&!deductionBalanceCacheVo.getBalanceUpdates().isEmpty()){
                insurancePracticeDisComPayVo.setActPayAmt(deductionBalanceCacheVo.getTotalAmt());
                balanceUpdates.addAll(deductionBalanceCacheVo.getBalanceUpdates());
                deductionBalanceCacheVo.getChargeRecords().forEach(c ->{
                    c.setPaymentId(payId);
                });

                chargeRecords.addAll(deductionBalanceCacheVo.getChargeRecords());
            }
            insurancePracticeDisComPayVos.add(insurancePracticeDisComPayVo);

            PayServiceSerialLogVo payServiceSerialLogVo = new PayServiceSerialLogVo();
            payServiceSerialLogVo.setCreator(loginName);
            payServiceSerialLogVo.setPaymentId(payId);
            payServiceSerialLogVo.setYurref(cmbSinglePayBusinessNo);
            payServiceSerialLogVo.setPayServiceSerialType(PayServiceSerialDetailType.CORP_SINGLE_PAY_PAY_COM.getParentCode());
            payServiceSerialLogVo.setPayServiceSerialDetailType(PayServiceSerialDetailType.CORP_SINGLE_PAY_PAY_COM.getCode());
            payServiceSerialLogVos.add(payServiceSerialLogVo);
        });
        if (CollectionUtils.isNotEmpty(insurancePracticeDisComPayVos)){
            iPaymentApplyWrapperService.saveInsurancePracticeDisComPayBatch(insurancePracticeDisComPayVos);
        }
        if (CollectionUtils.isNotEmpty(payServiceSerialLogVos)){
            iPaymentApplyWrapperService.insertPayServiceLog(payServiceSerialLogVos);
        }
        if (CollectionUtils.isNotEmpty(balanceUpdates)&&CollectionUtils.isNotEmpty(chargeRecords)){
            DeductionBalanceCacheVo deductionBalanceCacheVo = new DeductionBalanceCacheVo();
            deductionBalanceCacheVo.setBalanceUpdates(balanceUpdates);
            deductionBalanceCacheVo.setChargeRecords(chargeRecords);
            iInsurancePracticeOneFeeWrapperService.updateInsurancePracticeOneFeeBalanceAndRecordVo(deductionBalanceCacheVo);
        }
        paymentApplyVo.getPayDetailVos().forEach(c ->{
            c.setPayId(payId);
        });
        iInsurancePracticeOneFeeWrapperService.batchInsertPayCustDetailVos(paymentApplyVo.getPayDetailVos());
    }

    public void setFileId(String fileId, PaymentApplyVo paymentApplyVo){
        String fileIdStr = fileId.replaceAll("[\\[\\]\"]", "");
        paymentApplyVo.setFileId(fileIdStr);
    }
    public void checkCompanyBank(PracPaymentApplyVo paymentApplyVo){
        InsurancePracticePayBankConfigVo bankConfigByOrgCode = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getOrgCode());
        if (Objects.isNull(bankConfigByOrgCode)){
            paymentApplyVo.setApprovalFlag(true);
        }else if (StringUtils.isBlank(bankConfigByOrgCode.getDispatchBankNo())){
            paymentApplyVo.setApprovalFlag(true);
        }else if (bankConfigByOrgCode.getDispatchBankNo().equals(bankConfigByOrgCode.getReceivingBankNo())){
            paymentApplyVo.setApprovalFlag(true);
        }else {
            paymentApplyVo.setApprovalFlag(false);
        }

    }

    /**
     * 判断是否为节假日
     * @param lastDate
     * @return
     */
    public boolean checkLastDateISHoliday(String lastDate){
        DateTimeFormatter formatter;
        if (lastDate.length()>8){
            lastDate = lastDate.substring(0,10);
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        }else {
            formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        }
        LocalDate date = LocalDate.parse(lastDate, formatter);
        HolidaysVo holidayVo = holidaysWrapperService.selectHolidaysByDate(date);
        if (holidayVo==null){
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY){
                return false;
            }
        }else if (holidayVo.getStatus().equals(BooleanTypeEnum.NO.getCode())){
            return false;
        }

       return true;
    }

    public boolean checkLastDate(String lastDateS){
        DateTimeFormatter formatter;
        if (lastDateS.length()>8){
            lastDateS = lastDateS.substring(0,10);
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        }else {
            formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        }
        LocalDate lastDate = LocalDate.parse(lastDateS, formatter);
        LocalDate currentDate = LocalDate.now();
        LocalDate twoDaysLater = currentDate.plusDays(2);
        if (lastDate.isBefore(twoDaysLater)){
           return false;
        }
        return true;
    }

    public void insertOrUpdateDetailRemark(List<InsurancePracticePayBankDetailRemarkVo> remarkVos,String creator,Long payId,boolean flag){
        remarkVos.forEach(item ->{
            if (flag){
                item.setCreator(creator);
                item.setCreateTime(new Date());
                item.setPayId(payId);
                insurancePracticePayBankConfigWrapperService.addInsurancePracticePayBankDetailRemarkVo(item);
            }else {
                if (Objects.isNull(item.getId())){
                    item.setCreator(creator);
                    item.setCreateTime(new Date());
                    item.setPayId(payId);
                    insurancePracticePayBankConfigWrapperService.addInsurancePracticePayBankDetailRemarkVo(item);
                }else {
                    item.setUpdater(creator);
                    item.setUpdateTime(new Date());
                    insurancePracticePayBankConfigWrapperService.updateInsurancePracticePayBankDetailRemarkVo(item);
                }

            }
        });
    }

    /**
     * 计算服务费人数 和客户对应的服务费人数
     * @param practicePayDetailVos
     * @param paymentApplyVo
     * @return {@link Set }<{@link String }>
     */
    public void getServiceOrderSet(List<PracticePayDetailVo> practicePayDetailVos,PracPaymentApplyVo paymentApplyVo){

        List<PracticePayDetailVo> serviceOrderList = practicePayDetailVos.stream()
                .filter(c ->
                        c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE10.getIndex()
                                && c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE11.getIndex()
                                && Objects.equals(c.getFeeType(), Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()))
                )
                .collect(Collectors.toList());
        Map<Long, Integer> custOrderCountMap = serviceOrderList.stream()
                .collect(Collectors.groupingBy(
                        PracticePayDetailVo::getCustId,
                        Collectors.mapping(
                                PracticePayDetailVo::getOrderNo,
                                Collectors.collectingAndThen(
                                        Collectors.toSet(),
                                        Set::size
                                )
                        )
                ));
        Set<String> collect = serviceOrderList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
        paymentApplyVo.setServiceOrderSet(collect);
        paymentApplyVo.setCustIdAndServiceCountMap(custOrderCountMap);
    }


    /**
     * 抵扣金额计算
     * @param practicePayDetailVos
     * @param disCom
     * @param payCom
     * @param loginName
     * @param payAmt 派单地申请应付总额
     * @return {@link DeductionBalanceCacheVo }
     */
    public DeductionBalanceCacheVo insertBalance(List<PracticePayDetailVo> practicePayDetailVos,String disCom,String payCom,String loginName,BigDecimal payAmt,List<InsurancePracticeCustPayDetailVo> payDetailVos){
        List<Long> custIdList = practicePayDetailVos.stream().map(PracticePayDetailVo::getCustId).collect(Collectors.toList());

        //差异金额数据
        List<InsurancePracticeOneFeeBalanceVo> deductionAmtByCustIdList = iInsurancePracticeOneFeeWrapperService.getDeductionAmtByPayComAndDisCom(payCom, disCom);
        if (CollectionUtils.isEmpty(deductionAmtByCustIdList)){
            return null;
        }
        List<InsurancePracticeOneFeeBalanceVo> custInBalanceList = deductionAmtByCustIdList.stream().filter(c ->custIdList.contains(c.getCustId())).collect(Collectors.toList());
        List<InsurancePracticeOneFeeBalanceVo> custNotInBalanceList = deductionAmtByCustIdList.stream().filter(c ->!custIdList.contains(c.getCustId())).collect(Collectors.toList());
        Map<Long, InsurancePracticeOneFeeBalanceVo> custInIdBalanceMap = custInBalanceList.stream().collect(Collectors.toMap(InsurancePracticeOneFeeBalanceVo::getCustId, Function.identity()));
        Map<Long, InsurancePracticeOneFeeBalanceVo> custNotIdBalanceMap = custNotInBalanceList.stream().collect(Collectors.toMap(InsurancePracticeOneFeeBalanceVo::getCustId, Function.identity()));


        Map<String, InsurancePracticeCustPayDetailVo> custPayDetailVoMap =
                payDetailVos.stream().collect(Collectors.toMap(
                        vo -> vo.getCustId() + "," + disCom,
                        Function.identity()
                ));

        List<InsurancePracticeOneFeeChargeRecordVo> recordList = new ArrayList<>();
        List<InsurancePracticeOneFeeBalanceVo> balanceList = new ArrayList<>();
        for (Long custId : custInIdBalanceMap.keySet()) {
            InsurancePracticeCustPayDetailVo custPayDetailVo = custPayDetailVoMap.get(custId+","+disCom);
            //本次支付客户应付金额 不包含服务费
            BigDecimal custPayAmt = custPayDetailVo.getSocialAmt().add(custPayDetailVo.getProvidentAmt());
            //本次支付客户实际应付金额
            BigDecimal custActualPayAmt = BigDecimal.ZERO;



            InsurancePracticeOneFeeBalanceVo insurancePracticeOneFeeBalanceVo = custInIdBalanceMap.get(custId);
            insurancePracticeOneFeeBalanceVo.setUpdater(loginName);
            InsurancePracticeOneFeeChargeRecordVo insertRecordVo = setInsertRecord(insurancePracticeOneFeeBalanceVo.getId(), loginName, custId);
            //剩余抵扣金额
            BigDecimal remainAmt = insurancePracticeOneFeeBalanceVo.getRemainAmt();
            custPayDetailVo.setBalanceAmt(remainAmt);
                //正数
                if (remainAmt.compareTo(BigDecimal.ZERO) > 0) {
                    insertRecordVo.setChargeAmt(remainAmt);
                    payAmt = payAmt.add(remainAmt);
                    insurancePracticeOneFeeBalanceVo.setRemainAmt(BigDecimal.ZERO);
                    insurancePracticeOneFeeBalanceVo.setUsedAmt(insurancePracticeOneFeeBalanceVo.getTotalAmt());
                    custActualPayAmt = custPayAmt.add(remainAmt);
                } else {
                    BigDecimal resultAmt = custPayAmt.add(remainAmt);
                    if (resultAmt.compareTo(BigDecimal.ZERO) >= 0) {
                        payAmt = payAmt.subtract(custPayAmt).add(resultAmt);
                        insurancePracticeOneFeeBalanceVo.setRemainAmt(BigDecimal.ZERO);
                        insurancePracticeOneFeeBalanceVo.setUsedAmt(insurancePracticeOneFeeBalanceVo.getTotalAmt());
                        insertRecordVo.setChargeAmt(remainAmt);
                        custActualPayAmt = resultAmt;
                    } else {
                        insertRecordVo.setChargeAmt(custPayAmt.negate());
                        payAmt = payAmt.subtract(custPayAmt);
                        insurancePracticeOneFeeBalanceVo.setRemainAmt(resultAmt);
                        insurancePracticeOneFeeBalanceVo.setUsedAmt(insurancePracticeOneFeeBalanceVo.getUsedAmt().add(custPayAmt.negate()));
                        custPayDetailVo.setBalanceAmt(custPayAmt.negate());
                    }
                }
                custPayDetailVo.setActPayAmt(custActualPayAmt);

                recordList.add(insertRecordVo);
                balanceList.add(insurancePracticeOneFeeBalanceVo);


        }
        for (Long custId : custNotIdBalanceMap.keySet()) {
            InsurancePracticeOneFeeBalanceVo insurancePracticeOneFeeBalanceVo = custNotIdBalanceMap.get(custId);
            BigDecimal remainAmt = insurancePracticeOneFeeBalanceVo.getRemainAmt();
            if (remainAmt.compareTo(BigDecimal.ZERO) > 0){
                InsurancePracticeCustPayDetailVo insertCustPayDetailVo = setInsertCustPayDetailVo(custId, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, payCom, disCom,remainAmt,remainAmt,0,99);
                insertCustPayDetailVo.setType(BooleanTypeEnum.YES.getCode());
                InsurancePracticeOneFeeChargeRecordVo insertRecordVo = setInsertRecord(insurancePracticeOneFeeBalanceVo.getId(), loginName, custId);
                insertRecordVo.setChargeAmt(remainAmt);
                payAmt = payAmt.add(remainAmt);
                insurancePracticeOneFeeBalanceVo.setRemainAmt(BigDecimal.ZERO);
                insurancePracticeOneFeeBalanceVo.setUsedAmt(insurancePracticeOneFeeBalanceVo.getTotalAmt());
                recordList.add(insertRecordVo);
                balanceList.add(insurancePracticeOneFeeBalanceVo);
                payDetailVos.add(insertCustPayDetailVo);
            }
        }
        if (CollectionUtils.isNotEmpty(recordList)) {
            DeductionBalanceCacheVo cacheDto = new DeductionBalanceCacheVo();
            cacheDto.setChargeRecords(recordList);
            cacheDto.setBalanceUpdates(balanceList);
            cacheDto.setTotalAmt(payAmt);
            return cacheDto;
        }
        return null;
    }

    /**
     * 组装客户详情数据
     * @param practicePayDetailVos
     * @param serviceCountByCustMap
     * @param serviceAmt
     * @param disCom
     * @param payCom
     * @param type 1：派单方客户 2：接单方客户
     * @return {@link List }<{@link InsurancePracticeCustPayDetailVo }>
     */
    public List<InsurancePracticeCustPayDetailVo> getPayCustDetailVos(List<PracticePayDetailVo> practicePayDetailVos,Map<Long,Integer> serviceCountByCustMap,BigDecimal serviceAmt,String disCom,String payCom,Integer type) {
        Set<Long> custIdSet = practicePayDetailVos.stream().map(PracticePayDetailVo::getCustId).collect(Collectors.toSet());

        Map<Long, List<PracticePayDetailVo>> custOrderCountMap = practicePayDetailVos.stream()
                .collect(Collectors.groupingBy(
                        PracticePayDetailVo::getCustId
                ));
        List<InsurancePracticeCustPayDetailVo> insurancePracticeCustPayDetailVos = new ArrayList<>();
        /**处理数据 插入支付申请关联客户金额详情 表*/
        List<PracticePayDetailVo> soicalOrderList = practicePayDetailVos.stream()
                .filter(c ->
                        c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE10.getIndex()
                                && c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE11.getIndex()
                ).collect(Collectors.toList());
        Map<Long, BigDecimal> soicalAmtMap = soicalOrderList.stream()
                .collect(Collectors.groupingBy(
                        PracticePayDetailVo::getCustId,
                        Collectors.mapping(
                                PracticePayDetailVo::getAmount,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        List<PracticePayDetailVo> providentOrderList = practicePayDetailVos.stream()
                .filter(c ->
                        c.getProdCode().equals(BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE10.getIndex())
                                || c.getProdCode().equals(BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE11.getIndex())
                ).collect(Collectors.toList());
        Map<Long, BigDecimal> providentAmtMap = providentOrderList.stream()
                .collect(Collectors.groupingBy(
                        PracticePayDetailVo::getCustId,
                        Collectors.mapping(
                                PracticePayDetailVo::getAmount,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        for (Long custId : custIdSet) {
            List<PracticePayDetailVo> practicePayDetailVos1 = custOrderCountMap.get(custId);
            Set sum = practicePayDetailVos1.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            Integer count = serviceCountByCustMap.getOrDefault(custId, 0);
            BigDecimal serviceAmtByCust = serviceAmt.multiply(BigDecimal.valueOf(count));
            BigDecimal providentAmt = providentAmtMap.getOrDefault(custId, BigDecimal.ZERO);
            BigDecimal socialAmt = soicalAmtMap.getOrDefault(custId, BigDecimal.ZERO);
            BigDecimal custPayAmt = providentAmt.add(socialAmt);
            InsurancePracticeCustPayDetailVo insertCustPayDetailVo=null;
            if (type.equals(BooleanTypeEnum.YES.getCode())){
                insertCustPayDetailVo= setInsertCustPayDetailVo(custId,
                        soicalAmtMap.get(custId), providentAmtMap.get(custId),
                        BigDecimal.ZERO, custPayAmt,
                        payCom, disCom,BigDecimal.ZERO,custPayAmt,sum.size(),practicePayDetailVos1.get(0).getContractType());
            }else {
                insertCustPayDetailVo= setInsertCustPayDetailVo(custId,
                        soicalAmtMap.get(custId), providentAmtMap.get(custId),
                        serviceAmtByCust, custPayAmt.add(serviceAmtByCust),
                        payCom, disCom,BigDecimal.ZERO,custPayAmt.add(serviceAmtByCust),sum.size(),practicePayDetailVos1.get(0).getContractType());
            }


            insurancePracticeCustPayDetailVos.add(insertCustPayDetailVo);
        }
        return insurancePracticeCustPayDetailVos;
    }

    public InsurancePracticeOneFeeChargeRecordVo setInsertRecord(Long balanceId,String creator,Long custId) {
        InsurancePracticeOneFeeChargeRecordVo insetOneBalanceRecordVo = new InsurancePracticeOneFeeChargeRecordVo();
        insetOneBalanceRecordVo.setBalanceId(balanceId);
        insetOneBalanceRecordVo.setCustId(custId);
        insetOneBalanceRecordVo.setChargeStatus(1);
        insetOneBalanceRecordVo.setCreator(creator);
        insetOneBalanceRecordVo.setCreateTime(new Date());
        return insetOneBalanceRecordVo;
    }

    public InsurancePracticeCustPayDetailVo setInsertCustPayDetailVo(Long custId,BigDecimal socialAmt,BigDecimal providentAmt,BigDecimal serviceAmt,BigDecimal totalAmt,String orgCode,String disCom,BigDecimal balanceAmt,BigDecimal actPayAmt,Integer sum,Integer contractType) {
        InsurancePracticeCustPayDetailVo insurancePracticeCustPayDetailVo = new InsurancePracticeCustPayDetailVo();
        insurancePracticeCustPayDetailVo.setPayCom(orgCode);
        insurancePracticeCustPayDetailVo.setDisCom(disCom);
        insurancePracticeCustPayDetailVo.setCustId(custId);
        insurancePracticeCustPayDetailVo.setSocialAmt(socialAmt);
        insurancePracticeCustPayDetailVo.setProvidentAmt(providentAmt);
        insurancePracticeCustPayDetailVo.setServiceAmt(serviceAmt);
        insurancePracticeCustPayDetailVo.setTotalAmt(totalAmt);
        insurancePracticeCustPayDetailVo.setBalanceAmt(balanceAmt);
        insurancePracticeCustPayDetailVo.setActPayAmt(actPayAmt);
        insurancePracticeCustPayDetailVo.setType(BooleanTypeEnum.NO.getCode());
        insurancePracticeCustPayDetailVo.setOrgType(BooleanTypeEnum.NO.getCode());
        insurancePracticeCustPayDetailVo.setSum(sum);
        insurancePracticeCustPayDetailVo.setContractType(contractType);
        return insurancePracticeCustPayDetailVo;
    }



}

