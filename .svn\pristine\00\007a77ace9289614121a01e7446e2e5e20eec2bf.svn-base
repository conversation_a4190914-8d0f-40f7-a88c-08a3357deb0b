package com.reon.hr.sp.bill.dao.insurancePractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.dto.SearchInsuranceLockDto;
import com.reon.hr.api.bill.dto.SearchReportDto;
import com.reon.hr.api.bill.vo.insurancePractice.ExportPracticeReportVo;
import com.reon.hr.api.bill.vo.insurancePractice.InsurancePracticeReportVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.customer.vo.OrderAndInsuranceDiffExportVo;
import com.reon.hr.api.customer.vo.OrderAndInsuranceDiffParamVo;
import com.reon.hr.sp.bill.entity.insurancePractice.InsurancePracticeReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InsurancePracticeReportMapper {

    Integer deleteByInsurancePracticeReportLock(PracticeLockInfoVo practiceLockInfoVo);
    Integer deleteByOrgCodeAndPackCode(PracticeLockInfoVo practiceLockInfoVo);

    Integer insertList(@Param("list") List<InsurancePracticeReport> insurancePracticeReports, @Param("lockMonth") Integer lockMonth);

    List<ExportPracticeReportVo> getExportPracticeReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto);

    List<InsurancePracticeReportVo> getInsurancePracticeReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto);
    List<InsurancePracticeReportVo> getInsurancePracticeReport(SearchInsuranceLockDto searchInsuranceLockDto);
    List<InsurancePracticeReportVo> getInsurancePracticeReportByCondition(SearchInsuranceLockDto searchInsuranceLockDto);
    List<InsurancePracticeReportVo> getInsurancePracticeReportByConditionToReport(SearchInsuranceLockDto searchInsuranceLockDto);

    List<InsurancePracticeReportVo> getInsurancePracticeReportBypracticeId(@Param("practiceIds")List<Long> practiceIds,@Param("months") List<Integer> months);
    List<InsurancePracticeReportVo> getInsurancePracticeReportByOrderNos(@Param("orderNos")List<String> orderNos,@Param("months") List<Integer> months);


    List<InsurancePracticeReportVo> getIndividualFee(SearchReportDto lockInfoVo);

    void updateLockStatus(@Param("ids") List<Long> reportIds, @Param("status") Byte code,@Param("lockMonth") Integer reportMonth,@Param("updater")String creator);
    void updatePayLockStatus(@Param("ids") List<Long> reportIds, @Param("status") Byte code,@Param("lockMonth") Integer reportMonth,@Param("updater") String creator);

    List<InsurancePracticeReportVo> getCountByPakCodeListAndOrgCode(@Param("orgCode") String orgCode,@Param("packCodeList") List<String> packCodeList,@Param("lockMonth") Integer lockMonth);
    List<InsurancePracticeReportVo> getOrderNoListByPakCodeListAndOrgCode(@Param("orgCode") String orgCode,@Param("lockMonth") Integer lockMonth);

    List<OrderAndInsuranceDiffExportVo> getInsurancePracticeBillByOrgCodeAndMonth(@Param("vo") OrderAndInsuranceDiffParamVo paramVo);

    List<InsurancePracticeReportVo> getInsurancePracticeBillCountByLastMonth(@Param("lastMonth")Integer lastMonth);

}
