layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 验证数字正则表达式
    var number = '^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)|([0]))$'
    var list = [];
    // 页面初始化函数
    $(document).ready(function () {
        // 隐藏数据
        $(".ifShow").hide();
        var responsibleServiceInfo = [];
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getResponsibleService",
            dataType: 'json',
            success: function (data) {
                responsibleServiceInfo = [];
                responsibleServiceInfo = data.data;
                $.each(responsibleServiceInfo, function (i, item) {
                    $("#commissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
            }
        });

        // 页面加载时渲染空表格
        table.render({
            id: 'billGrid',
            elem: '#billGridTable',
            page: true,
            // url: '', // 空URL，不请求数据
            // method: 'POST',
            // where: {},
            data: [],
            height: 620,
            toolbar: "#toolbarDemo",
            defaultToolbar: [],
            limit: 50,
            limits: [50, 100, 200],
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {field: '', type: 'checkbox', align: 'center', fixed: 'left'},
                {field: 'orgName', title: '签单分公司', width: '120', align: 'center', fixed: 'left'},
                {field: 'custNo', title: '客户编号', width: '120', align: 'center', fixed: 'left'},
                {field: 'custName', title: '客户名称', width: '180', align: 'center', fixed: 'left'},
                {field: 'customerInvoiceName', title: '开票抬头', width: '250', align: 'center'},
                {
                    field: 'invoiceAmt', title: '发票金额', width: '150', align: 'center', templet: function (d) {
                        let text;
                        if (d.invoiceStatus != 6) {
                            text = "<a href='javascript:void(0);' style='text-decoration: underline;' lay-event='invoiceAmtEvent'>" + d.invoiceAmt + "</a>";
                        } else {
                            text = d.invoiceAmt;
                        }
                        return toRedData(d.invoiceStatus, text);
                    }
                },
                {
                    field: 'virtualRedStatus', title: '虚拟红冲状态', width: '120', align: 'center', templet: function (d) {
                        switch (d.virtualRedStatus) {
                            case 1:
                                return "未虚拟红冲";
                            case 2:
                                return "待虚拟红冲";
                            case 3:
                                return "虚拟红冲失败";
                            case 4:
                                return "已虚拟红冲";
                        }
                    }
                },
                {
                    field: 'billFlag', title: '是否重新生成账单', width: '120', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.billFlag);
                    }
                },
                {field: 'blueVDate', title: '蓝票提票日期', width: '170', align: 'center', sort: true},
                {field: 'blueIDate', title: '蓝票开票日期', width: '170', align: 'center', sort: true},
                {field: 'redVDate', title: '红票提票日期', width: '120', align: 'center', sort: true},
                {field: 'redIDate', title: '红票开票日期', width: '120', align: 'center', sort: true},
                {
                    field: 'invoiceStatus', title: '发票状态', width: '150', align: 'center', templet: function (d) {
                        let text = ML.dictFormatter("EXAMINE_INVOICE_STATUS", d.invoiceStatus);
                        return toRedData(d.invoiceStatus, text);
                    }
                },
                {field: 'remark', title: '开票备注', width: '120', align: 'center'},
                {field: 'invoiceRemark', title: '其他税收分类', width: '150', align: 'center'},
                {field: 'invoiceNo', title: '开票编号', width: '180', fixed: 'right'}
            ]],
            done: function (res, curr, count) {
                ML.hideNoAuth();

                // 监听表格行发票金额
                table.on('tool(billGridTableFilter)', function (obj) {
                    var layEvent = obj.event;  //获得lay-event数据
                    if (layEvent === 'invoiceAmtEvent') {
                        layer.open({
                            type: 2,
                            title: '查看开票明细',
                            area: ['70%', '85%'],
                            shade: 0,
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/customer/invoice/gotoInvoiceQueryDetailView?invoiceId=' + obj.data.id,
                        });
                    }
                })
            }
        });

        /*table.reload('billGrid', {
            data: [],
            page: { curr: 1 } // 重新从第 1 页开始
        });*/
    });

    // 查询按钮监听
    form.on('submit(btnQueryFilter)', function (data) {
        // 隐藏数据
        $(".ifShow").hide();

        // 校验日期字段
        if (!validateDates()) {
            return false;
        }

        table.reload('billGrid', {
            url: ML.contextPath + '/customer/invoice/getInvoiceByPage',
            method: 'POST',
            // where: JSON.stringify(serialize("searchForm")),
            where: data.field,
            page: { curr: 1 } // 重新从第 1 页开始
        });
        return false;
    });

    table.on("toolbar(billGridTableFilter)", function (obj) {
        let checkData = table.checkStatus(obj.config.id).data;

        //获得当前行数据
        const layEvent = obj.event;  //获得lay-event数据
        if (layEvent === 'abolishInvoice') {
            if (checkData.length === 0) {
                return layer.msg("请选择一行");
            }
            if (checkData.length > 1) {
                return layer.msg("不能同时废除多条");
            }
            let data = checkData[0];
            // 当蓝票已开票4或部分红票状态(8,9,10)才可进行废除
            if (data.invoiceStatus != 4 && data.invoiceStatus != 9 && data.invoiceStatus != 10 && data.invoiceStatus != 8) {
                layer.msg("只有蓝票已开票或部分红票状态才可进行废除!!");
                return false;
            }
            if (data.virtualRedStatus !== 1) {
                layer.msg("该开票正在进行虚拟红冲!!");
                return false;
            }
            ML.ajax(
                ML.contextPath + "/customer/invoice/abolishInvoice",
                {"invoiceId": data.id},
                function (result) {
                    layer.msg(result.msg);
                    table.reload('billGrid', {
                        page: {curr: 1} //重新从第 1 页开始
                    });
                },
                "POST")
        }
        if (layEvent === 'printInvoice') {
            if (checkData.length === 0) {
                return layer.msg("请选择一行");
            }
            let ids = [];
            for (let i = 0; i < checkData.length; i++) {
                const data = checkData[i];
                /*if (data.invoiceStatus == 6) {
                    return layer.msg("废除的发票不可查看开票申请单");
                }*/
                ids.push(data.id)
            }
            window.open(ML.contextPath + '/bill/invoice/export?ids=' + ids);
            // ML.asyncExport('/bill/invoice/export?ids=' + ids)
        }
        if (layEvent === 'selectInvoiceLog') {
            if (checkData.length === 0) {
                return layer.msg("请选择一行");
            }
            let ids = [];
            for (let i = 0; i < checkData.length; i++) {
                const data = checkData[i];
                ids.push(data.id)
            }
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                title: '查看开票明细',
                area: ['90%', '800px'],
                // shade: 0,
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ML.contextPath + '/customer/invoice/gotoInvoiceLogView?ids=' + ids + '&type=1',
            });
        }
        if (layEvent === 'uploadRedInvoice') {
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                title: '导入红票单号',
                area: ['600px', '350px'],
                // shade: 0,
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ML.contextPath + '/customer/invoice/gotoUpload',
            });
        }
        if (layEvent === 'prepareRedInvoice') {
            if (checkData.length === 0 || checkData.length > 1) {
                return layer.msg("请选择一行");
            }
            if (checkData[0].invoiceStatus != 4) {
                return layer.msg("只有蓝票已开票状态才可进行 准备虚拟红冲!!");
            }
            if (checkData[0].virtualRedStatus !== 1 && checkData[0].virtualRedStatus !== 4) {
                return layer.msg("该开票处于虚拟红冲状态!!");
            }
            // 添加确认提示框
            layer.confirm('确认要 准备虚拟红冲吗？', {
                btn: ['确认', '取消'] // 按钮
            }, function(){
                ML.ajax(
                    ML.contextPath + "/customer/invoice/prepareRedInvoice",
                    {"invoiceId": checkData[0].id},
                    function (result) {
                        layer.msg(result.msg);
                        table.reload('billGrid', {
                            page: {curr: 1} //重新从第 1 页开始
                        });
                    },
                    "POST"
                );
            });
        }
        // 取消虚拟废除
        if (layEvent === 'cancelPrepareRedInvoice') {
            if (checkData.length === 0 || checkData.length > 1) {
                return layer.msg("请选择一行");
            }
            if (checkData[0].invoiceStatus != 4) {
                return layer.msg("只有蓝票已开票状态才可进行 取消虚拟红冲!!");
            }
            if (checkData[0].virtualRedStatus !== 2 && checkData[0].virtualRedStatus !== 3) {
                return layer.msg("准备虚拟红冲 或 虚拟红冲失败 的发票才能取消虚拟红冲!!");
            }
            if (checkData[0].billFlag !== 1) {
                return layer.msg("已重新生成账单的开票不能取消虚拟红冲!!");
            }
            layer.confirm('确认要 取消虚拟红冲吗？', {
                btn: ['确定', '取消']
            }, function(){
                ML.ajax(
                    ML.contextPath + "/customer/invoice/cancelPrepareRedInvoice",
                    {"invoiceId": checkData[0].id},
                    function (result) {
                        layer.msg(result.msg);
                        table.reload('billGrid', {
                            page: {curr: 1} //重新从第 1 页开始
                        })
                    }
                )
            }
            );
        }
        if (layEvent === 'confirmRedInvoice') {
            if (checkData.length === 0 || checkData.length > 1) {
                return layer.msg("请选择一行");
            }
            if (checkData[0].invoiceStatus != 4) {
                return layer.msg("只有蓝票已开票状态才可进行 确认虚拟红冲!!");
            }
            if (checkData[0].virtualRedStatus !== 2 && checkData[0].virtualRedStatus !== 3) {
                return layer.msg("准备虚拟红冲 或 虚拟红冲失败 的发票才能确认虚拟红冲!!");
            }
            if (checkData[0].billFlag === 1) {
                return layer.msg("未重新生成账单的开票不能确认虚拟红冲!!");
            }

            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                title: '虚拟红冲数据核验',
                area: ['1300px', '800px'],
                // shade: 0,
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ML.contextPath + '/customer/invoice/confirmRedInvoicePage?invoiceId=' + checkData[0].id,
            });

        }
    });


    // 根据复选框选择渲染表格
    function handlerTableRander(writeOddTableData, incoicedTableData) {
        // 渲染应收表格
        table.render({
            id: 'writeOffGridTable',
            elem: '#writeOffGridTable',
            page: false,
            data: writeOddTableData,
            toolbar: false,
            defaultToolbar: [],
            limit: Number.MAX_VALUE,
            text: {
                none: '暂无数据' //无数据时展示
            },
            done: function (res, curr, count) {
                ML.hideNoAuth();
            },
            cols: [[
                {field: '', type: 'numbers', align: 'center', fixed: 'left'},
                {field: 'templetName', title: '客户帐套', width: '150', align: 'center', fixed: 'left'},
                {field: 'receivableMonth', title: '财务应收年月', width: '150', align: 'center', fixed: 'left'},
                {field: 'billMonth', title: '客户账单年月', width: '150', align: 'center'},
                {
                    field: 'receiveAmt', title: '应收金额', width: '150', align: 'center', templet: function (d) {
                        return "<a href='javascript:void(0);' style='text-decoration: underline;' lay-event='priceEvent'>" + d.receiveAmt + "</a>";
                    }
                },
                {
                    field: 'cancelStatus', title: '核销状态', width: '150', align: 'center', templet: function (d) {
                        return ML.dictFormatter('CANCEL_STATUS', d.cancelStatus)
                    }
                },
                {
                    field: 'invoiceStatus', title: '开票状态', width: '150', align: 'center', templet: function (d) {
                        return ML.dictFormatter('INVOICE_STATUS', d.invoiceStatus)
                    }
                }
            ]],
            done: function () {
                // 监听表格行应收金额
                table.on('tool(writeOffGridTableFilter)', function (obj) {
                    // 点击应收金额 ，打开应收详情页面
                    if (obj.event == 'priceEvent') {
                        var data = obj.data;
                        layer.open({
                            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                            title: '应收详情',
                            area: ['85%', '75%'],
                            shade: 0,
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/customer/invoice/invoiceReceivableDetail?custId=' + data.custId + '&templetId='
                                + data.templetId + '&billMonth=' + data.billMonth
                                + '&contractNo=' + data.contractNo
                                + '&billType=' + data.billType
                                + '&contractType=' + data.contractType,
                        });
                    }
                })
            }
        })

        // 渲染实收表格
        table.render({
            id: 'invoicedGridTable',
            elem: '#invoicedGridTable',
            page: false,
            data: incoicedTableData,
            toolbar: false,
            defaultToolbar: [],
            limit: Number.MAX_VALUE,
            text: {
                none: '暂无数据' //无数据时展示
            },
            done: function (res, curr, count) {
                ML.hideNoAuth();
            },
            cols: [[
                {field: '', type: 'numbers', align: 'center', fixed: 'left'},
                {field: 'checkAmt', title: '金额', width: '150', align: 'center', fixed: 'left'},
                {field: 'adjustAmt', title: '小额调整', width: '150', align: 'center', fixed: 'left'},
                {field: 'createTime', title: '核销日期', width: '150', align: 'center'},
                {field: 'formatPayTime', title: '到账日期', width: '150', align: 'center'},
                {field: 'payBank', title: '到款银行', width: '150', align: 'center'},
                {field: 'payAmt', title: '到账金额', width: '150', align: 'center'},
                {
                    field: 'payType', title: '支付类型', width: '150', align: 'center', templet: function (d) {
                        return ML.dictFormatter('PAY_METHOD', d.payType);
                    }
                },
                {field: 'payRemark', title: '到款备注', align: 'center'},
            ]]
        })
    }

    // 监听账单表格双击事件
    table.on('rowDouble(billGridTableFilter)', function (obj) {
        // 重载账单表格高度
        $(".ifShow").show();
        getReceivableAndNetReceipts(obj.data.id);
    })


    // 获取应收与实收数据
    function getReceivableAndNetReceipts(obj) {
        layer.load();
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/invoice/getReceivableAndNetReceipts?invoiceId=" + obj,
            dataType: 'json',
            success: function (data) {
                layer.closeAll('loading');
                if (data.code == 0) {
                    handlerTableRander(data.data.receivable, data.data.netReceipts);
                } else {
                    return layer.msg(data.msg);
                }
            },
            error: function (data) {
                layer.closeAll('loading');
                layer.msg(data);
                console.log("error")
            }
        });
    }

    // 渲染财务应收年月、开票日期
    lay('.receivableMonth').each(function () {
        laydate.render({
            elem: this
            , trigger: 'click'
            , min: '2010-01-01'
            , max: '2099-12-12'
            , theme: 'grid'
            , calendar: true
            , type: 'month'
            , format: 'yyyyMM'
        });
    });

    // 渲染账单月选择器
    var billMonth = laydate.render({
        elem: '#billMonth'
        , type: 'month'
        , theme: 'grid'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , trigger: 'click'
        , calendar: true
        , btns: ['clear', 'confirm']
        , format: 'yyyyMM'
        , done: function (value, date) {
            // 账单月选择完成后的回调
        }
    });

    lay('.receivableYears').each(function () {
        laydate.render({
            elem: this
            , trigger: 'click'
            , min: '2010-01-01'
            , max: '2099-12-12'
            , theme: 'grid'
            , calendar: true
            , format: 'yyyyMMdd'
        });
    });

    var startDate1 = laydate.render({
        elem: '.voucherDateS',
        type: 'datetime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate1.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds,
                };
            }
        }
    });

    var endDate1 = laydate.render({
        elem: '.voucherDateE',//选择器结束时间
        type: 'datetime',
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate1.config.max = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds,
                }
            }
        }
    });

    var startDate2 = laydate.render({
        elem: '.invoiceDateS',
        type: 'datetime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate2.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds,
                };
            }
        }
    });

    var endDate2 = laydate.render({
        elem: '.invoiceDateE',//选择器结束时间
        type: 'datetime',
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate2.config.max = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds,
                }
            }
        }
    });

    // 搜索条件  客户下拉列表框
    var appd1 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="groupName" placeholder="集团名称" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#groupName',
        checkedKey: 'id',
        appd: appd1,
        table: {
            url: '/customer/group/selectGroupByPage',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '集团ID', align: 'center'}
                , {field: 'groupName', title: '集团名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var custNo = '';
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.groupName)
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            // 将custId赋值到隐藏域中
            $("#groupId").val(id)
        }
    });

    // 自定义表单验证
    form.verify({
        receiveAmtCheck: function (value, item) { //value：表单的值、item：表单的DOM对象
            if (value && !value.match(number)) {
                return '只能为全数字！';
            }
        }
    });

    // 添加日期字段校验函数
    function validateDates() {
        const voucherDateS = $("#voucherDateS").val();
        const voucherDateE = $("#voucherDateE").val();
        const invoiceDateS = $("#invoiceDateS").val();
        const invoiceDateE = $("#invoiceDateE").val();
        const billMonth = $("#billMonth").val();
        const receivableMonthS = $("input[name='receivableMonthS']").val();
        const receivableMonthD = $("input[name='receivableMonthD']").val();

        // 检查是否有任意一项查询条件有值（提票日期、开票日期、账单月、财务应收月）
        if (!voucherDateS && !voucherDateE && !invoiceDateS && !invoiceDateE &&
            !billMonth && !receivableMonthS && !receivableMonthD) {
            layer.msg("请至少填写一项查询条件（提票日期、开票日期、账单月或财务应收月）");
            return false;
        }
        return true;
    }

    function toRedData(status, text) {
        if (status == 6) {
            return `<span style="color:red">${text}</span>`;
        } else if (status == 8) {
            return `<span style="color:orange">${text}</span>`;
        } else {
            return text;
        }
    }

    // 导出按钮监听
    form.on('submit(btnExportFilter)', function (data) {
        // 隐藏数据
        $(".ifShow").hide();

        // 校验日期字段
        if (!validateDates()) {
            return false;
        }

        let pass = checkVoucherDate($("#custId").val(), $("#voucherDateS").val(), $("#voucherDateE").val(), $("#invoiceDateS").val(), $("#invoiceDateE").val());
        if (pass) {
            window.location.href = ML.contextPath + "/customer/invoice/exportInvoiceByVo?vo=" + JSON.stringify(serialize("searchForm"));
        }
        return false;
    });

    // 监听表格输入框
    table.on('edit(billGridTableFilter)', function (obj) {
        // 监听发票编号修改
        if (obj.field == 'invoiceNo') {
            // console.log(obj)
        }
    })
})
