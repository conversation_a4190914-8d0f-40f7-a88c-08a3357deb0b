<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <title>薪资结果查询</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>
    <style>
        .layui-input-inline .layui-input{
            padding-right: 30px!important;
        }

        .layui-input-block .layui-input{
            padding-right: 30px!important;
        }
        .layui-form-label{
            width: 90px;
            padding: 9px 10px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body class="childrenBody">

<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">
            <div>

                <div class="layui-input-inline">
                    <label class="layui-form-label" title="客户编号">客户编号:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="custNo" maxlength="20" name="custNo" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>

                <div class="layui-input-inline">
                    <label class="layui-form-label" title="客户名称">客户名称:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="custName" maxlength="20" name="custName" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>

                <div class="layui-input-inline">
                    <label class="layui-form-label" title="合同编号:">合同编号:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="contractNo" maxlength="20" name="contractNo" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>

                <div class="layui-input-inline">
                    <label class="layui-form-label layui-elip" title="合同名称">合同名称:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="contractName" maxlength="20" name="contractName" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>
                <div class="layui-input-inline">
                    <label class="layui-form-label" title="雇员姓名">雇员姓名:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="employeeName" maxlength="20" name="employeeName" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>

            </div>

            <div>


                <div class="layui-input-inline">
                    <label class="layui-form-label" title="证件号码">证件号码:</label>
                    <div class="layui-input-inline">
                        <input type="text"  id="certNo" maxlength="20" name="certNo" placeholder="请输入" class="layui-input" autocomplete="off" onkeyup="value=value.replace(/^\s+|\s+$/g,'')">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="客户账单月起">客户账单月起:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" id="billMonthStart" name="billMonthStart" readonly >
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="客户账单月止">客户账单月止:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" name="billMonthEnd" id="billMonthEnd"   readonly >
                    </div>
                </div>



                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="工资计税月起">工资计税月起:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" id="taxMonthStart" name="taxMonthStart" readonly >
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="工资计税月止">工资计税月止:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" id="taxMonthEnd" name="taxMonthEnd"   readonly >
                    </div>
                </div>
            </div>


            
            <div>


                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="工资所属月起" >工资所属月起:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" id="salaryMonthStart" name="salaryMonthStart" readonly >
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip " title="工资所属月止">工资所属月止:</label>
                    <div class="layui-input-block">
                        <input type="text"  class="layui-input res" placeholder="请点击" id="salaryMonthEnd" name="salaryMonthEnd"   readonly >
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="派单方">派单方:</label>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input res" name="distComName" id="distComName" placeholder="请点击" readonly>
                        <input type="hidden" class="layui-input res" name="distCom" id="distCom">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="派单客服">派单客服:</label>
                    <div class="layui-input-block" id="distComManClick">
                        <select name="distComMan"  lay-search id="distComMan">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="扣缴义务人">扣缴义务人：</label>
                    <div class="layui-input-block">
                        <select class="layui-select" name="withholdingAgentNo" id="withholdingAgentNo" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
            </div>

            <div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="项目客服">项目客服:</label>
                    <div class="layui-input-inline">
                        <select name="commissioner" id="commissioner" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="薪资客服">薪资客服:</label>
                    <div class="layui-input-inline">
                        <select name="salaryCommissioner" id="salaryCommissioner" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="创建人">创建人:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="creator" maxlength="20" name="creator" placeholder="请输入"
                               class="layui-input layui-input-disposable" autocomplete="off" >
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="薪资发放状态">薪资发放状态:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="status" id="status" lay-search DICT_TYPE="SALARY_EMP_STATUS">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="工资支付日期">工资支付日期起:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="lastDateS" maxlength="20" name="lastDateS" placeholder="请选择"
                               class="layui-input layui-input-disposable" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="工资支付日期">工资支付日期止:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="lastDateE" maxlength="20" name="lastDateE" placeholder="请选择"
                               class="layui-input layui-input-disposable" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-input-inline">
                    <div class="layui-input-block" style="padding:10px;padding-bottom: 5px;margin-bottom: 0px;margin-left: 0px;left: 30px;">
                        <a class="layui-btn" id="btnQuery" data-type="reload" lay-filter="btnQuery" lay-submit="">检索</a>
                        <button class="layui-btn" id="reset" type="reset" >重置</button>
                    </div>
                </div>
            </div>

        </div>
    </form>
 <%--endQuery--%>
</blockquote>
<%--startTable--%>
<table class="layui-hide" id="salaryResultGrid" lay-filter="salaryResultGrid"></table>
<%--endTable--%>
<div id="salaryResultGridPage"></div>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="export" lay-event="export" authURI="/customer/salary/pay/exportSalaryResultList">导出</button>
</script>

<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/salaryPay/salaryResultQueryView.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/withholdingAgent/withholdingAgentNoSelect.js?v=${publishVersion}"></script>
</body>
</html>
