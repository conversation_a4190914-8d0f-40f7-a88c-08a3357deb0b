package com.reon.hr.api.base.dubbo.service.rpc.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.CompanyBankSearchVo;
import com.reon.hr.api.base.vo.CompanyBankVo;

import java.util.List;

public interface ICompanyBankWrapperService {
    Page<CompanyBankVo> selectCompBank(CompanyBankSearchVo vo);
    void  saveCompBank(CompanyBankVo vo);
    CompanyBankVo selectCompById(CompanyBankSearchVo vo);
    List<CompanyBankVo> getAllComp(String compNo,Integer type);
    List<CompanyBankVo> getAllComp();

    List<CompanyBankVo> getCompanyBanks(CompanyBankVo vo);

    void addSpecialCompanyBank(CompanyBankVo vo);

    void updateSpecialCompanyBankById(CompanyBankVo vo);

    CompanyBankVo getSpecialCompanyBankByCustId(Long custId);
}
