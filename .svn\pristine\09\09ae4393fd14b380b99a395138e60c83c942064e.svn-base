package com.reon.hr.common.cmb;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class QueryPayrollDetailInfoArgs implements Serializable {
    private String payeac; // 付款账户
    private String begdat; // 开始日期
    private String enddat; // 结束日期
    private String buscod; // 业务类型
    private String busmod; // 业务模式
    private String eacnam; // 收方户名
    private String ptyref; // 业务参考号
    private String minamt; // 最小金额
    private String maxamt; // 最大金额
    private String prtmod; // 打印模式
    private Integer begidx = 0; // 查询标记
    private String pagsiz; // 每批数量
    private String gwEnable = "true"; // 地址URL类型
    private String directFlag; // 地址URL类型
}
