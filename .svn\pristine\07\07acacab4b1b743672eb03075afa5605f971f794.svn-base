<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>批量更新账单模板</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input{
            padding-right: 30px;!important;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" title="转移名称" style="font-weight:800">转移名称</label>
                <div class="layui-input-inline">
                    <input type="text"  id="chgName" maxlength="20" name="chgName" placeholder="请输入" class="layui-input" autocomplete="off">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label layui-elip" title="旧小合同编号" style="font-weight:800">旧小合同编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="oldContractAreaNo" placeholder="请选择" autocomplete="off" class="layui-input" id="customer" lay-verType="tips">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="新小合同编号" style="font-weight:800">新小合同编号</label>
                <div class="layui-input-inline">
                    <input type="text"  class="layui-input" placeholder="请点击" name="newContractAreaNo" >
                </div>
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label layui-elip" title="转移生效月"
                       style="font-weight:800">转移生效月</label>
                <div class="layui-input-inline">
                    <input type="text" id="effectiveMonth" name="effectiveMonth" placeholder="请输入"
                           class="layui-input" autocomplete="off">
                </div>
            </div>

            <div class="layui-inline" width="98%">
                <div class="layui-input-block" style="padding:10px; padding-bottom: 5px; margin-bottom: 0px;">
                    <a class="layui-btn" id="btnQuery" data-type="reload" >检索</a>
                    <button class="layui-btn" id="reset" type="reset" >重置</button>
                </div>
            </div>
        </div>
    </form>
    <%--endQuery--%>
</blockquote>
<%--startTable--%>
<table id="ChangeTempleteInfoTable" lay-filter="ChangeTempleteInfoTable"></table>
<%--endTable--%>

<script type="text/jsp" id="btn">
    <a href="javascript:void(0)" title="查看" lay-event="check" ><i class="layui-icon layui-icon-search"></i></a>
</script>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="add" authURI="/customer/changeTempletInfo/gotoAddUpdateView" lay-event="add" >新增转移</button>
        <button class="layui-btn layui-btn-sm" id="check" authURI="/customer/changeTempletInfo/gotoCheckUpdateView" lay-event="check" >查看转移信息</button>
    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/employee/employeeTransfer/employeeTransferListPage.js?v=${publishVersion}"></script>
</body>
</html>
