package com.reon.hr.api.bill.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月04日
 * @Version 1.0
 */
@Data
public class InsurancePracticeDisComPayVo implements Serializable {



    private Long id;


    private Long payId;

    private String yurref;


    /**
     * 派单地
     */
    private String disCom;

    /**
     * 派单地审批岗位
     */
    private String disComApp;

    /**
     * 应支付金额
     */
    private BigDecimal payAmt;

    /**
     * 实际支付金额 抵扣金额减去申请金额
     */
    private BigDecimal actPayAmt;

    private BigDecimal balanceAmt;

    private BigDecimal serviceAmt;

    /**
     * 最晚支付日期
     */
    private String payDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    private Integer payFlag;

    private Integer onlineFlag;

    private Integer printType;

    private String payCom;


}
