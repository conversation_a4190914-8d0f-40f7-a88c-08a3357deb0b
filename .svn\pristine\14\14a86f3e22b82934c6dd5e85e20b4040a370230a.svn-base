package com.reon.hr.sp.customer.service.employee.salary.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.employee.EmployeeVo;
import com.reon.hr.api.customer.vo.salary.EmployeeHisVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeVo;
import com.reon.hr.sp.customer.entity.employee.Employee;

import java.util.List;
import java.util.Map;

public interface ISalaryEmployeeService {

    List<SalaryEmployeeVo> getSalaryEmployeeListPage(Page page,SalaryEmployeeVo salaryEmployeeVo);
    List<SalaryEmployeeVo> getNoSalaryEmployeeListPage(Page page,SalaryEmployeeVo salaryEmployeeVo);

    List<SalaryEmployeeVo> getSalaryEmployeeList(SalaryEmployeeVo salaryEmployeeVo);

    EmployeeVo getEmployeeByEmpNo(String empNo);

    int update(SalaryEmployeeVo salaryEmployeeVo);

    List<EmployeeHisVo> salaryEmployeeLogListPage(Page page,Long empId);

    SalaryEmployeeVo getSalaryEmployeeVo(String contractNo,String certNo, Integer certType, String empName);

    SalaryEmployeeVo getSalaryEmployeeVoByEmployeeId(Long employeeId);

    SalaryEmployeeVo getPureSalaryEmployeeVo(String contractNo,String certNo, Integer certType, String empName);

    List<SalaryEmployeeVo> getPayrollEmployeeListByCertNoAndName(String certNo, Integer certType, String empName);


    String getCertNo(String certNo, Long employeeId, Integer certType);
    void updateAcctNameByEmpId(Employee employee);
}
