<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.TemporaryPolicyMapper">

    <resultMap type="com.reon.hr.api.base.vo.TemporaryPolicyVo" id="TemporaryPolicyMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
        <result property="serviceSiteCode" column="service_site_code" jdbcType="VARCHAR"/>
        <result property="serviceSiteName" column="service_site_name" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="location" column="location" jdbcType="INTEGER"/>
        <result property="approvalStatus" column="approval_status" jdbcType="INTEGER"/>
        <result property="approvalOrg" column="approval_org" jdbcType="VARCHAR"/>
        <result property="approvalPos" column="approval_pos" jdbcType="VARCHAR"/>
        <result property="approvalPerson" column="approval_person" jdbcType="VARCHAR"/>
        <result property="approvalRemark" column="approval_remark" jdbcType="VARCHAR"/>
        <result property="approvalTime" column="approval_time" jdbcType="TIMESTAMP"/>
        <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TemporaryPolicyMap">
        select id,
               city_code,
               service_site_code,
               service_site_name,
               title,
               content,
               location,
               status,
               approval_status,
               approval_org,
               approval_pos,
               approval_person,
               approval_remark,
               approval_time,
               publish_time,
               creator,
               create_time,
               updater,
               update_time,
               del_flag
        from temporary_policy
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TemporaryPolicyMap">
        select
        id, city_code, service_site_code, service_site_name, publish_time, approval_remark, approval_time,
        title, content, status, approval_status, approval_org, approval_pos, location,
        approval_person, creator, create_time, updater, update_time, del_flag
        from temporary_policy
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="serviceSiteCode != null and serviceSiteCode != ''">
                and service_site_code = #{serviceSiteCode}
            </if>
            <if test="serviceSiteName != null and serviceSiteName != ''">
                and service_site_name = #{serviceSiteName}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <if test="approvalOrg != null and approvalOrg != ''">
                and approval_org = #{approvalOrg}
            </if>
            <if test="orgList != null and orgList.size() > 0">
                and approval_org in
                <foreach collection="orgList" item="orgCode" open="(" separator="," close=")">
                    #{orgCode}
                </foreach>
            </if>
            <if test="temporaryPolicyVos != null and temporaryPolicyVos.size() > 0">
                and
                <foreach collection="temporaryPolicyVos" item="item" open="(" separator="or" close=")">
                    (city_code = #{item.cityCode} and service_site_code = #{item.serviceSiteCode})
                </foreach>
            </if>
            <if test="approvalPos != null and approvalPos != ''">
                and approval_pos = #{approvalPos}
            </if>
            <if test="approvalPerson != null and approvalPerson != ''">
                and approval_person = #{approvalPerson}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="queryAllByLimitForRead" resultType="com.reon.hr.api.base.vo.TemporaryPolicyVo">
        select tp.id,
        tp.publish_time,
        tp.title,
        tp.content,
        tpr.read_at,
        if(tpr.id is null, 1, 2) read_flag
        from temporary_policy tp
        left join temporary_policy_read tpr on tp.id = tpr.temporary_policy_id and tpr.user_id = #{userId}
        <where>
            tp.status = 1
            and tp.approval_status = 2
            and tp.location = 2
            <if test="temporaryPolicyVos != null and temporaryPolicyVos.size() > 0">
                and
                <foreach collection="temporaryPolicyVos" item="item" open="(" separator="or" close=")">
                    (city_code = #{item.cityCode} and service_site_code = #{item.serviceSiteCode})
                </foreach>
            </if>
            <if test="type != null and type == 1">
                and tpr.id is null
            </if>
        </where>
        order by tp.publish_time desc
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from temporary_policy
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="serviceSiteCode != null and serviceSiteCode != ''">
                and service_site_code = #{serviceSiteCode}
            </if>
            <if test="serviceSiteName != null and serviceSiteName != ''">
                and service_site_name = #{serviceSiteName}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <if test="approvalOrg != null and approvalOrg != ''">
                and approval_org = #{approvalOrg}
            </if>
            <if test="approvalPos != null and approvalPos != ''">
                and approval_pos = #{approvalPos}
            </if>
            <if test="approvalPerson != null and approvalPerson != ''">
                and approval_person = #{approvalPerson}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into temporary_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cityCode != null">city_code,</if>
            <if test="serviceSiteCode != null">service_site_code,</if>
            <if test="serviceSiteName != null">service_site_name,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="location != null">location,</if>
            <if test="status != null">status,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="approvalOrg != null">approval_org,</if>
            <if test="approvalPos != null">approval_pos,</if>
            <if test="approvalPerson != null">approval_person,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cityCode != null">#{cityCode},</if>
            <if test="serviceSiteCode != null">#{serviceSiteCode},</if>
            <if test="serviceSiteName != null">#{serviceSiteName},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="location != null">#{location},</if>
            <if test="status != null">#{status},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="approvalOrg != null">#{approvalOrg},</if>
            <if test="approvalPos != null">#{approvalPos},</if>
            <if test="approvalPerson != null">#{approvalPerson},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into temporary_policy(city_code, service_site_code, service_site_name,
        title, content, status, approval_status,
        approval_org, approval_pos, approval_person, creator, create_time, updater, update_time, del_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.cityCode}, #{entity.serviceSiteCode}, #{entity.serviceSiteName},
            #{entity.title}, #{entity.content}, #{entity.status},
            #{entity.approvalStatus}, #{entity.approvalOrg}, #{entity.approvalPos}, #{entity.approvalPerson},
            #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.delFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into temporary_policy(city_code, service_site_code, service_site_name,
        title, content, status, approval_status,
        approval_org, approval_pos, approval_person, creator, create_time, updater, update_time, del_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.cityCode}, #{entity.serviceSiteCode}, #{entity.serviceSiteName},
            #{entity.title}, #{entity.content}, #{entity.status},
            #{entity.approvalStatus}, #{entity.approvalOrg}, #{entity.approvalPos}, #{entity.approvalPerson},
            #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.delFlag})
        </foreach>
        on duplicate key update
        city_code = values(city_code),
        service_site_code = values(service_site_code),
        service_site_name = values(service_site_name),
        title = values(title),
        content = values(content),
        status = values(status),
        approval_status = values(approval_status),
        approval_org = values(approval_org),
        approval_pos = values(approval_pos),
        approval_person = values(approval_person),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        del_flag = values(del_flag)
    </insert>
    <insert id="insertRead">
        insert into temporary_policy_read(temporary_policy_id, user_id)
        values (#{temporaryPolicyId}, #{userId})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update temporary_policy
        <set>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="serviceSiteCode != null">service_site_code = #{serviceSiteCode},</if>
            <if test="serviceSiteName != null">service_site_name = #{serviceSiteName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="location != null">location = #{location},</if>
            <if test="status != null">status = #{status},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="approvalOrg != null">approval_org = #{approvalOrg},</if>
            <if test="approvalPos != null">approval_pos = #{approvalPos},</if>
            <if test="approvalPerson != null">approval_person = #{approvalPerson},</if>
            <if test="approvalRemark != null">approval_remark = #{approvalRemark},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </set>
        where id = #{id}
    </update>

    <!--批量修改数据-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="entities" item="item" index="index" separator=";">
            update temporary_policy
            <set>
                <if test="item.cityCode != null">city_code = #{item.cityCode},</if>
                <if test="item.serviceSiteCode != null">service_site_code = #{item.serviceSiteCode},</if>
                <if test="item.serviceSiteName != null">service_site_name = #{item.serviceSiteName},</if>
                <if test="item.title != null">title = #{item.title},</if>
                <if test="item.content != null">content = #{item.content},</if>
                <if test="item.status != null">status = #{item.status},</if>
                <if test="item.approvalStatus != null">approval_status = #{item.approvalStatus},</if>
                <if test="item.approvalOrg != null">approval_org = #{item.approvalOrg},</if>
                <if test="item.approvalPos != null">approval_pos = #{item.approvalPos},</if>
                <if test="item.approvalPerson != null">approval_person = #{item.approvalPerson},</if>
                <if test="item.creator != null">creator = #{item.creator},</if>
                <if test="item.createTime != null">create_time = #{item.createTime},</if>
                <if test="item.updater != null">updater = #{item.updater},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.delFlag != null">del_flag = #{item.delFlag},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from temporary_policy
        where id = #{id}
    </delete>

</mapper>
