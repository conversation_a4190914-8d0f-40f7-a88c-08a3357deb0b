package com.reon.ehr.sp.sys.mapper.employee;

import com.reon.ehr.api.sys.vo.employee.EhrEmpRelativeFileVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EhrEmpRelativeFileMapper {
    int insertSelective(EhrEmpRelativeFileVo empRelativeFileVo);

    List<EhrEmpRelativeFileVo> getCertFileIdByOrderNo(String orderNo);

    int getCountByEmpId(Long ehrEmployeeId);

    List<EhrEmpRelativeFileVo> getEmpRelativeFileVoByEmpIdList(@Param("list") List<Long> empList);

    int deleteEhrEmpRelativeFile(EhrEmpRelativeFileVo empRelativeFileVo);
}
