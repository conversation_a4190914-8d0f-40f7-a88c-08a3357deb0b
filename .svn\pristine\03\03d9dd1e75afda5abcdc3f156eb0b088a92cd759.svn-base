var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    const laydate = layui.laydate, table = layui.table,        form = layui.form;
    layer = parent.layer === undefined ? layui.layer : parent.layer;


    function reloadTable(data) {
        table.reload('queryGridTable', {
            data: data
        });
    }

    table.render({
        id: 'queryGridTable',
        elem: '#queryGridTable'
        , data: []
        , height: '200'
        , page: false
        , defaultToolbar: []
        , contentType: 'application/json'
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {

        }, error: function (res, msg) {
            layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'orgName', title: '福利办理方', align: 'center', width: '230', fixed: 'left'}
            , {field: 'packName', title: '福利包名称', align: 'center', width: '230', fixed: 'left'}
            , {field: 'singleFlag', title: '是否是单立户', align: 'center', width: '150', templet: singleFlagFormat}
            , {
                field: 'pay_detail_type', title: '费用类型', width: '150', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PAY_DETAIL_TYPE", d.payDetailType);
                }
            }
            // , {field: 'normalAmt', title: '汇缴金额', width: '150', align: 'center'}
            // , {field: 'addAmt', title: '补缴金额', width: '150', align: 'center'}
            // , {field: 'frozenAmt', title: '支付中金额', width: '150', align: 'center'}
            // , {field: 'comPayAmt', title: '支付完成金额', width: '150', align: 'center'}
            , {field: 'payAmt', title: '费用金额', width: '150', align: 'center'}
            , {field: 'custName', title: '客户名称', width: '300', align: 'center'}
            , {field: 'lockMonth', title: '锁定年月', align: 'center', width: '150',}
            , {field: 'genStatusStr', title: '生成状态', align: 'center', width: '150',}
            , {
                field: 'lockMan', title: '锁定人', align: 'center', width: '180', templet: function (d) {
                    return ML.loginNameFormater(d.lockMan);
                }
            }
            , {field: 'lockStatusStr', title: '锁定状态', width: '150', align: 'center'}
            , {field: 'payStatusStr', title: '支付状态', width: '150', align: 'center'}
            , {field: 'lockTime', title: '锁定时间', width: '170', align: 'center'}
        ]]
    });




    function singleFlagFormat(d) {
        return d.singleFlag === 1 ? '否' : '是';
    }


    let lockInfoData;
    $(document).ready(function () {
        if($('#type').val() == 'check'){
            $('#lockStatus').val(2);
            $('#lockStatus').attr("readonly",true);
            form.render("select")
        }
        let ratioVo = $('#ratioVo').val();
        if (ML.isNotEmpty(ratioVo)) {
            let vo = JSON.parse(ratioVo);
            vo.forEach(function (item) {
                $("#insuranceRatioCode").append('<option class="insuranceRatioCode" value="' + item.insuranceRatioCode + '"  name="'+ item.ratioName+'">' + item.ratioName + '</option>');
            });
            form.render("select")
        }
        if ($('#reportData').val()) {
            let reportData = $('#reportData').val()
            let arr = JSON.parse(reportData);
            lockInfoData = arr[0]
            $('#lockMonth').val(lockInfoData.lockMonth);
            $('#packCode').val(lockInfoData.packCode);
            reloadTable(arr);
        }

    });


    table.render({
        id: 'feeGird',
        elem: '#feeGird'
        , data: []
        , url: "/bill/practiceReport/getIndividualFee"
        , where: serialize("searchForm")
        , height: '500'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , contentType: 'application/json'
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {

        }, error: function (res, msg) {
            layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'orderNo', title: '订单号', align: 'center', width: '230', fixed: 'left'}
            , {field: 'empName', title: '姓名', align: 'center', width: '230', fixed: 'left'}
            , {field: 'certNo', title: '证件号', align: 'center', width: '230', fixed: 'left'}
            , {field: 'lockStatusStr', title: '锁定状态', align: 'center', width: '230', fixed: 'left'}
            , {field: 'payStatusStr', title: '支付状态', align: 'center', width: '150',}
            // , {field: 'certNo', title: '是否是单立户', align: 'center', width: '150', templet: singleFlagFormat}
            , {
                field: 'feeType', title: '费用类型', width: '150', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PAY_DETAIL_TYPE", d.feeType);
                }
            }
            // totalAmt 不包含滞纳金的总金额
            , {field: 'totalAmt', title: '总金额(不含滞纳金)', width: '150', align: 'center'}
            , {field: 'comLateFee', title: '企业滞纳金', width: '300', align: 'center'}
            , {field: 'indLateFee', title: '个人滞纳金', align: 'center', width: '150'}
        ]]
    });

    function lockEventFun(data) {
        data.forEach(item =>{
            item.billId = lockInfoData.id;
        })
        $.ajax({
            url:ctx + "/bill/practiceReport/insertIndividualFeeLocks",
            type:'post',
            data: JSON.stringify(data),
            contentType: "application/json",
            success:function(result){
                if(result.code == 0){
                    table.reload('feeGird', {where:  serialize("searchForm")});
                }
                layer.msg(result.msg);
            },error: function (result) {
                layer.msg("错误!");
            }
        });
    }

    function unLockEventFun(data) {
        $.ajax({
            url:ctx + "/bill/practiceReport/deleteIndividualFeeLocks",
            type:'post',
            data:  JSON.stringify(data),
            contentType: "application/json",
            success:function(result){
                if(result.code == 0){
                    table.reload('feeGird', {where:  serialize("searchForm")});
                }
                layer.msg(result.msg);
            },error: function (result) {
                layer.msg("错误!");
            }
        });
    }

    function setParams(data){
        data.forEach(item =>{
            item['packCode'] = lockInfoData.packCode;
            item['orgCode'] = lockInfoData.orgCode;
            item['reportMonth'] = lockInfoData.lockMonth;
            item['reportId'] = item.ids;
            item['lockStatus'] = item.lockStatus;
        })
    }
    // 监听表格上方按钮
    table.on('toolbar(feeGirdFilter)', function (obj) {
        var checkStatus = table.checkStatus('feeGird')
            , data = checkStatus.data;
        if(data.length <= 0){
            return layer.msg("请选择费用数据！");
        }
        setParams(data)
        switch (obj.event) {
            case 'lockEvent':
                lockEventFun(data);
                break;
            case 'unLockEvent':
                unLockEventFun(data);
                break;
            default :
                break;
        }
    });


    $("#query").on("click",function () {
        table.reload('feeGird', {
            where:  serialize("searchForm")
        });
    });


});