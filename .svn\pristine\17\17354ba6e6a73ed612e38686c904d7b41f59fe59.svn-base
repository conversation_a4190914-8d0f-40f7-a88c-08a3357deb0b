package com.reon.hr.api.base.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
public enum UseStatusTypeEnum {
    NORMAL_USE(1, "正常使用"),
    DISUSE(2, "停止使用"),
    SPECIAL_USE(3, "特殊使用");

    private final Integer code;
    private final String name;

    UseStatusTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (UseStatusTypeEnum useStatusTypeEnum : UseStatusTypeEnum.values()) {
            if (Objects.equals(useStatusTypeEnum.getCode(), code)) {
                return useStatusTypeEnum.name;
            }
        }
        return null;
    }

    public static List<Integer> getCodeList(VisitSourceEnum visitSourceEnum) {
        switch (visitSourceEnum) {
            case REON:
                return Lists.newArrayList(NORMAL_USE.code, SPECIAL_USE.code);
            case EHR:
            default:
                return Lists.newArrayList(NORMAL_USE.code);
        }
    }
}
