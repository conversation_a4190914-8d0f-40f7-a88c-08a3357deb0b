<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }


        .detail {
            color: blue;
            background-color: #FFFFFF;
            border: 0px none;
            font-family: "宋体";
            font-size: 15px;
            text-decoration: underline;
        }

        .detail:hover {
            color: red;
            border: none;
            cursor: hand;
            cursor: pointer;
            text-decoration: underline;
        }

        .detail:focus {
            outline: none;
        }
    </style>
</head>
<body class="childrenBody">
<input type="hidden" id="selectedInseranceGroup"/>
<input type="text" class="layui-input" id="cityCode" name="cityCode" style="display: none;"/>
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm">
        <input type="hidden" name="adjustType" value="37    ">
        <table class="layui-table" lay-skin="nob" style="width: 75%">
            <tr>
                <td width="7%" align="right" style="font-weight:800">导入编号</td>
                <td width="8%">
                    <input type="text" id="importNo" name="importNo" placeholder="请输入" class="layui-input "
                           autocomplete="off">
                </td>
                <td width="5%" align="right" style="font-weight:800">姓名</td>
                <td width="8%">
                    <input type="text" id="creator" name="creator" placeholder="请输入" class="layui-input " autocomplete="off">
                </td>
                <td colspan="2" align="right">
                    <button class="layui-btn" type="button" id="query">查询</button>
                    <button class="layui-btn" type="button" id="reset">重置</button>
                </td>
            </tr>
        </table>
    </form>
</blockquote>

<table class="layui-hide" id="batchImportGrid" lay-filter="batchImportGridFilter"></table>
<script type="text/jsp" id="btn">
    <a href="javascript:void(0)" title="查看" lay-event="query"
       authURI="/customer/batchImport/gotoBatchImportedDataHistoryView"><i class="layui-icon layui-icon-search"></i></a>



</script>

<script type="text/jsp" id="detail">
    <button class="detail" adjustId = {{ d.id }} lay-event="detail" onclick="detail({{ d.id }})">详细</button>


</script>

<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container" id="buttonDiv">
        <button class="layui-btn layui-btn-sm" id="exportExcelTemplate" lay-event="download"
                    authURI="/customer/batchAdjust/exportExcelTemplate">下载模板
        </button>
        <button class="layui-btn layui-btn-sm" id="addAdjust" lay-event="import"
                authURI="/customer/batchAdjust/gotoImportDataPage">导入
        </button>

    </div>


</script>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/insurancePractice/societyInsurance/batchInsurancePracticeAdjust/batchInsurancePracticeAdjust.js?v=${publishVersion}"></script>
</body>
</html>
