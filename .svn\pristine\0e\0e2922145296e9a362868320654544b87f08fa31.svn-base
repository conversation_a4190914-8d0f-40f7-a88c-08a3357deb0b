package com.reon.hr.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * BigDecimal 工具类
 *
 * <AUTHOR>
 */
public abstract class BigDecimalUtil {
    /**
     * 数字金额格式
     */
    private static final DecimalFormat AMOUNT_FORMAT = new DecimalFormat("#,##0.00");


    public static boolean equalsVal(double val, BigDecimal compareVal) {
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal) == 0;
    }

    public static boolean greaterEqualVal(double val, BigDecimal compareVal) {
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal) > -1;
    }

    public static boolean lessEqualVal(double val, BigDecimal compareVal) {
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal) < 1;
    }

    public static BigDecimal max(BigDecimal b1, BigDecimal b2) {
        if (b1 == null) {
            return b2;
        }
        if (b2 == null) {
            return b1;
        }
        return b1.compareTo(b2) > 0 ? b1 : b2;
    }
    public static String getTwoDecimalPlacesStr(BigDecimal val) {
        return val.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    public static String getTwoDecimalPlacesAmountFormatStr(BigDecimal val) {
        return AMOUNT_FORMAT.format(val);
    }}
