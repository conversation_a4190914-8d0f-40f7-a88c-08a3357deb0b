package com.reon.hr.modules.report.controller;

import com.reon.hr.api.report.dubbo.service.rpc.IncomeCountTableWrapperService;
import com.reon.hr.api.report.utils.ExcelUtil;
import com.reon.hr.api.report.vo.MonthlyIncomeReportVo;
import com.reon.hr.core.annotation.RepeatSubmit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收入月度报表控制层
 *
 * <AUTHOR>
 * @date 2023/01/03
 */
@RequestMapping("/monthlyIncomeReport/")
@RestController
public class MonthlyIncomeReportController {
    private static final Logger log = LoggerFactory.getLogger(MonthlyIncomeReportController.class);

    @Resource
    IncomeCountTableWrapperService incomeCountTableWrapperService;

    @RequestMapping("gotoMonthlyIncomeReportPage")
    public ModelAndView gotoIncomeCountTableListPage() {
        return new ModelAndView("report/monthlyIncomeReportPage");
    }

    @RequestMapping(value = "exportFile", method = RequestMethod.GET)
    @RepeatSubmit
    public void getBillContractExportFile(HttpServletResponse response, @RequestParam("reportMonth") String reportMonth, @RequestParam(value = "signCom",required = false) String signCom, @RequestParam(value = "signComTitle",required = false) String signComTitle) {
        String fileName;
        MonthlyIncomeReportVo vo = new MonthlyIncomeReportVo();
        vo.setReportMonth(reportMonth);
        vo.setCompany(StringUtils.isNotBlank(signComTitle) ? signComTitle : null);
        vo.setSignCom(StringUtils.isNotBlank(signCom) ? signCom : null);
        try {
            vo.setCreateTimeFore(vo.getReportMonth());
            List<MonthlyIncomeReportVo> costReport = incomeCountTableWrapperService.getMonthlyIncomeFromReport(vo);
            fileName = vo.getReportMonth() + "_" + "收入月度报表";
            SXSSFWorkbook sheets = ExcelUtil.setWorkbook(costReport, MonthlyIncomeReportVo.class, fileName);
            ExcelUtil.closeInfo(response, sheets, fileName);
            if (CollectionUtils.isEmpty(costReport)) {
                log.info("数据为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("导出错误" + e.getMessage());
        }
    }
}
