package com.reon.hr.api.bill.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2022/7/28.
 */
@Data
public class OrgPositionDto implements Serializable {
    private String orgCode;

    private String posCode;

    private String loginName;//登陆用户

    private String relativePosCode;

    private Integer posKind;
    private Integer posType;  // '岗位类型（1:销售，2:项目客服，3：财务，4：法务，5:接单客服，6:供应商客服，7：人事行政 12:总裁办,9: 商保,11: 签章）'
}
