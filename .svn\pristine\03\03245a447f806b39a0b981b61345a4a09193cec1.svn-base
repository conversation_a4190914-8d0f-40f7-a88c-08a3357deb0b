layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
  var table = layui.table,
      $ = layui.$,
      form = layui.form,
      layer = layui.layer,
      layer = parent.layer === undefined ? layui.layer : parent.layer;

  // 取消按钮
  $(document).on('click', '#cancel', function () {
    layer.closeAll('iframe');
  });
  setTimeout(function () {
    form.render('select');
  }, 100);
  // 保存按钮
  form.on("submit(save)", function (data) {
    saveForm('save', data);
    return false;
  });


  // 保存数据方法
  function saveForm(type, obj) {
    if (obj.field.name === "") {
      layer.msg("雇员姓名不能为空");
      return false;
    }
    if (ML.isNotEmpty(obj.field.mobile)) {
      var phone = /^(0|86|17951)?(11[0-9]|12[0-9]|13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])[0-9]{8}$/;
      if (!(phone.test(obj.field.mobile))) {
        layer.msg("手机号格式不正确!");
        return false;
      }
    }


    $.ajax({
      url: ML.contextPath + "/customer/commInsurOrder/updateEmployeeNameOrMobileById",
      type: 'POST',
      dataType: 'json',
      contentType: 'application/json',
      data: JSON.stringify(obj.field),
      success: function (result) {
        layer.msg(result.msg);
        if (result.code==0) {
          layer.closeAll('iframe');//关闭弹窗
        }
      },
      error: function () {
        layer.alert('系统发生异常，请重试！');
      }
    });
  }


});