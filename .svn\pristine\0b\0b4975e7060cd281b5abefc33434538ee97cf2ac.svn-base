package com.reon.hr.sp.customer.service.ImportService;

import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.vo.batchImport.OneTimeChargeImportVo;

/**
 * <AUTHOR>
 * @Version: 1.0
 */
public interface BatchAddOneTimeChargeImportService {

    /**
     * 批量导入一次性
     * @param oneTimeChargeImport
     */
    void handleBatchAddOneTimeCharges(ImportDataDto<OneTimeChargeImportVo> oneTimeChargeImport);
}
