package com.reon.ehr.api.sys.enums.order;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR> on 2023/7/4.
 */
public enum StaffingState {
    TO_BE_SUBMITTED(1, "待提交"),
    SUBMITTED(2, "已提交"),
    CANCELLATION_OF_ADDITIONAL_PERSONNEL(3, "取消增员"),
    THE_INCREASE_HAS_BEEN_ACCEPTED(4, "增员已受理"),
    REDUCTION_SUBMIT(5, "减员提交"),
    REDUCTION_CANCEL(6, "减员取消"),
    REDUCTION_HANDLE(7, "减员受理中"),
    REDUCTION_PROCESSED(8, "减员已受理"),
    INCREASE_REJECT(9, "增员已驳回")
    ;
    private final Integer code;
    private final String info;

    StaffingState(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
    public static StaffingState getStaffingStateByCode(Integer code){
        for (StaffingState staffingState:StaffingState.values()) {
            if(staffingState.getCode().equals(code)){
                return staffingState;
            }
        }
        return null;
    }
    public static String getInfoByCode(Integer code){
        for (StaffingState staffingState:StaffingState.values()) {
            if(staffingState.getCode().equals(code)){
                return staffingState.getInfo();
            }
        }
        return null;
    }
    public static final List<Integer> REDUCE_LIST = Lists.newArrayList(REDUCTION_SUBMIT.getCode(),REDUCTION_HANDLE.getCode(),REDUCTION_PROCESSED.getCode());
    public static final List<Integer> CAN_REDUCE_LIST = Lists.newArrayList(THE_INCREASE_HAS_BEEN_ACCEPTED.getCode(),REDUCTION_CANCEL.getCode());
}
