package com.reon.hr.api.customer.vo;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MeetingRecordVo implements Serializable {
    private static final long serialVersionUID = -219216707808225166L;
    /**
     * 主键ID
     */
     @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合同编号
     */
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 会议时间
     */
    @TableField(value = "meeting_time")
    private Date meetingTime;

    /**
     * 会议主题
     */
    @TableField(value = "subject")
    private String subject;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 附件ID
     */
    @TableField(value = "file_id")
    private String fileId;

    private String creator;
    private Date createTime;


}
