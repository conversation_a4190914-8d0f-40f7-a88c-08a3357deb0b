/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/10
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ServiceSiteCfgImportVo implements Serializable {

    @ExcelProperty(index =0)
    private String cityName;

    @ExcelProperty(index = 1)
    private String bigAccountArea;

    @ExcelProperty(index = 2)
    private String singleAccountCounty;

    @ExcelProperty(index = 3)
    private String applyInsurFreqName;
    private Integer applyInsurFreq;

    @ExcelProperty(index = 4)
    private String applyFundFreqName;
    private Integer applyFundFreq;

    @ExcelProperty(index = 5)
    private String billFeeRuleName;
    private Integer billFeeRule;

    //人员类型在表中的string字段
    @ExcelProperty(index = 6)
    private String categoryCodeName;
    private String categoryCode;

    @ExcelProperty(index = 7)
    private Short insurAddDay;
    @ExcelProperty(index = 8)
    private Short insurSubDay;
    @ExcelProperty(index = 9)
    private Short crfAddDay;
    @ExcelProperty(index = 10)
    private Short crfSubDay;

    @ExcelProperty(index = 11)
    private String addCrfFlagName;
    private int addCrfFlag;

    @ExcelProperty(index = 12)
    private String insurRemitFlagName;
    private int insurRemitFlag;

    @ExcelProperty(index = 13)
    private String crfRemitFlagName;
    private int crfRemitFlag;

    @ExcelProperty(index = 14)
    private String addInfo;

    @ExcelProperty(index = 15)
    private String downInfo;

    @ExcelProperty(index = 16)
    private Integer additionFlag;

    @ExcelProperty(index = 17)
    private String additionStartMonth;

    @ExcelProperty(index = 18)
    private String additionRule;

    private Long id;

    private Integer districtCode;

    private Integer cityCode;

    private String serviceSiteCode;

    private Integer orgType;

    private String serviceSiteName;

    private String appendInfo;

    private Date createTime;

    private String creator;

    private Date updateTime;

    private String updater;

    private String delFlag;
    private int peopleIndType;//人员类型
}
