package com.reon.hr.api.customer.dubbo.service.rpc.customer;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.MonthServiceReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/2/23 0023 上午 10:45
 * @Version 1.0
 */
public interface MonthServiceReportWrapperService {

    int insertMonthServiceReportVo(MonthServiceReportVo monthServiceReportVo);
    MonthServiceReportVo getMonthServiceReportVoByCustIdAndMonth(MonthServiceReportVo monthServiceReportVo);

    Page<MonthServiceReportVo> getMonthServiceReportList(Integer page,Integer limit, Integer serviceMonth, List<OrgPositionDto> userOrgPositionDtoList,Long custId);
    MonthServiceReportVo getMonthServiceReportById(Long id);

    boolean updateFileIdById(Long id);

    int updateMonthServiceReportById(MonthServiceReportVo monthServiceReportVo);
}
