package com.reon.hr.sp.base.service.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.SscUpdateLogVo;

import java.util.List;

/**
 * (SscUpdateLog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-20 10:04:22
 */
public interface SscUpdateLogService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SscUpdateLogVo queryById(Long id);

    List<SscUpdateLogVo> queryBySscId(Long sscId);

    /**
     * 分页查询
     *
     * @param sscUpdateLogVo 筛选条件
     * @param page                页面
     * @param limit               限制
     * @return 查询结果
     */
    Page<SscUpdateLogVo> queryByPage(Integer page, Integer limit, SscUpdateLogVo sscUpdateLogVo);

    /**
     * 新增数据
     *
     * @param sscUpdateLogVo 实例对象
     * @return 影响行数
     */
    SscUpdateLogVo insert(SscUpdateLogVo sscUpdateLogVo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<com.reon.hr.api.base.vo.SscUpdateLogVo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(List<SscUpdateLogVo> entities);

    /**
     * 修改数据
     *
     * @param sscUpdateLogVo 实例对象
     * @return 影响行数
     */
    SscUpdateLogVo update(SscUpdateLogVo sscUpdateLogVo);
    
    /**
     * 批量修改数据（MyBatis原生foreach方法）
     *
     * @param entities List<com.reon.hr.api.base.vo.SscUpdateLogVo> 实例对象列表
     * @return 影响行数
     */
    int updateBatch(List<SscUpdateLogVo> entities);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

}
