package com.reon.hr.sp.bill.rabbitmq.listener;

import com.reon.hr.api.bill.constant.BillEnum;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ConsumerScopeTypeBill;
import com.reon.hr.sp.bill.dao.supplierPractice.SupplierPracticeBillMapper;
import com.reon.hr.sp.bill.entity.supplierPractice.SupplierPracticeBill;
import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.context.MqContext;

import com.reon.hr.sp.bill.service.bill.supplierPractice.ISupplierPracticeBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.bill.rabbitmq.listener
 * @ClassName: SupplierPracticeLockGenerateListener
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/5 13:07
 * @Version: 1.0
 */
@Component
public class SupplierPracticeLockGenerateListener extends AbstractConsumerListener {
    private final static String SUPPLIER_PRACTICE_LOCK_GENERATE_QUEUE = "supplier.practice.lock.generate.queue";

    private final static Logger logger = LoggerFactory.getLogger(SupplierPracticeLockGenerateListener.class);

    @Autowired
    private MqContext mqContext;

    @Autowired
    private ISupplierPracticeBillService supplierPracticeBillService;
    @Autowired
    private SupplierPracticeBillMapper supplierPracticeBillMapper;

    @Override
    protected void doWork(String message)  {
        logger.info("供应商报表id ==>,{}",message);
        String id = JsonUtil.jsonToBean(message, String.class);
        try {
            supplierPracticeBillService.handleGenSupplierBill(Long.valueOf(id));
        } catch (Exception e) {
            e.printStackTrace();
            SupplierPracticeBill bill = supplierPracticeBillMapper.selectById(id);
            bill.setBillAmt(BigDecimal.ZERO).setServiceFee(BigDecimal.ZERO).setServiceNum(BigDecimal.ZERO.intValue())
                    .setGenStatus(BillEnum.GenerateBillStatus.FAILED.getCode()).setUpdateTime(new Date());
            supplierPracticeBillMapper.updateById(bill);
            throw new RuntimeException(e);
        }

    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        super.init(ModuleType.REON_BILL, ConsumerScopeTypeBill.REON_SUPPLIER_PRACTICE_LOCK_GENERATE_COMPLETED,mqContext,SUPPLIER_PRACTICE_LOCK_GENERATE_QUEUE);
    }
}
