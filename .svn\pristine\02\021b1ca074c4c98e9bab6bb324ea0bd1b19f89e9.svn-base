package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.ComAcctInfoVo;
import com.reon.hr.api.customer.vo.CusContractBillVo;

import java.util.Date;
import java.util.List;

public interface ComAcctInfoService {
    Page<ComAcctInfoVo> getListPage(Integer page, Integer limit, String ParamDate);

    Boolean addComAcctInfo(ComAcctInfoVo comAcctInfoVo);

    boolean editComAcctInfo(ComAcctInfoVo comAcctInfoVo);

    Boolean deleteById(Long id);

    ComAcctInfoVo getComAcctInfo(String groupCode, String orgCode, Byte acctType);
    ComAcctInfoVo getComAcctInfoById(long id);

    ComAcctInfoVo getComAcctInfoByOrgCodeAndGroupCode(String orgCode, String groupCode);

    Boolean updateDelFlagById(Long id, String updater, Date updaterTime);
}
