<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.ContractAssignMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.cus.ContractAssign">
        <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="assign_type" jdbcType="INTEGER" property="assignType"/>
        <result column="relative_no" jdbcType="VARCHAR" property="relativeNo"/>
        <result column="cs_type" jdbcType="INTEGER" property="csType"/>
        <result column="commissioner" jdbcType="VARCHAR" property="commissioner"/>
        <result column="valid_date" jdbcType="DATE" property="validDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        id, assign_type, relative_no, cs_type, commissioner, valid_date, `status`, creator,
        create_time, updater, update_time, del_flag
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        update contract_assign
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="assign_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.assignType,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="relative_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.relativeNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cs_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.csType,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="commissioner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.commissioner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="valid_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.validDate,jdbcType=DATE}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="del_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        insert into contract_assign
        (assign_type, relative_no, cs_type, commissioner, valid_date, `status`, creator,
        create_time, updater, update_time, del_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.assignType,jdbcType=INTEGER}, #{item.relativeNo,jdbcType=VARCHAR}, #{item.csType,jdbcType=INTEGER},
            #{item.commissioner,jdbcType=VARCHAR}, #{item.validDate,jdbcType=DATE}, #{item.status,jdbcType=INTEGER},
            #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.delFlag,jdbcType=CHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.reon.hr.sp.customer.entity.cus.ContractAssign" useGeneratedKeys="true">
    <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        insert into contract_assign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            assign_type,
            relative_no,
            cs_type,
            commissioner,
            valid_date,
            `status`,
            creator,
            create_time,
            updater,
            update_time,
            del_flag,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{assignType,jdbcType=INTEGER},
            #{relativeNo,jdbcType=VARCHAR},
            #{csType,jdbcType=INTEGER},
            #{commissioner,jdbcType=VARCHAR},
            #{validDate,jdbcType=DATE},
            #{status,jdbcType=INTEGER},
            #{creator,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updater,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{delFlag,jdbcType=CHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            assign_type = #{assignType,jdbcType=INTEGER},
            relative_no = #{relativeNo,jdbcType=VARCHAR},
            cs_type = #{csType,jdbcType=INTEGER},
            commissioner = #{commissioner,jdbcType=VARCHAR},
            valid_date = #{validDate,jdbcType=DATE},
            `status` = #{status,jdbcType=INTEGER},
            creator = #{creator,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater = #{updater,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            del_flag = #{delFlag,jdbcType=CHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.reon.hr.sp.customer.entity.cus.ContractAssign" useGeneratedKeys="true">
        <!--@mbg.generated generated on Mon Jun 03 11:38:38 CST 2019.-->
        insert into contract_assign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="assignType != null">
                assign_type,
            </if>
            <if test="relativeNo != null">
                relative_no,
            </if>
            <if test="csType != null">
                cs_type,
            </if>
            <if test="commissioner != null">
                commissioner,
            </if>
            <if test="validDate != null">
                valid_date,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="posCode != null">
                pos_code,
            </if>
            <if test="laterPosCode != null">
                later_pos_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="assignType != null">
                #{assignType,jdbcType=INTEGER},
            </if>
            <if test="relativeNo != null">
                #{relativeNo,jdbcType=VARCHAR},
            </if>
            <if test="csType != null">
                #{csType,jdbcType=INTEGER},
            </if>
            <if test="commissioner != null">
                #{commissioner,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null">
                #{validDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="posCode != null">
                #{posCode,jdbcType=VARCHAR},
            </if>
            <if test="laterPosCode != null">
                #{laterPosCode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="assignType != null">
                assign_type = #{assignType,jdbcType=INTEGER},
            </if>
            <if test="relativeNo != null">
                relative_no = #{relativeNo,jdbcType=VARCHAR},
            </if>
            <if test="csType != null">
                cs_type = #{csType,jdbcType=INTEGER},
            </if>
            <if test="commissioner != null">
                commissioner = #{commissioner,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null">
                valid_date = #{validDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="posCode != null">
                pos_code = #{posCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getContractPageList" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT
        c.contract_name,
        c.contract_no,
        c.start_date,
        c.end_date,
        c.dist_com,
        cas.commissioner disCommissioner,
        c.contract_type,
        c.salary_commissioner,

        c.contract_no relative_no,
        cus.cust_name,
        cus.cust_no,
        cas.valid_date,
        cas.cs_type,
        c.status contractStatus

        FROM
        contract c LEFT JOIN contract_process cp ON
        c.contract_no= cp.contract_no
        LEFT JOIN customer cus ON cus.id = c.cust_id
        LEFT JOIN contract_assign cas ON cas.relative_no = c.contract_no and (cas.cs_type=1 or cas.cs_type=3)

        <where>
            and ((c.associated_flag = 1 and cp.proc_type = 'add') or (c.associated_flag = 2 and c.distributable_flag = 2 ))
            and cp.current_flag=1 and cp.approval_status=4
            and c.status != 2
            <if test="contractAssign.signCom != null and contractAssign.signCom != ''">
                and c.sign_com like concat('%', #{contractAssign.signCom,jdbcType=VARCHAR} ,'%')
            </if>
            <if test="contractAssign.custName != null and contractAssign.custName != ''">
                and cus.cust_name like concat('%', #{contractAssign.custName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.disCommissioner != null and contractAssign.disCommissioner != ''">
                and c.commissioner like concat('%', #{contractAssign.disCommissioner,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distributionFlag != null">
                and c.dispatch_flag=#{contractAssign.distributionFlag}
            </if>
            <if test="contractAssign.salaryDispatchFlag != null">
                and c.salary_dispatch_flag=#{contractAssign.salaryDispatchFlag}
            </if>
            <if test="contractAssign.contractName != null and contractAssign.contractName != ''">
                and c.contract_name like concat('%', #{contractAssign.contractName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distCom != null and contractAssign.distCom != ''">
                and c.dist_com like concat('%', #{contractAssign.distCom,jdbcType=VARCHAR} ,'%')
            </if>
            <if test="contractAssign.userOrgPositionDtoList != null and contractAssign.userOrgPositionDtoList.size > 0">
                and
                <foreach collection="contractAssign.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or" close=")">
                    (
                    (
                    ( c.seller_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.seller_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                    )
                    <if test="userOrgPositionDto.loginName != null">
                        and (
                        ( c.seller = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                        )
                    </if>
                    )
                </foreach>
            </if>

            <if test="contractAssign.contractTypeList.size>0">
                and c.contract_type in
        <foreach collection="contractAssign.contractTypeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
            </if>
            and cp.current_flag=1 and cp.approval_status=4
        </where>
        <if test="contractAssign.distributionFlag==null">
            ORDER BY c.create_time DESC
        </if>
        <if test="contractAssign.distributionFlag==0">
            ORDER BY cp.pass_time DESC
        </if>
        <if test="contractAssign.distributionFlag==1">
            ORDER BY cas.valid_date DESC
        </if>

    </select>

    <select id="getContractAreaPageList" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT
        c.contract_name,
        c.contract_no,
        c.start_date,
        c.end_date,
        c.dist_com,
        c.commissioner disCommissioner,
        c.contract_type,
        c.salary_commissioner,
        cus.cust_name,
        cus.cust_no,
        ca.`name`,
        ca.contract_area_no relative_no,
        ca.receiving_man,
        ca.receiving,
        ca.later_man,
        cas.valid_date,
        cas.cs_type,
        ca.account_flag,
        ca.attachment_id
        FROM
        contract c LEFT JOIN contract_process cp ON c.contract_no= cp.contract_no
        LEFT JOIN customer cus ON cus.id = c.cust_id
        LEFT JOIN contract_area ca ON c.contract_no = ca.contract_no
        LEFT JOIN contract_assign cas ON cas.relative_no = ca.contract_area_no
        <where>
            <if test="contractAssign.signCom != null and contractAssign.signCom != ''">
                and ca.receiving = #{contractAssign.signCom,jdbcType=VARCHAR}
            </if>
            <if test="contractAssign.custName != null and contractAssign.custName != ''">
                and cus.cust_name like concat('%', #{contractAssign.custName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.disCommissioner != null and contractAssign.disCommissioner != ''">
                and ca.receiving_man like concat('%', #{contractAssign.disCommissioner,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distributionFlag != null">
                and ca.dispatch_flag=#{contractAssign.distributionFlag}
            </if>
            <if test="contractAssign.laterDisFlag != null">
                and ca.later_dis_flag=#{contractAssign.laterDisFlag}
            </if>
            <if test="contractAssign.contractName != null and contractAssign.contractName != ''">
                and c.contract_name like concat('%', #{contractAssign.contractName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distCom != null and contractAssign.distCom != ''">
                and c.dist_com = #{contractAssign.distCom,jdbcType=VARCHAR}
            </if>
            <if test="contractAssign.name != null and contractAssign.name != ''">
                and ca.`name` like concat('%', #{contractAssign.name,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.accountFlag != null">
                and ca.account_flag=#{contractAssign.accountFlag}
            </if>
            and ca.contract_area_no != ''  and ((c.associated_flag = 1 and cp.proc_type = 'add') or (c.associated_flag = 2 and c.distributable_flag = 2 ))
            and cp.current_flag=1 and cp.approval_status=4
            and ca.recceiving_type = 0 and ca.del_flag = 'N' and c.del_flag = 'N'

            <if test="contractAssign.userOrgPositionDtoList != null and contractAssign.userOrgPositionDtoList.size > 0">
                and
                <foreach collection="contractAssign.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or" close=")">

                    ca.receiving like concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')

                </foreach>
            </if>

        </where>


        ORDER BY ca.create_time DESC
    </select>

    <select id="getContractSalaryPageList" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT
        c.contract_name,
        c.contract_no,
        c.start_date,
        c.end_date,
        c.dist_com,
        cas.commissioner salaryCommissioner,
        c.contract_type,
        c.salary_commissioner as contractSalaryCommissioner,

        c.contract_no relative_no,
        cus.cust_name,
        cus.cust_no,
        cas.valid_date,
        cas.cs_type

        FROM
        contract c LEFT JOIN contract_process cp ON
        c.contract_no= cp.contract_no
        LEFT JOIN customer cus ON cus.id = c.cust_id
        LEFT JOIN contract_assign cas ON cas.relative_no = c.contract_no and cas.cs_type=5

        <where>
            and ((c.associated_flag = 1 and cp.proc_type = 'add') or (c.associated_flag = 2 and c.distributable_flag = 2 ))
            and cp.current_flag=1 and cp.approval_status=4
            <if test="contractAssign.signCom != null and contractAssign.signCom != ''">
                and c.sign_com like concat('%', #{contractAssign.signCom,jdbcType=VARCHAR} ,'%')
            </if>
            <if test="contractAssign.custName != null and contractAssign.custName != ''">
                and cus.cust_name like concat('%', #{contractAssign.custName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.salaryCommissioner != null and contractAssign.salaryCommissioner != ''">
                and c.salary_commissioner like concat('%', #{contractAssign.salaryCommissioner,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distributionFlag != null">
                and c.salary_dispatch_flag=#{contractAssign.distributionFlag}
            </if>
            <if test="contractAssign.contractName != null and contractAssign.contractName != ''">
                and c.contract_name like concat('%', #{contractAssign.contractName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractAssign.distCom != null and contractAssign.distCom != ''">
                and c.dist_com like concat('%', #{contractAssign.distCom,jdbcType=VARCHAR} ,'%')
            </if>
            <if test="contractAssign.userOrgPositionDtoList != null and contractAssign.userOrgPositionDtoList.size > 0">
                and
                <foreach collection="contractAssign.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or" close=")">
                    (
                    (
                    ( c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( c.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.salary_comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                    )
                    <if test="userOrgPositionDto.loginName != null">
                        and (
                        ( c.seller = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                        )
                    </if>
                    )
                </foreach>
            </if>

            <if test="contractAssign.contractTypeList.size>0">
                and c.contract_type in
                <foreach collection="contractAssign.contractTypeList" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            and cp.current_flag=1 and cp.approval_status=4
        </where>
        <if test="contractAssign.distributionFlag==null">
            ORDER BY c.create_time DESC
        </if>
        <if test="contractAssign.distributionFlag==0">
            ORDER BY cp.pass_time DESC
        </if>
        <if test="contractAssign.distributionFlag==1">
            ORDER BY cas.valid_date DESC
        </if>

    </select>

    <select id="findByRelativeNo" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT id, assign_type, relative_no, cs_type, commissioner, valid_date, `status`

        from contract_assign

        <where>
            <if test="relativeNo != null and relativeNo != ''">
                and relative_no = #{relativeNo,jdbcType=VARCHAR}
            </if>
            <if test="assignType != null">
                and assign_type = #{assignType,jdbcType=INTEGER}
            </if>
            <!--<if test="status != null">
              and status = #{status,jdbcType=INTEGER}
            </if>-->
        </where>
    </select>
    <select id="findByRelativeNoAndCsType" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT id, assign_type, relative_no, cs_type, commissioner, valid_date, `status`

        from `reon-customerdb`.contract_assign

        <where>
            <if test="relativeNo != null and relativeNo != ''">
                and relative_no = #{relativeNo,jdbcType=VARCHAR}
            </if>
            <if test="assignType != null">
                and assign_type = #{assignType,jdbcType=INTEGER}
            </if>
            <if test="csType != null">
                and cs_type = #{csType,jdbcType=INTEGER}
            </if>
            <!--<if test="status != null">
              and status = #{status,jdbcType=INTEGER}
            </if>-->
        </where>
    </select>

    <update id="updateByRelativeNo">
        update contract_assign
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="assignType != null">
                assign_type = #{assignType,jdbcType=INTEGER},
            </if>
            <if test="relativeNo != null">
                relative_no = #{relativeNo,jdbcType=VARCHAR},
            </if>
            <if test="csType != null">
                cs_type = #{csType,jdbcType=INTEGER},
            </if>
            <if test="commissioner != null">
                commissioner = #{commissioner,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null">
                valid_date = #{validDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="posCode != null">
                pos_code = #{posCode,jdbcType=CHAR},
            </if>
            <if test="laterPosCode != null">
                later_pos_code = #{laterPosCode,jdbcType=CHAR},
            </if>
        </set>
        <where>
            <if test="relativeNo != null and relativeNo != ''">
                and relative_no = #{relativeNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <update id="updateByRelativeNoAndCsType">
        update contract_assign
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="assignType != null">
                assign_type = #{assignType,jdbcType=INTEGER},
            </if>
            <if test="relativeNo != null">
                relative_no = #{relativeNo,jdbcType=VARCHAR},
            </if>
            <if test="csType != null">
                cs_type = #{csType,jdbcType=INTEGER},
            </if>
            <if test="commissioner != null">
                commissioner = #{commissioner,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null">
                valid_date = #{validDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="posCode != null">
                pos_code = #{posCode,jdbcType=CHAR},
            </if>
            <if test="laterPosCode != null">
                later_pos_code = #{laterPosCode,jdbcType=CHAR},
            </if>
        </set>
        <where>
            <if test="relativeNo != null and relativeNo != ''">
                and relative_no = #{relativeNo,jdbcType=VARCHAR}
            </if>
            <if test="csType != null">
                and cs_type = #{csType,jdbcType=INTEGER}
            </if>
        </where>
    </update>

    <select id="findContractAssignList" resultType="com.reon.hr.api.customer.vo.ContractAssignVo">
        SELECT id, assign_type, relative_no, cs_type, commissioner, valid_date, `status`,pos_code,later_pos_code

        from contract_assign

        <where>
            <if test="relativeNo != null and relativeNo != ''">
                and relative_no = #{relativeNo,jdbcType=VARCHAR}
            </if>
            <if test="assignType != null">
                and assign_type = #{assignType,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="validDate != null">
                and valid_date <![CDATA[ <= ]]> #{validDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>
