package com.reon.hr.modules.customer.controller.salary.indTaxApplyInfoImport;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.customer.dto.importData.AddIndTaxApplyInfoImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.indTaxApplyInfo.IndTaxApplyInfoWrapperService;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.enums.salary.SalaryTaxTypeEnum;
import com.reon.hr.api.customer.listener.ImportExcelListener;
import com.reon.hr.api.customer.vo.batchImport.ImportDataVo;
import com.reon.hr.api.customer.vo.salary.IndTaxApplyInfoExportVo;
import com.reon.hr.api.customer.vo.salary.IndTaxApplyInfoVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.file.dubbo.service.rpc.IFileSystemService;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/customer/salary/indTaxApplyInfoImport")
public class IndTaxApplyInfoImportController extends BaseController {

    @Resource(name = "FileSystemServiceImpl")
    private IFileSystemService fileSystemService;

    @Resource(name = "batchImportDataDubboService")
    private IBatchImportDataWrapperService iBatchImportDataService;

    @Resource(name = "sequenceDubboService")
    private ISequenceService iSequenceService;
    @Autowired
    private IndTaxApplyInfoWrapperService indTaxApplyInfoWrapperService;
    @Autowired
    private IUserWrapperService iUserWrapperService;

    /**
     * 跳转到个税申报数据导入查询页面
     *
     * @return
     */
    @RequestMapping(value = "/gotoIndTaxApplyInfoImportQueryView", method = RequestMethod.GET)
    public ModelAndView gotoIndTaxApplyInfoImportQueryView() {
        return new ModelAndView ("/customer/salary/indTaxApplyInfoImport/indTaxApplyInfoImportQuery");
    }
    /**
     * 跳转到个税预申报数据导入查询页面
     *
     * @return
     */
    @RequestMapping(value = "/gotoIndTaxPreApplyInfoImportQueryView", method = RequestMethod.GET)
    public ModelAndView gotoIndTaxPreApplyInfoImportQueryView() {
        return new ModelAndView ("/customer/salary/indTaxApplyInfoImport/indTaxPreApplyInfoImportQuery");
    }



    /**
     * 跳转到个税申报数据导入文件页面
     *
     * @return
     */
    @RequestMapping(value = "/gotoIndTaxApplyInfoImportFileQueryView", method = RequestMethod.GET)
    public ModelAndView gotoIndTaxApplyInfoImportFileQueryView(Integer taxComparisonType, Model model) {
        model.addAttribute("taxComparisonType", taxComparisonType);
        if(taxComparisonType==null){
            return new ModelAndView ("/customer/salary/indTaxApplyInfoImport/indTaxPreApplyInfoImportData");
        }else {
            return new ModelAndView ("/customer/salary/indTaxApplyInfoImport/indTaxApplyInfoImportData");
        }
    }
    @RequestMapping("/gotoIndTaxApplyInfoImportFileCheckView")
    public ModelAndView gotoSalaryDeductionImportFileCheckView(Integer taxComparisonType, Model model,String taxComparisonTypeQueryListStr) {
        model.addAttribute("taxComparisonType", taxComparisonType);
        model.addAttribute("taxComparisonTypeQueryListStr", taxComparisonTypeQueryListStr);
        return new ModelAndView("/customer/salary/indTaxApplyInfoImport/indTaxApplyInfoImportFileCheckView");
    }


    /**
     * 查询个税申报数据导入信息
     *
     * @param indTaxApplyInfoVo
     * @param page
     * @param limit
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getIndTaxApplyInfoImportInfoListPage", method = RequestMethod.GET)
    public Object getIndTaxApplyInfoImportInfoListPage(IndTaxApplyInfoVo indTaxApplyInfoVo,
                                                       Integer page, Integer limit,HttpSession session) {
        indTaxApplyInfoVo.setCreator(getSessionUser().getLoginName());
        if(StringUtils.isNotBlank(indTaxApplyInfoVo.getTaxComparisonTypeQueryListStr())){
            String[] taxComparisonTypeQueryList = indTaxApplyInfoVo.getTaxComparisonTypeQueryListStr().split(",");
            List<Integer> taxComparisonTypeQueryListInt = Arrays.stream(taxComparisonTypeQueryList)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            indTaxApplyInfoVo.setTaxComparisonTypeQueryList(taxComparisonTypeQueryListInt);
        }

        Page<IndTaxApplyInfoVo> indTaxApplyInfoVoPage=new Page<>();
        if(indTaxApplyInfoVo.getTaxMonth()!=0){
            session.setAttribute("indTaxApplyInfoVo", indTaxApplyInfoVo);
            indTaxApplyInfoVoPage = indTaxApplyInfoWrapperService.getIndTaxApplyInfoVoListPage(page, limit,indTaxApplyInfoVo);
        }
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), indTaxApplyInfoVoPage.getTotal (), indTaxApplyInfoVoPage.getRecords ());
    }

    /**
     * 导入数据
     *
     * @param file 文件
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    @RepeatSubmit
    public Object importExcel(@RequestParam("file") CommonsMultipartFile file,@RequestParam("remark") String remark,@RequestParam("withholdingAgentNo") String withholdingAgentNo,
                              @RequestParam(value = "taxComparisonType",required = false) Integer taxComparisonType) {
        String fileId = null;
        CommonUserVo commonUserVo = getSessionUser ();
        String fileName = file.getOriginalFilename ();
        String batchImportDataNo = iSequenceService.getBatchImportDataNo ();
        ImportDataDto<AddIndTaxApplyInfoImportDto> importDataDto = new ImportDataDto<>();
        try {
            fileId = fileSystemService.uploadFile (file.getBytes (), fileName);
            fileName = fileId + "," + fileName;
            ImportExcelListener<AddIndTaxApplyInfoImportDto> importExcelListener = new ImportExcelListener<>();
            EasyExcel.read(file.getInputStream(), AddIndTaxApplyInfoImportDto.class, importExcelListener).sheet().doRead();
            importDataDto = importExcelListener.getImportDataDto();
            importDataDto.setFileId(fileName);
            importDataDto.setImportNo(batchImportDataNo);
            importDataDto.setLoginName(commonUserVo.getLoginName());
            importDataDto.setRemark(remark);
            importDataDto = iBatchImportDataService.batchAddIndTaxApplyInfoImport(importDataDto,withholdingAgentNo,taxComparisonType);
        } catch (Exception e) {
            e.printStackTrace ();
            return new LayuiReplay<String> (ResultEnum.ERR.getCode (), ResultEnum.ERR.getMsg (), e.getMessage ());
        }
        if(importDataDto.getErrorDesc().isEmpty()){
            return new LayuiReplay<String> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }else {
            String errorDescs="";
            int j=0;
            for (int i = 0; i < importDataDto.getErrorDesc().size(); i++) {
                String errorDesc = importDataDto.getErrorDesc().get(i);
                if(errorDesc!=null&&!errorDescs.contains(errorDesc)){
                    j++;
                    if(j<100){
                        errorDescs+=errorDesc.substring(0,1)+"第"+i+"行数据"+errorDesc.substring(1)+"</br>";
                    }else {
                        errorDescs+="数据错误信息过多！剩余错误信息已省略！";
                        break;
                    }
                }
            }
            return new LayuiReplay<String> (ResultEnum.ERR.getCode (), ResultEnum.ERR.getMsg (),errorDescs);
        }
    }



    @RequestMapping("/exportIndTaxApplyInfoImportInfoList")
    public void exportIndTaxApplyInfoImportInfoList(HttpServletResponse response, HttpSession session){
        IndTaxApplyInfoVo indTaxApplyInfoVo = (IndTaxApplyInfoVo) session.getAttribute("indTaxApplyInfoVo");
        List<IndTaxApplyInfoVo> indTaxApplyInfoVoList = new ArrayList<>();
        List<IndTaxApplyInfoExportVo> indTaxApplyInfoExportVos = new ArrayList<>();
        if (!Objects.isNull(indTaxApplyInfoVo)){
            indTaxApplyInfoVoList = indTaxApplyInfoWrapperService.getIndTaxApplyInfoVoList(indTaxApplyInfoVo);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
            if (CollectionUtils.isNotEmpty(indTaxApplyInfoVoList)){
                for (IndTaxApplyInfoVo taxApplyInfoVo : indTaxApplyInfoVoList) {
                    IndTaxApplyInfoExportVo indTaxApplyInfoExportVo = new IndTaxApplyInfoExportVo();
                    BeanUtils.copyProperties(taxApplyInfoVo,indTaxApplyInfoExportVo);
                    if (taxApplyInfoVo.getTaxStart()!=null){
                        indTaxApplyInfoExportVo.setTaxStart(format.format(taxApplyInfoVo.getTaxStart()));
                    }
                   if (taxApplyInfoVo.getTaxEnd()!=null){
                       indTaxApplyInfoExportVo.setTaxEnd(format.format(taxApplyInfoVo.getTaxEnd()));
                   }
                   if (taxApplyInfoVo.getCreateTime()!=null){
                       indTaxApplyInfoExportVo.setCreateTime(format.format(taxApplyInfoVo.getCreateTime()));
                   }
                   indTaxApplyInfoExportVo.setItemType(SalaryTaxTypeEnum.getNameByCode(taxApplyInfoVo.getItemType()));
                    indTaxApplyInfoExportVo.setCreator(allUserName.get(taxApplyInfoVo.getCreator()));
                    indTaxApplyInfoExportVos.add(indTaxApplyInfoExportVo);
                }
            }
        }
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = "个税申报数据" + format.format(new Date()) + ".xlsx";
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("utf-8"), "ISO8859-1"));
            EasyExcel.write(response.getOutputStream(), IndTaxApplyInfoExportVo.class).sheet("个税申报数据.xlsx").doWrite(indTaxApplyInfoExportVos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询导入统计的数据
     *
     * @param importNo
     * @param oprMan
     * @param startOprTime
     * @param endOprTime
     * @param page
     * @param limit
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getImportDataListPage", method = RequestMethod.GET)
    public Object getImportDataListPage( String importNo, String oprMan, String startOprTime,
                                        String endOprTime, Integer dataType, Integer page, Integer limit,
                                        @RequestParam(name = "taxComparisonType",required = false) Integer taxComparisonType,
                                        @RequestParam(name = "taxComparisonTypeQuery",required = false) Integer taxComparisonTypeQuery,
                                         @RequestParam(name = "taxComparisonTypeQueryListStr",required = false)String taxComparisonTypeQueryListStr) {

        CommonUserVo commonUserVo = getSessionUser();
        String loginName = commonUserVo.getLoginName();
        if (dataType != null && (
                dataType.equals(ImportDataType.SALARY_DEDUCTION.getCode()) ||
                        dataType.equals(ImportDataType.BATCH_ADD_IND_TAX_APPLY_INFO.getCode()))) {
            loginName = null;
        }
        Page<ImportDataVo> importDataListPage = iBatchImportDataService.getImportDataListByTaxComparisonTypePage( importNo, oprMan, startOprTime, endOprTime, loginName, dataType, page, limit,taxComparisonType,taxComparisonTypeQueryListStr,taxComparisonTypeQuery);
        return new LayuiReplay<ImportDataVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), importDataListPage.getTotal(), importDataListPage.getRecords());
    }

}
