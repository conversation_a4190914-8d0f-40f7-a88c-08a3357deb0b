<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>调基收集客户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <input type="hidden" value="${jobNo}" id="jobNo">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户名称">客户名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="custName" id="custName" placeholder="请选择" readonly
                               class="layui-input"/>
                        <input type="text" name="custId" id="custId" style="display: none;"/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="基数申报截止日起">基数申报截止日起：</label>
                    <div class="layui-input-inline">
                        <input type="text" maxlength="20" name="startEndDate" id="startEndDate" class="layui-input"
                               placeholder="请选择" autocomplete="off" readonly>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="基数申报截止日止">基数申报截止日止：</label>
                        <div class="layui-input-inline">
                            <input type="text" maxlength="20" name="endEndDate" id="endEndDate" class="layui-input"
                                   placeholder="请选择" autocomplete="off" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="任务状态">任务状态：</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" DICT_TYPE="COLLECT_JOB_STATUS" name="jobStatus"
                                    id="jobStatus">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="姓名/身份证/唯一号">姓名/身份证/唯一号：</label>
                        <div class="layui-input-inline">
                            <input type="text" maxlength="20" name="query" class="layui-input"
                                   placeholder="请输入" autocomplete="off">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                           lay-submit="">查询</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card-body">
    <table class="layui-hide" id="collectCustomerQueryTable" lay-filter="collectCustomerQueryTableFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <button class="layui-btn layui-btn-sm" lay-event="save" authURI="/change/declare/save"><i
                class="layui-icon layui-icon-ok"></i>保存
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="download" authURI="/change/declare/declareDownloadData"><i
                class="layui-icon layui-icon-export"></i>下载数据
        </button>
        <button class="layui-btn layui-btn-sm" id="upload" authURI="/change/declare/upload"><i class="layui-icon"></i>上传文件
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="export" authURI="/collect/customer/declareExport"><i
                class="layui-icon layui-icon-export"></i>导出数据-客服查看
        </button>
    </script>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/preventAccuracyLoss.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/change/collect/collectCustomerQuery.js?v=${publishVersion}"></script>
</body>
</html>
