package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.customer.dto.templetChange.ChangeTempletDto;
import com.reon.hr.api.customer.vo.ChangeTempletOrderVo;
import com.reon.hr.sp.customer.entity.cus.ChangeTempletOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ChangeTempletOrderMapper extends BaseMapper<ChangeTempletOrder> {
    int updateBatch(List<ChangeTempletOrder> list);

    int batchInsert(@Param("list") List<ChangeTempletOrder> list);

    int insertOrUpdate(ChangeTempletOrder record);

    int insertOrUpdateSelective(ChangeTempletOrderVo record);

//    List<ChangeTempletDto> selectAllDataByOrderNos(@Param("list") List<String> orderNos, @Param("status") int status);

    List<ChangeTempletDto> getChangeTempletByTempletId(@Param("list") Set<String> orderNos);

    Set<String> getAllOrderNosAndTempletId(@Param("templetId") Long templetId);

    List<ChangeTempletDto> selectAllDataByOrderNos(@Param("list") List<String> orderNos, @Param("status") Integer status);

    int updateBatchByIds(@Param("ids") List<Long> ids, @Param("status") Integer status);
}
