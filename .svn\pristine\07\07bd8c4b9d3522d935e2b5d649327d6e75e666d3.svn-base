package com.reon.hr.modules.bill.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.reon.hr.api.bill.dto.invoice.InvoiceDetailExportDto;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IBadDebtDealWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IBillCheckApprovalWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.salary.IRecBankRelativeWrapperService;
import com.reon.hr.api.bill.exception.BillCheckException;
import com.reon.hr.api.bill.exception.BillException;
import com.reon.hr.api.bill.utils.ExcelUtil;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.check.BillCheckApprovalVo;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.bill.vo.check.CheckSearchParmVo;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.constants.CommonConstants;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bill/check/")
public class BillCheckApprovalController extends BaseController {

    @Autowired
    private IBillCheckApprovalWrapperService billCheckWrapperService;
    @Autowired
    private IContractResourceWrapperService contractResourceWrapperService;
    @Autowired
    private IInsuranceBillWrapperService insuranceBillWrapperService;
    @Resource(name = "orgDubboService")
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Autowired
    private IRecBankRelativeWrapperService recBankRelativeWrapperService;
    private final static Logger log = LoggerFactory.getLogger(BillCheckApprovalController.class);

    @Resource(name = "iBadDebtDealWrapperService")
    private IBadDebtDealWrapperService iBadDebtDealWrapperService;

    @RequestMapping("getCheckUnApprovalCust")
    public LayuiReplay getCheckUnApprovalCust(@RequestParam("page") int page,
                                              @RequestParam("limit") int limit,
                                              @RequestParam("type") int type,
                                              @RequestParam(value = "searchParam", required = false) String searchParam) {
        String name = "";
        if (StringUtils.isNotBlank(searchParam)) {
            net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(searchParam);
            name = json.getString("name").trim();
        }

        // 获取当前操作人所在公司
        String companyCode = null;
        // 获取当前操作人角色
        String position = getSessionUser().getPosition();
        // 操作者为人事，则放开限制
        if (position != null && !position.contains(PositionEnum.HR_MANAGER.getCode())) {
            companyCode = getSessionUser().getCompanyCode();
        }

        Page<CustomerInfoVo> custPage = billCheckWrapperService.getUnApprovalCust(page, limit, companyCode, type, name);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), custPage.getTotal(), custPage.getRecords());
    }

    @RequestMapping("getCheckUnApprovalCustAll")
    public LayuiReplay getCheckUnApprovalCustAll(@RequestParam("page") int page,
                                                 @RequestParam("limit") int limit,
                                                 @RequestParam("type") int type,
                                                 @RequestParam(value = "searchParam", required = false) String searchParam) {
        String name = "";
        if (StringUtils.isNotBlank(searchParam)) {
            net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(searchParam);
            name = json.getString("name").trim();
        }

        Page<CustomerInfoVo> custPage = billCheckWrapperService.getUnApprovalCust(page, limit, null, type, name);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), custPage.getTotal(), custPage.getRecords());
    }

    @RequestMapping(value = "getCheckUnApproval", method = RequestMethod.GET)
    public LayuiReplay getCheckUnApproval(Integer page, Integer limit, CheckSearchParmVo param) {
        log.info("当前页{}，每页数{}", page, limit);

        /**
         * flag--->用来判断是否是页面加载请求
         * true 表示查询请求
         * false 表示页面加载请求，默认
         */

        if (!param.getFlag()) {
            return new LayuiReplay(ResultEnum.ERR.getCode(), "请输入查询条件");
        }

        //查询
        param.setLoginName(getSessionUser().getLoginName());
        Page<BillCheckApprovalVo> checkUnApprovals = billCheckWrapperService.getCheckUnApproval(page, limit, param);

        return new LayuiReplay<BillCheckApprovalVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), checkUnApprovals.getTotal(), checkUnApprovals.getRecords());
    }


    /**
     审批通过
     @return
     */
    @RequestMapping(value = "pass", method = RequestMethod.POST)
    @RepeatSubmit
    public LayuiReplay pass(@RequestBody Long[] ids) {
        if (ids.length == 0) {
            return new LayuiReplay(ResultEnum.ERR.getCode(), "请选择数据");
        }
        try {
            List<Long> billCheckList = Arrays.stream(ids).distinct().collect(Collectors.toList());
            Long[] checkIdArray = billCheckList.toArray(new Long[billCheckList.size()]);
            billCheckWrapperService.updateStatusByIds(checkIdArray, "pass", getSessionUser().getLoginName());
        } catch (BillException e) {
            log.error("pass bill check error", e);
            return new LayuiReplay(ResultEnum.ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("pass bill check error", e);
            return new LayuiReplay(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
        }
        return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    /**
     驳回
     @return
     */
    @RequestMapping(value = "reject", method = RequestMethod.POST)
    public LayuiReplay updateStatusByIds(@RequestBody Long[] ids) {
        if (ids.length == 0) {
            return new LayuiReplay(ResultEnum.ERR.getCode(), "请选择数据");
        }
        try {
            billCheckWrapperService.updateStatusByIds(ids, "reject", getSessionUser().getLoginName());
        } catch (BillException e) {
            log.error("reject bill check error", e);
            return new LayuiReplay(ResultEnum.ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("reject bill check error", e);
            return new LayuiReplay(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
        }
        return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    @RequestMapping("getPayCustAndInvoiceByCheckId")
    public LayuiReplay getPayCustAndInvoice(Long checkId) {

        Map<String, Object> payCustAndInvoice = billCheckWrapperService.getPayCustAndInvoice(checkId);
        return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), payCustAndInvoice);
    }


    @RequestMapping("gotoUnCheckBillApprovalPage")
    public ModelAndView gotoUnCheckBillApprovalPage() {
        return new ModelAndView("bill/unapproval/uncheckBill");
    }

    @PostMapping({"abolishCheck", "abolishCheckAdminUse"})
    @RepeatSubmit
    public LayuiReplay abolishCheck(@RequestBody List<Long> chekIdList) {
        List<Long> checkIds = chekIdList.stream().distinct().collect(Collectors.toList());
        try {
            recBankRelativeWrapperService.updateAndCheck(checkIds, getSessionUser().getLoginName());
            billCheckWrapperService.abolishCheck(checkIds, getSessionUser().getLoginName());
        } catch (BillCheckException e) {
            return new LayuiReplay(ResultEnum.OK.getCode(), e.getMessage());
        } catch (Exception e) {
            return new LayuiReplay(ResultEnum.OK.getCode(), "废除有误,请联系管理员!");
        }
        return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    @PostMapping({"abolishCheckByCheckIdAndBillId", "abolishCheckByCheckIdAndBillIdAdminUse"})
    @RepeatSubmit
    public LayuiReplay abolishCheckByCheckIdAndBillId(@RequestParam("checkId") Long checkId,
                                                      @RequestParam("billIdList[]") List<Long> biliIdList) {
        try {
            if (checkId == null || CollectionUtils.isEmpty(biliIdList)) {
                return LayuiReplay.error("请选择数据");
            }
            /**  todo 校验 是否是工资账单,以及是否可以废除 */
            biliIdList = recBankRelativeWrapperService.updateAndCheck(checkId, biliIdList, getSessionUser().getLoginName());
            if (CollectionUtils.isEmpty(biliIdList)) {
                return LayuiReplay.error("当前无可废除数据");
            }
            billCheckWrapperService.updateAbolishCheckByBillId(checkId, biliIdList, getSessionUser().getLoginName());
        } catch (BillCheckException e) {
            return new LayuiReplay(ResultEnum.OK.getCode(), e.getMessage());
        } catch (Exception e) {
            return new LayuiReplay(ResultEnum.OK.getCode(), "废除有误,请联系管理员!");
        }
        return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }


    @GetMapping("getAbolishCheckData")
    public LayuiReplay<InsuranceBillVo> getAbolishCheckData(Integer limit, String param, Integer page) {
        InsuranceBillVo insuranceBillVo = JsonUtil.jsonToBean(param, InsuranceBillVo.class);
        Page<InsuranceBillVo> result = billCheckWrapperService.getAbolishCheckData(insuranceBillVo, limit, page);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), result.getTotal(), result.getRecords());
    }

    @GetMapping("getInsuranceBillAndCheckIdByBillId")
    public LayuiReplay<InsuranceBillVo> getInsuranceBillAndCheckIdByBillId(Long billId) {
        List<BillCheckVo> result = billCheckWrapperService.getInsuranceBillAndCheckIdByBillId(billId);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), result);
    }

    @RequestMapping("gotoAbolishCheckPage")
    public ModelAndView gotoAbolishCheckPage() {
        return new ModelAndView("/bill/financial/billCheckAbolishAdminUse");
    }

    @RequestMapping(value = "/testCode", method = RequestMethod.GET)
    public Object testCode() {
        billCheckWrapperService.testCode();
        return new LayuiReplay<InsuranceBillVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    @RequestMapping("getBillCheckByTempletIdAndBillMonth")
    public List<BillCheckVo> getBillCheckByTempletIdAndBillMonth(@RequestBody List<InsuranceBillVo> insuranceBillVoList) {
        return billCheckWrapperService.getBillCheckByTempletIdAndBillMonth(insuranceBillVoList);
    }

    @RequestMapping("gotoCheckInvoiceAllBillAmt")
    public ModelAndView gotoCheckInvoiceAllBillAmt(Model model) {
        List<com.reon.hr.api.customer.dto.admin.OrgPositionDto> userOrgPositionDtoList = (List<com.reon.hr.api.customer.dto.admin.OrgPositionDto>) getSession().getAttribute(Constants.SESSION_ORG_POSITION);
        /**
         * Map<Long, String> custIdAndCustNameMap
         * Map<Long, Set<Long>> custGroupIdAndCustIdMap
         * Map<Long, String> custGroupIdAndGroupNameMap
         */
        Map<String, Object> custGroupAndCustList = contractResourceWrapperService.getCustGroupAndCustListByDefaultOrgPosCodeAndLoginNameFromContract(userOrgPositionDtoList);
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        model.addAttribute("custIdAndCustNameMap", JSON.toJSON(custGroupAndCustList.getOrDefault("custIdAndCustNameMap", Maps.newHashMap())));
        model.addAttribute("custGroupIdAndCustIdMap", JSON.toJSON(custGroupAndCustList.getOrDefault("custGroupIdAndCustIdMap", Maps.newHashMap())));
        model.addAttribute("custGroupIdAndGroupNameMap", JSON.toJSON(custGroupAndCustList.getOrDefault("custGroupIdAndGroupNameMap", Maps.newHashMap())));
        model.addAttribute("signTitleMap", JSON.toJSON(allCompany));
        return new ModelAndView("/bill/financial/checkInvoiceAllBillAmt");
    }


    @RequestMapping("gotoCheckInvoiceSelectedBill")
    public ModelAndView gotoCheckInvoiceSelectedBill(@RequestParam("billIdList") List<Long> billIdList, Model model, @RequestParam("billMonth") Integer billMonth) {
        /** 首先获取选择的账单id的金额汇总,然后格局选择的账单获取到 */
        BigDecimal sumReceiveAmt = insuranceBillWrapperService.getSumReceiveAmt(billIdList);
        List<PaymentCustomerVo> paymentCustomerVoList = insuranceBillWrapperService.getPaymentCustomerListByBillIdList(billIdList, billMonth);
        model.addAttribute("billIdList", JsonUtil.beanToJson(billIdList));
        model.addAttribute("billMonth", billMonth);
        model.addAttribute("sumReceiveAmt", sumReceiveAmt);
        model.addAttribute("paymentCustomerVoList", JsonUtil.beanToJson(paymentCustomerVoList));
        return new ModelAndView("/bill/financial/checkInvoiceSelectedBill");
    }

    @RequestMapping(value = "checkInvoiceAll", method = RequestMethod.POST)
    @RepeatSubmit
    public Object checkInvoiceAll(@RequestParam("billIdListStr") String billIdListStr, @RequestParam("billMonth") Integer billMonth
            , @RequestParam("orgCode") String orgCode, @RequestParam("orgName") String orgName) throws UnsupportedEncodingException {
        if (billMonth == null)
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "需要选择账单月!");

        List<Long> billIdList = JsonUtil.jsonToList(billIdListStr, Long.class);


        if (CollectionUtils.isEmpty(billIdList))
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择账单再进行核销!");

        if (StringUtils.isBlank(orgCode) || StringUtils.isBlank("orgName"))
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择开票分公司!");
        /** 用账单金额和余额,进行对比,如果没有问题  才能进行下一步 只余额,大于 账单进行才能直接进行核销开票 */
        BigDecimal sumReceiveAmt = insuranceBillWrapperService.getSumReceiveAmt(billIdList);
        List<PaymentCustomerVo> paymentCustomerVoList = insuranceBillWrapperService.getPaymentCustomerListByBillIdList(billIdList, billMonth);
        if (CollectionUtils.isNotEmpty(paymentCustomerVoList)) {
            BigDecimal sumAmt = paymentCustomerVoList.stream().map(PaymentCustomerVo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sumReceiveAmt.compareTo(sumAmt) > 0)
                return new LayuiReplay<>(ResultEnum.ERR.getCode(), "到款余额不足够进行核销!");
        } else
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "到款余额不足够进行核销!");


        Map<String, Object> conditonMap = Maps.newHashMap();

        conditonMap.put(CommonConstants.LOGIN_NAME, getSessionUser().getLoginName());
        conditonMap.put(CommonConstants.BILL_MONTH, billMonth);
        conditonMap.put(CommonConstants.ORG_CODE, orgCode);
        conditonMap.put(CommonConstants.ORG_NAME, orgName);
        billCheckWrapperService.checkInvoiceAll(billIdList, paymentCustomerVoList, conditonMap);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

    }

    @RequestMapping(value = "checkAll", method = RequestMethod.POST)
    @RepeatSubmit
    public Object checkAll(@RequestParam("billIdListStr") String billIdListStr, @RequestParam("billMonth") Integer billMonth
    ) {
        if (billMonth == null)
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "需要选择账单月!");

        List<Long> billIdList = JsonUtil.jsonToList(billIdListStr, Long.class);
        if (CollectionUtils.isEmpty(billIdList))
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择账单再进行核销!");

        /** 用账单金额和余额,进行对比,如果没有问题  才能进行下一步 只余额,大于 账单进行才能直接进行核销开票 */
        BigDecimal sumReceiveAmt = insuranceBillWrapperService.getSumReceiveAmt(billIdList);
        List<PaymentCustomerVo> paymentCustomerVoList = insuranceBillWrapperService.getPaymentCustomerListByBillIdList(billIdList, billMonth);
        if (CollectionUtils.isNotEmpty(paymentCustomerVoList)) {
            BigDecimal sumAmt = paymentCustomerVoList.stream().map(PaymentCustomerVo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sumReceiveAmt.compareTo(sumAmt) > 0)
                return new LayuiReplay<>(ResultEnum.ERR.getCode(), "到款余额不足够进行核销!");
        } else
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "到款余额不足够进行核销!");

        Map<String, Object> conditonMap = Maps.newHashMap();

        conditonMap.put(CommonConstants.LOGIN_NAME, getSessionUser().getLoginName());
        conditonMap.put(CommonConstants.BILL_MONTH, billMonth);
        billCheckWrapperService.checkAll(billIdList, paymentCustomerVoList, conditonMap);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

    }

    @RequestMapping(value = "invoiceAll", method = RequestMethod.POST)
    @RepeatSubmit
    public Object invoiceAll(@RequestParam("billIdListStr") String billIdListStr, @RequestParam("billMonth") Integer billMonth,
                             @RequestParam("orgCode") String orgCode,
                             @RequestParam("orgName") String orgName) {

        List<Long> billIdList = JsonUtil.jsonToList(billIdListStr, Long.class);
        if (CollectionUtils.isEmpty(billIdList))
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择账单再进行开票!");
        if (billMonth == null)
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "需要选择账单月!");
        if (StringUtils.isBlank(orgCode) || StringUtils.isBlank(orgName))
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), "需要选择开票公司!");


        Map<String, Object> conditonMap = Maps.newHashMap();
        conditonMap.put(CommonConstants.LOGIN_NAME, getSessionUser().getLoginName());
        conditonMap.put(CommonConstants.BILL_MONTH, billMonth);
        conditonMap.put(CommonConstants.ORG_CODE, orgCode);
        conditonMap.put(CommonConstants.ORG_NAME, orgName);
        /** 只有未开票的才能进行开票 不然就过滤掉 */
        billCheckWrapperService.invoiceAll(billIdList, conditonMap);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

    }

    @RequestMapping("gotoBadDebtDealListPage")
    public ModelAndView gotoBadDebtDealListPage() {
        return new ModelAndView("/bill/financial/badDebtDealListPage");
    }

    @RequestMapping("getBadDebtDealList")
    public Object getBadDebtDealList(Integer page, Integer limit,  String custId,String contractNo,String templetId,Integer billMonth ) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("custId", custId);
        paramMap.put("contractNo", contractNo);
        paramMap.put("templetId", templetId);
        paramMap.put("billMonth", billMonth);
        Page<BadDebtDealVo> pageInfo = iBadDebtDealWrapperService.getBadDebtDealList(page, limit, paramMap);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), pageInfo.getTotal(), pageInfo.getRecords());
    }
    @RequestMapping("gotoBadDebtDealPage")
    public ModelAndView gotoBadDebtDealPage() {
        return new ModelAndView("/bill/financial/badDebtDealPage");
    }

    @PostMapping("saveBadDebtDeal")
    @RepeatSubmit
    public Object saveBadDebtDeal(@RequestParam("billId") Long billId,@RequestParam("remark") String remark,@RequestParam("fileIdStr") String fileIdStr) {
         iBadDebtDealWrapperService.saveBadDebtDeal(billId, getSessionUser().getLoginName(),remark,fileIdStr);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg() );
    }


    @RequestMapping("exportBadDebtDealList")
    public void exportBadDebtDealList( String custId,String contractNo,String templetId,Integer billMonth, HttpServletResponse response) {

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("custId", custId);
        paramMap.put("contractNo", contractNo);
        paramMap.put("templetId", templetId);
        paramMap.put("billMonth", billMonth);
        List<BadDebtDealVo> resultList = iBadDebtDealWrapperService.getBadDebtDealListByExport(paramMap);

        String fileName = "坏账列表导出_" + DateUtil.format(new Date(), DateUtil.DATE_FORMAT_YYYYMMDD);
        try {
            ExcelUtil<BadDebtDealVo> excelUtil = new ExcelUtil<>(BadDebtDealVo.class);
            Workbook workbook = excelUtil.exportExcel(resultList, fileName);
            ExcelUtil.closeInfo(response, workbook, fileName);
        } catch (Exception e) {
            log.error("导出发票信息报表失败", e);
        }

    }


}
