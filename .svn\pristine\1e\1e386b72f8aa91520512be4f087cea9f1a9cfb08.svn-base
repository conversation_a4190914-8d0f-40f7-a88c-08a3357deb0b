/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/4/15
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.ehr.api.sys.utils;

import com.reon.ehr.api.sys.vo.SysDeptVo;
import com.reon.ehr.api.sys.vo.TreeSelect;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SysDeptBuilder
 * @description TODO
 * @date 2023/4/15 18:06
 */
public abstract class SysDeptBuilder {

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public static List<TreeSelect> buildDeptTreeSelect(List<SysDeptVo> depts)
    {
        List<SysDeptVo> deptTrees = buildDeptTree(depts);

        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public static List<SysDeptVo> buildDeptTree(List<SysDeptVo> depts)
    {
        List<SysDeptVo> returnList = new ArrayList<SysDeptVo>();
        List<Long> tempList = depts.stream().map(SysDeptVo::getDeptId).collect(Collectors.toList());
        for (SysDeptVo dept : depts)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private static void recursionFn(List<SysDeptVo> list, SysDeptVo t)
    {
        // 得到子节点列表
        List<SysDeptVo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDeptVo tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private static List<SysDeptVo> getChildList(List<SysDeptVo> list, SysDeptVo t)
    {
        List<SysDeptVo> tlist = new ArrayList<SysDeptVo>();
        Iterator<SysDeptVo> it = list.iterator();
        while (it.hasNext())
        {
            SysDeptVo n = (SysDeptVo) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private static boolean hasChild(List<SysDeptVo> list, SysDeptVo t)
    {
        return getChildList(list, t).size() > 0;
    }

}
