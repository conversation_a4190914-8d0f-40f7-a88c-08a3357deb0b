<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>

<head>
    <meta charset="utf-8">
    <title>欠款报表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all" />
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all" />
    <style type="text/css">
        .layui-form-label {
            width: 115px;
            padding-bottom: 20px
        }

        .export {
            height: 35px;
            margin-bottom: 10px;
            padding-left: 30px;
            padding-right: 30px;
            margin-left: 100px;
        }
    </style>
</head>

<body class="childrenBody">

<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm" action="" method="post">
        <div>

            <div class="layui-input-inline">
                <label class="layui-form-label layui-elip" title="服务年月开始月"><i
                        style="color: red">*</i>&nbsp;服务年月开始月</label>
                <div class="layui-input-inline">
                    <input type="text" name="startMonth" id="startMonth" lay-verify="required"
                           placeholder="请输入" class="layui-input receivableYears" readonly />
                </div>
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label layui-elip" title="服务年月截止月"><i
                        style="color: red">*</i>&nbsp;服务年月截止月</label>
                <div class="layui-input-inline">
                    <input type="text" name="endMonth" id="endMonth" lay-verify="required"
                           placeholder="请输入" class="layui-input receivableYears" readonly />
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label notNewline" title="客户名称">客户名称:</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"  id="custName" lay-filter="custName"
                           readonly
                           placeholder="请输入" autocomplete="off">
                    <input type="hidden" id="custId" name="custId">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label notNewline" title="雇员姓名">雇员姓名:</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" name="empName" id="empName"
                           placeholder="请输入" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label notNewline" title="证件号码">证件号码:</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" name="certNo" id="certNo"
                           placeholder="请输入" autocomplete="off">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label notNewline" title="订单号">订单号:</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" name="orderNo" id="orderNo"
                           placeholder="请输入" autocomplete="off">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label  layui-elip">所属城市</label>
                <div class="layui-input-inline" style="width: 182px">
                    <select name="cityCode"  id="cityCode" AREA_TYPE lay-search lay-Filter="cityCodeFilter">
                        <option value="" selected>-请选择</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="分公司 /供应商">分公司 /供应商：</label>
                <div class="layui-input-inline" style="width: 182px">
                    <select class="layui-select" name="receiving"  lay-search lay-Filter="serviceSiteFilter"
                            id="orgCode" readonly autocomplete="off" placeholder="请选择">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="是否单立户">是否单立户：</label>
                    <div class="layui-input-inline" style="width: 182px">
                        <select name="accountFlag" id="accountFlag" DICT_TYPE="BOOLEAN_TYPE">
                            <option value=""></option>
                        </select>
                    </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" title="合同类型" >合同类型</label>
                <div class="layui-input-inline" style="width: 182px">
                    <select name="contractType" id="contractType" lay-search DICT_TYPE="CONTRACT_CATEGORY" >
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" title="产品类型" >产品类型</label>
                <div class="layui-input-inline">
                        <select type="text" id="productType" name="productType" class="layui-input "  DICT_TYPE="PRODUCT_IND_TYPE">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" title="签约方抬头" >签约方抬头</label>
                <div class="layui-input-inline">
                    <select name="signComTitle" id="signComTitle" lay-filter="signTitle"
                            lay-search>
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-input-inline" style="padding-left: 50px">
                <div class="layui-input-inline">
                    <a class="layui-btn  export" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                       lay-submit="">导出</a>
                </div>
            </div>
        </div>


    </form>
    <div>
        &nbsp;<p><span style="color: red;font-size: 20px;font-weight: bold">点击导出后请到下载中心查看文件</span></p>&nbsp;
    </div> 
</blockquote>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/numberCheck.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/report/orderFeesReport.js?v=${publishVersion}"></script>
</body>

</html>