/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/6/17 0017
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.changeBase;

import lombok.Data;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdjustCfgVo
 *
 * @date 2021/6/17 0017 10:16
 */
@Data
public class AdjustCfgVo implements Serializable {
    private static final long serialVersionUID = -226185094982678840L;

    private long id;  // bigint not null auto_increment comment '主键ID',
    private long adjustId;  // bigint comment '调整任务ID',
    private int prodType;  // int comment '产品类型',
    private String oldRatioCode;  // varchar(50) comment '原比例编号',
    private String newRatioCode;  // varchar(50) comment '新比例编号',
    private BigDecimal newComRatio;  // varchar(50) comment '新企业比例',
    private BigDecimal newIndRatio;  // varchar(50) comment '新个人比例',
    private String comCol;  // char(1) comment '企业基数列',
    private String indCol;  // char(1) comment '个人基数列',
    private String creator;  // varchar(50) comment '创建人',
    private Date createTime;  // datetime default CURRENT_TIMESTAMP comment '创建时间',
    private String updater;  // varchar(50) comment '修改人',
    private Date updateTime;  // datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '修改时间',
    private String delFlag;  // char(1) default 'N' comment '删除标识(Y:已删除，N:未删除)',
}
