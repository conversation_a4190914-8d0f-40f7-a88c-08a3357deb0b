layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    // 监听保存按钮
    form.on("submit(save)", function (data) {
        saveForm(data);
        return false;
    });

    // 关闭弹窗
    $(document).on('click', '#cancelBtn', function () {
        layer.closeAll('iframe');
    });

    function saveForm(data) {
        console.log(data);
        console.log($("#id").val());
        $.ajax({
            url: ML.contextPath + "/customer/sales/saveVisitCompanyName",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                layer.msg(result.msg);
                layer.closeAll('iframe');
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });
    }

    $(document).ready(function () {
        var companyName = $("#companyName").val();
        console.log(companyName);
        if (companyName != ""){
            $('#companyName').attr("disabled",true);
            form.render();
        }

    })



})