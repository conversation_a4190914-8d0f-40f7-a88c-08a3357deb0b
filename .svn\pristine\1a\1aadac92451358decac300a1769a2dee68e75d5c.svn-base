package com.reon.ehr.sp.sys.service.impl.order;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeEntryDimissionVo;
import com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeEntryDimission;
import com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeOrderLog;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeEntryDimissionMapper;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeOrderLogMapper;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeOrderMapper;
import com.reon.ehr.sp.sys.service.order.IEpEmployeeEntryDimissionService;
import com.reon.ehr.sp.sys.service.order.IEpEmployeeOrderService;
import com.reon.hr.api.customer.enums.PersonOrderEnum;
import com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service("epEmployeeEntryDimissionService")
public class EpEmployeeEntryDimissionServiceImpl implements IEpEmployeeEntryDimissionService {
    @Autowired
    private EhrEmployeeEntryDimissionMapper entryDimissionMapper;

    @Autowired
    private EhrEmployeeOrderLogMapper employeeOrderLogMapper;

    @Autowired
    private IUserWrapperService iUserWrapperService;

    @Autowired
    private EhrEmployeeOrderMapper employeeOrderMapper;
    @Autowired
    private IEpEmployeeOrderService employeeOrderService;


    @Override
    public int updateByOrderNoSelective(EhrEmployeeEntryDimission record)  {
        return entryDimissionMapper.updateByOrderNoSelective(record);
    }

    @Override
    public EhrEmployeeEntryDimission getByOrderNo(String orderNo) {
        return entryDimissionMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<EhrEmployeeEntryDimission> getByOrderNos(List<String> orderNo) {
        return entryDimissionMapper.selectByOrderNos(orderNo);
    }

    @Override
    public int editIdStatus(List<String> orderNo) {
        return entryDimissionMapper.updataIdStatus(orderNo);

    }

    @Override
    public List<EhrEmployeeEntryDimissionVo> findAllDimissionRemarkByOrderNo(List<String> orderNoList) {
        return entryDimissionMapper.findAllDimissionRemarkByOrderNo(orderNoList);
    }

    @Override
    public int updateSaveTypeAndRemarkByOrderNo(Integer saveType, String remark, String orderNo,String loginName,String dimissionRemark) {
        /*EhrEmployeeOrderLog employeeOrderLog = new EhrEmployeeOrderLog();
        Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
        Long employeeId = employeeOrderMapper.findEmployeeIdByOrderNo(orderNo);
        employeeOrderLog.setEmployeeId(employeeId);
        String name = allUserName.get(loginName);
        employeeOrderLog.setOrderNo(orderNo);
        SimpleDateFormat dnf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String  format=dnf.format(new Date());

        employeeOrderLog.setRemark("保存"+"   "+name+"   " +format);
        employeeOrderLog.setOprType(2);
        employeeOrderLog.setCreator(loginName);
        employeeOrderLog.setUpdater(loginName);
        employeeOrderLog.setCreateTime(new Date());
        employeeOrderLog.setDelFlag("N");
        employeeOrderLogMapper.insertSelective(employeeOrderLog);
        employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, employeeId, PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode(), dimissionRemark.replaceAll("\n","  ")+" ");*/
        return entryDimissionMapper.updateSaveTypeAndRemarkByOrderNo(saveType, remark, orderNo);
    }

    @Override
    public List<String> getAllOrderNoByContractNo(String contractNo) {
        return entryDimissionMapper.getAllOrderNoByContractNo(contractNo);
    }

    @Override
    public Map<String, Date> getEntryDateMapByOrderNoSet(Set<String> orderNoSet) {
        if (CollectionUtils.isEmpty(orderNoSet))
            return Maps.newHashMap();
        EntityWrapper<EhrEmployeeEntryDimission> wrapper = new EntityWrapper<>();
        wrapper.setSqlSelect("order_no", "entry_date").eq("del_flag", "N").in("order_no", orderNoSet);
        List<EhrEmployeeEntryDimission> employeeEntryDimissions = entryDimissionMapper.selectList(wrapper);
        Map<String, Date> resultMap = employeeEntryDimissions.stream().collect(Collectors.toMap(EhrEmployeeEntryDimission::getOrderNo, EhrEmployeeEntryDimission::getEntryDate));
        return resultMap;
    }

    @Override
    public Set<String> adjustDimissionByOrderNo(Set<String> dealCfgStartMonthAndExpireMonthOrderNoSet, String time) {
        if (CollectionUtils.isEmpty(dealCfgStartMonthAndExpireMonthOrderNoSet))
            return Sets.newHashSet();
        return entryDimissionMapper.adjustDimissionByOrderNo(dealCfgStartMonthAndExpireMonthOrderNoSet,time);
    }
}
