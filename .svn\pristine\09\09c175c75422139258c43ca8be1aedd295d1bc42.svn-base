package com.reon.hr.sp.dubbo.service.rpc.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.common.RegionAndCityDto;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.OrgTypeEnum;
import com.reon.hr.api.vo.JsonResult;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.OrgPositionVo;
import com.reon.hr.api.vo.sys.OrgUserVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.common.enums.DepartmentEnum;
import com.reon.hr.common.enums.PositionType;
import com.reon.hr.sp.entity.sys.CommonUser;
import com.reon.hr.sp.service.sys.ICommonUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.reon.hr.api.enums.PositionEnum.FINANCIAL_MANAGER;
import static com.reon.hr.api.enums.PositionEnum.PROJECT_MANAGER;

@Service("userDubboService")
public class UserWrapperServiceImpl implements IUserWrapperService {

    @Autowired
    private ICommonUserService dsUserService;

    @Override
    public String findLoginNameBySameCityOrgTypeAndPosition(String orgCode, String orgType, String positionCode) {
        List<CommonUserVo> list = dsUserService.findUserBySameCityOrgAndPosition (orgCode, orgType, positionCode);
        return list.isEmpty () ? null : list.get (0).getLoginName ();
    }

    @Override
    public String findLoginNameBySameCityOrgTypeAndPositionCode(Integer owerCity,String orgCode, String orgType, String positionCode) {
        List<CommonUserVo> list = dsUserService.findLoginNameBySameCityOrgTypeAndPositionCode (owerCity,orgCode, orgType, positionCode);
        return list.isEmpty () ? null : list.get (0).getLoginName ();
    }

    @Override
    public Page<CommonUserVo> selectPage(CommonUserVo paramVo, Integer page, Integer limit) {
        Page<CommonUserVo> userVoPage = dsUserService.selectUserVoListPage (paramVo, page, limit);
        return userVoPage;
    }

    @Override
    public CommonUserVo selectUserById(String id) {
        return dsUserService.selectUserById (Long.parseLong (id));
    }

    @Override
    public JsonResult<Object> doUserLogin(String loginName, String password, String lang, String ip) {
        return dsUserService.doUserLogin (loginName, password, lang, ip);
    }

    @Override
    public boolean saveUser(CommonUserVo userVo, String operator) {
        return dsUserService.saveUser (userVo, operator);
    }

    @Override
    public void reloadAllCommonUser() {
        dsUserService.reloadAllCommonUser ();
    }

    @Override
    public Map<String, String> getAllUserMap() {
        return dsUserService.getAllUserMap ();
    }

    @Override
    public Map<String, String> selectAllUserEmail() {
        return dsUserService.selectAllUserEmail();
    }

    @Override
    public Map<String, String> getAllUserMapRealTime() {
        return dsUserService.getAllUserMapRealTime ();
    }

    @Override
    public JsonResult<Object> changeUserPwd(long userId, String oldPassword, String newPassword) {
        return dsUserService.changeUserPwd (userId, oldPassword, newPassword);
    }

    @Override
    public CommonUserVo selectUserByParams(Map<String, Object> queryMap) {
        CommonUser commonUser = dsUserService.selectUserByParams (queryMap);
        CommonUserVo userVo = null;
        if (null != commonUser) {
            userVo = new CommonUserVo ();
            BeanUtils.copyProperties (commonUser, userVo);
        }
        return userVo;
    }

    @Override
    public Page<OrgUserVo> findOrgUserList(String code, int startPage, int pageNum) {

        return dsUserService.findOrgUserList (code, startPage, pageNum);

    }

    @Override
    public UserOrgPosVo getLargeDefaultFlagOrgPosByOrgCode(String orgCode) {
        return dsUserService.getLargeDefaultFlagOrgPosByOrgCode(orgCode);
    }

    @Override
    public int deleteUser(List<Long> ids) {
        if (CollectionUtils.isNotEmpty (ids)) {
            return dsUserService.deleteUser (ids);
        }
        return 0;
    }

    @Override
    public boolean updateUserStatus(Long id, String status) {
        return dsUserService.updateUserStatus (id, status);
    }

    @Override
    public List<OrgUserVo> searchSaleByOrgCode() {
        return dsUserService.searchSaleByOrgCode ();
    }


    @Override
    public CommonUserVo findServiceSupervisorBySameCompanyOwnercity(String orgCode, String position) {
        if (StringUtils.isNotBlank (orgCode) && StringUtils.isNotBlank (position)) {
            List<CommonUserVo> list = dsUserService.findUserBySameCityOrgAndPosition (orgCode, OrgTypeEnum.CS_DT.getCode (), position);
            return list != null && list.size () > 0 ? list.get (0) : null;
        }
        return null;
    }

    @Override
    public Page<OrgUserVo> getOrgUserInfo(int page, int limit, String param) {
        return dsUserService.getOrgUserInfo (page, limit, param);
    }


    @Override
    public CommonUserVo findUserByLoginName(String userName) {
        if (StringUtils.isNotBlank (userName)) {
            return dsUserService.findUserByLoginName (userName);
        }
        return null;
    }

    @Override
    public List<CommonUserVo> findUsersByLoginNames(List<String> userNames) {
        if (userNames.size() > 0) {
            return dsUserService.findUsersByLoginNames (userNames);
        }
        return null;
    }


    @Override
    public CommonUserVo getLeaderListAndOrgInfo(Long userId) {
        return dsUserService.getLeaderListAndOrgInfo (userId);
    }

    @Override
    public String findLoginNameByPosCodeAndOrgCode(String orgCode, String posCode) {
        if (orgCode.length()>9){
            return dsUserService.findLoginNameByPosCodeAndOrgCode (orgCode.substring(0,9), posCode);
        }
        return dsUserService.findLoginNameByPosCodeAndOrgCode (orgCode, posCode);
    }

    @Override
    public List<CommonUserVo> findTransferServiceList(String loginName) {
        List<CommonUserVo> list = Lists.newArrayList ();
        List<OrgPositionVo> loginNameByPosCodeAndOrgCode = dsUserService.findLoginNameByPosCodeAndOrgCode (loginName);
        if (CollectionUtils.isNotEmpty (loginNameByPosCodeAndOrgCode)) {
            loginNameByPosCodeAndOrgCode.forEach (orgPositionVo -> {
                //获取总监下所有分公司下的经理
                StringBuffer stringBuffer = new StringBuffer (orgPositionVo.getPosCode ());
                if (stringBuffer.length () == 1) {
                    stringBuffer.append ("00");
                    List<CommonUserVo> loginNameByPosCodeAndOrgCodeList = dsUserService.findLoginNameByPosCodeAndOrgCodeList (orgPositionVo.getOrgCode (), stringBuffer.toString ());
                    loginNameByPosCodeAndOrgCodeList.forEach (commonUserVo -> {
                        list.add (commonUserVo);
                    });
                }
            });
            return list;
        }
        return null;
    }

    @Override
    public UserOrgPosVo findOrgByPosCodeAndLoginName(String posCode, String orgCode, String loginName) {
        return dsUserService.findOrgByPosCodeAndLoginName (posCode, orgCode, loginName);
    }

    @Override
    public CommonUserVo getPosition(CommonUserVo commonUserVo) {
        List<UserOrgPosVo> orgPosVos = commonUserVo.getOrgPosVos ();
        for (UserOrgPosVo orgPosVo : orgPosVos) {
            if (orgPosVo.getPosCode ().equals (PROJECT_MANAGER.getCode()) ||
                    orgPosVo.getPosCode ().equals (FINANCIAL_MANAGER.getCode())) {
                commonUserVo.setPosition (orgPosVo.getPosCode ());
            }
        }
        return commonUserVo;
    }

    @Override
    public List<UserOrgPosVo> findSubordinateByLoginNameAndCity(String loginName, String orgCode, String positionCode) {
        return dsUserService.findSubordinateByLoginNameAndCity (loginName, orgCode, positionCode);
    }

    @Override
    public List<UserOrgPosVo> findSubordinateByParentIdList(List<Long> directorList) {
        return dsUserService.findSubordinateByParentIdList (directorList);
    }

    @Override
    public String findLeaderLoginNameByPosCodeAndOrgCode(String posCode, String orgCode) {
        return dsUserService.findLeaderLoginNameByPosCodeAndOrgCode (posCode, orgCode);
    }

    @Override
    public String getRevMgrByCityCode(String cityCode) {
        return dsUserService.getRevMgrByCityCode(cityCode);
    }

    @Override
    public CommonUserVo getProjectDistrictManagerBySubarea(String subarea) {
        return dsUserService.getProjectDistrictManagerBySubarea (subarea);
    }

    @Override
    public List<CommonUserVo> findOrgCodeByowerCity(Integer owerCity) {
        return dsUserService.findOrgCodeByowerCity (owerCity);
    }

    @Override
    public Page<CommonUserVo> getUserByOrgCode(String orgCode, Integer page, Integer limit, String custName) {
        return dsUserService.getUserByOrgCode(orgCode,page,limit,custName);
    }

    @Override
    public List<OrgUserVo> getAllOrganization() {
        return dsUserService.getAllOrganization();
    }

    @Override
    public List<OrgUserVo> getAllReceivingMan() {
        return dsUserService.getAllReceivingMan();
    }

    @Override
    public Map<String, String> getAllUserMapIgnoreDelFlag() {
        return dsUserService.getAllUserMapIgnoreDelFlag();
    }

    @Override
    public List<CommonUserVo> getDisSupManList() {
        return dsUserService.getDisSupManList();
    }

    @Override
    public Map<String, String> getLoginUserPosType() {
        List<UserOrgPosVo> loginUserPosType = dsUserService.getLoginUserPosType();
        for (UserOrgPosVo userOrgPosVo : loginUserPosType) {
            String name = DepartmentEnum.getName(userOrgPosVo.getPosType());
            if (StringUtils.isEmpty(name)){
                userOrgPosVo.setPosition(PositionType.getName(userOrgPosVo.getPosType()));
            }else {
                userOrgPosVo.setPosition(name);
            }

        }
        return loginUserPosType.stream().filter(item->item.getLoginName()!=null&&item.getPosition()!=null).collect(Collectors.toMap(UserOrgPosVo::getLoginName, UserOrgPosVo::getPosition,(key1,key2)->key2));
}

    @Override
    public List<String> getUrlByLoginName(String loginName) {
        return dsUserService.getUrlByLoginName(loginName);
    }

    @Override
    public boolean updateUserBymain(CommonUserVo dsUserVo, String loginName) {
        return dsUserService.updateUserBymain(dsUserVo,loginName);
    }

    @Override
    public List<CommonUserVo> getAllDataByNameAndMobileList(List<Map<String, String>> queryMap) {
        return dsUserService.getAllDataByNameAndMobileList(queryMap);
    }

    @Override
    public List<String> getOrgCodeByLoginName(String loginName) {
        return dsUserService.getOrgCodeByLoginName(loginName);
    }

    @Override
    public Map<String, RegionAndCityDto> getRegionAndCityMap() {
        return dsUserService.getRegionAndCityMap();
    }

    @Override
    public List<Map<String, String>> getAllUserName() {
        return dsUserService.getAllUserName();
    }

    @Override
    public Page<OrgUserVo> getProjectServiceUserAndOrgNameByUserName(Integer page, Integer limit,String userName) {
        return dsUserService.getProjectServiceUserAndOrgNameByUserName(page,limit,userName);
    }

    @Override
    public List<String> getLongingNameByOrgCodeAndPosCode(String orgCode, String posCode) {
        return dsUserService.getLongingNameByOrgCodeAndPosCode(orgCode,posCode);
    }

    @Override
    public List<CommonUserVo> getByLoginNameLIst(List<String> loginNames) {
        return dsUserService.getByLoginNameLIst(loginNames);
    }


    @Override
    public Page<CommonUserVo> getAllUserForTableSelect(Integer page, Integer limit, CommonUserVo userVo) {
        return dsUserService.getAllUserForTableSelect(page,limit,userVo );
    }

    @Override
    public List<CommonUserVo> searchSellerByLoginName(String loginName) {
        return dsUserService.searchSellerByLoginName(loginName);
    }

    @Override
    public List<OrgUserVo> findCommercialInsuranceCustomerService() {
        return dsUserService.findCommercialInsuranceCustomerService();
    }

    @Override
    public CommonUserVo getUserByLogingName(String loginName) {
        return dsUserService.getUserByLogingName(loginName);
    }

    @Override
    public Integer updateCommonUser(CommonUserVo commonUserVo) {
        return dsUserService.updateCommonUser(commonUserVo);
    }

    @Override
    public List<String> findLoginNameSameCityByPosCodeAndOrgCode(String orgCode, String posCode) {
        return dsUserService.findLoginNameSameCityByPosCodeAndOrgCode(orgCode,posCode);
    }

    @Override
    public List<String> findLoginNameSameAreaByPosCodeAndOrgCode(String orgCode, String posCode) {
        return dsUserService.findLoginNameSameAreaByPosCodeAndOrgCode(orgCode,posCode);
    }

    @Override
    public List<CommonUserVo> getServiceListByOrgCodeAndPositionCode(String orgCode, String positionCode) {
        return dsUserService.getServiceListByOrgCodeAndPosCode(orgCode,positionCode);
    }

    @Override
    public List<OrgPositionVo> searchOrgCodeByLoginName(String loginName) {
        return dsUserService.searchOrgCodeByLoginName(loginName);
    }

    @Override
    public String getSupervisorByArea(long id) {
        return dsUserService.getSupervisorByArea(id);
    }

    @Override
    public Page<OrgUserVo> getReceivingManInfo(Integer page, Integer limit, CommonUserVo userVo) {

        return  dsUserService.getReceivingManInfo(page, limit, userVo);
    }
}
