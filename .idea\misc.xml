<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FrameworkDetectionExcludesConfiguration">
    <file type="web" url="file://$PROJECT_DIR$" />
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="http://192.168.12.149:9999/svn/reon/branches" />
                  <option value="http://192.168.12.149:9999/svn/reon/tags" />
                </list>
              </option>
              <option name="trunkUrl" value="http://192.168.12.149:9999/svn/reon/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>