package com.reon.hr.sp.report.dao.report;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.sp.report.entity.ServiceNumPeopleReport;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface ServiceNumPeopleReportMapper extends BaseMapper<ServiceNumPeopleReport> {
    int deleteByPrimaryKey(Long id);

    Integer insert(ServiceNumPeopleReport record);

    int insertSelective(ServiceNumPeopleReport record);

    ServiceNumPeopleReport selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceNumPeopleReport record);

    int updateByPrimaryKey(ServiceNumPeopleReport record);

    int updateBatch(List<ServiceNumPeopleReport> list);

    int batchInsert(@Param("list") List<ServiceNumPeopleReport> list);

    List<ServiceNumPeopleReport> getDataByYearMonth(@Param("yearMonthS") Integer yearMonthS, @Param("yearMonthE") Integer yearMonthE, @Param("item") Map<String, Object> conditonMap);

    @Update("update `reon-reportdb`.service_num_people_report snpr set del_flag = 'Y' where snpr.year_month_for_snp = #{month}")
    Integer deleteCurMonthDataByMonth(@Param("month") Integer currYearMonth);
}