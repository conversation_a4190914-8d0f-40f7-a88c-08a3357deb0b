<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.InsurancePracticeOneFeeMapper">

    <sql id="Base_Column_List">
        id,org_code,
                payment_id,
                amt,
                import_no,
                approve_status,
                first_approve_status,
                first_approve,
                first_approve_date,
                second_approve_status,
                second_approve,
                second_approve_date,
                creator,
                create_time,
                updater,
                update_time,
                del_flag
    </sql>

    <insert id="insertInsurancePracticeOneFee" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        INSERT INTO insurance_practice_one_fee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != orgCode and '' != orgCode">
                org_code,
            </if>
            <if test="null != paymentId ">
                payment_id,
            </if>
            <if test="null != importNo and '' != importNo">
                import_no,
            </if>
            <if test="null != amt ">
                amt,
            </if>
            <if test="null != approveStatus ">
                approve_status,
            </if>
            <if test="null != firstApproveStatus ">
                first_approve_status,
            </if>
            <if test="null != firstApprove and '' != firstApprove">
                first_approve,
            </if>
            <if test="null != firstApproveDate ">
                first_approve_date,
            </if>
            <if test="null != secondApproveStatus ">
                second_approve_status,
            </if>
            <if test="null != secondApprove and '' != secondApprove">
                second_approve,
            </if>
            <if test="null != secondApproveDate ">
                second_approve_date,
            </if>
            <if test="null != creator and '' != creator">
                creator,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updater and '' != updater">
                updater,
            </if>
            <if test="null != updateTime ">
                update_time,
            </if>
            <if test="null != delFlag and '' != delFlag">
                del_flag
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != orgCode and '' != orgCode">
                #{orgCode},
            </if>
            <if test="null != paymentId ">
                #{paymentId},
            </if>
            <if test="null != importNo and '' != importNo">
                #{importNo},
            </if>
            <if test="null != amt ">
                #{amt},
            </if>
            <if test="null != approveStatus ">
                #{approveStatus},
            </if>
            <if test="null != firstApproveStatus ">
                #{firstApproveStatus},
            </if>
            <if test="null != firstApprove and '' != firstApprove">
                #{firstApprove},
            </if>
            <if test="null != firstApproveDate ">
                #{firstApproveDate},
            </if>
            <if test="null != secondApproveStatus ">
                #{secondApproveStatus},
            </if>
            <if test="null != secondApprove and '' != secondApprove">
                #{secondApprove},
            </if>
            <if test="null != secondApproveDate ">
                #{secondApproveDate},
            </if>
            <if test="null != creator and '' != creator">
                #{creator},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updater and '' != updater">
                #{updater},
            </if>
            <if test="null != updateTime ">
                #{updateTime},
            </if>
            <if test="null != delFlag and '' != delFlag">
                #{delFlag}
            </if>
        </trim>
    </insert>


    <select id="getInsurancePracticeOneFeeByPaymentIdAndApproveStatus"
            resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select
        <include refid="Base_Column_List"/>
        from insurance_practice_one_fee where payment_id = #{paymentId} and approve_status
        in
        <foreach item="item" collection="approveStatusList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>


    </select>

    <delete id="deleteRejectedData">
        delete from insurance_practice_one_fee where id =#{id}
    </delete>

    <select id="getInsurancePracticeOneFeeByPaymentId" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select <include refid="Base_Column_List"/> from insurance_practice_one_fee where payment_id = #{paymentId}
    </select>

    <update id="updateInsurancePracticeOneFee">
        UPDATE insurance_practice_one_fee
        <set>
            <if test="null != orgCode and '' != orgCode">org_code = #{orgCode},</if>
            <if test="null != paymentId ">payment_id = #{paymentId},</if>
            <if test="null != importNo and '' != importNo">import_no = #{importNo},</if>
            <if test="null != amt ">amt = #{amt},</if>
            <if test="null != approveStatus ">approve_status = #{approveStatus},</if>
            <if test="null != firstApproveStatus ">first_approve_status = #{firstApproveStatus},</if>
            <if test="null != firstApprove and '' != firstApprove">first_approve = #{firstApprove},</if>
            <if test="null != firstApproveDate ">first_approve_date = #{firstApproveDate},</if>
            <if test="null != secondApproveStatus ">second_approve_status = #{secondApproveStatus},</if>
            <if test="null != secondApprove and '' != secondApprove">second_approve = #{secondApprove},</if>
            <if test="null != secondApproveDate ">second_approve_date = #{secondApproveDate},</if>
            <if test="null != updater and '' != updater">updater = #{updater},</if>
            <if test="null != updateTime ">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getInsurancePracticeOneFeeMessageRemind" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select second_approve,first_approve,second_approve_status,first_approve_status
        from insurance_practice_one_fee
        where approve_status = 2
          and (first_approve_status = 2 or second_approve_status = 2)

    </select>


    <select id="getApproveDifferencesPage" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select io.org_code, io.id, payment_id, amt,first_approve, second_approve,second_approve_status,first_approve_status,
               pa.apply_amt, pa.act_pay_amt, import_no,
               (pa.act_pay_amt - pa.apply_amt) as paymentApplyAmtDifference
        from insurance_practice_one_fee io
                 left join payment_apply pa on pa.id = io.payment_id
        where approve_status = 2
          and (first_approve_status = 2 or second_approve_status = 2)

    <if test="vo.orgCode !=null and vo.orgCode !=''">
        and io.org_code = #{vo.orgCode}
    </if>

    </select>


    <select id="getInsurancePracticeOneFeeById" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select <include refid="Base_Column_List"/> from insurance_practice_one_fee where id = #{id}
    </select>


    <select id="getAllApproveDifferencesPage" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select
        id.dis_com,
        id.cust_id,
        id.amt,
        io.approve_status,
        io.first_approve,
        io.second_approve,
        io.first_approve_status,
        io.org_code,
        io.import_no,
        io.first_approve,
        io.second_approve_status,
        pa.apply_amt,
        pa.act_pay_amt,
        (pa.act_pay_amt - pa.apply_amt) as paymentApplyAmtDifference
        from insurance_practice_one_fee io
        left join insurance_practice_one_fee_detail id on id.one_id = io.id
        left join payment_apply pa on pa.id = io.payment_id
        where io.org_code in
        <foreach collection="vo.orgCodeList" item="item" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        <if test="vo.disCom !=null and vo.disCom !=''">
            and id.dis_com = #{vo.disCom}
        </if>
        <if test="vo.custId !=null and vo.custId !=''">
            and id.cust_id = #{vo.custId}
        </if>
        order by io.update_time desc
    </select>


    <select id="getInsurancePracticeOneFeeListByPayIdSet"
            resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select payment_id,amt,create_time from insurance_practice_one_fee where approve_status = 3 and payment_id in
        <foreach item="item" collection="payIdSet" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="printBalanceDiff"  resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        SELECT ip.org_code,
               id.dis_com,
               id.cust_id,
               id.amt,
               id.order_no,
               id.product_code
        FROM insurance_practice_one_fee ip
                 LEFT JOIN insurance_practice_one_fee_detail id ON id.one_id = ip.id
        WHERE ip.payment_id = #{payId} and ip.create_time <![CDATA[<=]]> (select pass_time from payment_apply where id= #{payId})
          <if test="disCom !=null and disCom !=''">
            and id.dis_com = #{disCom}
        </if>
          and approve_status = 3
    </select>


    <select id="getInsurancePracticeOneFeeDisComListByPayIdSet" resultType="com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo">
        select distinct io.payment_id,id.dis_com,io.create_time
        from insurance_practice_one_fee io
        left join insurance_practice_one_fee_detail id on id.one_id = io.id where id.payment_id in
        <foreach item="item" collection="payIdSet" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and approve_status =3
    </select>



</mapper>