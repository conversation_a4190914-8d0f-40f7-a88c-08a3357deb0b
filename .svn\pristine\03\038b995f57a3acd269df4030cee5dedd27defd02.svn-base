package com.reon.hr.api.vo.sys;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserOrgPosVo implements Serializable {

    private static final long serialVersionUID = -6304786322582414263L;

    private Long id;

    /**
     定岗Id
     */
    private Long posOrgId;
    /**
     定岗父岗位id
     */
    private Long parentId;
    /**
     定岗名
     */
    private String posOrgName;
    /**
     职位名
     */
    private String position;
    /**
     机构code
     */
    private String orgCode;


    /**
     岗位类别标记
     */
    private Integer posKind;

    private String orgName;
    /**
     岗位code
     */
    private String posCode;

    /** 岗位类型 */
    private Integer posType;

    private String parentCode;
    private String parentOrgCode;//上一级机构
    private String parentPosCode;//上一级岗位


    /**
     定岗上两级id
     */
    private String grandparentId;
    /**
     上两级机构
     */
    private String grandparentOrgCode;
    /**
     上两级岗位
     */
    private String grandparentPosCode;

    private Long userId;

    private String loginName;

    private String userName;

    private String creator;

    private String updater;

    private Integer owerCity;

    private boolean LAY_CHECKED;

    private String mechanismCode;

    private String mechanismName;
    /** 默认岗标识(1:非默认，2:默认) */
    private Integer defaultFlag;
    /** 审批岗位机构code  可以定制的那种 从organization_position表数据取出 不能没有 */
    private String approveOrgPosCode;

    private String tel;

    /**
     防止序列化时get方法将属性名称改为小写
     @return
     */
    @JsonProperty("LAY_CHECKED")
    public boolean isLAY_CHECKED() {
        return LAY_CHECKED;
    }

    public void setLAY_CHECKED(boolean LAY_CHECKED) {
        this.LAY_CHECKED = LAY_CHECKED;
    }
}
