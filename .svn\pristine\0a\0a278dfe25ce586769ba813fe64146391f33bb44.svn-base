package com.reon.hr.sp.customer.entity.insurancePractice;

import lombok.Data;

import java.util.Date;

@Data
public class ImplementFeedback {
    private String orderNo;//订单编号
    private Date importTime;//导入时间
    private Integer addReduceType;//增减员类型
    private Integer productCode;//社保产品类型
    private Integer declareResult;//申报结果1.成功，2.失败
    private String reason;//原因
    private Integer insuredMonth;//参保年月
    private String remark;//备注

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}
