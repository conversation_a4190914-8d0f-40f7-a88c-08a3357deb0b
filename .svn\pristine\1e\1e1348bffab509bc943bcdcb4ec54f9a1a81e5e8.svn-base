package com.reon.hr.sp.customer.entity.cus;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`reon-customerdb`.bill_templet_sort")
public class BillTempletSort implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 合同编号
     */
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 账单模板id
     */
    @TableField(value = "templet_id")
    private Long templetId;

    /**
     * 排序号
     */
    @TableField(value = "sort_no")
    private Integer sortNo;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_CONTRACT_NO = "contract_no";

    public static final String COL_TEMPLET_ID = "templet_id";

    public static final String COL_SORT_NO = "sort_no";

    public static final String COL_CREATOR = "creator";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATER = "updater";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_DEL_FLAG = "del_flag";
}