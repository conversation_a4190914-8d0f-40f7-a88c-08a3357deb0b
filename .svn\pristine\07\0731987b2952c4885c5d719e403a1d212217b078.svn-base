package com.reon.hr.sp.workflow.service.activiti;


import com.reon.hr.sp.workflow.exception.WorkflowException;
import org.activiti.engine.identity.Group;
import org.activiti.engine.identity.Picture;
import org.activiti.engine.identity.User;
import org.activiti.engine.identity.UserQuery;
import org.activiti.engine.impl.Page;
import org.activiti.engine.impl.UserQueryImpl;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.activiti.engine.impl.persistence.entity.UserEntityImpl;
import org.activiti.engine.impl.persistence.entity.UserEntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ActUserEntityManager implements UserEntityManager,Session {

    @Autowired
    private ActUserGroupFacadeService userGroupFacadeService;
    @Override
    public User createNewUser(String userId) {
       return  new UserEntityImpl();
    }

    @Override
    public void updateUser(User updatedUser) {
        throw new WorkflowException();
    }

    @Override
    public List<User> findUserByQueryCriteria(UserQueryImpl query, Page page) {
        throw new WorkflowException();
    }

    @Override
    public long findUserCountByQueryCriteria(UserQueryImpl query) {
        throw new WorkflowException();
    }

    @Override
    public List<Group> findGroupsByUser(String userId) {

        return userGroupFacadeService.findGroupsByUser(userId);
    }

    @Override
    public UserQuery createNewUserQuery() {
        return new UserQueryImpl();
    }

    @Override
    public Boolean checkPassword(String userId, String password) {
        return null;
    }

    @Override
    public List<User> findUsersByNativeQuery(Map<String, Object> parameterMap, int firstResult, int maxResults) {
        throw new WorkflowException();
    }

    @Override
    public long findUserCountByNativeQuery(Map<String, Object> parameterMap) {
        return 0;
    }

    @Override
    public boolean isNewUser(User user) {
        return false;
    }

    @Override
    public Picture getUserPicture(String userId) {
        throw new WorkflowException();
    }

    @Override
    public void setUserPicture(String userId, Picture picture) {
        throw new WorkflowException();
    }

    @Override
    public void deletePicture(User user) {
        throw new WorkflowException();
    }

    @Override
    public UserEntity create() {
        return new UserEntityImpl();
    }


    @Override
    public UserEntity findById(String entityId) {

        return userGroupFacadeService.findByUserId(entityId);
    }

    @Override
    public void insert(UserEntity entity) {
        throw new WorkflowException();
    }

    @Override
    public void insert(UserEntity entity, boolean fireCreateEvent) {
        throw new WorkflowException();
    }

    @Override
    public UserEntity update(UserEntity entity) {
        throw new WorkflowException();
    }

    @Override
    public UserEntity update(UserEntity entity, boolean fireUpdateEvent) {
        throw new WorkflowException();
    }

    @Override
    public void delete(String id) {
        throw new WorkflowException();
    }

    @Override
    public void delete(UserEntity entity) {
        throw new WorkflowException();
    }

    @Override
    public void delete(UserEntity entity, boolean fireDeleteEvent) {
        throw new WorkflowException();
    }

    @Override
    public void flush() {

    }

    @Override
    public void close() {

    }
}
