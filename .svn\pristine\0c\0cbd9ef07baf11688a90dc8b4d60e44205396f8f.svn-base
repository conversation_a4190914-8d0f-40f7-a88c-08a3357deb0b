<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/22
  Time: 14:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
  <title>新增员工合同</title>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">
  <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
  <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
  <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
  <style>
    .layui-onlySelf {
      width: 125px;
    }

    .layui-tab-item {
      position: unset
    }

    .layui-btn layui-btn-sm tableSelect_btn_select reset {
      display: none !important;
    }


  </style>
</head>
<body>

<div class="layui-tab-item layui-show" style="margin-top: 5px">
  <div class="layui-fluid">
    <div class="layui-card">
      <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
        <%--隐藏域--%>
        <div class="layui-form-item">
          <input type="hidden" id="custId">
        </div>




      </form>
    </div>
  </div>
  <table id="customerGridTable" lay-filter="customerGridTable"></table>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/customer/editCustNameLog.js?v=${publishVersion}"></script>
</body>
</html>
