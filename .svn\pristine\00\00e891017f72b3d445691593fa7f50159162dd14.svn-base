<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>合同特殊审批</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>

</head>
<body class="childrenBody" >
<div class="layui-tab" lay-filter="tabFilter">
    <ul class="layui-tab-title">
        <li class="layui-this">合同特殊审批</li>
        <li  >特殊审批流程图</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 20px">
        <form class="layui-form" method="post">
            <%--隐藏域--%>
                <input type="hidden" id="eventEnums" value='${eventEnums}'>
                <input type="hidden" id="optType" value='${optType}'>
                <input type="hidden" id="pid" value='${data.pid}'>
                <input type="hidden" id="contractListJSON" value='${data.contractListJSON}'>
                <input type="hidden" id="uploadIdListJSON" value='${data.uploadIdListJSON}'>
                <input type="hidden" id="uploadIdList" value='${data.uploadIdList}'>
                <input type="hidden" id="receiveFreqStr" value='${data.receiveFreq}'>
                <input type="hidden" id="invoiceMethodStr" value='${data.invoiceMethod}'>
                <input type="hidden" id="id" value='${data.id}'>
                <input type="hidden" id="taskId" value='${taskId}'>
                <input type="hidden" id="approvalStatus" value='${data.approvalStatus}'>
                <input type="hidden" id="loginName" value='${data.loginName}'>
            <%--表单元素--%>
                <div class="layui-form-item" >
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="变更名称:"><i
                                style="color: red">*</i>变更名称：</label>
                        <div class="layui-input-inline">
                       <input class="layui-input" type="text" autocomplete="off" id="name" name="name"  lay-verify="required" value="${data.name}"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="回款频率:">回款频率：</label>
                        <div class="layui-input-inline">
                            <select name="receiveFreq" id="receiveFreq" class="layui-select"
                                    lay-verType="tips" DICT_TYPE="RECEIVE_FREQ"  >
                                <option></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="开具发票方式"> 开具发票方式：</label>
                        <div class="layui-input-inline">
                            <select name="invoiceMethod" id="invoiceMethod" class="layui-select"
                                    lay-verType="tips"  DICT_TYPE="INVOICE_METHOD"  >
                                <option></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="到款日"> 到款日：</label>
                        <div class="layui-input-inline">
                            <input type="text" id="incomeDate" name="incomeDate" placeholder="请选择" readonly value="${data.incomeDate}" autocomplete="off"
                                   autocomplete="off"
                                   class="layui-input date"   lay-verType="tips"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="合同选择">合同选择：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="contractName"   readonly width="300px">
                        </div>
                    </div>

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-elip" title="已选择合同:"><i style="color: red;">*</i>已选择合同:</label>
                    <div class="layui-inline">
                        <blockquote class="layui-elem-quote" style="margin-top: -20px;">
                            <div class="layui-upload-list" id="contract"></div>
                        </blockquote>
                    </div>
                </div>

                <fieldset class="layui-elem-field layui-field-title" style=" ">
                    <legend>特殊审批项选择</legend>
                </fieldset>
                <table id="specialFlagTable" lay-filter="specialFlagTableFilter"></table>

                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title" style=" ">
                        <legend>备注</legend>
                    </fieldset>
                    <textarea id="remark" name="remark" class="layui-textarea" style="margin-left:3%;width:80%"  >${data.remark}</textarea>
                </div>

                <fieldset class="layui-elem-field layui-field-title"  >
                    <legend>文件上传</legend>
                </fieldset>
                <div class="layui-upload" style="margin-left: 20px; margin-right: 20px;">
                    <button type="button" id="wfUpload" class="layui-btn layui-btn-normal">选择文件</button>
                    <blockquote class="layui-elem-quote " style="margin-top: 10px;">
                        预览图：
                        <div class="layui-upload-list" id="upload"></div>
                    </blockquote>
                </div>
                <div class="layui-form-item approval-btn">
                    <fieldset class="layui-elem-field layui-field-title" style=" ">
                        <legend>审批意见</legend>
                    </fieldset>
                    <textarea id="comment" name="comment" class="layui-textarea" style="margin-left:3%;width:60%"  ></textarea>
                </div>

            <div   style="margin-left:70%;padding-bottom: 10px;">
                <div class="normal">
                    <button class="layui-btn layui-btn-normal layui-inline" lay-submit lay-filter="save" id="save"
                            type="button">保存
                    </button>
                    <button class="layui-btn layui-btn-normal layui-inline" lay-submit lay-filter="commit" id="commit"
                            type="button">提交
                    </button>
                </div>
                <div class=" approval-btn"  >
                    <button class="layui-btn layui-btn-normal layui-inline" lay-submit lay-filter="pass" id="pass"
                            type="button">通过
                    </button>
                    <button class="layui-btn layui-btn-normal layui-inline" lay-submit lay-filter="reject" id="reject"
                            type="button">驳回
                    </button>
                </div>
<%--                <button class="layui-btn layui-btn-primary layui-inline" type="button" id="cancel">取消</button>--%>
            </div>
        </form>
    </div>

        <div class="layui-tab-item approval" style="margin-top: 20px">
            <img id="workFlowImg" src=""/>
            <div class="layui-form-item layui-hide" style="margin-top: 15px;" id="currentItem">
                <label class="layui-form-label filed-length" id="currentApproverName"></label>
                <div class="layui-input-inline">
                    <input type="text" id="currentApproverAssignee"  readonly disabled class="layui-input layui-anim layui-anim-scaleSpring" value=""/>
                </div>
            </div>
            <table class="layui-table" id="flowTable" lay-filter="flowTableFilter"></table>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/contract/changeContractItem.js?v=${publishVersion}"></script>
</body>
</html>
