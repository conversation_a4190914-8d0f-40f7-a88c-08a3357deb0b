package com.reon.hr.common.enums.invoice;

import lombok.Getter;

import java.util.Objects;

/**
 * 发票状态
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Getter
public enum InvoiceRequestStatusEnum {
    IS_SUBMIT(1, "已提交"),
    SUBMIT_FAIL(2, "提交失败"),
    SUBMIT_SUCCESS(3, "开票成功"),
    ;

    private final Integer code;
    private final String description;

    InvoiceRequestStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找对应的开票类型枚举项
     *
     * @param code 开票类型代码
     * @return 对应的枚举项，若找不到则返回null
     */
    public static InvoiceRequestStatusEnum fromCode(Integer code) {
        for (InvoiceRequestStatusEnum color : values()) {
            if (Objects.equals(color.getCode(), code)) {
                return color;
            }
        }
        return null;
    }
}
