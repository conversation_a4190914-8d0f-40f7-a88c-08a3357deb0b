/*
 * Copyright (C), 2016-2017, ML
 * FileName: IResourceService.java
 * Author:   bwz
 * Date:     2017年6月28日 下午6:03:24
 * Description: //模块目的、功能描述      
 * History: //修改记录
 * <author>      <time>      <version>    <desc>
 * 修改人姓名             修改时间            版本号                  描述
 */
package com.reon.hr.sp.service.sys;

import java.util.List;
import java.util.Map;

import com.reon.hr.api.vo.sys.ResourceVO;
import org.apache.ibatis.annotations.Param;


/**
 * 资源相关接口
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public interface IResourceService {

	/**
	 * 是否是超级管理员
	 * @param roleIdList
	 * @return
	 */
	public boolean isSuperAdmin(List<Integer> roleIdList);
	
    public List<String> getResourceByRoleId(long roleId);
    
    public List<ResourceVO> getResourceByRoleIds(String lang, List<Integer> roleIdList);
    
    public List<ResourceVO> searchAllResources(String lang);
    
    public List<ResourceVO> getResourceByParams(Map<String, Object> queryMap);

    /**
     * 保存（删除、插入在一个是事务里）
     * @param roleId
     * @param resourceIdlist
     */
    public void saveRoleResouce(int roleId, List<String> resourceIdlist);
    
    public List<ResourceVO> getRootResource();

    public List<ResourceVO> getResourceByResourceDefaultTypes(String lang);

    boolean updateResourceDefaultType(List<String> resourceNo);
}
