package com.reon.hr.api.bill.dubbo.service.rpc.bill.financial;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.reon.hr.api.bill.dto.ImportDataDto;
import com.reon.hr.api.bill.dto.PaymentCustomerDto;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.check.BillCheckApprovalVo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;

public interface INetSilverWrapperService {
    static Map<String, Object> netSilverHashMap = Maps.newConcurrentMap ();
    static final ForkJoinPool NS_FORK_JOIN_POOL = new ForkJoinPool (1);

    /**
     * 查询上传网银记录
     * @param parameterVo
     * @param page
     * @param limit
     * @return
     */
    Page<BankInfoAttachmentVo> getNetSilverUploadListPage( ParameterVo parameterVo,Integer page, Integer limit);

    /**
     * 查询网银记录
     * @param parameterVo
     * @param page
     * @param limit
     * @return
     */
    Page<NetSilverQueryVo> getNetSilverQueryListPage(ParameterVo parameterVo,Integer page, Integer limit);

    /**
     * 查询网银记录
     * @param parameterVo
     * @return
     */
    List<NetSilverQueryVo> getNetSilverQueryList(ParameterVo parameterVo);

    /**
     * 保存网银上传流水
     * @param record
     * @return
     */
    int saveBankInfoAttachment(BankInfoAttachmentVo record);

    /**
     * 批量保存到款客户信息
     * @param record
     * @return
     */
    int batchPaymentCustomerVo(List<PaymentCustomerVo> record);

    /**
     * 保存备注
     * @param paymentCustomerVo
     * @return
     */
    boolean saveRemark(PaymentCustomerVo paymentCustomerVo);

    /**
     * 更新冻结和解冻以及作废和恢复有效
     * @param ids
     * @param freezeFlag
     * @param status
     * @param loginName
     * @param remark
     * @return
     */
    boolean updateStatus(String ids,Integer freezeFlag,Integer status,String loginName,String remark);

    /**
     * 查询网银操作历史记录
     * @param payCustNo
     * @param loginName
     * @param page
     * @param limit
     * @return
     */
    Page<PaymentCustomerLogVo> getNetSilverOperatingHistoryLogListPage(Long payCustId,String loginName,Integer page, Integer limit);


    Page<BillCheckApprovalVo> getNetSilverCheckedQueryListPage(String prjCsLoginName, String financelLoginName,Integer payCustId, Integer page, Integer limit);

    ImportDataDto<PaymentCustomerDto> batchInsertPaymentCustomer(ImportDataDto<PaymentCustomerDto> importDataDto, String orgCode) throws Exception;

    void handleSyncBankLogs(String searchTime,String hasSearch);

    void handlePayCustomerMatchSalrayRece(String loginName);



}
