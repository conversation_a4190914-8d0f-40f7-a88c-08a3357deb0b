package com.reon.hr.common.constant;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.common.cmb.BaseResponseInfo;
import com.reon.hr.common.cmb.QueryPayrollDetailResInfo;
import com.reon.hr.common.enums.ServiceNameType;

import java.util.List;
import java.util.Map;

public interface CMBPayConstant {

     /**
      * 业务类型key
      */
     String BUS_CODE_KEY = "busCod";
     /**
      * 经办查询
      */
     String BUS_CODE_KEY_Q = "buscod";

     /**
      * 业务模式key
      */
     String BUS_MODE_KEY = "busMod";

     /**
      * 业务类型value "00034", "云直连0审批"
      * 此code需要根据业务模式查询 并不是固定
      */
//     String BUS_MODE_VALUE = "00034";
     String BUS_MODE_VALUE = "00002";

     /**
      * 代发经办业务类型value "S3006", "无审批代发工资"
      * 值：根据业务类型查询而来
      */
     String PAY_ROLL_BUS_MODE_VALUE = "S3006";

     /**
      * 业务流水号key
      */
     String BUS_FLOW_NO_KEY = "yurRef";
//     String BUS_FLOW_NO_KEY = "yurref";

     /**************单笔企业支付*******************/

     /**
      * 单笔支付模式信息key
      */
     String PAY_SINGLE_MODE_INFO_KEY = "bb1paybmx1";

     /**
      * 单笔支付模式信息key
      */
     String PAY_SINGLE_DETAIL_INFO_KEY = "bb1payopx1";

     /**
      * 单笔企业支付业务类型code
      */
     String PAY_SINGLE_BUS_CODE = "N02030";

     String PAY_SINGLE_QUERY_CONDITION_KEY = "bb1payqrx1";


     /***********************代发工资*********************************/

     /**
      * 代发工资业务类型code
      */
     String PAYROLL_BUS_CODE = "N03010";

     /**
      * 模式接口
      */
     String PAYROLL_BUS_MOD_INFO_KEY ="bb6busmod";
     /**
      * 代发工资汇总接口key
      */
     String PAYROLL_BATCH_INFO_KEY = "bb6cdcbhx1";
     /**
      * 代发工资明细接口key
      */
     String PAYROLL_DETAIL_INFO_KEY = "bb6cdcdlx1";

     /**
      * 代发工资接口明细最大条目数，超过这个数要续传
      */
     int MAX_PAYROLL_DETAIL_COUNT = 1000;

     /**
      * 标识：是
      */
     String YES_FLAG = "Y";

     /**
      * 标识否
      */
     String NO_FLAG = "N";

     /****************代发查询接口************************/
     /**
      * 代发工资查询接口ID
      */
     String QUERY_PAYROLL_INTERFACE_ID = "bb6bpdqyy1";

     /**
      * 续传批次号KEY
      */
     String CONTINUE_BATCH_NUMBER_KEY = "bthnbr";

     /**
      * 续传明细序号KEY
      */
     String CONTINUE_TRANS_DETAIL_SEQ_KEY = "trxseq";

     /**
      * 续传历史查询标志
      */
     String CONTINUE_HISTORY_FLAG = "histag";


     String PUBLICKEY = "cmb.publicKey";
     String PRIVATEKEY = "cmb.privateKey";
     String SYMKEY = "cmb.symKey";
     String UID = "cmb.uid";
     String NOTIFYPUBKEY = "cmb.notifyPubKey";
     String ENVIRONMENT_DEV = "dev";

     List<String> KEY_FIELD_LIST = Lists.newArrayList(PUBLICKEY,PRIVATEKEY,SYMKEY,UID,NOTIFYPUBKEY);
     List<String> PAY_TYPE = Lists.newArrayList(ServiceNameType.BB1PAYOP.getServiceCode(),ServiceNameType.BB6BTHHL.getServiceCode());

     String SUCCESS = "SUC0000";

     String NOTIFY_PRE = "notify:";
     Map<String,String> RELATIVE_BUSMAP = Maps.newHashMap();
     String DEV = "dev";
     String PROD = "prod";


     String DIC_CODE_TYPE = "CMB_FUN";


}
