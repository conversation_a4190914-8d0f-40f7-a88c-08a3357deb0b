var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var $ = layui.$,
        form = layui.form, laydate = layui.laydate,
        layer = layui.layer, tableSelect = layui.tableSelect,
        upload = layui.upload,
        table = layui.table,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    form.render('select');

    table.render({
        id: 'billGrid',
        elem: '#billGrid',
        data: [],
        page: true, //默认为不开启
        height: 650,
        limits: [50, 100, 200],
        limit: 50,
        title: "账单信息",
        defaultToolbar: [],
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {field: 'contractNo', title: '合同编号', width: '10%', align: 'center', fixed: 'left'},
            {field: 'contractName', title: '合同名称', width: '10%', align: 'center'},
            {
                field: 'contractType', title: '合同类型', width: '10%', align: 'center'
                , templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            },
            {
                field: 'seller', title: '销售', width: '10%', align: 'center'
                , templet: function (d) {
                    return ML.loginNameFormater(d.seller);
                }
            },
            {
                field: 'signCom', title: '签单分公司', width: '10%', align: 'center'
                , templet: function (d) {
                    if (!d.signCom || typeof (companyCodeToName(d.signCom)) == "undefined") {
                        return "无提交签单分公司"
                    }
                    return companyCodeToName(d.signCom);
                }
            },
            {field: 'custName', title: '客户名称', width: '10%', align: 'center'},
            {field: 'groupName', title: '集团名称', width: '10%', align: 'center'},
            {field: 'templetName', title: '客户帐套', width: '10%', align: 'center'},
            {field: 'cityName', title: '销售所属城市', width: '10%', align: 'center'},
            {field: 'billMonth', title: '账单年月', width: '6%', align: 'center'},
            {field: 'receivableMonth', title: '财务应收年月', width: '8%', align: 'center'},
            {
                field: 'employeeNum', title: '账单人数', width: '6%', align: 'center', templet: function (d) {
                    return d.employeeNum ? d.employeeNum : 0;
                }
            },
            {
                field: 'receiveAmt', title: '应收金额', width: '8%', align: 'center', templet: function (d) {
                    return d.receiveAmt ? d.receiveAmt : 0;
                }
            },
            {
                field: 'disability', title: '残保金', width: '8%', align: 'center', templet: function (d) {
                    return d.disability ? d.disability : 0;
                }
            },
            {
                field: 'genStatus', title: '生成状态', width: '6%', align: 'center', templet: function (d) {
                    return d.genStatus == 0 ? "生成中" : ML.dictFormatter("BILL_GEN_STATUS", d.genStatus)
                }
            },
            {
                field: 'genMan', title: '生成人', width: '10%', align: 'center', templet: function (d) {
                    return ML.loginNameFormater(d.genMan);
                }
            },
            {
                field: 'newFlag', title: '新增/存量标识', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_NEW_FLAG", d.newFlag);
                }
            },
            {field: 'seviceFee', title: '应收服务费', width: '6%', align: 'center', fixed: 'right'},
            {field: 'actGenTime', title: '出账时间', width: '8%', align: 'center', fixed: 'right'},
            {field: '', title: '操作', width: '6%', align: 'center', fixed: 'right', templet: function (d) {
                return '<a class="layui-btn layui-btn-xs" lay-event="downloadFile">账单打印</a>';
            }},
        ]],
        done: function (res) {
            ML.hideNoAuth();
            $("#totalNum").html("");
            $("#totalNum").append(res.count);
            table.on('tool(billFilter)', function (obj) {
                const data = obj.data; // 获得当前行数据
                switch (obj.event) {
                    case 'downloadFile':
                        var templetIdList = [];
                        templetIdList.push(data.templetId);
                        window.location.href = ML.contextPath + "/bill/billPrintAllData?contractNo="
                            + data.contractNo + "&templetIdList=" + JSON.stringify(templetIdList) + "&billMonth=" + data.billMonth + "&custId=" + data.custId
                            + "&printType=" + 1 + "&isMerge=" + 1 + "&sortType=" + 1+ "&isSaleReportFlag=" + 1;
                        break;
                }
            })
        }
    });


    // 查询
    form.on('submit(btnQuerySummaryFilter)', function (data) {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/report/sale/getSaleBillSummary",
            data: {"paramData": JSON.stringify(serialize("searchForm"))},
            dataType: 'json',
            success: function (data) {
                $(".clear").html("");
                $("#employeeNumSummary").append(data.data.employeeNum);
                $("#seviceFeeSummary").append(data.data.seviceFee);
                $("#receiveAmtSummary").append(data.data.receiveAmt);
            },
            error: function (data) {
                layer.alert('系统发生异常，请重试！' + data);
            }
        });
        return false;
    });

    // 查询
    form.on('submit(btnQueryFilter)', function (data) {
        if ($("#currentMonth").val()) {
            table.reload('billGrid', {
                url: ctx + '/report/sale/getSaleReportList',
                where: {"paramData": JSON.stringify(serialize("searchForm"))},
                page: {curr: 1}
            });
        }
        return false;
    });
    window.name = "dimListPage";
    form.on('submit(dimExportFilter)', function (data) {
        console.log(111)
        exportFun("/report/sale/exportSaleReportList"+"?paramData=" +JSON.stringify(serialize("searchForm")));
        return false;
    });
    function exportFun(url) {
        window.location.href = ctx + url;

    }

    var initYear;
    laydate.render({
        elem: '#billMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#billMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    var initYear;
    laydate.render({
        elem: '#currentMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#currentMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    var initYear;
    laydate.render({
        elem: '#endMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#endMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    //同时渲染多格laydate
    lay('.contractDate').each(function () {
        laydate.render({
            elem: this
            , trigger: 'click'
            , min: '2010-01-01'
            , max: '2099-12-12'
            , theme: 'grid'
            , calendar: true
            , format: 'yyyyMMdd'
        });
    });

    // 页面加载函数
    $(document).ready(function () {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/group/getAllGroupName",
            dataType: 'json',
            success: function (data) {
                var groupNames = data.data;
                $.each(groupNames, function (i, item) {
                    $("#groupId").append($("<option/>").text(item.groupName).attr("value",item.groupIds));
                });
                form.render("select");
            },
            error: function (data) {
                console.log("error")
            }
        });
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                var signCom = data.data;
                var ownerCityStr="";
                $.each(signCom, function (i, item) {
                    if(ownerCityStr.indexOf(item.owerCity)==-1){
                        ownerCityStr+=item.owerCity+"-";
                    }
                });
                var ownerCityList = ownerCityStr.split("-");
                $.each(ownerCityList, function (i, item) {
                    $("#ownerCity").append($("<option/>").text(ML.areaFormatter(item)).attr("value", item));
                });
                form.render("select");
            },
            error: function (data) {
                console.log("error")
            }
        });
    });

    // 客户下拉数据表格
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'custId',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            if (data.data.length > 0) {
                $("#custId").val(data.data[0].id);
                $("#custName").text(data.data[0].custName);
                elem.val(data.data[0].custName)
            }


        }
    });

});



var py = new PinYin();
$(document).on('blur', '#strSeller', function () {
    var src = $(this).val();
    var target = py.convert(src);
    $('#seller').val(target);
});
