<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>工资支付审批流程-复核-银企直连</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <!--add guoqian  20200904-->
    <style>
        .layui-table th {
            text-align: center;
        }
        .layui-form-label {
            width: 105px;
        }
    </style>
    <!--end guoqian 20200904-->
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="福利办理方">福利办理方:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input res" name="" id="distComName" placeholder="请点击" readonly>
                        <input type="hidden" class="layui-input res" name="payCom" id="distCom">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="申请人">申请人:</label>
                    <div class="layui-input-inline">
                        <select name="applicant" id="applicant" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="支付方式">支付方式：</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="payMethod" id="payMethod" dict_type="MODE_OF_PAYMENT" lay-filter="payMethodFilter">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="申请支付时间>=">申请支付时间</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                               name="startApplyTime"
                               id="startApplyTime" placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="最晚支付时间">最晚支付时间</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                               name="lastDate"
                               id="lastDate" placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>


                <div class="layui-inline" style="float: right;margin-right: 2%;">
                    <a class="layui-btn" id="btnQueryFilter" data-type="reload" lay-filter="btnQueryFilter"
                       lay-submit="">查询</a>
                    <button class="layui-btn" id="reset" type="reset">重置</button>
                </div>
            </div>
        </form>
    </div>

    <div class="layui-card-body">
        <table class="layui-hide" id="paymentApplyTable" lay-filter="paymentApplyFilter"></table>
        <div id="selectedSummary" style="margin-top: 10px; font-size: 20px; font-weight: normal; color: #333;">
            已选中：0 行 |
            实际支付金额总计：0.00 元 |
            抵扣金额总计：0.00 元 |
            服务费金额总计：0.00 元 |
            应付金额总计：0.00 元
        </div>
        <script type="text/jsp" id="toolbarDemo">
            <button class="layui-btn layui-btn-sm" id="disbursementSubmission" lay-event="disbursementSubmission" authURI="/bill/insurancePracticePayByCmb/disbursementSubmission">出款提交</button>
            <button class="layui-btn layui-btn-sm" id="rejectTask" lay-event="rejectTask" authURI="/workflow/rejectTask">驳回转账申请</button>
            <button class="layui-btn layui-btn-sm" id="offlineTransfer" lay-event="offlineTransfer" authURI="/bill/insurancePracticePayByCmb/offlineTransfer">线下转账</button>
        </script>
        <script type="text/jsp" id="toolDemo2">
            <a href="javascript:void(0)" title="人员查看" lay-event="detailView" authURI="/bill/insurancePracticeApproval/getInsurancePracticeApprovalDetailPage"><i class="layui-icon layui-icon-search"></i></a>
        </script>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/BigDecimal-all-last.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/format.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/insurancePracticePayByCmb/insurancePracticePayByCmbPage.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
</body>
</html>