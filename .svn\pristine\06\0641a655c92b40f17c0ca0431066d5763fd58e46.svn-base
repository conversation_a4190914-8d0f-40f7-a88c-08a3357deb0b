package com.reon.hr.sp.customer.rabbitmq.listener;

import com.reon.hr.api.base.dubbo.service.rpc.sys.IinsuranceGroupWrapperService;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IInsurancePracticeWrapperService;
import com.reon.hr.api.customer.enums.insurancePractice.InsurancePracticeEnum;
import com.reon.hr.api.customer.vo.InsurancePracticeVo;
import com.reon.hr.api.customer.vo.employee.EmpOrderEntryDimissionsProdVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.context.MqContext;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.customer.ConsumerScopeTypeCustomer;

import java.util.*;

@Component
public class SocietyInsuranceApplicationListener extends AbstractConsumerListener {

	private final static String QUEUE_NAME = "reon.society.insurance.application.enter.completed.queue";
	@Autowired
	private IinsuranceGroupWrapperService groupWrapperService;
	@Autowired
	private MqContext mqContext;
	@Autowired
	private IInsurancePracticeWrapperService iInsurancePracticeWrapperService;
	@Autowired
	IEmployeeOrderWrapperService iEmployeeOrderWrapperService;

//	/**
//	 * 待申请
//	 */
//	private static final Byte APPLICATION_PENDING = 1;
//	/**
//	 * 待办理
//	 */
//	private static final Byte HANDLE_PENDING = 2;
//	/**
//	 * 不需办理
//	 */
//	private static final Byte NO_REQUIRED = 3;
//
//	/** 退回待申请 */
//	private static final Byte RETURN_WAIT_APLICATION = 4;
//	/**
//	 * 开户失败
//	 */
//	private static final Byte FAILURE_TO_OPEN_ACCOUNT = 5;
//	/**
//	 * 在缴
//	 */
//	private static final Byte IN_PAYMENT = 6;
//	/**
//	 * 停缴
//	 */
//	private static final Byte STOP_THE_PAYMENT = 7;
//	/**
//	 * 已过期
//	 */
//	private static final Byte OUT_OF_DATE = 8;
//
//	/**
//	 * 社保组
//	 */
//	private static final Byte SOCIETY_INSURANCE = 1;
//	/**
//	 * 住房公积金
//	 */
//	private static final Byte HOUSE_FUND = 2;
//
//
//	/**
//	 * 不需办理
//	 */
//	private static Integer NO_REQUIRED_HANDLE = 1;
//
//	/**
//	 * 办理
//	 */
//	private static Integer HANDLE = 2;
//
//	/**
//	 * 退回
//	 */
//	private static Integer BACK = 3;


	@Override
	protected void doWork(String message) {
		List<String> orderNoList = JsonUtil.jsonToList(message, String.class);
		/** 根据orderNo 获取所有数据 */
		List<EmployeeOrderVo> orderVoList=iEmployeeOrderWrapperService.getSocietyInsuranceApplicationDataByOrderNo(orderNoList);
		orderVoList.forEach(item->{
			String orderNo = item.getOrderNo();
			EmpOrderEntryDimissionsProdVo result = iEmployeeOrderWrapperService.searchInsuranceCfgByOrderNoAndRatioOnly(orderNo,null);
			item.setInsurances(result.getCfgVos());
		});
		orderVoList.forEach(orderVo->{
		/**在这里的关键一步是 福利的起始月 如果是相同的产品,那么福利起始月应该是取最大的那一条
		 *  如果是不同的产品那么就是 ,也是取最大的一条
		 *  福利的截止月 --> 这个福利的截止月是 停止办理的时候填入的
		 * */
		List<OrderInsuranceCfgVo> insuranceVoList = orderVo.getInsurances();
		Set<String> groupSet = new HashSet<>();
		/**利用TreeSet取得最大值*/
		TreeSet<Integer> startMonthGroup = new TreeSet<>();
		/**获取所有groupId 放入set进行去重 ,然后获取所有的社保组,的起始时间取得最大值*/
		for (OrderInsuranceCfgVo orderInsuranceCfgVo : insuranceVoList) {
			groupSet.add(orderInsuranceCfgVo.getGroupCode());
			startMonthGroup.add(orderInsuranceCfgVo.getRevStartMonth());
		}
		List<InsurancePracticeVo> insurancePracticeList = new ArrayList<>();
		groupSet.forEach(item -> {
			InsurancePracticeVo insurancePractice = new InsurancePracticeVo();
			insurancePractice.setOrderNo(orderVo.getOrderNo());
			insurancePractice.setCustId(orderVo.getCustId());
			insurancePractice.setEmpId(orderVo.getEmployeeId());
//			insurancePractice.setGroupCode(item); /**社保组编号*/
			insurancePractice.setHandler(orderVo.getUpdater());  /**办理人*/
			insurancePractice.setProcTime(new Date());  /**办理时间*/
			/**  TODO 不准删
			 *insurancePractice.setAddMethod();新增方式,申请时插入
			 *insurancePractice.setAddMonth();     办理月,申请时插入
			 *insurancePractice.setEndMonth();福利截止月
			 *insurancePractice.setHandleParty();办理方
			 *insurancePractice.setFirstRemark();新办备注
			 * */
			/** 根据社保组编号查询这个社保组是什么 类别*/
			Integer groupType = groupWrapperService.getGroupTypeByGroupCode(item);
			/**租类别有三种
			 * 1 表示社保组
			 * 2 表示公积金组
			 * 3 表示医疗保险组
			 * */
//			if (groupType == 1 || groupType == 3) {
//				insurancePractice.setKind(SOCIETY_INSURANCE);    /**福利类型*/
//			} else if (groupType == 2) {
//				insurancePractice.setKind(HOUSE_FUND);
//			}
			insurancePractice.setStartMonth(startMonthGroup.last());/**福利起始月*/
			/**办理状态*/
			insurancePractice.setHandleStatus(InsurancePracticeEnum.HandleStatusEnum.WAIT_FOR_APPLICATION.getIndex());
			insurancePractice.setCreator(orderVo.getUpdater());
			insurancePractice.getStartMonth();
			insurancePractice.getEndMonth();
/** 根据orderNo和groupCode来判断是否存在 */
			insurancePracticeList.add(insurancePractice);
		});

/**把数据存入主表 存入主表以后 要获取到返回的主键Id然后才能存入产品办理信息,和实作操作日志,所以插入时
 * 要分别插入*/
		for (InsurancePracticeVo insurancePracticeVo : insurancePracticeList) {
//			Integer size = iInsurancePracticeWrapperService.isExist(insurancePracticeVo.getOrderNo(), insurancePracticeVo.getGroupCode());
//			if (size == 0) {
				Long practiceId =	iInsurancePracticeWrapperService.addInsurancePractice(insurancePracticeVo);
				/**把数据存入产品办理信息*/
				List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = orderVo.getInsurances();
				/** 需要进行判断是哪一个insurancePracticeId下面的数据才能进行插入 */
				orderInsuranceCfgVoList.forEach(item -> {
//					if (insurancePracticeVo.getOrderNo().equals(item.getOrderNo()) && insurancePracticeVo.getGroupCode().equals(item.getGroupCode()) && item.getProductCode() != null) {
//						ProdHandleInfoVo prodHandleInfoVo = new ProdHandleInfoVo();
//						prodHandleInfoVo.setPracticeId(practiceId);
//						prodHandleInfoVo.setOrderNo(orderVo.getOrderNo());
//						prodHandleInfoVo.setProdCode(item.getProductCode());
//						prodHandleInfoVo.setRatioCode(item.getRatioCode());
//						prodHandleInfoVo.setStartMonth(item.getRevStartMonth());
//						prodHandleInfoVo.setComBase(item.getComBase());
//						prodHandleInfoVo.setIndBase(item.getIndBase());
//						prodHandleInfoVo.setComAmt(item.getComAmt());
//						prodHandleInfoVo.setIndAmt(item.getIndAmt());
//						prodHandleInfoVo.setCreator(orderVo.getUpdater());
//						iInsurancePracticeWrapperService.addProHandleInfo(prodHandleInfoVo);
//					}
				});
//			}
		}
		});

	}

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		super.init(ModuleType.REON_APPLICATION, ConsumerScopeTypeCustomer.REON_APPLICATION, mqContext, QUEUE_NAME);
	}

}
