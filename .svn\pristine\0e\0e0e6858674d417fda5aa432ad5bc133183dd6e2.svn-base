package com.reon.hr.api.customer.vo.salary.pay;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> guoqian
 * @date 2021/2/1 0001 14:39
 * @title
 * @modify
 */
@Data
public class EmpDelayVo  implements Serializable {
      private  Long empId;
      private  Long salaryId;
      private  Long payId;
      private  Long batchId;
      private  Integer enable;//是否缓发
     private BigDecimal S006;
     private BigDecimal S007;
     private  String empName;
     private  Integer certType;
     private  String certNo;
     private  String employeeNo;
     private  String cardNo;
     private  String bankName;
     private  String custName;
      private  String categoryName;





 }
