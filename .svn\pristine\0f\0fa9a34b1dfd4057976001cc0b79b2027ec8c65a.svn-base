package com.reon.hr.api.customer.dto.customer.salary;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
@ExcelIgnoreUnannotated
public class IncomeTaxDifferencesImportDto implements Serializable {

    private Long empId;
    /**
     * 机构code
     * 岗位code
     * loginName
     */
    private List<OrgPositionDto> userOrgPositionDtoList;
    /**
     * 唯一号
     */
    @ColumnWidth(22)
    @ExcelProperty("唯一号")
    private String employeeNo;

    /**
     * 雇员名称
     */
    @ColumnWidth(10)
    @ExcelProperty("姓名")
    private String employeeName;

    /**
     * 证件号码
     */
    @ColumnWidth(22)
    @ExcelProperty("证件号码")
    private String certNo;

    /**
     * 证件类型
     */
    @ColumnWidth(10)
    @ExcelProperty("证件类型")
    private String certType;

    /**
     * 手机号码
     */
    //@Excel(name = "手机号码",width = 22)
    private String mobile;

    /**
     * 扣缴义务人
     */
    private String withholdingAgentNo;
    @ColumnWidth(26)
    @ExcelProperty("扣缴义务人")
    private String withholdingAgentName;

    /**
     * 扣缴义务人类型
     */
    @ColumnWidth(10)
    @ExcelProperty("扣缴义务人类型")
    private String withholdingAgentType;

    /**
     * 客户编号
     */
    @ColumnWidth(22)
    @ExcelProperty("客户编号")
    private String custNo;

    /**
     * 客户名称
     */
    @ColumnWidth(26)
    @ExcelProperty("客户名称")
    private String custName;
    @ColumnWidth(22)
    @ExcelProperty("合同编号")
    private String contractNo;//合同编号
    @ColumnWidth(22)
    @ExcelProperty("合同名字")
    private String contractName;//合同名字
    /**
     * 薪资发放状态
     */
    //@ColumnWidth(26)
    //@ExcelProperty("薪资发放状态")
    private String salaryInfoStatus;

    /**
     * 报税状态
     */
    //@ColumnWidth(26)
    //@ExcelProperty("报税状态")
    private String declareDutiableGoods;
    //@ColumnWidth(26)
    //@ExcelProperty("计税月")
    private Integer declarationTaxMonth;

    /**
     * 薪资类别名称
     */
    @ColumnWidth(26)
    @ExcelProperty("薪资类别名称")
    private String salaryCategoryName;

    /**
     * 发放名称
     */
    @ColumnWidth(26)
    @ExcelProperty("发放名称")
    private String salaryPayName;

    /**
     * 工资所属月
     */
    @ColumnWidth(10)
    @ExcelProperty("工资所属月")
    private String salaryMonth;

    /**
     * 工资计税月
     */
    private Integer taxMonthBeforeOne;
    private Integer taxMonthBeforeTwo;
    @ColumnWidth(10)
    @ExcelProperty("工资计税月")
    private Integer taxMonth;

    /**
     * 客户账单月
     */
    @ColumnWidth(10)
    @ExcelProperty("客户账单月")
    private String billMonth;
    /**
     * 支付年月
     */
    @ColumnWidth(10)
    @ExcelProperty("支付年月")
    private Integer paymentDate;
    @ColumnWidth(10)
    @ExcelProperty("所得项目")
    private String itemTypeStr;
    private Integer itemType;
    /**
     * 个税申报比对类型
     */
    @ColumnWidth(10)
    @ExcelProperty("个税申报比对类型")
    private String taxComparisonTypeStr;
    private Integer taxComparisonType;
    @ColumnWidth(10)
    @ExcelProperty(value = "反馈状态")
    private String feedbackStatusStr;
    private Integer feedbackStatus;
    @ColumnWidth(10)
    @ExcelProperty(value = "是否必须反馈")
    private String mustFeedbackFlagStr;
    private Integer mustFeedbackFlag;
    /**
     * 个税差异
     */
    @ColumnWidth(10)
    @ExcelProperty("个税差异")
    private BigDecimal difference;
    /**
     * 个税差异备注
     */
    @ColumnWidth(26)
    @ExcelProperty("个税差异备注")
    private String remark;
    private boolean findDifference;
    /**
     * 本期收入
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "本期收入"})
    private BigDecimal s037;
    private BigDecimal s043;
    /**
     * 收入合计
     */
    //@Excel(name = "收入合计",width = 26)
    private String s027;

    /**
     * 养老个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "养老个人"})
    private BigDecimal s031;

    /**
     * 医疗个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "医疗个人"})
    private BigDecimal s032Total;
    private BigDecimal s032;

    private BigDecimal s061;

    private BigDecimal s062;

    private BigDecimal s063;

    private BigDecimal s064;
    private String s065;
    private String s066;
    private String s067;

    /**
     * 失业个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "失业个人"})
    private BigDecimal s033;

    /**
     * 大病个人
     */

    private BigDecimal s034;

    /**
     * 公积金个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "公积金个人"})
    private BigDecimal s035;

    /**
     * 其他个人
     */
    //@Excel(name = "其他个人",width = 26)
    private String s036;

    /**
     * 累计收入
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计收入"})
    private BigDecimal s008;
    /**
     * 累计五险一金
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计五险一金"})
    private BigDecimal s011;
    private String s003;

    /**
     * 累计公积金超额计税部分
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计公积金超额计税部分"})
    private BigDecimal s047;

    /**
     * 累计基本减除费用
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计基本减除费用"})
    private BigDecimal s017;
    /**
     * 累计住房租金支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计住房租金支出扣除"})
    private BigDecimal s012;
    /**
     * 累计住房贷款利息支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计住房贷款利息支出扣除"})
    private BigDecimal s013;
    /**
     * 累计赡养老人支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计赡养老人支出扣除"})
    private BigDecimal s014;
    /**
     * 累计子女教育支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计子女教育支出扣除"})
    private BigDecimal s015;
    /**
     * 累计继续教育支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计继续教育支出扣除"})
    private BigDecimal s016;
    /**
     * 累计3岁以下婴幼儿照护
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计3岁以下婴幼儿照护"})
    private BigDecimal s039;
    /**
     * 累计其它扣除（专项）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计其它扣除（专项）"})
    private BigDecimal s041;
    /**
     * 累计个人养老金
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计个人养老金"})
    private BigDecimal s049;

    /**
     * 本次使用专项附加扣除
     */
    //@Excel(name = "本次使用专项附加扣除",width = 26)
    private String s024;

    /**
     * 累计专项附加扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计专项附加扣除"})
    private BigDecimal s018;

    /**
     * 应税工资
     */
    //@Excel(name = "应税工资",width = 26)
    private String s005;

    /**
     * 个人所得税
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "个人所得税"})
    private BigDecimal s006;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "个税调"})
    private BigDecimal s054;
    /**
     * 累计扣税（不包含当月）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "累计扣税（不包含当月）"})
    private BigDecimal s010;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线上数据", "跨年个税调"})
    private BigDecimal s068;


    /**
     * 工号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "工号"})
    private String staffNo;
    /**
     * 本期收入
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "本期收入"})
    private BigDecimal currIncome;

    /**
     * 养老个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "本期基本养老保险费"})
    private BigDecimal currEndowmentInsur;

    /**
     * 医疗个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "本期基本医疗保险费"})
    private BigDecimal currMedicalInsur;

    /**
     * 失业个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "本期失业保险费"})
    private BigDecimal currUnemployInsur;

    /**
     * 公积金个人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "本期住房公积金"})
    private BigDecimal currHousingFund;

    /**
     * 累计收入
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计收入额"})
    private BigDecimal accuIncome;
    /**
     * 累计五险一金
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计专项扣除"})
    private BigDecimal accuSpecDedu;

    /**
     * 累计基本减除费用
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计减除费用"})
    private BigDecimal accuSubFee;
    /**
     * 累计住房租金支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计住房租金支出扣除"})
    private BigDecimal accuRent;
    /**
     * 累计住房贷款利息支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计住房贷款利息支出扣除"})
    private BigDecimal accuInterest;
    /**
     * 累计赡养老人支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计赡养老人支出扣除"})
    private BigDecimal accuSupportElder;
    /**
     * 累计子女教育支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计子女教育支出扣除"})
    private BigDecimal accuChildEdu;
    /**
     * 累计继续教育支出扣除
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计继续教育支出扣除"})
    private BigDecimal accuContiEdu;
    /**
     * 累计3岁以下婴幼儿照护
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计3岁以下婴幼儿照护"})
    private BigDecimal accuBabyCare;
    /**
     * 累计其它扣除（专项）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计其它扣除（专项）"})
    private BigDecimal accuOtherDedu;
    /**
     * 累计个人养老金
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "累计个人养老金"})
    private BigDecimal accuPersonalPension;



    /**
     * 申报数据个税
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据", "应补(退)税额"})
    private BigDecimal accuAddTax;

    /**
     * 个人所得税-申报数据个税
     */
    private String s006_accuAddTax;

    /**
     * 实发合计
     */
    //@Excel(name = "实发合计",width = 26)
    private String s007;



    @ColumnWidth(15)
    @ExcelProperty(value = {"线下数据","已缴税额"})
    private BigDecimal accuAddedTax;//已缴税额

    private String accuDeduTax;//累计应扣缴税额
    /**
     * 税率表
     */
    private String taxListId;
    private Integer laborWagesType;
    //@Excel(name = "税率表",width = 26)
    private String taxListName;

    /**
     * 派单方
     */
    @ColumnWidth(26)
    @ExcelProperty("派单方")
    private String distCom;

    /**
     * 派单方客服
     */
    @ColumnWidth(15)
    @ExcelProperty("派单方客服")
    private String distComMan;

    /**
     * 薪资客服
     */
    @ColumnWidth(15)
    @ExcelProperty("薪资客服")
    private String salaryCommissioner;

    @ColumnWidth(15)
    @ExcelProperty("薪资创建人")
    private String salaryCreator;
    /**
     * 工资批次明细id
     */
    private String salaryBatchDetailIds;
    /**
     * 线下数据id
     */
    private String offlineDataIds;
    private Integer salaryComparisonType;






    private String empExcelAdd;
    private String indTaxApplyInfo;
    private String salaryCommOrg;
    private String salaryCommPos;

}
