<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <title>查看</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>

    <style type="text/css">

        .layui-inline{
            padding-top: 20px;
            padding-left: 20px;
        }
        .layui-elip {
            width: 120px;
        }
        .layui-tab-card {
            border-style: unset;
            box-shadow: unset;
        }
        .layui-table-view .layui-form-checkbox[lay-skin=primary] i {
            margin-top: unset !important;
        }
        .layui-input-inline {
            width: 180px;
        }

        .layui-table-box {
            overflow: visible;
        }

        .layui-table-body {
            overflow: visible;
        }

        /* 设置下拉框的高度与表格单元相同 */
        td .layui-form-select{
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
        }
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .tableClass .layui-table-cell {
            overflow: visible;
        }

        .tableClass .layui-form-select {
            position: initial;
        }

        .tableClass .layui-table-box {
            overflow: visible;
        }

        .tableClass .layui-table-body {
            overflow: visible;
        }





    </style>
</head>
<body class="childrenBody">
<div class="layui-tab layui-tab-card" style="margin-top: 0px" lay-filter="contractTabFilter">
    <ul class="layui-tab-title">
        <li class="layui-this">基本信息</li>

        <li>流程信息</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <form class="layui-form" method="post">
                <%--隐藏域--%>
                <input type="hidden" id="id" name="id" value="${id}">
                <input type="hidden" id="pid" name="pid" value="${pid}">
                <input type="hidden" id="taskId" name="taskId" value="${taskId}">
                <input type="hidden" id="type" name="type" value="${type}">
                <input type="hidden" id="optType" name="optType" value="query">
                <%--                    如果是 流程审批,则赋值true  默认为false--%>
                <input type="hidden" id="approveTip" value="false"/>
                <%--表单元素--%>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="合同名称："><i
                                style="color: red">*</i>合同名称：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="contractName" name="contractName"  placeholder="请填入"
                                   lay-verify="required" autocomplete="off" >
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="合同编号："><i
                                style="color: red">*</i>合同编号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="contractNo" name="contractNo"  placeholder="请填入"
                                   lay-verify="required" autocomplete="off" >
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="供应商："><i style="color: red">*</i>供应商：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="supplierName" id="supplierName" placeholder="请输入" autocomplete="off"
                                   class="layui-input" lay-verify="" lay-verType="tips" readonly />
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="供应商类型："><i style="color: red">*</i>供应商类型：</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" name="supplierType" id="supplierType" placeholder="请选择" lay-verify="" DICT_TYPE="SUPPLIER_TYPE"
                                    autocomplete="off"  disabled>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="供应商类型："><i style="color: red">*</i>合同类型：</label>
                        <div class="layui-input-inline" style="width: 180px">
                            <select class="layui-select" name="contractType" id="contractType" lay-verify="" DICT_TYPE="CONTRACT_TYPE" lay-Filter="supplierTypeFilter" disabled>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="供应商子类型："><i
                                style="color: red"></i>产品类型：</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" name="prodType" id="prodType"  lay-verify="" DICT_TYPE="CONTRACT_PRDO_TYPE" disabled
                                    autocomplete="off" >
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="我方签约公司："><i
                                style="color: red">*</i>我方签约公司：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="orgCode" id="orgCode" placeholder="请输入"
                                   autocomplete="off" class="layui-input" lay-verify="" lay-verType="tips"
                                   readonly/>
                            </select>

                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="合同开始日期："><i
                                style="color: red">*</i>合同开始日期：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="startTime" name="startTime" placeholder="请选择" lay-verify="required"
                                   autocomplete="off" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="合同结束日期："><i
                                style="color: red">*</i>合同结束日期：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="endTime" name="endTime" placeholder="请选择" lay-verify="required"
                                   autocomplete="off" readonly>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="账单日："><i style="color: red">*</i>账单日：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="billDate" name="billDate" placeholder="请选择" lay-verify="required"
                                   autocomplete="off" readonly>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="付款日："><i style="color: red">*</i>付款日：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="payDate" name="payDate" placeholder="请选择" lay-verify="required"
                                   autocomplete="off" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="是否自动顺延："><i
                                style="color: red">*</i>是否自动顺延：</label>
                        <div class="layui-input-inline" style="width: 180px">
                            <select class="layui-select" name="postponeFlag" id="postponeFlag" dict_type="BOOLEAN_TYPE" disabled>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="是否关联公司："><i
                                style="color: red">*</i>是否关联公司：</label>
                        <div class="layui-input-inline" style="width: 180px">
                            <select class="layui-select" name="relatedCompFlag" id="relatedCompFlag"  dict_type="BOOLEAN_TYPE" disabled lay-filter="relatedCompFlag"
                                    autocomplete="off" >
                                <option value=""></option>
                            </select>
                        </div>
                    </div>


                    <div style="padding-left: 80px;padding-top: 20px" >
                        <fieldset class="layui-elem-field layui-field-title">
                            <legend>关联公司</legend>
                        </fieldset>
                        <div id="jobTypeDiv" style="padding-left: 50px">

                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="备注">备注：</label>
                        <div class="layui-input-inline" style="width: 920px;">
                            <textarea placeholder="请输入内容" name="remark" id="remark" class="layui-textarea"
                                      autocomplete="off"
                            ></textarea>
                        </div>
                    </div>
                    <div style="padding-left: 80px;padding-top: 20px">
                        <fieldset class="layui-elem-field layui-field-title">
                            <legend>文件展示</legend>
                        </fieldset>
                        <div class="layui-upload" style="padding-left: 50px">

                            <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                                预览图：
                                <div class="layui-upload-list" id="upload"></div>
                            </blockquote>
                        </div>
                    </div>

                    <div style="padding-left: 80px;padding-top: 20px">
                            <fieldset class="layui-elem-field layui-field-title">
                                <legend>报价单</legend>
                            </fieldset>
                            <div class="layui-input-block" style="width: 1500px;max-height: 500px;overflow: auto">
                                <table id="bindingGridTable" lay-filter="bindingGridTableFilter"></table>
                            </div>
                    </div>
                    <div style="padding-left: 80px;padding-top: 20px">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>审批意见</legend>
                    </fieldset>
                    <div class="layui-input-block" style="width: 1166px;">
                        <textarea placeholder="请输入内容" name="comment" id="comment" class="layui-textarea"
                                  style="min-width: 55px"></textarea>
                    </div>

                </div>
                <div style="margin-left:60%;margin-bottom: 10px;">
                    <button class="layui-btn layui-btn-lg layui-btn-primary" type="button" style="margin-top: 10px;margin-bottom: 10px;" id="submitProc">提交</button>
                    <button class="layui-btn layui-btn-lg layui-btn-primary" type="button" style="margin-top: 10px;margin-bottom: 10px;" id="rejectProc">驳回</button>
                    <button class="layui-btn layui-btn-primary cancel" type="button" id="cancel" style="margin-top: 10px;margin-bottom: 10px;">关闭</button>
                </div>
            </form>
        </div>
        <%--流程信息--%>
        <div class="layui-tab-item">
            <img id="workFlowImg" src=""/>
            <div class="layui-form-item layui-hide" style="margin-top: 15px;" id="currentItem">
                <label class="layui-form-label filed-length" id="currentApproverName" ></label>
                <div class="layui-input-inline">
                    <input type="text" id="currentApproverAssignee"
                           readonly 
                           class="layui-input layui-anim layui-anim-scaleSpring" value=""/>
                </div>
            </div>
            <table class="layui-table" id="contractFlowTable" lay-filter="contractFlowTable"></table>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/js/xm-select.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/supplier/contract/approvalSupplierContract.js?v=${publishVersion}"></script>
</body>
</html>
