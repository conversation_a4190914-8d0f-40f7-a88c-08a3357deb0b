package com.reon.hr.api.customer.enums.quotation;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 报价单 产品子类 转换相匹配的合同类型
 */
@AllArgsConstructor
@Getter
public enum SubType {

    SUPPLEMENTARY_MEDICAL_INSURANCE(16,3,"补充医疗保险"),
    ACCIDENT_INSURANCE(10,2,"意外保险"),
    EMPLOYER_LIABILITY_INSURANCE(6,1,"雇主责任险");

    private int ContractCode;
    private int subCode;
    private String name;

    public static final Map<Integer, Integer> map = new HashMap<>();
    static {

        for (SubType value :values()){
            map.put(value.getContractCode(),value.getSubCode());
        }
    }

    public static Integer getValue(Integer contractCode){
        return map.get(contractCode);
    }


}
