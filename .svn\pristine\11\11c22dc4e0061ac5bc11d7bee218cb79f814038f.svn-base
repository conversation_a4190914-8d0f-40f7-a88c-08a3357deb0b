package com.reon.hr.api.vo.sys;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ResourceVO implements Serializable{
    /**
     */
    private static final long serialVersionUID = 1L;

    private String resourceNo;

    private String resourceName;

    private String resourceStatus;

    private String resourceUrl;

    private Integer resourceSort;

    private String resourcePid;

    private String resourceType;

    private String icon;

    private Integer resourceDefaultType;

    public Integer getResourceDefaultType() {
        return resourceDefaultType;
    }

    public void setResourceDefaultType(Integer resourceDefaultType) {
        this.resourceDefaultType = resourceDefaultType;
    }

    private Boolean checked = false; //是否选中，如果角色拥有该资源则为true

    private List<ResourceVO> children = new ArrayList<ResourceVO>();

    private Map<String,String> checkArr= Maps.newHashMap();

    public String getResourceNo() {
        return resourceNo;
    }

    public void setResourceNo(String resourceNo) {
        this.resourceNo = resourceNo == null ? null : resourceNo.trim();
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    public String getResourceStatus() {
        return resourceStatus;
    }

    public void setResourceStatus(String resourceStatus) {
        this.resourceStatus = resourceStatus == null ? null : resourceStatus.trim();
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl == null ? null : resourceUrl.trim();
    }

    public Integer getResourceSort() {
        return resourceSort;
    }

    public void setResourceSort(Integer resourceSort) {
        this.resourceSort = resourceSort;
    }

    public String getResourcePid() {
        return resourcePid;
    }

    public void setResourcePid(String resourcePid) {
        this.resourcePid = resourcePid == null ? null : resourcePid.trim();
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }


    public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public List<ResourceVO> getChildren() {
        return children;
    }
    public void setChildren(List<ResourceVO> children) {
        this.children = children;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public Map<String, String> getCheckArr() {
        return checkArr;
    }

    public void setCheckArr(Map<String, String> checkArr) {
        this.checkArr = checkArr;
    }

    //此方法toString不能动【重要】
	@Override
	public String toString() {
		return "{'rid':'" + resourceNo + "', 'rname':'" + resourceName + "', 'url':'" + resourceUrl + "', 'pid':'" + resourcePid + "', 'icon':'" + icon + "'}";
	}

}
