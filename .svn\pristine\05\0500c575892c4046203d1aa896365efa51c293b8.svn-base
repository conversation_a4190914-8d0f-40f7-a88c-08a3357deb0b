package com.reon.hr.api.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 临时政策(TemporaryPolicy)实体类
 *
 * <AUTHOR>
 * @since 2025-03-05 11:09:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TemporaryPolicyVo implements Serializable {
    private static final long serialVersionUID = 702700084105306020L;

    private Long id;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 服务网点(自有公司或供应商)
     */
    private String serviceSiteCode;

    private String serviceSiteName;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 展示位置(1:reon, 2:reon+客户端)
     */
    private Integer location;
    /**
     * 状态(1:启用, 2:停用)
     */
    private Integer status;
    /**
     * 审批状态(1:审批中, 2:通过, 3:驳回)
     */
    private Integer approvalStatus;
    /**
     * 审批人机构
     */
    private String approvalOrg;
    /**
     * 审批人岗位
     */
    private String approvalPos;
    /**
     * 实际审批人
     */
    private String approvalPerson;
    /**
     * 审批备注
     */
    private String approvalRemark;

    /**
     * 发布时间
     */
    private Date publishTime;
    /**
     * 审批时间
     */
    private Date approvalTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    /**
     * 一人多个区域接单总监岗位
     */
    private List<String> orgList;

    /**
     * 1.reon首页查询已审批且启用的,
     * 基础数据包临时政策页面
     * 2.查询所有,
     * 3.查询我审批的,
     * 4.ehr首页查询已审批且启用的,且对应todo
     */
    private Integer showType;

    /**
     * 是否我的审批 1:是, 2:否
     */
    private Integer myApprovalFlag;

    private List<TemporaryPolicyVo> temporaryPolicyVos;

    /**
     * 读取标志
     * 1:未读,2已读
     */
    private Integer readFlag;

    private Date readAt;

    private Long userId;

    private Integer type;
}

