package com.reon.hr.sp.base.dao.sys;

import com.reon.hr.api.base.vo.InsurancePracticeServiceConfigLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月21日
 * @Version 1.0
 */
public interface InsurancePracticeServiceConfigLogMapper {

    int addInsurancePracticeServiceConfigLog(InsurancePracticeServiceConfigLogVo insurancePracticeServiceConfigLogVo);

    List<InsurancePracticeServiceConfigLogVo> getLogByConfigId(@Param("confId") Long confId);
}
