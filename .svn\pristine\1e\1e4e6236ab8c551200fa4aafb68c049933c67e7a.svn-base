/****************************************
 * Copyright (c) 2017 LYF.
 * All rights reserved.
 * Created on 2017年10月5日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.api.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @title sql工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2017年10月5日
 */
public class SQLUtils {

	public final static String ESCAPE_STR="\\\\";
	/**
	 * 替换转义字符
	 */
	public static String replaceAll(String value) {
		if (!isEmpty(value)) {
			value = value.trim();
		} else {
			return null;
		}
		value = value.replaceAll("_", ESCAPE_STR+"_");
		value = value.replaceAll("%", ESCAPE_STR+"%");
		return value;
	}
	
	/**
	 * 前置匹配
	 * 
	 * @param value
	 * @return
	 */
	public static String getLikeStart(String value) {
		value = replaceAll(value);
		if (value==null){
			return null;
		}
		return "%" + value;
	}

	/**
	 * 后置匹配
	 * 
	 * @param value
	 * @return
	 */
	public static String getLikeEnd(String value) {
		value = replaceAll(value);
		if (value==null){
			return null;
		}
		return value + "%";
	}

	/**
	 * 前后都匹配
	 * 
	 * @param value
	 * @return
	 */
	public static String getLikeContain(String value) {
		value = replaceAll(value);
		if (value==null){
			return null;
		}
		return "%" + value + "%";
	}
	
	
	public static boolean isEmpty(String value) {
		  return StringUtils.isEmpty(value);
    }
}
