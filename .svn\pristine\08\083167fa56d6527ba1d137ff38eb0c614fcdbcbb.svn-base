/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/9/18
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.customer.dto.customer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CustomerSolutionDTO
 *
 * @date 2020/9/18 13:34
 */
@Data
public class CustomerSolutionDTO {
	private Long id;

	private Long custId;

	private Long supplierId;

	private String solutionNo;

	private String solutionName;

	private Byte payMethod;

	private Byte prodType;

	private BigDecimal cost;

	private BigDecimal price;

	private String creator;

	private Date createTime;

	private String updater;

	private Date updateTime;

	private String delFlag;
	//状态
	private Byte status;
}
