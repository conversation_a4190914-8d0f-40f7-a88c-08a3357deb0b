layui.use(['jquery','form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ =layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
    $.ajax({
        url: ML.contextPath + "/sys/org/getTransactionSupplier",
        type: 'GET',
        dataType: 'json',
        contentType: 'application/json',
        success: function (result) {
            $("#orgCode").empty();
            $("<option value=''></option>").appendTo("#orgCode")
            if (result==null||result=="") {
                layer.msg("该城市下没有办理方！")
            }
            for (var i in result) {
                var obj = result[i];
                $("<option value=" + obj.orgCode + ">" + obj.orgName + "</option>").appendTo("#orgCode")
            }
            form.render();
        }, error: function (result) {
            layer.msg("错误!");
        }
    })
    table.render({
        id: 'id',
        elem: '#comAcctInfoTable'
        ,url: ML.contextPath + '/sys/comAcctInfo/getListPage'
        ,page :true
        ,toolbar: '#topbtn'
        ,defaultToolbar:[]
        ,where: {"paramData": JSON.stringify(serialize("searchForm"))}
        ,limit:50
        ,height:600
        ,method: 'POST'
        ,limits: [50,100,200]
        ,text: {
            none: '暂无数据,请检索' //无数据时展示
        }
        ,cols :[[
            {field: '',type:'checkbox',width:'2%'}
            ,{field: 'orgCodeName',title:'办理方',width:'6%',align:'center'}
            ,{field: 'acctType',title:'账号类型',width:'7%',align:'center',
                templet: function (d) {
                    return ML.dictFormatter("ACCT_TYPE", d.acctType);
                }}
            ,{field: 'groupCodeName',title:'社保组',width:'7%',align:'center'}
            ,{field: 'acctNo',title:'账号',width:'7%',align:'center'}
            ,{field: 'bankName',title:'银行',width:'6%',align:'center'}
            ,{field: 'openBank',title:'开户银行',width:'7%',align:'center'}
            ,{field: 'openName',title:'开户名',width:'6%',align:'center'}
            ,{field: 'bankAcctNo',title:'开户账号',width:'6%',align:'center'}
            ,{field: 'provinceName',title:'省份',width:'6%',align:'center'}
            ,{field: 'cityCodeName',title:'城市',width:'6%',align:'center'}
            ,{field: 'remark',title:'备注',width:'9%',align:'center'}
            ,{field: 'lastDay',title:'约定支付最后日',width:'9%',align:'center'}
            ,{field: 'creator',title:'创建人',width:'7%',align:'center', templet: function (d) {
                    return ML.loginNameFormater( d.creator);
                }}
            ,{field: 'createTime',title:'创建时间',width:'7%',align:'center'}
            ,{field: 'updater',title:'修改人',width:'7%',align:'center', templet: function (d) {
                    return ML.loginNameFormater( d.updater);
                }}
            ,{field: 'updateTime',title:'修改时间',width:'7%',align:'center'}
        ]],
        done:function () {
            ML.hideNoAuth();
        }
    });
    table.on('toolbar(comAcctInfoTable)', function(obj){
        var checkStatus = table.checkStatus('id');
        switch(obj.event){
            case 'del':
                if ( obj.event == 'del' && checkStatus.data.length == 0){
                    return layer.msg("未选中删除行");
                }
                if (checkStatus.data.length >= 1) {
                    var arr=[];
                    $.each(checkStatus.data,function (index,item) {
                        arr.push(item.id);
                    });
                    delBatch(JSON.stringify(arr));
                }
                break;
        }
    });

    table.on('tool(comAcctInfoTable)',function (obj) {
        switch (obj.event) {
            case 'del':
                var  arr = [];
                arr.push(obj.data.id);
                delBatch(JSON.stringify(arr));
                break;
        }
    });


    var active = {
        reload: function () {
            table.reload('id', {
                where: {
                    paramData: JSON.stringify(serialize("searchForm")),
                    // page: 1 //重新从第 1 页开始
                }
            });
        }
    };
    $('#btnQuery').on('click', function () {
        var data = $("#orgCode option:selected").val();
        if(data==null||data==''){
            layer.msg("请选择办理方！");
            return false;
        }
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });


    function delBatch(ids) {
        layer.confirm('确定删除？', {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "DELETE",
                url: ML.contextPath + "/sys/comAcctInfo/delList?ids="+ids,
                dataType: "json",
                success: function(data){
                    if(data){
                        layer.msg("删除成功!",{icon: 1});
                    }else {
                        layer.msg("删除失败!",{icon: 5});
                    }
                    reloadTable();
                },
                error:function (data) {
                    layer.msg("删除失败!",{icon:5})
                }
            });

        });
    }


    //重载数据
    function reloadTable() {
        table.reload('id', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }
});
