package com.reon.hr.api.vo;

import java.util.LinkedHashMap;

public class JsonResult<T> extends LinkedHashMap<String, Object> {
    private static final long serialVersionUID = 3140355778147572974L;

    private final static String K_SUCCESS = "success";
    private final static String K_MSG = "msg";
    private final static String K_DATA = "data";

    // 消息类型：info、warning、error
    private final static String K_MSG_TYPE = "msgType";
    //消息对应编码（新增,可不传）
    private final static String K_CODE = "code";

    public JsonResult() {
    }

    public JsonResult(boolean success) {
        this.put(K_SUCCESS, success);
    }

    public JsonResult(boolean success, String msg) {
        this.put(K_SUCCESS, success);
        this.put(K_MSG, msg);
    }

    public JsonResult(boolean success, String msg, T data) {
        this.put(K_SUCCESS, success);
        this.put(K_MSG, msg);
        this.put(K_DATA, data);
    }

    public JsonResult(int code, String msg) {
        this("error", code, msg);
    }

    public JsonResult(String msgType, int code, String msg) {
        this.put(K_SUCCESS, false);
        this.put(K_MSG_TYPE, msgType);
        this.put(K_CODE, code);
        this.put(K_MSG, msg);
    }

    public boolean isSuccess() {
        Boolean success = (Boolean) this.get(K_SUCCESS);
        return success != null && success;
    }

    public void setSuccess(boolean success) {
        this.put(K_SUCCESS, success);
    }

    public String getMsg() {
        return (String) this.get(K_MSG);
    }

    public void setMsg(String msg) {
        this.put(K_MSG, msg);
    }

    @SuppressWarnings("unchecked")
    public T getData() {
        return (T) this.get(K_DATA);
    }

    public void setData(T data) {
        this.put(K_DATA, data);
    }

    public String getMsgType() {
        return (String) this.get(K_MSG_TYPE);
    }

    public void setMsgType(String msgType) {
        this.put(K_MSG_TYPE, msgType);
    }

    public int getCode() {
        return (int) this.get(K_CODE);
    }

    public void setCode(int code) {
        this.put(K_CODE, code);
    }
}
