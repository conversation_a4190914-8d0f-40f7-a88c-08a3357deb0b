<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>历史信息查看</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input {
            padding-right: 30px;
        !important;
        }

        .layui-table-cell {
            padding: 0px;
        }
    </style>
</head>
<body class="childrenBody">
<input type="hidden" name="importNo" id="importNo" value="${id}">
<%--startTable--%>
<table class="layui-hide" id="batchImportHistoryGrid" lay-filter="batchImportHistoryGridTable"></table>
<%--endTable--%>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="export" lay-event="export"
                authURI="/customer/batchImport/exportTemplate">导出数据
        </button>
    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/commInsurOrder/commInsurImportedHistoryQuery.js?v=${publishVersion}"></script>
</body>
</html>
