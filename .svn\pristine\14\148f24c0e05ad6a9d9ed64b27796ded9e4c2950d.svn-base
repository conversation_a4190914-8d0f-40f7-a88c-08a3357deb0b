var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


    //查出属于哪个公司 TODO(未完成)
    $(document).ready(function () {
        // $.ajax({
        //     type:"GET",
        //     url:ctx+"/bill/paymentApply/findOrgById",
        //     dataType:'json',
        //     param:"",
        //     success:function (data) {
        //         org = data.data;
        //     }
        // });
        getCityAndOrgCodeMapByLoginName()

        var payComList = [];
        $.ajax({
            type: "GET",
            url: ctx + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                payComList = [];
                payComList = data.data;
                form.render('select');
            },
            error: function (data) {
                console.log("error");
            }
        });
    });


    /*监听document的回车操作*/
    $(document).bind('keypress', function (event) {
        if (event.keyCode === 13) {
            reloadTable();
        }
    });

    //日期范围
    var startApplyTime = laydate.render({
        elem: '#startApplyTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endApplyTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endApplyTime = laydate.render({
        elem: '#endApplyTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startApplyTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    //日期范围
    var startPassTime = laydate.render({
        elem: '#startPassTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endPassTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endPassTime = laydate.render({
        elem: '#endPassTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startPassTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });


    //日期点击事件
    var initYear;
    laydate.render({
        elem: '#payMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#payMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });


    form.on('submit(btnQuery)', function (data) {

        table.reload('insurancePracticeApprovalGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });


    //查询实做支付数据
    function getSocialPaymentApplyInfo(params) {
        table.render({
            id: 'insurancePracticeApprovalGrid',
            elem: '#insurancePracticeApprovalGrid',
            url: ML.contextPath + '/bill/insurancePracticeApproval/getInsurancePracticeApprovalPage',
            where: params,
            method: 'get',
            page: true, //默认为不开启
            limits: [50, 100, 200],
            defaultToolbar: [],
            height: '600',
            toolbar: '#toolbarDemo',
            limit: 50,
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'checkbox', fixed: 'left', align: 'center'},
                {type:'numbers',align:'center', fixed: 'left'},
                // {field: 'payKind', title: '支付大类',  width: '15%',templet: function (d) {
                //         if(d.payKind){
                //             return ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", d.payKind);
                //         }else{
                //             return "无数据";
                //         }}},
                {field: 'payType', title: '支付类型',align:'center', width: '5%',templet:function(d){
                        if(d.payType){
                            return ML.dictFormatter("PAYMENT_SUBCLASS",d.payType);
                        }else{
                            return "无数据";
                        }}},
                {field: 'payMethod', title: '支付方式',align:'center',width: '10%',templet:function(d){
                        if(d.payMethod){
                            return ML.dictFormatter('MODE_OF_PAYMENT',d.payMethod);
                        }else{
                            return "无数据"}}},
                {field: 'payComName', title: '福利办理方',align:'center',width: '10%'},
                {field: 'payAmt', title: '应付金额',align:'center',width: '10%'},
                {field: 'applyAmt', title: '申请金额',align:'center',width: '10%'},
                {field: 'actPayAmt', title: '实际支付金额',align:'center',width: '10%'},
                {field: 'balanceAmt', title: '接单地账面盈余',align:'center',width: '12%'},
                {field: 'payMonth', title: '支付所属年月',align:'center',width: '10%'},
                {field: 'applicant', title: '申请人',align:'center',width: '10%', templet: function (d) {
                        return ML.loginNameFormater(d.applicant)
                    }},
                {field: 'applyTime', title: '申请时间',align:'center',width: '10%'},

                {field: '', title: '操作', toolbar: '#tooltask', align:'center',width: '5%', fixed: 'right'},
               // {field: 'noUpload', title: '操作', toolbar: '#noUpload', align:'center',width: '5%', fixed: 'right'},
                {field: 'upload', title: '操作', toolbar: '#upload', align:'center',width: '5%', fixed: 'right'},
                {field: '', title: '查看人员明细', toolbar: '#toolDemo2', align:'center',width: '10%', fixed: 'right'},
                {field: 'appCom' ,align:'center',title:'申请人分公司Code'}
            ]],
            done: function (res) {
                let flag =false;
                res.data.forEach(function (item) {
                    if(item.name == '客服上传凭证'){
                        flag = true;
                    }
                })
              
                if (!flag) {
                   // $("[data-field='noUpload']").css('display', 'none');
                    $("[data-field='upload']").css('display', 'none');
                }
                $("[data-field='appCom']").css('display','none');
                ML.hideNoAuth();
                table.on('toolbar(insurancePracticeApprovalGridFilter)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id), checkData = checkStatus.data;
                    switch (obj.event) {
                        case 'batchApproval':
                            if (checkData.length===0){
                                layer.msg('请选择要批量审批的数据数据!');
                                return false
                            }else{
                                let taskIdList = [];
                                checkData.forEach(function (item) {
                                    if(item.name == '发起支付申请'){
                                        layer.msg('请选择已经在审批流程中的数据!!');
                                        return false;
                                    }
                                    taskIdList.push(item.id)
                                })
                                ML.ajax("/workflow/batchSubmitTaskPayment", {"taskIdList": JSON.stringify(taskIdList)}, function (res) {
                                    layer.msg(res.msg);
                                    reloadTable();
                                });
                            }
                            console.log(checkStatus)
                            break;
                    }
                });
            }
        });
    }




    //监听工具条
    table.on('tool(insurancePracticeApprovalGridFilter)', function (obj) {
        var data = obj.data;
        if (obj.event === 'approval') {
            let flag = true;
            ML.ajax("/workflow/getWorkflowAuditLogList",{"pid":data.processInstanceId},function (res) {
                var commentData = res.data;
                commentData.forEach(function (item) {
                    if(item.comment == "实做支付转账数据错误"){
                        flag = false;
                    }
                })
            },'GET',false);
            if (!flag){
                layer.msg("此数据不允许重新提交，请联系发起人终止！")
                return false;
            }
            ML.ajax('/workflow/getApprovalInfoId', {
                'pid': data.processInstanceId,
                'processType': data.processDefinitionKey
            }, function (res) {
                if (data.processDefinitionKey == 'payment_apply') {
                    dealContractRenew(data, res);
                }
            }, 'GET')
        } else if (obj.event === 'updateFinalFile') {
            ML.ajax('/bill/oneFee/checkIsImportDifferences', {
                'paymentId': data.paymentApplyId,
            }, function (res) {
                if (res.code==0){
                    ML.ajax('/workflow/getApprovalInfoId', {
                        'pid': data.processInstanceId,
                        'processType': data.processDefinitionKey
                    }, function (res) {
                        dealUploadFile(data, res);
                    }, 'GET')
                }else {
                    layer.msg(res.msg);
                }
            }, 'GET')

        }else if (obj.event === 'detailView') {
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                title: '查看',
                area: ['60%', '85%'],
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ML.contextPath + '/bill/insurancePracticeApproval/gotoInsurancePracticeApprovalDetailPage',
                success: function (layero, index) {
                    var body = layer.getChildFrame('body', index);
                    body.find("#pid").val(data.processInstanceId);
                    body.find("#payCom").val(data.payCom);
                    body.find("#orgCode").val(data.payCom);
                },
                end: function () {
                    reloadTable();
                }
            });
        }else if (obj.event === 'noUpload') {
            ML.ajax('/workflow/getApprovalInfoId', {
                'pid': data.processInstanceId,
                'processType': data.processDefinitionKey
            }, function (res) {
                dealUploadFile(data, res);
            }, 'GET')
        }else if (obj.event === 'upload') {
            layer.open({
                type: 2,
                title: '导入差异数据',
                area: ['85%', '85%'],
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ML.contextPath + '/bill/oneFee/gotoImportDifferencesPage?paymentId=' +data.paymentApplyId,
                success: function (layero, index) {

                },
                end: function () {
                    reloadTable();
                }
            });
        }
    });

    function dealContractRenew(data, res) {
        var gotoPage = '';
        var titleStr = '';
        if (data.name == '发起支付申请') {
            gotoPage = 'gotoEditPaymentApplyApprovalPage';
            titleStr = '编辑提交';
        } else {
            gotoPage = 'gotoPaymentApplyApprovalPage';
            titleStr = '审批处理';
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看',
            area: ['60%', '85%'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/bill/paymentApply/' + gotoPage + '?id=' + res.data.id + '&taskId=' + data.id +'&pid=' + data.processInstanceId,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                // body.find("#posCode").val(window.top['userOrgPosPool'][0].posCode + ',' + window.top['userOrgPosPool'][0].orgCode);
                body.find("#flg,#approvalDiv").show();
                body.find("#appName").val(data.name);
            },
            end: function () {
                reloadTable();
            }
        });
    }

    function dealUploadFile(data,res) {
        var gotoPage = '/bill/paymentApply/gotoPaymentApplyApprovalUploadPage';
        var titleStr = '客服上传凭证';
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: titleStr,
            area: ['60%', '85%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + gotoPage+ '?id=' + res.data.id + '&taskId=' + data.id +'&pid=' + data.processInstanceId,
            end: function () {
                location.reload();
            }
        });
    }



    //重载数据
    function reloadTable() {
        table.reload('insurancePracticeApprovalGrid', {
            where: serialize("searchForm"),
        });
    }


    let orgCodes = [];
    //用于过滤是否单利户
    let packArr = [];
    //用于过滤是否单利户
    let packFilterCust = [];
    let orgCodeAndPackNameListMap = {}
    let orgCodeArray = [];

    function getCityAndOrgCodeMapByLoginName() {
        let statusStr = $('#statusStr').val();
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/insurancePractice/societyInsurance/getOrgCodeAndPackName?statusStr=" + statusStr,
            dataType: 'json',
            success: function (data) {
                orgCodeAndPackNameListMap = data.orgCodeAndPackNameListMap;
                orgCodeArray = data.orgVoList;
                if (ML.isNotEmpty(orgCodeArray)) {
                    initOrgCode(orgCodeArray);
                }
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    function initOrgCode(orgCodeArray) {
        $("#payCom").append($("<option/>"));
        $.each(orgCodeArray, function (i, item) {
            $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
            orgCodes.push(item.orgCode)
        });
        let params = serialize("searchForm");
        getSocialPaymentApplyInfo(params);

        initPackName(orgCodeArray[0].orgCode);
        form.render("select")
    }


    form.on("select(payComFilter)", function (data) {
        initPackName(data.value);
    });


    function renderPack(packs) {
        let singleFlag = $('#singleFlag').val();
        $.each(packs, function (i, item) {
            let name = item.packName;
            if (item.status == 2) {
                name = name + "（已暂停）";
            }
            if (ML.isNotEmpty(singleFlag)) {
                if (item.singleFlag == singleFlag) {
                    $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
                }
            } else {
                $("#packCode").append($("<option/>")).append($("<option/>").text(name).attr("value", item.packCode));
            }
        });
        form.render("select")
    }

    function initPackName(value) {
        $("#packCode").empty();
        clearCustomerAndSingleFlag();
        for (let orgCode in orgCodeAndPackNameListMap) {
            if (value === orgCode) {
                dataGrop = orgCodeAndPackNameListMap[value];
                packArr = dataGrop;
                packFilterCust = dataGrop;
                $("#packCode").append($("<option/>"));
                renderPack(dataGrop);
            }
        }
        $("#packCode").val('');
        form.render("select")
    }

    form.on("select(singleFlagFilter)", function (data) {
        $("#packCode").find("option:selected").text("");
        $("#packCode").empty();
        let targetPack = packArr.filter(pack => pack.singleFlag + "" === data.value);
        renderPack(targetPack);
        $("#custName").removeAttr("disabled");

    })

    //选择福利包如果是单立户带出客户
    form.on("select(packCodeFilter)", function (data) {
        if (ML.isEmpty(data.value)) {
            clearCustomerAndSingleFlag();
            form.render('select');
            return;
        }
        let singleFlag;
        let findPack = dataGrop.filter(item => item.packCode === data.value);
        if (findPack[0] && findPack[0].singleFlag === 2) {
            $('#custName').val(findPack[0].custName);
            $('#custId').val(findPack[0].custId);
            singleFlag = 2;
        }
        $('#singleFlag').find("option[value='" + singleFlag + "']").prop('selected', true);//选中value="2"的option
        form.render('select');
    })

    function clearCustomerAndSingleFlag() {
        $("#custName").val("");
        $("#custId").val("");
    }


});