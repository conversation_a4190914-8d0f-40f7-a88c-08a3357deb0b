package com.reon.hr.modules.bill.controller.insurancePractice;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IDictionaryWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.utils.ListPageUtil;
import com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice.IInsurancePracticeOneFeeWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.enums.OneFeeApproveStatusEnum;
import com.reon.hr.api.bill.listener.InsurancePracticeDifferenceListener;
import com.reon.hr.api.bill.utils.InsurancePracticeDifferenceImportUtil;
import com.reon.hr.api.bill.vo.InsurancePracticeOneFeeDetailVo;
import com.reon.hr.api.bill.vo.InsurancePracticeOneFeeVo;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.ReserveInquiryVo;
import com.reon.hr.api.bill.vo.insurancePractice.InsurancePracticeDifferenceImportVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.customer.dto.importData.EmployeeOrderImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.utils.SocialSecurityPaymentImportUtil;
import com.reon.hr.api.customer.vo.batchImport.ImportDataVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgPositionWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.file.dubbo.service.rpc.IFileSystemService;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.OrgPositionDto;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dto.TaskQueryDTO;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.core.utils.StringUtil;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.One;
import org.springframework.beans.BeanUtils;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.reon.hr.modules.customer.controller.insurancePractice.societyInsurance.SocietyInsuranceApplicationController.getLaterOrgPosList;

/**
 * <AUTHOR>
 * @Description 实做支付一次性
 * @Date 2025年06月23日
 * @Version 1.0
 */

@RestController
@RequestMapping("/bill/oneFee")
public class InsurancePracticeOneFeeController extends BaseController {

    public static final String DICTIONARY_TYPE = "PRODUCT_IND_TYPE";

    @Resource
    private ISequenceService iSequenceService;

    @Resource
    private IFileSystemService fileSystemService;

    @Resource
    private IPaymentApplyWrapperService paymentApplyService;

    @Resource
    private IDictionaryWrapperService dictionaryWrapService;

    @Resource
    private IInsurancePracticeOneFeeWrapperService iInsurancePracticeOneFeeWrapperService;

    @Resource
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource
    private IOrgPositionWrapperService iOrgPositionWrapperService;

    /**
     * 导入差异页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoImportDifferencesPage")
    public ModelAndView gotoImportDifferencesPage(Long paymentId, Model model) {
        model.addAttribute("paymentId", paymentId);
        return new ModelAndView ("/bill/oneFee/importDifferencesPage");
    }

    /**
     * 上传差异文件页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoUploadDifferencesPage")
    public ModelAndView gotoUploadDifferencesPage(Long paymentId, Model model) {
        model.addAttribute("paymentId", paymentId);
        return new ModelAndView ("/bill/oneFee/uploadDifferencesPage");
    }

    /**
     * 一次性数据差异数据审批页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoApproveDifferencesPage")
    public ModelAndView gotoApproveDifferencesPage() {
        return new ModelAndView ("/bill/oneFee/approveDifferencesPage");
    }

    /**
     * 一次性数据差异数据查询页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoAllApproveDifferencesPage")
    public ModelAndView gotoAllApproveDifferencesPage() {
        return new ModelAndView ("/bill/oneFee/allApproveDifferencesPage");
    }

    /**
     * 查询导入差异数据
     * @param paymentId
     * @return {@link Object }
     */
    @RequestMapping(value = "/getImportDifferencesPage", method = RequestMethod.GET)
    public Object getImportDifferencesPage(Long paymentId,Integer page, Integer limit) {
        Page<ImportDataVo> importDifferencesPage = iInsurancePracticeOneFeeWrapperService.getImportDifferencesPage(paymentId,getSessionUser().getLoginName(),page, limit);
        return new LayuiReplay<ImportDataVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), importDifferencesPage.getTotal(), importDifferencesPage.getRecords());
    }

    @ResponseBody
    @RequestMapping(value = "/getAllApproveDifferencesPage", method = RequestMethod.GET)
    public Object getAllApproveDifferencesPage(InsurancePracticeOneFeeDetailVo vo, HttpSession session,Integer page, Integer limit) {
        if (Objects.isNull(vo.getOrgCode())|| vo.getOrgCode().isEmpty()){
            List<OrgPositionDto> orgPosList = getLaterOrgPosList(session);
            if(orgPosList.isEmpty()){
                return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
            }
            List<OrgVo> allOrgVos = iOrgPositionWrapperService.getAllOrgCodeByLoginName(getSessionUser().getLoginName(), new OrgVo(), orgPosList);
            List<String> collect = allOrgVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
            if(collect.isEmpty()){
                return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
            }
            vo.setOrgCodeList(collect);
        }else {
            vo.setOrgCodeList(Collections.singletonList(vo.getOrgCode()));
        }
        List<InsurancePracticeOneFeeVo> allApproveDifferencesPage = iInsurancePracticeOneFeeWrapperService.getAllApproveDifferencesPage(vo);
        ListPageUtil<InsurancePracticeOneFeeVo> pager = new ListPageUtil<> (allApproveDifferencesPage, limit);
        List<InsurancePracticeOneFeeVo> pagedList = pager.getPagedList (page);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (),allApproveDifferencesPage.size(),pagedList);
    }

    /**
     *
     * 一次性审批页面查询
     * @param vo
     * @param page
     * @param limit
     * @return {@link Object }
     */
    @ResponseBody
    @RequestMapping(value = "/getApproveDifferencesPage", method = RequestMethod.GET)
    public Object getApproveDifferencesPage(InsurancePracticeOneFeeVo vo, Integer page, Integer limit) {

        List<UserOrgPosVo> allOrgCodeAndPosCode = iUserOrgPosWrapperService.getAllOrgCodeAndPosCode(getSessionUser().getLoginName());
        List<String> orgAndPosCodeList = allOrgCodeAndPosCode.stream()
                .map(dto -> dto.getOrgCode() + "," + dto.getPosCode())
                .collect(Collectors.toList());
        vo.setOrgAndPosCodeList(orgAndPosCodeList);
        vo.setCreator(getSessionUser().getLoginName());
        List<InsurancePracticeOneFeeVo> approveDifferencesPage = iInsurancePracticeOneFeeWrapperService.getApproveDifferencesPage(vo);
        ListPageUtil<InsurancePracticeOneFeeVo> pager = new ListPageUtil<> (approveDifferencesPage, limit);
        List<InsurancePracticeOneFeeVo> pagedList = pager.getPagedList (page);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), approveDifferencesPage.size(), pagedList);
    }

    /**
     *
     * 提交导入数据进入审批
     * @param id
     * @return {@link LayuiReplay }
     */
    @RequestMapping("submitApprove")
    public LayuiReplay submitApprove(Long id){
        iInsurancePracticeOneFeeWrapperService.submitApprove(id);
        return LayuiReplay.success();
    }

    @RequestMapping(value = "/approveDifferences", method = RequestMethod.POST)
    public Object approveDifferences(@RequestBody InsurancePracticeOneFeeVo vo){
        vo.setCreator(getSessionUser().getLoginName());
        iInsurancePracticeOneFeeWrapperService.approveDifferences(vo);
        if (vo.getIds().size() != 1){
            return new LayuiReplay<> (ResultEnum.ERR.getCode (), "只能选择一条");
        }
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
    }


    /**
     * 下载导入模板
     * @param response
     * @param paymentId
     */
    @RequestMapping(value = "/downloadImportTemplate", method = RequestMethod.GET)
    public void downloadImportTemplate(HttpServletResponse response, @RequestParam(value = "paymentId")Long paymentId) {
        List<List<String>> dynamicHead;
        List<PracticePayDetailVo> practicePayDetailVos = paymentApplyService.selectPracticePayDetailListByPayId(paymentId);
        Set<Integer> productCodeSet = practicePayDetailVos.stream().map(PracticePayDetailVo::getProdCode).collect(Collectors.toSet());
        Map<Integer, String> productMap = dictionaryWrapService.getAllByCodeType(DICTIONARY_TYPE);
        List<String> prodNames = productCodeSet.stream().filter(productMap::containsKey).map(productMap::get).collect(Collectors.toList());
        prodNames.add("滞纳金");
        dynamicHead = InsurancePracticeDifferenceImportUtil.getDynamicHead(prodNames);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat ("yyyyMMddHHmmssSSS");
        Date date = new Date ();
        String name = getSessionUser().getUserName()+"_"+simpleDateFormat.format (date.getTime ())+".xls";
        response.setCharacterEncoding("utf-8");
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name+".xlsx","UTF-8"));
            EasyExcel.write(response.getOutputStream()).head(dynamicHead).registerWriteHandler(new SimpleColumnWidthStyleStrategy(15)).sheet("模板").doWrite(Lists.newArrayList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @RequestMapping(value = "/importDifferences")
    @ResponseBody
    public Object importDifferences(@RequestParam("file") CommonsMultipartFile file,
                                         @RequestParam("remark") String remark,
                                    @RequestParam("paymentId") Long paymentId) {
        String originalFilename = file.getOriginalFilename();
        if (StringUtil.isEmpty(originalFilename)) {
            return LayuiReplay.error();
        }
        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(paymentId);
        List<Integer> approveStatusList = new ArrayList<>();
        if (paymentApplyVo.getAppStatus().equals(BooleanTypeEnum.YES.getCode())){
            approveStatusList.add(OneFeeApproveStatusEnum.APPROVING.getCode());
        }else {
            approveStatusList.add(OneFeeApproveStatusEnum.APPROVING.getCode());
            approveStatusList.add(OneFeeApproveStatusEnum.APPROVED.getCode());
        }
        InsurancePracticeOneFeeVo insurancePracticeOneFeeVo = iInsurancePracticeOneFeeWrapperService.getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(paymentId,approveStatusList);
        if (insurancePracticeOneFeeVo!=null){
            return LayuiReplay.error("此支付申请已经有差异数据正在审批中或已经审批通过，请先检查已导入的数据");
        }
        String fileId ;
        String fileName = file.getOriginalFilename();
        try {
            fileId = fileSystemService.uploadFile(file.getBytes(), fileName);
            fileName = fileId + "," + fileName;
            if (StringUtil.isEmpty(fileName)) {
                return new LayuiReplay<String>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
            } else {
                try {
                    InsurancePracticeDifferenceListener importExcelListener = new InsurancePracticeDifferenceListener();
                    EasyExcel.read(file.getInputStream(), importExcelListener).headRowNumber(2).sheet().doRead();
                    ImportDataDto<InsurancePracticeDifferenceImportVo> importDataDto = importExcelListener.getImportDataDto();
                    importDataDto.setLoginName(getSessionUser().getLoginName());
                    importDataDto.setImportNo(iSequenceService.getBatchImportDataNo());
                    importDataDto.setFileId(fileName);
                    importDataDto.setRemark(remark);
                    iInsurancePracticeOneFeeWrapperService.importDifferences(paymentId, importDataDto);
                } catch (Exception e) {
                    return new LayuiReplay<String>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg(), e.getMessage());
                } finally {
                    SocialSecurityPaymentImportUtil.clearTheadLocal();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return LayuiReplay.error();
        }
         return LayuiReplay.success();
    }

    /**
     * 校验差异数据是否上传
     * @param paymentId
     * @return {@link Object }
     */
    @RequestMapping(value = "/checkIsImportDifferences", method = RequestMethod.GET)
    @ResponseBody
    public Object checkIsImportDifferences(Long paymentId) {
        List<Integer> approveStatusList = new ArrayList<>();
        approveStatusList.add(OneFeeApproveStatusEnum.APPROVED.getCode());
        InsurancePracticeOneFeeVo insurancePracticeOneFeeVo = iInsurancePracticeOneFeeWrapperService.getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(paymentId,approveStatusList);
        if (insurancePracticeOneFeeVo==null){
            return new LayuiReplay<> (ResultEnum.ERR.getCode (), "此支付申请上传的差异数据未审批通过不允许提交");
        }
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
    }

}
