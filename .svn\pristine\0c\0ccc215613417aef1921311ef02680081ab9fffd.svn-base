package com.reon.hr.sp.bill.entity.supplierPractice;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-08 13:57:40
 */
@Data
@TableName("supplier_practice_product_snapshot")
public class SupplierPracticeProductSnapshot implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Long id;

	/**
	 * 供应商实做快照ID
	 */
	private Long pracSnapId;

	/**
	 * 客户ID
	 */
	private Long custId;
	/**
	 * 员工ID
	 */
	private Long employeeId;
	/**
	 * 实做ID
	 */
	private Long practiceId;
	/**
	 * 订单编号
	 */
	private String orderNo;
	/**
	 * 产品类型
	 */
	private Integer prodCode;
	/**
	 * 比例编码
	 */
	private String ratioCode;
	/**
	 * 收费起始月
	 */
	private Integer revStartMonth;
	/**
	 * 账单起始月
	 */
	private Integer billStartMonth;
	/**
	 * 收费截止月
	 */
	private Integer expiredMonth;
	/**
	 * 账单起始月
	 */
	private Integer billMonth;
	/**
	 * 企业基数
	 */
	private BigDecimal comBase;
	/**
	 * 个人基数
	 */
	private BigDecimal indBase;
	/**
	 * 企业金额
	 */
	private BigDecimal comAmt;
	/**
	 * 个人金额
	 */
	private BigDecimal indAmt;
	/**
	 * 账单模板ID
	 */
	private Long templetId;
	/**
	 * 收费模板ID
	 */
	private Long revTempId;
	/**
	 * 退费账单月
	 */
	private Integer returnMonth;
	/**
	 * 支付最后服务年月
	 */
	private Integer lastMonth;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updater;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 删除标识(Y:已删除，N:未删除)
	 */
	private String delFlag;

}
