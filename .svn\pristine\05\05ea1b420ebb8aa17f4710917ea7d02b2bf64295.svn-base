
layui.use(['table','layer', 'form', 'element',"laydate"], function(){
	  var layer = layui.layer
	  ,form = layui.form
	  ,element = layui.element
	  ,  laydate = layui.laydate
	  ,$ = layui.jquery,
	  table = layui.table;
		//定义必要的表单验证
		form.verify({
								  corpName : function(value, item) { // value：表单的值、item：表单的DOM对象
									if (!new RegExp(
											"^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$")
											.test(value)) {
										return '用户名不能有特殊字符';
									}
									if (/(^\_)|(\__)|(\_+$)/.test(value)) {
										return '用户名首尾不能出现下划线\'_\'';
									}
									if (/^\d+$/.test(value)) {
										return '用户名不能全为数字';
									}
									if (value.length > 50) {
										return '企业名称不能超过50个字符';
									}
								}

								// 我们既支持上述函数式的方式，也支持下述数组的形式
								// 数组的两个值分别代表：[正则匹配、匹配不符时的提示文字]
								,
								password : function(value, item) { // value：表单的值、item：表单的DOM对象
									if (!new RegExp(
									"^[a-zA-Z0-9_]{0,}$")
									.test(value)) {
								return '密码必须6到30位，且不能出现空格,中文汉字';
							             }
									if (value.length <6 || value.length >30) {
										return '密码必须6到30位，且不能出现空格,中文汉字';
									}
								},
								principal : function(value, item) { // value：表单的值、item：表单的DOM对象
									if (!new RegExp(
											"^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$")
											.test(value)) {
										return '负责人姓名不能有特殊字符';
									}
									if (/(^\_)|(\__)|(\_+$)/.test(value)) {
										return '负责人姓名首尾不能出现下划线\'_\'';
									}
									if (/^\d+$/.test(value)) {
										return '负责人姓名不能全为数字';
									}
									if (value.length > 10) {
										return '负责人姓名不能超过10个字符';
									}
								},
								phone : [ /^1\d{10}$/,
								//phone : [ /^[1][3,4,5,6,7,8][0-9]{9}$/,
										"请输入正确的手机号" ],
								email : function(value, item) {
									if (value.length > 50) {
										return '邮箱长度不得超过50个字符';
									}
									if (!new RegExp(
											"^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$")
											.test(value)) {
										return '请输入正确的邮箱账号';
									}
								}

								,
								bankName : function(value, item) {
									if (value.length > 50) {
										return '银行名称不得超过50个字符';
									}
								},
								bankCardNo : [ /^([1-9]{1})(\d{14}|\d{18})$/,
										"请正确输入银行卡号" ],
								money : function(value, item) {
									if (!new RegExp("^[1-9][0-9]*$")
											.test(value)) {
										return "最长20位数字,保留最后两位小数";
									}
									if (value.length > 20) {
										return "最长20位数字,保留最后两位小数";
									}
								},
								isNotNull : function(value, item) {
									if (null == value || '' == value) {
										return "不可以为空";
									}
								},
								charLength20 : function(value, item) {
									if (value.length > 20) {
										return '字符长度不得超过20个字符';
									}
								},
								rechargeAmount : [ /^[0-9]+$/, "充值金额必须是正整数" ]
				  
			});    
});
