/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/8
 * <p>
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.servicesitecfg.controller;

import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISocialSecurityFundTrialCalculationWrapperService;
import com.reon.hr.api.base.enums.SocialSecurityFundTrialCalculationEnum;
import com.reon.hr.api.base.utils.SocialSecurityFundExportExcelUtil;
import com.reon.hr.api.base.vo.ParameVo;
import com.reon.hr.api.base.vo.SocialSecurityFundCompositVo;
import com.reon.hr.api.base.vo.SocialSecurityFundExportVo;
import com.reon.hr.api.base.vo.SocialSecurityFundSingleVo;
import com.reon.hr.api.change.utils.ExportExcelUtil;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.StringProcessUtils;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.modules.common.BaseController;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * &#064;className  SocialSecurityFundTrialCalculationController
 * &#064;description  TODO
 * &#064;date  2020/6/8 9:41
 */
@Controller
@RequestMapping("/serviceSiteCfg/socialSecurityFundTC")
public class SocialSecurityFundTrialCalculationController extends BaseController {

    @Resource
    private ISocialSecurityFundTrialCalculationWrapperService iSocialSecurityFundTrialCalculationWrapperService;

    private final Map<String, List<ParameVo>> map = Maps.newHashMap();

    private final String listName = "list";

    /**
     * 转到社保基金试验计算查询列表页面
     *
     * @return {@link String}
     */
    @RequestMapping("gotoSocialSecurityFundTrialCalculationQueryListPage")
    public String gotoSocialSecurityFundTrialCalculationQueryListPage() {
        return "/serviceSiteCfg/socialSecurityFundTrialCalculationQuery";
    }

    /**
     * 获取参数
     *
     * @param socialSecurityFundCompositeVo 社保基金实体
     * @return {@link Object}
     */
    @ResponseBody
    @RequestMapping(value = "/getParame", method = RequestMethod.POST)
    public Object getParam(@RequestBody SocialSecurityFundCompositVo socialSecurityFundCompositeVo) {
        map.clear();
        List<ParameVo> list = socialSecurityFundCompositeVo.getList();
        for (ParameVo parameVo : socialSecurityFundCompositeVo.getList()) {
            parameVo.setPaymentDay(socialSecurityFundCompositeVo.getPaymentDay());
            if (list.size() == 1) {
                parameVo.setCategoryCode(socialSecurityFundCompositeVo.getCategoryCode());
            }
        }
        map.put(listName, socialSecurityFundCompositeVo.getList());
        return new LayuiReplay<SocialSecurityFundCompositVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    /**
     * 导出文件
     *
     * @param response 响应
     */
    @GetMapping(value = "/exportFile")
    public void exportFile(HttpServletResponse response) {
        CommonUserVo commonUserVo = getSessionUser();
        String loginUser = commonUserVo.getUserName();
        String format = DateUtil.getNowStringDate(DateUtil.DATE_FORMAT_yyyyMM);
        String accumulationFundOfSocialSecurityTrial = "社保公积金试算";
        String filePath = StringProcessUtils.getFileNameForTypeXls(loginUser, accumulationFundOfSocialSecurityTrial);
        List<ParameVo> list = map.get(listName);
        try {
            SocialSecurityFundCompositVo vo = iSocialSecurityFundTrialCalculationWrapperService.getSocialSecurityFundTrialCalculationData(list, Integer.valueOf(format), 1);
            HSSFWorkbook wb = new HSSFWorkbook();
            SocialSecurityFundExportExcelUtil.export(
                    vo.getNewAllSocialSecurityFundTrialCalculationExport(),
                    accumulationFundOfSocialSecurityTrial,
                    SocialSecurityFundExportExcelUtil.headerSSFTCOne(vo.getAllProductCodes(), vo.getViewItems()),
                    SocialSecurityFundExportExcelUtil.headerSSFTCTwo(vo.getAllProductCodes(), vo.getViewItems()),
                    SocialSecurityFundTrialCalculationEnum.getValue(vo.getAllProductCodes(), vo.getViewItems()),
                    vo.getViewItems().length() - (ISocialSecurityFundTrialCalculationWrapperService.VARIABLE_COLUMN_LIST.size() + ISocialSecurityFundTrialCalculationWrapperService.IMMUTABLE_COLUMN_LIST.size()),
                    wb,
                    "SSFTC"
            );
            ExportExcelUtil.getOutputStream(response, filePath, wb);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取单个城市试算数据
     *
     * @return {@link List}<{@link SocialSecurityFundSingleVo}>
     * @throws Exception 异常
     */
    @GetMapping(value = "/getSingleData")
    @ResponseBody
    public List<SocialSecurityFundSingleVo> getSingleData() throws Exception {
        List<ParameVo> list = map.get(listName);
        return iSocialSecurityFundTrialCalculationWrapperService.getSingleData(list);
    }


    /**
     * 根据城市获取优选或自有服务网点下的公积金比例
     *
     * @param cityCode 城市代码
     * @return {@link List}<{@link SocialSecurityFundExportVo}>
     */
    @ResponseBody
    @RequestMapping(value = "/getRadioByCityCode", method = RequestMethod.GET)
    public List<SocialSecurityFundExportVo> getRadioByCityCode(Integer cityCode, String ind) {
        return iSocialSecurityFundTrialCalculationWrapperService.getRadioByCityCode(cityCode,null, ind);
    }
}
