package com.reon.hr.sp.bill.service.impl.supplierPractice;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceRatioWrapperService;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.enums.InsuranceRatioChargeFreq;
import com.reon.hr.api.base.enums.InsuranceRatioEnum;
import com.reon.hr.api.base.vo.InsuranceRatioVo;
import com.reon.hr.api.bill.constant.BillEnum;
import com.reon.hr.api.bill.enums.*;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.bill.vo.bill.SupplierPracticeBillVo;
import com.reon.hr.api.bill.vo.supplierPractice.*;
import com.reon.hr.api.customer.dto.customer.SupplierBillDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.SupplierBillTempletWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.supplierPractice.ISupplierPracticeWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.SupplierContractProdTypeEnum;
import com.reon.hr.api.customer.enums.quotation.supQuotation.SupTempletType;
import com.reon.hr.api.customer.enums.supplierPractice.SupplierPracticeEnum;
import com.reon.hr.api.customer.vo.supContractArea.SupContractAreaRelativeTempVo;
import com.reon.hr.api.customer.vo.supplier.SupplierQuotationVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.*;
import com.reon.hr.common.bill.strategy.ChargeFreqStrategy;
import com.reon.hr.common.bill.strategy.IChargeFreqStrategyFactory;
import com.reon.hr.common.bill.strategy.concrete.ReceiveInfo;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.ChargeFreqInfo;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.ChargeFreqStrategyFactory;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.vo.ProdAmtVo;
import com.reon.hr.common.constants.CommonConstants;
import com.reon.hr.common.constants.SupplierPracticeConstants;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.utils.ReceiveMonthHelper;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ProducerScopeTypeBill;
import com.reon.hr.sp.bill.dao.supplierPractice.PerSupplierBillInfoMapper;
import com.reon.hr.sp.bill.dao.supplierPractice.SupplierPracticeBillMapper;
import com.reon.hr.sp.bill.dubbo.service.rpc.impl.supplierPractice.BeforePracticeServiceMonthUtilReturn;
import com.reon.hr.sp.bill.entity.supplierPractice.SupplierNonMonthlyProd;
import com.reon.hr.sp.bill.entity.supplierPractice.SupplierPracticeBill;
import com.reon.hr.sp.bill.service.bill.supplierPractice.*;
import com.reon.hr.sp.bill.utils.EnCryptUtil;
import com.reon.hr.sp.bill.utils.ServiceMonthUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.reon.hr.common.constants.SupplierPracticeConstants.SERVICE_CHARGE_PRODUCTCODE;
import static com.reon.hr.common.constants.SupplierPracticeConstants.TAX_COST_PRODUCTCODE;
import static com.reon.hr.sp.bill.service.impl.insurancePractice.InsurancePracticeBillServiceImpl.getYear;
import static java.util.stream.Collectors.*;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.bill.service.impl.supplierPractice
 * @ClassName: ISupplierPracticeBillServiceImpl
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/5 10:51
 * @Version: 1.0
 */

@Service
public class ISupplierPracticeBillServiceImpl implements ISupplierPracticeBillService {


    private static final int YEAR_PROD_START_MONTH = 202111;  //收费起始的月份
    private static final int YEAR_PROD_START_ADJUST_MONTH = 202112;  // 调整起始月份
    private static final int BOOLEAN_TRUE = 2;// 是

    private static final int BOOLEAN_FALSE = 1; //否
    private final String GT = "gt";
    private final String LT = "lt";

    private static final Logger logger = LoggerFactory.getLogger(ISupplierPracticeBillServiceImpl.class);
    @Autowired
    private MqMessageSender mqMessageSender;

    @Autowired
    private SupplierPracticeBillMapper supplierPracticeBillMapper;
    @Autowired
    private SupplierPracticeReportService supplierPracticeReportService;
    @Resource
    private ISupplierPracticeWrapperService iSupplierPracticeWrapperService;
    @Autowired
    private PerSupplierBillInfoMapper perSupplierBillInfoMapper;
    @Resource
    private IInsuranceRatioWrapperService insuranceRatioWrapperService;

    @Resource
    private ISupplierWrapperService supplierWrapperService;

    @Autowired
    private SupplierPracticeSnapshotService snapshotService;

    @Autowired
    private SupplierNonMonthlyProdService nonMonthlyProdService;
    @Autowired
    private BeforePracticeServiceMonthService beforePracticeServiceMonthService;

    @Resource
    private SupplierBillTempletWrapperService supplierBillTempletWrapperService;
    @Resource
    private IInsuranceRatioWrapperService ratioWrapperService;

    @Autowired
    private SupplierBillServiceNumInfoService supplierBillServiceNumInfoService;

    @Autowired
    private SupplierDisposableItemService supplierDisposableItemService;


    @Override
    public void insertSupplierReport(SupplierPracticeBillVo vo) {
        logger.info("生成供应商报表,{}", vo);
        SupplierPracticeBill data = new SupplierPracticeBill();
        data.setSupplierId(vo.getSupplierId())
                .setTempletId(vo.getTempletId())
                .setBillMonth(vo.getBillMonth());
        SupplierPracticeBill hasBill = supplierPracticeBillMapper.selectOne(data);
        Long billId;
        if (Objects.isNull(hasBill)) {
            data.setBillAmt(BigDecimal.ZERO)
                    .setServiceFee(BigDecimal.ZERO)
                    .setServiceNum(0)
                    .setBillMonth(vo.getBillMonth())
                    .setGenStatus(PracticeLockInfoGenStatusEnum.IN_GENERATED.getCode().intValue())
                    .setPayStatus(PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode().intValue())
                    .setLockStatus(PracticeBillLockStatusEnum.UNLOCKED.getCode().intValue())
                    .setCreator(vo.getCreator())
                    .setCreateTime(new Date())
                    .setUpdater(vo.getCreator())
                    .setUpdateTime(new Date());
            supplierPracticeBillMapper.insert(data);
            billId = data.getId();
        } else {
            hasBill.setBillAmt(BigDecimal.ZERO)
                    .setServiceFee(BigDecimal.ZERO)
                    .setServiceFeeOne(BigDecimal.ZERO)
                    .setServiceNum(0)
                    .setGenStatus(PracticeLockInfoGenStatusEnum.IN_GENERATED.getCode().intValue())
                    .setUpdater(vo.getCreator())
                    .setUpdateTime(new Date());
            supplierPracticeBillMapper.updateById(hasBill);
            billId = hasBill.getId();
        }

        mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeBill.REON_SUPPLIER_PRACTICE_LOCK_GENERATE_COMPLETED, billId.toString());

    }

    @Override
    public Page<SupplierPracticeBillVo> searchSupplierBill(SupplierPracticeBillVo vo) {
        Page<SupplierPracticeBillVo> billVoPage = new Page<>(vo.getPage(), vo.getLimit());
        List<SupplierPracticeBillVo> result = supplierPracticeBillMapper.searchSupplierBill(vo, billVoPage);
        if (CollectionUtils.isEmpty(result)){
            return  billVoPage.setRecords(result);
        }
        List<Long> supplierIds = result.stream().map(SupplierPracticeBillVo::getSupplierId).collect(toList());
        List<Long> templetIds = result.stream().map(SupplierPracticeBillVo::getTempletId).collect(toList());
        List<Long> billIdList = result.stream().map(SupplierPracticeBillVo::getId).collect(toList());
        List<SupplierDisposableItemVo> supplierDisposableItemVosByBillIdList = supplierDisposableItemService.getSupplierDisposableItemVosByBillIdList(billIdList, ExportBillDisposableApprovalEnum.SupplierBillDisposableStatusEnum.STATUS_4.getIndex());
        List<Long> oneTimeFeeBillIdList = supplierDisposableItemVosByBillIdList.stream().map(SupplierDisposableItemVo::getBillId).collect(toList());
        Map<Long, String> supplierMap = supplierWrapperService.getSupplierMapByIds(supplierIds);
//        Map<Long, String> templetMapByIds = supplierBillTempletWrapperService.getTempletMapByIds(templetIds);

        List<SupplierBillTempletVo> supplierBillTempletVos = supplierBillTempletWrapperService.getTempletByIds(Lists.newArrayList(templetIds));
        Map<Long, SupplierBillTempletVo> SupplierBillTempletVoIdMap = supplierBillTempletVos.stream()
                .collect(Collectors.toMap(SupplierBillTempletVo::getId, Function.identity()));

        for (SupplierPracticeBillVo bill : result) {
            if (oneTimeFeeBillIdList.contains(bill.getId())){
                bill.setStatus(ExportBillDisposableApprovalEnum.SupplierBillDisposableStatusEnum.STATUS_4.getIndex());
            }
            bill.setSupplierName(supplierMap.get(bill.getSupplierId()));
//            bill.setTempletName(templetMapByIds.get(bill.getTempletId()));
            SupplierBillTempletVo supplierBillTempletVo = SupplierBillTempletVoIdMap.get(bill.getTempletId());
            bill.setTempletName(supplierBillTempletVo.getTempletName());
            boolean single = SupTempletType.isSingle(supplierBillTempletVo.getTempletType());
            bill.setSingleFlag(single ? "是" : "否");
            BigDecimal serviceFee = Optional.ofNullable(bill.getServiceFee()).orElse(BigDecimal.ZERO);
            BigDecimal serviceFeeOne = Optional.ofNullable(bill.getServiceFeeOne()).orElse(BigDecimal.ZERO);
            bill.setServiceFee(serviceFee.add(serviceFeeOne));
        }
        billVoPage.setRecords(result);
        return billVoPage;
    }

    @Override
    public SupplierPracticeBillVo searchSupplierBillByArgs(SupplierPracticeBillVo vo) {
        SupplierPracticeBill condition = new SupplierPracticeBill();
        condition.setSupplierId(vo.getSupplierId()).setTempletId(vo.getTempletId()).setBillMonth(vo.getBillMonth());
        SupplierPracticeBill bill = supplierPracticeBillMapper.selectOne(condition);
        if (Objects.isNull(bill)) {
            return null;
        }
        SupplierPracticeBillVo billVo = new SupplierPracticeBillVo();
        BeanUtils.copyProperties(bill, billVo);
        return billVo;
    }

    @Override
    public List<SupplierPracticeBillVo> searchSupplierBillGlMonth(SupplierPracticeBillVo vo) {
        EntityWrapper<SupplierPracticeBill> wrapper = new EntityWrapper<>();
        wrapper.eq("supplier_id",vo.getSupplierId());
        wrapper.eq("templet_id",vo.getTempletId());
        wrapper.ge("bill_month",vo.getBillMonth());
        List<SupplierPracticeBill> bills = supplierPracticeBillMapper.selectList(wrapper);
        return bills.stream().map(item ->{
            SupplierPracticeBillVo billVo = new SupplierPracticeBillVo();
            BeanUtils.copyProperties(item, billVo);
            return billVo;
        }).collect(toList());
    }

    @Override
    public List<SupplierPracticeBillVo> getByIds(List<Long> id) {
        EntityWrapper<SupplierPracticeBill> wrapper = new EntityWrapper<>();
        wrapper.in("id", id);
        List<SupplierPracticeBill> bills = supplierPracticeBillMapper.selectList(wrapper);
        List<SupplierPracticeBillVo> collect = bills.stream().map(vo -> {
            SupplierPracticeBillVo billVo = new SupplierPracticeBillVo();
            BeanUtils.copyProperties(vo, billVo);
            return billVo;
        }).collect(toList());
        return collect;
    }

    @Override
    public void updateReportToLockByIds(List<Long> id, String loginName, Byte code) {
        supplierPracticeBillMapper.updateReportToLockByIds(id, loginName, code);
    }

    @Override
    public List<SupplierPracticeBillVo> getSupplierBillBySupIdAndTemId(List<String> tempIdArgs, List<Integer> monthBetween,Integer lockStatus,Integer payStatus) {
        return supplierPracticeBillMapper.getSupplierBillBySupIdAndTemId(tempIdArgs, monthBetween, lockStatus, payStatus);
    }

    @Override
    public List<SupplierPracticeBillVo> getSupplierPracticeBillLock(Integer month, Long supplierId) {
        return supplierPracticeBillMapper.getSupplierPracticeBillLock(month, supplierId);
    }

    @Override
    public Long getBillIdByBillMonthAndBillMonth(Integer billMonth, Long supplierId, Long templetId) {
        return supplierPracticeBillMapper.getBillIdByBillMonthAndBillMonth(billMonth, supplierId, templetId);
    }

    @Override
    public void handleGenSupplierBill(Long billId) throws Exception {
        logger.info("mq 接受到的消息为========>,{}", billId);
        SupplierPracticeBill bill = supplierPracticeBillMapper.selectById(billId);
        if (checkBillVo(billId, bill)){
            return;
        }
        logger.info("生成账单的供应商ID,{}账单模板ID,{}", bill.getSupplierId(), bill.getTempletId());
        Integer billMonth = bill.getBillMonth();
        Map<Long, String> productCodeMap = Maps.newHashMap();
        Map<Long, String> chargeCodeMap = Maps.newHashMap();
        SupplierBillTempletVo templetVo = supplierBillTempletWrapperService.getSupplierBillTempletVoById(bill.getTempletId());
        /**获取供应商实做数据*/
        SupplierPracticeVo condition = new SupplierPracticeVo();
        condition.setTempletId(bill.getTempletId())
                .setAddMonth(billMonth)
                .setSupplierId(bill.getSupplierId());
        SupplierBillDto supplierPracticeDto = iSupplierPracticeWrapperService.querySupplierPracticeVos(condition, null);
        List<SupplierPracticeVo>  allData = supplierPracticeDto.getPracticeVos();
        List<Long> allIds = allData.stream().map(SupplierPracticeVo::getId).collect(toList());
        List<SupplierPracticeVo> supplierPracticeVos = allData.stream().filter(item -> !item.getHandleStatus().equals(SupplierPracticeEnum.ADD_WAIT.getCode()) &&
                (item.getAddMonth() != null && item.getAddMonth() <= billMonth)
        ).collect(Collectors.toList());
        /**当前需要生成的数据*/
        List<Long> practiceIds = supplierPracticeVos.stream().map(SupplierPracticeVo::getId).distinct().collect(toList());
        List<PracServiceChargeVo> serviceChargeVos = supplierPracticeDto.getServiceChargeVos();
        List<SupContractAreaRelativeTempVo> contractAreaRelativeTempVos = supplierPracticeDto.getContractAreaRelativeTempVos();
        List<SupplierOneChargeVo> oneTimeFees = supplierPracticeDto.getOneTimeFees();
        List<SupplierQuotationVo> quotationVos = supplierPracticeDto.getQuotationVos();

        /**一个小合同只能绑定一个有效的报价单*/
        Map<String, SupplierQuotationVo> quotationNoAndContractTypeMap = quotationVos.stream().collect(toMap(SupplierQuotationVo::getContractAreaNo, Function.identity()));
        /**删除 首板、报表、同期快照*/
        supplierPracticeReportService.deletePracticeReport(bill.getSupplierId(), bill.getTempletId(), billMonth);
        if(CollectionUtils.isNotEmpty(allIds)){
            perSupplierBillInfoMapper.deletePerSupplierBillInfoVosByPracIds(allIds, billMonth);
            snapshotService.deletePracticeSnapshotByPracIdAndBillMonth(allIds, billMonth);
            nonMonthlyProdService.deleteSupplierNonMonthlyProdVo(allIds, billMonth);
//            perSupplierBillInfoMapper.deletePerSupplierBillInfoVos(allData, billMonth);
        }
        supplierBillServiceNumInfoService.deleteBillServiceNumInfoVo(bill.getId());
        if (CollectionUtils.isEmpty(supplierPracticeVos)) {
            logger.info("=====================报表生成完成: 没有有效的实做需要处理====================");
            bill.setBillAmt(BigDecimal.ZERO).setServiceFee(BigDecimal.ZERO).setServiceNum(BigDecimal.ZERO.intValue())
                    .setGenStatus(BillEnum.GenerateBillStatus.SUCCESSED.getCode()).setUpdateTime(new Date());
            supplierPracticeBillMapper.updateById(bill);
            return;
        }
        /**实做与服务费数据*/
        Map<String, SupContractAreaRelativeTempVo> tempVoMap = contractAreaRelativeTempVos.stream().collect(toMap(SupContractAreaRelativeTempVo::getContractAreaNo, Function.identity()));
        Map<Long, List<PracServiceChargeVo>> serviceChargesVoMap = Optional.ofNullable(serviceChargeVos).orElse(Lists.newArrayList()).stream().collect(groupingBy(PracServiceChargeVo::getPracticeId));
        /**订单一次性费用*/
        Map<String, List<SupplierOneChargeVo>> oneTimeFeeMap = oneTimeFees.stream().collect(groupingBy(SupplierOneChargeVo::getOrderNo));
        /**获取实做之前生成过账单的数据 并合并服务月*/
        BeforePracticeServiceMonthUtilReturn practiceServiceMonthUtilReturn = getReceiveMonthByPracticeIdList(practiceIds, billMonth);
        Map<Long, Map<Integer, Set<Integer>>> practiceIdAndReceiveAndBillMonthSetMap = practiceServiceMonthUtilReturn.getPracticeIdAndReceiveAndBillMonthSetMap();
        Map<Long, Map<Integer, Map<String, Integer>>> maxMinReceiveMonthMap = practiceServiceMonthUtilReturn.getPracticeIdAndProductCodeAndMaxMinReceiveMonthMap();
        /**没有排除当前月的数据*/
        Map<Long, Map<Integer, BeforePracticeServiceMonthVo>> practiceIdAndProductCodeAndDataMap = practiceServiceMonthUtilReturn.getPracticeIdAndProductCodeAndDataMap();
        /**根据实做生成报表数据*/
        List<SupplierPracticeReportVo> reportVos = buildReportDataByPractice(supplierPracticeVos, bill, productCodeMap, maxMinReceiveMonthMap, serviceChargesVoMap, chargeCodeMap, tempVoMap);
        Map<Long, List<SupplierPracticeReportVo>> reportVoMap = reportVos.stream().collect(Collectors.groupingBy(SupplierPracticeReportVo::getPracticeId));
        /** 判断数据是否为首次生成,与插入首版数据*/
        List<PerSupplierBillInfoVo> perSupplierBillInfos = CollectionUtils.isNotEmpty(reportVos) ? perSupplierBillInfoMapper.getSupplierBillInfoList(reportVos) : Lists.newArrayList();

        Map<String, PerSupplierBillInfoVo> perSupplierBillInfoMap = perSupplierBillInfos.stream().collect(Collectors.toMap(k -> buildKey(k.getPracticeId(), k.getProductCode()), Function.identity()));
        List<PerSupplierBillInfoVo> firstGenerateBill = isFirstGenerateBill(reportVos, billMonth, perSupplierBillInfoMap);
        /**供应商账单一次性*/
        List<SupplierDisposableItemVo> disposableItemVos = supplierDisposableItemService.getSupplierDisposableItemVosByBillId(billId, ExportBillDisposableApprovalEnum.SupplierBillDisposableStatusEnum.STATUS_3.getIndex());

        /** batchInsertReportVosList： 保存的report数据*/
        List<SupplierPracticeReportVo> batchInsertReportVosList = Lists.newArrayList();
        /**实做快照*/
        Map<Long, SupplierPracticeSnapshotVo> practiceSnapshotMap = snapshotService.getPracticeSnapshotMapByPracticeIdAndBillMonth(practiceIds, billMonth);
        List<Long> snapshotIds = practiceSnapshotMap.values().stream().map(SupplierPracticeSnapshotVo::getId).collect(toList());
        List<SupplierPracticeProductSnapshotVo> snapshotList = snapshotService.getPracticeProductSnapshotList(snapshotIds);
        Map<Long, Map<Integer, List<SupplierPracticeProductSnapshotVo>>> practiceProductSnapshotMap = snapshotList.stream().collect(Collectors.groupingBy(SupplierPracticeProductSnapshotVo::getPracticeId,
                Collectors.groupingBy(SupplierPracticeProductSnapshotVo::getProdCode)));
        Map<Long, List<SupplierNonMonthlyProdVo>> nonMonthlyMapProdMap = nonMonthlyProdService.getSupplierNonMonthlyMapProdVo(practiceIds);

        /** 获取快照中的全部收费频率*/
        List<Long> revTempIds = snapshotList.stream().map(SupplierPracticeProductSnapshotVo::getRevTempId).distinct().collect(Collectors.toList());
        List<SupplierTempletFeeCfgVo> supplierTempletFeeCfgVos = supplierBillTempletWrapperService.getSupplierTempletFeeCfgByIds(revTempIds);
        Map<Long, SupplierTempletFeeCfgVo> templetFeeCfgMap = supplierTempletFeeCfgVos.stream().collect(Collectors.toMap(SupplierTempletFeeCfgVo::getId, Function.identity()));
        /** 获取全部的社保比例*/
        List<InsuranceRatioVo> allInsuranceRatioVoList = ratioWrapperService.findAllInsuraceRatios();
        Map<String, InsuranceRatioVo> ratioVoMap = allInsuranceRatioVoList.stream().collect(Collectors.toMap(InsuranceRatioVo::getInsuranceRatioCode, o -> o));
        List<SupplierPracticeReportVo> normalReportVos = Lists.newArrayList();
        List<SupplierPracticeReportVo> nonMonthReportVos = Lists.newArrayList();
        /*** 遍历全部的实做并判断是否调整过数据*/
        for (Long practiceId : reportVoMap.keySet()) {
            List<SupplierPracticeReportVo> reportVoList = reportVoMap.get(practiceId);
            String orderNo = reportVoList.get(0).getOrderNo();
            String contractAreaNo = reportVoList.get(0).getContractAreaNo();
            boolean isOutSourcingTwo = false;
            SupplierQuotationVo quotationInfo = null;
            List<SupplierPracticeReportVo> currentList = Lists.newArrayList();
            if(quotationNoAndContractTypeMap.containsKey(contractAreaNo)){
                quotationInfo = quotationNoAndContractTypeMap.get(contractAreaNo);
                isOutSourcingTwo = quotationNoAndContractTypeMap.get(contractAreaNo).getProdType().equals(SupplierContractProdTypeEnum.OUTSOURCING.getCode());
            }
            /**获取实做全部服务月 */
            Map<Integer, List<SupplierNonMonthlyProdVo>> getNonMonthlyProdByProdCodeMap = nonMonthlyMapProdMap.getOrDefault(practiceId, Lists.newArrayList())
                    .stream().collect(groupingBy(SupplierNonMonthlyProdVo::getProdCode));

            getCurrentMonthsByProd(reportVoList, billMonth, getNonMonthlyProdByProdCodeMap);
            SupplierPracticeSnapshotVo snapshotVo = practiceSnapshotMap.get(practiceId);
            String prodEncryptCode = productCodeMap.get(practiceId);
            Map<String, Boolean> cryptCodeResult = getCryptCodeResult(snapshotVo, prodEncryptCode, chargeCodeMap.get(practiceId));
            Boolean prodChangeFlag = cryptCodeResult.get(CommonConstants.PROD_CHANGE_FLAG);
            Boolean serviceChangeFlag = cryptCodeResult.get(CommonConstants.SERVICE_CHARGE_FLAG);
            Map<Boolean, List<SupplierPracticeReportVo>> reportVoBooleanMap = reportVoList.stream().collect(partitioningBy(vo -> vo.getProdCode().equals(SERVICE_CHARGE_PRODUCTCODE)));
            List<SupplierPracticeReportVo> prodReportVos = reportVoBooleanMap.get(false);
            if (CollectionUtils.isNotEmpty(prodReportVos)) {
                if (prodChangeFlag) {
                    List<SupplierPracticeReportVo> needAdjustmentAndReturnData = getNeedAdjustmentAndReturnData(prodReportVos, practiceProductSnapshotMap, nonMonthlyMapProdMap, ratioVoMap, templetFeeCfgMap, bill,
                            practiceSnapshotMap.get(practiceId).getBillMonth());
                    /**正常收的全部月份都是需要收取服务费*/
                    List<SupplierPracticeReportVo> adjustReportVos = computeReceiveAmtAndCollectPerBillItems(needAdjustmentAndReturnData, false, nonMonthReportVos);
                    currentList.addAll(adjustReportVos);
                    normalReportVos.addAll(prodReportVos);
                } else {
                    List<SupplierPracticeReportVo> saveSupplierPracticeReportVos = computeReceiveAmtAndCollectPerBillItems(prodReportVos, false, nonMonthReportVos);
                    if (!saveSupplierPracticeReportVos.isEmpty()) {
                        currentList.addAll(saveSupplierPracticeReportVos);
                        normalReportVos.addAll(prodReportVos);
                    }
                }
            }

            /**服务费*/
            List<SupplierPracticeReportVo> serviceChargeReports = reportVoBooleanMap.get(true);
            if (CollectionUtils.isNotEmpty(serviceChargeReports)) {
                serviceChargeChangeFun(practiceId, serviceChangeFlag, serviceChargeReports, practiceProductSnapshotMap,
                        ratioVoMap, templetFeeCfgMap, bill, practiceSnapshotMap, templetVo, currentList, normalReportVos);
            }

            /**社保公积金 服务费计算完成后，如果是外包计算 税点成本*/
            getTaxCost(isOutSourcingTwo, currentList, quotationInfo);
            batchInsertReportVosList.addAll(currentList);
            /**一次性*/
            if (oneTimeFeeMap.containsKey(orderNo)) {
                List<SupplierOneChargeVo> supplierOneChargeVos = oneTimeFeeMap.get(orderNo);
                List<SupplierPracticeReportVo> sub = buildProdHandleByOneTimeFee(supplierOneChargeVos, bill, reportVoList.get(0), templetVo);
                batchInsertReportVosList.addAll(sub);
            }
        }

        if (CollectionUtils.isNotEmpty(normalReportVos)) {
            /**保存此次生成账单的快照*/
            saveSnapshots(normalReportVos, bill, productCodeMap, nonMonthReportVos, chargeCodeMap);
        }

        /**设置服务费 与服务人次*/
//        Map<Long, List<SupplierPracticeReportVo>> serviceChargeReportMap = batchInsertReportVosList.stream().filter(vo -> vo.getProdCode().equals(SERVICE_CHARGE_PRODUCTCODE)).collect(groupingBy(SupplierPracticeReportVo::getPracticeId));
        List<SupplierBillServiceNumInfoVo> serviceNumInfoVoList = dealBillServiceNumInfoList(batchInsertReportVosList, bill, serviceChargesVoMap);
        /**只更新在这次出账单收费的 或者调整的数据*/
        if (CollectionUtils.isNotEmpty(batchInsertReportVosList)) {
            insertBeforeDataAndCalTotal(batchInsertReportVosList, practiceIdAndProductCodeAndDataMap, bill, templetVo);
            supplierPracticeReportService.insertBatchReports(batchInsertReportVosList, billMonth);
        }

        /**账单一次性*/
        if(CollectionUtils.isNotEmpty(disposableItemVos)){
            Map<Boolean, List<SupplierDisposableItemVo>> collect = disposableItemVos.stream().collect(groupingBy(vo -> vo.getProdType().equals(ExportBillDisposableApprovalEnum.IsDisposalTypeEnum.DISPOSAL_TYPE7.getIndex())));
            //BigDecimal itemAmt = collect.getOrDefault(false,Lists.newArrayList()).stream().map(SupplierDisposableItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal serviceFeeAmt = collect.getOrDefault(true,Lists.newArrayList()).stream().map(SupplierDisposableItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if (templetVo.getReserveFlag().equals(BooleanTypeEnum.NO.getCode())){
//                serviceFeeAmt = BigDecimal.ZERO;
//            }
            //非服务费
            BigDecimal itemAmt = BigDecimal.ZERO;
            List<SupplierDisposableItemVo> supplierDisposableItemVos = collect.get(false);
            if (CollectionUtils.isNotEmpty(supplierDisposableItemVos)) {
                // 过滤掉社保
                if (templetVo.getInsurancelFlag().equals(BooleanTypeEnum.NO.getCode())) {
                    supplierDisposableItemVos = filterItemsByType(supplierDisposableItemVos, ExportBillDisposableApprovalEnum.IsDisposalTypeEnum.DISPOSAL_TYPE1.getIndex());
                }

                // 过滤掉工资
                if (templetVo.getSalaryFlag().equals(BooleanTypeEnum.NO.getCode())) {
                    supplierDisposableItemVos = filterItemsByType(supplierDisposableItemVos, ExportBillDisposableApprovalEnum.IsDisposalTypeEnum.DISPOSAL_TYPE4.getIndex());
                }
                if (CollectionUtils.isNotEmpty(supplierDisposableItemVos)){
                    itemAmt = supplierDisposableItemVos.stream().map(SupplierDisposableItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }

            bill.setBillAmt(bill.getBillAmt().add(itemAmt).add(serviceFeeAmt));
            bill.setServiceFeeOne(serviceFeeAmt);
            bill.setAmtOneTime(itemAmt);
        }
        /**插入首板数据*/
        if (CollectionUtils.isNotEmpty(firstGenerateBill)) {
            insertPerBillInfo(firstGenerateBill, batchInsertReportVosList);
        }

        /*** 插入service_num*/
        if (CollectionUtils.isNotEmpty(serviceNumInfoVoList)) {
            supplierBillServiceNumInfoService.insertBatchData(serviceNumInfoVoList);
        }

        bill.setGenStatus(BillEnum.GenerateBillStatus.SUCCESSED.getCode());
        supplierPracticeBillMapper.updateById(bill);
        logger.info("=====================报表生成完成====================");
    }

    private boolean checkBillVo(Long billId, SupplierPracticeBill bill) {
        if (Objects.isNull(bill)) {
            logger.error("=============根据账单ID没有找到账单数据，账单Id为,{}=================>", billId);
            return true;
        }
        EntityWrapper<SupplierPracticeBill> wrapper = new EntityWrapper<>();
        wrapper.eq("supplier_id",bill.getSupplierId());
        wrapper.eq("templet_id",bill.getTempletId());
        wrapper.ge("bill_month",bill.getBillMonth());
        List<SupplierPracticeBill> bills = supplierPracticeBillMapper.selectList(wrapper);
        boolean anyMatch = bills.stream().anyMatch(vo -> vo.getLockStatus().equals(PracticeBillLockStatusEnum.LOCK.getCode().intValue()));
        if(anyMatch){
            logger.error("=============生成的账单是锁定状态，请排查原因。账单Id 为,{}=================>", billId);
            return true;
        }
        return false;
    }

    /**
     * 服务费不含税
     * （个人 加 企业 加 服务费） * 税率
     * 服务费含税
     * （个人 加 企业）* 税率
     * @param isOutSourcingTwo
     * @param currentList
     * @param quotationInfo
     */
    private static void getTaxCost(boolean isOutSourcingTwo, List<SupplierPracticeReportVo> currentList, SupplierQuotationVo quotationInfo ) {
        if(isOutSourcingTwo){
            Map<String, List<SupplierPracticeReportVo>> reportGroupServiceMonthMap = currentList.stream().collect(groupingBy(vo ->vo.getServiceMonth()+"_"+vo.getFeeType()));
            for (String key : reportGroupServiceMonthMap.keySet()) {
                List<SupplierPracticeReportVo> subData = reportGroupServiceMonthMap.get(key);
                Map<Boolean, List<SupplierPracticeReportVo>> booleanListMap = subData.stream().collect(partitioningBy(vo -> vo.getProdCode().equals(SERVICE_CHARGE_PRODUCTCODE)));
                List<SupplierPracticeReportVo> serviceFeeReports = booleanListMap.getOrDefault(true, Lists.newArrayList());
                List<SupplierPracticeReportVo> prodFeeReports = booleanListMap.getOrDefault(false, Lists.newArrayList());
                SupplierPracticeReportVo reportVo = subData.get(0);
                BigDecimal prodAmt = prodFeeReports.stream().map(SupplierPracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal serviceAmt = serviceFeeReports.stream().map(SupplierPracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal outSourcingTwoValTaxAmt;
//                BigDecimal allAmt = quotationInfo.getTaxFlag().equals(QuotationTaxFlag.TAX_EXCLUSIVE.getCode()) ? prodAmt.add(serviceAmt) : prodAmt;
                /**报价单是外包类型的时候：不管服务费是不是含税，都当做不含税计算**/
                BigDecimal allAmt =  prodAmt.add(serviceAmt) ;
                outSourcingTwoValTaxAmt = allAmt.multiply(quotationInfo.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                SupplierPracticeReportVo taxCost = new SupplierPracticeReportVo();
                BeanUtils.copyProperties(reportVo,taxCost);
                taxCost.setProdCode(TAX_COST_PRODUCTCODE);
                taxCost.setComBase(BigDecimal.ZERO);
                taxCost.setRatioCode(null);
                taxCost.setIndBase(BigDecimal.ZERO);
                taxCost.setComRatio(BigDecimal.ZERO);
                taxCost.setIndRatio(BigDecimal.ZERO);
                taxCost.setComAmt(BigDecimal.ZERO);
                taxCost.setIndAmt(outSourcingTwoValTaxAmt);
                taxCost.setTotalAmt(outSourcingTwoValTaxAmt);
                currentList.add(taxCost);
            }
        }
    }

    private void serviceChargeChangeFun(Long practiceId, Boolean changeFlag, List<SupplierPracticeReportVo> reportVoList, Map<Long, Map<Integer, List<SupplierPracticeProductSnapshotVo>>> practiceProductSnapshotMap,
                                        Map<String, InsuranceRatioVo> ratioVoMap, Map<Long, SupplierTempletFeeCfgVo> templetFeeCfgMap, SupplierPracticeBill bill, Map<Long, SupplierPracticeSnapshotVo> practiceSnapshotMap, SupplierBillTempletVo templetVo,
                                        List<SupplierPracticeReportVo> batchInsertReportVosList, List<SupplierPracticeReportVo> normalReportVos) throws Exception {
        if (changeFlag) {
            List<SupplierPracticeReportVo> needAdjustmentAndReturnData = getNeedAdjustmentAndReturnData(reportVoList, practiceProductSnapshotMap, Maps.newHashMap(), ratioVoMap, templetFeeCfgMap, bill,
                    practiceSnapshotMap.get(practiceId).getBillMonth());
            /**正常收的全部月份都是需要收取服务费*/
            List<SupplierPracticeReportVo> adjustReportVos = computeReceiveAmtAndCollectPerBillItems(needAdjustmentAndReturnData,
                    false, Lists.newArrayList());
            batchInsertReportVosList.addAll(adjustReportVos);
            normalReportVos.addAll(reportVoList);
        } else {
            List<SupplierPracticeReportVo> saveSupplierPracticeReportVos = computeReceiveAmtAndCollectPerBillItems(reportVoList, false, Lists.newArrayList());
            if (!saveSupplierPracticeReportVos.isEmpty()) {
                batchInsertReportVosList.addAll(saveSupplierPracticeReportVos);
                normalReportVos.addAll(reportVoList);
            }
        }
    }


    private void handleNoExistCurrData(List<Long> noExistCurrData, Integer billMonth) {
        perSupplierBillInfoMapper.deletePerSupplierBillInfoVosByPracIds(noExistCurrData, billMonth);
        snapshotService.deletePracticeSnapshotByPracIdAndBillMonth(noExistCurrData, billMonth);
        nonMonthlyProdService.deleteSupplierNonMonthlyProdVo(noExistCurrData, billMonth);
        /** 修改before数据*/
//        List<BeforePracticeServiceMonthVo> beforeOrderServiceMonths = beforePracticeServiceMonthService.getBeforePracticeServiceMonths(noExistCurrData);
//        for (BeforePracticeServiceMonthVo beforeOrderServiceMonth : beforeOrderServiceMonths) {
//            Map<Integer, Set<Integer>> serviceMonthMap = Maps.newHashMap();
//            Map<Integer, Set<Integer>> serviceMonthCacheMap = Maps.newHashMap();
//            Map<Integer, Set<Integer>> yearServiceMonthMap = Maps.newHashMap();
//            Map<Integer, Set<Integer>> yearServiceMonthCacheMap = Maps.newHashMap();
//            if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonth())) {
//                Map<Integer, Set<Integer>> copy = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonth());
//                for (Integer serviceMonth : copy.keySet()) {
//                    Set<Integer> billMonths = copy.get(serviceMonth);
//                    if (!billMonths.contains(billMonth)) {
//                        serviceMonthMap.put(serviceMonth, billMonths);
//                    }
//                    if (billMonths.contains(billMonth) && billMonths.size() > 1) {
//                        billMonths.remove(billMonth);
//                        serviceMonthMap.put(serviceMonth, billMonths);
//                    }
//                }
//            }
//            if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonthCache())) {
//                Map<Integer, Set<Integer>> copy = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonthCache());
//                for (Integer billMonthKey : copy.keySet()) {
//                    if (!billMonthKey.equals(billMonth)) {
//                        serviceMonthCacheMap.put(billMonthKey, copy.get(billMonthKey));
//                    }
//                }
//            }
//            if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonth())) {
//                Map<Integer, Set<Integer>> copy = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getYearServiceMonth());
//                for (Integer serviceMonth : copy.keySet()) {
//                    Set<Integer> billMonths = copy.get(serviceMonth);
//                    if (!billMonths.contains(billMonth)) {
//                        yearServiceMonthMap.put(serviceMonth, billMonths);
//                    }
//                    if (billMonths.contains(billMonth) && billMonths.size() > 1) {
//                        billMonths.remove(billMonth);
//                        yearServiceMonthMap.put(serviceMonth, billMonths);
//                    }
//                }
//            }
//            if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonthCache())) {
//                Map<Integer, Set<Integer>> copy = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonthCache());
//                for (Integer billMonthKey : copy.keySet()) {
//                    if (!billMonthKey.equals(billMonth)) {
//                        yearServiceMonthCacheMap.put(billMonthKey, copy.get(billMonthKey));
//                    }
//                }
//            }
//            beforeOrderServiceMonth.setServiceMonth(JsonUtil.beanToJson(serviceMonthMap));
//            beforeOrderServiceMonth.setServiceMonthCache(JsonUtil.beanToJson(serviceMonthCacheMap));
//            beforeOrderServiceMonth.setYearServiceMonth(JsonUtil.beanToJson(yearServiceMonthMap));
//            beforeOrderServiceMonth.setYearServiceMonthCache(JsonUtil.beanToJson(yearServiceMonthCacheMap));
//        }
//        beforePracticeServiceMonthService.updateBeforeServiceMonthById(beforeOrderServiceMonths);
    }


    private void insertPerBillInfo(List<PerSupplierBillInfoVo> firstGenerateBill, List<SupplierPracticeReportVo> batchInsertReportVosList) {
        /**收过费才算首版*/
        Map<Long, Map<Integer, List<SupplierPracticeReportVo>>> pracAndProdMap = batchInsertReportVosList.stream()
                .collect(groupingBy(SupplierPracticeReportVo::getPracticeId, groupingBy(SupplierPracticeReportVo::getProdCode)));
        firstGenerateBill = firstGenerateBill.stream().filter(vo -> {
            Map<Integer, List<SupplierPracticeReportVo>> prodMap = pracAndProdMap.get(vo.getPracticeId());
            return prodMap != null && prodMap.containsKey(vo.getProductCode());
        }).collect(toList());
        if (CollectionUtils.isNotEmpty(firstGenerateBill)) {
            perSupplierBillInfoMapper.insertBatchPerSupplierBillInfoVo(firstGenerateBill);
        }
    }


    /**
     * 获取每一条报表数据 的全部有效服务月
     *
     * @param reportVos：                     报表数据
     * @param billMonth：账单月
     * @param getNonMonthlyProdByProdCodeMap 年缴快照
     * @return
     * @throws Exception
     */
    private Map<Integer, List<Integer>> getCurrentMonthsByProd(List<SupplierPracticeReportVo> reportVos, Integer billMonth, Map<Integer, List<SupplierNonMonthlyProdVo>> getNonMonthlyProdByProdCodeMap) throws Exception {
        Map<Integer, List<SupplierPracticeReportVo>> reportVosMap = reportVos.stream().collect(Collectors.groupingBy(SupplierPracticeReportVo::getProdCode, Collectors.toList()));
        //产品与产品的全部服务月
        Map<Integer, List<Integer>> prodFullMonthsMap = Maps.newHashMap();
        for (Integer prodCode : reportVosMap.keySet()) {
            List<SupplierPracticeReportVo> reportVosSameProd = reportVosMap.get(prodCode);
            reportVosSameProd = reportVosSameProd.stream().sorted(Comparator.comparingInt(SupplierPracticeReportVo::getStartMonth)).collect(toList());
            Integer startMonth = null, endMonth = null, billStartMonth = null, beforeMonths = 0, receiveMonthType = 1, collectFreq = null;
            List<Integer> fullMonths = Lists.newArrayList();
            //根据产品获取产品的全部服务月
            for (int i = 0; i < reportVosSameProd.size(); i++) {
                SupplierPracticeReportVo cfgVo = reportVosSameProd.get(i);
                if (i == 0) {
                    beforeMonths = cfgVo.getBeforeMonths();
//                    receiveMonthType = cfgVo.getReceiveMonthType();
                    receiveMonthType = 1;
//                    collectFreq = cfgVo.getCollectFreq();
                    collectFreq = 1;
                }
                if (BigDecimalUtil.equalsZero(cfgVo.getIndAmt()) && BigDecimalUtil.equalsZero(cfgVo.getComAmt())) {
                    continue;
                }
                if (cfgVo.getEndMonth() != null && cfgVo.getStartMonth() > cfgVo.getEndMonth()) {
                    continue;
                }
                Integer[] params = getReceiveParams(startMonth, billStartMonth, endMonth, cfgVo.getStartMonth(), cfgVo.getBillStartMonth(), cfgVo.getEndMonth());
                startMonth = params[0];
                billStartMonth = params[1];
                endMonth = params[2];
            }

            if (startMonth != null) {
                // 根据perBillInfo表 判断是否为首版账单
                /** {@link ServiceMonthUtil } */
                ReceiveInfo receiveInfo = new ReceiveInfo(receiveMonthType, beforeMonths, collectFreq, billMonth, billStartMonth, startMonth, endMonth, reportVosSameProd.get(0).getFirstBill());
                fullMonths = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getServiceMonths();
            }


            for (int i = 0; i < reportVosSameProd.size(); i++) {
                List<Integer> months = Lists.newArrayList();
                SupplierPracticeReportVo insuranceCfgVo = reportVosSameProd.get(i);
                if (fullMonths.size() > 0) {
                    if (!(BigDecimalUtil.equalsVal(BigDecimal.ZERO, insuranceCfgVo.getIndAmt()) && BigDecimalUtil.equalsVal(BigDecimal.ZERO, insuranceCfgVo.getComAmt()))) {
                        months = getSubMonthList(insuranceCfgVo.getStartMonth(), insuranceCfgVo.getEndMonth(), fullMonths);
                    }
                }
                Boolean isFirstBill = insuranceCfgVo.getFirstBill();
                // 如果该产品为首版账单 但是服务年月为空时，则该产品不插入per_bll_info表中
                if (isFirstBill && CollectionUtils.isEmpty(months)) {
                    isFirstBill = false;
                }
                insuranceCfgVo.setFirstBill(isFirstBill);

                if (isYearPay(insuranceCfgVo)) {
                    List<SupplierNonMonthlyProdVo> nonMonthlyProds = getNonMonthlyProdByProdCodeMap.get(insuranceCfgVo.getProdCode());
                    List<SupplierNonMonthlyProdVo> nonMonthlyProdCacheList = CollectionUtils.isNotEmpty(nonMonthlyProds) ? nonMonthlyProds.stream().filter(
                            item -> item.getBillMonth() <= billMonth).collect(Collectors.toList()) : Lists.newArrayList();

                    if (CollectionUtils.isNotEmpty(months)) {
                        months = months.stream().filter(item -> item >= SupplierPracticeConstants.MIN_REPORT_MONTH).collect(Collectors.toList());
                        boolean firstFlag;
                        if (getNonMonthlyProdByProdCodeMap.containsKey(insuranceCfgVo.getProdCode())) {
                            int count = nonMonthlyProdCacheList.size();
                            firstFlag = (count <= 0);
                        } else {
                            firstFlag = true;
                        }
                        if (!isFirstBill) {
                            firstFlag = false;
                        }
                        ChargeFreqInfo chargeFreqInfo = new ChargeFreqInfo(insuranceCfgVo.getChargeFreq(), insuranceCfgVo.getPayMonth(), insuranceCfgVo.getComAmt(), insuranceCfgVo.getIndAmt(),
                                insuranceCfgVo.getComMonthlyFee(), insuranceCfgVo.getIndMonthlyFee(), firstFlag, months, insuranceCfgVo.getEndMonth());
                        IChargeFreqStrategyFactory chargeFreqStrategyFactory = new ChargeFreqStrategyFactory();
                        ChargeFreqStrategy chargeFreqStrategy = chargeFreqStrategyFactory.createChargeFreqStrategy(chargeFreqInfo);

                        Map<Integer, ProdAmtVo> map = chargeFreqStrategy.getProdAmt();

                        insuranceCfgVo.setFirstBill(firstFlag);
                        insuranceCfgVo.setServiceMonths(Sets.newHashSet(chargeFreqStrategy.getServiceMonths()));

                        HashMap<Integer, ProdAmtVo> newInsertMap = Maps.newHashMap();
                        Set<Integer> periodSet = nonMonthlyProdCacheList.stream().map(SupplierNonMonthlyProdVo::getPeriod).collect(Collectors.toSet());
                        Set<Integer> allYear = map.keySet();
                        for (Integer year : allYear) {
                            ProdAmtVo prodAmtVo = map.get(year);
                            if (!periodSet.contains(prodAmtVo.getYear())) {
                                newInsertMap.put(year, prodAmtVo);
                            }
                        }
                        insuranceCfgVo.setYearProdMap(newInsertMap);

                    } else {
                        insuranceCfgVo.setYearProdMap(Maps.newHashMap());
                    }
                } else {
//                    if (productCodeAndMaxMinReceiveMonthMap.containsKey(itemVo.getProductCode())) {
//                        itemVo.setHavelastBillMonth(true);
//                        /**per 最后一个账单月 */
//                        Map<String, Integer> maxMinReceiveMonthMap = productCodeAndMaxMinReceiveMonthMap.get(itemVo.getProductCode());
//                        /**per 最大收费月 */
//                        itemVo.setMaxReceivableMonth(maxMinReceiveMonthMap.getOrDefault(CommonConstants.MAX, null));
//                        itemVo.setMinReceivableMonth(maxMinReceiveMonthMap.getOrDefault(CommonConstants.MIN, null));
//                    }
                    insuranceCfgVo.setServiceMonths(Sets.newHashSet(months));
                }
            }

            prodFullMonthsMap.put(prodCode, fullMonths);
        }
        return prodFullMonthsMap;
    }


    private List<SupplierBillServiceNumInfoVo> dealBillServiceNumInfoList(List<SupplierPracticeReportVo> batchInsertReportVosList, SupplierPracticeBill bill, Map<Long, List<PracServiceChargeVo>> serviceChargeVoMap) {
        List<SupplierBillServiceNumInfoVo> billServiceNumInfos = Lists.newArrayList();
        /**服务费*/
        List<SupplierPracticeReportVo> reportVos = batchInsertReportVosList.stream().filter(vo -> isServiceFeeProdCode(vo.getProdCode())).collect(toList());
        List<String> quoteNos = reportVos.stream().map(SupplierPracticeReportVo::getRatioCode).collect(toList());
        Map<String, PracServiceChargeVo> quotationVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(quoteNos)) {
            List<PracServiceChargeVo> quotationVos = iSupplierPracticeWrapperService.getSupplierQuotationVoByNos(quoteNos);
            quotationVoMap = quotationVos.stream().collect(toMap(PracServiceChargeVo::getQuotationNo, Function.identity()));
        }
        /**正常收，或者全额退*/
        List<SupplierPracticeReportVo> normalCharge = Lists.newArrayList();
        /**服务月  与服务费对应月份不一致的看做调基不算人数*/
        for (SupplierPracticeReportVo reportVo : reportVos) {
//               List<PracServiceChargeVo> serviceChargeVos = serviceChargeVoMap.get(reportVo.getPracticeId());
//               if (CollectionUtils.isNotEmpty(serviceChargeVos)) {
//                   PracServiceChargeVo chargeVo = serviceChargeVos.stream()
//                           .filter(vo -> vo.getRevStartMonth() <= reportVo.getServiceMonth() && (Objects.isNull(vo.getRevEndMonth()) || reportVo.getServiceMonth() <= vo.getRevEndMonth())).findFirst().orElse(null);
//                   if (Objects.nonNull(chargeVo) && isNormalFee(reportVo, chargeVo)) {
//                       batchInsertPerInsuranceBillList.add(reportVo);
//                   }
//               }
            PracServiceChargeVo chargeVo = quotationVoMap.get(reportVo.getRatioCode());
            if (Objects.nonNull(chargeVo) && isNormalFee(reportVo, chargeVo)) {
                normalCharge.add(reportVo);
            }
        }
        Long billId = bill.getId();
        String creator = bill.getCreator();

        Map<String, Map<String, List<SupplierPracticeReportVo>>> quoteNoAndCompareAndPerInsuranceBillMap = Maps.newHashMap(); // 用于 per_insurance_bill list根据quote_no 分组 然后再根据 大于0 小于0 分组
        Map<String, BigDecimal> quoteNoAndHaveTaxFeeMap = Maps.newHashMap();    // quoteNo与含税服务费Map

        if (CollectionUtils.isNotEmpty(normalCharge)) {
            /**获取到有金额 且根据城市分组*/
            quoteNoAndCompareAndPerInsuranceBillMap = normalCharge.stream().filter(item -> item.getTotalAmt().compareTo(BigDecimal.ZERO) != 0)
                    .collect(groupingBy(vo -> vo.getCityCode() + "_" + vo.getTotalAmt(), groupingBy(item -> {
                        if (item.getTotalAmt().compareTo(BigDecimal.ZERO) > 0)
                            return GT;
                        else
                            return LT;
                    })));

            /**获取城市 的服务费*/
            quoteNoAndHaveTaxFeeMap = normalCharge.stream().filter(item -> item.getTotalAmt().compareTo(BigDecimal.ZERO) != 0)
                    .collect(Collectors.toMap(vo -> vo.getCityCode() + "_" + vo.getTotalAmt(), SupplierPracticeReportVo::getTotalAmt, (a, b) -> b));
        }

        for (String cityCodeAndAmount : quoteNoAndCompareAndPerInsuranceBillMap.keySet()) {
            Map<String, List<SupplierPracticeReportVo>> positiveAndNegativeMap = quoteNoAndCompareAndPerInsuranceBillMap.get(cityCodeAndAmount);
            /** 金额为正的人次,减去金额为负数 的人次 最后得到总人次 p.s. 人次可以为负数 */
            Integer serviceNum = positiveAndNegativeMap.getOrDefault(GT, Lists.newArrayList()).size() - positiveAndNegativeMap.getOrDefault(LT, Lists.newArrayList()).size();
            SupplierBillServiceNumInfoVo billServiceNumInfo = new SupplierBillServiceNumInfoVo();
            String[] split = cityCodeAndAmount.split("_");
                billServiceNumInfo.setCityCode(Integer.valueOf(split[0])).setBillId(billId).setServiceNum(serviceNum).setCreator(creator);
            if (quoteNoAndHaveTaxFeeMap.containsKey(cityCodeAndAmount)) {
                BigDecimal price = quoteNoAndHaveTaxFeeMap.getOrDefault(cityCodeAndAmount, BigDecimal.ZERO);
                if (price.compareTo(BigDecimal.ZERO) < 0)
                    price = price.negate();
                billServiceNumInfo.setPrice(price);
            }
            billServiceNumInfos.add(billServiceNumInfo);
        }
        BigDecimal serviceFeeAmt = reportVos.stream().map(SupplierPracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer serviceNum = billServiceNumInfos.stream().map(SupplierBillServiceNumInfoVo::getServiceNum).reduce(0, Integer::sum);
        bill.setServiceFee(serviceFeeAmt);
        bill.setServiceNum(serviceNum);
        return billServiceNumInfos;
    }

    private boolean isNormalFee(SupplierPracticeReportVo reportVo, PracServiceChargeVo chargeVo) {
        return chargeVo.getAmount().compareTo(reportVo.getTotalAmt()) == 0 || chargeVo.getAmount().compareTo(reportVo.getTotalAmt().negate()) == 0;
    }

    private boolean isServiceFeeProdCode(Integer prodCode) {
        return prodCode.equals(SERVICE_CHARGE_PRODUCTCODE);
    }


    private void insertBeforeDataAndCalTotal(List<SupplierPracticeReportVo> batchInsertReportVosList, Map<Long, Map<Integer, BeforePracticeServiceMonthVo>> practiceIdAndProductCodeAndDataMap,
                                                  SupplierPracticeBill bill, SupplierBillTempletVo templetVo) {

        List<BeforePracticeServiceMonthVo> editData = Lists.newArrayList();
        List<BeforePracticeServiceMonthVo> insertData = Lists.newArrayList();
        List<SupplierPracticeReportVo> oneTimeFees = batchInsertReportVosList.stream().filter(vo -> vo.getProdCode().equals(InsuranceRatioEnum.ProductCode.DISPOSABLE_FEE.getIndex())).collect(toList());
        BigDecimal oneTimeAmt = oneTimeFees.stream().map(SupplierPracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<SupplierPracticeReportVo> taxCostList = batchInsertReportVosList.stream().filter(vo -> vo.getProdCode().equals(TAX_COST_PRODUCTCODE)).collect(toList());
        BigDecimal taxCostAmt = taxCostList.stream().map(SupplierPracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<Long, Map<Integer, List<SupplierPracticeReportVo>>> reportVoMap = batchInsertReportVosList.stream().filter(vo -> !(vo.getProdCode().equals(InsuranceRatioEnum.ProductCode.DISPOSABLE_FEE.getIndex()) ||
                        vo.getProdCode().equals(TAX_COST_PRODUCTCODE)))
                .collect(groupingBy(SupplierPracticeReportVo::getPracticeId,
                        groupingBy(SupplierPracticeReportVo::getProdCode)));
        //cache 中的月份小于当前月的数据移入serviceMonth 中，将当前月的数据放入cache 中
        Map<Integer, Set<Integer>> serviceMonthMap;
        Map<Integer, Set<Integer>> serviceMonthCacheMap;
        Map<Integer, Set<Integer>> yearServiceMonthMap;
        Map<Integer, Set<Integer>> yearServiceMonthCacheMap;
        /**只有账单模版 是否社保计入总额 是否公积金计入总额选择了 是否工资实发计入总额 都选择是 才会将一次性计入到总额中*/
        if (templetVo.getInsurancelFlag().equals(BooleanTypeEnum.NO.getCode())||
                templetVo.getSalaryFlag().equals(BooleanTypeEnum.NO.getCode())||
                templetVo.getReserveFlag().equals(BooleanTypeEnum.NO.getCode())){
            oneTimeAmt = BigDecimal.ZERO;
        }
        /**添加税点成本*/
        BigDecimal totalAmt = Optional.of(oneTimeAmt).orElse(BigDecimal.ZERO).add(taxCostAmt);
        for (Long practiceId : reportVoMap.keySet()) {

            Map<Integer, BeforePracticeServiceMonthVo> serviceMonthVoMap = practiceIdAndProductCodeAndDataMap.getOrDefault(practiceId, Maps.newHashMap());

            Map<Integer, List<SupplierPracticeReportVo>> prodMap = reportVoMap.get(practiceId);

            Set<Integer> allProdCodes = Sets.newHashSet(prodMap.keySet());
            allProdCodes.addAll(serviceMonthVoMap.keySet());

            for (Integer prodCode : allProdCodes) {
                serviceMonthMap = Maps.newHashMap();
                serviceMonthCacheMap = Maps.newHashMap();
                yearServiceMonthMap = Maps.newHashMap();
                yearServiceMonthCacheMap = Maps.newHashMap();
                BeforePracticeServiceMonthVo beforeOrderServiceMonth = practiceIdAndProductCodeAndDataMap.getOrDefault(practiceId, Maps.newHashMap()).getOrDefault(prodCode, null);
                boolean flag = false;
                List<SupplierPracticeReportVo> reportVos = prodMap.getOrDefault(prodCode,Lists.newArrayList());
                for (SupplierPracticeReportVo vo : reportVos) {
                    if (vo.getProdCode().equals(SERVICE_CHARGE_PRODUCTCODE)) {
                        totalAmt = totalAmt.add(vo.getTotalAmt());
                    } else {
                        // 是否社保计入总额
                        if (BOOLEAN_TRUE == templetVo.getInsurancelFlag()) {
                            if (InsuranceIRatioProductCodeEnum.isSocialSecurityProd(vo.getProdCode())) {
                                totalAmt = totalAmt.add(vo.getTotalAmt());
                            }
                        }
                        // 是否公积金计入总额
                        if (BOOLEAN_TRUE == templetVo.getReserveFlag()) {
                            if (InsuranceIRatioProductCodeEnum.isAccumulationFundProd(vo.getProdCode())) {
                                totalAmt = totalAmt.add(vo.getTotalAmt());
                            }
                        }
                    }
                    if (Objects.isNull(beforeOrderServiceMonth)) {
                        flag = true;
                        beforeOrderServiceMonth = new BeforePracticeServiceMonthVo().setOrderNo(vo.getOrderNo()).setPracticeId(vo.getPracticeId()).setProdCode(vo.getProdCode())
                                .setCreator(vo.getCreator()).setUpdater(vo.getCreator()).setUpdateTime(new Date());
                    }
                    if (isYearPay(vo)) {
                        addMonthToBeforServiceMonth(bill.getBillMonth(), yearServiceMonthMap, yearServiceMonthCacheMap, vo);
                    } else {
                        addMonthToBeforServiceMonth(bill.getBillMonth(), serviceMonthMap, serviceMonthCacheMap, vo);
                    }
                }

                if (!flag) {
                    Integer billMonth = bill.getBillMonth();
                    // 把所有的数据都变成 billMonth serviceMonth
                    if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonth())) {
                        // serviceMonth billMonth
                        Map<Integer, Set<Integer>> perServiceMonthMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonth());
                        /**需要清空当前账单月的数据*/
                        mergeBillAndServiceMonths(serviceMonthMap, serviceMonthCacheMap, perServiceMonthMap,billMonth);
                    }
                    if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonthCache())) {
                        Map<Integer, Set<Integer>> perServiceMonthCacheMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonthCache());
                        mergeBillAndServiceCacheMonths(serviceMonthMap, serviceMonthCacheMap, perServiceMonthCacheMap,billMonth);
                    }

                    if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonth())) {
                        Map<Integer, Set<Integer>> perYearServiceMonthMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getYearServiceMonth());
                        mergeBillAndServiceMonths(yearServiceMonthMap, yearServiceMonthCacheMap, perYearServiceMonthMap,billMonth);
                    }

                    if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonthCache())) {
                        Map<Integer, Set<Integer>> perYearServiceMonthCacheMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getYearServiceMonthCache());
                        mergeBillAndServiceCacheMonths(yearServiceMonthMap, yearServiceMonthCacheMap, perYearServiceMonthCacheMap,billMonth);
                    }
                    beforeOrderServiceMonth.setServiceMonth(JsonUtil.beanToJson(serviceMonthMap));
                    beforeOrderServiceMonth.setServiceMonthCache(JsonUtil.beanToJson(serviceMonthCacheMap));
                    beforeOrderServiceMonth.setYearServiceMonth(JsonUtil.beanToJson(yearServiceMonthMap));
                    beforeOrderServiceMonth.setYearServiceMonthCache(JsonUtil.beanToJson(yearServiceMonthCacheMap));
                    editData.add(beforeOrderServiceMonth);
                } else {
                    beforeOrderServiceMonth.setServiceMonth(JsonUtil.beanToJson(serviceMonthMap));
                    beforeOrderServiceMonth.setServiceMonthCache(JsonUtil.beanToJson(serviceMonthCacheMap));
                    beforeOrderServiceMonth.setYearServiceMonth(JsonUtil.beanToJson(yearServiceMonthMap));
                    beforeOrderServiceMonth.setYearServiceMonthCache(JsonUtil.beanToJson(yearServiceMonthCacheMap));
                    insertData.add(beforeOrderServiceMonth);
                }

            }
        }

        bill.setBillAmt(totalAmt);
        if (CollectionUtils.isNotEmpty(editData)) {
            beforePracticeServiceMonthService.updateBeforeServiceMonthById(editData);
        }
        if (CollectionUtils.isNotEmpty(insertData)) {
            beforePracticeServiceMonthService.insertBeforeServiceMonths(insertData);
        }


    }

    //将billMonth serviceMonth 合并
    private void mergeBillAndServiceCacheMonths(Map<Integer, Set<Integer>> serviceMonthMap, Map<Integer, Set<Integer>> serviceMonthCacheMap, Map<Integer, Set<Integer>> perServiceMonthCacheMap,Integer billMonth) {
        if (MapUtils.isNotEmpty(perServiceMonthCacheMap)) {
            perServiceMonthCacheMap.remove(billMonth);
        }

        for (Integer billMonthForSet : perServiceMonthCacheMap.keySet()) {
            Set<Integer> perServiceMonths = perServiceMonthCacheMap.get(billMonthForSet);
            if (billMonthForSet >= DateUtil.getCurrentYearMonth()) {
                Set<Integer> serviceMonths = serviceMonthCacheMap.computeIfAbsent(billMonthForSet, v -> Sets.newHashSet());
                serviceMonths.addAll(perServiceMonths);
            } else {
                for (Integer perServiceMonth : perServiceMonths) {
                    Set<Integer> billMonths = serviceMonthMap.computeIfAbsent(perServiceMonth, v -> Sets.newHashSet());
                    billMonths.add(billMonthForSet);
                }
            }
        }
    }

    /**
     * @param serviceMonthMap      存储账单月小于当前时间的数据
     * @param serviceMonthCacheMap 存储账单月大于当前时间的数据
     * @param perServiceMonthMap   往期的serviceMonthMap<serviceMonth,set<integer>billMonth>
     */
    private void mergeBillAndServiceMonths(Map<Integer, Set<Integer>> serviceMonthMap, Map<Integer, Set<Integer>> serviceMonthCacheMap,
                                           Map<Integer, Set<Integer>> perServiceMonthMap,Integer billMonth) {
        /** 正常已经收费完的月份数据需要过滤 只能拿当前账单月之前的数据(不包含当前账单月) */
        if (MapUtils.isNotEmpty(perServiceMonthMap)) {
            Set<Integer> keySet = Sets.newHashSet(perServiceMonthMap.keySet());
            for (Integer receiveMonth : keySet) {
                /**   一个收费月只有一个账单月的时候 并且账单月只有一个的时候  要把这一条删掉
                 * 1. 情况1 上月进行收费 并且是首版 往前收三个月   当前月再把 收费起始月调整一下 往前收两个月  但是正常收费月已经有这些数据了
                 *  可不可能出现 往前收,  serviceMonthMap 同一个收费月出现多个账单月, 这种情况我暂时没有想到  目前系统应该不会出现
                 */
                if (perServiceMonthMap.get(receiveMonth).size() == CommonConstants.ONE && Objects.equals(perServiceMonthMap.get(receiveMonth).iterator().next(), billMonth)) {
                    perServiceMonthMap.remove(receiveMonth);
                } else if (perServiceMonthMap.get(receiveMonth).size() > CommonConstants.ONE && perServiceMonthMap.get(receiveMonth).contains(billMonth)) {
                    Set<Integer> billMonthSet = perServiceMonthMap.get(receiveMonth);
                    billMonthSet.remove(billMonth);
                    perServiceMonthMap.put(receiveMonth, billMonthSet);
                }
            }
        }

        for (Integer serviceMonth : perServiceMonthMap.keySet()) {
            Set<Integer> billMonthSet = perServiceMonthMap.get(serviceMonth);
            for (Integer billMonthForSet : billMonthSet) {
                if (billMonthForSet >= DateUtil.getCurrentYearMonth()) {
                    Set<Integer> serviceMonths = serviceMonthCacheMap.computeIfAbsent(billMonthForSet, v -> Sets.newHashSet());
                    serviceMonths.add(serviceMonth);
                } else {
                    Set<Integer> billMonths = serviceMonthMap.computeIfAbsent(serviceMonth, v -> Sets.newHashSet());
                    billMonths.add(billMonthForSet);
                }
            }
        }

    }

    private void addMonthToBeforServiceMonth(Integer billMonth, Map<Integer, Set<Integer>> serviceMonthMap,
                                             Map<Integer, Set<Integer>> serviceMonthCacheMap, SupplierPracticeReportVo vo) {
        if (billMonth >= DateUtil.getCurrentYearMonth()) {
            Set<Integer> serviceMonths = serviceMonthCacheMap.computeIfAbsent(billMonth, v -> Sets.newHashSet());
            serviceMonths.add(vo.getServiceMonth());
            serviceMonthCacheMap.put(billMonth, serviceMonths);
        } else {
            Set<Integer> billMonthMonths = serviceMonthMap.computeIfAbsent(vo.getServiceMonth(), v -> Sets.newHashSet());
            billMonthMonths.add(billMonth);
            serviceMonthMap.put(vo.getServiceMonth(), billMonthMonths);
        }
    }


    private void saveSnapshots(List<SupplierPracticeReportVo> supplierPracticeReportVos, SupplierPracticeBill billVo,
                               Map<Long, String> practiceIdAndHashCode, List<SupplierPracticeReportVo> nonMonthReportVos, Map<Long, String> chargeCodeMap) {
        List<SupplierPracticeSnapshotVo> insertPracticeSnapshot = Lists.newArrayList();
        List<SupplierPracticeProductSnapshotVo> insertProdSnapshot = Lists.newArrayList();
        List<SupplierNonMonthlyProd> nonMonthlyProdList = Lists.newArrayList();
        /**记录是否生成过*/
        Set<Long> recordFlag = Sets.newHashSet();
        //                && item.isCalcFlag()
        Map<Boolean, List<SupplierPracticeReportVo>> reportVoPartitioning = supplierPracticeReportVos.stream().collect(partitioningBy(this::isYearPay));
        List<SupplierPracticeReportVo> yearProdItems = reportVoPartitioning.get(true);
        List<SupplierPracticeReportVo> normalProdItems = reportVoPartitioning.get(false);
        /**只有年缴收取过费用的时间*/
        List<SupplierPracticeReportVo> singleNonMonthReportVos = nonMonthReportVos.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(vo -> vo.getPracticeId() + "" + vo.getProdCode() + vo.getRatioCode() + vo.getPeriod()))), ArrayList::new));
        generateSnapshot(false, normalProdItems, billVo, practiceIdAndHashCode, recordFlag, insertPracticeSnapshot, insertProdSnapshot, nonMonthlyProdList, chargeCodeMap, Lists.newArrayList());
        generateSnapshot(true, singleNonMonthReportVos, billVo, practiceIdAndHashCode, recordFlag, insertPracticeSnapshot, insertProdSnapshot, nonMonthlyProdList, chargeCodeMap, yearProdItems);
        if (CollectionUtils.isNotEmpty(nonMonthlyProdList)) {
            nonMonthlyProdService.insertSupplierNonMonthlyProdVo(nonMonthlyProdList);
        }
        if (CollectionUtils.isNotEmpty(insertPracticeSnapshot)) {
            snapshotService.insertPracticeSnapshot(insertPracticeSnapshot, insertProdSnapshot);
        }
    }

    /***
     * 年缴的时候reportVos 为收取的费用 一个服务费月一条
     * 非年缴的时候reportVos 为供应商实做的产品数据
     * @param isYearPay
     * @param reportVos
     * @param billVo
     * @param practiceIdAndHashCode
     * @param recordFlag
     * @param insertPracticeSnapshot
     * @param insertProdSnapshot
     * @param nonMonthlyProdList
     * @param chargeCodeMap
     */
    private void generateSnapshot(Boolean isYearPay, List<SupplierPracticeReportVo> reportVos, SupplierPracticeBill billVo, Map<Long, String> practiceIdAndHashCode, Set<Long> recordFlag,
                                  List<SupplierPracticeSnapshotVo> insertPracticeSnapshot, List<SupplierPracticeProductSnapshotVo> insertProdSnapshot, List<SupplierNonMonthlyProd> nonMonthlyProdList,
                                  Map<Long, String> chargeCodeMap, List<SupplierPracticeReportVo> yearProdItems) {
        if (CollectionUtils.isNotEmpty(reportVos)) {
            Map<Long, Map<Integer, List<SupplierPracticeReportVo>>> practiceReportVoMap = reportVos.stream().collect(groupingBy(SupplierPracticeReportVo::getPracticeId,
                    groupingBy(SupplierPracticeReportVo::getProdCode)));
            Map<Long, Map<Integer, List<SupplierPracticeReportVo>>> yearProdItemsMap = yearProdItems.stream().collect(groupingBy(SupplierPracticeReportVo::getPracticeId,
                    groupingBy(SupplierPracticeReportVo::getProdCode)));

            for (Long practiceId : practiceReportVoMap.keySet()) {
                Map<Integer, List<SupplierPracticeReportVo>> prodReportsMap = practiceReportVoMap.get(practiceId);
                String orderNo = prodReportsMap.values().stream().flatMap(Collection::stream).findFirst().get().getOrderNo();
                String prodEncryptCode = practiceIdAndHashCode.get(practiceId);
                if (!recordFlag.contains(practiceId)) {
                    String hashCode = chargeCodeMap.get(practiceId);
                    SupplierPracticeSnapshotVo supplierPracticeSnapshot = new SupplierPracticeSnapshotVo();
                    supplierPracticeSnapshot.setProdCryptCode(prodEncryptCode).setChargeCryptCode(hashCode)
                            .setPracticeId(practiceId)
                            .setOrderNo(orderNo).setBillMonth(billVo.getBillMonth())
                            .setCreator(billVo.getCreator());
                    insertPracticeSnapshot.add(supplierPracticeSnapshot);

                }

                for (Integer prodCode : prodReportsMap.keySet()) {
                    List<SupplierPracticeReportVo> reportVosList = prodReportsMap.get(prodCode);
                    /**supplier_practice_product_snapshot 记录供应商实做产品里面的数据
                     * */
                    if (isYearPay) {
                        /**supplier_non_monthly_prod 表记录当前月收费得情况*/
                        for (SupplierPracticeReportVo reportVo : reportVosList) {
                            buildNonMonthlyProd(billVo, nonMonthlyProdList, reportVo);
                        }
                        reportVosList = yearProdItemsMap.getOrDefault(practiceId, Maps.newHashMap()).getOrDefault(prodCode, Lists.newArrayList());
                    }

                    for (SupplierPracticeReportVo reportVo : reportVosList) {
                        buildProdSnapshot(billVo, practiceId, reportVo, insertProdSnapshot, prodCode);
                    }

                }
                recordFlag.add(practiceId);
            }

        }
    }


    private void buildProdSnapshot(SupplierPracticeBill billVo, Long practiceId, SupplierPracticeReportVo vo, List<SupplierPracticeProductSnapshotVo> insertPracticeSnapshot,
                                   Integer prodCode) {
        SupplierPracticeProductSnapshotVo practiceProductSnapshotVo = new SupplierPracticeProductSnapshotVo();
        practiceProductSnapshotVo.setCustId(vo.getCustId())
                .setEmployeeId(vo.getEmployeeId())
                .setPracticeId(practiceId)
                .setOrderNo(vo.getOrderNo())
                .setProdCode(prodCode)
                .setRatioCode(vo.getRatioCode())
                .setCityCode(vo.getCityCode())
                .setRevStartMonth(vo.getStartMonth())
                .setBillStartMonth(vo.getBillStartMonth())
                .setExpiredMonth(vo.getEndMonth())
                .setBillMonth(billVo.getBillMonth())
                .setComBase(Optional.ofNullable(vo.getComBase()).orElse(BigDecimal.ZERO))
                .setIndBase(Optional.ofNullable(vo.getIndBase()).orElse(BigDecimal.ZERO))
                .setComAmt(Optional.ofNullable(vo.getComAmt()).orElse(BigDecimal.ZERO))
                .setIndAmt(vo.getIndAmt())
                .setTempletId(vo.getTempletId())
                .setRevTempId(vo.getRevTempId())
                .setReturnMonth(null)// todo
                .setCreator(billVo.getCreator());
        insertPracticeSnapshot.add(practiceProductSnapshotVo);
    }


    private void buildNonMonthlyProd(SupplierPracticeBill billVo, List<SupplierNonMonthlyProd> nonMonthlyProdList, SupplierPracticeReportVo perBillItem) {
        SupplierNonMonthlyProd nonMonthlyProd = new SupplierNonMonthlyProd();
        nonMonthlyProd.setOrderNo(perBillItem.getOrderNo());
        nonMonthlyProd.setPracticeId(perBillItem.getPracticeId());
        nonMonthlyProd.setProdCode(perBillItem.getProdCode());
        nonMonthlyProd.setComAmt(perBillItem.getComAmt());
        nonMonthlyProd.setIndAmt(perBillItem.getIndAmt());
        nonMonthlyProd.setInsuranceCode(perBillItem.getRatioCode());
        nonMonthlyProd.setBillId(billVo.getId());
        nonMonthlyProd.setBillMonth(billVo.getBillMonth());
        nonMonthlyProd.setPeriod(perBillItem.getPeriod());
        nonMonthlyProd.setReceivableMonth(perBillItem.getServiceMonth());
        nonMonthlyProd.setBillType(perBillItem.getFeeType());
        nonMonthlyProd.setCreator(billVo.getCreator());
        nonMonthlyProdList.add(nonMonthlyProd);
    }

    private List<SupplierPracticeReportVo> computeReceiveAmtAndCollectPerBillItems(List<SupplierPracticeReportVo> reportVos, boolean changeTempletTip, List<SupplierPracticeReportVo> nonMonthReportVos) {
//        BigDecimal indSubtotal = BigDecimal.ZERO, comSubtotal = BigDecimal.ZERO;
        List<SupplierPracticeReportVo> savePerInsuranceBillItemList = Lists.newArrayList();
        // 新增个人社保项
        for (SupplierPracticeReportVo itemVo : reportVos) {
            Set<Integer> serviceMonths = itemVo.getServiceMonths();
            for (Integer receivableMonth : serviceMonths) {
                // 个人社保项的服务年月数组 是否含有服务年月
                SupplierPracticeReportVo reportVo = new SupplierPracticeReportVo();
                BeanUtils.copyProperties(itemVo, reportVo);
                reportVo.setServiceMonth(receivableMonth);
                if (changeTempletTip || itemVo.getStartMonth() <= receivableMonth && (itemVo.getEndMonth() == null || itemVo.getEndMonth() == 0 || receivableMonth <= itemVo.getEndMonth())) {
                    //年缴产品设置
                    if (itemVo.getChargeFreq() != null && (isYearPay(itemVo))) {
                        if (!itemVo.getYearProdMap().isEmpty()) {
                            boolean addItemTip = false;
                            for (ProdAmtVo amtVo : itemVo.getYearProdMap().values()) {
                                if (receivableMonth.equals(amtVo.getServiceMonth())) {
                                    addItemTip = true;
                                    reportVo.setComAmt(amtVo.getComAmt());
                                    reportVo.setIndAmt(amtVo.getIndAmt());
                                    reportVo.setPeriod(amtVo.getYear());
                                    reportVo.setTotalAmt(amtVo.getTotalAmt());
                                    reportVo.setServiceMonth(amtVo.getServiceMonth());
                                    reportVo.setFeeType(itemVo.isNormalFlag() ? PerInsuranceBillType.NORMAL.getCode() : PerInsuranceBillType.ADJUST.getCode());
                                    break;
                                }
                            }
                            if (addItemTip) {
                                nonMonthReportVos.add(reportVo);
                                savePerInsuranceBillItemList.add(reportVo);
                            }
                        } else {
                            continue;
                        }
                    } else {
                        savePerInsuranceBillItemList.add(reportVo);
                    }
                } else {
                    continue;
                }

//                // 是否社保计入总额
//                if (BOOLEAN_TRUE == templetVo.getInsurancelFlag()) {
//                    if (InsuranceIRatioProductCodeEnum.isSocialSecurityProd(itemVo.getProdCode())) {
//                        indSubtotal = indSubtotal.add(itemVo.getIndAmt());
//                        comSubtotal = comSubtotal.add(itemVo.getComAmt());
//                    }
//                }
//                // 是否公积金计入总额
//                if (BOOLEAN_TRUE == templetVo.getReserveFlag()) {
//                    if (InsuranceIRatioProductCodeEnum.isAccumulationFundProd(itemVo.getProdCode())) {
//                        indSubtotal = indSubtotal.add(itemVo.getIndAmt());
//                        comSubtotal = comSubtotal.add(itemVo.getComAmt());
//                    }
//                }

            }
        }

//        bill.setBillAmt(Optional.ofNullable(bill.getBillAmt()).orElse(BigDecimal.ZERO).add(comSubtotal).add(indSubtotal));
        // 修改个人社保小计和公司社保小计
        if (CollectionUtils.isNotEmpty(savePerInsuranceBillItemList)) {
            SupplierPracticeReportVo serviceFeeVo = savePerInsuranceBillItemList.stream().filter(vo -> vo.getProdCode().equals(SERVICE_CHARGE_PRODUCTCODE) &&
                    vo.getFeeType() == PerInsuranceBillType.NORMAL.getCode()).findFirst().orElse(null);
            BigDecimal serviceFeeAmount = Objects.isNull(serviceFeeVo) ? BigDecimal.ZERO : serviceFeeVo.getIndAmt();
            for (SupplierPracticeReportVo reportVo : savePerInsuranceBillItemList) {
                reportVo.setServiceFee(serviceFeeAmount);
            }
        }

        return savePerInsuranceBillItemList;
    }


    /**
     * todo
     *
     * @param practiceIds 实做Id
     * @param billMonth   账单月
     * @return
     */
    public BeforePracticeServiceMonthUtilReturn getReceiveMonthByPracticeIdList(List<Long> practiceIds, Integer billMonth) {

        /**这里查询出来的是包含当前月，这个可以变动的数据**/
        List<BeforePracticeServiceMonthVo> beforeOrderServiceMonths = beforePracticeServiceMonthService.getBeforePracticeServiceMonths(practiceIds);
        BeforePracticeServiceMonthUtilReturn beforeOrderServiceMonthUtilReturn = new BeforePracticeServiceMonthUtilReturn();
        if (CollectionUtils.isEmpty(beforeOrderServiceMonths))
            return beforeOrderServiceMonthUtilReturn;

        /** 存储实做最大最小服务月 */
        Map<Long, Map<Integer, Map<String, Integer>>> practiceIdAndAndProductMaxMinReceiveMonth = Maps.newHashMap();

        /**记录根据金额合并以后真正的服务月与账单月 */
        Map<Long, Map<Integer, Set<Integer>>> practiceIdAndReceiveMonthAndBillMonthSetMap = Maps.newHashMap();

        Set<Long> practiceIdSet = beforeOrderServiceMonths.stream().map(BeforePracticeServiceMonthVo::getPracticeId).collect(toSet());
        /** 一定不会重复,如果报错表示表内数据有误 */
        Map<Long, Map<Integer, BeforePracticeServiceMonthVo>> practiceIdAndProductCodeAndDataMap = beforeOrderServiceMonths.stream().parallel().
                collect(groupingBy(BeforePracticeServiceMonthVo::getPracticeId, toMap(BeforePracticeServiceMonthVo::getProdCode, Function.identity())));
        /**实做 产品 与实做往期服务月数据*/
        beforeOrderServiceMonthUtilReturn.setPracticeIdAndProductCodeAndDataMap(practiceIdAndProductCodeAndDataMap);

        /** 处理收费月 */
        Map<String, Map<Integer, Set<Integer>>> practiceIdAndProductCodeServiceMonthMap = Maps.newHashMap();
        Map<String, Map<Integer, Set<Integer>>> practiceIdAndProductCodeYearServiceMonthMap = Maps.newHashMap();
        Map<Long, Map<Integer, List<Integer>>> orderNoAndProductAndNoDealMonthMap = Maps.newHashMap();

        // 记录账单月 与账单月中存在的实做Id
        Map<Integer, Set<Long>> billMonthAndPracticeIdListMap = Maps.newHashMap();

        List<SupplierPracticeReportVo> allReportList = Lists.newArrayList();
        //记录实做 与全部的账单月
        Map<Long, Set<Integer>> PracticeIdAndServiceMonthListMap = Maps.newHashMap();
        for (Long practiceId : practiceIdSet) {
            Map<Integer, BeforePracticeServiceMonthVo> productCodeAndDataMap = practiceIdAndProductCodeAndDataMap.get(practiceId);

            Set<Integer> allServiceWithPracticeId = PracticeIdAndServiceMonthListMap.getOrDefault(practiceId, Sets.newHashSet());

            for (Integer productCode : productCodeAndDataMap.keySet()) {
                BeforePracticeServiceMonthVo beforeOrderServiceMonth = productCodeAndDataMap.get(productCode);

                Map<Integer, Set<Integer>> serviceMonthMap = Maps.newHashMap();
                Map<Integer, Set<Integer>> serviceMonthCacheMap = Maps.newHashMap();
                Map<Integer, Set<Integer>> yearServiceMonthMap = Maps.newHashMap();
                Map<Integer, Set<Integer>> yearServiceMonthCacheMap = Maps.newHashMap();

                if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonth()))
                    serviceMonthMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonth());
                if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonthCache()))
                    serviceMonthCacheMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonthCache());
                if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonth()))
                    yearServiceMonthMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getYearServiceMonth());
                if (StringUtils.isNotBlank(beforeOrderServiceMonth.getYearServiceMonthCache()))
                    yearServiceMonthCacheMap = JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getYearServiceMonthCache());
                if (StringUtils.isNotBlank(beforeOrderServiceMonth.getNoDealMonth())) {
                    List<Integer> noDealMonth = JsonUtil.jsonToList(beforeOrderServiceMonth.getNoDealMonth(), Integer.class);

                    Map<Integer, List<Integer>> productAndNoDealMonthMapCache = orderNoAndProductAndNoDealMonthMap.getOrDefault(practiceId, Maps.newHashMap());
                    productAndNoDealMonthMapCache.put(productCode, noDealMonth);
                    orderNoAndProductAndNoDealMonthMap.put(practiceId, productAndNoDealMonthMapCache);
                }


                /** 处理缓存数据 */
                dealServiceMonthMap(billMonth, serviceMonthMap, serviceMonthCacheMap);
                dealServiceMonthMap(billMonth, yearServiceMonthMap, yearServiceMonthCacheMap);

                dealServiceMonthMapForChangeTemplet(serviceMonthMap, serviceMonthCacheMap, yearServiceMonthMap, yearServiceMonthCacheMap, allServiceWithPracticeId);
                String sb = practiceId + "-" + productCode;

                practiceIdAndProductCodeServiceMonthMap.put(sb, serviceMonthMap);
                practiceIdAndProductCodeYearServiceMonthMap.put(sb, yearServiceMonthMap);

                Set<Integer> billMonthSet = serviceMonthMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
                Set<Integer> billMonthSetForYear = yearServiceMonthMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
//                billMonthSet.addAll(yearServiceMonthMap.keySet());
                billMonthSet.addAll(billMonthSetForYear);
                /**billMonthSet: 全部的账单月
                 *billMonthAndPracticeIdListMap: 记录billMonth 与 存在billMonth中的实做Id
                 * **/
                for (Integer eachBillMonth : billMonthSet) {
                    if (billMonthAndPracticeIdListMap.containsKey(eachBillMonth)) {
                        Set<Long> practiceIdInBillMonth = billMonthAndPracticeIdListMap.getOrDefault(eachBillMonth, Sets.newHashSet());
                        practiceIdInBillMonth.add(practiceId);
                        billMonthAndPracticeIdListMap.put(eachBillMonth, practiceIdInBillMonth);
                    } else {
                        billMonthAndPracticeIdListMap.put(eachBillMonth, Sets.newHashSet(practiceId));
                    }
                }
            }
            PracticeIdAndServiceMonthListMap.put(practiceId, allServiceWithPracticeId);
        }

        beforeOrderServiceMonthUtilReturn.setPracticeIdAndServiceMonthList(PracticeIdAndServiceMonthListMap);

        Set<Integer> billMonthForSearchItemSet = Sets.newHashSet(billMonthAndPracticeIdListMap.keySet());

        /**将找到所有的账单月的实做 从实做平行表中查询出来进行合并*/
        for (Integer billMonthForSearchItem : billMonthForSearchItemSet) {
            Set<Long> practiceIdsSetForMap = billMonthAndPracticeIdListMap.get(billMonthForSearchItem);
            List<SupplierPracticeReportVo> perInsuranceBillAndItemList = null;
            try {
                perInsuranceBillAndItemList = supplierPracticeReportService.getPracticeReportData(billMonthForSearchItem, practiceIdsSetForMap);
            } catch (Exception e) {
                logger.info("===========", billMonthForSearchItem.toString());
                throw new RuntimeException(e);
            }
            allReportList.addAll(perInsuranceBillAndItemList);
        }

        /**将实做id 产品 和服务月相同的数据进行合并 计算金额总和*/
        Map<String, BigDecimal> practiceIdAndProductCodeAndReMonthAndTotalAmtMap = allReportList.stream()
                .collect(groupingBy(item -> item.getPracticeId() + "-" + item.getProdCode() + "-" + item.getServiceMonth(), reducing(BigDecimal.ZERO, SupplierPracticeReportVo::getAmountCount, BigDecimal::add)));
        //遍历全部的往期数据
        /**获取最大最小服务月*/
        for (Long practiceId : practiceIdSet) {
            Map<Integer, BeforePracticeServiceMonthVo> productCodeAndDataMap = practiceIdAndProductCodeAndDataMap.get(practiceId);
            //记录合并以后的服务月  对于的账单月
            Map<Integer, Set<Integer>> allReceiveMonthBillMonthSetMap = Maps.newHashMap();
            for (Integer productCode : productCodeAndDataMap.keySet()) {
                String sb = practiceId + "-" + productCode;

                //获取的实做 产品的全部服务月
                Map<Integer, Set<Integer>> serviceMonthMap = practiceIdAndProductCodeServiceMonthMap.getOrDefault(sb, Maps.newHashMap());
                Map<Integer, Set<Integer>> yearServiceMonthMap = practiceIdAndProductCodeYearServiceMonthMap.getOrDefault(sb, Maps.newHashMap());

                /** 从数据查询per_insurance_bill_item_数据 如果 合计金额为0 那么 就去掉这一条收费月 */
                dealForInsuranceBillItem(practiceId, productCode, allReceiveMonthBillMonthSetMap, serviceMonthMap, practiceIdAndProductCodeAndReMonthAndTotalAmtMap);
                dealForInsuranceBillItem(practiceId, productCode, allReceiveMonthBillMonthSetMap, yearServiceMonthMap, practiceIdAndProductCodeAndReMonthAndTotalAmtMap);
                /** 将不处理月份加入 */
                List<Integer> noDealMonthList = orderNoAndProductAndNoDealMonthMap.getOrDefault(practiceId, Maps.newHashMap()).getOrDefault(productCode, Lists.newArrayList());
                /** 年缴/月缴收费月合并合并 */
                Set<Integer> receiveMonthSet = Sets.newHashSet(serviceMonthMap.keySet());
                receiveMonthSet.addAll(yearServiceMonthMap.keySet());
                receiveMonthSet.addAll(noDealMonthList);

                /**记录最大最小服务月*/
                if (CollectionUtils.isNotEmpty(receiveMonthSet)) {
                    Map<Integer, Map<String, Integer>> productCodeAndMaxMinMap = practiceIdAndAndProductMaxMinReceiveMonth.getOrDefault(practiceId, Maps.newHashMap());
                    IntSummaryStatistics summaryStatistics = receiveMonthSet.stream().collect(summarizingInt(item -> item));
                    Map<String, Integer> maxMinReceiveMonthMap = Maps.newHashMap();
                    int max = summaryStatistics.getMax();
                    int min = summaryStatistics.getMin();
                    maxMinReceiveMonthMap.put(CommonConstants.MAX, max);
                    maxMinReceiveMonthMap.put(CommonConstants.MIN, min);
                    /** 如果put的时候报错,表示拿到的数据有误  ,orderNo和product应该都是唯一的 */
                    productCodeAndMaxMinMap.put(productCode, maxMinReceiveMonthMap);
                    practiceIdAndAndProductMaxMinReceiveMonth.put(practiceId, productCodeAndMaxMinMap);
                }
            }
            practiceIdAndReceiveMonthAndBillMonthSetMap.put(practiceId, allReceiveMonthBillMonthSetMap);
        }
        if (MapUtils.isNotEmpty(practiceIdAndReceiveMonthAndBillMonthSetMap)) {
            beforeOrderServiceMonthUtilReturn.setPracticeIdAndReceiveAndBillMonthSetMap(practiceIdAndReceiveMonthAndBillMonthSetMap);
        }
        beforeOrderServiceMonthUtilReturn.setPracticeIdAndProductCodeAndMaxMinReceiveMonthMap(practiceIdAndAndProductMaxMinReceiveMonth);
        return beforeOrderServiceMonthUtilReturn;
    }



    /**
     * @param practiceId
     * @param productCode
     * @param allReceiveMonthBillMonthSetMap                   存储全部的服务月
     * @param serviceMonthMap                                  记录在往期服务月中全部的服务月 与账单月
     * @param practiceIdAndProductCodeAndReMonthAndTotalAmtMap 记录的在实做在往期服务月中  服务月与对应的 计算过后真正收取的金额
     */
    private void dealForInsuranceBillItem(Long practiceId, Integer productCode,
                                          Map<Integer, Set<Integer>> allReceiveMonthBillMonthSetMap,
                                          Map<Integer, Set<Integer>> serviceMonthMap,
                                          Map<String, BigDecimal> practiceIdAndProductCodeAndReMonthAndTotalAmtMap) {
        Set<Integer> AllReceiveMonth = Sets.newHashSet(serviceMonthMap.keySet());
        for (Integer receiveMonth : AllReceiveMonth) {
            boolean tip = true;
            Set<Integer> billMonthSet = serviceMonthMap.getOrDefault(receiveMonth, Sets.newHashSet());
            Set<Integer> allBillMonthSet = allReceiveMonthBillMonthSetMap.getOrDefault(receiveMonth, Sets.newHashSet());
            if (billMonthSet.size() > 1) {
                String sb = practiceId + "-" + productCode + "-" + receiveMonth;
                BigDecimal resultAmt = practiceIdAndProductCodeAndReMonthAndTotalAmtMap.getOrDefault(sb, BigDecimal.ZERO);  // 如果是null 意味着根本没收过,或者调基调整没了 所以default 给 0
                if (resultAmt.compareTo(BigDecimal.ZERO) == 0) {
                    serviceMonthMap.remove(receiveMonth);
                    tip = false;
                }
            }
            if (tip) {
                allBillMonthSet.addAll(serviceMonthMap.get(receiveMonth));
                allReceiveMonthBillMonthSetMap.put(receiveMonth, allBillMonthSet);
            }
        }
    }


    /**
     * @param serviceMonthMap          正常服务月Map<receiveMonth,LIST<billMonth>>
     * @param serviceMonthCacheMap     正常缓存服务月Map<billMonth,List<receiveMonth>>
     * @param yearServiceMonthMap      正常服务月Map<receiveMonth,LIST<billMonth>>
     * @param yearServiceMonthCacheMap 正常缓存服务月Map<billMonth,List<receiveMonth>>
     * @param billMonthList            当前订单的账单月 包含所有的
     */
    private void dealServiceMonthMapForChangeTemplet(Map<Integer, Set<Integer>> serviceMonthMap, Map<Integer, Set<Integer>> serviceMonthCacheMap,
                                                     Map<Integer, Set<Integer>> yearServiceMonthMap, Map<Integer, Set<Integer>> yearServiceMonthCacheMap, Set<Integer> billMonthList) {
        billMonthList.addAll(serviceMonthMap.keySet());
        billMonthList.addAll(yearServiceMonthMap.keySet());
        billMonthList.addAll(serviceMonthCacheMap.values().stream().flatMap(Collection::stream).collect(toSet()));
        billMonthList.addAll(yearServiceMonthCacheMap.values().stream().flatMap(Collection::stream).collect(toSet()));
    }


    /**
     * @param billMonth            当前生成账单的月份
     * @param serviceMonthMap      正常已经收费完的月份   Map<receiveMonth,Set<billMonth>>
     * @param serviceMonthCacheMap 缓存月份 Map<billMonth,Set<receiveMonth>>
     */
    private static void dealServiceMonthMap(Integer billMonth, Map<Integer, Set<Integer>> serviceMonthMap, Map<Integer, Set<Integer>> serviceMonthCacheMap) {
        /** 正常已经收费完的月份数据需要过滤 只能拿当前账单月之前的数据(不包含当前账单月) */
        if (MapUtils.isNotEmpty(serviceMonthMap)) {
            Set<Integer> keySet = Sets.newHashSet(serviceMonthMap.keySet());
            for (Integer receiveMonth : keySet) {
                /**   一个收费月只有一个账单月的时候 并且账单月只有一个的时候  要把这一条删掉
                 * 1. 情况1 上月进行收费 并且是首版 往前收三个月   当前月再把 收费起始月调整一下 往前收两个月  但是正常收费月已经有这些数据了
                 *   TODO 2. 可不可能出现 往前收,  serviceMonthMap 同一个收费月出现多个账单月, 这种情况我暂时没有想到  目前系统应该不会出现
                 */
                if (serviceMonthMap.get(receiveMonth).size() == CommonConstants.ONE && serviceMonthMap.get(receiveMonth).iterator().next() >= billMonth) {
                    serviceMonthMap.remove(receiveMonth);
                } else if (serviceMonthMap.get(receiveMonth).size() > CommonConstants.ONE && serviceMonthMap.get(receiveMonth).contains(billMonth)) {
                    Set<Integer> billMonthSet = serviceMonthMap.get(receiveMonth);
                    billMonthSet.remove(billMonth);
                    serviceMonthMap.put(receiveMonth, billMonthSet);
                }
            }
        }
        if (MapUtils.isNotEmpty(serviceMonthCacheMap)) {
            for (Integer billMonthForCache : serviceMonthCacheMap.keySet()) {
                /** 不包含当前月和之后的月份  将缓存中的数据并入*/
                if (billMonthForCache < billMonth) {
                    for (Integer receiveMonthForCache : serviceMonthCacheMap.get(billMonthForCache)) {
                        Set<Integer> billMonthSet = serviceMonthMap.getOrDefault(receiveMonthForCache, Sets.newHashSet());
                        billMonthSet.add(billMonthForCache);
                        serviceMonthMap.put(receiveMonthForCache, billMonthSet);
                    }
                }
            }
        }
    }


    /**
     * @param reportVoList               根据供应商 账单模板查询出来的实做数据
     * @param practiceProductSnapshotMap 根据供应商 账单模板查询出来的实做数据，在根据实做查询出来的最近一次的快照数据
     * @param nonMonthlyMapProdMap       年缴快照
     * @param ratioVoMap                 全部的比例数据
     * @param billTempletFeeCfgVoMap     全部的账单模板数据
     * @param bill                       正在生成账单
     * @param lastBillMonth              快照中最近一次账单月
     * @return
     * @throws Exception
     */
    private List<SupplierPracticeReportVo> getNeedAdjustmentAndReturnData(List<SupplierPracticeReportVo> reportVoList, Map<Long, Map<Integer, List<SupplierPracticeProductSnapshotVo>>> practiceProductSnapshotMap,
                                                                          Map<Long, List<SupplierNonMonthlyProdVo>> nonMonthlyMapProdMap, Map<String, InsuranceRatioVo> ratioVoMap, Map<Long, SupplierTempletFeeCfgVo> billTempletFeeCfgVoMap,
                                                                          SupplierPracticeBill bill, Integer lastBillMonth) throws Exception {
        Integer currMonth = bill.getBillMonth();
        /*** 用来存储当前员工的所有的需要补差的项*/
        List<SupplierPracticeReportVo> adjustReportVos = Lists.newArrayList();
        /** 处理年缴数据   */
        List<SupplierPracticeReportVo> yearBillItemVos = yearProdAdjustmentAndReturnData(reportVoList, currMonth, nonMonthlyMapProdMap);
        /** 过滤年缴数据 并根据产品类型分组  该数据为以往report中数据*/
        Map<Integer, List<SupplierPracticeReportVo>> productMap = reportVoList.stream().filter(isNormalProd()).collect(Collectors.groupingBy(SupplierPracticeReportVo::getProdCode));

        /**region ---------------- 根据产品类型处理每种类型产品S ----------------*/
        for (Map.Entry<Integer, List<SupplierPracticeReportVo>> prodListEntry : productMap.entrySet()) {
            Integer productCode = prodListEntry.getKey();
                List<SupplierPracticeReportVo> reportVosWithProd = prodListEntry.getValue();
            /**按照收费起始月分组*/
            reportVosWithProd.sort(Comparator.comparing(SupplierPracticeReportVo::getStartMonth));
            /**快照中产品*/
            Map<Integer, SupplierPracticeReportBaseVo> snapShotMonthData = Maps.newHashMap();
            /**当前产品*/
            Map<Integer, SupplierPracticeReportBaseVo> currMonthData = Maps.newHashMap();
            /**当前产品所有服务月（包括当前账单月的服务月）*/
            List<Integer> currentTotalMonths = Lists.newArrayList();

            ReceiveInfo receiveInfo = getCurrentReceiveInfo(reportVosWithProd, currMonth);

            /** ------------- 设置该当前起始月截止月S ------------- */
            if (null != receiveInfo) {
                currentTotalMonths = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getFullOfServiceMonths();
            }
            /** ------------- 设置当前起始月截止月E ------------- */

            //已生成账单的最大服务月
            Integer maxReceivableMonth = reportVosWithProd.get(0).getMaxReceivableMonth();
            //已生成账单的一年内最小服务月
            Integer minReceivableMonth = reportVosWithProd.get(0).getMinReceivableMonth();
//            if (maxReceivableMonth != null) {
//                maxHisReceivableMonth = maxReceivableMonth > maxHisReceivableMonth ? maxReceivableMonth : maxHisReceivableMonth;
//            }
//            if (minReceivableMonth != null) {
//                minHisReceivableMonth = minReceivableMonth < minHisReceivableMonth ? minReceivableMonth : minHisReceivableMonth;
//            }
            //实际当月产品的服务月
            Set<Integer> actCurrentServiceMonths = Sets.newHashSet();
            /**存储*/
            Set<Integer> serviceMonthSet = new HashSet<>();
            for (int i = 0; i < reportVosWithProd.size(); i++) {
                SupplierPracticeReportVo billItemVo = reportVosWithProd.get(i);
                /**存储 正在遍历的产品数据的存在的所有服务月*/
                List<Integer> months = Lists.newArrayList();
                if (!(BigDecimalUtil.equalsVal(BigDecimal.ZERO, billItemVo.getIndAmt()) && BigDecimalUtil.equalsVal(BigDecimal.ZERO, billItemVo.getComAmt()))) {
                    months = getSubMonthList(billItemVo.getStartMonth(), billItemVo.getEndMonth(), currentTotalMonths);
                }
                for (Integer month : months) {
                    //大于历史最大服务月的或历史最大服务月为空的且当前账单月中的服务月大月当前月的作为正常账单的服务月
                    /** 第二个或 这里加这个判断是为了防止每个产品里里面的最大收费月为空其实是有最大收费月的,比如 2023年的时候
                     2021离职的人的最大收费月maxReceivableMonth是拿不到的会为null 这里需要加上当前月减去12月的判断来保持,拿取 per_insurance_bill_item 数据的一致*/
                    SupplierPracticeReportBaseVo insuranceItemBaseVo = new SupplierPracticeReportBaseVo();
                    BeanUtils.copyProperties(billItemVo, insuranceItemBaseVo);
                    currMonthData.put(month, insuranceItemBaseVo);
                    if (maxReceivableMonth == null || maxReceivableMonth < month) {
                        actCurrentServiceMonths.add(month);
                        continue;
                    }
                    serviceMonthSet.add(month);

                }
            }
            //把大于历史最大服务月的产品月份加入正常账单的服务月
//           perInsuranceBillVo.getCurrentMonthProdServiceMonthsMap().put(productCode, actCurrentServiceMonths);
            List<SupplierPracticeProductSnapshotVo> snapshots = practiceProductSnapshotMap.getOrDefault(reportVosWithProd.get(0).getPracticeId(), Maps.newHashMap()).getOrDefault(productCode, Lists.newArrayList());
            /** 根据产品的收费类型(月缴,年缴) 过滤掉年缴快照数据 */
            snapshots = snapshots.stream().filter(item -> (Objects.equals(item.getProdCode(), SERVICE_CHARGE_PRODUCTCODE) || !(isYearPay(ratioVoMap.get(item.getRatioCode()))))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(snapshots)) {
                long preRevTempId = snapshots.get(0).getRevTempId();
                SupplierTempletFeeCfgVo preTempletFeeCfgVo = billTempletFeeCfgVoMap.get(preRevTempId);
                //得到开始月 到截止月之间全部的月份
                List<Integer> preTotalMonths = getPreTotalMonths(snapshots, preTempletFeeCfgVo, lastBillMonth);

                for (int m = 0; m < snapshots.size(); m++) {
                    SupplierPracticeProductSnapshotVo cfgSnapshot = snapshots.get(m);
                    List<Integer> preMonths = Lists.newArrayList();
                    if (hasFee(cfgSnapshot)) {
                        preMonths = getSubMonthList(cfgSnapshot.getRevStartMonth(), cfgSnapshot.getExpiredMonth(), preTotalMonths);
                    }
                    for (Integer month : preMonths) {
                        serviceMonthSet.add(month);
                        SupplierPracticeReportBaseVo itemBaseVo = new SupplierPracticeReportBaseVo();
                        InsuranceRatioVo ratioVo = ratioVoMap.get(cfgSnapshot.getRatioCode());
                        if (productCode.equals(SERVICE_CHARGE_PRODUCTCODE)) {
                            itemBaseVo.buildBySnapshot(cfgSnapshot);
                            itemBaseVo.setIndRatio(null);
                            itemBaseVo.setComRatio(null);
                            itemBaseVo.setBillId(bill.getId());
                            itemBaseVo.setSupplierId(bill.getSupplierId());
                            itemBaseVo.setComAmt(BigDecimal.ZERO);
                            /**服务费现在只有月收*/
                            itemBaseVo.setChargeFreq(InsuranceRatioChargeFreq.MONTH_PAY.getCode());
                            snapShotMonthData.put(month, itemBaseVo);
                        } else if (ratioVo != null) {
                            itemBaseVo.buildBySnapshot(cfgSnapshot);
                            itemBaseVo.setIndRatio(ratioVo.getIndRatio());
                            itemBaseVo.setComRatio(ratioVo.getComRatio());
                            itemBaseVo.setBillId(bill.getId());
                            itemBaseVo.setSupplierId(bill.getSupplierId());
                            itemBaseVo.setChargeFreq(ratioVo.getChargeFreq());
                            snapShotMonthData.put(month, itemBaseVo);
                        }
                    }
                }

                //如果全部的产品都退费，需要退服务费，如果只退某一个则不需要
                // 基于当前产品列表比较
                for (Integer billMonth : serviceMonthSet) {
                    SupplierPracticeReportBaseVo preItemBaseVo = snapShotMonthData.get(billMonth);
                    SupplierPracticeReportBaseVo currItemBaseVo = currMonthData.get(billMonth);
                    if (preItemBaseVo == null) {
                        Set<Integer> serviceMonths = Sets.newHashSet();
                        // 需要补差的月
                        SupplierPracticeReportVo billItemVo = new SupplierPracticeReportVo();
                        BeanUtils.copyProperties(currItemBaseVo, billItemVo);
                        billItemVo.setTotalAmt(billItemVo.getIndAmt().add(billItemVo.getComAmt()));
                        serviceMonths.add(billMonth);
                        billItemVo.setServiceMonths(serviceMonths);
                        adjustReportVos.add(billItemVo);
                    } else if (currItemBaseVo == null) {
                        Set<Integer> serviceMonths = Sets.newHashSet();
                        // 需要补差的月
                        SupplierPracticeReportVo billItemVo = new SupplierPracticeReportVo();
                        BeanUtils.copyProperties(preItemBaseVo, billItemVo);
                        billItemVo.setIndAmt(billItemVo.getIndAmt().negate());
                        billItemVo.setComAmt(billItemVo.getComAmt().negate());
                        billItemVo.setComBase(billItemVo.getComBase().negate());
                        billItemVo.setIndBase(billItemVo.getIndBase().negate());
                        // 调基的产品的基数等于当前基数减原来的基数
                        billItemVo.setTotalAmt(billItemVo.getIndAmt().add(billItemVo.getComAmt()));
                        billItemVo.setFeeType(PerInsuranceBillType.ADJUST.getCode());
                        serviceMonths.add(billMonth);
                        billItemVo.setServiceMonths(serviceMonths);
                        adjustReportVos.add(billItemVo);
                        billItemVo.setNormalFlag(false);
                    } else if (!preItemBaseVo.equals(currItemBaseVo)) {
                        // 如果同月份 但是比例或基数或者金额或账单模板不同
                        Set<Integer> serviceMonths = Sets.newHashSet();
                        // 需要补差额
                        SupplierPracticeReportVo billItemVo = new SupplierPracticeReportVo();
                        BeanUtils.copyProperties(currItemBaseVo, billItemVo);
                        billItemVo.setIndAmt(currItemBaseVo.getIndAmt().subtract(preItemBaseVo.getIndAmt()));
                        if (currItemBaseVo.getComAmt().compareTo(BigDecimal.ZERO) == 0 && currItemBaseVo.getIndAmt().compareTo(BigDecimal.ZERO) == 0) {
                            billItemVo.setComBase(preItemBaseVo.getComBase().negate());
                            billItemVo.setIndBase(preItemBaseVo.getIndBase().negate());
                        } else if (preItemBaseVo.getComAmt().compareTo(BigDecimal.ZERO) == 0 && preItemBaseVo.getIndAmt().compareTo(BigDecimal.ZERO) == 0) {
                            billItemVo.setComBase(currItemBaseVo.getComBase());
                            billItemVo.setIndBase(currItemBaseVo.getIndBase());
                        } else {
                            billItemVo.setIndBase(currItemBaseVo.getIndBase().subtract(preItemBaseVo.getIndBase()));
                            billItemVo.setComBase(currItemBaseVo.getComBase().subtract(preItemBaseVo.getComBase()));
                        }
                        billItemVo.setComAmt(currItemBaseVo.getComAmt().subtract(preItemBaseVo.getComAmt()));
                        billItemVo.setTotalAmt(billItemVo.getIndAmt().add(billItemVo.getComAmt()));
                        serviceMonths.add(billMonth);
                        billItemVo.setServiceMonths(serviceMonths);
                        billItemVo.setCreator(reportVosWithProd.get(0).getCreator());
                        billItemVo.setUpdater(reportVosWithProd.get(0).getCreator());
                        billItemVo.setNormalFlag(false);
                        if (billItemVo.getIndAmt().compareTo(BigDecimal.ZERO) != 0 || billItemVo.getComAmt().compareTo(BigDecimal.ZERO) != 0) {
                            billItemVo.setFeeType(PerInsuranceBillType.ADJUST.getCode());
                            adjustReportVos.add(billItemVo);
                        }
                    }
                }
            } else if(CollectionUtils.isNotEmpty(serviceMonthSet)){
                /**没有快照，但是可能是 年缴变更为月缴
                 * 所以还是要进行处理*/
                Map<Integer, List<SupplierPracticeReportVo>> integerListMap = yearBillItemVos.stream().collect(groupingBy(SupplierPracticeReportVo::getProdCode));
                for (Integer serviceMonth : serviceMonthSet) {
                    SupplierPracticeReportBaseVo baseVo = currMonthData.get(serviceMonth);
                    List<SupplierPracticeReportVo> targetList = integerListMap.getOrDefault(productCode, Lists.newArrayList());
                    targetList.stream().filter(vo -> vo.getServiceMonths() != null && vo.getServiceMonths().contains(serviceMonth)).findFirst().ifPresent(reportVo -> {
                        String ratioCode = reportVo.getRatioCode();
                        InsuranceRatioVo ratioVo = ratioVoMap.get(ratioCode);
                        Integer currYear = getYear(serviceMonth / 100,ratioVo.getPayMonth(),serviceMonth % 100);
                        Map<Integer, ProdAmtVo> yearProdMap = reportVo.getYearProdMap();
                        ProdAmtVo prodAmtVo = yearProdMap.get(currYear);
                        if(prodAmtVo != null){
                            BigDecimal comAmtByYear = Optional.ofNullable(prodAmtVo.getComAmt()).orElse(BigDecimal.ZERO);
                            BigDecimal indAmtByYear = Optional.ofNullable(prodAmtVo.getIndAmt()).orElse(BigDecimal.ZERO);
                            BigDecimal comAmt = Optional.ofNullable(baseVo.getComAmt()).orElse(BigDecimal.ZERO);
                            BigDecimal indAmt = Optional.ofNullable(baseVo.getIndAmt()).orElse(BigDecimal.ZERO);
                            BigDecimal comTotal = comAmtByYear.add(comAmt);
                            BigDecimal indTotal = indAmtByYear.add(indAmt);
                            prodAmtVo.setComAmt(comTotal);
                            prodAmtVo.setIndAmt(indTotal);
                            prodAmtVo.setTotalAmt(comTotal.add(indTotal));
                        }
                    });
                }

            }



            //                //可能中途中加入产品的情况或者删除后产品再添加情况
            for (Integer billMonth : actCurrentServiceMonths) {
                SupplierPracticeReportBaseVo perInsuranceItemBaseVo = currMonthData.get(billMonth);
                SupplierPracticeReportVo billItemVo = new SupplierPracticeReportVo();
                BeanUtils.copyProperties(perInsuranceItemBaseVo, billItemVo);
                billItemVo.setTotalAmt(billItemVo.getIndAmt().add(billItemVo.getComAmt()));
                billItemVo.getServiceMonths().add(billMonth);
                billItemVo.setFeeType(PerInsuranceBillType.NORMAL.getCode());
                adjustReportVos.add(billItemVo);
            }
        }

        if (CollectionUtils.isNotEmpty(yearBillItemVos)) {
            adjustReportVos.addAll(yearBillItemVos);
        }

        return adjustReportVos;
    }

    private static boolean hasFee(SupplierPracticeProductSnapshotVo cfgSnapshot) {
        return !(BigDecimalUtil.equalsVal(BigDecimal.ZERO, Optional.ofNullable(cfgSnapshot.getIndAmt()).orElse(BigDecimal.ZERO))
                && BigDecimalUtil.equalsVal(BigDecimal.ZERO, Optional.ofNullable(cfgSnapshot.getComAmt()).orElse(BigDecimal.ZERO)));
    }

    private Predicate<SupplierPracticeReportVo> isNormalProd() {
        return c -> !isYearPay(c);
    }


    private List<SupplierPracticeReportVo> yearProdAdjustmentAndReturnData(List<SupplierPracticeReportVo> perInsuranceBill, Integer currMonth, Map<Long, List<SupplierNonMonthlyProdVo>> nonMonthlyProdMap) throws Exception {
        Map<Integer, List<SupplierPracticeReportVo>> productMap = perInsuranceBill.stream().filter(this::isYearPay).collect(Collectors.groupingBy(SupplierPracticeReportVo::getProdCode));
        if (productMap.isEmpty()) {
            return Lists.newArrayList();
        }
        Map<Integer, List<SupplierNonMonthlyProdVo>> nonMonthlyProdByProdCodeMap = Maps.newHashMap();
        if (null != nonMonthlyProdMap && nonMonthlyProdMap.containsKey(perInsuranceBill.get(0).getPracticeId())) {
            List<SupplierNonMonthlyProdVo> nonMonthlyProds = nonMonthlyProdMap.get(perInsuranceBill.get(0).getPracticeId());
            nonMonthlyProdByProdCodeMap = nonMonthlyProds.stream().collect(Collectors.groupingBy(SupplierNonMonthlyProdVo::getProdCode));
        }
        List<SupplierPracticeReportVo> yearBillItemVos = Lists.newArrayList();
        for (Map.Entry<Integer, List<SupplierPracticeReportVo>> integerListEntry : productMap.entrySet()) {
            Integer productCode = integerListEntry.getKey();
            Map<Integer, List<SupplierNonMonthlyProdVo>> nonMonthlyProdByPeriodMap;
            Map<Integer, ProdAmtVo> hisProdAmtVoMap = Maps.newHashMap();
            //年份列表
            Set<Integer> yearSet = new HashSet<>();
            if (nonMonthlyProdByProdCodeMap.containsKey(productCode)) {
                nonMonthlyProdByPeriodMap = nonMonthlyProdByProdCodeMap.get(productCode).stream().collect(Collectors.groupingBy(SupplierNonMonthlyProdVo::getPeriod));
                for (Integer period : nonMonthlyProdByPeriodMap.keySet()) {
                    ProdAmtVo prodAmtVo = new ProdAmtVo();
                    BigDecimal comAmt = BigDecimal.ZERO, indAmt = BigDecimal.ZERO;
                    Integer maxReceivableMonth = 0;
                    for (SupplierNonMonthlyProdVo nonMonthlyProd : nonMonthlyProdByPeriodMap.get(period)) {
                        comAmt = comAmt.add(nonMonthlyProd.getComAmt());
                        indAmt = indAmt.add(nonMonthlyProd.getIndAmt());
                        maxReceivableMonth = maxReceivableMonth > nonMonthlyProd.getReceivableMonth() ? maxReceivableMonth : nonMonthlyProd.getReceivableMonth();
                    }
                    if (maxReceivableMonth >= YEAR_PROD_START_ADJUST_MONTH) {
                        prodAmtVo.setComAmt(comAmt);
                        prodAmtVo.setIndAmt(indAmt);
                        prodAmtVo.setServiceMonth(maxReceivableMonth);
                        prodAmtVo.setTotalAmt(comAmt.add(indAmt));
                        prodAmtVo.setYear(period);
                        hisProdAmtVoMap.put(period, prodAmtVo);
                    }
                }
                yearSet.addAll(hisProdAmtVoMap.keySet());
            }
            //生成年缴费用的最小年
            int genMinYear = yearSet.stream().mapToInt(Integer::intValue).min().orElse(0);
            List<SupplierPracticeReportVo> billItemVos = integerListEntry.getValue();
            billItemVos.sort(Comparator.comparing(SupplierPracticeReportVo::getStartMonth));

            List<Integer> startYearMontList = Lists.newArrayList();

            ReceiveInfo receiveInfo = getCurrentReceiveInfo(billItemVos, currMonth);
            if (null != receiveInfo) {
                //当前产品所有服务（包括当前账单月的服务月）
                List<Integer> currentTotalMonths = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getFullOfServiceMonths();
                startYearMontList = currentTotalMonths.stream().filter(u -> u >= YEAR_PROD_START_MONTH).collect(Collectors.toList());
            }
            Map<Integer, ProdAmtVo> currentAllProdAmtMap = Maps.newHashMap();
            //非零费用的第一个收费段为
            int index = 0;
            Map<Integer, SupplierPracticeReportVo> adjustMap = Maps.newHashMap();
            for (int i = 0; i < billItemVos.size(); i++) {
                SupplierPracticeReportVo billItemVo = billItemVos.get(i);
                if (!(BigDecimalUtil.equalsVal(BigDecimal.ZERO, billItemVo.getIndAmt()) && BigDecimalUtil.equalsVal(BigDecimal.ZERO, billItemVo.getComAmt()))) {
                    List<Integer> months = getSubMonthList(billItemVo.getStartMonth(), billItemVo.getEndMonth(), startYearMontList);
                    if(CollectionUtils.isNotEmpty(months)){
                        Map<Integer, ProdAmtVo> prodAmtVoMap = getRegionProdAmtMap(billItemVo.getChargeFreq(), billItemVo.getPayMonth(), billItemVo.getComAmt(), billItemVo.getIndAmt(),
                                billItemVo.getComMonthlyFee(), billItemVo.getIndMonthlyFee(), index == 0, months, billItemVo.getEndMonth());
                        if (!prodAmtVoMap.isEmpty()) {
                            for (Integer year : prodAmtVoMap.keySet()) {
                                if (genMinYear > 0 && year < genMinYear) {
                                    continue;
                                }
                                ProdAmtVo amtVo = prodAmtVoMap.get(year);
                                //大于调整月的数据才进入调整
                                if (amtVo.getServiceMonth() >= YEAR_PROD_START_ADJUST_MONTH) {
                                    SupplierPracticeReportVo adjustBillItemVo = new SupplierPracticeReportVo();
                                    BeanUtils.copyProperties(billItemVo, adjustBillItemVo);
                                    adjustMap.put(year, adjustBillItemVo);
                                    currentAllProdAmtMap.put(year, amtVo);
                                    yearSet.add(year);
                                }
                            }
                        }
                        index++;
                    }
                }
            }
            for (Integer year : yearSet) {
                ProdAmtVo currentProdAmtVo = currentAllProdAmtMap.get(year);
                ProdAmtVo hisProdAmtVo = hisProdAmtVoMap.get(year);
                SupplierPracticeReportVo billItemVo = adjustMap.get(year);
                //正常账单
                boolean normalFlag = false;
                ProdAmtVo newProdAmt = null;
                if (currentProdAmtVo == null) {
                    newProdAmt = new ProdAmtVo();
                    newProdAmt.setYear(year);
                    newProdAmt.setTotalAmt(hisProdAmtVo.getTotalAmt().negate());
                    newProdAmt.setServiceMonth(hisProdAmtVo.getServiceMonth());
                    newProdAmt.setIndAmt(hisProdAmtVo.getIndAmt().negate());
                    newProdAmt.setComAmt(hisProdAmtVo.getComAmt().negate());
                } else if (hisProdAmtVo == null) {
                    newProdAmt = currentProdAmtVo;
                    normalFlag = true;
                } else if (currentProdAmtVo.getTotalAmt().compareTo(hisProdAmtVo.getTotalAmt()) != 0) {
                    newProdAmt = new ProdAmtVo();
                    newProdAmt.setYear(year);
                    newProdAmt.setServiceMonth(currentProdAmtVo.getServiceMonth());
                    newProdAmt.setTotalAmt(currentProdAmtVo.getTotalAmt().subtract(hisProdAmtVo.getTotalAmt()));
                    newProdAmt.setIndAmt(currentProdAmtVo.getIndAmt().subtract(hisProdAmtVo.getIndAmt()));
                    newProdAmt.setComAmt(currentProdAmtVo.getComAmt().subtract(hisProdAmtVo.getComAmt()));
                }
                if (newProdAmt != null) {
                    if (billItemVo == null) {
                        billItemVo = new SupplierPracticeReportVo();
                        /** 有历史收费段,没有值,取最后一个段  下面条件是为了跳过 后面的验证 */
                        BeanUtils.copyProperties(billItemVos.get(billItemVos.size() - 1), billItemVo);
                        billItemVo.setStartMonth(newProdAmt.getServiceMonth());
                        billItemVo.setEndMonth(null);
                    }
                    Map<Integer, ProdAmtVo> newProdAmtVoMap = Maps.newHashMap();
                    if (newProdAmt.getComAmt().add(newProdAmt.getIndAmt()).compareTo(BigDecimal.ZERO) != 0) {
                        newProdAmtVoMap.put(year, newProdAmt);
                        billItemVo.setServiceMonths(Collections.singleton(newProdAmt.getServiceMonth()));
                    }
                    billItemVo.setYearProdMap(newProdAmtVoMap);
                    billItemVo.setNormalFlag(normalFlag);
                    yearBillItemVos.add(billItemVo);
                }
            }

        }
        return yearBillItemVos;
    }

    private Map<Integer, ProdAmtVo> getRegionProdAmtMap(Integer chargeFreq, Short payMonth, BigDecimal comAmt, BigDecimal indAmt, BigDecimal comMonthlyFee,
                                                        BigDecimal indMonthlyFee, boolean firstFlag, List<Integer> months, Integer expiredMonth) throws Exception {
        ChargeFreqInfo chargeFreqInfo = new ChargeFreqInfo(chargeFreq, payMonth, comAmt, indAmt,
                comMonthlyFee, indMonthlyFee, firstFlag, months, expiredMonth);
        IChargeFreqStrategyFactory chargeFreqStrategyFactory = new ChargeFreqStrategyFactory();
        return chargeFreqStrategyFactory.createChargeFreqStrategy(chargeFreqInfo).getProdAmt();
    }

    private List<Integer> getPreTotalMonths(List<SupplierPracticeProductSnapshotVo> snapshots, SupplierTempletFeeCfgVo preTempletFeeCfgVo, Integer lastBillMonth) throws Exception {
        Integer start = null, billStartMonth = null, end = null;
        Integer beforeMonths = preTempletFeeCfgVo.getBeforeMonths(),
//                receiveMonthType = preTempletFeeCfgVo.getReceiveMonthType(),
                receiveMonthType = 1,
//                collectFreq = preTempletFeeCfgVo.getCollectFreq();
                collectFreq = 1;
        snapshots = snapshots.stream().sorted(Comparator.comparingInt(SupplierPracticeProductSnapshotVo::getRevStartMonth)).collect(toList());
        for (SupplierPracticeProductSnapshotVo snapshot : snapshots) {
            if (BigDecimalUtil.equalsZero(snapshot.getIndAmt()) && BigDecimalUtil.equalsZero(snapshot.getComAmt())) {
                continue;
            }
            if (snapshot.getExpiredMonth() != null && snapshot.getRevStartMonth() > snapshot.getExpiredMonth()) {
                continue;
            }
            Integer[] params = getReceiveParams(start, billStartMonth, end, snapshot.getRevStartMonth(), snapshot.getBillStartMonth(), snapshot.getExpiredMonth());
            start = params[0];
            billStartMonth = params[1];
            end = params[2];
        }
        List<Integer> months = Lists.newArrayList();
        if (start != null) {
            ReceiveInfo receiveInfo = new ReceiveInfo(receiveMonthType, beforeMonths, collectFreq, lastBillMonth, billStartMonth, start, end, false);
            months = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getFullOfServiceMonths();
        }

        return months;
    }


    private List<Integer> getSubMonthList(Integer start, Integer end, List<Integer> fullMonths) {
        List<Integer> months = Lists.newArrayList();
        for (Integer month : fullMonths) {
            if ((start <= month && end == null) || (start <= month && month <= end)) {
                months.add(month);
            }
        }
        return months;
    }

    /**
     * todo 收费月类型类型 和收费频率写死
     *
     * @param reportVosWithProd
     * @param currMonth
     * @return
     */
    private ReceiveInfo getCurrentReceiveInfo(List<SupplierPracticeReportVo> reportVosWithProd, Integer currMonth) {
        if (CollectionUtils.isEmpty(reportVosWithProd))
            return null;
        Integer start = null, billStartMonth = null, end = null;
        Integer beforeMonths = reportVosWithProd.get(0).getBeforeMonths(),
//                receiveMonthType = billItemVos.get(0).getReceiveMonthType(),
                receiveMonthType = 1,
//                collectFreq = billItemVos.get(0).getCollectFreq();
                collectFreq = 1;
        reportVosWithProd = reportVosWithProd.stream().sorted(Comparator.comparingInt(SupplierPracticeReportVo::getStartMonth)).collect(toList());
        for (SupplierPracticeReportVo itemVo : reportVosWithProd) {
            if (BigDecimalUtil.equalsZero(itemVo.getIndAmt()) && BigDecimalUtil.equalsZero(itemVo.getComAmt())) {
                continue;
            }
            if (itemVo.getEndMonth() != null && itemVo.getStartMonth() > itemVo.getEndMonth()) {
                continue;
            }
            Integer[] params = getReceiveParams(start, billStartMonth, end, itemVo.getStartMonth(), itemVo.getBillStartMonth(), itemVo.getEndMonth());
            start = params[0];
            billStartMonth = params[1];
            end = params[2];
        }
        if (start == null)
            return null;
        return new ReceiveInfo(receiveMonthType, beforeMonths, collectFreq, currMonth, billStartMonth, start, end, false);
    }


    private Integer[] getReceiveParams(Integer startMonth, Integer billStartMonth, Integer endMonth, Integer revStartMonth, Integer prodBillStartMonth, Integer expiredMonth) {

        if (startMonth == null) {
            startMonth = revStartMonth;
            billStartMonth = prodBillStartMonth == null ? startMonth : prodBillStartMonth;
            endMonth = expiredMonth;
        } else {
            if (expiredMonth != null && expiredMonth < startMonth) {
                startMonth = revStartMonth;
                billStartMonth = prodBillStartMonth == null ? startMonth : prodBillStartMonth;
            } else {
                endMonth = expiredMonth;
            }
        }

        return new Integer[]{startMonth, billStartMonth, endMonth};
    }

    private Map<String, Boolean> getCryptCodeResult(SupplierPracticeSnapshotVo snapshotVo, String prodEncryptCode, String perServiceEncryptCode) {
        //产品与上期账单产品是否一样，false：不一样，true:一样
        HashMap<String, Boolean> resultMap = Maps.newHashMap();
        boolean prodChangeFlag;
        boolean serviceChangeFlag;
        if (null != snapshotVo) {
            prodChangeFlag = snapshotVo.getProdCryptCode().equals(prodEncryptCode);
            serviceChangeFlag = Optional.ofNullable(snapshotVo.getChargeCryptCode()).orElse("").equals(perServiceEncryptCode);
        } else {
            prodChangeFlag = true;
            serviceChangeFlag = true;
        }
        resultMap.put(CommonConstants.PROD_CHANGE_FLAG, !prodChangeFlag);
        resultMap.put(CommonConstants.SERVICE_CHARGE_FLAG, !serviceChangeFlag);
        return resultMap;
    }

    private List<SupplierPracticeReportVo> buildReportDataByPractice(List<SupplierPracticeVo> supplierPracticeVos, SupplierPracticeBill bill, Map<Long, String> productCodeMap,
                                                                     Map<Long, Map<Integer, Map<String, Integer>>> maxMinReceiveMonthMap, Map<Long, List<PracServiceChargeVo>> serviceChargeVoMap, Map<Long, String> chargeCodeMap,
                                                                     Map<String, SupContractAreaRelativeTempVo> tempVoMap) {
        if (CollectionUtils.isEmpty(supplierPracticeVos)) {
            return Lists.newArrayList();
        }
        List<SupplierProdHandleInfoVo> allProdHandleInfoVos = supplierPracticeVos.stream().flatMap(vo -> vo.getProdHandleInfoVos().stream()).collect(Collectors.toList());
        List<String> ratioCodeList = allProdHandleInfoVos.stream().map(SupplierProdHandleInfoVo::getRatioCode).distinct().collect(Collectors.toList());
        List<InsuranceRatioVo> insuranceRatioVoList = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(ratioCodeList);
        Map<String, InsuranceRatioVo> getInsuranceRatioByRatioCodeMap = insuranceRatioVoList.stream().collect(Collectors.toMap(InsuranceRatioVo::getInsuranceRatioCode, c -> c));
        List<SupplierPracticeReportVo> reportList = Lists.newArrayList();
        List<SupplierPracticeReportVo> subReportList;
        Date date = new Date();
//        Map<String, List<SupplierProdHandleInfoVo>> chargesMap = Maps.newHashMap();
        for (SupplierPracticeVo practiveVo : supplierPracticeVos) {
            String orderNo = practiveVo.getOrderNo();
            Long id = practiveVo.getId();
            List<SupplierProdHandleInfoVo> prodHandleInfoVos = practiveVo.getProdHandleInfoVos();
            getOrderProductHashCode(practiveVo.getId(), prodHandleInfoVos, practiveVo.getRevTempId(), productCodeMap);
//            List<SupplierProdHandleInfoVo> validProd = prodHandleInfoVos.stream().filter(vo -> {
//                boolean flag = vo.getStartMonth() <= Optional.ofNullable(vo.getEndMonth()).orElse(Integer.MAX_VALUE);
//                Integer chargeFreq = getInsuranceRatioByRatioCodeMap.get(vo.getRatioCode()).getChargeFreq();
//                return flag && (chargeFreq != InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode() && chargeFreq != InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode());
//            }).collect(toList());
//            if (CollectionUtils.isNotEmpty(validProd)) {
//                boolean allMatch = validProd.stream().allMatch(vo -> vo.getComAmt().compareTo(BigDecimal.ZERO) == 0 && vo.getIndAmt().compareTo(BigDecimal.ZERO) == 0);
//                if (allMatch) {
//                    validProd.forEach(vo -> vo.setEndMonth(Integer.valueOf((getPrevMonthDate(IntToDate(vo.getStartMonth()), 1).replace("-", "")))));
//                }
//            }
//            /**由有效的产品来生成服务费数据*/
//            if (validProd.isEmpty()) {
//                validProd = prodHandleInfoVos;
//            }
//            /**将产品的最小开始月，赋给服务费
//             * 一个产品多条，且有 最小那一条是倒置无效的数据
//             * 和人为倒置最小一条需要退费的 怎么区分
//             * */
//            Integer minStartMonth = Math.max(validProd.stream().min(Comparator.comparingInt(SupplierProdHandleInfoVo::getStartMonth)).get().getStartMonth(), SupplierPracticeConstants.MIN_SERVICE_CHARGE_MONTH);
//            Integer maxEndMonth = findMaxEndMonth(validProd);
//            if (maxEndMonth != null && maxEndMonth == 0) {
//                throw new RuntimeException("=========出错了，订单号,{}======" + practiveVo.getOrderNo());
//            }
//
//            SupplierProdHandleInfoVo minBillStartMonthProd = validProd.stream().filter(vo -> vo.getBillStartMonth() != null).min(Comparator.comparingInt(SupplierProdHandleInfoVo::getBillStartMonth)).orElse(null);
//            Integer minBillStartMonth = Objects.isNull(minBillStartMonthProd) ? minStartMonth : minBillStartMonthProd.getBillStartMonth();
//            List<SupplierProdHandleInfoVo> serviceCharges = getServiceCharges(serviceChargeVoMap, practiveVo, minStartMonth, maxEndMonth);
//            if (CollectionUtils.isNotEmpty(serviceCharges)) {
//                List<SupplierProdHandleInfoVo> copy = ListUtils.copyListBean(serviceCharges, SupplierProdHandleInfoVo.class).stream().sorted(Comparator.comparingInt(SupplierProdHandleInfoVo::getStartMonth)).collect(toList());
//                List<SupplierProdHandleInfoVo> hitServiceChars = Lists.newArrayList();
//                /**如果是被导致的直接找到导致时候区间数据*/
//                if (minStartMonth > Optional.ofNullable(maxEndMonth).orElse(Integer.MAX_VALUE)) {
//                    /**没有大概率就是服务费数据有问题*/
//                    SupplierProdHandleInfoVo handleInfoVo = copy.stream().filter(vo -> vo.getStartMonth() <= minStartMonth && (Objects.isNull(vo.getEndMonth()) || vo.getEndMonth() >= minStartMonth)).findFirst().get();
//                    handleInfoVo.setEndMonth(maxEndMonth);
//                    handleInfoVo.setBillStartMonth(minBillStartMonth);
//                    hitServiceChars.add(handleInfoVo);
//                } else {
//                    int i = findMinEndMonthCharge(copy, minStartMonth);
//                    int j = findMaxEndMonthCharge(copy, maxEndMonth);
//                    while (i <= j) {
//                        SupplierProdHandleInfoVo handleInfoVo = copy.get(i);
//                        handleInfoVo.setBillStartMonth(minBillStartMonth);
//                        hitServiceChars.add(copy.get(i));
//                        i++;
//                    }
//                }
//
//                /**生成hashcode*/
//                buildChargeCode(chargeCodeMap, tempVoMap, practiveVo, hitServiceChars, minBillStartMonth);
//                prodHandleInfoVos.addAll(hitServiceChars);
//            }
            /***
             * 根据serviceCharge 生成SupplierPracticeReportVo
             */
            List<PracServiceChargeVo> serviceChargeVos = serviceChargeVoMap.get(id);
            if (CollectionUtils.isNotEmpty(serviceChargeVos)) {
                for (PracServiceChargeVo serviceChargeVo : serviceChargeVos) {
                    SupplierPracticeReportVo reportVo = buildServiceReportData(bill, date, practiveVo, serviceChargeVo, maxMinReceiveMonthMap);
                    reportList.add(reportVo);
                }

                /**生成hashcode*/
                buildChargeCode(chargeCodeMap, tempVoMap, practiveVo, serviceChargeVos);
            }
            subReportList = Lists.newArrayList();
            for (SupplierProdHandleInfoVo prodHandleInfoVo : prodHandleInfoVos) {
                SupplierPracticeReportVo supplierPracticeReportVo = buildReportData(bill, getInsuranceRatioByRatioCodeMap, date, practiveVo, prodHandleInfoVo, maxMinReceiveMonthMap);
                subReportList.add(supplierPracticeReportVo);
            }

            reportList.addAll(subReportList);
        }
        return reportList;
    }

    private List<SupplierPracticeReportVo> buildProdHandleByOneTimeFee(List<SupplierOneChargeVo> supplierOneChargeVos, SupplierPracticeBill bill, SupplierPracticeReportVo reportVo, SupplierBillTempletVo templetVo) {
        List<SupplierPracticeReportVo> sub = Lists.newArrayList();
        for (SupplierOneChargeVo oneChargeVo : supplierOneChargeVos) {
            SupplierPracticeReportVo handleInfoVo = new SupplierPracticeReportVo();
            handleInfoVo.setPracticeId(reportVo.getPracticeId());
            handleInfoVo.setOrderNo(oneChargeVo.getOrderNo());
            handleInfoVo.setProdCode(InsuranceRatioEnum.ProductCode.DISPOSABLE_FEE.getIndex());
            handleInfoVo.setStartMonth(oneChargeVo.getHappenMonth());
            handleInfoVo.setEndMonth(oneChargeVo.getHappenMonth());
            handleInfoVo.setBillMonth(bill.getBillMonth());
            handleInfoVo.setBillId(bill.getId());
            handleInfoVo.setFeeType(PerInsuranceBillType.NORMAL.getCode());
            handleInfoVo.setSupplierId(bill.getSupplierId());
            handleInfoVo.setCustId(reportVo.getCustId());
            handleInfoVo.setEmployeeId(reportVo.getEmployeeId());
            handleInfoVo.setTempletId(reportVo.getTempletId());
            handleInfoVo.setRevTempId(reportVo.getRevTempId());
            handleInfoVo.setRevTempId(reportVo.getRevTempId());
            handleInfoVo.setServiceMonth(reportVo.getBillMonth());
            handleInfoVo.setComBase(BigDecimal.ZERO);
            handleInfoVo.setIndBase(BigDecimal.ZERO);
            handleInfoVo.setCreator(bill.getUpdater());
            handleInfoVo.setUpdater(bill.getUpdater());
            handleInfoVo.setCityCode(reportVo.getCityCode());
            BigDecimal comAmt = Optional.ofNullable(oneChargeVo.getFee()).orElse(BigDecimal.ZERO);
            BigDecimal indAmt = Optional.ofNullable(oneChargeVo.getIndFee()).orElse(BigDecimal.ZERO);
            handleInfoVo.setComAmt(comAmt);
            handleInfoVo.setIndAmt(indAmt);
            handleInfoVo.setTotalAmt(comAmt.add(indAmt));
            sub.add(handleInfoVo);
        }
        return sub;
    }

    private int findMinEndMonthCharge(List<SupplierProdHandleInfoVo> copy, Integer minStartMonth) {
        for (int i = 0; i < copy.size(); i++) {
            SupplierProdHandleInfoVo handleInfoVo = copy.get(i);
            if (handleInfoVo.getStartMonth() <= minStartMonth && Optional.ofNullable(handleInfoVo.getEndMonth()).orElse(Integer.MAX_VALUE) >= minStartMonth) {
                handleInfoVo.setStartMonth(minStartMonth);
                return i;
            }
        }
        return copy.size();
    }

    private int findMaxEndMonthCharge(List<SupplierProdHandleInfoVo> copy, Integer maxEndMonth) {
        Integer max = Optional.ofNullable(maxEndMonth).orElse(Integer.MAX_VALUE);
        for (int i = 0; i < copy.size(); i++) {
            SupplierProdHandleInfoVo handleInfoVo = copy.get(i);
            if (handleInfoVo.getStartMonth() <= max && Optional.ofNullable(handleInfoVo.getEndMonth()).orElse(Integer.MAX_VALUE) >= max) {
                handleInfoVo.setEndMonth(maxEndMonth);
                return i;
            }
        }
        return copy.size();
    }

    private Integer findMaxEndMonth(List<SupplierProdHandleInfoVo> prodHandleInfoVos) {
        Integer maxMonth = 0;
        for (SupplierProdHandleInfoVo prodHandleInfoVo : prodHandleInfoVos) {
            if (prodHandleInfoVo.getEndMonth() == null) {
                return null;
            }
            if (prodHandleInfoVo.getEndMonth() > maxMonth) {
                maxMonth = prodHandleInfoVo.getEndMonth();
            }
        }
        return maxMonth;
    }

    private List<SupplierProdHandleInfoVo> getServiceCharges(Map<String, List<SupServiceChargeVo>> serviceChargeVoMap, SupplierPracticeVo practiveVo,
                                                             Integer minStartMonth, Integer maxEndMonth) {
        List<SupplierProdHandleInfoVo> list = Lists.newArrayList();
        String contractAreaNo = practiveVo.getContractAreaNo();
        List<SupServiceChargeVo> serviceChargeVos = serviceChargeVoMap.get(contractAreaNo);
        if (Objects.nonNull(serviceChargeVos)) {
            serviceChargeVos = serviceChargeVos.stream().sorted(Comparator.comparingInt(SupServiceChargeVo::getRevStartMonth)).collect(toList());
            for (SupServiceChargeVo serviceChargeVo : serviceChargeVos) {
                //开始月 小，截至月大于   存在minStartMonth ---截至月这段区间
                if (serviceChargeVo.getRevStartMonth() <= minStartMonth && Optional.ofNullable(serviceChargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE) >= minStartMonth) {
                    SupplierProdHandleInfoVo handleInfoVo = getSupplierProdHandleInfoVo(practiveVo, minStartMonth, serviceChargeVo);
                    list.add(handleInfoVo);
                }
                // 开始月大 或者  截至月小于等于
                else if (isValidRange(minStartMonth, maxEndMonth, serviceChargeVo)) {
                    SupplierProdHandleInfoVo handleInfoVo = getSupplierProdHandleInfoVo(practiveVo, serviceChargeVo);
                    list.add(handleInfoVo);
                }
            }
        }
        return list;
    }

    private static SupplierProdHandleInfoVo getSupplierProdHandleInfoVo(SupplierPracticeVo practiveVo, Integer minStartMonth, SupServiceChargeVo serviceChargeVo) {
        SupplierProdHandleInfoVo handleInfoVo = new SupplierProdHandleInfoVo();
        handleInfoVo.setPracticeId(practiveVo.getId());
        handleInfoVo.setOrderNo(practiveVo.getOrderNo());
        handleInfoVo.setProdCode(SERVICE_CHARGE_PRODUCTCODE);
        handleInfoVo.setRatioCode(serviceChargeVo.getQuoteNo());
        handleInfoVo.setStartMonth(minStartMonth);
        handleInfoVo.setBillStartMonth(serviceChargeVo.getRevStartMonth());
        handleInfoVo.setEndMonth(serviceChargeVo.getRevEndMonth());
        handleInfoVo.setIndAmt(serviceChargeVo.getAmount());
        handleInfoVo.setComBase(BigDecimal.ZERO);
        handleInfoVo.setIndBase(BigDecimal.ZERO);
        return handleInfoVo;
    }

    private SupplierProdHandleInfoVo getSupplierProdHandleInfoVo(SupplierPracticeVo practiveVo, SupServiceChargeVo serviceChargeVo) {
        SupplierProdHandleInfoVo handleInfoVo = new SupplierProdHandleInfoVo();
        handleInfoVo.setPracticeId(practiveVo.getId());
        handleInfoVo.setOrderNo(practiveVo.getOrderNo());
        handleInfoVo.setProdCode(SERVICE_CHARGE_PRODUCTCODE);
        handleInfoVo.setRatioCode(serviceChargeVo.getQuoteNo());
        Integer revStartMonth = serviceChargeVo.getRevStartMonth();
        handleInfoVo.setStartMonth(revStartMonth);
        handleInfoVo.setBillStartMonth(serviceChargeVo.getRevStartMonth());
        handleInfoVo.setEndMonth(serviceChargeVo.getRevEndMonth());
        handleInfoVo.setIndAmt(serviceChargeVo.getAmount());
        handleInfoVo.setComBase(BigDecimal.ZERO);
        handleInfoVo.setIndBase(BigDecimal.ZERO);
        return handleInfoVo;
    }

    private static boolean isValidRange(Integer minStartMonth, Integer maxEndMonth, SupServiceChargeVo serviceChargeVo) {
        return serviceChargeVo.getRevStartMonth() >= minStartMonth ||
                Optional.ofNullable(serviceChargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE) <= Optional.ofNullable(maxEndMonth).orElse(Integer.MAX_VALUE);
    }

    private static void buildChargeCode(Map<Long, String> chargeCodeMap, Map<String, SupContractAreaRelativeTempVo> tempVoMap, SupplierPracticeVo practiceVo, List<PracServiceChargeVo> serviceChargeVos) {
        String contractAreaNo = practiceVo.getContractAreaNo();
        SupContractAreaRelativeTempVo tempVo = tempVoMap.get(contractAreaNo);
        StringBuilder prodSb = new StringBuilder(String.valueOf(practiceVo.getId()));
        serviceChargeVos = serviceChargeVos.stream().sorted(Comparator.comparingInt(PracServiceChargeVo::getRevStartMonth)).collect(Collectors.toList());
        for (PracServiceChargeVo prodHandleInfoVo : serviceChargeVos) {
            prodSb.append(SERVICE_CHARGE_PRODUCTCODE).append(prodHandleInfoVo.getQuotationNo())
                    .append(prodHandleInfoVo.getAmount())
                    .append(prodHandleInfoVo.getRevStartMonth())
                    .append(prodHandleInfoVo.getRevEndMonth()).append(tempVo.getRevTempId());
        }
        String chargeHashCode = EnCryptUtil.getEnCryptCode(prodSb.toString());
        chargeCodeMap.put(practiceVo.getId(), chargeHashCode);
    }


    private void getOrderProductHashCode(Long practiceId, List<SupplierProdHandleInfoVo> prodHandleInfoVos, Long revTempleId, Map<Long, String> productCodeMap) {
        if (CollectionUtils.isEmpty(prodHandleInfoVos)) {
            return;
        }
        StringBuilder prodSb = new StringBuilder(String.valueOf(practiceId));
        prodHandleInfoVos = prodHandleInfoVos.stream().sorted(Comparator.comparingInt(SupplierProdHandleInfoVo::getStartMonth)).collect(Collectors.toList());
        for (SupplierProdHandleInfoVo prodHandleInfoVo : prodHandleInfoVos) {
            prodSb.append(prodHandleInfoVo.getProdCode()).append(prodHandleInfoVo.getRatioCode())
                    .append(prodHandleInfoVo.getComBase()).append(prodHandleInfoVo.getIndBase())
                    .append(prodHandleInfoVo.getComAmt()).append(prodHandleInfoVo.getIndAmt())
                    .append(prodHandleInfoVo.getStartMonth()).append(revTempleId)
                    .append(prodHandleInfoVo.getEndMonth());
        }
        String productHashCode = EnCryptUtil.getEnCryptCode(prodSb.toString());
        productCodeMap.put(practiceId, productHashCode);
    }

    private SupplierPracticeReportVo buildReportData(SupplierPracticeBill bill, Map<String, InsuranceRatioVo> getInsuranceRatioByRatioCodeMap, Date date,
                                                     SupplierPracticeVo practiveVo, SupplierProdHandleInfoVo prodHandleInfoVo, Map<Long, Map<Integer, Map<String, Integer>>> maxMinReceiveMonthMap) {

        SupplierPracticeReportVo supplierPracticeReportVo = new SupplierPracticeReportVo();
        InsuranceRatioVo insuranceRatioVo = getInsuranceRatioByRatioCodeMap.get(prodHandleInfoVo.getRatioCode());
        Integer maxMonth = null, minMonth = null;
        if (maxMinReceiveMonthMap.get(practiveVo.getId()) != null) {
            maxMonth = maxMinReceiveMonthMap.getOrDefault(practiveVo.getId(), new HashMap<>())
                    .getOrDefault(prodHandleInfoVo.getProdCode(), new HashMap<>()).getOrDefault(CommonConstants.MAX, null);
            minMonth = maxMinReceiveMonthMap.getOrDefault(practiveVo.getId(), new HashMap<>())
                    .getOrDefault(prodHandleInfoVo.getProdCode(), new HashMap<>()).getOrDefault(CommonConstants.MIN, null);
        }
        BigDecimal comAmt;
        BigDecimal indAmt;
        if (prodHandleInfoVo.getProdCode() != SERVICE_CHARGE_PRODUCTCODE) {
            if (!BigDecimalUtil.equalsZero(prodHandleInfoVo.getComAmt()) || !BigDecimalUtil.equalsZero(prodHandleInfoVo.getIndAmt())) {
                CalculateArgs args = new CalculateArgs(insuranceRatioVo.getSpecialFlag());
                args.setComArgs(prodHandleInfoVo.getComBase(), insuranceRatioVo.getComRatio(), insuranceRatioVo.getComAdd(), insuranceRatioVo.getComCalcMode(), insuranceRatioVo.getComExactVal());
                args.setIndArgs(prodHandleInfoVo.getIndBase(), insuranceRatioVo.getIndRatio(), insuranceRatioVo.getIndlAdd(), insuranceRatioVo.getIndCalcMode(), insuranceRatioVo.getIndExactVal());
                CalculateUtil.calculateAmt(args);
                comAmt = args.getComAmt();
                indAmt = args.getIndAmt();
            } else {
                comAmt = BigDecimal.ZERO;
                indAmt = BigDecimal.ZERO;
            }

            supplierPracticeReportVo.setComRatio(insuranceRatioVo.getComRatio())
                    .setIndRatio(insuranceRatioVo.getIndRatio())
                    .setChargeFreq(insuranceRatioVo.getChargeFreq())
                    .setPayMonth(insuranceRatioVo.getPayMonth())
                    .setComMonthlyFee(insuranceRatioVo.getComMonthlyFee())
                    .setIndMonthlyFee(insuranceRatioVo.getIndMonthlyFee())
                    .setRatioCode(insuranceRatioVo.getInsuranceRatioCode());
        } else {
            supplierPracticeReportVo.setChargeFreq(InsuranceRatioChargeFreq.MONTH_PAY.getCode());
            supplierPracticeReportVo.setRatioCode(prodHandleInfoVo.getRatioCode());
            comAmt = prodHandleInfoVo.getComAmt();
            indAmt = prodHandleInfoVo.getIndAmt();
        }
        BigDecimal totalAmount = Optional.ofNullable(comAmt).orElse(BigDecimal.ZERO).add(Optional.ofNullable(indAmt).orElse(BigDecimal.ZERO));
        supplierPracticeReportVo.setBillId(bill.getId())
                .setContractAreaNo(practiveVo.getContractAreaNo())
                .setSupplierId(practiveVo.getSupplierId())
                .setCustId(practiveVo.getCustId())
                .setEmployeeId(practiveVo.getEmpId())
                .setBillMonth(bill.getBillMonth())
                .setEmployeeId(practiveVo.getEmpId())
                .setPracticeId(practiveVo.getId())
                .setTempletId(practiveVo.getTempletId())
                .setRevTempId(practiveVo.getRevTempId())
                .setOrderNo(practiveVo.getOrderNo())
                .setProdCode(prodHandleInfoVo.getProdCode())
                .setComBase(prodHandleInfoVo.getComBase())
                .setIndBase(prodHandleInfoVo.getIndBase())
                .setComAmt(comAmt)
                .setIndAmt(indAmt)
                .setFeeType(PerInsuranceBillType.NORMAL.getCode())
                .setCityCode(practiveVo.getCityCode())
                .setTotalAmt(totalAmount)
                .setStartMonth(prodHandleInfoVo.getStartMonth())
                .setEndMonth(prodHandleInfoVo.getEndMonth())
                .setBeforeMonths(practiveVo.getBeforeMonths())
                .setMaxReceivableMonth(maxMonth)
                .setMinReceivableMonth(minMonth)
                .setBillStartMonth(prodHandleInfoVo.getBillStartMonth())
                .setCreator(bill.getCreator())
                .setCreateTime(date).setUpdater(bill.getCreator()).setUpdateTime(date);
        return supplierPracticeReportVo;
    }

    private SupplierPracticeReportVo buildServiceReportData(SupplierPracticeBill bill, Date date,
                                                            SupplierPracticeVo practiveVo, PracServiceChargeVo prodHandleInfoVo, Map<Long, Map<Integer, Map<String, Integer>>> maxMinReceiveMonthMap) {

        SupplierPracticeReportVo supplierPracticeReportVo = new SupplierPracticeReportVo();
        Integer maxMonth = null, minMonth = null;
        if (maxMinReceiveMonthMap.get(practiveVo.getId()) != null) {
            maxMonth = maxMinReceiveMonthMap.getOrDefault(practiveVo.getId(), new HashMap<>())
                    .getOrDefault(SERVICE_CHARGE_PRODUCTCODE, new HashMap<>()).getOrDefault(CommonConstants.MAX, null);
            minMonth = maxMinReceiveMonthMap.getOrDefault(practiveVo.getId(), new HashMap<>())
                    .getOrDefault(SERVICE_CHARGE_PRODUCTCODE, new HashMap<>()).getOrDefault(CommonConstants.MIN, null);
        }
        BigDecimal indAmt = prodHandleInfoVo.getAmount();
        BigDecimal comAmt = BigDecimal.ZERO;
        supplierPracticeReportVo.setChargeFreq(InsuranceRatioChargeFreq.MONTH_PAY.getCode());
        supplierPracticeReportVo.setRatioCode(prodHandleInfoVo.getQuotationNo());
        supplierPracticeReportVo.setBillId(bill.getId())
                .setContractAreaNo(practiveVo.getContractAreaNo())
                .setSupplierId(practiveVo.getSupplierId())
                .setCustId(practiveVo.getCustId())
                .setEmployeeId(practiveVo.getEmpId())
                .setBillMonth(bill.getBillMonth())
                .setEmployeeId(practiveVo.getEmpId())
                .setPracticeId(practiveVo.getId())
                .setTempletId(practiveVo.getTempletId())
                .setRevTempId(practiveVo.getRevTempId())
                .setOrderNo(practiveVo.getOrderNo())
                .setProdCode(SERVICE_CHARGE_PRODUCTCODE)
                .setComBase(BigDecimal.ZERO)
                .setServiceFee(comAmt)
                .setIndBase(BigDecimal.ZERO)
                .setComAmt(comAmt)
                .setIndAmt(indAmt)
                .setFeeType(PerInsuranceBillType.NORMAL.getCode())
                .setCityCode(practiveVo.getCityCode())
                .setTotalAmt(indAmt)
                .setStartMonth(prodHandleInfoVo.getRevStartMonth())
                .setEndMonth(prodHandleInfoVo.getRevEndMonth())
                .setBeforeMonths(practiveVo.getBeforeMonths())
                .setMaxReceivableMonth(maxMonth)
                .setMinReceivableMonth(minMonth)
                .setBillStartMonth(prodHandleInfoVo.getBillStartMonth())
                .setCreator(bill.getCreator())
                .setCreateTime(date).setUpdater(bill.getCreator()).setUpdateTime(date);
        return supplierPracticeReportVo;
    }

    /**
     * 校验实做是否为首次生成
     * 规则： 不存在与首次生成表中  或者存在但是生成月份小于等于首次生成时间
     *
     * @param reportVos
     */
    private List<PerSupplierBillInfoVo> isFirstGenerateBill(List<SupplierPracticeReportVo> reportVos, Integer billMonth, Map<String, PerSupplierBillInfoVo> perSupplierBillInfoMap) {
        if (CollectionUtils.isEmpty(reportVos)) {
            return Lists.newArrayList();
        }
        List<PerSupplierBillInfoVo> perSupplierBillInfoVos = new ArrayList<>();
        boolean flag;
        PerSupplierBillInfoVo data;
        Map<Long, Map<Integer, List<SupplierPracticeReportVo>>> reportVoMap = reportVos.stream().collect(groupingBy(SupplierPracticeReportVo::getPracticeId, groupingBy(SupplierPracticeReportVo::getProdCode)));
        for (Long id : reportVoMap.keySet()) {
            Map<Integer, List<SupplierPracticeReportVo>> prodMap = reportVoMap.get(id);
            for (Integer prodCode : prodMap.keySet()) {
                List<SupplierPracticeReportVo> reportVoList = prodMap.get(prodCode);
                flag = false;
                SupplierPracticeReportVo reportVo = reportVoList.get(0);
                PerSupplierBillInfoVo perSupplierBillInfoVo = perSupplierBillInfoMap.getOrDefault(buildKey(reportVo.getPracticeId(), reportVo.getProdCode()), null);
                if (Objects.isNull(perSupplierBillInfoVo)) {
                    flag = true;
                } else {
                    Integer firstBillMonth = perSupplierBillInfoVo.getFirstBillMonth();
                    if (firstBillMonth >= billMonth) {
                        flag = true;
                    }
                }
                if (flag) {
                    data = new PerSupplierBillInfoVo();
                    data.setCustId(reportVo.getCustId())
                            .setOrderNo(reportVo.getOrderNo())
                            .setEmployeeId(reportVo.getEmployeeId())
                            .setPracticeId(reportVo.getPracticeId())
                            .setProductCode(reportVo.getProdCode())
                            .setFirstBillMonth(billMonth)
                            .setCreator(reportVo.getCreator())
                            .setUpdater(reportVo.getCreator())
                            .setUpdateTime(reportVo.getUpdateTime());
                    perSupplierBillInfoVos.add(data);
                }
                for (SupplierPracticeReportVo vo : reportVoList) {
                    vo.setFirstBill(flag);
                }

            }
        }
        return perSupplierBillInfoVos;
    }


    private String buildKey(Long s, Integer s2) {
        return s + "_" + s2;
    }

    private boolean isYearPay(SupplierPracticeReportVo item) {
        return item.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode()
                || item.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode();
    }

    private boolean isYearPay(InsuranceRatioVo item) {
        return item.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode()
                || item.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode();
    }

    private List<SupplierDisposableItemVo> filterItemsByType(List<SupplierDisposableItemVo> items, int prodType) {
        return items.stream()
                .filter(vo -> !vo.getProdType().equals(prodType))
                .collect(toList());
    }

}
