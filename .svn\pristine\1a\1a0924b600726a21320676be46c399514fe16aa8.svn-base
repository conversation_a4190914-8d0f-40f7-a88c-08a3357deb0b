var ctx = ML.contextPath;
layui.use(['element', 'form', 'layer', 'table', 'laydate', 'tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, $ = layui.jquery,
        tableSelect = layui.tableSelect, util = layui.util;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    var custId = '';
    //
    table.render({
        id: 'billGrid',
        elem: '#billGrid',
        url: ctx + '/bill/invoice/getInvoiceUnApproval',
        method: 'get',
        page: true, //默认为不开启
        limits: [20, 30, 50],
        limit: 20,
        height: 500,
        title: "账单信息",
        toolbar: '#toolbarDemo',
        defaultToolbar: [],
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '2%', fixed: 'left'},
            {type: 'numbers', title: '序号', width: '2.5%', fixed: 'left'},
            {field: 'signComTitle', title: '签单抬头', align: 'center', width: '10%'},
            {field: 'signComTitle1', title: '业绩所属公司', align: 'center', width: '10%'},
            {field: 'custNo', title: '客户编号', align: 'center', width: '9.5%'},
            {field: 'custName', title: '客户名称', align: 'center', width: '10%'},
            {field: 'customerInvoiceName', title: '客户付款方', align: 'center', width: '8%'},
            {field: 'templetName', title: '客户帐套', align: 'center', width: '8%'},
            {
                field: 'invoiceAmt',
                title: '开票金额',
                align: 'center',
                sort: true,
                width: '6%',
                templet: function (d) {
                    return "<a href='javascript:void(0);' style='text-decoration: underline;' lay-event='invoiceAmtEvent'>" + d.invoiceAmt + "</a>";
                }
            },
            {
                field: 'commissioner', title: '提票人', align: 'center', width: '5%', templet: function (d) {
                    return ML.loginNameFormater(d.commissioner);
                }
            },
            {field: 'voucherDate', title: '提票日期', align: 'center', width: '6%'},
            {
                field: 'cancelStatus', title: '审批状态', align: 'center', width: '6%', templet: function (d) {
                    return ML.dictFormatter("DISPOSAL_STATUS", d.cancelStatus);
                }
            },
            {field: 'invoiceRemark', title: '开票备注', align: 'center', width: '8%'}
        ]],
        done: function () {
            ML.hideNoAuth();
            // 监听表格行发票金额
            table.on('tool(billFilter)', function (obj) {
                // 点击发票金额 ，打开开票明细页面
                if (obj.event == 'invoiceAmtEvent') {
                    layer.open({
                        type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                        title: '查看开票明细',
                        area: ['70%', '85%'],
                        shade: 0,
                        maxmin: true,
                        offset: 'auto',
                        shade: [0.8, '#393D49'],
                        content: ML.contextPath + '/customer/invoice/gotoInvoiceQueryDetailView?invoiceId=' + obj.data.id,
                    });
                }
            });
            table.on('toolbar(billFilter)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
                var ids = [];
                if (data.length == 0) {
                    return layer.msg("请选择数据");
                }
                layui.each(data, function (i, item) {
                    ids.push(item.id);
                });
                let param = {};
                switch (obj.event) {
                    case 'reject':
                        // 驳回
                        param.ids = ids;
                        if (data.length > 1) {
                            approvalFun("/bill/invoice/reject", param, 'POST');
                        }
                        if (data.length === 1) {
                            layer.prompt({title: '请输入驳回原因', formType: 2}, function (value, index, elem) {
                                if (value === '') return elem.focus();
                                param.rejectReason = util.escape(value);
                                approvalFun("/bill/invoice/reject", param, 'POST');
                                // 关闭 prompt
                                layer.close(index);
                            });
                        }

                        // approvalFun("/bill/invoice/reject", ids, 'POST');
                        break;
                    //导出
                    case 'export':
                        window.open(ML.contextPath + '/bill/invoice/export?ids=' + ids);
                        break;
                    // 开票
                    case 'invoice':
                        if (data.length > 1) {
                            return layer.msg('只能选择一条');
                        }
                        layer.open({
                            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                            title: '开票',
                            area: ['40%', '70%'],
                            shade: 0,
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/bill/invoice/gotoSetVoiceMsgPage?id=' + ids[0] + '&invoiceAmt=' + data[0].invoiceAmt,
                            end: function () {
                                table.reload('billGrid', {
                                    page: {curr: 1} //重新从第 1 页开始
                                });
                            },
                        });
                        // approvalFun("/bill/invoice/invoice", ids);
                        break;
                    case 'selectInvoiceLog':
                        layer.open({
                            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                            title: '查看开票明细',
                            area: ['90%', '800px'],
                            // shade: 0,
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/customer/invoice/gotoInvoiceLogView?ids=' + ids + '&type=2',
                        });
                        break;
                    case 'automaticInvoice':
                        const array = window.top['dictCachePool']['AUTOMATIC_INVOICE_COMPANY'];

                        for (const item of data) {
                            // 判断item.signComTitleCode是否包含在array中,只要有一个不包含在array中的则不能去自动开票
                            let nameArr = array.map(dict => dict.name);
                            if (nameArr.indexOf(item.signComTitleCode) == -1) {
                                return layer.msg(item.signComTitle + '  不可以自动开票');
                            }
                        }
                        layer.confirm('确定一键开票吗?', {icon: 3}, function () {
                            // 将data数组取到id数组
                            const invoiceIds = data.map(item => item.id);
                            data.map(item => item.signComTitleCode);
                            let dataParam = {
                                invoiceIds: JSON.stringify(invoiceIds),
                                invoiceFlag: 1
                            };
                            console.log(dataParam);
                            $.ajax({
                                url: ML.contextPath + '/bill/invoice/automaticInvoice',
                                type: 'POST',
                                dataType: 'json',
                                data: dataParam,
                                success: function (res) {
                                    layer.msg(res.msg);
                                    queryFun();
                                }
                            });
                        }, function () {
                            layer.msg('已取消');
                        });

                        // queryFun();
                        break;
                    case 'saveNo':
                        saveVoucherNo(data);
                        break;
                    case 'printInvoiceApply':
                        printInvoiceApply(ids);
                        break
                }
            });
        }
    });

    //派单方(分公司)
    var appd2 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#distComName',
        checkedKey: 'orgCode',
        appd: appd2,
        table: {
            url: ML.contextPath + '/sys/org/getCompanyByName',
            cols: [[
                {type: 'radio'},
                {type: 'numbers', title: '序号', align: 'center'},
                {field: 'orgName', title: '分公司名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#distCom").val($("#distComName").attr("ts-selected"));
        }
    });
    //派单方(分公司)
    var appd3 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#distComName1',
        checkedKey: 'orgCode',
        appd: appd3,
        table: {
            url: ML.contextPath + '/sys/org/getCompanyByName',
            cols: [[
                {type: 'radio'},
                {type: 'numbers', title: '序号', align: 'center'},
                {field: 'orgName', title: '分公司名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#distCom1").val($("#distComName1").attr("ts-selected"));
        }
    });

    function printInvoiceApply(ids) {
        if (ids.length !== 1) {
            return layer.msg("请选择一条数据打印");
        }
        var id = ids[0];

        $.ajax({
            type: "GET",
            url: ctx + "/bill/invoice/getInvoiceApplyVoucherData?billInvoiceId=" + id,
            dataType: 'json',
            success: function (data) {
                let invoiceApplyVoucherDataDto = data.data;
                let applyDetailDtoList = invoiceApplyVoucherDataDto.applyDetailDtoList;
                var printHtml = '<div><div style="text-align: center;font-size: 25px;font-weight: bold">' +
                    invoiceApplyVoucherDataDto.orgName +
                    '</div><hr align="center" color="#000" size="2px"/><div style="text-align: center;font-size: 25px;font-weight: bold">开  票  申  请  单</div>' +
                    '<table width="720px" style="border-collapse:collapse;font-size: 10px;text-align: center" class="printTable"><tr style="height: 30px;">' +
                    '<th  style="width:15%;" >开发票客户名称</th>' +
                    '<th  style="width:10%;" >发票内容</th>' +
                    '<th  style="width:15%;" >金额</th>' +
                    '<th  style="width:10%;" >税点/%</th>' +
                    '<th  style="width:17%;" >税收分类</th>' +
                    '<th  style="width:8%;" >专/普票</th>' +
                    '<th  style="width:12%;" >备注</th>' +
                    '<th  style="width:13%;" >其他税收分类</th></tr>\n';

                layui.each(applyDetailDtoList, function (i, item) {
                    let dataHtml = '<tr style="height: 45px;">';
                    if (i == 0) {
                        dataHtml = dataHtml + '<td rowspan="' + applyDetailDtoList.length + '" >' + invoiceApplyVoucherDataDto.invoiceCustomerName + '</td>';
                    }
                    dataHtml = dataHtml + '<td>' + item.invoiceContent + '</td>';
                    dataHtml = dataHtml + '<td>' + item.invoiceAmount + '</td>';
                    dataHtml = dataHtml + '<td>' + item.tax + '</td>';
                    dataHtml = dataHtml + '<td>' + item.taxType + '</td>';
                    dataHtml = dataHtml + '<td>' + item.invoiceType + '</td>';
                    if (i == 0) {
                        dataHtml = dataHtml + '<td rowspan="' + applyDetailDtoList.length + '" >' + invoiceApplyVoucherDataDto.remark + '</td>';
                        dataHtml = dataHtml + '<td rowspan="' + applyDetailDtoList.length + '" >' + invoiceApplyVoucherDataDto.voucherRemark + '</td>';
                    }
                    dataHtml = dataHtml + '</tr>';
                    printHtml = printHtml + dataHtml;
                });

                printHtml = printHtml + '<tr style="height: 45px;"><td>申请开票人</td><td colspan="2">' +
                    invoiceApplyVoucherDataDto.applyName +
                    '</td><td></td><td>申请开票日期</td><td colspan="2">' +
                    invoiceApplyVoucherDataDto.voucherDate + '</td><td></td></tr></table></div>';

                var iframe = document.getElementById("print-iframe");
                if (!iframe) {
                    iframe = document.createElement('IFRAME');
                    var doc = null;
                    iframe.setAttribute("id", "print-iframe");
                    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
                    document.body.appendChild(iframe);
                    doc = iframe.contentWindow.document;
                    doc.write('<style media="print">\n' +
                        '        @page {\n' +
                        '            size: auto;\n' +
                        '            margin: 0;\n' +
                        '        }\n' +
                        '        body {\n' +
                        '            margin: 10mm;\n' +
                        '        }\n' +
                        '        .printTable {\n' +
                        '            -webkit-print-color-adjust: exact;\n' +
                        '            color-adjust: exact;\n' +
                        '        }\n' +
                        '    </style>\n' +
                        '    <style>\n' +
                        '        .printTable th{\n' +
                        '            border:1px solid #000;\n' +
                        '            background-color:#8E8E8E;\n' +
                        '        }\n' +
                        '        .printTable td{\n' +
                        '            border:1px solid #000;\n' +
                        '        }\n' +
                        '    </style>');
                    doc.write(printHtml);
                    doc.close();
                    iframe.contentWindow.focus();
                }
                setTimeout(function () {
                    iframe.contentWindow.print();
                }, 50);
                setTimeout(function () {
                    document.body.removeChild(iframe);
                }, 100);
                return false;
            },
            error: function (data) {
                layer.closeAll('loading');
                console.log("error")
            }
        });

        return false;
    }

    // 保存发票编号
    function saveVoucherNo(data) {
        var arr = [];
        var flag = true;
        layui.each(data, function (i, item) {
            if (!item.voucherNo) {
                flag = false;
                return layer.msg("请输入发票编号");
            }
            var object = {};
            object['id'] = item.id;
            object['voucherNo'] = item.voucherNo;
            arr.push(object);
        });
        if (flag) {
            $.ajax({
                url: ctx + '/bill/invoice/saveVoucherNo',
                type: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(arr),
                success: function (res) {
                    layer.msg(res.msg);
                }
            });
        }

    }

    //监听行双击事件
    table.on('rowDouble(billFilter)', function (obj) {
        var data = obj.data;
        $("#conditionId").css('display', 'inline-block');
        getReceivableAndNetReceipts(data.id);

        //标注选中样式
        obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
    });

    // 获取应收与实收数据
    function getReceivableAndNetReceipts(id) {
        layer.load();
        $.ajax({
            type: "GET",
            url: ctx + "/customer/invoice/getReceivableAndNetReceipts?invoiceId=" + id,
            dataType: 'json',
            success: function (data) {
                layer.closeAll('loading');
                if (data.code == 0) {
                    handlerTableRander(data.data.receivable, data.data.netReceipts);
                } else {
                    return layer.msg(data.msg);
                }
            },
            error: function (data) {
                layer.closeAll('loading');
                console.log("error")
            }
        });
    }

    // 根据复选框选择渲染表格
    function handlerTableRander(writeOddTableData, incoicedTableData) {
        // 渲染应收表格
        table.render({
            id: 'billIds',
            elem: '#billIds',
            page: false,
            data: writeOddTableData,
            toolbar: false,
            defaultToolbar: [],
            limit: Number.MAX_VALUE,
            text: {
                none: '暂无数据' //无数据时展示
            },
            done: function (res, curr, count) {
                ML.hideNoAuth();
            },
            cols: [[
                {field: 'templetName', title: '客户帐套', width: '180', align: 'center', fixed: 'left'},
                {field: 'receivableMonth', title: '财务应收年月', width: '130', align: 'center', fixed: 'left'},
                {field: 'billMonth', title: '客户账单年月', width: '130', align: 'center'},
                {
                    field: 'receiveAmt', title: '应收金额', width: '130', align: 'center', templet: function (d) {
                        return "<a href='javascript:void(0);' style='text-decoration: underline;' lay-event='priceEvent'>" + d.receiveAmt + "</a>";
                    }
                },
                {
                    field: 'cancelStatus', title: '核销状态', width: '120', align: 'center', templet: function (d) {
                        return ML.dictFormatter('CANCEL_STATUS', d.cancelStatus)
                    }
                },
                {
                    field: 'invoiceStatus', title: '开票状态', width: '120', align: 'center', templet: function (d) {
                        return ML.dictFormatter('INVOICE_STATUS', d.invoiceStatus)
                    }
                }
            ]],
            done: function () {
                // 监听表格行应收金额
                table.on('tool(receiveFilter)', function (obj) {
                    var data = obj.data;
                    // 点击应收金额 ，打开应收详情页面
                    if (obj.event == 'priceEvent') {
                        layer.open({
                            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                            title: '应收详情',
                            area: ['85%', '75%'],
                            shade: 0,
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/customer/invoice/invoiceReceivableDetail?custId=' + data.custId + '&templetId=' + data.templetId + '&billMonth=' + data.billMonth + '&contractNo=' + data.contractNo + '&billType=' + data.billType,
                        });
                    }
                })
            }
        });

        // 渲染实收表格
        table.render({
            id: 'billCheckId',
            elem: '#billCheckId',
            page: false,
            data: incoicedTableData,
            toolbar: false,
            defaultToolbar: [],
            limit: Number.MAX_VALUE,
            text: {
                none: '暂无数据' //无数据时展示
            },
            done: function (res, curr, count) {
                ML.hideNoAuth();
            },
            cols: [[
                {field: 'checkAmt', title: '金额', width: '130', align: 'center', fixed: 'left'},
                {field: 'adjustAmt', title: '小额调整', width: '130', align: 'center', fixed: 'left'},
                {field: 'createTime', title: '核销日期', width: '130', align: 'center'},
                {field: 'payTime', title: '到账日期', width: '130', align: 'center'},
                {field: 'payBank', title: '到款银行', width: '130', align: 'center'},
                {field: 'payAmt', title: '到账金额', width: '130', align: 'center'},
                {
                    field: 'payType', title: '支付类型', width: '130', align: 'center', templet: function (d) {
                        return ML.dictFormatter("PAY_METHOD", d.payType);
                    }
                },
                {field: 'payRemark', title: '到款备注', width: '150', align: 'center'}
            ]]
        })
    }

    function approvalFun(url, param, type) {
        $.ajax({
            url: ctx + url,
            type: type,
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(param),
            success: function (result) {
                layer.msg(result.msg);

                if (result.code == 0) {
                    // 清除下表
                    $("#conditionId").css("display", "none");
                    table.reload('billGrid', {
                        page: {curr: 1} //重新从第 1 页开始
                    });
                }
            }
        });
    }

    // js触发查询点击事件
    function queryFun() {
        var data = {
            'custId': custId,
            'flag': true,//表示非手动查询请求
            'status': 1
        };
        table.reload('billGrid', {
            where: data,
            page: {curr: 1} //重新从第 1 页开始
        });
        $("#conditionId").css("display", "none");
    }

    // 查询
    form.on('submit(queryFilter)', function (data) {
        data.field['custId'] = custId;
        data.field['flag'] = true;//表示手动查询请求
        data.field['status'] = 1;

        table.reload('billGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        $("#conditionId").css("display", "none");
        return false;
    });

    // 客户下拉数据表格
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'custId',
        appd: appd,
        table: {
            url: ML.contextPath + '/bill/invoice/getInvoiceUnApprovalCust',
            cols: [[
                {type: 'radio'},
                {field: 'custNo', title: '客户编号', align: 'center', width: '160'},
                {field: 'custName', title: '客户名称', align: 'center', width: '180'},
                {field: 'contractNo', title: '合同编号', align: 'center', width: '160'},
                {field: 'contractName', title: '合同名称', align: 'center', width: '180'},
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
            });
            elem.val(NEWJSON.join(","));
            custId = $("#custName").attr("ts-selected");
            $(".tempOptionId").remove();
            $(".tempOptionInvoiceId").remove();
            // 根据客户Id获取账套下拉框
            ML.ajax("/customer/billTemplet/getListByCustId", {'custId': custId}, function (res) {
                if (res.data.length > 0) {
                    var optionStr = "";
                    layui.each(res.data, function (i, item) {
                        optionStr += "<option class='tempOptionId' value='" + item.id + "'>" + item.templetName + "</option>";
                    });
                    $("#templetId").append(optionStr);
                    form.render('select');
                }
            }, "get");

            // 根据客户Id获取付款方下拉框
            ML.ajax("/customer/invoice/getListByCustId", {'custId': custId}, function (res) {
                if (res.data.length > 0) {
                    var optionStr = "";
                    layui.each(res.data, function (i, item) {
                        optionStr += "<option class='tempOptionInvoiceId' value='" + item.id + "'>" + item.title + "</option>";
                    });
                    $("#custInvoiceId").append(optionStr);
                    form.render('select');
                }
            }, "get");

        }
    });

    // 点击重置时，将选中的值清除
    $("#resetBtn").on('click', function () {
        $("#custName").removeAttr("ts-selected");
        $("#distCom").val("");
        $("#signCom1").val("");
        custId = null;
        $(".tempOptionId").remove();
        $(".tempOptionInvoiceId").remove();

    });

    // 获取日期插件
    getLayDate("#receivableMonthMax");
    getLayDate("#receivableMonthMin");

    function getLayDate(elem) {
        var initYear;
        laydate.render({
            elem: elem,
            theme: 'grid',
            type: 'month',
            min: '2010-01-01',
            max: '2099-12-12',
            format: 'yyyyMM',
            // 点击即选中
            /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
            ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
                initYear = date.year;
            },
            change: function (value, date, endDate) {
                var selectYear = date.year;
                var differ = selectYear - initYear;
                if (differ == 0) {
                    if ($(".layui-laydate").length) {
                        $(elem).val(value);
                        $(".layui-laydate").remove();
                    }
                }
                initYear = selectYear;
            }
        });
    }

    //
    lay('.invoiceDate').each(function () {
        laydate.render({
            elem: this
            , trigger: 'click'
            , min: '2010-01-01'
            , max: '2099-12-12'
            , theme: 'grid'
            , calendar: true
            , format: 'yyyy-MM-dd'
        });
    });

    $(document).ready(function () {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getResponsibleService",
            dataType: 'json',
            success: function (data) {
                $.each(data.data, function (i, item) {
                    $("#commissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
                queryFun();
            },
            error: function (data) {
                layer.msg(data);
            }
        });
    })
});
