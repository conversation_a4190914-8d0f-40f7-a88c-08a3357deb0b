<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/21
  Time: 17:38
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>员工客户合同</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-onlySelf {
            width: 125px;

        }

        /*出现滚动条*/
        td {
            overflow: auto;
        }

        .layui-table th {
            text-align: center;
        }

        .notNewline {
            white-space: nowrap
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="客户名称">客户名称:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable"  id="custName" lay-filter="custName"
                               readonly
                               placeholder="请输入" autocomplete="off">
                        <input type="hidden" id="custId" name="custId">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="雇员姓名">雇员姓名:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="employeeName" id="employeeName"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="唯一号">唯一号:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="employeeNo"
                               id="employeeNo" placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="证件号码">证件号码:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="certNo" id="certNo"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>



                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="签署日期>">签署日期></label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="signDateS" id="signDateS"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="签署日期<">签署日期<</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="signDateE" id="signDateE"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="合同开始日期>">合同开始日期></label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="startDate" id="startDate"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="合同结束日期>">合同结束日期<</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" name="endDate" id="endDate"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="签署状态:">签署状态:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="signStatus" DICT_TYPE="SIGN_STATUS"
                                id="signStatus" placeholder="无法选择" readonly >
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="派单客服:">派单客服:</label>
                    <div class="layui-input-inline">
                        <input type="hidden" class="layui-input layui-input-disposable" name="dispatchMan" id="dispatchMan"
                               placeholder="请输入" autocomplete="off">
                        <input type="text" class="layui-input layui-input-disposable"  id="dispatchManShow"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="派单客服:">接单客服:</label>
                    <div class="layui-input-inline">
                        <input type="hidden" class="layui-input layui-input-disposable" name="receivingMan"
                               id="receivingMan" placeholder="请输入" autocomplete="off">
                        <input type="text" class="layui-input layui-input-disposable"
                               id="receivingManShow" placeholder="请输入" autocomplete="off">
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="入离职状态:">入离职状态:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="entryDimissionStatus"
                                id="entryDimissionStatus" readonly autocomplete="off" placeholder="请选择"
                                DICT_TYPE="IN_OUT_STATUS">
                            <option value=""></option>
                        </select>
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="增减员状态:">增减员状态:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="addReduceStatus" id="addReduceStatus" DICT_TYPE="EMP_ORDER_STATUS"
                                autocomplete="off" placeholder="请选择">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label notNewline" title="">&nbsp;</label>
                    <div class="layui-input-inline" style="text-align: right;">
                    <a class="layui-btn"  data-type="reload" lay-filter="btnQueryFilter" id="btnQuery"
                       lay-submit="">查询</a>
                    <%--                    <button class="layui-btn" id="reset" type="reset">重置</button>--%>
                    </div>
                </div>

            </div>
        </form>
    </div>
    <%--startTable--%>
        <table class="layui-hide" id="employeeContractTable" lay-filter="employeeContractFilter"></table>
    <%--endTable--%>

    <script type="text/jsp" id="toolbarDemo">
        <input type="button" class="layui-btn layui-btn-sm" id="leadingOut" lay-event="leadingOutEvent"
               authURI="" value="导出数据">
    </script>
</div>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/nationVerification/employeeContractAllList.js?v=${publishVersion}"></script>
</body>
</html>
