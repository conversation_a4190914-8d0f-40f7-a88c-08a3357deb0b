var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, tableSelect = layui.tableSelect;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    var custId = '', contractNo = '', contractType = '', billType = null, custName = '';
    ;
    form.render('select');
    getTable();

    var isclick = true;
    var templetType;
    var custIdAndCustNameMap = JSON.parse($("#custIdAndCustNameMap").val());
    var custGroupIdAndCustIdMap = JSON.parse($("#custGroupIdAndCustIdMap").val());
    var custGroupIdAndGroupNameMap = JSON.parse($("#custGroupIdAndGroupNameMap").val());

    if (ML.isNotEmpty(custGroupIdAndGroupNameMap)) {
        for (item in custGroupIdAndGroupNameMap)
            $("#custGroup").append($("<option/>").text(custGroupIdAndGroupNameMap[item]).attr("value", item));
        form.render('select');
    }

    function getTable(contractNo, templetId, billMonth, status,skipFlag) {
        let searchBillType = '';
        if ($("#billType").val()) {
            searchBillType = $("#billType").val();
        }
        // 如果 选择了大集团,那么 合同,和账单模板可以不用选择,只需要选择账单月
        // 如果没有选择大集团 那么合同和账单月必选

        let custIdList = [];
        if (ML.isNotEmpty(custGroupIdAndCustIdMap)) {
            custIdList = custGroupIdAndCustIdMap[$("#custGroup").val()]
            if (ML.isEmpty(custIdList)) {
                // 这里表示选择了大集团 ,那么这个集团下面的客户都可以选择到
                if (ML.isEmpty($("#contractName").val()) && ML.isEmpty(billMonth)) {
                    if(!skipFlag){
                        return layer.msg("[合同和账单月] 或 [大集团] 需要选择一个!")
                    }
                }
            }
        }

        table.render({
            id: 'billGrid',
            elem: '#billGrid',
            url: ctx + '/bill/getListByParams',
            where: {
                'contractNo': contractNo,
                'templetId': templetId,
                'billMonth': billMonth,
                'genStatus': $("#genStatus").val(),
                'supplierVerificationStatus': $("#supplierVerificationStatus").val(),
                'supplierGenStatus': $("#supplierGenStatus").val(),
                'status': status,
                'billType': searchBillType,
                'custIdList': JSON.stringify(custIdList)
            },
            page: true, //默认为不开启
            height: 650,
            limits: [50, 100, 200],
            limit: 50,
            title: "账单信息",
            toolbar: '#toolbarDemo',
            defaultToolbar: [],
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'checkbox', width: '2%', fixed: 'left'},
                {field: 'custName', title: '客户名称', width: '10%', align: 'center', fixed: 'left'},
                {
                    field: 'templetName',
                    title: '客户帐套',
                    width: '10%',
                    align: 'center',
                    fixed: 'left',
                    templet: function (d) {
                        if (d.billType == 4) {
                            // archFlagView
                            return  '<a href="javascript:void(0);" style="color:blue;text-decoration: underline;" lay-event="disposeEvent">'+d.templetName+'</a>'
                        } else return d.templetName;
                    }
                },
                {
                    field: 'billType',
                    title: '账单类型',
                    width: '6%',
                    align: 'center',
                    fixed: 'left',
                    templet: function (d) {
                        return ML.dictFormatter("BILL_TYPE", d.billType);
                    }
                },
                {field: 'billMonth', title: '账单年月', width: '6%', align: 'center', fixed: 'left'},
                {field: 'receivableMonth', title: '财务应收年月', width: '8%', align: 'center'},
                {
                    field: 'employeeNum', title: '账单人数', width: '6%', align: 'center', templet: function (d) {
                        if (d.employeeNum) {
                            return d.employeeNum;
                        }
                        return 0;
                    }
                },
                {
                    field: 'receiveAmt', title: '应收金额', width: '8%', align: 'center', templet: function (d) {
                        if (d.receiveAmt) {
                            return d.receiveAmt;
                        }
                        return 0;
                    }
                },
                {field: 'version', title: '账单版本', width: '7%', align: 'center'},
                {
                    field: 'genStatus', title: '生成状态', width: '6%', align: 'center', templet: function (d) {
                        if (d.genStatus == 0) {
                            return "生成中";
                        }
                        return ML.dictFormatter("BILL_GEN_STATUS", d.genStatus);
                    }
                },
                {field: 'genTimes', title: '今日已生成次数', width: '8%', align: 'center'},
                {
                    field: 'status', title: '账单状态', width: '7%', align: 'center', templet: function (d) {
                        if (d.status == 2 && d.unStatus == 1) {
                            return "解锁待审批";
                        }
                        return ML.dictFormatter("BILL_STATTUS", d.status);
                    }
                },
                {field: 'genFirstTime', title: '首次生成时间', width: '10%', align: 'center'},
                {
                    field: 'genMan', title: '生成人', width: '10%', align: 'center', templet: function (d) {
                        return ML.loginNameFormater(d.genMan);
                    }
                },
                {
                    field: 'cancelStatus', title: '核销状态', width: '6%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("CANCEL_STATUS", d.cancelStatus);
                    }
                },
                {
                    field: 'invoiceStatus', title: '开票状态', width: '6%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("INVOICE_STATUS", d.invoiceStatus);
                    }
                },
                {
                    field: 'appGenDate', title: '约定出账单日', width: '8%', align: 'center', templet: function (d) {
                        if ((d.appGenDate + "").length == 1) {
                            d.appGenDate = "0" + d.appGenDate;
                        }
                        return (d.billMonth + "").substr(0, 4) + "-" + (d.billMonth + "").substr(4, 6) + "-" + d.appGenDate;
                    }
                },
                /*  {
                      field: 'isInputProduct', title: '赠送产品成本是否录入', align: 'center', width: "10%", templet: function (d) {
                          if (d.isInputProduct==true) {
                              return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoAgentContView'>" + '是' + "</a>";
                          }
                          return '否';
                      }
                  },*/
                {
                    field: 'appLockDate', title: '约定锁定时间', width: '8%', align: 'center', templet: function (d) {
                        if ((d.appLockDate + "").length == 1) {
                            d.appLockDate = "0" + d.appLockDate;
                        }
                        return (d.billMonth + "").substr(0, 4) + "-" + (d.billMonth + "").substr(4, 6) + "-" + d.appLockDate;
                    }
                }, {field: 'receiveFreq', title: '回款频率', hide: true}, {
                    field: 'subTypes',
                    title: '账单模板中合同二级类型',
                    hide: true
                },{
                    field: 'supplierVerificationStatus', title: '供应商核验状态', width: '10%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("SUPPLIER_VERIFICATION_STATUS", d.supplierVerificationStatus);
                    }
                },{
                    field: 'supplierGenStatus', title: '供应商工资账单生成状态', width: '10%', align: 'center', templet: function (d) {
                        if (d.supplierGenStatus == 0) {
                            return "生成中";
                        }
                        return ML.dictFormatter("BILL_GEN_STATUS", d.supplierGenStatus);
                    }
                },
                // , {field: '', title: '操作', toolbar: '#toolDemo', width: '8%', fixed: 'right', align: 'center'}
            ]],
            done: function () {
                ML.hideNoAuth();
                table.on('tool(billFilter)', function (obj) {
                    var data = obj.data; //获得当前行数据
                    var orderNos = [];
                    orderNos.push(data.orderNo);
                    if (obj.event == 'query') {
                        // 查看账单明细
                        layer.msg("暂无");

                    } else if (obj.event == 'gotoAgentContView') {
                        layer.open({
                            type: 2,
                            title: '赠送产品信息录入记录查看',
                            area: ['50%', '60%'],
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/bill/gotoPriceNumView?billId=' + data.id,
                            success: function (layero, index) {
                                let body = layer.getChildFrame("body", index);
                                const parentIndex = layer.getChildFrame(window.name);
                                body.find("#id").val(data.id);
                            },

                        })
                    }else if(obj.event =="disposeEvent" ){
                        const billId = obj.data.id;
                        layer.open({
                            type: 2,
                            title: '查看信息',
                            area: ['50%', '60%'],
                            maxmin: true,
                            offset: 'auto',
                            shade: [0.8, '#393D49'],
                            content: ML.contextPath + '/bill/gotoViewDisposeBillPage?billId=' + billId,
                            success: function (layero, index) {

                            },
                        })
                    }
                });
                table.on('toolbar(billFilter)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id), checkData = checkStatus.data;
                    switch (obj.event) {
                        case 'delete':
                            //删除
                            if (checkData.length === 0)
                                layer.msg('请选择一行');
                            else {
                                deleteOneOrMore(checkData);
                            }
                            break;
                        case 'queryDetail':
                            //二期

                            break;
                        case 'lock':
                            let tip = false;
                            checkData.forEach(item => {
                                if (item.status === 2) tip = true;
                            })
                            //锁定
                            if (tip) {
                                return layer.msg("选择的账单中有已经锁定的账单!");
                            } else {
                                var ids = [];
                                for (var i = 0; i < checkData.length; i++) {
                                    ids.push(checkData[i].id);
                                }
                                if (ids.length > 0) {
                                    info.allCount = ids.length
                                    ids.forEach(item => {
                                        var data2 = {'ids': JSON.stringify(item), 'optType': 'lock'};
                                        submitLockOrUnLock(data2, "/bill/lockBill");
                                    })
                                    let msg = "共有" + ids.length + "申请\n"
                                        + "成功" + info.successCount + "条\n";
                                    if (ML.isNotEmpty(info.errorInfo)) msg = msg + "没成功原因分别为: " + info.errorInfo;
                                    layer.msg(msg, {time: 10000});
                                    getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),false);
                                    info = {errorInfo: "", allCount: 0, successCount: 0};
                                }
                            }
                            break;
                        case 'addPriceNum':
                            if (checkData.length !== 1) {
                                layer.msg('请选择一行');
                                return false;
                            } else if (checkData[0].billType === 1 || checkData[0].billType === 2) {
                                layer.open({
                                    type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                                    title: "赠送产品成本录入",
                                    area: ['50%', '60%'],
                                    shade: 0,
                                    maxmin: true,
                                    offset: 'auto',
                                    shade: [0.8, '#393D49'],
                                    content: ctx + "/bill/gotoAddPriceNumPage?billId=" + checkData[0].id,
                                });
                            } else {
                                layer.msg("请选择账单类型为工资或者社保的数据")
                            }
                            break;
                        case 'unlock':
                            //解锁
                            if (checkData.length === 0)
                                return layer.msg('请选择一行');
                            else if (checkData.length > 1) {
                                return layer.msg("只能同时解锁一个");
                            } else {
                                if (checkData[0].status == 1) {
                                    return layer.msg("该账单未锁定");
                                } else if (checkData[0].unStatus == 1) {
                                    return layer.msg("审批中，请勿重复提交");
                                } else if (checkData[0].status == 2) {
                                    var cancelStatus = checkData[0].cancelStatus;
                                    var invoiceStatus = checkData[0].invoiceStatus;
                                    var virtualRedStatus = checkData[0].virtualRedStatus;
                                    if (cancelStatus != 1) {
                                        return layer.msg("该账单已核销,请去废除!!");
                                    }
                                    // 如果不是虚拟红冲状态的账单需要检查是否已经核销或开票  否则可以直接重新生成
                                    if (virtualRedStatus != 2 && virtualRedStatus != 3) {
                                        if (invoiceStatus != 1) {
                                            return layer.msg("该账单已开票,请去废除!!");
                                        }
                                    }
                                    var ids = [];
                                    ids.push(checkData[0].id);
                                    var receiveFreq = checkData[0].receiveFreq;
                                    var subTypes = checkData[0].subTypes;
                                    var currDate
                                    var unlockFlag = false;
                                    if (receiveFreq == 1) {
                                        currDate = getCurrDate();
                                    }
                                    /** 合同的回款频率为次月,直接解锁  全代理 次级类型为单立户 单项人事代理 次级类型为单立户 */
                                    if (receiveFreq == 2 || (contractType == 11 && subTypes == 3) || (contractType == 1 && subTypes == 2)) {
                                        var currDateCross = getCurrDate(true);
                                        currDate = ML.calcMonths(currDateCross, -1)
                                        unlockFlag = true;
                                    }
                                    if (checkData[0].billMonth >= currDate) {
                                        var auth = ML.isAuth("/bill/toExamine");
                                        if (auth || getCurrDate() < checkData[0].billMonth || (checkData[0].billMonth == getCurrDate() && new Date().getDate() <= checkData[0].appLockDate)) {

                                            layer.prompt({
                                                formType: 2,
                                                value: ' ',//默认空字符，没值不能调回掉函数
                                                title: '解锁申请',
                                                area: ['250px', '180px'] //自定义文本域宽高
                                            }, function (value, index, elem) {
                                                // add guoqian 20200729
                                                var values = value.trim();//去除两边空格
                                                if (values == null && values == '') {
                                                    layer.msg('申请理由不能为空');
                                                    return false;
                                                }
                                                // 客服经理 直接解锁
                                                data2 = {
                                                    'ids': JSON.stringify(ids[0]),
                                                    'optType': 'unlock',
                                                    'remark': values
                                                };
                                                submitLockOrUnLock(data2, "/bill/unlockBill");
                                                layer.close(index);
                                                layer.closeAll('iframe'); //关闭弹窗
                                            });
                                        } else if (unlockFlag) {

                                            layer.prompt({
                                                formType: 2,
                                                value: ' ',//默认空字符，没值不能调回掉函数
                                                title: '解锁申请',
                                                area: ['250px', '180px'] //自定义文本域宽高
                                            }, function (value, index, elem) {
                                                // add guoqian 20200729
                                                var values = value.trim();//去除两边空格
                                                if (values == null && values == '') {
                                                    layer.msg('申请理由不能为空');
                                                    return false;
                                                }
                                                /** 合同的回款频率为次月,直接解锁   unlockFlag 这个unlockFlag为true时会直接解锁...*/
                                                data2 = {
                                                    'ids': JSON.stringify(ids[0]),
                                                    'optType': 'unlock',
                                                    "unlockFlag": unlockFlag,
                                                    'remark': values
                                                };
                                                submitLockOrUnLock(data2, "/bill/unlockBill");
                                                layer.close(index);
                                                layer.closeAll('iframe'); //关闭弹窗
                                            });
                                        } else {
                                            layer.prompt({
                                                formType: 2,
                                                value: ' ',//默认空字符，没值不能调回掉函数
                                                title: '解锁申请',
                                                area: ['250px', '180px'] //自定义文本域宽高
                                            }, function (value, index, elem) {
                                                // add guoqian 20200729
                                                var values = value.trim();//去除两边空格
                                                if (values == null && values == '') {
                                                    layer.msg('申请理由不能为空');
                                                    return false;
                                                }
                                                // end guoqian 20200729
                                                data2 = {
                                                    'ids': JSON.stringify(ids[0]),
                                                    'optType': 'unlock',
                                                    'remark': values
                                                };
                                                submitLockOrUnLock(data2, "/bill/unlockBill");
                                                layer.close(index);
                                                layer.closeAll('iframe'); //关闭弹窗
                                            });
                                        }
                                    } else {
                                        return layer.msg("本月之前的账单且不是次月到款，不能解锁！");
                                    }
                                }
                            }
                            break;
                        case 'queryLog':
                            ///查看账单日志
                            if (checkData.length === 0)
                                layer.msg('请选择一行');
                            else if (checkData.length > 1) {
                                layer.msg("只能同时查看一个");
                            } else {
                                layer.open({
                                    type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                                    title: "查看账单日志",
                                    area: ['50%', '60%'],
                                    shade: 0,
                                    maxmin: true,
                                    offset: 'auto',
                                    shade: [0.8, '#393D49'],
                                    content: ctx + "/bill/getBillLogPage?billId=" + checkData[0].id,
                                });
                            }
                            break;
                        case 'push':
                            if (checkData.length === 0)
                                layer.msg('请选择一行');
                            else {
                                var ids = [];
                                for (var i = 0; i < checkData.length; i++) {
                                    if (checkData[i].status == 1) {
                                        return layer.msg("[" + checkData[i].templetName + "]未锁定");
                                    }
                                    if (checkData[i].status == 2 && checkData[i].unStatus == 1) {
                                        return layer.msg("[" + checkData[i].templetName + "]在审批中");
                                    }
                                    if ((checkData[i].supplierVerificationStatus > 1 && checkData[i].supplierVerificationStatus!=5)||
                                        (checkData[i].supplierVerificationStatus == 1 && checkData[i].supplierGenStatus!=2)
                                    ) {
                                        return layer.msg("[" + checkData[i].templetName + "]不是未推送状态");
                                    }
                                    if (checkData[i].billType != 2) {
                                        return layer.msg("[" + checkData[i].templetName + "]不是工资账单");
                                    }
                                    ids.push(checkData[i].id);
                                }
                                pushOrUnPush(ids,obj.event);
                            }
                            break;
                        case 'unPush':
                            if (checkData.length === 0)
                                layer.msg('请选择一行');
                            else {
                                var ids = [];
                                for (var i = 0; i < checkData.length; i++) {
                                    if (checkData[i].status == 1) {
                                        return layer.msg("[" + checkData[i].templetName + "]未锁定");
                                    }
                                    if (checkData[i].status == 2 && checkData[i].unStatus == 1) {
                                        return layer.msg("[" + checkData[i].templetName + "]在审批中");
                                    }
                                    if (checkData[i].supplierVerificationStatus == 0 || checkData[i].supplierVerificationStatus == 4) {
                                        return layer.msg("[" + checkData[i].templetName + "]未推送或已项目客服确认");
                                    }
                                    ids.push(checkData[i].id);
                                }
                                pushOrUnPush(ids,obj.event);
                            }
                            break;
                    }
                });
            }
        });
    }

    var info = {errorInfo: "", allCount: 0, successCount: 0};

    function submitLockOrUnLock(data, url) {
        ML.ajax(url, data, function (res) {
            if (res.code == 0) {
                if (data.optType !== 'lock') getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),false);
                else if (data.optType === 'lock') info.successCount = info.successCount + 1;
            } else {
                if (data.optType === 'lock') if (ML.isNotEmpty(res.code === -1)) info.errorInfo = info.errorInfo + "\n" + res.msg;
            }
            if (data.optType !== 'lock') layer.msg(res.msg);
        }, "POST", false);
    }

    function deleteOneOrMore(data) {
        var ids = [];
        for (var i = 0; i < data.length; i++) {
            if (data[i].status == 2) {
                return layer.msg("[" + data[i].templetName + "]已经锁定");
            }
            if (data[i].status == 2 && data[i].unStatus == 1) {
                return layer.msg("[" + data[i].templetName + "]在审批中");
            }
            ids.push(data[i].id);
        }
        if (ids.length > 0) {
            layer.confirm("确认要删除吗？", {title: "删除确认"}, function (index) {
                layer.close(index);
                ML.ajax("/bill/batchDel", {"ids": JSON.stringify(ids)}, function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),false);
                    } else {
                        console.log("delete bill failed");
                    }
                });

            });
        }
    }

    //审批
    function approvalOne(optType, id) {
        ML.ajax("/bill/toExamine", {'id': id, 'optType': optType}, function (res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),false);
            } else {
                console.log("approval bill unlock failed");
            }
        });
    }

//监听帐套下拉框 为约定账单生成日和约定账单锁定日赋值
    form.on('select(templetFilter)', function (data) {
        var genDate = $('#templetId option:selected').attr("genDate");
        var lockDate = $('#templetId option:selected').attr("lockDate");
        templetType = $('#templetId option:selected').attr("templetType");
        if (templetType) {
            billType = templetType;
        }
        $("#genDate").val(genDate);
        $("#lockDate").val(lockDate);
    });

//查询
    form.on('submit(btnQueryFilter)', function (data) {
        // alert(contractType);
        getTable(contractNo, data.field['templetId'], data.field['billMonth'], data.field['status'],false);
        return false;
    });

    var initYear;
    var layDate = laydate.render({
        elem: '#billMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#billMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });
    var nowDate = new Date();
    layDate.config.max = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 3, //关键
    };


    var reloadTable = function () {
        getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),false);
    }

    // 生成账单按钮点击事件
    $("#generate").on("click", function () {
        // 生成账单
        var billMonth = $("#billMonth").val();
        var templetId = $("#templetId").val();
        if (ML.isEmpty($("#custGroup").val())) {
            if (!custId)
                return layer.msg("请选择合同");
            if (!billMonth)
                return layer.msg("请选择账单年月");
        }
        if (isclick) {
            isclick = false;
            if (contractType == 6 || contractType == 10 || contractType == 16) {
                ML.layuiButtonDisabled($('#generate'));// 禁用
            }
            let tip = false;
            let contractTypeList = [5, 7, 8, 12, 13, 14]; // 体检 商务咨询 福利 人才招聘 人才培训 定晴背调
            if (contractTypeList.includes(contractType)) {
                tip = true;
            }
            if (tip) {
                if (!contractNo) {
                    return layer.msg("请选择合同");
                }
                if (!templetId) {
                    return layer.msg("请选择账单模板");
                }
                if (!billMonth) {
                    return layer.msg("请选择账单年月");
                }
                layer.open({
                    type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                    title: "生成一次性账单",
                    area: ['70%', '80%'],
                    shade: 0,
                    maxmin: true,
                    offset: 'auto',
                    shade: [0.8, '#393D49'],
                    content: ctx + "/bill/gotoDisposGenerateBill?contractNo=" + contractNo + "&&templetId=" + templetId + "&&billMonth=" + billMonth,
                    end: function (data) {
                        reloadTable();
                    },
                    success: function (layero, index, data) {

                    }
                });
            } else {
                let custGroup = $("#custGroup").val();
                ML.ajax("/bill/generateBill", {
                    'contractNo': contractNo,
                    'custId': custId,
                    'templetId': templetId,
                    'billMonth': billMonth,
                    'contractType': contractType,
                    'custGroup': custGroup,
                    'billType': $("#billType").val()
                }, function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        reloadTable();
                        var reloadTableRound = setInterval(reloadTable, 5000);
                        //20s后停止刷新
                        setTimeout(function () {
                            clearInterval(reloadTableRound);
                        }, 20000);

                    } else {
                        console.log("generate bill failed");
                    }
                    ML.layuiButtonDisabled($('#generate'), true);// 取消禁用
                });
            }

            //定时器
            setTimeout(function () {
                isclick = true;
            }, 2000);
        }
    })

    /** crossFlag 为true 返回 yyyy-mm */
    function getCurrDate(crossFlag) {
        var now = new Date();
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        if (month < 10) {
            month = "0" + month;
        }
        if (crossFlag)
            return year + "-" + month;
        else
            return year + "" + month;
    }




    //获取客户
    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:800px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center',width:'20%'}
                , {field: 'custNo', title: '客户编号', align: 'center',width:'30%'}
                , {field: 'custName', title: '客户名称', align: 'center',width:'40%'}
            ]]
        },
        done: function (elem, data) {
            $('#contractName').val('')
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            custId=id;

            getContractName(custId)
        }
    });
    var templetIdList = [];
    //下拉数据表格
    function getContractName(custId){
        var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="合同名称/编号" autocomplete="off" class="layui-input">';
        tableSelect.render({
            elem: '#contractName',
            checkedKey: 'contractNo',
            appd: appd,
            table: {
                url: ML.contextPath + '/report/getContractPageByCustIdAndInBillTemplet?custId=' +custId,
                cols: [[
                    {type: 'radio'},
                    {field: 'contractNo', title: '合同编号', align: 'center', width: '160'},
                    {field: 'contractName', title: '合同名称', align: 'center', width: '200'},
                    {
                        field: 'contractType', title: '合同类型', align: 'center', width: '200', templet: function (d) {
                            return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                        }
                    },
                ]]
            },
            done: function (elem, data) {
                var NEWJSON = [];
                layui.each(data.data, function (index, item) {
                    NEWJSON.push(item.contractName);
                    contractType = item.contractType;
                });
                elem.val(NEWJSON.join(","));
                contractNo = $("#contractName").attr("ts-selected");
                $(".tempOptionId").remove();
                /*将生成日和锁定日置空*/
                $("#genDate").val("");
                $("#lockDate").val("");
                form.render('select');
                ML.ajax("/customer/billTemplet/getTempletByContractNoandCustId", {
                    'custId': custId,
                    'contractNo': contractNo
                }, function (res) {
                    if (res.data.length > 0) {
                        var optionStr = "";
                        templetIdList = [];
                        layui.each(res.data, function (i, item) {
                            optionStr += "<option class='tempOptionId' value='" + item.id + "' genDate='" + item.genDate + "' lockDate='" + item.lockDate + "'templetType='" + item.templetType + "'>" + item.templetName + "</option>";
                            templetIdList.push(item.id);
                        });
                        $("#templetId").append(optionStr);
                        form.render('select');
                    }
                }, "get");
            }
        });

    }


    /////点击重置时，将选中的值清除
    $("#resetBtn").on('click', function () {
        $("#contractName").removeAttr("ts-selected");
        custId = null
        contractNo = null
        contractType = null;//合同类型
        $(".tempOptionId").remove();
        templetIdList = [];
        billType = null;
        form.render('select');
    });


    // function billPrint(custId, templetId, billMonth, billType) {
    //     //billType是选择账单模板的时候的账单模板类型, 从页面上获取的账单模板类型是用户自己选择的
    //     if (ML.isEmpty(data[0].contractNo) || ML.isEmpty(data[0].billMonth))
    //         return layer.msg("合同和账单月必选")
    //     let sortType = 1;
    //     if ($("#sortType").val())
    //         sortType = $("#sortType").val();
    //     if (billType == 1) {
    //         window.location.href = ML.contextPath + "/bill/billPrint?custId=" + custId + "&templetId=" + templetId + "&billMonth=" + billMonth + "&contractNo=" + contractNo + "&sortType=" + sortType;
    //     }
    //     if (billType == 2) {
    //         window.location.href = ML.contextPath + "/bill/billSalaryPrint?custId=" + custId + "&templetId=" + templetId + "&billMonth=" + billMonth + "&contractNo=" + contractNo + "&contractType=" + contractType + "&custName=" + custName;
    //     }
    //     if (billType == 3) {
    //         var param = {'custId': custId};
    //         param.templetId = templetId;
    //         param.billMonth = billMonth;
    //         param.contractNo = contractNo;
    //         window.location.href = ML.contextPath + "/bill/billCommercePrint?jsonVo=" + JSON.stringify(param);
    //     }
    // }

    function billPrintAll(contractNo, templetIdList, billMonth, billType, layero) {
        layero.find("#sortType").val();
        let printType = layero.find("#printType").val();
        let isMerge = layero.find("#isMerge").val();
        let sortType = layero.find("#sortType").val();
        if (ML.isEmpty(printType))
            printType = 1;
        if (ML.isEmpty(isMerge))
            isMerge = 1;
        if (ML.isEmpty(sortType))
            sortType = 1;
        let billTypeSearch = $("#billType").val();
        if (ML.isEmpty($("#billType").val())) {
            billTypeSearch = '';
        }
        //  如果选择了集团,然后选择了合同账单模板  那么就需要 走以前一样的逻辑
        let printTip = true;
        let custGroup = $("#custGroup").val();
        if ((ML.isNotEmpty(custGroup) ||ML.isNotEmpty(custId))&& ML.isEmpty(contractNo)) {
            if (ML.isEmpty(billMonth))
                return layer.msg("账单月不能为空!");
            if (ML.isEmpty(billTypeSearch))
                return layer.msg("账单类型不能为空!");
            printTip = false;
            layer.msg("只会打印社保账单!");
            // 如果选择了集团
        }
        if (printTip) {
            if (ML.isEmpty(templetIdList) || (ML.isNotEmpty(templetIdList) && templetIdList.length === 0)) {
                return layer.msg("该合同下没有账单模板无法打印");
            }
            if (ML.isEmpty(contractNo) || ML.isEmpty(billMonth))
                return layer.msg("合同和账单月必选")
        }
        let jsonTempletIdList = JSON.stringify(templetIdList);
        if (printTip) {
            window.location.href = ML.contextPath + "/bill/billPrintAllData?contractNo="
                + contractNo + "&templetIdList=" + jsonTempletIdList + "&billMonth=" + billMonth + "&custId=" + custId
                + "&printType=" + printType + "&isMerge=" + isMerge + "&sortType=" + sortType + "&billType=" + billTypeSearch;
        } else {
            window.location.href = ML.contextPath + "/bill/billPrintAllDataForCustGroup?"+   "billMonth=" + billMonth + "&custId=" + custId
                + "&printType=" + printType + "&isMerge=" + isMerge + "&sortType=" + sortType + "&billType=" + billTypeSearch+"&custGroup="+custGroup;
        }
    }


    // 账单打印按钮点击事件
    $("#print").on("click", function () {
        //账单打印
        var templetId = $("#templetId").val();
        var billMonth = $("#billMonth").val();
        if (!billMonth) {
            return layer.msg("请选择账单年月");
        }
        var index = layer.open({
            type: 1,//这里依然指定类型是多行文本框，但是在下面content中也可绑定多行文本框
            title: '打印选择',
            area: ['300px', '300px'],
            btnAlign: 'c',
            skin: 'layui-layer-lan',
            anim: 6,
            closeBtn: '1',//右上角的关闭
            content: '<div style="\n' +
                '    margin-top: 30px;"> <div class="layui-input-inline">\n' +
                '        <label class="layui-form-label" title="打印方式" style="width: 100px;\n' +
                '    top: -10;">打印方式:</label>\n' +
                '        <div class="layui-input-inline">\n' +
                '            <select name="printType" id="printType" class="printType" lay-search>\n' +
                '                <option value="1">excel导出</option>\n' +
                '                <option value="2">压缩包导出</option>\n' +
                '            </select>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '    <div class="layui-input-inline">\n' +
                '        <label class="layui-form-label" title="是否合并打印" style="width: 100px;\n' +
                '    top: -10;">是否合并打印:</label>\n' +
                '        <div class="layui-input-inline">\n' +
                '            <select name="isMerge" id="isMerge" class="isMerge" lay-search>\n' +
                '                <option value="1">否</option>\n' +
                '                <option value="2">是</option>\n' +
                '            </select>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '    <div class="layui-input-inline">\n' +
                '        <label class="layui-form-label" title="排序方式" style="width: 100px;\n' +
                '    top: -10;">排序方式:</label>\n' +
                '        <div class="layui-input-inline">\n' +
                '            <select name="sortType" id="sortType" class="sortType" lay-search>\n' +
                '                <option value="1">入职日期</option>\n' +
                '                <option value="2">城市</option>\n' +
                '            </select>\n' +
                '        </div>\n' +
                '    </div> </div>',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                if (ML.isEmpty(templetId) && templetIdList.length == 1) {
                    billPrintAll(contractNo, templetIdList, billMonth, "", layero)
                } else if (ML.isEmpty(templetId) && templetIdList.length > 1) {
                    billPrintAll(contractNo, templetIdList, billMonth, "", layero)
                } else if (templetId) {
                    // 选择了 账单模板
                    templetIdList = [];
                    templetIdList.push(templetId);
                    billPrintAll(contractNo, templetIdList, billMonth, "", layero);
                }else{
                    billPrintAll(contractNo, templetIdList, billMonth, "", layero);
                }
                layer.close(index); //如果设定了yes回调，需进行手工关闭
            },
            no: function (index) {
                layer.close(index);
                return false;//点击按钮按钮不想让弹层关闭就返回false
            }
            , end: function () {
                layer.close(index);
            }

        });
    });
    function pushOrUnPush(ids,type) {
        if (ids.length > 0) {
            let msg="确认要";
            if(type=='unPush'){
                msg+="取消"
            }
            msg+="推送嘛？"
            layer.confirm(msg, {title: "确认"}, function (index) {
                layer.close(index);
                ML.ajax("/supplierSalaryBill/pushOrUnPush", {"ids": JSON.stringify(ids),"type":type}, function (res) {
                    if (res.code == 0) {
                        if(ML.isNotEmpty(res.msg)){
                            layer.msg(res.msg,{time: 3000});
                        }
                        getTable(contractNo, $("#templetId").val(), $("#billMonth").val(), $("#status").val(),true);
                    }
                });

            });
        }
    }

});
