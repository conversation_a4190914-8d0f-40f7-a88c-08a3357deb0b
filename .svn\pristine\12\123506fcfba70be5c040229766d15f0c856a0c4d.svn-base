package com.reon.hr.sp.customer.service.employee.salary.cumulativeCalculation;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.TaxTabVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayAndCategoryVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ICumulativeCalculationService {
    int payrollSalary (Long payId, String updater, TaxTabVo taxTabVo, SalaryPayAndCategoryVo salaryPayAndCategoryVo);
    int annualBonus (Long payId,String updater,TaxTabVo taxTabVo,SalaryPayAndCategoryVo salaryPayAndCategoryVo);
    int calculateLaborWages(Long payId, String updater, TaxTabVo taxTabVo, SalaryPayAndCategoryVo salaryPayAndCategoryVo);
    int calculateEconomicCompensation(Long payId, String updater, TaxTabVo taxTabVo, SalaryPayAndCategoryVo salaryPayAndCategoryVo);

    void deleteEmpSalary(Long payId, String updater);
    Page<Map<String,Object>> selectSalaryEmp(String keyWord, Long payId, Integer limit, Integer page) ;
    List<Map<String,Object>> selectSalaryEmp(String keyWord, Long payId) ;
}
