/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/8/27
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.bill.dubbo.service.rpc.impl.paymentApply;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.ehr.api.sys.dubbo.service.rpc.HolidaysWrapperService;
import com.reon.ehr.api.sys.vo.HolidaysVo;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.api.base.vo.InsurancePackVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.utils.ListPageUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.bill.vo.salary.PayApply2BatchVo;
import com.reon.hr.api.bill.vo.salary.PayServiceSerialLogVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPaymentRefundInputVo;
import com.reon.hr.api.workflow.vo.ParameVo;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.api.workflow.vo.WorkflowComentVo;
import com.reon.hr.sp.bill.entity.bill.PaymentApply;
import com.reon.hr.sp.bill.service.bill.InsurancePracticeDisComPayService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IPracticePayDetailService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.PayServiceSerialLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentApplyWrapperServiceImpl
 *
 * @date 2020/8/27 15:14
 */
@Service("paymentApplyDubboService")
public class PaymentApplyWrapperServiceImpl implements IPaymentApplyWrapperService {

    @Autowired
    private IPaymentApplyService iPaymentApplyService;
    @Autowired
    IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;

    @Resource
    private InsurancePracticeDisComPayService insurancePracticeDisComPayService;

    @Resource
    private IPracticePayDetailService practicePayDetailService;

    @Resource
    private PayServiceSerialLogService payServiceSerialLogService;

    @Resource
    private HolidaysWrapperService holidaysWrapperService;

    @Override
    public Page<PaymentApplyVo> getPaymentApplyListPage(PaymentApplyVo paymentApplyVo, Integer page, Integer limit) {
        Map<String, Object> map = Maps.newHashMap ();
        map.put ("payKind", paymentApplyVo.getPayKind ());
        map.put ("payType", paymentApplyVo.getPayType ());
        map.put ("appStatus", paymentApplyVo.getAppStatus ());
        map.put ("payMonth", paymentApplyVo.getPayMonth ());
        map.put ("payCom", paymentApplyVo.getPayCom ());
        map.put ("payMethod", paymentApplyVo.getPayMethod ());
        map.put ("packCode", paymentApplyVo.getPackCode ());
        if (StringUtils.isNotEmpty (paymentApplyVo.getStartApplyTime ())) {
            map.put ("startApplyTime", paymentApplyVo.getStartApplyTime () + " 00:00:00");
        }
        if (StringUtils.isNotEmpty (paymentApplyVo.getEndApplyTime ())) {
            map.put ("endApplyTime", paymentApplyVo.getEndApplyTime () + " 23:00:00");
        }
        if (StringUtils.isNotEmpty (paymentApplyVo.getStartPassTime ())) {
            map.put ("startPassTime", paymentApplyVo.getStartPassTime () + " 00:00:00");
        }
        if (StringUtils.isNotEmpty (paymentApplyVo.getEndPassTime ())) {
            map.put ("endPassTime", paymentApplyVo.getEndPassTime () + " 23:00:00");
        }
        if(CollectionUtils.isNotEmpty(paymentApplyVo.getAppComs())){
            map.put ("appComs", paymentApplyVo.getAppComs());
        }
        Page<PaymentApplyVo> pages = new Page<> (page, limit);
        List<PaymentApplyVo> paymentApplyListPage = iPaymentApplyService.getPaymentApplyListPage (pages, map);
        if(CollectionUtils.isNotEmpty(paymentApplyListPage)){
            /** 获取福利包名称 */
            List<String> packCodeList = paymentApplyListPage.stream().flatMap(vo -> {
                List<String> packCodes;
                if(StringUtils.isNotBlank(vo.getPackCode())){
                    packCodes = Arrays.stream(vo.getPackCode().split(",")).collect(Collectors.toList());
                } else {
                    packCodes = Lists.newArrayList();
                }
                vo.setPackCodes(packCodes);
                return packCodes.stream();
            }).distinct().collect(Collectors.toList());
            Map<String, InsurancePackVo> packNameByCodeMap = iInsurancePackResourceWrapperService.getPackNameByCode(packCodeList);
            for (PaymentApplyVo vo : paymentApplyListPage) {
                StringBuilder builder = new StringBuilder();
                for (String packCode : vo.getPackCodes()) {
                    InsurancePackVo insurancePackVo = packNameByCodeMap.get(packCode);
                    if(insurancePackVo != null){
                        String name = insurancePackVo.getPackName();
                        builder.append(name).append(",");
                        vo.setOrgCode(insurancePackVo.getOrgCode());
                    }
                }
                vo.setPackName(builder.substring(0,builder.length()-1));

            }
        }
        pages.setRecords (paymentApplyListPage);
        return pages;
    }

    @Override
    public List<PaymentApplyVo> getPaymentApprovalList(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.getPaymentApprovalList(paymentApplyVo);
    }

    @Override
    public int save(PaymentApplyVo record) {
        return iPaymentApplyService.insertSelective (record);
    }

    @Override
    public boolean updateByPrimaryKeySelective(PaymentApplyVo record) {
        return iPaymentApplyService.updateByPrimaryKeySelective (record);
    }

    @Override
    public PaymentApplyVo selectByPrimaryKey(Long id) {
        //将相关报表的 福利办理方 和 福利包名称查出来
        PaymentApplyVo paymentApplyVo = iPaymentApplyService.selectByPrimaryKey(id);
        List<PracticeLockInfoVo> practiceLockInfoVo = iPaymentApplyService.getPracticeLockIdByPaymentApplyId(paymentApplyVo.getId());
        if(CollectionUtils.isNotEmpty(practiceLockInfoVo)){
            List<String> packCodes = practiceLockInfoVo.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
            paymentApplyVo.setOrgCode(practiceLockInfoVo.get(0).getOrgCode());
            paymentApplyVo.setPackCodes(packCodes);
            paymentApplyVo.setLockMonth(practiceLockInfoVo.get(0).getLockMonth());
        }

        return paymentApplyVo;
    }
    @Override
    public PaymentApplyVo selectSalaryById(Long id) {
        return iPaymentApplyService.selectSalaryById(id);
    }

    @Override
    public PaymentApplyVo selectDetailById(Long id) {
        return iPaymentApplyService.selectDetailById (id);
    }

    @Override
    public PaymentApplyVo selectDetailByPid(String pid) {
        return iPaymentApplyService.selectDetailByPid(pid);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApplyList(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.getPaymentApplyList (paymentApplyVo);
    }

    @Override
    public List<TaskVo> getTaskVoList(ParameVo parameVo,PaymentApplyVo paymentApplyVo, HashMap<Object, TaskVo> taskVoMap, Map<String, WorkflowComentVo> finalWorkflowAuditLogListMap) {
        return iPaymentApplyService.getTaskVoList (parameVo,paymentApplyVo,taskVoMap,finalWorkflowAuditLogListMap);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApplyListByPidList(List<String> pidList) {
        return iPaymentApplyService.getPaymentApplyListByPidList(pidList);
    }

    @Override
    public ListPageUtil<PaymentApplyVo> getPaymentApplyListByAuth(PaymentApplyVo paymentApplyVo, List<OrgPositionDto> userOrgPositionDtoList) {
        return iPaymentApplyService.getPaymentApplyListByAuth(paymentApplyVo, userOrgPositionDtoList);
    }

    @Override
    public List<PaymentApplyVo> getPractIcePaymentApplyList(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.getPractIcePaymentApplyList (paymentApplyVo);
    }

    @Override
    public List<PaymentApplyVo> getPractIcePaymentApplyListByAppStatus() {
        return iPaymentApplyService.getPractIcePaymentApplyListByAppStatus();
    }

    @Override
    public Long findPaymentApplyPidAndProcType(String pid) {
        EntityWrapper<PaymentApply> wrapper = new EntityWrapper<> ();
        wrapper.eq ("pid", pid);
        PaymentApply paymentApply = iPaymentApplyService.selectOne (wrapper);
        return paymentApply.getId ();
    }

    @Override
    public int savePayBatch(PaymentApplyVo record) {
        return iPaymentApplyService.savePayBatch(record);
    }

    @Override
    public List<PayApply2BatchVo> getPayMent(List<Long> batchIds,Integer kind) {
        return iPaymentApplyService.getPayMent(batchIds,kind);
    }

    @Override
    public List<Map<String, Object>> getPay(List<Long> payMentIds) {
        return null;
    }

    @Override
    public Long applyInsurancePracticePayment(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.saveApplyInsurancePracticePayment(paymentApplyVo);
    }

    @Override
    public boolean updateAppStatus(PaymentApplyVo record) {
        return iPaymentApplyService.updateAppStatus(record);
    }

    @Override
    public boolean editPaymentApplyAndPrac(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.editPaymentApplyAndPrac(paymentApplyVo);
    }

    @Override
    public int updateDocumentStatus(List<String> pidList, Integer documentStatus, Integer appStatus) {
        return iPaymentApplyService.updateDocumentStatus(pidList,documentStatus,appStatus);
    }

    @Override
    public PaymentApplyVo getByBatchId(Long batchId) {
        return iPaymentApplyService.getByBatchId(batchId);
    }
    @Override
    public List<PaymentApplyVo> getByBatchIdList(List<Long> batchIdList) {
        return iPaymentApplyService.getByBatchIdList(batchIdList);
    }

    @Override
    public int deleteByIdList(List<Long> idList, String loginName) {
        return iPaymentApplyService.deleteByIdList(idList,loginName);
    }

    @Override
    public int updateLastDate(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.updateLastDate(paymentApplyVo);
    }
    @Override
    public int updatePayReceived(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.updatePayReceived(paymentApplyVo);
    }

    @Override
    public List<Long> getBatchIdListByLastDate(Integer lastDate) {
        return iPaymentApplyService.getBatchIdListByLastDate(lastDate);
    }

    @Override
    public List<Long> getBatchIdListByStringLastDate(String lastDate) {
        return iPaymentApplyService.getBatchIdListByStringLastDate(lastDate);
    }

    @Override
    public List<Long> getBatchIdListByStringLastDateStartAndEnd(String lastDateStart, String lastDateEnd) {
        return iPaymentApplyService.getBatchIdListByStringLastDateStartAndEnd(lastDateStart, lastDateEnd);
    }

    @Override
    public List<PaymentApplyVo> getLastDateAndAnewPayFlagByBatchIdList(List<Long> batchIdList) {
        return iPaymentApplyService.getLastDateAndAnewPayFlagByBatchIdList(batchIdList);
    }

    @Override
    public Page<Map<String, Object>> getSalaryMapListByVo(PaymentApplyVo paymentApplyVo, SalaryPaymentRefundInputVo vo, Integer limit, Integer page) {
        return iPaymentApplyService.getSalaryMapListByVo(paymentApplyVo,vo,limit,page);
    }

    @Override
    public List<PaymentApplyVo> getPayCreateTimeByPayBatchId(List<Long> payBatchIdList) {
        return iPaymentApplyService.getPayCreateTimeByPayBatchId(payBatchIdList);
    }

    @Override
    public boolean updateSalaryAppStatus(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.updateSalaryAppStatus(paymentApplyVo);
    }

    @Override
    public List<PaymentApplyVo> getByPidList(List<String> pidList) {
        return iPaymentApplyService.getByPidList(pidList);
    }

    @Override
    public int updateReprintCount(List<String> pidList, Integer reprintCount) {
        return iPaymentApplyService.updateReprintCount(pidList,reprintCount);
    }

    @Override
    public boolean getUnRejected(String contractNo, Long templetId, Integer billMonth) {
        return iPaymentApplyService.getUnRejected(contractNo,templetId,billMonth);
    }

    @Override
    public int updateSalaryFee(PaymentApplyVo paymentApplyVo) {
        return iPaymentApplyService.updateSalaryFee(paymentApplyVo);
    }

    @Override
    public int saveInsurancePracticeDisComPayBatch(List<InsurancePracticeDisComPayVo> insurancePracticeDisComPayVos) {
        return insurancePracticeDisComPayService.saveInsurancePracticeDisComPayBatch(insurancePracticeDisComPayVos);
    }

    @Override
    public void deleteInsurancePracticeDisComPayByPayId(Long payId) {
        insurancePracticeDisComPayService.deleteInsurancePracticeDisComPayByPayId(payId);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> getInsurancePracticeDisComListByPayId(Long payId) {
        List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = insurancePracticeDisComPayService.getInsurancePracticeDisComListByPayId(payId);
        if (CollectionUtils.isNotEmpty(insurancePracticeDisComListByPayId)){
            PaymentApplyVo paymentApplyVo = iPaymentApplyService.selectByPrimaryKey(payId);
            String lastDate = paymentApplyVo.getLastDate();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime dateTime = LocalDateTime.parse(lastDate, formatter);
            LocalDateTime updatedTime = dateTime.with(LocalTime.of(16, 0));
            insurancePracticeDisComListByPayId.forEach(item->{
                item.setPayDate(updatedTime.format(formatter));
            });
        }
        return insurancePracticeDisComListByPayId;
    }

    @Override
    public List<PracticePayDetailVo> selectDetailsByPayId(Long payId) {
        return practicePayDetailService.selectDetailsByPayId(payId);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApprovalListByDisCom(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeDisComPayService.getPaymentApprovalListByDisCom(paymentApplyVo);
    }

    @Override
    public String initiatePaymentBuyCmb(List<TaskVo> taskVos,String loginName) {
        return insurancePracticeDisComPayService.initiatePaymentBuyCmb(taskVos,loginName);
    }

    @Override
    public void offlineTransfer(List<TaskVo> taskVos, String loginName) {
         insurancePracticeDisComPayService.offlineTransfer(taskVos,loginName);
    }

    @Override
    public int insertPayServiceLog(List<PayServiceSerialLogVo> payServiceSerialLogVoList) {
        return payServiceSerialLogService.insertByList(payServiceSerialLogVoList);
    }

    @Override
    public  List<String> selectPidListByPid(List<String> pidList) {
        return iPaymentApplyService.selectPidListByPid(pidList);
    }

    @Override
    public int deletePayServiceLogByPayId(Long paymentId) {
        return payServiceSerialLogService.deletePayServiceLogByPayId(paymentId);
    }

    @Override
    public Page<PaymentApplyVo> getPrintApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeDisComPayService.getPrintApplicationFromPage(paymentApplyVo);
    }

    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printApplicationFrom(List<PrintPaymentFromExportVo> vos) {
        return insurancePracticeDisComPayService.printApplicationFrom(vos);
    }

    @Override
    public List<PaymentApplyVo> getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeDisComPayService.getPrintReceivingApplicationFromPage(paymentApplyVo);
    }

    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printAllDisApplicationFrom(PrintPaymentFromExportVo vo) {
        return insurancePracticeDisComPayService.printAllDisApplicationFrom(vo);
    }

    @Override
    public Map<String, List<PrintReceivingApplicationFromVo>> printReceivingApplicationFrom(PrintPaymentFromExportVo vo) {
        return insurancePracticeDisComPayService.printReceivingApplicationFrom(vo);
    }

    @Override
    public List<ReserveInquiryVo> getReserveInquiryListPage(PaymentApplyVo vo) {
        return iPaymentApplyService.getReserveInquiryListPage(vo);
    }


    @Override
    public List<ReserveInquiryVo> getReserveInquiryDetail(PaymentApplyVo vo) {
        return iPaymentApplyService.getReserveInquiryDetail(vo);
    }

    @Override
    public List<PracticePayDetailVo> selectPracticePayDetailListByPayId(Long payId) {
        return practicePayDetailService.selectPracticePayDetailListByPayId(payId);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> checkProgressByPayId(Long payId) {
        return insurancePracticeDisComPayService.checkProgressByPayId(payId);
    }
}
