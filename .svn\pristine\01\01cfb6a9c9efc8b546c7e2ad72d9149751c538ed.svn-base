package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.ContractPageDTO;
import com.reon.hr.api.customer.dto.customer.CustomerDto;
import com.reon.hr.api.customer.dto.customer.PrintContractDto;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.commInsurOrder.ContractTempletVo;
import com.reon.hr.sp.customer.entity.cus.Contract;
import com.reon.hr.sp.customer.entity.cus.QuotationChgTask;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ContractService extends IService<Contract> {

    /**
     * 获取page信息
     *
     * @param page            当前页
     * @param limit           页数
     * @param contractPageDTO 查询条件
     * @return page
     */
    public Page<ContractPageVo> getPage(int page, int limit, ContractPageDTO contractPageDTO);
    public Page<ContractPageVo> getAppOverPage(int page, int limit, ContractPageDTO contractPageDTO);
    List<ContractPageVo> getAllSellerList();
    Page<ContractPageVo> getContractSelectTable(int page, int limit, ContractPageDTO contractPage);
    public List<ContractPageVo> getPage(ContractPageDTO contractPageDTO);

    boolean saveContract(Contract contract);

    Contract findbyContractNo(String contractNo);
    List<String> getAssociatedContractNoByContractNo(String contractNo);
    List<ContractVo> findbyContractNoList(List<String> contractNoLsit);

    List<ContractVo> findbyAssociatedContractNoList(List<String> contractNoList);
    List<ContractVo> getByContractNoList(List<String> contractNoList);

    boolean updateByContractNo(ContractPageVo contractPageVo);

    Integer updateArchFlagById(List<Long> contractProcessIdList,Integer type,String loginName,String remark);

    String getArchRemarkByContractNo(String contractNo, String pid);

    /**
     * 自动顺延一年
     * @param contractNo 合同号
     * @return 是否成功
     */
    boolean autoDefer(String contractNo);

    boolean delByContractNo(String contracNo);

    List<ContractPageVo> getListSelective(ContractPageVo contractPage);

    Page<ContractPageVo> getPageByName(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getPageByCustId(Integer page, Integer limit, String custId);

    Page<ContractPageVo> getCustomerPageByName(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 查询合同中使用的客户但过滤掉开票信息中已经存在的
     *
     * @param page                   页面
     * @param limit                  限制
     * @param name                   名字
     * @param loginName              登录名
     * @param userOrgPositionDtoList 用户组织位置dto列表
     * @return {@link Page}<{@link ContractPageVo}>
     */
    Page<ContractPageVo> getCustomerWithoutExists(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);

    List<ContractPageVo> getContractNameByCustId(Long custId);

    Page<ContractPageVo> getContractInfoByKeyword(Integer page, Integer limit, String keyword, List<OrgPositionDto> loginName);
    Page<ContractPageVo> getCustomerFromAuthContract(Integer page, Integer limit, String keyword, List<OrgPositionDto> loginName);

    Page<ContractPageVo> getcontractNameAndNo(Integer page, Integer limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);
    /**
     * guoqian 根据客户订单编号查询小合同*/
    EmployeeContract getSupplierContract (String contractAreaNo);

    List<ContractPageVo> getContractList();

    List<ContractPageVo> getContractOneMonthBeforeList();

    public List<ContractPageVo> getContractList(ContractPageDTO contractPageDTO);

	List<ContractPageVo> getContractByPid(Map<String, Object> stringObjectMap, List<String> processInsList);

    List<ContractPageVo> getContractFlowByPid(List<String> processInsList,String key);

    List<TaskResultVo> getContractByContractName(String contractName);
	List<ContractBillReport> getContractUse(ContractBillReport vo);

    Page<ContractPageVo> findbyContractTypeList(Integer page, Integer limit,List<Integer> contractType ,String param,String currLogin);

    List<ContractTempletVo> getContractTempletListPage(Page page, Map<String, Object> map);

    List<ContractPageVo> findbyContractType(List<Integer> contractTypeList ,String contractNo);

    Page<ContractPageVo> getPageByCustIdAndSearchDate(int page, int limit, ContractPageDTO contractPage, String loginName);

//    ContractPageVo getContractNo(String contractNo,String currLogin);

    List<ContractPageVo> getContractTemplates();
    Integer updatePeopleNum(List<ContractPageVo> list);

    Integer updateNewFlagAndFirstBillMonthByContractNo(List<Contract> contractList);

	ContractPageVo getDataByContractNo(String contractNo);

    boolean updateSellerByContractNoList(List<ContractVo> contractVoList, int startMonth);

    List<Long> getCustIdListByUserNameAndAllocated(List<OrgPositionDto> userOrgPositionDtoList);

    Page<ContractPageVo> getPageByCustIdAndName(int page, int limit, String name, Long custId);

	Integer saveBindingQuotation(ContractRelativeQuotationVo contractRelativeQuotationVo);

    Integer existData(List<ContractRelativeQuotationVo> contractRelativeQuotationVoList);

    String getOldQuotationNoByContractNo(String contractNo);

    Integer updateContractDefaultQuoteNo();

    Integer insertQuotationChgTask(QuotationChgTask quotationChgTask);

    /**
     * @Description 更新 contract表中默认报价单编号 进行判断contractRelativeQuotation
     * 是否绑定了这个报价单号,如果没有绑定那么就插入,如果当前合同已经绑定了改报价单,那么不进行操作
     * 如果是商保,和纯代发,不需要这一步操作,因为商保和纯代发不可以进行绑定报价单
     * @param contractPageVo
     * @return java.lang.Integer
     * <AUTHOR>
     * @Date 2021/8/25 15:25
     */
    boolean updateByContractAndContractRelativeQuotationContractNo(ContractPageVo contractPageVo);

	ContractContentInfoVo getEditItemData(String contractNo);

	List<ContractPageVo> getContractListByContractNoList(List<String> contractNoList);

    PrintContractDto getPrintContractData(Long contractProcessId);

    List<QuotationItemVo> getQuotationItemByContractNo(String contractNo);

    int updateNewFlagByNewFlagAndFirstBillDate(int newFlag,int oldNewFlag,String nowDate);

    List<CustomerEmpTrackReportVo> findEmpTrackReport();
    List<CustomerEmpTrackReportVo> findEmpTrackReport(CustomerEmpTrackReportVo customerEmpTrackReportVo);

    List<ContractVo> findBillReportByContractNoList(List<String> contractNoList);

	List<ContractPageVo> getEffectiveContractListByContractNoList(List<String> contractNoList);

	ContractVo getContractByContractNo(String contractNo);

	List<ContractVo> getContractNameByContractNoList(List<String> contractList);

	int updateNewFlagAndRemark(ContractVo contractVo);

	int setContractLog(Map<String, String> contractNoAndLoginNameMap);

	String editComRemark(ContractPageVo contractPageVo);

    List<CustomerContractorExportVo> getCustomerContractor(String loginName);

	List<QuotationItemVo> getQuotationListByContractNo(String contractNo);
    List<ContractVo> getByCustIdAndContractType(List<Long> custIdList, List<Integer> contractTypeList);

	Page<ContractPageVo> getContractInfoByKeywordForRoot(int page, int limit, String keyword);

    void updateInitStartDate();

    int autoRefreshContractEndTime();

    Page getServiceInfo(Integer page, Integer limit, List<CustomerDto> customerDtoList, String userName,Long custId);

    List<Long> selectCustIdListByLoginName(String loginName);

    Map<String, Object> getCustGroupAndCustListByDefaultOrgPosCodeAndLoginNameFromContract(List<OrgPositionDto> orgPosCode);

    List<CustomerEmpTrackReportVo> findOldEmpTrackReportByDto(CustomerEmpTrackReportVo customerEmpTrackReportVo);

    Map<String,Integer>getContractTypeByContractNo(Set<String> contractNoList);

    int saveChangeQuotation(String contractNo,String quoteNo,String longinName);

    List<Map<String, String>> getServiceNature(List<Long> custIdList);

    int saveAddAssociation(String contractNo,String type,String pid,String loginName,List<String> fileIdList,String remark);

    Page<ContractPageVo> getContractPageByCustIdAndInBillTemplet(Integer page, Integer limit, String custId, String name);

    List<Contract> selectNoListByConditionMap(Map<String, Object> conditonMap);

    Integer getDataByContractTypeAndNoBelongLoginName(String seller, Integer contractType, Long custId);

    List<String> getContractNoByUserOrgPositionDtoList(List<String> contractNoList,List<OrgPositionDto> userOrgPositionDtoList);
    List<ContractAreaVo> getContractNoAndRecevingByUserOrgPositionDtoList(List<OrgPositionDto> userOrgPositionDtoList);

    Page<ContractPageVo> getSupplierContractInfoByKeyword(Integer page, Integer limit, String keyword,List<OrgPositionDto> userOrgPositionDtoList);

    Map<String, Object> getSupplierCustGroupAndCustListByLoginNameFromContract(List<OrgPositionDto> orgPosCode);


    boolean getUpdateNameByCustId(Long custId,String loginName);

    List<ContractVo>getCommissionerAndCustNameByCustId(List<Long> custIdList);


    List<ContractVo> getContractNoAndCustNameByContractNoList(List<String> contractNoList);

    List<String> getContractNoByContractNoVoAndAuthList(ContractVo contractVo, List<OrgPositionDto> userOrgPositionDtoList);

    List<ContractAreaVo> getContractVoByContractType();



    /**
     * 根据小合同号获取大合同类型
     */
    Integer selectContractTypeByContractAreaNo(String contractAreaNo);

    List<Long> selectCustIdListFromEhr();

    List<Long> selectCustIdListByLoginNameAndGroupByContractTypeAndLastComm(String loginName);

    Page<EditContractInfoWorkflowVo> getEditContractInfoList(Map<String, Object> paramMap);

    Page<ContractPageVo> getContractPageForSelect(int page, int limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getContractMySelfPageForSelect(int page, int limit, String keyword, String loginName);
    void saveChangeContractItem(ECInfoAddVo eCInfoAddVo);

    ECInfoAddVo getECIWDataById(Long id, String loginName);

    Integer updateECIWDataApprovalStatusByPId(String pid, int code);

    boolean checkContractCanCommit(List<String> contractNoList);

    List<EditContractInfoWorkflowVo> getSpecialDataByContractNo(String contractNo);

    void updatePidByContractNO(String pid, String contractNo);

    void updateSpecialApprovalStatus(String pid, int code);

    void rejectChangeContractItem(String pid, String comment);

    List<EditContractInfoWorkflowVo> getSpecialContractListByPid(Map<String, Object> map);


    List<PrintAccountExportVo> exportPrintAccount(Map<String, Object> conditionMap);

    void dealLogData();

    List<ContractVo> getDataByContractNoList(List<String> contractNoList);

    Map<String, Long> getConNoAndCustIdMapByContractNoList(List<String> salaryContractNoList);

    Integer getContractCompletionReminderCountByLoginName(String loginName);
}
