package com.reon.hr.api.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:
 * @Description: 社保组比例修改日志
 * @Date: 2022/10/14 14:05
 * @Version: 1.0
 */

@Data
public class InsuranceRatioLogVo implements Serializable {
    /**
     * 比例编码Code
     */
    private String insuraceRatioCode;
    /**
     * 操作类型(1：修改,2:删除)
     */
    private Integer oprType;
    /**
     * 修改之前的数据
     */
    private String beforRatio;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;
}
