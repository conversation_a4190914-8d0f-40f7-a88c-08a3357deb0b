package com.reon.hr.sp.customer.service.impl.employee;

import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IMessageWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IinsuranceGroupWrapperService;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.enums.MsgTypeEnum;
import com.reon.hr.api.base.vo.InsuranceGroupRatioVo;
import com.reon.hr.api.base.vo.MessageVo;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.change.enums.ProductCodeEnum;
import com.reon.hr.api.customer.dto.customer.BatchBillStartMonthDTO;
import com.reon.hr.api.customer.dto.customer.BatchEditBillStartMonthDTO;
import com.reon.hr.api.customer.dto.customer.BatchEditProdExpireMonthDTO;
import com.reon.hr.api.customer.dto.customer.QuotationDTO;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dubbo.service.rpc.IBillTempletWrapperService;
import com.reon.hr.api.customer.enums.*;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.enums.supplierPractice.OrderChangeSyncEnum;
import com.reon.hr.api.customer.enums.supplierPractice.SyncEnum;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.utils.ServiceMonthUtil;
import com.reon.hr.api.customer.vo.ContractAreaVo;
import com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo;
import com.reon.hr.api.customer.vo.NonProdFundChangeContext;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletFeeCfgVo;
import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.OrderChangeTaskDetailVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.OrderChangeTaskInfoVo;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.sp.customer.dao.cus.ContractAreaMapper;
import com.reon.hr.sp.customer.dao.cus.ContractMapper;
import com.reon.hr.sp.customer.dao.employee.*;
import com.reon.hr.sp.customer.dao.supplierPractice.OrderChangeTaskDetailMapper;
import com.reon.hr.sp.customer.dao.supplierPractice.OrderChangeTaskInfoMapper;
import com.reon.hr.sp.customer.dao.supplierPractice.SupplierOneChargeMapper;
import com.reon.hr.sp.customer.entity.employee.*;
import com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice.SupplierOneCharge;
import com.reon.hr.sp.customer.service.cus.IBatchImportDataService;
import com.reon.hr.sp.customer.service.cus.QuotationService;
import com.reon.hr.sp.customer.service.employee.IEmployeeEntryDimissionService;
import com.reon.hr.sp.customer.service.employee.IEmployeeOrderService;
import com.reon.hr.sp.customer.service.employee.IPersonOrderEditService;
import com.reon.hr.sp.customer.service.insurancePractice.IInsurancePracticeService;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum.ChangeType.PERSON_ORDER_CHANGE;
import static com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum.ChgMethod.NON_PROVIDENT_FUND_CHANGE;
import static com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum.ChgStatus.CHANGE_SUCCESS;
import static com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum.ChgStatus.CHANGE_WAIT_DISPATCH_CONFIRMED;
import static com.reon.hr.api.customer.utils.DateUtil.IntToDate;
import static com.reon.hr.api.customer.utils.DateUtil.getPrevMonthDate;
import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.*;

@Service
public class PersonOrderEditServiceImpl implements IPersonOrderEditService {
    private Logger log = LoggerFactory.getLogger(getClass());

    private static final String REQUIRED_FIELDS_NOT_FILLED = "必填项未填写";
    private static final String DISOBEY_RULE = "违反规则";

    private static final String ERROR_MESSAGE_1 = "收费开始月填写格式错误";
    private static final String ERROR_MESSAGE_2 = "此订单编号下没有对应的产品";
    private static final String ERROR_MESSAGE_3 = "你没有权限修改此数据";
    private static final String ERROR_MESSAGE_4 = "请检查填入的产品名称和原产品收费开始月";
    private static final String ERROR_MESSAGE_5 = "增员完成 减员完成 变更完成或未变更的才能修改";
    private static final String ERROR_MESSAGE_6 = "检查客户编号是否和订单编号匹配";
    private static final String ERROR_MESSAGE_7 = "相同产品不可修改成相同起始月";
    private static final String ERROR_MESSAGE_8 = "订单编号格式有误";
    private static final String ERROR_MESSAGE_9 = "产品类型中选择的数据,与系统中数据不一致,请按照模板数据选择";
    private static final String ERROR_MESSAGE_10 = "账单起始月不可以修改超过±3个月";
    @Autowired
    private PersonOrderEditMapper mapper;
    @Autowired
    private IinsuranceGroupWrapperService groupWrapperService;
    @Autowired
    private OrderOneChargeMapper orderOneChargeMapper;
    @Autowired
    private SupplierOneChargeMapper supplierOneChargeMapper;
    @Autowired
    private OrderServiceChargeMapper chargeMapper;
    @Autowired
    private EmployeeRemarkMapper remarkMapper;
    @Autowired
    private OrderInsuranceCfgMapper insuanceCfgMapper;

    @Autowired
    private EmployeeOrderMapper employeeOrderMapper;

    @Autowired
    private IEmployeeOrderService employeeOrderService;

    @Autowired
    private IBillTempletWrapperService templetWrapperService;
    @Autowired
    private IInsurancePracticeService insurancePracticeService;

    @Autowired
    private IBatchImportDataService batchImportDataService;

    @Autowired
    private OrderInsuranceCfgMapper orderInsuanceCfgMapper;

    @Autowired
    private PersonOrderEditMapper employeeOrderChangeMapper;

    @Autowired
    private ISequenceService iSequenceService;


    @Autowired
    private IMessageWrapperService iMessageWrapperService;
    @Autowired
    private OrderServiceChargeMapper orderChargeMapper;


    @Autowired
    private ContractAreaMapper contractAreaMapper;

    @Autowired
    private OrderChangeTaskDetailMapper orderChangeTaskDetailMapper;

    @Autowired
    private OrderChangeTaskInfoMapper orderChangeTaskInfoMapper;

    @Autowired
    private QuotationService quotationService;
    @Autowired
    private ContractMapper contractMapper;

    @Resource
    private IEmployeeEntryDimissionService employeeEntryDimissionService;

    @Override
    public Page<PersonOrderEditVo> getListPage(Integer page, Integer limit, PersonOrderEditVo orderVo, String loginName) {
        Page<PersonOrderEditVo> employeeOrderPage = new Page<>(page, limit);
        if (StringUtils.isNotEmpty(orderVo.getReceiving())) {
            orderVo.setReceiving(orderVo.getReceiving().split("_")[1]);
        }
        List<PersonOrderEditVo> record = mapper.getPersonOrderPage(employeeOrderPage, orderVo, loginName);
        employeeOrderPage.setRecords(record);
        return employeeOrderPage;
    }


    public List<OrderInsuranceCfgVo> getOldInsurances(String orderNo) {
        List<OrderInsuranceCfgVo> listByOrderNo = orderInsuanceCfgMapper.getListByOrderNo(orderNo);
        if (CollectionUtils.isNotEmpty(listByOrderNo)) {
            List<Long> groupRatioIdList = listByOrderNo.stream().map(OrderInsuranceCfgVo::getGroupRatioId).collect(Collectors.toList());
            List<InsuranceGroupRatioVo> allRatioAndAmtByGroupIdList = groupWrapperService.getAllRatioAndAmtByGroupIdList(groupRatioIdList);
            Map<Long, InsuranceGroupRatioVo> idInsuranceGroupRatioVoMap = allRatioAndAmtByGroupIdList.stream().collect(toMap(InsuranceGroupRatioVo::getId, Function.identity()));
            for (OrderInsuranceCfgVo orderInsuranceCfgVo : listByOrderNo) {
                InsuranceGroupRatioVo insuranceGroupRatioVo = idInsuranceGroupRatioVoMap.get(orderInsuranceCfgVo.getGroupRatioId());
                orderInsuranceCfgVo.setIndlAdd(insuranceGroupRatioVo.getIndlAdd());
                orderInsuranceCfgVo.setComAdd(insuranceGroupRatioVo.getComAdd());
                orderInsuranceCfgVo.setRatioName(insuranceGroupRatioVo.getRatioName());
                orderInsuranceCfgVo.setCityCode(insuranceGroupRatioVo.getCityCode());
                orderInsuranceCfgVo.setCityName(insuranceGroupRatioVo.getCityName());
                orderInsuranceCfgVo.setRatioCode(insuranceGroupRatioVo.getInsuranceRatioCode());
                orderInsuranceCfgVo.setIndRatio(insuranceGroupRatioVo.getIndRatio());
                orderInsuranceCfgVo.setComRatio(insuranceGroupRatioVo.getComRatio());
            }
            return listByOrderNo;
        }
        return null;
    }


    public List<OrderInsuranceCfgVo> getInsurances(String orderNo, Map<Long, InsuranceGroupRatioVo> idInsuranceGroupRatioVoMap) {
        List<OrderInsuranceCfgVo> listByOrderNo = orderInsuanceCfgMapper.getListByOrderNo(orderNo);
        for (OrderInsuranceCfgVo orderInsuranceCfgVo : listByOrderNo) {
            InsuranceGroupRatioVo insuranceGroupRatioVo = idInsuranceGroupRatioVoMap.get(orderInsuranceCfgVo.getGroupRatioId());
            orderInsuranceCfgVo.setIndlAdd(insuranceGroupRatioVo.getIndlAdd());
            orderInsuranceCfgVo.setComAdd(insuranceGroupRatioVo.getComAdd());
            orderInsuranceCfgVo.setRatioName(insuranceGroupRatioVo.getRatioName());
            orderInsuranceCfgVo.setCityCode(insuranceGroupRatioVo.getCityCode());
            orderInsuranceCfgVo.setCityName(insuranceGroupRatioVo.getCityName());
            orderInsuranceCfgVo.setRatioCode(insuranceGroupRatioVo.getInsuranceRatioCode());
            orderInsuranceCfgVo.setIndRatio(insuranceGroupRatioVo.getIndRatio());
            orderInsuranceCfgVo.setComRatio(insuranceGroupRatioVo.getComRatio());
        }
        return listByOrderNo;

    }

    @Override
    public int savePersonOrderChange(EmployeeOrderVo vo, String loginName, Integer roleType) {
        List<OrderInsuranceCfgVo> oldInsurances = getOldInsurances(vo.getOrderNo());
        JSONArray jsonArray = JSONArray.fromObject(oldInsurances);
        String oldContent = jsonArray.toString();
        // 检查 ，防止重复提交
        String checkRepeatSubmit = mapper.checkRepeatSubmit(vo.getOrderNo(), 1, 1, 1);
        if (StringUtils.isNotEmpty(checkRepeatSubmit)) {
            return 0;
        }
        EmployeeOrderChange orderChange = new EmployeeOrderChange();

        JSONArray json = JSONArray.fromObject(vo.getInsurances());
        String chgContent = json.toString();

        EmployeeOrder employeeOrder = new EmployeeOrder();
        employeeOrder.setOrderNo(vo.getOrderNo());
        employeeOrder.setChgState(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode());


        orderChange.setChgContent(chgContent);
        orderChange.setOrderNo(vo.getOrderNo());
        // 如果是接单客服更改，更改方式设置为 社保公积金变更，变更状态设置为待确认
        orderChange.setChgStatus(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode());
        orderChange.setChgMethod(EmployeeOrderChangeEnum.ChgMethod.RCS_COMMON_CHANGE.getCode());
        orderChange.setChgType(PERSON_ORDER_CHANGE.getCode());

        setEmployeeOrderChange(orderChange, loginName);

        String remark = "接单方订单变更：";
        String modifyRemark = "接单方订单变更备注：";
        // 判断是否是接单客服变更，项目客服变更直接体现到原数据
        if (1 == roleType) {
            // 如果是项目客服更改，更改方式设置为 变更账单月，变更状态设置为已确认
            orderChange.setChgMethod(EmployeeOrderChangeEnum.ChgMethod.BILL_MONTH_CHANGE.getCode());
            orderChange.setChgStatus(CHANGE_SUCCESS.getCode());
            employeeOrder.setChgState(CHANGE_SUCCESS.getCode());
            if (CollectionUtils.isNotEmpty(vo.getInsurances())) {
                for (int i = 0; i < vo.getInsurances().size(); i++) {
                    OrderInsuranceCfg insuanceCfg = new OrderInsuranceCfg();
                    BeanUtils.copyProperties(vo.getInsurances().get(i), insuanceCfg);
                    insuanceCfgMapper.updateByPrimaryKeySelective(insuanceCfg);
                }
            }
            remark = "项目个人订单变更->变更账单月: ";
            modifyRemark = "项目个人订单变更备注：";
        }
        if (vo.getRemarkType() != null) {
            if (EmployeeContractEnum.RemarkTypeEnum.REMARK_TYPE_OTHER.getIndex() == vo.getRemarkType()) {
                remark += EmployeeContractEnum.RemarkTypeEnum.REMARK_TYPE_OTHER.getName() + " " + vo.getOrderRemark() + " ";
            } else if (EmployeeContractEnum.RemarkTypeEnum.REMARK_TYPE_SUBMIT.getIndex() == vo.getRemarkType()) {
                remark += EmployeeContractEnum.RemarkTypeEnum.REMARK_TYPE_SUBMIT.getName() + " ";
            }
        }
        EmployeeRemark employeeRemark = remarkMapper.getByEmployeeId(vo.getOrderNo());
        if (employeeRemark == null) {
            employeeRemark = new EmployeeRemark();
            if (vo.getOrderNo() != null) {
                employeeRemark.setOrderNo(vo.getOrderNo());
            }
            if (vo.getCustId() != null) {
                employeeRemark.setCustomerId(vo.getCustId());
            }
            if (vo.getContractNo() != null) {
                employeeRemark.setContractNo(vo.getContractNo());
            }
            if (vo.getOrderRemark() != null) {
                employeeRemark.setRemark6(vo.getOrderRemark());
            }
            if (vo.getRemarkType() != null) {
                employeeRemark.setRemarkType(vo.getRemarkType());
            }
            if (vo.getAccuAcctNo() != null) {
                employeeRemark.setAccuAcctNo(vo.getAccuAcctNo());
            }
            if (loginName != null) {
                employeeRemark.setCreator(loginName);
            }
            if (vo.getEmployeeId() != null) {
                employeeRemark.setEmployeeId(vo.getEmployeeId());
                remarkMapper.insertSelective(employeeRemark);
            }
        } else {
            if (vo.getOrderRemark() != null) {
                employeeRemark.setRemark6(vo.getOrderRemark());
            }
            if (vo.getAccuAcctNo() != null) {
                employeeRemark.setAccuAcctNo(vo.getAccuAcctNo());
            }
            if (vo.getRemarkType() != null) {
                employeeRemark.setRemarkType(vo.getRemarkType());
            }
            remarkMapper.updateByEmployeeIdSelective(employeeRemark);
        }
        // 保存变更日志
        employeeOrderService.saveEmployeeOrderLog(loginName, vo.getOrderNo(), vo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), remark);
        employeeOrderService.saveEmployeeOrderLog(loginName, vo.getOrderNo(), vo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.MODIFY_REMARK.getCode(), modifyRemark + vo.getModifyRemark().replaceAll("\n", "  ") + " ");

        employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
        for (OneTimeCharge oneTimeCharge : vo.getOneTimeChargVos()) {
            oneTimeCharge.setOrderNo(vo.getOrderNo());
            oneTimeCharge.setCreator(loginName);
        }
        //添加一次性费用记录
        if (!vo.getOneTimeChargVos().isEmpty()) {
            /**只有在确认的时候才能插入数据*/
//            orderOneChargeMapper.insertOneTimeCharge(vo.getOneTimeChargVos());
            String chargesStr = JSONArray.fromObject(vo.getOneTimeChargVos()).toString();
            orderChange.setChgOneTimeFee(chargesStr);
        }
        if (roleType == 2) {
            addSupplierPractice(loginName, vo.getOrderNo(), oldContent, chgContent, vo.getContractAreaNo());
        }
        return mapper.savePersonOrderChange(orderChange);
    }

    @Override
    public int handleOrderServiceChange(NoInsuranceVo vo, String loginName) {
        List<OrderServiceCharge> allServiceCharges = new ArrayList<>();
        List<OrderServiceChargeVo> originalServiceCharges = vo.getServiceCharges().stream()
                .sorted(Comparator.comparing(OrderServiceChargeVo::getRevStartMonth)).collect(Collectors.toList());
        List<String> orderNoList = vo.getOrderNoList();
        ImmutablePair<Map<String, List<OrderServiceCharge>>, Map<String, String>> pair = searchOrderServiceChargeMap(orderNoList);
        Map<String, List<OrderServiceCharge>> orderServiceChargeMap = pair.getLeft();
        /**转入的服务费账单模板是第一个订单第一条服务费，他的收费模板可能和其他的 服务费数据不一致，
         *orderNoAndTempMap： 记录订单与账单模板 */
        Map<String, String> orderNoAndTempMap = pair.getRight();
        /**如果人员已经离职，他得变更任务没有填写截至月，那就将订单得离职月 附上，有的话得不用管*/
        List<EmployeeEntryDimissionVo> employeeEntryDimissionVos = employeeEntryDimissionService.selectByOrderNosAndStatus(orderNoList, PersonOrderEnum.EntryDimissionStatus.DIMISSION.getCode());
        Map<String, EmployeeEntryDimissionVo> dimissionVoMap = employeeEntryDimissionVos.stream().collect(toMap(EmployeeEntryDimissionVo::getOrderNo, Function.identity(), (a, b) -> a));
        List<Long> chargeIds = orderServiceChargeMap.values().stream().flatMap(Collection::stream).map(OrderServiceCharge::getId).collect(Collectors.toList());
        /**防止出现服务费表中已经出现脏数据导致，新增的金额不对*/
        Set<String> quotationsByPage = originalServiceCharges.stream().map(OrderServiceChargeVo::getQuotationNo).collect(Collectors.toSet());
        /**找到所有的报价单，并生成一条正确的服务费金额数据*/
        Set<String> quotations = orderServiceChargeMap.values().stream().flatMap(Collection::stream).map(OrderServiceCharge::getQuotationNo).collect(Collectors.toSet());
        quotations.addAll(quotationsByPage);
        Map<String, OrderServiceCharge> quotationAmountMap = getChargeByQuoteNo(Lists.newArrayList(quotations));
        for (String orderNo : orderNoList) {
            EmployeeEntryDimissionVo employeeEntryDimissionVo = dimissionVoMap.get(orderNo);
            List<OrderServiceCharge> subServiceCharges = new ArrayList<>();
            String[] tempArr = orderNoAndTempMap.get(orderNo).split("_");
            /**serviceCharges: 中的数据是变更选中订单的第一条，他的账单模板可能和其他订单不一致*/
            List<OrderServiceChargeVo> serviceCharges = originalServiceCharges.stream().map(item -> {
                OrderServiceChargeVo chargeVo = new OrderServiceChargeVo();
                BeanUtils.copyProperties(item, chargeVo);
                chargeVo.setTempletId(Long.valueOf(tempArr[0]));
                chargeVo.setRevTempId(Long.valueOf(tempArr[1]));
                return chargeVo;
            }).collect(toList());
            List<OrderServiceCharge> chargeVoList = orderServiceChargeMap.getOrDefault(orderNo, Lists.newArrayList());
            OrderServiceChargeVo lastCharge = serviceCharges.get(serviceCharges.size() - 1);
            if (Objects.nonNull(employeeEntryDimissionVo) && lastCharge.getRevEndMonth() == null) {
                lastCharge.setRevEndMonth(employeeEntryDimissionVo.getExpiredMonth());
            }
            /**如果新的服务费区间 覆盖旧，直接替换，将旧的删除，新增新的*/
            Integer oldMin = chargeVoList.get(0).getRevStartMonth();
            int oldMax = Math.max(Optional.ofNullable(chargeVoList.get(chargeVoList.size() - 1).getRevEndMonth()).orElse(Integer.MAX_VALUE), chargeVoList.get(chargeVoList.size() - 1).getRevStartMonth());
            Integer newMin = serviceCharges.get(0).getRevStartMonth();
            int newMax = Optional.ofNullable(serviceCharges.get(serviceCharges.size() - 1).getRevEndMonth()).orElse(Integer.MAX_VALUE);
            if (newMin <= oldMin && newMax >= oldMax) {
                getNewCharge(serviceCharges, orderNo, subServiceCharges, quotationAmountMap);
            } else if (oldMin > Math.max(newMax, newMin) || oldMax < Math.min(newMax, newMin)) {
                subServiceCharges.addAll(chargeVoList);
                getNewCharge(serviceCharges, orderNo, subServiceCharges, quotationAmountMap);
            }
            /**旧的服务费覆盖新的*/
            else {
                /**根据新旧数据 组合*/
                getNewCharge(serviceCharges, orderNo, subServiceCharges, quotationAmountMap);
                /**有多条服务费，如果有间隙且在旧的服务费区间中，使用旧服务费填充*/
                if (serviceCharges.size() > 1) {
                    /**上一条截止月 到下一条开始月之间有间隙*/
                    Integer preEndMonth = serviceCharges.get(0).getRevEndMonth();
                    for (int i = 1; i < serviceCharges.size(); i++) {
                        OrderServiceChargeVo newChargeVo = serviceCharges.get(i);
                        Integer revStartMonth = newChargeVo.getRevStartMonth();
                        Integer preMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(revStartMonth), 1).replaceAll("-", ""));
                        /**上一条截止月 到下一条开始月之间有间隙*/
                        if (Optional.ofNullable(preEndMonth).orElse(Integer.MAX_VALUE) < preMonth) {
                            /**从旧服务费中找到数据进行填充*/
                            List<OrderServiceCharge> rangeCharge = findRangeCharge(preEndMonth, preMonth, chargeVoList);
                            subServiceCharges.addAll(rangeCharge);
                        }
                    }
                }
                /**处理前面*/
                /**完全小于的直接加入*/
                OrderServiceChargeVo minData = serviceCharges.get(0);
                Integer minRevStartMonth = minData.getRevStartMonth();
                Map<Boolean, List<OrderServiceCharge>> leOrGeCharge = chargeVoList.stream().collect(
                        Collectors.partitioningBy(item -> item.getRevStartMonth() < minRevStartMonth && Optional.ofNullable(item.getRevEndMonth()).orElse(Integer.MAX_VALUE) < minRevStartMonth));
                subServiceCharges.addAll(Optional.ofNullable(leOrGeCharge.get(true)).orElse(Lists.newArrayList()));
                List<OrderServiceCharge> geCharges = leOrGeCharge.getOrDefault(false, Lists.newArrayList()).stream().sorted(Comparator.comparing(OrderServiceCharge::getRevStartMonth))
                        .collect(Collectors.toList());
                OrderServiceCharge chargeVo = null;
                if (CollectionUtils.isNotEmpty(geCharges)) {
                    chargeVo = geCharges.get(0);
                    Integer preMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(minRevStartMonth), 1).replaceAll("-", ""));
                    if (preMonth >= chargeVo.getRevStartMonth()) {
                        OrderServiceCharge oldServiceCharge = new OrderServiceCharge();
                        BeanUtils.copyProperties(chargeVo, oldServiceCharge);
                        oldServiceCharge.setRevEndMonth(preMonth);
                        subServiceCharges.add(oldServiceCharge);
                    }

                    /**如果新服务费倒置 比如
                     * 新的 202303 202302
                     * 旧的 202303 202304
                     * 此时应该变为 202303 202302 、 202304  202304，两段
                     * */
                    if (minData.getRevStartMonth() > Optional.ofNullable(minData.getRevEndMonth()).orElse(Integer.MAX_VALUE)) {
                        Integer startMonth = minData.getRevStartMonth();
                        Integer nextMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(startMonth), -1).replaceAll("-", ""));
                        if (Optional.ofNullable(chargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE) >= nextMonth) {
                            OrderServiceCharge oldServiceCharge = new OrderServiceCharge();
                            BeanUtils.copyProperties(chargeVo, oldServiceCharge);
                            oldServiceCharge.setRevStartMonth(nextMonth);
                            subServiceCharges.add(oldServiceCharge);
                        }
                    }

                }

                OrderServiceChargeVo maxData = serviceCharges.get(serviceCharges.size() - 1);
                List<OrderServiceCharge> hasNullData = subServiceCharges.stream().filter(item -> item.getRevEndMonth() == null).collect(Collectors.toList());
                /**将完全大于上面添加的区间的数据直接添加*/
                if (hasNullData.size() <= 0) {
                    Integer maxRevEndMonth = Math.max(maxData.getRevEndMonth(), maxData.getRevStartMonth());
                    Map<Boolean, List<OrderServiceCharge>> geOrLeCharge = chargeVoList.stream().collect(
                            Collectors.partitioningBy(item -> item.getRevStartMonth() > maxRevEndMonth && Optional.ofNullable(item.getRevEndMonth()).orElse(Integer.MAX_VALUE) > maxRevEndMonth));
                    subServiceCharges.addAll(Optional.ofNullable(geOrLeCharge.get(true)).orElse(Lists.newArrayList()));
                    List<OrderServiceCharge> leCharges = geOrLeCharge.getOrDefault(false, Lists.newArrayList()).stream().sorted(Comparator.comparing(OrderServiceCharge::getRevStartMonth))
                            .collect(Collectors.toList());
                    /***/
                    if (CollectionUtils.isNotEmpty(leCharges) && !leCharges.get(leCharges.size() - 1).equals(chargeVo)) {
                        OrderServiceCharge chargeVo2 = leCharges.get(leCharges.size() - 1);
                        Integer nextMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(maxRevEndMonth), -1).replaceAll("-", ""));
                        if (nextMonth <= Optional.ofNullable(chargeVo2.getRevEndMonth()).orElse(Integer.MAX_VALUE)) {
                            OrderServiceCharge oldServiceCharge = new OrderServiceCharge();
                            BeanUtils.copyProperties(chargeVo2, oldServiceCharge);

                            /**如果和已存在的冲突，将截止月向后移*/
                            Map<Integer, List<OrderServiceCharge>> collect = subServiceCharges.stream().collect(groupingBy(OrderServiceCharge::getRevStartMonth));
                            while (collect.containsKey(nextMonth)) {
                                nextMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(nextMonth), -1).replaceAll("-", ""));
                            }
                            oldServiceCharge.setRevStartMonth(nextMonth);
                            if (nextMonth <= Optional.ofNullable(chargeVo2.getRevEndMonth()).orElse(Integer.MAX_VALUE)) {
                                subServiceCharges.add(oldServiceCharge);
                            }
                        }
                    }
                }
            }
            allServiceCharges.addAll(subServiceCharges);
        }

        if (CollectionUtils.isNotEmpty(allServiceCharges)) {
            Date date = new Date();
            List<OrderServiceCharge> insertCharge = allServiceCharges.stream().map(item -> {
                OrderServiceCharge charge = new OrderServiceCharge();
                BeanUtils.copyProperties(item, charge);
                charge.setCreator(loginName);
                charge.setCreateTime(date);
                charge.setUpdater(loginName);
                charge.setUpdateTime(date);
                return charge;
            }).collect(Collectors.toList());
            /**如果修改出来开始月相同，或者多条数据时间区间存在交集的数据，则表示修改代码存在bug，此次修改失败*/
            if (!checkOrderServiceChargeData(insertCharge, orderNoAndTempMap, quotationAmountMap)) {
                return 0;
            }
            if (CollectionUtils.isNotEmpty(chargeIds)) {
                orderChargeMapper.deleteOrderServiceCharge(chargeIds);
            }
            orderChargeMapper.insertOrderServiceCharges(insertCharge);
        }


        // 修改订单变更状态
        employeeOrderMapper.batchUpdateChgStatusByOrderNos(3, orderNoList, loginName);
        EmployeeOrderChange orderChange;
        // 将 order_service_charge 对象转为字符串
        String chgContent = JsonUtil.beanToJson(vo.getServiceCharges());
        int result = 0;
        // 为每一个订单编号生成一条变更记录
        List<EmployeeOrderChange> list = new ArrayList<>();
        for (String orderNo : orderNoList) {
            orderChange = new EmployeeOrderChange();
            NonProdFundChangeContext changeContext = new NonProdFundChangeContext();
            List<OrderServiceCharge> chargeVoList = orderServiceChargeMap.get(orderNo);
            changeContext.setChangeContext(chgContent);
            if (CollectionUtils.isNotEmpty(chargeVoList)) {
                changeContext.setChangeDataBefore(JsonUtil.beanToJson(chargeVoList));
            }
            /**记录变更之前数据，用于回退数据，防止有人误操作*/
            orderChange.setChgContent(JsonUtil.beanToJson(changeContext));
//            orderChange.setChgContent(chgContent);
            orderChange.setOrderNo(orderNo);
            // 项目客服批量更改 变更方式设置为非社保公积金变更  变更状态设置为 已确认
            orderChange.setChgStatus(CHANGE_SUCCESS.getCode());
//            orderChange.setChgMethod(EmployeeOrderChangeEnum.ChgMethod.BILL_MONTH_CHANGE.getCode());
            orderChange.setChgMethod(NON_PROVIDENT_FUND_CHANGE.getCode());
            orderChange.setChgType(PERSON_ORDER_CHANGE.getCode());
            setEmployeeOrderChange(orderChange, loginName);
            list.add(orderChange);
            // 保存变更日志
            employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, vo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), "派单方订单变更：");
        }
        return mapper.batchSavePersonOrderChange(list);
    }

    private void getNewCharge(List<OrderServiceChargeVo> serviceCharges, String orderNo, List<OrderServiceCharge> subServiceCharges, Map<String, OrderServiceCharge> quotationAmountMap) {
        List<OrderServiceCharge> collect = serviceCharges.stream().map(item -> {
            OrderServiceCharge chargeVo = new OrderServiceCharge();
            BeanUtils.copyProperties(item, chargeVo);
            OrderServiceCharge serviceCharge = quotationAmountMap.get(item.getQuotationNo());
            chargeVo.setOrderNo(orderNo);
            BigDecimal amount = serviceCharge.getAmount();
            BigDecimal valTax = serviceCharge.getValTax();
            if (BigDecimalUtil.equalsZero(item.getAmount())) {
                amount = BigDecimal.ZERO;
                valTax = BigDecimal.ZERO;
            }
            BigDecimal taxfreeAmt = serviceCharge.getTaxfreeAmt();
            if (BigDecimalUtil.equalsZero(item.getTaxfreeAmt())) {
                taxfreeAmt = BigDecimal.ZERO;
            }
            chargeVo.setAmount(amount);
            chargeVo.setTaxfreeAmt(taxfreeAmt);
            chargeVo.setValTaxRate(serviceCharge.getValTaxRate());
            chargeVo.setValTax(valTax);
            return chargeVo;
        }).collect(Collectors.toList());
        subServiceCharges.addAll(collect);
    }

    private Map<String, OrderServiceCharge> getChargeByQuoteNo(List<String> quotations) {
        List<QuotationDTO> quotationDtos = quotationService.findQuotationPriceAndRatio(quotations);
        return quotationDtos.stream().map(vo -> {
            BigDecimal amount, taxfreeAmt, valTaxRate = vo.getTaxRatio(), valTax;
            OrderServiceCharge charge = new OrderServiceCharge();
            Integer taxFlag = vo.getTaxFlag();
            if (taxFlag.equals(QuotationEnum.TaxFlagEnum.BOOLEAN_TYPE1.getIndex())) {
                taxfreeAmt = vo.getPrice();
                amount = vo.getPrice().multiply(BigDecimal.ONE.add(valTaxRate)).setScale(2, RoundingMode.HALF_UP);
                valTax = amount.subtract(taxfreeAmt);
            } else {
                amount = vo.getPrice();
                taxfreeAmt = vo.getPrice().divide(BigDecimal.ONE.add(valTaxRate), 2, RoundingMode.HALF_UP);
                valTax = amount.subtract(taxfreeAmt);
            }
            charge.setValTaxRate(valTaxRate);
            charge.setAmount(amount);
            charge.setTaxfreeAmt(taxfreeAmt);
            charge.setValTax(valTax);
            charge.setQuotationNo(vo.getQuoteNo());
            return charge;
        }).collect(toMap(OrderServiceCharge::getQuotationNo, Function.identity()));
    }

    /**
     @param preEndMonth  上一个截止月
     @param preMonth     当前开始用的上一个月
     @param chargeVoList 已存在的数据
     */
    private List<OrderServiceCharge> findRangeCharge(Integer preEndMonth, Integer preMonth, List<OrderServiceCharge> chargeVoList) {
        List<OrderServiceCharge> result = Lists.newArrayList();
        int startMonth = Integer.parseInt(getPrevMonthDate(IntToDate(preEndMonth), -1).replaceAll("-", ""));
        int i = 0;
        while (startMonth <= preMonth && i < chargeVoList.size()) {
            OrderServiceCharge vo = chargeVoList.get(i);
            if (vo.getRevStartMonth() <= startMonth && Optional.ofNullable(vo.getRevEndMonth()).orElse(Integer.MAX_VALUE) >= startMonth) {
                OrderServiceCharge newServiceCharge = new OrderServiceCharge();
                BeanUtils.copyProperties(vo, newServiceCharge);
                newServiceCharge.setRevStartMonth(startMonth);
                Integer end = vo.getRevEndMonth();
                if (Optional.ofNullable(vo.getRevEndMonth()).orElse(Integer.MAX_VALUE) >= preMonth) {
                    end = preMonth;
                }
                newServiceCharge.setRevEndMonth(end);
                result.add(newServiceCharge);
                if (Objects.isNull(vo.getRevEndMonth())) {
                    break;
                }
                startMonth = Integer.parseInt(getPrevMonthDate(IntToDate(vo.getRevEndMonth()), -1).replaceAll("-", ""));
            }
            i++;
        }
        return result;
    }


    @Override
    public EmployeeOrderChangeVo findById(Long id) {
        return mapper.findById(id);
    }

    /**
     确认或驳回订单
     @return
     */
    @Override
    public int handleConfirmOrderChange(ConfirmOrderChangeVo vo, String loginName) {
        int result;
        if (vo.getStatus() == ExportPersonOrderEnum.IsTheStatusTypeEnum.ORDER_CHG_STATUS2.getIndex()) {
            result = mapper.updatePersonOrderChangeByKey(ExportPersonOrderEnum.IsTheStatusTypeEnum.ORDER_CHG_STATUS3.getIndex(), vo.getIds(), vo.getReason(), loginName);
        } else {
            result = mapper.updatePersonOrderChangeByKey(vo.getStatus(), vo.getIds(), vo.getReason(), loginName);
        }
        // 变更驳回，修改个人订单变更表状态
        // 同意变更，修改订单数据
        for (Long id : vo.getIds()) {
            EmployeeOrderChangeVo employeeOrderChangeVo = mapper.findById(id);
            // 保存变更日志
            // TODO 获取employeeId需优化
            EmployeeOrderVo employeeOrderVo = employeeOrderMapper.selectByPrimaryKey(employeeOrderChangeVo.getOrderNo(), DateUtil.getCurrYearMonth());
            String remark = "派单方订单变更驳回: " + vo.getReason() + "  ";
            /**驳回*/
            if (rejectApprove(vo, employeeOrderChangeVo)) {
                String employee = IMessageWrapperService.MESSAGE_EMPLOYEE_ORDER_REJECT.replace("{orderNo}", employeeOrderVo.getOrderNo());
                MessageVo messageVo = new MessageVo();
                messageVo.setMsgNo(iSequenceService.getMsgNo());
                messageVo.setMsgTitle(MsgTypeEnum.EMPLOYEE_ORDER_REJECT[0]);
                messageVo.setMsgType(MsgTypeEnum.ORDER_REJECTION_PENDING.getCode());
                messageVo.setReceiver(employeeOrderVo.getRevCs());
                messageVo.setReadFlag(1);
                messageVo.setContent(employee);
                messageVo.setCreator(employeeOrderVo.getRevCs());
                iMessageWrapperService.save(messageVo);
            }
            /**通过*/
            if (confirmApprove(vo, employeeOrderChangeVo)) {
                remark = "派单方订单变更确认: ";
                /**一次性变更：只会变更一次性数据，下面处理cfg 逻辑不需要执行*/
                if (oneTimeChangeFlag(employeeOrderChangeVo)) {
                    String chgOneTimeFeeStr = employeeOrderChangeVo.getChgOneTimeFee();
                    if (StringUtils.isNotBlank(chgOneTimeFeeStr)) {
                        JSONArray oneTimeArray = JSONArray.fromObject(chgOneTimeFeeStr);
                        List<OneTimeCharge> oneTimeCharges = JSONArray.toList(oneTimeArray, OneTimeCharge.class);
                        orderOneChargeMapper.insertOneTimeCharge(oneTimeCharges);
                        insuertToSupplier(oneTimeCharges);
                    }

                } else {
                    Map<String, Integer> getBillStartMonthMap = new HashMap<>();
                    for (OrderInsuranceCfgVo orderInsuranceCfgVo : vo.getInsurances()) {
                        String key = orderInsuranceCfgVo.getId() + "-" + orderInsuranceCfgVo.getGroupRatioId() + "-" + orderInsuranceCfgVo.getRevStartMonth();
                        if (!getBillStartMonthMap.containsKey(key)) {
                            getBillStartMonthMap.put(key, orderInsuranceCfgVo.getBillStartMonth());
                        }
                    }
                    // 根据订单编号删除订单产品项
                    List<OrderInsuranceCfgVo> listByOrderNo = insuanceCfgMapper.getListByOrderNo(employeeOrderChangeVo.getOrderNo());
                    Map<Integer, List<OrderInsuranceCfgVo>> getInsuranceCfgByProductCodeMap = listByOrderNo.stream().filter(c -> c.getReturnMonth() != null && c.getReturnMonth() != 0).collect(groupingBy(OrderInsuranceCfgVo::getProductCode));
                    /**获取变更中数据*/
                    ImmutablePair<List<OrderInsuranceCfgVo>, List<OneTimeCharge>> pair = getChangeData(employeeOrderChangeVo);
                    List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = pair.getLeft();
                    List<OneTimeCharge> oneTimeCharges = pair.getRight();
                    if (CollectionUtils.isNotEmpty(oneTimeCharges)) {
                        orderOneChargeMapper.insertOneTimeCharge(oneTimeCharges);
                        insuertToSupplier(oneTimeCharges);
                    }
                    insuanceCfgMapper.batchDelByOrderNo(employeeOrderChangeVo.getOrderNo());
                    Long revTempId = orderInsuranceCfgVoList.get(0).getRevTempId();
                    BillTempletFeeCfgVo feeTemplate = templetWrapperService.getFeeTempletById(revTempId);
                    for (OrderInsuranceCfgVo orderInsuranceCfgVo : orderInsuranceCfgVoList) {
                        String key = orderInsuranceCfgVo.getId() + "-" + orderInsuranceCfgVo.getGroupRatioId() + "-" + orderInsuranceCfgVo.getRevStartMonth();
                        if (getBillStartMonthMap.get(key) != null) {
                            orderInsuranceCfgVo.setBillStartMonth(getBillStartMonthMap.get(key));
                        }
                        OrderInsuranceCfg insuranceCfg = new OrderInsuranceCfg();
                        BeanUtils.copyProperties(orderInsuranceCfgVo, insuranceCfg);
                        // 将月份清空的字段 赋值为null
                        if (null != insuranceCfg.getExpiredMonth() && 0 == insuranceCfg.getExpiredMonth()) {
                            insuranceCfg.setExpiredMonth(null);
                        }
                        if (null != insuranceCfg.getBillStartMonth() && 0 == insuranceCfg.getBillStartMonth()) {
                            insuranceCfg.setBillStartMonth(null);
                        }
                        if (null != insuranceCfg.getRevStartMonth() && 0 == insuranceCfg.getRevStartMonth()) {
                            insuranceCfg.setRevStartMonth(null);
                        }
                        if (null != insuranceCfg.getReturnMonth() && 0 == insuranceCfg.getReturnMonth()) {
                            insuranceCfg.setReturnMonth(null);
                        }
                        if (insuranceCfg.getReturnMonth() == null && getInsuranceCfgByProductCodeMap.containsKey(orderInsuranceCfgVo.getProductCode())) {
                            insuranceCfg.setReturnMonth(getInsuranceCfgByProductCodeMap.get(orderInsuranceCfgVo.getProductCode()).get(0).getReturnMonth());
                        }
                        /** 如果变更了截止月,从缓存中获取数据 进行判断ReturnMonth 且 expiredMonth一定会有值 */
                        Integer returnMonth = null;
                        if (insuranceCfg.getExpiredMonth() != null) {
                            returnMonth = ServiceMonthUtil.getNextBillMonth(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), insuranceCfg.getExpiredMonth());
                        }
                        if (returnMonth != null && insuranceCfg.getReturnMonth() != null && ServiceMonthUtil.isGetBill(feeTemplate.getReceiveMonthType(), feeTemplate.getCollectFreq(), insuranceCfg.getReturnMonth()) && insuranceCfg.getReturnMonth() > returnMonth) {
                            /** 如果退费月命中则 使用退费月  如果没命中,则就是下个账单月*/
                            returnMonth = insuranceCfg.getReturnMonth();
                        }
                        insuranceCfg.setReturnMonth(returnMonth);
                        // 项目变更订单确认时，若截止月不为空，更新lastMonth值
                        if (null != insuranceCfg.getExpiredMonth()) {
                            insuranceCfg.setLastMonth(insuranceCfg.getExpiredMonth());
                        } else {
                            insuranceCfg.setLastMonth(0);
                        }
                        //根据id是否存在 判断新增还是修改
                        insuranceCfg.setOrderNo(employeeOrderChangeVo.getOrderNo());
                        insuranceCfg.setCreator(loginName);
                        insuranceCfg.setUpdater(loginName);
                        insuanceCfgMapper.insertSelective(insuranceCfg);
                    }
                    /**
                     * 订单确认变更 将产品的截止月 开始 等字段同步到实做中
                     *
                     * 订单变更时候 查看实做是否 是申请 如果是申请将变更的数据同步过实做
                     *
                     * 订单变更根据  产品来判断是否需要新增实做
                     */
                    insurancePracticeService.handleEmployeeOrderChangeInfluence(listByOrderNo, orderInsuranceCfgVoList, employeeOrderVo, loginName);
                }
            }

            employeeOrderService.saveEmployeeOrderLog(loginName, employeeOrderChangeVo.getOrderNo(), employeeOrderVo.getEmployeeId(), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), remark);

            // 修改订单状态
            EmployeeOrder employeeOrder = new EmployeeOrder();
            employeeOrder.setOrderNo(employeeOrderChangeVo.getOrderNo());
            employeeOrder.setChgState(CHANGE_SUCCESS.getCode());
            employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
        }
        return result;
    }

    private void insuertToSupplier(List<OneTimeCharge> oneTimeCharges) {
        List<SupplierOneCharge> supplierOneCharges = oneTimeCharges.stream().map(data -> {
            SupplierOneCharge charge = new SupplierOneCharge();
            BeanUtils.copyProperties(data, charge);
            return charge;
        }).collect(Collectors.toList());
        supplierOneChargeMapper.insertOneTimeCharge(supplierOneCharges);
    }

    private boolean oneTimeChangeFlag(EmployeeOrderChangeVo employeeOrderChangeVo) {
        return employeeOrderChangeVo.getChgStatus().equals(CHANGE_SUCCESS.getCode()) &&
                employeeOrderChangeVo.getChgMethod().equals(EmployeeOrderChangeEnum.ChgMethod.ONCE_FEE_CHG.getCode()) &&
                employeeOrderChangeVo.getChgType().equals(PERSON_ORDER_CHANGE.getCode());
    }

    private boolean confirmApprove(ConfirmOrderChangeVo vo, EmployeeOrderChangeVo employeeOrderChangeVo) {
        return null != employeeOrderChangeVo && StringUtils.isNotEmpty(employeeOrderChangeVo.getOrderNo()) && ChangeOperateStatus.CONFIRM.getCode() == vo.getStatus();
    }

    private boolean rejectApprove(ConfirmOrderChangeVo vo, EmployeeOrderChangeVo employeeOrderChangeVo) {
        return null != employeeOrderChangeVo && StringUtils.isNotEmpty(employeeOrderChangeVo.getOrderNo()) && ChangeOperateStatus.REJECT.getCode() == vo.getStatus();
    }

    private ImmutablePair<List<OrderInsuranceCfgVo>, List<OneTimeCharge>> getChangeData(EmployeeOrderChangeVo employeeOrderChangeVo) {
        String chgContent = employeeOrderChangeVo.getChgContent();
        JSONArray array = JSONArray.fromObject(chgContent);
        List<OrderInsuranceCfgVo> orderInsuranceCfgVoList = JSONArray.toList(array, OrderInsuranceCfgVo.class);
        List<OneTimeCharge> oneTimeCharge = Lists.newArrayList();
        String chgOneTimeFee = employeeOrderChangeVo.getChgOneTimeFee();
        if (StringUtils.isNotBlank(chgOneTimeFee)) {
            if (StringUtils.isNotBlank(chgOneTimeFee)) {
                JSONArray oneTimeArray = JSONArray.fromObject(chgOneTimeFee);
                oneTimeCharge.addAll(JSONArray.toList(oneTimeArray, OneTimeCharge.class));
            }
        }
        return new ImmutablePair<>(orderInsuranceCfgVoList, oneTimeCharge);
    }

    private void setEmployeeOrderChange(EmployeeOrderChange orderChange, String loginName) {
        orderChange.setCreateTime(new Date());
        orderChange.setCreator(loginName);
        orderChange.setDelFlag("N");
        orderChange.setUpdater(loginName);
        orderChange.setUpdateTime(new Date());
    }

    @Override
    public EmployeeOrderChangeVo searchByOrderNo(String orderNo, Integer chgType) {
        return mapper.searchByOrderNo(orderNo, chgType);
    }

    @Override
    public void batchPersonOrderEditSave(ImportDataDto<BatchBillStartMonthDTO> importDataDto) {
        String remark = "接单方批量变更收费开始月：";
        String loginName = importDataDto.getLoginName();
        batchImportDataService.addImportData(importDataDto, ImportDataType.BATCH_EDIT_BILLSTARTMONTH.getCode());
        log.info("批量导入开始,导入编号{}", importDataDto.getImportNo());
        // 校验导入数据必填项是否填写
        checkImportDataMustWriteField(importDataDto);
        Map<String, List<BatchBillStartMonthDTO>> orderNoMap = importDataDto.getDataList().stream().collect(groupingBy(BatchBillStartMonthDTO::getOrderNo));
        for (String orderNo : orderNoMap.keySet()) {
            EmployeeOrderChange orderChange = new EmployeeOrderChange();
            List<OrderInsuranceCfgVo> allInsuranceCfgByOrderNoForRatioOn = orderInsuanceCfgMapper.getAllInsuranceCfgByOrderNoForRatioOn(orderNo);
            for (BatchBillStartMonthDTO batchBillStartMonthDTO : orderNoMap.get(orderNo)) {
                if (batchBillStartMonthDTO.getErrorDescription().isEmpty()) {
                    //如果为空说明此订单号下没有对应的产品
                    if (CollectionUtils.isEmpty(allInsuranceCfgByOrderNoForRatioOn)) {
                        batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_2);
                    } else {
                        //找到对应的产品为
                        boolean flag = false;
                        for (OrderInsuranceCfgVo orderInsuranceCfgVo : allInsuranceCfgByOrderNoForRatioOn) {
                            if (orderInsuranceCfgVo.getProductCode().equals(InsuranceIRatioProductCodeEnum.getIndex(batchBillStartMonthDTO.getProductName()))) {
                                if (orderInsuranceCfgVo.getRevStartMonth().equals(Integer.valueOf(batchBillStartMonthDTO.getOldRevStartMonth()))) {
                                    orderInsuranceCfgVo.setRevStartMonth(Integer.valueOf(batchBillStartMonthDTO.getRevStartMonth()));
                                    flag = true;
                                }

                            }
                        }
                        if (!flag) {
                            batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_4);
                        }
                        Map<Integer, List<OrderInsuranceCfgVo>> collect = allInsuranceCfgByOrderNoForRatioOn.stream().collect(groupingBy(OrderInsuranceCfgVo::getProductCode));
                        for (Integer integer : collect.keySet()) {
                            List<OrderInsuranceCfgVo> orderInsuranceCfgVos = collect.get(integer);
                            if (orderInsuranceCfgVos.size() > 1) {
                                Set<Integer> setList = orderInsuranceCfgVos.stream().map(OrderInsuranceCfgVo::getRevStartMonth).collect(Collectors.toSet());
                                if (setList.size() == 1) {
                                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_7);
                                }
                            }
                        }
                    }
                    if (!batchBillStartMonthDTO.getErrorDescription().isEmpty()) {
                        importDataDto.recordError(batchBillStartMonthDTO.getRowNum(), JSONUtils.toJSONString(batchBillStartMonthDTO.getErrorDescription()));
                    }
                }

            }
            for (int i = 0; i < allInsuranceCfgByOrderNoForRatioOn.size(); i++) {
                InsuranceGroupRatioVo groupRatioVo = groupWrapperService.getGroupRatioNameAndCodeByGroupRatioId(allInsuranceCfgByOrderNoForRatioOn.get(i).getGroupRatioId());
                OrderInsuranceCfgVo orderInsuranceVo = allInsuranceCfgByOrderNoForRatioOn.get(i);
                if (orderInsuranceVo != null) {
                    orderInsuranceVo.setGroupRatioId(groupRatioVo.getId());
                    orderInsuranceVo.setRatioCode(groupRatioVo.getInsuranceRatioCode());
                    orderInsuranceVo.setRatioName(groupRatioVo.getRatioName());
                    orderInsuranceVo.setGroupCode(groupRatioVo.getInsuranceGroupCode());
                    orderInsuranceVo.setGroupName(groupRatioVo.getGroupName());
                    orderInsuranceVo.setComRatio(groupRatioVo.getComRatio());
                    orderInsuranceVo.setIndRatio(groupRatioVo.getIndRatio());
                    orderInsuranceVo.setComAdd(groupRatioVo.getComAdd());
                    orderInsuranceVo.setIndlAdd(groupRatioVo.getIndlAdd());
                    allInsuranceCfgByOrderNoForRatioOn.set(i, orderInsuranceVo);
                }
            }
            Long employeeId = employeeOrderMapper.findEmployeeIdByOrderNo(orderNo);
            EmployeeOrder employeeOrder = new EmployeeOrder();
            JSONArray json = JSONArray.fromObject(allInsuranceCfgByOrderNoForRatioOn);
            String chgContent = json.toString();
            employeeOrder.setOrderNo(orderNo);
            employeeOrder.setChgState(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode());
            orderChange.setChgContent(chgContent);
            orderChange.setOrderNo(orderNo);
            orderChange.setCreator(loginName);
            orderChange.setUpdater(loginName);
            orderChange.setChgStatus(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode());
            orderChange.setChgMethod(EmployeeOrderChangeEnum.ChgMethod.RCS_COMMON_CHANGE.getCode());
            orderChange.setChgType(PERSON_ORDER_CHANGE.getCode());
            ArrayList<String> strings = new ArrayList<>();
            List<Map<String, List<String>>> errorDescriptionList = orderNoMap.get(orderNo).stream().map(BatchBillStartMonthDTO::getErrorDescription).collect(Collectors.toList());
            for (Map<String, List<String>> stringListMap : errorDescriptionList) {
                strings.addAll(stringListMap.keySet());
            }
            if (CollectionUtils.isEmpty(strings)) {
                List<OrderInsuranceCfgVo> oldInsurances = getOldInsurances(orderNo);
                JSONArray jsonArray = JSONArray.fromObject(oldInsurances);
                String oldContent = jsonArray.toString();
                /** 供应商实做*/
                addSupplierPractice(loginName, orderNo, oldContent, chgContent, null);
                employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
                // 保存变更日志
                employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, employeeId, PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), remark);
                mapper.savePersonOrderChange(orderChange);
            }


        }

        //错误信息插入日志
        batchImportDataService.addAndupdateImportData(importDataDto);

    }

    @Override
    public void batchEditProdExpireMonth(ImportDataDto<BatchEditProdExpireMonthDTO> importDataDto) {
        String remark = "接单方批量变更产品截止月：";
        String loginName = importDataDto.getLoginName();
        batchImportDataService.addImportData(importDataDto, ImportDataType.BATCH_EDIT_PROD_EXPIREMONTH.getCode());
        log.info("接单方批量变更产品截止月,导入编号{}", importDataDto.getImportNo());
        // 校验导入数据必填项是否填写
        checkImportDataForExpireMonth(importDataDto);
        Map<String, List<BatchEditProdExpireMonthDTO>> orderNoMap = importDataDto.getDataList().stream().filter(item -> MapUtils.isEmpty(item.getErrorDescription())).collect(groupingBy(BatchEditProdExpireMonthDTO::getOrderNo));
        List<String> orderNoList = Lists.newArrayList(orderNoMap.keySet());
        List<OrderInsuranceCfgVo> allInsuranceCfgList = Lists.newArrayList();
        Map<Long, InsuranceGroupRatioVo> idInsuranceGroupRatioVoMap = Maps.newHashMap();
        List<EmployeeOrderVo> employeeIdByOrderNos = Lists.newArrayList();
        Map<Object, String> orderNoAndReceiveManMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            allInsuranceCfgList = orderInsuanceCfgMapper.getListByOrderNoList(orderNoList);
            List<Long> groupRatioIdList = allInsuranceCfgList.stream().map(OrderInsuranceCfgVo::getGroupRatioId).distinct().collect(toList());
            List<InsuranceGroupRatioVo> allRatioAndAmtByGroupIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupRatioIdList))
                allRatioAndAmtByGroupIdList = groupWrapperService.getAllRatioAndAmtByGroupIdList(groupRatioIdList);
            idInsuranceGroupRatioVoMap = allRatioAndAmtByGroupIdList.stream().collect(toMap(InsuranceGroupRatioVo::getId, Function.identity()));
            employeeIdByOrderNos = employeeOrderMapper.findEmployeeIdByOrderNos(orderNoList);
            List<ContractAreaVo> receivingManByOrderNo = contractAreaMapper.getReceivingManByOrderNo(orderNoList);
            orderNoAndReceiveManMap = receivingManByOrderNo.stream().collect(toMap(ContractAreaVo::getOrderNo, ContractAreaVo::getReceivingMan));

        }
        Map<String, Set<Integer>> orderNoAndProdCodeMap = allInsuranceCfgList.stream()
                .filter(item -> !BigDecimalUtil.equalsZero(item.getComBase()) &&
                        !BigDecimalUtil.equalsZero(item.getIndBase()) &&
                        (!BigDecimalUtil.equalsZero(item.getComAmt()) || !BigDecimalUtil.equalsZero(item.getIndAmt())))
                .collect(groupingBy(OrderInsuranceCfgVo::getOrderNo, mapping(OrderInsuranceCfgVo::getProductCode, toSet())));
        Map<String, Map<Integer, List<OrderInsuranceCfgVo>>> orderNoAndProdCodeAndCfgListMap = allInsuranceCfgList.stream()
                .filter(item -> !BigDecimalUtil.equalsZero(item.getComBase()) &&
                        !BigDecimalUtil.equalsZero(item.getIndBase()) &&
                        (!BigDecimalUtil.equalsZero(item.getComAmt()) || !BigDecimalUtil.equalsZero(item.getIndAmt())))
                .collect(groupingBy(OrderInsuranceCfgVo::getOrderNo, groupingBy(OrderInsuranceCfgVo::getProductCode)));

        Map<String, String> orderNoAndContractAreaNoMap = employeeIdByOrderNos.stream().collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getContractAreaNo, (a, b) -> b));
        Map<String, Long> orderNoAndEmpIdMap = employeeIdByOrderNos.stream().filter(item -> item.getEmployeeId() != null).collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getEmployeeId, (a, b) -> b));
        for (String orderNo : orderNoList) {
            List<OrderInsuranceCfgVo> updateList = Lists.newArrayList();
            for (BatchEditProdExpireMonthDTO batchEditProdExpireMonthDTO : orderNoMap.get(orderNo)) {
                //如果为空说明此订单号下没有对应的产品
                OrderInsuranceCfgVo orderInsuranceCfgVo = new OrderInsuranceCfgVo();
                Integer productCode = batchEditProdExpireMonthDTO.getProductCode();
                if (!importDataDto.getLoginName().equals(orderNoAndReceiveManMap.get(orderNo)))
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, "您不是当前订单的接单,无法变更");
                else if (!orderNoAndProdCodeMap.getOrDefault(orderNo, Sets.newHashSet()).contains(productCode))
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_2);
                else {
                    List<OrderInsuranceCfgVo> cfgList = orderNoAndProdCodeAndCfgListMap.getOrDefault(orderNo, Maps.newHashMap()).getOrDefault(productCode, Lists.newArrayList());
                    /** 首先,置为0的数据已经过滤掉了  现在List中只剩 倒置的和正常的数据
                     如果没有倒置的就取收费起始月最大那条
                     如果有倒置的  1. 倒置是最大的,那么不做调整,返回一个提示,让他去单个修改
                     2. 不是最大的 那么还是修改收费起始月最大那条  */
                    /** true 表示正常收费的, false 表示倒置的  */
                    Map<Boolean, OrderInsuranceCfgVo> partitionAndMaxRevStartMonthMap = cfgList.stream()
                            .collect(Collectors.groupingBy(item -> {
                                if (item.getExpiredMonth() == null || item.getRevStartMonth() <= item.getExpiredMonth())
                                    return true;
                                else return false;
                            }, Collectors.collectingAndThen(Collectors.maxBy(comparingInt(OrderInsuranceCfgVo::getRevStartMonth)), item -> item.get())));
                    if (!partitionAndMaxRevStartMonthMap.containsKey(true)) // 表示没有正常的数据
                        batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, "表中数据全为倒置,请去单个调整");
                    else {
                        if (!partitionAndMaxRevStartMonthMap.containsKey(false)) {  // 表示没有倒置数据
                            OrderInsuranceCfgVo normalCfg = partitionAndMaxRevStartMonthMap.get(true);
                            orderInsuranceCfgVo.setId(normalCfg.getId());
                            orderInsuranceCfgVo.setExpiredMonth(Integer.valueOf(batchEditProdExpireMonthDTO.getExpireMonth()));
                            updateList.add(orderInsuranceCfgVo);
                        } else {  // 表示有倒置数据又有正常数据 需要比较
                            OrderInsuranceCfgVo normalCfg = partitionAndMaxRevStartMonthMap.get(true);
                            OrderInsuranceCfgVo inversionCfg = partitionAndMaxRevStartMonthMap.get(false);
                            if (normalCfg.getRevStartMonth() > inversionCfg.getRevStartMonth()) {
                                orderInsuranceCfgVo.setId(normalCfg.getId());
                                orderInsuranceCfgVo.setExpiredMonth(Integer.valueOf(batchEditProdExpireMonthDTO.getExpireMonth()));
                                updateList.add(orderInsuranceCfgVo);
                            } else
                                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, "当前产品收费起始月最大数据为倒置数据,请去单个调整");
                        }
                    }
                }
                if (!batchEditProdExpireMonthDTO.getErrorDescription().isEmpty()) {
                    importDataDto.recordError(batchEditProdExpireMonthDTO.getRowNum(), JSONUtils.toJSONString(batchEditProdExpireMonthDTO.getErrorDescription()));
                }
            }
//            List<String> adjustList = orderNoMap.get(orderNo).stream().map(item -> item.getErrorDescription().keySet()).flatMap(Collection::stream).collect(toList());
            if (CollectionUtils.isNotEmpty(updateList)) {
                Map<Long, Integer> cfgIdAndExpireMonthMap = updateList.stream().collect(toMap(OrderInsuranceCfgVo::getId, OrderInsuranceCfgVo::getExpiredMonth, (a, b) -> b));
                List<OrderInsuranceCfgVo> oldInsurances = getInsurances(orderNo, idInsuranceGroupRatioVoMap);
                String oldContent = JSONArray.fromObject(oldInsurances).toString();
//                if (CollectionUtils.isNotEmpty(updateList))
//                    orderInsuanceCfgMapper.updateExpiredMonthByIds(updateList);
//                List<OrderInsuranceCfgVo> newOldInsurances = getInsurances(orderNo, idInsuranceGroupRatioVoMap);
                List<OrderInsuranceCfgVo> newOldInsurances = oldInsurances.stream().map(item -> {
                    OrderInsuranceCfgVo returnCfgVo = new OrderInsuranceCfgVo();
                    BeanUtils.copyProperties(item, returnCfgVo);
                    if (cfgIdAndExpireMonthMap.containsKey(item.getId()))
                        returnCfgVo.setExpiredMonth(cfgIdAndExpireMonthMap.get(item.getId()));
                    return returnCfgVo;
                }).collect(toList());
                String newContent = JSONArray.fromObject(newOldInsurances).toString();
                /** 供应商实做*/
                addSupplierPracticeForEditExpireMont(loginName, orderNo, oldContent, newContent, orderNoAndContractAreaNoMap.get(orderNo), OrderChangeSyncEnum.BATCH_EDIT_PROD_EXPIRE_MONTH.getCode());
                EmployeeOrder employeeOrder = new EmployeeOrder();
                employeeOrder.setOrderNo(orderNo);
                employeeOrder.setChgState(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode());
                employeeOrderMapper.updateByPrimaryKeySelective(employeeOrder);
                EmployeeOrderChange orderChange = new EmployeeOrderChange().setChgContent(newContent).setOrderNo(orderNo).setCreator(loginName).setUpdater(loginName)
                        .setChgStatus(CHANGE_WAIT_DISPATCH_CONFIRMED.getCode()).setChgMethod(EmployeeOrderChangeEnum.ChgMethod.BATCH_EDIT_PROD_EXPIRE_MONTH.getCode()).setChgType(PERSON_ORDER_CHANGE.getCode());
                mapper.savePersonOrderChange(orderChange);
                // 保存变更日志
                employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, orderNoAndEmpIdMap.get(orderNo), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), remark);
            }
        }
        batchImportDataService.addAndupdateImportData(importDataDto);
    }

    private void checkImportDataForExpireMonth(ImportDataDto<BatchEditProdExpireMonthDTO> importDataDto) {
        List<BatchEditProdExpireMonthDTO> dataList = importDataDto.getDataList();
        for (BatchEditProdExpireMonthDTO batchEditProdExpireMonthDTO : dataList) {
            if (StringUtils.isBlank(batchEditProdExpireMonthDTO.getOrderNo())) {
                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (!batchEditProdExpireMonthDTO.getOrderNo().trim().startsWith("YD-"))
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_8);
                int count = employeeOrderChangeMapper.getNoSuccessCount(batchEditProdExpireMonthDTO.getOrderNo());
                if (count >= 1) {
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                }
            }
            if (StringUtils.isEmpty(batchEditProdExpireMonthDTO.getCustNo())) {
                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            }
            EmployeeOrderVo statusByOrderNo = employeeOrderMapper.getStatusByOrderNo(batchEditProdExpireMonthDTO.getOrderNo(), batchEditProdExpireMonthDTO.getCustNo());
            if (statusByOrderNo == null) {
                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_6);
            } else {
                if (statusByOrderNo.getOrderStatus() == 6 || statusByOrderNo.getOrderStatus() == 9) {
                    if (statusByOrderNo.getChgState() == 2) {
                        batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                    }
                }
            }
            if (StringUtils.isBlank(batchEditProdExpireMonthDTO.getExpireMonth())) {
                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (!(batchEditProdExpireMonthDTO.getExpireMonth() != null && batchEditProdExpireMonthDTO.getExpireMonth().matches("\\d+") && batchEditProdExpireMonthDTO.getExpireMonth().length() == 6)) {
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_1);
                }
            }
            if (StringUtils.isEmpty(batchEditProdExpireMonthDTO.getProductTypeName())) {
                batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (ProductCodeEnum.getCode(batchEditProdExpireMonthDTO.getProductTypeName()) == null)
                    batchEditProdExpireMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_9);
                else
                    batchEditProdExpireMonthDTO.setProductCode(ProductCodeEnum.getCode(batchEditProdExpireMonthDTO.getProductTypeName()));
            }

            // 存在必填项未填写,记录错误信息.
            if (!batchEditProdExpireMonthDTO.getErrorDescription().isEmpty())
                importDataDto.recordError(batchEditProdExpireMonthDTO.getRowNum(), JSONUtils.toJSONString(batchEditProdExpireMonthDTO.getErrorDescription()));
            importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(batchEditProdExpireMonthDTO, importDataDto.getImportNo(), importDataDto.getLoginName()));
        }

    }

    @Override
    public void batchEditBillStartMonth(ImportDataDto<BatchEditBillStartMonthDTO> importDataDto) {
        String remark = "项目客服批量变更账单起始月：";
        String loginName = importDataDto.getLoginName();
        batchImportDataService.addImportData(importDataDto, ImportDataType.BATCH_EDIT_BILL_STARTMONTH.getCode());
        log.info("项目客服批量变更账单起始月,导入编号{}", importDataDto.getImportNo());
        // 校验导入数据必填项是否填写
        checkImportDataForBillStartMonth(importDataDto);
        Map<String, List<BatchEditBillStartMonthDTO>> orderNoMap = importDataDto.getDataList().stream().filter(item -> MapUtils.isEmpty(item.getErrorDescription())).collect(groupingBy(BatchEditBillStartMonthDTO::getOrderNo));
        List<String> orderNoList = Lists.newArrayList(orderNoMap.keySet());
        List<OrderInsuranceCfgVo> allInsuranceCfgList = Lists.newArrayList();
        List<EmployeeOrderVo> employeeIdByOrderNos = Lists.newArrayList();
        Map<Long, InsuranceGroupRatioVo> idInsuranceGroupRatioVoMap = Maps.newHashMap();
        Map<String, String> orderNoAndComMap = Maps.newHashMap();
        Map<String, List<OrderServiceChargeVo>> orderNoAndOSChargeListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            allInsuranceCfgList = orderInsuanceCfgMapper.getListByOrderNoList(orderNoList);
            employeeIdByOrderNos = employeeOrderMapper.findEmployeeIdByOrderNos(orderNoList);
            List<Long> groupRatioIdList = allInsuranceCfgList.stream().map(OrderInsuranceCfgVo::getGroupRatioId).distinct().collect(toList());
            List<InsuranceGroupRatioVo> allRatioAndAmtByGroupIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupRatioIdList))
                allRatioAndAmtByGroupIdList = groupWrapperService.getAllRatioAndAmtByGroupIdList(groupRatioIdList);
            idInsuranceGroupRatioVoMap = allRatioAndAmtByGroupIdList.stream().collect(toMap(InsuranceGroupRatioVo::getId, Function.identity()));
            List<EmployeeOrderVo> commissionerByOrderNoList = employeeOrderMapper.getCommissionerByOrderNoList(orderNoList);
            orderNoAndComMap = commissionerByOrderNoList.stream().collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getCommissioner, (a, b) -> b));
            List<OrderServiceChargeVo> orderServiceChargeVos = orderChargeMapper.getListByOrderNoSet(Sets.newHashSet(orderNoList));
            if (CollectionUtils.isNotEmpty(orderServiceChargeVos)) {
                orderNoAndOSChargeListMap = orderServiceChargeVos.stream().collect(groupingBy(OrderServiceChargeVo::getOrderNo));
            }
        }
        Map<String, Long> orderNoAndEmpIdMap = employeeIdByOrderNos.stream().filter(item -> item.getEmployeeId() != null).collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getEmployeeId, (a, b) -> b));
        Map<String, Set<Integer>> orderNoAndProdCodeMap = allInsuranceCfgList.stream().collect(groupingBy(OrderInsuranceCfgVo::getOrderNo, mapping(OrderInsuranceCfgVo::getProductCode, toSet())));
        Map<String, Map<Integer, List<OrderInsuranceCfgVo>>> orderNoAndProdCodeAndCfgListMap = allInsuranceCfgList.stream().collect(groupingBy(OrderInsuranceCfgVo::getOrderNo, groupingBy(OrderInsuranceCfgVo::getProductCode)));
        Map<String, String> orderNoAndContractAreaNoMap = employeeIdByOrderNos.stream().collect(toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getContractAreaNo, (a, b) -> b));
        for (String orderNo : orderNoList) {
            List<OrderInsuranceCfg> updateList = Lists.newArrayList();
            List<OrderServiceCharge> updateChargeList = Lists.newArrayList();

            if (orderNoAndOSChargeListMap.containsKey(orderNo) && CollectionUtils.isNotEmpty(orderNoAndOSChargeListMap.get(orderNo))) {
                /** 把服务费类型添加进去 */
                Set<Integer> prodCodeSet = orderNoAndProdCodeMap.getOrDefault(orderNo, Sets.newHashSet());
                prodCodeSet.add(ProductCodeEnum.PRODUCT_IND_TYPE_MINUS1.getCode());
                orderNoAndProdCodeMap.put(orderNo, prodCodeSet);
            }

            for (BatchEditBillStartMonthDTO batchEditBillStartMonthDTO : orderNoMap.get(orderNo)) {
                //如果为空说明此订单号下没有对应的产品
                OrderInsuranceCfgVo orderInsuranceCfgVo = new OrderInsuranceCfgVo();
                Integer productCode = batchEditBillStartMonthDTO.getProductCode();
                if (!importDataDto.getLoginName().equals(orderNoAndComMap.get(orderNo)))
                    batchEditBillStartMonthDTO.updateError(DISOBEY_RULE, "您不是当前订单的项目客服,无法变更!");
                else if (!orderNoAndProdCodeMap.getOrDefault(orderNo, Sets.newHashSet()).contains(productCode))
                    batchEditBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_2);
                else {
                    Integer billStartMonth = Integer.valueOf(batchEditBillStartMonthDTO.getBillStartMonth());
                    if (ProductCodeEnum.PRODUCT_IND_TYPE_MINUS1.getCode().equals(productCode)) {
                        /** 服务费单独处理 */
                        List<OrderServiceChargeVo> oscList = orderNoAndOSChargeListMap.getOrDefault(orderNo, Lists.newArrayList());
                        Integer minByCharge = oscList.stream().filter(item -> item.getBillStartMonth() != null).map(OrderServiceChargeVo::getBillStartMonth).min(Integer::compareTo).orElseGet(() -> null);
                        if (minByCharge != null && (billStartMonth < DateUtil.getYearMonthByCount(minByCharge, -3) || billStartMonth > DateUtil.getYearMonthByCount(minByCharge, 3)))
                            batchEditBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_10);
                        else {
                            List<OrderServiceCharge> addAllList = oscList.stream().map(item -> {
                                OrderServiceCharge orderServiceCharge = new OrderServiceCharge();
                                orderServiceCharge.setId(item.getId());
                                orderServiceCharge.setBillStartMonth(billStartMonth);
                                orderServiceCharge.setUpdater(loginName);
                                return orderServiceCharge;
                            }).collect(toList());
                            updateChargeList.addAll(addAllList);
                        }
                    } else {
                        /** 非服务费产品  */
                        List<OrderInsuranceCfgVo> cfgList = orderNoAndProdCodeAndCfgListMap.getOrDefault(orderNo, Maps.newHashMap()).getOrDefault(productCode, Lists.newArrayList());
                        Integer min = cfgList.stream().filter(item -> item.getBillStartMonth() != null).map(OrderInsuranceCfgVo::getBillStartMonth).min(Integer::compareTo).orElseGet(() -> null);
                        if (min != null && (billStartMonth < DateUtil.getYearMonthByCount(min, -3) || billStartMonth > DateUtil.getYearMonthByCount(min, 3)))
                            batchEditBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_10);
                        else {
                            List<OrderInsuranceCfg> addAllList = cfgList.stream().map(item -> {
                                OrderInsuranceCfg orderInsuranceCfg = new OrderInsuranceCfg();
                                orderInsuranceCfg.setId(item.getId());
                                orderInsuranceCfg.setBillStartMonth(billStartMonth);
                                orderInsuranceCfg.setUpdater(loginName);
                                return orderInsuranceCfg;
                            }).collect(toList());
                            updateList.addAll(addAllList);
                        }
                    }
                }
                if (!batchEditBillStartMonthDTO.getErrorDescription().isEmpty()) {
                    importDataDto.recordError(batchEditBillStartMonthDTO.getRowNum(), JSONUtils.toJSONString(batchEditBillStartMonthDTO.getErrorDescription()));
                }
            }

            if (CollectionUtils.isNotEmpty(updateList)) {

                if (CollectionUtils.isNotEmpty(updateList))
                    orderInsuanceCfgMapper.updateBatchById(updateList);
                if (CollectionUtils.isNotEmpty(updateChargeList))
                    orderChargeMapper.batchUpdateById(updateChargeList);

                String newContent = JSONArray.fromObject(getInsurances(orderNo, idInsuranceGroupRatioVoMap)).toString();

                EmployeeOrderChange orderChange = new EmployeeOrderChange();
                orderChange.setChgContent(newContent).setOrderNo(orderNo).setCreator(loginName).setUpdater(loginName)
                        .setChgStatus(CHANGE_SUCCESS.getCode()).setChgMethod(EmployeeOrderChangeEnum.ChgMethod.BILL_MONTH_CHANGE.getCode()).setChgType(PERSON_ORDER_CHANGE.getCode());
                mapper.savePersonOrderChange(orderChange);
                // 保存变更日志
                employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, orderNoAndEmpIdMap.get(orderNo), PersonOrderEnum.OrderLogOprType.MODIFY.getCode(), remark);
            }
        }
        batchImportDataService.addAndupdateImportData(importDataDto);
    }


    private void checkImportDataForBillStartMonth(ImportDataDto<BatchEditBillStartMonthDTO> importDataDto) {
        List<BatchEditBillStartMonthDTO> dataList = importDataDto.getDataList();
        for (BatchEditBillStartMonthDTO batchBillStartMonthDTO : dataList) {
            if (StringUtils.isBlank(batchBillStartMonthDTO.getOrderNo())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (!batchBillStartMonthDTO.getOrderNo().trim().startsWith("YD-"))
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_8);
                int count = employeeOrderChangeMapper.getNoSuccessCount(batchBillStartMonthDTO.getOrderNo());
                if (count >= 1) {
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                }
            }
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getCustNo())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            }
            EmployeeOrderVo statusByOrderNo = employeeOrderMapper.getStatusByOrderNo(batchBillStartMonthDTO.getOrderNo(), batchBillStartMonthDTO.getCustNo());
            if (statusByOrderNo == null) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_6);
            } else {
                if (statusByOrderNo.getOrderStatus() == 6 || statusByOrderNo.getOrderStatus() == 9) {
                    if (statusByOrderNo.getChgState() == 2) {
                        batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                    }
                }
            }
            if (StringUtils.isBlank(batchBillStartMonthDTO.getBillStartMonth())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (!(batchBillStartMonthDTO.getBillStartMonth() != null && batchBillStartMonthDTO.getBillStartMonth().matches("\\d+") && batchBillStartMonthDTO.getBillStartMonth().length() == 6)) {
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_1);
                }
            }
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getProductTypeName())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (ProductCodeEnum.getCode(batchBillStartMonthDTO.getProductTypeName()) == null)
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_9);
                else
                    batchBillStartMonthDTO.setProductCode(ProductCodeEnum.getCode(batchBillStartMonthDTO.getProductTypeName()));
            }
            // 存在必填项未填写,记录错误信息.
            if (!batchBillStartMonthDTO.getErrorDescription().isEmpty()) {
                importDataDto.recordError(batchBillStartMonthDTO.getRowNum(), JSONUtils.toJSONString(batchBillStartMonthDTO.getErrorDescription()));
            }
            importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(batchBillStartMonthDTO, importDataDto.getImportNo(), importDataDto.getLoginName()));
        }

    }

    @Override
    public EmployeeOrderChangeVo selectEmployeeOrderChange(String orderNo, List<Integer> chgMethod, Integer chgStatus, Integer chgType) {
        EmployeeOrderChange employeeOrderChange = mapper.selectEmpOrderChangeByRcsAndOnceChg(orderNo, chgMethod, chgStatus, chgType);
        if (Objects.nonNull(employeeOrderChange)) {
            EmployeeOrderChangeVo vo = new EmployeeOrderChangeVo();
            BeanUtils.copyProperties(employeeOrderChange, vo);
            return vo;
        }
        return null;
    }

    @Override
    public void handleRollBackDataByChangeIds(List<Long> changeIds) {
        List<EmployeeOrderChangeVo> employeeOrderChangeVos = mapper.findByIds(changeIds, NON_PROVIDENT_FUND_CHANGE.getCode(), CHANGE_SUCCESS.getCode(), PERSON_ORDER_CHANGE.getCode());
        List<String> orderNos = employeeOrderChangeVos.stream().map(EmployeeOrderChangeVo::getOrderNo).collect(Collectors.toList());
        List<OrderServiceCharge> allCharges = Lists.newArrayList();
        for (EmployeeOrderChangeVo changeVo : employeeOrderChangeVos) {
            String chgContent = changeVo.getChgContent();
            String orderNo = changeVo.getOrderNo();
            NonProdFundChangeContext changeContext = JsonUtil.jsonToBean(chgContent, NonProdFundChangeContext.class);
            String changeDataBefore = changeContext.getChangeDataBefore();
            if (StringUtils.isNotBlank(changeDataBefore)) {
                List<OrderServiceChargeVo> chargeVoList = JsonUtil.jsonToList(changeDataBefore, OrderServiceChargeVo.class);
                List<OrderServiceCharge> charges = chargeVoList.stream().map(vo -> {
                    OrderServiceCharge charge = new OrderServiceCharge();
                    BeanUtils.copyProperties(vo, charge);
                    return charge;
                }).collect(Collectors.toList());
                if (checkMonth(orderNo, charges)) {
                    log.info("===================订单号,{}变更内容中数据有误====================", orderNo);
                    return;
                }
                allCharges.addAll(charges);
            } else {
                log.info("===================订单号,{}变更内容中没有数据====================", orderNo);
                orderNos.remove(orderNo);
            }
        }

        if (CollectionUtils.isNotEmpty(orderNos)) {
            chargeMapper.deleteChargesByOrderNos(orderNos);
        }

        if (CollectionUtils.isNotEmpty(allCharges)) {
            chargeMapper.insertOrderServiceCharges(allCharges);
        }

    }


    private void checkImportDataMustWriteField(ImportDataDto<BatchBillStartMonthDTO> importDataDto) {
        List<BatchBillStartMonthDTO> dataList = importDataDto.getDataList();
        for (BatchBillStartMonthDTO batchBillStartMonthDTO : dataList) {
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getOrderNo())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                String receivingManNameByOrderNo = employeeOrderMapper.getReceivingManNameByOrderNo(batchBillStartMonthDTO.getOrderNo());
                if (!importDataDto.getLoginName().equals(receivingManNameByOrderNo)) {
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_3);
                }
                int count = employeeOrderChangeMapper.getCountByOrderNo(batchBillStartMonthDTO.getOrderNo());
                if (count >= 1) {
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                }

            }
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getCustNo())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            }
            EmployeeOrderVo statusByOrderNo = employeeOrderMapper.getStatusByOrderNo(batchBillStartMonthDTO.getOrderNo(), batchBillStartMonthDTO.getCustNo());
            if (statusByOrderNo == null) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_6);
            } else {
                if (statusByOrderNo.getOrderStatus() == 6 || statusByOrderNo.getOrderStatus() == 9) {
                    if (statusByOrderNo.getChgState() == 2) {
                        batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_5);
                    }

                }
            }
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getRevStartMonth()) || StringUtils.isEmpty(batchBillStartMonthDTO.getOldRevStartMonth())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            } else {
                if (batchBillStartMonthDTO.getRevStartMonth().contains("-") || batchBillStartMonthDTO.getRevStartMonth().contains("/")) {
                    batchBillStartMonthDTO.updateError(DISOBEY_RULE, ERROR_MESSAGE_1);
                }
            }
            if (StringUtils.isEmpty(batchBillStartMonthDTO.getProductName())) {
                batchBillStartMonthDTO.updateError(DISOBEY_RULE, REQUIRED_FIELDS_NOT_FILLED);
            }
            // 存在必填项未填写,记录错误信息.
            if (!batchBillStartMonthDTO.getErrorDescription().isEmpty()) {
                importDataDto.recordError(batchBillStartMonthDTO.getRowNum(), JSONUtils.toJSONString(batchBillStartMonthDTO.getErrorDescription()));
            }
            importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(batchBillStartMonthDTO, importDataDto.getImportNo(), importDataDto.getLoginName()));
        }

    }

    private ImmutablePair<Map<String, List<OrderServiceCharge>>, Map<String, String>> searchOrderServiceChargeMap(List<String> orderNoList) {
        List<OrderServiceCharge> orderServiceChargeVos = orderChargeMapper.searchOrderChangeDetailByOrderNos(orderNoList);
        Map<String, String> orderNoAndTempMap = orderServiceChargeVos.stream().collect(toMap(OrderServiceCharge::getOrderNo, vo -> vo.getTempletId() + "_" + vo.getRevTempId(), (a, b) -> a));
        HashMap<String, List<OrderServiceCharge>> orderServiceChargeMap = orderServiceChargeVos.stream().collect(
                groupingBy(
                        OrderServiceCharge::getOrderNo, HashMap::new, Collectors.collectingAndThen(
                                Collectors.toList(),
                                vos -> vos.stream().sorted(Comparator.comparing(OrderServiceCharge::getRevStartMonth)).collect(Collectors.toList())
                        )
                )
        );
        return new ImmutablePair<>(orderServiceChargeMap, orderNoAndTempMap);

    }


    public void addSupplierPractice(String loginName, String orderNo, String oldContent, String newContent, String contractAreaNo) {
        OrderChangeTaskDetailVo orderChangeTaskDetailVo = new OrderChangeTaskDetailVo();
        OrderChangeTaskInfoVo orderChangeTaskInfoVo = new OrderChangeTaskInfoVo();
        Integer recceivingTypeByContractAreaNo = null;
        if (StringUtils.isBlank(contractAreaNo)) {
            orderChangeTaskInfoVo.setChangeType(OrderChangeSyncEnum.BATCH_CHANGE_TOLL_MONTH.getCode());
            recceivingTypeByContractAreaNo = employeeOrderMapper.getReceivingTypeByOrderNo(orderNo);
        } else {
            orderChangeTaskInfoVo.setChangeType(OrderChangeSyncEnum.RECEIVING_ORDER_CHANGE.getCode());
            recceivingTypeByContractAreaNo = contractAreaMapper.getRecceivingTypeByContractAreaNo(contractAreaNo);
        }
        orderChangeTaskInfoVo
                .setCreator(loginName)
                .setOrderNo(orderNo)
                .setRecceivingType(recceivingTypeByContractAreaNo)
                .setTaskStatus(SyncEnum.TO_BE_SYNCHRONIZED.getCode());
        orderChangeTaskInfoMapper.insertOrderChangeTaskInfoVo(orderChangeTaskInfoVo);
        orderChangeTaskDetailVo.setCreator(loginName)
                .setBeforeData(oldContent)
                .setAfterData(newContent)
                .setTaskInfoId(orderChangeTaskInfoVo.getId());
        orderChangeTaskDetailMapper.insertOrderChangeTaskDetailVo(orderChangeTaskDetailVo);
    }

    public void addSupplierPracticeForEditExpireMont(String loginName, String orderNo, String oldContent, String newContent, String contractAreaNo, Integer changeType) {
        OrderChangeTaskDetailVo orderChangeTaskDetailVo = new OrderChangeTaskDetailVo();
        OrderChangeTaskInfoVo orderChangeTaskInfoVo = new OrderChangeTaskInfoVo();
        Integer recceivingTypeByContractAreaNo = null;
        orderChangeTaskInfoVo.setChangeType(changeType);
        recceivingTypeByContractAreaNo = employeeOrderMapper.getReceivingTypeByOrderNo(orderNo);
        orderChangeTaskInfoVo
                .setCreator(loginName)
                .setOrderNo(orderNo)
                .setRecceivingType(recceivingTypeByContractAreaNo)
                .setTaskStatus(SyncEnum.TO_BE_SYNCHRONIZED.getCode());
        orderChangeTaskInfoMapper.insertOrderChangeTaskInfoVo(orderChangeTaskInfoVo);
        orderChangeTaskDetailVo.setCreator(loginName)
                .setBeforeData(oldContent)
                .setAfterData(newContent)
                .setTaskInfoId(orderChangeTaskInfoVo.getId());
        orderChangeTaskDetailMapper.insertOrderChangeTaskDetailVo(orderChangeTaskDetailVo);
    }

    private boolean checkOrderServiceChargeData(List<OrderServiceCharge> insertCharge, Map<String, String> orderNoAndTempMap, Map<String, OrderServiceCharge> quotationAmountMap) {
        Map<String, List<OrderServiceCharge>> orderServiceChargeMap = insertCharge.stream().collect(groupingBy(OrderServiceCharge::getOrderNo));

        EmployeeOrderVo orderVo = employeeOrderService.getOneById(insertCharge.get(0).getOrderNo());
        List<String> quotationByContractNo = quotationService.getQuotationNoByContractNo(orderVo.getContractNo());
        for (String orderNo : orderServiceChargeMap.keySet()) {
            List<OrderServiceCharge> charges = orderServiceChargeMap.get(orderNo);
            List<Long> tempId = charges.stream().map(OrderServiceCharge::getTempletId).distinct().collect(Collectors.toList());
            List<Long> revId = charges.stream().map(OrderServiceCharge::getRevTempId).distinct().collect(Collectors.toList());
            if (tempId.size() > 1 || revId.size() > 1) {
                log.info("========================变更服务费账单模板或者收费频率出错，订单号,{}！！========================", orderNo);
                return false;
            }

            String[] tempArr = orderNoAndTempMap.get(orderNo).split("_");
            if (tempId.get(0) != Long.parseLong(tempArr[0]) || revId.get(0) != Long.parseLong(tempArr[1])) {
                log.info("========================变更服务费账单模板或者收费频率出错，订单号,{}！！========================", orderNo);
                return false;
            }

            for (OrderServiceCharge charge : charges) {
                /**根据报价单算出来的费用，与需要插入的数据进行比对*/
                OrderServiceCharge quotation = quotationAmountMap.get(charge.getQuotationNo());
                if (quotation.getAmount().compareTo(charge.getAmount()) != 0 && charge.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                    log.info("========================变更服务费含税金额不对，订单号,{}！！========================", orderNo);
                    return false;
                }
                if (quotation.getTaxfreeAmt().compareTo(charge.getTaxfreeAmt()) != 0 && charge.getTaxfreeAmt().compareTo(BigDecimal.ZERO) != 0) {
                    log.info("========================变更服务费不含税金额不对，订单号,{}！！========================", orderNo);
                    return false;
                }
                if (quotation.getValTax().compareTo(charge.getValTax()) != 0 && charge.getValTax().compareTo(BigDecimal.ZERO) != 0) {
                    log.info("========================变更服务费增值税不对，订单号,{}！！========================", orderNo);
                    return false;
                }
                if (quotation.getValTaxRate().compareTo(charge.getValTaxRate()) != 0) {
                    log.info("========================变更服务费税率不对，订单号,{}！！========================", orderNo);
                    return false;
                }
                if (!quotationByContractNo.contains(charge.getQuotationNo())) {
                    log.info("========================变更服务报价单有误，订单号,{}，报价单号,{}！！========================", orderNo, charge.getQuotationNo());
                    return false;
                }
            }

            if (checkMonth(orderNo, charges)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkMonth(String orderNo, List<OrderServiceCharge> charges) {
        List<Integer> startMonthList = charges.stream().map(OrderServiceCharge::getRevStartMonth).distinct().collect(Collectors.toList());
        if (startMonthList.size() != charges.size()) {
            log.info("========================变更服务费出现开始月相同的数据，订单号,{}！！========================", orderNo);
            return true;
        }
        List<OrderServiceCharge> collect = charges.stream().filter(item -> Objects.isNull(item.getRevEndMonth()) ||
                        (Objects.nonNull(item.getRevEndMonth()) && item.getRevEndMonth() >= item.getRevStartMonth())).sorted(Comparator.comparing(OrderServiceCharge::getRevStartMonth))
                .collect(Collectors.toList());
        int preStartMonth = 0, preEndMonth = 0;
        for (int i = 0; i < collect.size(); i++) {
            OrderServiceCharge chargeVo = collect.get(i);
            if (i == 0) {
                preStartMonth = chargeVo.getRevStartMonth();
                preEndMonth = Optional.ofNullable(chargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE);
                continue;
            }
            /**开始月截至月 都要比上一条大*/
            if (hasCoverRange(preStartMonth, preEndMonth, chargeVo)) {
                log.info("=====================变更服务费的数据出现重复区间======================");
                return true;
            }
            preStartMonth = chargeVo.getRevStartMonth();
            preEndMonth = Optional.ofNullable(chargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE);
        }
        return false;
    }

    private boolean hasCoverRange(int preStartMonth, int preEndMonth, OrderServiceCharge chargeVo) {
        return chargeVo.getRevStartMonth() <= preStartMonth || Optional.ofNullable(chargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE) <= preStartMonth
                || chargeVo.getRevStartMonth() <= preEndMonth || Optional.ofNullable(chargeVo.getRevEndMonth()).orElse(Integer.MAX_VALUE) <= preEndMonth;
    }
}
