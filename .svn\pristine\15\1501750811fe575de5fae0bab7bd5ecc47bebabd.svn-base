package com.reon.hr.api.bill.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.vo.ProdAmtVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class PerInsuranceBillItemVo implements Serializable {
    private static final long serialVersionUID = -7505031790787613661L;

    private Long id;

    private Long perBillId;

    private String insuranceRatioCode;

    private String ratioName;

    private Integer productCode;

    private String cityName;

    private Integer cityCode;

    private Integer revStartMonth;// 收费起始月

    private Integer expiredMonth;// 收费截止月

    private Integer billStartMonth;// 账单起始月

    private Integer returnMonth;// 退费账单月

    private Integer lastMonth;// 支付最后服务年月


    private BigDecimal total;// 总额

    private BigDecimal indAmt;// 个人金额

    private BigDecimal comAmt;// 企业金额

    private BigDecimal indBase;// 个人基数

    private BigDecimal comBase;// 企业基数



    private Integer indExactVal;// 个人精确值

    private Integer comExactVal;// 企业精确值

    private Integer indCalcMode;// 个人计算方式

    private Integer comCalcMode;// 企业计算方式


    private BigDecimal indRatio;

    private BigDecimal comRatio;

    private BigDecimal indAdd;

    private BigDecimal comAdd;

    private BigDecimal highBaseInd;

    private BigDecimal lowBaseInd;

    private BigDecimal lowBaseCom;

    private BigDecimal highBaseCom;

    private String creator;

    private String updater;

    /**
     * 账单模板
     */
    private Long templetId;

    private String viewItems;

    /**
     * 收费模板
     */
    private Long revTempId;

    private Integer beforeMonths;// 提前几月收

    private Integer collectFreq;// 收费频率

    private Integer receiveMonthType;// 收费月类型

    private List<Integer> serviceMonths= Lists.newArrayList();// 当前社保项对应的服务年月

    private boolean isFirstBill;// 是否为首版账单
    private Integer billType;//个人账单类型

    private Long employeeId;
    private String orderNo;
    private Integer lastBillMonth;
    private Integer maxReceivableMonth;
    private Integer billMonth;
    private Integer receivableMonth;
    private boolean havelastBillMonth = false;

    private boolean normalFlag; //正常账单（年缴产品）

    private Integer chargeFreq;// 收费频率
    //一年内最小服务月
    private Integer minReceivableMonth;

    private Integer period; //时期

    private BigDecimal comMonthlyFee;//公司月缴金额

    private BigDecimal indMonthlyFee;//个人月缴金额
    private Boolean isYearCharge = false;

    private Short payMonth; //年缴月份
    //存储年缴产品费用信息
    private Map<Integer,ProdAmtVo> yearProdMap= Maps.newHashMap();


}
