package com.reon.hr.sp.change.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.change.dubbo.service.rpc.change.ICollectJobWrapperService;
import com.reon.hr.api.change.enums.CollectLockStatusEnum;
import com.reon.hr.api.change.enums.ExcelOprTypeEnum;
import com.reon.hr.api.change.vo.CollectEmpVo;
import com.reon.hr.api.change.vo.CollectJobVo;
import com.reon.hr.api.change.vo.CollectVo;
import com.reon.hr.api.change.vo.declare.*;
import com.reon.hr.sp.change.entity.change.CollectJob;
import com.reon.hr.sp.change.service.change.ICollectEmpService;
import com.reon.hr.sp.change.service.change.ICollectJobService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("collectJobDubboService")
public class CollectJobWrapperServiceImpl implements ICollectJobWrapperService {


    @Autowired
    private ICollectJobService collectJobService;
    @Autowired
    private ICollectEmpService collectEmpService;

    @Override
    public CollectJobVo selectByJobNo(String jobNo) {
        CollectJob collectJob = collectJobService.selectByJobNo(jobNo);
        CollectJobVo collectJobVo = new CollectJobVo();
        if (collectJob != null) {
            BeanUtils.copyProperties(collectJob, collectJobVo);
        }
        return collectJobVo;
    }

    @Override
    public void terminate(Long id, String termiReason, String updater) {
        collectJobService.updateJobStatus(id, termiReason, updater);
    }

    @Override
    public Page<DeclareEmpVo> getByJobNo(String jobNo, String keyword, Integer page, Integer size, String loginName, CollectVo collectVo) {
        return collectEmpService.getByJobNo(jobNo, keyword, page, size, loginName,collectVo);
    }

    @Override
    public void lock(String jobNo, String updater) {
        collectJobService.updateLockStatus(jobNo, CollectLockStatusEnum.LOCKED.getCode(), updater);
    }

    @Override
    public void unlock(String jobNo, String updater) {
        collectJobService.updateLockStatus(jobNo, CollectLockStatusEnum.UNLOCK.getCode(), updater);
    }

    @Override
    public void declareBatchInnerUpdate(List<DeclareEmpVo> empList, String updater,String type) {
        collectEmpService.save(empList, null, updater, ExcelOprTypeEnum.DECLARE_IMPORT,type);
    }

    @Override
    public void adjustBatchInnerUpdate(List<DeclareEmpVo> empList, String uploadFileId, String updater) {
        collectEmpService.save(empList, uploadFileId, updater, ExcelOprTypeEnum.ADJUST_IMPORT,null);
    }

    @Override
    public void saveCollect(List<DeclareEmpVo> empList, String loginName) {
        collectEmpService.save(empList, null, loginName, ExcelOprTypeEnum.DECLARE_COLLECT,null);
    }
}
