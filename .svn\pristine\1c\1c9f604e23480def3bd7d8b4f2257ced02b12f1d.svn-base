package com.reon.hr.sp.bill.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.enums.PaymentApplyProcessStatus;
import com.reon.hr.api.bill.enums.PracticeLockInfoFeeTypeEnum;
import com.reon.hr.api.bill.enums.PracticePayDetailAmtType;
import com.reon.hr.api.bill.enums.PracticePayMentTypeEnum;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.ContractVo;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.report.enums.BillReportEnum;
import com.reon.hr.api.thirdpart.dubbo.service.rpc.bankcorp.cmb.ICMBpayWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ManualAction;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.common.cmb.*;
import com.reon.hr.common.constant.CMBPayConstant;
import com.reon.hr.common.enums.SalaryPaymentWorkflowEnum;
import com.reon.hr.common.utils.SensitiveReplaceUtil;
import com.reon.hr.sp.bill.dao.bill.InsurancePracticeCustPayDetailMapper;
import com.reon.hr.sp.bill.dao.bill.InsurancePracticeDisComPayMapper;
import com.reon.hr.sp.bill.service.bill.InsurancePracticeDisComPayService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IInsurancePracticeBillService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IPracticePayDetailService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.InsurancePracticeOneFeeService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月04日
 * @Version 1.0
 */
@Slf4j
@Service
public class InsurancePracticeDisComPayServiceImpl implements InsurancePracticeDisComPayService {


        public static final String ALREADY_PAID = "该数据已完成支付，无法重复操作。";
        public static final String EXCEED_LAST_DATE = "支付失败：支付日期应为最晚支付日期当日并且为下午4点前";
        public static final String MISSING_PAY_BANK = "支付失败：未找到对应支付地的招商银行账户，请检查系统信息。";
        public static final String MISSING_RECEIVING_BANK = "支付失败：未找到福利办理方的银行账户信息。";
        public static final String INCOMPLETE_SOCIAL_BANK_INFO = "支付失败：福利办理方的社保银行账户信息不完整，请核实后重试。";
        public static final String TRANSFER_REQUEST_FAILED = "支付失败：支付ID %s 转账请求未成功发送。";
        public static final String TRANSFER_RESULT_ERROR = "支付失败：支付ID %s 返回错误信息：%s";
        public static final String TRANSFER_FAILED_WITH_REASON = "支付失败：支付ID %s，错误信息：%s";
        private static final String MSG_PARTIAL_SUCCESS = "成功 %d 条，失败 %d 条，失败原因：%s";
        private static final String MSG_ACCOUNT_NOT_FOUND = "支付失败：支付ID %s 对应账户信息未找到。";
        private static final String MSG_BALANCE_NOT_ENOUGH = "支付失败：支付ID %s 对应账户余额不足。";






    @Resource
    private InsurancePracticeDisComPayMapper insurancePracticeDisComPayMapper;

    @Resource
    private IPaymentApplyService paymentApplyService;

    @Resource
    private ICMBpayWrapperService icmBpayWrapperService;

    @Resource
    private ISequenceService iSequenceService;



    @Resource
    private IWorkflowWrapperService workflowWrapperService;

    @Resource
    private ISystemConfigWrapperService iSystemConfigWrapperService;

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Resource
    private IPracticePayDetailService practicePayDetailService;

    @Resource
    private IEmployeeOrderWrapperService employeeOrderWrapperService;

    @Resource
    private ICustomerWrapperService customerWrapperService;

    @Resource
    private IInsurancePracticeServiceConfigWrapperService insurancePracticeServiceConfigWrapperService;

    @Resource
    private InsurancePracticePayBankConfigWrapperService insurancePracticePayBankConfigWrapperService;


    @Resource
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource
    private IUserWrapperService userWrapperService;

    @Resource
    private InsurancePracticeOneFeeService insurancePracticeOneFeeService;

    @Resource
    private InsurancePracticeCustPayDetailMapper insurancePracticeCustPayDetailMapper;


    @Override
    public void deleteInsurancePracticeDisComPayByPayId(Long payId) {
        insurancePracticeDisComPayMapper.deleteInsurancePracticeDisComPayByPayId(payId);
        insurancePracticeCustPayDetailMapper.deleteInsurancePracticeCustPayDetailByPayId(payId);
    }

    @Override
    public int saveInsurancePracticeDisComPayBatch(List<InsurancePracticeDisComPayVo> insurancePracticeDisComPayVos) {
        return insurancePracticeDisComPayMapper.saveInsurancePracticeDisComPayBatch(insurancePracticeDisComPayVos);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> getInsurancePracticeDisComListByPayId(Long payId) {
        return insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(payId);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApprovalListByDisCom(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeDisComPayMapper.getPaymentApprovalListByDisCom(paymentApplyVo);
    }

    @Override
    public Page<PaymentApplyVo> getPrintApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        Page<PaymentApplyVo> paymentApplyVoPage = new Page<>(paymentApplyVo.getPage(), paymentApplyVo.getLimit());
        List<PaymentApplyVo> printApplicationFromPage = insurancePracticeDisComPayMapper.getPrintApplicationFromPage(paymentApplyVoPage,paymentApplyVo);
        if (CollectionUtils.isEmpty(printApplicationFromPage)){
            return paymentApplyVoPage;
        }
        Set<Long> payIdSet = printApplicationFromPage.stream().map(PaymentApplyVo::getId).collect(Collectors.toSet());
        List<InsurancePracticeOneFeeVo> insurancePracticeOneFeeListByPayIdSet = insurancePracticeOneFeeService.getInsurancePracticeOneFeeDisComListByPayIdSet(payIdSet);
        Map<Long, List<String>> result = insurancePracticeOneFeeListByPayIdSet.stream()
                .collect(Collectors.groupingBy(
                        InsurancePracticeOneFeeVo::getPaymentId,
                        Collectors.mapping(
                                InsurancePracticeOneFeeVo::getDisCom,
                                Collectors.toList()
                        )
                ));
        Map<String, InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigMap = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigMap();
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        printApplicationFromPage.forEach(item ->{
            LocalDateTime dateTime = LocalDateTime.parse(item.getLastDate(), formatter);
            LocalDateTime payTime = dateTime.with(LocalTime.of(16, 0));
            item.setLastDate(payTime.format(formatter));
            BigDecimal payAmt = item.getPayAmt().add(item.getServiceAmt());
            item.setBalanceAmt(payAmt.subtract(item.getActPayAmt()));
            InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = insurancePracticePayBankConfigMap.get(item.getDisCom());
            if (insurancePracticePayBankConfigVo != null){
                item.setPayBankNo(insurancePracticePayBankConfigVo.getDispatchBankNo());
                item.setBankType(insurancePracticePayBankConfigVo.getDispatchBankType());
                item.setPayBankName(insurancePracticePayBankConfigVo.getDispatchBankName());
            }
            item.setOneFeeFlag(BooleanTypeEnum.NO.getCode());
            List<String> disComList = result.get(item.getId());
            if (disComList!=null&&disComList.contains(item.getDisCom())){
                item.setOneFeeFlag(BooleanTypeEnum.YES.getCode());
            }
            item.setDisComName(orgCodeAndNameMap.get(item.getDisCom()));
            item.setPayCom(orgCodeAndNameMap.get(item.getPayCom()));
        });
        paymentApplyVoPage.setRecords(printApplicationFromPage);
        return paymentApplyVoPage;
    }

    @Override
    public String initiatePaymentBuyCmb(List<TaskVo> taskVos, String loginName) {

        int successCount = 0;
        int failCount = 0;
        StringBuilder result = new StringBuilder();
        for (TaskVo taskVo : taskVos) {
            failCount++;
            InsurancePracticeDisComPayVo detailById = insurancePracticeDisComPayMapper.getDetailById(taskVo.getPaymentApplyComId());
            if (detailById.getPayFlag().equals(BooleanTypeEnum.YES.getCode())){
                result.append(ALREADY_PAID).append(";");
                continue;
            }
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(detailById.getPayId());
            String dateStr = paymentApplyVo.getLastDate();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime dateTime = LocalDateTime.parse(dateStr, formatter);
            // 获取截止时间为当天的16:00
            LocalDate dateOnly = dateTime.toLocalDate();
            LocalDateTime updatedTime = LocalDateTime.of(dateOnly, LocalTime.of(16, 0));

            LocalDateTime now = LocalDateTime.now();
//
//            if (!now.toLocalDate().isEqual(dateOnly) || now.isAfter(updatedTime)) {
//                result.append(EXCEED_LAST_DATE).append(";");
//                continue;
//            }


            InsurancePracticePayBankConfigVo payBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(detailById.getDisCom());
            if (Objects.isNull(payBankVo)){
                log.info("支付地银行账户信息:{}",payBankVo);
                result.append(MISSING_PAY_BANK).append(";");
                continue;
            }
            BigDecimal payAmt = detailById.getActPayAmt();
            boolean checkBalanceFlag = Boolean.parseBoolean(iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.CHECK_BALANCE_FLAG).getCfgValue());
            if (checkBalanceFlag){
                String resMsg = checkAndSetBalance(payBankVo.getDispatchBankNo(), payAmt, paymentApplyVo.getId());
                if (resMsg!=null){
                    result.append(resMsg).append(";");
                    continue;
                }
            }

            InsurancePracticePayBankConfigVo inBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
            if (Objects.isNull(inBankVo)){
                result.append(MISSING_RECEIVING_BANK).append(";");
                continue;
            }
            if (StringUtils.isBlank(inBankVo.getReceivingBankName())||StringUtils.isBlank(inBankVo.getReceivingBankNo())){
                result.append(INCOMPLETE_SOCIAL_BANK_INFO).append(";");
                continue;
            }
            if (payAmt.compareTo(BigDecimal.ZERO)==0){
                failCount = failCount-1;
                successCount++;
                Map<String, Object> variables = new HashMap<>();
                variables.put("loginName", loginName);
                workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
                insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
                continue;
            }
            //业务参考号
            YurrefArgs yurrefArgs = new YurrefArgs(Boolean.FALSE);
            TransAcctInfo transAcctInfo = new TransAcctInfo();
            transAcctInfo.setDbtAcc(payBankVo.getDispatchBankNo());
            transAcctInfo.setCrtAcc(inBankVo.getReceivingBankNo());
            transAcctInfo.setCrtNam(inBankVo.getReceivingBankName());
            transAcctInfo.setCrtBnk(inBankVo.getReceivingBankName());
            transAcctInfo.setCrtAdr("暂无");
            transAcctInfo.setYurRef(detailById.getYurref());
            transAcctInfo.setTrsAmt(payAmt.toEngineeringString());
            ResponseMsg<PaySingleResponseInfo> responseMsg = icmBpayWrapperService.corpSinglePay(transAcctInfo, yurrefArgs);
            PaySingleResponseInfo paySingleResponseInfo = responseMsg.getResponseData();
            if(paySingleResponseInfo==null){
                result.append(String.format(TRANSFER_REQUEST_FAILED,paymentApplyVo.getId())).append(";");
            }else {
                RespHead head = paySingleResponseInfo.getHead();
                if(Objects.equals(head.getResultcode(), CMBPayConstant.SUCCESS)){
                    List<PaySingleResponseInfo.PaySingleResult> resultList = paySingleResponseInfo.getBb1payopz1();
                    String errTxt = resultList.stream().map(PaySingleResponseInfo.PaySingleResult::getErrTxt).filter(Objects::nonNull).collect(Collectors.joining());
                    if(StringUtils.isNotBlank(errTxt)){
                        result.append(String.format(String.format(TRANSFER_RESULT_ERROR, paymentApplyVo.getId(), errTxt))).append(";");
                    }else {
                        failCount = failCount-1;
                        successCount++;
                        Map<String, Object> variables = new HashMap<>();
                        variables.put("loginName", loginName);
                        workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
                        insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
                    }
                }else {
                    result.append(String.format(TRANSFER_FAILED_WITH_REASON, paymentApplyVo.getId(), head.getResultmsg().split("-")[1])).append(";");
                }
            }
        }
        return String.format(MSG_PARTIAL_SUCCESS, successCount, failCount, result);

    }

    /**
     * 校验余额
     * @param bankNo
     * @param payAmt
     * @param paymentId
     * @return {@link String }
     */
    public String checkAndSetBalance(String bankNo,BigDecimal payAmt,Long paymentId){
        String queryBusinessNo = iSequenceService.getCMBQueryBusinessNo();
        List<QueryAccInfo> queryAccInfos=new ArrayList<>();
        QueryAccInfo queryAccInfo=new QueryAccInfo();
        queryAccInfo.setAccnbr(bankNo);
        queryAccInfos.add(queryAccInfo);
        ResponseMsg<QueryAcctInfoResInfo> responseMsg = icmBpayWrapperService.queryAcctInfoList(queryAccInfos, queryBusinessNo);
        QueryAcctInfoResInfo queryAcctInfoResInfo = responseMsg.getResponseData();
        List<AcctInfo> ntqacinfz = queryAcctInfoResInfo.getNtqacinfz().stream().distinct().collect(Collectors.toList());
        Map<String, AcctInfo> ntqacinMap = ntqacinfz.stream().collect(toMap(AcctInfo::getAccnbr, Function.identity()));
            AcctInfo acctInfo = ntqacinMap.get(bankNo);
            if (acctInfo==null){
                return String.format(MSG_ACCOUNT_NOT_FOUND, paymentId);
            }
            BigDecimal avlblv = new BigDecimal(acctInfo.getAvlblv());
            BigDecimal balance = avlblv.subtract(payAmt);
            if(balance.compareTo(BigDecimal.ZERO)<0){
                return String.format(MSG_BALANCE_NOT_ENOUGH, paymentId);
            }
       return null;
    }


    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printApplicationFrom(List<PrintPaymentFromExportVo> vos) {
        Map<String, List<PrintPaymentFromExportVo>> resultPrintMap = new HashMap<>();

        Map<String, BigDecimal> serviceConfigMap = getServiceMap();
        Map<Long, String> custIdAndNameMap = getCustomerMap();
        int i =1;
        for (PrintPaymentFromExportVo vo : vos) {
            InsurancePracticeDisComPayVo detailById = insurancePracticeDisComPayMapper.getDetailById(vo.getPaymentApplyComId());
            List<PracticePayDetailVo> practicePayDetailVos = practicePayDetailService.selectPracticePayDetailListByPayId(detailById.getPayId());
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(detailById.getPayId());
            Map<String, String> userNameMap = userWrapperService.getAllUserMap();
            String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant()));
            String applicantName = userNameMap.get(paymentApplyVo.getApplicant());

            InsurancePracticePayBankConfigVo companyBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
            List<EmployeeOrderVo> employeeOrderVos = selectCustByOrderNoList(practicePayDetailVos);

            employeeOrderVos = employeeOrderVos.stream().filter(v -> v.getSignDistCom().equals(detailById.getDisCom())).collect(Collectors.toList());
            List<String> orderNoList = employeeOrderVos.stream().map(EmployeeOrderVo::getOrderNo).collect(Collectors.toList());
            practicePayDetailVos = practicePayDetailVos.stream().filter(v -> orderNoList.contains(v.getOrderNo())).collect(Collectors.toList());

            setSignTitleToPracticePayDetail(practicePayDetailVos, employeeOrderVos);

            Map<Long, List<PracticePayDetailVo>> custIdMap = practicePayDetailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getCustId));
            Set<String> serviceOrderSet = getServiceOrderSet(practicePayDetailVos);
            BigDecimal service = serviceConfigMap.getOrDefault(detailById.getDisCom(), BigDecimal.ZERO);
            List<PrintPaymentFromExportVo> printPaymentFromExportVos = new ArrayList<>();
            List<InsurancePracticeOneFeeBalanceVo> balanceVos = insurancePracticeOneFeeService.getDeductionAmtSumByCustIdSetAndPayId(paymentApplyVo.getPayCom(), detailById.getDisCom(), custIdMap.keySet(), paymentApplyVo.getId());
            Map<Long, BigDecimal> csutIdAndBalanceAmtMap = balanceVos.stream().collect(toMap(InsurancePracticeOneFeeBalanceVo::getCustId, InsurancePracticeOneFeeBalanceVo::getTotalAmt));
            for (Long custId : custIdMap.keySet()) {
                Set<String> orderNos = custIdMap.get(custId).stream()
                        .map(PracticePayDetailVo::getOrderNo)
                        .collect(Collectors.toSet());
                Set<String> serviceOrderNos = custIdMap.get(custId).stream().filter(c ->!c.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())&&!c.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex()))
                        .map(PracticePayDetailVo::getOrderNo)
                        .collect(Collectors.toSet());
                PrintPaymentFromExportVo printVo = createPrintPaymentVo(custIdMap.get(custId), service,companyBankVo, orderNos,  custIdAndNameMap.get(custId), paymentApplyVo, serviceOrderSet, largeName, applicantName,csutIdAndBalanceAmtMap.get(custId),serviceOrderNos);
                printPaymentFromExportVos.add(printVo);

            }
            resultPrintMap.put(i+","+detailById.getDisCom()+","+paymentApplyVo.getPayCom(), printPaymentFromExportVos);
            i++;
        }

        List<Long> payDomIdList = vos.stream().map(PrintPaymentFromExportVo::getPaymentApplyComId).collect(Collectors.toList());
        insurancePracticeDisComPayMapper.batchUpdatePrintTypeByIdList(payDomIdList);
        return resultPrintMap;
    }


    @Override
    public void offlineTransfer(List<TaskVo> taskVos, String loginName) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("loginName", loginName);
        for (TaskVo taskVo : taskVos) {
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(taskVo.getPaymentApplyId());
            List<InsurancePracticeDisComPayVo> payVos = insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(taskVo.getPaymentApplyComId());
            payVos = payVos.stream().filter(vo -> vo.getPayFlag().equals(BooleanTypeEnum.NO.getCode()) && vo.getOnlineFlag().equals(BooleanTypeEnum.NO.getCode())).collect(Collectors.toList());
            if (payVos.size()==1&&payVos.get(0).getId().equals(taskVo.getPaymentApplyComId())){
                workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
                workflowWrapperService.triggerReceiveTask(paymentApplyVo.getPid(), ReonWorkflowType.PAYMENT_APPLY.getBussinessKey(),loginName,ManualAction.PASS.getDescription());
            }else {
                workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
            }
            insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
            insurancePracticeDisComPayMapper.updateOnlineFlagById(taskVo.getPaymentApplyComId());
        }
    }


    private PrintPaymentFromExportVo createPrintPaymentVo(List<PracticePayDetailVo> finalList,
                                                           BigDecimal service,InsurancePracticePayBankConfigVo companyBankVo,
                                                          Set<String> orderSet,String custName,
                                                          PaymentApplyVo paymentApplyVo,Set<String> serviceOrderSet,
                                                          String largeName,String applicantName,BigDecimal balanceAmt,Set<String> serviceOrderNos
                                                          ) {
        PrintPaymentFromExportVo printVo = new PrintPaymentFromExportVo();
        BigDecimal serviceAmt = service.multiply(BigDecimal.valueOf(serviceOrderNos.size()));
        printVo.setNum(orderSet.size());
        printVo.setPayMonth(paymentApplyVo.getPayMonth());
        orderSet.retainAll(serviceOrderSet);
        printVo.setServiceAmt(serviceAmt);
        Integer contractType = finalList.get(0).getContractType();
        String contractTypeName = EnumsUtil.getNameByCode(contractType, ContractType.class);
        printVo.setProdType(SensitiveReplaceUtil.replace(contractTypeName, false));
        printVo.setCustName(custName);
        printVo.setBankNo(companyBankVo.getReceivingBankNo());
        printVo.setBankName(companyBankVo.getReceivingBankName());
        printVo.setBankComCode(paymentApplyVo.getPayCom());
        printVo.setManager(largeName);
        printVo.setReceivingMan(applicantName);
        BigDecimal totalAmt = BigDecimal.ZERO, interestAmt = BigDecimal.ZERO;
        BigDecimal providentAmt = BigDecimal.ZERO, supProvidentAmt = BigDecimal.ZERO;
        int supNum = 0;

        for (PracticePayDetailVo payDetailVo : finalList) {
            totalAmt = totalAmt.add(payDetailVo.getAmount());
            if (payDetailVo.getAmtType().equals(PracticePayDetailAmtType.IND_LATE_AMT.getCode()) ||
                    payDetailVo.getAmtType().equals(PracticePayDetailAmtType.COM_LATE_AMT.getCode())) {
                interestAmt = interestAmt.add(payDetailVo.getAmount());
            }
            if (payDetailVo.getFeeType().equals(Integer.valueOf(PracticePayMentTypeEnum.REPLENISH_PAYMENT.getCode()))) {
                supNum++;
            }
            if (payDetailVo.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())) {
                providentAmt = providentAmt.add(payDetailVo.getAmount());
            }
            if (payDetailVo.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex())) {
                supProvidentAmt = supProvidentAmt.add(payDetailVo.getAmount());
            }
        }

        printVo.setSupNum(supNum);
        printVo.setSocialAmt(totalAmt.subtract(providentAmt).subtract(supProvidentAmt).subtract(interestAmt));
        printVo.setProvidentAmt(providentAmt);
        printVo.setSupProvidentAmt(supProvidentAmt);
        printVo.setInterestAmt(interestAmt);
        printVo.setTotalAmt(totalAmt.add(printVo.getServiceAmt()));
        printVo.setTotalNum(orderSet.size());
        if (balanceAmt!=null){
            printVo.setBalanceAmt(balanceAmt);
            printVo.setActPayAmt(printVo.getTotalAmt().add(printVo.getBalanceAmt()));
        }else {
            printVo.setActPayAmt(printVo.getTotalAmt());
        }
        return printVo;
    }


    @Override
    public List<PaymentApplyVo> getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        List<PaymentApplyVo> printReceivingApplicationFromPage = paymentApplyService.getPrintReceivingApplicationFromPage(paymentApplyVo);
        if (CollectionUtils.isEmpty(printReceivingApplicationFromPage)){
            return Collections.emptyList();
        }
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        Set<Long> payIdSet = printReceivingApplicationFromPage.stream().map(PaymentApplyVo::getId).collect(Collectors.toSet());
        List<InsurancePracticeOneFeeVo> insurancePracticeOneFeeListByPayIdSet = insurancePracticeOneFeeService.getInsurancePracticeOneFeeListByPayIdSet(payIdSet);
        Set<Long> oneFeePayIdSet = insurancePracticeOneFeeListByPayIdSet.stream().map(InsurancePracticeOneFeeVo::getPaymentId).collect(Collectors.toSet());
        List<String> payComList = printReceivingApplicationFromPage.stream().map(PaymentApplyVo::getPayCom).collect(Collectors.toList());
        List<InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigByOrgCodeList = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCodeList(payComList);
        Map<String, InsurancePracticePayBankConfigVo> bankConfigVoMap = insurancePracticePayBankConfigByOrgCodeList.stream().collect(toMap(InsurancePracticePayBankConfigVo::getOrgCode, Function.identity()));
        List<PaymentApplyVo> printReceivingApplicationList = new ArrayList<>();
        for (PaymentApplyVo applyVo : printReceivingApplicationFromPage) {
            applyVo.setDisComName(orgCodeAndNameMap.get(applyVo.getDisCom()));
            applyVo.setPayComName(orgCodeAndNameMap.get(applyVo.getPayCom()));
            applyVo.setPrintType(BooleanTypeEnum.YES.getCode());
            if (oneFeePayIdSet.contains(applyVo.getId())){
                applyVo.setOneFeeFlag(BooleanTypeEnum.YES.getCode());
                applyVo.setBalanceAmt(applyVo.getPayAmt().subtract(applyVo.getActPayAmt()));
            }else {
                applyVo.setOneFeeFlag(BooleanTypeEnum.NO.getCode());
            }
            if (applyVo.getAppStatus().equals(PaymentApplyProcessStatus.FINISHED.getCode())){
                setBankInfo(applyVo,bankConfigVoMap);
                printReceivingApplicationList.add(applyVo);
            }else {
                String currentApproveName = workflowWrapperService.getCurrentApproveName(applyVo.getPid());
                if ("接单地财务上传凭证".equals(currentApproveName)||"客服上传凭证".equals(currentApproveName)){
                    setBankInfo(applyVo,bankConfigVoMap);
                    printReceivingApplicationList.add(applyVo);
                }else if ("派单地财务支付".equals(currentApproveName)){
                    applyVo.setPrintType(BooleanTypeEnum.NO.getCode());
                    printReceivingApplicationList.add(applyVo);
                }
            }

        }
        return printReceivingApplicationList;
    }

    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printAllDisApplicationFrom(PrintPaymentFromExportVo vo) {
        List<PracticePayDetailVo> practicePayDetailVos = practicePayDetailService.selectPracticePayDetailListByPayId(vo.getId());
        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(vo.getId());
        Map<String, String> userNameMap = userWrapperService.getAllUserMap();
        String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant()));
        String applicantName = userNameMap.get(paymentApplyVo.getApplicant());
        List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(vo.getId());
        Map<String, BigDecimal> disComAmdActPayAmtMap = insurancePracticeDisComListByPayId.stream().collect(toMap(InsurancePracticeDisComPayVo::getDisCom, InsurancePracticeDisComPayVo::getActPayAmt));
        InsurancePracticePayBankConfigVo companyBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
        Map<String, BigDecimal> serviceMap = getServiceMap();
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        List<EmployeeOrderVo> employeeOrderVos = selectCustByOrderNoList(practicePayDetailVos);
        setSignTitleToPracticePayDetail(practicePayDetailVos, employeeOrderVos);
        Map<String, List<PracticePayDetailVo>> signTitleMap = practicePayDetailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getOrgCode));
        Map<String, List<PrintPaymentFromExportVo>> resultMap = new HashMap<>();
        ArrayList<PrintPaymentFromExportVo> printPaymentFromExportVos = new ArrayList<>();
        int i =1;
        for (String signTitle : signTitleMap.keySet()) {
            if (signTitle.equals(paymentApplyVo.getPayCom())&&!disComAmdActPayAmtMap.containsKey(signTitle)){
                continue;
            }
            BigDecimal actPayAmt = disComAmdActPayAmtMap.get(signTitle);
            List<PracticePayDetailVo> practicePayDetailVos1 = signTitleMap.get(signTitle);
            PrintPaymentFromExportVo printPaymentFromExportVo = new PrintPaymentFromExportVo();
            Set<String> serviceOrderSet =  new HashSet<>();
            Set<String> numSet =  new HashSet<>();
            Set<String> suppSet =  new HashSet<>();
            BigDecimal totalAmt = BigDecimal.ZERO;
            BigDecimal providentAmt = BigDecimal.ZERO;
            BigDecimal supProvidentAmt = BigDecimal.ZERO;
            BigDecimal interestAmt = BigDecimal.ZERO;
            for (PracticePayDetailVo practicePayDetailVo : practicePayDetailVos1) {
                if (practicePayDetailVo.getProdCode()!=InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex()&&
                    practicePayDetailVo.getProdCode()!=InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex()&&
                        practicePayDetailVo.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()))
                ){
                    serviceOrderSet.add(practicePayDetailVo.getOrderNo());
                }
                numSet.add(practicePayDetailVo.getOrderNo());
                if (practicePayDetailVo.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode()))){
                    suppSet.add(practicePayDetailVo.getOrderNo());
                }
                totalAmt = totalAmt.add(practicePayDetailVo.getAmount());
                if (practicePayDetailVo.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())){
                    providentAmt = providentAmt.add(practicePayDetailVo.getAmount());
                }
                if (practicePayDetailVo.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex())){
                    supProvidentAmt = supProvidentAmt.add(practicePayDetailVo.getAmount());
                }
                if (practicePayDetailVo.getAmtType().equals(PracticePayDetailAmtType.IND_LATE_AMT.getCode()) ||
                        practicePayDetailVo.getAmtType().equals(PracticePayDetailAmtType.COM_LATE_AMT.getCode())){
                    interestAmt = interestAmt.add(practicePayDetailVo.getAmount());
                }
            }

            printPaymentFromExportVo.setCustName(orgCodeAndNameMap.get(signTitle));
            printPaymentFromExportVo.setProdType("派遣2");
            printPaymentFromExportVo.setPayMonth(paymentApplyVo.getPayMonth());
            printPaymentFromExportVo.setReceivingMan(applicantName);
            printPaymentFromExportVo.setManager(largeName);
            printPaymentFromExportVo.setNum(numSet.size());
            printPaymentFromExportVo.setSupNum(suppSet.size());
            printPaymentFromExportVo.setTotalNum(numSet.size());
            printPaymentFromExportVo.setTotalAmt(totalAmt.add(serviceMap.get(signTitle).multiply(BigDecimal.valueOf(serviceOrderSet.size()))));
            printPaymentFromExportVo.setSocialAmt(totalAmt.subtract(supProvidentAmt).subtract(providentAmt));
            printPaymentFromExportVo.setProvidentAmt(providentAmt);
            printPaymentFromExportVo.setSupProvidentAmt(supProvidentAmt);
            printPaymentFromExportVo.setInterestAmt(interestAmt);
            printPaymentFromExportVo.setServiceAmt(serviceMap.get(signTitle).multiply(BigDecimal.valueOf(serviceOrderSet.size())));
            printPaymentFromExportVo.setBankNo(companyBankVo.getReceivingBankNo());
            printPaymentFromExportVo.setBankName(companyBankVo.getReceivingBankName());
            printPaymentFromExportVo.setBankComCode(paymentApplyVo.getPayCom());
            printPaymentFromExportVo.setActPayAmt(actPayAmt);
            printPaymentFromExportVo.setBalanceAmt(printPaymentFromExportVo.getTotalAmt().subtract(actPayAmt));

            printPaymentFromExportVos.add(printPaymentFromExportVo);
        }
        resultMap.put(i+","+paymentApplyVo.getPayCom()+","+paymentApplyVo.getPayCom(), printPaymentFromExportVos);
        return resultMap;
    }

    @Override
    public InsurancePracticeDisComPayVo getInsurancePracticeDisComPayById(Long id) {
        return insurancePracticeDisComPayMapper.getDetailById(id);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> checkProgressByPayId(Long payId) {
        List<InsurancePracticeDisComPayVo> payVos = insurancePracticeDisComPayMapper.checkProgressByPayId(payId);
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        if (CollectionUtils.isNotEmpty(payVos)){
            for (InsurancePracticeDisComPayVo payVo : payVos) {
                String disComApp = payVo.getDisComApp();
//                String[] parts = disComApp.split(",");
//                String loginName = userWrapperService.findLoginNameByPosCodeAndOrgCode(parts[0], parts[1]);
               // payVo.setDisComApp(loginName);
                payVo.setDisCom(orgCodeAndNameMap.get(payVo.getDisCom()));
            }
        }
        return payVos;
    }

    @Override
    public Map<String, List<PrintReceivingApplicationFromVo>> printReceivingApplicationFrom(PrintPaymentFromExportVo vo) {
        PrintReceivingApplicationFromResultVo printReceivingApplicationFromResultVo = new PrintReceivingApplicationFromResultVo();
        HashMap<String, List<PrintReceivingApplicationFromVo>> resultMap = new HashMap<>();
        /**主体数据*/
        List<PracticePayDetailVo> practicePayDetailVos = practicePayDetailService.selectPracticePayDetailListByPayId(vo.getId());
        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(vo.getId());

        Map<String, String> userNameMap = userWrapperService.getAllUserMap();
        String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant()));
        String applicantName = userNameMap.get(paymentApplyVo.getApplicant());
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        Map<Long, String> customerMap = getCustomerMap();
        List<EmployeeOrderVo> employeeOrderVos = selectCustByOrderNoList(practicePayDetailVos);
        setSignTitleToPracticePayDetail(practicePayDetailVos, employeeOrderVos);
        Map<String, List<PracticePayDetailVo>> signTitleMap = practicePayDetailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getOrgCode));
        List<PrintReceivingApplicationFromVo> providentVoList = new ArrayList<>();
        List<PrintReceivingApplicationFromVo> socialVoList = new ArrayList<>();
        for (String key : signTitleMap.keySet()) {
            List<PracticePayDetailVo> practicePayDetailVos1 = signTitleMap.get(key);
            if (key.equals(paymentApplyVo.getPayCom())) {
                continue;
            }
            PrintReceivingApplicationFromVo providentVo = new PrintReceivingApplicationFromVo();

            PrintReceivingApplicationFromVo socialVo = new PrintReceivingApplicationFromVo();

            fillProvidentData(providentVo, practicePayDetailVos1, paymentApplyVo);
            providentVo.setCustName(orgCodeAndNameMap.get(key));
            providentVo.setProdType("派遣2");


            fillSocialData(socialVo, practicePayDetailVos1, paymentApplyVo);
            socialVo.setCustName(orgCodeAndNameMap.get(key));
            socialVo.setProdType("派遣2");
            if (providentVo.getNum() != null && providentVo.getNum() != 0) {
                providentVo.setManager(largeName);
                providentVo.setReceivingMan(applicantName);
                providentVoList.add(providentVo);
            }
            if (socialVo.getNum() != null&& socialVo.getNum() != 0) {
                socialVo.setManager(largeName);
                socialVo.setReceivingMan(applicantName);
                socialVoList.add(socialVo);
            }
        }
        List<PracticePayDetailVo> revecingPracDetailList = signTitleMap.get(paymentApplyVo.getPayCom());
        if (CollectionUtils.isEmpty(revecingPracDetailList)) {
            resultMap.put("社保", socialVoList);
            resultMap.put("公积金", providentVoList);
            return resultMap;
        }
        Map<Long, List<PracticePayDetailVo>> custIdPracticeDetailVoListMap = revecingPracDetailList.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getCustId));
        for (Long custId : custIdPracticeDetailVoListMap.keySet()) {
            PrintReceivingApplicationFromVo providentCustVo = new PrintReceivingApplicationFromVo();
            PrintReceivingApplicationFromVo socialCustVo = new PrintReceivingApplicationFromVo();

            List<PracticePayDetailVo> customerPracticeDetailVoList = custIdPracticeDetailVoListMap.get(custId);

            Integer contractType = customerPracticeDetailVoList.get(0).getContractType();
            String contractTypeName = EnumsUtil.getNameByCode(contractType, ContractType.class);

            fillProvidentData(providentCustVo, customerPracticeDetailVoList, paymentApplyVo);

            providentCustVo.setProdType(SensitiveReplaceUtil.replace(contractTypeName, false));
            providentCustVo.setCustName(customerMap.get(custId));


            fillSocialData(socialCustVo, customerPracticeDetailVoList, paymentApplyVo);

            socialCustVo.setProdType(SensitiveReplaceUtil.replace(contractTypeName, false));
            socialCustVo.setCustName(customerMap.get(custId));

            if (providentCustVo.getNum() != null&& providentCustVo.getNum() != 0) {
                providentCustVo.setManager(largeName);
                providentCustVo.setReceivingMan(applicantName);
                providentVoList.add(providentCustVo);
            }
            if (socialCustVo.getNum() != null&& socialCustVo.getNum() != 0) {
                socialCustVo.setManager(largeName);
                socialCustVo.setReceivingMan(applicantName);
                socialVoList.add(socialCustVo);
            }
        }
        resultMap.put("社保", socialVoList);
        resultMap.put("公积金", providentVoList);
        return resultMap;
    }


    public void fillProvidentData(PrintReceivingApplicationFromVo providentVo,List<PracticePayDetailVo> practicePayDetailVos,PaymentApplyVo paymentApplyVo){
        //公积金汇缴
        List<PracticePayDetailVo> providentRemList = practicePayDetailVos.stream().filter(c -> c.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())
                && c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(providentRemList)){
            BigDecimal providentRemAmt = providentRemList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Set<String> orderNoSet = providentRemList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            providentVo.setProvidentCount(orderNoSet.size());
            providentVo.setProvidentAmt(providentRemAmt);
        }
        //公积金补缴
        List<PracticePayDetailVo> providentSupList = practicePayDetailVos.stream().filter(c -> c.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())
                && c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(providentSupList)){
            BigDecimal providentSupAmt = providentSupList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Set<String> orderNoSet = providentSupList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            providentVo.setProvidentSupCount(orderNoSet.size());
            providentVo.setProvidentAmt(providentSupAmt.add(providentVo.getProvidentAmt()));
        }

        //补充公积金汇缴
        List<PracticePayDetailVo> supProvidentRemList = practicePayDetailVos.stream().filter(c -> c.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex())
                && c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supProvidentRemList)){
            BigDecimal supProvidentRemAmt = supProvidentRemList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Set<String> orderNoSet = supProvidentRemList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            providentVo.setSupProvidentCount(orderNoSet.size());
            providentVo.setSupProvidentAmt(supProvidentRemAmt);
        }

        //补充公积金汇缴
        List<PracticePayDetailVo> supProvidentSupList = practicePayDetailVos.stream().filter(c -> c.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex())
                && c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supProvidentSupList)){
            BigDecimal supProvidentSupAmt = supProvidentSupList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Set<String> orderNoSet = supProvidentSupList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            providentVo.setSupProvidentSupCount(orderNoSet.size());
            providentVo.setSupProvidentAmt(supProvidentSupAmt.add(providentVo.getSupProvidentAmt()));
        }
        providentVo.setCountAmt(providentVo.getProvidentAmt().add(providentVo.getSupProvidentAmt()));
        providentVo.setNum(providentVo.getProvidentCount()+providentVo.getProvidentSupCount()+providentVo.getSupProvidentSupCount()+providentVo.getSupProvidentCount());
        providentVo.setMonth(paymentApplyVo.getPayMonth());
        providentVo.setPayTime(paymentApplyVo.getLastDate());
    }

    public void fillSocialData(PrintReceivingApplicationFromVo socialVo,List<PracticePayDetailVo> practicePayDetailVos,PaymentApplyVo paymentApplyVo){
        BigDecimal socialCountAmt  = BigDecimal.ZERO;
        //社保数据
        List<PracticePayDetailVo> socailList = practicePayDetailVos.stream().filter(c -> !c.getProdCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())
                && !c.getProdCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(socailList)){
            //社保汇缴
            List<PracticePayDetailVo> socailRemList = socailList.stream().filter(c -> c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()))
                    &&!c.getAmtType().equals(PracticePayDetailAmtType.COM_LATE_AMT.getCode())&&!c.getAmtType().equals(PracticePayDetailAmtType.IND_LATE_AMT.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(socailRemList)){
                BigDecimal socialRemAmt = socailRemList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Set<String> orderNoSet = socailRemList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
                socialVo.setSocialCount(orderNoSet.size());
                socialCountAmt = socialCountAmt.add(socialRemAmt);
            }
            //社保补缴
            List<PracticePayDetailVo> socialSupList = socailList.stream().filter(c -> c.getFeeType().equals(Integer.valueOf(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode()))
                    &&!c.getAmtType().equals(PracticePayDetailAmtType.COM_LATE_AMT.getCode())&&!c.getAmtType().equals(PracticePayDetailAmtType.IND_LATE_AMT.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(socialSupList)){
                BigDecimal socialSupAmt = socialSupList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Set<String> orderNoSet = socialSupList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
                socialVo.setSocialSupCount(orderNoSet.size());
                socialCountAmt = socialCountAmt.add(socialSupAmt);
            }
            Set<String> orderNoSet = socailList.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
            BigDecimal socialAllAmt = socailList.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            socialVo.setNum(orderNoSet.size());
            socialVo.setSocialAmt(socialCountAmt);
            socialVo.setInterestAmt(socialAllAmt.subtract(socialCountAmt));
            socialVo.setCountAmt(socialAllAmt);
            socialVo.setMonth(paymentApplyVo.getPayMonth());
            socialVo.setPayTime(paymentApplyVo.getLastDate());

        }
    }

    public void setBankInfo(PaymentApplyVo paymentApplyVo, Map<String, InsurancePracticePayBankConfigVo> bankConfigVoMap){
        InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = bankConfigVoMap.get(paymentApplyVo.getPayCom());
        if (!Objects.isNull(insurancePracticePayBankConfigVo)){
            paymentApplyVo.setBankType(insurancePracticePayBankConfigVo.getReceivingBankType());
            paymentApplyVo.setBankNo(insurancePracticePayBankConfigVo.getReceivingBankNo());
            paymentApplyVo.setPayBankName(insurancePracticePayBankConfigVo.getReceivingBankName());
        }
    }


    public Map<String, BigDecimal> getServiceMap(){
        List<InsurancePracticeServiceConfigVo> insurancePracticeServiceConfig = insurancePracticeServiceConfigWrapperService.getInsurancePracticeServiceConfig();
        return insurancePracticeServiceConfig.stream()
                .collect(Collectors.toMap(InsurancePracticeServiceConfigVo::getOrgCode, InsurancePracticeServiceConfigVo::getServiceAmt));

    }



    /**
     * 查询订单对应的客户信息
     * @param practicePayDetailVos
     * @return {@link List }<{@link EmployeeOrderVo }>
     */
    public List<EmployeeOrderVo> selectCustByOrderNoList(List<PracticePayDetailVo> practicePayDetailVos){

        List<String> paramOrderNos = practicePayDetailVos.stream()
                .map(PracticePayDetailVo::getOrderNo)
                .collect(Collectors.toList());

        return employeeOrderWrapperService.getContractTypeAndDisComByOrderNoList(paramOrderNos);
    }

    /**
     * 赋值给实做支付信息
     * @param practicePayDetailVos
     * @param employeeOrderVos
     */
    public void setSignTitleToPracticePayDetail(List<PracticePayDetailVo> practicePayDetailVos, List<EmployeeOrderVo> employeeOrderVos){
        Map<String, EmployeeOrderVo> ordrNoAndEmployeeOrderVoMap = employeeOrderVos.stream().collect(toMap(EmployeeOrderVo::getOrderNo, Function.identity()));
        for (PracticePayDetailVo payDetailVo : practicePayDetailVos) {
            String orderNo = payDetailVo.getOrderNo();
            EmployeeOrderVo employeeOrderVo = ordrNoAndEmployeeOrderVoMap.get(orderNo);
            payDetailVo.setCustId(employeeOrderVo.getCustId());
            payDetailVo.setContractType(employeeOrderVo.getContractType());
            payDetailVo.setOrgCode(employeeOrderVo.getSignDistCom());
        }
    }

    public Set<String> getServiceOrderSet(List<PracticePayDetailVo> practicePayDetailVos){
        return practicePayDetailVos.stream().filter(c -> c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE10.getIndex()
                && c.getProdCode() != BillReportEnum.IsProductIndTypeEnum.PRODUCT_IND_TYPE11.getIndex()
                && Objects.equals(c.getFeeType(), Integer.valueOf(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode())))
                .map(PracticePayDetailVo::getOrderNo).collect(Collectors.toSet());
    }

    public Map<Long, String> getCustomerMap(){
        List<CustomerVo> allCustomer = customerWrapperService.findAllCustomer();
        return allCustomer.stream()
                .collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
    }

    public Map<String, String> getOrgCodeAndNameMap(){
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        return allCompany.stream().collect(toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
    }



    public String getLargerName(String applicant){
        UserOrgPosVo userOrgPosVo = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(applicant);
        UserOrgPosVo largeUserVo = userWrapperService.getLargeDefaultFlagOrgPosByOrgCode(userOrgPosVo.getOrgCode());
        return largeUserVo.getLoginName();
    }


}


