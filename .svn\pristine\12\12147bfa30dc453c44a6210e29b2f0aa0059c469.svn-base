package com.reon.hr.sp.customer.dao.employee;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.ServiceSiteCfgVo;
import com.reon.hr.api.bill.dto.PerInsuranceBillDto;
import com.reon.hr.api.customer.dto.EmployeeChangeReportExportDto;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.employee.EmployeeOrderDto;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;
import com.reon.hr.api.customer.dto.exportData.ServiceNumPeopleReportDto;
import com.reon.hr.api.customer.dto.report.EmployeeReportDto;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeInfoVo;
import com.reon.hr.sp.customer.entity.employee.Employee;
import com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission;
import com.reon.hr.sp.customer.entity.employee.EmployeeOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface EmployeeOrderMapper extends BaseMapper<EmployeeOrder> {

    int deleteByPrimaryKey(String orderNo);

    int insertSelective(EmployeeOrder record);

    EmployeeOrderVo selectByPrimaryKey(@Param("orderNo") String orderNo, @Param("month") Integer month);

    int getUseQuotationEmpOrderCount(String quotationNo);


    List<EmployeeOrderVo> getOneByOrderNo(@Param("orderNo") String orderNo, @Param("certNo") String certNo,
                                          @Param("receiving") String receivingName, @Param("custNo") String custNo, @Param("month") Integer month);

    /**
     获得员工数据为垫付
     @param areaNoList 区域没有列表
     @return {@link EmployeeOrderVo}
     */
    List<EmployeeOrderVo> getEmpDataForAdvancePayment(@Param("list") List<String> areaNoList);

    EmployeeOrderVo selectByOrderNo(String orderNo);

    int updateByPrimaryKeySelective(EmployeeOrder record);

    /**
     批量修改员工订单信息
     @param employeeOrders 员工订单信息列表
     @return 更新行数
     */
    int updateByOrderNoList(List<EmployeeOrder> employeeOrders);

    /**
     生成个人订单Page
     @return
     */
    List<EmployeeOrderViewVo> getListPage(Page page, EmployeeOrderVo orderVo);

    List<EmployeeOrderVo> getRemark1();

    int deleteByOrderNos(@Param("orderNos") List<String> orderNos, @Param("updater") String updater);

    List<EmployeeOrderDto> getChangeTempleteList(ChangeTempletFindParaVo changeTempletFindParaVo);

    /**
     完善/确认/申请离职  Page
     @return
     */
    List<CompleteOrderViewVo> getCompleteOrderListPage(Page page, @Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    /**
     完善/确认/申请离职
     @return
     */
    List<CompleteOrderViewVo> getCompleteOrderListPage(@Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    /**
     批量修改状态 根据订单编号
     @return
     */
    int updateStatusBatch(@Param("orderNos") List<String> orderNos, @Param("status") Integer status, @Param("updater") String updater);


    List<EmployeeEntryDimission> getEmployeeStatus(String certNo);

    List<PerInsuranceBillVo> getListByCustIdAndTempletId(@Param("contractNo") String contractNo, @Param("templetId") Long templetId, @Param("list") Set<String> orderNoList);

//    int batchUpdateRevCsByContractAreaNo(@Param("revCs") String revCs,  @Param("contractAreaNo") String contractAreaNo);


    int batchUpdateChgStatusByOrderNos(@Param("chgStatus") Integer chgStatus, @Param("OrderNoList") List<String> OrderNoList, @Param("updater") String orderNos);


    /**
     查询减员报表
     @return
     */
    List<EmployeeReportDto> getDimReportByApplyDimDateAndCust(@Param("applyDimDateS") String applyDateS,
                                                              @Param("applyDimDateE") String applyDateE,
                                                              @Param("custName") String custName, @Param("distPlace") Integer distPlace, @Param("cityCode") Integer cityCode, @Param("receiving") String receiving, @Param("commissioner") String commissioner,
                                                              @Param("accountFlag") Integer accountFlag, @Param("groupName") String groupName, @Param("userOrgPositionDtoList") List<OrgPositionDto> userOrgPositionDtoList
    );

    /**
     查询增员报表
     @return
     */
    List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust(@Param("applyEntryDateS") String applyEntryDateS,
                                                                  @Param("applyEntryDateE") String applyEntryDateE,
                                                                  @Param("custName") String custName, @Param("distPlace") Integer distPlace, @Param("cityCode") Integer cityCode,
                                                                  @Param("receiving") String receiving);

    /**
     查询增员报表
     @return
     */
    List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust1(@Param("applyEntryDateS") String applyEntryDateS,
                                                                   @Param("applyEntryDateE") String applyEntryDateE,
                                                                   @Param("custName") String custName, @Param("distPlace") Integer distPlace, @Param("cityCode") Integer cityCode,
                                                                   @Param("receiving") String receiving,
                                                                   @Param("commissioner") String commissioner,
                                                                   @Param("accountFlag") Integer accountFlag,
                                                                   @Param("groupName") String groupName,
                                                                   @Param("userOrgPositionDtoList") List<OrgPositionDto> userOrgPositionDtoList);


    List<OrderInsuranceCfgVo> getComAmtAndIndAmt(@Param("contractNo") String contractNo);

    int getCountByContractAreaAndCertNo(@Param("contractAreaNo") String contractAreaNo, @Param("certNo") String certNo);

    /**
     汇总查询订单数据
     */
    List<SummaryOrderVo> summaryOrder(Page page,
                                      @Param("customerId") String customerId,
                                      @Param("signCom") String signCom);

    /**

     */
    List<CollectQueryEmployeeVo> collectQueryEmployeeInfo(@Param("vo") CollectQueryEmployeeVo collectQueryEmployeeVo,
                                                          @Param("list") List<String> groupRatioIdList);

    /**
     批量修改订单的接单客服和主管
     @return
     */
//    int batchUpdateRevCsByContractAreaSet(@Param("revCs") String revCs
//            , @Param("revSupervisor") String revSupervisor
//            , @Param("contractAreaNos") Set<String> contractAreaNos);
//
    int batchUpdatePrjByContractNo(@Param("prjCs") String prjCs,
                                   @Param("prjSupervisor") String prjSupervisor,
                                   @Param("contractNo") String contractNo);

    int updateByTransferContractNo(EmployeeOrder record);

    int updateByTransferContractAreaNo(EmployeeOrder record);

    List<EmployeeOrderViewVo> getListPageBysystem(EmployeeOrderVo orderVo);

    List<EmployeeOrderVo> getEmployeeDataForEmployeeContract(Page page, @Param("EO") EmployeeOrderVo employeeOrderVo);

    EmployeeOrderVo getEmployeeDataForEmployeeContractByProperty(@Param("EO") EmployeeOrderVo employeeOrderVo);

    String getEmployeeOrderNo(@Param("contractAreaNo") String contractAreaNo, @Param("employeeId") String employeeId);

    List<String> getAllEmployeeNoAndContractAreaNoList();

    List<String> getOrderNoByCustIdAndcontractAreaNo(@Param("employeeNo") String employeeNo, @Param("contractAreaNo") String contractAreaNo);

    String getOrderNoByEmpId(String empId);

    String getOrderNo(@Param("areaNo") String areaNo,
                      @Param("employeeId") String employeeId);

    List<StaffOnActiveDutyCustomerVo> getAllStaffOnActiveDutyByCityCode(@Param("cityCode") String cityCode, @Param("nowMonth") Integer nowMonth, @Param("receiving") String receiving, @Param("custNo") String custNo, @Param("accountFlag") Integer accountFlag, @Param("groupName") String groupName, @Param("receivingMan") String receivingMan, @Param("commissioner") String commissioner);


    List<EmployeeOrderVo> getEmployeeOrderVoByOrderNos(List<String> orderNos);

    /**
     根据证件号与证件类型查询订单
     @return
     */
    // List<EmployeeOrderVo> getEmployeeOrderVoByCertNos(@Param("certNos") List<String> certNos,@Param("receiving") String receiving,@Param("accountFlag")Integer accountFlag );
    List<EmployeeOrderVo> getLastEmployeeOrderVoByCertNos(@Param("certNos") List<String> certNos, @Param("receiving") String receiving, @Param("accountFlag") Integer accountFlag);

    List<EmployeeOrderVo> getEmployeeOrderVoByCertNo(@Param("certNo") String certNo);

    /**
     为 societyInsuranceApplicationListener获取数据
     */
    List<EmployeeOrderVo> getSocietyInsuranceApplicationDataByOrderNo(@Param("orderNoList") List<String> orderNoList);

    List<EmployeeOrderVo> getEmployeeOrderVosByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    List<String> getOrderNoList();

    List<EmployeeOrderVo> getEmployeeOrderList();


    /**
     查订单编号对应的员工id、编号、姓名等信息
     @return
     */
    List<EmployeeOrderVo> findAll();

    Employee getEmployeeByCertNo(@Param("certNo") String certNo);

    List<EmployeeOrderVo> getAllEmployeeOrderPartData();

    /**
     根据orderNo列表遍历查询所有匹配项
     @param orderNos 订单编号
     @return EmployeeOrderVo列表
     */
    List<EmployeeOrderVo> getEmployeeOrderByOrderNos(List<String> orderNos);

    List<OrderInsuranceCfgVo> getComAmtAndIndAmtListMap(@Param("list") List<String> contractNoList);

    List<NationwideEmployeeViewVo> getNationwideEmployee(@Param("page") Page nationwidePage, @Param("vo") EmployeeOrderVo employeeOrderVo);

    void rejectedLeave(@Param("list") List<String> orderNos, @Param("updater") String loginName);

    List<CompleteOrderViewVo> getLeaveDataListPage(Page page, @Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    List<CompleteOrderViewVo> getReductionPage(Page page, @Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    List<CompleteOrderViewVo> getReductionPage(@Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    int updateOrderStatusByOrderNo(@Param("list") List<EmployeeOrder> employeeOrders);

    EmployeeOrderVo getOneConfirmGetOneById(@Param("orderNo") String orderNo, @Param("month") Integer month);

    List<EmployeeOrder> selectEmployeeOrderByOrderNoList(@Param("list") List<String> orderNos);

    List<EmployeeOrderVo> getEmployeeOrderByOrderNoList(@Param("list") List<String> orderNoList);

    List<EmployeeOrderVo> getEmployeeOrderVoByCertNoAndNonOrderNo(@Param("certNo") String certNo, @Param("orderNo") String orderNo);

    List<EmployeeOrderVo> getAllEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos(@Param("list") List<EmployeeOrderVo> employeeOrderVos);

    List<EmployeeOrderVo> getEmployeeOrderVoByCustIdAndcontractAreaNo(@Param("employeeNo") String employeeNo, @Param("contractAreaNo") String contractAreaNo);

    List<EmployeeOrderVo> getActiveStaffByContractNo(@Param("list") Set<String> contractNoSet);


    List<String> getAllOrderNoByEmpId(Long empId);

    List<EmployeeOrderVo> getAllEmployeeOrderByCustIdList(@Param("list") List<Long> custIdList);

    List<EmployeeOrderVo> getAllContractNoAndTempletId(@Param("genDate") Integer genDate);

    List<EmployeeOrderVo> getCertNoByOrderNoList(@Param("list") List<String> orderNoList);

    List<EmployeeOrderVo> getNameByOrderNoList(@Param("list") List<String> orderNoList);

    List<CompleteOrderViewVo> getEmployeeDimissionListPage(Page page, @Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    List<CompleteOrderViewVo> getCompleteOrderListWorkFow(@Param("orderViewVo") CompleteOrderViewVo orderViewVo);

    List<EmployeeOrderVo> getOrderNoAndCityCodeByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    Long findEmployeeIdByOrderNo(String orderNo);

    List<EmployeeOrderVo> findEmployeeIdByOrderNos(@Param("orderNoList") List<String> orderNoList);

    String getReceivingManNameByOrderNo(String orderNo);

    List<EmployeeOrderVo> getReceivingManNameByOrderNos(@Param("orderNos") List<String> orderNos);

    ContractAreaVo getReceivingManAndOrgPosCodeOrderNo(String orderNo);

    EmployeeOrderVo getStatusByOrderNo(@Param("orderNo") String orderNo, @Param("custNo") String custNo);

    Long selectCountByOrderNo(String orderNo);


    EmployeeOrderVo getCommissionerAndReceivingManByOrderNo(@Param("orderNo") String orderNo);


    List<SalaryEmployeeInfoVo> getFileIdByempIdList(@Param("salaryEmployeeInfo") List<SalaryEmployeeInfoVo> salaryEmployeeInfo);

    List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatus(@Param("contractStatus") int contractStatus, @Param("start") long start, @Param("end") long end);

    Long getAllEmployeeOrderNoByContractStatusCount(int contractStatus);


    List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatusAndOrderNoList(@Param("contractStatus") int contractStatus, @Param("list") List<String> dealOrderNos);


    List<EmployeeOrderVo> getAllOrderNo(@Param("page") Page page, @Param("vo") EmployeeOrderVo employeeOrderVo);

    List<EmployeeOrderVo> getRemarkByOrderNoList(@Param("list") List<String> orderNoList);

    List<EmployeeOrderVo> getOrderNoAndCertNo();

    int updateFileStatusByOrderNo(@Param("orderNo") String orderNo, @Param("columnName") String columnName);

    List<EmployeeOrderVo> getAllEmployeeOrderByStatus();

    List<String> getAllOrderNoByContractNoAndTempletId(@Param("contractNo") String contractNo, @Param("templetId") Long templetId);

    List<EmployeeOrderVo> getAllEmpOrderByCustId(@Param("args") InsuranceBillAndPracDiffArgs args);

    Integer getReceivingTypeByOrderNo(String orderNo);

    /**
     在职
     查询合同号与账单模板下面的税率
     只能查出一个税率，如果查出多条就是数据有误
     @return
     */
    List<CommInsurEmpVo> getTaxRatioByContractNoAndTemId(@Param("contractNo") String contractNo, @Param("templetIds") List<Long> templetId);

    /**
     最近几个月的全部订单
     查询合同号与账单模板下面的税率
     只能查出一个税率，如果查出多条就是数据有误
     @return
     */
    List<CommInsurEmpVo> getAllTaxRatioByContractNoAndTemId(@Param("contractNo") String contractNo, @Param("templetIds") List<Long> templetId, @Param("date") String date);

    /**
     根据订单号查询税率
     @return
     */
    List<CommInsurEmpVo> getTaxRatioByOrderNos(@Param("orderNos") List<String> orderNos);


    List<EmployeeOrderVo> getEmployeeOrderVoByMonthsRange(@Param("preTime") String preTime, @Param("startMonth") Integer startMonth, @Param("endMonth") Integer endMonth, @Param("orderNos") List<String> orderNos);

    List<EmployeeOrderVo> getEmployeeOrderVoByCreateTime(@Param("oneMonthBefore") String oneMonthBefore, @Param("twoMonthBefore") String twoMonthBefore,
                                                         @Param("threeMonthBefore") String threeMonthBefore, @Param("orderNo") String orderNo);

    void updateContractFileFlag(@Param("orderNo") String orderNo);

    List<EmployeeOrderVo> getEmployeeAndContractAreaByOrderNoList(@Param("orderNoList") List<String> orderNoList);


    List<EmployeeOrderVo> getEmployeeOrderByInsuranceSetNo(@Param("insuranceSetNos") List<String> insuranceSetNo);

    List<PerInsuranceBillVo> getPibByOrderNoList(@Param("list") Set<String> orderNoList);

    List<String> getOrderNosByContractAreaNos(@Param("contractAreaNos") List<String> contractAreaNos);

    List<ServiceSiteCfgVo> getPeopleNumGroupByCityCode();


    List<OrderFeeVo> getOrderFee(@Param("vo") OrderFeeParamVo orderFeeParamVo, @Param("orderNoList") Set<String> orderNoList);

    int getOrderFeeCount(@Param("vo") OrderFeeVo vo);

    List<InsurancePracticeVo> getStartMonthAndEndMonthByCertNo(@Param("certNos") List<String> certNo);

    List<EmployeeOrderVo> getCommissionerByOrderNoList(@Param("list") List<String> orderNoList);

    Long getEmpIdByOrderNo(String orderNo);

    int getEmployeeOrderCountByCertNo(@Param("certNo") String certNo);

    List<ServiceNumPeopleReportDto> getServiceNum(@Param("item") Map<String, Object> conditonMap);

    EmployeeOrderVo selectOneByPrimaryKey(String orderNo);

    int updateLaborFileStatusByOrderNo(@Param("orderNo") String orderNo);


    EmployeeOrderVo getCommissionerOrReceivingMan(@Param("orderNo") String orderNo);

    int getCountByCommissionerOrReceivingMan(@Param("loginName") String loginName, @Param("status") Integer status);

    List<String> getReceivingByOrderNoList(@Param("orderNoList") List<String> orderNoList);


    @Select("select count(*) from `reon-customerdb`.employee_order where employee_id = #{employeeId} and del_flag = 'N'")
    Integer getSizeByEmpIdAndNotDel(@Param("employeeId") Long employeeId);

    List<PersonOrderQueryDto> selectReonOrderPage(@Param("page") Page page, @Param("cm") Map<String, Object> conditionMap);

    int getRejectOrderCountByLoginName(@Param("loginName") String loginName);


    List<FinancialRequireVo> getFinancialRequireDataBy(@Param("pibList") List<PerInsuranceBillDto> pibList, @Param("contractNoList") List<String> contractNoList, @Param("templetIdList") List<Long> templetIdList);

    String selectReceivingManByOrderNo(String orderNo);


    List<OrderAndInsuranceDiffExportVo> getOrderFeeDetailByOrderNoSet(@Param("orderNoList") Set<String> orderNoList);


    List<String> getOrderNoListByReceivingList(@Param("contractAreaNoList") List<String> contractAreaNoList);

    @Select("select order_no from `reon-customerdb`.employee_order where employee_id = (select employee_id from `reon-customerdb`.employee_order where order_no = #{orderNo} limit 1) and del_flag='N'")
    List<String> getAllOrderNoByOrderNo(@Param("orderNo") String orderNo);

    List<EmployeeChangeReportExportDto> getOrderNoByEmpIdHaveMutiOrderNo(@Param("year") String year, @Param("month") String month);

    List<EmployeeOrderVo> getContractSignComTitleByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    List<Long> getAllEmpIdByCommissionerOrReceivingMan(@Param("loginName") String loginName);


    List<EmployeeOrderVo> getContractTypeAndDisComByOrderNoList(@Param("orderNoList") List<String> orderNoList);


    List<EmployeeOrderVo> selectOrderByOrderNoList( @Param("orderNoList") Set<String> orderNoList);

    List<EmployeeOrderVo> selectContractTypeByOrderNoList(@Param("orderNoList") List<String> orderNoList);

}




