package com.reon.hr.api.report.utils;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.report.anno.ExcelColumn;
import com.reon.hr.api.report.dto.EmpTrackReportDto;
import com.reon.hr.api.report.vo.EmpTrackReportExportVo;
import com.reon.hr.api.report.vo.ServiceNumPeopleReportVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("deprecation")
public class ExcelUtil {

    public static final String TITLES = "titles";
    public static final String METHODS = "methods";
    public static final String COLUMN_WIDTHS = "columnWidths";
    public static final String FIELD_List = "fileldList";

    /**
     获取excel的表头的名称和get方法
     @return
     */
    public static Map<String, Object> getReflectInfo(Class<?> c) throws IntrospectionException {
        List<String> titles = Lists.newArrayList();
        List<Method> getMethods = Lists.newArrayList();
        List<Integer> columnWidths = Lists.newArrayList();
        Map<String, Object> maps = Maps.newHashMap();
        List<String> fieldNameList = Lists.newArrayList();
        Field[] fields = c.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            if (!fields[i].getName().equalsIgnoreCase("serialVersionUID") && !fields[i].getName().equalsIgnoreCase("$jacocoData")) {
                ExcelColumn excelColumn = fields[i].getAnnotation(ExcelColumn.class);
                if (excelColumn != null) {
                    fieldNameList.add(fields[i].getName());
                    titles.add(excelColumn.name());
                    columnWidths.add(excelColumn.columnWidth());
                    getMethods.add(new PropertyDescriptor(fields[i].getName(), c).getReadMethod());
                }
            }
        }
        maps.put(TITLES, titles);
        maps.put(METHODS, getMethods);
        maps.put(COLUMN_WIDTHS, columnWidths);
        maps.put(FIELD_List, fieldNameList);
        return maps;
    }

    /**
     设置样式
     @param isWrapText 是否开启自动换行
     @param isBold     是否加粗
     @return
     */
    public static CellStyle getCellStyle(SXSSFWorkbook wb, boolean isWrapText, boolean isBold) {
        CellStyle cellStyle = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontName("SimSun");
        font.setFontHeightInPoints((short) 10);
        // 加粗
        font.setBold(isBold);
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);// 左边框
        cellStyle.setBorderTop(BorderStyle.THIN);// 上边框
        cellStyle.setBorderRight(BorderStyle.THIN);// 右边框
        cellStyle.setWrapText(isWrapText);// 是否开启换行
        return cellStyle;
    }

    public static CellStyle getErrorCellStyle(SXSSFWorkbook wb, boolean isWrapText, boolean isBold) {
        CellStyle cellStyle = getCellStyle(wb, isWrapText, isBold);
        cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        return cellStyle;
    }

    /**
     填充excel中的内容
     */
    public static <T> SXSSFWorkbook setWorkbook(List<T> list, Class<?> clazz, String sheetName) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
        CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) {
                headRow.getCell(i).setCellStyle(headCellStyle);
            }
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        for (int i = 0; i < list.size(); i++) {
            T object = list.get(i);
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if (methods.get(j).getName().equals("getNo")) {
                    dataRow.createCell(j).setCellValue(i + 1);
                } else if (methods.get(j).getReturnType().equals(java.math.BigDecimal.class)) {
                    if (obj != null) {
                        String string = obj.toString();
                        BigDecimal bigDecimal = new BigDecimal(string);
                        dataRow.createCell(j).setCellValue(bigDecimal.doubleValue());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }

                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }
                }
                // 设置样式
                if (j < methods.size()) {
                    dataRow.getCell(j).setCellStyle(cellStyle);
                }
                // 如果该单元格为数据确认过程和离职确认过程 开启自动换行
                if ("getEntryProcess".equalsIgnoreCase(methods.get(j).getName()) || "getDimissionProcess".equalsIgnoreCase(methods.get(j).getName())) {
                    dataRow.getCell(j).setCellStyle(cellTypeWrapText);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, columnWidths.get(j));
            }
        }
        return sxssfWorkbook;
    }


    public static <T> SXSSFWorkbook setWorkbookForServiceNumReport(List<T> list, Class<?> clazz, String sheetName, List<String> monthBetween) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        titles.addAll(monthBetween);
        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
        CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) {
                headRow.getCell(i).setCellStyle(headCellStyle);
            }
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        for (int i = 0; i < list.size(); i++) {
            T object = list.get(i);
            ServiceNumPeopleReportVo snprVo = (ServiceNumPeopleReportVo) list.get(i);
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if (methods.get(j).getName().equals("getNo")) {
                    dataRow.createCell(j).setCellValue(i + 1);
                } else if (methods.get(j).getReturnType().equals(java.math.BigDecimal.class)) {
                    if (obj != null) {
                        String string = obj.toString();
                        BigDecimal bigDecimal = new BigDecimal(string);
                        dataRow.createCell(j).setCellValue(bigDecimal.doubleValue());
                    } else
                        dataRow.createCell(j).setCellValue("");
                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else
                        dataRow.createCell(j).setCellValue("");
                }

                // 设置样式
                if (j < methods.size()) {
                    dataRow.getCell(j).setCellStyle(cellStyle);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                Integer cw = Optional.ofNullable(columnWidths.get(j)).orElseGet(() -> 3000);
                sheet.setColumnWidth(j, cw);
            }
            Integer start = methods.size();
            Integer cycleIndex = monthBetween.size();
            if (MapUtils.isNotEmpty(snprVo.getMonthAndNumMap())) {
                Map<Integer, Integer> monthAndNumMap = snprVo.getMonthAndNumMap();
                for (String yearMonth : monthBetween) {
                    if (monthAndNumMap.containsKey(Integer.parseInt(yearMonth))) {
                        dataRow.createCell(start).setCellValue(monthAndNumMap.get(Integer.parseInt(yearMonth)));
                    } else {
                        dataRow.createCell(start).setCellValue("");
                    }
                    // 设置样式
                    dataRow.getCell(start).setCellStyle(cellStyle);
                    sheet.setColumnWidth(start, 3000);
                    start++;
                }
            }

        }
        return sxssfWorkbook;
    }

    public static <T> SXSSFWorkbook setWorkbookForEmpTrackReport(EmpTrackReportDto empTrackReportDto, Class<?> clazz, String sheetName) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        List<EmpTrackReportExportVo> list = empTrackReportDto.getEmpTrackReportExportVoList();
//        // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
//        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 1));

        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        List<String> fieldList = (List<String>) reflectInfos.get(FIELD_List);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);

        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);


        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) headRow.getCell(i).setCellStyle(headCellStyle);
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        Map<Long, Map<String, Integer>> groupIdAndRegionMap = empTrackReportDto.getGroupIdAndRegionMap();
        Map<Long, Map<String, Integer>> custIdAndRegionMap = empTrackReportDto.getCustIdAndRegionMap();
        Set<Long> custIdSet = Sets.newHashSet();
        Set<Long> groupIdList = Sets.newHashSet();
        Set<String> custIdRegionSet = Sets.newHashSet("custNameCurrentMonthEmpCnt", "custNameLastMonthEmpCnt", "custNameLastMonthContrastEmpCnt");   // 客户列 哪些列要合并
        Set<String> groupIdRegionSet = Sets.newHashSet("groupCurrentMonth_empCnt", "groupCurrentMonth_1_empCnt", "groupNameCurrentMonthConstantLastMonthEmpCnt",
                "groupNameCurrentMonthConstantLastMonthEmpCnt", "groupCurrentMonth_1_compareRatioStr", "groupCurrentMonth_2_compareRatioStr", "groupCurrentMonth_3_compareRatioStr",
                "groupCurrentMonth_4_compareRatioStr", "groupCurrentMonth_5_compareRatioStr", "groupThreeMonthsLayoffs", "groupNotHalfYearLayoffs", "groupCurrentMonth_compareRatioStr");   //  集团列 哪些列要合并

        for (int i = 0; i < list.size(); i++) {
            EmpTrackReportExportVo object = list.get(i);
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if (methods.get(j).getName().equals("getNo")) {
                    dataRow.createCell(j).setCellValue(i + 1);
                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }
                }
                // 设置样式
                if (j < methods.size()) {
                    dataRow.getCell(j).setCellStyle(cellStyle);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, columnWidths.get(j));
            }
        }
/** 这是合并显示的代码 ,现在又说要去掉合并显示,目前先不删除,防止又说要合并 */
//        for (int i = 0; i < fieldList.size(); i++) {
//            String fieldName = fieldList.get(i);
//            if(custIdRegionSet.contains(fieldName)){
//                for (Long custId : custIdAndRegionMap.keySet()) {
//                    sheet.addMergedRegion(new CellRangeAddress(custIdAndRegionMap.get(custId).get("s"), custIdAndRegionMap.get(custId).get("e"), i, i));
//                }
//            }
//            if(groupIdRegionSet.contains(fieldName)){
//                for (Long groupId : groupIdAndRegionMap.keySet()) {
//                    sheet.addMergedRegion(new CellRangeAddress(groupIdAndRegionMap.get(groupId).get("s"), groupIdAndRegionMap.get(groupId).get("e"), i, i));
//                }
//            }
//        }


        return sxssfWorkbook;
    }

    /**
     关闭流等
     */
    public static void closeInfo(HttpServletResponse response, SXSSFWorkbook sxssfWorkbook, String fileName) throws IOException {
        // 设置头信息
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        // 设置成xlsx格式
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
        // 创建一个输出流
        ServletOutputStream outputStream = response.getOutputStream();
        // 写入数据
        sxssfWorkbook.write(outputStream);
        // 关闭
        outputStream.close();
        sxssfWorkbook.close();
    }

    /**
     根据表头动态填充excel中的内容
     */
    public static <T> SXSSFWorkbook setWorkbook(List<T> list, Class<?> clazz, String sheetName, List<String> dateTitles) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        titles.addAll(dateTitles);
        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
        CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) {
                headRow.getCell(i).setCellStyle(headCellStyle);
            }
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        for (T object : list) {
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if ("getNo".equals(methods.get(j).getName())) {
                    dataRow.createCell(j).setCellValue(list.indexOf(object) + 1);
                } else if (methods.get(j).getReturnType().equals(java.math.BigDecimal.class)) {
                    if (obj != null) {
                        String string = obj.toString();
                        BigDecimal bigDecimal = new BigDecimal(string);
                        dataRow.createCell(j).setCellValue(bigDecimal.doubleValue());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }

                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }
                }
                // 设置样式
                if (j < methods.size()) {
                    dataRow.getCell(j).setCellStyle(cellStyle);
                }
                // 如果该单元格为数据确认过程和离职确认过程 开启自动换行
                if ("getEntryProcess".equalsIgnoreCase(methods.get(j).getName()) || "getDimissionProcess".equalsIgnoreCase(methods.get(j).getName())) {
                    dataRow.getCell(j).setCellStyle(cellTypeWrapText);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, columnWidths.get(j));
            }
            Method[] methods1 = object.getClass().getMethods();
            for (int j = 0; j < methods1.length; j++) {
                if ("getQuoteDataMap".equals(methods1[j].getName())) {
                    Map<String, Map<String, String>> quoteDateMap = (Map<String, Map<String, String>>) methods1[j].invoke(object);
                    if (quoteDateMap != null) {
                        List<String> values = new ArrayList<>();
                        for (int i = 0; i < quoteDateMap.size(); i++) {
                            Map<String, String> quoteData = quoteDateMap.get(String.valueOf(i + 1));
                            if (quoteData == null) {
                                continue;
                            }
                            Set set = quoteData.keySet();
                            Object[] arr = set.toArray();
                            Arrays.sort(arr);
                            for (Object key : arr) {
                                values.add(quoteData.get(key));
                            }
                        }
                        for (int i = methods.size(); i < methods.size() + values.size(); i++) {
                            int k = i - methods.size();
                            String quoteValue = values.get(k);
                            if (StringUtils.isNotEmpty(quoteValue)) {
                                dataRow.createCell(i).setCellValue(quoteValue);
                            } else {
                                dataRow.createCell(i).setCellValue("");
                            }
                            // 设置样式
                            if (i < methods.size() + values.size()) {
                                dataRow.getCell(i).setCellStyle(cellStyle);
                            }
                        }
                    }
                }
                if ("getSalaryPayDetailMap".equals(methods1[j].getName())) {
                    Map<String, BigDecimal> salaryPayDetailMap = (Map<String, BigDecimal>) methods1[j].invoke(object);
                    if (salaryPayDetailMap != null) {
                        List<BigDecimal> values = new ArrayList<>();
                        for (int i = 0; i < dateTitles.size(); i++) {
                            values.add(salaryPayDetailMap.get(dateTitles.get(i)));
                        }
                        for (int i = methods.size(); i < methods.size() + values.size(); i++) {
                            int k = i - methods.size();
                            if (values.get(k) != null) {
                                dataRow.createCell(i).setCellValue(values.get(k).toString());
                            } else {
                                dataRow.createCell(i).setCellValue("");
                            }
                            // 设置样式
                            if (i < methods.size() + values.size()) {
                                dataRow.getCell(i).setCellStyle(cellStyle);
                            }
                        }
                    }
                }
                if ("getVoucherDateMap".equals(methods1[j].getName())) {
                    Map<String, Object> voucherDateMap = (Map<String, Object>) methods1[j].invoke(object);
                    if (voucherDateMap != null) {
                        List<Object> values = new ArrayList<>();
                        for (int i = 0; i < dateTitles.size(); i++) {
                            values.add(voucherDateMap.get(dateTitles.get(i)));
                        }
                        for (int i = methods.size(); i < methods.size() + values.size(); i++) {
                            int k = i - methods.size();
                            if (values.get(k) != null) {
                                dataRow.createCell(i).setCellValue(values.get(k).toString());
                            } else {
                                dataRow.createCell(i).setCellValue("");
                            }
                            // 设置样式
                            if (i < methods.size() + values.size()) {
                                dataRow.getCell(i).setCellStyle(cellStyle);
                            }
                        }
                    }
                }
                if ("getFlexibleDateMap".equals(methods1[j].getName())) {
                    Map<String, Object> flexibleDateMap = (Map<String, Object>) methods1[j].invoke(object);
                    if (flexibleDateMap != null) {
                        List<Object> values = new ArrayList<>();
                        for (int i = 0; i < dateTitles.size(); i++) {
                            values.add(flexibleDateMap.get(dateTitles.get(i)));
                        }
                        for (int i = methods.size(); i < methods.size() + values.size(); i++) {
                            int k = i - methods.size();
                            if (values.get(k) != null) {
                                dataRow.createCell(i).setCellValue(values.get(k).toString());
                            } else {
                                dataRow.createCell(i).setCellValue("");
                            }
                            // 设置样式
                            if (i < methods.size() + values.size()) {
                                dataRow.getCell(i).setCellStyle(cellStyle);
                            }
                        }
                    }
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, 7000);
            }
        }
        return sxssfWorkbook;
    }

    /**
     填充excel中的内容
     */
    public static <T> SXSSFWorkbook setWorkbook(List<String> sheetNameList, Class<?> clazz, Map<String, List<T>> sheetNameDataListMap, Map<String, List<String>> dateTitleListMap) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        for (String sheetName : sheetNameList) {
            // 创建sheet页
            SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
            List<T> list = sheetNameDataListMap.get(sheetName);
            // 创建表头
            SXSSFRow headRow = sheet.createRow(0);
            List<String> billInvoiceTitleList = dateTitleListMap.get("billInvoiceTitleList");
            List<String> billCheckTitleList = dateTitleListMap.get("billCheckTitleList");
            Map<String, Object> reflectInfos = getReflectInfo(clazz);
            List<String> titles = (List<String>) reflectInfos.get(TITLES);
            titles.addAll(billInvoiceTitleList);
            titles.addAll(billCheckTitleList);
            List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
            List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
            CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
            CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
            CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
            // 表头
            for (int i = 0; i < titles.size(); i++) {
                headRow.createCell(i).setCellValue(titles.get(i));
                if (i < titles.size()) {
                    headRow.getCell(i).setCellStyle(headCellStyle);
                }
                headRow.setHeightInPoints(30);
            }
            Object obj = null;
            for (int i = 0; i < list.size(); i++) {
                // 填充数据
                SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
                for (int j = 0; j < methods.size(); j++) {
                    obj = methods.get(j).invoke(list.get(i));
                    if (methods.get(j).getName().equals("getNo")) {
                        dataRow.createCell(j).setCellValue(i + 1);
                    } else if (methods.get(j).getReturnType().equals(java.math.BigDecimal.class)) {
                        if (obj != null) {
                            String string = obj.toString();
                            BigDecimal bigDecimal = new BigDecimal(string);
                            dataRow.createCell(j).setCellValue(bigDecimal.doubleValue());
                        } else {
                            dataRow.createCell(j).setCellValue("");
                        }

                    } else {
                        if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                            dataRow.createCell(j).setCellValue(obj.toString());
                        } else {
                            dataRow.createCell(j).setCellValue("");
                        }
                    }
                    // 设置样式
                    if (j < methods.size()) {
                        dataRow.getCell(j).setCellStyle(cellStyle);
                    }
                    // 如果该单元格为数据确认过程和离职确认过程 开启自动换行
                    if ("getEntryProcess".equalsIgnoreCase(methods.get(j).getName()) || "getDimissionProcess".equalsIgnoreCase(methods.get(j).getName())) {
                        dataRow.getCell(j).setCellStyle(cellTypeWrapText);
                    }
                    // 设置行高
                    dataRow.setHeightInPoints(15);
                    // 设置列宽
                    sheet.setColumnWidth(j, columnWidths.get(j));
                }
                Method[] methods1 = list.get(i).getClass().getMethods();
                for (int j = 0; j < methods1.length; j++) {
                    if ("getBillInvoiceMapMap".equals(methods1[j].getName())) {
                        for (int l = methods.size(); l < methods.size() + billInvoiceTitleList.size(); l++) {
                            // 设置样式
                            if (l < methods.size() + billInvoiceTitleList.size()) {
                                dataRow.createCell(l).setCellStyle(cellStyle);
                            }
                        }
                        Map<Integer, Map<String, String>> billInvoiceMapMap = (Map<Integer, Map<String, String>>) methods1[j].invoke(list.get(i));
                        if (billInvoiceMapMap != null) {
                            List<String> values = new ArrayList<>();
                            for (int k = 0; k < billInvoiceMapMap.size(); k++) {
                                Map<String, String> billInvoiceMap = billInvoiceMapMap.get(k);
                                Set set = billInvoiceMap.keySet();
                                Object[] arr = set.toArray();
                                Arrays.sort(arr);
                                for (Object key : arr) {
                                    values.add(billInvoiceMap.get(key));
                                }
                            }
                            for (int l = methods.size(); l < methods.size() + values.size(); l++) {
                                int k = l - methods.size();
                                String value = values.get(k);
                                if (StringUtils.isNotEmpty(value)) {
                                    dataRow.getCell(l).setCellValue(value);
                                } else {
                                    dataRow.getCell(l).setCellValue("");
                                }
                            }
                        }
                    }
                    if ("getBillCheckMapMap".equals(methods1[j].getName())) {
                        int length = methods.size() + billInvoiceTitleList.size();
                        for (int l = length; l < length + billCheckTitleList.size(); l++) {
                            // 设置样式
                            if (l < length + billCheckTitleList.size()) {
                                dataRow.createCell(l).setCellStyle(cellStyle);
                            }
                        }
                        Map<Integer, Map<String, String>> billCheckMapMap = (Map<Integer, Map<String, String>>) methods1[j].invoke(list.get(i));
                        if (billCheckMapMap != null) {
                            List<String> values = new ArrayList<>();
                            for (int k = 0; k < billCheckMapMap.size(); k++) {
                                Map<String, String> billCheckMap = billCheckMapMap.get(k);
                                Set set = billCheckMap.keySet();
                                Object[] arr = set.toArray();
                                Arrays.sort(arr);
                                for (Object key : arr) {
                                    values.add(billCheckMap.get(key));
                                }
                            }
                            for (int l = length; l < length + values.size(); l++) {
                                int k = l - length;
                                String value = values.get(k);
                                if (StringUtils.isNotEmpty(value)) {
                                    dataRow.getCell(l).setCellValue(value);
                                } else {
                                    dataRow.getCell(l).setCellValue("");
                                }
                            }
                        }
                    }
                    // 设置行高
                    dataRow.setHeightInPoints(15);
                    // 设置列宽
                    sheet.setColumnWidth(j, 7000);
                }
            }
        }
        return sxssfWorkbook;
    }

    public static <T> SXSSFWorkbook setWorkbook(List<T> list, Class<?> clazz, String sheetName, Map<Integer, Integer> rowAndReasonMap) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
        CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
        CellStyle errorCellStyle = getErrorCellStyle(sxssfWorkbook, false, false);
        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) {
                headRow.getCell(i).setCellStyle(headCellStyle);
            }
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        for (int i = 0; i < list.size(); i++) {
            T object = list.get(i);
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if (methods.get(j).getName().equals("getNo")) {
                    dataRow.createCell(j).setCellValue(i + 1);
                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }
                }
                // 设置样式
                if (j < methods.size()) {
                    if (rowAndReasonMap.get(i).equals(1)) {
                        dataRow.getCell(j).setCellStyle(errorCellStyle);
                    } else {
                        dataRow.getCell(j).setCellStyle(cellStyle);
                    }
                }
                // 如果该单元格为数据确认过程和离职确认过程 开启自动换行
                if ("getEntryProcess".equalsIgnoreCase(methods.get(j).getName()) || "getDimissionProcess".equalsIgnoreCase(methods.get(j).getName())) {
                    dataRow.getCell(j).setCellStyle(cellTypeWrapText);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);

                // 设置列宽
                sheet.setColumnWidth(j, columnWidths.get(j));
            }
        }
        return sxssfWorkbook;
    }

    /**
     根据表头动态填充excel中的内容
     */
    public static <T> SXSSFWorkbook setWorkbook(List<T> list, Class<?> clazz, String sheetName, Set<String> dateTitles) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        // 创建poi导出数据对象0
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        // 创建sheet页
        SXSSFSheet sheet = sxssfWorkbook.createSheet(sheetName);
        // 创建表头
        SXSSFRow headRow = sheet.createRow(0);
        Map<String, Object> reflectInfos = getReflectInfo(clazz);
        List<String> titles = (List<String>) reflectInfos.get(TITLES);
        if (CollectionUtils.isNotEmpty(dateTitles)) {
            titles.addAll(dateTitles);
        }

        adjustColumnOrder(titles, "住房公积金最大收费截止月", "补充住房公积金最大收费截止月");

        List<Method> methods = (List<Method>) reflectInfos.get(METHODS);
        List<Integer> columnWidths = (List<Integer>) reflectInfos.get(COLUMN_WIDTHS);
        CellStyle headCellStyle = ExcelUtil.getCellStyle(sxssfWorkbook, false, true);
        CellStyle cellStyle = getCellStyle(sxssfWorkbook, false, false);
        CellStyle cellTypeWrapText = getCellStyle(sxssfWorkbook, true, false);
        // 表头
        for (int i = 0; i < titles.size(); i++) {
            headRow.createCell(i).setCellValue(titles.get(i));
            if (i < titles.size()) {
                headRow.getCell(i).setCellStyle(headCellStyle);
            }
            headRow.setHeightInPoints(30);
        }
        Object obj = null;
        for (T object : list) {
            // 填充数据
            SXSSFRow dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int j = 0; j < methods.size(); j++) {
                obj = methods.get(j).invoke(object);
                if ("getNo".equals(methods.get(j).getName())) {
                    dataRow.createCell(j).setCellValue(list.indexOf(object) + 1);
                } else {
                    if (obj != null && StringUtils.isNotEmpty(obj.toString())) {
                        dataRow.createCell(j).setCellValue(obj.toString());
                    } else {
                        dataRow.createCell(j).setCellValue("");
                    }
                }
                // 设置样式
                if (j < methods.size()) {
                    dataRow.getCell(j).setCellStyle(cellStyle);
                }
                // 如果该单元格为数据确认过程和离职确认过程 开启自动换行
                if ("getEntryProcess".equalsIgnoreCase(methods.get(j).getName()) || "getDimissionProcess".equalsIgnoreCase(methods.get(j).getName())) {
                    dataRow.getCell(j).setCellStyle(cellTypeWrapText);
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, columnWidths.get(j));
            }
            Method[] methods1 = object.getClass().getMethods();
            for (int j = 0; j < methods1.length; j++) {
                if ("getFlexibleDateMap".equals(methods1[j].getName())) {
                    Map<String, Object> flexibleDateMap = (Map<String, Object>) methods1[j].invoke(object);
                    if (flexibleDateMap != null) {
                        List<Object> values = new ArrayList<>();
                        for (String dateTitle : dateTitles) {
                            values.add(flexibleDateMap.get(dateTitle));
                        }
                        for (int i = methods.size(); i < methods.size() + values.size(); i++) {
                            int k = i - methods.size();
                            if (values.get(k) != null) {
                                dataRow.createCell(i).setCellValue(values.get(k).toString());
                            } else {
                                dataRow.createCell(i).setCellValue("");
                            }
                            // 设置样式
                            if (i < methods.size() + values.size()) {
                                dataRow.getCell(i).setCellStyle(cellStyle);
                            }
                        }
                    }
                }
                // 设置行高
                dataRow.setHeightInPoints(15);
                // 设置列宽
                sheet.setColumnWidth(j, 7000);
            }
        }
        return sxssfWorkbook;
    }

    public static void adjustColumnOrder(List<String> columns, String targetColumn, String referenceColumn) {
        if (columns.contains(targetColumn) && columns.contains(referenceColumn)) {

            int targetIndex = columns.indexOf(targetColumn);
            int referenceIndex = columns.indexOf(referenceColumn);

            if (targetIndex > referenceIndex) {
                columns.remove(targetIndex);
                columns.add(referenceIndex, targetColumn);
            }
        }


    }
}
