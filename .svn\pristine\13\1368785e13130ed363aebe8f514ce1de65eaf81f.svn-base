package com.reon.hr.sp.customer.dubbo.service.rpc.impl.supplierPractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.supplierPractice.ISupplierOrderChangeSyncWrapperService;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierPracticeSyncVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierPracticeVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierSyncCfgVo;
import com.reon.hr.sp.customer.service.supplierPractice.SupplierOrderChangeSyncService;
import com.reon.hr.sp.customer.service.supplierPractice.SupplierSyncCfgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年05月22日
 * @Version 1.0
 */

@Service("iSupplierOrderChangeSyncWrapperService")
public class ISupplierOrderChangeSyncWrapperServiceImpl implements ISupplierOrderChangeSyncWrapperService {

    @Autowired
    private SupplierOrderChangeSyncService supplierOrderChangeSyncService;

    @Autowired
    private SupplierSyncCfgService supplierSyncCfgService;
    @Override
    public Page<SupplierPracticeSyncVo> getSupplierOrderChangeSyncList(Integer limit, Integer page, SupplierPracticeVo supplierPracticeVo) {
        return supplierOrderChangeSyncService.getSupplierOrderChangeSyncList(limit, page, supplierPracticeVo);
    }

    @Override
    public Page<SupplierSyncCfgVo> getSupplierSyncCfgVoPage(SupplierSyncCfgVo vo, Integer page, Integer limit) {
        return supplierSyncCfgService.getSupplierSyncCfgVoPage(vo,page,limit);
    }

    @Override
    public Integer getSupplierSyncCfgVoCount(SupplierSyncCfgVo vo) {
        return supplierSyncCfgService.getSupplierSyncCfgVoCount(vo);
    }

    @Override
    public void deleteSupplierSyncCfgByIds(List<Long> ids) {
        supplierSyncCfgService.deleteSupplierSyncCfgByIds(ids);
    }

    @Override
    public void addSupplierSyncCfgVo(SupplierSyncCfgVo supplierSyncCfgVo) {
        supplierSyncCfgService.addSupplierSyncCfgVo(supplierSyncCfgVo);
    }

    @Override
    public void editSupplierSyncCfgVo(SupplierSyncCfgVo supplierSyncCfgVo) {
        supplierSyncCfgService.editSupplierSyncCfgVo(supplierSyncCfgVo);
    }

    @Override
    public int selectCountByOrder(String orderNo) {
        return supplierOrderChangeSyncService.selectCountByOrder(orderNo);
    }
}
