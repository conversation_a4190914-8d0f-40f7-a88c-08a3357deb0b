<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.employee.EmployeeTransferCfgMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.employee.EmpOrderTransferInfo">
    </resultMap>
    <sql id="baseColumn">
        chg_no,chg_name,recceiving_type,old_contract_area_no,new_contract_area_no,new_quote_no,effective_month
            ,status,failure_reason,remark,creator,create_time,updater,update_time,del_flag
    </sql>

    <insert id="insertTransferCfgs">
        insert into `employee_order_transfer_cfg`
        (chg_no,item_code,bill_start_month,tax_included_amount,tax_exclude_amount,templet_id,revTemp_id,creator,create_time,updater)
        <foreach collection="list" item="item" open="values" separator=",">
            (#{item.chgNo},#{item.itemCode},#{item.billStartMonth},#{item.taxIncludedAmount},
            #{item.taxExcludeAmount},#{item.templetId},#{item.revTempId},#{item.creator},#{item.createTime},#{item.updater})
        </foreach>
    </insert>


    <select id="selectEmpOrderTransferCfgByChgNo"
            resultType="com.reon.hr.api.customer.vo.employee.EmployeeOrderTransferCfgVo">
        select
            chg_no,item_code,bill_start_month,tax_included_amount,tax_exclude_amount,templet_id,revTemp_id,creator,create_time,updater
        from employee_order_transfer_cfg where chg_no = #{chgNo}
    </select>

</mapper>
