/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/11/2
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.cmb;



import com.reon.hr.common.uitl.CMBUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReqInfoBuilder
 * @description TODO
 * @date 2023/11/2 10:39
 */
public abstract class ReqInfoBuilder {


    private String uId;

    public ReqInfoBuilder (String uId){
        this.uId = uId;
    }

    public ReqInfo buildReqInfo(){
        ReqInfo reqInfo = new ReqInfo();
        Request request = new Request();
        ReqHead head = new ReqHead(getFunCode(),getuId(), CMBUtils.getReqIdByTime());
        request.setHead(head);
        request.setExthdr(DeviceInfo.getSystemInfo());
        request.setBody(getBodyMap());
        reqInfo.setRequest(request);
        reqInfo.setSignature(new Signature(CMBUtils.getCurrentTime()));
        return reqInfo;
    }




    public abstract  Map<String,Object> getBodyMap();

    public abstract String getFunCode();

    private String getuId() {
        return this.uId;
    }
}
