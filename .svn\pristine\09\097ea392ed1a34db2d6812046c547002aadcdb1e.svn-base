package com.reon.hr.sp.base.listener;

import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.context.MqContext;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.base.ConsumerScopeTypeBase;
import com.reon.hr.sp.base.service.sys.IInsuranceBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.base.listener
 * @ClassName: InsuranceBaseEditListener
 * @Author: Administrator
 * @Description:
 * @Date: 2023/7/26 11:18
 * @Version: 1.0
 */
@Component
public class InsuranceBaseEditListener extends AbstractConsumerListener {
    @Resource
    private MqContext mqContext;

    private final static String QUEUE_NAME = "insurance_base_edit.queue";

    @Autowired
    private IInsuranceBaseService insuranceBaseService;

    @Override
    protected void doWork(String message) {
        insuranceBaseService.handleBatchEditHighAndLow(message);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        super.init(ModuleType.REON_BASE, ConsumerScopeTypeBase.REON_BASE_INSURANCE_BASE_EDIT, mqContext, QUEUE_NAME);
    }
}
