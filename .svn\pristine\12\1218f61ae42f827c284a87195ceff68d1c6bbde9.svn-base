package com.reon.ehr.api.sys.vo.base;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class EhrImportDataDto<T> implements Serializable {

    private String remark;

    private String importNo;

    private String loginName;
    private Long custId;

    private String fileId;

    private List<T> dataList = new ArrayList<>();


    //解析第一次导入日志数据
    private List<EhrImportDataLogVo> importDataLogVoList = Lists.newArrayList ();

    //导入成功记录数
    private Integer successNum = 0;
    //导入失败记录数
    private Integer failNum = 0;

    public List<EhrImportDataLogVo> getImportDataLogVoList() {
        return importDataLogVoList;
    }

    public void setImportDataLogVoList(List<EhrImportDataLogVo> importDataLogVoList) {
        this.importDataLogVoList = importDataLogVoList;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getFailNum() {
        return failNum;
    }

    public void setFailNum(Integer failNum) {
        this.failNum = failNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getImportNo() {
        return importNo;
    }

    public void setImportNo(String importNo) {
        this.importNo = importNo;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    // 更新错误数据
    private Map<Integer,String> errorDesc = Maps.newHashMap ();

    public Map<Integer, String> getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(Map<Integer, String> errorDesc) {
        this.errorDesc = errorDesc;
    }

    public void recordError(Integer rowNum,String errorMsgJson){
        if (!errorDesc.containsKey(rowNum)){
            successNum--;
            failNum++;
        }
        errorDesc.put (rowNum,errorMsgJson);
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }
}
