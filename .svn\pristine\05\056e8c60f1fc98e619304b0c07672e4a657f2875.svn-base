package com.reon.hr.sp.service.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.CompServiceCityVo;
import com.reon.hr.api.vo.sys.OrgVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OrganizationInnerService {

    /**
     * 查找所有的组织机构
     *
     * @param orgVo
     * @return
     */
    List<OrgVo> findAllOrg(OrgVo orgVo);

    boolean saveOrUpdateOrg(OrgVo organization) throws Exception;

    String findMaxCode(List<String> orgTypeList, String pcode);

    OrgVo searchMaxCode(OrgVo orgVo);

    List<OrgVo> findAllCompany();
    List<OrgVo> findAllOpenedCompany();

    Page<OrgVo> getCompanyByName(Integer page, Integer limit, String name);

    /**
     * 根据城市编码，机构类型获取机构
     *
     * @param cityCode
     * @param orgType
     * @return
     */
    OrgVo findOrgByOwerCityAndOrgType(String cityCode, String orgType);


    /**
     * 根据orCode获取机构信息
     *
     * @param orCode
     * @return
     */
    OrgVo findOrgById(String orCode);

    List<String> getCountByOrgName(String orgName);

    List<OrgVo> getCompanyByNameAndCityCode(String keyword, Integer cityCode);

    Page<OrgVo> getRegionCompanyByName(Integer page, Integer limit, String name, String loginName);
    List<OrgVo> findAllOrgName();

    List<OrgVo> getCompanyByName();

    /**
     * 根据orgType查找orgCode、orgName
     * @param orgType
     * @return
     */
    List<OrgVo> findRegionByOrgType(String orgType);

    List<OrgVo> findOrgByOwerCityListAndOrgType(List<Integer> cityCodeList, String orgType);

    List<OrgVo> getOrgListByOrgCodeList(List<String> orgCodeList);

	Map<String, OrgVo> getAllCompanyToMap();

	void updateCompanyStructure(String source, String target, String afterIndex);

	Map<String, List<OrgVo>> getAllDepartmentByCompanyReturnMap();

    int getOrgPosNumByOrgCode(String target);

	List<OrgVo> findAllDepartment();

	int updateSyncDepartment(String source, String target);

    List<OrgVo> findAllOrgCity(OrgVo orgVo);
    List<OrgVo> findAllOpenedOrgCity(OrgVo orgVo);

    int getCountByOrgCode(String orgCode);

    String  getAllCityName(Long owerCity);

    List<OrgVo> findRegionCompanyByName(String name, String orgCode);

    List<OrgVo> getCompServiceCityVoByArgs(CompServiceCityVo args);


    List<CommonUserVo> getLaterServiceList(String orgCode);


    List<Integer> selectCityCodeByParentCode(Integer parentCode);

    Set<String> getNorthRegionCompanyOrgCode();


}
