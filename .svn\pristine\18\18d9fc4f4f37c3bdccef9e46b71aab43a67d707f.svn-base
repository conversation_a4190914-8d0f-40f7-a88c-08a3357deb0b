package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.customer.dto.templetChange.ChangeTempletDto;
import com.reon.hr.api.customer.dto.templetChange.ChangeTempletServiceMonthDto;
import com.reon.hr.api.customer.vo.ChangeTempletOrderVo;
import com.reon.hr.sp.customer.entity.cus.ChangeTempletOrder;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ChangeTempletOrderService extends IService<ChangeTempletOrder> {

    int insertOrUpdateSelective(ChangeTempletOrderVo record);

    ChangeTempletOrderVo getChangeTempletOrderVoByChgNo(String chgNo);

	Map<String, List<ChangeTempletDto>> getChangeTempletByOrderNos(List<String> orderNos, Integer status);

	Map<String, List<ChangeTempletServiceMonthDto>> getChangeTempletByTempletId(Set<String> orderNos, Integer billMonth, Map<String, Map<Integer, Map<String, Integer>>> receiveMonthByOrderNoMap ) throws Exception;

	Set<String> getAllOrderNosAndTempletId(Long templetId);
	List<ChangeTempletDto> getChangeTempletByOrderNosAndStatus(List<String> orderNos,Integer status);
    int updateBatchByIds(List<Long> ids, Integer status);
}
