<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.reon.hr.sp.bill.dao.insurancePractice.PackAmtDiffMonthlyMapper">



    <update id="updatePackAmtDiffMonthlyVo">
        update `reon-billdb`.pack_amt_diff_monthly set diff_amt = #{diffAmt},updater = #{updater} where pay_apply_id = #{payApplyId}
    </update>


    <delete id="deletePackAmtDiffMonthlyVo">
        delete from `reon-billdb`.pack_amt_diff_monthly where pay_apply_id = #{payId}
    </delete>
</mapper>