<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:reg="http://www.dangdang.com/schema/ddframe/reg" xmlns:job="http://www.dangdang.com/schema/ddframe/job"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.dangdang.com/schema/ddframe/reg
                        http://www.dangdang.com/schema/ddframe/reg/reg.xsd
                        http://www.dangdang.com/schema/ddframe/job
                        http://www.dangdang.com/schema/ddframe/job/job.xsd">
	<!--配置作业注册中心 -->
	<reg:zookeeper id="regCenter" server-lists="${elastic.job.zookeeperUrl}" namespace="reon-customer-job"
		base-sleep-time-milliseconds="1000" max-sleep-time-milliseconds="3000" max-retries="3" />

 	<job:simple id="contractAssignJob" class="com.reon.hr.sp.customer.job.customer.ContractAssignJob" registry-center-ref="regCenter"
		cron="0 40 23 * * ? *" description="分配客户Job" sharding-total-count="1" overwrite="true" />

	<job:simple id="customerSolutionUpgradeJob" class="com.reon.hr.sp.customer.job.customer.CustomerSolutionUpgradeJob" registry-center-ref="regCenter"
				cron="0 40 23 * * ? *" description="商保方案升级Job" sharding-total-count="1" overwrite="true" />

	<job:simple id="templetComparisonJob" class="com.reon.hr.sp.customer.job.customer.TempletComparisonJob" registry-center-ref="regCenter"
				cron="0 0 3 * * ? *" description="订单模板对比Job" sharding-total-count="1" overwrite="true" />

	<job:simple id="orderDataCheckRecordJob" class="com.reon.hr.sp.customer.job.customer.OrderDataCheckRecordJob" registry-center-ref="regCenter"
				cron="0 0 1 * * ?" description="订单数据检查记录表Job" sharding-total-count="1" overwrite="true" />
	<job:simple id="quotationChgTaskJob" class="com.reon.hr.sp.customer.job.customer.QuotationChgTaskJob"
				registry-center-ref="regCenter"
				cron="0 0 11 * * ?" description="续签自动更新合同表默认报价单Job" sharding-total-count="1" overwrite="true"/>
</beans>
