<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.SupplierContractMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.cus.SupplierContract">

  </resultMap>
  <resultMap id="SupplierContractVo" type="com.reon.hr.api.customer.vo.supplier.SupplierContractVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_id" jdbcType="BIGINT" property="supplierId" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="prod_type" jdbcType="INTEGER" property="prodType" />
    <result column="fee" jdbcType="DECIMAL" property="fee" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="bill_date" jdbcType="SMALLINT" property="billDate" />
    <result column="pay_date" jdbcType="SMALLINT" property="payDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, supplier_id, contract_type, prod_type, fee, start_time, end_time, bill_date,org_code,
    pay_date, status, pid, remark, creator, create_time, updater, update_time,contract_no,contract_name,creator_pos,creator_org
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.reon.hr.sp.customer.entity.cus.SupplierContract">
    insert into supplier_contract (id, supplier_id, contract_type, 
      prod_type, fee, start_time, 
      end_time, bill_date, pay_date, 
      status, pid, remark, 
      creator, create_time, updater, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{supplierId,jdbcType=BIGINT}, #{contractType,jdbcType=INTEGER}, 
      #{prodType,jdbcType=INTEGER}, #{fee,jdbcType=DECIMAL}, #{startTime,jdbcType=DATE},
      #{endTime,jdbcType=DATE}, #{billDate,jdbcType=SMALLINT}, #{payDate,jdbcType=SMALLINT},
      #{status,jdbcType=INTEGER}, #{pid,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.customer.entity.cus.SupplierContract" useGeneratedKeys="true" keyProperty="id">
    insert into supplier_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">

      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="prodType != null">
        prod_type,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="prodType != null">
        #{prodType,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=SMALLINT},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
    </trim>
    <selectKey resultType="long" order="AFTER" keyProperty="id">
      select LAST_INSERT_ID() AS id
    </selectKey>
  </insert>


  <update id="updateEndTimeById">
    update supplier_contract set end_time =#{endTime} where id=#{id}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.customer.entity.cus.SupplierContract">
    update supplier_contract
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="prodType != null">
        prod_type = #{prodType,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        fee = #{fee,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=SMALLINT},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.customer.entity.cus.SupplierContract">
    update supplier_contract
    set supplier_id = #{supplierId,jdbcType=BIGINT},
      contract_type = #{contractType,jdbcType=INTEGER},
      prod_type = #{prodType,jdbcType=INTEGER},
      fee = #{fee,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=DATE},
      end_time = #{endTime,jdbcType=DATE},
      bill_date = #{billDate,jdbcType=SMALLINT},
      pay_date = #{payDate,jdbcType=SMALLINT},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateSupplierContractNewVoById" parameterType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    UPDATE supplier_contract
    <set>

      <if test="null != contractType and '' != contractType">contract_type = #{contractType},</if>
      <if test="null != prodType and '' != prodType">prod_type = #{prodType},</if>
      <if test="  startTime!= null ">start_time = #{startTime},</if>
      <if test=" endTime!= null ">end_time = #{endTime},</if>
      <if test=" billDate!= null">bill_date = #{billDate},</if>
      <if test=" payDate!= null ">pay_date = #{payDate},</if>
      <if test="null != remark and '' != remark">remark = #{remark},</if>
      <if test="null != updater and '' != updater">updater = #{updater},</if>
      <if test="null != updateTime ">update_time = #{updateTime},</if>
      <if test="null != orgCode and '' != orgCode">org_code = #{orgCode},</if>
      <if test="null != contractName and '' != contractName">contract_name = #{contractName},</if>
      <if test="null != relatedCompFlag and '' != relatedCompFlag">related_comp_flag = #{relatedCompFlag},</if>
      <if test="null != postponeFlag and '' != postponeFlag">postpone_flag = #{postponeFlag}</if>
    </set>
    WHERE id = #{id}
  </update>

  <insert id="saveSupplierContractNewVo" parameterType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo" useGeneratedKeys="true" keyProperty="id">
    insert into supplier_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">

      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="prodType != null">
        prod_type,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>

      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="creatorPos != null">
        creator_pos,
      </if>
      <if test="creatorOrg != null">
        creator_org,
      </if>
      <if test="postponeFlag != null">
        postpone_flag,
      </if>
      <if test="relatedCompFlag != null">
        related_comp_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="prodType != null">
        #{prodType,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=SMALLINT},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=SMALLINT},
      </if>

      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="creatorPos != null">
        #{creatorPos,jdbcType=VARCHAR},
      </if>
      <if test="creatorOrg != null">
        #{creatorOrg,jdbcType=VARCHAR},
      </if>
      <if test="postponeFlag != null">
        #{postponeFlag,jdbcType=INTEGER},
      </if>
      <if test="relatedCompFlag != null">
        #{relatedCompFlag,jdbcType=INTEGER},
      </if>
    </trim>
    <selectKey resultType="long" order="AFTER" keyProperty="id">
      select LAST_INSERT_ID() AS id
    </selectKey>
  </insert>

  <select id="getContractBySupplierId" parameterType="Long" resultMap="SupplierContractVo">
    select <include refid="Base_Column_List"/>
    from supplier_contract
    where  id = (select max(id) from supplier_contract where supplier_id =#{supplierId})
  </select>
  <select id="getMaxContractBySupplierId" parameterType="java.lang.Long" resultMap="SupplierContractVo">
    select
    <include refid="Base_Column_List" />
    from supplier_contract
    where  id = (select max(id) from supplier_contract where supplier_id =#{supplierId})
  </select>

  <select id="getSupplierContractById" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select
    <include refid="Base_Column_List"/>
    from supplier_contract
    where supplier_id =
    #{supplierId}

  </select>

  <select id="getSupplierContractByPid" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select sc.id,
    sc.supplier_id,
    sc.contract_no,
    sp.creator,
    sp.start_date as startTimeD,
    sp.approval_status,
    sp.end_date as endTimeD,
    sp.pid,
    s.supplier_name
    from supplier_contract sc
    left join supplier s on s.id = sc.supplier_id left join supplier_contract_process sp on sp.contract_no =
    sc.contract_no
    where sp.current_flag =1 and  sp.proc_type =#{procType}
    <if test="supplierId!=null">
      and sc.supplier_id =
      #{supplierId}
    </if>
    <if test="processInsList != null and processInsList.size() > 0">
      and sp.pid in
      <foreach collection="processInsList" item="item" open="(" close=")" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    order by sc.create_time DESC
  </select>

  <select id="getSupplierContractIdByPid" resultType="java.lang.Long">
    select sc.id
    from supplier_contract sc
           left join supplier_contract_process sp on sc.contract_no = sp.contract_no
    where  sp.pid =#{pid} and sp.proc_type =#{procType} and sp.current_flag =1
  </select>

  <update id="updateStatusByPid" >
    update supplier_contract set status =#{status},update_time=now() where pid=#{pid}

  </update>
  <update id="updateStatusById" >
    update supplier_contract set status =#{status},update_time=now() where id=#{contractId}

  </update>

  <select id="getSupplierVoBySupplierContractId"
          resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select s.supplier_name,
           s.supplier_type,
           sc.id,
           sc.contract_no,
           sc.remark,
           sc.org_code,
           sc.contract_name,
           sp.start_date as startTime,
           sp.end_date   as endTime,
           sp.approval_status,
           sp.pid,
           sp.proc_type,
           sc.bill_date,
           sc.pay_date,
           sc.prod_type,
           sc.contract_type,
           sc.postpone_flag,
           sc.related_comp_flag
    from supplier_contract sc
           left join supplier_contract_process sp on sc.contract_no = sp.contract_no
           left join supplier s on s.id = sc.supplier_id
    where proc_type = #{procType}
      and current_flag = 1
      and sc.id = #{id}
  </select>
    <select id="selectSupplierContractPage" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
      select t.id,
      t.supplier_id,
      t.contract_type,
      t.prod_type,
      sp.proc_type,
      t.start_time,
      t.end_time,
      t.bill_date,
      t.pay_date,
      sp.approval_status,
      sp.pid,
      t.remark,
      t.org_code,
      t.contract_no,
      t.contract_name,
      s.supplier_type,
      s.supplier_name as supplierName,
      s.status as supplierStatus,
      sp.creator,
      sp.create_time,
      t.related_comp_flag,
      t.postpone_flag
      from supplier_contract t
      left join supplier s on t.supplier_id =s.id
      left join supplier_contract_process sp on sp.contract_no=t.contract_no
      where (proc_type =1 or ((proc_type =2 or proc_type=3) and sp.approval_status != 5 and sp.current_flag =1))
      <if test="vo.supplierId!=null and vo.supplierId !=''">
        and s.id =#{vo.supplierId}
      </if>
      <if test="vo.contractName!=null and vo.contractName!=''">
        and t.contract_name like concat('%',trim(#{vo.contractName}),'%')
      </if>
      <if test="vo.contractNo!=null and vo.contractNo!=''">
        and t.contract_no like concat('%',trim(#{vo.contractNo}),'%')
      </if>
      <if test="vo.orgCode!=null and vo.orgCode!=''">
        and t.org_code = #{vo.orgCode}
      </if>
      <if test="vo.supplierType!=null and vo.supplierType!=''">
        and s.supplier_type =#{vo.supplierType}
      </if>
      <if test="vo.approvalStatus!=null and vo.approvalStatus!=''">
        <if test="vo.approvalStatus==5">
          and sp.approval_status in (4,5)
        </if>
        <if test="vo.approvalStatus!=5">
          and sp.approval_status =#{vo.approvalStatus}
        </if>
      </if>
      <if test="vo.startTime!=null and vo.startTime!=''">
        and t.start_time <![CDATA[>=]]>#{vo.startTime}
      </if>
      <if test="vo.endTime!=null and vo.endTime!=''">
        and t.end_time <![CDATA[<=]]>#{vo.endTimeS}
      </if>

      and s.del_flag ='N'
      order by sp.create_time desc
  </select>

  <insert id="saveNoSupplierContractAndProduct" parameterType="com.reon.hr.sp.customer.entity.cus.SupplierContract" useGeneratedKeys="true" keyProperty="id">
    insert into supplier_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="prodType != null">
        prod_type,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="prodType != null">
        #{prodType,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=SMALLINT},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateSupplierContractProdTypeById">
    update supplier_contract set prod_type =#{prodType} where id =#{id}
  </update>

  <update id="editStatus">
 update supplier_contract
    set
      status = #{status,jdbcType=INTEGER},
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getContractBySupplierIds" resultType="com.reon.hr.sp.customer.entity.cus.SupplierContract">
    select
    <include refid="Base_Column_List" />
    from supplier_contract
    where  supplier_id in
    <foreach collection="supplierIds" item="supplierId" open="(" close=")" separator=",">
        #{supplierId}
    </foreach>
  </select>

  <select id="getContractNoIsNull"  resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select s.supplier_type, sc.id, sc.create_time, s.supplier_name, s.supplier_type, sc.prod_type
    from supplier_contract sc
           left join supplier s on s.id = sc.supplier_id
    where sc.contract_no is null
  </select>

  <update id="generateSupplierContractNo">
    update supplier_contract set contract_no =#{contractNo},contract_name=#{contractName} where id =#{id}
  </update>

  <select id="getSupplierQuotationVo"  resultType="com.reon.hr.api.customer.vo.supplier.SupplierQuotationVo">
    select sc.id,

           concat(sa.province_code, sa.city_code) as cityCode,
           sa.insurance_fee,
           sa.salary_fee,
           sc.supplier_id,
           s.supplier_name
    from supplier_contract sc
           left join supplier_area sa on sa.supplier_id = sc.supplier_id
           left join supplier s on s.id = sc.supplier_id
    where
      sa.salary_fee != 0 or sa.insurance_fee != 0


  </select>

  <select id="getSupplierContractCountBySupplierId" resultType="int">
    select count(1) from supplier_contract where supplier_id in
    <foreach collection="idList" item="supplierId" open="(" close=")" separator=",">
      #{supplierId}
    </foreach>
  </select>

  <select id="getSupplierContractNewVoById" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select sc.*,s.supplier_type
    from supplier_contract sc  left join supplier s on sc.supplier_id=s.id
    where sc.id =  #{id}
  </select>

  <select id="getSupplierContractByContractNo" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select * from supplier_contract where contract_no =#{contractNo}
  </select>

  <select id="getSupplierContractNewVoByIdAndProcType" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select sc.id, sp.pid,sc.supplier_id,sp.start_date as startTime,sp.end_date as endTime,sc.contract_no
    from supplier_contract sc
           left join supplier_contract_process sp on sp.contract_no = sc.contract_no
    where sc.id =#{id}
      and sp.proc_type =#{procType}
      and sp.current_flag = 1
  </select>

  <update id="autoContinueSupplierContract">

    update supplier_contract
    set
      end_time = DATE_ADD(end_time,INTERVAL 1 YEAR)
    where
      id =#{id}

  </update>

  <select id="getAllData" resultType="com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo">
    select * from supplier_contract where  PERIOD_DIFF(DATE_FORMAT(end_time,'%Y%m%d'),DATE_FORMAT(CURDATE(),'%Y%m%d')) between 0 and 15;
  </select>



</mapper>