package com.reon.hr.api.bill.vo.supplierPractice;

import com.baomidou.mybatisplus.annotations.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.api.bill.vo.supplierPractice
 * @ClassName: SupplierNonMonthlyProdVo
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/8 15:00
 * @Version: 1.0
 */
@Data
public class SupplierNonMonthlyProdVo implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 实做ID
     */
    private Long practiceId;
    /**
     * 账单ID
     */
    private Long billId;
    /**
     * 账单月
     */
    private Integer billMonth;
    /**
     * 产品类型
     */
    private Integer prodCode;
    /**
     * 社保比例code
     */
    private String insuranceCode;
    /**
     * 企业金额
     */
    private BigDecimal comAmt;
    /**
     * 个人金额
     */
    private BigDecimal indAmt;
    /**
     * 服务月
     */
    private Integer receivableMonth;
    /**
     * 账单类型(1:正常账单，2:补差账单)
     */
    private Integer billType;
    /**
     * 所属期（年缴样式为eg:2022）
     */
    private Integer period;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;
}
