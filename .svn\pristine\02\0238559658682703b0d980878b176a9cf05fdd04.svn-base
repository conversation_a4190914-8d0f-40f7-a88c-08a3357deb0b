package com.reon.hr.common.cmb.notify.payResult;

import lombok.Data;

/**
 * 支付退票通知
 *
 * <AUTHOR>
 */
@Data
public class PayRefund {
    /**
     * 处理机构
     */
    private String trsBrn;
    /**
     * 转账汇款种类
     */
    private String busTyp;
    /**
     * 流程实例号
     */
    private String reqNbr;
    /**
     * 付方开户机构
     */
    private String sndBrn;
    /**
     * 汇款方式
     */
    private String outTyp;
    /**
     * 退票原因
     */
    private String rtnNar;
    /**
     * 系统内外标志
     */
    private String sysFlg;
    /**
     * 摘要
     */
    private String narTxt;
    /**
     * 收方开户行
     */
    private String crtBnk;
    /**
     * 更新日期
     */
    private String updDat;
    /**
     * 收方开户地
     */
    private String rcvEaa;
    /**
     * 同城异地标志
     */
    private String ctyFlg;
    /**
     * 付方户口号
     */
    private String dbtAcc;
    /**
     * 收方户名
     */
    private String crtNam;
    /**
     * 处理分行
     */
    private String trsBbk;
    /**
     * 汇款发起通道
     */
    private String isuCnl;
    /**
     * 汇款优先级
     */
    private String busLvl;
    /**
     * 费用总额
     */
    private String feeAmt;
    /**
     * 退票理由代码
     */
    private String rtnCod;
    /**
     * 汇款业务状态
     */
    private String busSts;
    /**
     * 记录状态
     */
    private String rcdSts;
    /**
     * 收方户口号
     */
    private String crtAcc;
    /**
     * 付方客户号
     */
    private String sndClt;
    /**
     * 发起日期
     */
    private String isuDat;
    /**
     * 付方户名
     */
    private String dbtNam;
    /**
     * 业务参考号
     */
    private String yurRef;
    /**
     * 交易货币
     */
    private String ccyNbr;
    /**
     * 金额
     */
    private String trsAmt;
    /**
     * 汇款编号
     */
    private String busNbr;
}

/**
 * 票通知（FINB）：
 * 字段名称	字段ID	数据类型	是否必输	描述
 * 退票明细	backInfo	JSONObj	Y
 * backInfo（退票明细）:
 * <p>
 * 字段名称	字段ID	数据类型	是否必输	描述
 * 流程实例号	reqNbr	String(20)
 * 业务参考号	yurRef	String(30)
 * 汇款编号	busNbr	String(20)
 * 汇款方式	outTyp	String(2)
 * 转账汇款种类	busTyp	String(2)
 * 汇款优先级	busLvl	String(1)
 * 汇款业务状态	busSts	String(1)
 * 付方客户号	sndClt	String(10)
 * 清算状态	clrSts	String(1)
 * 汇款发起通道	isuCnl	String(3)
 * 发起日期	isuDat	String(8)
 * 处理分行	trsBbk	String(3)
 * 处理机构	trsBrn	String(6)
 * 交易货币	ccyNbr	String(2)		A.3 货币代码表
 * 金额	trsAmt	String(17)
 * 付方户口号	dbtAcc	String(35)
 * 付方户名	dbtNam	String(62)
 * 付方开户机构	sndBrn	String(6)
 * 收方户口号	crtAcc	String(35)
 * 收方户名	crtNam	String(62)
 * 收方开户行	crtBnk	String(62)
 * 收方开户地	rcvEaa	String(62)
 * 摘要	narTxt	String(100)
 * 费用总额	feeAmt	String(17)
 * 币种	feeCcy	String(2)
 * 提出凭证种类	psbTyp	String(4)
 * <p>
 * 凭证号码	psbNbr	String(20)
 * 同城异地标志	ctyFlg	String(1)
 * 系统内外标志	sysFlg	String(1)		Y 系统内
 * N系统外
 * 收方公私标志	rcvTyp	String(1)
 * 资金停留原因	watRcn	String(1)
 * 资金停留流水	watTrs	String(16)
 * 更新日期	updDat	String(8)
 * 退票理由代码	rtnCod	String(10)
 * 退票原因	rtnNar	String(100)		返回失败原因
 * 记录状态	rcdSts	String(1)
 */