package com.reon.hr.api.bill.vo.salary;

import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.api.customer.vo.salary.TaxDateRangeVo;
import com.reon.hr.api.customer.vo.supplier.supplierBank.SupplierBankVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class
PayBatchListVo  implements Serializable {
    private Long id;

    private Long custId;
    private String custName;

    private Long payId;

    private String batchNo;//发放批次编号

    private String batchName;//发放批次名称
    private Integer salaryMonth;
    private Integer taxMonth;
    private Integer billMonth;
    private BigDecimal totalActApy;//实发合计

    private BigDecimal totalTax;//税合计

    private Integer applyCnt;//申请人次

    private Date genTime;

    private Byte genFlag;
    private Integer anewPayFlag;
    private String paymentContentSummary;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private  String delFlag;
    private  BigDecimal applyAmt;// 申请金额
    private  Integer  appStatus;//审批状态
    private  String remark;//备注
    private  String dispatchCom;//派单地
    private  String dispatchComName;//派单地
    private  String dispatch;//派单地抬头
    private  String dispatchName;//派单地抬头
    private  String revCom;//接单地抬头
    private  String revComName;//接单地抬头
    private  String revPurpose;//接单地支付目的
    private Long payMentId;//支付ID
    private  String applicant;//申请人
    private Date passTime;//审批通过时间
    private Date lastDate;//最晚支付时间
    private Date paymentDate;
    private Long pid;//流程ID
    private  Integer payMentFlag;

    private String overReason;

    private String contractNo;
    private Long templetId;

    private BigDecimal totalApplyAmt;//总申请金额
    private BigDecimal totalSalaryFee;//工资服务费总计
    private BigDecimal totalSupplierDisFund;
    private BigDecimal totalSupplierCrossBankHandlingFees;
    private BigDecimal totalSupplierUnionFees;
    private BigDecimal totalSupplierSalarySaleTax;
    /**
     * 补偿金
     */
    private BigDecimal totalCompensation;//补偿金
    /**
     * 补偿金个税
     */
    private BigDecimal totalCompensationTax;//补偿金个税
    /**
     * 年终奖
     */
    private BigDecimal totalAnnualBonus;//年终奖
    /**
     * 年终奖个税
     */
    private BigDecimal totalAnnualBonusTax;//年终奖个税
    /**
     * 劳务工资
     */
    private BigDecimal totalLaborWages;//劳务工资
    /**
     * 劳务工资个税
     */
    private BigDecimal totalLaborWagesTax;//劳务工资个税
    private String payCom;//支付抬头
    private String payAssociatedCom;//支付关联抬头
    private String payAssociatedComName;
    private String paymentIds;//帐单模板付款方id
    private String receivedPaymentName;//帐单模板付款方
    private String withholdingAgentNo;//扣缴义务人编号
    private List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList;
    private List<SalaryPayBatchInfoVo> salaryPayBatchInfoVoList;
    private List<CompanyBankVo> companyBankVoList;
    private List<SupplierBankVo> supplierBankVoList;
    private Integer contractType;
    private String  categoryNo;
    private  String categoryName;
    private String payNo;
    private String payName;
    private Integer documentStatus;

    private String commissioner;
    private String salaryCommissioner;
    private Integer taxMonthWhiteListFlag;
    private List<TaxDateRangeVo> taxDateRangeVoList;
    private Integer competitionFlag;


}