<template>
    <el-dialog title="社保计算器" v-model="dialogVisible" width="45%" :close-on-click-modal="false"
        :before-close="handleClose">
        <el-form ref="queryRef" :model="formData" label-width="auto" :rules="rules">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="城市" prop="city">
                        <el-select v-model="formData.city" filterable placeholder="请选择城市" @change="handleCityChange">
                            <el-option v-for="city in cityList" :key="city.cityCode" :value="city.cityCode"
                                :label="city.cityName" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="人员类别" prop="indTypeCode">
                        <el-select v-model="formData.indTypeCode" placeholder="请选择人员类别" @change="indTypeCodeChange">
                            <el-option v-for="option in indTypeCodes" :key="option.categoryCode"
                                :label="option.cityName + '-' + getLabel(ep_people_ind_type, option.indTypeCode)"
                                :value="option.categoryCode"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="公积金缴费比例" prop="housingFundRatio">
                <el-select v-model="formData.housingFundRatio" placeholder="请选择公积金缴费比例" @change="ratioChange">
                    <el-option v-for="item in ratio" :key="item.insuranceRatioCode" :value="item.insuranceRatioCode"
                        :label="item.ratioName" />
                </el-select>
            </el-form-item>

            <el-form-item label="社保缴费基数">
                <el-radio-group v-model="formData.baseType">
                    <el-radio border label="1">最低基数</el-radio>
                    <el-radio border label="2">最高基数</el-radio>
                    <el-radio border label="3">其它基数</el-radio>
                </el-radio-group>

                <el-input class="ml10" style="width: 220px;" v-if="formData.baseType === '3'"
                    v-model="formData.customBase" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="公积金缴费基数">
                <el-radio-group v-model="formData.housingBaseType">
                    <el-radio border label="1">最低基数</el-radio>
                    <el-radio border label="2">最高基数</el-radio>
                    <el-radio border label="3">其它基数</el-radio>
                </el-radio-group>
                <el-input class="ml10" style="width: 220px;" v-if="formData.housingBaseType === '3'"
                    v-model="formData.customHousingBase" placeholder="请输入" clearable />
            </el-form-item>
        </el-form>
        <el-divider />
        <h3 class="result-title">基数比例试算表一览</h3>
        <el-table :data="resultData" border style="width: 100%">
            <el-table-column prop="productName" label="产品名称" align="center" />
            <el-table-column prop="unitBaseAA" label="单位基数" align="center" />
            <el-table-column prop="comRatioAA" label="单位比例" align="center" />
            <el-table-column prop="comAddAA" label="单位定值" align="center" />
            <el-table-column prop="personalBaseAA" label="个人基数" align="center" />
            <el-table-column prop="indRatioAA" label="个人比例" align="center" />
            <el-table-column prop="indlAddAA" label="个人定值" align="center" />
            <el-table-column prop="enterpriseSubtotalAA" label="单位金额" align="center" />
            <el-table-column prop="personalSubtotalAA" label="个人金额" align="center" />
            <el-table-column prop="productSubtotalAA" label="单位+个人合计" align="center" />
        </el-table>

        <template #footer>
            <el-button type="primary" @click="calculateSocialInsurance">开始计算</el-button>
            <el-button @click="resetForm">重置</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { getCity, getRadioByCityCode, getPersonType, calculate } from "@/api/enterprise/serviceOutlets/serviceOutlets";
import { getLabel } from "@/utils/reon";
const { proxy } = getCurrentInstance();
const allSelectDict = proxy.useDict('ep_people_ind_type');
const { ep_people_ind_type } = allSelectDict;
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:visible', 'close']);

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

const formData = reactive({
    city: '',
    housingFundRatio: '',
    baseType: '',
    customBase: '',
    housingBaseType: '',
    customHousingBase: ''
});


const data = reactive({
    rules: {
        cityCode: [{ required: true, message: "请选择所属城市", trigger: "blur" }],
        indTypeCode: [{ required: true, message: "请选择所属人员类型", trigger: "blur" }],
        housingFundRatio: [{ required: true, message: "请选择公积金缴费比例", trigger: "blur" }],
    },
    indTypeCodes: [],
    rows: [],
});

const {
    rules,
    indTypeCodes,
    rows,
} = toRefs(data);

const resultData = ref([]);
// 关闭
const handleClose = () => {
    emit('close');
    dialogVisible.value = false;
};

const cityList = ref([])
// 获取城市
function getStartData() {
    getCity().then(res => {
        cityList.value = res.rows;
    })
}
const cityCode = ref('')
const categoryCode = ref('')
//  城市
function handleCityChange(e) {
    cityCode.value = e
    indTypeCodes.value = [];
    ratio.value = []
    formData.indTypeCode = ''
    formData.housingFundRatio = ''
    getPersonType(e).then(response => {
        console.log(response.rows)
        rows.value = response.rows;
        for (let i = 0; i < response.rows.length; i++) {
            if (indTypeCodes.value.find(item => item.indTypeCode === response.rows[i].indTypeCode) === undefined) {
                indTypeCodes.value.push(response.rows[i]);
            }
        }
    })
}
// 人员类别
function indTypeCodeChange(e) {
    categoryCode.value = e
    contributionRatio(cityCode.value, e)
}
const ratio = ref([])
// 比例
function contributionRatio(cityCode, categoryCode) {
    let parmes = {
        cityCode: cityCode,
        ind: categoryCode
    }
    getRadioByCityCode(parmes).then((res) => {
        ratio.value = res.data
        console.log(res);
    })
}

const base = reactive({
    minSocialBase: 0,
    maxSocialBase: 0,
    minHousingFundBase: 0,
    maxHousingFundRatio: 0
})
// 比例改变
function ratioChange(e) {
    ratio.value.forEach((i) => {
        if (e == i.insuranceRatioCode) {
            base.minSocialBase = i.lowBaseCom
            base.minHousingFundBase = i.lowBaseInd
            base.maxSocialBase = i.highBaseCom
            base.maxHousingFundRatio = i.highBaseInd
        }
    });
}

// 重置
const resetForm = () => {
    proxy.resetForm("queryRef");
    formData.housingBaseType = ''
    formData.baseType = ''
};
// 开始计算
const calculateSocialInsurance = () => {
    // 深拷贝表单数据
    const params = JSON.parse(JSON.stringify(formData));

    // 设置社保基数
    const setSocialSecurityBase = () => {
        switch (params.baseType) {
            case '1': return base.minSocialBase;
            case '2': return base.maxSocialBase;
            default: return params.customBase;
        }
    };

    // 设置公积金基数
    const setProvidentFundBase = () => {
        switch (params.housingBaseType) {
            case '1': return base.minHousingFundBase;
            case '2': return base.maxHousingFundRatio;
            default: return params.customHousingBase;
        }
    };

    // 确保基数在合法范围内
    const clampValue = (value, min, max) => Math.min(Math.max(value, min), max);

    // 计算最终基数
    params.socialSecurityBase = clampValue(
        setSocialSecurityBase(),
        base.minSocialBase,
        base.maxSocialBase
    );

    params.providentFundBase = clampValue(
        setProvidentFundBase(),
        base.minHousingFundBase,
        base.maxHousingFundRatio
    );

    // 表单验证并提交计算
    proxy.$refs["queryRef"].validate(valid => {
        if (!valid) return;

        calculate({
            cityCode: params.city,
            insuranceRatioCode: params.housingFundRatio,
            socialSecurityBase: params.socialSecurityBase,
            providentFundBase: params.providentFundBase,
            categoryCode: categoryCode.value,
        }).then(res => {
            resultData.value = res.data;
        });
    });
};

getStartData()
</script>

<style scoped>
.result-title {
    text-align: center;
    font-weight: 600;
}

:deep(.el-radio) {
    margin-right: 10px;
}
</style>