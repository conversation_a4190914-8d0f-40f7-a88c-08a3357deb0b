package com.reon.hr.sp.bill.dao.bill;

import com.reon.hr.api.bill.vo.bill.NonMonthlyProdVo;
import com.reon.hr.sp.bill.entity.bill.NonMonthlyProd;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface NonMonthlyProdMapper {
    int deleteByPrimaryKey(Long id);

    int insert(NonMonthlyProd record);

    int insertSelective(NonMonthlyProd record);

    NonMonthlyProd selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(NonMonthlyProd record);

    int updateByPrimaryKey(NonMonthlyProd record);

    List<NonMonthlyProd> findListByOrderNos(@Param("orderNos") List<String> orderNos,@Param("billMonth")Integer billMonth);
    List<NonMonthlyProdVo> findListAllByOrderNos(@Param("orderNos") List<String> orderNos);

    int batchInsert(@Param("nonMonthlyProds") List<NonMonthlyProd> nonMonthlyProds);

    int deleteByBillMonthAndOrderNoList(@Param("orderNos") List<String> orderNos, @Param("billMonth") Integer billMonth, @Param("billId") Long billId);

	Integer deleteByBillId(@Param("billId") Long billId);



    int updateNMPNoBelongCurrentBillId(@Param("list") Set<String> orderNoAndBillMOnthAndPeriodAndProdCodeSet, @Param("billId") Long id);

    List<NonMonthlyProdVo> findReturnNMPByOrderNoList(@Param("billId") Long maxBillId);

}