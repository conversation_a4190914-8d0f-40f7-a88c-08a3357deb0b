<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.BillInvoiceMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="invoice_id" jdbcType="BIGINT" property="invoiceId"/>
        <result column="invoice_amt" jdbcType="DECIMAL" property="invoiceAmt"/>
        <result column="adjust_amt" jdbcType="DECIMAL" property="adjustAmt"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="voucher_type" jdbcType="INTEGER" property="voucherType"/>
        <result column="voucher_no" jdbcType="VARCHAR" property="voucherNo"/>
        <result column="voucher_date" jdbcType="VARCHAR" property="voucherDate"/>
        <result column="voucher_remark" jdbcType="VARCHAR" property="voucherRemark"/>
        <result column="prj_cs" jdbcType="VARCHAR" property="prjCs"/>
        <result column="finance" jdbcType="VARCHAR" property="finance"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,invoice_id, invoice_amt, adjust_amt, org_code, org_name,
    `status`, remark, voucher_date, prj_cs, finance, tax_type_id, bill_flag, virtual_red_status,
    creator, updater, create_time, update_time, del_flag, version, voucher_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_invoice
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectById" parameterType="java.lang.Long" resultType="com.reon.hr.api.bill.vo.BillInvoiceVo">
        select
        <include refid="Base_Column_List"/>
        from bill_invoice
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bill_invoice
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillInvoice" useGeneratedKeys="true"
            keyProperty="id">
        insert into bill_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="invoiceId != null">
                invoice_id,
            </if>
            <if test="invoiceAmt != null">
                invoice_amt,
            </if>
            <if test="adjustAmt != null">
                adjust_amt,
            </if>
            <if test="adjustFlag != null">
                adjust_flag,
            </if>
            <if test="voucherType != null">
                voucher_type,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="voucherNo != null">
                voucher_no,
            </if>
            <if test="voucherDate != null">
                voucher_date,
            </if>
            <if test="voucherRemark != null">
                voucher_remark,
            </if>
            <if test="prjCs != null">
                prj_cs,
            </if>
            <if test="finance != null">
                finance,
            </if>
            <if test="financeOrgCode != null">
                finance_org_code,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="specialRate != null">
                special_rate,
            </if>
            <if test="taxTypeId != null">
                tax_type_id,
            </if>
            <if test="pushMode != null">
                push_mode,
            </if>
            <if test="serviceTaxType != null">
                service_tax_type,
            </if>
            <if test="agentTaxType != null">
                agent_tax_type,
            </if>
            <if test="invoiceNo != null">
                invoice_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="invoiceId != null">
                #{invoiceId,jdbcType=BIGINT},
            </if>
            <if test="invoiceAmt != null">
                #{invoiceAmt,jdbcType=DECIMAL},
            </if>
            <if test="adjustAmt != null">
                #{adjustAmt,jdbcType=DECIMAL},
            </if>
            <if test="adjustFlag != null">
                #{adjustFlag},
            </if>
            <if test="voucherType != null">
                #{voucherType},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="voucherNo != null">
                #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherDate != null">
                #{voucherDate,jdbcType=VARCHAR},
            </if>
            <if test="voucherRemark != null">
                #{voucherRemark,jdbcType=VARCHAR},
            </if>
            <if test="prjCs != null">
                #{prjCs,jdbcType=VARCHAR},
            </if>
            <if test="finance != null">
                #{finance,jdbcType=VARCHAR},
            </if>
            <if test="financeOrgCode != null">
                #{financeOrgCode},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="specialRate != null">
                #{specialRate,jdbcType=DECIMAL},
            </if>
            <if test="taxTypeId != null">
                #{taxTypeId},
            </if>
            <if test="pushMode != null">
                #{pushMode},
            </if>
            <if test="serviceTaxType != null">
                #{serviceTaxType},
            </if>
            <if test="agentTaxType != null">
                #{agentTaxType},
            </if>
            <if test="invoiceNo != null">
                #{invoiceNo},
            </if>
        </trim>
        <selectKey resultType="long" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID() AS id
        </selectKey>
    </insert>
    <insert id="saveInvoiceDetail" parameterType="com.reon.hr.api.bill.vo.invoice.InvoiceDetailVo">
        insert into invoice_detail
            (
                bill_invoice_id,
                voucher_type,
                voucher_no,
                voucher_date,
                no_tax_amt,
                tax_amt,
                item_name,
                status,
                creator
            )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.billInvoiceId},
                #{item.voucherType},
                #{item.voucherNo},
                #{item.voucherDate},
                #{item.noTaxAmt},
                #{item.taxAmt},
                #{item.itemName},
                #{status},
                #{creator}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        update bill_invoice
        <set>
            <if test="invoiceAmt != null">
                invoice_amt = #{invoiceAmt,jdbcType=DECIMAL},
            </if>
            <if test="adjustAmt != null">
                adjust_amt = #{adjustAmt,jdbcType=DECIMAL},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="voucherType != null">
                voucher_type = #{voucherType,jdbcType=INTEGER},
            </if>
            <if test="voucherNo != null">
                voucher_no = #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherDate != null">
                voucher_date = #{voucherDate,jdbcType=DATE},
            </if>
            <if test="invoiceDate != null">
                invoice_date = #{invoiceDate,jdbcType=DATE},
            </if>
            <if test="abolishVoucherDate != null">
                abolish_voucher_date = #{abolishVoucherDate,jdbcType=DATE},
            </if>
            <if test="abolishInvoiceDate != null">
                abolish_invoice_date = #{abolishInvoiceDate,jdbcType=DATE},
            </if>
            <if test="voucherRemark != null">
                voucher_remark = #{voucherRemark,jdbcType=VARCHAR},
            </if>
            <if test="prjCs != null">
                prj_cs = #{prjCs,jdbcType=VARCHAR},
            </if>
            <if test="finance != null">
                finance = #{finance,jdbcType=VARCHAR},
            </if>
            <if test="virtualRedStatus != null">
                virtual_red_status = #{virtualRedStatus,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBillInvoicesToAbolish" parameterType="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        update bill_invoice
        set abolish_invoice_date = NOW(),
        status = if(status = 10, 9, 6),
        updater = #{loginName}
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        -- 如果status为10(部分红冲审批中)则红冲完毕后修改主表状态为9(部分红冲完成)否则为6(全部红冲完成)
    </update>

    <update id="updateVoucherDate" parameterType="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        update bill_invoice
            set invoice_date = NOW()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="copyInvoiceDetail" parameterType="long">
        insert into invoice_detail
        (bill_invoice_id,
        voucher_date,
        `status`,
        voucher_type,
        no_tax_amt,
        tax_amt,
        creator,
        updater)
        select
        bill_invoice_id,
        NOW(),
        2,
        voucher_type,
        no_tax_amt,
        tax_amt,
        creator,
        updater
        from invoice_detail
        where bill_invoice_id in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>

    <select id="getBillByPage" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT
        insu.id,
        -- 审批金额
        bci.invoice_approving_amt AS examineAmt,
        -- 客户编号
        insu.cust_id AS custId,
        -- 客户名称
        insu.cust_name AS custName,
        -- 付款客户编号
        bci.invoice_id AS customerInvoiceNo,
        -- 付款客户名称
        bci.invoice_title AS customerInvoiceName,
        -- 财务应收月
        insu.receivable_month AS receivableMonth,
        -- 应收金额
        insu.receive_amt AS receiveAmt,
        -- 开票状态
        bci.invoice_status AS invoiceStatus,
        -- 核销状态
        bci.cancel_status AS cancelStatus,
        -- 未开票金额
        bci.uninvoice_amt AS uninvoiceAmt,
        -- 已开票金额
        bci.invoiced_amt AS invoicedAmt,
        -- 已开票附加金额
        bci.add_amt AS addAmt,
        -- 客户帐套
        insu.templet_name AS templetName,
        -- 客户帐套id
        insu.templet_id AS templetId,
        -- 合同编号
        insu.contract_no AS contractNo,
        con.contract_type AS contractType
        FROM
        insurance_bill insu
        LEFT JOIN bill_check_invoice bci on insu.id = bci.id
        left join contract con on insu.contract_no = con.contract_no
        WHERE insu.del_flag = 'N' and bci.del_flag='N' AND insu.status = 2 AND bci.invoice_status != 3
        <if test="vo.custId!=null and vo.custId!=''">
            -- 客户
            AND insu.cust_id = #{vo.custId}
        </if>

        <if test="vo.customerInvoiceNo!=null and vo.customerInvoiceNo!=''">
            -- 付款客户
            AND bci.invoice_id = #{vo.customerInvoiceNo}
        </if>

        <if test="vo.templetId!=null and vo.templetId!=''">
            -- 客户帐套
            AND insu.templet_id = #{vo.templetId}
        </if>

        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != ''">
            -- 财务应收月
            and insu.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS}
        </if>
        <if test="vo.receivableMonthD != null and vo.receivableMonthD != ''">
            and insu.receivable_month <![CDATA[ <= ]]>  #{vo.receivableMonthD}
        </if>

        <if test="vo.receiveAmtS != null  and vo.receiveAmtS != ''">
            -- 应收金额
            and insu.receive_amt <![CDATA[ >= ]]> #{vo.receiveAmtS}
        </if>
        <if test="vo.receiveAmtD != null and vo.receiveAmtD != ''">
            and insu.receive_amt <![CDATA[ <= ]]>  #{vo.receiveAmtD}
        </if>

        <if test="vo.invoiceStatus!=null and vo.invoiceStatus!=''">
            -- 开票状态
            AND bci.invoice_status = #{vo.invoiceStatus}
        </if>

        <if test="vo.cancelStatus!=null and vo.cancelStatus!=''">
            -- 核销状态
            AND bci.cancel_status = #{vo.cancelStatus}
        </if>
        <if test="vo.userOrgPositionDtoList != null and vo.userOrgPositionDtoList.size > 0">
            and
            <foreach collection="vo.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                     close=")">
                (
                ( con.seller_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.seller_org like
                concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                or
                ( con.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and con.comm_org like
                concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                )
                <if test="userOrgPositionDto.loginName != null">
                    and (
                    con.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}
                    or
                    con.creator = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}
                    )
                </if>
            </foreach>
        </if>
        GROUP BY insu.id DESC
    </select>

    <select id="getCancelData" resultType="com.reon.hr.api.bill.vo.InvoiceViewCancelVo">
        SELECT
        bc.creator as checkCreator, -- 核销人
        bsc.check_amt as checkAmt, -- 核销金额
        bsc.adjust_amt as adjustAmt, -- 调整金额
        bc.status as status, -- 调整金额
        bc.create_time as checkCreateTime, -- 核销时间
        payCust.pay_amt as payAmt, -- 到账金额
        payCust.pay_bank as payBank, -- 到账银行
        payCust.pay_date as payDate -- 到账时间
        FROM
        bill_sub_check bsc
        left join bill_check bc on bsc.bill_check_id = bc.id
        left join payment_customer payCust on payCust.id = bc.pay_cust_id
        WHERE bsc.bill_id in (
        <foreach collection="billId" index="index" item="item" separator=",">
            <trim suffixOverrides=",">
                #{item,jdbcType=VARCHAR},
            </trim>
        </foreach>
        ) and bsc.del_flag = 'N' and bc.del_flag = 'N'
    </select>

    <select id="getInvoiceData" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT
        invoice.id AS id,
        invoice.create_time as invoiceCreateTime,
        invoice.invoice_amt AS invoiceAmt,
        invoice.voucher_date AS voucherDate,
        invoice.`status` AS invoiceStatus
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom on invoiceFrom.bill_invoice_id = invoice.id
        WHERE
        invoiceFrom.bill_id in (
        <foreach collection="billId" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item,jdbcType=VARCHAR},
            </trim>
        </foreach>
        )
    </select>

    <select id="getInvoiceByPage" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT DISTINCT
        invoice.id,
        invoice.invoice_no,
        invoice.org_name,
        bill.cust_id,
        invoice.invoice_id AS customerInvoiceNo,
        invoice.invoice_amt,
        -- IFNULL(invoice.abolish_voucher_date, invoice.voucher_date) as voucher_date,
        -- IFNULL(invoice.abolish_voucher_date, invoice.invoice_date) as invoice_date,
        invoice.voucher_date as blueVDate,
        invoice.invoice_date as blueIDate,
        invoice.abolish_voucher_date as redVDate,
        invoice.abolish_invoice_date as redIDate,
        invoice.`status`   AS invoiceStatus,
#         invoice.virtual_red_flag,
        invoice.virtual_red_status,
        invoice.bill_flag,
        invoice.remark,
        invoice.invoice_id AS invoiceId
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        <if test="companyCode!=null and companyCode != ''">
            AND invoice.org_code = #{companyCode}
        </if>
        <if test="vo.invoiceNo!=null and vo.invoiceNo != ''">
            AND invoice.invoice_no = #{vo.invoiceNo}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            AND invoice.org_name like concat('%',#{vo.orgName},'%')
        </if>
        <if test="vo.commissioner != null and vo.commissioner != ''">
            AND invoice.prj_cs like concat('%',#{vo.commissioner},'%')
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.customerIdList != null and vo.customerIdList.size > 0">
            AND bill.cust_id in
            <foreach collection="vo.customerIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.customerInvoiceNo != null and vo.customerInvoiceNo != ''">
            -- 客户付款方名称
            and invoice.invoice_id = #{vo.customerInvoiceNo}
        </if>
        <if test="vo.templetId != null and vo.templetId != ''">
            -- 客户帐套
            and bill.templet_id = #{vo.templetId}
        </if>

        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and (vo.receivableMonthD == null or vo.receivableMonthD == '')">
            -- 财务应收月起（仅起始日期）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS}
        </if>
        <if test="(vo.receivableMonthS == null or vo.receivableMonthS == '') and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月止（仅结束日期）
            and bill.receivable_month <![CDATA[ <= ]]>  #{vo.receivableMonthD}
        </if>
        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月范围（起始和结束日期都有）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS} AND bill.receivable_month <![CDATA[ <= ]]>
            #{vo.receivableMonthD}
        </if>

        <if test="vo.billMonth != null and vo.billMonth != ''">
            -- 账单月
            and bill.bill_month = #{vo.billMonth}
        </if>

        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE == null">
            -- 开票金额
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS}
        </if>
        <if test="vo.invoiceAmtS == null and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ <= ]]>  #{vo.invoiceAmtE}
        </if>
        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS} AND invoice.invoice_amt <![CDATA[ <= ]]>
            #{vo.invoiceAmtE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            -- 提票起始日期
            and DATE(IFNULL(invoice.abolish_voucher_date, invoice.voucher_date)) <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.invoiceAmtE != null and vo.voucherDateE != ''">
            and DATE(IFNULL(invoice.abolish_voucher_date, invoice.voucher_date)) <![CDATA[ <= ]]>  #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and DATE(IFNULL(invoice.abolish_voucher_date, invoice.voucher_date)) <![CDATA[ >= ]]> #{vo.voucherDateS}
            AND DATE(IFNULL(invoice.abolish_voucher_date, invoice.voucher_date)) <![CDATA[ <= ]]> #{vo.voucherDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and (vo.invoiceDateE == null or vo.invoiceDateE == '')">
            -- 开票起始日期
            and DATE(IFNULL(invoice.abolish_invoice_date, invoice.invoice_date)) <![CDATA[ >= ]]> #{vo.invoiceDateS}
        </if>
        <if test="(vo.invoiceDateS == null or vo.invoiceDateS == '') and vo.invoiceAmtE != null and vo.invoiceDateE != ''">
            and DATE(IFNULL(invoice.abolish_invoice_date, invoice.invoice_date)) <![CDATA[ <= ]]>  #{vo.invoiceDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and vo.invoiceDateE != null and vo.invoiceDateE != ''">
            and DATE(IFNULL(invoice.abolish_invoice_date, invoice.invoice_date)) <![CDATA[ >= ]]> #{vo.invoiceDateS}
            AND DATE(IFNULL(invoice.abolish_invoice_date, invoice.invoice_date)) <![CDATA[ <= ]]> #{vo.invoiceDateE}
        </if>

        <if test="vo.invoiceStatus != null and vo.invoiceStatus!= ''">
            and invoice.status = #{vo.invoiceStatus}
        </if>
        ORDER BY invoice.update_time DESC ,invoice.create_time DESC
    </select>
    <select id="getInvoiceByPage1" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        select * from (
        SELECT
        DISTINCT invoice.id,
        1 as statusType,
        invoice.org_name,
        bill.cust_id,
        bill.cust_name,
        bci.invoice_id AS customerInvoiceNo,
        bci.invoice_status AS allInvoiceStatus,
        invoice.invoice_id AS invoiceId,
        bill.contract_no,
        bill.templet_id,
        bill.id as billId,
        bill.supplier_cost,
        bill.service_num,
        bill.service_fee,
        bill.employee_num,
        invoice.invoice_amt,
        invoice.voucher_date as voucherDate,
        invoice.`status` as invoiceStatus,
        invoice.remark as invoiceRemark,
        invoice.invoice_date as invoiceDate,
        c.seller_org,
        c.seller_pos,
        c.seller,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.contract_no as contractNo,
        invoice.voucher_type,
        invoice.create_time,
        invoice.special_rate
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        LEFT join contract c on bill.contract_no = c.contract_no
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        <if test="companyCode!=null and companyCode != ''">
            AND invoice.org_code = #{companyCode}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            -- 签单分公司
            AND invoice.org_name like concat('%',#{vo.orgName},'%')
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            -- 客户名称
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.customerInvoiceNo != null and vo.customerInvoiceNo != ''">
            -- 客户付款方名称
            and bci.invoice_id = #{vo.customerInvoiceNo}
        </if>
        <if test="vo.templetId != null and vo.templetId != ''">
            -- 客户帐套
            and bill.templet_id = #{vo.templetId}
        </if>

        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and (vo.receivableMonthD == null or vo.receivableMonthD == '')">
            -- 财务应收月起（仅起始日期）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS}
        </if>
        <if test="(vo.receivableMonthS == null or vo.receivableMonthS == '') and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月止（仅结束日期）
            and bill.receivable_month <![CDATA[ <= ]]>  #{vo.receivableMonthD}
        </if>
        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月范围（起始和结束日期都有）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS} AND bill.receivable_month <![CDATA[ <= ]]>
            #{vo.receivableMonthD}
        </if>

        <if test="vo.billMonth != null and vo.billMonth != ''">
            -- 账单月
            and bill.bill_month = #{vo.billMonth}
        </if>

        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE == null">
            -- 开票金额
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS}
        </if>
        <if test="vo.invoiceAmtS == null and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ <= ]]>  #{vo.invoiceAmtE}
        </if>
        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS} AND invoice.invoice_amt <![CDATA[ <= ]]>
            #{vo.invoiceAmtE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            -- 提票起始日期
            and DATE(invoice.voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.invoiceAmtE != null and vo.voucherDateE != ''">
            and DATE(invoice.voucher_date) <![CDATA[ <= ]]>  #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and DATE(invoice.voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS} AND DATE(invoice.voucher_date) <![CDATA[ <= ]]>
            #{vo.voucherDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and (vo.invoiceDateE == null or vo.invoiceDateE == '')">
            -- 开票起始日期
            and DATE(invoice.invoice_date) <![CDATA[ >= ]]> #{vo.invoiceDateS}
        </if>
        <if test="(vo.invoiceDateS == null or vo.invoiceDateS == '') and vo.invoiceAmtE != null and vo.invoiceDateE != ''">
            and DATE(invoice.invoice_date) <![CDATA[ <= ]]>  #{vo.invoiceDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and vo.invoiceDateE != null and vo.invoiceDateE != ''">
            and DATE(invoice.invoice_date) <![CDATA[ >= ]]> #{vo.invoiceDateS} AND DATE(invoice.invoice_date) <![CDATA[ <= ]]>
            #{vo.invoiceDateE}
        </if>

        <if test="vo.invoiceStatus != null and vo.invoiceStatus!= ''">
            and invoice.status = #{vo.invoiceStatus}
        </if>
        union
        SELECT
        DISTINCT invoice.id,
        2 as statusType,
        invoice.org_name,
        bill.cust_id,
        bill.cust_name,
        bci.invoice_id AS customerInvoiceNo,
        bci.invoice_status AS allInvoiceStatus,
        invoice.invoice_id AS invoiceId,
        bill.contract_no,
        bill.templet_id,
        bill.id as billId,
        bill.supplier_cost,
        bill.service_num,
        bill.service_fee,
        bill.employee_num,
        invoice.invoice_amt,
        invoice.abolish_voucher_date as voucherDate,
        invoice.`status` as invoiceStatus,
        invoice.remark as invoiceRemark,
        invoice.abolish_invoice_date as invoiceDate,
        c.seller_org,
        c.seller_pos,
        c.seller,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.contract_no as contractNo,
        invoice.voucher_type,
        invoice.create_time,
        invoice.special_rate
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        LEFT join contract c on bill.contract_no = c.contract_no
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        <if test="companyCode!=null and companyCode != ''">
            AND invoice.org_code = #{companyCode}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            -- 签单分公司
            AND invoice.org_name like concat('%',#{vo.orgName},'%')
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            -- 客户名称
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.customerInvoiceNo != null and vo.customerInvoiceNo != ''">
            -- 客户付款方名称
            and bci.invoice_id = #{vo.customerInvoiceNo}
        </if>
        <if test="vo.templetId != null and vo.templetId != ''">
            -- 客户帐套
            and bill.templet_id = #{vo.templetId}
        </if>

        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and (vo.receivableMonthD == null or vo.receivableMonthD == '')">
            -- 财务应收月起（仅起始日期）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS}
        </if>
        <if test="(vo.receivableMonthS == null or vo.receivableMonthS == '') and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月止（仅结束日期）
            and bill.receivable_month <![CDATA[ <= ]]>  #{vo.receivableMonthD}
        </if>
        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            -- 财务应收月范围（起始和结束日期都有）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS} AND bill.receivable_month <![CDATA[ <= ]]>
            #{vo.receivableMonthD}
        </if>

        <if test="vo.billMonth != null and vo.billMonth != ''">
            -- 账单月
            and bill.bill_month = #{vo.billMonth}
        </if>

        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE == null">
            -- 开票金额
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS}
        </if>
        <if test="vo.invoiceAmtS == null and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ <= ]]>  #{vo.invoiceAmtE}
        </if>
        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS} AND invoice.invoice_amt <![CDATA[ <= ]]>
            #{vo.invoiceAmtE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            -- 提票起始日期
            and invoice.abolish_voucher_date <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.invoiceAmtE != null and vo.voucherDateE != ''">
            and invoice.abolish_voucher_date <![CDATA[ <= ]]>  #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and invoice.abolish_voucher_date <![CDATA[ >= ]]> #{vo.voucherDateS} AND invoice.abolish_voucher_date <![CDATA[ <= ]]>
            #{vo.voucherDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and (vo.invoiceDateE == null or vo.invoiceDateE == '')">
            -- 开票起始日期
            and invoice.abolish_invoice_date <![CDATA[ >= ]]> #{vo.invoiceDateS}
        </if>
        <if test="(vo.invoiceDateS == null or vo.invoiceDateS == '') and vo.invoiceAmtE != null and vo.invoiceDateE != ''">
            and invoice.abolish_invoice_date <![CDATA[ <= ]]>  #{vo.invoiceDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and vo.invoiceDateE != null and vo.invoiceDateE != ''">
            and invoice.abolish_invoice_date <![CDATA[ >= ]]> #{vo.invoiceDateS} AND invoice.abolish_invoice_date <![CDATA[ <= ]]>
            #{vo.invoiceDateE}
        </if>

        <if test="vo.invoiceStatus != null and vo.invoiceStatus!= ''">
            and invoice.status = #{vo.invoiceStatus}
        </if>
        ) a
        ORDER BY a.create_time DESC
    </select>
<!--
    <select id="getInvoiceByPage1" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT DISTINCT
        &#45;&#45; 开票ID
        invoice.id,
        &#45;&#45; 签单分公司
        invoice.org_name,
        &#45;&#45; 客户ID
        bill.cust_id,
        &#45;&#45; 开票信息维护ID
        invoice.invoice_id AS customerInvoiceNo,
        &#45;&#45; 开票金额
        invoice.invoice_amt,
        &#45;&#45; 提票时间
        invoice.voucher_date,
        &#45;&#45; 开票时间
        invoice.invoice_date,
        &#45;&#45; 开票状态
        invoice.`status`   AS invoiceStatus,
        &#45;&#45; 备注
        invoice.remark,
        &#45;&#45; 票面备注
        invoice.invoice_id AS invoiceId
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        <if test="companyCode!=null and companyCode != ''">
            AND invoice.org_code = #{companyCode}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            &#45;&#45; 签单分公司
            AND invoice.org_name like concat('%',#{vo.orgName},'%')
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            &#45;&#45; 客户名称
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.customerInvoiceNo != null and vo.customerInvoiceNo != ''">
            &#45;&#45; 客户付款方名称
            and bci.invoice_id = #{vo.customerInvoiceNo}
        </if>
        <if test="vo.templetId != null and vo.templetId != ''">
            &#45;&#45; 客户帐套
            and bill.templet_id = #{vo.templetId}
        </if>

        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and (vo.receivableMonthD == null or vo.receivableMonthD == '')">
            &#45;&#45; 财务应收月起（仅起始日期）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS}
        </if>
        <if test="(vo.receivableMonthS == null or vo.receivableMonthS == '') and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            &#45;&#45; 财务应收月止（仅结束日期）
            and bill.receivable_month <![CDATA[ <= ]]>  #{vo.receivableMonthD}
        </if>
        <if test="vo.receivableMonthS != null  and vo.receivableMonthS != '' and vo.receivableMonthD != null and vo.receivableMonthD != ''">
            &#45;&#45; 财务应收月范围（起始和结束日期都有）
            and bill.receivable_month <![CDATA[ >= ]]> #{vo.receivableMonthS} AND bill.receivable_month <![CDATA[ <= ]]>
            #{vo.receivableMonthD}
        </if>

        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE == null">
            &#45;&#45; 开票金额
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS}
        </if>
        <if test="vo.invoiceAmtS == null and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ <= ]]>  #{vo.invoiceAmtE}
        </if>
        <if test="vo.invoiceAmtS != null  and vo.invoiceAmtS != '' and vo.invoiceAmtE != null and vo.invoiceAmtE != ''">
            and invoice.invoice_amt <![CDATA[ >= ]]> #{vo.invoiceAmtS} AND invoice.invoice_amt <![CDATA[ <= ]]>
            #{vo.invoiceAmtE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            &#45;&#45; 提票起始日期
            and invoice.voucher_date <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.invoiceAmtE != null and vo.voucherDateE != ''">
            and invoice.voucher_date <![CDATA[ <= ]]>  #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and invoice.voucher_date <![CDATA[ >= ]]> #{vo.voucherDateS} AND invoice.voucher_date <![CDATA[ <= ]]>
            #{vo.voucherDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and (vo.invoiceDateE == null or vo.invoiceDateE == '')">
            &#45;&#45; 开票起始日期
            and invoice.invoice_date <![CDATA[ >= ]]> #{vo.invoiceDateS}
        </if>
        <if test="(vo.invoiceDateS == null or vo.invoiceDateS == '') and vo.invoiceAmtE != null and vo.invoiceDateE != ''">
            and invoice.invoice_date <![CDATA[ <= ]]>  #{vo.invoiceDateE}
        </if>
        <if test="vo.invoiceDateS != null  and vo.invoiceDateS != '' and vo.invoiceDateE != null and vo.invoiceDateE != ''">
            and invoice.invoice_date <![CDATA[ >= ]]> #{vo.invoiceDateS} AND invoice.invoice_date <![CDATA[ <= ]]>
            #{vo.invoiceDateE}
        </if>

        <if test="vo.invoiceStatus != null and vo.invoiceStatus!= ''">
            and invoice.status = #{vo.invoiceStatus}
        </if>
        ORDER BY invoice.create_time DESC
    </select>
-->

    <select id="getInvoiceUnApprovalCust" resultType="com.reon.hr.api.bill.vo.CustomerInfoVo">
        SELECT DISTINCT ib.cust_id,cust_name,contract_no
        from bill_invoice bi LEFT JOIN bill_invoice_from bif on bi.id=bif.bill_invoice_id
        LEFT JOIN insurance_bill ib on ib.id=bif.bill_id
        where (bi.`status` = 1 or bi.`status` = 3) and bi.del_flag = 'N' and ib.del_flag = 'N'
        <if test="list != null and list.size > 0">
            and
            <foreach collection="list" item="item" open="(" separator="or" close=")">
                (bi.finance_org_code like concat ('',#{item},'%'))
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and cust_name like concat('%',#{name},'%')
        </if>
    </select>
    <select id="getInvoiceUnApproval" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo"
            parameterType="com.reon.hr.api.bill.vo.invoice.InvoiceSearchParamVo">
        SELECT DISTINCT ib.cust_id,
                        bi.invoice_id,
                        ib.cust_name,
                        ib.contract_no,
                        bci.invoice_id                           as customerInvoiceNo,
                        GROUP_CONCAT(DISTINCT bci.invoice_title) as customerInvoiceName,
                        bi.invoice_amt,
                        GROUP_CONCAT(DISTINCT ib.templet_name)   as templetName,
                        bi.voucher_date,
                        bi.`status`                              as invoiceStatus,
                        bi.id,
                        bi.remark                                as invoiceRemark,
                        bi.creator                               as commissioner,
                        bi.invoice_no as voucherNo
        FROM bill_invoice bi
        LEFT JOIN bill_invoice_from bif on bi.id = bif.bill_invoice_id
        LEFT JOIN insurance_bill ib on ib.id = bif.bill_id
        left join contract c on ib.contract_no = c.contract_no
        left join bill_check_invoice bci on bci.id = ib.id
        <where>
            bi.del_flag = 'N' and bif.del_flag = 'N' and ib.del_flag = 'N'
            <if test="orgList != null and orgList.size > 0">
                and
                <foreach collection="orgList" item="item" open="(" separator="or" close=")">
                    (bi.finance_org_code like concat ('',#{item},'%'))
                </foreach>
            </if>

            <if test="status != null">
                and bi.`status`= #{status}
            </if>
            <if test="abolishStatus != null and abolishStatus.size > 0">
                and bi.`status` in
                <foreach collection="abolishStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="signCom != null and signCom != ''">
                and ib.sign_title = #{signCom}
            </if>
            <if test="signCom1 != null and signCom1 != ''">
                and c.sign_com = #{signCom1}
            </if>
            <if test="custId != null">
                and ib.cust_id = #{custId}
            </if>
            <if test="custInvoiceId != null">
                and bci.invoice_id = #{custInvoiceId}
            </if>
            <if test="commissioner != null and commissioner != ''">
                and bi.creator = #{commissioner}
            </if>
            <if test="templetId != null">
                and ib.templet_id = #{templetId}
            </if>
            <if test="receivableMonthMax != null">
                and ib.receivable_month &lt;= #{receivableMonthMax}
            </if>
            <if test="receivableMonthMin != null">
                and ib.receivable_month &gt;= #{receivableMonthMin}
            </if>
            <if test="invoiceAmtMax != null">
                and bi.invoice_amt &lt;= #{invoiceAmtMax}
            </if>
            <if test="invoiceAmtMin != null">
                and bi.invoice_amt &gt;= #{invoiceAmtMin}
            </if>
            <if test="invoiceDateS != null and invoiceDateS != ''">
                and bi.create_time &gt;= #{invoiceDateS,jdbcType=TIMESTAMP}
            </if>
            <if test="invoiceDateE != null and invoiceDateE != ''">
                and bi.create_time &lt;= #{invoiceDateE,jdbcType=TIMESTAMP}
            </if>
        </where>
        group by bi.id
        order by bi.update_time desc,bi.create_time desc
    </select>
    <update id="updateStatusByIds">
        update bill_invoice
        set status = #{status}
        <if test="updater != null and updater != ''">
            , updater = #{updater}
        </if>
        <if test="voucherFlag != null">
          <if test="voucherFlag == 1">
              , abolish_voucher_date = CURRENT_DATE()
          </if>
          <if test="voucherFlag == 2">
              , abolish_invoice_date = CURRENT_DATE()
          </if>
        </if>
        where id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateStatus" parameterType="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        UPDATE bill_invoice
        SET status = CASE id
        <foreach collection="list" item="item" open="" close="" separator=" ">
            WHEN #{item.id} THEN #{item.status}
        </foreach>
        END,
        updater = #{updater}
        WHERE id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>

    </update>

    <select id="searchCustByOrgCode" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT DISTINCT
        -- 客户ID
        bill.cust_id,
        -- 客户名字
        bill.cust_name,
        bci.invoice_id AS customerInvoiceNo
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT join bill_check_invoice bci on bci.id=bill.id
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N'
        <if test="companyCode != null and companyCode != ''">
            AND invoice.org_code = #{companyCode}
        </if>
        <if test="custName != null and custName != ''">
            -- 客户名称
            AND bill.cust_name like concat('%',#{custName},'%')
        </if>
    </select>
    <select id="getIncomeCountTableDate" resultType="com.reon.hr.api.bill.vo.IncomeCountTableReportDto">
        SELECT DISTINCT bc.id,
                        bsc.bill_id                                       as bill_id,
                        ib.templet_id,
                        ib.local_flag as local_flag ,
                        ib.sign_title                                     AS company,
                        ib.cust_id                                        AS cust_id,
                        ib.employee_num,
                        ib.bill_month,
                        ib.bill_type,
                        pc.pay_date                                       AS pay_date,
                        pc.pay_amt                                        AS pay_amt,            -- 到账金额
                        pc.pay_remark                                     AS finance_remark,     -- 财务摘要为 到款客户信息(payment_customer)中的到款备注
                        ib.bill_month                                     AS bill_month,         -- 账单月
                        ib.receive_amt                                    AS bill_amt,           -- 应收金额
                        ib.service_num                                    AS persons_served_num, --   服务人次
                        ib.service_fee                                    AS tax_inclusive_fee,  -- 服务费    账单中的服务费
                        ib.contract_no                                     AS contract_no,
                        ib.supplier_cost,
                        bsc.create_time,
                        bsc.creator,
                        bsc.check_amt subCheckAmt
                      FROM `reon-billdb`.bill_sub_check as bsc
                 LEFT JOIN `reon-billdb`.bill_check bc on bsc.bill_check_id = bc.id
                 LEFT JOIN `reon-billdb`.insurance_bill AS ib ON ib.id = bsc.bill_id
                 LEFT JOIN `reon-billdb`.contract AS c ON c.contract_no = ib.contract_no
                 LEFT JOIN `reon-billdb`.bill_check_invoice bci ON bci.id = bsc.bill_id
                 LEFT JOIN `reon-billdb`.payment_customer AS pc ON pc.id = bc.pay_cust_id
                 left join `reon-billdb`.bill_invoice_from as bif on bif.bill_id = bsc.bill_id
                 left join `reon-billdb`.bill_invoice as bi on bi.id = bif.bill_invoice_id
        WHERE (bci.cancel_status = 2 OR bci.cancel_status = 3)
          and bsc.id = #{subCheckId} and bsc.del_flag = 'N' and bc.del_flag = 'N';
    </select>
    <select id="getCheckAmtAndInvoiceAmtByBillCheckInvoice" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        select id, checked_amt, invoiced_amt
        from bill_check_invoice
        where id = #{billId};
    </select>
    <select id="getBillIdByInvoiceId" resultType="java.lang.Long">
        select bill_id
        from bill_invoice_from
        where bill_invoice_id = #{invoiceId}
    </select>
    <select id="getBillInvoiceByIdList" resultType="com.reon.hr.sp.bill.entity.bill.BillInvoice">
        select
        <include refid="Base_Column_List"/>
        from bill_invoice where id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="getAllInvoiceIdByCheckIdFromBillInvoice" resultType="java.lang.Long">
        SELECT distinct bill_invoice_id
        FROM bill_invoice_from bif
                 left join bill_check_invoice bci
                           on bci.id = #{billId}
        where bill_id = #{billId}
          AND (bci.invoice_status = 2 OR bci.invoice_status = 3 AND bci.cancel_status = 2 OR bci.cancel_status = 3)
    </select>
    <select id="getInvoiceUnApprovalById" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT DISTINCT ib.cust_id,
                        bi.invoice_id,
                        ib.cust_name,
                        GROUP_CONCAT(distinct ib.contract_no)    as contract_no,
                        GROUP_CONCAT(distinct ib.templet_id)     as templet_id,
                        bci.invoice_id                           as customerInvoiceNo,
                        GROUP_CONCAT(DISTINCT bci.invoice_title) as customerInvoiceName,
                        bi.invoice_amt,
                        GROUP_CONCAT(DISTINCT ib.templet_name)   as templetName,
                        if(bi.`status` in (5,6,7,8,9,10), bi.abolish_voucher_date, bi.voucher_date) as voucherDate,
                        bi.invoice_date                          as blueInvoiceDate,
                        bi.`status`                              as invoiceStatus,
                        bi.id,
                        bi.remark                                as invoiceRemark,
                        bi.voucher_remark,
                        bi.voucher_type,
                        bi.special_rate,
                        bi.tax_type_id,
                        bi.service_tax_type,
                        bi.agent_tax_type,
                        bi.invoice_no,
                        bi.creator,
                        bi.push_mode
        FROM bill_invoice bi
                 LEFT JOIN bill_invoice_from bif on bi.id = bif.bill_invoice_id
                 LEFT JOIN insurance_bill ib on ib.id = bif.bill_id
                 left join bill_check_invoice bci on bci.id = ib.id
        where bi.id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="invoiceStatus != null">
            and bi.`status` = #{invoiceStatus}
        </if>
        group by bi.id
       -- order by bi.update_time desc, bi.create_time desc;
    </select>
    <select id="getInvoiceById" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        SELECT
        DISTINCT
        invoice.id,
        invoice.org_name,
        bill.cust_id,
        bill.cust_name,
        bill.id as billId,
        bci.invoice_id AS customerInvoiceNo,
        invoice.invoice_id AS invoiceId,
        bill.contract_no,
        bill.templet_id,
        invoice.invoice_amt,
        invoice.voucher_date,
        invoice.`status` as invoiceStatus,
        invoice.remark as invoiceRemark,
        invoice.invoice_date as invoiceDate,
        c.seller_org,
        c.seller_pos,
        c.seller,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.contract_no,
        invoice.voucher_type
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        LEFT join contract c on bill.contract_no = c.contract_no
        -- LEFT JOIN invoice_detail id on invoice.id = id.bill_invoice_id
        where invoice.id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        ORDER BY invoice.create_time DESC;
    </select>
    <select id="getInvoiceByDateAndCustId" resultType="com.reon.hr.api.bill.vo.InvoiceBillVo">
        select *
        from (
        SELECT
        DISTINCT invoice.id,
        1 as statusType,
        invoice.org_name,
        bill.cust_id,
        bill.cust_name,
        bci.invoice_id AS customerInvoiceNo,
        bci.invoice_status AS allInvoiceStatus,
        invoice.invoice_id AS invoiceId,
        bill.contract_no,
        bill.templet_id,
        bill.id as billId,
        bill.supplier_cost,
        bill.service_num,
        bill.service_fee,
        bill.employee_num,
        invoice.invoice_amt,
        invoice.voucher_date as voucherDate,
        invoice.`status` as invoiceStatus,
        invoice.remark as invoiceRemark,
        invoice.invoice_date as invoiceDate,
        c.seller_org,
        c.seller_pos,
        c.seller,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.contract_no as contractNo,
        invoice.voucher_type,
        invoice.create_time,
        invoice.special_rate,
        invoice.tax_type_id,
        invoice.service_tax_type,
        invoice.agent_tax_type,
        invoice.push_mode
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        LEFT join contract c on bill.contract_no = c.contract_no
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        and (invoice.status = 4 or invoice.status = 6 or invoice.status = 9)
        <if test="vo.invoiceOrgCode != null and vo.invoiceOrgCode != ''">
            -- 发票开具公司
            AND invoice.org_code = #{vo.invoiceOrgCode}
        </if>
        <if test="vo.signCom != null and vo.signCom != ''">
            -- 业绩所属公司
            AND c.sign_com = #{vo.signCom}
        </if>
        <if test="vo.orgCodes != null and vo.orgCodes.size > 0">
            -- 销售所属城市查出的公司
            AND c.sign_com in
            <foreach collection="vo.orgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            -- 提票起始日期
            and DATE(invoice.voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.voucherDateE != '' and vo.voucherDateE != null">
            and DATE(invoice.voucher_date) <![CDATA[ <= ]]> #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and DATE(invoice.voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS} AND DATE(invoice.voucher_date) <![CDATA[ <= ]]> #{vo.voucherDateE}
        </if>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and (vo.actualInvoiceEndTime == null or vo.actualInvoiceEndTime == '')">
            -- 开票起始日期
            and DATE(invoice.invoice_date) <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime}
        </if>
        <if test="(vo.actualInvoiceStartTime == null or vo.actualInvoiceStartTime == '') and vo.actualInvoiceEndTime != '' and vo.actualInvoiceEndTime != null">
            and DATE(invoice.invoice_date) <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and vo.actualInvoiceEndTime != null and vo.actualInvoiceEndTime != ''">
            and DATE(invoice.invoice_date) <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime} AND DATE(invoice.invoice_date) <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>
        <if test="vo.startDate != null  and vo.startDate != '' and (vo.endDate == null or vo.endDate == '')">
            -- 签约起始日期(不算中止日期)
            and c.start_date <![CDATA[ >= ]]> #{vo.startDate}
        </if>
        <if test="(vo.startDate == null or vo.startDate == '') and vo.endDate != '' and vo.endDate != null">
            and c.start_date <![CDATA[ <= ]]>  #{vo.endDate}
        </if>
        <if test="vo.startDate != null  and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
            and c.start_date <![CDATA[ >= ]]> #{vo.startDate} AND c.start_date <![CDATA[ <= ]]> #{vo.endDate}
        </if>
        <if test="vo.contractType != null and vo.contractType != ''">
            -- 产品类型
            AND c.contract_type = #{vo.contractType}
        </if>
        <if test="vo.sellerPos != null and vo.sellerPos != ''">
            -- 签约部门
            AND c.seller_pos like concat('',#{vo.sellerPos},'%')
        </if>
        union
        SELECT
        DISTINCT invoice.id,
        2 as statusType,
        invoice.org_name,
        bill.cust_id,
        bill.cust_name,
        bci.invoice_id AS customerInvoiceNo,
        bci.invoice_status AS allInvoiceStatus,
        invoice.invoice_id AS invoiceId,
        bill.contract_no,
        bill.templet_id,
        bill.id as billId,
        bill.supplier_cost,
        bill.service_num,
        bill.service_fee,
        bill.employee_num,
        invoice.invoice_amt,
        invoice.abolish_voucher_date as voucherDate,
        invoice.`status` as invoiceStatus,
        invoice.remark as invoiceRemark,
        invoice.abolish_invoice_date as invoiceDate,
        c.seller_org,
        c.seller_pos,
        c.seller,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.contract_no as contractNo,
        invoice.voucher_type,
        invoice.create_time,
        invoice.special_rate,
        invoice.tax_type_id,
        invoice.service_tax_type,
        invoice.agent_tax_type,
        invoice.push_mode
        FROM
        bill_invoice invoice
        LEFT JOIN bill_invoice_from invoiceFrom ON invoiceFrom.bill_invoice_id = invoice.id
        LEFT JOIN insurance_bill bill ON bill.id = invoiceFrom.bill_id
        LEFT JOIN bill_check_invoice bci on bill.id = bci.id
        LEFT join contract c on bill.contract_no = c.contract_no
        WHERE bill.del_flag = 'N' AND invoice.del_flag = 'N' and bci.del_flag = 'N'
        and (invoice.status = 4 or invoice.status = 6 or invoice.status = 9)
        <if test="vo.invoiceOrgCode != null and vo.invoiceOrgCode != ''">
            -- 发票开具公司
            AND invoice.org_code = #{vo.invoiceOrgCode}
        </if>
        <if test="vo.signCom != null and vo.signCom != ''">
            -- 业绩所属公司
            AND c.sign_com = #{vo.signCom}
        </if>
        <if test="vo.orgCodes != null and vo.orgCodes.size > 0">
            -- 销售所属城市查出的公司
            AND c.sign_com in
            <foreach collection="vo.orgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.custId != null and vo.custId != ''">
            -- 客户名称
            AND bill.cust_id = #{vo.custId}
        </if>
        <if test="vo.voucherDateS != null  and vo.voucherDateS != '' and (vo.voucherDateE == null or vo.voucherDateE == '')">
            -- 提票起始日期
            and DATE(invoice.abolish_voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS}
        </if>
        <if test="(vo.voucherDateS == null or vo.voucherDateS == '') and vo.voucherDateE != '' and vo.voucherDateE != null">
            and DATE(invoice.abolish_voucher_date) <![CDATA[ <= ]]> #{vo.voucherDateE}
        </if>
        <if test="vo.voucherDateS != null and vo.voucherDateS != '' and vo.voucherDateE != null and vo.voucherDateE != ''">
            and DATE(invoice.abolish_voucher_date) <![CDATA[ >= ]]> #{vo.voucherDateS} AND DATE(invoice.abolish_voucher_date) <![CDATA[ <= ]]> #{vo.voucherDateE}
        </if>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and (vo.actualInvoiceEndTime == null or vo.actualInvoiceEndTime == '')">
            -- 开票起始日期
            and DATE(invoice.abolish_invoice_date) <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime}
        </if>
        <if test="(vo.actualInvoiceStartTime == null or vo.actualInvoiceStartTime == '') and vo.actualInvoiceEndTime != '' and vo.actualInvoiceEndTime != null">
            and DATE(invoice.abolish_invoice_date) <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and vo.actualInvoiceEndTime != null and vo.actualInvoiceEndTime != ''">
            and DATE(invoice.abolish_invoice_date) <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime} AND DATE(invoice.abolish_invoice_date) <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>
        <if test="vo.startDate != null  and vo.startDate != '' and (vo.endDate == null or vo.endDate == '')">
            -- 签约起始日期(不算中止日期)
            and c.start_date <![CDATA[ >= ]]> #{vo.startDate}
        </if>
        <if test="(vo.startDate == null or vo.startDate == '') and vo.endDate != '' and vo.endDate != null">
            and c.start_date <![CDATA[ <= ]]>  #{vo.endDate}
        </if>
        <if test="vo.startDate != null  and vo.startDate != '' and vo.endDate != null and vo.endDate != ''">
            and c.start_date <![CDATA[ >= ]]> #{vo.startDate} AND c.start_date <![CDATA[ <= ]]> #{vo.endDate}
        </if>
        <if test="vo.contractType != null and vo.contractType != ''">
            -- 产品类型
            AND c.contract_type = #{vo.contractType}
        </if>
        <if test="vo.sellerPos != null and vo.sellerPos != ''">
            -- 签约部门
            AND c.seller_pos like concat('',#{vo.sellerPos},'%')
            <if test="vo.sellerPos == '2'">
                or c.seller_pos like '9%'
            </if>
        </if>
        ) a
        ORDER BY a.create_time DESC
    </select>
    <select id="getInvoiceDetailByInvoiceId" resultType="com.reon.hr.api.bill.vo.invoice.InvoiceDetailVo">
        select id,
               bill_invoice_id,
               voucher_type,
               voucher_no,
               no_tax_amt,
               tax_amt,
               item_name,
               status,
               creator,
               updater,
               create_time,
               update_time,
               del_flag,
               version
        from invoice_detail
        where bill_invoice_id = #{billInvoiceId}
        and del_flag = 'N'
    </select>
    <select id="getInvoiceDetailByInvoiceIdList" resultType="com.reon.hr.api.bill.vo.invoice.InvoiceDetailVo">
        select id,
        bill_invoice_id,
        voucher_type,
        voucher_no,
        no_tax_amt,
        tax_amt,
        item_name,
        voucher_date,
        `status`,
        creator,
        updater,
        create_time,
        update_time,
        del_flag,
        version
        from invoice_detail
        <where>
            del_flag = 'N'
            and
            <foreach collection="vos" open="(" close=")" separator="or" item="vo">
                (
                bill_invoice_id = #{vo.id}
                and `status` = #{vo.statusType}
                <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and (vo.actualInvoiceEndTime == null or vo.actualInvoiceEndTime == '')">
                    and voucher_date <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime}
                </if>
                <if test="(vo.actualInvoiceStartTime == null or vo.actualInvoiceStartTime == '') and vo.actualInvoiceEndTime != '' and vo.actualInvoiceEndTime != null">
                    and voucher_date <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
                </if>
                <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and vo.actualInvoiceEndTime != null and vo.actualInvoiceEndTime != ''">
                    and voucher_date <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime} and voucher_date <![CDATA[ <= ]]>
                    #{vo.actualInvoiceEndTime}
                </if>
                )
            </foreach>
        </where>
        <!--and bill_invoice_id in
        <foreach collection="list" open="(" close=")" separator="," item="billInvoiceId">
            #{billInvoiceId}
        </foreach>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and (vo.actualInvoiceEndTime == null or vo.actualInvoiceEndTime == '')">
            &#45;&#45; 开票起始日期
            and voucher_date <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime}
        </if>
        <if test="(vo.actualInvoiceStartTime == null or vo.actualInvoiceStartTime == '') and vo.actualInvoiceEndTime != '' and vo.actualInvoiceEndTime != null">
            and voucher_date <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>
        <if test="vo.actualInvoiceStartTime != null  and vo.actualInvoiceStartTime != '' and vo.actualInvoiceEndTime != null and vo.actualInvoiceEndTime != ''">
            and voucher_date <![CDATA[ >= ]]> #{vo.actualInvoiceStartTime} AND voucher_date <![CDATA[ <= ]]> #{vo.actualInvoiceEndTime}
        </if>-->
    </select>

    <update id="batchUpdatePrjByBillId">
        UPDATE bill_invoice a , bill_invoice_from b
        SET a.prj_cs = #{prjCs}
        WHERE
        b.bill_invoice_id = a.id
        and
        b.bill_id in
        <foreach collection="billIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateVirRedInvoiceStatus">
        UPDATE bill_invoice
        SET virtual_red_status = #{status}
        WHERE
        id = #{invoiceId}
    </update>
    <update id="updateAdjustAmtById">
        update bill_invoice
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="adjust_amt = case" suffix="end,">
                <foreach collection="list" item="item">
                    when id = #{item.id} then #{item.adjustAmt}
                </foreach>
            </trim>
            <trim prefix="updater = case" suffix="end,">
                <foreach collection="list" item="item">
                    when id = #{item.id} then #{item.updater}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateInvoiceGenFlag">
        update bill_invoice
        set bill_flag = #{flag}
        where id = #{invoiceId}
    </update>

    <select id="getAdjustAmtByInvoiceId" resultType="com.reon.hr.api.bill.vo.check.BillCheckVo">
        select  adjust_amt, adjust_flag from bill_invoice where invoice_id = #{invoiceId} group by invoice_id
    </select>
    <select id="getAdjustAmtByInvoiceIdList" resultType="com.reon.hr.api.bill.vo.check.BillCheckVo">
        select  adjust_amt, adjust_flag,invoice_id
        from `reon-billdb`.bill_invoice
        where invoice_id in
              <foreach collection="invoiceIdList" open="(" close=")" separator="," item="invoiceId">
                  #{invoiceId}
              </foreach>
              group by invoice_id
    </select>
</mapper>
