package com.reon.ehr.sp.sys.mapper.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.vo.employee.EhrEmployeeVo;
import com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployee;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.PersonOrderEditVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeBankCardVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeInfoVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EhrEmployeeMapper {
    List<Long> selectId(String certNo);
    List<Long> selectEmpId(@Param("certNo")String certNo, @Param("name") String name);
    Long selectEmpIdBySE(@Param("certNo")String certNo, @Param("name") String name);
    Long selectEmpIds(@Param("certNo")String certNo, @Param("name") String name);

    int deleteByPrimaryKey(Long id);

    int insertSelective(EhrEmployee record);

    EhrEmployee selectByPrimaryKey(Long id);

    EhrEmployee getEmployeeByCertNoAndCertType(@Param("certNo") String certNo, @Param("certType") Integer certType);

    int updateByPrimaryKeySelective(EhrEmployee record);

    /**
     * 逻辑删除
     *
     * @return
     */
    int deleteById(@Param("id") Long id, @Param("updater") String updater);

    List<PersonOrderEditVo> searchEmployeeByOrderNo(@Param("orderNoList") List<String> orderNoList);

    boolean updateEmployee(@Param("vo") List<EmployeeOrderVo> record);

    EhrEmployee selectByCertNo(@Param("certNo") String certNo, @Param("id") Long id);

    List<EhrEmployeeVo> selectEmployeeByCertNo(@Param("certNo") String certNo);

    List<SalaryEmployeeBankCardVo> getSalaryEmployeeBankCardVoListPage(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo);

    List<SalaryEmployeeVo> getSalaryEmployeeBySalaryEmployeeVoListPage(Page page, SalaryEmployeeVo salaryEmployeeVo);
    List<SalaryEmployeeVo> getNoSalaryEmployeeBySalaryEmployeeVoListPage(Page page, SalaryEmployeeVo salaryEmployeeVo);

    List<SalaryEmployeeVo> getSalaryEmployeeBySalaryEmployeeVoListPage(SalaryEmployeeVo salaryEmployeeVo);

    EhrEmployeeVo getEmployeeByEmpNo(@Param("employeeNo") String employeeNo);

    String getEmployeeIdByEmployeeNo(@Param("employeeNo") String employeeNo);

    SalaryEmployeeVo getEmployeeByEmployeeNoandcertNo(@Param("employeeNo") String employeeNo, @Param("certNo") String certNo);

    List<String> getEmployeeNoList();

    SalaryEmployeeVo getInsuranceEmployee(@Param("contractNo") String contractNo, @Param("certNo") String certNo, @Param("certType") Integer certType, @Param("empName") String empName);

    SalaryEmployeeVo getPurePayEmployee(@Param("contractNo") String contractNo, @Param("certNo") String certNo, @Param("certType") Integer certType, @Param("empName") String empName);


    void editEmployeeNameByEmployeeNo(PersonOrderQueryDto personOrderQueryDto);

    List<SalaryEmployeeBankCardVo> getSalaryEmployeeBankCardVoListPage(Page page, SalaryEmployeeBankCardVo salaryEmployeeBankCardVo);

    List<SalaryEmployeeVo> getPayrollEmployeeListByCertNoAndName(@Param("certNo") String certNo, @Param("certType") Integer certType, @Param("empName") String empName);


    String getCertNo(@Param("certNo") String certNo, @Param("employeeId") Long employeeId, @Param("certType") Integer certType);

    SalaryEmployeeVo getSalaryEmployeeVoByEmployeeId(@Param("employeeId") Long employeeId);
    SalaryEmployeeVo getEmployeeVoByEmployeeId(@Param("employeeId") Long employeeId);

    List<EhrEmployeeVo> getAllEmployeeOrderVo();

    /**
     * 根据employeeId集合批量查询
     *
     * @param employeeIds 员工id
     * @return List<EmployeeVo>
     */
    List<EhrEmployeeVo> getEmployeeOrderVo(List<Long> employeeIds);

    List<EhrEmployeeVo> getAllEmployeeIdByCertNoList(@Param("list") List<String> certNoList);

    int updateEmployeeByCertNoAndCertTypeAndEmpName(EhrEmployee employee);

    List<EhrEmployeeVo> getEmployeeListByEmployeeNoList(@Param("list") List<String> conditionEmployeeNoList);

    List<EhrEmployeeVo> getEmployeeListByEmployeeIdList(@Param("list") List<Long> employeeIdList);

    int updateEmployeeById(EhrEmployeeVo employeeVo);

    String findCertNameByCertNo(String certNo);

    List<EhrEmployeeVo> findAllCertNoAndEmpIdByCertNos(@Param("certNos") List<String> certNos);

    List<EhrEmployeeVo> findAllCertNoName(@Param("empIds") List<SalaryEmployeeInfoVo> salaryEmployeeInfoVos);
    List<EhrEmployeeVo> findAllCertNo();

    int updateEmailById(EhrEmployee employee);

    int updateSpecialEmployee(@Param("list") List<EhrEmployee> employeeList);

    int updateByPrimaryKeySpecialSelective(EhrEmployee employee);

    List<EhrEmployeeVo> getSpecialByEmpIdList(@Param("list")List<Long> empIdList);
}
