<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input{
            padding-right: 30px;!important;
        }
        .layui-table-cell{
            padding: 0px;
        }
        #inp{
            padding-top:15px;

        }

        .layui-table-tool {
            min-height: 0px;
        }

    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="get">
        <input type="hidden" name="lockStatus" value="1">
        <input type="hidden" name="genStatus" value="1">
        <div class="layui-form-item reset">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="供应商名称" style="font-weight:800"><i
                        style="color: red">*</i>供应商名称</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="supplierId" id="supplierId" lay-filter="supplierIdFilter" lay-search=""
                            placeholder="请选择" autocomplete="off">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="供应商账单模板" style="font-weight:800"><i
                        style="color: red">*</i>供应商账单模板</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="templetId" id="templetId" lay-filter="templetIdFilter"
                            placeholder="请选择" autocomplete="off">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="报表年月" style="font-weight:800"><i
                        style="color: red">*</i>报表年月</label>
                <div class="layui-input-inline">
                    <input type="text" id="billMonth" name="billMonth" placeholder="请输入"
                           class="layui-input reset" autocomplete="off">
                </div>
            </div>


            <div class="layui-inline">
                <a class="layui-btn layui-btn-sm" id="btnQuery" data-type="reload">查询</a>
            </div>
        </div>
    </form>
</blockquote>
<div class="layui-card-body">
    <table id="queryGridTable" lay-filter="queryGridTableFilter" ></table>
</div>


<div class="layui-card-body" id="isShow">
    <table class="layui-hide" id="billDisposableGrid" lay-filter="billDisposableFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <input type="button" class="layui-btn layui-btn-sm" id="save" lay-event="save" value="添加">
        <input type="button" class="layui-btn layui-btn-sm" id="delete" lay-event="delete" value="删除">
        <input type="button" class="layui-btn layui-btn-sm" id="submitAudit" lay-event="submitAudit" value="提交">

    </script>
</div>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/supplier/practice/oneTime/supplierDisposableItem.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/BigDecimal-all-last.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/defaultComputerJs.js?v=${publishVersion}"></script>

<%--产品类别--%>
<script type="text/jsp" id="prodType">
    <select id="proType" dataId="{{d.prodType}}" lay-verify="required" lay-verType="tips" name="proType"
            lay-filter="proType" lay-search>
        <option value=""></option>
        {{# layui.each(window.top['dictCachePool']['DISPOSAL_TYPE'], function(index, item){ }}
        <option value="{{item.code}}" {{ d.prodType==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>

</script>

<script type="text/jsp" id="prodKind">
    <select id="proKind" dataId="{{d.proKind}}" lay-verify="required" lay-verType="tips" name="proKind"
            lay-filter="prodKind" lay-search>
        <option value=""></option>
        {{# layui.each(ML.getSubDictByParent("DISPOSAL_TYPE", d.prodType), function(index, item){ }}
        <option value="{{item.code}}" {{ d.prodKind==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>
</script>

<script type="text/jsp" id="disSupMan">
    <select id="disSupMan" dataId="{{d.disSupMan}}"   lay-verType="tips" name="disSupMan"
            lay-filter="disSupMan" lay-search>
               <option value=""></option>
        {{# layui.each(window.top['disSupManList'], function(index, item){ }}
        <option value="{{item.loginName}}" {{ d.disSupMan==item.loginName?'selected':'' }}>{{ item.userName }}</option>
        {{# }); }}
    </select>

</script>


</body>
</html>