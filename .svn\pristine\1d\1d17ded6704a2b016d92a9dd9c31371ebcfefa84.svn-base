package com.reon.hr.api.bill.dubbo.service.rpc.bill;


import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.vo.BadDebtDealVo;

import java.util.List;
import java.util.Map;

public interface IBadDebtDealWrapperService {


    Page<BadDebtDealVo> getBadDebtDealList(Integer page, Integer limit, Map<String, Object> paramMap);

    void saveBadDebtDeal(Long billId, String loginName, String remark, String fileIdStr);

    List<BadDebtDealVo> getBadDebtDealListByExport(Map<String, Object> paramMap);
}
