package com.reon.hr.api.change.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectJobVo implements Serializable {

    private Long id;

    private String jobNo;

    private String chgName;

    private Integer chgType;

    private Integer empType;

    private String receiving;

    private String deCom;

    private String insurHandler;

    private Integer cityCode;

    private Long custId;

    private String custName;

    private Integer zeroFlag;

    private Integer startMonth;

    private Integer endMonth;

    private Integer jobStatus;

    private Integer lockStatus;

    private String endDate;

    private String endTime;

    private Integer publishFlag;

    private Integer comAuditFlag;

    private Integer indAuditFlag;

    private String comCol;

    private String indCol;

    private Integer exeMonth;

    private String policyLink;

    private String policyFile;

    private String remark;

    private String termiReason;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;


}
