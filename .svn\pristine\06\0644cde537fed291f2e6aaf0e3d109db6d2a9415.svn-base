package com.reon.hr.sp.customer.entity.insurancePractice;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/05/11
 */
@Data
public class InsurancePracticeLog {
	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 实做ID
	 */
	private Long practiceId;

	/**
	 * 操作类型(1:不需办理，2：办理,3:退回)
	 */
	private Integer oprType;

	/**
	 * 备注信息
	 */
	private String remark;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改人
	 */
	private String updater;

	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 删除标识(Y:已删除，N:未删除)
	 */
	private String delFlag;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getPracticeId() {
		return practiceId;
	}

	public void setPracticeId(Long practiceId) {
		this.practiceId = practiceId;
	}

	public Integer getOprType() {
		return oprType;
	}

	public void setOprType(Integer oprType) {
		this.oprType = oprType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator == null ? null : creator.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater == null ? null : updater.trim();
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag == null ? null : delFlag.trim();
	}
}