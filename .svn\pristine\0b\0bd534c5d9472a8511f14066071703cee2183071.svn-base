package com.reon.hr.sp.customer.service.ImportService;

import com.reon.hr.api.customer.dto.importData.AddIndTaxApplyInfoImportDto;
import com.reon.hr.api.customer.dto.importData.AddSalaryActualInfoImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;

/**
 * <AUTHOR> on 2021/10/11.
 */
public interface BatchAddSalaryActualInfoImportService {

    ImportDataDto<AddSalaryActualInfoImportDto> batchAddSalaryActualInfoImport(ImportDataDto<AddSalaryActualInfoImportDto> importDataDto, String withholdingAgentNo);
}
