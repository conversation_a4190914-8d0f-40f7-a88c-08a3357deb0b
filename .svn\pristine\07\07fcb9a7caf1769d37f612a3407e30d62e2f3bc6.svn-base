import {allCityListData} from "@/api/system/baseData";

const cityList= ref([])
const useCityStore = defineStore(
    'city',
    {
        state: () => ({
            city: null
        }),
        actions: {
            // 获取字典
            getCity() {
                /*if(this.city.length===0){
                    return null;
                }*/
                return this.city;
            },
            // 设置字典
            setCity() {
                this.city=cityList.value
            }
        }
    })
function getList() {
    allCityListData().then(resp => {
        cityList.value = resp.data
    })
}
getList()

export default useCityStore