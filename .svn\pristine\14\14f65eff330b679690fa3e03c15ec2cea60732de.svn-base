package com.reon.hr.api.bill.vo.supplierPractice;

import com.baomidou.mybatisplus.annotations.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.api.bill.vo
 * @ClassName: SupplierPracticeSnapshotVo
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/8 14:21
 * @Version: 1.0
 */
@Accessors(chain = true)
@Data
public class SupplierPracticeSnapshotVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 实做ID
     */
    private Long practiceId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 产品信息Code
     */
    private String prodCryptCode;
    /**
     * 服务费code
     */
    private String chargeCryptCode;
    /**
     * 账单月
     */
    private Integer billMonth;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

}
