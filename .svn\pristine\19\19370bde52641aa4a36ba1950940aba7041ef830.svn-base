<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /*去掉type=number时的上下加减按钮*/
        /* 谷歌 */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        /* 火狐 */
        input{
            -moz-appearance:textfield;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post">
    <input type="hidden" id="id" name="id" value="${id}">
    <input type="hidden" id="type" value="${type}">
    <input type="hidden" id="optType" value="${optType}">
    <input type="hidden" id="withholdingAgentNo" value="${withholdingAgentNo}">
    <input type="hidden" id="receiving" value="${receiving}">
    <input type="hidden" id="fileId" value="${fileId}">
    <input type="hidden" id="cityCodeOld" value="${cityCodeOld}">
    <table class="layui-table" lay-skin="nob" style="width: 65%;">
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>城市名称：</td>
            <td width="60%">
                <select class="layui-select" name="cityCode" id="cityCode" lay-filter="cityCode" lay-search AREA_TYPE lay-verify="required">
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>合同名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="" id="contractName" lay-verify="required" value="${disabilityRecordVo.contractName}" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>合同编号：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="contractNo" id="contractNo" lay-verify="required" value="${disabilityRecordVo.contractNo}" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>客户名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="" id="custName" lay-verify="required" value="${disabilityRecordVo.custName}" readonly>
                <input class="layui-input" type="hidden" name="custId" id="custId" value="${disabilityRecordVo.custId}" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>备案类型：</td>
            <td width="60%">
                <select class="layui-select" name="type" id="recordTypeFilter" lay-filter="type" lay-search lay-verify="required">
                    <option value=""></option>
                    <option value="1">社保</option>
                    <option value="2">工资</option>
                </select>
            </td>
        </tr>
        <tr id="myTr1">
            <td align="right">接单方：</td>
            <td>
                <select class="layui-select layui-select-disabled" name="receiving" id="orgCode" lay-filter="type" lay-search lay-verify="">
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr id="myTr">
            <td align="right">扣缴义务人名称：</td>
            <td>
                <select class="layui-select layui-select-disabled" name="withholdingAgentNo" id="withholdingAgentName" lay-filter="withholdingAgentName" lay-search >
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right">扣缴义务人类型：</td>
            <td>
                <select class="layui-select layui-select-disabled" name="withholdingAgentType" id="withholdingAgentType" lay-filter="withholdingAgentType" lay-search DICT_TYPE="WITHHOLDING_AGENT_TYPE"  disabled>
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right" >
                <button type="button" id="employContractUpload" class="layui-btn layui-btn-normal">选择文件
                </button>
            </td>
            <td>
                <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                    预览图：
                    <div class="layui-upload-list" id="upload"></div>
                </blockquote>
            </td>
        </tr>
        <tr >
            <td align="right">备案内容：</td>
            <td>
                <textarea placeholder="" class="layui-textarea" name="remark" lay-verify="required" style="width: 500px;">${disabilityRecordVo.remark}</textarea>
            </td>
        </tr>
    </table>
    <div style="float: right; margin-right: 40%;" id="but">
        <button class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn" >保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/disabilityRecord/addFilingPage.js?v=${publishVersion}"></script>
</body>
</html>