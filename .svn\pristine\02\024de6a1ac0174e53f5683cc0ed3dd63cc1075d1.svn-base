var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
    var batchIds = $("#batchIds").val();
    var oldTableDate=[];
    reloadTable();
    function reloadTableDate(tableDate) {
        table.render({
            id: 'empId',
            elem: '#empId',
            data:tableDate,
            page: false, //默认不开启
            limits: [50, 100, 200],
            defaultToolbar: [],
            height: '600',
            toolbar: '#toolbarDemo',
            limit: 50,
            text: {
                none: '暂无数据'
            },
            cols:[[
                {type: 'checkbox', width: '50px', fixed: 'left'},
                {field: 'remark', title: '失败原因', width: '15%', align: 'center',fixed: 'left'},
                {field: 'employeeNo', title: '员工编号', width: '15%', align: 'center',fixed: 'left'},
                {field: 'name', title: '姓名', width: '10%', align: 'center',fixed: 'left'},
                {field: 'certType', title:'证件类型',width: '10%', align: 'center',fixed: 'left',templet: function (d) {
                        return ML.dictFormatter("CERT_TYPE", d.certType);
                    }},
                {field: 'certNo', title:'证件号码',width: '15%', align: 'center',},
                {field: 'salMonth', title:'账单月',width: '7%', align: 'center'},
                {field:  'custNo', title:'客户编号',width: '7%', align: 'center',},
                {field: 'custName', title:'客户名称',width: '7%', align: 'center',},
                {field: 'cardNo', title:'银行卡号',width: '7%', align: 'center',},
                {field: 'bankName', title:' 开户行',width: '7%', align: 'center',templet:function (d) {
                        return ML.dictFormatter("BANK", d.bankName);
                    }},
                {field: 'S007', title:'实发合计',width: '7%', align: 'center',},
                {field: 'S006', title:'个人所得税',width: '7%', align: 'center',},
                {field: 'S002', title:'工资服务费',width: '7%', align: 'center',},
                {field: 'S052', title:'经济补偿金实发金额',width: '7%', align: 'center',},
                {field: 'S051', title:'经济补偿金个税',width: '7%', align: 'center',},
                {field: 'status', title:'状态',width: '7%', align: 'center',templet: function (d) {
                        return ML.dictFormatter("SALARY_EMP_STATUS", d.status);
                    }}
            ]],
            done: function (res) {
                //  ML.hideNoAuth();
                if(oldTableDate.length==0){
                    oldTableDate = table.cache.empId;
                }
                table.on('toolbar(empId)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
                    switch (obj.event) {
                        case 'set':
                            if(data.length>0){
                                var url="/bill/payBatch/editRefundInputPage";
                                openPage(url,data);
                            }else{
                                layer.msg("请先勾选数据");
                                return false;
                            }
                            break;
                        case 'cancle':
                            let index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                            parent.layer.close(index); //再执行关闭
                            break;
                    }
                });
            }


        });
    }

    function openPage(url,data) {
        if (data.length > 0){
            layer.open({
                type:2,
                content:ctx+url
                , title: "录入失败原因"
                , area:['35%','55%']
                , btn: ['提交', '取消']
                ,yes:function (index,layero) {
                    var obj = $(layero).find("iframe")[0].contentWindow;//obj可以调用子页面的任何方法
                    var remark=obj.$("#remark").val();
                    if(!remark){
                        return layer.msg("请填写失败原因！")
                    }

                    for (var i = 0; i <data.length ; i++) {
                        data[i].remark=remark;
                        if(oldTableDate.length>0){
                            for (let j = 0; j < oldTableDate.length; j++) {
                                if(data[i].salaryId==oldTableDate[j].salaryId){
                                    oldTableDate[j].remark=data[i].remark;
                                }
                            }
                        }
                    }
                    layer.close(index); // 关闭弹出层
                    reloadTable();
                },
                cancel : function(index, layero) { // 取消按钮回调函数
                    layer.close(index); // 关闭弹出层
                }
            });

        }

    }

    //重载表格
    function reloadTable() {
        ML.ajax("/bill/payBatch/selectSalaryPaymentRefundInputEmpAll", {'keyWord':$("#keyWord").val(),'batchIds':batchIds,
            'bankNo':$("#bankNo").val(),'actApy':$("#actApy").val(),'limit':50,'page':1}, function (res) {
            var data = res.data;
            for (var i = 0; i <data.length ; i++) {
                if(oldTableDate.length>0){
                    for (let j = 0; j < oldTableDate.length; j++) {
                        if(data[i].salaryId==oldTableDate[j].salaryId){
                            data[i].remark=oldTableDate[j].remark;
                        }
                    }
                }
            }
            reloadTableDate(data);
        }, 'GET');
    }

    $("#btnQuery").on('click',function () {
        reloadTable();
        return false;
    });
    $("#commit").on('click',function () {
        var salaryPaymentRefundInputVoList=[];
        let result=true;
        for (let i = 0; i < oldTableDate.length; i++) {
            if(oldTableDate[i].remark){
                result=false;
                let salaryPaymentRefundInputVo={};
                salaryPaymentRefundInputVo.salaryInfoId=oldTableDate[i].salaryId;
                salaryPaymentRefundInputVo.remark=oldTableDate[i].remark;
                salaryPaymentRefundInputVo.taskId=$("#taskId").val();
                salaryPaymentRefundInputVo.pid=$("#pid").val();
                salaryPaymentRefundInputVo.bizId=$("#bizId").val();
                salaryPaymentRefundInputVo.batchId=oldTableDate[i].batchId;
                salaryPaymentRefundInputVoList.push(salaryPaymentRefundInputVo)
            }
        }
        if (result){
            layer.msg("最少有一个存在失败原因");
            return false;
        }
        layer.confirm("确定提交嘛", {btn: ["确定", '取消']}, function (index) {
            $.ajax({
                type: 'POST',
                url: ctx + "/bill/payBatch/saveSalaryPaymentRefundInput",
                data: JSON.stringify(salaryPaymentRefundInputVoList),
                dataType: 'json',
                contentType: 'application/json',//添加这句话
                success: function (results) {
                    layer.msg(results.msg);
                    if (results.code==0) {
                        parent.layer.closeAll('iframe');
                    }
                },
                error: function (data) {
                    layer.msg("系统繁忙，请稍后重试!");
                }
            });
        })
        return false;
    });

})