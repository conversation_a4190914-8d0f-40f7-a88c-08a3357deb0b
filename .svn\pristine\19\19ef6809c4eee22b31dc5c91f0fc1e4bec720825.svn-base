package com.reon.hr.sp.customer.entity.salary;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SpecImportInfo implements Serializable {

    private static final long serialVersionUID = 1985831121819411047L;
    
    private Long id;

    private String staffNo;

    private String staffName;

    private String certType;

    private String certNo;

    private String withholdingAgentNo;//扣缴义务人编号

    private BigDecimal cumuChildEdu;

    private BigDecimal cumuContiEdu;

    private BigDecimal cumuRent;

    private BigDecimal cumuInterest;

    private BigDecimal cumuSupportElder;

    private BigDecimal cumuOtherDeduction;//累计其它扣除
    private String remark;

    private BigDecimal cumuBaseSub;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    public String getWithholdingAgentNo() {
        return withholdingAgentNo;
    }

    public void setWithholdingAgentNo(String withholdingAgentNo) {
        this.withholdingAgentNo = withholdingAgentNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStaffNo() {
        return staffNo;
    }

    public void setStaffNo(String staffNo) {
        this.staffNo = staffNo == null ? null : staffNo.trim();
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName == null ? null : staffName.trim();
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType == null ? null : certType.trim();
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo == null ? null : certNo.trim();
    }

    public BigDecimal getCumuChildEdu() {
        return cumuChildEdu;
    }

    public void setCumuChildEdu(BigDecimal cumuChildEdu) {
        this.cumuChildEdu = cumuChildEdu;
    }

    public BigDecimal getCumuContiEdu() {
        return cumuContiEdu;
    }

    public void setCumuContiEdu(BigDecimal cumuContiEdu) {
        this.cumuContiEdu = cumuContiEdu;
    }

    public BigDecimal getCumuRent() {
        return cumuRent;
    }

    public void setCumuRent(BigDecimal cumuRent) {
        this.cumuRent = cumuRent;
    }

    public BigDecimal getCumuInterest() {
        return cumuInterest;
    }

    public void setCumuInterest(BigDecimal cumuInterest) {
        this.cumuInterest = cumuInterest;
    }

    public BigDecimal getCumuSupportElder() {
        return cumuSupportElder;
    }

    public void setCumuSupportElder(BigDecimal cumuSupportElder) {
        this.cumuSupportElder = cumuSupportElder;
    }

    public BigDecimal getCumuBaseSub() {
        return cumuBaseSub;
    }

    public void setCumuBaseSub(BigDecimal cumuBaseSub) {
        this.cumuBaseSub = cumuBaseSub;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater == null ? null : updater.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public BigDecimal getCumuOtherDeduction() {
        return cumuOtherDeduction;
    }

    public void setCumuOtherDeduction(BigDecimal cumuOtherDeduction) {
        this.cumuOtherDeduction = cumuOtherDeduction;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
