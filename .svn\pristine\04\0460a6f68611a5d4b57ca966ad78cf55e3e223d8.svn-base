<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:reg="http://www.dangdang.com/schema/ddframe/reg" xmlns:job="http://www.dangdang.com/schema/ddframe/job"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.dangdang.com/schema/ddframe/reg
                        http://www.dangdang.com/schema/ddframe/reg/reg.xsd
                        http://www.dangdang.com/schema/ddframe/job
                        http://www.dangdang.com/schema/ddframe/job/job.xsd">
	<!--配置作业注册中心 -->
	<reg:zookeeper id="regCenter" server-lists="${elastic.job.zookeeperUrl}" namespace="reon-base-job"
		base-sleep-time-milliseconds="1000" max-sleep-time-milliseconds="3000" max-retries="3" />

 	<job:simple id="messageJob" class="com.reon.hr.sp.base.job.contract.MessageJob" registry-center-ref="regCenter"
				cron="0 0 23 ? * WED" description="消息Job" sharding-total-count="1" overwrite="true" />
	<job:simple id="remarkMessageJob" class="com.reon.hr.sp.base.job.contract.RemarkMessageJob" registry-center-ref="regCenter"
				cron="0 40 23 * * ? *" description="消息Job" sharding-total-count="1" overwrite="true" />
	<!--每月1,10,20号的过期社保比例提醒-->
	<!--<job:simple id="setRatioMessageJob" class="com.reon.hr.sp.base.job.contract.SetRatioMessageJob" registry-center-ref="regCenter"
				cron="0 * * * * ?" description="过期社保比例提醒" sharding-total-count="1" overwrite="true" />-->
	<job:simple id="setRatioMessageJob" class="com.reon.hr.sp.base.job.contract.SetRatioMessageJob" registry-center-ref="regCenter"
				cron="0 1 0 1,10,20 * ?" description="过期社保比例提醒" sharding-total-count="1" overwrite="true" />
</beans>
