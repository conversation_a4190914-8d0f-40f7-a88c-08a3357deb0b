package com.reon.hr.api.bill.utils;
import com.reon.hr.api.bill.vo.PrintReceivingApplicationBankVo;
import com.reon.hr.api.bill.vo.PrintReceivingApplicationFromVo;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月26日
 * @Version 1.0
 */
public class PrintReceivingApplicationFromUtil {


    private static final DecimalFormat THOUSAND_FORMAT = new DecimalFormat("###,###.##");

    public static XSSFWorkbook generateExcel(
            Map<String, List<PrintReceivingApplicationFromVo>> dataMap,
            String sheetTitleName,
            Map<String, List<PrintReceivingApplicationBankVo>> bankMap
    ) {
        XSSFWorkbook workbook = new XSSFWorkbook();

        createSheet(workbook, "社保", dataMap.get("社保"), sheetTitleName + "社保出款详情", new String[]{
                "客户名称", "产品方案", "费用所属月份", "当月服务人数",
                "社保金额", "利息/滞纳金", "最晚支付时间", "总计"
        }, bankMap);

        createSheet(workbook, "公积金", dataMap.get("公积金"), sheetTitleName + "公积金出款详情", new String[]{
                "客户名称", "产品方案", "费用所属月份", "当月服务人数",
                "住房公积金金额", "补充公积金金额", "最晚支付时间", "备注", "总计"
        }, bankMap);

        return workbook;
    }


    private static void createSheet(XSSFWorkbook workbook, String sheetName, List<PrintReceivingApplicationFromVo> data, String title, String[] headers, Map<String, List<PrintReceivingApplicationBankVo>> bankMap) {
        Sheet sheet = workbook.createSheet(sheetName);

        List<Integer> zeroCols = findAllZeroColumns(sheetName, data);

        //createTitleRow(sheet, title, headers.length,zeroCols);
        createHeaderRow(sheet, headers, zeroCols);

        int rowIndex = 2;
        int totalServicePeople = 0;
        BigDecimal totalSocialAmt = BigDecimal.ZERO, totalInterestAmt = BigDecimal.ZERO, totalFinalAmt = BigDecimal.ZERO;

        int totalProvidentServicePeople = 0;
        BigDecimal totalHousingProvidentFund = BigDecimal.ZERO, totalSupProvidentAmt = BigDecimal.ZERO, totalProvidentFinalAmt = BigDecimal.ZERO;
        Map<String, String> leaderMap = new HashMap<>();
        if (data != null) {
            for (PrintReceivingApplicationFromVo vo : data) {
                Row row = sheet.createRow(rowIndex++);
                fillDataRow(sheet, row, vo, sheetName, zeroCols);

                if ("社保".equals(sheetName)) {
                    totalServicePeople += vo.getNum();
                    totalSocialAmt = totalSocialAmt.add(vo.getSocialAmt());
                    totalInterestAmt = totalInterestAmt.add(vo.getInterestAmt());
                    totalFinalAmt = totalFinalAmt.add(vo.getCountAmt());
                } else if ("公积金".equals(sheetName)) {
                    totalProvidentServicePeople += vo.getNum();
                    totalHousingProvidentFund = totalHousingProvidentFund.add(vo.getProvidentAmt());
                    totalSupProvidentAmt = totalSupProvidentAmt.add(vo.getSupProvidentAmt());
                    totalProvidentFinalAmt = totalProvidentFinalAmt.add(vo.getCountAmt());
                }
                leaderMap.put("经办人", vo.getReceivingMan());
                leaderMap.put("区域负责人", vo.getManager());
            }
        }

        createTotalRow(sheet, rowIndex, sheetName, totalServicePeople,
                totalSocialAmt, totalInterestAmt, totalFinalAmt, totalProvidentServicePeople,  totalHousingProvidentFund,
                totalSupProvidentAmt, totalProvidentFinalAmt,headers,zeroCols);

        createBankInfoSection(sheet, sheetName, bankMap.get(sheetName),leaderMap);
        setFixedColumnWidth(sheet, headers.length);
        setPrintSetting(workbook,sheet,title);
    }

    private static CellStyle getTotalStyle(Sheet sheet) {
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }


    private static void setFixedColumnWidth(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.setColumnWidth(i, 25 * 256);
        }
    }

    private static void createTitleRow(Sheet sheet, String title, int colSpan, List<Integer> zeroCols) {
        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue(title);
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        cell.setCellStyle(style);

        int mergedEnd = colSpan - 1 - (int) zeroCols.stream().filter(col -> col < colSpan).count();
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, mergedEnd));
    }

    private static void createHeaderRow(Sheet sheet, String[] headers, List<Integer> zeroCols) {
        Row row = sheet.createRow(1);
        row.setHeightInPoints(30); // 设置标题行高度
        Workbook workbook = sheet.getWorkbook();
        CellStyle style = workbook.createCellStyle();

        // 创建字体样式
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);

        // 设置居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);

        // 设置背景色和填充方式
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex()); // 灰色背景
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND); // 填充模式

        int colIndex = 0;
        for (int i = 0; i < headers.length; i++) {
            if (zeroCols.contains(i)) continue;
            Cell cell = row.createCell(colIndex++);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(style);
        }
    }


    private static void fillDataRow(Sheet sheet, Row row, PrintReceivingApplicationFromVo vo, String sheetName, List<Integer> zeroCols) {
        row.setHeightInPoints(30);
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setFontHeightInPoints((short) 11);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setWrapText(true); // 开启自动换行
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setFont(font);

        List<Object> values = new ArrayList<>();
        if ("社保".equals(sheetName)) {
            values = Arrays.asList(
                    vo.getCustName(), vo.getProdType(), vo.getMonth(), vo.getNum(),
                     format(vo.getSocialAmt()), format(vo.getInterestAmt()),
                    vo.getPayTime(), format(vo.getCountAmt())
            );
        } else {
            values = Arrays.asList(
                    vo.getCustName(), vo.getProdType(), vo.getMonth(), vo.getNum(),
                    format(vo.getProvidentAmt()), format(vo.getSupProvidentAmt()), vo.getPayTime(), vo.getRemark(), format(vo.getCountAmt())
            );
        }

        int colIndex = 0;
        for (int i = 0; i < values.size(); i++) {
            if (zeroCols.contains(i)) continue;
            Cell cell = row.createCell(colIndex++);
            Object val = values.get(i);
            if (val instanceof Number) {
                cell.setCellValue(((Number) val).doubleValue());
            } else {
                cell.setCellValue(val == null ? "" : val.toString());
            }
            if (val == null || val.toString().trim().isEmpty()) {
                // 空值，不设置边框样式（或设置无边框样式）
                cell.setCellStyle(getNoBorderStyle(sheet));
            } else {
                cell.setCellValue(val.toString());
                cell.setCellStyle(style); // 有边框的样式
            }
        }
    }

    private static CellStyle getNoBorderStyle(Sheet sheet) {
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        style.setWrapText(true); // 可选：是否换行
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 👇 不设置边框就是无边框了
        return style;
    }


    private static String format(BigDecimal value) {
        return value == null ? "0.00" : THOUSAND_FORMAT.format(value);
    }

    private static void createTotalRow(Sheet sheet, int rowIndex, String sheetName,
                                       int totalServicePeople,
                                       BigDecimal totalSocialAmt, BigDecimal totalInterestAmt, BigDecimal totalFinalAmt,
                                       int totalProvidentServicePeople,
                                       BigDecimal totalHousingProvidentFund, BigDecimal totalSupProvidentAmt,
                                       BigDecimal totalProvidentFinalAmt,
                                       String[] oldHeaders,List<Integer> zeroCols) {

        Row totalRow = sheet.createRow(rowIndex);
        totalRow.setHeightInPoints(25);
        CellStyle totalStyle = getTotalStyle(sheet);

        // 第一列写“总计”，不隐藏
        Cell cell0 = totalRow.createCell(0);
        cell0.setCellValue("总计");
        cell0.setCellStyle(totalStyle);
        Set<String> titleSet = new HashSet<>();
        String[] headers;
        if (zeroCols == null || zeroCols.isEmpty()) {
            headers = Arrays.copyOf(oldHeaders, oldHeaders.length);
        } else {
            Set<Integer> indexSet = new HashSet<>(zeroCols);
            headers = new String[oldHeaders.length - indexSet.size()];
            int newIndex = 0;
            for (int i = 0; i < oldHeaders.length; i++) {
                if (!indexSet.contains(i)) {
                    headers[newIndex++] = oldHeaders[i];
                }
            }
        }

        // 构建 dataMap：每列对应的总计值（不管是否隐藏，后续判断）
        Map<Integer, Object> dataMap = new HashMap<>();

        if ("社保".equals(sheetName)) {
            int index = Arrays.asList(headers).indexOf("当月服务人数");
            if (index !=-1){
                dataMap.put(index, totalServicePeople);
            }
//            int index1 = Arrays.asList(headers).indexOf("社保缴纳人次");
//            if (index1 !=-1){
//                dataMap.put(index1, totalSocialCount);
//            }
//            int index2 = Arrays.asList(headers).indexOf("社保补缴人次");
//            if (index2 !=-1){
//                dataMap.put(index2, totalSocialSupCount);
//            }
            int index3 = Arrays.asList(headers).indexOf("社保金额");
            if (index3 !=-1){
                dataMap.put(index3, totalSocialAmt);
            }
            titleSet.add("社保金额");
            int index4= Arrays.asList(headers).indexOf("利息/滞纳金");
            if (index4 !=-1){
                dataMap.put(index4, totalInterestAmt);
            }
            titleSet.add("利息/滞纳金");
            int index5= Arrays.asList(headers).indexOf("总计");
            if (index5 !=-1){
                dataMap.put(index5, totalFinalAmt);
            }
            titleSet.add("总计");
        } else if ("公积金".equals(sheetName)) {
            int index = Arrays.asList(headers).indexOf("当月服务人数");
            if (index !=-1){
                dataMap.put(index, totalProvidentServicePeople);
            }
//            int index1 = Arrays.asList(headers).indexOf("公积金汇缴人次");
//            if (index1 !=-1){
//                dataMap.put(index1, totalProvidentCount);
//            }
//            int index2 = Arrays.asList(headers).indexOf("公积金补缴人次");
//            if (index2 !=-1){
//                dataMap.put(index2, totalProvidentSupCount);
//            }
//            int index3 = Arrays.asList(headers).indexOf("补充公积金汇缴人次");
//            if (index3 !=-1){
//                dataMap.put(index3, totalSupProvidentCount);
//            }
//            int index4= Arrays.asList(headers).indexOf("补充公积金补缴人次");
//            if (index4 !=-1){
//                dataMap.put(index4, totalSupProvidentSupCount);
//            }

            int index5= Arrays.asList(headers).indexOf("住房公积金金额");
            if (index5 !=-1){
                dataMap.put(index5, totalHousingProvidentFund);
            }
            titleSet.add("住房公积金金额");
            int index6= Arrays.asList(headers).indexOf("补充公积金金额");
            if (index6 !=-1){
                dataMap.put(index6, totalSupProvidentAmt);
            }
            titleSet.add("补充公积金金额");
            int index7= Arrays.asList(headers).indexOf("总计");
            if (index7 !=-1){
                dataMap.put(index7, totalProvidentFinalAmt);
            }
            titleSet.add("总计");
        }


        for (Integer colIdx : dataMap.keySet()) {

            Cell cell = totalRow.createCell(colIdx);

            if (dataMap.containsKey(colIdx)) {
                Object val = dataMap.get(colIdx);
                if (val instanceof Number) {
                    String headerName = headers[colIdx]; // 因为 headers 从下标 0 开始，而列从 1 起写
                    if (titleSet.contains(headerName)) {
                        cell.setCellValue(format((BigDecimal) val));
                    } else {
                        cell.setCellValue(((Number) val).doubleValue());
                    }
                } else {
                    cell.setCellValue(val.toString());
                }
            } else {
                // 无数据列写空
                cell.setCellValue("");
            }
            cell.setCellStyle(totalStyle);
        }
    }





    private static void createBankInfoSection(Sheet sheet, String sheetName, List<PrintReceivingApplicationBankVo> bankInfoList,Map<String, String> infoMap) {
        if (bankInfoList == null || bankInfoList.isEmpty()) return;

        int startRow = sheet.getLastRowNum() + 2;
        int maxPerLine = 4; // 每行最多展示4个供应商信息

        // 定义字段标签
        String[] labels = "社保".equals(sheetName) ?
                new String[]{"供应商名称", "银行名称", "银行账号", "金额"} :
                new String[]{"供应商名称", "公积金比例", "支付类型", "详细支付类型","收款银行支行信息", "银行名称", "银行账号", "金额", "备注", "用途"};

        CellStyle headerStyle = getBankHeaderStyle(sheet);

        int totalItems = bankInfoList.size();
        int groupCount = (int) Math.ceil(totalItems / (double) maxPerLine);

        for (int g = 0; g < groupCount; g++) {
            int groupStartCol = 1; // 数据从 B 列开始
            int currentRowOffset = g * (labels.length + 1); // 每组之间空一行

            // 写入标签（A列）
            for (int i = 0; i < labels.length; i++) {
                Row row = sheet.getRow(startRow + currentRowOffset + i);
                if (row == null) {
                    row = sheet.createRow(startRow + currentRowOffset + i);
                }
                row.setHeightInPoints(25);
                Cell cell = row.createCell(0);
                cell.setCellValue(labels[i]);
                cell.setCellStyle(headerStyle);
            }

            // 写入每组的内容列（横向展示最多5个供应商）
            for (int i = 0; i < maxPerLine; i++) {
                int index = g * maxPerLine + i;
                if (index >= totalItems) break;

                PrintReceivingApplicationBankVo bankVo = bankInfoList.get(index);
                int colIdx = groupStartCol + i;

                // 写入内容到对应的行
                int rowIdx = startRow + currentRowOffset;

                writeCell(sheet, rowIdx++, colIdx, bankVo.getOrgName());
                if ("公积金".equals(sheetName)) {
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getRatioCode());
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getPayType());
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getPayTime());
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getSubBank());
                }
                writeCell(sheet, rowIdx++, colIdx, bankVo.getBankName());
                writeCell(sheet, rowIdx++, colIdx, bankVo.getBankNo());
                writeCell(sheet, rowIdx++, colIdx, format(bankVo.getAmount()));

                if ("公积金".equals(sheetName)) {
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getRemark());
                    writeCell(sheet, rowIdx++, colIdx, bankVo.getPurpose());
                }
            }
        }

        int footerStartRow = startRow + groupCount * (labels.length + 1) + 2;
        createBankInfoFooter(sheet, footerStartRow, infoMap);

    }

    private static void createBankInfoFooter(Sheet sheet, int startRow, Map<String, String> infoMap) {
        String[] labels = {"经办人", "区域负责人"};

        CellStyle headerStyle = getBankHeaderStyle(sheet);
        CellStyle contentStyle = getBoldCenteredCellStyle(sheet);

        Row labelRow = sheet.createRow(startRow);         // 创建表头行
        labelRow.setHeightInPoints(25);
        Row valueRow = sheet.createRow(startRow + 1);     // 创建内容行
        valueRow.setHeightInPoints(25);

        for (int i = 0; i < labels.length; i++) {
            // 表头单元格
            Cell labelCell = labelRow.createCell(i);
            labelCell.setCellValue(labels[i]);
            labelCell.setCellStyle(headerStyle);

            // 内容单元格
            Cell valueCell = valueRow.createCell(i);
            valueCell.setCellValue(Optional.ofNullable(infoMap.get(labels[i])).orElse(""));
            valueCell.setCellStyle(contentStyle);
        }
    }




    private static void writeCell(Sheet sheet, int rowIndex, int colIndex, Object value) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) row = sheet.createRow(rowIndex);
        Cell cell = row.createCell(colIndex);

        CellStyle boldStyle = getBoldCenteredCellStyle(sheet);
        if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            if (Objects.isNull(value)) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(String.valueOf(value));
            }
        }
        if (value == null || value.toString().trim().isEmpty()) {
            // 空值，不设置边框样式（或设置无边框样式）
            cell.setCellStyle(getNoBorderStyle(sheet));
        } else {
            cell.setCellValue(value.toString());
            cell.setCellStyle(boldStyle); // 有边框的样式
        }

    }


    private static CellStyle getBoldCenteredCellStyle(Sheet sheet) {
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 11); // 可调大小
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setWrapText(true); // 开启自动换行

        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }




    private static CellStyle getBankHeaderStyle(Sheet sheet) {
        CellStyle style = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 10);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }



    private static List<Integer> findAllZeroColumns(String sheetName, List<PrintReceivingApplicationFromVo> data) {
        List<Integer> result = new ArrayList<>();
        if (data == null || data.isEmpty()) return result;

        int[][] colDefs = "社保".equals(sheetName)
                ? new int[][]{{4, 'b'}, {5, 'b'}}
                : new int[][]{{4, 'b'}, {5, 'b'}, {7, 's'}};

        for (int[] def : colDefs) {
            int col = def[0];
            char type = (char) def[1];
            boolean allZero = true;

            for (PrintReceivingApplicationFromVo vo : data) {
                if (type == 's') {
                    // 字符串字段判断
                    String strVal = null;
                    switch (col) {
                        case 7:
                            strVal = vo.getRemark();
                            break;
                    }

                    if (strVal != null && !strVal.trim().isEmpty()) {
                        allZero = false;
                        break;
                    }
                } else {
                    Number val = null;
                    switch (col) {
                        case 4:
                            val = "社保".equals(sheetName) ? vo.getSocialAmt() : vo.getProvidentAmt();
                            break;
                        case 5:
                            val = "社保".equals(sheetName) ? vo.getInterestAmt() : vo.getSupProvidentAmt();
                            break;
                    }

                    if (val != null && (type == 'i' ? val.intValue() != 0 : val.doubleValue() != 0.0)) {
                        allZero = false;
                        break;
                    }
                }
            }

            if (allZero) result.add(col);
        }

        return result;
    }


    private static void setPrintSetting(Workbook workbook, Sheet sheet, String title) {
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setLandscape(true); // 横向打印
        printSetup.setPaperSize(PrintSetup.A4_PAPERSIZE);

        sheet.setFitToPage(true);
        printSetup.setFitWidth((short) 1);
        printSetup.setFitHeight((short) 0);

        sheet.setMargin(Sheet.TopMargin, 0.5);
        sheet.setMargin(Sheet.BottomMargin, 0.5);
        sheet.setMargin(Sheet.LeftMargin, 0.5);
        sheet.setMargin(Sheet.RightMargin, 0.5);
        sheet.setPrintGridlines(false);
        sheet.setHorizontallyCenter(true);

        // ✅ 设置每页重复打印第2行（即表头）
        String safeSheetName = sheet.getSheetName().replaceAll("[^\\w]", "_");
        String uniquePrintTitleName = "Print_Titles_" + safeSheetName;

        int nameIndex = workbook.getNameIndex(uniquePrintTitleName);
        if (nameIndex != -1) {
            workbook.removeName(nameIndex);
        }

        Name printTitle = workbook.createName();
        printTitle.setNameName(uniquePrintTitleName);
        printTitle.setRefersToFormula("'" + sheet.getSheetName() + "'!$2:$2");

        // ✅ 页眉（作为打印标题）
        Header header = sheet.getHeader();
        header.setCenter("&\"黑体,Bold\"&14" + title);

        // ✅ 页脚（页码 + 日期时间）
        Footer footer = sheet.getFooter();
        footer.setCenter("第 &P 页 / 共 &N 页");
        footer.setRight("打印时间: &D &T");
    }




    public static void closeInfo(HttpServletResponse response, XSSFWorkbook workbook, String fileName) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
        workbook.close();
    }




}
