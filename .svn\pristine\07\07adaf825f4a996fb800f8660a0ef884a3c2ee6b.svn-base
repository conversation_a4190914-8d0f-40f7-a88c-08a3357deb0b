<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<form class="layui-form" action="">
    <table class="layui-table" lay-skin="nob" style="width: 80%;">
        <tr>
            <td align="right"><i style="color: red;">*</i>账单起始月</td>
            <td align="left"><input type="text" class="layui-input" placeholder="yyyyMM" id="billMonth" readonly></td>
        </tr>
        <tr>
            <td align="right"><input type="checkbox" id="flag" lay-skin="primary"></td>
            <td align="left">没有下一账期则填写账单起始月</td>
        </tr>
    </table>

    <input type="hidden" id="parentIframeIndex">
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript">
    layui.use(['form', 'laydate'], function () {
        var form = layui.form, laydate = layui.laydate;
        var initYear;
        var d = new Date();
        var date = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + "01";
        laydate.render({
            elem: '#billMonth',
            type: 'month',
            trigger: 'click',
            min: date,
            max: '2099-12-12',
            showBottom: false,
            theme: 'grid',
            format: 'yyyyMM',
            // 点击即选中
            /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
            ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
                initYear = date.year;
            },
            change: function (value, date, endDate) {
                var selectYear = date.year;
                var differ = selectYear - initYear;
                if (differ == 0) {
                    if ($(".layui-laydate").length) {
                        $("#billMonth").val(value);
                        $(".layui-laydate").remove();
                    }
                }
                initYear = selectYear;
            }
        });
        form.render();
    });
</script>
</body>
</html>