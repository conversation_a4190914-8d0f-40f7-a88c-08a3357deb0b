/****************************************
 * Copyright (c) 2015 NiuWa.
 * All rights reserved.
 * Created on 2017年2月15日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.sp.utils;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @title 服务器信息工具包
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2017年2月15日
 */
public class ServerUtils {
	
	private final static Logger logger = LoggerFactory.getLogger(ServerUtils.class);
	
	private static String DEFAULT_CODE ="Unknown";
	private static String HOST_IP_NAME= DEFAULT_CODE;
	private static String HOST_IP= DEFAULT_CODE;
	private static String HOST_NAME= DEFAULT_CODE;
	private static byte[] HOST_IP_BYTE = null;
	
	static{
		try {
			int i = 0;
			String tmpHostAddress = DEFAULT_CODE;
			String tmpHostName = DEFAULT_CODE;
			Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
			while(interfaces.hasMoreElements()){
				NetworkInterface networkInterface = interfaces.nextElement();
				Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
				while(addresses.hasMoreElements()) {
					InetAddress inetAddress= addresses.nextElement();
					boolean isContinue=false;
					String netName=networkInterface.getDisplayName().toLowerCase();
					if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp() 
							|| (inetAddress instanceof Inet6Address) 
							|| netName.contains("virbr") || netName.contains("virtual")) {
						isContinue=true;
					}
					i++;
					try {
						tmpHostAddress = inetAddress.getHostAddress();
						tmpHostName = inetAddress.getHostName();
					} catch (Exception e) {
						logger.error("[Server Net Info] ServerUtils error!", e);
					}
					logger.info("[Server Net Info] net{}: {}  [isContinue：{}, HostName：{}, interface：{}, displayName:{}, isVirtual:{}]", i, tmpHostAddress, isContinue, tmpHostName, networkInterface.getName(), networkInterface.getDisplayName(), networkInterface.isVirtual());
					if(isContinue){
						continue;
					}
					if(DEFAULT_CODE.equals(HOST_IP)){//取第一个有效IP
					//if(true){//取最后一个有效IP
						HOST_IP = tmpHostAddress;
						HOST_NAME = tmpHostName;
						HOST_IP_BYTE = inetAddress.getAddress();
						HOST_IP_NAME = HOST_IP + " | " + HOST_NAME;
					}
				}
			}
			if("Unknown".equals(HOST_IP_NAME)){
				HOST_IP_NAME = HOST_IP_NAME + " | " + HOST_NAME;
			}
			logger.info("[Server Net Info] HOST_IP_NAME: " + HOST_IP_NAME);
			logger.info("[Server Net Info] HOST_IP: " + HOST_IP);
			logger.info("[Server Net Info] HOST_NAME: " + HOST_NAME);
		} catch (Exception e) {
			logger.error("ServerUtils,获取主机信息失败！",e);
		}
		
	}
	
	/**
	 * 获取主机名和ip
	 */
	public static String getHostNameIP(){
		return HOST_IP_NAME;
	}

	/**
	 * 获取当前服务器ip
	 */
	public static String getHostIp() {
		return HOST_IP;
	}
	/**
	 * 获取当前服务器名称
	 */
	public static String getHostName() {
		return HOST_NAME;
	}
	/**
	 * 获取当前服务器ip的byte[]
	 */
	public static byte[] getHostIpByte() {
		return HOST_IP_BYTE;
	}
	
	
}
