<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.SupplierMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.cus.Supplier">
    </resultMap>
    <sql id="Base_Column_List">
    id, supplier_name, contactor, tel, wechat, email, addr, purchaser, creator,create_time, updater,purchaser_org,purchaser_pos,commissioner,
    update_time,supplier_type,status
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insertSelective" parameterType="com.reon.hr.sp.customer.entity.cus.Supplier" useGeneratedKeys="true"
            keyProperty="id">
        insert into supplier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="supplierType != null">
                supplier_type ,
            </if>
            <if test="contactor != null">
                contactor,
            </if>
            <if test="tel != null">
                tel,
            </if>
            <if test="wechat != null">
                wechat,
            </if>
            <if test="purchaserOrg != null and purchaserOrg != ''">
                purchaser_org ,
            </if>
            <if test="purchaserPos != null and purchaserPos != ''">
                purchaser_pos ,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="addr != null">
                addr,
            </if>
            <if test="purchaser!=null">
                purchaser,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierType != null">
                #{supplierType,jdbcType=INTEGER},
            </if>
            <if test="contactor != null">
                #{contactor,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>

            <if test="wechat != null">
                #{wechat,jdbcType=VARCHAR},
            </if>
            <if test="purchaserOrg != null and purchaserOrg != ''">
                #{purchaserOrg},
            </if>
            <if test="purchaserPos != null and purchaserPos != ''">
                #{purchaserPos},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="addr != null">
                #{addr,jdbcType=VARCHAR},
            </if>
            <if test="purchaser!=null">
                #{purchaser,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status},
            </if>
        </trim>
        <selectKey resultType="long" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID() AS id
        </selectKey>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.customer.entity.cus.Supplier">
        update supplier
        <set>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierType != null">
                supplier_type = #{supplierType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="contactor != null">
                contactor = #{contactor,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                tel = #{tel,jdbcType=VARCHAR},
            </if>
            <if test="wechat != null">
                wechat = #{wechat,jdbcType=VARCHAR},
            </if>
            <if test="purchaserOrg != null and purchaserOrg != ''">
                purchaser_org = #{purchaserOrg}
            </if>
            <if test="purchaserPos != null and purchaserPos != ''">
                purchaser_pos = #{purchaserPos}
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="addr != null">
                addr = #{addr,jdbcType=VARCHAR},
            </if>
            <if test="purchaser!=null">
                purchaser=#{purchaser,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.customer.entity.cus.Supplier">
    update supplier
    set supplier_name = #{supplierName,jdbcType=VARCHAR},
       supplier_type = #{supplierType,jdbcType=INTEGER},
      contactor = #{contactor,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=VARCHAR},
      wechat = #{wechat,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      addr = #{addr,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
        update_time =now()
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="getSupplierListPage" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select distinct
        s.id,s.supplier_name,s.contactor,s.tel,s.wechat,s.email,s.addr,s.purchaser,s.creator,s.create_time,
        s.supplier_type,s.status as supplierStatus,s.commissioner,
        sc.contract_type as contractType,
        c.salary_commissioner as salaryCommissioner
        from supplier s
        left join supplier_area sa on s.id=sa.supplier_id
        left join supplier_contract sc on s.id=sc.supplier_id and sc.del_flag='N'
        left join contract c on sc.contract_no=c.contract_no and c.del_flag='N'
        where s.del_flag='N'
        <if test="userOrgPositionDtoList.size >0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" close=")" open="(" separator="or">
                (s.purchaser_org like concat( #{userOrgPositionDto.orgCode} ,'%') and  s.purchaser_pos like concat( #{userOrgPositionDto.posCode} ,'%')
                <if test="userOrgPositionDto.loginName != null and userOrgPositionDto.loginName != ''">
                    or  s.purchaser = #{userOrgPositionDto.loginName} or  sa.commissioner =#{userOrgPositionDto.loginName}
                </if>
                    )
            </foreach>
        </if>
        <if test="startTime!=null and startTime!=''">
            and sc.start_time  &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime!=''">
            and sc.end_time &lt; #{endTime}
        </if>
        <if test="provinceCode!=null and provinceCode!=''">
            and sa.province_code=#{provinceCode}
        </if>
        <if test="cityCode!=null and cityCode!=''">
            and sa.city_code=#{cityCode}
        </if>
        <if test="status!=null and status!=''">
            and sc.status = #{status}
        </if>
        <if test="keyword!=null and keyword!=''">
            and (s.supplier_name like concat('%',trim(#{keyword}),'%') or s.contactor like concat('%',trim(#{keyword}),'%'))
        </if>
        <if test="supplierType ==1">
            and supplier_type =1
        </if>
        <if test="supplierType !=1">
            and supplier_type in(2,3,4,5,6,7,8,9,10)
        </if>
        order by s.create_time desc
    </select>
    <select id="getSupplierList" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select distinct
        s.id,s.supplier_name,s.supplier_type,s.status as supplierStatus
        from supplier s left join supplier_area sa on s.id=sa.supplier_id
        where 1 = 1
        <if test="userOrgPositionDtoList.size >0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" close=")" open="(" separator="or">
                (s.purchaser_org like concat( #{userOrgPositionDto.orgCode} ,'%') and  s.purchaser_pos like concat( #{userOrgPositionDto.posCode} ,'%')
                <if test="userOrgPositionDto.loginName != null and userOrgPositionDto.loginName != ''">
                    or  s.purchaser = #{userOrgPositionDto.loginName} or  sa.commissioner =#{userOrgPositionDto.loginName}
                </if>
                    )
            </foreach>
        </if>

        <if test="supplierType ==1">
            and supplier_type =1
        </if>
        <if test="supplierType ==2">
            and supplier_type in(2,3,4)
        </if>
        order by s.create_time desc
    </select>
    <select id="getSupplierInfoById" parameterType="Long" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM supplier
        where id=#{supplierId} and del_flag='N'
    </select>
    <update id="deleteById" parameterType="Long">
        update supplier
        set del_flag='Y',updater=#{updater}
        where id in
        <foreach collection="ids" open="(" separator="," close=")" index="index" item="item">
          #{item}
        </foreach>
    </update>
    <select id="getSupplierListByArea"  resultType="com.reon.hr.api.customer.vo.org.OrgAndSupplierVo">
        select distinct CONCAT('SUPPLIER_', s.id) AS id,s.supplier_name AS name ,CONCAT(sa.province_code,sa.city_code) as cityCode
        from supplier s left join supplier_area sa on s.id=sa.supplier_id
        where s.del_flag='N'
        and s.status =2 <!--启用状态-->
        <if test="provinceCode!=null and provinceCode!=''">
        and sa.province_code=#{provinceCode}
        </if>
        <if test="cityCode!=null and cityCode!=''">
        and sa.city_code=#{cityCode}
        </if>
        <if test="priorityFlag!=null and priorityFlag!=''">
            and sa.priority_flag=#{priorityFlag}
        </if>
    </select>
    <select id="getSuppliersByArea"  resultType="com.reon.hr.api.customer.vo.org.OrgAndSupplierVo">
        select distinct
        CONCAT('SUPPLIER_', s.id) AS id,
        s.supplier_name AS name,
        sa.priority_flag AS priorityFlag,
        sa.city_code
        from supplier s left join supplier_area sa on s.id=sa.supplier_id
        where s.del_flag='N'
        and s.status =2 <!--启用状态-->
        <if test="provinceCode!=null and provinceCode!=''">
        and sa.province_code=#{provinceCode}
        </if>
        <if test="cityCode!=null and cityCode!=''">
        and sa.city_code=#{cityCode}
        </if>
        <if test="priorityFlag!=null and priorityFlag!=''">
            and sa.priority_flag=#{priorityFlag}
        </if>
    </select>
    <select id="getSupplierCountByCommissioner" resultType="int" parameterType="String">
        select COALESCE(count(id),0)
        from supplier
        where  del_flag='N'
    </select>
    <select id="getSupplierByNameAndCityCode" resultType="com.reon.hr.api.vo.sys.OrgVo">
        select DISTINCT concat('SUPPLIER_',s.id) as orgCode,s.supplier_name as orgName,concat(sa.province_code,sa.city_code) as ower_city
        from supplier s LEFT JOIN supplier_area sa on s.id=sa.supplier_id
        where s.del_flag='N'
        <if test="keyword !=null and keyword!=''">
            and s.supplier_name like concat('%',#{keyword},'%')
        </if>
        <if test="cityCode!=null and cityCode!=''">
            and concat(sa.province_code,sa.city_code) = #{cityCode,jdbcType=VARCHAR}
        </if>
        group by s.id
    </select>

    <select id="getAllSuppliers" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name,supplier_type
        from supplier
        where del_flag='N'
        <if test="supplierName!=null and supplierName!=''">
           and  supplier_name =#{supplierName}
        </if>
        order by update_time desc
    </select>

    <select id="selectAllEnableSupplier" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name,supplier_type
        from supplier
        where del_flag='N' and status=2
        order by update_time desc
    </select>

    <select id="selectAllEnableSupplierByType" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name,supplier_type
        from supplier
        where del_flag='N' and status=2 and supplier_type in
        <foreach collection="list" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        order by update_time desc
    </select>
    <select id="getSupplierName" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select supplier_name,
          id
        from supplier
        where del_flag='N'
        and status =2<!--启用状态数据-->
        <if test="id!=null">
        and  id =#{id}
        </if>
    </select>

    <select id="getAllSupplierByType" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name
        from supplier
        where del_flag='N' and supplier_type =1 and status =2
    </select>

    <select id="getAllSupplier" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name
        from supplier
        where del_flag='N'
    </select>

    <select id="getSupplierByIds" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        select id, supplier_name
        from supplier
        where del_flag='N' and id in
        <foreach collection="Ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getSupplierByCityCodeAndPriorityFlag"
            resultType="com.reon.hr.api.customer.vo.org.OrgAndSupplierVo">
        select distinct s.id,
        concat(sa.province_code, sa.city_code) as cityCode
        from supplier s
        left join supplier_area sa on s.id = sa.supplier_id
        where s.del_flag = 'N'
        and s.status = 2
        and sa.enable = 2
        and sa.priority_flag = #{priorityFlag}
        and
        <foreach collection="supplierVos" item="item" open="(" close=")" separator="or">
            (
            sa.supplier_id = #{item.receiving}
            and sa.province_code = SUBSTRING(#{item.cityCode}, 1, 2)
            and sa.city_code = SUBSTRING(#{item.cityCode}, 3, 4)
            )
        </foreach>
    </select>
    <select id="getSalarySupplier" resultType="com.reon.hr.api.customer.vo.supplier.SupplierVo">
        SELECT DISTINCT s.id,s.supplier_name
        from supplier_area sa
        LEFT JOIN supplier s on s.id=sa.supplier_id
        LEFT JOIN withholding_agent wa on wa.org_code=sa.supplier_id and
        wa.pay_place=CONCAT(sa.province_code,sa.city_code)
        LEFT JOIN `reon-customerdb`.salary_info si on si.withholding_agent_no=wa.withholding_agent_no
        LEFT JOIN `reon-customerdb`.salary_category sc on sc.id=si.salary_category_id
        LEFT JOIN `reon-customerdb`.contract c on c.contract_no=sc.contract_no
        LEFT join `reon-customerdb`.customer_invoice ci on ci.cust_id=c.cust_id
        LEFT JOIN `reon-customerdb`.contract_process cp on cp.contract_no = c.contract_no
        where wa.withholding_agent_type=3 and si.del_flag='N' and c.del_flag='N'
        and ci.id is not null and cp.approval_status = 4 and (c.associated_flag=1 or (c.associated_flag = 2 and c.distributable_flag = 2 ))
        <if test="userOrgPositionDtoList != null and userOrgPositionDtoList.size > 0">
            and
            <foreach collection="userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                     close=")">
                (
                (
                <if test="userOrgPositionDto.loginName != null">
                    sa.commissioner=#{userOrgPositionDto.loginName} or
                </if>
                (
                s.purchaser_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and s.purchaser_org like
                concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')
                <if test="userOrgPositionDto.loginName != null">
                    and s.purchaser=#{userOrgPositionDto.loginName}
                </if>
                )

                ) or
                    (
                    c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat (#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')
                    <if test="userOrgPositionDto.loginName != null">
                        and c.commissioner=#{userOrgPositionDto.loginName}
                    </if>
                    )
                )
            </foreach>
        </if>
    </select>
    <select id="getAllAggregationCommissioner"
            resultType="com.reon.hr.api.customer.vo.supplier.SupplierAreaVo">
        SELECT DISTINCT s.id supplier_id, s.supplier_name, sa.commissioner,s.purchaser
        from supplier s
        LEFT JOIN supplier_area sa on s.id = sa.supplier_id and sa.commissioner is not null
        where s.del_flag = 'N';
    </select>
    
    <select id="getSupplierIdByCommissioner" resultType="long">
        select s.id from supplier s left join supplier_area sa on s.id = sa.supplier_id where s.commissioner =#{loginName} or sa.commissioner =#{loginName}
    </select>
</mapper>