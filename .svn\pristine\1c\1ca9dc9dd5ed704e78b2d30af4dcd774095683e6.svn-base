/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/8/27
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.bill.service.impl.paymentApply;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.bill.enums.PaymentApplyDocumentStatusEnum;
import com.reon.hr.api.bill.enums.PaymentApplyProcessStatus;
import com.reon.hr.api.bill.enums.PracticeLockInfoPayStatusEnum;
import com.reon.hr.api.bill.enums.PracticePayMentTypeEnum;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.bill.utils.ListPageUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.bill.vo.insurancePractice.PackAmtDiffMonthlyVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.bill.vo.salary.*;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.salary.SalaryInfoStatus;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.salary.pay.EmpDelaySearchVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPaymentRefundInputVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.ActHiVarinstVo;
import com.reon.hr.api.workflow.vo.ParameVo;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.api.workflow.vo.WorkflowComentVo;
import com.reon.hr.common.enums.PaymentApplyPayTypeEnum;
import com.reon.hr.common.enums.PrintFlagEnum;
import com.reon.hr.common.enums.salary.PayServiceSerialDetailType;
import com.reon.hr.sp.bill.dao.bill.*;
import com.reon.hr.sp.bill.dao.cus.BillCostMapper;
import com.reon.hr.sp.bill.dao.insurancePractice.PracticeBillMapper;
import com.reon.hr.sp.bill.dao.insurancePractice.PracticeReportToPayMapper;
import com.reon.hr.sp.bill.dao.salary.*;
import com.reon.hr.sp.bill.entity.bill.PaymentApply;
import com.reon.hr.sp.bill.entity.insurancePractice.PracticePayDetail;
import com.reon.hr.sp.bill.entity.insurancePractice.PracticeReportToPay;
import com.reon.hr.sp.bill.entity.salary.PayApply2Batch;
import com.reon.hr.sp.bill.entity.salary.SalaryPayBatch;
import com.reon.hr.sp.bill.entity.salary.SynchronizationSupplierSalaryFeeRecord;
import com.reon.hr.sp.bill.service.bill.IBillCheckApprovalService;
import com.reon.hr.sp.bill.service.bill.financial.INetSilverService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IInsurancePracticeBillService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.PackAmtDiffMonthlyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.PayServiceSerialLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentApplyServiceImpl
 *
 * @date 2020/8/27 15:18
 */
@Service
public class PaymentApplyServiceImpl extends ServiceImpl<PaymentApplyMapper, PaymentApply> implements IPaymentApplyService {
    @Autowired
    private PaymentApplyMapper paymentApplyMapper;
    @Autowired
    private PayApply2BatchMapper apply2BatchMapper;
    @Autowired
    private SalaryPayBatchMapper salaryPayBatchMapper;
    @Autowired
    private IInsurancePracticeBillService insurancePracticeLockService;
    @Autowired
    private PracticeReportToPayMapper practiceReportToPayMapper;

    @Autowired
    private PracticeBillMapper practiceBillMapper;

    @Autowired
    private PackAmtDiffMonthlyService packAmtDiffMonthlyService;
    @Resource(name = "orgDubboService")
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Autowired
    private ISalaryPayWrapperService iSalaryPayWrapperService;
    @Autowired
    private ICustomerWrapperService customerWrapperService;
    @Autowired
    private ISupplierWrapperService supplierWrapperService;
    @Autowired
    private BillCostMapper billCostMapper;
    @Autowired
    private SynchronizationSupplierSalaryFeeRecordMapper synchronizationSupplierSalaryFeeRecordMapper;
    @Autowired
    private SalaryPayBatchReceivedMapper salaryPayBatchReceivedMapper;
    @Autowired
    private SalaryPayBatch2ReceivedMapper salaryPayBatch2ReceivedMapper;
    @Autowired
    private PaymentApplyLastDateLogMapper paymentApplyLastDateLogMapper;
    @Autowired
    private IUserWrapperService userWrapperService;
    @Autowired
    private PayServiceSerialLogMapper payServiceSerialLogMapper;
    @Autowired
    private SalaryPayBatchInfoMapper salaryPayBatchInfoMapper;
    @Autowired
    private RecBankRelativeMapper recBankRelativeMapper;
    @Autowired
    private IBillCheckApprovalService billCheckService;
    @Autowired
    private BillCheckMapper billCheckMapper;
    @Autowired
    private INetSilverService iNetSilverService;
    @Autowired
    private SalaryPaymentApplyMapper salaryPaymentApplyMapper;
    @Autowired
    private IWorkflowWrapperService workflowWrapperService;
    @Resource
    private ISequenceService iSequenceService;
    @Resource
    private PayServiceSerialLogService payServiceSerialLogService;

    @Resource
    private InsurancePracticeDisComPayMapper insurancePracticeDisComPayMapper;

    @Resource
    private InsurancePracticeOneFeeBalanceMapper insurancePracticeOneFeeBalanceMapper;
    @Override
    public List<PaymentApplyVo> getPaymentApplyListPage(Page page, Map<String, Object> map) {
        return paymentApplyMapper.getPaymentApplyListPage(page, map);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApprovalList(PaymentApplyVo paymentApplyVo) { 
        return paymentApplyMapper.getPaymentApprovalList(paymentApplyVo);
    }

    @Override
    public int insertSelective(PaymentApplyVo record) {
        //获取申请人分公司  几乎废弃
//        if (StringUtils.isNotEmpty(record.getPosCode())) {
//            String posCode = record.getPosCode().split(",")[1];
//            if (posCode.length() == 8) {
//                posCode = posCode.substring(0, 6);
//            }
//            record.setAppCom(posCode);
//        }
        //更新操作
        paymentApplyMapper.updateByPrimaryKeySelective (record);
        return 0;
    }

    @Override
    public boolean updateByPrimaryKeySelective(PaymentApplyVo record) {
        return paymentApplyMapper.updateByPrimaryKeySelective (record);
    }

    @Override
    public PaymentApplyVo selectByPrimaryKey(Long id) {
        return paymentApplyMapper.selectByPrimaryKey (id);
    }
    @Override
    public PaymentApplyVo selectSalaryById(Long id) {
        PaymentApplyVo paymentApplyVo = paymentApplyMapper.selectSalaryById(id);
        paymentApplyVo = getPaymentApplyVoList(Collections.singletonList(paymentApplyVo)).get(0);
        List<SalaryPayBatchVo> salaryPayBatchVoList = paymentApplyVo.getSalaryPayBatchVoList();
        Integer applycnt= salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getApplyCnt).reduce(0, Integer::sum);
        paymentApplyVo.setTotalApplyCnt(applycnt);
        paymentApplyVo.setTotalSalaryFee(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSalaryFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        return paymentApplyVo;
    }
    @Override
    public PaymentApplyVo selectDetailById(Long id) {
        PaymentApplyVo paymentApplyVo =  paymentApplyMapper.selectByPayMentId (id);
            paymentApplyVo = getPaymentApplyVoList(Collections.singletonList(paymentApplyVo)).get(0);
        return paymentApplyVo;
    }

    @Override
    public PaymentApplyVo selectDetailByPid(String pid) {
        return paymentApplyMapper.selectDetailByPid(pid);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApplyList(PaymentApplyVo paymentApplyVo) {
        List<PaymentApplyVo> paymentApplyVoList =paymentApplyMapper.getPaymentApplyList (paymentApplyVo);
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)){
            List<PayServiceSerialLogVo> payServiceSerialLogVoList = payServiceSerialLogMapper.getByPaymentIdList(paymentApplyVoList.stream().map(PaymentApplyVo::getId).collect(Collectors.toList()));
            List<String> psslList = payServiceSerialLogVoList.stream().map(p -> p.getPaymentId() + "-" + p.getPayServiceSerialDetailType()).collect(Collectors.toList());
            if(paymentApplyVo.getPayServiceSerialDetailType()!=null){
                paymentApplyVoList.removeIf(p->!psslList.contains(p.getId()+"-"+paymentApplyVo.getPayServiceSerialDetailType()));
            }
        }
        paymentApplyVoList=getPaymentApplyVoList(paymentApplyVoList);
        paymentApplyVoList=getPaymentApplyVoListByMatch(paymentApplyVo,paymentApplyVoList);
        return getPaymentApplyList(paymentApplyVoList);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApplyListByPidList(List<String> pidList) {
        return paymentApplyMapper.getPaymentApplyListByPidList(pidList);
    }

    @Override
    public ListPageUtil<PaymentApplyVo> getPaymentApplyListByAuth(PaymentApplyVo paymentApplyVo, List<OrgPositionDto> userOrgPositionDtoList) {
        for (OrgPositionDto dto:userOrgPositionDtoList) {
            String orgCode = dto.getOrgCode();
            int index = orgCode.indexOf("D");
            if(index>=0){
                dto.setOrgCode(orgCode.substring(0,index));
            }
        }
        List<PaymentApplyVo> paymentApplyVoList =paymentApplyMapper.getPaymentApplyListByAuth (paymentApplyVo,userOrgPositionDtoList);
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)&&paymentApplyVo.getOvertimeFlag()!=null){
            List<String> pidList = paymentApplyVoList.stream().map(PaymentApplyVo::getPid).collect(toList());
            List<ActHiVarinstVo> actHiVarinstVoList = workflowWrapperService.getActHiVarinstByOvertimeFlag(pidList, paymentApplyVo.getOvertimeFlag());
            List<String> oldPidList = actHiVarinstVoList.stream().map(ActHiVarinstVo::getProcInstId).collect(toList());
            if(CollectionUtils.isEmpty(oldPidList)){
                paymentApplyVoList=new ArrayList<>();
            }else {
                paymentApplyVoList.removeIf(p->!oldPidList.contains(p.getPid()));
            }
        }
        if (CollectionUtils.isNotEmpty(paymentApplyVoList)&&(paymentApplyVo.getDispatchPrintFlag()!=null||paymentApplyVo.getReceivingPrintFlag()!=null)){
            List<Long> payApplyIdList=salaryPaymentApplyMapper.getByPaymentApplyVo(paymentApplyVo,paymentApplyVoList.stream().map(PaymentApplyVo::getId).distinct().collect(toList()));
            paymentApplyVoList.removeIf(p->!payApplyIdList.contains(p.getId()));
        }
        paymentApplyVoList=getPaymentApplyVoList(paymentApplyVoList);
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)){
            if(StringUtils.isNotBlank(paymentApplyVo.getCommissioner())){
                paymentApplyVoList.removeIf(p->!paymentApplyVo.getCommissioner().equals(p.getCommissioner()));
            }
            if(StringUtils.isNotBlank(paymentApplyVo.getSalaryCommissioner())){
                paymentApplyVoList.removeIf(p->!paymentApplyVo.getSalaryCommissioner().equals(p.getSalaryCommissioner()));
            }
        }
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)&&CollectionUtils.isNotEmpty(userOrgPositionDtoList)&&CollectionUtils.isEmpty(paymentApplyVo.getSupplierIdList()) && !paymentApplyVo.getAdminFlag()){
            paymentApplyVoList=paymentApplyVoList.stream().filter(v->{
                        for (OrgPositionDto o:userOrgPositionDtoList) {
                            if(StringUtils.isNotBlank(o.getLoginName())){
                                if(o.getLoginName().equals(v.getApplicantCode())||o.getLoginName().equals(v.getCommissioner())||o.getLoginName().equals(v.getSalaryCommissioner())){
                                    return true;
                                }
                            }else {
                                return true;
                            }
                        }
                        return false;
                    }
            ).collect(toList());
        }
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)&&paymentApplyVo.getMatchFlag()==null && !paymentApplyVo.getAdminFlag()){
            List<PayServiceSerialLogVo> payServiceSerialLogVoList = payServiceSerialLogMapper.getByPaymentIdList(paymentApplyVoList.stream().map(PaymentApplyVo::getId).collect(Collectors.toList()));
            Map<String, PayServiceSerialLogVo> logVoMap = payServiceSerialLogVoList.stream().collect(toMap(p -> p.getPaymentId().toString() + p.getPayServiceSerialDetailType(), Function.identity()));
            List<PaymentApplyVo> bankCorporateVoList=new ArrayList<>();
            List<PaymentApplyVo> bankCorporateNotVoList=new ArrayList<>();
            List<OrgPositionDto> userOrgPositionDtoDispatchList = userOrgPositionDtoList.stream().filter(o -> (o.getPosCode().startsWith(PositionEnum.TREASURY_RECHECK_ATTACHE.getCode()) ||
                    o.getPosCode().startsWith(PositionEnum.TREASURY_DOCUMENTATION_ATTACHE.getCode()))).collect(toList());
            paymentApplyVoList.forEach(p->{
                String dispatchKey = p.getId().toString() + PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM.getCode();
                String receivingKey = p.getId().toString() + PayServiceSerialDetailType.SEND_PAYROLL_REV_COM.getCode();
                if(logVoMap.containsKey(dispatchKey)){
                    PayServiceSerialLogVo logVo = logVoMap.get(dispatchKey);
                    PaymentApplyVo bankCorporateVo = new PaymentApplyVo();
                    BeanUtils.copyProperties(p,bankCorporateVo);
                    bankCorporateVo.setPayServiceSerialDetailType(logVo.getPayServiceSerialDetailType());
                    //派单地银企直连
                    if(CollectionUtils.isNotEmpty(userOrgPositionDtoDispatchList)){
                        if (userOrgPositionDtoDispatchList.stream().anyMatch(o -> p.getPayCom().startsWith(o.getOrgCode()))) {
                            bankCorporateVoList.add(bankCorporateVo);
                        }else {
                            if(!logVoMap.containsKey(receivingKey)||
                                    userOrgPositionDtoDispatchList.stream().noneMatch(o -> (StringUtils.isNotBlank(p.getPayAssociatedCom())&&p.getPayAssociatedCom().startsWith(o.getOrgCode()))
                                    ||p.getRevCom().startsWith(o.getOrgCode()))){
                                //当接单地是非银企直连时，此时是非银企直连
                                bankCorporateVo.setPayServiceSerialDetailType(null);
                                bankCorporateNotVoList.add(bankCorporateVo);
                            }
                        }
                    }else {
                        bankCorporateVoList.add(bankCorporateVo);
                    }
                }else {
                    bankCorporateNotVoList.add(p);
                }
                if(logVoMap.containsKey(receivingKey)){
                    PayServiceSerialLogVo logVo = logVoMap.get(receivingKey);
                    PaymentApplyVo bankCorporateVo = new PaymentApplyVo();
                    BeanUtils.copyProperties(p,bankCorporateVo);
                    bankCorporateVo.setPayServiceSerialDetailType(logVo.getPayServiceSerialDetailType());
                    //接单地银企直连
                    if(CollectionUtils.isNotEmpty(userOrgPositionDtoDispatchList)){
                        if (userOrgPositionDtoDispatchList.stream().anyMatch(o -> (StringUtils.isNotBlank(p.getPayAssociatedCom())&&p.getPayAssociatedCom().startsWith(o.getOrgCode()))
                                ||p.getRevCom().startsWith(o.getOrgCode()))) {
                            bankCorporateVoList.add(bankCorporateVo);
                        }else if(bankCorporateVoList.stream().noneMatch(b->b.getId().equals(bankCorporateVo.getId()))){
                            //当派单地是非银企直连时，此时是非银企直连
                            bankCorporateVo.setPayServiceSerialDetailType(null);
                            bankCorporateNotVoList.add(bankCorporateVo);
                        }
                    }else {
                        bankCorporateVoList.add(bankCorporateVo);
                    }
                }else if(!Objects.equals(p.getPayType(), PaymentApplyPayTypeEnum.SALARY.getCode())&&!Objects.equals(p.getPayType(), PaymentApplyPayTypeEnum.SUPPLIER.getCode())){
                    if(CollectionUtils.isNotEmpty(userOrgPositionDtoDispatchList)){
                        if(bankCorporateVoList.stream().noneMatch(b->b.getId().equals(p.getId()))){
                            //当派单地是非银企直连时，此时是非银企直连
                            bankCorporateNotVoList.add(p);
                        }
                    }else {
                        bankCorporateNotVoList.add(p);
                    }
                }
                /*if(logVoMap.containsKey(dispatchKey)){
                    List<PayServiceSerialLogVo> logVoList = logVoMap.get(p.getId());
                    for (PayServiceSerialLogVo logVo : logVoList) {
                        PaymentApplyVo bankCorporateVo = new PaymentApplyVo();
                        BeanUtils.copyProperties(p,bankCorporateVo);
                        bankCorporateVo.setPayServiceSerialDetailType(logVo.getPayServiceSerialDetailType());
                        if (logVo.getPayServiceSerialDetailType() == PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM.getCode()) {
                            //派单地银企直连
                            if(CollectionUtils.isNotEmpty(userOrgPositionDtoDispatchList)){
                                if (userOrgPositionDtoDispatchList.stream().anyMatch(o -> p.getPayCom().startsWith(o.getOrgCode()))) {
                                    bankCorporateVoList.add(bankCorporateVo);
                                }else {
                                    bankCorporateNotVoList.add(bankCorporateVo);
                                }
                            }else {
                                bankCorporateVoList.add(bankCorporateVo);
                            }
                        } else {
                            //接单地银企直连
                            if(CollectionUtils.isNotEmpty(userOrgPositionDtoDispatchList)){
                                if (userOrgPositionDtoDispatchList.stream().anyMatch(o -> (StringUtils.isNotBlank(p.getPayAssociatedCom())&&p.getPayAssociatedCom().startsWith(o.getOrgCode()))
                                        ||p.getRevCom().startsWith(o.getOrgCode()))) {
                                    bankCorporateVoList.add(bankCorporateVo);
                                }else {
                                    bankCorporateNotVoList.add(bankCorporateVo);
                                }
                            }else {
                                bankCorporateVoList.add(bankCorporateVo);
                            }
                        }
                    }
                }else {
                    bankCorporateNotVoList.add(p);
                }*/
            });
            Map<Long, PaymentApplyVo> bankCorporateVoMap = bankCorporateVoList.stream().collect(Collectors.toMap(PaymentApplyVo::getId, Function.identity(), (v1, v2) -> v1));
            Map<Long, PaymentApplyVo> bankCorporateNotVoMap = bankCorporateNotVoList.stream().collect(Collectors.toMap(PaymentApplyVo::getId, Function.identity(), (v1, v2) -> v1));
            if(paymentApplyVo.getPayServiceSerialDetailType()!=null){
                //银企直连查询
                List<Long> idQueryList = paymentApplyVo.getIdList();
                if(CollectionUtils.isNotEmpty(idQueryList)){
                    //导出
                    paymentApplyVoList=bankCorporateVoList;
                    //已经自有复核的且银企直连的才能添加进来
                    paymentApplyVoList.removeIf(p->p.getPayServiceSerialDetailType()== PayServiceSerialDetailType.SEND_PAYROLL_REV_COM.getCode()
                            &&p.getDocumentStatus()!=PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode());
                    // 使用Comparator根据另一个list进行排序
                    paymentApplyVoList.sort(new Comparator<PaymentApplyVo>() {
                        @Override
                        public int compare(PaymentApplyVo o1, PaymentApplyVo o2) {
                            int index1 = idQueryList.indexOf(o1.getId());
                            int index2 = idQueryList.indexOf(o2.getId());
                            return Integer.compare(index1, index2);
                        }
                    });
                }else {
                    paymentApplyVoList=new ArrayList<>(bankCorporateVoMap.values());
                }
            }else{
                //非银企直连查询
                paymentApplyVoList=new ArrayList<>(bankCorporateNotVoMap.values());
            }
        }
        paymentApplyVoList=getPaymentApplyVoListByMatch(paymentApplyVo,paymentApplyVoList);
        List<PaymentApplyVo> paymentApplyList = getPaymentApplyList(paymentApplyVoList);
        if(paymentApplyVo.getPage()==0){
            paymentApplyVo.setPage(1);
            paymentApplyVo.setLimit(paymentApplyList.size());
        }
        paymentApplyList.sort(Comparator.comparing(PaymentApplyVo::getCreateTime).reversed());
        ListPageUtil<PaymentApplyVo> pager = new ListPageUtil<PaymentApplyVo>(paymentApplyList, paymentApplyVo.getLimit());
        pager.getPagedList(paymentApplyVo.getPage(),true);
        return pager;
    }

    @Override
    public List<PaymentApplyVo> getPaymentApplyVoListByMatch(PaymentApplyVo paymentApplyVo,List<PaymentApplyVo> paymentApplyVoList){
        if(paymentApplyVo.getMatchFlag()!=null&&CollectionUtils.isNotEmpty(paymentApplyVoList)){
            List<Long> billCheckIdList = paymentApplyVo.getBillCheckIdList();
            if(CollectionUtils.isNotEmpty(billCheckIdList)){
                Map<Long,PaymentApplyVo> paymentIdByBillCheckIdMap=new HashMap<>();
                for (PaymentApplyVo applyVo:paymentApplyVoList) {
                    List<String> billCheckIdStrList = applyVo.getSalaryPayBatchVoList().stream().map(SalaryPayBatchVo::getBillCheckId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    List<Long> billCheckIdListA =new ArrayList<>();
                    for (String billCheckIdStr:billCheckIdStrList) {
                        billCheckIdListA.addAll(Arrays.stream(billCheckIdStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList()));
                    }
                    for (Long id:billCheckIdListA) {
                        if(!paymentIdByBillCheckIdMap.containsKey(id)){
                            paymentIdByBillCheckIdMap.put(id,applyVo);
                        }
                    }
                }
                List<PaymentApplyVo> paymentApplyVoByBillCheckIdList =new ArrayList<>();
                for (Long id:billCheckIdList) {
                    if(paymentIdByBillCheckIdMap.containsKey(id)){
                        paymentApplyVoByBillCheckIdList.add(paymentIdByBillCheckIdMap.get(id));
                    }
                }
                paymentApplyVoList=paymentApplyVoByBillCheckIdList.stream().distinct().collect(Collectors.toList());
            }
            releaseMatchingData(paymentApplyVoList,paymentApplyVo.getUpdater());
            List<PaymentApplyVo> paymentApplyVoMatchList =new ArrayList<>();
            List<Long> recIdList = paymentApplyVoList.stream()
                    .flatMap(p -> p.getSalaryPayBatchVoList().stream())
                    .flatMap(s -> s.getSalaryPayBatchReceivedVoList().stream())
                    .map(SalaryPayBatchReceivedVo::getId)
                    .collect(Collectors.toList());
            Map<Long, RecBankRelativeVo> recBankRelativeVoMap =new HashMap<>();
            if(CollectionUtils.isNotEmpty(recIdList)){
                List<RecBankRelativeVo> voListByRecIdList = recBankRelativeMapper.getVoListByRecIdList(recIdList);
                recBankRelativeVoMap = voListByRecIdList.stream().collect(Collectors.toMap(RecBankRelativeVo::getRecId, Function.identity(), (v1, v2) -> v2));
            }
            for (PaymentApplyVo applyVo:paymentApplyVoList) {
                StringBuilder billCheckId= new StringBuilder();
                boolean recMatchFlag=true;//都匹配成功
                b:for (SalaryPayBatchVo salaryPayBatchVo:applyVo.getSalaryPayBatchVoList()) {
                    if(StringUtils.isBlank(salaryPayBatchVo.getBillCheckId())){
                        List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList = salaryPayBatchVo.getSalaryPayBatchReceivedVoList();
                        BigDecimal amtTotal = salaryPayBatchReceivedVoList.stream().map(SalaryPayBatchReceivedVo::getReceivedPaymentAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                        //如果单笔申请支付金额大于预计到款金额则不能走匹配要去走核销
                        //例如：
                        //支付金额          10000			50000
                        //预计到款金额       8000			8000   (薪资计算增加发放批次时填的)
                        //预计到款金额		            52000  (生成支付时填的)
                        if(applyVo.getPayAmt().compareTo(amtTotal)>0){
                            recMatchFlag = false;
                            break b;
                        }
                        for (SalaryPayBatchReceivedVo salaryPayBatchReceivedVo: salaryPayBatchReceivedVoList) {
                            if (!recBankRelativeVoMap.containsKey(salaryPayBatchReceivedVo.getId())) {
                                recMatchFlag = false;//有一次匹配失败就失败
                                break b;
                            }
                        }
                    }else {
                        billCheckId.append(",").append(salaryPayBatchVo.getBillCheckId());
                    }
                }
                if(!paymentApplyVoMatchList.contains(applyVo)&&
                        (StringUtils.isNotBlank(billCheckId)||(recMatchFlag)||applyVo.getAnewPayFlag()==BooleanTypeEnum.YES.getCode())
                ){
                    paymentApplyVoMatchList.add(applyVo);
                }
            }
            if(paymentApplyVo.getMatchFlag()){
                return paymentApplyVoMatchList;
            }else {
                if(CollectionUtils.isNotEmpty(paymentApplyVoMatchList)){
                    paymentApplyVoList.removeAll(paymentApplyVoMatchList);
                }
            }
        }
        return paymentApplyVoList;
    }

    @Override
    public List<TaskVo> getTaskVoList(ParameVo parameVo,PaymentApplyVo paymentApplyVo, HashMap<Object, TaskVo> taskVoMap, Map<String, WorkflowComentVo> finalWorkflowAuditLogListMap) {
        List<PaymentApplyVo> paymentApplyList = getPaymentApplyList(paymentApplyVo);
        List<TaskVo> taskList = Lists.newArrayList();
        for (PaymentApplyVo paymentApply:paymentApplyList) {
            if (taskVoMap.containsKey(paymentApply.getPid())&&
                    (PaymentApplyProcessStatus.REJECTED.getCode()!=paymentApply.getAppStatus()||StringUtils.isBlank(paymentApplyVo.getUpdater()))
            ) {
                if(parameVo.getDocumentStatus()==null||
                        (parameVo.getDocumentStatus()!=null&&
                                (parameVo.getDocumentStatus().equals(paymentApply.getDocumentStatus())||
                                        (parameVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode())&&
                                                paymentApply.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode())&&
                                                (parameVo.getDocumentStatusQuery()==null||parameVo.getDocumentStatusQuery().equals(paymentApply.getDocumentStatus()))
                                        )
                                )
                        )
                ){
                    TaskVo taskVo = taskVoMap.get(paymentApply.getPid());
                    BeanUtils.copyProperties (paymentApply,taskVo);
                    taskVo.setSalaryPayBatchVoListStr(JsonUtil.beanToJson(paymentApply.getSalaryPayBatchVoList()));
                    taskVo.setPaymentApplyId(paymentApply.getId());
                    String starterKey=taskVo.getProcessInstanceId()+"-"+"starter";//客服提交
                    String comManagerKey=taskVo.getProcessInstanceId()+"-"+"comManager";//客服复核
                    String financialOfficer=taskVo.getProcessInstanceId()+"-"+"financialOfficer";//财务制单
                    String financialManager=taskVo.getProcessInstanceId()+"-"+"financialManager";//财务复核
                    String secondFinancialOfficer=taskVo.getProcessInstanceId()+"-"+"secondFinancialOfficer";//发薪地财务制单
                    String secondFinancialManager=taskVo.getProcessInstanceId()+"-"+"secondFinancialManager";//发薪地财务复核
                    if(finalWorkflowAuditLogListMap.containsKey(starterKey)){
                        taskVo.setStarter(finalWorkflowAuditLogListMap.get(starterKey).getCreator());
                    }
                    if(finalWorkflowAuditLogListMap.containsKey(comManagerKey)){
                        taskVo.setComManager(finalWorkflowAuditLogListMap.get(comManagerKey).getCreator());
                    }
                    if(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode()==taskVo.getDocumentStatus()||
                            PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode()==taskVo.getDocumentStatus()){
                        if(finalWorkflowAuditLogListMap.containsKey(financialOfficer)&&finalWorkflowAuditLogListMap.get(financialOfficer)!=null){
                            taskVo.setFinancialOfficer(finalWorkflowAuditLogListMap.get(financialOfficer).getCreator());
                        }
                        if(finalWorkflowAuditLogListMap.containsKey(financialManager)&&finalWorkflowAuditLogListMap.get(financialManager)!=null){
                            taskVo.setFinancialManager(finalWorkflowAuditLogListMap.get(financialManager).getCreator());
                        }
                    }else if(PaymentApplyDocumentStatusEnum.REVIEWED.getCode()==taskVo.getDocumentStatus()||
                            PaymentApplyDocumentStatusEnum.OWN_DOCUMENT_MADE.getCode()==taskVo.getDocumentStatus()){
                        if(finalWorkflowAuditLogListMap.containsKey(secondFinancialOfficer)&&finalWorkflowAuditLogListMap.get(secondFinancialOfficer)!=null){
                            taskVo.setFinancialOfficer(finalWorkflowAuditLogListMap.get(secondFinancialOfficer).getCreator());
                        }
                        if(finalWorkflowAuditLogListMap.containsKey(secondFinancialManager)&&finalWorkflowAuditLogListMap.get(secondFinancialManager)!=null){
                            taskVo.setFinancialManager(finalWorkflowAuditLogListMap.get(secondFinancialManager).getCreator());
                        }
                    }
                    taskList.add(taskVo);
                }
            }
        }
        return taskList;
    }

    @Override
    public Page<Map<String, Object>> getSalaryMapListByVo(PaymentApplyVo paymentApplyVo, SalaryPaymentRefundInputVo vo, Integer limit, Integer page) {
        List<PaymentApplyVo> paymentApplyList = getPaymentApplyList(paymentApplyVo);
        Map<Long, PaymentApplyVo> paymentApplyVoMap = paymentApplyList.stream().collect(Collectors.toMap(PaymentApplyVo::getId, Function.identity()));
        List<SalaryPayBatchVo> salaryPayBatchVoList = new ArrayList<>();
        for (PaymentApplyVo applyVo : paymentApplyList) {
            if(CollectionUtils.isNotEmpty(applyVo.getSalaryPayBatchVoList())){
                salaryPayBatchVoList.addAll(applyVo.getSalaryPayBatchVoList());
            }
        }
        Map<Long, SalaryPayBatchVo> salaryPayBatchVoMap = salaryPayBatchVoList.stream().collect(toMap(SalaryPayBatchVo::getId, Function.identity()));
        List<Long> batchIdList = Arrays.asList(salaryPayBatchVoMap.keySet().toArray(new Long[0]));
        if(CollectionUtils.isNotEmpty(batchIdList)){
            Page<Map<String, Object>> mapPage = iSalaryPayWrapperService.selectSalaryEmpByBatchIdList(batchIdList, vo, limit, page);
            List<Map<String, Object>> records = mapPage.getRecords();
            for (Map<String, Object> map:records) {
                Long batchId = Long.parseLong(map.get("batchId").toString());
                if (salaryPayBatchVoMap.containsKey(batchId)) {
                    Long paymentId = salaryPayBatchVoMap.get(batchId).getPaymentId();
                    map.put("lastDate",paymentApplyVoMap.get(paymentId).getLastDate());
                    map.put("paymentId",paymentId);
                }
            }
            return mapPage;
        }
        return null;
    }

    /**
     * 释放匹配数据,并重新匹配
     * @param paymentApplyVoList
     */
    public void releaseMatchingData(List<PaymentApplyVo> paymentApplyVoList,String loginName){
        if(CollectionUtils.isNotEmpty(paymentApplyVoList)){
            List<InsuranceBillVo> insuranceBillVoList = paymentApplyVoList.stream()
                    .flatMap(p -> p.getSalaryPayBatchVoList().stream())
                    .map(s->{
                        InsuranceBillVo insuranceBillVo=new InsuranceBillVo();
                        insuranceBillVo.setContractNo(s.getContractNo());
                        insuranceBillVo.setTempletId(s.getTempletId());
                        insuranceBillVo.setBillMonth(s.getBillMonth());
                        return insuranceBillVo;
                    })
                    .collect(Collectors.toList());
            List<BillCheckVo> billCheckVoList = billCheckService.getBillCheckByTempletIdAndBillMonth(insuranceBillVoList);
            //全部核销的
            List<String> checkedMap = billCheckVoList.stream().filter(b -> b.getUncheckAmtTotal().compareTo(BigDecimal.ZERO) == 0).map(b -> b.getContractNo() + "-" + b.getBillMonth() + "-" + b.getTempletId()).collect(Collectors.toList());
            billCheckVoList = billCheckVoList.stream().filter(b -> checkedMap.contains(b.getContractNo() + "-" + b.getBillMonth() + "-" + b.getTempletId()))
                    .collect(Collectors.toList());
            List<Long> payCustIdQueryList = billCheckVoList.stream().map(BillCheckVo::getPayCustId).distinct().collect(Collectors.toList());
            Map<String, List<BillCheckVo>> billCheckVoMap = billCheckVoList.stream().collect(Collectors.groupingBy(
                    b -> b.getContractNo() + "-" + b.getBillMonth() + "-" + b.getTempletId())
            );
            //根据核销的到款反查已匹配且已经过财务处理的数据
            List<Long> payCustIdMatchedList =new ArrayList<>();
            if(CollectionUtils.isNotEmpty(payCustIdQueryList)){
                List<RecBankRelativeVo> voListByPayCustIdList=recBankRelativeMapper.getVoListByPayCustIdList(payCustIdQueryList);
                payCustIdMatchedList = voListByPayCustIdList.stream().map(RecBankRelativeVo::getPayCustId).collect(Collectors.toList());
            }
            List<Long> recIdDeleteList=new ArrayList<>();
            List<Long> applyUnmatchIdList=new ArrayList<>();
            List<SalaryPayBatchVo> salaryPayBatchVoList=new ArrayList<>();
            for (PaymentApplyVo applyVo:paymentApplyVoList) {
                boolean deleteFlag=true;
                List<Long> recIdDeleteByApplyList=new ArrayList<>();
                for (SalaryPayBatchVo salaryPayBatchVo:applyVo.getSalaryPayBatchVoList()) {
                    String key=salaryPayBatchVo.getContractNo()+"-"+salaryPayBatchVo.getBillMonth()+"-"+salaryPayBatchVo.getTempletId();
                    if(billCheckVoMap.containsKey(key)){
                        List<BillCheckVo> billCheckVos = billCheckVoMap.get(key);
                        String billCheckId = billCheckVos.stream().map(b -> b.getId().toString()).collect(Collectors.joining(","));
                        salaryPayBatchVo.setBillCheckId(billCheckId);
                        for (BillCheckVo billCheckVo:billCheckVos) {
                            //如果根据核销的到款能查到财务已经操作的数据,
                            // 不能释放匹配数据，不能视为核销
                            // 例：A1匹配B1,A2匹配B2,B2核销A1,若A2已经被财务操作过，则A2不能释放数据，A1不能视为核销
                            if(payCustIdMatchedList.contains(billCheckVo.getPayCustId())){
                                salaryPayBatchVo.setBillCheckId(null);
                                deleteFlag=false;
                                if(!applyUnmatchIdList.contains(applyVo.getId())){
                                    applyUnmatchIdList.add(applyVo.getId());
                                }
                            }
                        }
                        for (SalaryPayBatchReceivedVo salaryPayBatchReceivedVo:salaryPayBatchVo.getSalaryPayBatchReceivedVoList()) {
                            recIdDeleteByApplyList.add(salaryPayBatchReceivedVo.getId());
                        }
                    }else {
                        //没有核销数据时清空核销id
                        salaryPayBatchVo.setBillCheckId(null);
                    }
                    salaryPayBatchVoList.add(salaryPayBatchVo);
                }
                if(deleteFlag){
                    recIdDeleteList.addAll(recIdDeleteByApplyList);
                }
            }
            //修改存储的核销id
            if(CollectionUtils.isNotEmpty(salaryPayBatchVoList)){
                salaryPayBatchMapper.updateBillCheckIdList(salaryPayBatchVoList);
            }
            //释放已经匹配过的，现在核销的
            if(CollectionUtils.isNotEmpty(recIdDeleteList)){
                recBankRelativeMapper.deleteByRecIdList(recIdDeleteList);
                iNetSilverService.handlePayCustomerMatchSalrayRece(loginName);
            }
        }
    }

    public List<PaymentApplyVo> getPaymentApplyVoList(List<PaymentApplyVo> paymentApplyVoList){
        CompletableFuture<Map<String, String>> userFuture = CompletableFuture.supplyAsync(() -> userWrapperService.getAllUserMap());

        if(CollectionUtils.isNotEmpty(paymentApplyVoList)&&paymentApplyVoList.get(0)!=null){
            List<Long> paymentIdList = paymentApplyVoList.stream().map(PaymentApplyVo::getId).collect(Collectors.toList());
            List<SalaryPayBatchVo> salaryPayBatchVoList=salaryPayBatchMapper.selectByPaymentIdList(paymentIdList);
            List<Long> billCheckIdList = salaryPayBatchVoList.stream().filter(vo ->StringUtils.isNotBlank(vo.getBillCheckId())).flatMap(vo -> Arrays.stream(vo.getBillCheckId().split(","))).map(Long::valueOf).distinct().collect(toList());
            Map<Long, BillCheckVo> billCheckVoMap =new HashMap<>();
            if(CollectionUtils.isNotEmpty(billCheckIdList)){
                List<BillCheckVo> billCheckVoList=billCheckMapper.getPayCustByCheckId(billCheckIdList);
                billCheckVoMap = billCheckVoList.stream().collect(Collectors.toMap(BillCheckVo::getId, Function.identity(), (v1, v2) -> v1));
            }

            List<PaymentApplyVo> finalPaymentApplyVoList = paymentApplyVoList;
            CompletableFuture<Map<Long, String>> custFuture = CompletableFuture.supplyAsync(() -> {
                List<Long> custIdList = salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getCustId).collect(Collectors.toList());
                custIdList.addAll(finalPaymentApplyVoList.stream().map(PaymentApplyVo::getCustId).collect(Collectors.toList()));
                List<CustomerVo> customerVoList = customerWrapperService.getCustNameByCustIdList(custIdList);
                Map<Long, String> custMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
                return custMap;
            });

            CompletableFuture<Map<Long, List<SalaryPayBatchReceivedVo>>> salaryPayBatchReceivedVoListFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> batchIdList = salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getId).collect(Collectors.toList());
                Map<Long, List<SalaryPayBatchReceivedVo>> salaryPayBatchReceivedVoListMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(batchIdList)) {
                    List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList = salaryPayBatchReceivedMapper.getByBatchIdList(batchIdList);
                salaryPayBatchReceivedVoListMap = salaryPayBatchReceivedVoList.stream().collect(Collectors.groupingBy(SalaryPayBatchReceivedVo::getBatchId));
            }
                return salaryPayBatchReceivedVoListMap;
            });

            CompletableFuture<Map<Long, SalaryPayVo>> salaryPayVoFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> payIdList = salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getPayId).collect(Collectors.toList());
                Map<Long, SalaryPayVo> salaryPayVoMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(payIdList)) {
                List<SalaryPayVo> billTempletByPayIdList = iSalaryPayWrapperService.getBillTempletByPayIdList(payIdList);
                    salaryPayVoMap = billTempletByPayIdList.stream().collect(Collectors.toMap(SalaryPayVo::getId, Function.identity()));
                }
                return salaryPayVoMap;
            });
            Map<String, String> allUserMap;
            Map<Long, String> custMap;
            Map<Long, List<SalaryPayBatchReceivedVo>> salaryPayBatchReceivedVoListMap;
            Map<Long, SalaryPayVo> salaryPayVoMap;
            try {
                allUserMap = userFuture.get(15, TimeUnit.SECONDS);
                custMap = custFuture.get(15, TimeUnit.SECONDS);
                salaryPayBatchReceivedVoListMap = salaryPayBatchReceivedVoListFuture.get(15, TimeUnit.SECONDS);
                salaryPayVoMap = salaryPayVoFuture.get(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

                for (SalaryPayBatchVo salaryPayBatchVo:salaryPayBatchVoList) {
                    if(salaryPayVoMap.containsKey(salaryPayBatchVo.getPayId())){
                        SalaryPayVo salaryPayVo = salaryPayVoMap.get(salaryPayBatchVo.getPayId());
                        salaryPayBatchVo.setTempletId(salaryPayVo.getTempletId());
                        salaryPayBatchVo.setContractNo(salaryPayVo.getContractNo());
                        salaryPayBatchVo.setContractType(salaryPayVo.getContractType());
                        salaryPayBatchVo.setSubTypes(salaryPayVo.getSubTypes());
                        salaryPayBatchVo.setCustName(custMap.get(salaryPayBatchVo.getCustId()));
                        salaryPayBatchVo.setCommOrg(salaryPayVo.getCommOrg());
                        salaryPayBatchVo.setCommPos(salaryPayVo.getCommPos());
                        salaryPayBatchVo.setCommissioner(salaryPayVo.getCommissioner());
                        salaryPayBatchVo.setSalaryCommOrg(salaryPayVo.getSalaryCommOrg());
                        salaryPayBatchVo.setSalaryCommPos(salaryPayVo.getSalaryCommPos());
                        salaryPayBatchVo.setSalaryCommissioner(salaryPayVo.getSalaryCommissioner());
                        salaryPayBatchVo.setCompetitionFlag(salaryPayVo.getCompetitionFlag());
                    }
                    if(salaryPayBatchReceivedVoListMap.containsKey(salaryPayBatchVo.getId())){
                        salaryPayBatchVo.setSalaryPayBatchReceivedVoList(salaryPayBatchReceivedVoListMap.get(salaryPayBatchVo.getId()));
                    }
                }

            Map<Long, List<SalaryPayBatchVo>> salaryPayBatchListMap = salaryPayBatchVoList.stream().collect(Collectors.groupingBy(SalaryPayBatchVo::getPaymentId));

            paymentApplyVoList = new ArrayList<>(paymentApplyVoList);
            paymentApplyVoList.removeIf(o->!salaryPayBatchListMap.containsKey(o.getId()));
            for (PaymentApplyVo paymentApplyVo:paymentApplyVoList) {
                paymentApplyVo.setCustName(custMap.get(paymentApplyVo.getCustId()));
                if(salaryPayBatchListMap.containsKey(paymentApplyVo.getId())){
                    List<SalaryPayBatchVo> voList = salaryPayBatchListMap.get(paymentApplyVo.getId());
                    paymentApplyVo.setSalaryPayBatchVoList(voList);
                    List<SalaryPayBatchReceivedVo> voReceivedVoList = new ArrayList<>(voList.stream()
                            .flatMap(s -> s.getSalaryPayBatchReceivedVoList().stream())
                            .collect(Collectors.toMap(SalaryPayBatchReceivedVo::getId,Function.identity(),(v1, v2)->v1)).values());
                    String salaryPayBatchReceivedStr = "";
                    for (SalaryPayBatchReceivedVo receivedVo:voReceivedVoList) {
                        salaryPayBatchReceivedStr+=DateUtil.formatDateToString(receivedVo.getReceivedPaymentTime(),DateUtil.DATE_FORMAT_YYYY_MM_DD)+"_"+receivedVo.getReceivedPaymentName()+"_"+receivedVo.getReceivedPaymentAmt()+";";
                    }
                    List<Long> paymentBillCheckIdList = voList.stream().filter(vo ->StringUtils.isNotBlank(vo.getBillCheckId())).flatMap(vo -> Arrays.stream(vo.getBillCheckId().split(","))).map(Long::valueOf).collect(toList());
                    paymentApplyVo.setBillCheckIdList(paymentBillCheckIdList);
                    List<Long> payCustIdList=new ArrayList<>();
                    String paymentCustomerStr = "";
                    for (Long billCheckId:paymentBillCheckIdList) {
                        if (billCheckVoMap.containsKey(billCheckId)) {
                            BillCheckVo billCheckVo = billCheckVoMap.get(billCheckId);
                            if(!payCustIdList.contains(billCheckVo.getPayCustId())){
                                payCustIdList.add(billCheckVo.getPayCustId());
                                paymentCustomerStr+=billCheckVo.getPayDate()+"_"+billCheckVo.getPayCustName()+"_"+billCheckVo.getPayAmt()+";";
                            }
                        }
                    }
                    paymentApplyVo.setPaymentCustomerStr(paymentCustomerStr);
                    if(StringUtils.isBlank(paymentCustomerStr)){
                        paymentApplyVo.setSalaryPayBatchReceivedStr(salaryPayBatchReceivedStr);
                    }
                    SalaryPayBatchVo salaryPayBatchVo = voList.get(0);
                    paymentApplyVo.setWithholdingAgentNo(salaryPayBatchVo.getWithholdingAgentNo());
                    paymentApplyVo.setCommissioner(salaryPayBatchVo.getCommissioner());
                    paymentApplyVo.setApplicantCode(paymentApplyVo.getApplicant());
                    paymentApplyVo.setCommOrg(salaryPayBatchVo.getCommOrg());
                    paymentApplyVo.setCommPos(salaryPayBatchVo.getCommPos());
                    paymentApplyVo.setSalaryCommOrg(salaryPayBatchVo.getSalaryCommOrg());
                    paymentApplyVo.setSalaryCommPos(salaryPayBatchVo.getSalaryCommPos());
                    paymentApplyVo.setSalaryCommissioner(salaryPayBatchVo.getSalaryCommissioner());
                    paymentApplyVo.setContractType(salaryPayBatchVo.getContractType());
                    paymentApplyVo.setContractTypeName(EnumsUtil.getNameByCode(salaryPayBatchVo.getContractType(), ContractType.class));
                    paymentApplyVo.setDocumentStatusStr(EnumsUtil.getNameByCode(paymentApplyVo.getDocumentStatus(),PaymentApplyDocumentStatusEnum.class));
                    paymentApplyVo.setAppStatusStr(EnumsUtil.getNameByCode(paymentApplyVo.getAppStatus(),PaymentApplyProcessStatus.class));
                    paymentApplyVo.setPayTypeStr(PaymentApplyPayTypeEnum.getMsgByCode(paymentApplyVo.getPayType()));
                    paymentApplyVo.setAnewPayFlagStr(EnumsUtil.getNameByCode(paymentApplyVo.getAnewPayFlag(), BooleanTypeEnum.class));
                    paymentApplyVo.setApplicant(allUserMap.get(paymentApplyVo.getApplicant()));
                    if(StringUtils.isNotBlank(paymentApplyVo.getLastDate())&&paymentApplyVo.getLastDate().length()>=10){
                        paymentApplyVo.setLastDate(paymentApplyVo.getLastDate().substring(0,10));
                    }
                    paymentApplyVo.setTotalActApy(voList.stream().map(SalaryPayBatchVo::getTotalActApy).reduce(BigDecimal.ZERO,BigDecimal::add));
                    paymentApplyVo.setTotalCompensation(voList.stream().map(SalaryPayBatchVo::getTotalCompensation).reduce(BigDecimal.ZERO,BigDecimal::add));
                    paymentApplyVo.setTotalAnnualBonus(voList.stream().map(SalaryPayBatchVo::getTotalAnnualBonus).reduce(BigDecimal.ZERO,BigDecimal::add));
                    paymentApplyVo.setTotalLaborWages(voList.stream().map(SalaryPayBatchVo::getTotalLaborWages).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
            }
        }
        return paymentApplyVoList;
    }


    @Override
    public List<PaymentApplyVo> getPractIcePaymentApplyList(PaymentApplyVo paymentApplyVo) {
        List<PaymentApplyVo> paymentApplyVoList =paymentApplyMapper.getPractIcePaymentApplyList(paymentApplyVo);
        return getPracticePaymentApplyList(paymentApplyVoList);
    }

    @Override
    public List<PaymentApplyVo> getPractIcePaymentApplyListByAppStatus() {
        return paymentApplyMapper.getPractIcePaymentApplyListByAppStatus();
    }

    @Override
    public int savePayBatch(PaymentApplyVo record) {
        record.setApplyTime(new Date());
        if(Objects.equals(record.getOpType(),"edit")) {
            record.setId(record.getPayMentId());
        }
        paymentApplyMapper.insertSelective (record);
        SalaryPaymentApplyVo salaryPaymentApplyVo = new SalaryPaymentApplyVo();
        BeanUtils.copyProperties(record, salaryPaymentApplyVo);
        salaryPaymentApplyVo.setDispatchPrintFlag(PrintFlagEnum.NO.getCode());
        salaryPaymentApplyVo.setReceivingPrintFlag(PrintFlagEnum.NO.getCode());
        salaryPaymentApplyVo.setPayApplyId(record.getId());
        salaryPaymentApplyMapper.insertSelective(salaryPaymentApplyVo);
        PayApply2Batch payApply2Batch =new PayApply2Batch();
        payApply2Batch.setPayApplyId(record.getId());
        payApply2Batch.setCreator(record.getCreator());
        payApply2Batch.setUpdater(record.getUpdater());
        List<PayServiceSerialLogVo> payServiceSerialLogVoList = record.getPayServiceSerialLogVoList();
        if(CollectionUtils.isNotEmpty(payServiceSerialLogVoList)){
            for (PayServiceSerialLogVo payServiceSerialLogVo:payServiceSerialLogVoList) {
                payServiceSerialLogVo.setPaymentId(record.getId());
            }
            payServiceSerialLogMapper.insertByList(payServiceSerialLogVoList);
        }

        //拉回发放批次状态
        List<SalaryPayBatchVo> salaryPayBatchVoList = JsonUtil.jsonToList(record.getSalaryPayBatchVoListStr(), SalaryPayBatchVo.class);
        for (SalaryPayBatchVo salaryPayBatchVo:salaryPayBatchVoList) {
            SalaryPayBatch salaryPayBatch =new SalaryPayBatch();
            salaryPayBatch.setId(salaryPayBatchVo.getId());
            salaryPayBatch.setUpdater(record.getUpdater());
            salaryPayBatch.setGenFlag(2);
            salaryPayBatch.setBillCheckId(salaryPayBatchVo.getBillCheckId());
            payApply2Batch.setPayBatchId(salaryPayBatchVo.getId());
            if(Objects.equals(record.getOpType(),"add")){
                payApply2Batch.setKind(1);//生成支付审核
                apply2BatchMapper.insertSelective(payApply2Batch);

            }
            /*if(Objects.equals(record.getOpType(),"addRev")){
                payApply2Batch.setKind(2);//生成接单地支付审核
                apply2BatchMapper.insertSelective(payApply2Batch);
            }*/
            salaryPayBatchMapper.updateByPrimaryKeySelective(salaryPayBatch);
        }
        List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList = JsonUtil.jsonToList(record.getSalaryPayBatchReceivedVoListStr(), SalaryPayBatchReceivedVo.class);
        updateAndInsertSalaryPayBatchReceivedVoList(salaryPayBatchReceivedVoList,record);
        List<SalaryPayBatchInfoVo> salaryPayBatchInfoVoList = JsonUtil.jsonToList(record.getSalaryPayBatchInfoVoListStr(), SalaryPayBatchInfoVo.class);
        if(CollectionUtils.isNotEmpty(salaryPayBatchInfoVoList)){
            for (SalaryPayBatchInfoVo salaryPayBatchInfoVo:salaryPayBatchInfoVoList) {
                salaryPayBatchInfoVo.setUpdater(record.getCreator());
            }
            salaryPayBatchInfoMapper.updateByList(salaryPayBatchInfoVoList);
        }

        return 0;
    }
    public void updateAndInsertSalaryPayBatchReceivedVoList(List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList,PaymentApplyVo record){
        if(CollectionUtils.isNotEmpty(salaryPayBatchReceivedVoList)){
            List<SalaryPayBatchReceivedVo> insertVoList =new ArrayList<>();
            List<SalaryPayBatchReceivedVo> updateVoList =new ArrayList<>();
            for (SalaryPayBatchReceivedVo salaryPayBatchReceivedVo:salaryPayBatchReceivedVoList) {
                if(salaryPayBatchReceivedVo.getId()==null){
                    salaryPayBatchReceivedVo.setCreator(record.getCreator());
                    salaryPayBatchReceivedVo.setCreateTime(new Date());
                    insertVoList.add(salaryPayBatchReceivedVo);
                }else {
                    salaryPayBatchReceivedVo.setUpdater(record.getCreator());
                    salaryPayBatchReceivedVo.setUpdateTime(new Date());
                    updateVoList.add(salaryPayBatchReceivedVo);
                }
            }
            boolean matchFlag=false;
            if(CollectionUtils.isNotEmpty(updateVoList)){
                List<Long> recIdDeleteList = updateVoList.stream().map(SalaryPayBatchReceivedVo::getId).collect(toList());
                List<RecBankRelativeVo> paidVoListByRecIdList = recBankRelativeMapper.getPaidVoListByRecIdList(recIdDeleteList);
                if(CollectionUtils.isNotEmpty(paidVoListByRecIdList)){
                    throw new RuntimeException(ResultEnum.ERROR_PREFIX+"修改的预计到款数据中存在已匹配的，且其对应的支付被财务处理过的数据，所以不能修改！");
                }
                salaryPayBatchReceivedMapper.updateByList(updateVoList);
                recBankRelativeMapper.deleteByRecIdList(recIdDeleteList);
                matchFlag=true;
            }
            if(CollectionUtils.isNotEmpty(insertVoList)){
                List<SalaryPayBatch2ReceivedVo> salaryPayBatch2ReceivedVoList =new ArrayList<>();
                salaryPayBatchReceivedMapper.insertList(insertVoList);
                matchFlag=true;
                for (SalaryPayBatchReceivedVo salaryPayBatchReceivedVo:insertVoList) {
                    SalaryPayBatch2ReceivedVo salaryPayBatch2ReceivedVo=new SalaryPayBatch2ReceivedVo();
                    salaryPayBatch2ReceivedVo.setBatchId(salaryPayBatchReceivedVo.getBatchId());
                    salaryPayBatch2ReceivedVo.setReceivedId(salaryPayBatchReceivedVo.getId());
                    salaryPayBatch2ReceivedVo.setCreator(salaryPayBatchReceivedVo.getCreator());
                    salaryPayBatch2ReceivedVo.setCreateTime(new Date());
                    salaryPayBatch2ReceivedVoList.add(salaryPayBatch2ReceivedVo);
                }
                salaryPayBatch2ReceivedMapper.insertList(salaryPayBatch2ReceivedVoList);
            }
            if(matchFlag){
                iNetSilverService.handlePayCustomerMatchSalrayRece(record.getUpdater());
            }
        }
    }

    @Override
    public List<PayApply2BatchVo> getPayMent(List<Long> batchIds,Integer kind) {
        return apply2BatchMapper.getPayBatch(batchIds,kind);
    }

    @Override
    public List<Map<String, Object>> getPay(List<Long> payMentIds) {
        return null;
    }

    @Override
    public Long saveApplyInsurancePracticePayment(PaymentApplyVo paymentApplyVo) {
        paymentApplyVo.setAppStatus (PaymentApplyProcessStatus.PENDING_APPROVAL.getCode());
        Date now = new Date();
        paymentApplyVo.setCreateTime(now);
        paymentApplyVo.setUpdater(paymentApplyVo.getCreator());
        paymentApplyVo.setUpdateTime(now);
        paymentApplyVo.setApplicant(paymentApplyVo.getCreator());
        paymentApplyVo.setApplyTime(now);
        paymentApplyVo.setAppCom(paymentApplyVo.getAppCom());
        //福利办理方就是 支付抬头
        paymentApplyVo.setPayCom(paymentApplyVo.getOrgCode());
        paymentApplyMapper.insertSelective(paymentApplyVo);

        List<PracticeReportToPay> practiceReportToPayList = new ArrayList<>();
        /**将报表支付中金额，加上此次支付任务的金额*/
        PracPaymentApplyVo pracPaymentApplyVo = (PracPaymentApplyVo) paymentApplyVo;
        Long payId = pracPaymentApplyVo.getId();
        /**
         * 记录当前支付任务： 申请支付总额 与应付总额差异
         */
        insertPracticeBillDiffAmt(pracPaymentApplyVo);
        List<PracticePayDetailVo> detailVos = pracPaymentApplyVo.getDetailVos();
        Map<Long, List<PracticePayDetailVo>> collect = detailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getBillId));
        List<Long> billIds = Lists.newArrayList(collect.keySet());
        List<Integer> pracIds = billIds.stream().map(Long::intValue).collect(Collectors.toList());
        List<PracticeLockInfoVo> lockInfoVos = pracPaymentApplyVo.getPracticeLockInfoVoList();
        Map<Long, PracticeLockInfoVo> lockInfoVoMap = lockInfoVos.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, Function.identity()));
        List<PracticeLockInfoVo> conditions = Lists.newArrayList();
        Byte payDetailType = paymentApplyVo.getPayDetailType();
        PracticeLockInfoVo condition;
        for (Integer pracId : pracIds) {
            condition = new PracticeLockInfoVo();
            List<PracticePayDetailVo> payDetailVos = collect.get(pracId.longValue());
            BigDecimal frozenAmtTotal = payDetailVos.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal::add).get();
            PracticeLockInfoVo practiceLockInfoVo = lockInfoVoMap.get(pracId.longValue());
            BigDecimal frozenAmt = Optional.ofNullable(practiceLockInfoVo.getFrozenAmt()).orElse(BigDecimal.ZERO);
            condition.setId(practiceLockInfoVo.getId());
            condition.setFrozenAmt(frozenAmt.add(frozenAmtTotal));
            if(!payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
                condition.setPayStatus(PracticeLockInfoPayStatusEnum.IN_PAID.getCode());
            }
            condition.setVersion(practiceLockInfoVo.getVersion());
            conditions.add(condition);

            PracticeReportToPay practiceReportToPay = new PracticeReportToPay();
            practiceReportToPay.setPayApplyId(paymentApplyVo.getId());
            practiceReportToPay.setPracticeLockId(Long.valueOf(pracId));
            practiceReportToPay.init(paymentApplyVo.getCreator(),new Date());
            practiceReportToPayList.add(practiceReportToPay);
        }
        practiceReportToPayMapper.insertList(practiceReportToPayList);

        /**还需要维护 insurance_practice_report_xx 与 individual_fee_lock*/
        if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            List<PracticePayDetail> details = detailVos.stream().map(vo -> {
                PracticePayDetail detail = new PracticePayDetail();
                BeanUtils.copyProperties(vo, detail);
                return detail;
            }).collect(Collectors.toList());
            insurancePracticeLockService.editReportAndIndividualFeeLock(payId,details,PracticeLockInfoPayStatusEnum.IN_PAID.getCode(),pracPaymentApplyVo.getCreator());
        }

        insurancePracticeLockService.updatePracticeLockInfoVAmt(conditions);
        /**为支付详细数据 添加支付状态、payId、*/
        PracticePayDetailVo detailVoCondition = new PracticePayDetailVo();
        List<Long> ids = detailVos.stream().map(PracticePayDetailVo::getId).collect(Collectors.toList());
        detailVoCondition.setIds(ids);
        detailVoCondition.setAppStatus(PaymentApplyProcessStatus.PENDING_APPROVAL.getCode());
        detailVoCondition.setPayApplyId(paymentApplyVo.getId());
        detailVoCondition.setUpdater(paymentApplyVo.getCreator());
        detailVoCondition.setUpdateTime(new Date());
        insurancePracticeLockService.updatePracticePayDetailVoByIds(detailVoCondition);
        return paymentApplyVo.getId();
    }



    private void insertPracticeBillDiffAmt(PracPaymentApplyVo paymentApplyVo) {
        String packCode = paymentApplyVo.getPracticeLockInfoVoList().stream().map(PracticeLockInfoVo::getPackCode).findFirst().get();
        PackAmtDiffMonthlyVo vo = new PackAmtDiffMonthlyVo();
        BigDecimal payAmt = Optional.ofNullable(paymentApplyVo.getPayAmt()).orElse(BigDecimal.ZERO);
        BigDecimal applyAmt = Optional.ofNullable(paymentApplyVo.getApplyAmt()).orElse(BigDecimal.ZERO);
        BigDecimal diff = payAmt.subtract(applyAmt);
        paymentApplyVo.setPackCode(packCode);
        if(!BigDecimalUtil.equalsZero(diff)){
            vo.build(paymentApplyVo,diff);
            packAmtDiffMonthlyService.insertPackAmtDiffMonthlyVo(vo);
        }
    }

    @Override
    public List<PracticeLockInfoVo> getPracticeLockIdByPaymentApplyId(Long paymentApplyId) {
        List<Long> practiceBillId = practiceReportToPayMapper.getPracticeLockIdByPaymentApplyId(paymentApplyId);
        List<PracticeLockInfoVo> practiceLockInfoVo =Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(practiceBillId)){
            List<Integer> collect = practiceBillId.stream().map(Long::intValue).collect(Collectors.toList());
            practiceLockInfoVo = insurancePracticeLockService.getByIds(collect);
        }
        return practiceLockInfoVo;
    }

    //根据公司code获取公司名
    private List<PaymentApplyVo> getPaymentApplyList(List<PaymentApplyVo> paymentApplyVoList) {
        List<OrgVo> allOrgName = orgnizationResourceWrapperService.findAllOrgName();
        Map<String, String> orgNameMap = allOrgName.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        Map<Long, String> supplierMap = supplierWrapperService.allSupplierMap();
        /*Map<Long,Integer> contractTypeMap=new HashMap<>();

        if(CollectionUtils.isNotEmpty(paymentApplyVoList)){
            List<Long> payIdList = paymentApplyVoList.stream().map(PaymentApplyVo::getPayId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(payIdList)){
                contractTypeMap=iSalaryPayWrapperService.getContractTypeByPayIdList(payIdList);
            }
        }*/
        for (PaymentApplyVo pav : paymentApplyVoList) {
            List<SalaryPayBatchVo> salaryPayBatchVoList = pav.getSalaryPayBatchVoList();
            if(CollectionUtils.isNotEmpty(salaryPayBatchVoList)){
                pav.setTotalActApy(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalActApy).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalTax(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalTax).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalSalaryFee(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSalaryFee).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalSupplierDisFund(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSupplierDisFund).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalSupplierCrossBankHandlingFees(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSupplierCrossBankHandlingFees).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalSupplierUnionFees(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSupplierUnionFees).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalSupplierSalarySaleTax(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalSupplierSalarySaleTax).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalCompensation(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalCompensation).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalCompensationTax(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalCompensationTax).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalAnnualBonus(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalAnnualBonus).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalAnnualBonusTax(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalAnnualBonusTax).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalLaborWages(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalLaborWages).reduce(BigDecimal.ZERO,BigDecimal::add));
                pav.setTotalLaborWagesTax(salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getTotalLaborWagesTax).reduce(BigDecimal.ZERO,BigDecimal::add));
            }
            if(orgNameMap.containsKey(pav.getAppCom())){
                pav.setAppComName(orgNameMap.get(pav.getAppCom()));
            }
            if(orgNameMap.containsKey(pav.getPayCom())){
                pav.setPayComName(orgNameMap.get(pav.getPayCom()));
            }
            if(StringUtils.isNotBlank(pav.getRevCom())){
                if(orgNameMap.containsKey(pav.getRevCom())){
                    pav.setRevComName(orgNameMap.get(pav.getRevCom()));
                }else if(supplierMap.containsKey(Long.parseLong(pav.getRevCom()))){
                    pav.setRevComName(supplierMap.get(Long.parseLong(pav.getRevCom())));
                }
            }
            if(StringUtils.isNotBlank(pav.getPayAssociatedCom())){
                if(orgNameMap.containsKey(pav.getPayAssociatedCom())){
                    pav.setPayAssociatedComName(orgNameMap.get(pav.getPayAssociatedCom()));
                }
            }
            /*if(contractTypeMap.containsKey(pav.getPayId())){
                pav.setContractType(contractTypeMap.get(pav.getPayId()));
                pav.setContractTypeName(EmployeeReportEnum.ContractTypeEnum.getName(pav.getContractType()));
            }*/
        }
        return paymentApplyVoList;
    }
    //根据公司code获取公司名
    private List<PaymentApplyVo> getPracticePaymentApplyList(List<PaymentApplyVo> paymentApplyVoList) {
        List<OrgVo> allOrgName = orgnizationResourceWrapperService.findAllOrgName();
        for (PaymentApplyVo pav : paymentApplyVoList) {
            for (OrgVo org : allOrgName) {
                if (pav.getAppCom().trim().equals(org.getOrgCode().trim())) {
                    pav.setAppComName(org.getOrgName());
                }
            }
        }
        return paymentApplyVoList;
    }
    @Override
    public boolean updateAppStatus(PaymentApplyVo record){
        paymentApplyMapper.updateByPrimaryKeySelective (record);
        List<Long> practiceLockIds = practiceReportToPayMapper.getPracticeLockIdByPaymentApplyId(record.getId());
        return practiceBillMapper.updateAppStatus(practiceLockIds, PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode());
    }

    @Override
    public boolean editPaymentApplyAndPrac(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeLockService.editPaymentApplyAndPrac(paymentApplyVo);
    }


    @Override
    public int updateDocumentStatus(List<String> pidList, Integer documentStatus,Integer appStatus) {
        PaymentApplyVo paymentApplyVo=new PaymentApplyVo();
        if(documentStatus!=null){
            paymentApplyVo.setDocumentStatus(documentStatus);
            if(Objects.equals(PaymentApplyDocumentStatusEnum.REVIEWED.getCode(), documentStatus)){
                paymentApplyVo.setReviewTime(new Date());
            }else if(Objects.equals(PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode(), documentStatus)){
                paymentApplyVo.setOwnReviewTime(new Date());
            }
        }
        if(appStatus!=null){
            paymentApplyVo.setPassTime(new Date());
            paymentApplyVo.setAppStatus(appStatus);
            if(appStatus==PaymentApplyProcessStatus.FINISHED.getCode()){
                List<Long> batchIdList = paymentApplyMapper.getBatchIdListByPidList(pidList);
                if(CollectionUtils.isNotEmpty(batchIdList)){
                    EmpDelaySearchVo empDelaySearchVo=new EmpDelaySearchVo();
                    empDelaySearchVo.setBatchIdList(batchIdList);
                    empDelaySearchVo.setSendTime(Integer.parseInt(DateUtil.formatDateToString(new Date(),DateUtil.DATE_FORMAT_YYYYMMDD)));
                    empDelaySearchVo.setStatus(SalaryInfoStatus.PAY_SUCCESS.getCode());
                    iSalaryPayWrapperService.updateSalaryInfoStatus(empDelaySearchVo);
                }
            }
        }
        return paymentApplyMapper.updateDocumentStatus(pidList,paymentApplyVo);
    }

    @Override
    public PaymentApplyVo getByBatchId(Long batchId) {
        PaymentApplyVo paymentApplyVo = paymentApplyMapper.getByBatchId(batchId);
        List<SalaryPayBatchVo> salaryPayBatchVoList=salaryPayBatchMapper.selectByPaymentIdList(Collections.singletonList(paymentApplyVo.getId()));
        Integer applycnt= salaryPayBatchVoList.stream().map(SalaryPayBatchVo::getApplyCnt).reduce(0, Integer::sum);
        paymentApplyVo.setTotalApplyCnt(applycnt);
        return paymentApplyVo;
    }
    @Override
    public List<PaymentApplyVo> getByBatchIdList(List<Long> batchIdList) {
        return paymentApplyMapper.getByBatchIdList(batchIdList);
    }

    @Override
    public int deleteByIdList(List<Long> idList, String loginName) {
        return paymentApplyMapper.deleteByIdList(idList,loginName);
    }

    @Override
    public List<String> selectPidListByPid(List<String> pidList) {
        return paymentApplyMapper.selectPidListByPid(pidList);
    }

    @Override
    public int updatePaymentAppStatus(List<Long> ids, Integer status, String updater) {
        return paymentApplyMapper.updatePaymentAppStatus(ids,status,updater);
    }

    @Override
    public int updateLastDate(PaymentApplyVo paymentApplyVo) {
        PaymentApplyVo applyVo = paymentApplyMapper.selectByPrimaryKey(paymentApplyVo.getPayMentId());
        PaymentApplyLastDateLogVo paymentApplyLastDateLogVo = new PaymentApplyLastDateLogVo();
        paymentApplyLastDateLogVo.setPaymentApplyId(applyVo.getId());
        paymentApplyLastDateLogVo.setOldLastDate(DateUtil.getFormattedDate(DateUtil.DATE_FORMAT_YYYY_MM_DD,applyVo.getLastDate()));
        paymentApplyLastDateLogVo.setNewLastDate(DateUtil.getFormattedDate(DateUtil.DATE_FORMAT_YYYY_MM_DD,paymentApplyVo.getLastDate()));
        paymentApplyLastDateLogVo.setCreator(paymentApplyVo.getUpdater());
        paymentApplyLastDateLogVo.setCreateTime(new Date());
        PaymentApplyVo updateVo = new PaymentApplyVo();
        updateVo.setId(paymentApplyVo.getPayMentId());
        updateVo.setUpdater(paymentApplyVo.getUpdater());
        updateVo.setUpdateTime(paymentApplyLastDateLogVo.getCreateTime());
        updateVo.setLastDate(paymentApplyVo.getLastDate());
        if(StringUtils.isNotEmpty(paymentApplyVo.getPaymentDate())){
            updateVo.setPaymentDate(paymentApplyVo.getPaymentDate());
        }
        paymentApplyMapper.updateByPrimaryKeySelective(updateVo);
        List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList = JsonUtil.jsonToList(paymentApplyVo.getSalaryPayBatchReceivedVoListStr(), SalaryPayBatchReceivedVo.class);
        updateAndInsertSalaryPayBatchReceivedVoList(salaryPayBatchReceivedVoList,paymentApplyVo);
        List<SalaryPayBatchInfoVo> salaryPayBatchInfoVoList = JsonUtil.jsonToList(paymentApplyVo.getSalaryPayBatchInfoVoListStr(), SalaryPayBatchInfoVo.class);
        if(CollectionUtils.isNotEmpty(salaryPayBatchInfoVoList)){
            for (SalaryPayBatchInfoVo salaryPayBatchInfoVo:salaryPayBatchInfoVoList) {
                salaryPayBatchInfoVo.setUpdater(paymentApplyVo.getUpdater());
            }
            salaryPayBatchInfoMapper.updateByList(salaryPayBatchInfoVoList);
        }
        return paymentApplyLastDateLogMapper.insertByVo(paymentApplyLastDateLogVo);
    }
    @Override
    public int updatePayReceived(PaymentApplyVo paymentApplyVo) {
        List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList = JsonUtil.jsonToList(paymentApplyVo.getSalaryPayBatchReceivedVoListStr(), SalaryPayBatchReceivedVo.class);
        updateAndInsertSalaryPayBatchReceivedVoList(salaryPayBatchReceivedVoList,paymentApplyVo);
        return 0;
    }

    @Override
    public List<Long> getBatchIdListByLastDate(Integer lastDate) {
        return paymentApplyMapper.getBatchIdListByLastDate(lastDate);
    }

    @Override
    public List<Long> getBatchIdListByStringLastDate(String lastDate) {
        return paymentApplyMapper.getBatchIdListByStringLastDate(lastDate);
    }

    @Override
    public List<Long> getBatchIdListByStringLastDateStartAndEnd(String lastDateStart, String lastDateEnd) {
        return paymentApplyMapper.getBatchIdListByStringLastDateStartAndEnd(lastDateStart, lastDateEnd);
    }

    @Override
    public List<PaymentApplyVo> getLastDateAndAnewPayFlagByBatchIdList(List<Long> batchIdList) {
        return paymentApplyMapper.getLastDateAndAnewPayFlagByBatchIdList(batchIdList);
    }

    @Override
    public List<PaymentApplyVo> getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        return paymentApplyMapper.getPrintReceivingApplicationFromPage(paymentApplyVo);
    }

    @Override
    public List<PaymentApplyVo> getPayCreateTimeByPayBatchId(List<Long> payBatchIdList) {
        return apply2BatchMapper.getPayCreateTimeByPayBatchId(payBatchIdList);
    }

    @Override
    public boolean updateSalaryAppStatus(PaymentApplyVo paymentApplyVo) {
        return paymentApplyMapper.updateByPrimaryKeySelective (paymentApplyVo);
    }

    @Override
    public List<PaymentApplyVo> getByPidList(List<String> pidList) {
        return paymentApplyMapper.getByPidList(pidList);
    }

    @Override
    public int updateReprintCount(List<String> pidList, Integer reprintCount) {
        return paymentApplyMapper.updateReprintCount(pidList,reprintCount);
    }

    @Override
    public boolean getUnRejected(String contractNo, Long templetId, Integer billMonth) {
        List<Long> payIdList=iSalaryPayWrapperService.getPayIdList(contractNo,templetId,billMonth);
        int result=0;
        if(CollectionUtils.isNotEmpty(payIdList)){
            result=paymentApplyMapper.getUnRejected(payIdList);
        }
        return result>0;
    }

    @Override
    public int updateSalaryFee(PaymentApplyVo paymentApplyVo) {
        PaymentApplyVo applyVo = this.selectSalaryById(paymentApplyVo.getId());
        List<SalaryPayBatchVo> salaryPayBatchVoList = applyVo.getSalaryPayBatchVoList();
        BigDecimal total =BigDecimal.ZERO;
        for (SalaryPayBatchVo salaryPayBatchVo:salaryPayBatchVoList) {
            BigDecimal salaryFee = paymentApplyVo.getNewSupplierSalaryFee().multiply(BigDecimal.valueOf(salaryPayBatchVo.getApplyCnt()));
            SalaryPayBatch salaryPayBatch = new SalaryPayBatch();
            salaryPayBatch.setId(salaryPayBatchVo.getId());
            salaryPayBatch.setUpdateTime(new Date());
            salaryPayBatch.setUpdater(paymentApplyVo.getUpdater());
            salaryPayBatch.setTotalSalaryFee(salaryFee);
            salaryPayBatchMapper.updateByPrimaryKeySelective(salaryPayBatch);
            paymentApplyVo.setUpdateTime(salaryPayBatch.getUpdateTime());
            SynchronizationSupplierSalaryFeeRecord synchronizationSupplierSalaryFeeRecord = new SynchronizationSupplierSalaryFeeRecord();
            synchronizationSupplierSalaryFeeRecord.setBatchId(salaryPayBatchVo.getId());
            synchronizationSupplierSalaryFeeRecord.setSupplierId(Long.parseLong(paymentApplyVo.getRevCom()));
            synchronizationSupplierSalaryFeeRecord.setCityCode(paymentApplyVo.getCityCode());
            synchronizationSupplierSalaryFeeRecord.setNewSalaryFee(paymentApplyVo.getNewSupplierSalaryFee());
            synchronizationSupplierSalaryFeeRecord.setNewTotalSalaryFee(salaryFee);
            synchronizationSupplierSalaryFeeRecord.setOldTotalSalaryFee(salaryPayBatchVo.getTotalSalaryFee());
            synchronizationSupplierSalaryFeeRecord.setOldSalaryFee(salaryPayBatchVo.getTotalSalaryFee().divide(BigDecimal.valueOf(salaryPayBatchVo.getApplyCnt()), 2, BigDecimal.ROUND_HALF_UP));
            synchronizationSupplierSalaryFeeRecord.setCreator(paymentApplyVo.getUpdater());
            synchronizationSupplierSalaryFeeRecordMapper.insert(synchronizationSupplierSalaryFeeRecord);
            total = total.add(salaryFee).add(salaryPayBatchVo.getTotalActApy()).add(salaryPayBatchVo.getTotalTax());
            SalaryPayVo salaryPayById = iSalaryPayWrapperService.getSalaryPayById(salaryPayBatchVo.getPayId());
            salaryPayById.setCityCode(paymentApplyVo.getCityCode());
            salaryPayById.setOrgCode(paymentApplyVo.getRevCom());
            salaryPayById.setSalaryFee(paymentApplyVo.getNewSupplierSalaryFee());
            salaryPayById.setUpdater(salaryPayBatch.getUpdater());
            salaryPayById.setUpdateTime(salaryPayBatch.getUpdateTime());
            billCostMapper.updateServiceFeeBySalaryPay(salaryPayById);
        }
        paymentApplyVo.setApplyAmt(total);
        paymentApplyVo.setPayAmt(total);
        paymentApplyMapper.updateByPrimaryKeySelective(paymentApplyVo);
        return 0;
    }

    @Override
    public List<ReserveInquiryVo> getReserveInquiryListPage(PaymentApplyVo vo) {
        List<PaymentApplyVo> reserveInquiryListByDisCom = insurancePracticeDisComPayMapper.getReserveInquiryListByDisCom(vo);
        List<PaymentApplyVo> reserveInquiryListByPayCom = paymentApplyMapper.getReserveInquiryListPage( vo);
            if (StringUtils.isNotBlank(vo.getPayCom())&&StringUtils.isNotBlank(vo.getDisCom())){
                reserveInquiryListByPayCom = reserveInquiryListByPayCom.stream().filter(paymentApplyVo -> paymentApplyVo.getPayCom().equals(vo.getPayCom())).collect(Collectors.toList());
                reserveInquiryListByDisCom = reserveInquiryListByDisCom.stream().filter(paymentApplyVo -> paymentApplyVo.getDisCom().equals(vo.getDisCom())).collect(Collectors.toList());
            }
            if (StringUtils.isNotBlank(vo.getDisCom())&&StringUtils.isBlank(vo.getPayCom())){
                reserveInquiryListByDisCom = reserveInquiryListByDisCom.stream().filter(paymentApplyVo -> paymentApplyVo.getDisCom().equals(vo.getDisCom())).collect(Collectors.toList());
                reserveInquiryListByPayCom = Collections.emptyList();
            }
            if (StringUtils.isBlank(vo.getDisCom())&&StringUtils.isNotBlank(vo.getPayCom())){
                reserveInquiryListByPayCom = reserveInquiryListByPayCom.stream().filter(paymentApplyVo -> paymentApplyVo.getPayCom().equals(vo.getPayCom())).collect(Collectors.toList());
                reserveInquiryListByDisCom = Collections.emptyList();
            }
            if (!Objects.isNull(vo.getIsFlag())){
                if (vo.getIsFlag().equals(BooleanTypeEnum.NO.getCode())){
                    reserveInquiryListByDisCom = Collections.emptyList();
                }else if (vo.getIsFlag().equals(BooleanTypeEnum.YES.getCode())){
                    reserveInquiryListByPayCom = Collections.emptyList();
                }
            }



        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));

        Map<String, List<PaymentApplyVo>> payDateGroupDisComMap = reserveInquiryListByDisCom.stream().collect(Collectors.groupingBy(PaymentApplyVo::getLastDate));
        Map<String, List<PaymentApplyVo>> payDateGroupPayComMap = reserveInquiryListByPayCom.stream().collect(Collectors.groupingBy(PaymentApplyVo::getLastDate));
        ArrayList<ReserveInquiryVo> reserveInquiryVos = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (String disComLastDate : payDateGroupDisComMap.keySet()) {
            LocalDateTime dateTime = LocalDateTime.parse(disComLastDate, formatter);
            LocalDateTime payTime = dateTime.with(LocalTime.of(16, 0));
            List<PaymentApplyVo> paymentApplyVos = payDateGroupDisComMap.get(disComLastDate);
            Map<String, List<PaymentApplyVo>> disComAmtMap = paymentApplyVos.stream().collect(Collectors.groupingBy(PaymentApplyVo::getDisCom));
            for (String disCom : disComAmtMap.keySet()) {
                List<PaymentApplyVo> allAmtList = disComAmtMap.get(disCom);
                BigDecimal totalPayAmt = allAmtList.stream()
                        .map(PaymentApplyVo::getPayAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalServiceAmt = allAmtList.stream()
                        .map(PaymentApplyVo::getServiceAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalActPayAmt = allAmtList.stream()
                        .map(PaymentApplyVo::getActPayAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
                reserveInquiryVo.setPayDate(payTime.format(formatter));
                reserveInquiryVo.setPayCom(orgVoMap.get(disCom));
                reserveInquiryVo.setIsFlag(BooleanTypeEnum.YES.getCode());
                reserveInquiryVo.setPayComCode(disCom);
                reserveInquiryVo.setPayAmt(totalPayAmt.add(totalServiceAmt));
                reserveInquiryVo.setPayAmtBalance(totalPayAmt.add(totalServiceAmt).subtract(totalActPayAmt));
                reserveInquiryVo.setActPayAmt(totalActPayAmt);
                reserveInquiryVos.add(reserveInquiryVo);
            }
        }
        for (String payLastDate : payDateGroupPayComMap.keySet()) {
            LocalDateTime dateTime = LocalDateTime.parse(payLastDate, formatter);
            LocalDateTime payTime = dateTime.with(LocalTime.of(16, 0));
            List<PaymentApplyVo> newPaymentVos = new ArrayList<>();
            List<PaymentApplyVo> paymentApplyVos = payDateGroupPayComMap.get(payLastDate);
            for (PaymentApplyVo paymentApplyVo : paymentApplyVos) {
                String currentApproveName = workflowWrapperService.getCurrentApproveName(paymentApplyVo.getPid());
                if (!"客服上传凭证".equals(currentApproveName)) {
                    newPaymentVos.add(paymentApplyVo);
                }
            }
            if (CollectionUtils.isNotEmpty(newPaymentVos)){
                Map<String, BigDecimal> payComMap = newPaymentVos.stream()
                        .collect(Collectors.groupingBy(
                                PaymentApplyVo::getPayCom,
                                Collectors.reducing(BigDecimal.ZERO, PaymentApplyVo::getPayAmt, BigDecimal::add)
                        ));
                for (String payCom : payComMap.keySet()) {
                    ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
                    reserveInquiryVo.setPayDate(payTime.format(formatter));
                    reserveInquiryVo.setPayCom(orgVoMap.get(payCom));
                    reserveInquiryVo.setIsFlag(BooleanTypeEnum.NO.getCode());
                    reserveInquiryVo.setPayComCode(payCom);
                    reserveInquiryVo.setPayAmt(payComMap.get(payCom));
                    reserveInquiryVo.setPayAmtBalance(BigDecimal.ZERO);
                    reserveInquiryVo.setActPayAmt(payComMap.get(payCom));
                    reserveInquiryVos.add(reserveInquiryVo);
                }
            }
        }
        reserveInquiryVos.sort(Comparator.comparing((ReserveInquiryVo e) -> LocalDateTime.parse(e.getPayDate(), formatter)));
        return reserveInquiryVos;
    }

    @Override
    public List<ReserveInquiryVo> getReserveInquiryDetail(PaymentApplyVo vo) {

        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        List<ReserveInquiryVo> reserveInquiryVos = new ArrayList<>();
        if (vo.getPrintType().equals(BooleanTypeEnum.NO.getCode())){
            List<PaymentApplyVo> reserveInquiryDetail = paymentApplyMapper.getReserveInquiryDetail(vo);
            List<Long> payIdList = reserveInquiryDetail.stream().map(PaymentApplyVo::getId).collect(toList());
            List<InsurancePracticeDisComPayVo> insurancePracticeDisComList = insurancePracticeDisComPayMapper.getPayAmtGroupByDisComByPayIdList(payIdList);
            BigDecimal payTotalAmt = reserveInquiryDetail.stream()
                    .map(PaymentApplyVo::getPayAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal disTotalAmt =BigDecimal.ZERO;
            BigDecimal disActTotalAmt =BigDecimal.ZERO;
            for (InsurancePracticeDisComPayVo item : insurancePracticeDisComList) {
                disTotalAmt = disTotalAmt.add(item.getPayAmt());
                disActTotalAmt = disActTotalAmt.add(item.getActPayAmt());
                ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
                reserveInquiryVo.setPayCom(orgVoMap.get(item.getDisCom()));
                reserveInquiryVo.setActPayAmt(item.getActPayAmt());
                reserveInquiryVo.setPayAmt(item.getPayAmt());
                reserveInquiryVo.setPayAmtBalance(BigDecimal.ZERO);
                reserveInquiryVos.add(reserveInquiryVo);
            }
            ReserveInquiryVo reserveInquiryVo1 = new ReserveInquiryVo();
            reserveInquiryVo1.setPayCom("派单地付款总额");
            reserveInquiryVo1.setPayAmt(disTotalAmt);
            reserveInquiryVo1.setActPayAmt(disActTotalAmt);
            reserveInquiryVos.add(reserveInquiryVo1);
            ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
            reserveInquiryVo.setPayCom("接单地自有客户备款额");
            reserveInquiryVo.setPayAmt(payTotalAmt.subtract(disTotalAmt));
            reserveInquiryVos.add(reserveInquiryVo);
        }else {
            List<InsurancePracticeDisComPayVo> disComPayVos = insurancePracticeDisComPayMapper.getDisComPayAmtHGroupByPayComAndDisComByDisComAndLastDate(vo.getPayCom(), vo.getLastDate());
            BigDecimal payTotalAmt = BigDecimal.ZERO;
            BigDecimal actPayTotalAmt = BigDecimal.ZERO;
            BigDecimal serviceTotalAmt = BigDecimal.ZERO;
            BigDecimal balanceTotalAmt = BigDecimal.ZERO;
            for (InsurancePracticeDisComPayVo disComPayVo : disComPayVos) {
                BigDecimal serviceAmt = disComPayVo.getServiceAmt()==null?BigDecimal.ZERO:disComPayVo.getServiceAmt();
                BigDecimal payAmt = disComPayVo.getPayAmt();
                BigDecimal actPayAmt = disComPayVo.getActPayAmt();
                BigDecimal add = serviceAmt.add(payAmt);
                BigDecimal balanceAmt = actPayAmt.subtract(add);
                ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
                reserveInquiryVo.setDisCom(orgVoMap.get(disComPayVo.getDisCom()));
                reserveInquiryVo.setPayCom(orgVoMap.get(disComPayVo.getPayCom()));
                reserveInquiryVo.setPayAmt(payAmt);
                reserveInquiryVo.setServiceAmt(serviceAmt);
                reserveInquiryVo.setPayAmtBalance(balanceAmt);
                reserveInquiryVo.setActPayAmt(actPayAmt);
                reserveInquiryVo.setIsFlag(BooleanTypeEnum.NO.getCode());
                reserveInquiryVos.add(reserveInquiryVo);
                payTotalAmt = payTotalAmt.add(payAmt);
                actPayTotalAmt = actPayTotalAmt.add(actPayAmt);
                serviceTotalAmt = serviceTotalAmt.add(serviceAmt);
                balanceTotalAmt = balanceTotalAmt.add(balanceAmt);
            }
            ReserveInquiryVo reserveInquiryVo = new ReserveInquiryVo();
            reserveInquiryVo.setPayCom("备款总额");
            reserveInquiryVo.setPayAmt(payTotalAmt);
            reserveInquiryVo.setServiceAmt(serviceTotalAmt);
            reserveInquiryVo.setPayAmtBalance(balanceTotalAmt);
            reserveInquiryVo.setActPayAmt(actPayTotalAmt);
            reserveInquiryVos.add(reserveInquiryVo);

        }

        return reserveInquiryVos;
    }


}
