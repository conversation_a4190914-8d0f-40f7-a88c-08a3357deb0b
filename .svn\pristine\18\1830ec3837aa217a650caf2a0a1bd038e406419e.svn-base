package com.reon.hr.sp.bill.dao.bill;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.dto.QueryOfReceiptWriteOffDto;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.check.BillCheckApprovalVo;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.bill.vo.check.CheckSearchParmVo;
import com.reon.hr.sp.bill.entity.bill.BillCheck;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface BillCheckMapper extends BaseMapper<BillCheck> {
    int deleteByPrimaryKey(Long id);
    int insert(BillCheckApprovalVo record);
    int updateByPrimaryKeySelective(BillCheck record);
    int updateByPrimaryKey(BillCheck record);


    int insertSelective(BillCheck record);



    /**

     * 获取所有未审批的核销的 客户信息
     *
     * @param companyCode
     * @return
     */
    List<CustomerInfoVo> getUnApprovalCust(@Param("companyCode") String companyCode,
                                           @Param("keyword") String keyword,
                                           @Param("type")  Integer type,
                                           Page page);


    /**

     * 获取未审批的核销信息
     *
     * @param parm
     * @param page
     * @return
     */
    List<BillCheckApprovalVo> getCheckUnApproval(CheckSearchParmVo parm, Page page);

    /**
     * 根据核销表中的主键Id批量修改状态
     *
     * @param status
     * @param updater
     * @param ids
     * @return
     */
    int updateStatusByIds(@Param("ids") Long[] ids, @Param("status") Integer status, @Param("updater") String updater);



    List<BillCheckVo> getRecord(@Param("ids")List<Long> ids);

    int batchInsert(@Param("vo") List<BillCheckVo> record);

    int batchUpdate(@Param("vo") BillCheckAndUpdateBillVo record);

  /** todo */
    List<BillCheckVo> getBillCheckByBillIds(@Param("billIdList") List<Long> billIdList);

    BillCheck getBillIdData(Long id);

    List<BillCheckApprovalVo> getCheckApproval(Page page, Map<String,Object> map);

    List<QueryOfReceiptWriteOffDto> queryOfReceiptWriteOffByPage(Page page,
                                                                 @Param("param") QueryOfReceiptWriteOffParam param,
                                                                 @Param("companyCode") String companyCode);

    List<BillCheckVo> getCheckAndBillByBillId(@Param("payCustId") Long payCustId);

    int batchUpdatePrjByBillId(@Param("billIdList") List<Integer> billIdList,
                               @Param("prjCs") String prjCs);



	List<Integer> getBillCheckStatus(Long billId);
    BillCheck selectByPrimaryKey(Long id);

/** 根据idList获取所有的核销主表数据 */
List<BillCheck> selectCheckDataByCheckIds(@Param("list") List<Long> checkIds);

    void deleteByIds(@Param("list") List<Long> checkIds);

    List<BillCheckVo> getBillCheckByBillIdList(@Param("list") List<Long> billIdList);

	List<BillCheckVo> getInsuranceBillAndCheckIdByBillId(@Param("billId") Long billId);

    List<BillCheckVo> getCheckIdAndPayCustNameMapByCheckId(@Param("list") List<Long> checkIdList);

    List<BillCheckVo> getBillCheckByTempletIdAndBillMonth(@Param("list")List<InsuranceBillVo> insuranceBillVoList);
    List<InvoiceBillVo> getBillInvoiceByTempletIdAndBillMonth(@Param("vo")InsuranceBillVo insuranceBillVo);

    List<BillCheckVo> getByCheckIdList(@Param("list")List<Long> billCheckIdList);

    List<BillCheckApprovalVo> getNetSilverCheckedQueryListByPayCustIdList(@Param("payCustIdList")List<Long> payCustIdList );

    List<BillCheckVo> getPayCustByCheckId(@Param("list") List<Long> billCheckId);

    @Update("UPDATE `reon-billdb`.bill_check SET check_total_amt = #{updateCheckTotalAmt},abolish_amt=#{abolishAmt},not_abolish_amt=#{notAbolishAmt} WHERE id = #{id}")
    Integer updateCheckAmtByIds(@Param("id") Long id,@Param("updateCheckTotalAmt") BigDecimal updateCheckTotalAmt,@Param("abolishAmt")BigDecimal abolishAmt,@Param("notAbolishAmt")BigDecimal notAbolishAmt);

}