var ctx = ML.contextPath;
let use = layui.use(['form', 'layer', 'laydate', 'table'], function () {
    var table = layui.table;
    layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 页面 加载事件
    $(document).ready(function () {
        if ($("#fileIdList").val()) {
            var fileStr = $("#fileIdList").val();
           let  uploadIds = fileStr.split(",");
            if (uploadIds.length > 0) {
                for (var i = 0; i < uploadIds.length; i++) {
                    $.ajaxData.getFileName(uploadIds[i], 'check')
                }
            }

        }
    })


});