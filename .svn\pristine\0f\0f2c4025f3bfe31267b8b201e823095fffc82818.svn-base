package com.reon.ehr.api.sys.vo;

import com.reon.hr.api.customer.dto.customer.CustomerDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeContractVo implements Serializable {
    private static final long serialVersionUID = 231145494895135693L;

/** 录入时间  也就是employee_order 的create_time */


    /**
     * 雇员姓名
     */
//    @Excel(name = "雇员姓名")
    private String employeeName;

    /**
     * 员工合同编号
     */
//    @Excel(name = " 员工合同编号")
    private String empContractNo;

    /**
     * 唯一号
     */
//    @Excel(name = " 唯一号")
    private String employeeNo;
    /**
     * 证件类型
     */
//    @Excel(name = "证件类型", readConverterExp = "1=身份证,2=军人证,3=港澳身份证,4=台胞证,5=护照,6=其他")
    private Integer certType;
    /**
     * 证件号码
     */
//    @Excel(name = "证件号码")
    private String certNo;

    /**
     * 客户编号
     */
//    @Excel(name = "客户编号")
    private String custNo;

    /**
     * 订单编号
     */
//    @Excel(name = "订单号")
    private String orderNo;

    /**
     * 客户名称
     */
//    @Excel(name = "客户名称")
    private String custName;
    /**
     * 增减员状态
     */
//    @Excel(name = "增减员状态", readConverterExp = "1=草稿,2=驳回,3=等待接单方完善,4=挂起,5=等待派单方确认,6=增员完成,7=减员等待接单方确认,8=减员等待派单方确认,9=离职完成")
    private Byte addReduceStatus;
    /**
     * 劳动合同状态
     */
//    @Excel(name = "劳动合同状态")
    private String empContractStatus;
    /**
     * 正式工资
     */
//    @Excel(name = "正式工资")
    private BigDecimal formalSalary;
    /**
     * 是否外呼
     */
//    @Excel(name = "是否外呼", readConverterExp = "1=否,2=是")
    private Integer callFlag;
//    @Excel(name = "签订操作人")
    private String creator;
//    @Excel(name = "签署状态", readConverterExp = "1=未签署,2=已签署,3=取消聘用,4=过期劳动合同")
    private Byte signStatus;
    private Integer signStatusInt;
    private Integer relieveFile;
    /**
     * 签署日期
     */
//    @Excel(name = "签署日期", dateFormat = "yyyyMMdd")
    private Date signDate;
    /**
     * 工作制
     */
//    @Excel(name = "工作制")
    private String workMethod;
    /**
     * 合同原则
     */
//    @Excel(name = "合同原则")
    private String principle;
    /**
     * 派遣期限起
     */
//    @Excel(name = "派遣期限起", dateFormat = "yyyyMMdd")
    private Date dispatchStart;
    /**
     * 派遣期限止
     */
//    @Excel(name = "派遣期限止", dateFormat = "yyyyMMdd")
    private Date dispatchEnd;
    /**
     * 劳动合同起始时间
     */
//    @Excel(name = "劳动合同起始时间", dateFormat = "yyyyMMdd")
    private Date startDate;
    /**
     * 续签中间时间
     */
//    @Excel(name = "续签中间时间")
    private String middleTime;
    /**
     * 劳动合同结束时间
     */
//    @Excel(name = "劳动合同结束时间", dateFormat = "yyyyMMdd")
    private Date endDate;
    /**
     * 是否有试用期
     */
//    @Excel(name = "是否有试用期", readConverterExp = "1=否,2=是")
    private Integer probationFlag;
//    @Excel(name = "试用期起始时间", dateFormat = "yyyyMMdd")
    private Date probaStart;
//    @Excel(name = "试用期月数")
    private Integer probationMonths;//试用期月数
//    @Excel(name = "试用期结束时间", dateFormat = "yyyyMMdd")
    private Date probaEnd;
//    @Excel(name = "试用工资")
    private BigDecimal probaSalary;
//    @Excel(name = "备注")
    private String remark;
    /**
     * 合同签订地
     */
//    @Excel(name = "合同签订地")
    private String signPlace;
    /**
     * 合同版本地
     */
//    @Excel(name = "合同版本地")
    private String tempPlace;
    /**
     * 合同版本类型
     */
//    @Excel(name = "劳动合同版本", readConverterExp = "1=范本,2=客户范本")
    private Integer tempType;
    /**
     * 劳动合同类别
     */
//    @Excel(name = "劳动合同类别", readConverterExp = "1=劳动合同&有固定期限,2=劳动合同&无固定期限（审批）,3=劳务合同&实习（学生证）,4=劳务合同&退休（退休证明）,5=劳务合同&兼职（审批）")
    private Integer empContractType;
//    @Excel(name = "用工单位")
    private String employingUnit;//用工单位
    /**
     * 终止原因
     */
//    @Excel(name = "终止聘用原因")
    private String termiReason;
    /**
     * 派单客服
     */
//    @Excel(name = " 派单客服")
    private String dispatchMan;
    /**
     * 接单客服
     */
//    @Excel(name = " 接单客服")
    private String receivingMan;
    private Date entryTime;
//    @Excel(name = "录入时间",dateFormat = "yyyyMMdd")
    private Date entryTimeEx;
    /**
     * 小合同编号
     */
    private String contractAreaNo;

    private Integer dispatchType;//派单类型
    /**
     * 唯一ID
     */
    private String employeeId;
    private Long custId;



    private String signDateS;
    private String signDateE;

    /**
     * 入离职状态
     */
//	@Excel(name = "入离职状态", readConverterExp = "1=入职未生效,2=在职,3=离职")
    private Byte entryDimissionStatus;
    private Integer entryDimissionStatusInt;
    /**
     * 附件Id
     */
    private String fileId;


    private String createTime;

    private String updater;


    private Date updateTime;

    private String delFlag;
    private String loginName;


//    private List<OrgPositionDto> userOrgPositionDtoList;

    private String contractType;
    private String cityCode;

    private String workPlace;

    private String jobPosition;

    private Integer uploadType;

    private Date applyDimissionDate;

    private Integer oprType;
//    @Excel(name = " 终止操作记录")
    private String oprRemark;
    private List<CustomerDto> customerDtoList;
}