package com.reon.hr.sp.report.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/03/19
 */
@Data
public class IncomeCountTable {
	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 账单ID
	 */
	private Long billId;

	/**
	 * 核销ID
	 */
	private Long checkId;

	/**
	 * 开票ID
	 */
	private Long invoiceId;

	/**
	 * 所属公司
	 */
	private String company;

	/**
	 * 大集团
	 */
	private String conglomerate;

	/**
	 * 客户id
	 */
	private Long custId;
	private String custName;

	/**
	 * 到款日期
	 */
	private Date payDate;

	/**
	 * 到账金额
	 */
	private BigDecimal payAmt;
	private BigDecimal alreadyCheckAmt;
	/**
	 * 财务摘要
	 */
	private String financeRemark;

	/**
	 * 凭证号（发票号）
	 */
	private String voucherNo;

	/**
	 * 账单所属月
	 */
	private Integer billMonth;

	/**
	 * 账单金额
	 */
	private BigDecimal billAmt;

	/**
	 * 含税服务费
	 */
	private BigDecimal taxInclusiveFee;

	/**
	 * 产品类型
	 */
	private Integer productType;

	/**
	 * 产品方案
	 */
	private Byte productProgram;

	/**
	 * 是否本地单(本地单/异地单)
	 */
	private Byte localFlag;

	/**
	 * 是否单立户(单立户/大户)
	 */
	private Integer accountFlag;

	/**
	 * 执行期
	 */
	private Date executionDate;

	/**
	 * 合同编号
	 */
	private String contractNo;

	/**
	 * 合同报价(单价)
	 */
	private BigDecimal contractPrice;

	/**
	 * 服务人次
	 */
	private Integer personsServedNum;

	/**
	 * 供应商服务人次
	 */
	private Integer supplierServedNum;

	/**
	 * 供应商成本
	 */
	private BigDecimal supplierCost;

	/**
	 * 一次性产品
	 */
	private Integer disposableItem;

	/**
	 * 渠道成本
	 */
	private BigDecimal channelCost;

	/**
	 * 服务成本
	 */
	private BigDecimal serverCost;

	/**
	 * 客服成本
	 */
	private BigDecimal csCost;

	/**
	 * 税点成本
	 */
	private BigDecimal taxCost;

	/**
	 * 其他
	 */
	private String other;

	/**
	 * 客服
	 */
	private String customService;

	/**
	 * 销售1
	 */
	private String sellerFirst;

	/**
	 * 销售2
	 */
	private String sellerSecond;

	/**
	 * 转单标注
	 */
	private Integer transferFlag;

	/**
	 * 转单时间
	 */
	private Date transferTime;

	/**
	 * 一次性产品毛利润
	 */
	private BigDecimal disposableGrossProfit;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改人
	 */
	private String updater;

	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 删除标识(Y:已删除，N:未删除)
	 */
	private String delFlag;

	private Long subCheckId;
	private BigDecimal disposAmt;

	private Integer employeeNum;
	private List<String> contractNoList;
    /** 单工伤人次 */
    private Integer industrialInjuryNum;}