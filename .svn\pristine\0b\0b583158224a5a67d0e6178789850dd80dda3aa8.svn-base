<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" title="客户名称" style="font-weight:800">&nbsp;客户名称</label>
                <div class="layui-input-inline">
                    <input type="text" id="custName" maxlength="20" name="custName" placeholder="请输入"
                           class="layui-input" autocomplete="off">
                </div>
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label" style="font-weight:800">开票抬头</label>
                <div class="layui-input-inline">
                    <input type="text"  id="title" name="title" class="layui-input" autocomplete="off">
                </div>
            </div>
        </div>
        <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" authURI="/customer/invoice/getListPage">查询</button>
        <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button>
    </form>
</blockquote>
<table class="layui-hide" id="invoiceGrid" lay-filter="invoiceFilter"></table>
<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="addOne" lay-event="add" authURI="/customer/invoice/gotoSavePage">新增</button>
    <button class="layui-btn layui-btn-sm" id="update" lay-event="update" authURI="/customer/invoice/gotoEditPage">修改</button>
    <button class="layui-btn layui-btn-sm" id="export" lay-event="export" authURI="/customer/invoice/exportInvoice">导出</button>
<%--    <button class="layui-btn layui-btn-sm" id="delete" lay-event="delete" authURI="/customer/invoice/delete">删除</button>--%>
  <%--  <button class="layui-btn layui-btn-sm" id="import" lay-event="import" authURI="/customer/invoice/import">数据导入</button>--%>
</script>
<script type="text/jsp" id="toolDemo">
    <a href="javascript:void(0)" title="修改" lay-event="update" authURI="/customer/invoice/gotoEditPage"><i class="layui-icon layui-icon-edit"></i></a>&nbsp;
<%--    <a href="javascript:void(0)" title="删除" lay-event="delete" authURI="/customer/invoice/delete"><i class="layui-icon layui-icon-delete"></i></a>&nbsp;--%>
    <a href="javascript:void(0)" title="查看" lay-event="query" authURI="/customer/invoice/gotoQueryPage"><i class="layui-icon layui-icon-search"></i></a>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/invoice/invoiceList.js?v=${publishVersion}"></script>
</body>
</html>