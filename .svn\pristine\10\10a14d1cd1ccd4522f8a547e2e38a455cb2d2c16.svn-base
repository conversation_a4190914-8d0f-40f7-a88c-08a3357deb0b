<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
            http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
            http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
            http://www.springframework.org/schema/redis http://www.springframework.org/schema/redis/spring-redis.xsd">

    <!-- 读取数据库配置文件 -->
    <context:property-placeholder location="classpath:test-jdbc.properties" ignore-unresolvable="true"></context:property-placeholder>

    <context:annotation-config/>
    <context:component-scan base-package="com.reon.hr.sp" ></context:component-scan>
    <aop:aspectj-autoproxy/>

	<!-- 引入系统其他配置文件 -->
    <bean id="propertiesReader" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <value>classpath*:*.properties</value>
        </property>
    </bean>

    <!-- 扩展propertiesReader为 placeholder -->
    <context:property-placeholder properties-ref="propertiesReader" ignore-unresolvable="true"/>

    <import resource="classpath*:spring-test-config-mybatis.xml" />
    <import resource="classpath*:spring-test-config-redis.xml" />
    <import resource="classpath*:spring-test-config-task.xml" />
<!--    <import resource="classpath*:spring-test-dubbo-consume.xml" />-->
</beans>