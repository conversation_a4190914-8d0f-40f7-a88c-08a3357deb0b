package com.reon.hr.sp.customer.service.ImportService;

import com.reon.hr.api.customer.dto.importData.AddEhrEmployeeDimissionImportDto;
import com.reon.hr.api.customer.dto.importData.AddEmployeeDimissionImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;

/**
 * <AUTHOR> on 2021/10/26.
 */
public interface BatchAddEhrEmployeeDimissionImportService {
    void batchAddEhrEmployeeDimissionImport(ImportDataDto<AddEhrEmployeeDimissionImportDto> importDataDto);
}
