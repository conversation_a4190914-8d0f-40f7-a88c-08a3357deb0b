package com.reon.hr.api.customer.utils;

import com.google.common.collect.Lists;
import com.reon.hr.api.customer.enums.insurancePractice.SuppliPayTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Administrator
 * @Description:
 * @Date: 2022/9/23 17:23
 * @Version: 1.0
 */
public class SocialSecurityPaymentImportUtil {
    public static final String DICTIONARY_TYPE = "PRODUCT_IND_TYPE";
    /**
     * 保存当前导入的类型 1 全额导入  2补差导入    3滞纳金
     */
    private static ThreadLocal<Integer> IMPORT_TYPE = ThreadLocal.withInitial(() -> 1);
    public static ThreadLocal<Integer> SUPPLIER_COMPARE_TYPE = ThreadLocal.withInitial(() -> 1);

    public static void setTheadLocal(Integer importType){
        IMPORT_TYPE.set(importType);
    }
    public static Integer getTheadLocal( ){
        return IMPORT_TYPE.get();
    }

    public static void clearTheadLocal(){
        IMPORT_TYPE.remove();
    }




    public static List<List<String>> getDynamicHead(Integer importType, List<String> prodNames) {
        ArrayList<List<String>>  template= new ArrayList<>();
        List<String> baseColumns = Lists.newArrayList();
        baseColumns.add("员工姓名*");
//        baseColumns.add("证件号码");
        baseColumns.add("订单号*");

        List<String> baseDynamicColums = Lists.newArrayList();
        List<String> differenceColumns = Lists.newArrayList();
        differenceColumns.add("补缴起始月*");
        differenceColumns.add("补缴截至月*");
        baseDynamicColums.addAll(differenceColumns);
        baseDynamicColums.add("企业金额*");
        baseDynamicColums.add("个人金额*");



        List<String> oldColumns = Lists.newArrayList();
        oldColumns.addAll(differenceColumns);
        oldColumns.add("个人基数*");
        oldColumns.add("企业基数*");

        List<String> baseHead;
        for (int i = 0; i < baseColumns.size(); i++) {
            baseHead = Lists.newArrayList();
            baseHead.add(baseColumns.get(i));
            template.add(baseHead);
        }
        //组装列
        if(SuppliPayTypeEnum.SuppliPayImportTypeEnum.DIFFERENCE_IMPORT.getCode().equals(importType)||SuppliPayTypeEnum.SuppliPayImportTypeEnum.ALL_IMPORT.getCode().equals(importType)){
            getProdColumn(prodNames, oldColumns, template);
        }else {
            getProdColumn(prodNames, baseDynamicColums, template);
        }
        return template;
    }

    private static void getProdColumn(List<String> prodCode, List<String> baseDynamicColumns, List<List<String>> list) {
        ArrayList<String> dynamicHead;
        for (String prodName : prodCode) {
            for (int i = 0; i < baseDynamicColumns.size(); i++) {
                dynamicHead = Lists.newArrayList();
                dynamicHead.add(prodName);;
                dynamicHead.add(baseDynamicColumns.get(i));
                list.add(dynamicHead);
            }
        }
    }


}
