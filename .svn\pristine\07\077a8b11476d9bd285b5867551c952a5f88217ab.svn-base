<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>薪资计算查看</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table-cell .layui-input {
            padding: 2px 3px;
            border-color: white;
        }
    </style>
</head>
<body class="childrenBody">
<input type="hidden" value="${payId}" id="payId">
<input type="hidden" id="confirmFlag">
<input type="hidden" id="billMonth">
<input type="hidden" id="contractNo">
<input type="hidden" id="templetId">

<table class="layui-hide" id="empId" lay-filter="empId"></table>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/cumulativeCalculation/lookSalaryEmp.js?v=${publishVersion}"></script>
<script type="text/jsp" id="toolbarDemo">
    <div class="layui-input-inline">
        <div class="layui-inline">
            姓名/身份证/唯一号：
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="keyword" autocomplete="off">
            </div>
        </div>
    </div>
    <button class="layui-btn layui-btn-sm" lay-event="query" authURI="/customer/salary/cumulativeCalculation/selectEmpSalary"><i class="layui-icon layui-icon-search"></i>查询</button>&nbsp;&nbsp;&nbsp;&nbsp;
    <button class="layui-btn layui-btn-sm" lay-event="export" authURI="/customer/salary/cumulativeCalculation/exportSalaryPayDetail"><i class="layui-icon layui-icon-search"></i>导出</button>
</script>
</body>
</html>