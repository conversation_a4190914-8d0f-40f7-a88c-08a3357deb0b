package com.reon.hr.api.bill.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ServiceAndCollectionCostVo implements Serializable {

    /**
     * 主键ID
     */
    private Long billId;

    /**
     * 开票id
     */
    private Long invoiceId;

    /**
     * 账单详表id
     */
    private Long id;

    /**
     * 账单年月
     */
    private String billMonth;

    /**
     * 财务应收年月
     */
    private String receivableMonth;

    /**
     * 客户帐套
     */
    private String templetName;

    /**
     * 客户帐套
     */
    private Long templetId;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 总金额
     */
    private BigDecimal receiveAmt;

    /**
     * 应收金额
     */
    private BigDecimal billReceiveAmt = BigDecimal.ZERO;

    /**
     * 总金额，用于前端判断
     */
    private BigDecimal checkReceiveAmt;

    /**
     * 总金额，用于前端保存上一次修改的值
     */
    private BigDecimal editFrontReceiveAmt;

    /**
     * 不含税总金额
     */
    private BigDecimal unTaxAmt;

       /**
     * 增值税金额
     */
    private BigDecimal receiveValTax;

    /**
     * 增值税税率
     */
    private BigDecimal valTaxRate;

    /**
     * 总人数
     */
    private String number;

    /**
     * 大合同类型
     */
    private Integer contractType;

    /**
     * 签单分公司
     */
    private String signCom;

    /**
     * 企业金额
     */
    private BigDecimal comAmt;

    /**
     * 个人金额
     */
    private BigDecimal indAmt;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 开票状态
     */
    private String invoiceStatus;

    /**
     * 核销状态
     */
    private String cancelStatus;

    /**
     * 客户id
     */
    private Long custId;

    /**
     * 已开票金额
     */
    private BigDecimal invoiceAmt = BigDecimal.ZERO;

    //账单类型
    private Integer billType;

    //核销分类类型
    private Integer detailType;
}
