/****************************************
 * Copyright (c) 2017 ML.
 * All rights reserved.
 * Created on 2017年6月26日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.api.bill.exception;


/**
 * @title 自定义异常
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2017年6月26日
 */
public class BillCheckException extends RuntimeException {
	private static final long serialVersionUID = 1L;

	public BillCheckException() {
		super();
	}

	public BillCheckException(String message) {
		super(message);
	}

	public BillCheckException(String message, Throwable cause) {
		super(message, cause);
	}

	public BillCheckException(Throwable cause) {
		super(cause);
	}

	protected BillCheckException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
