package com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.send;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.salary.SendSalaryInfoMailVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.customer.vo.salary.pay.SupplierSalaryInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;

public interface ISalarySendQueryWrapperService {

    static final ForkJoinPool SE_FORK_JOIN_POOL = new ForkJoinPool (1);

    /**
     *查询薪资发送邮件数据分页接口
     * @param sendSalaryInfoMailVo
     * @param page
     * @param limit
     * @return
     */
    Page<SendSalaryInfoMailVo> getSendSalaryInfoMailVoListPage(SendSalaryInfoMailVo sendSalaryInfoMailVo, Integer page, Integer limit);

    /**
     * 查询工资json数据接口
     * @param sendSalaryInfoMailVo
     * @return
     */
    List<SalaryInfoVo> getSalaryJsonInfoList(SendSalaryInfoMailVo sendSalaryInfoMailVo);

    /**
     * 更新薪资邮件发送次数接口
     * @param salaryInfoVo
     * @return
     */
    boolean updateByPrimaryKeySelective(SalaryInfoVo salaryInfoVo);

    List<SupplierSalaryInfoVo> getSalaryInfoByEmpIdAndPayIdList(@Param("list") List<Map<String, Object>> empIdAndPayIdMapList);

}
