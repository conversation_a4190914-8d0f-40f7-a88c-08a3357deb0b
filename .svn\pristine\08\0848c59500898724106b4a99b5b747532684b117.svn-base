package com.reon.hr.api.thirdpart.domain.invoice.nuonuo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 发票开具请求参数
 *
 * <AUTHOR>
 * @date 2024/07/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceRed implements Serializable {
    private String elecInvoiceNumber;
    private String orderNo;
    private String callBackUrl;
    private String billUuid;
    private String deptId;
    private String clerkId;
    private String paperInvoiceType;
    private String invoiceCode;
    private String orderTime;
    private String extensionNumber;
    private String invoiceNumber;
    private String invoiceId;
    private String invoiceLine;
    private String taxNum;
    private String billNo;
}