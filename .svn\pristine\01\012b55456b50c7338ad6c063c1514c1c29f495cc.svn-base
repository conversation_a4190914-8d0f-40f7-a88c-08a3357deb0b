package com.reon.hr.sp.customer.service.impl.salary.pay;

import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IDisabilityGoldRateWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ITaxTalWrapperService;
import com.reon.hr.api.base.enums.DisabilityGoldRateEnum;
import com.reon.hr.api.base.enums.DisabilityGoldRateLogEnum;
import com.reon.hr.api.base.enums.InsuranceRatioEnum;
import com.reon.hr.api.base.vo.DisabilityGoldRateVo;
import com.reon.hr.api.base.vo.TaxTabVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.PerInsuranceBillItemVo;
import com.reon.hr.api.bill.vo.PerInsuranceBillVo;
import com.reon.hr.api.change.enums.ProductCodeEnum;
import com.reon.hr.api.customer.enums.*;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.quotation.QuotationTaxFlag;
import com.reon.hr.api.customer.enums.salary.*;
import com.reon.hr.api.customer.vo.QuotationItemVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.customer.vo.commInsurOrder.EmpCardInfoVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.salary.AfterTaxDeductionsVo;
import com.reon.hr.api.customer.vo.salary.ExcessCalculationAmountDetailVo;
import com.reon.hr.api.customer.vo.salary.IndTaxApplyInfoVo;
import com.reon.hr.api.customer.vo.salary.PayEmpBaseVo;
import com.reon.hr.api.customer.vo.salary.pay.*;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.sp.customer.dao.commInsurOrder.EmpCardInfoMapper;
import com.reon.hr.sp.customer.dao.cus.*;
import com.reon.hr.sp.customer.dao.salary.*;
import com.reon.hr.sp.customer.dao.salaryImport.PayEmpBaseMapper;
import com.reon.hr.sp.customer.dao.salaryImport.PayItemDataMapper;
import com.reon.hr.sp.customer.dao.withholdingAgent.WithholdingAgentMapper;
import com.reon.hr.sp.customer.entity.salary.SalaryInfo;
import com.reon.hr.sp.customer.service.employee.salary.pay.SalaryCalculatorService;
import com.reon.hr.sp.customer.service.employee.salary.pay.SupplierSalaryInfoService;
import com.reon.hr.sp.customer.util.PayRollUtils;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2023/3/15.
 */
@Service
public class SalaryCalculatorServiceImpl implements SalaryCalculatorService {

    @Autowired
    private SalaryInfoMapper salaryInfoMapper;
    @Autowired
    private SupplierSalaryInfoService supplierSalaryInfoService;
    @Autowired
    private SalaryItemMapper salaryItemMapper;
    @Autowired
    private PayItemDataMapper payItemDataMapper;
    @Autowired
    private ITaxTalWrapperService taxTalService;
    @Autowired
    private IInsuranceBillWrapperService iInsuranceBillWrapperService;
    @Autowired
    private QuotationItemMapper quotationItemMapper;
    @Autowired
    private IndTaxApplyInfoMapper indTaxApplyInfoMapper;
    @Autowired
    private SpecImportInfoMapper specImportInfoMapper;
    @Autowired
    private ContractAreaMapper contractAreaMapper;
    @Autowired
    private IDisabilityGoldRateWrapperService disabilityGoldRateWrapperService;
    @Autowired
    private PayEmpBaseMapper payEmpBaseMapper;
    @Autowired
    private ContractRelativeQuotationMapper contractRelativeQuotationMapper;
    @Autowired
    private EmpCardInfoMapper empCardInfoMapper;
    @Autowired
    private WithholdingAgentMapper withholdingAgentMapper;
    @Autowired
    private ExcessCalculationAmountDetailMapper excessCalculationAmountDetailMapper;
    @Autowired
    private BillTempletMapper billTempletMapper;
    @Autowired
    private AfterTaxDeductionsMapper afterTaxDeductionsMapper;


    private final Integer SALARY_MONTH = 1;
    private final Integer SALARY_MONTH_2 = 2;
    private final Integer SALARY_MONTH_3 = 3;
    private final Integer SALARY_MONTH_12 = 12;
    private static final String ERROR_MESSAGE = "error_message";
    private final BigDecimal LABOR_WAGE_DEDUCTION_4000 = new BigDecimal("4000");
    private final BigDecimal LABOR_WAGE_DEDUCTION_TAX = new BigDecimal("0.2");
    private final BigDecimal LABOR_WAGE_DEDUCTION_800 = new BigDecimal("800");
    private static final Logger logger = LoggerFactory.getLogger (SalaryCalculatorServiceImpl.class);
    @Override
    public int payrollSalary(Long payId, String updater,TaxTabVo taxTabVo,SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        //新增工资信息
        //拉取导入数据
        List<PayItemDataVo> payItemDataList = payItemDataMapper.getEmpAll (payId);
        List<PayEmpBaseVo> payEmpBaseVoList = payEmpBaseMapper.getByPayId(payId);
        if (CollectionUtils.isNotEmpty (payItemDataList)||CollectionUtils.isNotEmpty (payEmpBaseVoList)) {
            List<PerInsuranceBillVo> listSubTotal = getBillSocial (salaryPayAndCategoryVo);   //获取账单信息
            //薪资项目总和 薪资类别绑定的所有薪资项
            List<SalaryEmpTitleVo> salaryEmpTitleVos = salaryItemMapper.selectEmpItemByPayId (payId, null);
            Map<String, List<SalaryEmpTitleVo>> EmpTitleMap = salaryEmpTitleVos.stream ().collect (Collectors.groupingBy (SalaryEmpTitleVo::getItemNo));
            //账单数据分组
            Map<Long, List<PerInsuranceBillVo>> mapBillList = listSubTotal.stream ().filter(prod->prod.getEmployeeId()!=null).collect (Collectors.groupingBy (PerInsuranceBillVo::getEmployeeId));
            //薪资数据分组
            Map<Long, List<PayItemDataVo>> listMap = payItemDataList.stream ().collect (Collectors.groupingBy (PayItemDataVo::getEmpId));
            List<Long> empIds = payItemDataList.stream().map(PayItemDataVo::getEmpId).distinct().collect(Collectors.toList());
            Map<Long, List<PayEmpBaseVo>> payEmpBaseVoListMap = payEmpBaseVoList.stream ().collect (Collectors.groupingBy (PayEmpBaseVo::getEmpId));
            if(CollectionUtils.isEmpty(empIds)){
                empIds=payEmpBaseVoList.stream().map(PayEmpBaseVo::getEmpId).distinct().collect(Collectors.toList());
            }
            List<String> withholdingAgentNoList = payEmpBaseVoList.stream().map(PayEmpBaseVo::getWithholdingAgentNo).distinct().collect(Collectors.toList());
            List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList = excessCalculationAmountDetailMapper.getByWithholdingAgentNoList(withholdingAgentNoList);
            Map<String, BigDecimal> accuExcessAmountMap=new HashMap<>();
            Map<String,BigDecimal> economicExcessAmountMap=new HashMap<>();
            Integer taxMonth = salaryPayAndCategoryVo.getTaxMonth();
            Integer lastTaxMonth = DateUtil.getYearMonthByCount(taxMonth, -1);
            for (ExcessCalculationAmountDetailVo detailVo:excessCalculationAmountDetailVoList) {
                if(detailVo.getExcessCalculationType()== ExcessCalculationTypeEnum.RESERVE_FUND.getCode()){
                    if(detailVo.getValidFrom()<=taxMonth&&taxMonth<=detailVo.getValidTo()){
                        accuExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+taxMonth,detailVo.getExcessCalculationAmount());
                    }
                    if(detailVo.getValidFrom()<=lastTaxMonth&&lastTaxMonth<=detailVo.getValidTo()){
                        accuExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+lastTaxMonth,detailVo.getExcessCalculationAmount());
                    }
                }else {
                    if(detailVo.getValidFrom()<=taxMonth&&taxMonth<=detailVo.getValidTo()){
                        economicExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+taxMonth,detailVo.getExcessCalculationAmount());
                    }
                    if(detailVo.getValidFrom()<=lastTaxMonth&&lastTaxMonth<=detailVo.getValidTo()){
                        economicExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+lastTaxMonth,detailVo.getExcessCalculationAmount());
                    }
                }
            }
            List<WithholdingAgentVo> withholdingAgentVoList = withholdingAgentMapper.getByNoList(withholdingAgentNoList);
            Map<String,String> payPlaceMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getPayPlace));
            Map<String,String> orgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            //Map<String, BigDecimal> accuExcessAmountMap=withholdingAgentVoList.stream().filter(withholdingAgentVo -> withholdingAgentVo.getAccuExcessAmount()!=null).collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getAccuExcessAmount));
            //Map<String,BigDecimal> economicExcessAmountMap=withholdingAgentVoList.stream().filter(withholdingAgentVo -> withholdingAgentVo.getEconomicExcessAmount()!=null).collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getEconomicExcessAmount));
            Map<String, Integer> withholdingAgentTypeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getWithholdingAgentType));
            Map<String, String> withholdingOrgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            List<String> payPlaceList=new ArrayList<>(payPlaceMap.values());
            List<AfterTaxDeductionsVo> afterTaxDeductionsVoList = afterTaxDeductionsMapper.getByWithholdingAgentNoList(withholdingAgentNoList);
            Map<String, AfterTaxDeductionsVo> afterTaxDeductionsVoMap = afterTaxDeductionsVoList.stream().collect(Collectors.toMap(a -> a.getWithholdingAgentNo() + "-" + a.getProductCode(), Function.identity()));
            //残障金
            List<DisabilityGoldRateVo> disabilityGoldRateVoList = disabilityGoldRateWrapperService.selectByCityCodeList(payPlaceList);
//            Map<String, BigDecimal> disabilityGoldRateMap = disabilityGoldRateVoList.stream()
//                    .collect(Collectors.toMap(
//                            obj -> {
//                                String key = obj.getCityCode() + "_" + obj.getType();
//                                if (StringUtils.isNotEmpty(obj.getOrgCode())) {
//                                    key += "_" + obj.getOrgCode();
//                                }
//                                return key;
//                            },
//                            DisabilityGoldRateVo::getRate
//                    ));

            //专项抵扣数据
            Map<String, EmpTaxDeductionVo> empTaxDeductionVoMap = selectEmpTaxDeductionVoMap(payEmpBaseVoList, taxMonth);
            //个税申报数据
            Integer lastLastTaxMonth = DateUtil.getYearMonthByCount(taxMonth, -2);
            Integer lastLastLastTaxMonth = DateUtil.getYearMonthByCount(taxMonth, -3);
            List<Integer> taxMonthList = new ArrayList<>();
            taxMonthList.add(lastTaxMonth);
            taxMonthList.add(lastLastTaxMonth);
            taxMonthList.add(lastLastLastTaxMonth);
            List<IndTaxApplyInfoVo> indTaxApplyInfoVoList = indTaxApplyInfoMapper.findByTaxMonthListAndEmpIds(taxMonthList, IndTaxApplyInfoItemType.NORMAL_SALARY.getCode(), empIds,withholdingAgentNoList);
            Map<String, IndTaxApplyInfoVo> indTaxApplyInfoVoMap = indTaxApplyInfoVoList.stream().collect(Collectors.toMap(i->i.getEmpId()+"-"+i.getWithholdingAgentNo()+"-"+i.getTaxMonth(), Function.identity(), (key1, key2) -> key2));
            //第一批次工资数据
            List<SalaryInfoVo> salaryInfoVoList=salaryInfoMapper.selectByEmpIdsAndSalaryPayAndCategoryVo(empIds,salaryPayAndCategoryVo,withholdingAgentNoList);
            Map<String, String> salaryJsonInfoByEmpIdMap = salaryInfoVoList.stream().collect(Collectors.toMap(salaryInfoVo -> salaryInfoVo.getEmpId()+"-"+salaryInfoVo.getWithholdingAgentNo(), SalaryInfoVo::getSalaryJsonInfo));
            //参保地
            Map<Long, String> receivingMap=setReceivingMap(empIds,salaryPayAndCategoryVo);
            //银行卡
            List<EmpCardInfoVo> empCardInfoVoList=empCardInfoMapper.getByEmpIdList(empIds);
            Map<Long, Long> empCardIdMap = empCardInfoVoList.stream().collect(Collectors.toMap(EmpCardInfoVo::getEmpId, EmpCardInfoVo::getId));
            List<SupplierSalaryInfoVo> supplierSalaryInfoVoList=new ArrayList<>();
            // 数据循环
            for (int i = 0; i < empIds.size(); i++) {
                PayEmpBaseVo payEmpBaseVo =new PayEmpBaseVo();
                if(CollectionUtils.isNotEmpty(payEmpBaseVoListMap.get(empIds.get(i)))){
                    payEmpBaseVo = payEmpBaseVoListMap.get(empIds.get(i)).get(0);
                }else {
                    payEmpBaseVo.setBaseSalary(BigDecimal.ZERO);
                    payEmpBaseVo.setIndEndowment(BigDecimal.ZERO);
                    payEmpBaseVo.setIndAccuFund(BigDecimal.ZERO);
                    payEmpBaseVo.setIndMedical(BigDecimal.ZERO);
                    payEmpBaseVo.setIndLongTermInsurance(BigDecimal.ZERO);
                    payEmpBaseVo.setBigMedicaid(BigDecimal.ZERO);
                    payEmpBaseVo.setSupplementaryMedicalInsurance(BigDecimal.ZERO);
                    payEmpBaseVo.setOutpatientMedicalCare(BigDecimal.ZERO);
                    payEmpBaseVo.setIndOther(BigDecimal.ZERO);
                    payEmpBaseVo.setIndSeriousIllness(BigDecimal.ZERO);
                    payEmpBaseVo.setIndUnemployeement(BigDecimal.ZERO);
                    payEmpBaseVo.setDisFund(BigDecimal.ZERO);
                    payEmpBaseVo.setEconomicAmount(BigDecimal.ZERO);
                }
                CalculateSalaryVo calculateSalaryVo = new CalculateSalaryVo ();
                calculateSalaryVo.setPayId(salaryPayAndCategoryVo.getId());
                calculateSalaryVo.setS025 (BigDecimal.ZERO);
                calculateSalaryVo.setS026 (BigDecimal.ZERO);
                calculateSalaryVo.setAddTotal (BigDecimal.ZERO);
                calculateSalaryVo.setReduceTotal (BigDecimal.ZERO);
                //插入对象
                SalaryInfo salaryInfo = new SalaryInfo ();
                salaryInfo.setEmpId (empIds.get(i));
                salaryInfo.setSalMonth (salaryPayAndCategoryVo.getSalaryMonth ());
                salaryInfo.setQuoteNo(payEmpBaseVo.getQuoteNo());
                salaryInfo.setPayPlace(payEmpBaseVo.getPayPlace());
                salaryInfo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setEmpName(payEmpBaseVo.getEmpName());
                calculateSalaryVo.setS055(payEmpBaseVo.getCrossBankHandlingFees());
                calculateSalaryVo.setS060(payEmpBaseVo.getUnionFees());
                calculateSalaryVo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setOrgCode(orgCodeMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setCityCode(payPlaceMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                if(payEmpBaseVo.getIncomeTaxAdjustment()==null){
                    payEmpBaseVo.setIncomeTaxAdjustment(BigDecimal.ZERO);
                }
                //calculateSalaryVo.setS054(payEmpBaseVo.getIncomeTaxAdjustment());
                calculateSalaryVo.setS054(BigDecimal.ZERO);
                calculateSalaryVo.setS043(payEmpBaseVo.getEconomicAmount());//经济补偿金
                BigDecimal economicAmountTop=economicExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+taxMonth);
                BigDecimal S044=BigDecimal.ZERO;
                if(economicAmountTop!=null){
                    calculateSalaryVo.setEconomicAmountTop(economicAmountTop);
                    S044 = payEmpBaseVo.getEconomicAmount().subtract(economicAmountTop);
                    if(S044.compareTo(BigDecimal.ZERO)<0){
                        S044=BigDecimal.ZERO;
                    }
                }
                calculateSalaryVo.setS044(S044);//经济补偿金超额计税部分
                BigDecimal lastEconomicAmountTop=economicExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+lastTaxMonth);
                calculateSalaryVo.setLastMonthEconomicAmountTop(lastEconomicAmountTop);
                salaryInfo.setEmpCardId(empCardIdMap.get(empIds.get(i)));
                salaryInfo.setStatus (SalaryInfoStatus.FUTURE_PAY.getCode());
                salaryInfo.setSendCnt (0);//发送次数
                salaryInfo.setSendStatus (1);//默认发送状态
                salaryInfo.setPayId (payId);
                salaryInfo.setSalaryCategoryId (salaryPayAndCategoryVo.getSalaryCategoryId ());
                salaryInfo.setCreator (updater);
                salaryInfo.setUpdater (updater);
                //查询抵扣信息
                EmpTaxDeductionVo empTaxDeductionVo=empTaxDeductionVoMap.get(empIds.get(i)+"-"+calculateSalaryVo.getWithholdingAgentNo());
                //List<EmpTaxDeductionVo> empTaxDeductionVos = empTaxDeductionMapper.getSpecialProject (subMap.getKey (), salaryPayAndCategoryVo.getTaxMonth (),salaryPayAndCategoryVo.getCustId());
                //薪资内容
                //增项数据总和
                List<PayItemDataVo> subSalarys = listMap.get(empIds.get(i));
                Map<String, Object> mapSalary = new HashMap<> ();
                //同个工资项会有多条
                Map<String, List<PayItemDataVo>> itemNoMaps = new HashMap<>();
                if(CollectionUtils.isNotEmpty(subSalarys)){
                    itemNoMaps=subSalarys.stream ().collect (Collectors.groupingBy (PayItemDataVo::getItemNo));
                }
                //itemNos
                //导入数据组装 每个工资项进行循环
                for (Map.Entry<String, List<PayItemDataVo>> itemNos : itemNoMaps.entrySet ()) {
                    List<PayItemDataVo> itemNo = itemNos.getValue ();
                    //数组进行排序
                    Collections.sort (itemNo, new Comparator<PayItemDataVo> () {
                        @Override
                        //从大到小排序
                        public int compare(PayItemDataVo o1, PayItemDataVo o2) {
                            return o2.getId ().compareTo (o1.getId ());
                        }
                    });
                    PayItemDataVo payItemDataVo = itemNo.get (0);
                    //求取税前增，税前减
                    getItemType (calculateSalaryVo, payItemDataVo);
                    //存入数据
                    mapSalary.put (itemNos.getKey (), payItemDataVo.getItemVal ());
                }
                //税前增项
                calculateSalaryVo.setAddTotal (calculateSalaryVo.getAddTotal ().add (payEmpBaseVo.getBaseSalary()));
                //税后增项
                calculateSalaryVo.setS026(calculateSalaryVo.getS026().add(payEmpBaseVo.getEconomicAmount()));
                calculateSalaryVo.setS001(payEmpBaseVo.getBaseSalary());
                //五险一金
                BigDecimal bigS003 = BigDecimal.ZERO;
                BigDecimal afterTaxDeduction = BigDecimal.ZERO;
                //1、如果不拉取社保账单的五险一金就使用固定模板的
                //2、如果拉取社保账单，但是导入填了数据，就使用导入的
                //pullFlag默认拉取，导入模板有一个不为null就不拉取
                boolean pullFlag=true;
                if(payEmpBaseVo.getIndEndowment()==null){
                    payEmpBaseVo.setIndEndowment(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndMedical()==null){
                    payEmpBaseVo.setIndMedical(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndLongTermInsurance()==null){
                    payEmpBaseVo.setIndLongTermInsurance(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getBigMedicaid()==null){
                    payEmpBaseVo.setBigMedicaid(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getSupplementaryMedicalInsurance()==null){
                    payEmpBaseVo.setSupplementaryMedicalInsurance(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getOutpatientMedicalCare()==null){
                    payEmpBaseVo.setOutpatientMedicalCare(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndUnemployeement()==null){
                    payEmpBaseVo.setIndUnemployeement(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndSeriousIllness()==null){
                    payEmpBaseVo.setIndSeriousIllness(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndAccuFund()==null){
                    payEmpBaseVo.setIndAccuFund(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                if(payEmpBaseVo.getIndOther()==null){
                    payEmpBaseVo.setIndOther(BigDecimal.ZERO);
                }else {
                    pullFlag=false;
                }
                calculateSalaryVo.setS031(payEmpBaseVo.getIndEndowment());//S031 养老个人
                calculateSalaryVo.setS032(payEmpBaseVo.getIndMedical());//S032 医疗个人

                calculateSalaryVo.setS062(payEmpBaseVo.getBigMedicaid());//S062 大额医疗补助
                calculateSalaryVo.setS064(payEmpBaseVo.getOutpatientMedicalCare());//S064 门诊医疗(社保)

                calculateSalaryVo.setS034(BigDecimal.ZERO);//S034 大病个人
                calculateSalaryVo.setS065(BigDecimal.ZERO);//S065 大病保险(税后扣除)
                String indSeriousIllnessKey = calculateSalaryVo.getWithholdingAgentNo() + "-" + InsuranceRatioEnum.ProductCode.SERIOUS_ILLNESS.getIndex();
                if(afterTaxDeductionsVoMap.containsKey(indSeriousIllnessKey)){
                    AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(indSeriousIllnessKey);
                    String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                    if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                        calculateSalaryVo.setS034(payEmpBaseVo.getIndSeriousIllness());//S034 大病个人
                    }else {
                        calculateSalaryVo.setS065(payEmpBaseVo.getIndSeriousIllness());//S065 大病保险(税后扣除)
                    }
                }else {
                    calculateSalaryVo.setS034(payEmpBaseVo.getIndSeriousIllness());//S034 大病个人
                }

                calculateSalaryVo.setS063(BigDecimal.ZERO);//S063 补充医疗保险
                calculateSalaryVo.setS066(BigDecimal.ZERO);//S066 补充医疗保险(税后扣除)
                String supplementaryMedicalInsuranceKey = calculateSalaryVo.getWithholdingAgentNo() + "-" + InsuranceRatioEnum.ProductCode.REPLENISH_MEDICAL_SECURITY.getIndex();
                if(afterTaxDeductionsVoMap.containsKey(supplementaryMedicalInsuranceKey)){
                    AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(supplementaryMedicalInsuranceKey);
                    String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                    if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                        calculateSalaryVo.setS063(payEmpBaseVo.getSupplementaryMedicalInsurance());//S063 补充医疗保险
                    }else {
                        calculateSalaryVo.setS066(payEmpBaseVo.getSupplementaryMedicalInsurance());//S066 补充医疗保险(税后扣除)
                    }
                }else {
                    calculateSalaryVo.setS063(payEmpBaseVo.getSupplementaryMedicalInsurance());//S063 补充医疗保险
                }

                calculateSalaryVo.setS061(BigDecimal.ZERO);//S061 个人长护险
                calculateSalaryVo.setS067(BigDecimal.ZERO);//S067 个人长护险(税后扣除)
                String indLongTermInsuranceKey = calculateSalaryVo.getWithholdingAgentNo() + "-" + InsuranceRatioEnum.ProductCode.LONG_TIME_NURSE.getIndex();
                if(afterTaxDeductionsVoMap.containsKey(indLongTermInsuranceKey)){
                    AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(indLongTermInsuranceKey);
                    String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                    if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                        calculateSalaryVo.setS061(payEmpBaseVo.getIndLongTermInsurance());//S061 个人长护险
                    }else {
                        calculateSalaryVo.setS067(payEmpBaseVo.getIndLongTermInsurance());//S067 个人长护险(税后扣除)
                    }
                }else {
                    calculateSalaryVo.setS061(payEmpBaseVo.getIndLongTermInsurance());//S061 个人长护险
                }

                calculateSalaryVo.setS033(payEmpBaseVo.getIndUnemployeement());//S033 失业个人
                calculateSalaryVo.setS035(payEmpBaseVo.getIndAccuFund());//S035 公积金个人
                calculateSalaryVo.setS036(payEmpBaseVo.getIndOther());//S036 其他个人

                if (pullFlag&&mapSalary.containsKey (SalaryItemEnum.S003.getItemNo ())) {
                    bigS003 = (BigDecimal) mapSalary.get (SalaryItemEnum.S003.getItemNo ());
                }
                //3、拉取社保账单且导入无数据才使用系统的数据
                if(SalaryPayEnum.pullFlagEnum.YES.getIndex ()==salaryPayAndCategoryVo.getPullFlag ()&&pullFlag){
                    bigS003 = BigDecimal.ZERO;
                    calculateSalaryVo.setS031(BigDecimal.ZERO);//S031 养老个人
                    calculateSalaryVo.setS032(BigDecimal.ZERO);//S032 医疗个人
                    calculateSalaryVo.setS061(BigDecimal.ZERO);//S061 个人长护险
                    calculateSalaryVo.setS062(BigDecimal.ZERO);//S062 大额医疗补助
                    calculateSalaryVo.setS063(BigDecimal.ZERO);//S063 补充医疗保险
                    calculateSalaryVo.setS064(BigDecimal.ZERO);//S064 门诊医疗(社保)
                    calculateSalaryVo.setS065(BigDecimal.ZERO);//S065 大病保险(税后扣除)
                    calculateSalaryVo.setS066(BigDecimal.ZERO);//S066 补充医疗保险(税后扣除)
                    calculateSalaryVo.setS067(BigDecimal.ZERO);//S067 个人长护险(税后扣除)
                    calculateSalaryVo.setS033(BigDecimal.ZERO);//S033 失业个人
                    calculateSalaryVo.setS034(BigDecimal.ZERO);//S034 大病个人
                    calculateSalaryVo.setS035(BigDecimal.ZERO);//S035 公积金个人
                    calculateSalaryVo.setS036(BigDecimal.ZERO);//S036 其他个人
                    //logger.info ("=====================拉取账单五险一金数据====================mapBillList" + mapBillList.toString ());
                    if (mapBillList.containsKey (empIds.get(i))) {
                        List<PerInsuranceBillVo> perInsuranceBillVoList = mapBillList.get(empIds.get(i));
                        for (int j = 0; j <perInsuranceBillVoList.size() ; j++) {
                            PerInsuranceBillVo perInsuranceBillVo = perInsuranceBillVoList.get(j);
                            //bigS003=bigS003.add(perInsuranceBillVo.getIndSubtotal());
                            List<PerInsuranceBillItemVo> perInsuranceBillItems = perInsuranceBillVo.getPerInsuranceBillItems();
                            Map<Integer, BigDecimal> indAmtMap = perInsuranceBillItems.stream().collect(Collectors.toMap(PerInsuranceBillItemVo::getProductCode, PerInsuranceBillItemVo::getIndAmt, BigDecimal::add));
                            /*EmpTitleMap.forEach((key,value)->{
                                value.forEach(salaryEmpTitleVo ->{
                                    Integer code = ProductCodeEnum.getCode(salaryEmpTitleVo.getItemName());
                                    if(code!=null){
                                        BigDecimal indAmt =BigDecimal.ZERO;
                                        if(indAmtMap.get(code)!=null){
                                            indAmt = PayRollUtils.cutBigDecimal (salaryEmpTitleVo.getItemNo(), EmpTitleMap, indAmtMap.get(code));
                                        }
                                        if(mapSalary.containsKey(salaryEmpTitleVo.getItemNo())){
                                            String indAmtStr = mapSalary.get(salaryEmpTitleVo.getItemNo()).toString();
                                            if(StringUtils.isNotBlank(indAmtStr)){
                                                indAmt=indAmt.add(new BigDecimal(indAmtStr));
                                            }
                                        }
                                        mapSalary.put(salaryEmpTitleVo.getItemNo(), indAmt);
                                    }
                                });
                            });*/
                            indAmtMap.forEach((productCode,indAmt)->{
                                indAmt = PayRollUtils.keepBigDecimal(SalaryPayEnum.decimalCntEnum.TWO.getIndex(), indAmt);
                                String productCodeKey = calculateSalaryVo.getWithholdingAgentNo() + "-" + productCode;
                                ProductCodeEnum productCodeEnumByCode = ProductCodeEnum.getProductCodeEnumByCode(productCode);
                                switch (productCodeEnumByCode){
                                    case PRODUCT_IND_TYPE1:
                                        calculateSalaryVo.setS031(calculateSalaryVo.getS031().add(indAmt));//S031 养老个人
                                        break;
                                    case PRODUCT_IND_TYPE2:
                                        calculateSalaryVo.setS032(calculateSalaryVo.getS032().add(indAmt));//S032 医疗个人
                                        break;
                                    case PRODUCT_IND_TYPE15:
                                        calculateSalaryVo.setS062(calculateSalaryVo.getS062().add(indAmt));//S062 大额医疗补助
                                        break;
                                    case PRODUCT_IND_TYPE16:
                                        if(afterTaxDeductionsVoMap.containsKey(productCodeKey)){
                                            AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(productCodeKey);
                                            String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                                            if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                                                calculateSalaryVo.setS063(calculateSalaryVo.getS063().add(indAmt));//S063 补充医疗保险
                                            }else {
                                                calculateSalaryVo.setS066(calculateSalaryVo.getS066().add(indAmt));//S066 补充医疗保险(税后扣除)
                                            }
                                        }else {
                                            calculateSalaryVo.setS063(calculateSalaryVo.getS063().add(indAmt));//S063 补充医疗保险
                                        }
                                        break;
                                    case PRODUCT_IND_TYPE17:
                                        calculateSalaryVo.setS064(calculateSalaryVo.getS064().add(indAmt));//S064 门诊医疗(社保)
                                        break;
                                    case PRODUCT_IND_TYPE18:
                                        if(afterTaxDeductionsVoMap.containsKey(productCodeKey)){
                                            AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(productCodeKey);
                                            String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                                            if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                                                calculateSalaryVo.setS061(calculateSalaryVo.getS061().add(indAmt));//S061 个人长护险
                                            }else {
                                                calculateSalaryVo.setS067(calculateSalaryVo.getS067().add(indAmt));//S066 个人长护险(税后扣除)
                                            }
                                        }else {
                                            calculateSalaryVo.setS061(calculateSalaryVo.getS061().add(indAmt));//S061 个人长护险
                                        }
                                        break;
                                    case PRODUCT_IND_TYPE3:
                                        calculateSalaryVo.setS033(calculateSalaryVo.getS033().add(indAmt));//S033 失业个人
                                        break;
                                    case PRODUCT_IND_TYPE6:
                                        if(afterTaxDeductionsVoMap.containsKey(productCodeKey)){
                                            AfterTaxDeductionsVo afterTaxDeductionsVo = afterTaxDeductionsVoMap.get(productCodeKey);
                                            String exclusionCustId = afterTaxDeductionsVo.getExclusionCustId();
                                            if(StringUtils.isNotBlank(exclusionCustId)&&exclusionCustId.contains(salaryPayAndCategoryVo.getCustId().toString())){
                                                calculateSalaryVo.setS034(calculateSalaryVo.getS034().add(indAmt));//S034 大病个人
                                            }else {
                                                calculateSalaryVo.setS065(calculateSalaryVo.getS065().add(indAmt));//S065 大病保险(税后扣除)
                                            }
                                        }else {
                                            calculateSalaryVo.setS034(calculateSalaryVo.getS034().add(indAmt));//S034 大病个人
                                        }
                                        break;
                                    case PRODUCT_IND_TYPE10:
                                    case PRODUCT_IND_TYPE11:
                                    case PRODUCT_IND_TYPE13:
                                        calculateSalaryVo.setS035(calculateSalaryVo.getS035().add(indAmt));//S035 公积金个人
                                        break;
                                    /*case PRODUCT_IND_TYPE_MINUS1:
                                        //特殊存值
                                        calculateSalaryVo.setS003(calculateSalaryVo.getS003()==null?indAmt:calculateSalaryVo.getS003().add(indAmt));
                                        break;*/
                                }
                                /*if(ProductCodeEnum.PRODUCT_IND_TYPE1.getCode().equals(productCode)){
                                    calculateSalaryVo.setS031(calculateSalaryVo.getS031().add(indAmt));//S031 养老个人
                                }else if(ProductCodeEnum.PRODUCT_IND_TYPE2.getCode().equals(productCode)){
                                    calculateSalaryVo.setS032(calculateSalaryVo.getS032().add(indAmt));//S032 医疗个人
                                }else if(ProductCodeEnum.PRODUCT_IND_TYPE3.getCode().equals(productCode)){
                                    calculateSalaryVo.setS033(calculateSalaryVo.getS033().add(indAmt));//S033 失业个人
                                }else if(ProductCodeEnum.PRODUCT_IND_TYPE6.getCode().equals(productCode)){
                                    calculateSalaryVo.setS034(calculateSalaryVo.getS034().add(indAmt));//S034 大病个人
                                }else if(ProductCodeEnum.PRODUCT_IND_TYPE10.getCode().equals(productCode)
                                        ||ProductCodeEnum.PRODUCT_IND_TYPE11.getCode().equals(productCode)
                                        ||ProductCodeEnum.PRODUCT_IND_TYPE13.getCode().equals(productCode)){
                                    calculateSalaryVo.setS035(calculateSalaryVo.getS035().add(indAmt));//S035 公积金个人
                                }else if(ProductCodeEnum.PRODUCT_IND_TYPE_MINUS1.getCode().equals(productCode)){

                                }else {
                                    calculateSalaryVo.setS036(calculateSalaryVo.getS036().add(indAmt));//S036 其他个人
                                }*/
                            });
                            //特殊存值处理
                            /*if(calculateSalaryVo.getS003()!=null){
                                bigS003=bigS003.subtract(calculateSalaryVo.getS003());
                                calculateSalaryVo.setS003(BigDecimal.ZERO);
                            }*/
                        }
                    }
                }
                bigS003=calculateSalaryVo.getS031().add(calculateSalaryVo.getS032()).add(calculateSalaryVo.getS061())
                        .add(calculateSalaryVo.getS033())
                        .add(calculateSalaryVo.getS034()).add(calculateSalaryVo.getS035())
                        .add(calculateSalaryVo.getS062()).add(calculateSalaryVo.getS063()).add(calculateSalaryVo.getS064());
                afterTaxDeduction=calculateSalaryVo.getS065().add(calculateSalaryVo.getS066()).add(calculateSalaryVo.getS067());

                BigDecimal accumulationFundTop=accuExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+taxMonth);
                BigDecimal S046 =BigDecimal.ZERO;
                if(accumulationFundTop!=null){
                    calculateSalaryVo.setAccumulationFundTop(accumulationFundTop);
                    S046 = calculateSalaryVo.getS035().subtract(accumulationFundTop);
                    if(S046.compareTo(BigDecimal.ZERO)<0){
                        S046=BigDecimal.ZERO;
                    }
                }
                calculateSalaryVo.setS046(S046);//公积金超额计税部分

                BigDecimal lastAccumulationFundTop=accuExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+lastTaxMonth);
                calculateSalaryVo.setLastMonthAccumulationFundTop(lastAccumulationFundTop);

                calculateSalaryVo.setEmpId (empIds.get(i));
                //第一批次工资数据
                String salaryJsonInfoFirst = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo());
                if(StringUtils.isBlank(salaryJsonInfoFirst)){
                    calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().subtract(calculateSalaryVo.getS046()));
                }
                //五险一金 截取小数
                bigS003 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S003.getItemNo (), EmpTitleMap, bigS003);
                calculateSalaryVo.setS003 (bigS003);
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().add(bigS003));
                afterTaxDeduction = PayRollUtils.cutBigDecimal (SalaryItemEnum.S025.getItemNo (), EmpTitleMap, afterTaxDeduction);
                calculateSalaryVo.setS025(calculateSalaryVo.getS025().add(afterTaxDeduction));
                calculateSalaryVo.setBillMonth (salaryPayAndCategoryVo.getBillMonth ());
                calculateSalaryVo.setTaxMonth(taxMonth);
                calculateSalaryVo.setSalaryCategoryId(salaryPayAndCategoryVo.getSalaryCategoryId());
                calculateSalaryVo.setEmploymentDate(payEmpBaseVo.getEmploymentDate());
                calculateSalaryVo.setLeaveDate(payEmpBaseVo.getLeaveDate());
                //残障金比率
                calculateSalaryVo.setS042(payEmpBaseVo.getDisFund());
               // BigDecimal disabilityGoldRate=BigDecimal.ZERO;
//                if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()))){
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }else {
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingOrgCodeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }


                DisabilityGoldRateVo disabilityGoldRateVo = getDisabilityGoldRateVo(disabilityGoldRateVoList,payPlaceMap,withholdingAgentTypeMap, withholdingOrgCodeMap,salaryInfo,salaryPayAndCategoryVo);

                BillTempletVo billTempletVo = billTempletMapper.getTempletVoByTempletId(salaryPayAndCategoryVo.getTempletId());
                if(calculateSalaryVo.getS042()==null){
                    if(billTempletVo.getIndTaxFlag()!=null&&billTempletVo.getIndTaxFlag()==BooleanTypeEnum.NO.getCode()){
                        calculateSalaryVo.setS042(BigDecimal.ZERO);
                    }
                }
                calculateSalaryVo.setCreateTime(salaryPayAndCategoryVo.getCreateTime());
                //数据准备
                try {
                    calculateSalaryVo.setTempletId(salaryPayAndCategoryVo.getTempletId());
                    getCalculateSalary (mapSalary, empTaxDeductionVo, calculateSalaryVo, taxTabVo, EmpTitleMap,indTaxApplyInfoVoMap,salaryJsonInfoByEmpIdMap,disabilityGoldRateVo,receivingMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException ("员工姓名" + payEmpBaseVo.getEmpName() + "计算正常工资数据准备时报错");
                }
                if(mapSalary.containsKey(ERROR_MESSAGE)){
                    throw new RuntimeException ("员工姓名" + payEmpBaseVo.getEmpName() + mapSalary.get(ERROR_MESSAGE).toString());
                }
                salaryInfo.setChangePayPlace(calculateSalaryVo.getChangePayPlace());
                salaryInfo.setNewPay(calculateSalaryVo.getNewPay());
                salaryInfo.setPayInsuranceFlag(calculateSalaryVo.getPayInsuranceFlag());

                setBankChangeFlag(salaryInfo,calculateSalaryVo);

                //实发工资+个税+残障金
                BigDecimal salaryTotal = calculateSalaryVo.getS007().add(calculateSalaryVo.getS006()).add(calculateSalaryVo.getS042());
                salaryTotal = salaryTotal.add(calculateSalaryVo.getS052()).add(calculateSalaryVo.getS051());
                salaryTotal = salaryTotal.add(calculateSalaryVo.getS054()).add(calculateSalaryVo.getS068());

                //计算工资服务费
                setServiceFee(salaryPayAndCategoryVo,payEmpBaseVo,salaryTotal,mapSalary,salaryInfo);

                //重置工资服务费
                //代发工资服务费
                /*List<QuotationItemVo> defaultQuotationItemVos=quotationItemMapper.getQuotationSolution (salaryPayAndCategoryVo.getContractNo ());
                List<QuotationItemVo> quotationItemVos =contractRelativeQuotationMapper.getQuotationItemByContractNo (salaryPayAndCategoryVo.getContractNo ());
                Map<String, QuotationItemVo> quoteNoMap =quotationItemVos.stream().collect(Collectors.toMap(QuotationItemVo::getQuoteNo, Function.identity(), (key1, key2) -> key2));
                BigDecimal serviceFee=BigDecimal.ZERO;
                if (quoteNoMap.size() > 0 && StringUtils.isNotBlank(payEmpBaseVo.getQuoteNo())){
                    QuotationItemVo quotationItemVo = quoteNoMap.get(payEmpBaseVo.getQuoteNo());
                    if(quotationItemVo!=null){
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }else {
                            serviceFee=quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                    }
                    mapSalary.put ("S002", serviceFee);
                }else {
                    if (CollectionUtils.isNotEmpty (defaultQuotationItemVos)){
                        QuotationItemVo quotationItemVo = defaultQuotationItemVos.get(0);
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_PAYROLL_CREDIT.getIndex ())){
                            serviceFee= quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }
                        mapSalary.put ("S002", serviceFee);
                    }
                }*/
                //数据小数位数保留
                Map<String, Object> mapSalaryResult = new HashMap<> ();
                for (Map.Entry<String, Object> subMapSalary : mapSalary.entrySet ()) {
                    if (EmpTitleMap.containsKey (subMapSalary.getKey ()) &&
                            Objects.equals (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getTxtFlag (), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex ())) {
                        BigDecimal result = PayRollUtils.keepBigDecimal (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getDecimalCnt (), subMapSalary.getValue ());
                        mapSalaryResult.put (subMapSalary.getKey (), result);
                    } else {
                        mapSalaryResult.put (subMapSalary.getKey (), subMapSalary.getValue ());
                    }
                }
                String salaryJsonInfo = JSONObject.fromObject (mapSalaryResult).toString ();
                salaryInfo.setSalaryJsonInfo (salaryJsonInfo);
                salaryInfoMapper.insertSelective (salaryInfo);
                supplierSalaryInfoVoList = setSupplierSalaryInfoVoList(supplierSalaryInfoVoList, salaryInfo, withholdingAgentTypeMap);
            }
            supplierSalaryInfoService.deleteByVoList(supplierSalaryInfoVoList);
        }
        return 0;
    }

    /**
     * 计算工资服务费
     * @param salaryPayAndCategoryVo
     * @param payEmpBaseVo
     * @param salaryTotal
     * @param mapSalary
     * @param salaryInfo
     */
    public void setServiceFee(SalaryPayAndCategoryVo salaryPayAndCategoryVo,PayEmpBaseVo payEmpBaseVo,BigDecimal salaryTotal,Map<String, Object> mapSalary,SalaryInfo salaryInfo){
        List<QuotationItemVo> defaultQuotationItemVos=quotationItemMapper.getQuotationSolution (salaryPayAndCategoryVo.getContractNo ());
        List<QuotationItemVo> quotationItemVos =contractRelativeQuotationMapper.getQuotationItemByContractNo (salaryPayAndCategoryVo.getContractNo ());
        Map<String, QuotationItemVo> quoteNoMap =quotationItemVos.stream().collect(Collectors.toMap(QuotationItemVo::getQuoteNo, Function.identity(), (key1, key2) -> key2));
        if (quoteNoMap.size() > 0 && StringUtils.isNotBlank(payEmpBaseVo.getQuoteNo())){
            QuotationItemVo quotationItemVo = quoteNoMap.get(payEmpBaseVo.getQuoteNo());
            if(quotationItemVo!=null){
                if ((Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                        ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ()))
                        &&quotationItemVo.getProratedFee()==BooleanTypeEnum.YES.getCode()){
                    setServiceFeeByFeeRatio(quotationItemVo,mapSalary,salaryTotal);
                }else {
                    setServiceFeeByPrice(quotationItemVo,mapSalary);
                }
            }
        }else {
            if (CollectionUtils.isNotEmpty (defaultQuotationItemVos)){
                QuotationItemVo quotationItemVo = defaultQuotationItemVos.get(0);
                if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_PAYROLL_CREDIT.getIndex ())){
                    setServiceFeeByPrice(quotationItemVo,mapSalary);
                    salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())){
                    if(quotationItemVo.getProratedFee()==BooleanTypeEnum.YES.getCode()){
                        setServiceFeeByFeeRatio(quotationItemVo,mapSalary,salaryTotal);
                    }else {
                        setServiceFeeByPrice(quotationItemVo,mapSalary);
                    }
                    salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                    //外包一的，不填报价单时，如果默认报价单为按比例收费，就也计算服务费
                    if(quotationItemVo.getProratedFee()==BooleanTypeEnum.YES.getCode()){
                        setServiceFeeByFeeRatio(quotationItemVo,mapSalary,salaryTotal);
                        salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                    }
                }
            }
        }
    }

    /**
     * 根据报价计算服务费
     * @param quotationItemVo
     * @param mapSalary
     */
    public void setServiceFeeByPrice(QuotationItemVo quotationItemVo,Map<String, Object> mapSalary){
        BigDecimal serviceFee=quotationItemVo.getPrice();
        if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
            serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        mapSalary.put ("S002", serviceFee);
    }

    /**
     * 根据服务比例计算服务费
     * @param quotationItemVo
     * @param mapSalary
     * @param salaryTotal
     */
    public void setServiceFeeByFeeRatio(QuotationItemVo quotationItemVo,Map<String, Object> mapSalary,BigDecimal salaryTotal){
        //总金额*服务费比例
        BigDecimal serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
        if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
            //总金额*服务费比例*(1+税率)
            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        mapSalary.put ("S002", serviceFee);
    }

    /**
     * 扣缴义务人非供应商的需要删除供应商工资数据
     * @param supplierSalaryInfoVoList
     * @param salaryInfo
     * @param withholdingAgentTypeMap
     * @return
     */
    public List<SupplierSalaryInfoVo> setSupplierSalaryInfoVoList(List<SupplierSalaryInfoVo> supplierSalaryInfoVoList,SalaryInfo salaryInfo,Map<String, Integer> withholdingAgentTypeMap){
        SupplierSalaryInfoVo supplierSalaryInfoVo = new SupplierSalaryInfoVo();
        if (!Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE3.getIndex(),withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()))){
            supplierSalaryInfoVo.setPayId(salaryInfo.getPayId());
            supplierSalaryInfoVo.setEmpId(salaryInfo.getEmpId());
            supplierSalaryInfoVoList.add(supplierSalaryInfoVo);
        }
        return supplierSalaryInfoVoList;
    }

    private List<com.reon.hr.api.bill.vo.PerInsuranceBillVo> getBillSocial(SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        List<com.reon.hr.api.bill.vo.PerInsuranceBillVo> list = Lists.newArrayList ();
        //获取社保，抛异常
        if (salaryPayAndCategoryVo.getTaxListId () == null) {
            throw new RuntimeException ("薪资类别为" + salaryPayAndCategoryVo.getCategoryNo () + "税率表不能为空");
        }
        if (Objects.equals (salaryPayAndCategoryVo.getPullFlag (), SalaryPayEnum.pullFlagEnum.YES.getIndex ()) && salaryPayAndCategoryVo.getBasisType () == null) {
            throw new RuntimeException ("薪资类别为" + salaryPayAndCategoryVo.getCategoryNo () + "拉取社保数据，没有选择数据来源");
        }
        if (salaryPayAndCategoryVo.getBillMonth () == null) {
            throw new RuntimeException ("薪资类别为" + salaryPayAndCategoryVo.getCategoryNo () + "没有选择客户账单月");
        }
        Integer receivableMonth = null;
        /**
         * 拉取账单数据，指定账单月*/
        if (SalaryPayEnum.pullFlagEnum.YES.getIndex ()==salaryPayAndCategoryVo.getPullFlag ()) {
            if (SalaryPayEnum.pullMonthEnum.STATUS_LAST.getIndex ()==salaryPayAndCategoryVo.getPullMonth ()) {
                receivableMonth = DateUtil.getYearMonthByCount (salaryPayAndCategoryVo.getBillMonth (), -1);
            } else if (SalaryPayEnum.pullMonthEnum.STATUS_LAST_LAST.getIndex ()==salaryPayAndCategoryVo.getPullMonth ()) {
                receivableMonth = DateUtil.getYearMonthByCount (salaryPayAndCategoryVo.getBillMonth (), -2);
            }else {
                receivableMonth = salaryPayAndCategoryVo.getBillMonth ();
            }

            //获取账单数据
            InsuranceBillVo insuranceBillVo = new InsuranceBillVo ();
            insuranceBillVo.setBillMonth (salaryPayAndCategoryVo.getBillMonth ());
            insuranceBillVo.setReceivableMonth (receivableMonth);
            insuranceBillVo.setContractNo (salaryPayAndCategoryVo.getContractNo ());
            insuranceBillVo.setCustId (salaryPayAndCategoryVo.getCustId ());
            //insuranceBillVo.setTempletId(salaryPayAndCategoryVo.getTempletId());
            list = iInsuranceBillWrapperService.getEmpIndSubtotal (insuranceBillVo,salaryPayAndCategoryVo.getBasisType());
        }

        return list;
    }

    /**
     * 查询、计算工资抵扣数据。
     * @param payEmpBaseVoList
     * @param taxMonth
     * @return
     */
    private Map<String,EmpTaxDeductionVo> selectEmpTaxDeductionVoMap(List<PayEmpBaseVo> payEmpBaseVoList,Integer taxMonth){
        List<EmpTaxDeductionVo> thisEmpTaxDeductionVoList=specImportInfoMapper.getByMonthAndEmpIdList(payEmpBaseVoList,taxMonth);
        Map<String, EmpTaxDeductionVo> thisEmpTaxDeductionVoMap = thisEmpTaxDeductionVoList.stream().filter(empTaxDeductionVo ->
                {
                    if (empTaxDeductionVo.getIncomeMonth().equals(taxMonth)) {
                        return true;
                    }else {
                        //不等于计税月的，先判断是否是1月
                        if(DateUtil.getMonths(taxMonth).equals(SALARY_MONTH)){
                            return false;
                        }else {
                            //不是一月可以取上一个月的
                            Integer yearMonthByCount = DateUtil.getYearMonthByCount(taxMonth, -1);
                            return empTaxDeductionVo.getIncomeMonth().equals(yearMonthByCount);
                        }
                    }
                }
        ).collect(Collectors.toMap(empTaxDeductionVo -> empTaxDeductionVo.getEmpId()+"-"+empTaxDeductionVo.getWithholdingAgentNo(), Function.identity(), (key1, key2) ->
                {
                    Integer key1IncomeMonth = key1.getIncomeMonth();
                    Integer key2IncomeMonth = key2.getIncomeMonth();
                    //取最大的
                    if (key1IncomeMonth < key2IncomeMonth) {
                        return key2;
                    }else {
                        return key1;
                    }
                }
        ));
        Map<String, BigDecimal> otherDeductionMap=thisEmpTaxDeductionVoList.stream().collect(Collectors.groupingBy(empTaxDeductionVo -> empTaxDeductionVo.getEmpId()+"-"+empTaxDeductionVo.getWithholdingAgentNo(),Collectors.collectingAndThen(Collectors.toList(),
                etd-> etd.stream().map(EmpTaxDeductionVo::getOtherDeduction).reduce(BigDecimal.ZERO, BigDecimal::add))));
        for (PayEmpBaseVo payEmpBaseVo:payEmpBaseVoList) {
            String key = payEmpBaseVo.getEmpId() + "-" + payEmpBaseVo.getWithholdingAgentNo();
            if(thisEmpTaxDeductionVoMap.containsKey(key)){
                EmpTaxDeductionVo thisEmpTaxDeductionVo = thisEmpTaxDeductionVoMap.get(key);
                thisEmpTaxDeductionVo.setOtherDeduction(otherDeductionMap.get(key));
                thisEmpTaxDeductionVo.setTreatment(thisEmpTaxDeductionVo.getTreatment()!=null?thisEmpTaxDeductionVo.getTreatment():BigDecimal.ZERO);
            }else {
                EmpTaxDeductionVo newEmpTaxDeductionVo=new EmpTaxDeductionVo();
                newEmpTaxDeductionVo.setEmpId(payEmpBaseVo.getEmpId());
                newEmpTaxDeductionVo.setIncomeMonth(taxMonth);
                newEmpTaxDeductionVo.setChildEdu(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setRent(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setInterest(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setSupportElder(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setBabyCare(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setPersonalPension(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setOtherDeduction(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setTreatment(BigDecimal.ZERO);
                newEmpTaxDeductionVo.setContiEdu(BigDecimal.ZERO);
                thisEmpTaxDeductionVoMap.put(key,newEmpTaxDeductionVo);
            }
        }
        return thisEmpTaxDeductionVoMap;
    }

    /**
     * create by: guoqian
     * description: TODO
     * create time: 2021/1/12 0012 16:04
     *
     * @param mapSalary         存值json
     * @param calculateSalaryVo 计算系统值
     * @param taxTabVo          税率表
     * @return
     * @Param empTaxDeductionVos 待抵扣专项
     *
     */
    private  Map<String, Object> getCalculateSalary(Map<String, Object> mapSalary, EmpTaxDeductionVo empTaxDeductionVo, CalculateSalaryVo calculateSalaryVo, TaxTabVo taxTabVo, Map<String, List<SalaryEmpTitleVo>> EmpTitleMap,
                                                          Map<String, IndTaxApplyInfoVo> indTaxApplyInfoVoMap, Map<String, String> salaryJsonInfoByEmpIdMap, DisabilityGoldRateVo disabilityGoldRateVo, Map<Long, String> receivingMap) {
        Integer lastTaxMonth = DateUtil.getYearMonthByCount(calculateSalaryVo.getTaxMonth(), -1);
        Integer lastLastTaxMonth = DateUtil.getYearMonthByCount(calculateSalaryVo.getTaxMonth(), -2);
        BigDecimal childEdu = BigDecimal.ZERO, rent = BigDecimal.ZERO, interest = BigDecimal.ZERO, supportElder = BigDecimal.ZERO, treatment = BigDecimal.ZERO, contiEdu = BigDecimal.ZERO,
                babyCare = BigDecimal.ZERO,personalPension=BigDecimal.ZERO,otherDeduction = BigDecimal.ZERO;
        if (empTaxDeductionVo!=null&&calculateSalaryVo.getWithholdingAgentNo().equals(empTaxDeductionVo.getWithholdingAgentNo())) {
            //累计各专项附加扣除
            childEdu = empTaxDeductionVo.getChildEdu()!=null?empTaxDeductionVo.getChildEdu():BigDecimal.ZERO;//累计子女教育支出
            rent = empTaxDeductionVo.getRent()!=null?empTaxDeductionVo.getRent():BigDecimal.ZERO;//累计住房租金支出
            interest = empTaxDeductionVo.getInterest()!=null?empTaxDeductionVo.getInterest():BigDecimal.ZERO;//累计住房贷款利息支出
            supportElder = empTaxDeductionVo.getSupportElder()!=null?empTaxDeductionVo.getSupportElder():BigDecimal.ZERO;//累计赡养老人支出
            babyCare = empTaxDeductionVo.getBabyCare()!=null?empTaxDeductionVo.getBabyCare():BigDecimal.ZERO;//累计3岁以下婴幼儿照护
            personalPension = empTaxDeductionVo.getPersonalPension()!=null?empTaxDeductionVo.getPersonalPension():BigDecimal.ZERO;//累计个人养老金
            otherDeduction = empTaxDeductionVo.getOtherDeduction()!=null?empTaxDeductionVo.getOtherDeduction():BigDecimal.ZERO;//累计其它扣除
            treatment = empTaxDeductionVo.getTreatment()!=null?empTaxDeductionVo.getTreatment():BigDecimal.ZERO;//累计大病医疗
            contiEdu = empTaxDeductionVo.getContiEdu()!=null?empTaxDeductionVo.getContiEdu():BigDecimal.ZERO;//累计继续教育
        }
        //累计专项附加扣除
        BigDecimal specialProject = childEdu.add (rent).add (interest).add (supportElder).add (treatment).add (contiEdu).add (babyCare).add(personalPension).add(otherDeduction);
        //基本扣除
        //reduceTotal.add( new BigDecimal(taxTabVo.getStartPoint()));
        //第一批次工资数据
        String salaryJsonInfo = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo());
        Integer count = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth ());
        CalculateSalaryVo calculateLast = new CalculateSalaryVo();
        CalculateSalaryVo calculateLastQueryVo = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth (),taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
        //查询最近一次数据 ，累计扣除，累计工资，累计五险一金
        if(StringUtils.isNotBlank(salaryJsonInfo)){
            calculateLast=getFirstEmpSalary(calculateSalaryVo,taxTabVo.getId());
            setEmpCardId(calculateSalaryVo,calculateLast);
        }else {
            if(calculateLastQueryVo!=null){
                BeanUtils.copyProperties(calculateLastQueryVo,calculateLast);
                setEmpCardId(calculateSalaryVo,calculateLast);
                if (!calculateLast.getTaxMonth().equals(lastTaxMonth)) {
                    calculateLast = null;
                }
            }else {
                calculateLast=null;
            }
        }
        // 1、如果扣缴义务人不相等，上次数据就是null
        // 2、有可能上次数据直接是null
        //如果之前没有数据，发薪是否新增：是；
        if(count>0){
            calculateSalaryVo.setNewPay(BooleanTypeEnum.NO.getCode());
        }else {
            calculateSalaryVo.setNewPay(BooleanTypeEnum.YES.getCode());
        }

        //如果上次数据是null，发薪是否新增：是；发薪地变更：是
        Integer month = DateUtil.getMonths(calculateSalaryVo.getTaxMonth());
        IndTaxApplyInfoVo indTaxApplyInfoVo=indTaxApplyInfoVoMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo()+"-"+calculateSalaryVo.getTaxMonth());
        if(calculateLast==null){
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.YES.getCode());
            if(indTaxApplyInfoVo!=null&& month !=SALARY_MONTH
                    && indTaxApplyInfoVo.getWithholdingAgentNo().equals(calculateSalaryVo.getWithholdingAgentNo())){
                calculateLast=new CalculateSalaryVo();
                calculateLast.setS006(BigDecimal.ZERO);
                calculateLast.setS044(BigDecimal.ZERO);
                calculateLast.setS045(BigDecimal.ZERO);
                calculateLast.setS046(BigDecimal.ZERO);
                calculateLast.setS047(BigDecimal.ZERO);
                calculateLast.setS008(indTaxApplyInfoVo.getAccuIncome());//累计收入->累计工资（系统）
                calculateLast.setS010(indTaxApplyInfoVo.getAccuDeduTax());//累计应扣缴税额->累计扣税（不包含当月）（系统）
                calculateLast.setS053(BigDecimal.ZERO);
                calculateLast.setS054(BigDecimal.ZERO);
                calculateLast.setS051(BigDecimal.ZERO);
                calculateLast.setS011(indTaxApplyInfoVo.getAccuSpecDedu());//累计专项扣除->累计五险一金（系统）
                calculateLast.setS012(indTaxApplyInfoVo.getAccuRent());//累计住房租金支出扣除->累计住房租金支出扣除（系统）
                calculateLast.setS013(indTaxApplyInfoVo.getAccuInterest());//累计住房贷款利息支出扣除->累计住房贷款利息支出扣除（系统）
                calculateLast.setS014(indTaxApplyInfoVo.getAccuSupportElder());//累计赡养老人支出扣除->累计赡养老人支出扣除（系统）
                calculateLast.setS039(indTaxApplyInfoVo.getAccuBabyCare());//累计3岁以下婴幼儿照护->累计3岁以下婴幼儿照护（系统）
                calculateLast.setS049(indTaxApplyInfoVo.getAccuPersonalPension());//累计个人养老金->累计个人养老金（系统）
                calculateLast.setS041(indTaxApplyInfoVo.getAccuOtherDedu());//累计其它扣除->累计其它扣除（专项）（系统）
                calculateLast.setS015(indTaxApplyInfoVo.getAccuChildEdu());//累计子女教育支出扣除->累计子女教育支出扣除（系统）
                calculateLast.setS016(indTaxApplyInfoVo.getAccuContiEdu());//累计继续教育支出扣除->累计继续教育支出扣除（系统）
                calculateLast.setS017(indTaxApplyInfoVo.getAccuSubFee());//累计减除费用->累计基本减除费用（系统）
                //累计专项附加扣除=累计子女教育支出扣除+累计继续教育支出扣除+累计住房贷款利息支出扣除+累计住房租金支出扣除+累计赡养老人支出扣除
                BigDecimal fiveCumulativeSpecialProjects = indTaxApplyInfoVo.getAccuChildEdu().add(indTaxApplyInfoVo.getAccuContiEdu()).add(indTaxApplyInfoVo.getAccuInterest())
                        .add(indTaxApplyInfoVo.getAccuRent()).add(indTaxApplyInfoVo.getAccuSupportElder()).add(indTaxApplyInfoVo.getAccuBabyCare())
                        .add(indTaxApplyInfoVo.getAccuPersonalPension()).add(indTaxApplyInfoVo.getAccuOtherDedu());
                calculateLast.setS018(fiveCumulativeSpecialProjects);//累计专项附加扣除->累计专项附加扣除（系统）
                calculateLast.setS009(fiveCumulativeSpecialProjects.add(indTaxApplyInfoVo.getAccuSpecDedu()).add(indTaxApplyInfoVo.getAccuSubFee()));//累计专项附加扣除+累计专项扣除（累计五险一金）+累计减除费用->累计扣除（系统）
                calculateLast.setS029(indTaxApplyInfoVo.getCurrEnterpriseAnnuity());//本期企业(职业)年金->其它扣除（系统）
                //calculateLast.setS030(indTaxApplyInfoVo.getAccuOtherDedu());//累计其他扣除->累计其它扣除（系统）
                calculateLast.setS030(calculateLast.getS029());//累计其它扣除（系统）
            }
        }else {
            //如果上次数据不是null，否；发薪地变更：否
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.NO.getCode());
            BigDecimal S030=BigDecimal.ZERO;
            if(calculateLast.getS030()!=null){
                S030=S030.add(calculateLast.getS030());
            }
            if(calculateLast.getS029()!=null){
                S030=S030.add(calculateLast.getS029());
            }else {
                calculateLast.setS029(BigDecimal.ZERO);
            }
            calculateLast.setS030(S030);//累计其他扣除
        }
        setPayInsuranceFlag(receivingMap,calculateSalaryVo);
        //入职日期计算逻辑：
        //例如：系统计税月是9月，入职日期8月1日-8月31日，则抵扣额为10000；系统计税月9月，入职日期9月1日-9月30日，则抵扣额5000；
        //离职日期计算逻辑：
        //例如：系统计税月是9月，离职日期8月1日-8月31日，则计税月9月无5000抵扣额；系统计税月是9月，离职日期9月1日-9月30日，则计税月9月有5000抵扣额；
        Integer startPoint = taxTabVo.getStartPoint();
        if(calculateSalaryVo.getEmploymentDate()!=null){
            if (DateUtil.getMonth(calculateSalaryVo.getEmploymentDate())!= month) {
                startPoint+=taxTabVo.getStartPoint();
            }
        }
        if(calculateSalaryVo.getLeaveDate()!=null){
            if (DateUtil.getMonth(calculateSalaryVo.getLeaveDate())!= month) {
                startPoint-=taxTabVo.getStartPoint();
            }
        }
        BigDecimal lastDisFund=BigDecimal.ZERO;
        /**
         * 当前账单月为1月，但是查询上次出账单也是1月份就要进行累加*/
        if (((month != SALARY_MONTH) ||
                (month == SALARY_MONTH && calculateLast != null && DateUtil.getMonths (calculateLast.getTaxMonth ()) == SALARY_MONTH))
                && calculateLast != null) {
            calculateSalaryVo.setS008 (calculateLast.getS008 ().add (calculateSalaryVo.getAddTotal ()).subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()).subtract(calculateSalaryVo.getS046()));//S008   累计工资
            calculateSalaryVo.setS010 (calculateLast.getS010 ().add (calculateLast.getS006 ()).add(calculateLast.getS054())); //S010   累计扣税（不包含当月）
            calculateSalaryVo.setS053 (calculateLast.getS053 ().add (calculateLast.getS051 ())); //S053   累计经济补偿金扣税（不包含当月）
            calculateSalaryVo.setS011 (calculateLast.getS011 ().add (calculateSalaryVo.getS003 ()));//S011  累计五险一金
            calculateSalaryVo.setS018 (specialProject);//S018   累计专项附加扣除
            if(StringUtils.isBlank(salaryJsonInfo)){
                calculateSalaryVo.setS017 (calculateLast.getS017 ().add (new BigDecimal (startPoint)));//S017      累计基本减除费用
                setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS045(calculateLast.getS045().add(calculateSalaryVo.getS044()));
                setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS047(calculateLast.getS047().add(calculateSalaryVo.getS046()));
            }else {
                //二批计算的
                calculateSalaryVo.setS017 (calculateLast.getS017 ());//S017      累计基本减除费用
                if(calculateSalaryVo.getEconomicAmountTop()!=null){
                    BigDecimal S044 = calculateLast.getS043().add(calculateSalaryVo.getS043()).subtract(calculateSalaryVo.getEconomicAmountTop());
                    if(S044.compareTo(BigDecimal.ZERO)<0){
                        S044=BigDecimal.ZERO;
                    }
                    calculateSalaryVo.setS044(S044.subtract(calculateLast.getS044()));
                }else {
                    calculateSalaryVo.setS044(BigDecimal.ZERO);
                }
                setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS045(calculateLast.getS045().add(calculateSalaryVo.getS044()));
                if(calculateSalaryVo.getAccumulationFundTop()!=null){
                    BigDecimal S046 = calculateLast.getS035().add(calculateSalaryVo.getS035()).subtract(calculateSalaryVo.getAccumulationFundTop());
                    if(S046.compareTo(BigDecimal.ZERO)<0){
                        S046=BigDecimal.ZERO;
                    }
                    calculateSalaryVo.setS046(S046.subtract(calculateLast.getS046()));
                }else {
                    calculateSalaryVo.setS046(BigDecimal.ZERO);
                }
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().subtract(calculateSalaryVo.getS046()));
                setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS047(calculateLast.getS047().add(calculateSalaryVo.getS046()));
                lastDisFund=calculateLast.getS042();
            }
            //累计五险一金+累计专项附加扣除+累计基本扣减-累计公积金超额部分
            calculateSalaryVo.setS009 (calculateSalaryVo.getS011().add (calculateSalaryVo.getS018()).add(calculateSalaryVo.getS017()).subtract(calculateSalaryVo.getS047()));//S009    累计扣除

            //S029   其它扣除
            calculateSalaryVo.setS029(calculateLast.getS029());
            //S030   累计其它扣除
            calculateSalaryVo.setS030(calculateLast.getS030());
        } else {
            setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
            calculateSalaryVo.setS008 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()).subtract(calculateSalaryVo.getS046()));
            calculateSalaryVo.setS047(calculateSalaryVo.getS046());
            calculateSalaryVo.setS009 (specialProject.add (calculateSalaryVo.getS003 ()).add (new BigDecimal (startPoint)).subtract(calculateSalaryVo.getS047()));
            calculateSalaryVo.setS010 (BigDecimal.ZERO);
            calculateSalaryVo.setS053 (BigDecimal.ZERO);
            calculateSalaryVo.setS011 (calculateSalaryVo.getS003 ());
            calculateSalaryVo.setS017 (new BigDecimal (startPoint));
            calculateSalaryVo.setS018 (specialProject);
            calculateSalaryVo.setS029(BigDecimal.ZERO);
            calculateSalaryVo.setS030(BigDecimal.ZERO);
            setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
            calculateSalaryVo.setS045(calculateSalaryVo.getS044());
        }
        calculateSalaryVo.setS012 (rent); //S012  累计住房租金支出扣除
        calculateSalaryVo.setS013 (interest); //S013   累计住房贷款利息支出扣除
        calculateSalaryVo.setS014 (supportElder); //S014    累计赡养老人支出扣除
        calculateSalaryVo.setS039 (babyCare); //S039    累计3岁以下婴幼儿照护
        calculateSalaryVo.setS049 (personalPension); //S049    累计个人养老金
        calculateSalaryVo.setS041 (otherDeduction); //S041    累计其他扣除
        calculateSalaryVo.setS015 (childEdu);   //S015 累计子女教育支出扣除
        calculateSalaryVo.setS016 (contiEdu);//S016  累计继续教育支出扣除
        BigDecimal S012 = BigDecimal.ZERO,S013= BigDecimal.ZERO,S014= BigDecimal.ZERO,S015= BigDecimal.ZERO,S016= BigDecimal.ZERO,
                S018= BigDecimal.ZERO,S039= BigDecimal.ZERO,S041= BigDecimal.ZERO,S049=BigDecimal.ZERO;
        if(month != SALARY_MONTH&&calculateLast!=null){
            S012=calculateLast.getS012();
            S013=calculateLast.getS013();
            S014=calculateLast.getS014();
            S015=calculateLast.getS015();
            S016=calculateLast.getS016();
            S018=calculateLast.getS018();
            S039=calculateLast.getS039();
            S041=calculateLast.getS041();
            S049=calculateLast.getS049();
        }
        if(rent.compareTo(BigDecimal.ZERO)>0){
            //S019     本次扣减的住房租金支出
            calculateSalaryVo.setS019 (rent.subtract(S012));
        }
        if(interest.compareTo(BigDecimal.ZERO)>0){
            //S020     本次扣减的住房贷款利息
            calculateSalaryVo.setS020 (interest.subtract(S013));
        }
        if(supportElder.compareTo(BigDecimal.ZERO)>0){
            //S021 本次扣减的赡养老人支出
            calculateSalaryVo.setS021 (supportElder.subtract(S014));
        }
        if(babyCare.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的3岁以下婴幼儿照护
            calculateSalaryVo.setS038 (babyCare.subtract(S039));
        }
        if(personalPension.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的个人养老金
            calculateSalaryVo.setS048 (personalPension.subtract(S049));
        }
        if(otherDeduction.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的其它扣除
            calculateSalaryVo.setS040 (otherDeduction.subtract(S041));
        }
        if(childEdu.compareTo(BigDecimal.ZERO)>0){
            //S022	本次扣减子女教育支出
            calculateSalaryVo.setS022 (childEdu.subtract(S015));
        }
        if(contiEdu.compareTo(BigDecimal.ZERO)>0){
            //S023	本次扣减的继续教育支出
            calculateSalaryVo.setS023 (contiEdu.subtract(S016));
        }
        if(specialProject.compareTo(BigDecimal.ZERO)>0){
            //S024	本次使用专项附加扣除
            calculateSalaryVo.setS024 (specialProject.subtract(S018));
        }



        calculateSalaryVo.setS050(calculateSalaryVo.getS044());

        // S005;//应税工资(含个税基数)
        //五险一金加入到税前扣减
        calculateSalaryVo.setS005 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()));
        if(calculateSalaryVo.getS005().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS005(BigDecimal.ZERO);
        }
        //数据准备结束
        //计算个人所得税（累计工资-累计扣除-累计其它扣除+公积金超额计税部分）*税率-累计扣税（不包涵当月的）;
        BigDecimal taxableSalary = calculateSalaryVo.getS008 ().subtract (calculateSalaryVo.getS009 ()).subtract(calculateSalaryVo.getS030())
                .add(calculateSalaryVo.getS047());//累计应税工资
        calculateSalaryVo.setS056("");
        calculateSalaryVo.setS057(BigDecimal.ZERO);
        //个人所得税
        BigDecimal S006 = PayRollUtils.S006 (taxTabVo.getTaxSpreadsheetVos (), taxableSalary, calculateSalaryVo.getS010 (),calculateSalaryVo,false);
        S006=PayRollUtils.cutBigDecimal (SalaryItemEnum.S006.getItemNo (), EmpTitleMap, S006);
        //小数精确
        calculateSalaryVo.setS006 (S006);
        calculateSalaryVo.setS058("");
        calculateSalaryVo.setS059(BigDecimal.ZERO);
        //计算经济补偿金个人所得税（累计经济补偿金超额计税部分）*税率-累计扣税（不包涵当月的）;
        BigDecimal S051 = PayRollUtils.S006 (taxTabVo.getTaxSpreadsheetVos (), calculateSalaryVo.getS045(), calculateSalaryVo.getS053 (),calculateSalaryVo,true);
        //小数精确
        S051 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S051.getItemNo (), EmpTitleMap, S051);
        calculateSalaryVo.setS051 (S051);
        //S037
        calculateSalaryVo.setS037 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()).subtract(calculateSalaryVo.getS046()));
//        //计算残障金=应发工资*残障金比例
//        if(calculateSalaryVo.getS042()==null){
//            BigDecimal disFund = disabilityGoldRate!=null?(calculateSalaryVo.getS037().multiply(disabilityGoldRate)).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//            calculateSalaryVo.setS042(disFund);
//        }
        Boolean lastLastFlag=false;
        if(indTaxApplyInfoVo==null){
            indTaxApplyInfoVo=indTaxApplyInfoVoMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo()+"-"+lastTaxMonth);
            if(indTaxApplyInfoVo==null&&calculateSalaryVo.getChangePayPlace()==BooleanTypeEnum.NO.getCode()){
                indTaxApplyInfoVo=indTaxApplyInfoVoMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo()+"-"+lastLastTaxMonth);
                lastLastFlag=true;
                if(calculateLastQueryVo!=null){
                    //计算个人所得税（累计工资-累计扣除-累计其它扣除+公积金超额计税部分）*税率-累计扣税（不包涵当月的）;
                    BigDecimal calculateLastTaxableSalary = calculateLastQueryVo.getS008 ().subtract (calculateLastQueryVo.getS009 ()).subtract(calculateLastQueryVo.getS030())
                            .add(calculateLastQueryVo.getS047());//累计应税工资
                    PayRollUtils.S006 (taxTabVo.getTaxSpreadsheetVos (), calculateLastTaxableSalary, calculateLastQueryVo.getS010 (),calculateLastQueryVo,false);
                }
            }
        }
        //计税月11月，能查到计税月10月申报数据就跟计税月10月申报数据比较，能查到计税月9月申报数据就拿计税月10月的数据跟计税月9月的申报数据比较
        //跨年就多退少补，否则只补收，退在计算个税那
        //计税月上次的申报数据                            计税月当月的计算数据
        //累计应扣缴税额	已缴税额	  应补(退)税额		累计应扣缴税额	    累计扣税（不包含当月）	个税		个税调
        //5905.81	    6308.55	  0		            5905.81	         6146.33	        0		162.22
        //6308.55	    6308.55	  0		            5905.81	         6146.33	        0		162.22
        //6308.55	    5905.81	  402.74		    5905.81	         6146.33	        0		162.22
        //5905.81	    6308.55	  0		            5905.81	         5800	            105.81	402.74
        //5905.81	    6308.55	  0		            5905.81	         6000	            0		308.55
        //6308.55	    6308.55	  0		            6500	         6308.55	        191.45	0
        //1019.85	    1435.88	  0	                914	             1435.88	        0		0
        //914	        914		  0                 914	             1435.88	        0		-521.88

        if(indTaxApplyInfoVo!=null){
            BigDecimal indTaxApplyInfoVoMaxTax=indTaxApplyInfoVo.getAccuAddedTax().compareTo(indTaxApplyInfoVo.getAccuDeduTax())>=0?
                    indTaxApplyInfoVo.getAccuAddedTax():indTaxApplyInfoVo.getAccuDeduTax();
            //个税申报计税月	11	    12	    1       2
            //计税月	        12	    1(跨年)	2(跨年)  3
            //计税月	        1(不调)	2(跨年)	3       4
            if(month==SALARY_MONTH||month==SALARY_MONTH_2||month==SALARY_MONTH_3){
                Integer firstYearMonth = DateUtil.getFirstYearMonth(calculateSalaryVo.getTaxMonth());
                CalculateSalaryVo firstCalculateLast=getLastEmpSalary (calculateSalaryVo.getEmpId (), DateUtil.getNextYearMonth(firstYearMonth),taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
                CalculateSalaryVo secondCalculateLast=null;
                CalculateSalaryVo sameCalculateLast=null;
                if(month==SALARY_MONTH_2||month==SALARY_MONTH_3){
                    sameCalculateLast = getFirstEmpSalary(calculateSalaryVo,taxTabVo.getId());
                }
                if(month==SALARY_MONTH_3){
                    secondCalculateLast = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth(),taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
                }
                BigDecimal endIndTaxApplyInfoVoMaxTax=indTaxApplyInfoVoMaxTax;
                Integer endTaxMonth=DateUtil.getPreYearMonth(firstYearMonth);
                boolean endIndTaxApplyInfoVoFlag=true;
                if(DateUtil.getMonths (indTaxApplyInfoVo.getTaxMonth ()) != SALARY_MONTH_12){
                    IndTaxApplyInfoVo endIndTaxApplyInfoVo=indTaxApplyInfoVoMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo()+"-"+endTaxMonth);
                    if(endIndTaxApplyInfoVo==null){
                        endIndTaxApplyInfoVoFlag=false;
                    }else {
                        endIndTaxApplyInfoVoMaxTax=endIndTaxApplyInfoVo.getAccuAddedTax().compareTo(endIndTaxApplyInfoVo.getAccuDeduTax())>=0?
                                endIndTaxApplyInfoVo.getAccuAddedTax():endIndTaxApplyInfoVo.getAccuDeduTax();
                    }
                }
                if(endIndTaxApplyInfoVoFlag){
                    //跨年个税调=取累计应扣缴税额	已缴税额  中最大的，然后减去计税月12月计算的个税、累计扣税（不包含当月）、个税调、跨年个税调
                    CalculateSalaryVo calculate12MonthVo = getLastEmpSalary (calculateSalaryVo.getEmpId (), firstYearMonth,taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
                    if(calculate12MonthVo==null){
                        //12月断了就查11月的
                        calculate12MonthVo=getLastEmpSalary (calculateSalaryVo.getEmpId (), endTaxMonth,taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
                    }
                    if(calculate12MonthVo!=null){
                        BigDecimal discrepancyAmount = PayRollUtils.cutBigDecimal (SalaryItemEnum.S054.getItemNo (), EmpTitleMap, endIndTaxApplyInfoVoMaxTax.subtract(calculate12MonthVo.getS010()).subtract(calculate12MonthVo.getS006()).subtract(calculate12MonthVo.getS054()));
                        if(firstCalculateLast!=null){
                            discrepancyAmount=discrepancyAmount.subtract(firstCalculateLast.getS068());
                        }
                        if(sameCalculateLast!=null){
                            discrepancyAmount=discrepancyAmount.subtract(sameCalculateLast.getS068());
                        }
                        if(secondCalculateLast!=null){
                            discrepancyAmount=discrepancyAmount.subtract(secondCalculateLast.getS068());
                        }
                        calculateSalaryVo.setS068(discrepancyAmount);
                    }
                }
            }
            if(month!=SALARY_MONTH){
                //个税调=取累计应扣缴税额	已缴税额  (本月及上月)计算的累计应扣缴税额 四个中最大的，然后减去计算的个税、累计扣税（不包含当月）
                //TODO 根据薪资类别配置增加 个税调=取累计应扣缴税额	已缴税额  (本月)计算的累计应扣缴税额 三个中最大的，然后减去计算的个税、累计扣税（不包含当月）
                //TODO 根据薪资类别配置增加 不计算退的。小于0设为0
                indTaxApplyInfoVoMaxTax=indTaxApplyInfoVoMaxTax.compareTo(calculateSalaryVo.getAccuDeduTax())>=0?
                        indTaxApplyInfoVoMaxTax:calculateSalaryVo.getAccuDeduTax();
                if(calculateLastQueryVo!=null){
                    indTaxApplyInfoVoMaxTax=indTaxApplyInfoVoMaxTax.compareTo(calculateLastQueryVo.getAccuDeduTax())>=0?
                            indTaxApplyInfoVoMaxTax:calculateLastQueryVo.getAccuDeduTax();
                }
                indTaxApplyInfoVoMaxTax=PayRollUtils.keepBigDecimal(SalaryPayEnum.decimalCntEnum.TWO.getIndex(),indTaxApplyInfoVoMaxTax);
                BigDecimal discrepancyAmount = PayRollUtils.cutBigDecimal (SalaryItemEnum.S054.getItemNo (), EmpTitleMap, indTaxApplyInfoVoMaxTax.subtract(calculateSalaryVo.getS010()).subtract(S006));
                calculateSalaryVo.setS054(discrepancyAmount);
            }
        }
        // S007;//实发合计 （增项-请假（减项））-五险一金-个人所得税-税后减+税后增-个税调-跨年个税调
        calculateSalaryVo.setS007 (calculateSalaryVo.getS037 ().subtract (calculateSalaryVo.getS003 ()).subtract (calculateSalaryVo.getS006 ()).subtract (calculateSalaryVo.getS025 ())
                .add (calculateSalaryVo.getS026 ()).subtract(calculateSalaryVo.getS043()).subtract(calculateSalaryVo.getS054()).subtract(calculateSalaryVo.getS068()));
        if(calculateSalaryVo.getS007().compareTo(BigDecimal.ZERO) < 0){
            if(calculateSalaryVo.getS054().compareTo(BigDecimal.ZERO)!=0||calculateSalaryVo.getS068().compareTo(BigDecimal.ZERO)!=0){
                mapSalary.put(ERROR_MESSAGE,"姓名为"+calculateSalaryVo.getEmpName()+"的员工计算出个税调:"+calculateSalaryVo.getS054()
                        +",跨年个税调:"+calculateSalaryVo.getS068()+"，且实发为负！实发:"+calculateSalaryVo.getS007());
            }/*else {
                mapSalary.put(ERROR_MESSAGE,"姓名为"+calculateSalaryVo.getEmpName()+"的员工计算出实发为负！实发:"+calculateSalaryVo.getS007());
            }*/
            calculateSalaryVo.setS007(BigDecimal.ZERO);
        }
        //计算残障金=应发工资*残障金比例
        if(calculateSalaryVo.getS042()==null){
            BigDecimal disFund = countDisFund(disabilityGoldRateVo, calculateSalaryVo.getS037(), calculateSalaryVo.getS007(),lastDisFund);
            calculateSalaryVo.setS042(disFund);
        }
        // S052;//经济补偿金实发合计 经济补偿金-个人所得税
        calculateSalaryVo.setS052 (calculateSalaryVo.getS043 ().subtract (calculateSalaryVo.getS051 ()));
        if(calculateSalaryVo.getS052().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS052(BigDecimal.ZERO);
        }
        calculateSalaryVo.setS002 (BigDecimal.ZERO);
        calculateSalaryVo.setS027 (calculateSalaryVo.getAddTotal ());
        calculateSalaryVo.setS028 (calculateSalaryVo.getReduceTotal ());
        //把calculateSalaryVo数据存map中
        Method method = null;
        for (int i = 1; i <= calculateSalaryVo.getClass().getDeclaredFields().length; i++) {
            try {
                method = calculateSalaryVo.getClass ().getDeclaredMethod ("getS" + String.format ("%03d", i));
                mapSalary.put ("S" + String.format ("%03d", i), method.invoke (calculateSalaryVo));
            } catch (NoSuchMethodException ignored) {
            } catch (IllegalAccessException e) {
                e.printStackTrace ();
            } catch (InvocationTargetException e) {
                e.printStackTrace ();
            }

        }
        return mapSalary;

    }
    public void setUpdatedLastMonthAccumulationFundTopS046(CalculateSalaryVo calculateSalaryVo, TaxTabVo taxTabVo, CalculateSalaryVo calculateLast){
        if(calculateLast!=null){
            //上次公积金-变更后的上次超上限金额,小于0则为0,然后再减去上次的部分就是差异
            if(calculateSalaryVo.getLastMonthAccumulationFundTop()!=null){
                BigDecimal updatedLastS046 = calculateLast.getS035Total().subtract(calculateSalaryVo.getLastMonthAccumulationFundTop());
                if(updatedLastS046.compareTo(BigDecimal.ZERO)<=0){
                    updatedLastS046=BigDecimal.ZERO;
                }
                BigDecimal discrepancy = updatedLastS046.subtract(calculateLast.getS046Total());
                //都放本次
                //TODO 跨年的调整,不能放在本次的累计
                calculateSalaryVo.setS046(calculateSalaryVo.getS046().add(discrepancy));
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().subtract(discrepancy));
                /*if(DateUtil.getMonths(calculateLast.getTaxMonth()).equals(SALARY_MONTH_12)){

                }*/
            }
        }
    }
    public void setUpdatedLastMonthEconomicAmountTopS044(CalculateSalaryVo calculateSalaryVo, TaxTabVo taxTabVo, CalculateSalaryVo calculateLast){
        if(calculateLast!=null){
            //上次经济补偿金-变更后的上次超上限金额,小于0则为0,然后再减去上次的部分就是差异
            if(calculateSalaryVo.getLastMonthEconomicAmountTop()!=null){
                BigDecimal updatedLastS044 = calculateLast.getS043().subtract(calculateSalaryVo.getLastMonthEconomicAmountTop());
                if(updatedLastS044.compareTo(BigDecimal.ZERO)<=0){
                    updatedLastS044=BigDecimal.ZERO;
                }
                BigDecimal discrepancy = updatedLastS044.subtract(calculateLast.getS044());
                //都放本次
                //TODO 跨年的调整,不能放在本次的累计
                calculateSalaryVo.setS044(calculateSalaryVo.getS044().add(discrepancy));
                /*if(DateUtil.getMonths(calculateLast.getTaxMonth()).equals(SALARY_MONTH_12)){

                }*/
            }
        }
    }

    @Override
    public CalculateSalaryVo getLastEmpSalary(Long empId, Integer taxMonth, Long taxListId,String withholdingAgentNo) {
        //改成只查上个计税月的
        //上个月多批数据的要整合一下公积金、公积金超额计算金额
        Integer lastTaxMonth = DateUtil.getYearMonthByCount(taxMonth, -1);
        List<CalculateSalaryVo> lastEmpSalaryList = salaryInfoMapper.getEmpSalaryList(empId, lastTaxMonth, taxListId, withholdingAgentNo);
        if(CollectionUtils.isEmpty(lastEmpSalaryList)){
            return null;
        }
        lastEmpSalaryList.sort(Comparator.comparing(CalculateSalaryVo::getPayId).reversed());
        CalculateSalaryVo calculateSalaryVo = lastEmpSalaryList.get(0);
        BigDecimal s046Total = BigDecimal.ZERO;
        BigDecimal s035Total = BigDecimal.ZERO;
        BigDecimal S068 = BigDecimal.ZERO;
        for (CalculateSalaryVo vo:lastEmpSalaryList) {
            s046Total=s046Total.add(vo.getS046());
            s035Total=s035Total.add(vo.getS035());
            S068=S068.add(vo.getS068());
        }
        calculateSalaryVo.setS046Total(s046Total);
        calculateSalaryVo.setS035Total(s035Total);
        calculateSalaryVo.setS068(S068);
        return calculateSalaryVo;
    }
    public Integer getLastEmpSalary(Long empId, Integer taxMonth) {
        return salaryInfoMapper.getLastEmpSalary (empId, taxMonth);
    }

    @Override
    public CalculateSalaryVo getFirstEmpSalary(CalculateSalaryVo calculateSalaryVo, Long taxListId) {
        List<CalculateSalaryVo> empSalaryList = salaryInfoMapper.getFirstEmpSalary(calculateSalaryVo, taxListId);
        if(CollectionUtils.isEmpty(empSalaryList)){
            return null;
        }
        CalculateSalaryVo firstEmpSalary = empSalaryList.get(0);
        BigDecimal S068=BigDecimal.ZERO;
        for (CalculateSalaryVo vo:empSalaryList) {
            S068=S068.add(vo.getS068());
        }
        firstEmpSalary.setS068(S068);
        return firstEmpSalary;
    }

    /**
     * 解析数据模式
     */
    private CalculateSalaryVo getItemType(CalculateSalaryVo calculateSalaryVo, PayItemDataVo payItemDataVo) {
        /**
         * 数字的才进行增加*/
        if (!Objects.equals (payItemDataVo.getItemNo (), SalaryItemEnum.S003.getItemNo ())
                && Objects.equals (payItemDataVo.getTxtFlag (), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex ())) {
            //税前增项
            if (Objects.equals (payItemDataVo.getIncreaseFlag (), SalaryPayEnum.increaseFlagEnum.ADD.getIndex ())
                    && Objects.equals (payItemDataVo.getTaxFlag (), SalaryPayEnum.taxFlagEnum.BEFORE_TAX.getIndex ())
                    && StringUtils.isNotBlank (payItemDataVo.getItemVal ())) {
                calculateSalaryVo.setAddTotal (calculateSalaryVo.getAddTotal ().add (new BigDecimal (payItemDataVo.getItemVal ())));
            }
            //税后增
            if (Objects.equals (payItemDataVo.getIncreaseFlag (), SalaryPayEnum.increaseFlagEnum.ADD.getIndex ())
                    && Objects.equals (payItemDataVo.getTaxFlag (), SalaryPayEnum.taxFlagEnum.AFTER_TAX.getIndex ())
                    && StringUtils.isNotBlank (payItemDataVo.getItemVal ())) {
                calculateSalaryVo.setS026 (calculateSalaryVo.getS026 ().add (new BigDecimal (payItemDataVo.getItemVal ())));
            }

            //税前减项
            if (Objects.equals (payItemDataVo.getIncreaseFlag (), SalaryPayEnum.increaseFlagEnum.REDUCE.getIndex ())
                    && Objects.equals (payItemDataVo.getTaxFlag (), SalaryPayEnum.taxFlagEnum.BEFORE_TAX.getIndex ())
                    && StringUtils.isNotBlank (payItemDataVo.getItemVal ())) {
                calculateSalaryVo.setReduceTotal (calculateSalaryVo.getReduceTotal ().add (new BigDecimal (payItemDataVo.getItemVal ())));
            }
            //税后减
            if (Objects.equals (payItemDataVo.getIncreaseFlag (), SalaryPayEnum.increaseFlagEnum.REDUCE.getIndex ())
                    && Objects.equals (payItemDataVo.getTaxFlag (), SalaryPayEnum.taxFlagEnum.AFTER_TAX.getIndex ())
                    && StringUtils.isNotBlank (payItemDataVo.getItemVal ())) {
                calculateSalaryVo.setS025 (calculateSalaryVo.getS025 ().add (new BigDecimal (payItemDataVo.getItemVal ())));
            }
        }
        return calculateSalaryVo;
    }

    @Override
    public int annualBonus(Long payId, String updater, TaxTabVo taxTabVo, SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        //拉取导入数据
        List<PayItemDataVo> payItemDataList = payItemDataMapper.getEmpAll (payId);
        List<PayEmpBaseVo> payEmpBaseVoList = payEmpBaseMapper.getByPayId(payId);
        if (CollectionUtils.isNotEmpty (payItemDataList)||CollectionUtils.isNotEmpty (payEmpBaseVoList)) {
            //薪资项目总和 薪资类别绑定的所有薪资项
            List<SalaryEmpTitleVo> salaryEmpTitleVos = salaryItemMapper.selectEmpItemByPayId (payId, null);
            Map<String, List<SalaryEmpTitleVo>> EmpTitleMap = salaryEmpTitleVos.stream ().collect (Collectors.groupingBy (SalaryEmpTitleVo::getItemNo));
            //薪资数据分组
            Map<Long, List<PayItemDataVo>> listMap = payItemDataList.stream ().collect (Collectors.groupingBy (PayItemDataVo::getEmpId));
            List<Long> empIds = payItemDataList.stream().map(PayItemDataVo::getEmpId).distinct().collect(Collectors.toList());
            Map<Long, List<PayEmpBaseVo>> payEmpBaseVoListMap = payEmpBaseVoList.stream ().collect (Collectors.groupingBy (PayEmpBaseVo::getEmpId));
            if(CollectionUtils.isEmpty(empIds)){
                empIds=payEmpBaseVoList.stream().map(PayEmpBaseVo::getEmpId).distinct().collect(Collectors.toList());
            }
            List<String> withholdingAgentNoList = payEmpBaseVoList.stream().map(PayEmpBaseVo::getWithholdingAgentNo).distinct().collect(Collectors.toList());
            List<WithholdingAgentVo> withholdingAgentVoList = withholdingAgentMapper.getByNoList(withholdingAgentNoList);
            Map<String,String> payPlaceMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getPayPlace));
            Map<String,String> orgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            Map<String, Integer> withholdingAgentTypeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getWithholdingAgentType));
            Map<String, String> withholdingOrgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            List<String> payPlaceList=new ArrayList<>(payPlaceMap.values());
            //残障金
            List<DisabilityGoldRateVo> disabilityGoldRateVoList = disabilityGoldRateWrapperService.selectByCityCodeList(payPlaceList);
//            Map<String, BigDecimal> disabilityGoldRateMap = disabilityGoldRateVoList.stream()
//                    .collect(Collectors.toMap(
//                            obj -> {
//                                String key = obj.getCityCode() + "_" + obj.getType();
//                                if (StringUtils.isNotEmpty(obj.getOrgCode())) {
//                                    key += "_" + obj.getOrgCode();
//                                }
//                                return key;
//                            },
//                            DisabilityGoldRateVo::getRate
//                    ));
            //第一批次工资数据
            List<SalaryInfoVo> salaryInfoVoList=salaryInfoMapper.selectByEmpIdsAndSalaryPayAndCategoryVo(empIds,salaryPayAndCategoryVo,withholdingAgentNoList);
            Map<String, String> salaryJsonInfoByEmpIdMap = salaryInfoVoList.stream().collect(Collectors.toMap(salaryInfoVo -> salaryInfoVo.getEmpId()+"-"+salaryInfoVo.getWithholdingAgentNo(), SalaryInfoVo::getSalaryJsonInfo));
            //参保地
            Map<Long, String> receivingMap=setReceivingMap(empIds,salaryPayAndCategoryVo);
            //银行卡
            List<EmpCardInfoVo> empCardInfoVoList=empCardInfoMapper.getByEmpIdList(empIds);
            Map<Long, Long> empCardIdMap = empCardInfoVoList.stream().collect(Collectors.toMap(EmpCardInfoVo::getEmpId, EmpCardInfoVo::getId));
            List<SupplierSalaryInfoVo> supplierSalaryInfoVoList=new ArrayList<>();
            // 数据循环
            for (int i = 0; i < empIds.size(); i++) {
                PayEmpBaseVo payEmpBaseVo = payEmpBaseVoListMap.get(empIds.get(i)).get(0);
                CalculateSalaryVo calculateSalaryVo = new CalculateSalaryVo ();
                calculateSalaryVo.setPayId(salaryPayAndCategoryVo.getId());
                calculateSalaryVo.setS025 (BigDecimal.ZERO);
                calculateSalaryVo.setS026 (BigDecimal.ZERO);
                calculateSalaryVo.setAddTotal (BigDecimal.ZERO);
                calculateSalaryVo.setReduceTotal (BigDecimal.ZERO);
                //插入对象
                SalaryInfo salaryInfo = new SalaryInfo ();
                salaryInfo.setEmpId (empIds.get(i));
                salaryInfo.setSalMonth (salaryPayAndCategoryVo.getSalaryMonth ());
                salaryInfo.setPayPlace(payEmpBaseVo.getPayPlace());
                salaryInfo.setQuoteNo(payEmpBaseVo.getQuoteNo());
                salaryInfo.setStatus (SalaryInfoStatus.FUTURE_PAY.getCode());
                salaryInfo.setSendCnt (0);//发送次数
                salaryInfo.setSendStatus (1);//默认发送状态
                salaryInfo.setPayId (payId);
                salaryInfo.setSalaryCategoryId (salaryPayAndCategoryVo.getSalaryCategoryId ());
                salaryInfo.setCreator (updater);
                salaryInfo.setUpdater (updater);
                salaryInfo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                salaryInfo.setEmpCardId(empCardIdMap.get(empIds.get(i)));
                calculateSalaryVo.setS055(payEmpBaseVo.getCrossBankHandlingFees());
                calculateSalaryVo.setS060(payEmpBaseVo.getUnionFees());
                calculateSalaryVo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setOrgCode(orgCodeMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setCityCode(payPlaceMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                //薪资内容
                //增项数据总和
                List<PayItemDataVo> subSalarys = listMap.get(empIds.get(i));
                Map<String, Object> mapSalary = new HashMap<> ();
                //同个工资项会有多条
                Map<String, List<PayItemDataVo>> itemNoMaps = new HashMap<>();
                if(CollectionUtils.isNotEmpty(subSalarys)){
                    itemNoMaps=subSalarys.stream ().collect (Collectors.groupingBy (PayItemDataVo::getItemNo));
                }
                //itemNos
                //导入数据组装 每个工资项进行循环
                for (Map.Entry<String, List<PayItemDataVo>> itemNos : itemNoMaps.entrySet ()) {
                    List<PayItemDataVo> itemNo = itemNos.getValue ();
                    //数组进行排序
                    Collections.sort (itemNo, new Comparator<PayItemDataVo> () {
                        @Override
                        //从大到小排序
                        public int compare(PayItemDataVo o1, PayItemDataVo o2) {
                            return o2.getId ().compareTo (o1.getId ());
                        }
                    });
                    PayItemDataVo payItemDataVo = itemNo.get (0);
                    //求取税前增，税前减
                    getItemType (calculateSalaryVo, payItemDataVo);
                    //存入数据
                    mapSalary.put (itemNos.getKey (), payItemDataVo.getItemVal ());
                }
                //税前增项
                calculateSalaryVo.setAddTotal (calculateSalaryVo.getAddTotal ().add (payEmpBaseVo.getBaseSalary()));
                calculateSalaryVo.setS001(payEmpBaseVo.getBaseSalary());
                //五险一金
                BigDecimal bigS003 = BigDecimal.ZERO;

                if (mapSalary.containsKey (SalaryItemEnum.S003.getItemNo ())) {
                    bigS003 = (BigDecimal) mapSalary.get (SalaryItemEnum.S003.getItemNo ());
                }
                //五险一金 截取小数
                bigS003 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S003.getItemNo (), EmpTitleMap, bigS003);
                calculateSalaryVo.setS003 (bigS003);
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().add(bigS003));
                calculateSalaryVo.setBillMonth (salaryPayAndCategoryVo.getBillMonth ());
                calculateSalaryVo.setTaxMonth(salaryPayAndCategoryVo.getTaxMonth());
                calculateSalaryVo.setTempletId(salaryPayAndCategoryVo.getTempletId());
                calculateSalaryVo.setSalaryCategoryId(salaryPayAndCategoryVo.getSalaryCategoryId());
                calculateSalaryVo.setEmpId (empIds.get(i));
                //残障金比率
                calculateSalaryVo.setS042(payEmpBaseVo.getDisFund());
//                BigDecimal disabilityGoldRate=BigDecimal.ZERO;
//                if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()))){
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }else {
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingOrgCodeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }
                DisabilityGoldRateVo disabilityGoldRateVo = getDisabilityGoldRateVo(disabilityGoldRateVoList,payPlaceMap,withholdingAgentTypeMap, withholdingOrgCodeMap,salaryInfo,salaryPayAndCategoryVo);

                //数据准备
                try {
                    getAnnualBonus (mapSalary, calculateSalaryVo, taxTabVo, EmpTitleMap,disabilityGoldRateVo,receivingMap,salaryJsonInfoByEmpIdMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException ("员工ID" + salaryInfo.getEmpId () + "年终奖计算数据准备时报错");
                }
                salaryInfo.setChangePayPlace(calculateSalaryVo.getChangePayPlace());
                salaryInfo.setNewPay(calculateSalaryVo.getNewPay());
                salaryInfo.setPayInsuranceFlag(calculateSalaryVo.getPayInsuranceFlag());

                setBankChangeFlag(salaryInfo,calculateSalaryVo);

                //实发工资+个税+残障金
                BigDecimal salaryTotal = calculateSalaryVo.getS007().add(calculateSalaryVo.getS006()).add(calculateSalaryVo.getS042());

                //计算工资服务费
                setServiceFee(salaryPayAndCategoryVo,payEmpBaseVo,salaryTotal,mapSalary,salaryInfo);
                //重置工资服务费
                //代发工资服务费
                /*List<QuotationItemVo> defaultQuotationItemVos=quotationItemMapper.getQuotationSolution (salaryPayAndCategoryVo.getContractNo ());
                List<QuotationItemVo> quotationItemVos =contractRelativeQuotationMapper.getQuotationItemByContractNo (salaryPayAndCategoryVo.getContractNo ());
                Map<String, QuotationItemVo> quoteNoMap =quotationItemVos.stream().collect(Collectors.toMap(QuotationItemVo::getQuoteNo, Function.identity(), (key1, key2) -> key2));
                BigDecimal serviceFee=BigDecimal.ZERO;
                if (quoteNoMap.size() > 0 && StringUtils.isNotBlank(payEmpBaseVo.getQuoteNo())){
                    QuotationItemVo quotationItemVo = quoteNoMap.get(payEmpBaseVo.getQuoteNo());
                    if(quotationItemVo!=null){
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }else {
                            serviceFee=quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                    }
                    mapSalary.put ("S002", serviceFee);
                }else {
                    if (CollectionUtils.isNotEmpty (defaultQuotationItemVos)){
                        QuotationItemVo quotationItemVo = defaultQuotationItemVos.get(0);
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_PAYROLL_CREDIT.getIndex ())){
                            serviceFee= quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }
                        mapSalary.put ("S002", serviceFee);
                    }
                }*/
                //数据小数位数保留
                Map<String, Object> mapSalaryResult = new HashMap<> ();
                for (Map.Entry<String, Object> subMapSalary : mapSalary.entrySet ()) {
                    if (EmpTitleMap.containsKey (subMapSalary.getKey ()) &&
                            Objects.equals (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getTxtFlag (), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex ())) {
                        BigDecimal result = PayRollUtils.keepBigDecimal (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getDecimalCnt (), subMapSalary.getValue ());
                        mapSalaryResult.put (subMapSalary.getKey (), result);
                    } else {
                        mapSalaryResult.put (subMapSalary.getKey (), subMapSalary.getValue ());
                    }
                }
                String salaryJsonInfo = JSONObject.fromObject (mapSalaryResult).toString ();
                salaryInfo.setSalaryJsonInfo (salaryJsonInfo);
                salaryInfoMapper.insertSelective (salaryInfo);
                supplierSalaryInfoVoList = setSupplierSalaryInfoVoList(supplierSalaryInfoVoList, salaryInfo, withholdingAgentTypeMap);
            }
            supplierSalaryInfoService.deleteByVoList(supplierSalaryInfoVoList);
        }
        return 0;
    }

    private Map<String, Object> getAnnualBonus(Map<String, Object> mapSalary, CalculateSalaryVo calculateSalaryVo, TaxTabVo taxTabVo, Map<String, List<SalaryEmpTitleVo>> empTitleMap,
                                               DisabilityGoldRateVo disabilityGoldRateVo, Map<Long, String> receivingMap,Map<String, String> salaryJsonInfoByEmpIdMap) {
        calculateSalaryVo.setS008 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()));
        calculateSalaryVo.setS009 (calculateSalaryVo.getS003 ());
        calculateSalaryVo.setS010 (BigDecimal.ZERO);
        calculateSalaryVo.setS011 (calculateSalaryVo.getS003 ());
        calculateSalaryVo.setS012 (BigDecimal.ZERO);
        calculateSalaryVo.setS013 (BigDecimal.ZERO);
        calculateSalaryVo.setS014 (BigDecimal.ZERO);
        calculateSalaryVo.setS039 (BigDecimal.ZERO);
        calculateSalaryVo.setS049 (BigDecimal.ZERO);
        calculateSalaryVo.setS041 (BigDecimal.ZERO);
        calculateSalaryVo.setS015 (BigDecimal.ZERO);
        calculateSalaryVo.setS016 (BigDecimal.ZERO);
        calculateSalaryVo.setS017 (BigDecimal.ZERO);
        calculateSalaryVo.setS018 (BigDecimal.ZERO);
        calculateSalaryVo.setS029(BigDecimal.ZERO);
        calculateSalaryVo.setS030(BigDecimal.ZERO);
        //S019     本次扣减的住房租金支出
        //calculateSalaryVo.setS019 (BigDecimal.ZERO);
        //S020     本次扣减的住房贷款利息
        //calculateSalaryVo.setS020 (BigDecimal.ZERO);
        //S021 本次扣减的赡养老人支出
        //calculateSalaryVo.setS021 (BigDecimal.ZERO);
        //S038 本次扣减的3岁以下婴幼儿照护
        //calculateSalaryVo.setS038 (BigDecimal.ZERO);
        //S040 本次扣减的其它扣除
        //calculateSalaryVo.setS040 (BigDecimal.ZERO);
        //S022	本次扣减子女教育支出
        //calculateSalaryVo.setS022 (BigDecimal.ZERO);
        //S023	本次扣减的继续教育支出
        //calculateSalaryVo.setS023 (BigDecimal.ZERO);
        //S024	本次使用专项附加扣除
        //calculateSalaryVo.setS024 (BigDecimal.ZERO);
        //第一批次工资数据
        String salaryJsonInfo = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo());
        Integer count = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth ());
        CalculateSalaryVo calculateLast = new CalculateSalaryVo();
        BigDecimal lastDisFund=BigDecimal.ZERO;
        //查询最近一次数据 ，累计扣除，累计工资，累计五险一金
        if(StringUtils.isNotBlank(salaryJsonInfo)){
            calculateLast=getFirstEmpSalary(calculateSalaryVo,taxTabVo.getId());
            setEmpCardId(calculateSalaryVo,calculateLast);
            lastDisFund=calculateLast.getS042();
        }else {
            //查上次正常发薪的数据，用以确认发薪地是否变更
            //年终奖计算除多批计算外，不会累计
            calculateLast = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth (),Long.parseLong(String.valueOf(TaxTableEnum.TAX_RATE_SCHEDULE_2019.getCode())),calculateSalaryVo.getWithholdingAgentNo());
            setEmpCardId(calculateSalaryVo,calculateLast);
        }
        // 1、如果扣缴义务人不相等，上次数据就是null
        // 2、有可能上次数据直接是null
        //如果之前没有数据，发薪是否新增：是；
        if(count>0){
            calculateSalaryVo.setNewPay(BooleanTypeEnum.NO.getCode());
        }else {
            calculateSalaryVo.setNewPay(BooleanTypeEnum.YES.getCode());
        }
        if (calculateLast != null&&StringUtils.isNotBlank(salaryJsonInfo)) {
            calculateSalaryVo.setS008 (calculateLast.getS008 ().add (calculateSalaryVo.getAddTotal ()).subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()));//S008   累计工资
            calculateSalaryVo.setS010 (calculateLast.getS010 ().add (calculateLast.getS006 ())); //S010   累计扣税（不包含当月）
        }
        //如果上次数据是null，发薪是否新增：是；发薪地变更：是
        if(calculateLast==null){
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.YES.getCode());
        }else {
            //如果上次数据不是null，否；发薪地变更：否
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.NO.getCode());
        }


        setPayInsuranceFlag(receivingMap,calculateSalaryVo);
        // S005;//应税工资(含个税基数)
        //五险一金已经放到睡前扣减里
        calculateSalaryVo.setS005 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()));
        if(calculateSalaryVo.getS005().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS005(BigDecimal.ZERO);
        }
        //数据准备结束
        BigDecimal annualBonus = calculateSalaryVo.getS008 ().subtract (calculateSalaryVo.getS009 ()).subtract(calculateSalaryVo.getS030());//累计应税工资
        calculateSalaryVo.setS056("");
        calculateSalaryVo.setS057(BigDecimal.ZERO);
        //年终奖个人所得税
        BigDecimal S006 = PayRollUtils.AnnualBonus (taxTabVo.getTaxSpreadsheetVos (), annualBonus,calculateSalaryVo,calculateSalaryVo.getS010());
        //小数精确
        S006 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S006.getItemNo (), empTitleMap, S006);
        calculateSalaryVo.setS006 (S006);
        //S037
        calculateSalaryVo.setS037 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()));
        // S007;//实发合计 （增项-请假（减项））-五险一金-个人所得税-税后减+税后增
        calculateSalaryVo.setS007 (calculateSalaryVo.getS037 ().subtract (calculateSalaryVo.getS003 ()).subtract (calculateSalaryVo.getS006 ()).subtract (calculateSalaryVo.getS025 ()).add (calculateSalaryVo.getS026 ()));
        if(calculateSalaryVo.getS007().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS007(BigDecimal.ZERO);
        }
        //计算残障金=应发工资*残障金比例
        if(calculateSalaryVo.getS042()==null){
            BigDecimal disFund = countDisFund(disabilityGoldRateVo, calculateSalaryVo.getS037(), calculateSalaryVo.getS007(),lastDisFund);
            calculateSalaryVo.setS042(disFund);
        }
        calculateSalaryVo.setS002 (BigDecimal.ZERO);
        calculateSalaryVo.setS027 (calculateSalaryVo.getAddTotal ());
        calculateSalaryVo.setS028 (calculateSalaryVo.getReduceTotal ());
        //把calculateSalaryVo数据存map中
        Method method = null;
        for (int i = 1; i <= calculateSalaryVo.getClass().getDeclaredFields().length; i++) {
            try {
                method = calculateSalaryVo.getClass ().getDeclaredMethod ("getS" + String.format ("%03d", i));
                mapSalary.put ("S" + String.format ("%03d", i), method.invoke (calculateSalaryVo));
            } catch (NoSuchMethodException ignored) {
            } catch (IllegalAccessException e) {
                e.printStackTrace ();
            } catch (InvocationTargetException e) {
                e.printStackTrace ();
            }

        }
        return mapSalary;
    }

    @Override
    public int calculateLaborWages(Long payId, String updater, TaxTabVo taxTabVo, SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        //新增工资信息
        //拉取导入数据
        List<PayItemDataVo> payItemDataList = payItemDataMapper.getEmpAll (payId);
        List<PayEmpBaseVo> payEmpBaseVoList = payEmpBaseMapper.getByPayId(payId);
        if (CollectionUtils.isNotEmpty (payItemDataList)||CollectionUtils.isNotEmpty (payEmpBaseVoList)) {
            //薪资项目总和 薪资类别绑定的所有薪资项
            List<SalaryEmpTitleVo> salaryEmpTitleVos = salaryItemMapper.selectEmpItemByPayId (payId, null);
            Map<String, List<SalaryEmpTitleVo>> EmpTitleMap = salaryEmpTitleVos.stream ().collect (Collectors.groupingBy (SalaryEmpTitleVo::getItemNo));
            //薪资数据分组
            Map<Long, List<PayItemDataVo>> listMap = payItemDataList.stream ().collect (Collectors.groupingBy (PayItemDataVo::getEmpId));
            List<Long> empIds = payItemDataList.stream().map(PayItemDataVo::getEmpId).distinct().collect(Collectors.toList());
            Map<Long, List<PayEmpBaseVo>> payEmpBaseVoListMap = payEmpBaseVoList.stream ().collect (Collectors.groupingBy (PayEmpBaseVo::getEmpId));
            if(CollectionUtils.isEmpty(empIds)){
                empIds=payEmpBaseVoList.stream().map(PayEmpBaseVo::getEmpId).distinct().collect(Collectors.toList());
            }
            List<String> withholdingAgentNoList = payEmpBaseVoList.stream().map(PayEmpBaseVo::getWithholdingAgentNo).distinct().collect(Collectors.toList());
            List<WithholdingAgentVo> withholdingAgentVoList = withholdingAgentMapper.getByNoList(withholdingAgentNoList);
            Map<String,String> payPlaceMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getPayPlace));
            Map<String,String> orgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            Map<String, Integer> withholdingAgentTypeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getWithholdingAgentType));
            Map<String, String> withholdingOrgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            List<String> payPlaceList=new ArrayList<>(payPlaceMap.values());
            //残障金
            List<DisabilityGoldRateVo> disabilityGoldRateVoList = disabilityGoldRateWrapperService.selectByCityCodeList(payPlaceList);
//            Map<String, BigDecimal> disabilityGoldRateMap = disabilityGoldRateVoList.stream()
//                    .collect(Collectors.toMap(
//                            obj -> {
//                                String key = obj.getCityCode() + "_" + obj.getType();
//                                if (StringUtils.isNotEmpty(obj.getOrgCode())) {
//                                    key += "_" + obj.getOrgCode();
//                                }
//                                return key;
//                            },
//                            DisabilityGoldRateVo::getRate
//                    ));
            //第一批次工资数据
            List<SalaryInfoVo> salaryInfoVoList=salaryInfoMapper.selectByEmpIdsAndSalaryPayAndCategoryVo(empIds,salaryPayAndCategoryVo,withholdingAgentNoList);
            Map<String, String> salaryJsonInfoByEmpIdMap = salaryInfoVoList.stream().collect(Collectors.toMap(salaryInfoVo -> salaryInfoVo.getEmpId()+"-"+salaryInfoVo.getWithholdingAgentNo(), SalaryInfoVo::getSalaryJsonInfo));
            //参保地
            Map<Long, String> receivingMap=setReceivingMap(empIds,salaryPayAndCategoryVo);
            //银行卡
            List<EmpCardInfoVo> empCardInfoVoList=empCardInfoMapper.getByEmpIdList(empIds);
            Map<Long, Long> empCardIdMap = empCardInfoVoList.stream().collect(Collectors.toMap(EmpCardInfoVo::getEmpId, EmpCardInfoVo::getId));
            //连续劳务税率
            TaxTabVo taxTabVo2 = taxTalService.selectTaxTabById(Long.parseLong(String.valueOf(TaxTableEnum.TAX_RATE_SCHEDULE_2019.getCode())));
            List<SupplierSalaryInfoVo> supplierSalaryInfoVoList=new ArrayList<>();
            // 数据循环
            for (int i = 0; i < empIds.size(); i++) {
                PayEmpBaseVo payEmpBaseVo =new PayEmpBaseVo();
                if(CollectionUtils.isNotEmpty(payEmpBaseVoListMap.get(empIds.get(i)))){
                    payEmpBaseVo = payEmpBaseVoListMap.get(empIds.get(i)).get(0);
                }else {
                    payEmpBaseVo.setBaseSalary(BigDecimal.ZERO);
                    payEmpBaseVo.setDisFund(BigDecimal.ZERO);
                }
                CalculateSalaryVo calculateSalaryVo = new CalculateSalaryVo ();
                calculateSalaryVo.setPayId(salaryPayAndCategoryVo.getId());
                calculateSalaryVo.setS025 (BigDecimal.ZERO);
                calculateSalaryVo.setS026 (BigDecimal.ZERO);
                calculateSalaryVo.setAddTotal (BigDecimal.ZERO);
                calculateSalaryVo.setReduceTotal (BigDecimal.ZERO);
                //插入对象
                SalaryInfo salaryInfo = new SalaryInfo ();
                salaryInfo.setEmpId (empIds.get(i));
                salaryInfo.setSalMonth (salaryPayAndCategoryVo.getSalaryMonth ());
                salaryInfo.setQuoteNo(payEmpBaseVo.getQuoteNo());
                salaryInfo.setPayPlace(payEmpBaseVo.getPayPlace());
                salaryInfo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setS055(payEmpBaseVo.getCrossBankHandlingFees());
                calculateSalaryVo.setS060(payEmpBaseVo.getUnionFees());
                calculateSalaryVo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setOrgCode(orgCodeMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setCityCode(payPlaceMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setS043(payEmpBaseVo.getEconomicAmount());//经济补偿金
                calculateSalaryVo.setLaborWagesType(payEmpBaseVo.getLaborWagesType());
                TaxTabVo trueTaxTabVo=taxTabVo;
                if(calculateSalaryVo.getLaborWagesType()!=null&& LaborWagesTypeEnum.CONTINUOUS.getCode()==calculateSalaryVo.getLaborWagesType()){
                    trueTaxTabVo=taxTabVo2;
                    trueTaxTabVo.setId(taxTabVo.getId());
                }
                salaryInfo.setEmpCardId(empCardIdMap.get(empIds.get(i)));
                salaryInfo.setStatus (SalaryInfoStatus.FUTURE_PAY.getCode());
                salaryInfo.setSendCnt (0);//发送次数
                salaryInfo.setSendStatus (1);//默认发送状态
                salaryInfo.setPayId (payId);
                salaryInfo.setSalaryCategoryId (salaryPayAndCategoryVo.getSalaryCategoryId ());
                salaryInfo.setCreator (updater);
                salaryInfo.setUpdater (updater);
                //薪资内容
                //增项数据总和
                List<PayItemDataVo> subSalarys = listMap.get(empIds.get(i));
                Map<String, Object> mapSalary = new HashMap<> ();
                //同个工资项会有多条
                Map<String, List<PayItemDataVo>> itemNoMaps = new HashMap<>();
                if(CollectionUtils.isNotEmpty(subSalarys)){
                    itemNoMaps=subSalarys.stream ().collect (Collectors.groupingBy (PayItemDataVo::getItemNo));
                }
                //itemNos
                //导入数据组装 每个工资项进行循环
                for (Map.Entry<String, List<PayItemDataVo>> itemNos : itemNoMaps.entrySet ()) {
                    List<PayItemDataVo> itemNo = itemNos.getValue ();
                    //数组进行排序
                    Collections.sort (itemNo, new Comparator<PayItemDataVo> () {
                        @Override
                        //从大到小排序
                        public int compare(PayItemDataVo o1, PayItemDataVo o2) {
                            return o2.getId ().compareTo (o1.getId ());
                        }
                    });
                    PayItemDataVo payItemDataVo = itemNo.get (0);
                    //求取税前增，税前减
                    getItemType (calculateSalaryVo, payItemDataVo);
                    //存入数据
                    mapSalary.put (itemNos.getKey (), payItemDataVo.getItemVal ());
                }
                //税前增项
                calculateSalaryVo.setAddTotal (calculateSalaryVo.getAddTotal ().add (payEmpBaseVo.getBaseSalary()));
                //税后增项
                calculateSalaryVo.setS026(calculateSalaryVo.getS026());
                calculateSalaryVo.setS001(payEmpBaseVo.getBaseSalary());


                calculateSalaryVo.setBillMonth (salaryPayAndCategoryVo.getBillMonth ());
                calculateSalaryVo.setTaxMonth(salaryPayAndCategoryVo.getTaxMonth());
                calculateSalaryVo.setEmpId (empIds.get(i));
                calculateSalaryVo.setSalaryCategoryId(salaryPayAndCategoryVo.getSalaryCategoryId());
                //残障金比率
                calculateSalaryVo.setS042(payEmpBaseVo.getDisFund());
//                BigDecimal disabilityGoldRate=BigDecimal.ZERO;
//                if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()))){
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }else {
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingOrgCodeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }
                DisabilityGoldRateVo disabilityGoldRateVo = getDisabilityGoldRateVo(disabilityGoldRateVoList,payPlaceMap,withholdingAgentTypeMap, withholdingOrgCodeMap,salaryInfo,salaryPayAndCategoryVo);
                calculateSalaryVo.setCreateTime(salaryPayAndCategoryVo.getCreateTime());
                calculateSalaryVo.setTempletId(salaryPayAndCategoryVo.getTempletId());
                //数据准备
                try {
                    getCalculateLaborWages (mapSalary, calculateSalaryVo, trueTaxTabVo, EmpTitleMap,disabilityGoldRateVo,receivingMap,salaryJsonInfoByEmpIdMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException ("员工ID" + salaryInfo.getEmpId () + "计算劳务工资数据准备时报错");
                }
                salaryInfo.setChangePayPlace(calculateSalaryVo.getChangePayPlace());
                salaryInfo.setNewPay(calculateSalaryVo.getNewPay());
                salaryInfo.setPayInsuranceFlag(calculateSalaryVo.getPayInsuranceFlag());

                setBankChangeFlag(salaryInfo,calculateSalaryVo);

                //实发工资+个税+残障金
                BigDecimal salaryTotal = calculateSalaryVo.getS007().add(calculateSalaryVo.getS006()).add(calculateSalaryVo.getS042());

                //计算工资服务费
                setServiceFee(salaryPayAndCategoryVo,payEmpBaseVo,salaryTotal,mapSalary,salaryInfo);
                //重置工资服务费
                //代发工资服务费
                /*List<QuotationItemVo> defaultQuotationItemVos=quotationItemMapper.getQuotationSolution (salaryPayAndCategoryVo.getContractNo ());
                List<QuotationItemVo> quotationItemVos =contractRelativeQuotationMapper.getQuotationItemByContractNo (salaryPayAndCategoryVo.getContractNo ());
                Map<String, QuotationItemVo> quoteNoMap =quotationItemVos.stream().collect(Collectors.toMap(QuotationItemVo::getQuoteNo, Function.identity(), (key1, key2) -> key2));
                BigDecimal serviceFee=BigDecimal.ZERO;
                if (quoteNoMap.size() > 0 && StringUtils.isNotBlank(payEmpBaseVo.getQuoteNo())){
                    QuotationItemVo quotationItemVo = quoteNoMap.get(payEmpBaseVo.getQuoteNo());
                    if(quotationItemVo!=null){
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }else {
                            serviceFee=quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                    }
                    mapSalary.put ("S002", serviceFee);
                }else {
                    if (CollectionUtils.isNotEmpty (defaultQuotationItemVos)){
                        QuotationItemVo quotationItemVo = defaultQuotationItemVos.get(0);
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_PAYROLL_CREDIT.getIndex ())){
                            serviceFee= quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }
                        mapSalary.put ("S002", serviceFee);
                    }
                }*/
                //数据小数位数保留
                Map<String, Object> mapSalaryResult = new HashMap<> ();
                for (Map.Entry<String, Object> subMapSalary : mapSalary.entrySet ()) {
                    if (EmpTitleMap.containsKey (subMapSalary.getKey ()) &&
                            Objects.equals (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getTxtFlag (), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex ())) {
                        BigDecimal result = PayRollUtils.keepBigDecimal (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getDecimalCnt (), subMapSalary.getValue ());
                        mapSalaryResult.put (subMapSalary.getKey (), result);
                    } else {
                        mapSalaryResult.put (subMapSalary.getKey (), subMapSalary.getValue ());
                    }
                }
                String salaryJsonInfo = JSONObject.fromObject (mapSalaryResult).toString ();
                salaryInfo.setSalaryJsonInfo (salaryJsonInfo);
                salaryInfoMapper.insertSelective (salaryInfo);
                supplierSalaryInfoVoList = setSupplierSalaryInfoVoList(supplierSalaryInfoVoList, salaryInfo, withholdingAgentTypeMap);
            }
            supplierSalaryInfoService.deleteByVoList(supplierSalaryInfoVoList);
        }
        return 0;
    }

    private Map<String, Object> getCalculateLaborWages(Map<String, Object> mapSalary, CalculateSalaryVo calculateSalaryVo,
                                                       TaxTabVo taxTabVo, Map<String, List<SalaryEmpTitleVo>> empTitleMap,
                                                       DisabilityGoldRateVo disabilityGoldRateVo, Map<Long, String> receivingMap,Map<String,String> salaryJsonInfoByEmpIdMap) {
        //第一批次工资数据
        String salaryJsonInfo = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId()+"-"+calculateSalaryVo.getWithholdingAgentNo());
        Integer count = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth ());
        CalculateSalaryVo calculateLast = new CalculateSalaryVo();
        BigDecimal lastDisFund=BigDecimal.ZERO;
        //查询最近一次数据 ，累计扣除，累计工资，累计五险一金
        if(StringUtils.isNotBlank(salaryJsonInfo)){
            calculateLast=getFirstEmpSalary(calculateSalaryVo,taxTabVo.getId());
            setEmpCardId(calculateSalaryVo,calculateLast);
            lastDisFund=calculateLast.getS042();
        }else {
            calculateLast = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth (),taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
            setEmpCardId(calculateSalaryVo,calculateLast);
            if(calculateLast!=null&&
                    (!calculateLast.getTaxMonth().equals(DateUtil.getYearMonthByCount(calculateSalaryVo.getTaxMonth(),-1))||DateUtil.getMonths(calculateSalaryVo.getTaxMonth()).equals(SALARY_MONTH))){
                calculateLast=null;
            }
        }
        // 1、如果扣缴义务人不相等，上次数据就是null
        // 2、有可能上次数据直接是null
        //如果之前没有数据，发薪是否新增：是；
        if(count>0){
            calculateSalaryVo.setNewPay(BooleanTypeEnum.NO.getCode());
        }else {
            calculateSalaryVo.setNewPay(BooleanTypeEnum.YES.getCode());
        }
        //如果上次数据是null，发薪地变更：是
        if(calculateLast==null){
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.YES.getCode());
        }else {
            //如果上次数据不是null，发薪地变更：否
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.NO.getCode());
            BigDecimal S030=BigDecimal.ZERO;
            if(calculateLast.getS030()!=null){
                S030=S030.add(calculateLast.getS030());
            }
            if(calculateLast.getS029()!=null){
                S030=S030.add(calculateLast.getS029());
            }else {
                calculateLast.setS029(BigDecimal.ZERO);
            }
            calculateLast.setS030(S030);//累计其他扣除
        }
        setPayInsuranceFlag(receivingMap,calculateSalaryVo);
        calculateSalaryVo.setS008 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()));
        calculateSalaryVo.setS010 (BigDecimal.ZERO);
        if(StringUtils.isNotBlank(salaryJsonInfo)&&calculateLast!=null){
            calculateSalaryVo.setS008(calculateSalaryVo.getS008().add(calculateLast.getS008()));
            calculateSalaryVo.setS010(calculateLast.getS010());
        }
        calculateSalaryVo.setS029(BigDecimal.ZERO);
        calculateSalaryVo.setS030(BigDecimal.ZERO);


        // S005;//应税工资(含个税基数)
        calculateSalaryVo.setS005 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()));
        if(calculateSalaryVo.getS005().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS005(BigDecimal.ZERO);
        }
        //数据准备结束
        //非连续劳务不扣除
        calculateSalaryVo.setS017 (BigDecimal.ZERO);
        //应发工资<=4000时，扣除800元后，按照劳务工资税率表计算所得税
        //应发工资>4000时，扣除收入的20%后，按照劳务工资税率表计算所得税
        if(calculateSalaryVo.getS008().compareTo(LABOR_WAGE_DEDUCTION_4000) > 0){
            calculateSalaryVo.setS009 (calculateSalaryVo.getS008().multiply(LABOR_WAGE_DEDUCTION_TAX).setScale(2, BigDecimal.ROUND_HALF_UP));
        }else {
            calculateSalaryVo.setS009 (LABOR_WAGE_DEDUCTION_800);
        }
        BigDecimal S010=BigDecimal.ZERO;
        //连续劳务减除起征点标准：5000，
        if(calculateSalaryVo.getLaborWagesType()!=null&&LaborWagesTypeEnum.CONTINUOUS.getCode()==calculateSalaryVo.getLaborWagesType()){
            BigDecimal startPoint = new BigDecimal(taxTabVo.getStartPoint());
            calculateSalaryVo.setS017 (startPoint);
            calculateSalaryVo.setS009 (calculateSalaryVo.getS009().add(calculateSalaryVo.getS017()));
            if(calculateLast!=null){
                S010=calculateLast.getS010().add(calculateLast.getS006());
                calculateSalaryVo.setS010(S010);
                if(StringUtils.isBlank(salaryJsonInfo)){
                    calculateSalaryVo.setS008(calculateSalaryVo.getS008().add(calculateLast.getS008()));
                    calculateSalaryVo.setS017 (calculateLast.getS017().add(startPoint));
                }else {
                    calculateSalaryVo.setS017 (calculateLast.getS017());
                }
                if(calculateSalaryVo.getS008().compareTo(LABOR_WAGE_DEDUCTION_4000) > 0){
                    calculateSalaryVo.setS009 (calculateSalaryVo.getS008().multiply(LABOR_WAGE_DEDUCTION_TAX).setScale(2, BigDecimal.ROUND_HALF_UP));
                }else {
                    calculateSalaryVo.setS009 (LABOR_WAGE_DEDUCTION_800);
                }
                calculateSalaryVo.setS009 (calculateSalaryVo.getS009().add(calculateSalaryVo.getS017()));
            }
        }


        //计算个人所得税（累计工资-累计扣除-累计其它扣除）*税率-累计扣税（不包涵当月的）;
        BigDecimal taxableSalary = calculateSalaryVo.getS008 ().subtract (calculateSalaryVo.getS009 ()).subtract(calculateSalaryVo.getS030());//累计应税工资
        calculateSalaryVo.setS056("");
        calculateSalaryVo.setS057(BigDecimal.ZERO);
        //个人所得税
        BigDecimal S006 = PayRollUtils.S006 (taxTabVo.getTaxSpreadsheetVos (), taxableSalary,S010,calculateSalaryVo,false);
        //小数精确
        S006 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S006.getItemNo (), empTitleMap, S006);
        calculateSalaryVo.setS006 (S006);
        //S037
        calculateSalaryVo.setS037 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()));
//        //计算残障金=应发工资*残障金比例
//        if(calculateSalaryVo.getS042()==null){
//            BigDecimal disFund = disabilityGoldRate!=null?(calculateSalaryVo.getS037().multiply(disabilityGoldRate)).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//            calculateSalaryVo.setS042(disFund);
//        }
        // S007;//实发合计 （增项-请假（减项））-个人所得税-税后减+税后增
        calculateSalaryVo.setS007 (calculateSalaryVo.getS037 ().subtract (calculateSalaryVo.getS006 ()).subtract (calculateSalaryVo.getS025 ()).add (calculateSalaryVo.getS026 ()));
        if(calculateSalaryVo.getS007().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS007(BigDecimal.ZERO);
        }
        //计算残障金
        if(calculateSalaryVo.getS042()==null){
            BigDecimal disFund = countDisFund(disabilityGoldRateVo, calculateSalaryVo.getS037(), calculateSalaryVo.getS007(),lastDisFund);
            calculateSalaryVo.setS042(disFund);
        }
        calculateSalaryVo.setS002 (BigDecimal.ZERO);
        calculateSalaryVo.setS027 (calculateSalaryVo.getAddTotal ());
        calculateSalaryVo.setS028 (calculateSalaryVo.getReduceTotal ());
        //把calculateSalaryVo数据存map中
        Method method = null;
        for (int i = 1; i <= calculateSalaryVo.getClass().getDeclaredFields().length; i++) {
            try {
                method = calculateSalaryVo.getClass ().getDeclaredMethod ("getS" + String.format ("%03d", i));
                mapSalary.put ("S" + String.format ("%03d", i), method.invoke (calculateSalaryVo));
            } catch (NoSuchMethodException ignored) {
            } catch (IllegalAccessException e) {
                e.printStackTrace ();
            } catch (InvocationTargetException e) {
                e.printStackTrace ();
            }

        }
        return mapSalary;
    }

    /**
     * 计算经济补偿金薪资
     */
    @Override
    public int calculateEconomicCompensation(Long payId, String updater,TaxTabVo taxTabVo,SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        //新增工资信息
        //拉取导入数据
        List<PayItemDataVo> payItemDataList = payItemDataMapper.getEmpAll (payId);
        List<PayEmpBaseVo> payEmpBaseVoList = payEmpBaseMapper.getByPayId(payId);
        if (CollectionUtils.isNotEmpty (payItemDataList)||CollectionUtils.isNotEmpty (payEmpBaseVoList)) {
            //薪资项目总和 薪资类别绑定的所有薪资项
            List<SalaryEmpTitleVo> salaryEmpTitleVos = salaryItemMapper.selectEmpItemByPayId (payId, null);
            Map<String, List<SalaryEmpTitleVo>> EmpTitleMap = salaryEmpTitleVos.stream ().collect (Collectors.groupingBy (SalaryEmpTitleVo::getItemNo));

            //薪资数据分组
            Map<Long, List<PayItemDataVo>> listMap = payItemDataList.stream ().collect (Collectors.groupingBy (PayItemDataVo::getEmpId));
            List<Long> empIds = payItemDataList.stream().map(PayItemDataVo::getEmpId).distinct().collect(Collectors.toList());
            Map<Long, List<PayEmpBaseVo>> payEmpBaseVoListMap = payEmpBaseVoList.stream ().collect (Collectors.groupingBy (PayEmpBaseVo::getEmpId));
            if(CollectionUtils.isEmpty(empIds)){
                empIds=payEmpBaseVoList.stream().map(PayEmpBaseVo::getEmpId).distinct().collect(Collectors.toList());
            }
            List<String> withholdingAgentNoList = payEmpBaseVoList.stream().map(PayEmpBaseVo::getWithholdingAgentNo).distinct().collect(Collectors.toList());
            List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList = excessCalculationAmountDetailMapper.getByWithholdingAgentNoList(withholdingAgentNoList);
            Map<String,BigDecimal> economicExcessAmountMap=new HashMap<>();
            Integer taxMonth = salaryPayAndCategoryVo.getTaxMonth();
            Integer lastTaxMonth = DateUtil.getYearMonthByCount(taxMonth, -1);
            for (ExcessCalculationAmountDetailVo detailVo:excessCalculationAmountDetailVoList) {
                if(detailVo.getExcessCalculationType()== ExcessCalculationTypeEnum.ECONOMIC_COMPENSATION.getCode()){
                    if(detailVo.getValidFrom()<=taxMonth&&taxMonth<=detailVo.getValidTo()){
                        economicExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+taxMonth,detailVo.getExcessCalculationAmount());
                    }
                    if(detailVo.getValidFrom()<=lastTaxMonth&&lastTaxMonth<=detailVo.getValidTo()){
                        economicExcessAmountMap.put(detailVo.getWithholdingAgentNo()+"-"+lastTaxMonth,detailVo.getExcessCalculationAmount());
                    }
                }
            }
            List<WithholdingAgentVo> withholdingAgentVoList = withholdingAgentMapper.getByNoList(withholdingAgentNoList);
            Map<String,String> payPlaceMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getPayPlace));
            Map<String,String> orgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            //Map<String,BigDecimal> economicExcessAmountMap=withholdingAgentVoList.stream().filter(withholdingAgentVo -> withholdingAgentVo.getEconomicExcessAmount()!=null).collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getEconomicExcessAmount));
            Map<String, Integer> withholdingAgentTypeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getWithholdingAgentType));
            Map<String, String> withholdingOrgCodeMap=withholdingAgentVoList.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo,WithholdingAgentVo::getOrgCode));
            List<String> payPlaceList=new ArrayList<>(payPlaceMap.values());
            //残障金
            List<DisabilityGoldRateVo> disabilityGoldRateVoList = disabilityGoldRateWrapperService.selectByCityCodeList(payPlaceList);
//            Map<String, BigDecimal> disabilityGoldRateMap = disabilityGoldRateVoList.stream()
//                    .collect(Collectors.toMap(
//                            obj -> {
//                                String key = obj.getCityCode() + "_" + obj.getType();
//                                if (StringUtils.isNotEmpty(obj.getOrgCode())) {
//                                    key += "_" + obj.getOrgCode();
//                                }
//                                return key;
//                            },
//                            DisabilityGoldRateVo::getRate
//                    ));

            //第一批次工资数据
            List<SalaryInfoVo> salaryInfoVoList=salaryInfoMapper.selectByEmpIdsAndSalaryPayAndCategoryVo(empIds,salaryPayAndCategoryVo,withholdingAgentNoList);
            Map<Long, String> salaryJsonInfoByEmpIdMap = salaryInfoVoList.stream().collect(Collectors.toMap(SalaryInfoVo::getEmpId, SalaryInfoVo::getSalaryJsonInfo));
            //参保地
            Map<Long, String> receivingMap=setReceivingMap(empIds,salaryPayAndCategoryVo);
            //银行卡
            List<EmpCardInfoVo> empCardInfoVoList=empCardInfoMapper.getByEmpIdList(empIds);
            Map<Long, Long> empCardIdMap = empCardInfoVoList.stream().collect(Collectors.toMap(EmpCardInfoVo::getEmpId, EmpCardInfoVo::getId));
            List<SupplierSalaryInfoVo> supplierSalaryInfoVoList=new ArrayList<>();
            // 数据循环
            for (int i = 0; i < empIds.size(); i++) {
                PayEmpBaseVo payEmpBaseVo =new PayEmpBaseVo();
                if(CollectionUtils.isNotEmpty(payEmpBaseVoListMap.get(empIds.get(i)))){
                    payEmpBaseVo = payEmpBaseVoListMap.get(empIds.get(i)).get(0);
                }else {
                    payEmpBaseVo.setBaseSalary(BigDecimal.ZERO);
                    payEmpBaseVo.setIndEndowment(BigDecimal.ZERO);
                    payEmpBaseVo.setIndAccuFund(BigDecimal.ZERO);
                    payEmpBaseVo.setIndMedical(BigDecimal.ZERO);
                    payEmpBaseVo.setIndLongTermInsurance(BigDecimal.ZERO);
                    payEmpBaseVo.setBigMedicaid(BigDecimal.ZERO);
                    payEmpBaseVo.setSupplementaryMedicalInsurance(BigDecimal.ZERO);
                    payEmpBaseVo.setOutpatientMedicalCare(BigDecimal.ZERO);
                    payEmpBaseVo.setIndOther(BigDecimal.ZERO);
                    payEmpBaseVo.setIndSeriousIllness(BigDecimal.ZERO);
                    payEmpBaseVo.setIndUnemployeement(BigDecimal.ZERO);
                    payEmpBaseVo.setDisFund(BigDecimal.ZERO);
                    payEmpBaseVo.setEconomicAmount(BigDecimal.ZERO);
                }
                CalculateSalaryVo calculateSalaryVo = new CalculateSalaryVo ();
                calculateSalaryVo.setPayId(salaryPayAndCategoryVo.getId());
                calculateSalaryVo.setS025 (BigDecimal.ZERO);
                calculateSalaryVo.setS026 (BigDecimal.ZERO);
                calculateSalaryVo.setAddTotal (BigDecimal.ZERO);
                calculateSalaryVo.setReduceTotal (BigDecimal.ZERO);
                //插入对象
                SalaryInfo salaryInfo = new SalaryInfo ();
                salaryInfo.setEmpId (empIds.get(i));
                salaryInfo.setSalMonth (salaryPayAndCategoryVo.getSalaryMonth ());
                salaryInfo.setQuoteNo(payEmpBaseVo.getQuoteNo());
                salaryInfo.setPayPlace(payEmpBaseVo.getPayPlace());
                salaryInfo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setS055(payEmpBaseVo.getCrossBankHandlingFees());
                calculateSalaryVo.setS060(payEmpBaseVo.getUnionFees());
                calculateSalaryVo.setWithholdingAgentNo(payEmpBaseVo.getWithholdingAgentNo());
                calculateSalaryVo.setOrgCode(orgCodeMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setCityCode(payPlaceMap.get(payEmpBaseVo.getWithholdingAgentNo()));
                calculateSalaryVo.setS043(payEmpBaseVo.getEconomicAmount());//经济补偿金
                BigDecimal economicAmountTop=economicExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+taxMonth);
                BigDecimal S044=BigDecimal.ZERO;
                if(economicAmountTop!=null){
                    calculateSalaryVo.setEconomicAmountTop(economicAmountTop);
                    S044 = payEmpBaseVo.getEconomicAmount().subtract(economicAmountTop);
                    if(S044.compareTo(BigDecimal.ZERO)<0){
                        S044=BigDecimal.ZERO;
                    }
                }
                calculateSalaryVo.setS044(S044);//经济补偿金超额计税部分
                BigDecimal lastEconomicAmountTop=economicExcessAmountMap.get(calculateSalaryVo.getWithholdingAgentNo()+"-"+lastTaxMonth);
                calculateSalaryVo.setLastMonthEconomicAmountTop(lastEconomicAmountTop);
                salaryInfo.setEmpCardId(empCardIdMap.get(empIds.get(i)));
                salaryInfo.setStatus (SalaryInfoStatus.FUTURE_PAY.getCode());
                salaryInfo.setSendCnt (0);//发送次数
                salaryInfo.setSendStatus (1);//默认发送状态
                salaryInfo.setPayId (payId);
                salaryInfo.setSalaryCategoryId (salaryPayAndCategoryVo.getSalaryCategoryId ());
                salaryInfo.setCreator (updater);
                salaryInfo.setUpdater (updater);
                //薪资内容
                //增项数据总和
                List<PayItemDataVo> subSalarys = listMap.get(empIds.get(i));
                Map<String, Object> mapSalary = new HashMap<> ();
                //同个工资项会有多条
                Map<String, List<PayItemDataVo>> itemNoMaps = new HashMap<>();
                if(CollectionUtils.isNotEmpty(subSalarys)){
                    itemNoMaps=subSalarys.stream ().collect (Collectors.groupingBy (PayItemDataVo::getItemNo));
                }
                //itemNos
                //导入数据组装 每个工资项进行循环
                for (Map.Entry<String, List<PayItemDataVo>> itemNos : itemNoMaps.entrySet ()) {
                    List<PayItemDataVo> itemNo = itemNos.getValue ();
                    //数组进行排序
                    Collections.sort (itemNo, new Comparator<PayItemDataVo> () {
                        @Override
                        //从大到小排序
                        public int compare(PayItemDataVo o1, PayItemDataVo o2) {
                            return o2.getId ().compareTo (o1.getId ());
                        }
                    });
                    PayItemDataVo payItemDataVo = itemNo.get (0);
                    //求取税前增，税前减
                    getItemType (calculateSalaryVo, payItemDataVo);
                    //存入数据
                    mapSalary.put (itemNos.getKey (), payItemDataVo.getItemVal ());
                }
                //税前增项
                calculateSalaryVo.setAddTotal (calculateSalaryVo.getAddTotal ().add (payEmpBaseVo.getBaseSalary()));
                //税后增项
                calculateSalaryVo.setS026(calculateSalaryVo.getS026().add(payEmpBaseVo.getEconomicAmount()));
                calculateSalaryVo.setS001(payEmpBaseVo.getBaseSalary());
                //五险一金
                BigDecimal bigS003 = BigDecimal.ZERO;
                calculateSalaryVo.setS031(BigDecimal.ZERO);//S031 养老个人
                calculateSalaryVo.setS032(BigDecimal.ZERO);//S032 医疗个人
                calculateSalaryVo.setS061(BigDecimal.ZERO);//S061 个人长护险
                calculateSalaryVo.setS062(BigDecimal.ZERO);//S062 大额医疗补助
                calculateSalaryVo.setS063(BigDecimal.ZERO);//S063 补充医疗保险
                calculateSalaryVo.setS064(BigDecimal.ZERO);//S064 门诊医疗(社保)
                calculateSalaryVo.setS065(BigDecimal.ZERO);//S065 大病保险(税后扣除)
                calculateSalaryVo.setS066(BigDecimal.ZERO);//S066 补充医疗保险(税后扣除)
                calculateSalaryVo.setS067(BigDecimal.ZERO);//S067 个人长护险(税后扣除)
                calculateSalaryVo.setS033(BigDecimal.ZERO);//S033 失业个人
                calculateSalaryVo.setS034(BigDecimal.ZERO);//S034 大病个人
                calculateSalaryVo.setS035(BigDecimal.ZERO);//S035 公积金个人
                calculateSalaryVo.setS036(BigDecimal.ZERO);//S036 其他个人
                calculateSalaryVo.setS046(BigDecimal.ZERO);//公积金超额计税部分
                calculateSalaryVo.setEmpId (empIds.get(i));
                //第一批次工资数据
                String salaryJsonInfoFirst = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId());
                if(StringUtils.isBlank(salaryJsonInfoFirst)){
                    calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal());
                }
                calculateSalaryVo.setS003 (bigS003);
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal());
                calculateSalaryVo.setBillMonth (salaryPayAndCategoryVo.getBillMonth ());
                calculateSalaryVo.setTaxMonth(salaryPayAndCategoryVo.getTaxMonth());
                calculateSalaryVo.setSalaryCategoryId(salaryPayAndCategoryVo.getSalaryCategoryId());
                //残障金比率
                calculateSalaryVo.setS042(payEmpBaseVo.getDisFund());
//                BigDecimal disabilityGoldRate=BigDecimal.ZERO;
//                if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()))){
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }else {
//                    disabilityGoldRate =disabilityGoldRateMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo())+"_"+withholdingOrgCodeMap.get(salaryInfo.getWithholdingAgentNo()));
//                }
                DisabilityGoldRateVo disabilityGoldRateVo = getDisabilityGoldRateVo(disabilityGoldRateVoList,payPlaceMap,withholdingAgentTypeMap,withholdingOrgCodeMap,salaryInfo,salaryPayAndCategoryVo);
                BillTempletVo billTempletVo = billTempletMapper.getTempletVoByTempletId(salaryPayAndCategoryVo.getTempletId());
                if(calculateSalaryVo.getS042()==null){
                    if(billTempletVo.getIndTaxFlag()!=null&&billTempletVo.getIndTaxFlag()==BooleanTypeEnum.NO.getCode()){
                        calculateSalaryVo.setS042(BigDecimal.ZERO);
                    }
                }
                //数据准备
                try {
                    calculateSalaryVo.setTempletId(salaryPayAndCategoryVo.getTempletId());
                    getCalculateEconomicCompensation (mapSalary, calculateSalaryVo, taxTabVo, EmpTitleMap,salaryJsonInfoByEmpIdMap,disabilityGoldRateVo,receivingMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException ("员工ID" + salaryInfo.getEmpId () + "计算经济补偿金数据准备时报错");
                }
                salaryInfo.setChangePayPlace(calculateSalaryVo.getChangePayPlace());
                salaryInfo.setNewPay(calculateSalaryVo.getNewPay());
                salaryInfo.setPayInsuranceFlag(calculateSalaryVo.getPayInsuranceFlag());

                setBankChangeFlag(salaryInfo,calculateSalaryVo);

                //实发工资+个税+残障金
                BigDecimal salaryTotal = calculateSalaryVo.getS007().add(calculateSalaryVo.getS006()).add(calculateSalaryVo.getS042());
                salaryTotal = salaryTotal.add(calculateSalaryVo.getS052()).add(calculateSalaryVo.getS051());

                //计算工资服务费
                setServiceFee(salaryPayAndCategoryVo,payEmpBaseVo,salaryTotal,mapSalary,salaryInfo);
                //重置工资服务费
                //代发工资服务费
                /*List<QuotationItemVo> defaultQuotationItemVos=quotationItemMapper.getQuotationSolution (salaryPayAndCategoryVo.getContractNo ());
                List<QuotationItemVo> quotationItemVos =contractRelativeQuotationMapper.getQuotationItemByContractNo (salaryPayAndCategoryVo.getContractNo ());
                Map<String, QuotationItemVo> quoteNoMap =quotationItemVos.stream().collect(Collectors.toMap(QuotationItemVo::getQuoteNo, Function.identity(), (key1, key2) -> key2));
                BigDecimal serviceFee=BigDecimal.ZERO;
                if (quoteNoMap.size() > 0 && StringUtils.isNotBlank(payEmpBaseVo.getQuoteNo())){
                    QuotationItemVo quotationItemVo = quoteNoMap.get(payEmpBaseVo.getQuoteNo());
                    if(quotationItemVo!=null){
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }else {
                            serviceFee=quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                    }
                    mapSalary.put ("S002", serviceFee);
                }else {
                    if (CollectionUtils.isNotEmpty (defaultQuotationItemVos)){
                        QuotationItemVo quotationItemVo = defaultQuotationItemVos.get(0);
                        if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_PAYROLL_CREDIT.getIndex ())){
                            serviceFee= quotationItemVo.getPrice();
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                serviceFee=serviceFee.multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }else if (Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_2.getIndex ())
                                ||Objects.equals (salaryPayAndCategoryVo.getContractType (), EmployeeReportEnum.ContractTypeEnum.CONTRACT_TYPE_OUTSOURCING_1.getIndex ())){
                            //总金额*服务费比例
                            serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).setScale (2, BigDecimal.ROUND_HALF_UP);
                            if(quotationItemVo.getTaxFlag()== QuotationTaxFlag.TAX_EXCLUSIVE.getCode()){
                                //总金额*服务费比例*(1+税率)
                                serviceFee=salaryTotal.multiply(quotationItemVo.getFeeRatio()).multiply(BigDecimal.ONE.add(quotationItemVo.getTaxRatio())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            }
                            salaryInfo.setQuoteNo(quotationItemVo.getQuoteNo());
                        }
                        mapSalary.put ("S002", serviceFee);
                    }
                }*/
                //数据小数位数保留
                Map<String, Object> mapSalaryResult = new HashMap<> ();
                for (Map.Entry<String, Object> subMapSalary : mapSalary.entrySet ()) {
                    if (EmpTitleMap.containsKey (subMapSalary.getKey ()) &&
                            Objects.equals (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getTxtFlag (), SalaryPayEnum.txtFlagEnum.NUMBER.getIndex ())) {
                        BigDecimal result = PayRollUtils.keepBigDecimal (EmpTitleMap.get (subMapSalary.getKey ()).get (0).getDecimalCnt (), subMapSalary.getValue ());
                        mapSalaryResult.put (subMapSalary.getKey (), result);
                    } else {
                        mapSalaryResult.put (subMapSalary.getKey (), subMapSalary.getValue ());
                    }
                }
                String salaryJsonInfo = JSONObject.fromObject (mapSalaryResult).toString ();
                salaryInfo.setSalaryJsonInfo (salaryJsonInfo);
                salaryInfoMapper.insertSelective (salaryInfo);
                supplierSalaryInfoVoList = setSupplierSalaryInfoVoList(supplierSalaryInfoVoList, salaryInfo, withholdingAgentTypeMap);
            }
            supplierSalaryInfoService.deleteByVoList(supplierSalaryInfoVoList);
        }
        return 0;
    }

    /**
     *
     * @param mapSalary         存值json
     * @param calculateSalaryVo 计算系统值
     * @param taxTabVo          税率表
     * @return
     * @Param empTaxDeductionVos 待抵扣专项
     */
    private Map<String, Object> getCalculateEconomicCompensation(Map<String, Object> mapSalary, CalculateSalaryVo calculateSalaryVo, TaxTabVo taxTabVo, Map<String, List<SalaryEmpTitleVo>> EmpTitleMap,
                                                                 Map<Long,String> salaryJsonInfoByEmpIdMap,DisabilityGoldRateVo disabilityGoldRateVo,Map<Long, String> receivingMap) {
        BigDecimal childEdu = BigDecimal.ZERO, rent = BigDecimal.ZERO, interest = BigDecimal.ZERO, supportElder = BigDecimal.ZERO, treatment = BigDecimal.ZERO, contiEdu = BigDecimal.ZERO,
                babyCare = BigDecimal.ZERO,personalPension=BigDecimal.ZERO,otherDeduction = BigDecimal.ZERO,specialProject =BigDecimal.ZERO;
        //第一批次工资数据
        String salaryJsonInfo = salaryJsonInfoByEmpIdMap.get(calculateSalaryVo.getEmpId());
        Integer count = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth ());
        CalculateSalaryVo calculateLast = new CalculateSalaryVo();
        CalculateSalaryVo calculateLastQueryVo = getLastEmpSalary (calculateSalaryVo.getEmpId (), calculateSalaryVo.getTaxMonth (),taxTabVo.getId(),calculateSalaryVo.getWithholdingAgentNo());
        //查询最近一次数据 ，累计扣除，累计工资，累计五险一金
        if(StringUtils.isNotBlank(salaryJsonInfo)){
            calculateLast=getFirstEmpSalary(calculateSalaryVo,taxTabVo.getId());
            setEmpCardId(calculateSalaryVo,calculateLast);
        }else {
            if(calculateLastQueryVo!=null){
                BeanUtils.copyProperties(calculateLastQueryVo,calculateLast);
                setEmpCardId(calculateSalaryVo,calculateLast);
                if (!calculateLast.getTaxMonth().equals(DateUtil.getYearMonthByCount(calculateSalaryVo.getTaxMonth(),-1))) {
                    calculateLast = null;
                }
            }else {
                calculateLast=null;
            }
        }
        // 1、如果扣缴义务人不相等，上次数据就是null
        // 2、有可能上次数据直接是null
        //如果之前没有数据，发薪是否新增：是；
        if(count>0){
            calculateSalaryVo.setNewPay(BooleanTypeEnum.NO.getCode());
        }else {
            calculateSalaryVo.setNewPay(BooleanTypeEnum.YES.getCode());
        }

        //如果上次数据是null，发薪是否新增：是；发薪地变更：是
        if(calculateLast==null){
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.YES.getCode());
        }else {
            //如果上次数据不是null，否；发薪地变更：否
            calculateSalaryVo.setChangePayPlace(BooleanTypeEnum.NO.getCode());
            BigDecimal S030=BigDecimal.ZERO;
            if(calculateLast.getS030()!=null){
                S030=S030.add(calculateLast.getS030());
            }
            if(calculateLast.getS029()!=null){
                S030=S030.add(calculateLast.getS029());
            }else {
                calculateLast.setS029(BigDecimal.ZERO);
            }
            calculateLast.setS030(S030);//累计其他扣除
        }
        setPayInsuranceFlag(receivingMap,calculateSalaryVo);
        BigDecimal lastDisFund=BigDecimal.ZERO;
        /**
         * 当前账单月为1月，但是查询上次出账单也是1月份就要进行累加*/
        if (((DateUtil.getMonths (calculateSalaryVo.getTaxMonth ()) != SALARY_MONTH) ||
                (DateUtil.getMonths (calculateSalaryVo.getTaxMonth ()) == SALARY_MONTH && calculateLast != null && DateUtil.getMonths (calculateLast.getTaxMonth ()) == SALARY_MONTH))
                && calculateLast != null) {
            calculateSalaryVo.setS008 (calculateLast.getS008 ().add (calculateSalaryVo.getAddTotal ()).subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()));//S008   累计工资
            calculateSalaryVo.setS010 (calculateLast.getS010 ().add (calculateLast.getS006 ())); //S010   累计扣税（不包含当月）
            calculateSalaryVo.setS053 (calculateLast.getS053 ().add (calculateLast.getS051 ())); //S053   累计经济补偿金扣税（不包含当月）
            calculateSalaryVo.setS011 (calculateLast.getS011 ().add (calculateSalaryVo.getS003 ()));//S011  累计五险一金
            calculateSalaryVo.setS018 (specialProject);//S018   累计专项附加扣除
            if(StringUtils.isBlank(salaryJsonInfo)){
                calculateSalaryVo.setS017 (calculateLast.getS017 ().add (new BigDecimal (taxTabVo.getStartPoint ())));//S017      累计基本减除费用
                setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS045(calculateLast.getS045().add(calculateSalaryVo.getS044()));
                setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS047(calculateLast.getS047().add(calculateSalaryVo.getS046()));
            }else {
                //二批计算的
                calculateSalaryVo.setS017 (calculateLast.getS017 ());//S017      累计基本减除费用
                if(calculateSalaryVo.getEconomicAmountTop()!=null){
                    BigDecimal S044 = calculateLast.getS043().add(calculateSalaryVo.getS043()).subtract(calculateSalaryVo.getEconomicAmountTop());
                    if(S044.compareTo(BigDecimal.ZERO)<0){
                        S044=BigDecimal.ZERO;
                    }
                    calculateSalaryVo.setS044(S044.subtract(calculateLast.getS044()));
                }else {
                    calculateSalaryVo.setS044(BigDecimal.ZERO);
                }
                setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setS045(calculateLast.getS045().add(calculateSalaryVo.getS044()));
                if(calculateSalaryVo.getAccumulationFundTop()!=null){
                    BigDecimal S046 = calculateLast.getS035().add(calculateSalaryVo.getS035()).subtract(calculateSalaryVo.getAccumulationFundTop());
                    if(S046.compareTo(BigDecimal.ZERO)<0){
                        S046=BigDecimal.ZERO;
                    }
                    calculateSalaryVo.setS046(S046.subtract(calculateLast.getS046()));
                }else {
                    calculateSalaryVo.setS046(BigDecimal.ZERO);
                }
                setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
                calculateSalaryVo.setReduceTotal(calculateSalaryVo.getReduceTotal().subtract(calculateSalaryVo.getS046()));
                calculateSalaryVo.setS047(calculateLast.getS047().add(calculateSalaryVo.getS046()));
                lastDisFund=calculateLast.getS042();
            }
            //累计五险一金+累计专项附加扣除+累计基本扣减-累计公积金超额部分
            calculateSalaryVo.setS009 (calculateSalaryVo.getS011().add (calculateSalaryVo.getS018()).add(calculateSalaryVo.getS017()).subtract(calculateSalaryVo.getS047()));//S009    累计扣除

            //S029   其它扣除
            calculateSalaryVo.setS029(calculateLast.getS029());
            //S030   累计其它扣除
            calculateSalaryVo.setS030(calculateLast.getS030());
        } else {
            setUpdatedLastMonthAccumulationFundTopS046(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
            calculateSalaryVo.setS008 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()));
            calculateSalaryVo.setS047(calculateSalaryVo.getS046());
            calculateSalaryVo.setS009 (specialProject.add (calculateSalaryVo.getS003 ()).add (new BigDecimal (taxTabVo.getStartPoint ())).subtract(calculateSalaryVo.getS047()));
            calculateSalaryVo.setS010 (BigDecimal.ZERO);
            calculateSalaryVo.setS053 (BigDecimal.ZERO);
            calculateSalaryVo.setS011 (calculateSalaryVo.getS003 ());
            calculateSalaryVo.setS017 (new BigDecimal (taxTabVo.getStartPoint ()));
            calculateSalaryVo.setS018 (specialProject);
            calculateSalaryVo.setS029(BigDecimal.ZERO);
            calculateSalaryVo.setS030(BigDecimal.ZERO);
            setUpdatedLastMonthEconomicAmountTopS044(calculateSalaryVo,taxTabVo,calculateLastQueryVo);
            calculateSalaryVo.setS045(calculateSalaryVo.getS044());
        }
        calculateSalaryVo.setS012 (rent); //S012  累计住房租金支出扣除
        calculateSalaryVo.setS013 (interest); //S013   累计住房贷款利息支出扣除
        calculateSalaryVo.setS014 (supportElder); //S014    累计赡养老人支出扣除
        calculateSalaryVo.setS039 (babyCare); //S039    累计3岁以下婴幼儿照护
        calculateSalaryVo.setS049 (personalPension); //S049    累计个人养老金
        calculateSalaryVo.setS041 (otherDeduction); //S041    累计其他扣除
        calculateSalaryVo.setS015 (childEdu);   //S015 累计子女教育支出扣除
        calculateSalaryVo.setS016 (contiEdu);//S016  累计继续教育支出扣除
        BigDecimal S012 = BigDecimal.ZERO,S013= BigDecimal.ZERO,S014= BigDecimal.ZERO,S015= BigDecimal.ZERO,S016= BigDecimal.ZERO,
                S018= BigDecimal.ZERO,S039= BigDecimal.ZERO,S041= BigDecimal.ZERO,S049=BigDecimal.ZERO;
        if(DateUtil.getMonths (calculateSalaryVo.getTaxMonth ()) != SALARY_MONTH&&calculateLast!=null){
            S012=calculateLast.getS012();
            S013=calculateLast.getS013();
            S014=calculateLast.getS014();
            S015=calculateLast.getS015();
            S016=calculateLast.getS016();
            S018=calculateLast.getS018();
            S039=calculateLast.getS039();
            S041=calculateLast.getS041();
            S049=calculateLast.getS049();
        }
        if(rent.compareTo(BigDecimal.ZERO)>0){
            //S019     本次扣减的住房租金支出
            calculateSalaryVo.setS019 (rent.subtract(S012));
        }
        if(interest.compareTo(BigDecimal.ZERO)>0){
            //S020     本次扣减的住房贷款利息
            calculateSalaryVo.setS020 (interest.subtract(S013));
        }
        if(supportElder.compareTo(BigDecimal.ZERO)>0){
            //S021 本次扣减的赡养老人支出
            calculateSalaryVo.setS021 (supportElder.subtract(S014));
        }
        if(babyCare.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的3岁以下婴幼儿照护
            calculateSalaryVo.setS038 (babyCare.subtract(S039));
        }
        if(personalPension.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的个人养老金
            calculateSalaryVo.setS048 (personalPension.subtract(S049));
        }
        if(otherDeduction.compareTo(BigDecimal.ZERO)>0){
            //本次扣减的其它扣除
            calculateSalaryVo.setS040 (otherDeduction.subtract(S041));
        }
        if(childEdu.compareTo(BigDecimal.ZERO)>0){
            //S022	本次扣减子女教育支出
            calculateSalaryVo.setS022 (childEdu.subtract(S015));
        }
        if(contiEdu.compareTo(BigDecimal.ZERO)>0){
            //S023	本次扣减的继续教育支出
            calculateSalaryVo.setS023 (contiEdu.subtract(S016));
        }
        if(specialProject.compareTo(BigDecimal.ZERO)>0){
            //S024	本次使用专项附加扣除
            calculateSalaryVo.setS024 (specialProject.subtract(S018));
        }


        // S050 经济补偿金应税金额
        //五险一金加入到税前扣减
        calculateSalaryVo.setS050 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS044()));
        if(calculateSalaryVo.getS050().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS050(BigDecimal.ZERO);
        }
        //数据准备结束
        //计算个人所得税（累计工资-累计扣除-累计其它扣除+累计经济补偿金超额计税部分+公积金超额计税部分）*税率-累计扣税（不包涵当月的）;
        BigDecimal taxableSalary = calculateSalaryVo.getS008 ().subtract (calculateSalaryVo.getS009 ()).subtract(calculateSalaryVo.getS030())
                .add(calculateSalaryVo.getS045()).add(calculateSalaryVo.getS047());//累计应税工资
        calculateSalaryVo.setS058("");
        calculateSalaryVo.setS059(BigDecimal.ZERO);
        //个人所得税
        BigDecimal S051 = PayRollUtils.S006 (taxTabVo.getTaxSpreadsheetVos (), taxableSalary, calculateSalaryVo.getS053 (),calculateSalaryVo,true);
        //小数精确
        S051 = PayRollUtils.cutBigDecimal (SalaryItemEnum.S051.getItemNo (), EmpTitleMap, S051);
        calculateSalaryVo.setS051 (S051);
        calculateSalaryVo.setS006 (BigDecimal.ZERO);
        //S037
        calculateSalaryVo.setS037 (calculateSalaryVo.getAddTotal ().subtract (calculateSalaryVo.getReduceTotal ()).add(calculateSalaryVo.getS003()).subtract(calculateSalaryVo.getS046()));
        //计算残障金=应发工资*残障金比例
//        if(calculateSalaryVo.getS042()==null){
//            BigDecimal disFund = disabilityGoldRate!=null?(calculateSalaryVo.getS037().multiply(disabilityGoldRate)).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//            calculateSalaryVo.setS042(disFund);
//        }
        calculateSalaryVo.setS007(BigDecimal.ZERO);
        // S052;//实发合计 （增项-请假（减项））-个人所得税-税后减+税后增
        calculateSalaryVo.setS052 (calculateSalaryVo.getS037 ().subtract (calculateSalaryVo.getS051 ()).subtract (calculateSalaryVo.getS025 ()).add (calculateSalaryVo.getS026 ()));
        if(calculateSalaryVo.getS052().compareTo(BigDecimal.ZERO) < 0){
            calculateSalaryVo.setS052(BigDecimal.ZERO);
        }
        //计算残障金=应发工资*残障金比例
        if(calculateSalaryVo.getS042()==null){
            BigDecimal disFund = countDisFund(disabilityGoldRateVo, calculateSalaryVo.getS037(), calculateSalaryVo.getS007(),lastDisFund);
            calculateSalaryVo.setS042(disFund);
        }
        calculateSalaryVo.setS002 (BigDecimal.ZERO);
        calculateSalaryVo.setS027 (calculateSalaryVo.getAddTotal ());
        calculateSalaryVo.setS028 (calculateSalaryVo.getReduceTotal ());
        //把calculateSalaryVo数据存map中
        Method method = null;
        for (int i = 1; i <= calculateSalaryVo.getClass().getDeclaredFields().length; i++) {
            try {
                method = calculateSalaryVo.getClass ().getDeclaredMethod ("getS" + String.format ("%03d", i));
                mapSalary.put ("S" + String.format ("%03d", i), method.invoke (calculateSalaryVo));
            } catch (NoSuchMethodException ignored) {
            } catch (IllegalAccessException e) {
                e.printStackTrace ();
            } catch (InvocationTargetException e) {
                e.printStackTrace ();
            }

        }
        return mapSalary;

    }

    public Map<Long, String> setReceivingMap(List<Long> empIds,SalaryPayAndCategoryVo salaryPayAndCategoryVo){
        //参保地
        List<EmployeeOrderVo> employeeOrderVoList=new ArrayList<>();
        if(salaryPayAndCategoryVo.getContractType()== ContractType.CONTRACT_TYPE_PAYROLL_CREDIT.getCode()){
            employeeOrderVoList=contractAreaMapper.selectReceivingByEmpIdListAndContractType(empIds,salaryPayAndCategoryVo);
        }else {
            employeeOrderVoList=contractAreaMapper.selectReceivingByEmpIdList(empIds,salaryPayAndCategoryVo);
        }
        //先取离职日期为null的，然后取离职日期最大的，相等时，取申报离职时间最大的
        Map<Long, EmployeeOrderVo> employeeOrderVoMap = employeeOrderVoList.stream().collect(Collectors.toMap(EmployeeOrderVo::getEmployeeId,d->d,(v1, v2) -> {
                    return v1.getDimissionDate()==null?v1:
                            v2.getDimissionDate()==null?v2:
                                    DateUtil.parseStringToDate(v1.getDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()>DateUtil.parseStringToDate(v2.getDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()?v1:
                                            DateUtil.parseStringToDate(v1.getDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()==DateUtil.parseStringToDate(v2.getDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()?
                                                    DateUtil.parseStringToDate(v1.getApplyDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()>DateUtil.parseStringToDate(v2.getApplyDimissionDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD).getTime()?v1:v2:v2;
                }
        ));
        Map<Long, String> receivingMap=new HashMap<>();
        for (Long employeeId:employeeOrderVoMap.keySet()) {
            EmployeeOrderVo employeeOrderVo = employeeOrderVoMap.get(employeeId);
            if(!receivingMap.containsKey(employeeOrderVo.getEmployeeId())){
                String value=employeeOrderVo.getReceiving();
                if(BooleanTypeEnum.YES.getCode()==employeeOrderVo.getAccountFlag()){
                    value=String.valueOf(employeeOrderVo.getCustId());
                }
                value+="-"+employeeOrderVo.getCityCode();
                receivingMap.put(employeeOrderVo.getEmployeeId(),value);
            }
        }
        return receivingMap;
    }
    public void setPayInsuranceFlag(Map<Long, String> receivingMap,CalculateSalaryVo calculateSalaryVo){
        //判断发薪地与参保地
        //判断发薪地与参保地不一致:只有接单方与扣缴义务人编号相等时一致
        if(receivingMap.get(calculateSalaryVo.getEmpId())!=null){
            if(calculateSalaryVo.getOrgCode()!=null&&receivingMap.get(calculateSalaryVo.getEmpId()).equals(calculateSalaryVo.getOrgCode()+"-"+calculateSalaryVo.getCityCode())){
                calculateSalaryVo.setPayInsuranceFlag(BooleanTypeEnum.NO.getCode());
            }else {
                calculateSalaryVo.setPayInsuranceFlag(BooleanTypeEnum.YES.getCode());
            }
        }else {
            calculateSalaryVo.setPayInsuranceFlag(BooleanTypeEnum.YES.getCode());
        }
    }

    /**
     * 获取符合条件的残障金比例vo
     */
    public DisabilityGoldRateVo getDisabilityGoldRateVo(List<DisabilityGoldRateVo> disabilityGoldRateVoList,Map<String, String> payPlaceMap,
                                                        Map<String, Integer> withholdingAgentTypeMap,
                                                        Map<String, String> withholdingOrgCodeMap,
                                                        SalaryInfo salaryInfo, SalaryPayAndCategoryVo salaryPayAndCategoryVo) {
        DisabilityGoldRateVo disabilityGoldRateVo = new DisabilityGoldRateVo();
        Map<String, List<DisabilityGoldRateVo>> cityCodeAndDisabilityGoldRateVoMap = disabilityGoldRateVoList.stream().collect(Collectors.groupingBy(DisabilityGoldRateVo::getCityCode));
        List<DisabilityGoldRateVo> disabilityGoldRateVos = cityCodeAndDisabilityGoldRateVoMap.get(payPlaceMap.get(salaryInfo.getWithholdingAgentNo()));

        if (CollectionUtils.isNotEmpty(disabilityGoldRateVos)) {
            /**
             * 大户
             */
            Integer largeCode = WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex();
            /**
            * 扣缴义务人类型
            */
            Integer withType = withholdingAgentTypeMap.get(salaryInfo.getWithholdingAgentNo());
            /**
             * 大户code 单立户 供应商 id
             */
            String orgCode = withholdingOrgCodeMap.get(salaryInfo.getWithholdingAgentNo());
            if (Objects.equals(largeCode, withType)) {
                /**
                 * 大户默认类型只有一个税率 并且orgCode为空
                 * 特殊的需要加上合同号 和orgCode比对
                 */
                List<DisabilityGoldRateVo> largeTypeDisRateList = disabilityGoldRateVos.stream().filter(o -> o.getType().equals(largeCode)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(largeTypeDisRateList)) {
                    List<DisabilityGoldRateVo> specialList = largeTypeDisRateList.stream().filter(o -> StringUtils.isNotEmpty(o.getContractNo()) && o.getOrgCode().equals(orgCode) && o.getContractNo().equals(salaryPayAndCategoryVo.getContractNo())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specialList)) {
                        disabilityGoldRateVo = specialList.get(0);
                    } else {
                        List<DisabilityGoldRateVo> defalutLargeDisRateList = largeTypeDisRateList.stream().filter(o -> Objects.equals(o.getDisType(), DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(defalutLargeDisRateList)){
                            disabilityGoldRateVo = defalutLargeDisRateList.get(0);
                        }
                    }
                }
            } else {
                List<DisabilityGoldRateVo> otherTypeDisRateList = disabilityGoldRateVos.stream().filter(o -> !o.getType().equals(largeCode) && o.getOrgCode().equals(orgCode) && o.getType().equals(withType)
                        &&(StringUtils.isBlank(o.getWithholdingAgentNo())||o.getWithholdingAgentNo().equals(salaryInfo.getWithholdingAgentNo()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherTypeDisRateList)) {
                    List<DisabilityGoldRateVo> specialList = otherTypeDisRateList.stream().filter(o -> StringUtils.isNotEmpty(o.getContractNo()) && o.getContractNo().equals(salaryPayAndCategoryVo.getContractNo())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specialList)) {
                        disabilityGoldRateVo = specialList.get(0);
                    } else {
                        List<DisabilityGoldRateVo> defalutOtherDisRateList = otherTypeDisRateList.stream().filter(o -> Objects.equals(o.getDisType(), DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(defalutOtherDisRateList)){
                            disabilityGoldRateVo = defalutOtherDisRateList.get(0);
                        }
                    }
                }

            }
        }
        return disabilityGoldRateVo;
    }

    /**
     * 计算残障金金额
     * @param disabilityGoldRateVo vo
     * @param S037 应发工资
     * @param S007 实发工资
     * @return {@link BigDecimal }
     */
    public BigDecimal countDisFund(DisabilityGoldRateVo disabilityGoldRateVo,BigDecimal S037,BigDecimal S007,BigDecimal lastDisFund){
        BigDecimal disFund = BigDecimal.ZERO;
        if (disabilityGoldRateVo.getId()!=null){
            if (disabilityGoldRateVo.getDisType().equals(DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())||disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.SHOULD_BE_CALCULATED.getCode())){
                disFund = disabilityGoldRateVo.getRate()!=null?(S037.multiply(disabilityGoldRateVo.getRate())).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
            }else {
                if (disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.FIXED_AMOUNT.getCode())){
                    disFund = disabilityGoldRateVo.getFixed();
                }else if (disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.ACTUAL_CALCULATION.getCode())){
                    disFund = disabilityGoldRateVo.getRate()!=null?(S007.multiply(disabilityGoldRateVo.getRate())).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
                }
            }
            if(lastDisFund!=null){
                disFund=disFund.add(lastDisFund);
            }
            if (disabilityGoldRateVo.getUpperLimit().compareTo(BigDecimal.ZERO) > 0&&disabilityGoldRateVo.getUpperLimit().compareTo(disFund)<0){
                disFund = disabilityGoldRateVo.getUpperLimit();
            }
            if(lastDisFund!=null){
                disFund=disFund.subtract(lastDisFund);
                if(disFund.compareTo(BigDecimal.ZERO)<0){
                    disFund=BigDecimal.ZERO;
                }
            }
        }
        return disFund;
    }

    public void setBankChangeFlag(SalaryInfo salaryInfo,CalculateSalaryVo calculateSalaryVo){
        if(calculateSalaryVo.getEmpCardId()!=null&&
                (salaryInfo.getEmpCardId()==null||!salaryInfo.getEmpCardId().equals(calculateSalaryVo.getEmpCardId()))
        ){
            salaryInfo.setBankChangeFlag(BooleanTypeEnum.YES.getCode());
        }else {
            salaryInfo.setBankChangeFlag(BooleanTypeEnum.NO.getCode());
        }
    }
    public void setEmpCardId(CalculateSalaryVo calculateSalaryVo,CalculateSalaryVo calculateLast){
        if(calculateLast!=null){
            calculateSalaryVo.setEmpCardId(calculateLast.getEmpCardId());
        }
    }

}
