package com.reon.hr.sp.bill.entity.supplierPractice;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 往期实做服务月表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-09 09:29:37
 */
@Accessors(chain = true)
@Data
@TableName("before_practice_service_month")
public class BeforePracticeServiceMonth implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Long id;
	/**
	 * 订单编号
	 */
	private String orderNo;
	/**
	 * 实做ID
	 */
	private Long practiceId;
	/**
	 * 产品类型
	 */
	private Integer prodCode;
	/**
	 * 正常服务月Map<receiveMonth,LIST<billMonth>>
	 */
	private String serviceMonth;
	/**
	 * 正常缓存服务月Map<billMonth,List<receiveMonth>>
	 */
	private String serviceMonthCache;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updater;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 删除标识(Y:已删除，N:未删除)
	 */
	private String delFlag;
	/**
	 * 年缴产品服务月Map<receiveMonth,LIST<billMonth>>
	 */
	private String yearServiceMonth;
	/**
	 * 年缴产品缓存服务月Map<billMonth,List<receiveMonth>>
	 */
	private String yearServiceMonthCache;
	/**
	 * List<receiveMonth>补充计入最大最小收费月的List 只会在第一次生成的时候处理离职的并且超过12个月的订单
	 */
	private String noDealMonth;

}
