package com.reon.hr.sp.base.dao.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo;
import com.reon.hr.api.base.vo.InsurancePracticePayBankDetailConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月06日
 * @Version 1.0
 */
public interface InsurancePracticePayBankConfigMapper {

    List<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigList(@Param("vo") InsurancePracticePayBankConfigVo vo, Page page);

    int insertInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo);

    int updateInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo);

    int getInsurancePracticePayBankConfigCountByOrgCode(String orgCode);

    InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigById(Long id);


    List<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigByOrgCodeList(@Param("orgCodeList") List<String> orgCodeList);

    InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigByOrgCode(String orgCode);

    List<InsurancePracticePayBankConfigVo> getAllInsurancePracticePayBankConfig();
}
