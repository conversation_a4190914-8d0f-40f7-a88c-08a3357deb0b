/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/8/27
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.service.cus;

import com.reon.hr.api.base.vo.DictVo;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.sp.customer.dubbo.service.rpc.impl.contract.ContractRelativeQuotationWrapperServiceImpl;
import com.reon.hr.sp.customer.service.CommonTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import com.reon.hr.sp.customer.service.cus.ContractService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TestContractServiceImpl
 * @description TODO
 * @date 2021/8/27 14:25
 */


public class TestContractServiceImpl extends CommonTest {
    @Autowired
    ContractService contractService;
    @Autowired
    private RedisTemplate redisTemplate;
    private String message = "hellowWorld";

    @Autowired
    private ContractRelativeQuotationWrapperServiceImpl contractRelativeQuotationWrapperService;
//    @Autowired
//    private  ContractBillWrapperServiceImpl contractBillWrapperService;
@Autowired
    private IContractResourceWrapperService iContractResourceWrapperService;

    @Test
    public void testContractServiceDemo() {
        Integer integer = contractService.updateContractDefaultQuoteNo();
        System.out.println(integer);
    }

    @Test
    public void test03() {

        Map<String, List<DictVo>> map = redisTemplate.opsForHash().entries("GLOBAL_DICT");
        System.out.println(map);
    }

    @Test
    public void testDealContractArchiLog() {
        contractService.dealLogData();
    }

    @Test
    public void testContractRelativeQuotationWrapperService() {
        Map<String, Map<BigDecimal, String>> stringMapMap = contractRelativeQuotationWrapperService.selectQuoteNo();
        Map<BigDecimal, String> bigDecimalStringMap = stringMapMap.get("HT-20250210006201");
        System.out.println(bigDecimalStringMap);
        //    log.info("");
    }
}
