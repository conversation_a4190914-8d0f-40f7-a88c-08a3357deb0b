/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2022/1/27
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.service.impl.importService;

import com.alibaba.druid.support.json.JSONUtils;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.dto.importData.CommInsurBatchReductionImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.enums.CommInsurOrderEnum;
import com.reon.hr.api.customer.enums.commInsur.ComInsurOrderLogOprType;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.vo.commInsurOrder.CommInsurOrderVo;
import com.reon.hr.api.customer.vo.employee.EmployeeVo;
import com.reon.hr.sp.customer.dao.commInsurOrder.ComInsurOrderLogMapper;
import com.reon.hr.sp.customer.dao.commInsurOrder.CommInsurOrderMapper;
import com.reon.hr.sp.customer.dao.commInsurOrder.OrderApplyMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeMapper;
import com.reon.hr.sp.customer.entity.employee.ComInsurOrderLog;
import com.reon.hr.sp.customer.entity.employee.CommInsurOrder;
import com.reon.hr.sp.customer.entity.employee.OrderApply;
import com.reon.hr.sp.customer.service.ImportService.BatchAddCommInsurImportService;
import com.reon.hr.sp.customer.service.cus.IBatchImportDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 <AUTHOR>
 @version 1.0 */
@Service
public class BatchAddCommInsurReductionServiceImpl implements BatchAddCommInsurImportService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private static final String BREAK_THE_RULES = "违反规则";
	private static final String SYSTEM_ERROR = "系统错误";
	private static final String REPEAT_DATA_ERROR = "有重复数据";
	private static final String THE_EMPLOYEE_DOES_NOT_EXIST_IN_THE_SYSTEM = "系统中不存在该员工";
	private static final String THE_ORDER_NO_DOES_NOT_EXIST_IN_THE_SYSTEM = "未查出信息，请检查合同编号、方案编号、身份证号是否有误！";
	private static final String THE_EMPLOYEE_HANDLES_STATUS_AND_ORDER_STATUS_OUT_OF_SERVICE = "该员工处理状态和订单状态非在职";
	private static final String WRONG_ID_CARD_OR_NAME = "身份证或姓名与系统中对比有误";
	private static final String EXPIRE_TIME_CANNOT_BE_EARLIER_THAN = "收费截止月不能早于收费起始月";
	private static final String PLEASE_FILL_IN_THE_CORRECT_DATE_AND_YEAR = "请填写正确年月 例:202001";
	private static final String IS = "是";
	private static final String NO = "否";
	private static final String FILL_IN_THE_WRONG = "是否出险:填写有误,只能填入是否";
	private static final Integer IS_INT = 2;
	private static final Integer NO_INT = 1;
	@Autowired
	private IBatchImportDataService batchImportDataService;
	@Autowired
	EmployeeMapper employeeMapper;
	@Autowired
	CommInsurOrderMapper commInsurOrderMapper;
	@Autowired
	OrderApplyMapper orderApplyMapper;
	@Autowired
	private ComInsurOrderLogMapper comInsurOrderLogMapper;

	@Override
	public void addImportCommInsurReductionData(ImportDataDto<CommInsurBatchReductionImportDto> importDataDto) {
		logger.info("商保批量减员开始:导入编号{}", importDataDto.getImportNo());
		batchImportDataService.addImportData(importDataDto, ImportDataType.COMM_INSUR_BATCH_IMPORT.getCode());
		List<CommInsurOrder> allCommInsurOrders = checkImportDataJudgeField(importDataDto);
		/** 插入错误消息日志 */
		batchInsertAllReductionData(allCommInsurOrders, importDataDto.getLoginName(), importDataDto.getRemark());
		batchImportDataService.addAndupdateImportData(importDataDto);
	}

	private void batchInsertAllReductionData(List<CommInsurOrder> allCommInsurOrders, String loginName, String remark) {
		Set<String> orderNoSet = allCommInsurOrders.stream().map(CommInsurOrder::getOrderNo).collect(Collectors.toCollection(HashSet::new));
		HashSet<String> orderApplyOrderNoSet = new HashSet<>();
		if (orderApplyOrderNoSet.size() > 0) {
			orderApplyOrderNoSet = orderApplyMapper.getAllOrderApplyByOrderNoList(orderNoSet);
		}
		List<CommInsurOrder> batchUpdateCommInsurOrderList = Lists.newArrayList();
		List<OrderApply> insertOrderApplyList = Lists.newArrayList();
		List<OrderApply> updateOrderApplyList = Lists.newArrayList();
		List<ComInsurOrderLog> comInsurOrderLogList = Lists.newArrayList();
		for (CommInsurOrder commInsurOrder : allCommInsurOrders) {
			commInsurOrder.setDealStatus(CommInsurOrderEnum.DealStatusEnum.DEAL_STATUS5.getIndex());
			commInsurOrder.setStatus(CommInsurOrderEnum.StatusEnum.STATUS1.getIndex());
			commInsurOrder.setUpdater(loginName);
			batchUpdateCommInsurOrderList.add(commInsurOrder);
			if (orderApplyOrderNoSet.contains(commInsurOrder.getOrderNo())) {
				OrderApply orderApply = new OrderApply();
				orderApply.setOrderNo(commInsurOrder.getOrderNo());
				orderApply.setUpdater(loginName);
				orderApply.setOutFlag(commInsurOrder.getOutFlag());
				orderApply.setExpiredMonth(commInsurOrder.getEndMonth());
				updateOrderApplyList.add(orderApply);
			} else {
				OrderApply orderApply = new OrderApply();
				orderApply.setOrderNo(commInsurOrder.getOrderNo());
				orderApply.setCreator(loginName);
				orderApply.setOutFlag(commInsurOrder.getOutFlag());
				orderApply.setExpiredMonth(commInsurOrder.getEndMonth());
				insertOrderApplyList.add(orderApply);
			}
			ComInsurOrderLog ciol = new ComInsurOrderLog();
			ciol.setOrderNo(commInsurOrder.getOrderNo());
			ciol.setEmployeeId(commInsurOrder.getEmployeeId());
			ciol.setOprType(ComInsurOrderLogOprType.APPLY_FOR_REDUCE_PEOPLE.getCode());
			ciol.setRemark(remark);
			ciol.setCreator(loginName);
			comInsurOrderLogList.add(ciol);
		}

		try {
			int updateNum = 0;
			int orderApplyInsertNum = 0;
			int orderApplyUpdateNum = 0;
			int comInsurOrderLogInsertNum = 0;
			if (batchUpdateCommInsurOrderList.size() > 0) {
				updateNum = commInsurOrderMapper.batchEfficientUpdateCommInsurOrderByOrderNo(batchUpdateCommInsurOrderList);
			}
			if (insertOrderApplyList.size() > 0) {
				orderApplyInsertNum = orderApplyMapper.batchInsertList(insertOrderApplyList);
			}
			if (updateOrderApplyList.size() > 0) {
				orderApplyUpdateNum = orderApplyMapper.batchUpdateOrderApply(updateOrderApplyList);
			}
			if (comInsurOrderLogList.size() > 0) {
				comInsurOrderLogInsertNum = comInsurOrderLogMapper.batchInsertList(comInsurOrderLogList);
			}
			logger.info("commInsurOrder update : {} ,orderApplyInsertNum : {} orderApplyUpdateNum : {} comInsurOrderLogInsertNum : {}", updateNum, orderApplyInsertNum, orderApplyUpdateNum, comInsurOrderLogInsertNum);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("存储错误");
		}
	}

	private ArrayList<CommInsurOrder> checkImportDataJudgeField(ImportDataDto<CommInsurBatchReductionImportDto> importDataDto) {
		List<CommInsurBatchReductionImportDto> dataList = importDataDto.getDataList();
		//根据合同号、方案号、身份证号获取数据
		List<CommInsurOrderVo> commInsurOrderVoList=commInsurOrderMapper.getByContractNoAndSolutionNoAndCertNo(dataList);
		Map<String,String> contractNoAndSolutionNoMap=new HashMap<>();
		for (CommInsurOrderVo commInsurOrderVo:commInsurOrderVoList) {
			contractNoAndSolutionNoMap.put(commInsurOrderVo.getContractNo()+"-"+commInsurOrderVo.getSolutionNo()+"-"+commInsurOrderVo.getCertNo(),commInsurOrderVo.getOrderNo());
		}
		List<String> certNoList = dataList.stream().map(CommInsurBatchReductionImportDto::getCertNo).collect(Collectors.toList());
		//region 获取到该excel中的所有员工进行验证 如果这里报错,表示employee 表里面有重复的身份证
		List<EmployeeVo> allEmployeeIdByCertNoList = employeeMapper.getAllEmployeeIdByCertNoList(certNoList);
		Map<String, EmployeeVo> certNoAndEmployeeMap = allEmployeeIdByCertNoList.stream().collect(Collectors.toMap(EmployeeVo::getCertNo, Function.identity()));
		Map<Long, EmployeeVo> employeeIdAndEmployeeMap = allEmployeeIdByCertNoList.stream().collect(Collectors.toMap(EmployeeVo::getId, Function.identity()));
		//endregion


		Map<String, CommInsurOrderVo> orderNoAndCommInsurOrderMap = commInsurOrderVoList.stream().collect(Collectors.toMap(CommInsurOrderVo::getOrderNo, Function.identity()));
		Map<String, Integer> orderNoAndStartTimeMap = commInsurOrderVoList.stream().collect(Collectors.toMap(CommInsurOrderVo::getOrderNo, CommInsurOrderVo::getRevStartMonth));
		//		commInsur表中 在职的 deal_status = 4 status = 2
		Map<String, CommInsurOrderVo> matchStateMap = commInsurOrderVoList.stream().filter(x -> x.getDealStatus() == CommInsurOrderEnum.DealStatusEnum.DEAL_STATUS4.getIndex() && x.getStatus() == CommInsurOrderEnum.StatusEnum.STATUS2.getIndex()).collect(Collectors.toMap(CommInsurOrderVo::getOrderNo, Function.identity()));

		List<String> successContractNoAndSolutionNoAndCertNoList = Lists.newArrayList();
		ArrayList<CommInsurOrder> commInsurOrders = Lists.newArrayList();
		for (CommInsurBatchReductionImportDto item : dataList) {
			String contractNoAndSolutionNoAndCertNo=item.getContractNo()+"-"+item.getSolutionNo()+"-"+item.getCertNo();
			if (successContractNoAndSolutionNoAndCertNoList.contains(contractNoAndSolutionNoAndCertNo)) {
				item.updateError(BREAK_THE_RULES, "合同编号:"+item.getContractNo()+"方案编号:"+item.getSolutionNo()+"身份证号:"+item.getCertNo()+REPEAT_DATA_ERROR);
			} else {
				successContractNoAndSolutionNoAndCertNoList.add(contractNoAndSolutionNoAndCertNo);
			}
			/** 验证合同号、方案号、身份证号是否存在 */
			if (!contractNoAndSolutionNoMap.containsKey(contractNoAndSolutionNoAndCertNo)) {
				item.updateError(BREAK_THE_RULES, THE_ORDER_NO_DOES_NOT_EXIST_IN_THE_SYSTEM);
			} else {
				item.setOrderNo(contractNoAndSolutionNoMap.get(contractNoAndSolutionNoAndCertNo));
				if (!matchStateMap.containsKey(item.getOrderNo())) {
					item.updateError(BREAK_THE_RULES, THE_EMPLOYEE_HANDLES_STATUS_AND_ORDER_STATUS_OUT_OF_SERVICE);
				}
				/** 验证身份 -> 系统中要有该员工,商保订单中要有该员工.*/
				if (!certNoAndEmployeeMap.containsKey(item.getCertNo())) {
					item.updateError(BREAK_THE_RULES, THE_EMPLOYEE_DOES_NOT_EXIST_IN_THE_SYSTEM);
				} else {
					Long employeeId = orderNoAndCommInsurOrderMap.get(item.getOrderNo()).getEmployeeId();
					/** 验证身份证和姓名 */
					EmployeeVo employeeVo = employeeIdAndEmployeeMap.get(employeeId);
					if (!(employeeVo.getCertNo().equals(item.getCertNo()) && employeeVo.getName().equals(item.getEmployeeName()))) {
						item.updateError(BREAK_THE_RULES, WRONG_ID_CARD_OR_NAME);
					}
				}
				if (!(item.getErrorDescription().size() > 0)) {
					/** 验证时间 结束时间不能早于开始时间 */
					Integer startTime = orderNoAndStartTimeMap.get(item.getOrderNo());
					/** 如果时间验证有问题,那么就不进比较了 没有意义*/
					boolean tip = true;
					String dateStr = item.getEndMonth().toString();
					if (null == getDate(dateStr)) {
						tip = false;
					}
					if (dateStr.length() != 6 || tip == false) {
						item.updateError(BREAK_THE_RULES, PLEASE_FILL_IN_THE_CORRECT_DATE_AND_YEAR);
					}
					if (tip) {
						if (startTime > item.getEndMonth()) {
							item.updateError(BREAK_THE_RULES, EXPIRE_TIME_CANNOT_BE_EARLIER_THAN);
						}
					}
				}
			}
			if (IS.equals(item.getOutFlagSt())) {
				item.setOutFlag(IS_INT);
			} else if (NO.equals(item.getOutFlagSt())) {
				item.setOutFlag(NO_INT);
			} else {
				item.updateError(BREAK_THE_RULES, FILL_IN_THE_WRONG);
			}
			if (!item.getErrorDescription().isEmpty()) {
				importDataDto.recordError(item.getRowNum(), JSONUtils.toJSONString(item.getErrorDescription()));
			}
			if (item.getErrorDescription().isEmpty()) {
				CommInsurOrder commInsurOrder = new CommInsurOrder();
				BeanUtils.copyProperties(item, commInsurOrder);
				commInsurOrder.setEmployeeId(certNoAndEmployeeMap.get(item.getCertNo()).getId());
				commInsurOrders.add(commInsurOrder);
			}
			importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(item, importDataDto.getImportNo(), importDataDto.getLoginName()));
		}
		return commInsurOrders;
	}

	private Date getDate(String dateStr) {
		Date date = null;
		for (SimpleDateFormat simpleDateFormat : formatList) {
			try {
				date = simpleDateFormat.parse(dateStr);
				break;
			} catch (Exception e) {
			}
		}
		return date;
	}

	private static final List<SimpleDateFormat> formatList = Arrays.asList(
			new SimpleDateFormat("yyyyMM")
	);

}
