package com.reon.hr.api.customer.vo.insurancePractice;

import com.reon.hr.api.customer.vo.changeBase.AdjustCfgVo;
import com.reon.hr.api.customer.vo.changeBase.AdjustDetailVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Date: 2022/12/6 15:21
 * @Version: 1.0
 */

@Data
public class PracAdjustJobVo implements Serializable {
    private Long id;
    /**
     * 调整任务编号
     */
    private String adjustNo;
    /**
     * 调整名称
     */
    private String adjustName;
    /**
     * 福利办理方
     */
    private String handleOrgCode;
    /**
     * 福利包code
     */
    private String packCode;
    private String packName;
    /**
     * 调整类型(1:基数调整，2:比例替换，3:基数调整+比例替换)
     */
    private Integer adjustType;

    /**
     * 调整范围
     */
    private String rangeType;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 调整起始月
     */
    private Integer startMonth;
    /**
     * 调整状态(1:未调整，2：已调整)
     */
    private Integer status;
    /**
     * 调整人所在机构
     */
    private String orgCode;
    /**
     * 调整人岗位
     */
    private String posCode;
    /**
     * 福利办理方
     */
    private List<PracAdjustCfgVo> adjustCfgVoList;
    private List<PracAdjustDetailVo> adjustDetailVoList;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;

    public void initAdjustJob(String loginName){
        this.setCreator(loginName);
        this.setUpdater(loginName);
        this.setUpdateTime(new Date());
        this.setCreateTime(new Date());
    }
}
