package com.reon.hr.api.customer.dto.importData;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @datetime 2022年 08月 31日 14:05
 * @version: 1.0
 */
@ExcelIgnoreUnannotated
@Data
public class SalaryPayDto extends BaseImportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 发放编号
     */
    private String payNo;

    /**
     * 薪资类别id
     */
    @ExcelProperty("薪资类别编号*")
    private String salaryCategoryId;



    /**
     * 发放名称
     */
    @ExcelProperty("发放名称*")
    private String payName;

    /**
     * 工资所属月
     */
    @ExcelProperty("工资所属月*")
    private String salaryMonth;

    /**
     * 工资计税月
     */
    @ExcelProperty("工资计税月*")
    private String taxMonth;

    /**
     * 客户账单月
     */
    @ExcelProperty("客户账单月*")
    private String billMonth;

    /**
     * 是否确认（1:否，2:是）
     */

    private int confirmFlag;

    /**
     * 数据确认时间
     */

    private Date confirmTime;

    /**
     * 发放状态(1:未发放，2:发放中，3:已发放)
     */

    private int payStatus;

    /**
     * 缓发人数
     */

    private Integer delayCnt;

    /**
     * 发放人数
     */

    private Integer payCnt;

    /**
     * 未发放人数
     */

    private Integer noPayCnt;

    /**
     * 创建人
     */

    private String creator;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 修改人
     */

    private String updater;

    /**
     * 修改时间
     */

    private Date updateTime;

    /**
     * 删除标识(y:已删除，n:未删除)
     */

    private String delFlag;

}
