<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>保险模板配置记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <!-- import CSS -->
    <link rel="stylesheet" href="${ctx}/layui/vue/general.css?v=${publishVersion}" media="all">
    <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
    <!-- import JavaScript -->
    <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/icon.js?v=${publishVersion}"></script>

</head>
<body class="childrenBody">
<div id="app">
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="城市:">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市"
                           @change="handleCityChange">
                    <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                               :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:">
                <el-input class="width220" v-model="obj.queryParams.custName" placeholder="请选择客户" clearable
                          readonly>
                    <template #append>
                        <el-button icon="Search" @click="handleSelectCustomer">选择</el-button>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="合同编号:">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请选择合同" clearable
                          readonly>
                    <template #append>
                        <el-button icon="Search" @click="handleSelectContract">选择</el-button>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="接单方:">
                <el-select class="width220" filterable v-model="obj.queryParams.orgCode" placeholder="请选择接单方">
                    <el-option v-for="item in obj.orgList" :key="item.id" :label="item.name"
                               :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="View" :disabled="obj.single"
                           @click="handleDetail">查看
                </el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip max-height="calc(100vh - 240px)" border
                  :data="obj.tableData"
                  @selection-change="handleSelectionChange" @row-dblclick="handleRowDoubleClick"
                  row-key="id" style="width: 100%">
            <el-table-column type="selection" align="center" width="50"></el-table-column>
            <el-table-column label="序号" align="center" prop="index" width="60"></el-table-column>
            <el-table-column label="城市名称" align="center" prop="cityCode" width="120">
                <template #default="scope">
                    {{ getCityName(scope.row.cityCode) }}
                </template>
            </el-table-column>
            <el-table-column label="合同编号" align="center" prop="contractNo" width="200"></el-table-column>
            <el-table-column label="合同类型" align="center" prop="contractType" width="120">
                <template #default="scope">
                    {{ getContractTypeName(scope.row.contractType) }}
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="custName"></el-table-column>
            <el-table-column label="接单方" align="center" prop="receivingStr"></el-table-column>
            <el-table-column label="备案内容" align="center" prop="remark"></el-table-column>
            <el-table-column label="查看文件" align="center" prop="fileId">
                <template #default="scope">
                    <el-button v-if="scope.row.fileId" link type="primary" @click="handleViewFile(scope.row)"
                               size="small">点击
                    </el-button>
                    <span v-else>无</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" size="small">查看
                    </el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" size="small">修改
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
                v-show="obj.total > 0"
                :current-page="obj.queryParams.page"
                :page-size="obj.queryParams.limit"
                :page-sizes="[10, 50, 100, 200]"
                :total="obj.total"
                background
                layout="total, sizes, prev, pager, next, jumper"
                style="margin-top: 20px; text-align: right;"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>

        <!-- 新增/修改/查看弹窗 -->
        <el-dialog v-model="obj.dialogVisible" :title="obj.dialogTitle" width="50%" :close-on-click-modal="false">
            <el-form :model="obj.formData" :rules="obj.formRules" ref="formRef" label-width="auto">
                <!-- 第一行 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="城市名称" prop="cityCode">
                            <el-select :disabled="obj.isView" v-model="obj.formData.cityCode" placeholder="请选择城市"
                                       filterable
                                       @change="handleFormCityChange" style="width: 100%">
                                <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                                           :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户名称" prop="custName">
                            <el-input :disabled="obj.isView" v-model="obj.formData.custName" placeholder="请选择客户"
                                      readonly>
                                <template #append v-if="!obj.isView">
                                    <el-button icon="Search" @click="handleSelectFormCustomer">选择</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 第二行 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="合同编号" prop="contractNo">
                            <el-input :disabled="obj.isView" v-model="obj.formData.contractNo" placeholder="请选择合同"
                                      readonly>
                                <template #append v-if="!obj.isView">
                                    <el-button icon="Search" @click="handleSelectFormContract">选择</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合同类型" prop="contractType">
                            <el-input :disabled="obj.isView" v-model="obj.formData.contractTypeName" placeholder="合同类型"
                                      readonly></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 第三行 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="接单方" prop="receiving">
                            <el-select :disabled="obj.isView" v-model="obj.formData.receiving" placeholder="请选择接单方"
                                       filterable
                                       style="width: 100%">
                                <el-option v-for="item in obj.formOrgList" :key="item.id" :label="item.name"
                                           :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户编号" prop="custNo">
                            <el-input :disabled="obj.isView" v-model="obj.formData.custNo" placeholder="客户编号"
                                      readonly></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 第四行 -->
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="备案内容" prop="remark">
                            <el-input :disabled="obj.isView" v-model="obj.formData.remark" placeholder="请输入备案内容"
                                      type="textarea" :rows="3"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 附件上传 -->
                <el-row :gutter="20" v-if="!obj.isView">
                    <el-col :span="24">
                        <el-form-item label="附件上传">
                            <el-upload
                                    class="upload-demo"
                                    :action="uploadUrl"
                                    :on-success="handleUploadSuccess"
                                    :on-error="handleUploadError"
                                    :file-list="obj.fileList"
                                    :before-upload="beforeUpload"
                                    multiple>
                                <el-button type="primary">选择文件</el-button>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        支持上传多个文件
                                    </div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 查看模式下的附件列表 -->
                <el-row :gutter="20" v-else-if="obj.formData.fileId">
                    <el-col :span="24">
                        <el-form-item label="附件">
                            <div>
                                <el-button link type="primary" @click="handleDownloadFile">查看附件</el-button>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSave" v-if="!obj.isView">保存</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 客户选择弹窗 -->
        <el-dialog v-model="obj.customerDialogVisible" title="选择客户" width="40%" :close-on-click-modal="false">
            <el-form :model="obj.customerQueryParams" inline label-width="auto">
                <el-form-item label="客户名称:">
                    <el-input v-model="obj.customerQueryParams.custName" placeholder="请输入客户名称" clearable
                              @keyup.enter="handleCustomerQuery"></el-input>
                </el-form-item>
                <el-form-item label="客户编号:">
                    <el-input v-model="obj.customerQueryParams.custNo" placeholder="请输入客户编号" clearable
                              @keyup.enter="handleCustomerQuery"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleCustomerQuery">查询</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="obj.customerLoading" :data="obj.customerTableData" max-height="400px" border
                      @selection-change="handleCustomerSelectionChange" style="margin-top: 10px;">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column label="客户ID" align="center" prop="id" width="100"></el-table-column>
                <el-table-column label="客户编号" align="center" prop="custNo" width="150"></el-table-column>
                <el-table-column label="客户名称" align="center" prop="custName"
                                 show-overflow-tooltip></el-table-column>
            </el-table>

            <el-pagination
                    v-show="obj.customerTotal > 0"
                    :current-page="obj.customerQueryParams.page"
                    :page-size="obj.customerQueryParams.limit"
                    :page-sizes="[20, 50, 100]"
                    :total="obj.customerTotal"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    style="margin-top: 20px; text-align: right;"
                    @size-change="handleCustomerSizeChange"
                    @current-change="handleCustomerCurrentChange"></el-pagination>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.customerDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleCustomerConfirm">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 合同选择弹窗 -->
        <el-dialog v-model="obj.contractDialogVisible" title="选择合同" width="40%" :close-on-click-modal="false">
            <el-form :model="obj.contractQueryParams" inline label-width="auto">
                <el-form-item label="合同名称:">
                    <el-input v-model="obj.contractQueryParams.contractName" placeholder="请输入合同名称" clearable
                              @keyup.enter="handleContractQuery"></el-input>
                </el-form-item>
                <el-form-item label="客户名称:">
                    <el-input v-model="obj.contractQueryParams.custName" placeholder="请输入客户名称" clearable
                              @keyup.enter="handleContractQuery"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleContractQuery">查询</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="obj.contractLoading" :data="obj.contractTableData" max-height="400px" border
                      @selection-change="handleContractSelectionChange" style="margin-top: 10px;">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column label="合同编号" align="center" prop="contractNo" width="160"></el-table-column>
                <el-table-column label="合同名称" align="center" prop="contractName" width="180"
                                 show-overflow-tooltip></el-table-column>
                <el-table-column label="客户编号" align="center" prop="custNo" width="160"></el-table-column>
                <el-table-column label="客户名称" align="center" prop="custName" width="180"
                                 show-overflow-tooltip></el-table-column>
                <el-table-column label="合同类型" align="center" prop="contractType" width="180">
                    <template #default="scope">
                        {{ getContractTypeName(scope.row.contractType) }}
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    v-show="obj.contractTotal > 0"
                    :current-page="obj.contractQueryParams.page"
                    :page-size="obj.contractQueryParams.limit"
                    :page-sizes="[20, 50, 100]"
                    :total="obj.contractTotal"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    style="margin-top: 20px; text-align: right;"
                    @size-change="handleContractSizeChange"
                    @current-change="handleContractCurrentChange"></el-pagination>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.contractDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleContractConfirm">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 文件查看弹窗 -->
        <el-dialog v-model="obj.fileDialogVisible" title="查看文件" width="50%" :close-on-click-modal="false">
            <div style="text-align: center;">
                <el-button type="primary" @click="handleDownloadViewFile">下载文件</el-button>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.fileDialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</div>
</body>
<%--suppress JSUnresolvedReference --%>
<script>
    const {createApp, reactive, ref, onMounted} = Vue
    const {ElMessage, ElMessageBox} = ElementPlus

    const app = createApp({
        setup() {
            const formRef = ref(null);
            const uploadUrl = ML.contextPath + '/file/upload';

            const obj = reactive({
                multiple: true,//是否多选
                single: true,//是否单选
                loading: false,//加载中
                queryParams: {
                    page: 1,
                    limit: 10,
                    cityCode: '',
                    custName: '',
                    custId: '',
                    contractNo: '',
                    orgCode: '',
                },//查询表单
                total: 0,//总条数

                tableData: [],//表格数据
                ids: [],//选中的id

                cityList: window.top['area'] || [],
                contractTypeList: window.top['dictCachePool']['CONTRACT_CATEGORY'] || [],
                orgList: [],

                // 弹窗相关
                dialogVisible: false,
                dialogTitle: '',
                isView: false,
                formData: {
                    id: '',
                    cityCode: '',
                    custName: '',
                    custId: '',
                    custNo: '',
                    contractNo: '',
                    contractName: '',
                    contractType: '',
                    contractTypeName: '',
                    receiving: '',
                    receivingStr: '',
                    remark: '',
                    fileId: '',
                },
                formRules: {
                    cityCode: [{required: true, message: '请选择城市', trigger: 'change'}],
                    custName: [{required: true, message: '请选择客户', trigger: 'blur'}],
                    contractNo: [{required: true, message: '请选择合同', trigger: 'blur'}],
                },

                // 表单专用数据
                formOrgList: [],
                fileList: [],

                // 客户选择弹窗相关
                customerDialogVisible: false,
                customerLoading: false,
                customerQueryParams: {
                    page: 1,
                    limit: 20,
                    custName: '',
                    custNo: '',
                },
                customerTotal: 0,
                customerTableData: [],
                selectedCustomer: [],

                // 合同选择弹窗相关
                contractDialogVisible: false,
                contractLoading: false,
                contractQueryParams: {
                    page: 1,
                    limit: 20,
                    contractName: '',
                    custName: '',
                },
                contractTotal: 0,
                contractTableData: [],
                selectedContract: [],

                // 文件查看弹窗相关
                fileDialogVisible: false,
                currentFileId: '',
            })

            /** 检查会话是否过期 */
            function checkSessionExpired(error) {
                if (error.response && error.response.status === 302) {
                    ElMessage.error('会话已过期，请重新登录');
                    setTimeout(() => {
                        window.location.href = ML.contextPath + '/login';
                    }, 2000);
                    return true;
                }
                return false;
            }

            /** 列表 */
            function getList() {
                obj.loading = true;

                const params = {
                    page: obj.queryParams.page,
                    limit: obj.queryParams.limit
                };

                if (obj.queryParams.cityCode) params.cityCode = obj.queryParams.cityCode;
                if (obj.queryParams.custId) params.custId = obj.queryParams.custId;
                if (obj.queryParams.contractNo) params.contractNo = obj.queryParams.contractNo;
                if (obj.queryParams.orgCode) params.orgCode = obj.queryParams.orgCode;

                axios.get(ML.contextPath + '/base/InsuTempCfgRecord/getInsuTempCfgRecordPage', {
                    params: params
                }).then((res) => {
                    if (res.data.code === 0) {
                        obj.tableData = res.data.data || [];
                        for (let i = 0; i < obj.tableData.length; i++) {
                            obj.tableData[i].index = (obj.queryParams.page - 1) * obj.queryParams.limit + i + 1;
                        }
                        obj.total = res.data.count || 0;
                        obj.loading = false;
                    } else {
                        ElMessage.error(res.data.msg || '查询失败');
                        obj.loading = false;
                    }
                }).catch((error) => {
                    console.error('查询失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('查询失败，请检查网络连接');
                    }
                    obj.loading = false;
                });
            }

            /** 搜索按钮操作 */
            function handleQuery() {
                obj.queryParams.page = 1;
                getList();
            }

            /** 重置按钮操作 */
            function resetQuery() {
                obj.queryParams = {
                    page: 1,
                    limit: 10,
                    cityCode: '',
                    custName: '',
                    custId: '',
                    contractNo: '',
                    orgCode: '',
                }
                obj.orgList = [];
                handleQuery();
            }

            /** 多选框选中数据 */
            function handleSelectionChange(selection) {
                obj.ids = selection.map(item => item.id);
                obj.single = selection.length != 1;
                obj.multiple = !selection.length;
            }

            /** 城市变化处理 */
            function handleCityChange(cityCode) {
                obj.queryParams.orgCode = '';
                obj.orgList = [];

                if (cityCode) {
                    loadOrgListByCity(cityCode);
                }
            }

            /** 选择客户 */
            function handleSelectCustomer() {
                obj.customerQueryParams = {
                    page: 1,
                    limit: 20,
                    custName: '',
                    custNo: '',
                };
                obj.selectedCustomer = [];
                obj.customerDialogVisible = true;
                getCustomerList();
            }

            /** 选择合同 */
            function handleSelectContract() {
                obj.contractQueryParams = {
                    page: 1,
                    limit: 20,
                    contractName: '',
                    custName: '',
                };
                obj.selectedContract = [];
                obj.contractDialogVisible = true;
                getContractList();
            }


            /** 新增按钮操作 */
            function handleAdd() {
                resetFormData();
                obj.dialogTitle = '新增保险模板配置记录';
                obj.isView = false;
                obj.dialogVisible = true;
            }

            /** 双击行查看 */
            function handleRowDoubleClick(row) {
                if (row.id != null) {
                    handleDetail(row);
                }
            }

            /** 查看详情 */
            function handleDetail(row) {
                if (row instanceof Event) {
                    row = null;
                }
                const id = row ? row.id : obj.ids[0];
                if (!id) {
                    ElMessage.warning('请选择一行');
                    return;
                }
                loadFormData(id, 'view');
            }

            /** 修改按钮操作 */
            function handleUpdate(row) {
                if (row instanceof Event) {
                    row = null;
                }
                const id = row ? row.id : obj.ids[0];
                console.log(id)
                if (!id) {
                    ElMessage.warning('请选择一行');
                    return;
                }
                loadFormData(id, 'edit');
            }

            /** 查看文件 */
            function handleViewFile(row) {
                obj.currentFileId = row.fileId;
                obj.fileDialogVisible = true;
            }

            /** 分页大小改变 */
            function handleSizeChange(val) {
                obj.queryParams.limit = val;
                obj.queryParams.page = 1;
                getList();
            }

            /** 当前页改变 */
            function handleCurrentChange(val) {
                obj.queryParams.page = val;
                getList();
            }

            /** 获取城市名称 */
            function getCityName(cityCode) {
                const city = obj.cityList.find(item => item.code == cityCode);
                return city ? city.name : cityCode;
            }

            /** 获取合同类型名称 */
            function getContractTypeName(type) {
                const item = obj.contractTypeList.find(item => item.code == type);
                return item ? item.name : type;
            }

            /** 重置表单数据 */
            function resetFormData() {
                obj.formData = {
                    id: '',
                    cityCode: '',
                    custName: '',
                    custId: '',
                    custNo: '',
                    contractNo: '',
                    contractName: '',
                    contractType: '',
                    contractTypeName: '',
                    receiving: '',
                    receivingStr: '',
                    remark: '',
                    fileId: '',
                };
                obj.formOrgList = [];
                obj.fileList = [];
                obj.orgList = [];
            }

            /** 表单中城市变化处理 */
            function handleFormCityChange(cityCode) {
                obj.formData.receiving = '';
                obj.formOrgList = [];

                if (cityCode) {
                    loadFormOrgListByCity(cityCode);
                }
            }

            /** 表单中选择客户 */
            function handleSelectFormCustomer() {
                handleSelectCustomer();
            }

            /** 表单中选择合同 */
            function handleSelectFormContract() {
                handleSelectContract();
            }

            /** 根据城市加载组织列表 */
            function loadOrgListByCity(cityCode) {
                axios.get(ML.contextPath + '/customer/contractArea/getSupplierOrCompany?cityCode=' + cityCode)
                    .then((res) => {
                        if (res.data.code === 0) {
                            obj.orgList = res.data.data || [];
                        }
                    }).catch((error) => {
                    console.error('加载组织列表失败:', error);
                    obj.orgList = [];
                });
            }

            /** 根据城市加载表单组织列表 */
            function loadFormOrgListByCity(cityCode) {
                axios.get(ML.contextPath + '/customer/contractArea/getSupplierOrCompany?cityCode=' + cityCode)
                    .then((res) => {
                        if (res.data.code === 0) {
                            obj.formOrgList = res.data.data || [];
                            obj.formData.receiving =  obj.formOrgList.find((item) => item.id.split('_')[1] === obj.formData.receiving).name;
                        }
                    }).catch((error) => {
                    console.error('加载组织列表失败:', error);
                    obj.formOrgList = [];
                });
            }

            /** 获取客户列表 */
            function getCustomerList() {
                obj.customerLoading = true;

                const params = {
                    page: obj.customerQueryParams.page,
                    limit: obj.customerQueryParams.limit
                };

                if (obj.customerQueryParams.custName) params.custName = obj.customerQueryParams.custName;
                if (obj.customerQueryParams.custNo) params.custNo = obj.customerQueryParams.custNo;

                axios.get(ML.contextPath + '/customer/contract/getCustomerByAll', {
                    params: params
                }).then((res) => {
                    if (res.data.code === 0) {
                        obj.customerTableData = res.data.data || [];
                        obj.customerTotal = res.data.count || 0;
                        obj.customerLoading = false;
                    } else {
                        ElMessage.error(res.data.msg || '查询客户失败');
                        obj.customerLoading = false;
                    }
                }).catch((error) => {
                    console.error('查询客户失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('查询客户失败，请检查网络连接');
                    }
                    obj.customerLoading = false;
                });
            }

            /** 获取合同列表 */
            function getContractList() {
                obj.contractLoading = true;

                const params = {
                    page: obj.contractQueryParams.page,
                    limit: obj.contractQueryParams.limit
                };

                if (obj.contractQueryParams.contractName) params.contractName = obj.contractQueryParams.contractName;
                if (obj.contractQueryParams.custName) params.custName = obj.contractQueryParams.custName;

                axios.get(ML.contextPath + '/report/getContractPageByName', {
                    params: params
                }).then((res) => {
                    if (res.data.code === 0) {
                        obj.contractTableData = res.data.data || [];
                        obj.contractTotal = res.data.count || 0;
                        obj.contractLoading = false;
                    } else {
                        ElMessage.error(res.data.msg || '查询合同失败');
                        obj.contractLoading = false;
                    }
                }).catch((error) => {
                    console.error('查询合同失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('查询合同失败，请检查网络连接');
                    }
                    obj.contractLoading = false;
                });
            }

            /** 客户查询 */
            function handleCustomerQuery() {
                obj.customerQueryParams.page = 1;
                getCustomerList();
            }

            /** 合同查询 */
            function handleContractQuery() {
                obj.contractQueryParams.page = 1;
                getContractList();
            }

            /** 客户选择变化 */
            function handleCustomerSelectionChange(selection) {
                obj.selectedCustomer = selection;
            }

            /** 合同选择变化 */
            function handleContractSelectionChange(selection) {
                obj.selectedContract = selection;
            }

            /** 客户确认选择 */
            function handleCustomerConfirm() {
                if (obj.selectedCustomer.length === 0) {
                    ElMessage.warning('请选择一个客户');
                    return;
                }

                const customer = obj.selectedCustomer[0];

                // 如果是查询条件中的客户选择
                if (!obj.dialogVisible) {
                    obj.queryParams.custId = customer.id;
                    obj.queryParams.custName = customer.custName;
                } else {
                    // 如果是表单中的客户选择
                    obj.formData.custId = customer.id;
                    obj.formData.custName = customer.custName;
                    obj.formData.custNo = customer.custNo;
                }

                obj.customerDialogVisible = false;
            }

            /** 合同确认选择 */
            function handleContractConfirm() {
                if (obj.selectedContract.length === 0) {
                    ElMessage.warning('请选择一个合同');
                    return;
                }

                const contract = obj.selectedContract[0];

                // 如果是查询条件中的合同选择
                if (!obj.dialogVisible) {
                    obj.queryParams.contractNo = contract.contractNo;
                } else {
                    // 如果是表单中的合同选择
                    obj.formData.contractNo = contract.contractNo;
                    obj.formData.contractName = contract.contractName;
                    obj.formData.contractType = contract.contractType;
                    obj.formData.contractTypeName = getContractTypeName(contract.contractType);
                    obj.formData.custId = contract.custId;
                    obj.formData.custName = contract.custName;
                    obj.formData.custNo = contract.custNo;
                }

                obj.contractDialogVisible = false;
            }

            /** 客户分页大小改变 */
            function handleCustomerSizeChange(val) {
                obj.customerQueryParams.limit = val;
                obj.customerQueryParams.page = 1;
                getCustomerList();
            }

            /** 客户当前页改变 */
            function handleCustomerCurrentChange(val) {
                obj.customerQueryParams.page = val;
                getCustomerList();
            }

            /** 合同分页大小改变 */
            function handleContractSizeChange(val) {
                obj.contractQueryParams.limit = val;
                obj.contractQueryParams.page = 1;
                getContractList();
            }

            /** 合同当前页改变 */
            function handleContractCurrentChange(val) {
                obj.contractQueryParams.page = val;
                getContractList();
            }

            /** 文件上传前检查 */
            function beforeUpload(file) {
                const isLt10M = file.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                    ElMessage.error('上传文件大小不能超过 10MB!');
                }
                return isLt10M;
            }

            /** 文件上传成功 */
            function handleUploadSuccess(response, file) {
                if (response.code === 0) {
                    obj.formData.fileId = response.data.fileId;
                    ElMessage.success('文件上传成功');
                } else {
                    ElMessage.error(response.msg || '文件上传失败');
                }
            }

            /** 文件上传失败 */
            function handleUploadError(error) {
                ElMessage.error('文件上传失败');
            }

            /** 下载附件 */
            function handleDownloadFile() {
                if (obj.formData.fileId) {
                    window.open(ML.contextPath + '/file/download?fileId=' + obj.formData.fileId);
                }
            }

            /** 下载查看的文件 */
            function handleDownloadViewFile() {
                if (obj.currentFileId) {
                    window.open(ML.contextPath + '/file/download?fileId=' + obj.currentFileId);
                }
            }

            /** 加载表单数据 */
            function loadFormData(id, mode) {
                const row = obj.tableData.find((item) => item.id === id);
                if (row) {
                    obj.formData = {
                        ...row,
                        contractTypeName: getContractTypeName(row.contractType)
                    };
                    loadFormOrgListByCity(row.cityCode)
                    obj.dialogTitle = mode === 'view' ? '查看保险模板配置记录' : '修改保险模板配置记录';
                    obj.isView = mode === 'view';
                    obj.dialogVisible = true;
                }
            }

            /** 保存表单 */
            function handleSave() {
                formRef.value.validate((valid) => {
                    if (!valid) {
                        ElMessage.error('请完善必填信息');
                        return;
                    }

                    const submitData = {
                        ...obj.formData
                    };

                    const url = obj.formData.id ?
                        '/base/InsuTempCfgRecord/updateInsuTempCfgRecord' :
                        '/base/InsuTempCfgRecord/addInsuTempCfgRecord';

                    axios.post(ML.contextPath + url, submitData, {
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    }).then((res) => {
                        if (res.data.code === 0) {
                            ElMessage.success(res.data.msg || '操作成功');
                            obj.dialogVisible = false;
                            getList();
                        } else {
                            ElMessage.error(res.data.msg || '操作失败');
                        }
                    }).catch((error) => {
                        console.error('提交失败:', error);
                        if (!checkSessionExpired(error)) {
                            ElMessage.error('提交失败，请检查网络连接');
                        }
                    });
                });
            }

            // 加载图标
            if (typeof ElementPlusIconsVue !== 'undefined') {
                for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                    app.component(key, component)
                }
            } else {
                console.error('ElementPlusIconsVue 未定义，请检查图标文件是否正确加载')
                if (window.ElementPlusIconsVue) {
                    for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
                        app.component(key, component)
                    }
                }
            }

            onMounted(() => {
                getList()
            })

            return {
                obj,
                formRef,
                uploadUrl,
                handleQuery,
                resetQuery,
                handleSelectionChange,
                handleCityChange,
                handleSelectCustomer,
                handleSelectContract,
                handleAdd,
                handleRowDoubleClick,
                handleDetail,
                handleUpdate,
                handleViewFile,
                handleSizeChange,
                handleCurrentChange,
                getCityName,
                getContractTypeName,
                resetFormData,
                handleFormCityChange,
                handleSelectFormCustomer,
                handleSelectFormContract,
                loadOrgListByCity,
                loadFormOrgListByCity,
                getCustomerList,
                getContractList,
                handleCustomerQuery,
                handleContractQuery,
                handleCustomerSelectionChange,
                handleContractSelectionChange,
                handleCustomerConfirm,
                handleContractConfirm,
                handleCustomerSizeChange,
                handleCustomerCurrentChange,
                handleContractSizeChange,
                handleContractCurrentChange,
                beforeUpload,
                handleUploadSuccess,
                handleUploadError,
                handleDownloadFile,
                handleDownloadViewFile,
                loadFormData,
                handleSave,
            }
        }
    })
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount('#app')
</script>
</html>
