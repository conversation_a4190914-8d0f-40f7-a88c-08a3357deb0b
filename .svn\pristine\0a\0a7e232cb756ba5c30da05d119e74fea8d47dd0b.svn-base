package com.reon.hr.core.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

public class SpringContextUtil implements ApplicationContextAware {
    
    private  static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    	setContext(applicationContext);
    }
    
    private static void setContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
    
    public static Object getBean(String beanName){
        return context.getBean(beanName);
    }
    
    public static <T> T getBean(Class<T> clazz){
        return context.getBean(clazz);
    }
}
