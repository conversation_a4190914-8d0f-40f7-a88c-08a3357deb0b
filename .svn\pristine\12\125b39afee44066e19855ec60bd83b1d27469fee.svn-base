<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.BillCheckAbolishForPersonnelMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.bill.BillCheckAbolishForPersonnel">
    <!--@mbg.generated-->
    <!--@Table `reon-billdb`.bill_check_abolish_for_personnel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bili_id" jdbcType="BIGINT" property="biliId" />
    <result column="old_check_date" jdbcType="TIMESTAMP" property="oldCheckDate" />
    <result column="abolish_date" jdbcType="TIMESTAMP" property="abolishDate" />
    <result column="old_bill_amt" jdbcType="DECIMAL" property="oldBillAmt" />
    <result column="old_check_amt" jdbcType="DECIMAL" property="oldCheckAmt" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bili_id, old_check_date, abolish_date, old_bill_amt, old_check_amt, creator, 
    create_time, updater, update_time, del_flag
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `reon-billdb`.bill_check_abolish_for_personnel
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bili_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.biliId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="old_check_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="abolish_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.abolishDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="old_bill_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldBillAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="old_check_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckAmt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="del_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `reon-billdb`.bill_check_abolish_for_personnel
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bili_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.biliId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.biliId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="old_check_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldCheckDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="abolish_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.abolishDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.abolishDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="old_bill_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldBillAmt != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.oldBillAmt,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="old_check_amt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldCheckAmt != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.oldCheckAmt,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="del_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.delFlag != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-billdb`.bill_check_abolish_for_personnel
    (bili_id, old_check_date, abolish_date, old_bill_amt, old_check_amt, creator )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.biliId,jdbcType=BIGINT}, #{item.oldCheckDate,jdbcType=TIMESTAMP}, #{item.abolishDate,jdbcType=TIMESTAMP}, 
        #{item.oldBillAmt,jdbcType=DECIMAL}, #{item.oldCheckAmt,jdbcType=DECIMAL}, #{item.creator,jdbcType=VARCHAR} )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckAbolishForPersonnel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-billdb`.bill_check_abolish_for_personnel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      bili_id,
      old_check_date,
      abolish_date,
      old_bill_amt,
      old_check_amt,
      creator,
      create_time,
      updater,
      update_time,
      del_flag,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{biliId,jdbcType=BIGINT},
      #{oldCheckDate,jdbcType=TIMESTAMP},
      #{abolishDate,jdbcType=TIMESTAMP},
      #{oldBillAmt,jdbcType=DECIMAL},
      #{oldCheckAmt,jdbcType=DECIMAL},
      #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP},
      #{delFlag,jdbcType=CHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      bili_id = #{biliId,jdbcType=BIGINT},
      old_check_date = #{oldCheckDate,jdbcType=TIMESTAMP},
      abolish_date = #{abolishDate,jdbcType=TIMESTAMP},
      old_bill_amt = #{oldBillAmt,jdbcType=DECIMAL},
      old_check_amt = #{oldCheckAmt,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.bill.entity.bill.BillCheckAbolishForPersonnel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-billdb`.bill_check_abolish_for_personnel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="biliId != null">
        bili_id,
      </if>
      <if test="oldCheckDate != null">
        old_check_date,
      </if>
      <if test="abolishDate != null">
        abolish_date,
      </if>
      <if test="oldBillAmt != null">
        old_bill_amt,
      </if>
      <if test="oldCheckAmt != null">
        old_check_amt,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biliId != null">
        #{biliId,jdbcType=BIGINT},
      </if>
      <if test="oldCheckDate != null">
        #{oldCheckDate,jdbcType=TIMESTAMP},
      </if>
      <if test="abolishDate != null">
        #{abolishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="oldBillAmt != null">
        #{oldBillAmt,jdbcType=DECIMAL},
      </if>
      <if test="oldCheckAmt != null">
        #{oldCheckAmt,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="biliId != null">
        bili_id = #{biliId,jdbcType=BIGINT},
      </if>
      <if test="oldCheckDate != null">
        old_check_date = #{oldCheckDate,jdbcType=TIMESTAMP},
      </if>
      <if test="abolishDate != null">
        abolish_date = #{abolishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="oldBillAmt != null">
        old_bill_amt = #{oldBillAmt,jdbcType=DECIMAL},
      </if>
      <if test="oldCheckAmt != null">
        old_check_amt = #{oldCheckAmt,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>

  <select id="getBillCheckAbolishForPersonnelReportByMonth"
            resultType="com.reon.hr.api.bill.dto.BillCheckAbolishForPersonnelDto">
      select id,
             bili_id,
             old_check_date,
             abolish_date,
             old_bill_amt,
             old_check_amt,
             creator
      from `reon-billdb`.bill_check_abolish_for_personnel
      where del_flag = 'N'
        and date_format(date_add(create_time, interval -1 month) , '%Y%m')  =  #{month}
      order by bili_id, id;
  </select>
</mapper>