package com.reon.hr.api.customer.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data

@HeadRowHeight(25)//设置标题头行高
@HeadFontStyle(fontHeightInPoints = 9)//设置标题头字体大小
@ColumnWidth(20)//设置单元格宽度
@ContentRowHeight(25)//设置行高
@ContentFontStyle(fontHeightInPoints = 12)//设置excel文件内容字体大小
@ContentStyle(wrapped = true,horizontalAlignment = HorizontalAlignment.CENTER) //设置是否自动换行,内容居中
public class ExcelExportVo implements Serializable {

  private static final long serialVersionUID = -3009772772584104328L;
  @ExcelProperty(value = "客户编号")
  private String custNo;
  @ExcelProperty(value = "客户名称")
  @ColumnWidth(25)
  private String custName;
  @ExcelProperty(value = "小合同编号")
  @ColumnWidth(23)
  private String contractAreaNo;
  @ExcelProperty(value = "小合同名称")
  @ColumnWidth(30)
  private String name;
  @ExcelProperty(value = "合同编号")
  private String contractNo;
  @ExcelIgnore
  private Integer contractType;
  @ExcelProperty(value = "合同类型")
  @ColumnWidth(15)
  private String contractTypeName;
  @ExcelIgnore
  private Integer distributeType;
 /* @ExcelProperty(value = "人员分布")
  @ColumnWidth(15)
  private String distributeTypeName;*/
  @ExcelProperty(value = "客户账单模板")
  @ColumnWidth(30)
  private String templetName;
  @ExcelIgnore
  private Integer hasLaborContract;
  @ExcelProperty(value = "是否需要签订劳动合同")
  @ColumnWidth(10)
  private String hasLaborContractName;
  @ExcelIgnore
  private Integer archiveFlag;
  @ExcelProperty(value = "是否存档")
  @ColumnWidth(10)
  private String archiveFlagName;
  @ExcelIgnore
  private Integer telFlag;
  @ExcelProperty(value = "是否外呼")
  @ColumnWidth(10)
  private String telFlagName;
  @ExcelIgnore
  private Integer accountFlag;
  @ExcelProperty(value = "是否单立户")
  private String accuntFlagName;
    @ExcelProperty(value = "单立户名称")
    private String sinAccName;
  @ExcelIgnore
  private Integer injuryFlag;
  @ExcelProperty(value = "是否单工伤")
  private String injuryFlagName;
  @ExcelIgnore
  private Integer dispatchType;
  @ExcelProperty(value = "派单类型")
  @ColumnWidth(15)
  private String dispatchTypeName;
  @ExcelProperty(value = "特殊说明")
  private String remark;
  @ExcelIgnore
  private String genBill;
  @ExcelProperty(value = "账单方")
  @ColumnWidth(30)
  private String genBillName;
  @ExcelIgnore
  private String receiverId;
  @ExcelProperty(value = "收款方")
  @ColumnWidth(30)
  private String receiverIdName;
  @ExcelIgnore
  private String signCom;
  @ExcelProperty(value = "签单方")
  @ColumnWidth(30)
  private String signComName;
  @ExcelIgnore
  private String distCom;
  @ExcelProperty(value = "派单方")
  @ColumnWidth(30)
  private String distComName;
  @ExcelProperty(value = "接单方")
  @ColumnWidth(30)
  private String receiving;
  @ExcelIgnore
  private Integer cityCode;
  @ExcelProperty(value = "城市")
  @ColumnWidth(17)
  private String cityCodeName;
  @ExcelIgnore
  private String commissioner;
  @ExcelProperty(value = "项目客服")
  @ColumnWidth(15)
  private String commissionerName;
  @ExcelIgnore
  private String receivingMan;
  @ExcelProperty(value = "接单方客服")
  @ColumnWidth(15)
  private String receivingManName;
  @ExcelProperty(value = "在职人数")
  @ColumnWidth(10)
  private Integer numberOfEmployes;
  @ExcelIgnore
  private Integer status;
  @ExcelProperty(value = "小合同状态")
  @ColumnWidth(15)
  private String statusName;
  @ExcelIgnore
  private Integer firstFlag;
  @ExcelProperty(value = "是否优先")
  @ColumnWidth(10)
  private String firstFlagName;
}
