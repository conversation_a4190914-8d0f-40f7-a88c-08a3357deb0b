var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        tableSelect = layui.tableSelect,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    /** 该接单所有的城市 */
    var cityList = [];
    /** 城市和公司map */
    var orgMap = {};
    /** 公司list */
    var orgList = [];
    $(document).ready(function () {
        document.getElementById("orderNo").focus();
        ML.dictFormatter()
        /** 给城市和福利办理方赋值 */
        if (ML.isNotEmpty($("#cityCodeList").val())) {
            cityList = JSON.parse(ML.trimAll($("#cityCodeList").val()));
            $.each(cityList, function (i, item) {
                $("#city").append($("<option/>").text(ML.areaFormatter(item)).attr("value", item));
            });
            form.render('select');
        }
        if (ML.isNotEmpty($("#cityCodeAndOrgListMap").val())) {
            orgMap = JSON.parse($("#cityCodeAndOrgListMap").val());
        }
        /** 给orgCode 设置值 */
        if (ML.isNotEmpty($("#city").val())) {
            setOrgCode($("#city").val());
        }

    });

    /*监听document的回车操作*/
    $(document).bind('keypress', function (event) {
        if (event.keyCode === 13) {
            reloadTable();
        }
    });

    table.render({
        id: 'noMatchPackGrid',
        elem: '#noMatchPackGridTable'
        , url: ML.contextPath + '/customer/insurancePractice/getNoMatchPackPageList'
        , height: 'full-200'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , where: {"paramData": JSON.stringify(serialize("searchForm"))}
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            ML.hideNoAuth();
            for (var i = 0; i < res.data.length; i++) {
                /** 已处理未匹配 */
                if (res.data[i].status == 2) {
                    $("tr").eq(i + 1).css("color", "red");
                    $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                }
            }
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50'}
            , {field: 'orderNo', title: '订单号', width: '150', align: 'center'}
            , {
                field: 'orgCode', title: '福利办理方', width: '220', align: 'center', templet: function (d) {
                    if (ML.isNotEmpty(d.orgCode)) {
                        return getCompanyName(d.orgCode)
                    }
                }
            }
            , {
                field: 'singleFlag', title: '是否是大户', width: '150', align: 'center', templet: function (d) {
                    if (ML.isNotEmpty(d.singleFlag)) {
                        return ML.dictFormatter("INSUR_PACK_SINGLE", d.singleFlag);
                    }
                }
            }
            , {field: 'custId', hide: true}
            , {field: 'custName', title: '客户名称', width: '150', align: 'center'}
            , {field: 'ratioCode', hide: true}
            , {field: 'id', hide: true}
            , {field: 'ratioName', title: '未匹配比例', width: '600', align: 'center'}
            , {
                field: 'status', title: '状态', width: '150', align: 'center', templet: function (d) {
                    if (ML.isNotEmpty(d.status)) {
                        return ML.dictFormatter("NO_MATCH_PACK_STATUS", d.status);
                    }
                }
            }
            , {field: 'createTime', title: '创建时间', width: '150', align: 'center'}


        ]]
    });
// }

    table.on('toolbar(noMatchPackGridTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'setProcessed':
                if (checkStatus.data.length == 0) {
                    return layer.msg("未选中处理行!");
                }
                if (checkStatus.data.length >= 1) {
                    var processedArray = [];
                    for (var i = 0; i < checkStatus.data.length; i++) {
                        var item = checkStatus.data[i];
                        let insertItem = {};
                        insertItem.orderNo = item.orderNo;
                        insertItem.ratioCode = item.ratioCode;
                        insertItem.id = item.id;
                        insertItem.type = item.type
                        processedArray.push(insertItem);
                    }
                    ableBatch(JSON.stringify(processedArray));
                }
                break;
            case "export":
                var url = ML.contextPath + "/customer/insurancePractice/exportNoMatchData?1=1";
                var paramData = JSON.stringify(serialize("searchForm"));
                if (paramData != null && paramData !== '') {
                    url += "&paramData=" + paramData
                }
                window.location.href = url;
                break;
        }
    });


    function ableBatch(ids) {
        var url;
        url = ML.contextPath + "/customer/insurancePractice/setProcessed";
        layer.confirm('确定操作？', {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                type: "POST",
                url: url,
                data: {"processedList":  ids},
                dataType: "json",
                success: function (data) {
                    layer.msg(data.msg, {icon: 1});
                    reloadTable()
                },
                error: function (data) {
                    layer.msg(data.msg, {icon: 5})
                }
            });

        });
    }


    //重载数据
    function reloadTable() {
        console.log($('#custId').val());
        table.reload('noMatchPackGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    var active = {
        reload: function () {
            reloadTable()
        }
    };

    $('#btnQuery').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    $('#clearAll').on('click', function () {
        $("#custName").val("");
        $("#custId").val("");
        $("#orderNo").val("");
        $("#ratioName").val("");
        document.getElementById("sinAccName").options.length = 1;
        form.render('select')
    });


    form.on("select(cityFilter)", function (data) {
        let value = data.value;
        setOrgCode(value);
    });

    function setOrgCode(data) {
        $("#handler").empty();
        if (ML.isNotEmpty(orgMap[data])) {
            orgList = orgMap[data];
            $.each(orgList, function (i, item) {
                $("#handler").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
            });
        }
        form.render('select');
    }


    // 搜索条件  客户下拉列表框
    var custAppd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: custAppd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        }
        , done: function (elem, data) {
            $("#custId").val(ML.isNotEmpty(data.data[0]) ? data.data[0].id : "");
            $("#custName").val(ML.isNotEmpty(data.data[0]) ? data.data[0].custName : "");
            if (ML.isNotEmpty($("#custId").val())){
                document.getElementById("sinAccName").options.length = 0
                $("#sinAccName").append("<option value=''></option>");
                $.ajax({
                    type: "GET",
                    url: ML.contextPath + "/prac/singleAccount/getSingleAccountNameByCustId?custId="+$("#custId").val(),
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        $.each(data.data, function (i, item) {
                            $("#sinAccName").append($("<option/>").text(item.name).attr("value", item.id));
                        });

                    },
                    error: function (data) {
                        console.log("error")
                    }
                });
            }else {
                let selectElement = document.getElementById("sinAccName");
                selectElement.options.length = 1;
            }
            form.render('select');
        }
    });

    function getCompanyName(data) {
        let comPanyName = "";
        for (let index in orgMap) {
            let list = orgMap[index];
            $.each(list, function (i, item) {
                if (item.orgCode == data) {
                    comPanyName = item.orgName;
                }
            })
        }
        return comPanyName;
    }

});
