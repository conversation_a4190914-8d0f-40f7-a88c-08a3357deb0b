package com.reon.hr.sp.customer.dao.cus;


import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.supplier.NoSupplierContractVo;
import com.reon.hr.api.customer.vo.supplier.NoSupplierVo;
import com.reon.hr.api.customer.vo.supplier.SupplierSearchVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NoSupplierMapper {

    List<SupplierVo> selectNosupplier(Page page, SupplierSearchVo sup1plierSearchVo);//非设备公积金查询 分页
    List<SupplierVo> selectNosupplier(SupplierSearchVo sup1plierSearchVo);//非设备公积金查询 分页
    List<NoSupplierVo>  selectNosupplierById(@Param("id") Long id);//页面查看详情查询SQL
    /**
     * 查询非社保供应商 ，外部调用*/
    List<SupplierVo>  getNoSupplierName(@Param("supplierName")String  supplierName,@Param("type") Integer type);
    /**查询非社保供应商，页面下拉框调用*/
    List<SupplierVo>  getAllNoSupplierName(@Param("supplierName")String  supplierName);

//	void saveNoSupplierContractAndProduct(@Param("NSCV") NoSupplierContractVo noSupplierContractVo);
}