package com.reon.hr.api.base.vo;

import lombok.Data;

@Data
public abstract class AbstractPolicyVo {
    private Long id;
    /**
     * 公司类型 (1:供应商 2:自有公司)
     */
    private Integer compType;
    private String compTypeStr;
    /**
     * 省
     */
    private String province;
    private String provinceStr;
    /**
     * 市
     */
    private String city;
    private String cityStr;
    /**
     * 人员类型
     */
    private String category;
}
