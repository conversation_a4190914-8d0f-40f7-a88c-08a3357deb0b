var ctx = ML.contextPath;
layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer,
        upload = layui.upload;


    var iStatus = '';
    var closeInterval = '';
    //开始日期范围
    var startDate = laydate.render({
        elem: '#startOprTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    //结束日期范围
    var endDate = laydate.render({
        elem: '#endOprTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });

    //查询导入统计数据
    table.render({
        id: 'batchImportGrid'
        , elem: '#batchImportGrid'
        , url: ML.contextPath + '/customer/batchPersonOrderEdit/getImportDataListPage?dataType=33'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , limit: 50
        , method: 'GET'
        , limits: [50, 100, 200],
        height: 'full-200'
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'importNo', title: '导入编号', width: '150', align: 'center', fixed: 'left'}
            , {
                field: 'oprMan', title: '导入人', width: '150', align: 'center', fixed: 'left', templet: function (d) {
                    if (typeof (d.oprMan) == "undefined") {
                        return "无导入人"
                    }
                    return ML.loginNameFormater(d.oprMan);
                }
            }
            , {field: 'oprTime', title: '导入时间', align: 'center', width: '150'}
            , {field: 'remark', title: '备注', align: 'center', width: '150'}
            , {field: 'successNum', title: '成功记录数', align: 'center', width: '150'}
            , {field: 'failNum', title: '失败记录数', align: 'center', width: '150'}
            , {
                field: 'fileId', title: '导入文件', align: 'center', width: '150', templet: function (d) {
                    var split = d.fileId.split(",");
                    return '<a href="' + ML.fileServerUrl + split[0] + '" target="_self" class="layui-table-link" style="text-decoration:underline" >' + split[1] + '</a>';
                }
            }
            , {
                field: 'iStatus', title: '处理状态', align: 'center', width: '150'
                , templet: function (d) {
                    if (d.iStatus == "处理中") {
                        return ' <span style="color: #f51d10;">' + d.iStatus + '</span>';
                    } else if (d.iStatus == "已处理") {
                        return ' <span">' + d.iStatus + '</span>';
                    } else {
                        return ' <span">' + d.iStatus + '</span>';
                    }
                }
            }
            , {
                field: 'creator', title: '创建人', align: 'center', width: '150', templet: function (d) {
                    if (typeof (d.creator) == "undefined") {
                        return "无创建人"
                    }
                    return ML.loginNameFormater(d.creator);
                }
            }
            , {field: 'createTime', title: '创建时间', align: 'center', width: '150'}
            , {field: 'updater', title: '修改人', align: 'center', width: '150'}
            , {field: 'updateTime', title: '修改时间', align: 'center', width: '150'}
            , {field: '', title: '历史信息查看', toolbar: '#btn', align: 'center', width: '150', fixed: 'right'}
        ]],
        done: function (res) {
            if (res.data[0]) {
                iStatus = res.data[0].iStatus;
            }
            table.on('tool(batchImportGridTable)', function (obj) {
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                var ids = [];
                ids.push(data.importNo);
                switch (layEvent) {
                    case 'query':
                        if (data.iStatus == "已处理") {
                            customerImport("历史信息查看", 'query', ['60%', '60%'], data)
                        } else {
                            layer.msg("导入数据" + data.iStatus + "状态,不能查看历史信息");
                        }
                        break;
                }
            });
            table.on('toolbar(batchImportGridTable)', function (obj) {
                // var checkStatus = table.checkStatus(obj.config.id);
                switch (obj.event) {
                    case 'import':
                        customerImport("上传导入数据", "import", ['35%', '26%'], null);
                        break;
                    case 'download':
                        batchAddDistributionTemplate();
                        break;
                }
            });

            //监听行双击事件
            table.on('rowDouble(batchImportGridTable)', function (obj) {
                if (obj.data.iStatus == "已处理") {
                    customerImport("历史信息查看", 'query', ['60%', '60%'], obj.data)
                } else {
                    layer.msg("导入数据" + obj.data.iStatus + "状态,不能查看历史信息");
                }
            });
        }
    });



    //打开窗口
    function customerImport(title, optType, area, data) {
        var url = "";
        if (optType == 'import') {
            url = "/customer/batchPersonOrderEdit/gotoBatchImportDataViewForBillStartMonth";
        } else if (optType == 'query') {
            url = "/customer/batchPersonOrderEdit/gotoBatchPersonOrderEditDataHistoryView?id=" + data.importNo + "&optType=" + 33;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
                if (optType == 'import'){
                    closeInterval = setInterval('reloadView()', 5000);
                }
            }
        });
    }

    //初始化表单数据
    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('batchImportGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    //批量客户导入数据
    upload.render({
        elem: '#selectFile'
        , accept: 'file'
        , url: ctx + '/customer/batchPersonOrderEdit/importExcelForStartBillMonth'
        , auto: false
        //,multiple: true
        , bindAction: '#upload'
        , method: 'POST'
        , exts: 'xls|xlsx'
        , before: function (obj) { //obj参数包含的信息
            this.data = {
                'remark': $('#remark').val()
            };//关键代码
            ML.layuiButtonDisabled($('#upload'));// 禁用
        }, choose: function (obj) {
            //读取本地文件
            obj.preview(function (index, file, result) {
                $("#selectFile").val(file.name);
            });
        },
        done: function (res, index, upload) {
            if (res.code === 0) {
                layer.msg("操作成功");
                layer.closeAll('loading');
                layer.closeAll('iframe');
                reloadTable();
            } else {
                layer.msg(res.msg);
                layer.closeAll('loading');
                layer.closeAll('iframe');
                reloadTable();
            }
            setInterval('reloadView()', 5000);
        }, error: function (index, upload) {
            ML.layuiButtonDisabled($('#upload'), true);// 取消禁用
            layer.msg("数据接口异常，导入失败！");
            reloadTable();
        }
    });

    //重载数据
    function reloadTable() {
        table.reload('batchImportGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    //下载模板
    function batchAddDistributionTemplate() {
        window.location.href = ML.fileServerUrl + ML.loadGlobalCfg("batchEditBillStartMonthTemplate");
    }

    //关闭弹窗
    $("#cancelBtn").click(function () {
        layer.closeAll('iframe');
        reloadTable();
    });

    //轮询当前数据表格
    window.reloadView = function () {
        reloadTable();
        if (iStatus == "已处理") {
            clearInterval(closeInterval);
        }
    }





});
