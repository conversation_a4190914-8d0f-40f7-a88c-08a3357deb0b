package com.reon.hr.sp.base.dubbo.rpc.sys.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.InsurancePracticePayBankConfigWrapperService;
import com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo;
import com.reon.hr.api.base.vo.InsurancePracticePayBankDetailConfigVo;
import com.reon.hr.api.base.vo.InsurancePracticePayBankDetailRemarkVo;
import com.reon.hr.sp.base.service.sys.InsurancePracticePayBankConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月06日
 * @Version 1.0
 */
@Service("insurancePracticePayBankConfigWrapperService")
public class InsurancePracticePayBankConfigWrapperServiceImpl implements InsurancePracticePayBankConfigWrapperService {

    @Resource
    private InsurancePracticePayBankConfigService insurancePracticePayBankConfigService;

    @Override
    public Page<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigPage(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigPage(insurancePracticePayBankConfigVo);
    }

    @Override
    public int updateInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo) {
        return insurancePracticePayBankConfigService.updateInsurancePracticePayBankConfig(insurancePracticePayBankConfigVo);
    }

    @Override
    public int addInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo) {
        return insurancePracticePayBankConfigService.addInsurancePracticePayBankConfig(insurancePracticePayBankConfigVo);
    }

    @Override
    public int getInsurancePracticePayBankConfigCountByOrgCode(String orgCode) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigCountByOrgCode(orgCode);
    }


    @Override
    public List<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigByOrgCodeList(List<String> orgCodeList) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigByOrgCodeList(orgCodeList);
    }

    @Override
    public InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigByOrgCode(String orgCode) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigByOrgCode(orgCode);
    }

    @Override
    public Map<String, InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigMap() {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigMap();
    }

    @Override
    public InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigById(Long id) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankConfigById(id);
    }

    @Override
    public List<InsurancePracticePayBankDetailConfigVo> getInsurancePracticePayBankDetailConfigByOrgCode(String orgCode) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankDetailConfigByOrgCode(orgCode);
    }

    @Override
    public int addInsurancePracticePayBankDetailRemarkVo(InsurancePracticePayBankDetailRemarkVo insurancePracticePayBankDetailRemarkVo) {
        return insurancePracticePayBankConfigService.addInsurancePracticePayBankDetailRemarkVo(insurancePracticePayBankDetailRemarkVo);
    }

    @Override
    public int updateInsurancePracticePayBankDetailRemarkVo(InsurancePracticePayBankDetailRemarkVo insurancePracticePayBankDetailRemarkVo) {
        return insurancePracticePayBankConfigService.updateInsurancePracticePayBankDetailRemarkVo(insurancePracticePayBankDetailRemarkVo);
    }

    @Override
    public List<InsurancePracticePayBankDetailRemarkVo> getInsurancePracticePayBankDetailRemarkVoListByIppbcIdList(List<Long> ippbcIds, Integer lockMonth) {
        return insurancePracticePayBankConfigService.getInsurancePracticePayBankDetailRemarkVoListByIppbcIdList(ippbcIds, lockMonth);
    }
}
