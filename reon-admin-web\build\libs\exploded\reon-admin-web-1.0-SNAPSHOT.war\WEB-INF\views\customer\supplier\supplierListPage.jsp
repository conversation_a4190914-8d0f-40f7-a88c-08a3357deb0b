<%@ page contentType="text/html;charset=UTF-8" language="java" %>
    <%@include file="../../common/taglibs.jsp" %>
        <html>

        <head>
            <meta charset="utf-8">
            <title>供应商管理</title>
            <meta name="renderer" content="webkit">
            <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
            <meta name="apple-mobile-web-app-status-bar-style" content="black">
            <meta name="apple-mobile-web-app-capable" content="yes">
            <meta name="format-detection" content="telephone=no">

            <!-- import CSS -->
            <link rel="stylesheet" href="${ctx}/layui/vue/general.css?v=${publishVersion}" media="all">
            <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
            <!-- import JavaScript -->
            <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
            <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
            <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
            <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
            <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>
            <script src="${ctx}/layui/element-plus/icon.js?v=${publishVersion}"></script>

        </head>

        <body class="childrenBody">
            <div id="app">
                <div class="app-container">
                    <!-- 查询条件 -->
                    <el-form :model="obj.queryParams" inline label-width="auto">
                        <el-form-item label="供应商/联系人:">
                            <el-input class="width220" v-model="obj.queryParams.keyword" placeholder="请输入供应商名称或联系人"
                                clearable></el-input>
                        </el-form-item>
                        <el-form-item label="参保地省级:">
                            <el-select class="width220" filterable v-model="obj.queryParams.provinceCode"
                                placeholder="请选择省份" @change="handleProvinceChange">
                                <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                                    :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="参保地市级:">
                            <el-select class="width220" filterable v-model="obj.queryParams.cityCode"
                                placeholder="请选择城市">
                                <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                                    :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="客服负责人:">
                            <el-input class="width220" v-model="obj.queryParams.commissioner" placeholder="请输入客服负责人"
                                clearable></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                            <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>

                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改
                            </el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                                @click="handleDelete">删除
                            </el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="warning" plain icon="Setting" :disabled="obj.single"
                                @click="handleSupplierArea">社保供应商服务城市分配
                            </el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="success" plain icon="Check" :disabled="obj.multiple"
                                @click="handleEnable">启用
                            </el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="info" plain icon="Close" :disabled="obj.multiple" @click="handleDisable">禁用
                            </el-button>
                        </el-col>
                    </el-row>

                    <!-- 表格 -->
                    <el-table v-loading="obj.loading" show-overflow-tooltip max-height="calc(100vh - 240px)" border
                        :data="obj.tableData" @selection-change="handleSelectionChange"
                        @row-dblclick="handleRowDoubleClick" row-key="id" style="width: 100%">
                        <el-table-column type="selection" align="center" width="50"></el-table-column>
                        <el-table-column label="供应商名称" align="center" prop="supplierName" width="200"></el-table-column>
                        <el-table-column label="供应商类型" align="center" prop="supplierType" width="120">
                            <template #default="scope">
                                {{ getSupplierTypeName(scope.row.supplierType) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" align="center" prop="supplierStatus" width="100">
                            <template #default="scope">
                                {{ getSupplierStatusName(scope.row.supplierStatus) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="联系人" align="center" prop="contactor" width="100"></el-table-column>
                        <el-table-column label="联系方式" align="center" prop="tel" width="120"></el-table-column>
                        <el-table-column label="邮箱" align="center" prop="email" width="180"></el-table-column>
                        <el-table-column label="地址" align="center" prop="addr" width="200"></el-table-column>
                        <el-table-column label="注册时间" align="center" prop="createTime" width="150"></el-table-column>
                        <el-table-column label="采购负责人" align="center" prop="purchaser" width="120">
                            <template #default="scope">
                                {{ getLoginName(scope.row.purchaser) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="客服负责人" align="center" prop="commissioner" width="150">
                            <template #default="scope">
                                <span v-if="scope.row.supplierType == 1" style="color: #409EFF; cursor: pointer;"
                                    @click="handleViewCommissioner(scope.row)">
                                    单击查看供应商客服负责人
                                </span>
                                <span v-else>{{ getLoginName(scope.row.commissioner) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="200" fixed="right">
                            <template #default="scope">
                                <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                                    size="small">查看
                                </el-button>
                                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                                    size="small">修改
                                </el-button>
                                <el-button link type="warning" icon="Setting" @click="handleSupplierArea(scope.row)"
                                    size="small">分配
                                </el-button>
                                <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                                    size="small">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination v-show="obj.total > 0" :current-page="obj.queryParams.page"
                        :page-size="obj.queryParams.limit" :page-sizes="[50, 100, 200]" :total="obj.total" background
                        layout="total, sizes, prev, pager, next, jumper" style="margin-top: 20px; text-align: right;"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
                </div>
            </div>

            <script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="addOne" lay-event="add" authURI="/customer/supplier/gotoSaveSupplierPage">新增</button>
    <button class="layui-btn layui-btn-sm" id="update" lay-event="update" authURI="/customer/supplier/gotoEditSupplierPage">修改</button>
    <button class="layui-btn layui-btn-sm" id="delete" lay-event="delete" authURI="/customer/supplier/delete">删除</button>
   <%-- <button class="layui-btn layui-btn-sm" id="distribute" lay-event="distribute" authURI="/customer/supplier/gotoDistributePage">分配</button>--%>
    <button class="layui-btn layui-btn-sm" id="supplierArea" lay-event="supplierArea" authURI="/customer/supplier/gotoSupplierAreaPage">社保供应商服务城市分配</button>
    <button class="layui-btn layui-btn-sm" id="begin" lay-event="begin" authURI="/customer/supplier/updateStatusNosupplier">启用</button>
    <button class="layui-btn layui-btn-sm" id="" lay-event="disable" authURI="/customer/supplier/updateSatusCancleNosupplier">禁用</button>
<%--    <button class="layui-btn layui-btn-sm" id="export" lay-event="export" authURI="/customer/supplier/export">导出数据</button>
    <button class="layui-btn layui-btn-sm" id="import" lay-event="import" authURI="/customer/supplier/import">参保城市导入</button>--%>
</script>
            <script type="text/jsp" id="toolDemo">
    <a href="javascript:void(0)" title="查看" lay-event="query" authURI="/customer/supplier/gotoQuerySupplierPage"><i class="layui-icon layui-icon-search"></i></a>
    <a href="javascript:void(0)" title="修改" lay-event="update" authURI="/customer/supplier/gotoEditSupplierPage"><i class="layui-icon layui-icon-edit"></i></a>
    <a href="javascript:void(0)" title="社保供应商服务城市分配" lay-event="supplierArea" authURI="/customer/supplier/gotoSupplierAreaPage"><i class="layui-icon layui-icon-username"></i></a>
    <a href="javascript:void(0)" title="删除" lay-event="delete" authURI="/customer/supplier/delete"><i class="layui-icon layui-icon-delete"></i></a>
</script>
            <script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
            <script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
            <script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
            <script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
            <script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
            <script type="text/javascript"
                src="${ctx}/js/modules/customer/supplier/supplierList.js?v=${publishVersion}"></script>
        </body>

        </html>