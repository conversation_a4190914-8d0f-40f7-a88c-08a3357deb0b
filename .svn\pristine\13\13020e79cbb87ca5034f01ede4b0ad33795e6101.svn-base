/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/10/26
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.cmb;

import lombok.Data;

import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @className Resp
 * @description 响应报文
 * @date 2023/10/26 16:26
 */
@Data
public class Response {

    private RespHead head;

    private Map<String,Object> body;
}
