package com.reon.hr.api.customer.enums.employee;

import com.reon.hr.api.customer.enums.BaseEnum;
import com.reon.hr.api.customer.enums.employeeContract.SignStatusEnum;

public enum EmployeeContractType  implements BaseEnum {

    TIME_LABOR_CONTRACT(1, "劳动合同&有固定期限"),
    OPEN_TERM_LABOR_CONTRACT(2, "劳动合同&无固定期限（审批）"),
    INTERNSHIP_CONTRACT(3, "劳务合同&实习（学生证）"),
    LABOR_CONTRACT_RETIREMENT(4, "劳务合同&退休（退休证明）"),
    LABOR_CONTRACT_PART_TIME(5, "劳务合同&兼职（审批）");

    private int code;
    private String name;

    EmployeeContractType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(Integer code) {
        if (code != null){
            for (EmployeeContractType c : EmployeeContractType.values()) {
                if (c.getCode() == code) {
                    return c.getName();
                }
            }
        }
        return null;
    }

}
