package com.reon.ehr.sp.sys.mapper.base;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.vo.base.EhrImportDataVo;
import com.reon.ehr.sp.sys.domain.entity.base.EhrImportData;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface EhrImportDataMapper {
    int insert(EhrImportData ehrImportData);

    int insertSelective(EhrImportData ehrImportData);

    EhrImportDataVo getById(String importNo);

    boolean updateByImportDataNo(@Param("importNo") String importNo, @Param("status") Integer status);

    boolean updateByImportDataDelflag(@Param("importNo") String importNo);

    boolean updateByImportRecordNumber(@Param("importNo") String importNo,@Param("successNum") Integer successNum,
                                   @Param("failNum") Integer failNum);
    boolean updateByImportRecordNumberAndFileId(@Param("importNo") String importNo,@Param("successNum") Integer successNum,
                                   @Param("failNum") Integer failNum, @Param("fileId") String fileId);

    /**
     * 条件 分页查询
     *
     * @param page
     * @return
     */
    List<EhrImportDataVo> getImportDataListPage(Page page,EhrImportDataVo ehrImportDataVo);

    List<EhrImportDataVo> getAllBatchImportData(Page page, Map<String, Object> map);

}