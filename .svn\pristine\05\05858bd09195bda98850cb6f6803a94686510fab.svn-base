package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.CustomerDto;
import com.reon.hr.api.customer.vo.ContractAreaSpecialAttachmentVo;
import com.reon.hr.api.customer.vo.ContractAreaVo;
import com.reon.hr.api.customer.vo.ContractAssignLogVo;
import com.reon.hr.api.customer.vo.EmployeeContract;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayAndCategoryVo;
import com.reon.hr.api.customer.vo.supContractArea.SupContractAreaQueryVo;
import com.reon.hr.api.customer.vo.supContractArea.SupContractAreaRelativeTempVo;
import com.reon.hr.api.customer.vo.supContractArea.SupContractAreaVo;
import com.reon.hr.api.customer.vo.supplier.SupplierQuotationVo;
import com.reon.hr.sp.customer.entity.cus.ContractArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ContractAreaMapper extends BaseMapper<ContractArea> {
    boolean updateBatch(List<ContractArea> list);

    boolean batchInsert(@Param("list") List<ContractAreaVo> list);

    boolean insertOrUpdate(ContractArea record);

    boolean insertOrUpdateSelective(ContractArea record);

    List<ContractAreaVo> getContractAreaPage(@Param("page") Page page, @Param("contractArea") ContractAreaVo contractArea, @Param("currentLogin") String currentLogin,@Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList);


    List<ContractAreaVo> findInfoByContractNo(String contractNo);

    /**
     * 通过合同编号找到区域
     * 通过合同编号找到小合同
     *
     * @param contractAreaVos 合同面积vos
     * @return {@link List}<{@link ContractAreaVo}>
     */
    List<ContractAreaVo> findAreaByContractNo(@Param("list") List<ContractAreaVo> contractAreaVos);

    /**
     * 查询接单方
     *
     * @param customers
     * @return {@link List}<{@link ContractAreaVo}>
     */
    List<ContractAreaVo> findReceiving(@Param("customers") List<CustomerDto> customers);


    List<ContractAreaVo> getContractAreaPages(@Param("page") Page page, @Param("contractArea") ContractAreaVo contractArea, @Param("cityCode") String currentLogin,@Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList);
   /**
    * 禁用*/
    boolean delateByContractNo(String contractNo);

    boolean delateByContractAreaNo(String contractAreaNo);

    /**
     * 根据小合同号删除小合同
     *
     * @param contractAreaNo 合同面积不
     * @return boolean
     */
    boolean deleteByContractAreaNos(@Param("list") List<String> contractAreaNo);

    boolean setFirst(String contractAreaNo);

    /**
     * 根据需要去更新
     *
     * @param contractArea
     * @return
     */
    boolean updateSelective(ContractAreaVo contractArea);

    /**
     * 获取已经分配的小合同
     *
     * @param page
     * @return
     */
    List<ContractAreaVo> getAllContractArea(Page page, @Param("contractArea") ContractAreaVo contractArea, @Param("currentLogin") String currentLogin);
    List<ContractAreaVo> getAllContractAreas(Page page, @Param("contractArea") ContractAreaVo contractArea, @Param("currentLogin") String currentLogin);
    List<ContractAreaVo> getAllContractAreaByTransfer(@Param("contractArea") ContractAreaVo contractArea, @Param("currentLogin") String currentLogin);

    ContractAreaVo fingByContractAreaNo(String contractAreaNo);
    List<ContractAreaVo> fingByContractAreaNos(@Param("contractAreaNos") List<String> contractAreaNos);

    List<ContractAreaVo> getContractAreaList(@Param("contractArea") ContractAreaVo contractArea, @Param("currentLogin") String currentLogin);

    /**
     * 根据分配类型获取合同名称与客户名称
     */
    ContractAssignLogVo searchNameByRelativeNo(@Param("csType") Integer csType, @Param("relativeNo") String relativeNo);

    Integer searchContractAreaOrderByNo(@Param("contractAreaNo") String contractAreaNo);

    /**
     * 批量根据小合同编号查询是否有关联订单
     *
     * @param contractAreaNo 合同面积不
     * @return {@link Integer}
     */
    Integer searchContractAreasOrderByNo(@Param("list") List<String> contractAreaNo);

    /**
     * 修改接单客服经理
     *
     * @param contractArea
     * @return
     */
    boolean updateTransferSelective(ContractAreaVo contractArea);

    /**
     * 批量变更接单专员和主管
     *
     * @param receivingMan
     * @param receivingManOrg
     * @param receivingManPos
     * @param contractAreaNos
     * @return
     */
    int updateRevManByContractAreaNos(@Param("receivingMan") String receivingMan
            , @Param("receivingManOrg") String receivingManOrg
            , @Param("receivingManPos") String receivingManPos
            , @Param("contractAreaNos") Set<String> contractAreaNos);

    /**
     * 查询接单类型为供应商的并且未分配的小合同
     *
     * @param
     * @return
     */
    List<ContractAreaVo> getByReceivingAndRecceivingType(@Param("receiving") String receiving, @Param("recceivingType") Integer recceivingType);
    /**
     * 查询接单类型为供应商的,并且未分配的，以及供应商责任客服被分配的城市与小合同的城市相同的小合同
     *
     * @param
     * @return
     */
    List<ContractAreaVo> getByCityCodeAndReceivingAndRecceivingType(@Param("cityCode") Integer cityCode,@Param("receiving") String receiving, @Param("recceivingType") Integer recceivingType);

    /**
     * 根据账单模板id查询合同编号
     *
     * @param templetId
     * @return
     */
    List<String> getContractNoByTempletId(Long templetId);
    List<ContractAreaVo> getAllAreaByContractNo(String contractNo);
    Integer  insertContractArea(ContractArea record);
    Integer updateContractArea(ContractArea record);
    //boolean ByContractAreaNo(String contractAreaNo);

/**
*@Description: 根据 contractAreaNo list 查询小合同
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2020/12/30 14:14
*/
    List<ContractAreaVo> getContractAreaListByContractNoList(@Param("CANL")String contractAreaNoList);

    List<ContractAreaVo> getContractAreaByNo(@Param("CANL") String paramData);


	List<String> gatAllContractAreaNoList();

    List<ContractAreaVo> selectContractAreaDataByContractNo(@Param("contractNo") String contractNo);

    List<ContractAreaVo> getGatherRevCsByContractAndTempletId(@Param("contractNo") String contractNo, @Param("templetId") Long templetId);

    List<EmployeeContract> getSupplierContractByContractAreaNoList(@Param("contractAreaNoList") List<String> contractAreaNoList);

    List<ContractAreaVo> selectContractAreaListByContractNoList(@Param("list") List<String> contractAreaNoList);


    List<ContractAreaVo> selectByContractAreaNoList(@Param("contractAreaNoList")List<String> contractAreaNoList);

    Integer selectContractNoSizeByContractAreaNo(@Param("contractAreaNo") String contractAreaNo);

    List<EmployeeOrderVo> selectReceivingByEmpIdList(@Param("empIds")List<Long> empIds, @Param("vo")SalaryPayAndCategoryVo salaryPayAndCategoryVo);
    List<EmployeeOrderVo> selectReceivingByEmpIdListAndContractType(@Param("empIds")List<Long> empIds, @Param("vo")SalaryPayAndCategoryVo salaryPayAndCategoryVo);

	ContractAreaVo getContractAreaByContractAreaNo(@Param("contractAreaNo") String contractAreaNo);

	List<ContractAreaVo> getAllContractAreaByContractAreaNoList(@Param("list") List<String> contractAreaNoList);
    List<ContractAreaVo> getContractAreaAndContractByContractAreaNoList(List<String> contractAreaNoList);
    Integer getNmuberOfemployeesByContractAreaNo(@Param("contractAreaNo") String contractAreaNo);

    Integer getCountBySupplierIdList(@Param("supplierIdList") List<Long> supplierIdList);

    Integer getCountBySupplierIdAndCityCodeList(@Param("supplierId") Long supplierId,@Param("cityCodeList") List<String> cityCodeList);

    Integer updateStatusBySupplierIdAndCityCodeList(@Param("supplierId") String supplierId,@Param("cityCodeList") List<String> cityCodeList,@Param("status") Integer status);

    List<ContractAreaVo> getCityCodeByContractAreaNoList(@Param("contractAreaNoList")List<String> contractAreaNoList);

    Integer getRecceivingTypeByContractAreaNo(String contractAreaNo);

    List<ContractAreaVo> getDistributionContractAreaList(@Param("contractAreaNoList")List<String> contractAreaNoList,@Param("currentLogin") String currentLogin);
    /**
     * 获取供应商 小合同与账单模板
     * @param contractAreaNoList
     * @return
     */
    List<SupContractAreaRelativeTempVo> getSupContractAreaAndTemp(@Param("contractAreaNoList")List<String> contractAreaNoList);

    List<SupContractAreaVo> getSupContractAreaPage(@Param("page") Page page, @Param("contractArea") SupContractAreaQueryVo contractArea, @Param("cityCode") String currentLogin, @Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList);

    List<EmployeeContract> getSupplierContractAndServicePriceByContractAreaNoList(@Param("list") List<String> contractAreaNoList);

    List<ContractAreaVo> generateContractArea();

    List<SupplierQuotationVo> getSupContractAreaAndQuo(@Param("contractAreaNoList") List<String> contractAreaNoList);

    List<ContractAreaVo>  getReceivingManByOrderNo(@Param("list") List<String> orderNoList);

    Set<String> getSingleWorkInjuryContractAreaNo(@Param("list") List<String> allContractAreaNoList);

    List<ContractAreaVo> getContractAreaByTempletIdList(@Param("contractNo") String contractNo,@Param("templetIdList") List<Long> templetIdList);

    /** 获取的是收集单的数据 */
    List<ContractAreaVo> getContractAreaByContractAreaToTempletId(@Param("list") List<String> contractNoList);

    Set<String> getCollectContractAreaByTempletId(@Param("templetId") Long templetId);


    ContractAreaVo selectSingleNameByContractAreaNo(@Param("contractAreaNo") String contractAreaNo);

    List<String> getContractAreaNoByReceivingList(@Param("receivingList") List<String> receivingList);

    /**
     * 分页查询特殊附件信息
     * @param page 分页对象
     * @param paramData 查询参数
     * @return 特殊附件列表
     */
    List<ContractAreaSpecialAttachmentVo> getSpecialAttachmentPage(@Param("page") Page<ContractAreaSpecialAttachmentVo> page, @Param("paramData") Map<String, Object> paramData);

    /**
     * 查询特殊附件信息
     * @param paramData 查询参数
     * @return 特殊附件列表
     */
    List<ContractAreaSpecialAttachmentVo> querySpecialAttachmentList(@Param("paramData") Map<String, Object> paramData);


    /**
     * 根据ID查询特殊附件
     * @param id 附件ID
     * @return 附件信息
     */
    ContractAreaSpecialAttachmentVo getSpecialAttachmentById(@Param("id") Long id);

    /**
     * 插入特殊附件
     * @param attachment 特殊附件实体
     * @return 影响行数
     */
    int insertSpecialAttachment(ContractAreaSpecialAttachmentVo attachment);

    /**
     * 更新特殊附件
     * @param attachment 特殊附件实体
     * @return 影响行数
     */
    int updateSpecialAttachment(ContractAreaSpecialAttachmentVo attachment);

    /**
     * 逻辑删除特殊附件（批量更新del_flag）
     * @param ids 附件ID列表
     * @return 影响行数
     */
    int deleteSpecialAttachmentBatch(@Param("ids") List<Long> ids , @Param("updater") String loginName);

    /**
     * 根据对象查询特殊附件
     * @param attachment 查询条件实体
     * @return 特殊附件列表
     */
    List<ContractAreaSpecialAttachmentVo> querySpecialAttachmentByEntity(ContractAreaSpecialAttachmentVo attachment);


}
