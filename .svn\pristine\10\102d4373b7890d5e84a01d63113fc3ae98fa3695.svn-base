package com.reon.hr.sp.customer.service.impl.cus.supplier;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.customer.vo.supplier.supplierBank.SupplierBankSearchVo;
import com.reon.hr.api.customer.vo.supplier.supplierBank.SupplierBankVo;
import com.reon.hr.sp.customer.dao.cus.SupplierMapper;
import com.reon.hr.sp.customer.dao.cus.supplierBank.SupplierBankMapper;
import com.reon.hr.sp.customer.entity.cus.supplierBank.SupplierBank;
import com.reon.hr.sp.customer.service.cus.Supplier.SupplierBankService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> guoqian
 * @date 2021/3/10 0010 18:10
 * @title
 * @modify
 */
@Service
public class SupplierBankServiceImpl implements SupplierBankService {
    @Autowired
    private SupplierBankMapper supplierBankMapper;
    @Autowired
    private SupplierMapper supplierMapper;
    @Override
    public Page<SupplierBankVo> selectSupplierBank(SupplierBankSearchVo vo) {
        Page<SupplierBankVo> page = new Page<>(vo.getPage(),vo.getLimit());
        List<SupplierVo> allSuppliers = supplierMapper.getAllSuppliers(null);
        Map<Long, String> supplierNameMap = allSuppliers.stream().collect(Collectors.toMap(SupplierVo::getId, SupplierVo::getSupplierName));
        List<SupplierBankVo> supplierBankVos = supplierBankMapper.selectSupplierBank(vo,page);
        for (SupplierBankVo supplierBankVo : supplierBankVos) {
            if(supplierNameMap.containsKey(supplierBankVo.getSupplierId())) supplierBankVo.setSupplierName(supplierNameMap.get(supplierBankVo.getSupplierId()));
        }
        page.setRecords(supplierBankVos);
        return page;
    }

    @Override
    public void saveSupplierBank(SupplierBankVo vo) {
        if(Objects.equals(vo.getOptype(),"add")){
            List<SupplierBank> supplierBanks=  supplierBankMapper.selectBySupplierId(vo.getSupplierId()) ;
            if(CollectionUtils.isNotEmpty(supplierBanks)){
                throw new RuntimeException("该供应商已经有银行账户记录无法新增新的账户记录");
            }
            SupplierBank supplierBank =new SupplierBank();
            BeanUtils.copyProperties(vo,supplierBank);
            supplierBankMapper.insertSelective(supplierBank);

        }else if(Objects.equals(vo.getOptype(),"update")){
            SupplierBank supplierBank =new SupplierBank();
            BeanUtils.copyProperties(vo,supplierBank);
            supplierBankMapper.updateByPrimaryKeySelective(supplierBank);

        }

    }

    @Override
    public SupplierBankVo selectSupplierById(SupplierBankSearchVo vo) {
        return supplierBankMapper.selectByPrimaryKey(vo.getId());
    }

    @Override
    public List<SupplierBankVo> getAllSupplier(Long supplierId) {
        return supplierBankMapper.getAllSupplier(supplierId);
    }

    @Override
    public List<SupplierBankVo> getAllSupplier() {
        return supplierBankMapper.getAllSupplier();
    }
}
