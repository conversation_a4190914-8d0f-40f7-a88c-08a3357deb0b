<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.banktrans.ElectReturnOrderFileMapper">

    <sql id="col">
        taskid,service_code,acct_no,re_order_no,trx_seq,yur_ref,returl,create_time,update_time
    </sql>
    <insert id="insertNoExist">
        insert ignore into `elect_return_order_file` ( taskid, service_code, acct_no, trx_seq, yur_ref, returl, create_time, update_time, re_order_no)
               values (#{taskid} ,#{serviceCode},#{acctNo},#{trxSeq},#{yurRef},#{returl},now(),now(),#{reOrderNo})   ;
    </insert>

    <select id="getElectReturnOrderFileVoPage" resultType="com.reon.hr.api.bill.vo.ElectReturnOrderFileVo">
        select t1.*,t2.payment_id from `elect_return_order_file` t1 left join pay_service_serial_log t2 on t1.yur_ref = t2.yurref
        where 1=1
        <if test="fileVo.yurRef != null and fileVo.yurRef != ''">
            AND t1.yur_ref = #{fileVo.yurRef}
        </if>
    </select>

</mapper>
