/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/6/22
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.report.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StaffOnActiveDutyExportVo
 *
 * @date 2021/6/22 15:59
 */
@Data
public class StaffOnActiveDutyExportVo implements Serializable {
	private static final long serialVersionUID = 7493596652263203088L;
	String orderNo;


	/**
	 * 订单编号
	 */
	private String employeeName;
	private String retire;
	private String certNo;
	private String employeeNo;
	private String categoryCode;
	private String categoryCodeName;
	private String custNo;
	private String custName;
	private Long templetId;//帐单模板id
	private String templetName;//帐单模板名称
	private Integer cityCode;
	private String cityName;
	private String contractAreaName;
	private String contractAreaNo;
	private Integer accountFlag;
	private String accountFlagName;
	private String prjCs;
	private String revCs;
	private String receiving;       //接单方id
	private Integer recceivingType;  //接单方类型(0,自有公司，1、供应商公司)
	private Integer totalFeeTime; //总费用时间
	private BigDecimal amount = BigDecimal.ZERO;   // 服务金额
	private BigDecimal archiveFee= BigDecimal.ZERO;//档案费
	private BigDecimal oneFee= BigDecimal.ZERO;// 企业一次性金额
	private BigDecimal indFee= BigDecimal.ZERO;// 个人一次性金额
	private BigDecimal feeTotal= BigDecimal.ZERO;// 个人一次性金额
	private BigDecimal comTotal= BigDecimal.ZERO; //企业小计
	private BigDecimal indTotal= BigDecimal.ZERO; //个人小计
	private BigDecimal rowTotal= BigDecimal.ZERO; // 列小计
	private Integer happenMonth;
	private Map<Integer, OrderInsuranceCfgReportVo> insurances;  //社保公积金产品
	private Map<Integer,OrderInsuranceCfgReportVo> totalPriceMap; //计算的总额

	private String remark1;
	private String remark2;
	private String remark3;
	private String remark4;
	private String remark5;
	private String receivingRemark;
	private String accuAcctNo;

	private String signComTitle;
	private String distCom;

	private String newFlagRemark;

	private BigDecimal socailSalary=BigDecimal.ZERO;
	private BigDecimal acctSalary=BigDecimal.ZERO;
	private String retireDate;
	private String sex;

	private String sinAccName;

	private String certType;

	private String groupName;

}
