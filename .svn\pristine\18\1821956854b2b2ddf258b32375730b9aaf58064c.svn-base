package com.reon.hr.api.bill.vo.salary;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SalaryPayBatchInfoVo implements Serializable {
    private Long id;
    private Long batchId;
    private Date lastDate;
    private Date paymentDate;
    private String paymentContentSummary;
    private String purpose;
    private String remark;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}