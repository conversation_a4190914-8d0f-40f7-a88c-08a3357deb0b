/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/3/19
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.report.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.report.dubbo.service.rpc.IncomeCountTableWrapperService;
import com.reon.hr.api.report.utils.IncomeCountTableExcelUtil;
import com.reon.hr.api.report.vo.IncomeCountTableReportVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.core.utils.StringUtil;
import com.reon.hr.modules.common.BaseController;
import com.reon.hr.modules.customer.controller.EmployeeContractController;
import net.sf.json.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className incomeCountTableController
 *
 * @date 2021/3/19 16:20
 */
@RestController
@RequestMapping("/incomeCountTableReport/")
public class IncomeCountTableController extends BaseController {
	@Autowired
	IncomeCountTableWrapperService incomeCountTableWrapperService;
	@Autowired
	private IContractResourceWrapperService contractResourceWrapperService;

	private static final Logger log = LoggerFactory.getLogger(EmployeeContractController.class);

	@RequestMapping("gotoIncomeCountTableListPage")
	public ModelAndView gotoIncomeCountTableListPage() {
		return new ModelAndView("report/incomeCountTableListPage");
	}

	@RequestMapping("getIncomeCountTableListPage")
	public Object getIncomeCountTableListPage(Integer limit, Integer page, @RequestParam(value = "companyCode", required = false) String company, @RequestParam(value = "createTime", required = false) String createTime) throws ParseException {
		IncomeCountTableReportVo incomeCountTableReportVo = new IncomeCountTableReportVo();
		incomeCountTableReportVo.setCompany(company);
		incomeCountTableReportVo.setCreateTimeFore(createTime);
		Page<IncomeCountTableReportVo> costReportPage = incomeCountTableWrapperService.getIncomeCountTableListPage(limit, page, incomeCountTableReportVo);
		return new LayuiReplay<IncomeCountTableReportVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), costReportPage.getTotal(), costReportPage.getRecords());
	}


	@RequestMapping(value = "exportFile", method = RequestMethod.GET)
	public void getBillContractExportFile(HttpServletResponse response, String vot) {
		String fileName;
		SXSSFWorkbook sxssfWorkbook;
		JSONObject json = JSONObject.fromObject(vot);
		IncomeCountTableReportVo vo = JSON.parseObject(vot, IncomeCountTableReportVo.class);
		if (vo.getSupplierTip() == null){
			vo.setSupplierTip(1);
		}
		 Map<String, Integer> conditionMap = Maps.newHashMap();
		/** 方便拓展
		 */
		conditionMap.put("supplierTip", vo.getSupplierTip());
		try {
		vo.setCreateTimeFore(vo.getReportMonth());
		Map<String,Object> resultMap = incomeCountTableWrapperService.getIncomeCountTableListPageFromReport(vo);
			fileName =   vo.getReportMonth()  + "_" + "收入数据表";
			sxssfWorkbook = IncomeCountTableExcelUtil.getDetailWorkBook(resultMap, fileName,conditionMap);
			IncomeCountTableExcelUtil.closeInfo(response, sxssfWorkbook, fileName);
		} catch (Exception e) {
			e.printStackTrace();
			getLog("导出错误" + e.getMessage());
}
	}

	private <E> E getData(String resource, E target) {
		if (StringUtil.isNotBlank(resource)) {
			target = (E) JsonUtil.jsonToBean(resource, target.getClass());
		}
		return target;
	}


	/**
	 * 日志方法
	 */
	private void getLog(String LogData) {
		String loginName = getSessionUser().getLoginName();
		log.info(loginName + " -- " + LogData);
	}

	@RequestMapping("gotoIncomeCountReportRestore")
	public ModelAndView gotoIncomeCountReportRestore() {
		return new ModelAndView("report/incomeCountTableRestore/incomeCountTableRestore");
	}

	private final String CONTRACT_NO = "contractNo";
	private final String TEMPLET_ID = "templetId";
	private final String BILL_MONTH = "billMonth";
	private final String OPT_TYPE = "optType";
	private final String START_DATE = "startDate";
	private final String END_DATE = "endDate";

	@RequestMapping("restoreData")
	public Object restoreData(@RequestParam(value = "param") String param) {
		Long templetId = null;
		String contractNo = "";
		Integer billMonth = null;
		/** contractNo templetId billMonth */
		Map<String, String> paramMap = JSON.parseObject(param, Map.class);
		/** 根据传过来的数据获取核销数据 */
		if (StringUtil.isNotBlank(paramMap.get(CONTRACT_NO)))
			contractNo = paramMap.get(CONTRACT_NO);
		if (StringUtil.isNotBlank(paramMap.get(TEMPLET_ID)))
			templetId = Long.valueOf(paramMap.get(TEMPLET_ID));
		if (StringUtil.isNotBlank(paramMap.get(BILL_MONTH)))
			billMonth = Integer.valueOf(paramMap.get(BILL_MONTH));
		Date startDate = null;
		Date endDate = null;
		if (StringUtils.isNotEmpty(paramMap.get(START_DATE)))
			startDate = DateUtil.parseStringToDate(paramMap.get(START_DATE) + " 00:00:00", DateUtil.DATE_FORMAT_LONG);
		if (StringUtils.isNotEmpty(paramMap.get(END_DATE)))
			endDate = DateUtil.parseStringToDate(paramMap.get(END_DATE) + " 23:59:59", DateUtil.DATE_FORMAT_LONG);
		incomeCountTableWrapperService.restoreData(contractNo, templetId, billMonth, startDate, endDate);
		return LayuiReplay.success();
	}

	@RequestMapping("delIncomeDateByDelDate")
	public Object delIncomeDateByDelDate(@RequestParam(value = "delDate") String delDate) {
		Integer size = incomeCountTableWrapperService.deleteIncomeCountTableAndIntermidiateByDelDate(delDate);
		return LayuiReplay.success();
	}


}
