/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/4/6
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.ehr.sp.sys.dubbo.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.dubbo.service.rpc.ISysConfigWrapperService;
import com.reon.ehr.api.sys.vo.SysConfigVo;
import com.reon.ehr.sp.sys.domain.entity.SysConfig;
import com.reon.ehr.sp.sys.service.sys.ISysConfigService;
import com.reon.hr.common.utils.VoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SysConfigWrapperServiceImpl
 * @description TODO
 * @date 2023/4/6 19:02
 */
@Service("sysConfigWrapperService")
public class SysConfigWrapperServiceImpl implements ISysConfigWrapperService {

    @Autowired
    private ISysConfigService sysConfigService;
    @Override
    public SysConfigVo selectConfigById(Long configId) {
        return VoUtil.copyProperties(sysConfigService.selectConfigById(configId),SysConfigVo.class);
    }

    @Override
    public String selectConfigByKey(String configKey) {
        return sysConfigService.selectConfigByKey(configKey);
    }

    @Override
    public boolean selectCaptchaEnabled() {
        return sysConfigService.selectCaptchaEnabled();
    }

    @Override
    public List<SysConfigVo> selectConfigList(SysConfigVo config) {
        SysConfig sysConfig = VoUtil.copyProperties(config, SysConfig.class);
        return VoUtil.copyProperties(sysConfigService.selectConfigList(sysConfig),SysConfigVo.class);
    }

    @Override
    public Page<SysConfigVo> getConfigListPage(Integer pageNum, Integer pageSize, SysConfigVo config) {
        return sysConfigService.getConfigListPage(pageNum,pageSize,config);
    }

    @Override
    public int insertConfig(SysConfigVo config) {
        return sysConfigService.insertConfig(VoUtil.copyProperties(config, SysConfig.class));
    }

    @Override
    public int updateConfig(SysConfigVo config) {
        return sysConfigService.updateConfig(VoUtil.copyProperties(config, SysConfig.class));
    }

    @Override
    public void deleteConfigByIds(Long[] configIds) {
        sysConfigService.deleteConfigByIds(configIds);
    }

    @Override
    public void loadingConfigCache() {
        sysConfigService.loadingConfigCache();
    }

    @Override
    public void clearConfigCache() {
        sysConfigService.clearConfigCache();
    }

    @Override
    public void resetConfigCache() {
        sysConfigService.resetConfigCache();
    }

    @Override
    public boolean checkConfigKeyUnique(SysConfigVo config) {
        return sysConfigService.checkConfigKeyUnique(VoUtil.copyProperties(config, SysConfig.class));
    }
}
