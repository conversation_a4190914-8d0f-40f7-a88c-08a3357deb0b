package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.customer.vo.ContractAttachmentVo;
import com.reon.hr.api.customer.vo.MeetingRecordVo;
import com.reon.hr.sp.customer.entity.cus.MeetingRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

public interface MeetingRecordMapper extends BaseMapper<MeetingRecord> {
    boolean updateBatch(List<MeetingRecord> list);

    boolean batchInsert(@Param("list") List<MeetingRecord> list);

    boolean insertOrUpdate(MeetingRecord record);

    boolean insertOrUpdateSelective(MeetingRecord record);

    List<MeetingRecordVo> findByContractNo(@Param("contractNo")String contractNo);

    List<ContractAttachmentVo> findFileIdByContractNo(String contractNo);
    boolean updateFileIdByContractNo(@Param("contractNo") String contractNo,@Param("fileId") String fileId);

    boolean delByContractNo(String contractNo);
}
