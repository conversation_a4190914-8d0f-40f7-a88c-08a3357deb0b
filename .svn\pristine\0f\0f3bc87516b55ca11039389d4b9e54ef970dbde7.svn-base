<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.employee.EmployeeEntryDimissionMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission">
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, entry_date, entry_remark, add_reason, apply_entry_time, apply_entry_man, 
    apply_dimission_date, dimission_date, apply_dimission_man, dimission_remark, suspend_reason,
    suspend_remark, status,dimission_reason,reduce_reason,reduce_detail_reason,dim_material,
    creator, create_time, updater, update_time, del_flag,expired_month
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from employee_entry_dimission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from employee_entry_dimission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission">
    insert into employee_entry_dimission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="entryDate != null">
        entry_date,
      </if>
      <if test="entryRemark != null">
        entry_remark,
      </if>
      <if test="addReason != null">
        add_reason,
      </if>
      <if test="applyEntryTime != null">
        apply_entry_time,
      </if>
      <if test="applyEntryMan != null">
        apply_entry_man,
      </if>
      <if test="applyDimissionDate != null">
        apply_dimission_date,
      </if>
      <if test="dimissionDate != null">
        dimission_date,
      </if>
      <if test="applyDimissionMan != null">
        apply_dimission_man,
      </if>
      <if test="dimissionRemark != null">
        dimission_remark,
      </if>
      <if test="suspendReason != null">
        suspend_reason,
      </if>
      <if test="suspendRemark != null">
        suspend_remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="dimissionReason!=null">
        dimission_reason,
      </if>
      <if test="reduceReason">
        reduce_reason,
      </if>
      <if test="reduceDetailReason">
        reduce_detail_reason,
      </if>
      <if test="dimMaterial">
        dim_material,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="entryDate != null">
        #{entryDate,jdbcType=DATE},
      </if>
      <if test="entryRemark != null">
        #{entryRemark,jdbcType=VARCHAR},
      </if>
      <if test="addReason != null">
        #{addReason,jdbcType=INTEGER},
      </if>
      <if test="applyEntryTime != null">
        #{applyEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyEntryMan != null">
        #{applyEntryMan,jdbcType=VARCHAR},
      </if>
      <if test="applyDimissionDate != null">
        #{applyDimissionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dimissionDate != null">
        #{dimissionDate,jdbcType=DATE},
      </if>
      <if test="applyDimissionMan != null">
        #{applyDimissionMan,jdbcType=VARCHAR},
      </if>
      <if test="dimissionRemark != null">
        #{dimissionRemark,jdbcType=VARCHAR},
      </if>
      <if test="suspendReason != null">
        #{suspendReason,jdbcType=INTEGER},
      </if>
      <if test="suspendRemark != null">
        #{suspendRemark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="dimissionReason!=null">
        #{dimissionReason,jdbcType=INTEGER},
      </if>
      <if test="reduceReason">
        #{reduceReason,jdbcType=INTEGER},
      </if>
      <if test="reduceDetailReason">
        #{reduceDetailReason,jdbcType=VARCHAR},
      </if>
      <if test="dimMaterial">
        #{dimMaterial,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>

  <update id="updateApplyConfirmTimeByOrderNo">
    update employee_entry_dimission set apply_confirm_time = #{date} where order_no = #{orderNo}
  </update>
  <update id="updateByOrderNoSelective" parameterType="com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission">
    update employee_entry_dimission
    <set>
      <if test="entryDate != null">
        entry_date = #{entryDate,jdbcType=DATE},
      </if>
      <if test="entryRemark != null">
        entry_remark = #{entryRemark,jdbcType=VARCHAR},
      </if>
      <if test="addReason != null">
        add_reason = #{addReason,jdbcType=INTEGER},
      </if>
      <if test="applyEntryTime != null">
        apply_entry_time = #{applyEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyEntryMan != null">
        apply_entry_man = #{applyEntryMan,jdbcType=VARCHAR},
      </if>
      <if test="applyDimissionDate != null">
        apply_dimission_date = #{applyDimissionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dimissionDate != null">
        dimission_date = #{dimissionDate,jdbcType=DATE},
      </if>
      <if test="applyDimissionMan != null">
        apply_dimission_man = #{applyDimissionMan,jdbcType=VARCHAR},
      </if>
      <if test="dimissionRemark != null">
        dimission_remark = #{dimissionRemark,jdbcType=VARCHAR},
      </if>
      <if test="suspendReason != null">
        suspend_reason = #{suspendReason,jdbcType=INTEGER},
      </if>
      <if test="suspendRemark != null">
        suspend_remark = #{suspendRemark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="dimissionReason!=null">
        dimission_reason=#{dimissionReason,jdbcType=INTEGER},
      </if>
      <if test="reduceReason">
        reduce_reason=#{reduceReason,jdbcType=INTEGER},
      </if>
      <if test="reduceDetailReason">
        reduce_detail_reason=#{reduceDetailReason,jdbcType=VARCHAR},
      </if>
      <if test="dimMaterial">
        dim_material=#{dimMaterial,jdbcType=VARCHAR},
      </if>
      <if test="entryConfirmTime!=null">
        entry_confirm_time=#{entryConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="callFlag != null">
        call_flag = #{callFlag},
      </if>
        <if test="dimissionDate != null">
            dimission_date_for_bill= #{dimissionDate,jdbcType=DATE},
        </if>
      <if test="expiredMonth">
        expired_month = #{expiredMonth}
      </if>

    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </update>
  <update id="deleteByOrderNos">
    update employee_entry_dimission
    set del_flag='Y',updater=#{updater}
    where order_no in
      <foreach collection="orderNos" index="index" item="item" open="(" separator="," close=")">
        (#{item})
      </foreach>
  </update>
  <select id="selectByOrderNo" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from employee_entry_dimission
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByOrderNos" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from employee_entry_dimission
    where order_no in
    <foreach collection="list" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
  </select>

  <select id="getIdStatusByOrderNo" resultType="java.lang.String">
     select id_status from employee_entry_dimission where order_no =#{orderNo}
  </select>


  <update id="updateBatchByOrderNo" parameterType="java.util.List">
    update employee_entry_dimission
    <trim prefix="set" suffixOverrides=",">
        <trim prefix="dimission_date = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                <if test="item.dimissionDate !=null">
                    when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.dimissionDate,jdbcType=DATE}
                </if>
            </foreach>
        </trim>
      <trim prefix="expired_month = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expiredMonth !=null">
          when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.expiredMonth,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="dimission_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dimissionReason !=null">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.dimissionReason,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="call_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.callFlag !=null">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.callFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="reduce_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reduceReason !=null">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.reduceReason,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="reduce_detail_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reduceDetailReason !=null and item.reduceDetailReason!=''">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.reduceDetailReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dimission_remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dimissionRemark !=null and item.dimissionRemark!=''">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.dimissionRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status !=null">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
        <if test="item.updater!=null and item.updater!=''">
          when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.updater,jdbcType=VARCHAR}
        </if>
        </foreach>
      </trim>
      <trim prefix="apply_dimission_man = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
        <if test="item.applyDimissionMan!=null and item.applyDimissionMan!=''">
          when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{item.applyDimissionMan,jdbcType=VARCHAR}
        </if>
        </foreach>
      </trim>
      <trim prefix="apply_dimission_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when order_no = #{item.orderNo,jdbcType=VARCHAR} then now()
        </foreach>
      </trim>
    </trim>
    where order_no in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.orderNo,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="updataIdStatus">
    update `reon-customerdb`.employee_entry_dimission set id_status = "Y" where order_no in
    <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
      #{orderNo}
    </foreach>
  </update>


  <update id="updateApplyDimissionDateByOrderNo" parameterType="java.util.List">
    update employee_entry_dimission
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="updater!=null and updater!=''">
            when order_no = #{item.orderNo,jdbcType=VARCHAR} then #{updater,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="apply_dimission_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when order_no = #{item.orderNo,jdbcType=VARCHAR} then now()
        </foreach>
      </trim>
    </trim>
    where order_no in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.orderNo,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="removeApplyDimissionDateByOrderNoSet">
    update employee_entry_dimission set apply_dimission_date = null where
    order_no in
    <foreach collection="orderNoSet" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </update>

  <select id="selectAllEntryDimissionByEmpIdList"
          resultType="com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission">
    select id, order_no, entry_date, entry_remark, add_reason, apply_entry_time, apply_entry_man,
    apply_dimission_date, dimission_date, apply_dimission_man, dimission_remark, suspend_reason,
    suspend_remark, status,dimission_reason,reduce_reason,reduce_detail_reason,dim_material,
    creator, create_time, updater, update_time, del_flag,expired_month,employee_id from (
            select
            id, eed.order_no, entry_date, entry_remark, add_reason, apply_entry_time, apply_entry_man,
            apply_dimission_date, dimission_date, apply_dimission_man, dimission_remark, suspend_reason,
            suspend_remark, eed.status,dimission_reason,reduce_reason,reduce_detail_reason,dim_material,
            eed.creator, eed.create_time, eed.updater, eed.update_time, eed.del_flag,expired_month,eo.employee_id
            from employee_entry_dimission eed
            LEFT JOIN employee_order eo on eo.order_no=eed.order_no
            where eed.del_flag = 'N' and eo.del_flag='N' and
            eo.employee_id in
            <foreach collection="list" separator="," open="(" close=")" item="item">
              #{item}
            </foreach>
            ORDER BY eed.apply_entry_time desc LIMIT 10000000
    )a GROUP BY a.employee_id
  </select>

  <select id="findAllDimissionRemarkByOrderNo" resultType="com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo">
      select dimission_remark, order_no
      from employee_entry_dimission
      where status = 2
        and order_no in
    <foreach collection="list" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </select>
  <select id="getAllOrderNoByContractNo" resultType="java.lang.String">
      select eo.order_no from `reon-customerdb`.employee_order eo left join employee_entry_dimission eed on eo.order_no = eed.order_no
where eed.status = 3 and  eo.contract_no = #{contractNo};
  </select>
  <select id="adjustDimissionByOrderNo" resultType="java.lang.String">
    select order_no from employee_entry_dimission where status = 3 and dimission_date <![CDATA[ < ]]>    #{time}  and
    order_no
    in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="selectEntryDimissionDateByEmpIdList"
          resultType="com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo">
    select distinct
    entry_date,dimission_date dimission_date_d,eed.status,eo.employee_id,eo.cust_id
    from employee_entry_dimission eed
    LEFT JOIN employee_order eo on eo.order_no=eed.order_no
    where eed.del_flag = 'N' and eo.del_flag='N' and
    eo.employee_id in
    <foreach collection="list" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </select>


  <select id="selectByOrderNosAndStatus" resultType="com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo">
    select
    <include refid="Base_Column_List" />
    from employee_entry_dimission
    where status = #{status} and order_no in
    <foreach collection="list" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
  </select>


  <update id="updateSaveTypeAndRemarkByOrderNo">
      update employee_entry_dimission
      set save_type  =#{saveType},
          dimission_remark =#{remark}
      where order_no = #{orderNo}
  </update>

  <update id="updateApplyEntryTimeByOrderNo">
    update employee_entry_dimission set apply_entry_time =now() where order_no=#{orderNo}
  </update>
</mapper>