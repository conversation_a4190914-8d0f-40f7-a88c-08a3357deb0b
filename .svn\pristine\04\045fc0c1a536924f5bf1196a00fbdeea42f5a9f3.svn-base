var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        tableSelect = layui.tableSelect,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    $(document).ready(function () {
    })

    $('#btnQuery').on('click', function () {
        $.ajax({
            url: ctx + "/thirdPart/cmb/queryBusinessModals?buscod=" +$('#buscod').val(),
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            success: function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    let dataStr = result.data;
                    let data = JSON.parse(result.data);
                    table.reload('queryBusinessModalsPage',{data: data.ntqmdlstz});
                }
            },
            error: function (resp, textStatus, errorThrown) {
                ML.ajaxErrorCallback(resp, textStatus, errorThrown);
            }
        });

    });

    table.render({
        id: 'queryBusinessModalsPage',
        elem: '#QueryGridTable'
        , data: []
        , height: 'full-200'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , limit: 50
        , method: 'GET'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            var that = this.elem.next();
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }

        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'busmod', title: '业务模式编号', align: 'center', width: '150'}
            , {field: 'modals', title: '业务模式名称', align: 'center', width: '150'}
        ]]
    });




});