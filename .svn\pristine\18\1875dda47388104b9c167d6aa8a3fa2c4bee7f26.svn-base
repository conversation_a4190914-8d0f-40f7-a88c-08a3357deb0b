package com.reon.hr.modules.customer.controller.salary.salaryItem;

import com.alibaba.fastjson.JSON;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.customer.vo.supplier.NoSupplierVo;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

public class SalaryItemInsuTemCfgReportControllerTest {
	@Resource(name = "sequenceDubboService")
	private ISequenceService sequenceService;
	@Test
	public void aaa(){
		String salaryItemNo = sequenceService.getSalaryItemNo();
		System.out.println(salaryItemNo);
	}


}