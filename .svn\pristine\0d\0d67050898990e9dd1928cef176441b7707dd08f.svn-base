<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.ehr.sp.sys.mapper.HolidaysMapper">

    <resultMap type="com.reon.ehr.api.sys.vo.HolidaysVo" id="HolidaysMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="holidayName" column="holiday_name" jdbcType="VARCHAR"/>
        <result property="date" column="date" jdbcType="TIMESTAMP"/>
        <result property="year" column="year" jdbcType="INTEGER"/>
        <result property="month" column="month" jdbcType="INTEGER"/>
        <result property="day" column="day" jdbcType="INTEGER"/>
        <result property="festivalFlag" column="festival_flag" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="HolidaysMap">
        select id,
               holiday_name,
               date,
               year,
               month,
               day,
               festival_flag,
               status

        from holidays
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="HolidaysMap">
        select
        id, holiday_name, date, year, month, day, festival_flag, status
        from holidays
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="holidayName != null and holidayName != ''">
                and holiday_name = #{holidayName}
            </if>
            <if test="date != null">
                and date = #{date}
            </if>
            <if test="year != null">
                and year = #{year}
            </if>
            <if test="month != null">
                and month = #{month}
            </if>
            <if test="day != null">
                and day = #{day}
            </if>
            <if test="festivalFlag != null">
                and festival_flag = #{festivalFlag}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <!--查询指定行数据-->
    <select id="queryByDate" resultMap="HolidaysMap">
        select
        id, holiday_name, date, year, month, day, festival_flag, status
        from holidays
        <where>
            <if test="holidayName != null and holidayName != ''">
                and holiday_name = #{holidayName}
            </if>
            <if test="date != null">
                and date = #{date}
            </if>
            <if test="dateStart != null">
                and date <![CDATA[ >= ]]> #{dateStart}
            </if>
            <if test="dateEnd != null">
                and date <![CDATA[ <= ]]> #{dateEnd}
            </if>
            <if test="year != null">
                and year = #{year}
            </if>
            <if test="month != null">
                and month = #{month}
            </if>
            <if test="day != null">
                and day = #{day}
            </if>
            <if test="festivalFlag != null">
                and festival_flag = #{festivalFlag}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="queryHolidaysByDate" resultMap="HolidaysMap">
        SELECT h.*
        FROM holidays h
        WHERE h.holiday_name IN (
            -- 场景1: 时间段内的假期名称
            SELECT DISTINCT holiday_name
            FROM holidays
            WHERE date BETWEEN #{dateS} AND #{dateE}

            UNION

            -- 场景2: 结束日期后一天是假期，且结束日期是工作日
            SELECT h1.holiday_name
            FROM holidays h1
            WHERE h1.date = DATE_ADD(#{dateE}, INTERVAL 1 DAY)
              AND (
                -- 判断结束日期是否为工作日
                EXISTS (
                    SELECT 1
                    FROM holidays h_work
                    WHERE h_work.date = #{dateE}
                      AND h_work.status = 2  -- 补班日
                )
                    OR (
                    NOT EXISTS (  -- 自然工作日（不在假期表中）
                        SELECT 1
                        FROM holidays h_nonwork
                        WHERE h_nonwork.date = #{dateE}
                    )
                        AND WEEKDAY(#{dateE}) BETWEEN 0 AND 4  -- 周一到周五
                    )
                )
        )
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from holidays
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="holidayName != null and holidayName != ''">
                and holiday_name = #{holidayName}
            </if>
            <if test="date != null">
                and date = #{date}
            </if>
            <if test="year != null">
                and year = #{year}
            </if>
            <if test="month != null">
                and month = #{month}
            </if>
            <if test="day != null">
                and day = #{day}
            </if>
            <if test="festivalFlag != null">
                and festival_flag = #{festivalFlag}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into holidays
            (holiday_name, date, year, month, day, festival_flag, status)
        values (#{holidayName}, #{date}, #{year}, #{month}, #{day}, #{festivalFlag}, #{status})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into holidays(holiday_name, date, year, month, day, festival_flag, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.holidayName}, #{entity.date}, #{entity.year}, #{entity.month}, #{entity.day},
            #{entity.festivalFlag}, #{entity.status})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into holidays(holiday_name, date, year, month, day, festival_flag, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.holidayName}, #{entity.date}, #{entity.year}, #{entity.month}, #{entity.day},
            #{entity.festivalFlag}, #{entity.status})
        </foreach>
        on duplicate key update
        holiday_name = values(holiday_name),
        date = values(date),
        year = values(year),
        month = values(month),
        day = values(day),
        festival_flag = values(festival_flag),
        status = values(status)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update holidays
        <set>
            <if test="holidayName != null and holidayName != ''">
                holiday_name = #{holidayName},
            </if>
            <if test="date != null">
                date = #{date},
            </if>
            <if test="year != null">
                year = #{year},
            </if>
            <if test="month != null">
                month = #{month},
            </if>
            <if test="day != null">
                day = #{day},
            </if>
            <if test="festivalFlag != null">
                festival_flag = #{festivalFlag},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--批量修改数据-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="entities" item="item" index="index" separator=";">
            update holidays
            <set>
                <if test="item.holidayName != null and item.holidayName != ''">
                    holiday_name = #{item.holidayName},
                </if>
                <if test="item.date != null">
                    date = #{item.date},
                </if>
                <if test="item.year != null">
                    year = #{item.year},
                </if>
                <if test="item.month != null">
                    month = #{item.month},
                </if>
                <if test="item.day != null">
                    day = #{item.day},
                </if>
                <if test="item.festivalFlag != null">
                    festival_flag = #{item.festivalFlag},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from holidays
        where id = #{id}
    </delete>

</mapper>

