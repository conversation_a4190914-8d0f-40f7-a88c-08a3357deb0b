package com.reon.hr.sp.customer.service.impl.salary.taxDeclarationInformation;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.vo.NationalityVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation.*;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.SalaryItemEnum;
import com.reon.hr.api.customer.enums.employee.EmpCardInfoBankNameEnum;
import com.reon.hr.api.customer.enums.employee.EmpRelativeSex;
import com.reon.hr.api.customer.enums.salary.*;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.employee.EmployeeVo;
import com.reon.hr.api.customer.vo.salary.ExcessCalculationAmountDetailVo;
import com.reon.hr.api.customer.vo.salary.pay.EmpTaxDeductionVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryBatchDetailVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.common.enums.BooleanEnum;
import com.reon.hr.sp.customer.dao.employee.EmployeeMapper;
import com.reon.hr.sp.customer.dao.salary.*;
import com.reon.hr.sp.customer.service.employee.salary.taxDeclarationInformation.TaxDeclarationInformationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2023/3/2.
 */
@Service
public class TaxDeclarationInformationServiceImpl implements TaxDeclarationInformationService {
    @Autowired
    private SalaryPayMapper salaryPayMapper;
    @Autowired
    private SalaryInfoMapper salaryInfoMapper;
    @Autowired
    private SpecImportInfoMapper specImportInfoMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private IAreaResourceWrapperService areaResourceWrapperService;
    @Autowired
    private SalaryBatchDetailMapper salaryBatchDetailMapper;
    @Autowired
    private IPaymentApplyWrapperService iPaymentApplyWrapperService;
    @Autowired
    private ExcessCalculationAmountDetailMapper excessCalculationAmountDetailMapper;
    public static final String YES_YES="是,是";
    public static final String YES_NO="是,否";
    public static final String NO_YES="否,是";
    public static final String NO_NO="否,否";
    public static final String REAL="real";
    @Override
    public Page<TaxDeclarationInformationImportDto> getTaxDeclarationInformationListPage(Integer page, Integer limit, TaxDeclarationInformationDto vo) {
        Page<TaxDeclarationInformationImportDto> taxDeclarationInformationImportDtoPage=new Page<>(page,limit);
        List<TaxDeclarationInformationImportDto> taxDeclarationInformationDtoList = getTaxDeclarationInformationListExport(vo);
        taxDeclarationInformationImportDtoPage.setTotal(taxDeclarationInformationDtoList.size());
        List<TaxDeclarationInformationImportDto> list=new ArrayList<>();
        for (int i = (page-1)*limit; i < taxDeclarationInformationDtoList.size()&&i<=page*limit; i++) {
            list.add(taxDeclarationInformationDtoList.get(i));
        }
        taxDeclarationInformationImportDtoPage.setRecords(list);
        return taxDeclarationInformationImportDtoPage;
    }
    public void setSalaryTaxType(TaxDeclarationInformationDto vo){
        if(vo.getSalaryTaxType()!=null){
            if(vo.getSalaryTaxType()== SalaryTaxTypeEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE_CONTINUE.getCode()){
                vo.setSalaryTaxType(TaxTableEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE.getCode());
                vo.setLaborWagesType(LaborWagesTypeEnum.CONTINUOUS.getCode());
            }else if(vo.getSalaryTaxType()== SalaryTaxTypeEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE_NO_CONTINUE.getCode()){
                vo.setSalaryTaxType(TaxTableEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE.getCode());
                vo.setLaborWagesType(LaborWagesTypeEnum.DISCONTINUOUS.getCode());
            }
        }
    }
    @Override
    public List<TaxDeclarationInformationImportDto> getTaxDeclarationInformationListExport(TaxDeclarationInformationDto vo) {
        Integer taxMonth = vo.getTaxMonth();
        Integer lastTaxMonth=DateUtil.getYearMonthByCount(taxMonth, -1);
        List<Integer> taxMonthList=new ArrayList<>();
        taxMonthList.add(taxMonth);
        taxMonthList.add(lastTaxMonth);
        vo.setTaxMonthList(taxMonthList);
        setSalaryTaxType(vo);
        List<TaxDeclarationInformationImportDto> taxDeclarationInformationDtoList=salaryPayMapper.getTaxDeclarationInformationListExport(vo);

        List<Long> empIdList = taxDeclarationInformationDtoList.stream().map(TaxDeclarationInformationImportDto::getEmployeeId).collect(Collectors.toList());
        List<String> withholdingAgentNoList = taxDeclarationInformationDtoList.stream().map(TaxDeclarationInformationImportDto::getWithholdingAgentNo).collect(Collectors.toList());
        Map<String,EmpTaxDeductionVo> empTaxDeductionVoMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(empIdList)&&CollectionUtils.isNotEmpty(withholdingAgentNoList)){
            List<EmpTaxDeductionVo> empTaxDeductionVoList = specImportInfoMapper.getByMonthListAndEmpIdList(empIdList, taxMonthList, withholdingAgentNoList).stream().distinct().collect(Collectors.toList());
            empTaxDeductionVoMap = empTaxDeductionVoList.stream().collect(Collectors.toMap(empTaxDeductionVo ->empTaxDeductionVo.getWithholdingAgentNo()+"-"
                    + empTaxDeductionVo.getEmpId() + "-" + empTaxDeductionVo.getIncomeMonth(), v -> v));
        }
        Map<Integer, String> nationalityMap = areaResourceWrapperService.findAllNationality().stream().collect(Collectors.toMap(NationalityVo::getCode, NationalityVo::getName));
        Map<Long, EmployeeVo> employeeVoMap =new HashMap<>();
        Map<String, String> minTaxMonthDayMap =new HashMap<>();
        if(CollectionUtils.isNotEmpty(empIdList)){
            List<EmployeeVo> employeeVoList=employeeMapper.getSpecialByEmpIdList(empIdList);
            employeeVoMap = employeeVoList.stream().collect(Collectors.toMap(EmployeeVo::getId, Function.identity()));
            minTaxMonthDayMap = getMinTaxMonthDayMap(empIdList, taxMonth);
        }
        Map<String, TaxDeclarationInformationImportDto> map = taxDeclarationInformationDtoList.stream().collect(Collectors.toMap(taxDeclarationInformationImportDto ->
                taxDeclarationInformationImportDto.getWithholdingAgentNo() + "-" + taxDeclarationInformationImportDto.getEmployeeId()
                        + "-" + taxDeclarationInformationImportDto.getTaxMonth(), Function.identity(), (key1, key2) -> {
            //5.多批计算的时候入离职时间取，第一次的，
            if(key1.getPayId().compareTo(key2.getPayId())<0){
                return key1;
            }
            return key2;
        }));
        List<TaxDeclarationInformationImportDto> taxDeclarationInformationImportDtoList=new ArrayList<>();
        List<EmployeeVo> employeeUpdateVoList=new ArrayList<>();
        for (String key:map.keySet()) {
            TaxDeclarationInformationImportDto taxDeclarationInformationImportDto = map.get(key);
            String newKey = taxDeclarationInformationImportDto.getWithholdingAgentNo() + "-" + taxDeclarationInformationImportDto.getEmployeeId() + "-";
            String lastKey =newKey + lastTaxMonth;
            String nowKey =newKey + taxMonth;
            String depositBank = taxDeclarationInformationImportDto.getDepositBank();
            if(StringUtil.isNotBlank(depositBank)){
                taxDeclarationInformationImportDto.setDepositBank(EnumsUtil.getNameByCode(Integer.parseInt(depositBank), EmpCardInfoBankNameEnum.class));
            }
            //taxDeclarationInformationImportDto.setCertType(EnumsUtil.getNameByCode(Integer.parseInt(taxDeclarationInformationImportDto.getCertType()), EmployeeCertType.class));


            //从emp拉出来的
            EmployeeVo employeeVo = employeeVoMap.get(taxDeclarationInformationImportDto.getEmployeeId());
            if(employeeVo.getSalaryCertType()!=null){
                taxDeclarationInformationImportDto.setCertType(EnumsUtil.getNameByCode(employeeVo.getSalaryCertType(), SalaryCertType.class));
            }
            if(StringUtil.isNotBlank(taxDeclarationInformationImportDto.getOtherName())){
                taxDeclarationInformationImportDto.setEmployeeName(taxDeclarationInformationImportDto.getOtherName());
            }

            if(StringUtil.isNotBlank(taxDeclarationInformationImportDto.getOtherCertNo())){
                taxDeclarationInformationImportDto.setCertNo(taxDeclarationInformationImportDto.getOtherCertNo());
            }
            taxDeclarationInformationImportDto.setOtherDocumentsTypes(SalaryCertTypeSubType.getNameByCodeAndParentCode(employeeVo.getSalaryCertTypeSubType(),employeeVo.getSalaryCertType()));
            taxDeclarationInformationImportDto.setOtherDocumentNumber(employeeVo.getOtherCertNo());
            taxDeclarationInformationImportDto.setNationalityOrRegion(nationalityMap.get(employeeVo.getNationality()));
            taxDeclarationInformationImportDto.setSex(EnumsUtil.getNameByCode(employeeVo.getGender(), EmpRelativeSex.class));
            taxDeclarationInformationImportDto.setBirthday(DateUtil.formatDateToString(employeeVo.getBirthDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
            taxDeclarationInformationImportDto.setTaxRelatedMatters(EnumsUtil.getNameByCode(employeeVo.getTaxRelatedMatters(), TaxRelatedMattersEnum.class));
            taxDeclarationInformationImportDto.setFirstEntryTime(DateUtil.formatDateToString(employeeVo.getFirstEntryTime(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
            taxDeclarationInformationImportDto.setDepartureTimeEstimated(DateUtil.formatDateToString(employeeVo.getEstimatedTimeOfDeparture(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
            taxDeclarationInformationImportDto.setBirthCountryRegion(nationalityMap.get(employeeVo.getBirthCountry()));
            String employmentType = EnumsUtil.getNameByCode(employeeVo.getEmploymentType(), EmploymentTypeEnum.class);
            String otherCircumstancesExplanations=null;
            if(StringUtil.isBlank(employmentType)){
                if(taxDeclarationInformationImportDto.getTaxListId().equals(TaxTableEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE.getCode())){
                    if(taxDeclarationInformationImportDto.getLaborWagesType().equals(LaborWagesTypeEnum.CONTINUOUS.getCode())){
                        employmentType=EmploymentTypeEnum.STUDENT_INTERN.getName();
                    }else {
                        employmentType=EmploymentTypeEnum.OTHER.getName();
                        otherCircumstancesExplanations=OtherCircumstancesExplanations.OTHER_INCOME.getName();
                    }
                }else {
                    employmentType=EmploymentTypeEnum.EMPLOYEE.getName();
                }
            }
            taxDeclarationInformationImportDto.setEmploymentType(employmentType);
            taxDeclarationInformationImportDto.setOtherCircumstancesExplanations(otherCircumstancesExplanations);

            boolean realEmploymentDateFlag=false;
            //上个月没有这个月有,正常
            if(!map.containsKey(lastKey)&&map.containsKey(nowKey)){
                if(empTaxDeductionVoMap.containsKey(nowKey)){
                    EmpTaxDeductionVo empTaxDeductionVo = empTaxDeductionVoMap.get(nowKey);
                    taxDeclarationInformationImportDto.setStaffNo(empTaxDeductionVo.getStaffNo());
                }
//                if (employeeVo.getEmploymentDate()!=null){
//                    taxDeclarationInformationImportDto.setEmploymentDate(com.reon.hr.api.customer.utils.DateUtil.getDefautlString(employeeVo.getEmploymentDate()));
//                }else {
//                    taxDeclarationInformationImportDto.setEmploymentDate(DateUtil.getDateByInteger(taxMonth,true));
//                }
                if(taxDeclarationInformationImportDto.getPebEmploymentDate()!=null){
                    taxDeclarationInformationImportDto.setEmploymentDate(DateUtil.format(taxDeclarationInformationImportDto.getPebEmploymentDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
                    realEmploymentDateFlag=true;
                    taxDeclarationInformationImportDto.setRealEntryDimissionFlag(YES_NO);
                }else {
                    taxDeclarationInformationImportDto.setEmploymentDate(DateUtil.getDateByInteger(taxMonth,true));
                    taxDeclarationInformationImportDto.setRealEntryDimissionFlag(NO_NO);
                }

                taxDeclarationInformationImportDto.setPersonnelStatus(PersonnelStatusEnum.NORMAL.getName());
                taxDeclarationInformationImportDtoList.add(taxDeclarationInformationImportDto);
            }
            //没有离职日期，但有入职日期，且非正常的，入职日期为税务系统的入职日期
            //有离职日期且离职日期与本次离职日期一致，有入职日期，且非正常的，入职日期为税务系统的入职日期
            String dimissionDateStr = DateUtil.getDateByInteger(lastTaxMonth, false);
            boolean taxSystemFlag = employeeVo.getEmploymentDate() != null &&
                    (employeeVo.getLeaveDate() == null || dimissionDateStr.equals(com.reon.hr.api.customer.utils.DateUtil.getDefautlString(employeeVo.getLeaveDate())));
            //这个月有但是有真实离职时间的
            if(map.containsKey(nowKey)){
                if(empTaxDeductionVoMap.containsKey(nowKey)){
                    EmpTaxDeductionVo empTaxDeductionVo = empTaxDeductionVoMap.get(nowKey);
                    taxDeclarationInformationImportDto.setStaffNo(empTaxDeductionVo.getStaffNo());
                }

                if(taxDeclarationInformationImportDto.getPebLeaveDate()!=null){
                    //上个月有
                    if(map.containsKey(lastKey)){
                        realEmploymentDateFlag=setEmploymentDate(taxDeclarationInformationImportDto,employeeVo,taxSystemFlag,minTaxMonthDayMap,realEmploymentDateFlag);
                    }
                    EmployeeVo employeeUpdateVo=new EmployeeVo();
                    employeeUpdateVo.setId(employeeVo.getId());
                    employeeUpdateVo.setLeaveDate(taxDeclarationInformationImportDto.getPebLeaveDate());
                    employeeUpdateVoList.add(employeeUpdateVo);
                    taxDeclarationInformationImportDto.setDimissionDate(DateUtil.format(taxDeclarationInformationImportDto.getPebLeaveDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
                    if(realEmploymentDateFlag){
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(YES_YES);
                    }else {
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(NO_YES);
                    }
                    taxDeclarationInformationImportDto.setPersonnelStatus(PersonnelStatusEnum.NORMAL.getName());
                    //taxDeclarationInformationImportDto.setEmploymentType(EmploymentTypeEnum.OTHER.getName());
                    //上个月有
                    if(map.containsKey(lastKey)){
                        taxDeclarationInformationImportDtoList.add(taxDeclarationInformationImportDto);
                    }
                }
            }
            //上个月有这个月没有,非正常
            if(map.containsKey(lastKey)&&!map.containsKey(nowKey)){
                if(empTaxDeductionVoMap.containsKey(lastKey)){
                    EmpTaxDeductionVo empTaxDeductionVo = empTaxDeductionVoMap.get(lastKey);
                    taxDeclarationInformationImportDto.setStaffNo(empTaxDeductionVo.getStaffNo());
                }
                realEmploymentDateFlag=setEmploymentDate(taxDeclarationInformationImportDto,employeeVo,taxSystemFlag,minTaxMonthDayMap,realEmploymentDateFlag);
//                if (employeeVo.getLeaveDate()!=null){
//                    taxDeclarationInformationImportDto.setDimissionDate(com.reon.hr.api.customer.utils.DateUtil.getDefautlString(employeeVo.getLeaveDate()));
//                }else {
//                    taxDeclarationInformationImportDto.setDimissionDate(DateUtil.getDateByInteger(lastTaxMonth,false));
//                }
                //2.导入的入离职时间在申报采集那同步设置
                if(taxDeclarationInformationImportDto.getPebLeaveDate()!=null){
                    /*EmployeeVo employeeUpdateVo=new EmployeeVo();
                    employeeUpdateVo.setId(employeeVo.getId());
                    employeeUpdateVo.setLeaveDate(taxDeclarationInformationImportDto.getPebLeaveDate());
                    employeeUpdateVoList.add(employeeUpdateVo);
                    taxDeclarationInformationImportDto.setDimissionDate(DateUtil.format(taxDeclarationInformationImportDto.getPebLeaveDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
                    if(realEmploymentDateFlag){
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(YES_YES);
                    }else {
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(NO_YES);
                    }*/
                    continue;
                }else {
                    taxDeclarationInformationImportDto.setDimissionDate(dimissionDateStr);
                    if(realEmploymentDateFlag){
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(YES_NO);
                    }else {
                        taxDeclarationInformationImportDto.setRealEntryDimissionFlag(NO_NO);
                    }
                    if(taxSystemFlag&& Objects.equals(taxMonth, DateUtil.getCurrentYearMonth())){
                        EmployeeVo employeeUpdateVo=new EmployeeVo();
                        employeeUpdateVo.setId(employeeVo.getId());
                        employeeUpdateVo.setLeaveDate(DateUtil.parseStringToDate(dimissionDateStr, DateUtil.DATE_FORMAT_YYYY_MM_DD));
                        employeeUpdateVoList.add(employeeUpdateVo);
                    }
                }
                taxDeclarationInformationImportDto.setPersonnelStatus(PersonnelStatusEnum.NON_NORMAL.getName());

                //taxDeclarationInformationImportDto.setEmploymentType(employmentType);
                taxDeclarationInformationImportDtoList.add(taxDeclarationInformationImportDto);
            }
        }
        if(CollectionUtils.isNotEmpty(employeeUpdateVoList)){
            employeeMapper.updateDimissionDateByList(employeeUpdateVoList);
        }
        return taxDeclarationInformationImportDtoList;
    }
    @Override
    public Map<String, String> getMinTaxMonthDayMap(List<Long> empIdList,Integer taxMonth){
        Map<String, String> minTaxMonthDayMap=new HashMap<>();
        List<SalaryInfoVo> salaryInfoVoList=salaryInfoMapper.getTaxMonthByEmpIdList(empIdList,taxMonth);
        Map<String, List<SalaryInfoVo>> salaryInfoVoListMap = salaryInfoVoList.stream().collect(Collectors.groupingBy(salaryInfoVo -> salaryInfoVo.getEmpId() + "-" + salaryInfoVo.getWithholdingAgentNo()));
        for (String key:salaryInfoVoListMap.keySet()) {
            List<SalaryInfoVo> salaryInfoVos = salaryInfoVoListMap.get(key);
            List<Integer> thisTaxMonthList = salaryInfoVos.stream().map(SalaryInfoVo::getTaxMonth).collect(Collectors.toList());
            Integer maxTaxMonth=0;
            String maxTaxMonthDay=null;
            Long minPayId=null;
            Date pebEmploymentDate=null;
            for (SalaryInfoVo salaryInfoVo : salaryInfoVos) {
                Integer thisTaxMonth = salaryInfoVo.getTaxMonth();
                Integer beforeTaxMonth = DateUtil.getYearMonthByCount(thisTaxMonth, -1);
                //最后一个上一月没有数据
                if (!thisTaxMonthList.contains(beforeTaxMonth) && thisTaxMonth.compareTo(maxTaxMonth) >= 0) {
                    Long thisPayId = salaryInfoVo.getPayId();
                    if(thisTaxMonth.compareTo(maxTaxMonth)>0){
                        maxTaxMonth = thisTaxMonth;
                        maxTaxMonthDay=DateUtil.getDateByInteger(maxTaxMonth,true);
                        minPayId= thisPayId;
                        pebEmploymentDate = salaryInfoVo.getPebEmploymentDate();
                    }else if(thisTaxMonth.compareTo(maxTaxMonth)==0){
                        //多批计算的,取第一次的
                        if(thisPayId.compareTo(minPayId)<0){
                            minPayId=thisPayId;
                            pebEmploymentDate=salaryInfoVo.getPebEmploymentDate();
                        }
                    }
                }
            }
            if(pebEmploymentDate==null){
                minTaxMonthDayMap.put(key,maxTaxMonthDay);
            }else {
                minTaxMonthDayMap.put(key,DateUtil.format(pebEmploymentDate,DateUtil.DATE_FORMAT_YYYY_MM_DD)+REAL);
            }
        }
        return minTaxMonthDayMap;
    }
    public boolean setEmploymentDate(TaxDeclarationInformationImportDto taxDeclarationInformationImportDto,EmployeeVo employeeVo,boolean taxSystemFlag,Map<String, String> minTaxMonthDayMap,boolean realEmploymentDateFlag){
        //没有离职日期，但有入职日期，且非正常的，入职日期为税务系统的入职日期
        //有离职日期且离职日期与本次离职日期一致，有入职日期，且非正常的，入职日期为税务系统的入职日期
        if (taxSystemFlag){
            taxDeclarationInformationImportDto.setEmploymentDate(com.reon.hr.api.customer.utils.DateUtil.getDefautlString(employeeVo.getEmploymentDate()));
        }else {
            String minKey = taxDeclarationInformationImportDto.getEmployeeId() + "-" + taxDeclarationInformationImportDto.getWithholdingAgentNo();
            if(minTaxMonthDayMap.containsKey(minKey)){
                String employmentDate = minTaxMonthDayMap.get(minKey);
                if(employmentDate.contains(REAL)){
                    employmentDate=employmentDate.replaceAll(REAL,"");
                    realEmploymentDateFlag=true;
                }
                taxDeclarationInformationImportDto.setEmploymentDate(employmentDate);
            }
        }
        return realEmploymentDateFlag;
    }

    @Override
    public List<NaturalWagesDetailImportDto> getTaxDeclarationInformationDetailListExport(TaxDeclarationInformationDto vo) {
        setSalaryTaxType(vo);
        List<NaturalWagesDetailImportDto> naturalWagesDetailImportDtoList=salaryPayMapper.getTaxDeclarationInformationDetailListPage(vo);
        return setTaxDeclarationInformationDetailList(vo,naturalWagesDetailImportDtoList);
    }

    public List<NaturalWagesDetailImportDto> setTaxDeclarationInformationDetailList(TaxDeclarationInformationDto vo, List<NaturalWagesDetailImportDto> naturalWagesDetailImportDtoList){
        List<String> withholdingAgentNoList = naturalWagesDetailImportDtoList.stream().map(NaturalWagesDetailImportDto::getWithholdingAgentNo).distinct().collect(Collectors.toList());
        List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList =new ArrayList<>();
        if(CollectionUtils.isNotEmpty(withholdingAgentNoList)){
            excessCalculationAmountDetailVoList = excessCalculationAmountDetailMapper.getByWithholdingAgentNoList(withholdingAgentNoList);
        }
        Map<String, BigDecimal> accuExcessAmountMap=new HashMap<>();
        Integer taxMonth = vo.getTaxMonth();
        for (ExcessCalculationAmountDetailVo detailVo:excessCalculationAmountDetailVoList) {
            if(detailVo.getExcessCalculationType()== ExcessCalculationTypeEnum.RESERVE_FUND.getCode()){
                if(detailVo.getValidFrom()<=taxMonth&&taxMonth<=detailVo.getValidTo()){
                    accuExcessAmountMap.put(detailVo.getWithholdingAgentNo(),detailVo.getExcessCalculationAmount());
                }
            }
        }
        //经济补偿金跟基本工资需要分开
        if(vo.getSalaryTaxType().equals(SalaryTaxTypeEnum.TAX_RATE_SCHEDULE_OF_ECONOMIC_COMPENSATION.getCode())){
            TaxDeclarationInformationDto taxDeclarationInformationDto = new TaxDeclarationInformationDto();
            BeanUtils.copyProperties(vo,taxDeclarationInformationDto);
            taxDeclarationInformationDto.setSalaryTaxType(SalaryTaxTypeEnum.TAX_RATE_SCHEDULE_2019.getCode());
            taxDeclarationInformationDto.setS043(BigDecimal.ZERO);
            List<NaturalWagesDetailImportDto> taxDeclarationInformationDetailListPage = salaryPayMapper.getTaxDeclarationInformationDetailListPage(taxDeclarationInformationDto);
            if(CollectionUtils.isNotEmpty(taxDeclarationInformationDetailListPage)){
                naturalWagesDetailImportDtoList.addAll(taxDeclarationInformationDetailListPage);
            }
        }
        List<Long> empIdList = naturalWagesDetailImportDtoList.stream().map(NaturalWagesDetailImportDto::getEmployeeId).collect(Collectors.toList());
        Map<String,EmpTaxDeductionVo> empTaxDeductionVoMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(empIdList)&&CollectionUtils.isNotEmpty(withholdingAgentNoList)){
            List<EmpTaxDeductionVo> empTaxDeductionVoList = specImportInfoMapper.getByMonthListAndEmpIdList(empIdList, Collections.singletonList(vo.getTaxMonth()), withholdingAgentNoList).stream().distinct().collect(Collectors.toList());
            empTaxDeductionVoMap = empTaxDeductionVoList.stream().collect(Collectors.toMap(empTaxDeductionVo -> empTaxDeductionVo.getEmpId() + "-" + empTaxDeductionVo.getWithholdingAgentNo(), v -> v));
        }

        List<Long> salaryIdList = naturalWagesDetailImportDtoList.stream().map(NaturalWagesDetailImportDto::getSalaryId).collect(Collectors.toList());
        Map<Long, PaymentApplyVo> paymentApplyVoByBatchIdMap=new HashMap<>();
        Map<Long, Long> batchIdBySalaryIdMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(salaryIdList)){
            List<SalaryBatchDetailVo> salaryBatchDetailVoList=salaryBatchDetailMapper.getTaxDeclarationBySalaryIdList(salaryIdList);
            if(CollectionUtils.isNotEmpty(salaryBatchDetailVoList)){
                batchIdBySalaryIdMap = salaryBatchDetailVoList.stream().collect(Collectors.toMap(SalaryBatchDetailVo::getSalaryId, SalaryBatchDetailVo::getBatchId));
                List<PaymentApplyVo> paymentApplyVos = iPaymentApplyWrapperService.getByBatchIdList(new ArrayList<>(batchIdBySalaryIdMap.values()));
                paymentApplyVoByBatchIdMap = paymentApplyVos.stream().collect(Collectors.toMap(PaymentApplyVo::getBatchId, Function.identity()));
            }
        }

        List<NaturalWagesDetailImportDto> endDtoList=new ArrayList<>();
        Map<String, List<NaturalWagesDetailImportDto>> dtoMap = naturalWagesDetailImportDtoList.stream().collect(Collectors.groupingBy(dto -> dto.getEmployeeId() + "-" + dto.getWithholdingAgentNo()));
        for (String dtoKey:dtoMap.keySet()) {
            List<NaturalWagesDetailImportDto> naturalWagesDetailImportDtos = dtoMap.get(dtoKey);
            NaturalWagesDetailImportDto endDto = naturalWagesDetailImportDtos.get(0);
            if(empTaxDeductionVoMap.containsKey(dtoKey)){
                EmpTaxDeductionVo empTaxDeductionVo = empTaxDeductionVoMap.get(dtoKey);
                endDto.setStaffNo(empTaxDeductionVo.getStaffNo());
                endDto.setOther(empTaxDeductionVo.getOtherDeduction().compareTo(BigDecimal.ZERO)>0?empTaxDeductionVo.getOtherDeduction():null);
            }
            if(StringUtil.isNotBlank(endDto.getOtherName())){
                endDto.setEmployeeName(endDto.getOtherName());
            }

            if(StringUtil.isNotBlank(endDto.getOtherCertNo())){
                endDto.setCertNo(endDto.getOtherCertNo());
            }
            for (int i = 0; i < naturalWagesDetailImportDtos.size(); i++) {
                boolean endResult=i==naturalWagesDetailImportDtos.size()-1;
                NaturalWagesDetailImportDto naturalDto = naturalWagesDetailImportDtos.get(i);
                Map<String, Object> mapSalaryInfo = JSON.parseObject(naturalDto.getSalaryJsonInfo(), Map.class);

                BigDecimal currentIncome = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S037.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S037.getItemNo()).toString() : "0");
                //经济补偿金跟基本工资需要分开
                if(vo.getSalaryTaxType().equals(SalaryTaxTypeEnum.TAX_RATE_SCHEDULE_OF_ECONOMIC_COMPENSATION.getCode())){
                    currentIncome=new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S043.getItemNo()).toString());
                }
                endDto.setCurrentIncome(endDto.getCurrentIncome()!=null?endDto.getCurrentIncome().add(currentIncome):currentIncome);

                BigDecimal basicPensionPremium = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S031.getItemNo()).toString());
                endDto.setBasicPensionPremium(endDto.getBasicPensionPremium()!=null?endDto.getBasicPensionPremium().add(basicPensionPremium):basicPensionPremium);
                if(endResult&&endDto.getBasicPensionPremium().compareTo(BigDecimal.ZERO)<0){
                    endDto.setCurrentIncome(endDto.getCurrentIncome().subtract(endDto.getBasicPensionPremium()));
                    endDto.setBasicPensionPremium(BigDecimal.ZERO);
                }

                String S061 = mapSalaryInfo.get(SalaryItemEnum.S061.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S061.getItemNo()).toString() : "0";
                String S062 = mapSalaryInfo.get(SalaryItemEnum.S062.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S062.getItemNo()).toString() : "0";
                String S063 = mapSalaryInfo.get(SalaryItemEnum.S063.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S063.getItemNo()).toString() : "0";
                String S064 = mapSalaryInfo.get(SalaryItemEnum.S064.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S064.getItemNo()).toString() : "0";
                BigDecimal basicMedicalInsurancePremium = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S032.getItemNo()).toString())
                        .add(new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S034.getItemNo()).toString()))
                        .add(new BigDecimal(S061)).add(new BigDecimal(S062)).add(new BigDecimal(S063)).add(new BigDecimal(S064));
                endDto.setBasicMedicalInsurancePremium(endDto.getBasicMedicalInsurancePremium()!=null?endDto.getBasicMedicalInsurancePremium().add(basicMedicalInsurancePremium):basicMedicalInsurancePremium);
                if(endResult&&endDto.getBasicMedicalInsurancePremium().compareTo(BigDecimal.ZERO)<0){
                    endDto.setCurrentIncome(endDto.getCurrentIncome().subtract(endDto.getBasicMedicalInsurancePremium()));
                    endDto.setBasicMedicalInsurancePremium(BigDecimal.ZERO);
                }

                BigDecimal unemploymentInsuranceExpense = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S033.getItemNo()).toString());
                endDto.setUnemploymentInsuranceExpense(endDto.getUnemploymentInsuranceExpense()!=null?endDto.getUnemploymentInsuranceExpense().add(unemploymentInsuranceExpense):unemploymentInsuranceExpense);
                if(endResult&&endDto.getUnemploymentInsuranceExpense().compareTo(BigDecimal.ZERO)<0){
                    endDto.setCurrentIncome(endDto.getCurrentIncome().subtract(endDto.getUnemploymentInsuranceExpense()));
                    endDto.setUnemploymentInsuranceExpense(BigDecimal.ZERO);
                }

                BigDecimal housingFund = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S035.getItemNo()).toString());
                endDto.setHousingFund(endDto.getHousingFund()!=null?endDto.getHousingFund().add(housingFund):housingFund);
                if(endResult&&endDto.getHousingFund().compareTo(BigDecimal.ZERO)<0){
                    endDto.setCurrentIncome(endDto.getCurrentIncome().subtract(endDto.getHousingFund()));
                    endDto.setHousingFund(BigDecimal.ZERO);
                }
                BigDecimal S046 = new BigDecimal(mapSalaryInfo.get(SalaryItemEnum.S046.getItemNo()) != null ? mapSalaryInfo.get(SalaryItemEnum.S046.getItemNo()).toString() : "0");
                if(endDto.getCurrentAccumulationFundExcessTaxAmount()!=null){
                    endDto.setCurrentAccumulationFundExcessTaxAmount(endDto.getCurrentAccumulationFundExcessTaxAmount().add(S046));
                }else {
                    endDto.setCurrentAccumulationFundExcessTaxAmount(S046);
                }
                if(endResult){
                    //实时上限金额
                    BigDecimal accuExcessAmount = accuExcessAmountMap.get(endDto.getWithholdingAgentNo());
                    BigDecimal currentAccumulationFundExcessTaxAmount=endDto.getCurrentAccumulationFundExcessTaxAmount();
                    endDto.setCurrentAccumulationFundExcessTaxAmountChangeFlag(BooleanTypeEnum.NO.getName());
                    endDto.setCurrentAccumulationFundTopAmount(accuExcessAmount);
                    BigDecimal currentAccumulationFundExcessTaxAmountNow=BigDecimal.ZERO;
                    if(accuExcessAmount!=null){
                        currentAccumulationFundExcessTaxAmountNow = endDto.getHousingFund().subtract(accuExcessAmount);
                        if(currentAccumulationFundExcessTaxAmountNow.compareTo(BigDecimal.ZERO)<0){
                            currentAccumulationFundExcessTaxAmountNow=BigDecimal.ZERO;
                        }
                    }
                    if(currentAccumulationFundExcessTaxAmount.compareTo(currentAccumulationFundExcessTaxAmountNow)!=0){
                        currentAccumulationFundExcessTaxAmount= currentAccumulationFundExcessTaxAmountNow;
                        endDto.setCurrentAccumulationFundExcessTaxAmountChangeFlag(BooleanTypeEnum.YES.getName());
                    };
                    endDto.setHousingFund(endDto.getHousingFund().subtract(currentAccumulationFundExcessTaxAmount));
                    endDto.setCurrentAccumulationFundExcessTaxAmount(currentAccumulationFundExcessTaxAmount);
                }

                if(endResult&&endDto.getCurrentIncome().compareTo(BigDecimal.ZERO)<0){
                    endDto.setCurrentIncome(BigDecimal.ZERO);
                }
            }
            if(batchIdBySalaryIdMap.containsKey(endDto.getSalaryId())){
                PaymentApplyVo applyVo = paymentApplyVoByBatchIdMap.get(batchIdBySalaryIdMap.get(endDto.getSalaryId()));
                endDto.setLastDate(applyVo.getLastDate().substring(0,10));
                endDto.setAnewPayFlagStr(EnumsUtil.getNameByCode(applyVo.getAnewPayFlag(), BooleanTypeEnum.class));
            }
            endDtoList.add(endDto);
        }
        return endDtoList;
    }
    @Override
    public Page<NaturalWagesDetailImportDto> getTaxDeclarationInformationDetailListPage(Integer page, Integer limit, TaxDeclarationInformationDto vo) {
        Page<NaturalWagesDetailImportDto> naturalWagesDetailImportDtoPage=new Page<>(page,limit);
        setSalaryTaxType(vo);
        List<NaturalWagesDetailImportDto> naturalWagesDetailImportDtoList=salaryPayMapper.getTaxDeclarationInformationDetailListPage(vo);
        List<NaturalWagesDetailImportDto> records = setTaxDeclarationInformationDetailList(vo, naturalWagesDetailImportDtoList);
        naturalWagesDetailImportDtoPage.setRecords(records);
        naturalWagesDetailImportDtoPage.setTotal(records.size());
        return naturalWagesDetailImportDtoPage;
    }
}
