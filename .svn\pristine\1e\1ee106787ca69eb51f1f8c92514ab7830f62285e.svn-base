<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>社保公积金一览</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">


    <!-- import CSS -->
    <link rel="stylesheet" href="${ctx}/layui/vue/general.css?v=${publishVersion}" media="all">
    <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
    <!-- import JavaScript -->
    <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/icon.js?v=${publishVersion}"></script>

    <style>
        .query-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .selected-cities-card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .selected-cities {
            padding: 15px;
        }

        .selected-cities .label {
            font-weight: bold;
            margin-right: 10px;
            color: #606266;
        }

        .tags-container {
            display: inline-block;
            min-height: 32px;
        }

        .city-tag {
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .action-row {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .customer-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .text-right {
            text-align: right;
        }

        .el-form-item {
            margin-bottom: 18px;
        }

        .el-card {
            border: 1px solid #e6e6e6;
        }

        .el-tabs--border-card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .el-tabs--border-card > .el-tabs__content {
            padding: 20px;
        }

        .el-button {
            margin-left: 8px;
        }

        .el-button:first-child {
            margin-left: 0;
        }

        .search-container {
            flex: 1;
        }

        .action-buttons {
            margin-left: 20px;
        }

        .action-buttons .el-button {
            margin-left: 8px;
        }

        .action-buttons .el-button:first-child {
            margin-left: 0;
        }

        /* 搜索按钮样式优化 */
        .el-input-group__append .el-button {
            border-left: none;
            border-radius: 0 4px 4px 0;
        }

        .el-input-group__append .el-button:hover {
            background-color: #409eff;
            border-color: #409eff;
        }

        .el-input-group__append > .el-button {
            margin: auto;
        }
    </style>
</head>
<body>
<div id="app">
    <el-tabs type="border-card" v-model="obj.activeTab">
        <el-tab-pane label="根据城市导出" name="city">
            <!-- 城市选择区域 -->
            <div class="query-form">
                <el-form :model="obj.cityForm" inline label-width="auto">
                    <el-form-item label="城市:" prop="city">
                        <el-select class="width220" v-model="obj.cityForm.city" placeholder="请选择城市"
                                   @change="handleCityChange" filterable :disabled="obj.includePreferred">
                            <el-option v-for="item in obj.cityOptions" :key="item.value" :label="item.label"
                                       :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 已选城市卡片 -->
            <div class="selected-cities-card">
                <div class="selected-cities">
                    <span class="label">已选城市:</span>
                    <div class="tags-container">
                        <el-tag v-for="city in obj.selectedCities" :key="city.value" closable
                                class="city-tag" @close="handleCityRemove(city)">
                            {{ city.label }}
                        </el-tag>
                        <span v-if="obj.selectedCities.length === 0" style="color: #999;">暂无选择城市</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-row">
                <div class="text-right">
                    <el-switch v-model="obj.includePreferred" class="mr10" inline-prompt active-text="All"></el-switch>
                    <el-button type="default" @click="handleCityReset">重置城市</el-button>
                    <el-button type="primary" @click="handleGeneratePreferred"
                               :disabled="obj.selectedCities.length === 0 && !obj.includePreferred">生成优选
                    </el-button>
                    <el-button type="success" @click="handleGenerateFile"
                               :disabled="obj.selectedCities.length === 0 && !obj.includePreferred">生成文件
                    </el-button>
                </div>
            </div>
        </el-tab-pane>

        <el-tab-pane label="根据客户导出" name="customer">
            <!-- 客户查询区域 -->
            <div class="customer-form">
                <el-form :model="obj.currentRow" inline label-width="auto">
                    <el-form-item label="客户名称:" prop="customerName">
                        <el-input v-model="obj.currentRow.custName" placeholder="请输入客户名称" clearable
                                  readonly class="width220" @click="customerOpen"></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-row">
                <div class="text-right">
                    <el-button type="success" :disabled="!obj.currentRow?.custName" @click="handleGenerateCustomerFile">
                        生成文件
                    </el-button>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>

    <el-dialog v-model="obj.dialogShow" width="35%" append-to-body draggable @close="close">
        <div class="mt20 mb20 flex-between-center">
            <div class="search-container">
                <el-input
                        style="width: 320px"
                        v-model="obj.customerParams.custName"
                        placeholder="客户名称/编号/合同名称/编号"
                        @keyup.enter="customerOpen"
                        clearable>
                    <template #append>
                        <el-button @click="customerOpen" icon="Search" type="primary">搜索</el-button>
                    </template>
                </el-input>
            </div>
            <div class="action-buttons">
                <el-button @click="clear">清空</el-button>
                <el-button type="primary" @click="select">选择</el-button>
            </div>
        </div>
        <el-table :data="obj.customerData" style="width: 100%;max-height: 600px;overflow: auto"
                  @current-change="customerSelect" border>
            <el-table-column align="center" width="80">
                <template #default="scope">
                    <el-radio v-model="obj.radio" :value="scope.row.id" @change="customerSelect(scope.row)"></el-radio>
                </template>
            </el-table-column>
            <el-table-column label="客户ID" align="center" prop="id" width="80"></el-table-column>
            <el-table-column label="客户编号" align="center" prop="custNo" width="200"></el-table-column>
            <el-table-column label="客户名称" align="center" prop="custName"></el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
                v-model:current-page="obj.customerParams.page"
                v-model:page-size="obj.customerParams.limit"
                :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="obj.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>
    </el-dialog>
</div>
</body>
<script>
    const {createApp, reactive, ref, onMounted} = Vue
    const {ElMessage} = ElementPlus

    const app = createApp({
        setup() {

            // 响应式数据
            const obj = reactive({
                // 页面状态
                activeTab: 'city',

                // 城市相关数据
                cityForm: {
                    city: ''
                },
                cityOptions: [],
                selectedCities: [],

                // 客户相关数据
                customerParams: {
                    page: 1,
                    limit: 10,
                    custName: '',
                },
                radio: '',
                total: 0,
                customerData: [],
                currentRow: {},

                includePreferred: false,
            })

            // ==================== 城市相关功能 ====================
            function handleCityChange(value) {
                if (!value) return;

                // 检查是否已存在
                const exists = obj.selectedCities.some(city => city.value === value);
                if (exists) {
                    ElMessage.warning('该城市已选择');
                    return;
                }

                // 添加到已选城市
                const cityItem = obj.cityOptions?.find(item => item.value === value);
                if (cityItem) {
                    obj.selectedCities.push(cityItem);
                    // obj.cityForm.city = ''; // 清空选择
                }
            }

            // 删除已选城市
            function handleCityRemove(city) {
                const index = obj.selectedCities.findIndex(item => item.value === city.value);
                if (index > -1) {
                    obj.selectedCities.splice(index, 1);
                }
            }

            // 重置城市选择
            function handleCityReset() {
                obj.selectedCities = [];
                obj.cityForm.city = '';
                ElMessage.success('已重置城市选择');
            }

            // 生成优选
            /**
             * 生成优选数据
             * @param {boolean|Event} isGenerateFile - 是否生成文件，如果是Event对象则表示生成优选
             */
            function handleGeneratePreferred(isGenerateFile) {
                // 判断是否为生成文件操作（true表示生成文件，false或Event表示生成优选）
                const isFileGeneration = !(isGenerateFile instanceof Event) && isGenerateFile === true;

                // 构建请求参数
                const params = buildGenerateParams(isFileGeneration);

                // 如果是选择城市模式且没有选择城市，则提示用户
                if (!obj.includePreferred && obj.selectedCities.length === 0) {
                    ElMessage.warning('请先选择城市');
                    return;
                }

                // 调用API生成数据
                generateData(params, !obj.includePreferred);
            }

            /**
             * 构建生成请求的参数
             * @param {boolean} isFileGeneration - 是否为文件生成
             * @returns {Object} 请求参数对象
             */
            function buildGenerateParams(isFileGeneration) {
                const params = {};

                // 设置城市参数
                if (obj.includePreferred) {
                    // 包含所有优选城市
                    params.citys = '';
                } else {
                    // 使用选中的城市
                    const selectedCityValues = obj.selectedCities.map(city => city.value);
                    params.citys = selectedCityValues.join(',');
                }

                // 如果不是文件生成，则添加type参数
                if (!isFileGeneration) {
                    params.type = 1;
                }

                return params;
            }

            /**
             * 调用API生成数据
             * @param {Object} params - 请求参数
             * @param {boolean} shouldResetCities - 是否需要重置城市选择
             */
            function generateData(params, shouldResetCities) {
                SocialSecurityOverviewAPI.generated(params).then(res => {
                    const result = window.extractResponseData(res);
                    if (result.code === 0) {
                        // 成功时重置城市选择（仅在选择城市模式下）
                        if (shouldResetCities) {
                            handleCityReset();
                        }
                        ElMessage.success('任务已生成，请到任务中心查看，编号为:' + result.msg);
                    } else {
                        ElMessage.error('生成失败！');
                    }
                }).catch(error => {
                    console.error('生成数据失败:', error);
                    ElMessage.error('生成失败，请稍后重试！');
                });
            }

            /**
             * 生成文件
             * 调用生成优选方法，传入true表示生成文件
             */
            function handleGenerateFile() {
                handleGeneratePreferred(true);
            }

            // ==================== 客户相关功能 ====================

            //获取客户列表
            function customerOpen() {
                let params = {
                    page: obj.customerParams.page,
                    limit: obj.customerParams.limit,
                    searchParam: {
                        name: obj.customerParams.custName
                    }
                }

                // 如果没有搜索条件，移除 searchParam
                if (!obj.customerParams.custName) {
                    delete params.searchParam;
                } else {
                    params.searchParam = JSON.stringify(params.searchParam)
                }

                console.log('客户查询参数:', params)
                SocialSecurityOverviewAPI.customer(params).then(res => {
                    if (res.data.code === 0) {
                        obj.customerData = res.data.data;
                        obj.total = res.data.count;
                    } else {
                        ElMessage.error('查询失败！');
                    }
                }).catch(error => {
                    console.error('客户查询失败:', error);
                    ElMessage.error('客户查询失败');
                })
                obj.dialogShow = true;
            }

            //关闭窗口
            function close() {
                obj.dialogShow = false;
                obj.customerParams = {
                    page: 1,
                    limit: 10,
                }
            }

            //客户选择
            function customerSelect(row) {
                if (row) {
                    obj.radio = row.id;
                }
            }

            function select() {
                if (obj.radio) {
                    obj.currentRow = obj.customerData.find(item => item.id === obj.radio)
                }
                obj.dialogShow = false
            }

            // 客户重置
            function clear() {
                obj.queryParams = {
                    page: 1,
                    limit: 10,
                    clientName: null,
                };
                obj.currentRow = {}
                obj.radio = null;
                obj.dialogShow = false;
            }

            // 客户查询
            function handleCustomerSearch() {
                obj.customerParams.page = 1
                customerOpen()
            }

            //分页
            function handleSizeChange(size) {
                obj.customerParams.limit = size
                customerOpen()
            }

            function handleCurrentChange(page) {
                obj.customerParams.page = page
                customerOpen()
            }


            // 生成客户文件
            function handleGenerateCustomerFile() {
                if (!obj.currentRow) {
                    ElMessage.warning('请先选择客户');
                    return;
                }
                SocialSecurityOverviewAPI.customerGenerated({custId: obj.currentRow.id}).then(res => {
                    let result = window.extractResponseData(res);
                    if (result.code === 0) {
                        ElMessage.success('任务已生成，请到任务中心查看，编号为:' + result.msg);
                    } else {
                        ElMessage.error('生成失败！');
                    }
                })
            }

            // ==================== 初始化 ====================
            onMounted(() => {
                // 初始化城市选项
                obj.cityOptions = window.top['area']?.map(item => ({
                    label: item.name,
                    value: item.code
                })) || [];
            });

            // 加载图标
            if (typeof ElementPlusIconsVue !== 'undefined') {
                // console.log('ElementPlusIconsVue 已加载，可用图标：', Object.keys(ElementPlusIconsVue))
                for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                    app.component(key, component)
                }
            } else {
                console.error('ElementPlusIconsVue 未定义，请检查图标文件是否正确加载')
                // 尝试从 window 对象获取
                if (window.ElementPlusIconsVue) {
                    // console.log('从 window 对象找到 ElementPlusIconsVue')
                    for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
                        app.component(key, component)
                    }
                }
            }
            return {
                obj,
                handleCityChange,
                handleCityRemove,
                handleCityReset,
                handleGeneratePreferred,
                handleGenerateFile,

                customerOpen,
                close,
                customerSelect,
                select,
                clear,
                handleCustomerSearch,
                handleSizeChange,
                handleCurrentChange,
                handleGenerateCustomerFile,
            }

        }
    })
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount('#app')
</script>
</html>
