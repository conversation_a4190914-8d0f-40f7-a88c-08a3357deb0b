package com.reon.hr.api.customer.enums.contract;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.reon.hr.api.customer.enums.BaseEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 合同类型
 */
public enum ContractType implements BaseEnum {

    CONTRACT_TYPE_AGENCY(1, "单项人事代理", "singleAgencyContractTemplate"),  // 代理
    CONTRACT_TYPE_DISPATCH(2,"派遣","dispatchContractTemplate"),
    CONTRACT_TYPE_OUTSOURCING_1(3,"外包1","outsourcing1ContractTemplate"),
    CONTRACT_TYPE_OUTSOURCING_2(4,"外包2","outsourcing2ContractTemplate"),
    CONTRACT_TYPE_EXAMINATION(5,"体检",""),
    COMMERCIAL_INSURANCE(6, "雇主责任险", "employerLiabilityInsuranceContractTemplate"), //商保
    CONTRACT_TYPE_COUNSEL(7, "商务咨询", ""),
    CONTRACT_TYPE_WELFARE(8,"福利",""),
    CONTRACT_TYPE_PAYROLL_CREDIT(9, "代发工资", "payrollCreditContractTemplate"), //代理
    UNEXPECTED_AND_SUPPLEMENTARY(10, "意外保险", "accidentAndReplenishInsuranceContractTemplate"), //商保
    All_AGENCY(11, "全代理", "allAgencyContractTemplate"),  //代理
    TALENT_RECRUITMENT(12,"人才招聘",""),
    TALENT_TRAINING(13,"人才培训",""),
    KEEP_YOUR_EYES_BACK(14,"定睛背调",""),
    CONTRACT_REPLENISH(16, "补充医疗保险", "replenishInsuranceContractTemplate"), //商保
    //下面只影响合同模板,非添加合同类型
    PEER_CONTRACT_1(17, "同行单项人事代理", "peerSingleAgencyContractTemplate"), //同行单项人事代理
    PEER_CONTRACT_2(18, "同行外包1", "peerOutsourcing1ContractTemplate"), //同行外包1s
    INDIVIDUAL_BUSINESSES(19, "人事代理合同(个体)", "individualBusinesses"),
    AGENCY_ACCOUNT_BUSINESSES(20, "单项人事代理(单立户)", "accountBusinesses"), //单项人事代理合同(单立户)
    All_AGENCY_ACCOUNT_BUSINESSES(21, "全代理(单立户)", "allAgencyAccountBusinesses"), //全代理(单立户)
    CONTRACT_TYPE_OUTSOURCING_1_RECRUIT(22,"外包1(招聘-劳动关系)","outsourcing1recruitContractTemplate"),
    CONTRACT_TYPE_OUTSOURCING_2_RECRUIT(23,"外包2(招聘-劳动关系)","outsourcing2recruitContractTemplate"),
    SINGLE_WORK_INJURY_FLAG_SINGLE_AGENCY(24,"单工伤单项人事代理","singleWorkInjuryFlagSingleAgencyContractTemplate"),
    ;

    private String name;
    private int code;
    private String template;
    public static Set<Integer> oneTimeContractTypeIndexSet = Sets.newHashSet(CONTRACT_TYPE_EXAMINATION.getCode()
            ,CONTRACT_TYPE_COUNSEL.getCode(),CONTRACT_TYPE_WELFARE.getCode(),TALENT_RECRUITMENT.getCode(),
            TALENT_TRAINING.getCode(),KEEP_YOUR_EYES_BACK.getCode());
    public static Set<Integer> agentContractTypeIndexSet = Sets.newHashSet(All_AGENCY.getCode(), CONTRACT_TYPE_AGENCY.getCode(), CONTRACT_TYPE_PAYROLL_CREDIT.getCode(),
            PEER_CONTRACT_1.getCode(),INDIVIDUAL_BUSINESSES.getCode(),AGENCY_ACCOUNT_BUSINESSES.getCode(),All_AGENCY_ACCOUNT_BUSINESSES.getCode(),SINGLE_WORK_INJURY_FLAG_SINGLE_AGENCY.getCode());
    ContractType(int code,String name,String template) {
        this.name = name;
        this.code = code;
        this.template = template;
    }
    /** 派遣 外包1 外包2 */
    public static List<Integer> SOCIAL_ADD_SPECIAL_LIST = Arrays.asList(CONTRACT_TYPE_DISPATCH.code, CONTRACT_TYPE_OUTSOURCING_1.code, CONTRACT_TYPE_OUTSOURCING_2.code);
    /** 社保工资类   全代理 单项人事代理 代发 派遣 外包1 外包2 */
    public static List<Integer> SOCIAL_SALARY_LIST = Arrays.asList(All_AGENCY.code, CONTRACT_TYPE_AGENCY.code, CONTRACT_TYPE_PAYROLL_CREDIT.code,
            CONTRACT_TYPE_DISPATCH.code, CONTRACT_TYPE_OUTSOURCING_1.code, CONTRACT_TYPE_OUTSOURCING_2.code);
    /** 工资类   全代理 代发 派遣 外包1 外包2 */
    public static List<Integer> SALARY_LIST = Arrays.asList(All_AGENCY.code, CONTRACT_TYPE_PAYROLL_CREDIT.code,
            CONTRACT_TYPE_DISPATCH.code, CONTRACT_TYPE_OUTSOURCING_1.code, CONTRACT_TYPE_OUTSOURCING_2.code);
    /** 商保体检类  补充医疗保险  意外保险  雇主责任险   体检 */
    public static List<Integer> COMMERCIAL_MEDICAL_LIST = Arrays.asList(COMMERCIAL_INSURANCE.code, UNEXPECTED_AND_SUPPLEMENTARY.code,
            CONTRACT_REPLENISH.code, CONTRACT_TYPE_EXAMINATION.code);
    /** 商保体检类  补充医疗保险  意外保险  雇主责任险   体检 福利*/
    public static List<Integer> COMMERCIAL_MEDICAL_LIST_SELECT = Arrays.asList(COMMERCIAL_INSURANCE.code, UNEXPECTED_AND_SUPPLEMENTARY.code,
            CONTRACT_REPLENISH.code, CONTRACT_TYPE_EXAMINATION.code,CONTRACT_TYPE_WELFARE.code);
    /**
     * 除体检外一次性服务类型
     */
    public static List<Integer> DISPOSABLE_SERVICE_LIST_NOT_EXAMINATION = Lists.newArrayList(CONTRACT_TYPE_COUNSEL.code, CONTRACT_TYPE_WELFARE.code, TALENT_RECRUITMENT.code, TALENT_TRAINING.code, KEEP_YOUR_EYES_BACK.code);
    /**
     * 一次性服务类型
     */
    public static List<Integer> ONE_TIME_LIST = Lists.newArrayList(CONTRACT_TYPE_EXAMINATION.code, CONTRACT_TYPE_COUNSEL.code, CONTRACT_TYPE_WELFARE.code, TALENT_RECRUITMENT.code, TALENT_TRAINING.code, KEEP_YOUR_EYES_BACK.code);

    /** 项目客服分配类 */
    public static List<Integer> SOCIAL_ASSIGN = Arrays.asList(All_AGENCY.code, CONTRACT_TYPE_AGENCY.code, CONTRACT_TYPE_PAYROLL_CREDIT.code,
            CONTRACT_TYPE_DISPATCH.code, CONTRACT_TYPE_OUTSOURCING_1.code, CONTRACT_TYPE_OUTSOURCING_2.code, CONTRACT_TYPE_COUNSEL.code, TALENT_RECRUITMENT.code,
            TALENT_TRAINING.code, KEEP_YOUR_EYES_BACK.code);

    /** 是否是社保工资类 */
    public static boolean isSocialSalary(Integer code) {
        return SOCIAL_SALARY_LIST.contains(code);
    }

    /** 是否是商保体检类 */
    public static boolean isCommercialMedical(Integer code) {
        return COMMERCIAL_MEDICAL_LIST.contains(code);
    }

    /**
     * 商保合同集合
     */
    public static final List<Integer> COMMERCIAL_INSURANCE_CODE_LIST = Arrays.asList(
            COMMERCIAL_INSURANCE.code,
            UNEXPECTED_AND_SUPPLEMENTARY.code,
            CONTRACT_REPLENISH.code
    );
    /**
     * 补充医疗和意外保险集合
     */
    public static final List<Integer> SUPPLEMENTARY_MEDICAL_AND_ACCIDENT_INSURANCE_LIST = Arrays.asList(
            UNEXPECTED_AND_SUPPLEMENTARY.code,
            CONTRACT_REPLENISH.code
    );
    /**
     * 外包合同集合
     */
    public static final List<Integer> OUTSOURCING_INSURANCE_CODE_LIST = Arrays.asList(
            CONTRACT_TYPE_OUTSOURCING_1.code,
            CONTRACT_TYPE_OUTSOURCING_2.code
    );

    /**
     * 单立户对应合同类型集合
     */
    public static final List<Integer> JUDGE_LIST = Arrays.asList(
            CONTRACT_TYPE_AGENCY.code,
            All_AGENCY.code
    );

    /**
     * 全代理,单项人事代理,代发工资,派遣
     */
    public static final List<Integer> ZERO_AND_FIVE_PERCENT_LIST = Arrays.asList(
            All_AGENCY.getCode(),
            CONTRACT_TYPE_AGENCY.getCode(),
            CONTRACT_TYPE_PAYROLL_CREDIT.getCode(),
            CONTRACT_TYPE_DISPATCH.getCode()
    );

    public static final String checkboxForTrue = "☑";
    public static final String checkboxForFalse = "□";

    public static final List<String> empReq1 = Lists.newArrayList(
            "%s男；",
            "%s女；",
            "%s无；"
    );
    public static final List<String> empReq2 = Lists.newArrayList(
            "%s男",
            " %scm- ",
            "%scm；",
            "%s女",
            " %scm- ",
            "%scm；",
            "%s无；"
    );
    public static final List<String> empReq3 = Lists.newArrayList(
            "%s高中；",
            "%s中专；",
            "%s本科；",
            "%s研究生；",
            "%s其他；"
    );
    public static final List<String> empReq4 = Lists.newArrayList(
            "%s英语4级；",
            "%s英语6级；",
            "%s普通话标准；",
            "%s其他；"
    );
    public static final List<String> empReq5 = Lists.newArrayList(
            "%s是；",
            "%s否；"
    );

    public static String getNameByCode(Integer code) {
        for (ContractType value : ContractType.values()) {
            if (value.code == code) {
                return value.name;
            }
        }
        return null;
    }

    /**
     * 根据产品code判断是否是商保产品
     * @param code 合同类型编码
     * @return boolean
     */
    public static boolean isCommercialInsurance(Integer code){
        return COMMERCIAL_INSURANCE_CODE_LIST.contains(code);
    }

    /** 判断是否单立户  单立户返回 true 非单立户返回 false */
    public static boolean judgeAccountFlag(int contractType, int subType) {
        /**
         11 全代理          3 代理单立户 4 代发工资单立户
         1 单项人事代理      2 单立户
         */
        if ((contractType == All_AGENCY.getCode() && (subType == 3 || subType == 4)) || (contractType == CONTRACT_TYPE_AGENCY.getCode() && subType == 2))
            return true;
        else
            return false;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }
}
