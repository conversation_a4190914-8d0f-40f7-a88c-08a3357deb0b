layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    //检索事件
    form.on('submit(btnQuery)', function (data) {
        table.reload('billGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    // 渲染账单表格数据
    table.render({
        id: 'billGrid',
        elem: '#billGridTable',
        page: true,
        url: ML.contextPath + '/customer/invoice/getTaskByPage',
        method: 'get',
        height: 540,
        skin: 'nob',// 无边框风格
        size: 'lg',// 大尺寸
        toolbar: "#toolbarDemo",
        defaultToolbar: [],
        limit: 50,
        limits: [50, 100, 200],
        text: {
            none: '暂无数据' //无数据时展示
        },
        even: true,
        cols: [[
            // {title: '', type: 'checkbox', width: '3%'},
            {field: 'taskNo', title: '消息编号', align: 'center', width: "15%"},
            {
                field: 'taskType', title: '任务类型', align: 'center', width: "13%", templet: function (d) {
                    return ML.dictFormatter("TASK_TYPE", d.taskType);
                }
            },
            {
                field: 'taskStatus', title: '任务状态', align: 'center', width: "10%", templet: function (d) {
                    return formatTaskStatus(d);
                }
            },
            {field: 'fileName', title: '文件名称', align: 'center', width: "20%"},
            {
                field: 'creator', title: '创建人', align: 'center', width: "10%", templet: function (d) {
                    return ML.loginNameFormater(d.creator);
                }
            },
            {field: 'createTime', title: '任务开始时间', align: 'center', width: "15%"},
            {field: 'errorMessage', title: '失败原因', align: 'center', width: "25%"},
            {
                fixed: 'right', title: '操作', width: "10%", minWidth: 125, align: 'center', templet: function (d) {
                    // 返回一个下载文件按钮,如果d.taskStatus不为3则置灰不能点击
                    if (d.taskStatus == 3) {
                        return '<a class="layui-btn layui-btn-xs" lay-event="downloadFile">下载文件</a>';
                    } else {
                        return '<a class="layui-btn layui-btn-xs layui-btn-disabled">下载文件</a>';
                    }
                }
            }
        ]],
        done: function (res, curr, count) {
            ML.beautifyTable();
            table.on('tool(billGridTableFilter)', function (obj) {
                const data = obj.data; // 获得当前行数据

                switch (obj.event) {
                    case 'downloadFile':
                        console.log(data);
                        ML.downLoadFile(data.fileName, data.fileUrl);
                        break;
                }
            })
        }
    });

    // 定义一个函数用于编码HTML，避免XSS风险
    function encodeHTML(str) {
        return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');
    }

// 使用对象映射来减少硬编码，提高可维护性
    const statusColors = {
        1: '#FFB800',
        2: '#D2D2D2',
        3: '#4DAF29',
        4: '#CF1900',
    };

    function formatTaskStatus(d) {
        const taskStatusStr = ML.dictFormatter("TASK_STATUS", d.taskStatus);
        // 使用模板字符串和映射对象来构造返回值，同时对taskStatusStr进行编码以避免XSS
        const color = statusColors[d.taskStatus] || '#cccccc'; // 如果没有找到对应的颜色，则使用默认颜色
        return `<span style="background-color: ${color}; color: #f3eded">${encodeHTML(taskStatusStr)}</span>`;
    }

    var startDate = laydate.render({
        elem: '#createTimeS',
        max: "2099-12-31",//设置一个默认最大值
        showBottom: false,
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });
    var endDate = laydate.render({
        elem: '#createTimeE',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        showBottom: false,
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    $("#reset").click(function () {
        startDate.config.max = {
            year: 2099,
            month: 12 - 1,//关键
            date: 30
        }
        endDate.config.min = {
            year: 1900,
            month: 1 - 1, //关键
            date: 1
        };
    });
})
