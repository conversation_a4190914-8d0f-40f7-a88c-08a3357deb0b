package com.reon.hr.core.utils;

import java.math.BigDecimal;

/**
 * BigDecimal 工具类
 * <AUTHOR>
 *
 */
public abstract  class BigDecimalUtil {


    public static boolean equalsVal(double val,BigDecimal compareVal ){
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal)==0;
    }

    public static boolean greaterEqualVal(double val,BigDecimal compareVal ){
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal)>-1;
    }

    public static boolean lessEqualVal(double val,BigDecimal compareVal ){
        BigDecimal decimal = new BigDecimal(val);
        return decimal.compareTo(compareVal)<1;
    }
}
