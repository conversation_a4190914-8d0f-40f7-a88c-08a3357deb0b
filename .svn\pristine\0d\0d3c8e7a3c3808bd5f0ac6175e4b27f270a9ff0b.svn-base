package com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.api.bill.utils.ListPageUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.bill.vo.salary.PayApply2BatchVo;
import com.reon.hr.api.bill.vo.salary.PayServiceSerialLogVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPaymentRefundInputVo;
import com.reon.hr.api.workflow.vo.ParameVo;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.api.workflow.vo.WorkflowComentVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IPaymentApplyWrapperService {
    /**
     * 查询支付管理数据
     * @param paymentApplyVo
     * @param page
     * @param limit
     * @return
     */
    Page<PaymentApplyVo> getPaymentApplyListPage(PaymentApplyVo paymentApplyVo, Integer page, Integer limit);

    /**
     * 查询符合条件的pid
     * @param paymentApplyVo
     * @return {@link List }<{@link String }>
     */
    List<PaymentApplyVo> getPaymentApprovalList(PaymentApplyVo paymentApplyVo);


    /**
     * 保存支付管理数据
     * @param record
     * @return
     */
    int save(PaymentApplyVo record);

    /**
     * 更新支付管理数据
     * @param record
     * @return
     */
    boolean updateByPrimaryKeySelective(PaymentApplyVo record);

    /**
     * 根据支付id查询支付管理数据
     * @param id
     * @return
     */
    PaymentApplyVo selectByPrimaryKey(Long id);
    /**
     * 根据支付id查询工资支付数据
     * @param id
     * @return
     */
    PaymentApplyVo selectSalaryById(Long id);

    PaymentApplyVo selectDetailById(Long id);
    PaymentApplyVo selectDetailByPid(String pid);

    /**
     * 根据流程id和类型查询支付管理数据
     * @param paymentApplyVo
     * @return
     */
    List<PaymentApplyVo> getPaymentApplyList(PaymentApplyVo paymentApplyVo);
    List<TaskVo> getTaskVoList(ParameVo parameVo,PaymentApplyVo paymentApplyVo, HashMap<Object, TaskVo> taskVoMap, Map<String, WorkflowComentVo> finalWorkflowAuditLogListMap);
    List<PaymentApplyVo> getPaymentApplyListByPidList(List<String> pidList);

    /**
     * 权限改造只涉及到了 工资支付查询 单独一个接口
     * @param paymentApplyVo
     * @return
     */
    ListPageUtil<PaymentApplyVo> getPaymentApplyListByAuth(PaymentApplyVo paymentApplyVo, List<OrgPositionDto> userOrgPositionDtoList);
    /**
     * 根据流程id和类型查询支付管理数据
     * @param paymentApplyVo
     * @return
     */
    List<PaymentApplyVo> getPractIcePaymentApplyList(PaymentApplyVo paymentApplyVo);
    List<PaymentApplyVo> getPractIcePaymentApplyListByAppStatus();


    Long findPaymentApplyPidAndProcType(String pid);
    /**
     * 保存支付管理数据
     * @param record
     * @return
     */
    int savePayBatch(PaymentApplyVo record);
    /**
     * 查看发放是否已经生成支付审核*/
    List<PayApply2BatchVo> getPayMent(List<Long> batchIds,Integer kind);
    List<Map<String,Object>>  getPay(List<Long> payMentIds);


    Long applyInsurancePracticePayment(PaymentApplyVo paymentApplyVo);


    boolean updateAppStatus(PaymentApplyVo record);
    boolean editPaymentApplyAndPrac(PaymentApplyVo paymentApplyVo);

    int updateDocumentStatus(List<String> pidList, Integer documentStatus,Integer appStatus);

    PaymentApplyVo getByBatchId(Long batchId);

    boolean updateSalaryAppStatus(PaymentApplyVo paymentApplyVo);

    List<PaymentApplyVo> getByPidList(List<String> pidList);

    int updateReprintCount(List<String> pidList, Integer reprintCount);

    boolean getUnRejected(String contractNo, Long templetId, Integer billMonth);

    int updateSalaryFee(PaymentApplyVo paymentApplyVo);

    List<PaymentApplyVo> getByBatchIdList(List<Long> batchIdList);

    int deleteByIdList(List<Long> idList, String loginName);


    int updateLastDate(PaymentApplyVo paymentApplyVo);
    int updatePayReceived(PaymentApplyVo paymentApplyVo);

    List<Long> getBatchIdListByLastDate(Integer lastDate);

    List<PaymentApplyVo> getPayCreateTimeByPayBatchId(List<Long> payBatchIdList);

    List<Long> getBatchIdListByStringLastDate(String lastDate);

    List<Long> getBatchIdListByStringLastDateStartAndEnd(String lastDateStart,String lastDateEnd);

    Page<Map<String, Object>> getSalaryMapListByVo(PaymentApplyVo paymentApplyVo, SalaryPaymentRefundInputVo vo, Integer limit, Integer page);

    List<PaymentApplyVo> getLastDateAndAnewPayFlagByBatchIdList(List<Long> batchIdList);


    void deleteInsurancePracticeDisComPayByPayId(Long payId);

    int saveInsurancePracticeDisComPayBatch(List<InsurancePracticeDisComPayVo> insurancePracticeDisComPayVos);

    List<InsurancePracticeDisComPayVo> getInsurancePracticeDisComListByPayId(Long payId);

    List<PracticePayDetailVo> selectDetailsByPayId(Long payId);

    /**
     * 此查询为银企直联派单地查询
     * @param paymentApplyVo
     * @return {@link List }<{@link String }>
     */
    List<PaymentApplyVo> getPaymentApprovalListByDisCom(PaymentApplyVo paymentApplyVo);


    /**
     * 发起支付通过招商银行
     * @param taskVos
     * @param loginName
     * @return {@link String }
     */
    String initiatePaymentBuyCmb(List<TaskVo> taskVos ,String loginName);


    /**
     * 线下转账
     * @param taskVos
     * @param loginName
     * @return {@link String }
     */
    void offlineTransfer(List<TaskVo> taskVos ,String loginName);

    /**
     * 这两个方法是新增和删除支付日志
     * @param payServiceSerialLogVoList
     * @return
     */
    int insertPayServiceLog(List<PayServiceSerialLogVo> payServiceSerialLogVoList);
    int deletePayServiceLogByPayId(Long paymentId);


    Page<PaymentApplyVo> getPrintApplicationFromPage(PaymentApplyVo paymentApplyVo);

    Map<String,List<PrintPaymentFromExportVo>> printApplicationFrom(List<PrintPaymentFromExportVo> vos);

    List<String> selectPidListByPid(List<String> pidList);


    List<PaymentApplyVo> getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo);


    Map<String,List<PrintPaymentFromExportVo>> printAllDisApplicationFrom(PrintPaymentFromExportVo vo);

    Map<String,List<PrintReceivingApplicationFromVo>> printReceivingApplicationFrom(PrintPaymentFromExportVo vo);

    List<ReserveInquiryVo> getReserveInquiryListPage(PaymentApplyVo vo);

    List<ReserveInquiryVo> getReserveInquiryDetail(PaymentApplyVo vo);


    List<PracticePayDetailVo> selectPracticePayDetailListByPayId(Long payId);

    List<InsurancePracticeDisComPayVo> checkProgressByPayId(Long payId);

}
