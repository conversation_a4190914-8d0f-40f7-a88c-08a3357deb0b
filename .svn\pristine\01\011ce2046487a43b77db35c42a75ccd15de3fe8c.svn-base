package com.reon.hr.api.bill.enums;

public enum PracticeLockInfoPayStatusEnum {
    /**
     * 完全没有支付过
     */
    NON_PAYMENT ((byte)1,"未支付"),
    /**
     *
     */
    IN_PAID((byte)2,"支付中"),
    /**
     * 全部都支付完成
     */
    HAVE_PAID((byte)3,"已支付"),
    /**
     * 只要有过支付成功就不能修改为支付失败
     */
    PAID_FAIL((byte)4,"支付失败"),
    /**
     * 已经有过支付成功的数据
     */
    PART_PAID((byte)5,"部分支付");

    private Byte code;
    private String msg;

    PracticeLockInfoPayStatusEnum(Byte code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public Byte getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsgByCode(Byte code){
        String msg = null;
        if (code != null){
            for (PracticeLockInfoPayStatusEnum billStatus : PracticeLockInfoPayStatusEnum.values()) {
                if (billStatus.getCode().equals(code)){
                    msg = billStatus.getMsg();
                }
            }
        }
        return msg;
    }
}
