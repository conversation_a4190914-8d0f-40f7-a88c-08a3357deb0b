package com.reon.hr.api.bill.vo.supplierPractice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年02月18日
 * @Version 1.0
 */
@Data
public class InsuranceSupplierPracticeDetailVo implements Serializable {

    private Long id;
    /**
     * 自有实做和供应商实做服务费关联表id
    */
    private Long ispId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 收服务费的月份
     */
    private Integer month;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 费用类型 1:汇缴 2:补缴
     */
    private byte feeType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

}
