<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>复制组织机构</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui_ext/dtree/dtree.css?v=${publishVersion}"/>
    <link rel="stylesheet" href="${ctx}/layui_ext/dtree/font/dtreefont.css?v=${publishVersion}"/>
    <style>
        .selectStyle {
            width: 300px;
            background: white;
            height: 200px;
            overflow-y: scroll;
            display: none;
            position: absolute;
            z-index: 1000;
            left: 130px;
        }
        .lable {
            width: 110px !important;
        }
        .commonInput {
            width: 300px;
        }
        .layui-form-label {
            margin-bottom: 38px;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post" id="copyOrgForm">
    <div class="layui-form-item" style="margin-top: 20px; margin-bottom: 0px;">
        <div class="layui-inline">
            <label class="layui-form-label lable">源机构名称<i style="color: red">*</i>：</label>
            <input type="text" placeholder="请选择" class="layui-input commonInput" id="sourceOrgName" name="sourceOrgName"
                   autocomplete="off"
                   lay-verify="required" readonly maxlength="20">
            <input class="layui-input commonInput" id="sourceOrgCode" name="sourceOrgCode" autocomplete="off"
                   readonly maxlength="20" style="display:none">
            <div>
                <div id="sourceOrgTree" class="selectStyle">
                    <ul id="sourceDemoTree" class="dtree" data-id="0"></ul>
                </div>
            </div>
        </div>

    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label lable">目标机构名称<i style="color: red">*</i>：</label>
            <input type="text" placeholder="请选择" class="layui-input commonInput" id="targetOrgName" name="targetOrgName"
                   autocomplete="off"
                   lay-verify="required" readonly maxlength="20">
            <input class="layui-input commonInput" id="targetOrgCode" name="targetOrgCode" autocomplete="off"
                   readonly maxlength="20" type="hidden">
            <div>
                <div id="targetOrgTree" class="selectStyle">
                    <ul id="targetDemoTree" class="dtree" data-id="0"></ul>
                </div>
            </div>
        </div>
        <div class="layui-inline" style="left: 0px;right: 0px;margin-left: 0px;">
            <lable class="layui-form-label lable">部门后缀<i style="color: red">*</i>:</lable>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="afterIndex" autocomplete="off" maxlength="20">
            </div>
        </div>
    </div>

    <div class="layui-form-item" style="margin-right: 100px;">
        <div align="right">
            <button class="layui-btn " type="button"  id="commit">确定</button>
            <button class="layui-btn layui-btn-primary" type="button" id="cancel">取消</button>
        </div>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/sys/organization/copyOrgPage.js?v=${publishVersion}"></script>
</body>
</html>
