/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/5
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.servicesitecfg.controller;

import com.alibaba.fastjson2.JSONObject;
import com.reon.hr.api.base.vo.SocialSecurityFundParamVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.TaskLogWrapperService;
import com.reon.hr.api.bill.enums.TaskTypeEnum;
import com.reon.hr.api.bill.vo.TaskLogVo;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.modules.common.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className SocialSecurityFundController
 * @date 2020/6/5 13:53
 */
@RestController
@RequestMapping("/serviceSiteCfg/socialSecurityFund")
public class SocialSecurityFundController extends BaseController {

    @Resource
    private TaskLogWrapperService taskLogWrapperService;

    // 跳转到社保公积金一览页面
    @RequestMapping("gotoSocialSecurityFundQueryListPage")
    public ModelAndView gotoSocialSecurityFundQueryListPage() {
        return new ModelAndView("/serviceSiteCfg/socialSecurityFundQuery");
    }

    // 导出社保公积金一览数据
    // type = 1生成优选,0为生成全部文件
    // 若是通过客户导出,则只需要custId
    @GetMapping(value = "/exportFile")
    public LayuiReplay exportFile(String[] citys, Integer type, Long custId) {
        return LayuiReplay.success(commonMethod(citys, type, custId));
    }

    private String commonMethod(String[] citys, Integer type, Long custId) {
        SocialSecurityFundParamVo paramVo;
        if (custId != null) {
            paramVo = SocialSecurityFundParamVo.builder()
                    .custId(custId)
                    .currentDate(Integer.valueOf(DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_yyyyMM)))
                    .exportType(2)
                    .build();
        } else {
            List<String> cityList = Arrays.asList(citys);
            paramVo = SocialSecurityFundParamVo.builder()
                    .cityList(cityList)
                    .currentDate(Integer.valueOf(DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_yyyyMM)))
                    .exportType(1)
                    .type(type)
                    .build();
        }
        CommonUserVo commonUserVo = getSessionUser();
        String loginName = commonUserVo.getLoginName();


        TaskLogVo taskLogVo = TaskLogVo.builder()
                .taskType(TaskTypeEnum.SOCIAL_SECURITY_PROVIDENT_FUND_EXPORTS.getCode())
                .param(JSONObject.toJSONString(paramVo))
                .creator(loginName)
                .updater(loginName)
                .build();
        return taskLogWrapperService.generateTask(taskLogVo);

    }
}
