package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.group.GroupVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.sp.customer.entity.cus.Customer;

import java.util.List;
import java.util.Map;

public interface CustomerService extends IService<Customer> {

    long saveCustomer(Customer customer);

    /**
     * 批量保存客户
     *
     * @param customers 客户
     * @return boolean
     */
    boolean batchSaveCustomer(List<Customer> customers);

    /**
     * 保存关联公司中间信息
     *
     * @param vo 签证官
     * @return long
     */
    long saveCustomerRef(CustomerRefVo vo);

    Customer findById(Long id);

    CustomerInvoiceVo findCropKindByCustId(Long custId);

    CustomerRefVo getContractByRefId(Long id);

    String findRejectionById(Long custRefId);

    boolean updateById(CustomerVo customerVo);

    boolean auditCustInfo(CustomerApprovalVo customerApprovalVo);

    /**
     * 批量更新
     *
     * @param vo 签证官
     * @return int
     */
    boolean batchUpdate(CustomerRefVo vo);

    boolean updateByCustRefId(CustomerRefVo customerVo);

    boolean delById(Long id);

    List<String> checkCustomerName2(Long customerName);

    Page<CustomerVo> getCurrentLoginCustomer(int page,int limit,String param,String currLogin);

    Page<CustomerVo>  getCustomerByTemplateId(int page,int limit,String param);

    /**
     * 根据开票信息维护id查询开票信息维护编号和客户编号
     * @param invoiceId
     * @return
     */
    CustNoAndInvoiceNoVo searchCustNoAndInvoiceNoByInvoiceId(String invoiceId);

    /**
     * 获取所有的客户
     */
    Page<CustomerVo> findCustomerByAll(Integer page, Integer limit, String param, Long groupId);

    /**
     * 验证客户名是否相同
     * @param customerName 用户名
     * @return boolean
     */
    boolean checkCustomerName(String customerName);
    /**
     * 验证客户名是否相同
     * @param customerName 用户名
     * @return boolean
     */
    String checkCustomerName1(Long id);

    /**
     * 新增关联公司客户审批信息
     * @param customerApprovalVo
     * @return
     */
    long saveAuditCustomer(CustomerApprovalVo customerApprovalVo);

    /**
     * 新增关联公司统一接口(防止分布式事务问题)
     *
     * @param customerVo
     * @return
     */
    CustomerRefVo relUnifyCustomer(CustomerVo customerVo, String orgCode, String posCode, String loginName);

    /**
     * rel统一
     * 新增关联公司统一接口(防止分布式事务问题)
     *
     * @param customerVo 客户签证官
     * @param orgCode    组织代码
     * @param posCode    pos代码
     * @param loginName  登录名
     */
    void relUnify(CustomerVo customerVo, String orgCode, String posCode, String loginName);

    /**
     * 分页获取数据
     * @param customerPageVo 条件
     * @return Page
     */
    Page<CustomerPageVo> findCustomerByPage(CustomerPageVo  customerPageVo);

    /**
     * 获得批准者页面
     *
     * @param customerPageVo 客户页签证官
     * @return {@link Page}<{@link CustomerPageVo}>
     */
    Page<CustomerPageVo> getApproverPage(CustomerPageVo  customerPageVo, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 获得批准状态
     *
     * @param vo 签证官
     * @return {@link CustomerRefVo}
     */
    List<CustomerRefVo> getApprovalStatus(List<CustomerRefVo> vo);


    boolean checkCustomerNo(String customerNo);

    Integer findByCustomerNo(String customerNo);

    Page<CustomerVo> getCustomerForEmployeeContract(Integer page, Integer limit, CustomerVo customerVo);

    List<CustomerVo> findAllCustomer();

    /**
     * 查找所有客户及其关联客户
     *
     * @return {@link List}<{@link CustomerRelVo}>
     */
    List<CustomerRelVo> findAllCustomerWithRel();

    List<CustomerVo> getCustomerListByIds(List<Long> custIdList);

    List<CustomerRefVo> getCustomerListByRefCustId(Long refCustId);

    List<CustomerVo> getCustomerAndRelevanceCustomerById(Long id);

    List<Long> getCustIdListByRefCustIds(List<Long> refCustIds);

    List<CustomerVo> getListByCityCodeAndCustIds(Integer cityCode, List<Long> custIdList);

    List<CustContactorVo> getCustContactorListByCustId(CustContactorVo custContactorVo);

    Integer batchSaveCustContactor(List<CustContactorVo> custContactorVoList, String loginName);

    Integer editCustContactor(CustContactorVo custContactorVo, String loginName);

    List<CustomerVo> getCustomerByCustIdList(List<Long> custIdList);

    List<CustomerVo> selectCustNameByGroupIdAndName(String name, Long groupId, Page<CustomerVo> customerVoPage);

    /**
     * 根据集团获取客户id集合
     *
     * @param name 名字
     * @return {@link List}<{@link String}>
     */
    List<CustomerVo> getCustIdsByGroup(String name);

    List<CustomerVo> findCustomerListAndGroupId();
    List<GroupVo> getAllGroupNameByCustId(ContractVo contractVo, List<OrgPositionDto> userOrgPositionDtoList);

    List<CustomerVo> getCustNameByCustIdList(List<Long> custIdList);

    List<CustomerVo> getCustomerRelevanceByCustId(Long custId);

    List<Customer> selectByIdList(List<Long> custIdList);

    void editCustomerNameById(CustomerOperateLogVo customerOperateLogVo);

    List<CustomerOperateLogVo> getEditNameLogPage(Long custId);

    List<Long> getCustIdsByGroupId(Long custGroupId, Long custId);

    List<CustomerVo> findAllCustomerName();

    List<Customer> selectListByCustGroupId(Map<String, Object> conditonMap);

    List<CustomerVo>  getGroupNameByCustIdList(List<Long> custIdList,String groupName);

    List<Long> selectIdByCustGroupId(List<Long> groupIdList);

    boolean getGroupNameByCustId(Long custId);

    List<String>getPidByPidListAndTableName(List<String>pidList,String procType,String tableName,String delFlag);

    Long getCustIdByCustName(String custName);

    List<OrgVo> getCustomerByName(String keyword);

    List<Long> getCustIdByCustNameNotLike(String custName);

}