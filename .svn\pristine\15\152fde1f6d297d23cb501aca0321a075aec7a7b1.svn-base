package com.reon.hr.api.customer.dubbo.service.rpc.qiyuesuo;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.qiyuesuo.QysMappingFieldVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysSecretKeyVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年05月07日
 * @Version 1.0
 */
public interface IQysSecretKeyWrapperService {

    /**
     * 获取所有的秘钥key
     * @return {@link List}<{@link String}>
     */
    List<String> getAllSecretKey();


    Page<QysSecretKeyVo> getAllCallBackKey(QysSecretKeyVo qysSecretKeyVo);

    QysSecretKeyVo getCallBackKeyById(Long id);

    int addCallBackKey(QysSecretKeyVo qysSecretKeyVo);

    int updateCallBackKey(QysSecretKeyVo qysSecretKeyVo);


    QysSecretKeyVo getQysSecretKeyVoByBuClasId(Long buClasId);

    QysSecretKeyVo getQysSecretKeyVoBySecretKey(String secretKey);


    List<QysMappingFieldVo> getCheckTemplateFields();

    List<QysSecretKeyVo> getAllQysSecretKeyVo();

    void batchUpdateByBusClasId(List<QysSecretKeyVo> qysSecretKeyVos);


}
