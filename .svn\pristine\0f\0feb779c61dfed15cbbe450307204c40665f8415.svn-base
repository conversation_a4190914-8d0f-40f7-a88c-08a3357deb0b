layui.use(['jquery','form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    $('#selectperson').attr("disabled","disabled");
    form.render('select');

    var aratext;
    var persontext;
    var flag = false;
    /*城市选择*/
    form.on('select(area)', function(data){

        aratext = data.elem[data.elem.selectedIndex].text;
        if (aratext){
            $("#selectperson").removeAttr("disabled");
        }
        if (aratext && persontext){
            $('#typename').val(aratext+'-'+persontext);
        }
        form.render('select');
    });
    /*人员类型选择*/
    form.on('select(person)', function(data){
            persontext = data.elem[data.elem.selectedIndex].text;
            if(aratext) {
                $('#typename').val(aratext + '-' + persontext);
            }
        form.render('select');
    });
    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });
    form.on("submit(addOrUpdateCategory)", function(data) {
        if (data.field['cityCode'] !== "" && data.field['indTypeCode'] !=="") {
            ML.layuiButtonDisabled($('#addOrUpdateCategory'));// 禁用
            ML.ajax("/sys/IndividualCategory/saveCategory", data.field, function(result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    reloadTable();
                    layer.closeAll('iframe');//关闭弹窗
                } else if (result.code == -1) {
                    ML.layuiButtonDisabled($('#addOrUpdateCategory'),'true');
                }
            });
        }else {
            layer.msg("所属城市和人员类型必填")
        }
        return false;
    })
    //重载数据
    function reloadTable() {
        table.reload('categoryGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }
});
