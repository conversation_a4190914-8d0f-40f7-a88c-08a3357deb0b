package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.CustomerAndCustInvoiceVo;
import com.reon.hr.api.customer.vo.CustomerInvoiceExportVo;
import com.reon.hr.api.customer.vo.CustomerInvoiceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ICustomerInvoiceService {

    Page<CustomerInvoiceVo> getListPage(Integer page, Integer limit, Map<String,Object> queryMap);
    Page<CustomerInvoiceVo> getListPageForContract(Integer page, Integer limit, Map<String, Object> queryMap);

    int deleteById(List<Long> ids,String updater);

    CustomerInvoiceVo getOneById(Long id);

    List<CustomerInvoiceVo> selectByPrimaryKeys(@Param("list") List<Long> list);
    List<CustomerInvoiceVo> selectByCustIds(@Param("list") List<Long> list);

    int update(CustomerInvoiceVo invoice);

    int insert(CustomerInvoiceVo invoice);

    List<CustomerInvoiceVo> getListByCustId(Long custId);

    CustomerAndCustInvoiceVo searchCustomerAndInvoiceById(Long id);

    List<CustomerInvoiceVo> getListByCustIds(List<Long> custIds);

    List<CustomerInvoiceVo> getListByCustIdAndOther(CustomerInvoiceVo invoiceVo);

	Integer batchSaveInvoiceData(List<CustomerInvoiceVo> invoiceVoList, String loginName);

	Integer editInvoiceData(CustomerInvoiceVo invoiceVo, String loginName);

    List<CustomerInvoiceExportVo> getExportInvoiceData (String custName, String title,List<Long> custIdList, List<OrgPositionDto> userOrgPositionDtoList);

    List<Long> getAllCustIdByInvoiceIdList(List<Long> invoiceIdList);
}
