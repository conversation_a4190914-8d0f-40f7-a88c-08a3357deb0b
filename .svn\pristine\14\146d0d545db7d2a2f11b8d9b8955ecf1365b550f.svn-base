package com.reon.hr.sp.base.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.vo.PolicyUnemploymentVo;
import com.reon.hr.sp.base.dao.sys.PolicyUnemploymentMapper;
import com.reon.hr.sp.base.service.sys.PolicyUnemploymentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 失业福利待遇(PolicyUnemploymentVo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:40
 */
@Service("policyUnemploymentService")
public class PolicyUnemploymentServiceImpl implements PolicyUnemploymentService {
    @Resource
    private PolicyUnemploymentMapper policyUnemploymentMapper;
    @Resource
    private IAreaResourceWrapperService iAreaResourceWrapperService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PolicyUnemploymentVo queryById(Long id) {
        return policyUnemploymentMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param policyUnemployment 筛选条件
     * @param page                页面
     * @param limit               限制
     * @return 查询结果
     */
    @Override
    public Page<PolicyUnemploymentVo> queryByPage(Integer page, Integer limit, PolicyUnemploymentVo policyUnemployment) {
        Page<PolicyUnemploymentVo> pageRequest = new Page<>(page, limit);
        pageRequest.setRecords(policyUnemploymentMapper.queryAllByLimit(policyUnemployment, pageRequest));
        return pageRequest;
    }

    @Override
    public List<PolicyUnemploymentVo> queryList(PolicyUnemploymentVo policyWorkInjuryVo) {
        return policyUnemploymentMapper.queryList(policyWorkInjuryVo);
    }

    @Override
    public List<PolicyUnemploymentVo> queryList(List<PolicyUnemploymentVo> policyWorkInjuryVos) {
        return policyUnemploymentMapper.queryListByList(policyWorkInjuryVos);
    }

    /**
     * 新增数据
     *
     * @param policyUnemployment 实例对象
     * @return 实例对象
     */
    @Override
    public PolicyUnemploymentVo insert(PolicyUnemploymentVo policyUnemployment) {
        policyUnemploymentMapper.insert(policyUnemployment);
        return policyUnemployment;
    }

    @Override
    public int insertBatch(List<PolicyUnemploymentVo> entities) {
        return policyUnemploymentMapper.insertBatch(entities);
    }

    @Override
    public int updateBatch(List<PolicyUnemploymentVo> entities) {
        return policyUnemploymentMapper.updateBatch(entities);
    }

    /**
     * 修改数据
     *
     * @param policyUnemployment 实例对象
     * @return 实例对象
     */
    @Override
    public PolicyUnemploymentVo update(PolicyUnemploymentVo policyUnemployment) {
        policyUnemploymentMapper.update(policyUnemployment);
        return this.queryById(policyUnemployment.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return policyUnemploymentMapper.deleteById(id) > 0;
    }
}
