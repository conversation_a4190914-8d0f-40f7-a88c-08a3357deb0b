package com.reon.hr.modules.customer.controller.insurancePractice.societyInsurance;

import com.reon.hr.api.customer.enums.ComAcctInfoEnum;
import com.reon.hr.modules.common.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;


@RestController
@RequestMapping("/customer/socialSecurity")
public class SocietyInsuranceInfoController extends BaseController {
	@RequestMapping(value = "/gotoSocialSecurityInfoView", method = RequestMethod.GET)
	public ModelAndView gotoSocietyInsuranceInfoListPage() {
		ModelAndView modelAndView = new ModelAndView("/customer/insurancePractice/societyInsurance/comAcctInfo/comAcctInfoListPage");
		modelAndView.addObject("acctType", ComAcctInfoEnum.AcctTypeEnum.SOCIETY.getIndex());
		return modelAndView;
	}
}
