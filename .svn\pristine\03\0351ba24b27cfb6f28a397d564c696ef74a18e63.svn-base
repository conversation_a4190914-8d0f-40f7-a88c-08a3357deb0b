layui.use(['jquery','form', 'layer', 'element', 'laydate', 'table','upload'], function () {
    var $ = layui.$,
        upload = layui.upload,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var customFileList = [];
    //文件
    var uploadIds =[];
    var fileType = '';
    var fileName = '';
    var fileId = '';
    var size = '';
    var tip = true;
    var empId = $("#empId").val()
    var contractNo = $("#contractNo").val()
    var fileType = $("#fileType").val();

    upload.render({
        elem: '#elemUpload' //绑定元素
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: {contentType: false, processData: false}
        , method: 'POST'
        , exts: fileType==1?'jpg|png|gif|bmp|jpeg|zip|pdf|doc|docx':'jpg|png|gif|bmp|jpeg|zip|pdf'
        , field: 'file'
        , auto: false
        , choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                size = file.size;
                fileName = file.name;
                console.log(size);
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", { icon: 2 });
                    tip = false;
                    return;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push(res.data.fileId);
                $('#upload').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag">' +
                    '<a href="' + ML.fileServerUrl + res.data.fileId + '"  target="_blank" id="gethref">' + fileName + '</a>' +
                    '<a href="javascript:void(0)" class="deleteFile"  }" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', {icon: 1});
            }
        }
        , error: function () {
            //请求异常回调
        }
    });


   //保存
    form.on("submit(save)", function (data) {
            if (uploadIds.length==0){
                return layer.msg("请选择文件!");
            }
            uploadIds.forEach(function(fileId) {
                customFileList.push({
                    "fileId": fileId,
                    "empId": $("#empId").val(),
                    "contractNo": $("#contractNo").val(),
                    "fileType": $("#fileType").val()
                });
            });




        ML.layuiButtonDisabled($('#save'));// 禁用
        if (customFileList.length > 0) {
            $.ajax({
                url:ML.contextPath+"/customer/laborContract/uploadLaborContractFileOrCertFile",
                type:'POST',
                dataType:'json',
                contentType: 'application/json',
                data:JSON.stringify(customFileList),
                success:function (result) {
                    layer.closeAll('iframe');
                    layer.msg(result.msg);
                },
                error:function (data) {
                    layer.msg("系统繁忙，请稍后重试!");
                    ML.layuiButtonDisabled($('#save'),'true');
                }
            });
        }else {
            ML.layuiButtonDisabled($('#save'),1);
            return layer.msg("请选择文件")
        }
        return false;
    });








    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        uploadIds.splice(uploadIds.indexOf($(this).parent()[0].id.split('-')[1]), 1);
        layer.msg("删除文件成功！")
        $(this).parent()[0].remove();
    });

    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });

});
