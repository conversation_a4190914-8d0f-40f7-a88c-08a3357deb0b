package com.reon.hr.modules.customer.controller.workInjury;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.workInjury.IBackEndInjuryFormalInfoWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.workInjury.IInjuryFormalInfoWrapperService;
import com.reon.hr.api.customer.enums.WorkInjuryProcessEnum;
import com.reon.hr.api.customer.vo.workInjury.InjuryFormalInfoDto;
import com.reon.hr.api.customer.vo.workInjury.InjuryFormalInfoVo;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.base.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 后道接口
 * @Date 2023/2/20 0020 上午 09:25
 * @Version 1.0
 */
@RequestMapping("/customer/backEndWorkInjury")
@RestController
public class BackEndWorkInjuryController extends BaseController {

    @Autowired
    private IBackEndInjuryFormalInfoWrapperService iBackEndInjuryFormalInfoWrapperService;

    @Autowired
    private IInjuryFormalInfoWrapperService workInjuryWrapperService;

    /**
     * 后道工伤主页面
     *
     * @return {@link ModelAndView}
     */
    @RequestMapping(value = "/gotoBackEndWorkInjuryPage",method = RequestMethod.GET)
    public ModelAndView gotoBackEndWorkInjuryPage(){
        return new ModelAndView("customer/backEndWorkInjury/backEndWorkInjury");
    }


    /**
     * 转到更新工伤页面
     *
     * @param model 模型
     * @param id    id
     * @return {@link ModelAndView}
     */
    @RequestMapping(value = "/gotoEditBackEndWorkInjuryPage",method = RequestMethod.GET)
    public ModelAndView gotoEditBackEndWorkInjuryPage(Model model, Long id){
        InjuryFormalInfoVo injuryFormalInfoById = workInjuryWrapperService.getInjuryFormalInfoById(id);
        model.addAttribute("vo",injuryFormalInfoById);
        model.addAttribute("id",id);
        return new ModelAndView("customer/backEndWorkInjury/updateBackEndWorkInjury");
    }

    @RequestMapping(value = "/receiveBackEndWorkInjuryPage",method = RequestMethod.GET)
    public ModelAndView gotoReceiveBackEndWorkInjuryPage(Model model, Long id,String type){
        InjuryFormalInfoVo injuryFormalInfoById = workInjuryWrapperService.getInjuryFormalInfoById(id);
        model.addAttribute("vo",injuryFormalInfoById);
        model.addAttribute("id",id);
        model.addAttribute("type",type);
        return new ModelAndView("customer/backEndWorkInjury/receiveBackEndWorkInjury");
    }


    /**
     * 主页面展示数据
     *
     * @param page               页面
     * @param limit              限制
     * @param injuryFormalInfoVo 受伤正式信息签证官
     * @return {@link LayuiReplay}<{@link InjuryFormalInfoVo}>
     */
    @RequestMapping(value = "/getBackEndWorkInjuryList",method = RequestMethod.GET)
    public LayuiReplay<InjuryFormalInfoVo> getBackEndWorkInjuryList(Integer page, Integer limit, InjuryFormalInfoVo injuryFormalInfoVo){
        List<OrgPositionDto> userOrgPositionDtoList = (List<com.reon.hr.api.customer.dto.admin.OrgPositionDto>)getSession().getAttribute(Constants.SESSION_ORG_POSITION);
        injuryFormalInfoVo.setLoginName(getSessionUser().getLoginName());
        Page<InjuryFormalInfoVo> allWorkInjuryPage = iBackEndInjuryFormalInfoWrapperService.getAllWorkInjuryPage(page, limit, injuryFormalInfoVo,userOrgPositionDtoList);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), allWorkInjuryPage.getTotal(), allWorkInjuryPage.getRecords());
    }




    /**
     * 更新工伤
     *
     * @param injuryFormalInfoDto 参数数据
     * @return {@link Object}
     */
    @RequestMapping(value = "/updateBackEndWorkInjury", method = RequestMethod.POST)
    public Object updateBackEndWorkInjury(@RequestBody InjuryFormalInfoDto injuryFormalInfoDto) {
        String loginName = getSessionUser().getLoginName();
        injuryFormalInfoDto.setLoginName(loginName);
        iBackEndInjuryFormalInfoWrapperService.updateInjuryFormalInfo(injuryFormalInfoDto);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

    }


    /**
     * 工伤状态修改
     *
     * @param id           id
     * @param type         类型
     * @return {@link Object}
     */
    @RequestMapping(value = "/receiveBackEndWorkInjury", method = RequestMethod.GET)
    public Object receiveBackEndWorkInjury(Long id,String type,String remark,Integer next) {
        InjuryFormalInfoVo injuryFormalInfoVo = new InjuryFormalInfoVo();
        injuryFormalInfoVo.setType(type);
        injuryFormalInfoVo.setId(id);
        injuryFormalInfoVo.setLoginName(getSessionUser().getLoginName());
        injuryFormalInfoVo.setRemark(remark);
        if ((Objects.equal(type, WorkInjuryProcessEnum.REJECT.getIndex())
                || Objects.equal(type, WorkInjuryProcessEnum.RETURN.getIndex())
                || Objects.equal(type, WorkInjuryProcessEnum.FAIL.getIndex())
                || Objects.equal(type, WorkInjuryProcessEnum.CANCEL.getIndex())) && StringUtils.isEmpty(remark)
        ) {
            return LayuiReplay.error("备注不能为空");
        }
        iBackEndInjuryFormalInfoWrapperService.updateStatusById(injuryFormalInfoVo);
        if (Objects.equal(type, WorkInjuryProcessEnum.SUCCESS.getIndex())){
            boolean nextOpt = iBackEndInjuryFormalInfoWrapperService.getNextOpt(id, next);
            if (!nextOpt){
                return new LayuiReplay<>(ResultEnum.OK.getCode(), "办理成功完成,但此城市下没有所选的下一步对应的流程,请去员工业务办理页面新增!");
            }
        }

        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

    }




}
