package com.reon.hr.api.bill.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年06月25日
 * @Version 1.0
 */
@Data
public class InsurancePracticeOneFeeVo implements Serializable {

    private Long id;
    /**
     * 福利办理方
     */
    private String orgCode;

    /**
     * 支付申请id
     */
    private Long paymentId;

    /**
     * 导入编号
     */
    private String importNo;

    /**
     * 一次性金额
     */
    private BigDecimal amt;

    /**
     * 1:未审批 2:审批中 3:审批通过 4:驳回
     */
    private Integer approveStatus;

    /**
     * 1:未审批 2:审批中 3:审批通过 4:驳回
     */
    private Integer firstApproveStatus;

    /**
     * 一级审批人
     */
    private String firstApprove;

    /**
     * 一级审批时间
     */
    private Date firstApproveDate;

    /**
     * 1:未审批 2:审批中 3:审批通过 4:驳回
     */
    private Integer secondApproveStatus;

    /**
     * 二级审批人
     */
    private String secondApprove;

    /**
     * 二级审批时间
     */
    private Date secondApproveDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    private BigDecimal applyAmt;

    private BigDecimal actPayAmt;

    /**
     * 系统申请金额差异
     */
    private BigDecimal systemApplyAmtDifference;

    private List<String> orgAndPosCodeList;

    private String fileId;

    private List<Long> ids;

    private String disCom;
    private String disComCode;
    private String payCom;

    private Long custId;

    private String custName;

    private BigDecimal usedAmt;
    private BigDecimal remainAmt;

    private String orderNo;

    private Integer productCode;


}
