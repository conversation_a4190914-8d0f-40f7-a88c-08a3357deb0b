var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'upload', 'tableSelect', 'table'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        upload = layui.upload,
        layer = layui.layer,
        tableSelect = layui.tableSelect;
    layer = parent.layer === undefined ? layui.layer : parent.layer;

    var tableData = [];
    var checkData = [];
    var adjustId = '';

    $("#groupCode").on('click', function () {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: "社保公积金组",
            area: ['40%', '70%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + "/change/collect/gotoSelectInsuranceGroup",
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                var parentIndex = parent.layer.getFrameIndex(window.name);
                body.find("#parentIframeIndex").val(parentIndex);
            },
            end: function () {
                if ($("#selectedGroupCode").val() != '') {
                    var groupCode = $("#selectedGroupCode").val();
                    var groupName = $("#selectedGroupName").val();
                    getReceivingByCity();
                    $("#groupCode").val(groupName);
                    $.ajax({
                        type: "GET",
                        url: ML.contextPath + "/sys/InsuranceGroup/findProdInsuranceRatioByGroupCode?groupCode=" + groupCode,
                        dataType: 'json',
                        success: function (data) {
                            checkData = [];
                            tableData = data.data;
                            var adjustType = $("#adjustType").val();
                            for (var i = 0; i < tableData.length; i++) {
                                tableData[i]['adjustType'] = adjustType;
                            }
                            table.reload('collectSetGridTable', {data: tableData})
                        },
                        error: function (data) {
                            layer.msg(data);
                            console.log("error")
                        }
                    });
                }
            }
        });
    });

    function getReceivingByCity(receiving) {
        var cityCode = $("#cityCode").val();
        if(ML.isNotEmpty(cityCode)){
            $.ajax({
                type: "GET",
                url: ML.contextPath + "/customer/changeBase/getReceivingListByCityCode?cityCode=" + cityCode,
                dataType: 'json',
                success: function (data) {
                    let _o = $("#receiving");
                    _o.empty();
                    _o.append("<option  value=''>" + '请选择' + "</option>");
                    data.forEach(item =>{
                        var option
                        if(receiving == item.orgCode){
                            option = "<option selected  value=" + item.orgCode + ">" + item.orgName + "</option>";
                        } else {
                            option = "<option  value=" + item.orgCode + ">" + item.orgName + "</option>";
                        }
                        _o.append(option);
                    })
                    form.render("select")
                },
                error: function (data) {
                    layer.msg(data);
                    console.log("error")
                }
            });
        }

    }

    table.render({
        id: 'collectSetGridTable',
        elem: '#collectSetGridTable',
        data: [],
        page: false,
        toolbar: '', // 绑定按钮
        defaultToolbar: [],
        method: 'POST',
        width : 1150,
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {

        },
        cols: [[
            {field: '', type: 'checkbox', width: '4%'},
            {field: 'prodName', title: '产品名称', align: 'center', width: '10%'},
            {field: 'oldRatioCode', title: '原比例', align: 'center', width: '18%', templet: '#oldRatioCodeScr'},
            {field: 'newRatioCode', title: '新比例', align: 'center', width: '18%', templet: '#newRatioCodeScr'},
            {field: 'newComRatio', title: '新企业比例', align: 'center', width: '10%'},
            {field: 'newIndRatio', title: '新个人比例', align: 'center', width: '10%'},
            {field: 'comCol', title: '企业基数列', align: 'center', width: '15%', templet: '#comColScr'},
            {field: 'indCol', title: '个人基数列', align: 'center', width: '15%', templet: '#indColScr'},
        ]],
        limit:500//显示的数量
    });

    function initSelect(){
        var changeAdjustCfgVoList = [];
        var adjustType = '';
        if ($("#adjustJobVo").val() != '') {
            var adjustJobVo = JSON.parse($("#adjustJobVo").val());
            $("#adjustName").val(adjustJobVo.adjustName);
            $("#startMonth").val(adjustJobVo.startMonth);
            $("#endMonth").val(adjustJobVo.endMonth);
            // $("#returnStartMonth").val(adjustJobVo.returnStartMonth);
            var adjustTypeSelect = 'dd[lay-value=' + adjustJobVo.adjustType + ']';
            var rangeTypeSelect = 'dd[lay-value=' + adjustJobVo.rangeType + ']';
            var zeroFlagSelect = 'dd[lay-value=' + adjustJobVo.zeroFlag + ']';
            $('#adjustType').siblings("div.layui-form-select").find('dl').find(adjustTypeSelect).click();
            $('#rangeType').siblings("div.layui-form-select").find('dl').find(rangeTypeSelect).click();
            $('#zeroFlag').siblings("div.layui-form-select").find('dl').find(zeroFlagSelect).click();
            $('#adjustAll').val(adjustJobVo.adjustAll);
            $('#returnOrNotType').val(adjustJobVo.returnOrNotType);
            $('#accountFlag').val(adjustJobVo.accountFlag);
            // $('#cityCode').val(adjustJobVo.cityCode);
            getReceivingByCity(adjustJobVo.receiving);
            changeAdjustCfgVoList = adjustJobVo.adjustCfgVoList;
            adjustType = adjustJobVo.adjustType;
            form.render("select")
        }
        if ($("#selectedGroupCode").val() != '') {
            var groupCode = $("#selectedGroupCode").val();
            var groupName = $("#selectedGroupName").val();
            $("#groupCode").val(groupName);
            $.ajax({
                type: "GET",
                url: ML.contextPath + "/sys/InsuranceGroup/findProdInsuranceRatioByGroupCode?groupCode=" + groupCode,
                dataType: 'json',
                success: function (data) {
                    checkData = [];
                    tableData = data.data;
                    for (var i = 0; i < tableData.length; i++) {
                        for (var j = 0; j < changeAdjustCfgVoList.length; j++) {
                            if (tableData[i].productCode == changeAdjustCfgVoList[j].prodType){
                                checkData.push(i);
                                tableData[i]["LAY_CHECKED"]=true;
                                tableData[i]['comCol'] = changeAdjustCfgVoList[j]['comCol'];
                                tableData[i]['indCol'] = changeAdjustCfgVoList[j]['indCol'];
                                tableData[i]['oldRatioCode'] = changeAdjustCfgVoList[j]['oldRatioCode'];
                                tableData[i]['newRatioCode'] = changeAdjustCfgVoList[j]['newRatioCode'];
                                tableData[i]['newComRatio'] = changeAdjustCfgVoList[j]['newComRatio'];
                                tableData[i]['newIndRatio'] = changeAdjustCfgVoList[j]['newIndRatio'];
                            }
                        }
                        tableData[i]['adjustType'] = adjustType;
                    }
                    table.reload('collectSetGridTable', {data: tableData})
                },
                error: function (data) {
                    layer.msg(data);
                    console.log("error")
                }
            });
        }
        if ($("#adjustId").val() != '') {
            adjustId = $("#adjustId").val();
        }
    }

    upload.render({
        elem: '#selectFile'
        , accept: 'file'
        , url: ctx + '/customer/changeBase/import'
        , auto: false
        //,multiple: true
        , bindAction: '#upload'
        , method: 'POST'
        , exts: 'xls|xlsx'
        , before: function (obj) { //obj参数包含的信息
            if (!isNotEmpty(adjustId)){
                layer.msg("请先保存导入设置");
                layer.stopPropagation();
            }
            this.data = {"adjustId":adjustId};
            ML.layuiButtonDisabled($('#upload'));// 禁用
        }, choose: function (obj) {
            //读取本地文件
            obj.preview(function (index, file, result) {
                $("#selectFile").val(file.name);
                //     layer.msg("一次不能超过3000条数据")
            });
        },
        done: function (res, index, upload) {
            ML.layuiButtonDisabled($('#upload'), true);// 取消禁用
            if (res.code == "0"){
                layer.msg("操作成功");
                layer.closeAll('iframe');
                layui.each(window.parent, function (index, item) {
                    if (item.name == "changeBaseListPage"){
                        item.document.getElementById("query").click();
                    }
                })
            } else {
                layer.msg(res.msg);
            }
        }, error: function (index, upload) {
            ML.layuiButtonDisabled($('#upload'), true);// 取消禁用
            layer.msg("数据接口异常，导入失败！");
        }
    });

    initSelect();

    // 表单提交
    form.on("submit(submit)", function (data) {
        $("#submit").attr("disabled","disabled");
        data.field['groupCode'] = $("#selectedGroupCode").val();
        delete data.field['file'];
        // let returnType = $("#returnOrNotType").val();
        // if(returnType ==2){
        //     if ($("#returnStartMonth").val()==null){
        //         layer.msg("基数低于原基数是否退费选择是,新基数低于原基数的个人订单开始调整月必填！");
        //         return false;
        //     }
        // }
        if (checkData.length === 0){
            layer.open({
                title: '提示'
                ,content: '至少选择一条产品数据'
            });
            $('#submit').removeAttr("disabled");
            return false;
        }
        var isBase;
        switch (data.field['adjustType']) {
            case '1':
                isBase = true;
                break;
            case '2':
                isBase = false;
                break;
        }
        if (ML.isEmpty(data.field['receiving']) && data.field['adjustAll'] == 2){
            layer.msg("连续调整,接单方必填！");
            return false;
        }
        var adjustCfgVoList = [];
        for (var i = 0; i < checkData.length; i++) {
            if (typeof isBase != "undefined"){
                if (isBase){
                    if (!isNotEmpty(tableData[checkData[i]].comCol) || !isNotEmpty(tableData[checkData[i]].indCol)){
                        layer.msg("参数选择不完整,请完善选项！");
                        $('#submit').removeAttr("disabled");
                        return false;
                    }
                }else {
                    if (!isNotEmpty(tableData[checkData[i]].oldRatioCode) || !isNotEmpty(tableData[checkData[i]].newRatioCode)){
                        layer.msg("参数选择不完整,请完善选项！");
                        $('#submit').removeAttr("disabled");
                        return false;
                    }else {
                        if (tableData[checkData[i]].oldRatioCode == tableData[checkData[i]].newRatioCode){
                            layer.msg("新老比例不可相同！");
                            $('#submit').removeAttr("disabled");
                            return false;
                        }
                    }
                }
            }else {
                if (!isNotEmpty(tableData[checkData[i]].comCol) || !isNotEmpty(tableData[checkData[i]].indCol) || !isNotEmpty(tableData[checkData[i]].oldRatioCode) || !isNotEmpty(tableData[checkData[i]].newRatioCode)){
                    layer.msg("参数选择不完整,请完善选项！");
                    $('#submit').removeAttr("disabled");
                    return false;
                }else {
                    if (tableData[checkData[i]].oldRatioCode == tableData[checkData[i]].newRatioCode){
                        layer.msg("新老比例不可相同！");
                        $('#submit').removeAttr("disabled");
                        return false;
                    }
                }
            }
            let adjustCfgVo = {
                "prodType":tableData[checkData[i]].productCode,
                "oldRatioCode":tableData[checkData[i]].oldRatioCode,
                "newRatioCode":tableData[checkData[i]].newRatioCode,
                "comCol":tableData[checkData[i]].comCol,
                "indCol":tableData[checkData[i]].indCol,
            };
            adjustCfgVoList.push(adjustCfgVo);
        }
        data.field['adjustCfgVoList'] = adjustCfgVoList;
        if (isNotEmpty($("#adjustId").val())){
            data.field['id'] = $("#adjustId").val();
        }
        $.ajax({
            url: ML.contextPath + "/customer/changeBase/saveImportSettings",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data.field),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                if (result.code == "0"){
                    adjustId = result.data;
                    $("#adjustId").val(result.data);
                    layer.msg(result.msg);
                    $('#submit').removeAttr("disabled");
                    layui.each(window.parent, function (index, item) {
                        if (item.name == "changeBaseListPage"){
                            item.document.getElementById("query").click();
                        }
                    })                }else {
                    $('#submit').removeAttr("disabled");
                    layer.msg(result.msg);
                }
            },
            error: function (data) {
                $('#submit').removeAttr("disabled");
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
        return false;
    });

    var initYear;
    laydate.render({
        elem: '#startMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#startMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
            if($("#startMonth").val() && $("#endMonth").val() &&$("#startMonth").val() > $("#endMonth").val()){
                $("#startMonth").val('');
                layer.msg("截止月不可以小于起始月!");
            }
        }
    });

    var endInitYear;
    laydate.render({
        elem: '#endMonth',
        theme: 'grid',
        type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            endInitYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectEndYear = date.year;
            var differEnd = selectEndYear - endInitYear;
            if (differEnd == 0) {
                if ($(".layui-laydate").length) {
                    $("#endMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            endInitYear = selectEndYear;
            if($("#startMonth").val() && $("#endMonth").val() && $("#startMonth").val() > $("#endMonth").val()){
                $("#endMonth").val('');
                layer.msg("截止月不可以小于起始月!");
            }
        }
    });

    // 新单位基数列
    form.on('select(adjustTypeFilter)', function (data) {
        var isBase;
        switch (data.value) {
            case '1':
                isBase = true;
                break;
            case '2':
                isBase = false;
                break;
        }
        for (var i = 0; i < tableData.length; i++) {
            if (typeof isBase != "undefined"){
                if (isBase){
                    tableData[i]['newComRatio'] = "";
                    tableData[i]['newIndRatio'] = "";
                    tableData[i]['oldRatioCode'] = "";
                    tableData[i]['newRatioCode'] = "";
                }else {
                    tableData[i]['comCol'] = "";
                    tableData[i]['indCol'] = "";
                }
            }
            tableData[i]['adjustType'] = data.value;
        }
        table.reload('collectSetGridTable', {data: tableData});
        return false;
    });


    form.on('select(adjustAllFilter)', function (data) {
        if(data.value == 1){
            $('#receiving').val('')
        }
        form.render("select")
        return false;
    });


    $(document).on('click',"#download",function(){
        var url = ML.fileServerUrl + ML.loadGlobalCfg("socialSecurityProvidentFundAdjustment");
        window.location.href = url;
        return false;
    });

    // 新单位基数列
    form.on('select(comCol)', function (data) {
        // 获取列索引
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        // 赋值
        tableData[index]['comCol'] = data.value;
    });

    // 新个人基数列
    form.on('select(indCol)', function (data) {
        // 获取列索引
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        // 赋值
        tableData[index]['indCol'] = data.value;
    });

    // 新单位基数列
    form.on('select(oldRatioCode)', function (data) {
        // 获取列索引
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        // 赋值
        tableData[index]['oldRatioCode'] = data.value;
    });

    // 新个人基数列
    form.on('select(newRatioCode)', function (data) {
        // 获取列索引
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        // 赋值
        tableData[index]['newRatioCode'] = data.value;
        var elem = data.othis.parents('tr');//第一列的值是Guid，取guid来判断
        var $td = elem.children('td');
        var selectValue =data.value;
        var prodName = $td.eq(1).find(".layui-table-cell").text();
        if (selectValue == ''){
            tableData[index]['newComRatio'] = "";
            tableData[index]['newIndRatio'] = "";
        }else {
            for (var i = 0; i < tableData.length; i++) {
                let tableDatum = tableData[i];
                if (tableDatum.prodName == prodName){
                    var insuranceRatioVoList = tableDatum.insuranceRatioVoList;
                    for (var i1 = 0; i1 < insuranceRatioVoList.length; i1++) {
                        let insuranceRatioVoListElement = insuranceRatioVoList[i1];
                        if (selectValue == insuranceRatioVoListElement.insuranceRatioCode){
                            tableData[index]['newComRatio'] = insuranceRatioVoListElement.comRatio;
                            tableData[index]['newIndRatio'] = insuranceRatioVoListElement.indRatio;
                        }
                    }
                }
            }
        }
        table.reload('collectSetGridTable', {data: tableData});
    });

    // 监听表格复选框
    table.on('checkbox(collectSetGridTableFilter)', function (obj) {
        if (obj.type == 'one') {
            if (obj.checked) {
                tableData[Number(obj.tr.attr("data-index"))]["LAY_CHECKED"]=true;
                checkData.push(Number(obj.tr.attr("data-index")))
            } else {
                tableData[Number(obj.tr.attr("data-index"))]["LAY_CHECKED"]=false;
                checkData.splice($.inArray(Number(obj.tr.attr("data-index")), checkData), 1);
            }
        } else {
            if (obj.checked) {
                checkData = [];
                for (var i = 0; i < tableData.length; i++) {
                    checkData.push(i);
                    tableData[i]["LAY_CHECKED"]=true;
                }
            } else {
                checkData = [];
                for (var i = 0; i < tableData.length; i++) {
                    tableData[i]["LAY_CHECKED"]=false;
                }
            }
        }
    })

    function isNotEmpty(obj) {
        if (typeof obj == "undefined" || obj == null || obj === "") {
            return false;
        } else {
            return true;
        }
    }
});