package com.reon.hr.sp.bill.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.enums.PaymentApplyProcessStatus;
import com.reon.hr.api.bill.enums.PracticeLockInfoFeeTypeEnum;
import com.reon.hr.api.bill.enums.PracticePayDetailAmtType;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.report.enums.BillReportEnum;
import com.reon.hr.api.thirdpart.dubbo.service.rpc.bankcorp.cmb.ICMBpayWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ManualAction;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.api.workflow.vo.WorkflowComentVo;
import com.reon.hr.common.cmb.*;
import com.reon.hr.common.constant.CMBPayConstant;
import com.reon.hr.common.enums.SalaryPaymentWorkflowEnum;
import com.reon.hr.common.utils.SensitiveReplaceUtil;
import com.reon.hr.sp.bill.dao.bill.InsurancePracticeCustPayDetailMapper;
import com.reon.hr.sp.bill.dao.bill.InsurancePracticeDisComPayMapper;
import com.reon.hr.sp.bill.service.bill.InsurancePracticeDisComPayService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IInsurancePracticeBillService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IPracticePayDetailService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.InsurancePracticeOneFeeService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月04日
 * @Version 1.0
 */
@Slf4j
@Service
public class InsurancePracticeDisComPayServiceImpl implements InsurancePracticeDisComPayService {


        public static final String ALREADY_PAID = "该数据已完成支付，无法重复操作。";
        public static final String EXCEED_LAST_DATE = "支付失败：支付日期应为最晚支付日期当日并且为下午4点前";
        public static final String MISSING_PAY_BANK = "支付失败：未找到对应支付地的招商银行账户，请检查系统信息。";
        public static final String MISSING_RECEIVING_BANK = "支付失败：未找到福利办理方的银行账户信息。";
        public static final String INCOMPLETE_SOCIAL_BANK_INFO = "支付失败：福利办理方的社保银行账户信息不完整，请核实后重试。";
        public static final String TRANSFER_REQUEST_FAILED = "支付失败：支付ID %s 转账请求未成功发送。";
        public static final String TRANSFER_RESULT_ERROR = "支付失败：支付ID %s 返回错误信息：%s";
        public static final String TRANSFER_FAILED_WITH_REASON = "支付失败：支付ID %s，错误信息：%s";
        private static final String MSG_PARTIAL_SUCCESS = "成功 %d 条，失败 %d 条，失败原因：%s";
        private static final String MSG_ACCOUNT_NOT_FOUND = "支付失败：支付ID %s 对应账户信息未找到。";
        private static final String MSG_BALANCE_NOT_ENOUGH = "支付失败：支付ID %s 对应账户余额不足。";






    @Resource
    private InsurancePracticeDisComPayMapper insurancePracticeDisComPayMapper;

    @Resource
    private IPaymentApplyService paymentApplyService;

    @Resource
    private ICMBpayWrapperService icmBpayWrapperService;

    @Resource
    private ISequenceService iSequenceService;



    @Resource
    private IWorkflowWrapperService workflowWrapperService;

    @Resource
    private ISystemConfigWrapperService iSystemConfigWrapperService;

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Autowired
    private InsurancePracticeDisComPayServiceImpl insurancePracticeDisComPayService;

    @Resource
    private ICustomerWrapperService customerWrapperService;

    @Resource
    private IInsurancePracticeServiceConfigWrapperService insurancePracticeServiceConfigWrapperService;

    @Resource
    private InsurancePracticePayBankConfigWrapperService insurancePracticePayBankConfigWrapperService;


    @Resource
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource
    private IUserWrapperService userWrapperService;

    @Resource
    private InsurancePracticeOneFeeService insurancePracticeOneFeeService;

    @Resource
    private InsurancePracticeCustPayDetailMapper insurancePracticeCustPayDetailMapper;

    @Resource
    private IInsurancePracticeBillService insurancePracticeLockService;

    @Resource
    private IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;

    @Resource
    private IAreaResourceWrapperService areaResourceWrapperService;


    @Override
    public void deleteInsurancePracticeDisComPayByPayId(Long payId) {
        insurancePracticeDisComPayMapper.deleteInsurancePracticeDisComPayByPayId(payId);
        insurancePracticeCustPayDetailMapper.deleteInsurancePracticeCustPayDetailByPayId(payId);
        insurancePracticePayBankConfigWrapperService.deleteRemarkByPayId(payId);
    }

    @Override
    public int saveInsurancePracticeDisComPayBatch(List<InsurancePracticeDisComPayVo> insurancePracticeDisComPayVos) {
        return insurancePracticeDisComPayMapper.saveInsurancePracticeDisComPayBatch(insurancePracticeDisComPayVos);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> getInsurancePracticeDisComListByPayId(Long payId) {
        return insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(payId);
    }

    @Override
    public List<PaymentApplyVo> getPaymentApprovalListByDisCom(PaymentApplyVo paymentApplyVo) {
        return insurancePracticeDisComPayMapper.getPaymentApprovalListByDisCom(paymentApplyVo);
    }

    @Override
    public Page<PaymentApplyVo> getPrintApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        Page<PaymentApplyVo> paymentApplyVoPage = new Page<>(paymentApplyVo.getPage(), paymentApplyVo.getLimit());
        List<PaymentApplyVo> printApplicationFromPage = insurancePracticeDisComPayMapper.getPrintApplicationFromPage(paymentApplyVoPage,paymentApplyVo);
        if (CollectionUtils.isEmpty(printApplicationFromPage)){
            return paymentApplyVoPage;
        }
        Set<Long> payIdSet = printApplicationFromPage.stream().map(PaymentApplyVo::getId).collect(Collectors.toSet());
        List<InsurancePracticeOneFeeVo> insurancePracticeOneFeeListByPayIdSet = insurancePracticeOneFeeService.getInsurancePracticeOneFeeDisComListByPayIdSet(payIdSet);
        Map<Long, Map<String, Date>> resultMap = insurancePracticeOneFeeListByPayIdSet.stream()
                .collect(Collectors.groupingBy(
                        InsurancePracticeOneFeeVo::getPaymentId,
                        Collectors.toMap(
                                InsurancePracticeOneFeeVo::getDisCom,
                                InsurancePracticeOneFeeVo::getCreateTime,
                                (existingDate, replacementDate) -> {
                                    // 如果 disCom 重复，保留更早的时间
                                    return existingDate.before(replacementDate) ? existingDate : replacementDate;
                                }
                        )
                ));
        Map<String, InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigMap = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigMap();
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        printApplicationFromPage.forEach(item ->{
            setPackName(item.getId(),item);
            LocalDateTime dateTime = LocalDateTime.parse(item.getLastDate(), formatter);
            LocalDateTime payTime = dateTime.with(LocalTime.of(16, 0));
            item.setLastDate(payTime.format(formatter));
            BigDecimal payAmt = item.getPayAmt().add(item.getServiceAmt());
            item.setBalanceAmt(payAmt.subtract(item.getActPayAmt()));
            InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = insurancePracticePayBankConfigMap.get(item.getDisCom());
            if (insurancePracticePayBankConfigVo != null){
                item.setPayBankNo(insurancePracticePayBankConfigVo.getDispatchBankNo());
                item.setBankType(insurancePracticePayBankConfigVo.getDispatchBankType());
                item.setPayBankName(insurancePracticePayBankConfigVo.getDispatchBankName());
            }
            item.setOneFeeFlag(BooleanTypeEnum.NO.getCode());
            if (!resultMap.isEmpty()&&resultMap.containsKey(item.getId())){
                Map<String, Date> stringDateMap = resultMap.get(item.getId());
                if (stringDateMap!=null&&!stringDateMap.isEmpty()&&stringDateMap.containsKey(item.getDisCom())){
                    if (item.getAppStatus().equals(PaymentApplyProcessStatus.FINISHED.getCode())&&!item.getPassTime().before(stringDateMap.get(item.getDisCom()))){
                        item.setOneFeeFlag(BooleanTypeEnum.YES.getCode());
                    }
                }
            }
            item.setDisComName(orgCodeAndNameMap.get(item.getDisCom()));
            item.setPayCom(orgCodeAndNameMap.get(item.getPayCom()));
        });
        paymentApplyVoPage.setRecords(printApplicationFromPage);
        return paymentApplyVoPage;
    }

    @Override
    public String initiatePaymentBuyCmb(List<TaskVo> taskVos, String loginName) {

        int successCount = 0;
        int failCount = 0;
        StringBuilder result = new StringBuilder();
        for (TaskVo taskVo : taskVos) {
            failCount++;
            InsurancePracticeDisComPayVo detailById = insurancePracticeDisComPayMapper.getDetailById(taskVo.getPaymentApplyComId());
            if (detailById.getPayFlag().equals(BooleanTypeEnum.YES.getCode())){
                result.append(ALREADY_PAID).append(";");
                continue;
            }
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(detailById.getPayId());
            String dateStr = paymentApplyVo.getLastDate();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime dateTime = LocalDateTime.parse(dateStr, formatter);
            // 获取截止时间为当天的16:00
            LocalDate dateOnly = dateTime.toLocalDate();
            LocalDateTime updatedTime = LocalDateTime.of(dateOnly, LocalTime.of(16, 0));

            LocalDateTime now = LocalDateTime.now();

            if (!now.toLocalDate().isEqual(dateOnly) || now.isAfter(updatedTime)) {
                result.append(EXCEED_LAST_DATE).append(";");
                continue;
            }


            InsurancePracticePayBankConfigVo payBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(detailById.getDisCom());
            if (Objects.isNull(payBankVo)){
                log.info("支付地银行账户信息:{}",payBankVo);
                result.append(MISSING_PAY_BANK).append(";");
                continue;
            }
            BigDecimal payAmt = detailById.getActPayAmt();
            boolean checkBalanceFlag = Boolean.parseBoolean(iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.CHECK_BALANCE_FLAG).getCfgValue());
            if (checkBalanceFlag){
                String resMsg = checkAndSetBalance(payBankVo.getDispatchBankNo(), payAmt, paymentApplyVo.getId());
                if (resMsg!=null){
                    result.append(resMsg).append(";");
                    continue;
                }
            }

            InsurancePracticePayBankConfigVo inBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
            if (Objects.isNull(inBankVo)){
                result.append(MISSING_RECEIVING_BANK).append(";");
                continue;
            }
            if (StringUtils.isBlank(inBankVo.getReceivingBankName())||StringUtils.isBlank(inBankVo.getReceivingBankNo())){
                result.append(INCOMPLETE_SOCIAL_BANK_INFO).append(";");
                continue;
            }
            List<OrgVo> allOrgName = orgnizationResourceWrapperService.findAllOrgName();
            Map<String, Integer> orgCodeAndCityMap = allOrgName.stream().collect(toMap(OrgVo::getOrgCode, OrgVo::getOwerCity));
            Integer cityCode = orgCodeAndCityMap.get(paymentApplyVo.getPayCom());
            String cityName = areaResourceWrapperService.getCityName(cityCode);
            if (payAmt.compareTo(BigDecimal.ZERO)==0){
                failCount = failCount-1;
                successCount++;
                Map<String, Object> variables = new HashMap<>();
                variables.put("loginName", loginName);
                workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
                insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
                continue;
            }
            Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
            //业务参考号
            YurrefArgs yurrefArgs = new YurrefArgs(Boolean.FALSE);
            TransAcctInfo transAcctInfo = new TransAcctInfo();
            transAcctInfo.setDbtAcc(payBankVo.getDispatchBankNo());
            transAcctInfo.setCrtAcc(inBankVo.getReceivingBankNo());
            transAcctInfo.setCrtNam(orgCodeAndNameMap.get(paymentApplyVo.getPayCom()));
            transAcctInfo.setCrtBnk(inBankVo.getReceivingBankName());
            transAcctInfo.setNusAge(paymentApplyVo.getPurpose()==null?"转账":paymentApplyVo.getPurpose());
            transAcctInfo.setCrtAdr(cityName);
            transAcctInfo.setYurRef(detailById.getYurref());
            transAcctInfo.setTrsAmt(payAmt.toEngineeringString());
            ResponseMsg<PaySingleResponseInfo> responseMsg = icmBpayWrapperService.corpSinglePay(transAcctInfo, yurrefArgs);
            PaySingleResponseInfo paySingleResponseInfo = responseMsg.getResponseData();
            if(paySingleResponseInfo==null){
                result.append(String.format(TRANSFER_REQUEST_FAILED,paymentApplyVo.getId())).append(";");
            }else {
                RespHead head = paySingleResponseInfo.getHead();
                if(Objects.equals(head.getResultcode(), CMBPayConstant.SUCCESS)){
                    List<PaySingleResponseInfo.PaySingleResult> resultList = paySingleResponseInfo.getBb1payopz1();
                    String errTxt = resultList.stream().map(PaySingleResponseInfo.PaySingleResult::getErrTxt).filter(Objects::nonNull).collect(Collectors.joining());
                    if(StringUtils.isNotBlank(errTxt)){
                        result.append(String.format(String.format(TRANSFER_RESULT_ERROR, paymentApplyVo.getId(), errTxt))).append(";");
                    }else {
                        failCount = failCount-1;
                        successCount++;
                        Map<String, Object> variables = new HashMap<>();
                        variables.put("loginName", loginName);
                        workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
                        insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
                    }
                }else {
                    result.append(String.format(TRANSFER_FAILED_WITH_REASON, paymentApplyVo.getId(), head.getResultmsg().split("-")[1])).append(";");
                }
            }
        }
        return String.format(MSG_PARTIAL_SUCCESS, successCount, failCount, result);

    }

    /**
     * 校验余额
     * @param bankNo
     * @param payAmt
     * @param paymentId
     * @return {@link String }
     */
    public String checkAndSetBalance(String bankNo,BigDecimal payAmt,Long paymentId){
        String queryBusinessNo = iSequenceService.getCMBQueryBusinessNo();
        List<QueryAccInfo> queryAccInfos=new ArrayList<>();
        QueryAccInfo queryAccInfo=new QueryAccInfo();
        queryAccInfo.setAccnbr(bankNo);
        queryAccInfos.add(queryAccInfo);
        ResponseMsg<QueryAcctInfoResInfo> responseMsg = icmBpayWrapperService.queryAcctInfoList(queryAccInfos, queryBusinessNo);
        QueryAcctInfoResInfo queryAcctInfoResInfo = responseMsg.getResponseData();
        List<AcctInfo> ntqacinfz = queryAcctInfoResInfo.getNtqacinfz().stream().distinct().collect(Collectors.toList());
        Map<String, AcctInfo> ntqacinMap = ntqacinfz.stream().collect(toMap(AcctInfo::getAccnbr, Function.identity()));
            AcctInfo acctInfo = ntqacinMap.get(bankNo);
            if (acctInfo==null){
                return String.format(MSG_ACCOUNT_NOT_FOUND, paymentId);
            }
            BigDecimal avlblv = new BigDecimal(acctInfo.getAvlblv());
            BigDecimal balance = avlblv.subtract(payAmt);
            if(balance.compareTo(BigDecimal.ZERO)<0){
                return String.format(MSG_BALANCE_NOT_ENOUGH, paymentId);
            }
       return null;
    }


    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printApplicationFrom(List<PrintPaymentFromExportVo> vos) {
        Map<String, List<PrintPaymentFromExportVo>> resultPrintMap = new HashMap<>();

        Map<Long, String> custIdAndNameMap = getCustomerMap();
        int i =1;
        for (PrintPaymentFromExportVo vo : vos) {
            InsurancePracticeDisComPayVo detailById = insurancePracticeDisComPayMapper.getDetailById(vo.getPaymentApplyComId());
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(detailById.getPayId());
            Map<String, String> userNameMap = userWrapperService.getAllUserMap();
            String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant(),paymentApplyVo.getPid()));
            String applicantName = userNameMap.get(paymentApplyVo.getApplicant());

            InsurancePracticePayBankConfigVo companyBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
            List<PrintPaymentFromExportVo> printPaymentFromExportVos = new ArrayList<>();
            List<InsurancePracticeCustPayDetailVo> printVos = insurancePracticeCustPayDetailMapper.getInsurancePracticeCustPayDetailListByPayIdAndDisComList(paymentApplyVo.getId(), Collections.singletonList(detailById.getDisCom()));
            for (InsurancePracticeCustPayDetailVo detailVo : printVos) {
                PrintPaymentFromExportVo printVo = new PrintPaymentFromExportVo();
                printVo.setActPayAmt(detailVo.getActPayAmt());
                printVo.setCustName(custIdAndNameMap.get(detailVo.getCustId()));
                printVo.setPayMonth(paymentApplyVo.getPayMonth());
                printVo.setNum(detailVo.getSum());
                printVo.setBankNo(companyBankVo.getReceivingBankNo());
                printVo.setBankName(companyBankVo.getReceivingBankName());
                printVo.setBankComCode(paymentApplyVo.getPayCom());
                printVo.setManager(largeName);
                printVo.setReceivingMan(applicantName);
                if (detailVo.getContractType()==99){
                    printVo.setProdType("派遣2");
                }else {
                    String contractTypeName = EnumsUtil.getNameByCode(detailVo.getContractType(), ContractType.class);
                    printVo.setProdType(SensitiveReplaceUtil.replace(contractTypeName, false));
                }
                printVo.setSocialAmt(detailVo.getSocialAmt());
                printVo.setProvidentAmt(detailVo.getProvidentAmt());
                printVo.setServiceAmt(detailVo.getServiceAmt());
                printVo.setBalanceAmt(detailVo.getBalanceAmt());
                printVo.setTotalAmt(detailVo.getSocialAmt().add(detailVo.getProvidentAmt()).add(detailVo.getServiceAmt()).add(detailVo.getBalanceAmt()));
                printPaymentFromExportVos.add(printVo);
            }
            resultPrintMap.put(i+","+detailById.getDisCom()+","+paymentApplyVo.getPayCom(), printPaymentFromExportVos);
            i++;

        }

        List<Long> payDomIdList = vos.stream().map(PrintPaymentFromExportVo::getPaymentApplyComId).collect(Collectors.toList());
        insurancePracticeDisComPayMapper.batchUpdatePrintTypeByIdList(payDomIdList);
        return resultPrintMap;
    }


    @Override
    public List<String> offlineTransfer(List<TaskVo> taskVos, String loginName) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("loginName", loginName);
        List<Long> idList = taskVos.stream().map(TaskVo::getPaymentApplyId).collect(Collectors.toList());
        List<InsurancePracticeDisComPayVo> noPayIdListByIdList = insurancePracticeDisComPayMapper.getNoPayIdListByPayIdList(idList);
        Map<Long, List<InsurancePracticeDisComPayVo>> payIdMap = noPayIdListByIdList.stream().collect(Collectors.groupingBy(InsurancePracticeDisComPayVo::getPayId));
        for (TaskVo taskVo : taskVos) {
            workflowWrapperService.excuteTask(taskVo.getId(), variables, ManualAction.PASS, ManualAction.PASS.getDescription());
            insurancePracticeDisComPayMapper.updatePayFlagById(taskVo.getPaymentApplyComId());
            insurancePracticeDisComPayMapper.updateOnlineFlagById(taskVo.getPaymentApplyComId());
        }
        Map<Long, List<TaskVo>> taskMap = taskVos.stream().collect(Collectors.groupingBy(TaskVo::getPaymentApplyId));
        List<String> pidList = new ArrayList<>();
        for (Long payId : idList) {
            List<TaskVo> taskVos1 = taskMap.get(payId);
            int taskComIdCount = taskVos1.size();
            List<InsurancePracticeDisComPayVo> payVos = payIdMap.get(payId);
            int payVoCount = payVos.size();
            if (taskComIdCount == payVoCount) {
                PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(payId);
                if (paymentApplyVo != null) {
                    pidList.add(paymentApplyVo.getPid());
                }
            }
        }
        return pidList;
    }




    @Override
    public List<PaymentApplyVo> getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo) {
        List<PaymentApplyVo> printReceivingApplicationFromPage = paymentApplyService.getPrintReceivingApplicationFromPage(paymentApplyVo);
        if (CollectionUtils.isEmpty(printReceivingApplicationFromPage)){
            return Collections.emptyList();
        }
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        Set<Long> payIdSet = printReceivingApplicationFromPage.stream().map(PaymentApplyVo::getId).collect(Collectors.toSet());
        List<InsurancePracticeOneFeeVo> insurancePracticeOneFeeListByPayIdSet = insurancePracticeOneFeeService.getInsurancePracticeOneFeeListByPayIdSet(payIdSet);
        Map<Long, Date> payIdAndCreateTimeMap = insurancePracticeOneFeeListByPayIdSet.stream()
                .collect(Collectors.toMap(
                        InsurancePracticeOneFeeVo::getPaymentId,
                        InsurancePracticeOneFeeVo::getCreateTime,
                        (existing, replacement) -> {
                            return existing.before(replacement) ? existing : replacement;
                        }
                ));
        List<String> payComList = printReceivingApplicationFromPage.stream().map(PaymentApplyVo::getPayCom).collect(Collectors.toList());
        List<InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigByOrgCodeList = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCodeList(payComList);
        Map<String, InsurancePracticePayBankConfigVo> bankConfigVoMap = insurancePracticePayBankConfigByOrgCodeList.stream().collect(toMap(InsurancePracticePayBankConfigVo::getOrgCode, Function.identity()));
        List<PaymentApplyVo> printReceivingApplicationList = new ArrayList<>();
        for (PaymentApplyVo applyVo : printReceivingApplicationFromPage) {
            setPackName(applyVo.getId(),applyVo);
            applyVo.setDisComName(orgCodeAndNameMap.get(applyVo.getDisCom()));
            applyVo.setPayComName(orgCodeAndNameMap.get(applyVo.getPayCom()));
            applyVo.setPrintType(BooleanTypeEnum.YES.getCode());
            applyVo.setOneFeeFlag(BooleanTypeEnum.NO.getCode());
            if (!payIdAndCreateTimeMap.isEmpty()&&applyVo.getAppStatus().equals(PaymentApplyProcessStatus.FINISHED.getCode())){
                if (!payIdAndCreateTimeMap.containsKey(applyVo.getId())){
                    applyVo.setOneFeeFlag(BooleanTypeEnum.NO.getCode());
                }else if (!applyVo.getPassTime().before(payIdAndCreateTimeMap.get(applyVo.getId()))){
                    applyVo.setOneFeeFlag(BooleanTypeEnum.YES.getCode());
                    applyVo.setBalanceAmt(applyVo.getPayAmt().subtract(applyVo.getActPayAmt()));
                }
            }
            if (applyVo.getAppStatus().equals(PaymentApplyProcessStatus.FINISHED.getCode())){
                setBankInfo(applyVo,bankConfigVoMap);
                printReceivingApplicationList.add(applyVo);
            }else {
                String currentApproveName = workflowWrapperService.getCurrentApproveName(applyVo.getPid());
                if ("接单地财务上传凭证".equals(currentApproveName)||"客服上传凭证".equals(currentApproveName)){
                    setBankInfo(applyVo,bankConfigVoMap);
                    printReceivingApplicationList.add(applyVo);
                }else if ("派单地财务支付".equals(currentApproveName)){
                    applyVo.setPrintType(BooleanTypeEnum.NO.getCode());
                    printReceivingApplicationList.add(applyVo);
                }
            }

        }
        return printReceivingApplicationList;
    }

    @Override
    public Map<String, List<PrintPaymentFromExportVo>> printAllDisApplicationFrom(PrintPaymentFromExportVo vo) {
        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(vo.getId());
        InsurancePracticePayBankConfigVo companyBankVo = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
        Map<String, String> userNameMap = userWrapperService.getAllUserMap();
        String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant(),paymentApplyVo.getPid()));
        String applicantName = userNameMap.get(paymentApplyVo.getApplicant());
        List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(vo.getId());
        List<String> disComList = insurancePracticeDisComListByPayId.stream().map(InsurancePracticeDisComPayVo::getDisCom).collect(Collectors.toList());
        List<InsurancePracticeCustPayDetailVo> detailVos = insurancePracticeCustPayDetailMapper.getInsurancePracticeCustPayDetailListGroupByPayIdAndDisComList(vo.getId(), disComList);
        Map<String, InsurancePracticeCustPayDetailVo> disComDetailMap = detailVos.stream().collect(Collectors.toMap(InsurancePracticeCustPayDetailVo::getDisCom,Function.identity()));
        List<PrintPaymentFromExportVo> printPaymentFromExportVos = new ArrayList<>();

        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        for (String disCom : disComList) {
            PrintPaymentFromExportVo printPaymentFromExportVo = new PrintPaymentFromExportVo();
            printPaymentFromExportVo.setCustName(orgCodeAndNameMap.get(disCom));
            printPaymentFromExportVo.setProdType("派遣2");
            printPaymentFromExportVo.setPayMonth(paymentApplyVo.getPayMonth());
            printPaymentFromExportVo.setReceivingMan(applicantName);
            printPaymentFromExportVo.setManager(largeName);
            printPaymentFromExportVo.setBankNo(companyBankVo.getReceivingBankNo());
            printPaymentFromExportVo.setBankName(companyBankVo.getReceivingBankName());
            printPaymentFromExportVo.setBankComCode(paymentApplyVo.getPayCom());
            InsurancePracticeCustPayDetailVo custPayDetailVos = disComDetailMap.get(disCom);
            printPaymentFromExportVo.setNum(custPayDetailVos.getSum());
            printPaymentFromExportVo.setServiceAmt(custPayDetailVos.getServiceAmt());
            printPaymentFromExportVo.setSocialAmt(custPayDetailVos.getSocialAmt());
            printPaymentFromExportVo.setProvidentAmt(custPayDetailVos.getProvidentAmt());
            printPaymentFromExportVo.setActPayAmt(custPayDetailVos.getActPayAmt());
            printPaymentFromExportVo.setBalanceAmt(custPayDetailVos.getBalanceAmt());
            printPaymentFromExportVo.setTotalAmt(custPayDetailVos.getServiceAmt().add(custPayDetailVos.getProvidentAmt()).add(custPayDetailVos.getSocialAmt()).add(custPayDetailVos.getBalanceAmt()));
            printPaymentFromExportVos.add(printPaymentFromExportVo);
        }

        Map<String, List<PrintPaymentFromExportVo>> resultMap = new HashMap<>();

        resultMap.put(1+","+paymentApplyVo.getPayCom()+","+paymentApplyVo.getPayCom(), printPaymentFromExportVos);
        return resultMap;
    }

    @Override
    public InsurancePracticeDisComPayVo getInsurancePracticeDisComPayById(Long id) {
        return insurancePracticeDisComPayMapper.getDetailById(id);
    }

    @Override
    public List<InsurancePracticeDisComPayVo> checkProgressByPayId(Long payId) {
        List<InsurancePracticeDisComPayVo> payVos = insurancePracticeDisComPayMapper.checkProgressByPayId(payId);
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        if (CollectionUtils.isNotEmpty(payVos)){
            for (InsurancePracticeDisComPayVo payVo : payVos) {
                String disComApp = payVo.getDisComApp();
//                String[] parts = disComApp.split(",");
//                String loginName = userWrapperService.findLoginNameByPosCodeAndOrgCode(parts[0], parts[1]);
               // payVo.setDisComApp(loginName);
                payVo.setDisCom(orgCodeAndNameMap.get(payVo.getDisCom()));
            }
        }
        return payVos;
    }

    @Override
    public Map<String, List<PrintReceivingApplicationFromVo>> printReceivingApplicationFrom(PrintPaymentFromExportVo vo) {
        HashMap<String, List<PrintReceivingApplicationFromVo>> resultMap = new HashMap<>();

        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(vo.getId());
        Map<String, String> userNameMap = userWrapperService.getAllUserMap();
        String largeName = userNameMap.get(getLargerName(paymentApplyVo.getApplicant(),paymentApplyVo.getPid()));
        String applicantName = userNameMap.get(paymentApplyVo.getApplicant());
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        Map<Long, String> customerMap = getCustomerMap();
        Map<String, BigDecimal> serviceMap = getServiceMap();

        List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = insurancePracticeDisComPayMapper.getInsurancePracticeDisComListByPayId(vo.getId());
        List<String> disComList = insurancePracticeDisComListByPayId.stream().map(InsurancePracticeDisComPayVo::getDisCom).collect(Collectors.toList());
        List<PrintReceivingApplicationFromVo> providentVoList = new ArrayList<>();
        List<PrintReceivingApplicationFromVo> socialVoList = new ArrayList<>();
        if (!disComList.isEmpty()){
            disComList.remove(paymentApplyVo.getPayCom());
                if (!disComList.isEmpty()){
                    List<InsurancePracticeCustPayDetailVo> detailVos = insurancePracticeCustPayDetailMapper.getInsurancePracticeCustPayDetailListGroupByPayIdAndDisComList(vo.getId(), disComList);
                    Map<String, InsurancePracticeCustPayDetailVo> disComDetailMap = detailVos.stream().collect(Collectors.toMap(InsurancePracticeCustPayDetailVo::getDisCom,Function.identity()));
                    for (String disCom : disComDetailMap.keySet()) {
                        String disComName = orgCodeAndNameMap.get(disCom);
                        PrintReceivingApplicationFromVo providentVo = new PrintReceivingApplicationFromVo();
                        providentVo.setCustName(disComName);
                        providentVo.setProdType("派遣2");
                        providentVo.setManager(largeName);
                        providentVo.setReceivingMan(applicantName);
                        PrintReceivingApplicationFromVo socialVo = new PrintReceivingApplicationFromVo();
                        socialVo.setCustName(disComName);
                        socialVo.setProdType("派遣2");
                        socialVo.setManager(largeName);
                        socialVo.setReceivingMan(applicantName);
                        InsurancePracticeCustPayDetailVo insurancePracticeCustPayDetailVo = disComDetailMap.get(disCom);
                        socialVo.setSocialAmt(insurancePracticeCustPayDetailVo.getSocialAmt());
                        providentVo.setProvidentAmt(insurancePracticeCustPayDetailVo.getProvidentAmt());
                        if (insurancePracticeCustPayDetailVo.getProvidentAmt()!=null&&insurancePracticeCustPayDetailVo.getProvidentAmt().compareTo(BigDecimal.ZERO)>0){
                            providentVo.setCountAmt(insurancePracticeCustPayDetailVo.getProvidentAmt());
                            providentVo.setPayTime(paymentApplyVo.getRemark());
                            providentVo.setMonth(paymentApplyVo.getPayMonth());
                            providentVoList.add(providentVo);
                        }
                        if (insurancePracticeCustPayDetailVo.getSocialAmt()!=null&&insurancePracticeCustPayDetailVo.getSocialAmt().compareTo(BigDecimal.ZERO)>0){
                            socialVo.setCountAmt(insurancePracticeCustPayDetailVo.getSocialAmt());
                            socialVo.setPayTime(paymentApplyVo.getRemark());
                            socialVo.setMonth(paymentApplyVo.getPayMonth());
                            socialVoList.add(socialVo);
                        }
                    }
                }
        }

        List<InsurancePracticeCustPayDetailVo> detailVos = insurancePracticeCustPayDetailMapper.getInsurancePracticeCustPayDetailListByPayIdAndDisComList(vo.getId(), Collections.singletonList(paymentApplyVo.getPayCom()));
        Map<Long, InsurancePracticeCustPayDetailVo> custIdMap = detailVos.stream().collect(toMap(InsurancePracticeCustPayDetailVo::getCustId, Function.identity()));
        for (Long custId : custIdMap.keySet()) {
            InsurancePracticeCustPayDetailVo detailVo = custIdMap.get(custId);
            if (detailVo.getContractType()==99){
                continue;
            }
            String contractTypeName = EnumsUtil.getNameByCode(detailVo.getContractType(), ContractType.class);
            PrintReceivingApplicationFromVo providentVo = new PrintReceivingApplicationFromVo();
            providentVo.setCustName(customerMap.get(custId));
            providentVo.setProdType(contractTypeName);
            providentVo.setManager(largeName);
            providentVo.setReceivingMan(applicantName);
            PrintReceivingApplicationFromVo socialVo = new PrintReceivingApplicationFromVo();
            socialVo.setCustName(customerMap.get(custId));
            socialVo.setProdType(contractTypeName);
            socialVo.setManager(largeName);
            socialVo.setReceivingMan(applicantName);
            socialVo.setNum(detailVo.getSum());
            providentVo.setNum(detailVo.getSum());
            if (detailVo.getSocialAmt()!=null&&detailVo.getSocialAmt().compareTo(BigDecimal.ZERO)>0){
                socialVo.setSocialAmt(detailVo.getSocialAmt());
                socialVo.setCountAmt(detailVo.getSocialAmt());
                socialVo.setPayTime(paymentApplyVo.getRemark());
                socialVo.setMonth(paymentApplyVo.getPayMonth());
                socialVoList.add(socialVo);
            }
            if (detailVo.getProvidentAmt()!=null&&detailVo.getProvidentAmt().compareTo(BigDecimal.ZERO)>0){
                providentVo.setProvidentAmt(detailVo.getProvidentAmt());
                providentVo.setCountAmt(detailVo.getProvidentAmt());
                providentVo.setPayTime(paymentApplyVo.getRemark());
                providentVo.setMonth(paymentApplyVo.getPayMonth());
                providentVoList.add(providentVo);
            }

        }

        resultMap.put("社保", socialVoList);
        resultMap.put("公积金", providentVoList);
        return resultMap;
    }


    @Override
    public Map<String, List<InsurancePracticeExportByMonthAndDisComVo>> exportDisComPayByMonth(Integer payMonth, String disCom) {
        LocalDate currentDate = LocalDate.now();
        int currentYearMonth = currentDate.getYear() * 100 + currentDate.getMonthValue();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String firstDay = null;
        String lastDay = null;
        if (payMonth == currentYearMonth) {
            lastDay = currentDate.format(formatter);
            LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);
            firstDay =  firstDayOfMonth.format(formatter);
        } else {
            // 否则输出该月的第一天 和 最后一天
            int year = payMonth / 100;
            int month = payMonth % 100;

            // 检查月份是否合法（1~12）
            if (month < 1 || month > 12) {
                log.info("月份不合法{}",payMonth);
                return Collections.emptyMap();
            }

            YearMonth ym = YearMonth.of(year, month);
            LocalDate firstDayDate = ym.atDay(1);
            LocalDate lastDayDate = ym.atEndOfMonth();

            firstDay = firstDayDate.format(formatter);
            lastDay =  lastDayDate.format(formatter);
        }
        List<InsurancePracticeCustPayDetailVo> detailVoList = insurancePracticeCustPayDetailMapper.getInsurancePracticeCustPayDetailListByPayMonthAndDisCom(firstDay,lastDay, disCom);
        if (CollectionUtils.isEmpty(detailVoList)){
            return Collections.emptyMap();
        }
        Map<String, List<InsurancePracticeExportByMonthAndDisComVo>> resultMap = new HashMap<>();
        Map<Long, String> customerMap = getCustomerMap();
        Map<String, String> orgCodeAndNameMap = getOrgCodeAndNameMap();
        Map<String, List<InsurancePracticeCustPayDetailVo>> payComMap = detailVoList.stream().collect(Collectors.groupingBy(InsurancePracticeCustPayDetailVo::getPayCom));
        for (String payCom : payComMap.keySet()) {
            Map<Long, List<InsurancePracticeCustPayDetailVo>> custIdMap = payComMap.get(payCom).stream().collect(Collectors.groupingBy(InsurancePracticeCustPayDetailVo::getCustId));
            List<InsurancePracticeExportByMonthAndDisComVo> monthAndDisComVos = new ArrayList<>();
            BigDecimal serviceTotalAmt = BigDecimal.ZERO;
            BigDecimal socialTotalAmt = BigDecimal.ZERO;
            BigDecimal providentTotalAmt = BigDecimal.ZERO;
            BigDecimal balanceTotalAmt = BigDecimal.ZERO;
            BigDecimal totalAmt = BigDecimal.ZERO;

            for (Long custId : custIdMap.keySet()) {
                List<InsurancePracticeCustPayDetailVo> custPayDetailVos = custIdMap.get(custId);
                InsurancePracticeExportByMonthAndDisComVo monthAndDisComVo = new InsurancePracticeExportByMonthAndDisComVo();
                monthAndDisComVo.setCustName(customerMap.get(custId));
                BigDecimal serviceAmt = custPayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getServiceAmt()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal socialAmt = custPayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getSocialAmt()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal providentAmt = custPayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getProvidentAmt()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal balanceAmt = custPayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getBalanceAmt()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal actPayAmt = custPayDetailVos.stream()
                        .map(vo -> Optional.ofNullable(vo.getActPayAmt()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                serviceTotalAmt= serviceTotalAmt.add(serviceAmt);
                socialTotalAmt= socialTotalAmt.add(socialAmt);
                providentTotalAmt= providentTotalAmt.add(providentAmt);
                balanceTotalAmt= balanceTotalAmt.add(balanceAmt);
                totalAmt= totalAmt.add(actPayAmt);
                monthAndDisComVo.setServiceFee(serviceAmt);
                monthAndDisComVo.setSocialAmt(socialAmt);
                monthAndDisComVo.setProvidentAmt(providentAmt);
                monthAndDisComVo.setBalanceAmt(balanceAmt);
                monthAndDisComVo.setActualPayAmt(actPayAmt);
                monthAndDisComVos.add(monthAndDisComVo);
                if (custPayDetailVos.get(0).getContractType()==99){
                    monthAndDisComVo.setProdType("派遣2");
                }else {
                    String contractTypeName = EnumsUtil.getNameByCode(custPayDetailVos.get(0).getContractType(), ContractType.class);
                    monthAndDisComVo.setProdType(SensitiveReplaceUtil.replace(contractTypeName, false));
                }
            }
            InsurancePracticeExportByMonthAndDisComVo insurancePracticeExportByMonthAndDisComVo = new InsurancePracticeExportByMonthAndDisComVo();
            insurancePracticeExportByMonthAndDisComVo.setServiceFee(serviceTotalAmt);
            insurancePracticeExportByMonthAndDisComVo.setSocialAmt(socialTotalAmt);
            insurancePracticeExportByMonthAndDisComVo.setProvidentAmt(providentTotalAmt);
            insurancePracticeExportByMonthAndDisComVo.setBalanceAmt(balanceTotalAmt);
            insurancePracticeExportByMonthAndDisComVo.setActualPayAmt(totalAmt);
            insurancePracticeExportByMonthAndDisComVo.setCustName("汇总");
            monthAndDisComVos.add(0, insurancePracticeExportByMonthAndDisComVo);
            resultMap.put(orgCodeAndNameMap.get(payCom), monthAndDisComVos);
        }

        return resultMap;
    }

    public void setBankInfo(PaymentApplyVo paymentApplyVo, Map<String, InsurancePracticePayBankConfigVo> bankConfigVoMap){
        InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = bankConfigVoMap.get(paymentApplyVo.getPayCom());
        if (!Objects.isNull(insurancePracticePayBankConfigVo)){
            paymentApplyVo.setBankType(insurancePracticePayBankConfigVo.getReceivingBankType());
            paymentApplyVo.setBankNo(insurancePracticePayBankConfigVo.getReceivingBankNo());
            paymentApplyVo.setPayBankName(insurancePracticePayBankConfigVo.getReceivingBankName());
        }
    }


    public Map<String, BigDecimal> getServiceMap(){
        List<InsurancePracticeServiceConfigVo> insurancePracticeServiceConfig = insurancePracticeServiceConfigWrapperService.getInsurancePracticeServiceConfig();
        return insurancePracticeServiceConfig.stream()
                .collect(Collectors.toMap(InsurancePracticeServiceConfigVo::getOrgCode, InsurancePracticeServiceConfigVo::getServiceAmt));

    }





    public Map<Long, String> getCustomerMap(){
        List<CustomerVo> allCustomer = customerWrapperService.findAllCustomer();
        return allCustomer.stream()
                .collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
    }

    public Map<String, String> getOrgCodeAndNameMap(){
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        return allCompany.stream().collect(toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
    }



    public String getLargerName(String applicant,String pid){
        List<WorkflowComentVo> workflowAuditLogList = workflowWrapperService.getWorkflowAuditLogList(pid);
        UserOrgPosVo insurancePayApproveOrgPos = iUserOrgPosWrapperService.getInsurancePayApproveOrgPos(applicant);
        if (insurancePayApproveOrgPos!=null&&insurancePayApproveOrgPos.getApproveOrgPosCode()!=null){
            List<WorkflowComentVo> collect = workflowAuditLogList.stream().filter(c -> c.getUserId().equals(insurancePayApproveOrgPos.getApproveOrgPosCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                return collect.get(0).getCreator();
            }
        }
        return "暂无";
    }

    public void setPackName(Long id,PaymentApplyVo applyVo){
        List<PracticeLockInfoVo> lockInfosByPayId = insurancePracticeLockService.getLockInfosByPayId(id);
        if (CollectionUtils.isNotEmpty(lockInfosByPayId)){
            List<String> packCodeList = lockInfosByPayId.stream().map(PracticeLockInfoVo::getPackCode).collect(Collectors.toList());
            List<InsurancePackVo> allInsuPackByPackCodeList = iInsurancePackResourceWrapperService.getAllInsuPackByPackCodeList(packCodeList);
            String packNames = allInsuPackByPackCodeList.stream().map(InsurancePackVo::getPackName).collect(Collectors.joining(","));
            applyVo.setPackName(packNames);
        }
    }

}


