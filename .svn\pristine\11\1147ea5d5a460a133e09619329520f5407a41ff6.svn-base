package com.reon.hr.api.customer.dubbo.service.rpc.qiyuesuo;

import com.reon.hr.api.customer.vo.qiyuesuo.QysContractApprovalVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年05月14日
 * @Version 1.0
 */
public interface IQysContractApprovalWrapperService {

    List<QysContractApprovalVo> getQysContractApprovalListByContractId(Long contractId);

    void updateStatusAndCurrentById(Integer current, Integer status, Date passTime,Long id);

    void updateCurrentByContractIdAndContractId(Integer current,Long id,Long contractId);


    void deleteByContractId(Long contractId);

    void updateContractStatusAndAppStatus(List<QysContractApprovalVo> qysContractApprovalVos,Long contractId,Integer effective,Integer contractStatus);


    void updateAppCreator(String loginName, String qysContractApprovalId);

    List<QysContractApprovalVo> getCurrentApprovalByContractIds(List<Long> qysContractIdList);
}
