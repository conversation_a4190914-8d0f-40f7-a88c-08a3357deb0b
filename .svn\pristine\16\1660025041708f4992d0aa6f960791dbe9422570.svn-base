/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/3/16
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.core.vo;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Worker
 *
 * @date 2021/3/16 15:02
 */


import java.util.Date;


import org.springframework.web.multipart.MultipartFile;

public class Worker {

	private Integer id;
	private Integer projectWorktypeId;
	private String workerName;
	private Integer workerAge;
	private String workerPhone;
	private String workerCard;
	private String workerScanFront;
	private String workerScanBack;
	private String bankType;
	private String bankNumber;
	private Integer status;
	private String createBy;
	private Date createTime;
	private String modifyBy;
	private Date modifyTime;
	private Integer leader;
	private String corpCode;
	private Integer deleted;
	private Integer version;

	private Integer count;

	private String image;
	private MultipartFile file;

	private String templeteName;

	private String contractContent;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getProjectWorktypeId() {
		return projectWorktypeId;
	}

	public void setProjectWorktypeId(Integer projectWorktypeId) {
		this.projectWorktypeId = projectWorktypeId;
	}

	public String getWorkerName() {
		return workerName;
	}

	public void setWorkerName(String workerName) {
		this.workerName = workerName;
	}

	public Integer getWorkerAge() {
		return workerAge;
	}

	public void setWorkerAge(Integer workerAge) {
		this.workerAge = workerAge;
	}

	public String getWorkerPhone() {
		return workerPhone;
	}

	public void setWorkerPhone(String workerPhone) {
		this.workerPhone = workerPhone;
	}

	public String getWorkerCard() {
		return workerCard;
	}

	public void setWorkerCard(String workerCard) {
		this.workerCard = workerCard;
	}

	public String getWorkerScanFront() {
		return workerScanFront;
	}

	public void setWorkerScanFront(String workerScanFront) {
		this.workerScanFront = workerScanFront;
	}

	public String getWorkerScanBack() {
		return workerScanBack;
	}

	public void setWorkerScanBack(String workerScanBack) {
		this.workerScanBack = workerScanBack;
	}

	public String getBankType() {
		return bankType;
	}

	public void setBankType(String bankType) {
		this.bankType = bankType;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModifyBy() {
		return modifyBy;
	}

	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Integer getLeader() {
		return leader;
	}

	public void setLeader(Integer leader) {
		this.leader = leader;
	}

	public String getCorpCode() {
		return corpCode;
	}

	public void setCorpCode(String corpCode) {
		this.corpCode = corpCode;
	}

	public Integer getDeleted() {
		return deleted;
	}

	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public MultipartFile getFile() {
		return file;
	}

	public void setFile(MultipartFile file) {
		this.file = file;
	}

	public String getTempleteName() {
		return templeteName;
	}

	public void setTempleteName(String templeteName) {
		this.templeteName = templeteName;
	}

	public String getContractContent() {
		return contractContent;
	}

	public void setContractContent(String contractContent) {
		this.contractContent = contractContent;
	}

	@Override
	public String toString() {
		return "Worker [id=" + id + ", projectWorktypeId=" + projectWorktypeId
				+ ", workerName=" + workerName + ", workerAge=" + workerAge
				+ ", workerPhone=" + workerPhone + ", workerCard=" + workerCard
				+ ", workerScanFront=" + workerScanFront + ", workerScanBack="
				+ workerScanBack + ", bankType=" + bankType + ", bankNumber="
				+ bankNumber + ", status=" + status + ", createBy=" + createBy
				+ ", createTime=" + createTime + ", modifyBy=" + modifyBy
				+ ", modifyTime=" + modifyTime + ", leader=" + leader
				+ ", corpCode=" + corpCode + ", deleted=" + deleted
				+ ", version=" + version + ", count=" + count + ", image="
				+ image + ", file=" + file + ", templeteName=" + templeteName
				+ ", contractContent=" + contractContent + "]";
	}


}
