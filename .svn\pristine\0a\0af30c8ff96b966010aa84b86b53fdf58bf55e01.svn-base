var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table, form = layui.form,
        $ = layui.$,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    //年月选择器
    laydate.render({
        elem: '#paymentDate'
        , type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#paymentDate").val(value);
                    $(".layui-laydate").remove();
                }
            }
        }
    });
    //年月选择器
    laydate.render({
        elem: '#paymentDateSearch'
        , type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#paymentDateSearch").val(value);
                    $(".layui-laydate").remove();
                }
            }
        }
    });
    $("#insertSalaryPaymentComparison").click(function (data) {
        var paymentDate = $("#paymentDate").val();
        if(!paymentDate){
            return layer.msg("请选择支付年月！");
        }
        //按钮失效
        $("#insertSalaryPaymentComparison").attr("disabled", true);
        layer.msg("数据生成中,按钮失效,请等待响应后再点击");
        $.ajax({
            type: 'POST',
            url: ctx + "/customer/salary/salaryPayment/insertSalaryPaymentComparison",
            data: {
                paymentDate: paymentDate
            },
            success: function (data) {
                if (data) {
                    layer.msg("数据生成成功！")
                } else {
                    layer.msg("数据生成失败！")
                }
                //按钮生效
                $("#insertSalaryPaymentComparison").attr("disabled", false);
            }, error: function () {
                //按钮生效
                $("#insertSalaryPaymentComparison").attr("disabled", false);
            }
        })
        return false;
    });

    //年月选择器
    laydate.render({
        elem: '#taxMonth'
        , type: 'month',
        min: '2010-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
            initYear = date.year;
        },
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#taxMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
        }
    });
    $("#insertSalaryPaymentTaxComparison").click(function (data) {
        var taxMonth = $("#taxMonth").val();
        var taxComparisonType = $("#taxComparisonType").val();
        if(!taxMonth){
            return layer.msg("请选择支付年月！");
        }
        //按钮失效
        $("#insertSalaryPaymentTaxComparison").attr("disabled", true);
        layer.msg("数据生成中,按钮失效,请等待响应后再点击");
        $.ajax({
            type: 'POST',
            url: ctx + "/customer/salary/salaryPayment/insertSalaryPaymentTaxComparison",
            data: {
                taxMonth: taxMonth,
                taxComparisonType: taxComparisonType
            },
            success: function (data) {
                if (data) {
                    layer.msg("数据生成成功！")
                } else {
                    layer.msg("数据生成失败！")
                }
                //按钮生效
                $("#insertSalaryPaymentTaxComparison").attr("disabled", false);
            }, error: function () {
                //按钮生效
                $("#insertSalaryPaymentTaxComparison").attr("disabled", false);
            }
        })
        return false;
    });

//查询工资实发数据导入信息
    table.render({
        id: 'salaryPaymentComparisonGrid'
        , elem: '#salaryPaymentComparisonGrid'
        , url: ML.contextPath + '/customer/salary/salaryPayment/getSalaryPaymentComparisonListPage'
        , page: true
        , toolbar: '#toolbarDemo'
        , defaultToolbar: []
        , limit: 50
        , method: 'GET'
        , limits: [50, 100, 200],
        height: 'full-200'
        , text: {
            none: '暂无数据，请点击查询' //无数据时展示
        }
        , cols: [[
            {field: '', type: 'numbers', width: '50', fixed: 'left'}
            , {field: 'empName', title: '姓名', width: '150', align: 'center'}
            , {field: 'certNo', title: '证件号码', width: '200', align: 'center'}
            , {field: 'salaryComparisonType', title: '工资比较类型', width: '150', align: 'center',templet: function(d){
                    return ML.dictFormatter("SALARY_COMPARISON_TYPE",d.salaryComparisonType);
                }}
            , {field: 'difference', title: '差异', width: '150', align: 'center'}
            , {field: 'paymentDate', title: '支付年月', align: 'center', width: '150'}
            , {field: 'taxMonth', title: '计税月', align: 'center', width: '150'}
            , {field: 'remark', title: '备注', align: 'center', width: '150'}
            , {field: 'commissioner', title: '项目客服', align: 'center', width: '150'}
            , {field: 'salaryCreator', title: '薪资创建人', align: 'center', width: '150'}
            , {field: 'custName', title: '客户名称', align: 'center', width: '250'}
            , {field: 'withholdingAgentName', title: '扣缴义务人名称', align: 'center', width: '250'}
        ]],
        done: function (res) {
            ML.hideNoAuth();
            table.on('toolbar(salaryPaymentComparisonGridFilter)', function (obj) {
                switch (obj.event) {
                    case 'export':
                        window.open(ctx+'/customer/salary/salaryPayment/exportSalaryPaymentComparisonList?paramData=' +
                            JSON.stringify(serialize("searchForm")))
                        break;
                }
            });
        }
    });
//初始化表单数据
    form.on('submit(btnQuery)', function (data) {
        table.reload('salaryPaymentComparisonGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });
});