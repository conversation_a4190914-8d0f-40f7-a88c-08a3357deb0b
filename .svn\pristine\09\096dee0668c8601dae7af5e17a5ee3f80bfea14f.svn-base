package com.reon.hr.api.customer.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年04月26日
 * @Version 1.0
 */
@Getter
public enum QYSOrderContractFileEnum {

    QYS_ENTRY("契约锁入职合同文件",1),
    QYS_DEPART("契约锁离职合同文件",2),
    QYS_LETTER("契约锁辞职信文件",3);


    // 成员变量
    private String name;
    private Integer code;

    // 构造方法
    QYSOrderContractFileEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }
}
