var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        upload = layui.upload,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var fileId = "";
    laydate.render({
        elem: '#endDate',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
    });

    //时间范围
    laydate.render({
        elem: '#endTime'
        , type: 'time'
    });

    //日期点击事件
    var initYear;
    laydate.render({
        elem: '#exeMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#exeMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    //上传文件
    upload.render({
        elem: '#policyFile',
        url: ctx + "/sys/file/upload",
        accept: 'file',///普通文件
        exts: 'zip|rar|jpg|png|jpeg|doc|docx|pdf',
        auto: true,
        before: function () {
            layer.load();
        },
        //上传成功的回调
        done: function (res, index, upload) {
            layer.msg(res.msg);
            if (res.code == 0) {
                fileId = res.data.fileId;
                $("#uploadId").find("button").hide();
                $("#uploadId").append("<a href='" + ML.fileServerUrl + fileId + "' target='_blank' >查看文件</a><a href='javascript:void(0)' title='删除' id='deleteFileId'><i class='layui-icon layui-icon-delete'></i></a>")
            }
            layer.closeAll('loading');
        },
        error: function (index, upload) {
            layer.alert('上传失败！' + index);
            layer.closeAll('loading');
        }
    });
    $(document).on('click', '#deleteFileId', function () {
        ML.ajax("/change/issue/delByFileId?fileId=" + fileId, {}, function (result) {
                if (result.code == 0) {
                    $("#uploadId").find("a").hide();
                    $("#uploadId").find("button").show();
                }
            },
            'POST');
    });

    //初始化表单
    form.on('submit(save)', function (data) {
        delete data.field['file'];
        data.field['policyFile'] = fileId;
        ML.layuiButtonDisabled($('#save'));//禁用
        $.ajax({
            type: 'POST',
            url: ctx + "/change/issue/save",
            data: JSON.stringify(data.field),
            dataType: 'json',
            contentType: 'application/json',//添加这句话
            success: function (results) {
                layer.msg("保存成功！");
                ML.layuiButtonDisabled($('#save'), true);// 取消禁用
                layer.closeAll('iframe');
            },
            error: function (resp, textStatus, errorThrown) {
                ML.layuiButtonDisabled($('#save'), true);// 取消禁用
                ML.ajaxErrorCallback(resp, textStatus, errorThrown);
            }
        });
    });


});