<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.reon.hr.sp.customer.dao.supplierPractice.SupplierSyncCfgMapper">

    <update id="updateByIds">
        update supplier_sync_cfg set del_flag = 'Y' WHERE id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>

    </update>

    <select id="getSupplierSyncCfgVoPage"
            resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierSyncCfgVo">
        select t1.*,s.supplier_name
        from supplier_sync_cfg t1 left join supplier s on t1.supplier_id = s.id
        where 1 = 1
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        <if test="cityCode != null">
            and city_code = #{cityCode}
        </if>
        <if test="prodCode != null">
            and prod_code = #{prodCode}
        </if>
        order by create_time desc
    </select>

    <select id="getSupplierSyncCfgVoBySupIdAndCityCode"
            resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierSyncCfgVo">
        select * from supplier_sync_cfg where concat(supplier_id,'_',city_code) in
        <foreach collection="params" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>