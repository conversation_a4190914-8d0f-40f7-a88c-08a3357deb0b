package com.reon.hr.modules.report.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.TaskLogWrapperService;
import com.reon.hr.api.bill.enums.TaskTypeEnum;
import com.reon.hr.api.bill.utils.DateUtil;
import com.reon.hr.api.bill.vo.TaskLogVo;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAreaResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.PracSingleAccountWrapperService;
import com.reon.hr.api.customer.vo.OrderAndInsuranceDiffParamVo;
import com.reon.hr.api.customer.vo.employee.ConfirmOrderChangeVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.modules.common.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description 订单与实做差异报表
 * @Date 2025年02月06日
 * @Version 1.0
 */
@RestController
@RequestMapping("/report/")
@Slf4j
public class OrderAndInsuranceDiffController extends BaseController {


    @Autowired
    private TaskLogWrapperService taskLogWrapperService;


    @Resource
    private IOrgnizationResourceWrapperService iOrgnizationResourceWrapperService;

    @Resource
    private IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;


    @RequestMapping("/gotoOrderAndInsuranceDiffPage")
    public ModelAndView gotoOrderAndInsuranceDiffPage(){
        return new ModelAndView("/report/orderAndInsuranceDiffPage");
    }

    @RequestMapping("/exportOrderAndInsuranceDiff")
    public Object exportOrderAndInsuranceDiff(@RequestBody OrderAndInsuranceDiffParamVo paramVo){
        if (Objects.isNull(paramVo.getStartMonth())||Objects.isNull(paramVo.getArea())|| CollectionUtils.isEmpty(paramVo.getCityCodeList())||Objects.isNull(paramVo.getEndMonth())){
            return LayuiReplay.error("请将参数选择完整");
        }

        List<Integer> revMonthList =new ArrayList<>();
        try {
            revMonthList = DateUtil.getBillMonth(paramVo.getStartMonth(), paramVo.getEndMonth());
            paramVo.setMonthList(revMonthList);
            if (revMonthList.size()>2&&StringUtils.isBlank(paramVo.getOrgCode())){
                return LayuiReplay.error("导出月份超过二个月时要选择福利办理方！");
            }
            if (revMonthList.size()>6){
                return LayuiReplay.error("最多只能导出6个月数据！");
            }

        } catch (ParseException e) {
            log.info("时间转换错误!");
        }

        if (StringUtils.isBlank(paramVo.getOrgCode())){
            List<OrgVo> allOrgName = iOrgnizationResourceWrapperService.findAllOrgName();
            List<OrgVo> orgVoList = allOrgName.stream().filter(co -> "1".equals(co.getOrgType())).collect(Collectors.toList());
            List<String> orgList = new ArrayList<>();
            orgVoList.forEach(item->{
                if (paramVo.getCityCodeList().contains(item.getOwerCity())){
                    orgList.add(item.getOrgCode());
                }
            });
            paramVo.setOrgCodeList(orgList);
        }else {
            paramVo.setOrgCodeList(Collections.singletonList(paramVo.getOrgCode()));
        }

        /**查询出所有的福利包*/
        List<String> packCodeList = iInsurancePackResourceWrapperService.getPackCodeByOrgCodeAndSingleFlag(paramVo.getOrgCodeList(), paramVo.getSingleFlag());
        if (CollectionUtils.isEmpty(packCodeList)){
            return LayuiReplay.error("没有有找到对应的福利包!");
        }
        paramVo.setPackCodeList(packCodeList);

        TaskLogVo taskLogVo = TaskLogVo.builder()
                .taskType(TaskTypeEnum.ORDER_INSURANCE_DIFF_EXPORTS.getCode())
                .param(JSONObject.toJSONString(paramVo))
                .creator(getSessionUser().getLoginName())
                .updater(getSessionUser().getLoginName())
                .build();
        String taskNo = taskLogWrapperService.generateTask(taskLogVo);
        return LayuiReplay.success(taskNo);
    }
}
