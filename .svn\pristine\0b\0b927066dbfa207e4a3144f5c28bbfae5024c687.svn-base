package com.reon.hr.modules.bill.controller.insurancePractice;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice.IInsurancePracticeOneFeeWrapperService;
import com.reon.hr.api.bill.utils.OneFeeDiffDataExportUtil;
import com.reon.hr.api.vo.sys.OrgUserVo;
import com.reon.hr.api.workflow.constant.ManualAction;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.ui.Model;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ICompanyBankWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceRatioWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.InsurancePracticePayBankConfigWrapperService;
import com.reon.hr.api.base.utils.ListPageUtil;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.insurancePractice.IInsurancePracticeBillWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.enums.PracticePayDetailFlagEnum;
import com.reon.hr.api.bill.enums.ProvidentPayTypeEnum;
import com.reon.hr.api.bill.utils.ExportPracticeReportUtil;
import com.reon.hr.api.bill.utils.PrintApplicationFromUtil;
import com.reon.hr.api.bill.utils.PrintReceivingApplicationFromUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.PracticeLockInfoVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dto.TaskQueryDTO;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.core.utils.StringUtil;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月07日
 * @Version 1.0
 */
@RestController
@RequestMapping("/bill/insurancePracticePayByCmb")
public class InsurancePracticePayByCmbController extends BaseController {


    @Resource
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource
    private IWorkflowWrapperService workflowWrapperService;

    @Resource
    private IPaymentApplyWrapperService iPaymentApplyWrapperService;

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Resource
    private IInsurancePracticeBillWrapperService insurancePracticeLockWrapperService;

    @Resource
    private InsurancePracticePayBankConfigWrapperService insurancePracticePayBankConfigWrapperService;

    @Resource
    private IInsuranceRatioWrapperService insuranceRatioWrapperService;

    @Resource
    private IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;

    @Resource
    private IInsurancePracticeOneFeeWrapperService iInsurancePracticeOneFeeWrapperService;



    /**
     * 备款查询页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoReserveInquiryPage")
    public ModelAndView gotoReserveInquiryPage() {
        return new ModelAndView ("/bill/insurancePracticePayByCmb/reserveInquiryPage");
    }

    /**
     * 备款查询详情页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoReserveInquiryDetailPage")
    public ModelAndView gotoReserveInquiryDetailPage(String lastDate, String payCom,Integer isFlag, Model model) {
        model.addAttribute("payCom", payCom);
        model.addAttribute("lastDate", lastDate);
        model.addAttribute("isFlag", isFlag);
        return new ModelAndView ("/bill/insurancePracticePayByCmb/reserveInquiryDetailPage");
    }

    /**
     * 实做银企直联支付页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoInsurancePracticePayByCmbPage")
    public ModelAndView gotoInsurancePracticePayByCmbPage() {
        return new ModelAndView ("/bill/insurancePracticePayByCmb/insurancePracticePayByCmbPage");
    }

    /**
     * 派单地打印申请单页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoPrintApplicationFromPage")
    public ModelAndView gotoPrintApplicationFromPage(){
        return new ModelAndView ("/bill/insurancePracticePayByCmb/printApplicationFromPage");
    }

    /**
     * 接单地打印申请单页面
     * @return {@link ModelAndView }
     */
    @RequestMapping("gotoPrintReceivingApplicationFromPage")
    public ModelAndView gotoPrintReceivingApplicationFromPage(){
        return new ModelAndView ("/bill/insurancePracticePayByCmb/printReceivingApplicationFromPage");
    }


    /**
     * 获取实做银企直联支付页面数据
     * @param paymentApplyVo
     * @param page
     * @param limit
     * @return {@link Object }
     */
    @ResponseBody
    @RequestMapping(value = "/getInsurancePracticePayByCmbPage", method = RequestMethod.GET)
    public Object getInsurancePracticePayByCmbPage(PaymentApplyVo paymentApplyVo,
                                                   Integer page, Integer limit) {

        List<UserOrgPosVo> allOrgCodeAndPosCode = iUserOrgPosWrapperService.getAllOrgCodeAndPosCode(getSessionUser().getLoginName());
        List<UserOrgPosVo> financialList = allOrgCodeAndPosCode.stream().filter(userOrgPosVo -> userOrgPosVo.getPosCode().equals(PositionEnum.TREASURY_RECHECK_ATTACHE.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(financialList)){
            return new LayuiReplay<>(ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        List<TaskVo> approvalList = new ArrayList<>();
        List<String> disComAppList = new ArrayList<>();
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO();
        taskQueryDTO.setProcessDefinitionKey(ReonWorkflowType.PAYMENT_APPLY.getDefineKey());
        for (UserOrgPosVo userOrgPosVo : financialList) {
            String userId = userOrgPosVo.getOrgCode()+","+userOrgPosVo.getPosCode();
            taskQueryDTO.setUserId(userId);
            List<TaskVo> taskListWithoutPageConditionForOrgCode = workflowWrapperService.getTaskListWithoutPageCondition(taskQueryDTO);
            if(CollectionUtils.isNotEmpty(taskListWithoutPageConditionForOrgCode)){
                approvalList.addAll(taskListWithoutPageConditionForOrgCode);
                disComAppList.add(userId);
            }
        }
        if (CollectionUtils.isEmpty(approvalList)){
            return new LayuiReplay<>(ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        List<String> pidList = approvalList.stream().map(TaskVo::getProcessInstanceId).collect(Collectors.toList());
        paymentApplyVo.setPidList(pidList);
        paymentApplyVo.setDisComAppList(disComAppList);
        List<PaymentApplyVo> paymentApprovalList = iPaymentApplyWrapperService.getPaymentApprovalListByDisCom(paymentApplyVo);
        if (CollectionUtils.isEmpty(paymentApprovalList)){
            return new LayuiReplay<>(ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));

        Map<String, InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigMap = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigMap();
        List<TaskVo> taskVos = new ArrayList<>();
        paymentApprovalList.forEach(vo -> {
            approvalList.forEach(taskVo -> {
                if (taskVo.getProcessInstanceId().equals(vo.getPid())&&taskVo.getAssignee().equals(vo.getDisComApp())){
                    if (StringUtil.isNotBlank(vo.getPayCom())) {
                        vo.setPayComName(orgVoMap.get(vo.getPayCom()));
                    }
                    vo.setAppComName(orgVoMap.get(vo.getAppCom()));
                    vo.setDisComName(orgVoMap.get(vo.getDisCom()));
                    String currentApproveName = workflowWrapperService.getCurrentApproveName(vo.getPid());
                    if ("派单地财务支付".equals(currentApproveName)){
                        BeanUtils.copyProperties(vo,taskVo);
                        InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = insurancePracticePayBankConfigMap.get(taskVo.getDisCom());
                        if (insurancePracticePayBankConfigVo!=null){
                            taskVo.setBankNo(insurancePracticePayBankConfigVo.getDispatchBankNo());
                            taskVo.setBankType(insurancePracticePayBankConfigVo.getDispatchBankType());
                        }
                        taskVo.setPaymentApplyId(vo.getId());
                        taskVo.setPaymentApplyComId(vo.getPayMentId());

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        BigDecimal payAmt = vo.getServiceAmt().add(vo.getPayAmt());
                        taskVo.setTotalAmt(vo.getActPayAmt());
                        taskVo.setBalanceAmt(payAmt.subtract(vo.getActPayAmt()));
                        taskVo.setPayComName(orgVoMap.get(taskVo.getPayCom()));
                        LocalDateTime dateTime = LocalDateTime.parse(taskVo.getLastDate(), formatter);
                        LocalDateTime updatedTime = dateTime.with(LocalTime.of(16, 0));
                        taskVo.setLastDate(updatedTime.format(formatter));
                        setPackName(vo.getId(),taskVo);
                        taskVos.add(taskVo);
                    }
                }
            });

        });
        ListPageUtil<TaskVo> pager = new ListPageUtil<> (taskVos, limit);
        List<TaskVo> pagedList = pager.getPagedList (page);

        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), taskVos.size(), pagedList);
    }

    /**
     * 出款提交
     * @param taskVos
     * @return {@link Object }
     */
    @PostMapping("/disbursementSubmission")
    public Object disbursementSubmission(@RequestBody List<TaskVo> taskVos) {
        if (CollectionUtils.isEmpty(taskVos)){
            return new LayuiReplay<> (ResultEnum.ERR.getCode (), "请选择有效数据");
        }                
        String result = iPaymentApplyWrapperService.initiatePaymentBuyCmb(taskVos,getSessionUser().getLoginName());
        return new LayuiReplay<> (ResultEnum.OK.getCode (), result);
    }

    /**
     * 线下转账
     * @param taskVos
     * @return {@link Object }
     */
    @RequestMapping(value = "/offlineTransfer")
    public Object offlineTransfer(@RequestBody List<TaskVo> taskVos) {
        List<String> pidList = iPaymentApplyWrapperService.offlineTransfer(taskVos, getSessionUser().getLoginName());
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), pidList);
    }

    @PostMapping(value = "/passMessageNotification")
    public LayuiReplay<String> passMessageNotification(@RequestBody List<String> ids){
        if(CollectionUtils.isNotEmpty(ids)){
            ids = ids.stream().distinct().collect(Collectors.toList());
            String loginName = getSessionUser().getLoginName();
            for (String pid : ids) {
                workflowWrapperService.triggerReceiveTask(pid, ReonWorkflowType.PAYMENT_APPLY.getBussinessKey(),loginName,ManualAction.PASS.getDescription());
            }
        }
        return LayuiReplay.success();
    }


    @RequestMapping("/getPrintApplicationFromPage")
    public Object getPrintApplicationFromPage(PaymentApplyVo paymentApplyVo){
        List<String> orgCodeList = getOrgCodeByLoginName(getSessionUser().getLoginName());
        if (CollectionUtils.isEmpty(orgCodeList)){
            return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        paymentApplyVo.setDisComAppList(orgCodeList);
        Page<PaymentApplyVo> printApplicationFromList = iPaymentApplyWrapperService.getPrintApplicationFromPage(paymentApplyVo);

        return new LayuiReplay<PaymentApplyVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), printApplicationFromList.getTotal(),printApplicationFromList.getRecords());
    }

    @GetMapping("/printApplicationFrom")
    private void printApplicationFrom(String paramData, HttpServletResponse response){
        List<PrintPaymentFromExportVo> printPaymentFromExportVos = JsonUtil.jsonToList(paramData, PrintPaymentFromExportVo.class);
        if (CollectionUtils.isEmpty(printPaymentFromExportVos)) {
            return ;
        }
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));

        Map<String, List<PrintPaymentFromExportVo>> resultMap = iPaymentApplyWrapperService.printApplicationFrom(printPaymentFromExportVos);

        try {
            SXSSFWorkbook sheets = PrintApplicationFromUtil.generateExcel(resultMap, orgMap,"付款单");
            PrintApplicationFromUtil.closeInfo(response,sheets,printPaymentFromExportVos.get(0).getDisComName()+"派单地转账详情");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("/getPrintReceivingApplicationFromPage")
    public Object getPrintReceivingApplicationFromPage(PaymentApplyVo paymentApplyVo){
        List<String> orgCodeList = getOrgCodeByLoginName(getSessionUser().getLoginName());
        if (CollectionUtils.isEmpty(orgCodeList)){
            return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        paymentApplyVo.setDisComAppList(orgCodeList);
        List<PaymentApplyVo> printApplicationFromList = iPaymentApplyWrapperService.getPrintReceivingApplicationFromPage(paymentApplyVo);
        ListPageUtil<PaymentApplyVo> pager = new ListPageUtil<PaymentApplyVo>(printApplicationFromList, paymentApplyVo.getLimit());
        List<PaymentApplyVo> pagedList = pager.getPagedList(paymentApplyVo.getPage());
        return new LayuiReplay<PaymentApplyVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), printApplicationFromList.size(), pagedList);
    }

    @GetMapping("/printReceivingApplicationFrom")
    private void printReceivingApplicationFrom(String paramData, HttpServletResponse response){
        PrintPaymentFromExportVo printPaymentFromExportVo = JSON.parseObject(paramData, PrintPaymentFromExportVo.class);
        if (Objects.isNull(printPaymentFromExportVo)) {
            return ;
        }
        PaymentApplyVo paymentApplyVo = iPaymentApplyWrapperService.selectByPrimaryKey(printPaymentFromExportVo.getId());

        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        if (printPaymentFromExportVo.getType()==1){
            Map<String, List<PrintPaymentFromExportVo>> resultMap = iPaymentApplyWrapperService.printAllDisApplicationFrom(printPaymentFromExportVo);
            try {
                SXSSFWorkbook sheets = PrintApplicationFromUtil.generateExcel(resultMap, orgMap,"收款单");
                PrintApplicationFromUtil.closeInfo(response,sheets,orgMap.get(paymentApplyVo.getPayCom())+"收款单");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if (printPaymentFromExportVo.getType()==2){
            Map<String, List<PrintReceivingApplicationFromVo>> resultMap = iPaymentApplyWrapperService.printReceivingApplicationFrom(printPaymentFromExportVo);
            Map<String, List<PrintReceivingApplicationBankVo>> bankMap = setExportBankData(paymentApplyVo, orgMap, resultMap);
            try {
                XSSFWorkbook sheets = PrintReceivingApplicationFromUtil.generateExcel(resultMap,orgMap.get(paymentApplyVo.getPayCom()),bankMap);
                PrintReceivingApplicationFromUtil.closeInfo(response,sheets,orgMap.get(paymentApplyVo.getPayCom())+"出款单");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    @ResponseBody
    @RequestMapping(value = "/getReserveInquiryPage", method = RequestMethod.GET)
    public Object getReserveInquiryPage(PaymentApplyVo paymentApplyVo) {
        List<String> orgCodeList = getOrgCodeByLoginName(getSessionUser().getLoginName());
        if (CollectionUtils.isEmpty(orgCodeList)){
            return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg ());
        }
        paymentApplyVo.setDisComAppList(orgCodeList);
        List<ReserveInquiryVo> reserveInquiryListPage = iPaymentApplyWrapperService.getReserveInquiryListPage(paymentApplyVo);
        ListPageUtil<ReserveInquiryVo> pager = new ListPageUtil<> (reserveInquiryListPage, paymentApplyVo.getLimit());
        List<ReserveInquiryVo> pagedList = pager.getPagedList (paymentApplyVo.getPage());
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (),reserveInquiryListPage.size(),pagedList);
    }

    @ResponseBody
    @RequestMapping(value = "/getReserveInquiryDetail", method = RequestMethod.GET)
    public Object getReserveInquiryDetail(@RequestParam(value = "lastDate") String lastDate, @RequestParam(value = "payCom") String payCom,@RequestParam(value = "isFlag") Integer isFlag) {

        PaymentApplyVo paymentApplyVo = new PaymentApplyVo();
        paymentApplyVo.setLastDate(lastDate.substring(0, 10));
        paymentApplyVo.setPayCom(payCom);
        paymentApplyVo.setPrintType(isFlag);
        List<ReserveInquiryVo> reserveInquiryListPage = iPaymentApplyWrapperService.getReserveInquiryDetail(paymentApplyVo);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (),reserveInquiryListPage);
    }

    @RequestMapping("/printBalanceDiff")
    private void printBalanceDiff(Long payId,Integer type, HttpServletResponse response) {
        if (payId == null){
            return ;
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = "差异数据" + f.format(new Date()) + ".xlsx";
        List<OneFeeDiffDataExportVo> oneFeeDiffDataExportVos = iInsurancePracticeOneFeeWrapperService.printBalanceDiff(payId, type);
        try {
            SXSSFWorkbook sheets = OneFeeDiffDataExportUtil.exportDiffData(oneFeeDiffDataExportVos,type);
            OneFeeDiffDataExportUtil.closeInfo(response,sheets,"差异数据");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/checkProgress", method = RequestMethod.GET)
    @ResponseBody
    public LayuiReplay<InsurancePracticeDisComPayVo> checkProgress(Long payId) {
        List<InsurancePracticeDisComPayVo> payVos = iPaymentApplyWrapperService.checkProgressByPayId(payId);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), payVos);
    }



    private static String extractFullRatio(String input) {
        Pattern pattern = Pattern.compile("(?<=\\()([^)]*)(?=\\))|(?<=（)([^）]*)(?=）)");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group().trim();
        }
        return "";
    }

    public void setPackName(Long id,TaskVo taskVo){
        List<PracticeLockInfoVo> lockInfosByPayId = insurancePracticeLockWrapperService.getLockInfosByPayId(id);
        if (CollectionUtils.isNotEmpty(lockInfosByPayId)){
            List<String> packCodeList = lockInfosByPayId.stream().map(PracticeLockInfoVo::getPackCode).collect(Collectors.toList());
            List<InsurancePackVo> allInsuPackByPackCodeList = iInsurancePackResourceWrapperService.getAllInsuPackByPackCodeList(packCodeList);
            String packNames = allInsuPackByPackCodeList.stream().map(InsurancePackVo::getPackName).collect(Collectors.joining(","));
            taskVo.setPackName(packNames);
        }
    }

    public Map<String, List<PrintReceivingApplicationBankVo>> setExportBankData(PaymentApplyVo paymentApplyVo,Map<String, String> orgMap,Map<String, List<PrintReceivingApplicationFromVo>> resultMap){
        Map<String, List<PrintReceivingApplicationBankVo>> bankMap = new HashMap<>();

        InsurancePracticePayBankConfigVo bankConfigByOrgCode = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigByOrgCode(paymentApplyVo.getPayCom());
        List<InsurancePracticePayBankDetailConfigVo> providentDetails = insurancePracticeLockWrapperService.getProvidentDetails(paymentApplyVo.getOrgCode(), paymentApplyVo.getLockMonth(), paymentApplyVo.getPayDetailType(),paymentApplyVo.getId());
        ArrayList<PrintReceivingApplicationBankVo> printReceivingApplicationBankVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(providentDetails)){
            for (InsurancePracticePayBankDetailConfigVo providentDetail : providentDetails) {
                PrintReceivingApplicationBankVo printReceivingApplicationBankVo = new PrintReceivingApplicationBankVo();
                if (providentDetail.getProvidentPayType().equals(ProvidentPayTypeEnum.BANK_TRANSFER.getCode())){
                    printReceivingApplicationBankVo.setBankName(providentDetail.getReceivePaymentBankName());
                    printReceivingApplicationBankVo.setBankNo(providentDetail.getReceivePaymentBankNo());
                }else {
                    printReceivingApplicationBankVo.setBankName(providentDetail.getPaymentBankName());
                    printReceivingApplicationBankVo.setBankNo(providentDetail.getPaymentBankNo());
                }
                if (providentDetail.getReceivePaymentSubBank()!=null){
                    printReceivingApplicationBankVo.setSubBank(providentDetail.getReceivePaymentSubBank());
                }
                InsurancePracticePayBankConfigVo insurancePracticePayBankConfigById = insurancePracticePayBankConfigWrapperService.getInsurancePracticePayBankConfigById(providentDetail.getIppbcId());

                printReceivingApplicationBankVo.setPayType(ProvidentPayTypeEnum.getMsgByCode(providentDetail.getProvidentPayType()));
                printReceivingApplicationBankVo.setPayTime(PracticePayDetailFlagEnum.getMsgByCode(insurancePracticePayBankConfigById.getProvidentPracticePayDetailFlag()));
                printReceivingApplicationBankVo.setPurpose(paymentApplyVo.getPurpose());
                printReceivingApplicationBankVo.setRemark(providentDetail.getRemark());
                if (bankConfigByOrgCode.getRatioPayFlag().equals(BooleanTypeEnum.NO.getCode())){
                    printReceivingApplicationBankVo.setRatioCode(providentDetail.getRatioName());
                }else {
                    String ratioName = insuranceRatioWrapperService.getRatioNameByRatioCode(providentDetail.getRatio());
                    printReceivingApplicationBankVo.setRatioCode(extractFullRatio(ratioName));
                }

                printReceivingApplicationBankVo.setOrgName(orgMap.get(paymentApplyVo.getPayCom()));
                printReceivingApplicationBankVo.setAmount(providentDetail.getAmount());
                printReceivingApplicationBankVos.add(printReceivingApplicationBankVo);
            }
        }
        bankMap.put("公积金", printReceivingApplicationBankVos);
        PrintReceivingApplicationBankVo printReceivingApplicationBankVo = new PrintReceivingApplicationBankVo();
        printReceivingApplicationBankVo.setBankName(bankConfigByOrgCode.getReceivingBankName());
        printReceivingApplicationBankVo.setBankNo(bankConfigByOrgCode.getReceivingBankNo());
        printReceivingApplicationBankVo.setOrgName(orgMap.get(paymentApplyVo.getPayCom()));
        BigDecimal socialAllAmt = resultMap.get("社保").stream().map(PrintReceivingApplicationFromVo::getCountAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        printReceivingApplicationBankVo.setAmount(socialAllAmt);
        bankMap.put("社保", Lists.newArrayList(printReceivingApplicationBankVo));
        return bankMap;
    }


    public List<String> getOrgCodeByLoginName(String loginName){
        List<UserOrgPosVo> allOrgCodeAndPosCode = iUserOrgPosWrapperService.getAllOrgCodeAndPosCode(getSessionUser().getLoginName());
        List<UserOrgPosVo> financialList = allOrgCodeAndPosCode.stream().filter(userOrgPosVo -> userOrgPosVo.getPosCode().equals(PositionEnum.TREASURY_RECHECK_ATTACHE.getCode())||userOrgPosVo.getPosCode().equals(PositionEnum.TREASURY_DOCUMENTATION_ATTACHE.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(financialList)){
            return Collections.emptyList();
        }
        return financialList.stream().distinct().map(UserOrgPosVo::getOrgCode).collect(Collectors.toList());
    }

}
