package com.reon.hr.api.report.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.report.vo.ExportSaleReportExcelVo;
import com.reon.hr.api.report.vo.SalesStatementHistoryVo;

import java.util.List;
import java.util.Map;

public interface ISalesStatementWrapperService {

    /**
     * 查询销售报表
     * @param page
     * @param size
     * @return
     */
    Page<SalesStatementHistoryVo> getSalesStatementListPage(Integer page, Integer size,SalesStatementHistoryVo salesStatementHistoryVo);

    /**
     * 导出销售报表
     * @param
     * @param
     * @param
     * @param
     * @param
     * @param
     * @param
     * @return
     */
    List<ExportSaleReportExcelVo> exportSalesStatementListPage(SalesStatementHistoryVo salesStatementHistoryVo);



    /**
     * 获取账单汇总数据
     * @param salesStatementHistoryVo
     * @return
     */
    SalesStatementHistoryVo getSaleBillSummary(SalesStatementHistoryVo salesStatementHistoryVo);

    /**
     * 根据账单id导出单个销售报表
     * @param billId
     * @param billMonth
     * @return {@link List }<{@link ExportSaleReportExcelVo }>
     */
    List<ExportSaleReportExcelVo> exportSalesStatementByBillIdAndBillMonth(Long billId, Integer billMonth);
}
