package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.customer.dto.customer.QuotationDTO;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.sp.customer.entity.cus.Quotation;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface QuotationService extends IService<Quotation> {


    QuotationDTO findQuotationData(String quoteNo);

    Page<CommInsuQuotationVo> getSelectPageByCustId(int page, int limit, String para, String currentLogin);


    Page<QuotationVo> searchSelectByOrderChange(int page, int limit, String param);

    List<QuotationVo> getQuotationByPid(Map<String, Object> stringObjectMap, List<String> processInsList);

    List<TaskResultVo> getQuotationByQuotationName(String quoteName);

    int updateQuotationPidAndAppStatusByPid(String pid);

    CustomerCostReport getQuotationContract(CustomerCostReport vo);
    List<CustomerCostReport> getQuotationContractByList(List<CustomerCostReport> costReportList);

    boolean updateSeller(String quoteNo, String seller, String saleSupervisor);

    List<String> getQuotationNoByContractNo(String contractNo);

    boolean updateSellerByQuoteNos(List<String> quoteNos, String seller, String saleSupervisor, ContractVo contractVo);

	Page<CommInsuQuotationVo> getRenewContractQuotationPage(int page, int limit, String loginName, QuotationVo quotationVo);

    QuotationDTO findQuotationDataAndQuotationItem(String quoteNo);

    Map<String, QuotationDTO>  getQuoteAmtByQuoteNoList(List<String> allQuotationNo);

	Map<String, String> getQuoteNameByQuoteNoList(List<String> quoteNoList);

	Map<String, QuotationDTO> getQuoteMapByQuoteNoList(List<String> quoteNoList);

	Map<String, BigDecimal> getQuoNoAndHaveTaxFeeMapByQuoteNoList(List<String> quoteNoList);

    List<QuotationDTO> findQuotationPriceAndRatio(List<String> quoteNo);

    BigDecimal getTaxRatioByOrderNo(List<String> orderNos,String contractNo);

    List<QuotationVo> getDataByQuoteNoList(List<String> quoteNoList);
}
