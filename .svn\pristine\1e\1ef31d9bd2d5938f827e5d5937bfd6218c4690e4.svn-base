package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.MonthServiceReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/2/23 0023 上午 10:47
 * @Version 1.0
 */
public interface MonthServiceReportMapper {

    int insertMonthServiceReportVo(MonthServiceReportVo monthServiceReportVo);
    MonthServiceReportVo getMonthServiceReportVoByCustIdAndMonth( MonthServiceReportVo monthServiceReportVo);

    List<MonthServiceReportVo> getMonthServiceReportList(@Param("page") Page page, @Param("serviceMonth") Integer serviceMonth, @Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList,@Param("custId") Long custId);


    MonthServiceReportVo getMonthServiceReportById(Long id);

    boolean updateFileIdById(Long id);


    int updateMonthServiceReportById(MonthServiceReportVo monthServiceReportVo);
}
