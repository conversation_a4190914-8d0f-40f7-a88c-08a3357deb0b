package com.reon.hr.api.bill.utils;


import com.reon.hr.api.bill.anno.Excel;
import com.reon.hr.api.bill.anno.Excel.ColumnType;
import com.reon.hr.api.bill.anno.Excel.Type;
import com.reon.hr.api.bill.anno.Excels;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.bill.vo.salary.PaymentApplyPrintVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 * @version 1.0
 * @className ExcelUtil
 * @date 2020/9/29 16:36
 */
public class ExcelUtil<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int SHEET_SIZE = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 是否导出动态字段
     */
    private boolean whetherTheDynamicFieldIsExported = false;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    public void init(List<T> list, String sheetName, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        createExcelField();
        createWorkbook();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public Workbook exportExcel(List<T> list, String sheetName) {
        this.init(list, sheetName, Type.EXPORT);
        return exportExcel();
    }

    public Workbook exportExcel(List<T> list, String sheetName, boolean bool) {
        this.whetherTheDynamicFieldIsExported = bool;
        this.init(list, sheetName, Type.EXPORT);
        return exportExcel();
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public Workbook exportExcel() {
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / SHEET_SIZE);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);

                // 产生一行
                Row row = sheet.createRow(0);
                int column = 0;
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                if (Type.EXPORT.equals(type)) {
                    fillExcelData(index);
                }
            }
            return wb;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出Excel异常{}，请联系网站管理员！");
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     */
    public void fillExcelData(int index) throws IllegalAccessException {
        int startNo = index * SHEET_SIZE;
        int endNo = Math.min(startNo + SHEET_SIZE, list.size());
        for (int i = startNo; i < endNo; i++) {
            int rowNum = i + 1 - startNo;
            Row row = sheet.createRow(rowNum);
            // 得到导出对象.
            T vo = list.get(i);
            int column = 0;
            int regionRowNum = 0;
            Field[] declaredFields = vo.getClass().getDeclaredFields();
            for (Field field1 : declaredFields) {
                if ("regionRowNum".equals(field1.getName())) {
                    field1.setAccessible(true);
                    regionRowNum = (int) field1.get(vo);
                }
            }
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                // 设置实体类私有属性可访问
                field.setAccessible(true);

                //当该条数据中的regionRowNum不为0时,代表该行的可合并字段要和前面行的可合并字段合并
                //为0时,不做合并,无需再进行判断
                if (regionRowNum != 0) {
                    //代表当前字段(列)是否可以合并
                    if (excel.isRegion()) {
                        //当前行往后合并,合并要减去1(原因:假如第一行和第二行合并,下标0开始,下标0 + 2需要减去1才是要合并的最后一行下标)
                        setRegionCellStyle(sheet, rowNum, rowNum + regionRowNum - 1, column, column);
                    }
                }

                this.addCell(excel, row, vo, field, column++);
            }
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 9);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataFont = wb.createFont();
        dataFont.setBold(true);
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 9);
        style.setFont(dataFont);
        styles.put("dataBold", style);

        style = wb.createCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataFont = wb.createFont();
        dataFont.setBold(true);
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 11);
        style.setFont(dataFont);
        styles.put("data11Bold", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat("[$-409]h:mm AM/PM;@"));
        styles.put("dataAM", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat("yyyy\"年\"m\"月\";@"));
        styles.put("dataYM", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat("[$-F800]dddd!, mmmm dd!, yyyy"));
        styles.put("dataYMD", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data11Bold"));
        style.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat("[$-F800]dddd!, mmmm dd!, yyyy"));
        styles.put("dataYMD11Bold", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("微软雅黑");
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setBold(true);
        //headerFont.setColor (IndexedColors.WHITE.getIndex ());
        style.setFont(headerFont);
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataFont = wb.createFont();
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 9);
        style.setFont(dataFont);
        style.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
        styles.put("numerical", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataFont = wb.createFont();
        dataFont.setBold(true);
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 9);
        style.setFont(dataFont);
        style.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
        styles.put("numericalBold", style);

        style = wb.createCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataFont = wb.createFont();
        dataFont.setBold(true);
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 11);
        style.setFont(dataFont);
        style.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
        styles.put("numerical11Bold", style);


        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        Font font = wb.createFont();//创建一个字体
        font.setBold(true);//设置是否加粗
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 14);
        titleStyle.setFont(font);
        styles.put("titleStyle", titleStyle);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        dataFont = wb.createFont();
        dataFont.setFontName("微软雅黑");
        dataFont.setFontHeightInPoints((short) 9);
        style.setFont(dataFont);
        styles.put("center", style);

        return styles;
    }

    /**
     * 创建单元格
     */
    public Cell createCell(Excel attr, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        if (ColumnType.STRING == attr.cellType()) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(value == null ? attr.defaultValue() : value + attr.suffix());
        } else if (ColumnType.NUMERIC == attr.cellType()) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellStyle(styles.get("numerical"));
            if (value != null) {
                cell.setCellValue(new BigDecimal(value + "").doubleValue());
            }
        } else if (ColumnType.AM == attr.cellType()) {
            cell.setCellStyle(styles.get("dataAM"));
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Date) value);
        } else if (ColumnType.YM == attr.cellType()) {
            cell.setCellStyle(styles.get("dataYM"));
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Date) value);
        } else if (ColumnType.YMD == attr.cellType()) {
            cell.setCellStyle(styles.get("dataYMD"));
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Date) value);
        }
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
            row.setHeight((short) (attr.height() * 20));
        }
        // 如果设置了提示信息则鼠标放上去提示.
        if (StringUtils.isNotEmpty(attr.prompt())) {
            // 这里默认设了2-101列提示.
            setXSSFPrompt(sheet, "", attr.prompt(), 1, 100, column, column);
        }
        // 如果设置了combo属性则本列只能选择不能输入
        if (attr.combo().length > 0) {
            // 这里默认设了2-101列只能选择不能输入.
            setXSSFValidation(sheet, attr.combo(), 1, 100, column, column);
        }
    }

    /**
     * 添加单元格
     */
    public void addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell;
        try {
            // 设置行高
            row.setHeight((short) (attr.height() * 20));
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                cell.setCellStyle(styles.get("data"));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                if (StringUtils.isNotEmpty(dateFormat) && value != null) {
                    cell.setCellValue(DateUtil.getString((Date) value, dateFormat));
                } else if (StringUtils.isNotEmpty(readConverterExp) && value != null) {
                    cell.setCellValue(convertByExp(String.valueOf(value), readConverterExp));
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
    }

    /**
     * 设置合并单元格样式
     *
     * @param sheet    表
     * @param firstRow 第一行
     * @param lastRow  最后一行
     * @param firstCol 第一列
     * @param lastCol  最后一列
     */
    private static void setRegionCellStyle(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        CellRangeAddress cellRangeTitle = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.addMergedRegion(cellRangeTitle);
        // 下边框
        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeTitle, sheet);
        // 左边框
        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeTitle, sheet);
        // 有边框
        RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeTitle, sheet);
        // 上边框
        RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeTitle, sheet);
        RegionUtil.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex(), cellRangeTitle, sheet);
        RegionUtil.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex(), cellRangeTitle, sheet);
        RegionUtil.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex(), cellRangeTitle, sheet);
        RegionUtil.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex(), cellRangeTitle, sheet);
    }

    /**
     * 设置 POI XSSFSheet 单元格提示
     *
     * @param sheet         表单
     * @param promptTitle   提示标题
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFPrompt(Sheet sheet, String promptTitle, String promptContent, int firstRow, int endRow,
                              int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        dataValidation.createPromptBox(promptTitle, promptContent);
        dataValidation.setShowPromptBox(true);
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public void setXSSFValidation(Sheet sheet, String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @return 解析后值
     * @throws Exception
     */
    public static String convertByExp(String propertyValue, String converterExp) throws Exception {
        try {
            String[] convertSource = converterExp.split(",");
            for (String item : convertSource) {
                String[] itemArray = item.split("=");
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return propertyValue;
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if (target.contains(".")) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            String methodName = "get" + name.substring(0, 1).toUpperCase() + name.substring(1);
            Method method = clazz.getMethod(methodName);
            o = method.invoke(o);
        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = new ArrayList<>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            // 单注解
            if (field.isAnnotationPresent(Excel.class)) {
                Excel attr = field.getAnnotation(Excel.class);
                if (!attr.isDynamicColumns() || (attr.isDynamicColumns() && whetherTheDynamicFieldIsExported)) {
                    putToField(field, field.getAnnotation(Excel.class));
                }
            }

            // 多注解
            if (field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for (Excel excel : excels) {
                    putToField(field, excel);
                }
            }
        }
    }

    /**
     * 放到字段集合中
     */
    private void putToField(Field field, Excel attr) {
        if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
            this.fields.add(new Object[]{field, attr});
        }
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(double sheetNo, int index) {
        this.sheet = wb.createSheet();
        this.styles = createStyles(wb);
        // 设置工作表的名称.
        if (sheetNo == 0) {
            wb.setSheetName(wb.getSheetIndex(this.sheet.getSheetName()), sheetName);
        } else {
            wb.setSheetName(wb.getSheetIndex(this.sheet.getSheetName()), sheetName + "-" + index);
        }
    }

    public static void closeInfo(HttpServletResponse response, Workbook workbook, String fileName) throws IOException {
        // 设置头信息
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        // 设置成xlsx格式
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
        // 创建一个输出流
        ServletOutputStream outputStream = response.getOutputStream();
        // 写入数据
        workbook.write(outputStream);
        // 关闭
        outputStream.close();
        workbook.close();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public Workbook exportExcel(List<T> list, String sheetName, String titleName, T t) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = Type.EXPORT;
        createExcelField();
        this.wb = new XSSFWorkbook();
        Workbook workbook = this.exportExcel();
        return printPaymentSummary(workbook, titleName, t);
    }

    public Workbook printPaymentSummary(Workbook workbook, String titleName, T t) {
        //表头
        Sheet sheet = workbook.getSheet(sheetName);
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setLandscape(true);
        printSetup.setPaperSize(PrintSetup.A4_PAPERSIZE); //纸张
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        sheet.shiftRows(firstRowNum, lastRowNum, 3);
        sheet.getRow(3).setHeight((short) (49.75 * 20));
        Row row0 = sheet.createRow(0);
        Cell cell0_0 = row0.createCell(0);
        cell0_0.setCellValue(titleName);
        cell0_0.setCellStyle(styles.get("titleStyle"));
        int lastCol=20;
        PaymentApplyPrintVo paymentApplyPrintVo = (PaymentApplyPrintVo) t;
        List<Integer> columnToBeDeletedList=new ArrayList<>();
        if(paymentApplyPrintVo.getSumTotalActApy().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(5);
        }
        if(paymentApplyPrintVo.getSumTotalTax().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(6);
        }
        if(paymentApplyPrintVo.getSumTotalCompensation().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(7);
        }
        if(paymentApplyPrintVo.getSumTotalCompensationTax().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(8);
        }
        if(paymentApplyPrintVo.getSumTotalAnnualBonus().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(9);
        }
        if(paymentApplyPrintVo.getSumTotalAnnualBonusTax().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(10);
        }
        if(paymentApplyPrintVo.getSumTotalLaborWages().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(11);
        }
        if(paymentApplyPrintVo.getSumTotalLaborWagesTax().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(12);
        }
        if(paymentApplyPrintVo.getSumTotalSalaryFee().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(13);
        }
        if(paymentApplyPrintVo.getSumTotalSupplierDisFund().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(14);
        }
        if(paymentApplyPrintVo.getSumTotalSupplierCrossBankHandlingFees().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(15);
        }
        if(paymentApplyPrintVo.getSumTotalSupplierUnionFees().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(16);
        }
        if(paymentApplyPrintVo.getSumTotalSupplierSalarySaleTax().doubleValue()==0){
            lastCol-=1;
            columnToBeDeletedList.add(17);
        }
        CellRangeAddress region = new CellRangeAddress(0, 2, 0, lastCol);
        sheet.addMergedRegion(region);
        //合计
        int rowNum14 = sheet.getLastRowNum();
        Row row14 = sheet.getRow(rowNum14);
        Cell cell14_1 = row14.getCell(1);
        cell14_1.setCellValue("合计");
        CellStyle dataBold = styles.get("dataBold");
        cell14_1.setCellStyle(dataBold);
        Cell cell14_5 = row14.getCell(5);

        CellStyle numericalBold = styles.get("numericalBold");
        cell14_5.setCellValue(paymentApplyPrintVo.getSumTotalActApy().doubleValue());
        cell14_5.setCellStyle(numericalBold);
        Cell cell14_6 = row14.getCell(6);
        cell14_6.setCellValue(paymentApplyPrintVo.getSumTotalTax().doubleValue());
        cell14_6.setCellStyle(numericalBold);

        Cell cell14_7 = row14.getCell(7);
        cell14_7.setCellValue(paymentApplyPrintVo.getSumTotalCompensation().doubleValue());
        cell14_7.setCellStyle(numericalBold);
        Cell cell14_8 = row14.getCell(8);
        cell14_8.setCellValue(paymentApplyPrintVo.getSumTotalCompensationTax().doubleValue());
        cell14_8.setCellStyle(numericalBold);
        Cell cell14_9 = row14.getCell(9);
        cell14_9.setCellValue(paymentApplyPrintVo.getSumTotalAnnualBonus().doubleValue());
        cell14_9.setCellStyle(numericalBold);
        Cell cell14_10 = row14.getCell(10);
        cell14_10.setCellValue(paymentApplyPrintVo.getSumTotalAnnualBonusTax().doubleValue());
        cell14_10.setCellStyle(numericalBold);
        Cell cell14_11 = row14.getCell(11);
        cell14_11.setCellValue(paymentApplyPrintVo.getSumTotalLaborWages().doubleValue());
        cell14_11.setCellStyle(numericalBold);
        Cell cell14_12 = row14.getCell(12);
        cell14_12.setCellValue(paymentApplyPrintVo.getSumTotalLaborWagesTax().doubleValue());
        cell14_12.setCellStyle(numericalBold);

        Cell cell14_13 = row14.getCell(13);
        cell14_13.setCellValue(paymentApplyPrintVo.getSumTotalSalaryFee().doubleValue());
        cell14_13.setCellStyle(numericalBold);
        Cell cell14_14 = row14.getCell(14);
        cell14_14.setCellValue(paymentApplyPrintVo.getSumTotalSupplierDisFund().doubleValue());
        cell14_14.setCellStyle(numericalBold);
        Cell cell14_15 = row14.getCell(15);
        cell14_15.setCellValue(paymentApplyPrintVo.getSumTotalSupplierCrossBankHandlingFees().doubleValue());
        cell14_15.setCellStyle(numericalBold);
        Cell cell14_16 = row14.getCell(16);
        cell14_16.setCellValue(paymentApplyPrintVo.getSumTotalSupplierUnionFees().doubleValue());
        cell14_16.setCellStyle(numericalBold);
        Cell cell14_17 = row14.getCell(17);
        cell14_17.setCellValue(paymentApplyPrintVo.getSumTotalSupplierSalarySaleTax().doubleValue());
        cell14_17.setCellStyle(numericalBold);

        Cell cell14_18 = row14.getCell(18);
        cell14_18.setCellValue(paymentApplyPrintVo.getSumPayAmt().doubleValue());
        cell14_18.setCellStyle(numericalBold);

        // 删除列
        for (int i = 0; i < sheet.getLastRowNum() + 1; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < columnToBeDeletedList.size(); j++) {
                    Integer columnToBeDeleted = columnToBeDeletedList.get(j)-j;
                    Cell cell = row.getCell(columnToBeDeleted);
                    if(cell!=null){
                        row.removeCell(cell);
                    }
                    //往前移
                    for (int k = columnToBeDeleted + 1; k <= row.getLastCellNum(); k++) {
                        Cell currentCell = row.getCell(k);
                        if (currentCell != null) {
                            Cell newCell = row.createCell(k - 1, currentCell.getCellType());
                            newCell.setCellStyle(styles.get("data"));
                            switch (currentCell.getCellTypeEnum()){
                                case NUMERIC:
                                    newCell.setCellValue(currentCell.getNumericCellValue());
                                    newCell.setCellStyle(styles.get("numerical"));
                                    break;
                                case STRING:
                                    newCell.setCellValue(currentCell.getStringCellValue());
                                    break;
                                case FORMULA:
                                    newCell.setCellValue(currentCell.getCellFormula());
                                    break;
                                case BOOLEAN:
                                    newCell.setCellValue(currentCell.getBooleanCellValue());
                                    break;
                            }
                            if(i==3){
                                newCell.setCellStyle(styles.get("header"));
                            }else if(i==17){
                                newCell.setCellStyle(numericalBold);
                            }
                            row.removeCell(currentCell);
                        }
                    }
                }
            }
        }

        //提交人
        Row row16 = sheet.createRow(rowNum14 + 2);
        Cell cell16_0 = row16.createCell(0);
        cell16_0.setCellValue("客服提交：" + paymentApplyPrintVo.getStarter());
        CellStyle center = styles.get("center");
        cell16_0.setCellStyle(center);
        Cell cell16_1 = row16.createCell(1);
        cell16_1.setCellValue("客服复核：" + paymentApplyPrintVo.getComManager());
        cell16_1.setCellStyle(center);
        Cell cell16_3 = row16.createCell(3);
        cell16_3.setCellValue("财务制单：");
        cell16_3.setCellStyle(center);
        Cell cell16_4 = row16.createCell(4);
        cell16_4.setCellValue(paymentApplyPrintVo.getFinancialOfficer());
        cell16_4.setCellStyle(center);
        Cell cell16_6 = row16.createCell(6);
        cell16_6.setCellValue("财务复核：");
        cell16_6.setCellStyle(center);
        String financialManager = paymentApplyPrintVo.getFinancialManager();
        if (StringUtils.isNotBlank(financialManager)) {
            cell16_6.setCellValue(cell16_6.getStringCellValue()+financialManager);
            /*Cell cell16_7 = row16.createCell(7);
            cell16_7.setCellValue(financialManager);
            cell16_7.setCellStyle(center);*/
        }
        Cell cell16_11 = row16.createCell(lastCol);
        cell16_11.setCellValue("打印日期：" + paymentApplyPrintVo.getPrintDate());
        cell16_11.setCellStyle(center);
        //到款
        Row row17 = sheet.createRow(rowNum14 + 3);
        Cell cell17_1 = row17.createCell(0);
        cell17_1.setCellValue("到款（预计）日期");
        CellStyle data11Bold = styles.get("data11Bold");
        cell17_1.setCellStyle(data11Bold);
        Row row18 = sheet.createRow(rowNum14 + 4);
        Cell cell18_1 = row18.createCell(0);
        cell18_1.setCellValue("到款名称抬头");
        cell18_1.setCellStyle(data11Bold);
        Row row19 = sheet.createRow(rowNum14 + 5);
        Cell cell19_1 = row19.createCell(0);
        cell19_1.setCellValue("到款金额");
        cell19_1.setCellStyle(data11Bold);
        int row17RowNum = row17.getRowNum();
        int row18RowNum = row18.getRowNum();
        int row19RowNum = row19.getRowNum();
        List<BillCheckVo> billCheckVoList = paymentApplyPrintVo.getBillCheckVoList();
        for (int i = 0; i < billCheckVoList.size(); i++) {
            BillCheckVo billCheckVo = billCheckVoList.get(i);
            int j = i + 1;
            if (i > (lastCol-1)) {
                j = i % (lastCol+1);
                int special = 4 * (i / (lastCol+1));
                row17 = sheet.getRow(row17RowNum + special) == null ? sheet.createRow(row17RowNum + special) : sheet.getRow(row17RowNum + special);
                row18 = sheet.getRow(row18RowNum + special) == null ? sheet.createRow(row18RowNum + special) : sheet.getRow(row18RowNum + special);
                row19 = sheet.getRow(row19RowNum + special) == null ? sheet.createRow(row19RowNum + special) : sheet.getRow(row19RowNum + special);
            }
            Cell row17Cell = row17.createCell(j);
            row17Cell.setCellValue(DateUtil.parseStringToDate(billCheckVo.getPayDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD));
            row17Cell.setCellStyle(styles.get("dataYMD11Bold"));
            Cell row18Cell = row18.createCell(j);
            row18Cell.setCellValue(billCheckVo.getPayCustName());
            row18Cell.setCellStyle(data11Bold);
            Cell row19Cell = row19.createCell(j);
            row19Cell.setCellValue(billCheckVo.getPayAmt().doubleValue());
            row19Cell.setCellStyle(styles.get("numerical11Bold"));
        }


        for (int i = 4; i < sheet.getLastRowNum() - 1; i++) {
            List<Integer> rowNum = new ArrayList<>();
            rowNum.add(18);
            rowNum.add(19);
            if (!rowNum.contains(i)) {
                Optional.ofNullable(sheet.getRow(i))
                        .ifPresent(row -> row.setHeight((short) (30 * 20)));
            }
        }
        sheet.setAutobreaks(true);//这个是sheet缩放设置，设置行调整为一列和行调整为一列必须要true
        //printSetup.setFitHeight((short) 1);//将所有列调整为一页
        //printSetup.setFitWidth((short) 1);//将所有行调整为一页
        //sheet.setMargin(XSSFSheet.BottomMargin, (double) 0.5);// 页边距（下）
        //sheet.setMargin(XSSFSheet.LeftMargin, (double) 0.1);// 页边距（左）
        //sheet.setMargin(XSSFSheet.RightMargin, (double) 0.1);// 页边距（右）
        //sheet.setMargin(XSSFSheet.TopMargin, (double) 0.5);// 页边距（上）
        //sheet.setHorizontallyCenter(true);//设置打印页面为水平居中
        //sheet.setVerticallyCenter(true);//设置打印页面为垂直居中
        sheet.setFitToPage(true);//启用“适合页面”打印选项的标志。(默认选择的是“将工作表调整为一页”)

        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet); // 下边框
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet); // 左边框
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet); // 有边框
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet); // 上边框
        RegionUtil.setBottomBorderColor(IndexedColors.BLACK.getIndex(), region, sheet);
        RegionUtil.setLeftBorderColor(IndexedColors.BLACK.getIndex(), region, sheet);
        RegionUtil.setRightBorderColor(IndexedColors.BLACK.getIndex(), region, sheet);
        RegionUtil.setTopBorderColor(IndexedColors.BLACK.getIndex(), region, sheet);
        return workbook;
    }

    public Workbook addSheet(Workbook workbook, List<T> list, String sheetName, String titleName, T t) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = Type.EXPORT;
        createExcelField();
        this.wb = workbook;
        workbook = this.exportExcel();
        return printPaymentSummary(workbook, titleName, t);
    }
}

