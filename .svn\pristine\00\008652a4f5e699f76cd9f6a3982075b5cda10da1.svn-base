package com.reon.hr.sp.customer.service.impl.salary.pay;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IDisabilityGoldRateWrapperService;
import com.reon.hr.api.base.enums.DisabilityGoldRateEnum;
import com.reon.hr.api.base.enums.DisabilityGoldRateLogEnum;
import com.reon.hr.api.base.vo.DisabilityGoldRateVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.salary.ISupplierSalaryBillWrapperService;
import com.reon.hr.api.bill.enums.SupplierVerificationStatus;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.SupplierContractProdTypeEnum;
import com.reon.hr.api.customer.enums.WithholdingAgentEnum;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.salary.SalaryInfoStatus;
import com.reon.hr.api.customer.enums.supplier.ServiceFeeType;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.customer.vo.salary.pay.*;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.customer.vo.supplier.SupplierQuotationVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.sp.customer.dao.cus.BillTempletMapper;
import com.reon.hr.sp.customer.dao.cus.SupplierMapper;
import com.reon.hr.sp.customer.dao.cus.SupplierQuotationMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeEntryDimissionMapper;
import com.reon.hr.sp.customer.dao.salary.SalaryInfoMapper;
import com.reon.hr.sp.customer.dao.salary.SupplierSalaryInfoMapper;
import com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission;
import com.reon.hr.sp.customer.entity.supContractArea.SupplierQuotation;
import com.reon.hr.sp.customer.service.cus.SupplierQuotationService;
import com.reon.hr.sp.customer.service.employee.salary.pay.SupplierSalaryInfoService;
import com.reon.hr.sp.customer.service.employee.salary.taxDeclarationInformation.TaxDeclarationInformationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class SupplierSalaryInfoServiceImpl implements SupplierSalaryInfoService {
    @Autowired
    private SalaryInfoMapper salaryInfoMapper;
    @Autowired
    private SupplierSalaryInfoMapper supplierSalaryInfoMapper;
    @Autowired
    private IDisabilityGoldRateWrapperService disabilityGoldRateWrapperService;
    @Autowired
    private BillTempletMapper billTempletMapper;
    @Autowired
    private SupplierQuotationService supplierQuotationService;
    @Autowired
    private EmployeeEntryDimissionMapper employeeEntryDimissionMapper;
    @Autowired
    private TaxDeclarationInformationService taxDeclarationInformationService;
    @Autowired
    private ISupplierSalaryBillWrapperService supplierSalaryBillWrapperService;
    @Autowired
    private SupplierMapper supplierMapper;
    @Autowired
    private SupplierQuotationMapper supplierQuotationMapper;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    public static final String REAL="real";

    @Override
    public List<SupplierSalaryBillVo> getSupplierEmpBill(SalaryBillSearchVo vo) {
        List<SupplierSalaryBillVo> listEmp = supplierSalaryInfoMapper.getSupplierEmpBill(vo);

        List<SupplierAreaVo> supplierAreaVoList = new ArrayList<>();
        Map<String, SupplierQuotationVo> supplierAreaSalaryFeeMap = new HashMap<>();
        Map<String, SupplierQuotationVo> supplierAreaSalaryFeeByNoMap = new HashMap<>();
        Map<Long, CalculateSalaryVo> calculateLastVoMap = new HashMap<>();
        long count = listEmp.stream().filter(s -> s.getImportSupplierQuotationNo() == null).count();
        if(count>0){
            for (SupplierSalaryBillVo salaryBillVo : listEmp) {
                SupplierAreaVo supplierAreaVo = new SupplierAreaVo();
                supplierAreaVo.setSupplierId(Long.parseLong(salaryBillVo.getOrgCode()));
                String cityCode = salaryBillVo.getCityCode();
                supplierAreaVo.setProvinceCode(cityCode.substring(0, 2));
                supplierAreaVo.setCityCode(cityCode.substring(2, 6));
                supplierAreaVoList.add(supplierAreaVo);
                CalculateSalaryVo calculateSalaryVo=new CalculateSalaryVo();
                calculateSalaryVo.setSalaryCategoryId(salaryBillVo.getSalaryCategoryId());
                calculateSalaryVo.setTaxMonth(salaryBillVo.getTaxMonth());
                calculateSalaryVo.setTempletId(salaryBillVo.getTempletId());
                calculateSalaryVo.setWithholdingAgentNo(salaryBillVo.getWithholdingAgentNo());
                calculateSalaryVo.setEmpId(salaryBillVo.getEmployeeId());
                calculateSalaryVo.setPayId(salaryBillVo.getPayId());
                //查询多批计算的上一批次的计算数据
                CalculateSalaryVo calculateLast =salaryInfoMapper.getFirstSupplierEmpSalary(calculateSalaryVo,salaryBillVo.getTaxListId());
                if(calculateLast!=null){
                    calculateLastVoMap.put(salaryBillVo.getSupplierSalaryInfoId(),calculateLast);
                }
            }
            if (CollectionUtils.isNotEmpty(supplierAreaVoList)) {
                supplierAreaSalaryFeeMap = supplierQuotationService.getSalaryFeeBySupplierVoList(supplierAreaVoList);
            }
        }
        List<String> supplierQuotationNoList = listEmp.stream().map(SupplierSalaryBillVo::getImportSupplierQuotationNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(supplierQuotationNoList)){
            List<SupplierQuotationVo> supplierQuotationVoList = supplierQuotationMapper.getSupplierQuotationVoByQuotationNo(supplierQuotationNoList);
            supplierAreaSalaryFeeByNoMap = supplierQuotationVoList.stream().collect(Collectors.toMap(SupplierQuotationVo::getSupQuotationNo, Function.identity()));
        }


        List<String> payPlaceList = listEmp.stream().filter(salaryBillVo -> StringUtils.isNotBlank(salaryBillVo.getPayPlace())).map(SalaryBillVo::getPayPlace).collect(Collectors.toList());
        List<DisabilityGoldRateVo> disabilityGoldRateVoList = disabilityGoldRateWrapperService.selectByCityNameList(payPlaceList);
//        Map<String, BigDecimal> disabilityGoldRateMap = disabilityGoldRateVoList.stream()
//                .collect(Collectors.toMap(
//                        obj -> {
//                            String key = obj.getCityCode() + "_" + obj.getType();
//                            if (StringUtils.isNotEmpty(obj.getOrgCode())) {
//                                key += "_" + obj.getOrgCode();
//                            }
//                            return key;
//                        },
//                        DisabilityGoldRateVo::getRate
//                ));
        List<SupplierSalaryInfoVo> updateVoList =new ArrayList<>();
        List<SupplierSalaryInfoVo> addVoList =new ArrayList<>();
        for (SupplierSalaryBillVo salaryBillVo : listEmp) {
            Long supplierId = Long.parseLong(salaryBillVo.getOrgCode());
            SupplierQuotationVo supplierQuotationVo = supplierAreaSalaryFeeMap.get(supplierId + "-" + salaryBillVo.getCityCode());
            String supplierQuotationNo = salaryBillVo.getImportSupplierQuotationNo();
            if(salaryBillVo.getSupplierSalaryInfoId()!=null&&StringUtils.isNotBlank(supplierQuotationNo)){
                supplierQuotationVo=supplierAreaSalaryFeeByNoMap.get(supplierQuotationNo);
            }
            salaryBillVo.setSalary(salaryBillVo.getSalary().add(salaryBillVo.getEconomicCompensationSalary()));
            salaryBillVo.setIndTax(salaryBillVo.getIndTax().add(salaryBillVo.getEconomicCompensationIndTax()));
            salaryBillVo.setActTotal(salaryBillVo.getActTotal().add(salaryBillVo.getEconomicCompensationActTotal()));
            //残障金=应发工资*残障金比例
            BigDecimal disFund = salaryBillVo.getImportSupplierDisFund();
            if(disFund ==null){
//                BigDecimal disabilityGoldRate;
//                if (Objects.equals(salaryBillVo.getWithholdingAgentType(), WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex())) {
//                    disabilityGoldRate = disabilityGoldRateMap.get(salaryBillVo.getCityCode() + "_" + salaryBillVo.getWithholdingAgentType());
//                } else {
//                    disabilityGoldRate = disabilityGoldRateMap.get(salaryBillVo.getCityCode() + "_" + salaryBillVo.getWithholdingAgentType() + "_" + salaryBillVo.getOrgCode());
//                }
                //disFund = disabilityGoldRate != null ? (salaryBillVo.getCollPay().multiply(disabilityGoldRate)).setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                BigDecimal lastDisFund=null;
                if(calculateLastVoMap.containsKey(salaryBillVo.getSupplierSalaryInfoId())){
                    lastDisFund=calculateLastVoMap.get(salaryBillVo.getSupplierSalaryInfoId()).getSupplierDisFund();
                }
                DisabilityGoldRateVo disabilityGoldRateVo = getDisabilityGoldRateVo(disabilityGoldRateVoList, salaryBillVo);
                disFund = countDisFund(disabilityGoldRateVo, salaryBillVo.getCollPay(), salaryBillVo.getActTotal(),lastDisFund);
            }
            salaryBillVo.setDisFund(disFund);
            salaryBillVo.setSupplierDisFund(disFund);
            salaryBillVo.setCrossBankHandlingFees(salaryBillVo.getSupplierCrossBankHandlingFees());
            salaryBillVo.setUnionFees(salaryBillVo.getSupplierUnionFees());
            //TODO 税后增减项不计算外包税费待确认
            //税金计算规则是：=round（（社保+公积金+服务费+实发工资+个税）*6.83%，2）
            //外包税费=（实发工资+个税+残障金）*外包税率+不含税服务费*外包税率
            BigDecimal salaryFee = BigDecimal.ZERO;
            boolean supplierSalarySaleTaxFlag = salaryBillVo.getImportSupplierSalarySaleTax()==null;
            if(supplierSalarySaleTaxFlag){
                salaryBillVo.setSupplierSalarySaleTax(BigDecimal.ZERO);
            }else {
                salaryBillVo.setSupplierSalarySaleTax(salaryBillVo.getImportSupplierSalarySaleTax());
            }
            if (supplierQuotationVo != null) {
                if (StringUtils.isBlank(supplierQuotationVo.getErrorRemark())) {
                    salaryFee = supplierQuotationVo.getServiceFee();
                    supplierQuotationNo=supplierQuotationVo.getSupQuotationNo();
                    BigDecimal serviceFeeSaleTas = BigDecimal.ZERO;
                    if (Objects.equals(supplierQuotationVo.getProdType(), SupplierContractProdTypeEnum.OUTSOURCING.getCode())&&supplierSalarySaleTaxFlag) {
                        BigDecimal rate = supplierQuotationVo.getTaxRate();
                        salaryBillVo.setSupplierSalarySaleTax((salaryBillVo.getActTotal().add(salaryBillVo.getIndTax()).add(salaryBillVo.getIndTaxAdjust()).add(disFund).multiply(rate)).setScale(2, BigDecimal.ROUND_HALF_UP));
                        if (supplierQuotationVo.getTaxFlag() == BooleanTypeEnum.NO.getCode()) {
                            serviceFeeSaleTas = salaryFee.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                        } else {
                            serviceFeeSaleTas = salaryFee.subtract(salaryFee.divide(BigDecimal.ONE.add(rate), 2, RoundingMode.HALF_UP));
                        }
                        salaryBillVo.setSupplierSalarySaleTax(salaryBillVo.getSupplierSalarySaleTax().add(serviceFeeSaleTas));
                    }
                }else {
                    salaryBillVo.setErrorRemark(supplierQuotationVo.getErrorRemark());
                }
            }
            salaryBillVo.setSalarySaleTax(salaryBillVo.getSupplierSalarySaleTax());
            salaryBillVo.setSupplierQuotationNo(supplierQuotationNo);
            salaryBillVo.setSupplierServiceFee(salaryFee);
            salaryBillVo.setServiceFee(salaryFee);
            //企业总计
            salaryBillVo.setCustPayAmt(salaryBillVo.getSupplierServiceFee().add(salaryBillVo.getSupplierSalarySaleTax()).add(salaryBillVo.getDisFund())
                    .add(salaryBillVo.getCrossBankHandlingFees()).add(salaryBillVo.getUnionFees()).add(salaryBillVo.getActTotal())
                    .add(salaryBillVo.getIndTax()).add(salaryBillVo.getIndTaxAdjust()));
            salaryBillVo.setSupplierCustPayAmt(salaryBillVo.getCustPayAmt());
            //费用合计
            salaryBillVo.setFeeTotal(salaryBillVo.getSupplierServiceFee().add(salaryBillVo.getCrossBankHandlingFees()).add(salaryBillVo.getUnionFees())
                    .add(salaryBillVo.getActTotal()).add(salaryBillVo.getIndTax()).add(salaryBillVo.getIndTaxAdjust()));
            salaryBillVo.setSupplierFeeTotal(salaryBillVo.getFeeTotal());

            SupplierSalaryInfoVo addOrUpdateVo =new SupplierSalaryInfoVo();
            BeanUtils.copyProperties(salaryBillVo,addOrUpdateVo);
            addOrUpdateVo.setEmpId(salaryBillVo.getEmployeeId());
            addOrUpdateVo.setCreator(vo.getCreator());
            addOrUpdateVo.setUpdater(vo.getCreator());
            if(salaryBillVo.getSupplierSalaryInfoId()==null){
                addVoList.add(addOrUpdateVo);
            }else {
                updateVoList.add(addOrUpdateVo);
            }
        }
        if(CollectionUtils.isNotEmpty(addVoList)){
            supplierSalaryInfoMapper.insertByList(addVoList);
        }
        if(CollectionUtils.isNotEmpty(updateVoList)){
            supplierSalaryInfoMapper.updateByList(updateVoList);
        }

        return listEmp;
    }

    @Override
    public List<String> getBySupplierSalaryBillVoList(List<SupplierSalaryBillVo> queryVoList) {
        Map<String, Long> billIdMap = queryVoList.stream().collect(Collectors.toMap(s -> s.getContractNo() + "-" + s.getTempletId() + "-" + s.getBillMonth(), SupplierSalaryBillVo::getBillId,(v1,v2)->v1));
        List<SupplierSalaryBillVo> voList=supplierSalaryInfoMapper.getBySupplierSalaryBillVoList(queryVoList);
        List<String> billIdAndSupplierIdList=new ArrayList<>();
        for (SupplierSalaryBillVo vo:voList) {
            String key = vo.getContractNo() + "-" + vo.getTempletId() + "-" + vo.getBillMonth();
            if(billIdMap.containsKey(key)){
                Long billId = billIdMap.get(key);
                billIdAndSupplierIdList.add(billId+"-"+vo.getSupplierId());
            }
        }
        return billIdAndSupplierIdList;
    }

    @Override
    public List<SupplierSalaryInfoVo> getSalaryInfoVoList(List<SupplierSalaryInfoVo> salaryInfoVoList) {
        List<SupplierSalaryInfoVo> salaryInfoVos = supplierSalaryInfoMapper.getSalaryInfoVoList(salaryInfoVoList);
        if(CollectionUtils.isEmpty(salaryInfoVos)){
            return salaryInfoVos;
        }
        List<Long> empIdList = salaryInfoVos.stream().map(SalaryInfoVo::getEmpId).collect(Collectors.toList());
        Integer taxMonth = salaryInfoVos.stream().map(SalaryInfoVo::getTaxMonth).max(Integer::compareTo).get();
        Map<String, String> minTaxMonthDayMap = taxDeclarationInformationService.getMinTaxMonthDayMap(empIdList, taxMonth);
        List<EmployeeEntryDimission> employeeEntryDimissionList = employeeEntryDimissionMapper.selectAllEntryDimissionByEmpIdList(empIdList);
        Map<Long, EmployeeEntryDimission> employeeEntryDimissionMap = employeeEntryDimissionList.stream().collect(Collectors.toMap(EmployeeEntryDimission::getEmployeeId, employeeEntryDimission -> employeeEntryDimission));
        for (SupplierSalaryInfoVo salaryInfoVo:salaryInfoVos) {
            EmployeeEntryDimission employeeEntryDimission = employeeEntryDimissionMap.get(salaryInfoVo.getEmpId());
            if(employeeEntryDimission!=null){
                if(salaryInfoVo.getContractType()!=ContractType.CONTRACT_TYPE_PAYROLL_CREDIT.getCode()&&employeeEntryDimission.getApplyDimissionDate()!=null){
                    salaryInfoVo.setApplyDimissionDate(DateUtil.formatDateToString(employeeEntryDimission.getApplyDimissionDate(),DateUtil.DATE_FORMAT_LONG));
                }
                if(employeeEntryDimission.getApplyEntryTime()!=null){
                    salaryInfoVo.setApplyEntryTime(DateUtil.formatDateToString(employeeEntryDimission.getApplyEntryTime(),DateUtil.DATE_FORMAT_LONG));
                }
                String minKey = salaryInfoVo.getEmpId() + "-" + salaryInfoVo.getWithholdingAgentNo();
                if(minTaxMonthDayMap.containsKey(minKey)){
                    String employmentDate = minTaxMonthDayMap.get(minKey);
                    if(employmentDate.contains(REAL)){
                        employmentDate=employmentDate.replaceAll(REAL,"");
                        salaryInfoVo.setApplyEntryTime(employmentDate);
                    }
                }
            }
        }
        return salaryInfoVos;
    }

    @Override
    public Map<String, List<String>> getSupplierSalaryDropDownDataSource(Map<String, Object> params) {
        List<SupplierAreaVo> supplierAreaVoList=supplierMapper.getAllAggregationCommissioner();
        String loginName = params.get("loginName").toString();
        List<Long> querySupplierIdList = supplierAreaVoList.stream().filter(s -> loginName.equals(s.getCommissioner()) || loginName.equals(s.getPurchaser())).map(SupplierAreaVo::getSupplierId).distinct().collect(Collectors.toList());
        params.put("supplierIdList",querySupplierIdList);
        List<com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo> supplierSalaryBillVoList=supplierSalaryBillWrapperService.getSupplierSalaryTemplet(params);
        supplierSalaryBillVoList = supplierSalaryBillVoList.stream().filter(s -> s.getSupplierVerificationStatus().equals(SupplierVerificationStatus.PUSHED.getCode())
                || s.getSupplierVerificationStatus().equals(SupplierVerificationStatus.PROJECT_REJECTED.getCode())).collect(Collectors.toList());
        Map<Long, List<com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo>> supplierSalaryBillVoListMap = supplierSalaryBillVoList.stream()
                .collect(Collectors.groupingBy(com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo::getSupplierId));

        List<Long> supplierIdList = supplierSalaryBillVoList.stream().map(com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo::getSupplierId).distinct().collect(Collectors.toList());
        List<Long> templetIdList = supplierSalaryBillVoList.stream().map(com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo::getTempletId).distinct().collect(Collectors.toList());

        Map<Long,String> templetNameMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(templetIdList)){
            List<BillTempletVo> templetNameList = billTempletMapper.getBillTempletNameByTempIdList(templetIdList);
            templetNameMap=templetNameList.stream().collect(Collectors.toMap(BillTempletVo::getId,BillTempletVo::getTempletName));
        }

        //正则表达式
        String regEx="[\n`~!@#$%^&*()+=\\-|{}':;',\\[\\].<>/?~！@#￥%……&*（）+|{}【】‘；：”“’， 、？]";
        Pattern p = Pattern.compile(regEx);

        Map<Long,String> supplierNameMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(templetIdList)){
            List<SupplierVo> supplierNameList = supplierMapper.getSupplierByIds(supplierIdList);
            supplierNameMap=supplierNameList.stream().collect(Collectors.toMap(SupplierVo::getId,SupplierVo::getSupplierName));
            for (Long key:supplierNameMap.keySet()) {
                Matcher sm = p.matcher(supplierNameMap.get(key));
                String supplierName=sm.replaceAll("_").trim();
                supplierNameMap.put(key,supplierName);
            }
        }

        EntityWrapper<SupplierQuotation> wrapper = new EntityWrapper<>();
        wrapper.in("supplier_id",supplierIdList);
        List<SupplierQuotation> quotationList = supplierQuotationMapper.selectList(wrapper);
        Map<Long, List<SupplierQuotation>> supplierQuotationListMap = quotationList.stream().filter(s -> s.getServiceFeeType() == ServiceFeeType.PAYROLL_SERVICE_CHARGE.getCode()
                || s.getServiceFeeType() == ServiceFeeType.SOCIAL_SECURITY_WAGE_SERVICE_FEE.getCode()).collect(Collectors.groupingBy(SupplierQuotation::getSupplierId));
        Map<Long, List<String>> supplierQuotationMap=new HashMap<>();
        for (Long supplierId:supplierQuotationListMap.keySet()) {
            List<SupplierQuotation> supplierQuotations = supplierQuotationListMap.get(supplierId);
            List<String> quotationNoAndServiceFeeList = supplierQuotations.stream().map(s -> s.getSupQuotationNo() + "_" + s.getServiceFee()).distinct().collect(Collectors.toList());
            supplierQuotationMap.put(supplierId,quotationNoAndServiceFeeList);
        }



        Map<String, List<String>> dropDownDataSource=new LinkedHashMap<>();
        List<String> supplierNameList=new ArrayList<>();
        for (Long supplierId:supplierIdList) {
            if(supplierNameMap.containsKey(supplierId)){
                supplierNameList.add(supplierNameMap.get(supplierId)+"_"+supplierId);
            }
        }
        if(CollectionUtils.isEmpty(supplierNameList)){
            supplierNameList.add("");
        }
        supplierNameList = supplierNameList.stream().distinct().collect(Collectors.toList());
        dropDownDataSource.put("一级区域", supplierNameList);

        Map<String, List<String>> quotationNoAndServiceFeeListMap=new LinkedHashMap<>();
        for (Long supplierId:supplierSalaryBillVoListMap.keySet()) {
            String supplierName="";
            if(supplierNameMap.containsKey(supplierId)){
                supplierName=supplierNameMap.get(supplierId)+"_"+supplierId;
                List<com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo> voList = supplierSalaryBillVoListMap.get(supplierId);
                List<String> templetNameList=new ArrayList<>();
                for (com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo vo:voList) {
                    Long templetId=vo.getTempletId();
                    String templetName="";
                    if(templetNameMap.containsKey(templetId)){
                        Matcher tm = p.matcher(templetNameMap.get(templetId));//这里把想要替换的字符串传进来
                        templetName=tm.replaceAll("_").trim()+"_"+templetId+"_"+supplierId;
                        templetNameList.add(templetName);

                        if(supplierQuotationMap.containsKey(supplierId)){
                            List<String> quotationNoAndServiceFeeList = supplierQuotationMap.get(supplierId);
                            quotationNoAndServiceFeeListMap.put(templetName,quotationNoAndServiceFeeList);
                        }
                    }
                }
                //二级
                if(CollectionUtils.isNotEmpty(templetNameList)){
                    dropDownDataSource.put(supplierName,templetNameList.stream().distinct().collect(Collectors.toList()));
                }

            }
        }
        //3级
        for (String templetName:quotationNoAndServiceFeeListMap.keySet()) {
            dropDownDataSource.put(templetName,quotationNoAndServiceFeeListMap.get(templetName));
        }

        return dropDownDataSource;
    }

    @Override
    public Integer getPayStatusBySalaryBillVoList(List<SalaryBillVo> salaryBillVoAllList) {
        if(CollectionUtils.isEmpty(salaryBillVoAllList)){
            return SalaryInfoStatus.FUTURE_PAY.getCode();
        }
        List<Integer> payStatusBySalaryBillVoList = supplierSalaryInfoMapper.getPayStatusBySalaryBillVoList(salaryBillVoAllList);
        return payStatusBySalaryBillVoList.stream().max(Integer::compareTo).orElse(1);
    }

    @Override
    public void updateSupplierConfirmFlag(List<SalaryBillVo> salaryBillVoList, SupplierSalaryInfoVo supplierSalaryInfoVo) {
        if(CollectionUtils.isNotEmpty(salaryBillVoList)){
            supplierSalaryInfoMapper.updateSupplierConfirmFlag(salaryBillVoList,supplierSalaryInfoVo);
        }
    }

    @Override
    public List<WithholdingAgentVo> checkWithholdingAgent(SalaryBillSearchVo vo) {
        return supplierSalaryInfoMapper.checkWithholdingAgent(vo);
    }

    @Override
    public void deleteByVoList(List<SupplierSalaryInfoVo> supplierSalaryInfoVoList) {
        if(CollectionUtils.isNotEmpty(supplierSalaryInfoVoList)){
            supplierSalaryInfoMapper.deleteByVoList(supplierSalaryInfoVoList);
        }
    }

    /**
     * 获取符合条件的残障金比例vo
     */
    public DisabilityGoldRateVo getDisabilityGoldRateVo(List<DisabilityGoldRateVo> disabilityGoldRateVoList,SalaryBillVo salaryBillVo) {
        DisabilityGoldRateVo disabilityGoldRateVo = new DisabilityGoldRateVo();
        Map<String, List<DisabilityGoldRateVo>> cityCodeAndDisabilityGoldRateVoMap = disabilityGoldRateVoList.stream().collect(Collectors.groupingBy(DisabilityGoldRateVo::getCityCode));
        List<DisabilityGoldRateVo> disabilityGoldRateVos = cityCodeAndDisabilityGoldRateVoMap.get(salaryBillVo.getCityCode());

        if (CollectionUtils.isNotEmpty(disabilityGoldRateVos)) {
            /**
             * 大户
             */
            Integer largeCode = WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex();
            /**
             * 扣缴义务人类型
             */
            Integer withType = salaryBillVo.getWithholdingAgentType();
            /**
             * 大户code 单立户 供应商 id
             */
            String orgCode = salaryBillVo.getOrgCode();
            if (Objects.equals(largeCode, withType)) {
                /**
                 * 大户默认类型只有一个税率 并且orgCode为空
                 * 特殊的需要加上合同号 和orgCode比对
                 */
                List<DisabilityGoldRateVo> largeTypeDisRateList = disabilityGoldRateVos.stream().filter(o -> o.getType().equals(largeCode)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(largeTypeDisRateList)) {
                    List<DisabilityGoldRateVo> specialList = largeTypeDisRateList.stream().filter(o -> StringUtils.isNotEmpty(o.getContractNo()) && o.getOrgCode().equals(orgCode) && o.getContractNo().equals(salaryBillVo.getContractNo())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specialList)) {
                        disabilityGoldRateVo = specialList.get(0);
                    } else {
                        List<DisabilityGoldRateVo> defalutLargeDisRateList = largeTypeDisRateList.stream().filter(o -> Objects.equals(o.getDisType(), DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(defalutLargeDisRateList)){
                            disabilityGoldRateVo = defalutLargeDisRateList.get(0);
                        };
                    }
                }
            } else {
                List<DisabilityGoldRateVo> otherTypeDisRateList = disabilityGoldRateVos.stream().filter(o -> !o.getType().equals(largeCode) && o.getOrgCode().equals(orgCode) && o.getType().equals(withType)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherTypeDisRateList)) {
                    List<DisabilityGoldRateVo> specialList = otherTypeDisRateList.stream().filter(o -> StringUtils.isNotEmpty(o.getContractNo()) && o.getContractNo().equals(salaryBillVo.getContractNo())
                            &&(StringUtils.isBlank(o.getWithholdingAgentNo())||o.getWithholdingAgentNo().equals(salaryBillVo.getWithholdingAgentNo()))).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specialList)) {
                        disabilityGoldRateVo = specialList.get(0);
                    } else {
                        List<DisabilityGoldRateVo> defalutOtherDisRateList = otherTypeDisRateList.stream().filter(o -> Objects.equals(o.getDisType(), DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(defalutOtherDisRateList)){
                            disabilityGoldRateVo = defalutOtherDisRateList.get(0);
                        };
                    }
                }

            }
        }
        return disabilityGoldRateVo;
    }

    /**
     * 计算残障金金额
     * @param disabilityGoldRateVo vo
     * @param S037 应发工资
     * @param S007 实发工资
     * @return {@link BigDecimal }
     */
    public BigDecimal countDisFund(DisabilityGoldRateVo disabilityGoldRateVo,BigDecimal S037,BigDecimal S007,BigDecimal lastDisFund){
        BigDecimal disFund = BigDecimal.ZERO;
        if (disabilityGoldRateVo.getId()!=null){
            if (disabilityGoldRateVo.getDisType().equals(DisabilityGoldRateLogEnum.DisabilityType.DEFAULT.getCode())||disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.SHOULD_BE_CALCULATED.getCode())){
                disFund = disabilityGoldRateVo.getRate()!=null?(S037.multiply(disabilityGoldRateVo.getRate())).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
            }else {
                if (disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.FIXED_AMOUNT.getCode())){
                    disFund = disabilityGoldRateVo.getFixed();
                }else if (disabilityGoldRateVo.getSpecialType().equals(DisabilityGoldRateEnum.SpecialEnum.ACTUAL_CALCULATION.getCode())){
                    disFund = disabilityGoldRateVo.getRate()!=null?(S007.multiply(disabilityGoldRateVo.getRate())).setScale(2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
                }
            }
            if(lastDisFund!=null){
                disFund=disFund.add(lastDisFund);
            }
            if (disabilityGoldRateVo.getUpperLimit().compareTo(BigDecimal.ZERO) > 0&&disabilityGoldRateVo.getUpperLimit().compareTo(disFund)<0){
                disFund = disabilityGoldRateVo.getUpperLimit();
            }
            if(lastDisFund!=null){
                disFund=disFund.subtract(lastDisFund);
                if(disFund.compareTo(BigDecimal.ZERO)<0){
                    disFund=BigDecimal.ZERO;
                }
            }
        }
        return disFund;
    }
}
