layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload'], function () {
    var $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    $(document).ready(function () {
        $("#newFlag").val($("#newFlagCache").val());
        form.render("select");
    });

    //保存
    form.on("submit(save)", function (data) {
        ML.layuiButtonDisabled($('#save'));// 禁用
        $.ajax({
            url: ML.contextPath + "/customer/contract/updateNewFlagAndRemark",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });
        return false;
    });

    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });
});
