package com.reon.hr.sp.bill.service.bill;

import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.InsuranceDisposBillVo;
import com.reon.hr.api.bill.vo.bill.OneServiceBillCfgVo;
import com.reon.hr.sp.bill.entity.bill.InsuranceBill;
import com.reon.hr.sp.bill.entity.bill.OneServiceBillCfg;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IInsuranceDisposBillService {

    void generateBillByDisposBill(InsuranceDisposBillVo insuranceDisposBillVo) throws Exception;

	Map<String, List<OneServiceBillCfgVo>> getOneServiceBillCfgList(String contractNo, Long templetId, Integer billMonth);

	InsuranceBillVo getBillIdByContractNoAndTempletIdAndBillMonth(String contractNo, Long templetId, Integer billMonth);

	List<OneServiceBillCfg> getInsuranceDisposData(Long billId);

	Map<Long, BigDecimal> getBillIdAndCostMapByBillId(Set<Long> disposeInsuranceBillIdSet);

    Map<Long, Map<String, String>> getOneBillBillIdAndDataMap(Set<Long> oneBillIdSet);
}