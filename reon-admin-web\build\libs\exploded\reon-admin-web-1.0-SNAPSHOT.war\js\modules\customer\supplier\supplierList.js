var ctx = ML.contextPath;
layui.use(['form', 'layer', 'laydate', 'table'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    form.render('select');
    form.on('select(provinceFilter)',function (data) {
        $("#cityCode").attr("CITYS",data.value+"0000");
        ML.cityOption(data.value+"0000");
        form.render('select');
    });
     var  param = serialize("searchForm");
      param.supplierType =1;
    table.render({
        id: 'supplierGrid',
        elem: '#supplierGrid',
        url: ctx + '/customer/supplier/getSupplierListPage',
        where: {"paramData": JSON.stringify(param)},
        method: 'post',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        limit:50,
        height:650,
        title: "供应商信息",
        toolbar: '#toolbarDemo',
        defaultToolbar: [],
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%'},
            {field: 'supplierName', title: '供应商名称', width: '12%',align:'center'},
            {field: 'supplierType', title: '供应商类型', width: '12%',align:'center',templet: function(d){
                    return ML.dictFormatter("SUPPLIER_TYPE",d.supplierType);}
            },
            {field: 'supplierStatus', title: '状态', width: '12%',align:'center',templet: function(d){
                    return ML.dictFormatter("SUPPLIER_STATUS",d.supplierStatus);}},
            {field: 'contactor', title: '联系人', width: '7%',align:'center'},
            {field: 'tel', title: '联系方式', width: '7%',align:'center'},
            {field: 'email', title: '邮箱', width: '12%',align:'center'},
            {field: 'addr', title: '地址', width: '10%',align:'center'},
            {field: 'createTime', title: '注册时间', width: '10%',align:'center'},
            {field: 'purchaser', title: '采购负责人', width: '8%',align:'center',templet:function (d) {
                    return ML.loginNameFormater(d.purchaser);
                }},
            {field: 'commissioner', title: '客服负责人', width: '8%',align:'center',event: 'commissioner',templet:function (d) {
                //1代表社保
                if(d.supplierType==1){
                    return "单击查看供应商客服负责人"
                }else {
                    return ML.loginNameFormater(d.commissioner);
                }
                }},
           // {field: 'status', title: '审批状态', width: '8%'},
            {field: '', title: '操作', toolbar: '#toolDemo', width: '8%',align:'center'}
        ]],
        done:function () {
            ML.hideNoAuth();
            table.on('tool(supplierFilter)',function (obj) {
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                var ids = [];
                ids.push(data.id);
                switch (layEvent) {
                    case 'query':
                        editOne("查看供应商", 'query', data);
                        break;
                    case 'update':
                        editOne("编辑供应商", 'update', data);
                        break;
                    case 'delete':
                        optOneOrMany("删除", {"ids": JSON.stringify(ids)});
                        break;
                    case 'supplierArea':
                        supplierArea(data,ids);
                        break;
                    case 'commissioner':
                        //1代表社保
                        if(data.supplierType==1){
                            layer.open({
                                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                                title: "查看供应商客服负责人",
                                area: ['50%', '90%'],
                                shade: 0,
                                maxmin: true,
                                offset: 'auto',
                                shade: [0.8, '#393D49'],
                                content: ctx + "/customer/supplier/gotoSupplierAreaPage",
                                success:function (layero, index) {
                                    var body = layer.getChildFrame('body', index);
                                    body.find("#supplierName").val(data.supplierName);
                                    body.find("#purchaser").val(data.purchaser);
                                    body.find("#supplierType").val(data.supplierType);
                                    if (!ids){
                                        body.find("#supplierId").val(data.id);
                                    } else{
                                        body.find("#supplierId").val(ids);
                                    }
                                },
                                end:function () {
                                    reloadTable();
                                }
                            });
                        }
                        break;
                }
            });
            table.on('toolbar(supplierFilter)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
                var ids = [];// 选中操作的id数组
                data.forEach(function (supplier) {
                    ids.push(supplier.id);
                });
                switch (obj.event) {
                    case 'add':
                        editOne("新增供应商","add",null);
                        break;
                    case 'update':
                        if (data.length === 0) {
                            layer.msg('请选择一行');
                        } else if (data.length > 1) {
                            layer.msg('只能同时修改一个');
                        } else {
                            editOne("修改供应商","update",data[0]);
                        }
                        break;
                    case 'delete':
                        if (data.length === 0) {
                            layer.msg('请选择一行');
                        } else {
                            optOneOrMany("删除", {"ids": JSON.stringify(ids)});
                        }
                        break;
                    case 'supplierArea':
                        if (data.length != 1) {
                           return layer.msg('请选择一行');
                        }
                        supplierArea(data[0],ids);
                        break;
                    case 'export':
                        layer.msg("暂无");
                        break;
                    case 'import':
                        layer.msg("暂无");
                        break;
                    case 'disable'://禁用
                        if (data.length === 0) {
                            return layer.msg('请选择一行');
                        } else {
                            var index =0;
                            data.forEach(function (supplier) {
                                if (supplier.status!=2) {
                                    layer.msg('只用启用状态才能点禁用');
                                    return false;
                                }
                            })
                            if(index ===0){
                                updateStatus('disable', ids);
                            }
                        }
                        break;
                    case 'begin':
                        if (data.length === 0) {
                            return layer.msg('请选择一行');
                        } else {
                            var index =0;
                            data.forEach(function (supplier) {
                                if (data.commissioner === '') {
                                    layer.msg('供应商' + supplier.supplierName + '还未分配不能启用');
                                    index++;
                                    return false;
                                }
                            })
                            if(index===0){
                                updateStatus('begin', ids);
                            }
                        }
                        break;
                }
            });

        }
    });

    ////双击该行查看
    table.on('rowDouble(supplierFilter)', function (obj) {
        var data = obj.data;
        editOne("查看供应商", 'query', data);
    });

    function editOne(title, optType, data) {
        var url ="";
        if (optType=='add'){
            url="/customer/supplier/gotoSaveSupplierPage?optType="+optType;
        }else if (optType=='update'){
            url="/customer/supplier/gotoEditSupplierPage?supplierId="+data.id+"&optType="+optType;
        } else if (optType=='query'){
            url="/customer/supplier/gotoQuerySupplierPage?supplierId="+data.id+"&optType="+optType;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['60%', '80%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            end: function () {
                reloadTable();
            }
        });
    }
    ///逻辑删除
    function optOneOrMany(title, params) {
        layer.confirm("确认要" + title + "吗？", {title: title + "确认"}, function (index) {
            layer.close(index);
            ML.ajax("/customer/supplier/delete", params, function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    layer.closeAll('iframe');//关闭弹窗
                }
               reloadTable();
            });
        });
    }
    //分配城市
    function supplierArea(data,ids){
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: "分配城市",
            area: ['80%', '90%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + "/customer/supplier/gotoSupplierAreaPage",
            success:function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#supplierName").val(data.supplierName);
                body.find("#purchaser").val(data.purchaser);
                if (!ids){
                    body.find("#supplierId").val(data.id);
                } else{
                    body.find("#supplierId").val(ids);
                }
            },
            end:function () {
                reloadTable();
            }
        });
    }

////查询
    form.on('submit(btnQueryFilter)', function (data) {
        reloadTable();
        return false;
    });

    function reloadTable() {
        param =  serialize("searchForm");
        param.supplierType =1;//社保供应商
        table.reload('supplierGrid', {
            where: {
                paramData: JSON.stringify(param),
                page: {curr: 1} //重新从第 1 页开始
            }
        });
    }

    //日期范围
    var startTime = laydate.render({
        elem: '#startTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            endTime.config.min = {
                year: date.year,
                month: date.month - 1, //关键
                date: date.date
            };
        }
    });
    var endTime = laydate.render({
        elem: '#endTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            startTime.config.max = {
                year: date.year,
                month: date.month - 1,//关键
                date: date.date
            }
        }
    });
    //批量启用和禁用
    function updateStatus(optType,ids){
        //禁用要判断是否已经被使用，被使用不能禁用，后台抛异常判断
        ML.ajax("/customer/supplier/updateStatusNosupplier", {"ids":JSON.stringify(ids),"optType":optType}, function (result) {
            layer.msg(result.msg);
            if (result.code == 0) {
                // layer.closeAll('iframe');//关闭弹窗
            }
            reloadTable();
        });


    }

});