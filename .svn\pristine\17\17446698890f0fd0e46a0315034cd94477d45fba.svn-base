package com.reon.hr.api.customer.vo.supplier;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/8/7
 * @DESCRIPTION:
 */

@Data
public class SupplierContractNewVo implements Serializable {



    private String contractNo;

    
    private Long id;
    /**
     * 文件
     */
    private List<String> fileIdList;

    /**
     * 报价单
     */
    private List<SupplierQuotationVo> quotationList;

    /**
     * 供应商id
     */
    private Long supplierId;

    private String supplierName;

    /**
     * 供应商类型
     */
    private Integer supplierType;

    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 我方签约公司
     */
    @Getter
    private String orgCode;

    /**
     * 产品类型
     */
    private Integer prodType;

    /**
     * 服务费
     */
    private BigDecimal fee;

    /**
     * 起始日
     */
    private String startTime;

    /**
     * 结束日
     */
    private String endTime;

    /**
     * 账单日
     */
    private Integer billDate;

    /**
     * 付款日
     */
    private Integer payDate;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 流程id
     */
    private String pid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    private Date endTimeD;

    private Date startTimeD;

    private Integer supplierStatus;

    private String contractName;

    private String taskId;

    private List<SupplierContractNewVo> supplierContractNewVoList;

    private String creatorPos;

    private String creatorOrg;

    private Integer fileType;
    private Integer procType;
    private Integer approvalStatus;

    /**
     * 关联公司
     */
    private List<String> relatedCompList;
    /**
     * 是否关联公司 1否 2是
     */
    private Integer relatedCompFlag;

    /**
     * 是否自动顺延 1否 2是
     */
    private Integer postponeFlag;

    /**
     * 补充协议文件
     */
    private List<String> agreementFileIdList;

    /**
     * 续签文件
     */
    private List<String> renewFileIdList;

    private String quoRemark;

}
