/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2022/12/29
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.bill.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.*;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceRatioWrapperService;
import com.reon.hr.api.base.enums.InsuranceRatioChargeFreq;
import com.reon.hr.api.bill.dto.PerInsuranceBillAndPerInsuranceBillItemDto;
import com.reon.hr.api.bill.exception.InsuranceBillDealException;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.bill.vo.BillLockOrUnLockVo;
import com.reon.hr.api.bill.vo.BillMainServiceCount;
import com.reon.hr.api.bill.vo.PerInsuranceBillVo;
import com.reon.hr.api.customer.constant.ContractStatus;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAreaResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IQuotationResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeEntryDimissionWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IOrderInsuranceCfgWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IOrderServiceChargeWrapperService;
import com.reon.hr.api.customer.enums.contract.ContractAreaRecceivingType;
import com.reon.hr.api.customer.vo.ContractAreaVo;
import com.reon.hr.api.customer.vo.EmployeeContract;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.customer.vo.employee.OrderServiceChargeVo;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.util.BillEnum;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.common.constants.CommonConstants;
import com.reon.hr.common.enums.DelFlagEnum;
import com.reon.hr.common.enums.PerInsuranceBillEnum;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.sp.bill.dao.bill.*;
import com.reon.hr.sp.bill.entity.bill.*;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillDealService;
import com.reon.hr.sp.bill.service.bill.IPerInsuranceBillService;
import com.reon.hr.sp.bill.utils.EnCryptUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class IInsuranceBillDealServiceImpl implements IInsuranceBillDealService {
	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Autowired
	private PerInsuranceBillMapper perInsuranceBillMapper;
	@Autowired
	private PerInsuranceBillItemMapper perInsuranceBillItemMapper;
	@Autowired
	private IEmployeeEntryDimissionWrapperService iEmployeeEntryDimissionWrapperService;
	@Autowired
	private InsuranceCfgSnapshotMapper insuranceCfgSnapshotMapper;
	@Autowired
	IOrderInsuranceCfgWrapperService iOrderInsuranceCfgWrapperService;
	@Autowired
	InsuranceBillMapper insuranceBillMapper;
	@Autowired
	private IPerInsuranceBillService perInsuranceBillService;
	@Autowired
	private OrderSnapshotMapper orderSnapshotMapper;
	@Autowired
	private IEmployeeOrderWrapperService iEmployeeOrderWrapperService;
	@Autowired
	private BeforeOrderServiceMonthMapper beforeOrderServiceMonthMapper;
	@Autowired
	IInsuranceRatioWrapperService iInsuranceRatioWrapperService;
	@Autowired
	BeforeOrderServiceMonthServiceImpl beforeOrderServiceMonthService;
	@Autowired
	IContractAreaResourceWrapperService iContractAreaResourceWrapperService;
	@Autowired
	ISupplierWrapperService iSupplierWrapperService;
	@Autowired
	private BillUnlockApprovalMapper billUnlockApprovalMapper;
	@Autowired
    private IOrderServiceChargeWrapperService orderServiceChargeWrapperService;
    @Autowired
    private IContractResourceWrapperService contractWrapperService;
    @Autowired
    private IQuotationResourceWrapperService iQuotationResourceWrapperService;

    public void updateBeforeOrderServiceMonthData(List<String> dealOrderNos) {
		log.info("------------------- 处理BeforeOrderServiceMonth 数据开始 -------------------");
		long startTime = System.currentTimeMillis();
		List<BeforeOrderServiceMonth> beforeOrderServiceMonthList = Lists.newArrayList();
		if (CollectionUtils.isEmpty(dealOrderNos)) {
			beforeOrderServiceMonthList = beforeOrderServiceMonthService.getAllBeforeOrderServiceMonth();
		} else {
			beforeOrderServiceMonthList = beforeOrderServiceMonthService.getDataByOrderNoList(dealOrderNos);
		}

		Map<String, Map<Integer, BeforeOrderServiceMonth>> orderNoAndProductCodeAndDataMap = beforeOrderServiceMonthList.stream().collect(groupingBy(BeforeOrderServiceMonth::getOrderNo, toMap(BeforeOrderServiceMonth::getProdCode, Function.identity())));
		Set<String> orderNoSet = orderNoAndProductCodeAndDataMap.keySet();

		for (String orderNo : orderNoSet) {
			Map<Integer, BeforeOrderServiceMonth> productCodeAndDataMap = orderNoAndProductCodeAndDataMap.get(orderNo);
			Set<Integer> productCodeSet = productCodeAndDataMap.keySet();
			for (Integer productCode : productCodeSet) {
				BeforeOrderServiceMonth beforeOrderServiceMonth = orderNoAndProductCodeAndDataMap.get(orderNo).get(productCode);

				String yearServiceMonth = beforeOrderServiceMonth.getYearServiceMonth();
				String yearServiceMonthCache = beforeOrderServiceMonth.getYearServiceMonthCache();
				String serviceMonth = beforeOrderServiceMonth.getServiceMonth();
				String serviceMonthCache = beforeOrderServiceMonth.getServiceMonthCache();

				Map<Integer, Set<Integer>> yearServiceMonthCacheMap = Maps.newHashMap();  /** Map<billMonth,Set<receiveMonth>>  */
				Map<Integer, Set<Integer>> yearServiceMonthMap = Maps.newHashMap();     /**  Map<receiveMonth,Set<billMonth>>  */
				Map<Integer, Set<Integer>> serviceMonthCacheMap = Maps.newHashMap();  /** Map<billMonth,Set<receiveMonth>>  */
				Map<Integer, Set<Integer>> serviceMonthMap = Maps.newHashMap();     /**   Map<receiveMonth,Set<billMonth>>  */

				if (StringUtils.isNotBlank(serviceMonth))
					serviceMonthMap = com.reon.hr.api.bill.utils.JsonUtil.jsonToMapSet(serviceMonth);
				if (StringUtils.isNotBlank(serviceMonthCache))
					serviceMonthCacheMap = com.reon.hr.api.bill.utils.JsonUtil.jsonToMapSet(serviceMonthCache);
				if (StringUtils.isNotBlank(yearServiceMonth))
					yearServiceMonthMap = com.reon.hr.api.bill.utils.JsonUtil.jsonToMapSet(yearServiceMonth);
				if (StringUtils.isNotBlank(yearServiceMonthCache))
					yearServiceMonthCacheMap = com.reon.hr.api.bill.utils.JsonUtil.jsonToMapSet(yearServiceMonthCache);

				mergeServiceMonthAndCache(serviceMonthMap, serviceMonthCacheMap);
				mergeServiceMonthAndCache(yearServiceMonthMap, yearServiceMonthCacheMap);
				beforeOrderServiceMonth.setServiceMonth(JsonUtil.beanToJson(serviceMonthMap)).setServiceMonthCache(JsonUtil.beanToJson(serviceMonthCacheMap))
						.setYearServiceMonth(JsonUtil.beanToJson(yearServiceMonthMap)).setYearServiceMonthCache(JsonUtil.beanToJson(yearServiceMonthCacheMap));
			}
		}
		if (CollectionUtils.isNotEmpty(beforeOrderServiceMonthList)) {
			//	分组更新
			int dataSize = beforeOrderServiceMonthList.size();
			int dealCapacity = 5000;  // 处理大小
			int dealTimes = dataSize % dealCapacity == 0 ? dataSize / dealCapacity : (dataSize / dealCapacity) + 1;
			log.info("数据大小:{},每次处理大小:{},处理次数:{}", dataSize, dealCapacity, dealTimes);
			for (int i = 0; i < dealTimes; i++) {
				log.info("更新致第{}轮", i);
                beforeOrderServiceMonthService.batchUpdateByList(beforeOrderServiceMonthList.subList(i * dealCapacity, i == (dealTimes - 1) ? dataSize : (i + 1) * dealCapacity));
			}
		} else {
			log.info("该次处理没有数据!!请检查!!");
		}
		long endTime = System.currentTimeMillis();
        log.info("------------------- 处理BeforeOrderServiceMonth 数据结束 处理时间:{}ms-------------------", (endTime - startTime));
	}

	private void mergeServiceMonthAndCache(Map<Integer, Set<Integer>> serviceMonthMap, Map<Integer, Set<Integer>> serviceMonthCacheMap) {
		int currentYearMonth = DateUtil.getCurrentYearMonth();
		if (MapUtils.isNotEmpty(serviceMonthCacheMap)) {
			Set<Integer> billMonthFroCache = Sets.newHashSet(serviceMonthCacheMap.keySet());
			for (Integer billMonthForCache : billMonthFroCache) {
				/** 不包含当前月和之后的月份  将缓存中的数据并入*/
				if (billMonthForCache < currentYearMonth) {
					for (Integer receiveMonthForCache : serviceMonthCacheMap.get(billMonthForCache)) {
						Set<Integer> billMonthSet = serviceMonthMap.getOrDefault(receiveMonthForCache, Sets.newHashSet());
						billMonthSet.add(billMonthForCache);
						serviceMonthMap.put(receiveMonthForCache, billMonthSet);
					}
					/** 删除掉缓存数据 */
					serviceMonthCacheMap.remove(billMonthForCache);
				}
			}
		}
	}

	@Override
	public void dealTwoMonthsAgoPerInsuranceBillItem(List<String> dealOrderNos) {
		log.info("-------------dealTwoMonthsAgoPerInsuranceBillItem处理开始-------------");
		long startTime = System.currentTimeMillis();
		Integer nextYearMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.formatDateToString(new Date(), "yyyyMM")), 4);   // 当前月往后四个月
		List<Integer> monthBetween = DateUtil.getMonthBetween("201811", nextYearMonth.toString());
		Integer markMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.formatDateToString(new Date(), "yyyyMM")), -1);  // markMonth 用来标记月份,是进入cache 还是进入serviceMonth字段

		/** 根据月份分的orderNo Map */
		HashMap<Integer, Set<String>> monthAndOrderNoListMap = Maps.newHashMap();
		/** 所有查出的productCode 和 receiveMonth 和 bill_type List*/
		List<PerInsuranceBillAndPerInsuranceBillItemDto> normalList = Lists.newArrayList();
		List<PerInsuranceBillAndPerInsuranceBillItemDto> greaterThanMarkMonthList = Lists.newArrayList();  // 大于等于当前月的item数据List
		List<EmployeeOrderVo> allOrderVoList = Lists.newArrayList();

		/** 获取所有年缴产品的insurance_code  chargeFreq = 3 4 */
		List<Integer> chargeFreqList = Lists.newArrayList(InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode(), InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode());
		HashSet<String> yearInsuranceCodeSet = iInsuranceRatioWrapperService.getAllInsuranceCodeByChargeFreq(chargeFreqList);

		if (CollectionUtils.isEmpty(dealOrderNos)) {
			/**  先删再添加 */
			int deleteResult = beforeOrderServiceMonthMapper.deleteAll();
			long orderVoListCount = iEmployeeOrderWrapperService.getAllEmployeeOrderNoByContractStatusCount(ContractStatus.NEW_SIGN.getCode());
			/** 这里用了分页查询, 需要在凌晨处理,不然线上有人新增,数据容易有误差 */
			/** 获取所有订单数据 不查询已经终止的合同*/
			long dealCapacity = 4000;  // 处理大小
			long dealCount = orderVoListCount / dealCapacity;
			long dealFrequency = orderVoListCount % dealCapacity == 0 ? dealCount : dealCount + 1;  // 处理次数

			for (long i = 0; i < dealFrequency; i++) {
				log.info("分页查询 page: " + i);
				long start = i * dealCapacity;
				long end = (i == dealFrequency - 1 ? orderVoListCount : (i + 1) * dealCapacity - 1);
				List<EmployeeOrderVo> orderVoList = iEmployeeOrderWrapperService.getAllEmployeeOrderNoByContractStatus(ContractStatus.NEW_SIGN.getCode(), start, end);
				allOrderVoList.addAll(orderVoList);
			}
		} else {
			/**  先删再添加 */
			EntityWrapper<BeforeOrderServiceMonth> wrapper = new EntityWrapper<>();
			wrapper.in("order_no", dealOrderNos);
			int deleteResult = beforeOrderServiceMonthMapper.delete(wrapper);
			List<EmployeeOrderVo> orderVoList = iEmployeeOrderWrapperService.getAllEmployeeOrderNoByContractStatusAndOrderNoList(ContractStatus.NEW_SIGN.getCode(), dealOrderNos);
			allOrderVoList.addAll(orderVoList);
		}

		//YD-20210204001792
		Map<String, Set<Long>> orderNoAndCountMap = allOrderVoList.stream().parallel().collect(groupingBy(EmployeeOrderVo::getOrderNo, mapping(EmployeeOrderVo::getTempletId, toSet())));

		/** 拿到所有的order_no 进行产品起始月截止月处理 */
		Set<String> dealCfgStartMonthAndExpireMonthOrderNoSet = Sets.newHashSet(orderNoAndCountMap.keySet());

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.MONTH, -6);
		Date date = calendar.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String time = sdf.format(date);
		Set<String> alreadyDimissionOrderNo = iEmployeeEntryDimissionWrapperService.adjustDimissionByOrderNo(dealCfgStartMonthAndExpireMonthOrderNoSet, time);
		/** 获取到所有离职的,拿到产品的最小起始月和最大截止月  Map<OrderNo,Map<productCode,Map<max/min,month>>>>   这里只取离职的*/
		Map<String, Map<Integer, Map<String, Integer>>>
				orderNoAndProductAndMaxMinMonthMap = iOrderInsuranceCfgWrapperService.getCfgStartAndExpireByOrderNoSet(alreadyDimissionOrderNo);

		/** 产品中有不同账单模板的 orderNoList  如果有的话,就是错误数据需要直接报错,并进行处理*/
		Set<String> mulTempletIdOrderNoList = orderNoAndCountMap.keySet().stream().parallel().filter(item -> orderNoAndCountMap.get(item).size() > 1).collect(toSet());
		if (mulTempletIdOrderNoList.size() > 0) {
			StringBuilder sb = new StringBuilder("同一个个人订单有多个账单模板Id orderNo: ");
			sb.append(mulTempletIdOrderNoList.toString());
			log.info(sb.toString());
			throw new InsuranceBillDealException("同一个个人订单有多个账单模板Id,请检查!!");
		}
//		List<InsuranceBill> insuranceBillList = insuranceBillMapper.getAllContractAndTempletAndBillMonthByTwoMonthsAgoMonth(String.valueOf(nextYearMonth));
//		Map<String, Set<Integer>> contractTempletIdAndBillMonthSetMap = insuranceBillList.stream().collect(groupingBy(item -> item.getContractNo() + item.getTempletId(), mapping(InsuranceBill::getBillMonth, toSet())));
		/** 根据账单数据过滤订单编号 */
		for (EmployeeOrderVo employeeOrderVo : allOrderVoList) {
//			String contractNoAndTempletIdStr = employeeOrderVo.getContractNo() + employeeOrderVo.getTempletId();
			String orderNo = employeeOrderVo.getOrderNo();
//			Set<Integer> billMonthSet = contractTempletIdAndBillMonthSetMap.getOrDefault(contractNoAndTempletIdStr, Sets.newHashSet());
			for (Integer month : monthBetween) {
					Set<String> orderNoSet = monthAndOrderNoListMap.getOrDefault(month, Sets.newHashSet());
					orderNoSet.add(orderNo);
					monthAndOrderNoListMap.put(month, orderNoSet);
			}
		}

		for (Integer month : monthBetween) {
			Set<String> orderNoSet = monthAndOrderNoListMap.get(month);
			List<PerInsuranceBillAndPerInsuranceBillItemDto> productCodeAndReceiveMonthList = Lists.newArrayList();
			if (CollectionUtils.isNotEmpty(orderNoSet))
				productCodeAndReceiveMonthList = perInsuranceBillItemMapper.getProductCodeAndReceiveMonth(Lists.newArrayList(orderNoSet),
						PerInsuranceBillEnum.PER_INSURSNCE_BILL_TABLE_PREFIX.getName() + month,
						PerInsuranceBillEnum.PER_INSURANCE_BILL_ITEM_TABLE_PREFIX.getName() + month, month);

			if (month >= markMonth) {   // 大于等于当前月 要进行分别处理,进入cache字段
				greaterThanMarkMonthList.addAll(productCodeAndReceiveMonthList);
			} else
				normalList.addAll(productCodeAndReceiveMonthList);
		}

		/** -------------小于markMonth的Item数据处理S-------------*/
		/** Map<orderNoAndProductCode,Map<receivableMonth,<Set<BillMonth>>> */
		Map<String, Map<Integer, Set<Integer>>> orderNoProductCodeAndReceiveMonthStrMap = normalList.stream().parallel()
				.filter(item -> StringUtils.isNotBlank(item.getOrderNo()) && item.getProductCode() != null
                        && (StringUtils.isNotBlank(item.getInsuranceRatioCode()) || item.getProductCode().equals(CommonConstants.SERVICE_FEE_PRODUCT_CODE)) && !yearInsuranceCodeSet.contains(item.getInsuranceRatioCode()))
				.collect(groupingBy(item -> item.getOrderNo() + "," + item.getProductCode(),
						groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getReceivableMonth, mapping(PerInsuranceBillAndPerInsuranceBillItemDto::getBillMonth, toSet())))
				);

		/** 过滤出年缴数据  Map<orderNoAndProductCode, Map<receivableMonth,<Set<BillMonth>>>*/
		Map<String, Map<Integer, Set<Integer>>> yearOrderNoProductCodeAndInsuranceCodeMap = normalList.stream().parallel()
				.filter(item -> StringUtils.isNotBlank(item.getOrderNo()) && item.getProductCode() != null && StringUtils.isNotBlank(item.getInsuranceRatioCode())
						&& yearInsuranceCodeSet.contains(item.getInsuranceRatioCode()))
				.collect(groupingBy(item -> item.getOrderNo() + "," + item.getProductCode(),
						groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getReceivableMonth, mapping(PerInsuranceBillAndPerInsuranceBillItemDto::getBillMonth, toSet()))));
		/** -------------小于markMonth的Item数据处理E-------------*/

		/** -------------大于等于markMonth的Item数据处理S-------------*/
		/**  Map<orderNoAndProductCode, Map<billMonth,<Set<receivableMonth>>>*/
		Map<String, Map<Integer, Set<Integer>>> gtOrderNoProductCodeAndBillMonthStrMap = greaterThanMarkMonthList.stream().parallel()
				.filter(item -> StringUtils.isNotBlank(item.getOrderNo()) && item.getProductCode() != null && StringUtils.isNotBlank(item.getInsuranceRatioCode()) && !yearInsuranceCodeSet.contains(item.getInsuranceRatioCode()))
				.collect(groupingBy(item -> item.getOrderNo() + "," + item.getProductCode(), groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getBillMonth, mapping(PerInsuranceBillAndPerInsuranceBillItemDto::getReceivableMonth, toSet()))));
		Map<String, Map<Integer, Set<Integer>>> gtYearOrderNoProductCodeAndBillMonthMap = greaterThanMarkMonthList.stream().parallel()
				.filter(item -> StringUtils.isNotBlank(item.getOrderNo()) && item.getProductCode() != null && StringUtils.isNotBlank(item.getInsuranceRatioCode()) && yearInsuranceCodeSet.contains(item.getInsuranceRatioCode()))
				.collect(groupingBy(item -> item.getOrderNo() + "," + item.getProductCode(), groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getBillMonth, mapping(PerInsuranceBillAndPerInsuranceBillItemDto::getReceivableMonth, toSet()))));
		/** -------------大于等于markMonth的Item数据处理E-------------*/

		HashSet<String> gtOrderNoAndProductCodeSet = Sets.newHashSet(gtOrderNoProductCodeAndBillMonthStrMap.keySet());
		gtOrderNoAndProductCodeSet.addAll(gtYearOrderNoProductCodeAndBillMonthMap.keySet());

		/** 合并 */
		HashSet<String> allOrderNoProductCodeStrSet = Sets.newHashSet(orderNoProductCodeAndReceiveMonthStrMap.keySet());
		allOrderNoProductCodeStrSet.addAll(yearOrderNoProductCodeAndInsuranceCodeMap.keySet());

		/** 过滤出当前月以前数据没有的  删除掉所有没有包含的,剩下的就是需要新建的*/
		gtOrderNoAndProductCodeSet.removeAll(allOrderNoProductCodeStrSet);

		List<BeforeOrderServiceMonth> beforeOrderServiceMonthList = Lists.newArrayList();
		for (String orderNoAndProductCodeStr : allOrderNoProductCodeStrSet) {
			String receiveMonthAndBillMonthMapJson = null;
			String yearReceiveMonthAndBillMonthMapJson = null;
			String gtBillMonthAndReceiveMonthMapCacheJson = null;
			String yearBillMonthAndReceiveMonthMapCacheJson = null;
			if (gtOrderNoProductCodeAndBillMonthStrMap.containsKey(orderNoAndProductCodeStr))
				gtBillMonthAndReceiveMonthMapCacheJson = JsonUtil.beanToJson(gtOrderNoProductCodeAndBillMonthStrMap.get(orderNoAndProductCodeStr));
			if (gtYearOrderNoProductCodeAndBillMonthMap.containsKey(orderNoAndProductCodeStr))
				yearBillMonthAndReceiveMonthMapCacheJson = JsonUtil.beanToJson(gtYearOrderNoProductCodeAndBillMonthMap.get(orderNoAndProductCodeStr));
			if (orderNoProductCodeAndReceiveMonthStrMap.containsKey(orderNoAndProductCodeStr))
				receiveMonthAndBillMonthMapJson = JsonUtil.beanToJson(orderNoProductCodeAndReceiveMonthStrMap.get(orderNoAndProductCodeStr));
			if (yearOrderNoProductCodeAndInsuranceCodeMap.containsKey(orderNoAndProductCodeStr))
				yearReceiveMonthAndBillMonthMapJson = JsonUtil.beanToJson(yearOrderNoProductCodeAndInsuranceCodeMap.get(orderNoAndProductCodeStr));

			Set<Integer> receiveMonthSet = orderNoProductCodeAndReceiveMonthStrMap.getOrDefault(orderNoAndProductCodeStr, Maps.newHashMap()).keySet();

			List<Integer> noDealMonthList = Lists.newArrayList();
			if (CollectionUtils.isNotEmpty(receiveMonthSet)) {
				List<Integer> receiveMonthList = Lists.newArrayList(receiveMonthSet);
				Collections.sort(receiveMonthList);
				String[] split = orderNoAndProductCodeStr.split(",");
				String orderNo = split[0];
				Integer productCode = Integer.valueOf(split[1]);
				Map<String, Integer> maxMinMonthMap = orderNoAndProductAndMaxMinMonthMap.getOrDefault(orderNo, Maps.newHashMap()).getOrDefault(productCode, Maps.newHashMap());
				if (MapUtils.isNotEmpty(maxMinMonthMap)) {
					// receiveMonthList.get(0) 拿到第一条最小的 因为排序过了,肯定是最小的月份
					if (!(maxMinMonthMap.get(CommonConstants.MIN).equals(receiveMonthList.get(0))) && receiveMonthList.get(0) > maxMinMonthMap.get(CommonConstants.MIN)) {
						List<Integer> monthBetweenOne = DateUtil.getMonthBetween(maxMinMonthMap.get(CommonConstants.MIN).toString(), receiveMonthList.get(0).toString());
						/** 删除掉最后一条 不应该包含receiveMonthList中的最小值*/
						monthBetweenOne.remove(monthBetweenOne.size() - 1);
						noDealMonthList.addAll(monthBetweenOne);
					}
					if (!(maxMinMonthMap.get(CommonConstants.MAX).equals(receiveMonthList.get(receiveMonthList.size() - 1))) && maxMinMonthMap.get(CommonConstants.MAX) > receiveMonthList.get(receiveMonthList.size() - 1)) {
						List<Integer> monthBetweenTwo = DateUtil.getMonthBetween(receiveMonthList.get(receiveMonthList.size() - 1).toString(), maxMinMonthMap.get(CommonConstants.MAX).toString());
						monthBetweenTwo.remove(0);  // 不应该包含 receiveList中最后那条数据
						noDealMonthList.addAll(monthBetweenTwo);
					}
				}
			}

			if (StringUtils.isNotBlank(orderNoAndProductCodeStr)) {
				String[] orderNoAndProductCodeArray = orderNoAndProductCodeStr.split(",");
				BeforeOrderServiceMonth beforeOrderServiceMonth = new BeforeOrderServiceMonth();
				beforeOrderServiceMonth.setCreator("admin").setOrderNo(orderNoAndProductCodeArray[0]).setProdCode(Integer.valueOf(orderNoAndProductCodeArray[1]))
						.setServiceMonth(receiveMonthAndBillMonthMapJson).setYearServiceMonth(yearReceiveMonthAndBillMonthMapJson)
						.setServiceMonthCache(gtBillMonthAndReceiveMonthMapCacheJson).setYearServiceMonthCache(yearBillMonthAndReceiveMonthMapCacheJson).setNoDealMonth(JsonUtil.beanToJson(noDealMonthList));
				beforeOrderServiceMonthList.add(beforeOrderServiceMonth);
			}
		}
		for (String gtOrderNoAndProductCodeStr : gtOrderNoAndProductCodeSet) {
			String gtBillMonthAndReceiveMonthMapCacheJson = null;
			String yearBillMonthAndReceiveMonthMapCacheJson = null;
			if (gtOrderNoProductCodeAndBillMonthStrMap.containsKey(gtOrderNoAndProductCodeStr))
				gtBillMonthAndReceiveMonthMapCacheJson = JsonUtil.beanToJson(gtOrderNoProductCodeAndBillMonthStrMap.get(gtOrderNoAndProductCodeStr));
			if (gtYearOrderNoProductCodeAndBillMonthMap.containsKey(gtOrderNoAndProductCodeStr))
				yearBillMonthAndReceiveMonthMapCacheJson = JsonUtil.beanToJson(gtYearOrderNoProductCodeAndBillMonthMap.get(gtOrderNoAndProductCodeStr));
			if (StringUtils.isNotBlank(gtOrderNoAndProductCodeStr)) {
				String[] gtOrderNoAndProductCodeArray = gtOrderNoAndProductCodeStr.split(",");
				BeforeOrderServiceMonth beforeOrderServiceMonth = new BeforeOrderServiceMonth();
				beforeOrderServiceMonth.setCreator("admin").setOrderNo(gtOrderNoAndProductCodeArray[0]).setProdCode(Integer.valueOf(gtOrderNoAndProductCodeArray[1]))
						.setServiceMonthCache(gtBillMonthAndReceiveMonthMapCacheJson).setYearServiceMonthCache(yearBillMonthAndReceiveMonthMapCacheJson);
				beforeOrderServiceMonthList.add(beforeOrderServiceMonth);
			}
		}


		if (beforeOrderServiceMonthList.size() > 0) {
			int allInsertCount = beforeOrderServiceMonthList.size();
			int insertFrquency = 5000;
			int insertCount = allInsertCount % insertFrquency == 0 ? allInsertCount / insertFrquency : allInsertCount / insertFrquency + 1;
			for (int i = 0; i < insertCount; i++) {
                beforeOrderServiceMonthMapper.insertList(beforeOrderServiceMonthList.subList(i * insertFrquency, i == insertCount - 1 ? allInsertCount : (i + 1) * insertFrquency));
			}
		}
		long endTime = System.currentTimeMillis();
        log.info("-------------dealTwoMonthsAgoPerInsuranceBillItem处理结束 用了 {}秒 -------------", (endTime - startTime) / 1000);
	}


	public void updateRestoreInsuranceBillSnapshot(String contractNo) {

		log.info("------------------- 开始恢复快照 -------------------");
		/** 完全没有快照的订单编号 */
		Set<String> noHaveSnapshotOrderNoSet = Sets.newHashSet();
		/** 即没有产品,也没有快照的orderNoList */
		List<String> noHaveSnapshotAndCfgOrderNoList = Lists.newArrayList();
		Set<String> orderSnapshotHaveCfgSnapshotNoHaveOrderNo = Sets.newHashSet();

		/** 获取该合同下的所有离职的 订单编号 */
		List<String> orderNoByContractNoList = iEmployeeEntryDimissionWrapperService.getAllOrderNoByContractNo(contractNo);
		/** 对比获取出所有没有快照的订单编号 */
		List<InsuranceCfgSnapshot> insuranceCfgSnapshots = insuranceCfgSnapshotMapper.selectAllSnapshotByOrderNo(orderNoByContractNoList);
		Map<String, List<InsuranceCfgSnapshot>> insuranceCfgSnapshotsByOrderNoMap = insuranceCfgSnapshots.stream().collect(Collectors.groupingBy(InsuranceCfgSnapshot::getOrderNo));
		noHaveSnapshotOrderNoSet = orderNoByContractNoList.stream().filter(item -> {
			return !insuranceCfgSnapshotsByOrderNoMap.containsKey(item);
		}).collect(Collectors.toSet());
		/** 获取到当前这些人的所有产品 */
		Map<String, List<OrderInsuranceCfgVo>> orderInsuranceCfgVosByOrderNoMap = Maps.newHashMap();
		if (noHaveSnapshotOrderNoSet.size() > 0)
			orderInsuranceCfgVosByOrderNoMap = iOrderInsuranceCfgWrapperService.getCfgMapByOrderNoList(Lists.newArrayList(noHaveSnapshotOrderNoSet));

		/** 即没有产品,也没有快照的orderNoList */
		Map<String, List<OrderInsuranceCfgVo>> finalOrderInsuranceCfgVosByOrderNoMap = orderInsuranceCfgVosByOrderNoMap;
		noHaveSnapshotAndCfgOrderNoList = noHaveSnapshotOrderNoSet.stream().filter(orderNo -> !finalOrderInsuranceCfgVosByOrderNoMap.containsKey(orderNo)).collect(Collectors.toList());

		/** 获取202106 - 当前 per_insurance_item_bill */
		String nowDate = DateUtil.formatDateToString(new Date(), "yyyyMM");
		List<Integer> monthBetween = DateUtil.getMonthBetween("202106", nowDate);
		List<PerInsuranceBillVo> allPerInsuranceList = Lists.newArrayList();
		for (Integer month : monthBetween) {
			List<PerInsuranceBillVo> perBillItems = perInsuranceBillService.getPerInsuranceBillByOrderNosAndMonth(Lists.newArrayList(noHaveSnapshotOrderNoSet), month);
			allPerInsuranceList.addAll(perBillItems);
		}
		Map<String, Integer> orderNoAndMaxReceiveMonthMap = allPerInsuranceList.stream().filter(item -> item.getReceivableMonth() != null && item.getOrderNo() != null)
				.collect(Collectors.groupingBy(PerInsuranceBillVo::getOrderNo, collectingAndThen(maxBy(Comparator.comparingInt(PerInsuranceBillVo::getReceivableMonth)), item -> item.get().getReceivableMonth())));

		/** 过滤一下 202106 - 到当前 完全没有收费过的员工 */
		noHaveSnapshotOrderNoSet = noHaveSnapshotOrderNoSet.stream().filter(orderNoAndMaxReceiveMonthMap::containsKey).collect(Collectors.toSet());

		Wrapper<OrderSnapshot> searchOrderSnapshotWrapper = new EntityWrapper<>();
		searchOrderSnapshotWrapper.in("order_no", noHaveSnapshotOrderNoSet).eq("del_flag", DelFlagEnum.N.getCode()).lt("bill_month", nowDate);
		List<OrderSnapshot> orderSnapshots = orderSnapshotMapper.selectList(searchOrderSnapshotWrapper);
		/** 过滤一下order_snapshot 里面有没有快照 如果 在最后一个收费月 有 要直接报错 ,因为 我们后期要数据进去,不能有相同的billMonth */
		List<String> maxReceiveMonthAndAndSnapShotEqualsOrderNos = orderSnapshots.stream()
				.filter(item -> orderNoAndMaxReceiveMonthMap.containsKey(item.getOrderNo()) && orderNoAndMaxReceiveMonthMap.get(item.getOrderNo()).equals(item.getBillMonth()))
				.map(OrderSnapshot::getOrderNo).collect(toList());

		if (CollectionUtils.isNotEmpty(maxReceiveMonthAndAndSnapShotEqualsOrderNos)) {
			StringBuilder sb = new StringBuilder("处理数据失败!订单在order_snapshot 表中有数据 orderNos:[ ");
			sb.append(maxReceiveMonthAndAndSnapShotEqualsOrderNos.toString()).append(" ]");
			throw new InsuranceBillDealException(sb.toString());
		}
		/** 获取 InsuranceCfgSnapshot 的数据 和 order_snapshot 的数据 */
		List<InsuranceCfgSnapshot> insuranceCfgSnapshotInsertList = Lists.newArrayList();
		List<OrderSnapshot> orderSnapshotInsertList = Lists.newArrayList();

		List<EmployeeOrderVo> employeeOrderByOrderNoList = iEmployeeOrderWrapperService.getEmployeeOrderByOrderNoList(Lists.newArrayList(noHaveSnapshotOrderNoSet));
		Map<String, EmployeeOrderVo> employeeOrderListByOrderNoMap = employeeOrderByOrderNoList.stream().collect(toMap(EmployeeOrderVo::getOrderNo, Function.identity(), (a, b) -> b));
		String creator = DateUtil.formatDateToString(new Date(), "yyyy-MM-dd ") + "快照修复数据";
		for (String orderNo : noHaveSnapshotOrderNoSet) {
			Integer billMonth = orderNoAndMaxReceiveMonthMap.get(orderNo);
			OrderSnapshot orderSnapshot = new OrderSnapshot()
					.setOrderNo(orderNo).setProdCryptCode("123456")
					.setCreator(creator).setDelFlag(DelFlagEnum.N.getCode())
					.setBillMonth(billMonth);
			if (orderInsuranceCfgVosByOrderNoMap.containsKey(orderNo)) {
				List<OrderInsuranceCfgVo> orderInsuranceCfgVos = orderInsuranceCfgVosByOrderNoMap.get(orderNo);
				for (OrderInsuranceCfgVo item : orderInsuranceCfgVos) {
					EmployeeOrderVo employeeOrderVo = employeeOrderListByOrderNoMap.get(orderNo);
					InsuranceCfgSnapshot insuranceCfgSnapshot = new InsuranceCfgSnapshot()
							.setOrderNo(orderNo)
							.setCustId(employeeOrderVo.getCustId()).setEmployeeId(employeeOrderVo.getEmployeeId())
							.setInsuranceRatioCode(item.getRatioCode()).setProdCode(item.getProductCode())
							.setRevStartMonth(item.getRevStartMonth()).setBillStartMonth(item.getBillStartMonth())
							.setExpiredMonth(item.getExpiredMonth()).setBillMonth(billMonth)
							.setTempletId(item.getTempletId()).setRevTempId(item.getRevTempId())
							.setReturnMonth(item.getReturnMonth()).setLastMonth(item.getLastMonth())
							.setRemark(item.getRemark()).setCreator(creator)
							.setCreateTime(new Date()).setDelFlag(DelFlagEnum.N.getCode())
							.setComBase(item.getComBase()).setIndBase(item.getIndBase());
//							.setComAmt(CalculateUtil.calculateAmt(item.getComBase(), item.getComRatio(), item.getComAdd(), item.getComCalcMode(), item.getComExactVal()))
//							.setIndAmt(CalculateUtil.calculateAmt(item.getIndBase(), item.getIndRatio(), item.getIndlAdd(), item.getIndCalcMode(), item.getIndExactVal()));
					CalculateArgs args = new CalculateArgs();
					args.setComArgs(item.getComBase(), item.getComRatio(), item.getComAdd(), item.getComCalcMode(), item.getComExactVal());
					args.setIndArgs(item.getIndBase(), item.getIndRatio(), item.getIndlAdd(), item.getIndCalcMode(), item.getIndExactVal())
							.setSpecialFlag(item.getSpecialFlag());
					CalculateUtil.calculateAmt(args);
					insuranceCfgSnapshot.setComAmt(args.getComAmt());
					insuranceCfgSnapshot.setIndAmt(args.getIndAmt());
					insuranceCfgSnapshotInsertList.add(insuranceCfgSnapshot);
				}
			}
			orderSnapshotInsertList.add(orderSnapshot);
		}
		if (insuranceCfgSnapshotInsertList.size() == orderSnapshotInsertList.size()) {
			if (orderSnapshotInsertList.size() > 0) {
				int insuranceCfgSnapshotNum = insuranceCfgSnapshotMapper.batchInsert(insuranceCfgSnapshotInsertList);
				int orderSnapshotNum = orderSnapshotMapper.batchInsert(orderSnapshotInsertList);
			}
		} else {
			Set<String> orderNoFromCfgSnapshot = insuranceCfgSnapshotInsertList.stream().map(InsuranceCfgSnapshot::getOrderNo).collect(toSet());
			orderSnapshotHaveCfgSnapshotNoHaveOrderNo = orderSnapshotInsertList.stream().map(OrderSnapshot::getOrderNo).collect(toSet()).stream().filter(item -> {
				return !orderNoFromCfgSnapshot.contains(item);
			}).collect(toSet());
		}

		log.info("有order_snapshot有数据,但是 cfg_snapshot 没有数据: {} ", orderSnapshotHaveCfgSnapshotNoHaveOrderNo.toString());
		log.info("被恢复快照的orderNoList: {} ", noHaveSnapshotOrderNoSet.toString());
		log.info("即没有产品也没有快照的orderNoList: {} ", noHaveSnapshotAndCfgOrderNoList.toString());
		log.info("------------------- 恢复快照结束 -------------------");
	}

    /**
     * 修复供应商成本数据  --> 修复完成后需要重新生成billCost
     */
	@Override
	public void updateSupplierCost(Integer billMonth) {
		log.info("---------------开始刷新{}月供应商成本数据-----------", billMonth);
		/** 获取表名  */
		String tableName = CommonConstants.PER_INSURSNCE_BILL_TABLE_PREFIX + billMonth;
		/** 根据表名获取所有小合同  根据类型  供应商*/
		List<PerInsuranceBillVo> perInsuranceBillVoList = perInsuranceBillMapper.getContractAreaNoByTableName(tableName, ContractAreaRecceivingType.SUPPLIER_COMPANY.getCode());
        List<String> quoteNoList = perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getQuoteNo).collect(toList());
        Map<String, BigDecimal> quoteNoAndHaveTaxFeeMap = iQuotationResourceWrapperService.getQuoNoAndHaveTaxFeeMapByQuoteNoList(quoteNoList);
		List<String> contractAreaNoList = perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getContractAreaNo).distinct().collect(toList());
		List<String> contractNoAndTempletIdList = perInsuranceBillVoList.stream().map(item -> item.getContractNo() + "#" + item.getTempletId()).distinct().collect(toList());
		/** 根据小合同 获取所有对应的供应商的价格 */

        /** contract_area */
        List<EmployeeContract> employeeContractList = contractWrapperService.getSupplierContractByContractAreaNoList(contractAreaNoList);
        /** 从逻辑上来讲,一个小合同对应的一个供应商的费用只有一条,所以 (a,b) ->b 应该永远用不到,但是以防万一 */
        Map<String, BigDecimal> contractAreaNoAndSupplierPriceMap = employeeContractList.stream().collect(Collectors.toMap(EmployeeContract::getContractAreaNo, em -> {
            if (em.getServicePrice() == null) return BigDecimal.ZERO;
            else return em.getServicePrice();
        }, (a, b) -> b));

        List<PerInsuranceBill> updateList = Lists.newArrayList();
        updateList = perInsuranceBillVoList.stream().map(item -> {
            PerInsuranceBill perInsuranceBill = new PerInsuranceBill();
            BeanUtils.copyProperties(item, perInsuranceBill);
            // 如果处在收费月内 且服务费 判断为true 且 为正数,则收取  如果为负数,则退款
            if (perInsuranceBill.getServiceFee() != null && !BigDecimalUtil.equalsZero(perInsuranceBill.getServiceFee()) &&
                    quoteNoAndHaveTaxFeeMap.get(perInsuranceBill.getQuoteNo()) != null &&
                    (perInsuranceBill.getServiceFee().compareTo(quoteNoAndHaveTaxFeeMap.get(perInsuranceBill.getQuoteNo())) == 0 ||
                            perInsuranceBill.getServiceFee().negate().compareTo(quoteNoAndHaveTaxFeeMap.get(perInsuranceBill.getQuoteNo())) == 0)) {
                BigDecimal supCost = contractAreaNoAndSupplierPriceMap.getOrDefault(perInsuranceBill.getContractAreaNo(), BigDecimal.ZERO);
                if (BigDecimalUtil.greaterZero(perInsuranceBill.getServiceFee()))
                    perInsuranceBill.setServicePrice(supCost);
                else
                    perInsuranceBill.setServicePrice(supCost.negate());
		}
            return perInsuranceBill;
        }).collect(Collectors.toList());

		/** 将价格插入 */
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<PerInsuranceBill>> partition = Lists.partition(updateList, 1000);
			partition.forEach(item -> {
                int updateSize = perInsuranceBillMapper.updateServicePriceById(item, tableName);
			});
		}
		for (String contractNoAndTempletIdStr : contractNoAndTempletIdList) {
			String[] split = contractNoAndTempletIdStr.split("#");
			String contractNo = split[0];
			Long templetId = Long.valueOf(split[1]);
			BillMainServiceCount serviceCount = perInsuranceBillService.ServiceCount(contractNo, templetId, billMonth);
			BigDecimal serviceFee = serviceCount.getSeviceFee() == null ? BigDecimal.ZERO : serviceCount.getSeviceFee();
			BigDecimal supplierCost = serviceCount.getSupplierCost() == null ? BigDecimal.ZERO : serviceCount.getSupplierCost();
			if (serviceFee.compareTo(BigDecimal.ZERO) == 0) {
				supplierCost = BigDecimal.ZERO;
			}
			insuranceBillMapper.updateSupplierCostByContractNoAndTempletIdAndBillMonth(contractNo, templetId, billMonth, supplierCost);
		}
		log.info("--------------- 刷新{}月供应商成本数据结束!!-----------", billMonth);
	}

	@Override
	public Page<BillLockOrUnLockVo> getContractTaskList(Map<String, Object> conditionMap, Set<String> approvalSet, Integer page, Integer limit) {
		Page<BillLockOrUnLockVo> pageResult = new Page<>(page, limit);
		pageResult.setRecords(billUnlockApprovalMapper.getContractTaskList(conditionMap, approvalSet, pageResult));
		return pageResult;
    }

    @Override
	public void updatePerInsuItemForServiceChange(List<String> orderNoList) {
		log.info("------ 开始导入服务费数据进入PerInsuranceItem -------------");
        orderSnapshotMapper.updateChargeCryptCodeNull(orderNoList, null);
        insuranceCfgSnapshotMapper.deleteByProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE, orderNoList, null);
		Integer nextYearMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.formatDateToString(new Date(), "yyyyMM")), 4);   // 当前月往后四个月
		List<Integer> monthBetween = DateUtil.getMonthBetween("201901", nextYearMonth.toString());
		Set<String> orderNoSet = Sets.newConcurrentHashSet();
		monthBetween = monthBetween.stream().sorted((a, b) -> b - a).collect(toList());
		/** 最后一个perInsuranceBill的数据 并且收费月是最大的 */
		Map<String, PerInsuranceBillVo> orderNoAndFirstPerInsuranceBillMap = Maps.newConcurrentMap();
		for (Integer serviceMonth : monthBetween) {
			/** 拿到所有订单编号 */
			String perInsuranceTableName = CommonConstants.PER_INSURSNCE_BILL_TABLE_PREFIX + serviceMonth;

			List<PerInsuranceBillVo> perInsuranceBillVoList = Lists.newArrayList();
			/** 获取报价单信息 */
			if (CollectionUtils.isEmpty(orderNoList)) {
				perInsuranceBillVoList = perInsuranceBillMapper.getDataByTableName(perInsuranceTableName, null);
			} else {
				perInsuranceBillVoList = perInsuranceBillMapper.getDataByTableName(perInsuranceTableName, orderNoList);
			}

			List<Long> perBillIdList = perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getId).collect(toList());
			/**过滤没有订单号的数据*/
			Map<String, PerInsuranceBillVo> orderNoAndPerInsuranceCacheMap = perInsuranceBillVoList.stream().filter(
							item -> {
								if (Optional.ofNullable(item.getServiceFee()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0) {
									if (orderNoAndFirstPerInsuranceBillMap.containsKey(item.getOrderNo()))
										return orderNoAndFirstPerInsuranceBillMap.get(item.getOrderNo()).getReceivableMonth() < item.getReceivableMonth();
									else
										return true;
								} else
									return false;
							})
                    .collect(toMap(item -> item.getOrderNo().trim(), Function.identity(), (a, b) -> {
				if (a.getReceivableMonth() > b.getReceivableMonth())
					return a;
				else
					return b;
			}));
			orderNoAndFirstPerInsuranceBillMap.putAll(orderNoAndPerInsuranceCacheMap);
			List<PerInsuranceBillItem> insertItemList = perInsuranceBillVoList.stream().map(item -> {
				PerInsuranceBillItem perInsuranceBillItem = new PerInsuranceBillItem();
				perInsuranceBillItem.setPerBillId(item.getId());
				perInsuranceBillItem.setProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE);
				perInsuranceBillItem.setInsuranceRatioCode(item.getQuotationNo());
				perInsuranceBillItem.setIndAmt(item.getServiceFee());
				perInsuranceBillItem.setTotal(item.getServiceFee());
				perInsuranceBillItem.setCreator("admin");
				return perInsuranceBillItem;
			}).collect(toList());

			orderNoSet.addAll(perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getOrderNo).collect(toSet()));

			String perInsuranceItemTableName = CommonConstants.PER_INSURANCE_BILL_ITEM_TABLE_PREFIX + serviceMonth;

			if (CollectionUtils.isNotEmpty(insertItemList)) {
				perInsuranceBillItemMapper.deleteByProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE, perInsuranceItemTableName, Sets.newHashSet(perBillIdList));
				Integer delCountOne = 4000;
				int batchOne = (insertItemList.size() + delCountOne - 1) / delCountOne;
				for (int i = 0; i < batchOne; i++) {
					Integer start = i * delCountOne;
					Integer end = (i == batchOne - 1 ? insertItemList.size() : i * delCountOne + delCountOne);
					log.info(serviceMonth + "月 perInsuranceBillItems: start: " + start + "end: " + end + " 测试 ");
					perInsuranceBillItemMapper.batchInsert(insertItemList.subList(start, end), perInsuranceItemTableName);
				}
			}
		}

		List<OrderSnapshot> orderSnapshotList = Lists.newArrayList();
		Map<String, OrderSnapshot> orderNoAndDataMap = Maps.newConcurrentMap();
		Map<String, CopyOnWriteArrayList<OrderServiceChargeVo>> orderNoAndOrderServiceChargeMap = Maps.newConcurrentMap();

		List<OrderServiceChargeVo> serviceChargeList = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(orderNoSet)) {
			long dealCapacity = 3000;  // 处理大小
			long dealCount = orderNoSet.size() / dealCapacity;
			long dealFrequency = orderNoSet.size() % dealCapacity == 0 ? dealCount : dealCount + 1;  // 处理次数
			/** 更具orderNoSet获取所有最大的那条orderSnapshot */
			List<String> strings = Lists.newArrayList(orderNoSet);
			for (long i = 0; i < dealFrequency; i++) {
				log.info("分页查询 page: " + i);
                int index = (i + 1) * dealCapacity > orderNoSet.size() ? orderNoSet.size() : (int) ((i + 1) * dealCapacity);
				List<String> list = strings.subList((int) (i * dealCapacity), index);
				Set<String> collect = new HashSet<>(list);
				List<OrderSnapshot> maxDateDataByOrderNoSet = orderSnapshotMapper.getMaxDateDataByOrderNoSet(collect);
				List<OrderServiceChargeVo> listByOrderNoSet = orderServiceChargeWrapperService.getListByOrderNoSet(collect);
				serviceChargeList.addAll(listByOrderNoSet);
				orderSnapshotList.addAll(maxDateDataByOrderNoSet);
			}

			orderNoAndDataMap = orderSnapshotList.stream().collect(toConcurrentMap(OrderSnapshot::getOrderNo, Function.identity()));

			Map<String, List<OrderServiceChargeVo>> orderNoAndOrderServiceChargeMap1 = serviceChargeList.stream().collect(groupingByConcurrent(OrderServiceChargeVo::getOrderNo));
            orderNoAndOrderServiceChargeMap1.forEach((k, v) -> {
				CopyOnWriteArrayList<OrderServiceChargeVo> orderServiceChargeVos = new CopyOnWriteArrayList<>(v);
                orderNoAndOrderServiceChargeMap.put(k, orderServiceChargeVos);
			});
		}

		Map<String, OrderSnapshot> finalOrderNoAndDataMap = orderNoAndDataMap;
		List<OrderSnapshot> updateOrderSnapshotList = Lists.newArrayList();
		/**使用并行流会有问题*/
		orderNoSet.stream().filter(finalOrderNoAndDataMap::containsKey).forEach(orderNo -> {
				OrderSnapshot orderSnapshot = finalOrderNoAndDataMap.get(orderNo);
				CopyOnWriteArrayList<OrderServiceChargeVo> orderServiceChargeVos = orderNoAndOrderServiceChargeMap.get(orderNo);
				String serviceChargeHashCode = getServiceChargeHashCode(orderNo, orderServiceChargeVos);
				orderSnapshot.setChargeCryptCode(serviceChargeHashCode);
				updateOrderSnapshotList.add(orderSnapshot);
		});


		List<InsuranceCfgSnapshot> insertInsuranceCfgSnapshotList = updateOrderSnapshotList.stream().filter(
				item -> orderNoAndFirstPerInsuranceBillMap.containsKey(item.getOrderNo()) && orderNoAndOrderServiceChargeMap.containsKey(item.getOrderNo()))
				.map(item -> {
			PerInsuranceBillVo perInsuranceBillVo = orderNoAndFirstPerInsuranceBillMap.get(item.getOrderNo());
			OrderServiceChargeVo orderServiceChargeVo = orderNoAndOrderServiceChargeMap.get(item.getOrderNo()).get(0);
			InsuranceCfgSnapshot insuranceCfgSnapshot = new InsuranceCfgSnapshot()
					.setCustId(perInsuranceBillVo.getCustId())
					.setEmployeeId(perInsuranceBillVo.getEmployeeId())
					.setOrderNo(perInsuranceBillVo.getOrderNo())
					.setInsuranceRatioCode(perInsuranceBillVo.getQuotationNo())
					.setRevStartMonth(orderServiceChargeVo.getRevStartMonth())
					.setBillStartMonth(orderServiceChargeVo.getBillStartMonth())
					.setExpiredMonth(orderServiceChargeVo.getRevEndMonth())
					.setBillMonth(item.getBillMonth())
					.setIndAmt(perInsuranceBillVo.getServiceFee())
					.setTempletId(perInsuranceBillVo.getTempletId())
					.setRevTempId(orderServiceChargeVo.getRevTempId())
					.setProdCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE)
					.setComBase(BigDecimal.ZERO).setIndBase(BigDecimal.ZERO).setComAmt(BigDecimal.ZERO)
					.setCreator(CommonConstants.ADMIN)
					.setCreateTime(new Date());
			return insuranceCfgSnapshot;
		}).collect(toList());

		 List<OrderSnapshot> updateList = Lists.newArrayList(updateOrderSnapshotList);
		if (CollectionUtils.isNotEmpty(updateList)) {
			Integer delCountTwo = 3000;
			int batchTwo = (updateList.size() + delCountTwo - 1) / delCountTwo;
			for (int i = 0; i < batchTwo; i++) {
				Integer start = i * delCountTwo;
				Integer end = (i == batchTwo - 1 ? updateList.size() : i * delCountTwo + delCountTwo);
                log.info("updateList: start: " + start + "end: " + end + " 测试 ");
				orderSnapshotMapper.updateList(updateList.subList(start, end));
			}
		}
		if (CollectionUtils.isNotEmpty(insertInsuranceCfgSnapshotList)) {
			Integer delCountThree = 3000;
			int batchThree = (insertInsuranceCfgSnapshotList.size() + delCountThree - 1) / delCountThree;
			for (int i = 0; i < batchThree; i++) {
				Integer start = i * delCountThree;
				Integer end = (i == batchThree - 1 ? insertInsuranceCfgSnapshotList.size() : i * delCountThree + delCountThree);
                log.info("月 insertInCfgSnpList: start: " + start + "end: " + end + " 测试 ");
				insuranceCfgSnapshotMapper.batchInsert(insertInsuranceCfgSnapshotList.subList(start, end));
			}
		}
		log.info("------  导入服务费数据进入PerInsuranceItem  结束-------------");
	}


	@Override
	public void updateCurrLockBillPerInsuItemForServiceChange(Integer serviceMonth) {
		log.info("------ 开始导入服务费数据进入PerInsuranceItem -------------");
		EntityWrapper<InsuranceBill> entityWrapper = new EntityWrapper<>();
        entityWrapper.eq("bill_month", serviceMonth);
		entityWrapper.eq("bill_type", com.reon.hr.api.bill.constant.BillEnum.billType.social_type.getCode());
		entityWrapper.eq("status", BillEnum.BillStatus.LOCK_STATUS.getCode());
		entityWrapper.lt("act_lock_time", "2023-07-01 00:00:00");
		entityWrapper.gt("employee_num", 0);
		entityWrapper.gt("receive_amt", 0);
		List<InsuranceBill> insuranceBills = insuranceBillMapper.selectList(entityWrapper);
        if (CollectionUtils.isNotEmpty(insuranceBills)) {
			String perInsuranceTableName = CommonConstants.PER_INSURSNCE_BILL_TABLE_PREFIX + serviceMonth;
			List<PerInsuranceBillVo> perInsuranceBillVoList = perInsuranceBillMapper.getPerInsuranceBillByBill(perInsuranceTableName, insuranceBills);
			/**存在订单*/
            if (CollectionUtils.isNotEmpty(perInsuranceBillVoList)) {
				List<String> orderNoList = perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getOrderNo).collect(toList());
                orderSnapshotMapper.updateChargeCryptCodeNull(orderNoList, serviceMonth);
                insuranceCfgSnapshotMapper.deleteByProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE, orderNoList, serviceMonth);
				Set<String> orderNoSet = Sets.newConcurrentHashSet();
				List<Long> perBillIdList = perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getId).collect(toList());
				Map<String, PerInsuranceBillVo> orderNoAndFirstPerInsuranceBillMap = perInsuranceBillVoList.stream()
                        .collect(toMap(item -> item.getOrderNo().trim(), Function.identity(), (a, b) -> {
							if (a.getReceivableMonth() > b.getReceivableMonth())
								return a;
							else
								return b;
						}));
				List<PerInsuranceBillItem> insertItemList = perInsuranceBillVoList.stream().map(item -> {
					PerInsuranceBillItem perInsuranceBillItem = new PerInsuranceBillItem();
					perInsuranceBillItem.setPerBillId(item.getId());
					perInsuranceBillItem.setProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE);
					perInsuranceBillItem.setInsuranceRatioCode(item.getQuotationNo());
					perInsuranceBillItem.setIndAmt(item.getServiceFee());
					perInsuranceBillItem.setTotal(item.getServiceFee());
					perInsuranceBillItem.setCreator("admin");
					return perInsuranceBillItem;
				}).collect(toList());
				orderNoSet.addAll(perInsuranceBillVoList.stream().map(PerInsuranceBillVo::getOrderNo).collect(toSet()));
				String perInsuranceItemTableName = CommonConstants.PER_INSURANCE_BILL_ITEM_TABLE_PREFIX + serviceMonth;
				if (CollectionUtils.isNotEmpty(insertItemList)) {
					perInsuranceBillItemMapper.deleteByProductCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE, perInsuranceItemTableName, Sets.newHashSet(perBillIdList));
					perInsuranceBillItemMapper.batchInsert(insertItemList, perInsuranceItemTableName);
				}

				List<OrderSnapshot> orderSnapshotList = Lists.newArrayList();
				Map<String, OrderSnapshot> orderNoAndDataMap = Maps.newConcurrentMap();
				Map<String, List<OrderServiceChargeVo>> orderNoAndOrderServiceChargeMap = Maps.newConcurrentMap();
				List<OrderServiceChargeVo> serviceChargeList = Lists.newArrayList();
				if (CollectionUtils.isNotEmpty(orderNoSet)) {
					List<OrderServiceChargeVo> listByOrderNoSet = orderServiceChargeWrapperService.getListByOrderNoSet(orderNoSet);
                    List<OrderSnapshot> maxDateDataByOrderNoSet = orderSnapshotMapper.getOrderSnapshotsDataByBillMonthAndOrderNo(orderNoSet, serviceMonth);
					serviceChargeList.addAll(listByOrderNoSet);
					orderSnapshotList.addAll(maxDateDataByOrderNoSet);
					orderNoAndDataMap = orderSnapshotList.stream().collect(toConcurrentMap(OrderSnapshot::getOrderNo, Function.identity()));
					orderNoAndOrderServiceChargeMap = serviceChargeList.stream().collect(groupingByConcurrent(OrderServiceChargeVo::getOrderNo));

				}

				Map<String, OrderSnapshot> finalOrderNoAndDataMap = orderNoAndDataMap;
				List<OrderSnapshot> updateOrderSnapshotList = Lists.newArrayList();
				/**使用并行流会有问题*/
				Map<String, List<OrderServiceChargeVo>> finalOrderNoAndOrderServiceChargeMap = orderNoAndOrderServiceChargeMap;
				orderNoSet.stream().filter(finalOrderNoAndDataMap::containsKey).forEach(orderNo -> {
					OrderSnapshot orderSnapshot = finalOrderNoAndDataMap.get(orderNo);
					List<OrderServiceChargeVo> orderServiceChargeVos = finalOrderNoAndOrderServiceChargeMap.get(orderNo);
					String serviceChargeHashCode = getServiceChargeHashCode(orderNo, orderServiceChargeVos);
					orderSnapshot.setChargeCryptCode(serviceChargeHashCode);
					updateOrderSnapshotList.add(orderSnapshot);
				});

				List<InsuranceCfgSnapshot> insertInsuranceCfgSnapshotList = updateOrderSnapshotList.stream().filter(
								item -> orderNoAndFirstPerInsuranceBillMap.containsKey(item.getOrderNo()) && finalOrderNoAndOrderServiceChargeMap.containsKey(item.getOrderNo()))
						.map(item -> {
							PerInsuranceBillVo perInsuranceBillVo = orderNoAndFirstPerInsuranceBillMap.get(item.getOrderNo());
							OrderServiceChargeVo orderServiceChargeVo = finalOrderNoAndOrderServiceChargeMap.get(item.getOrderNo()).get(0);
							InsuranceCfgSnapshot insuranceCfgSnapshot = new InsuranceCfgSnapshot()
									.setCustId(perInsuranceBillVo.getCustId())
									.setEmployeeId(perInsuranceBillVo.getEmployeeId())
									.setOrderNo(perInsuranceBillVo.getOrderNo())
									.setInsuranceRatioCode(perInsuranceBillVo.getQuotationNo())
									.setRevStartMonth(orderServiceChargeVo.getRevStartMonth())
									.setBillStartMonth(orderServiceChargeVo.getBillStartMonth())
									.setExpiredMonth(orderServiceChargeVo.getRevEndMonth())
									.setBillMonth(item.getBillMonth())
									.setIndAmt(perInsuranceBillVo.getServiceFee())
									.setTempletId(perInsuranceBillVo.getTempletId())
									.setRevTempId(orderServiceChargeVo.getRevTempId())
									.setProdCode(CommonConstants.SERVICE_FEE_PRODUCT_CODE)
									.setComBase(BigDecimal.ZERO).setIndBase(BigDecimal.ZERO).setComAmt(BigDecimal.ZERO)
									.setCreator(CommonConstants.ADMIN)
									.setCreateTime(new Date());
							return insuranceCfgSnapshot;
						}).collect(toList());

				if (CollectionUtils.isNotEmpty(updateOrderSnapshotList)) {
					orderSnapshotMapper.updateList(updateOrderSnapshotList);
				}
				if (CollectionUtils.isNotEmpty(insertInsuranceCfgSnapshotList)) {
					insuranceCfgSnapshotMapper.batchInsert(insertInsuranceCfgSnapshotList);
				}
			}

			log.info("------  导入服务费数据进入PerInsuranceItem  结束-------------");

		}

	}


	/**
	 * 将beforeServiceMonth 中服务费数据的最小起始月 同步到 order_service_charge  和  insurance_cfg_snapshot 表
     *
	 * @param orderNos
	 */
	@Override
	public void handleSyncServiceChangeStartMonth(List<String> orderNos) {
		log.info("=============================同步数据start==========================");
		Map<String, Integer> beforeServiceMonthMap = beforeOrderServiceMonthService.queryBeforeServiceMonthsServiceFeePart(orderNos);
		EntityWrapper<InsuranceCfgSnapshot> entityWrapper = new EntityWrapper<>();
		/*** 29797694 现在生产上最大快照id*/
        entityWrapper.gt("id", 29797694);
        entityWrapper.eq("prod_code", -1);
		/**只处理当前月当前的数据*/
		Integer preMonth =  com.reon.hr.api.customer.utils.DateUtil.getYearMonthByCount(DateUtil.getCurrentYearMonth(), -1);
        entityWrapper.le("bill_month", preMonth);
        if (CollectionUtils.isNotEmpty(orderNos)) {
            entityWrapper.in("order_no", orderNos);
		}
		List<InsuranceCfgSnapshot> insuranceCfgSnapshots = insuranceCfgSnapshotMapper.selectList(entityWrapper);
		Map<String, Integer> snapShotMap = Maps.newHashMap();
		for (InsuranceCfgSnapshot chargeVo : insuranceCfgSnapshots) {
			getMinMonth(snapShotMap, chargeVo.getOrderNo(), chargeVo.getRevStartMonth());
		}
		//查询服务费表
		//记录服务费中的最小开始月
		List<OrderServiceChargeVo> orderServiceChargeVos = orderServiceChargeWrapperService.getListByOrderNos(orderNos);
		Map<String, Long> serviceChargeIdMap = orderServiceChargeVos.stream().collect(toMap(vo -> vo.getOrderNo() + '-' + vo.getRevStartMonth(), OrderServiceChargeVo::getId));
		Map<String, Integer> serviceChargeMap = Maps.newHashMap();
		for (OrderServiceChargeVo chargeVo : orderServiceChargeVos) {
			getMinMonth(serviceChargeMap, chargeVo.getOrderNo(), chargeVo.getRevStartMonth());
		}
        if (CollectionUtils.isEmpty(orderNos)) {
			orderNos = Lists.newArrayList(beforeServiceMonthMap.keySet());
		}
		List<OrderServiceChargeVo> chargeEdits = Lists.newArrayList();
		List<InsuranceCfgSnapshot> snapShotEdits = Lists.newArrayList();
		for (String orderNo : orderNos) {
			Integer minMonthForServiceMonth = beforeServiceMonthMap.get(orderNo);
			Integer minMonthForSnapshot = snapShotMap.get(orderNo);
			Integer minMonthForServiceCharge = serviceChargeMap.get(orderNo);
            if (Objects.nonNull(minMonthForServiceMonth)) {
                if (Objects.nonNull(minMonthForSnapshot) && !minMonthForServiceMonth.equals(minMonthForSnapshot)) {
					InsuranceCfgSnapshot vo = new InsuranceCfgSnapshot();
					vo.setOrderNo(orderNo);
					vo.setProdCode(-1);
					vo.setRevStartMonth(minMonthForServiceMonth);
					snapShotEdits.add(vo);
				}
                if (Objects.nonNull(minMonthForServiceCharge) && !minMonthForServiceMonth.equals(minMonthForServiceCharge)) {
					Long id = serviceChargeIdMap.get(orderNo + "-" + minMonthForServiceCharge);
					OrderServiceChargeVo vo = new OrderServiceChargeVo();
					vo.setId(id);
					vo.setRevStartMonth(minMonthForServiceMonth);
					chargeEdits.add(vo);
				}
			}
		}
        if (CollectionUtils.isNotEmpty(snapShotEdits)) {
			insuranceCfgSnapshotMapper.updateStartMonthByOrderNo(snapShotEdits);
		}
        if (CollectionUtils.isNotEmpty(chargeEdits)) {
			orderServiceChargeWrapperService.updateOrderServiceChargeStartMonth(chargeEdits);
		}
		log.info("=============================同步数据end==========================");
	}

    private void getMinMonth(Map<String, Integer> serviceChargeMap, String orderNo, Integer revStartMonth) {
		if (serviceChargeMap.containsKey(orderNo)) {
			Integer startMonth = serviceChargeMap.get(orderNo);
            if (startMonth > revStartMonth) {
				serviceChargeMap.put(orderNo, revStartMonth);
			}
		} else {
			serviceChargeMap.put(orderNo, revStartMonth);
		}
	}

	private String getServiceChargeHashCode(String orderNo, CopyOnWriteArrayList<OrderServiceChargeVo> orderServiceChargeVos) {
		StringBuilder prodSb = new StringBuilder(orderNo);
		for (OrderServiceChargeVo item : orderServiceChargeVos) {
			prodSb.append(item.getQuotationNo())
					.append(item.getRevStartMonth()).append(item.getRevEndMonth()).append(item.getBillStartMonth())
					.append(item.getTempletId()).append(item.getRevTempId())
					.append(item.getAmount()).append(item.getTaxfreeAmt()).append(item.getValTaxRate()).append(item.getAddRate()).append(item.getValTax());
		}
		return EnCryptUtil.getEnCryptCode(prodSb.toString());
	}

	private String getServiceChargeHashCode(String orderNo, List<OrderServiceChargeVo> orderServiceChargeVos) {
		StringBuilder prodSb = new StringBuilder(orderNo);
		for (OrderServiceChargeVo item : orderServiceChargeVos) {
			prodSb.append(item.getQuotationNo())
					.append(item.getRevStartMonth()).append(item.getRevEndMonth()).append(item.getBillStartMonth())
					.append(item.getTempletId()).append(item.getRevTempId())
					.append(item.getAmount()).append(item.getTaxfreeAmt()).append(item.getValTaxRate()).append(item.getAddRate()).append(item.getValTax());
		}
		return EnCryptUtil.getEnCryptCode(prodSb.toString());
	}


    /**
     * 只做新增,不做删除,而且只新增Cache数据
     */
    public String updateBeforeServiceMonthLoseMonth(Integer billMonth, String contractNo, Long templetId, List<String> orderNoSet) {
        if (billMonth == null) return "账单月必填";
        log.info("-------------------------- 开始修复ServiceMonthCacheMonth ----------------------------");
        List<PerInsuranceBillAndPerInsuranceBillItemDto> productCodeAndReceiveMonthList = Lists.newArrayList();

            productCodeAndReceiveMonthList = perInsuranceBillItemMapper.getProductCodeAndReceiveMonthByOrderNoListAndContractNoAndTempletId(
                    orderNoSet, contractNo, templetId,
                    PerInsuranceBillEnum.PER_INSURSNCE_BILL_TABLE_PREFIX.getName() + billMonth,
                    PerInsuranceBillEnum.PER_INSURANCE_BILL_ITEM_TABLE_PREFIX.getName() + billMonth, billMonth);

        orderNoSet = productCodeAndReceiveMonthList.stream().map(PerInsuranceBillAndPerInsuranceBillItemDto::getOrderNo).distinct().collect(toList());

        List<Integer> chargeFreqList = Lists.newArrayList(InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode(), InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode());
        HashSet<String> yearInsuranceCodeSet = iInsuranceRatioWrapperService.getAllInsuranceCodeByChargeFreq(chargeFreqList);
        Wrapper<BeforeOrderServiceMonth> resultWrapper = new EntityWrapper<>();
        resultWrapper.in("order_no", orderNoSet);
        List<BeforeOrderServiceMonth> beforeOrderServiceMonths = beforeOrderServiceMonthMapper.selectList(resultWrapper);



        Map<String, Map<Integer, Set<Integer>>> orderNoProductCodeAndReceiveMonthMap = productCodeAndReceiveMonthList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getOrderNo()) && item.getProductCode() != null
                        && (StringUtils.isNotBlank(item.getInsuranceRatioCode()) || item.getProductCode().equals(CommonConstants.SERVICE_FEE_PRODUCT_CODE)) && !yearInsuranceCodeSet.contains(item.getInsuranceRatioCode()))
                .collect(groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getOrderNo,
                        groupingBy(PerInsuranceBillAndPerInsuranceBillItemDto::getProductCode, mapping(PerInsuranceBillAndPerInsuranceBillItemDto::getReceivableMonth, toSet()))));

        HashBasedTable<String, Integer, BeforeOrderServiceMonth> orderNoAndProductCodeAndServiceMonthCacheMap = Optional.ofNullable(beforeOrderServiceMonths).orElseGet(Lists::newArrayList).stream().map(item -> ImmutableTable.of(item.getOrderNo(), item.getProdCode(), item)).collect(HashBasedTable::create, HashBasedTable::putAll, HashBasedTable::putAll);
        List<BeforeOrderServiceMonth> insertList = Lists.newArrayList();
        for (String orderNo : orderNoProductCodeAndReceiveMonthMap.keySet()) {
            for (Integer productCode : orderNoProductCodeAndReceiveMonthMap.getOrDefault(orderNo,Maps.newHashMap()).keySet()) {
                BeforeOrderServiceMonth beforeOrderServiceMonth = orderNoAndProductCodeAndServiceMonthCacheMap.get(orderNo, productCode);
                Set<Integer> receiveMonthSet = Optional.ofNullable(orderNoProductCodeAndReceiveMonthMap.getOrDefault(orderNo, Maps.newHashMap()).get(productCode)).orElseGet(Sets::newHashSet);  // perInsuranceBill表中的 receiveMothSet
                if (beforeOrderServiceMonth != null) {
                    Map<Integer, Set<Integer>> billMonthAndServiceMonthMap = Maps.newHashMap();   // before表里面的数据 这里面的数据只做增加不做删除
                    if (StringUtils.isNotBlank(beforeOrderServiceMonth.getServiceMonthCache()))
                        billMonthAndServiceMonthMap = com.reon.hr.api.bill.utils.JsonUtil.jsonToMapSet(beforeOrderServiceMonth.getServiceMonthCache());
                    if (billMonthAndServiceMonthMap.containsKey(billMonth))  // 有就加入,因为是Set不会重复
                    {
                        if (CollectionUtils.isNotEmpty(receiveMonthSet))
                            billMonthAndServiceMonthMap.get(billMonth).addAll(receiveMonthSet);
                    } else {  // 没有新增
                        if (CollectionUtils.isNotEmpty(receiveMonthSet))
                            billMonthAndServiceMonthMap.put(billMonth, receiveMonthSet);
                    }
                    beforeOrderServiceMonth.setServiceMonthCache(com.reon.hr.api.bill.utils.JsonUtil.beanToJson(billMonthAndServiceMonthMap));
                } else {   /** 如果before表中没有 就插入 */
                    if (CollectionUtils.isNotEmpty(receiveMonthSet)) {
                        BeforeOrderServiceMonth insertBOSM = new BeforeOrderServiceMonth();
                        String emptyMap = com.reon.hr.api.bill.utils.JsonUtil.beanToJson(Maps.newHashMap());
                        insertBOSM.setOrderNo(orderNo).setProdCode(productCode).setServiceMonth(emptyMap)
                                .setYearServiceMonth(emptyMap).setYearServiceMonthCache(emptyMap).setCreator("admin")
                                .setNoDealMonth(com.reon.hr.api.bill.utils.JsonUtil.beanToJson(Lists.newArrayList()));
                        Map<Integer, Set<Integer>> billMonthAndServiceMonthMap = Maps.newHashMap();
                        billMonthAndServiceMonthMap.put(billMonth, receiveMonthSet);
                        insertBOSM.setServiceMonthCache(com.reon.hr.api.bill.utils.JsonUtil.beanToJson(billMonthAndServiceMonthMap));
                        insertList.add(insertBOSM);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(beforeOrderServiceMonths)) {
            List<List<BeforeOrderServiceMonth>> partition = Lists.partition(beforeOrderServiceMonths, 1000);
            partition.forEach(item -> {
                int size = beforeOrderServiceMonthMapper.updateServiceMonthCacheByIdList(item);
            });
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<BeforeOrderServiceMonth>> partition = Lists.partition(insertList, 1000);
            for (List<BeforeOrderServiceMonth> orderServiceMonths : partition) {
                beforeOrderServiceMonthMapper.insertList(orderServiceMonths);
            }
        }

        log.info("--------------------------  修复ServiceMonthCacheMonth结束 ----------------------------");
        return "成功";
    }

}
