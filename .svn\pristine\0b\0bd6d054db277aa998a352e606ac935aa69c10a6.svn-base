package com.reon.hr.api.customer.dubbo.service.rpc.employee;


import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.importData.AddEmployeeContractImportDto;
import com.reon.hr.api.customer.dto.importData.AddTransferEmpContractImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dto.qys.QysMappingFieldDto;
import com.reon.hr.api.customer.vo.EmpContractLogVo;
import com.reon.hr.api.customer.vo.EmployeeContractVo;
import com.reon.hr.api.customer.vo.employee.EmployeeContractOperationLogVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderContractFileVo;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;

public interface IEmployeeContractWrapperService {
	static String CONTRACT_AREA_NO="小合同编号";
	static String EMPLOYEE_NO="唯一号";
	static String FORMAL_SALARY="正式工资";
	static String CALL_FLAG="是否外呼";
	static String SIGN_DATE="签署日期";
	static String WORK_METHOD="工作制";
	static String EMP_CONTRACT_TYPE="合同类别";
	static String DISPATCH_START="派遣期限起";
	static String DISPATCH_END="派遣期限止";
	static String START_DATE="劳动合同起始时间";
	static String END_DATE="劳动合同结束时间";
	static String PROBATION_FLAG="是否有试用期";
	static String PROBA_START="试用期起始时间";
	static String PROBA_END="试用期结束时间";
	static String PROBA_SALARY="试用工资";
	static String TEMP_TYPE="合同版本";
	static String SIGN_PLACE="合同签定地";
	static String TEMP_PLACE="合同版本地";
	static String PRINCIPLE="合同原则";
	static String REMARK="备注";


	static final ForkJoinPool PRI_FORK_JOIN_POOL = new ForkJoinPool (1);
	Integer saveEmployeeContract(EmployeeContractVo employeeContractVo);

	Integer editEmployeeContract(EmployeeContractVo employeeContractVo);

	/**
	 * 更新文件id
	 * @param records
	 */
	void updateFileIdByOrderNo(List<EmployeeContractVo> records,String loginName);
	void updateFileIdByOrderNo(EmployeeContractVo records);

	boolean getByOrderNoList(List<String> orderNos);

	int getPerContractCountByEmpId(List<String> certNoList);

	int getPerContractCountByEmpIdAndContractNo(Long empId,String contractNo);

	Page<EmployeeContractVo> getEmployeeContractPage(Integer page, Integer limit, EmployeeContractVo employeeContractVo);
	Page<EmployeeContractVo> getEmployeeContractPageForEhr(Integer page, Integer limit, EmployeeContractVo employeeContractVo);

	EmployeeContractVo getDataByContractNo(String empContractNo);

	Integer delEmployeeContractByNo(List delNoList);
	Integer updateSignStatustByNo(List updateNoList);

	Integer commitEmployeeContractByBatchNo(List<String> commitNoList);

	Integer stopEmployeeContract(EmployeeContractVo employeeContractVo);

	Page<EmpContractLogVo> getEmployeeContractLogPage(Integer page, Integer limit, EmpContractLogVo empContractLogVo);

	List<EmployeeContractVo> getEmployeeContractList(EmployeeContractVo employeeContractList);

	Integer batchSave(ArrayList<EmployeeContractVo> employeeContractVoList);
/**
 * 根据员工合同终止月查询所有过期员工合同
 * */
	List<EmployeeContractVo> getExpiredEmployeeContractList();

	/**
	 * 根据员工合同终止月查询之前一个月的合同
	 * */
	List<EmployeeContractVo> getExpiredMonthLaterEmployeeContractList();


	List<String> getAllOrderNo();

	Long getEmpIdByEmpNo(String empNo);
	Page<EmployeeOrderVo> getContractDataByCertNo(Integer limit, Integer page, EmployeeOrderVo employeeOrderVo);

	void batchAddEmployeeContractImport(ImportDataDto<AddEmployeeContractImportDto> importDataDto);


	List<String> batchImportTransferEmpContractInfo(ImportDataDto<AddTransferEmpContractImportDto> importDataDto);

	Page<EmployeeContractVo> getEmployeeContractAllPage(Integer page, Integer limit, EmployeeContractVo employeeContractVo) throws ParseException;

	List<EmployeeContractVo> getEmployeeContractAllList(EmployeeContractVo employeeContractVo);

	int getCountByFileId(String orderNo, String fileId);

	EmployeeContractOperationLogVo getEmployeeContractOperationLogByEmpContractNo(String empContractNo);

    List<EmployeeContractVo> getNeedUploadFileList();

	int deleteFileId(String fileId,String loginName);


	/**
	 * 将申报转移前订单的文件id 添加到转移后
	 * @param orderNos
	 */
	void updateEmployeeContractByTransfer(List<String> orderNos);


	void reSignEmployeeContract(EmployeeContractVo employeeContractVo);

	int insertAgentContractFile(OrderContractFileVo orderContractFileVo);

	List<OrderContractFileVo> getFileByEmpId(Long empId,Integer fileType);
	List<OrderContractFileVo> getFileByOrderNo(String orderNo,Integer fileType);

    QysMappingFieldDto getQysMappingFieldDtoData(String orderNo, String orgCode, Map<String, Object> renewConditionMap);

	List<OrderContractFileVo> getPersonalContractFileByEmpId(Long empId,Integer fileType,String contractNo);

	void deleteTransferEmpContractNo();



}
