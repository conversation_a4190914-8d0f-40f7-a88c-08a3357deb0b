package com.reon.hr.api.bill.enums;

import lombok.Getter;

/**
 * 开票状态枚举
 *
 * <AUTHOR>
 * @date 2023/02/15
 */
@Getter
public enum InvoiceStatusEnum {
    UNINVOICE(1,"未开票"),
    INVOICE_PART(2,"部分开票"),
    INVOICE_FULL(3, "全部开票");
    private int code;

    private String msg;

    InvoiceStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code){
        String msg = null;
        if (code != null){
            for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
                if (invoiceStatusEnum.getCode() == code){
                    msg = invoiceStatusEnum.getMsg();
                }
            }
        }
        return msg;
    }
}
