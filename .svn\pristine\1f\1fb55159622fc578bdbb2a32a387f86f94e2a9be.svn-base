def releaseTime() {
    return new Date().format("yyyyMMddHHmmss", TimeZone.getTimeZone("GMT+8"))
}

configure(subprojects) {
    apply plugin: 'java'
    apply plugin: 'eclipse'
    apply plugin: 'eclipse-wtp'
    apply plugin: 'idea'
    //apply plugin: 'jdepend'
//    apply plugin: 'checkstyle'
//    apply plugin: 'findbugs'
//
//    //暂时先不启用
// 	checkstyle {
//   		toolVersion = '8.10'
//	}
//
//	task checkstyle(type: Checkstyle) {
//	    ignoreFailures false
//	    showViolations true
//	    source 'src'
//	    include '**/*.java'
//	    exclude '**/gen/**', '**/test/**', '**/build/**'
//
//	    classpath = files()
//	}
//
//   tasks.withType(FindBugs) {
//       reports {
//           xml.enabled = false
//           html.enabled = true
//       }
//       reportLevel = 'high'
//   }
//   tasks.withType(Checkstyle) {
//       reports {
//           xml.enabled = false
//           html.enabled = true
//       }
//   }
    
    version = '1.0.1-SNAPSHOT'
    // JVM \u7248\u672c
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
    //java \u7f16\u8bd1\u5b57\u7b26\u96c6
    [compileJava, compileTestJava]*.options*.encoding = 'UTF-8'


    repositories {

        maven { url "https://maven.aliyun.com/repository/public" }
        mavenCentral()
        jcenter()
    }

    ext {
        springVersion = '5.3.20'
        resteasyVersion = '3.0.16.Final'
        cxfrtVersion = '2.6.1'
        tomcatEmbedVersion = '8.5.6'
        jacksonVersion = '2.9.10'
        mybatisplusVersion = '2.3'
        druidVersion = '1.1.10'
        log4jVersion = '2.8.2'
    }

    dependencies {
        testImplementation ("junit:junit:4.12",
                "org.hamcrest:hamcrest-all:1.3",
                "org.springframework:spring-test:$springVersion",
                "com.h2database:h2:1.4.197",
                "org.mockito:mockito-all:1.10.19")

        // Spring依赖
        implementation("org.springframework:spring-aop:$springVersion",
                "org.springframework:spring-orm:$springVersion",
                "org.springframework:spring-jdbc:$springVersion",
                //"org.springframework:spring-instrument:$springVersion",
                //"org.springframework:spring-expression:4.3.3.RELEASE",
                "org.springframework:spring-core:$springVersion",
                "org.springframework:spring-context-support:$springVersion",
                "org.springframework:spring-context:$springVersion",
                "org.springframework:spring-beans:$springVersion",
                "org.springframework:spring-aspects:$springVersion",
                "org.springframework:spring-tx:$springVersion",
                "org.springframework:spring-web:$springVersion",
                "org.springframework:spring-webmvc:$springVersion"
        )

        // 数据库相关
        implementation("mysql:mysql-connector-java:6.0.6",
                "com.alibaba:druid:$druidVersion"
        )

        implementation 'net.sf.json-lib:json-lib:2.4:jdk15'


        // mybtais-plus
        implementation("com.baomidou:mybatis-plus:$mybatisplusVersion",
                "org.apache.velocity:velocity:1.7",
                "org.mybatis.generator:mybatis-generator-core:1.3.5"
        )
        
        // quartz
        implementation ("org.quartz-scheduler:quartz:2.3.0",
                "org.quartz-scheduler:quartz-jobs:2.3.0",
        )

        // elastic-job-lite
        implementation ("com.dangdang:elastic-job-lite-core:2.1.5",
                "com.dangdang:elastic-job-lite-spring:2.1.5"
        )

        // json 处理
        implementation("com.alibaba:fastjson:1.2.83",
                "com.google.code.gson:gson:2.8.9",
                "com.fasterxml.jackson.core:jackson-databind:2.9.10.8",
                "com.fasterxml.jackson.core:jackson-core:${jacksonVersion}",
                "com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}",
                "com.fasterxml.jackson.module:jackson-module-jaxb-annotations:${jacksonVersion}"
        )

        // 日志相关
        implementation("org.slf4j:slf4j-api:1.7.25",
                "org.apache.logging.log4j:log4j-api:${log4jVersion}",
                "org.apache.logging.log4j:log4j-core:${log4jVersion}",
                "org.apache.logging.log4j:log4j-slf4j-impl:${log4jVersion}",
                "org.apache.logging.log4j:log4j-jcl:${log4jVersion}",
                "org.apache.logging.log4j:log4j-web:${log4jVersion}"
        )

        // 其他
        implementation("org.apache.commons:commons-lang3:3.7",
                "com.google.guava:guava:25.1-jre",
                "commons-codec:commons-codec:1.11"
        )

        implementation("org.apache.dubbo:dubbo:3.1.11",
                "org.apache.curator:curator-framework:2.12.0",
                "io.netty:netty-all:4.1.87.Final",
                "org.apache.curator:curator-x-discovery-server:2.9.1",
                "com.google.protobuf:protobuf-java:3.23.4"
        )
		
        implementation('org.apache.zookeeper:zookeeper:3.4.8') {
            exclude group: 'org.slf4j'
        }
        // https://mvnrepository.com/artifact/org.apache.commons/commons-text
		implementation('org.apache.commons:commons-text:1.4')

        //lombbok
        compileOnly 'org.projectlombok:lombok:1.18.26'
        annotationProcessor 'org.projectlombok:lombok:1.18.26'

        implementation group: 'org.jasypt', name: 'jasypt', version: '1.9.2'
        implementation group: 'org.jasypt', name: 'jasypt-spring31', version: '1.9.2'

    }


    configurations.all {


        // 当遇到版本冲突时直接构建失败
        //resolutionStrategy.failOnVersionConflict()

        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        all*.exclude group: 'log4j', module: 'log4j'
        all*.exclude group: 'org.slf4j', module: 'slf4j-log4j12'

    }

}
