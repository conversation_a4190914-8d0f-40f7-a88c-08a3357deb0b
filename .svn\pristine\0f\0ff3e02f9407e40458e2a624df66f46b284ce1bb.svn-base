package com.reon.ehr.sp.sys.domain.entity;

import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.vo.MailSendVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MailSendBuilder {
    private static final String EHR_SUBJECT = "【系统账号激活】定e达账号开通";
    /*private static String EHR_CONTENT =
            "尊敬的 [custName]，\n" +
            "\n" +
            "您好！\n" +
            "\n" +
            "感谢您选择我们的系统服务。为了方便您使用我们的系统，我们已为您成功开通了系统账号。以下是您的登录信息，请妥善保管：\n" +
            "\n" +
            "- **用户名**：[username]\n" +
            "- **初始密码**：[password]\n" +
            "\n" +
            "请注意，为了您的账户安全，我们建议您在首次登录后立即更改初始密码。您可以按照以下步骤操作：\n" +
            "\n" +
            "1. 访问系统登录页面：https://ehr.reonhr.com\n" +
            "2. 输入您的用户名和初始密码进行登录。\n" +
            "3. 登录后，进入“个人中心”页面，根据提示更改密码。\n" +
            "\n" +
            "如有任何问题或需要进一步的帮助，请随时联系我们\n" +
            "祝您使用愉快！\n" +
            "\n" +
            "定山信息技术有限公司\n" +
            "[date]\n";*/
    private static String EHR_CONTENT =
            "<body>\n" +
            "    <p>尊敬的 [custName]，</p>\n" +
            "    <p>您好！</p>\n" +
            "    <p>感谢您选择我们的系统服务。为了方便您使用我们的系统，我们已为您成功开通了系统账号。以下是您的登录信息，请妥善保管：</p>\n" +
            "    <ul>\n" +
            "        <li><strong>用户名</strong>：[username]</li>\n" +
            "        <li><strong>初始密码</strong>：[password]</li>\n" +
            "    </ul>\n" +
            "    <p>请注意，为了您的账户安全，我们建议您在首次登录后立即更改初始密码。您可以按照以下步骤操作：</p>\n" +
            "    <ol>\n" +
            "        <li>访问系统登录页面：<a href=\"https://ehr.reonhr.com\">定e达</a></li>\n" +
            "        <li>输入您的用户名和初始密码进行登录。</li>\n" +
            "        <li>登录后，进入“个人中心”页面，根据提示更改密码。</li>\n" +
            "    </ol>\n" +
            "    <p>如有任何问题或需要进一步的帮助，请随时联系我们。</p>\n" +
            "    <p>祝您使用愉快！</p>\n" +
            "    <p>定山信息技术有限公司</p>\n" +
            "    <p>[date]</p>\n" +
            "</body>";

    /**
     * 构建reon客户端邮件发送对象
     *
     * @param mailUrl  邮件 URL
     * @param custName 客户名称
     * @param username 用户名
     * @param password 密码
     * @return {@link MailSendVo }
     */
    public static MailSendVo buildEhr(String mailUrl, String custName, String username, String password) {
        return MailSendVo.builder()
                .mailUrl(mailUrl)
                .mailContent(EHR_CONTENT.replace("[custName]", custName)
                        .replace("[username]", username)
                        .replace("[password]", password)
                        .replace("[date]", DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_YYYY_MM_DD)))
                .mailSubject(EHR_SUBJECT)
                .build();
    }


    /**
     * 构建她
     *
     * @param mailUrl     邮件 URL
     * @param sysUserList SYS 用户列表
     * @return {@link MailSendVo }
     */
    public static MailSendVo buildEhr(String mailUrl, List<SysUser> sysUserList) {
        StringBuilder emailBody = new StringBuilder();
        emailBody.append("<body>\n");
        emailBody.append("    <p>尊敬的管理员，</p>\n");
        emailBody.append("    <p>您好！</p>\n");
        emailBody.append("    <p>我们已为以下客户成功开通了系统账号。以下是他们的登录信息，请妥善保管：</p>\n");

        emailBody.append("    <table border=\"1\" cellspacing=\"0\" cellpadding=\"5\">\n");
        emailBody.append("        <thead>\n");
        emailBody.append("            <tr>\n");
        emailBody.append("                <th>客户名称</th>\n");
        emailBody.append("                <th>用户名</th>\n");
        emailBody.append("                <th>初始密码</th>\n");
        emailBody.append("            </tr>\n");
        emailBody.append("        </thead>\n");
        emailBody.append("        <tbody>\n");

        for (SysUser user : sysUserList) {
            emailBody.append("            <tr>\n");
            emailBody.append("                <td>").append(user.getCustName()).append("</td>\n");
            emailBody.append("                <td>").append(user.getUserName()).append("</td>\n");
            emailBody.append("                <td>").append(user.getPassword()).append("</td>\n");
            emailBody.append("            </tr>\n");
        }

        emailBody.append("        </tbody>\n");
        emailBody.append("    </table>\n");

        emailBody.append("    <p>请注意，为了账户安全，我们建议客户在首次登录后立即更改初始密码。他们可以按照以下步骤操作：</p>\n");
        emailBody.append("    <ol>\n");
        emailBody.append("        <li>访问系统登录页面：<a href=\"https://ehr.reonhr.com\">定e达</a></li>\n");
        emailBody.append("        <li>输入用户名和初始密码进行登录。</li>\n");
        emailBody.append("        <li>登录后，进入“个人中心”页面，根据提示更改密码。</li>\n");
        emailBody.append("    </ol>\n");

        emailBody.append("    <p>如有任何问题或需要进一步的帮助，请随时联系我们。</p>\n");
        emailBody.append("    <p>祝您工作顺利！</p>\n");
        emailBody.append("    <p>定山信息技术有限公司</p>\n");
        emailBody.append("    <p>").append(DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_YYYY_MM_DD)).append("</p>\n");
        emailBody.append("</body>");
        return MailSendVo.builder()
                .mailUrl(mailUrl)
                .mailContent(emailBody.toString())
                .mailSubject(EHR_SUBJECT)
                .build();
    }


    /**
     * 构建忘记密码通知邮件
     *
     * @param mailUrl     邮件 URL
     * @param customerName 客户名
     * @param username    用户名（客户的账号）
     * @return {@link MailSendVo}
     */
    public static MailSendVo buildForgetPasswordNotification(String mailUrl, String customerName, String username) {
        String subject = "【系统账号管理】忘记密码通知";
        String content = "<body>\n" +
                "    <p>尊敬的客服，</p>\n" +
                "    <p>您好！</p>\n" +
                "    <p>客户 <strong>" + customerName + "</strong> 的账号 <strong>" + username + "</strong> 已请求忘记密码重置。为了确保账户安全，请确认用户身份后联系共享中心重置密码。</p>\n" +
                "    <p>重置密码操作步骤如下：</p>\n" +
                "    <ol>\n" +
                "        <li>登录系统管理后台：<a href=\"https://ehr.reonhr.com\">定e达</a></li>\n" +
                "        <li>导航至用户管理页面。</li>\n" +
                "        <li>找到客户 <strong>" + customerName + "</strong> 的账号 <strong>" + username + "</strong> 并确认其身份。</li>\n" +
                "        <li>点击“重置密码”按钮，为用户生成新的密码。</li>\n" +
                "        <li>将新密码通过安全方式告知用户。</li>\n" +
                "    </ol>\n" +
                "    <p>如有任何问题或需要进一步的帮助，请随时联系我们。</p>\n" +
                "    <p>祝您工作顺利！</p>\n" +
                "    <p>定山信息技术有限公司</p>\n" +
                "    <p>" + DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_YYYY_MM_DD) + "</p>\n" +
                "</body>";

        return MailSendVo.builder()
                .mailUrl(mailUrl)
                .mailContent(content)
                .mailSubject(subject)
                .build();
    }
}
