package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.commInsurOrder.CommInsurOrderVo;
import com.reon.hr.api.customer.vo.customerDetail.CustomerSolutionPriceVo;
import com.reon.hr.api.customer.vo.customerDetail.CustomerSolutionVo;
import com.reon.hr.sp.customer.entity.cus.CustomerSolution;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface CustomerSolutionMapper extends BaseMapper<CustomerSolution> {
	List<CustomerSolutionVo> getByPage(Page page,@Param("cus") CustomerSolution customerSolution);

	List<CommInsurOrderVo> findCustomerSolutionPageList(Page page,@Param("param") String param);

	List<CustomerSolutionVo> getCustomerSolutionBySupplierId(@Param("supplierId") Long supplierId);

	int insertSolution(CustomerSolution customerSolutionVo);


	/**
	 * 根据方案编号查询方案*/
	//CustomerSolutionVo selectByPrimaryKey(String solutionNo);
	CustomerSolution selectByPrimaryKey(String solutionNo);
	CustomerSolution selectByPrimaryKey(@Param("solutionNo")String solutionNo,@Param("id")Long id);

	CustomerSolutionVo selectSoultionBysolutionNo (@Param("solutionNo") String solutionNo);

	int deleteByPrimaryKey(Long solutionNo);


	int updateByPrimaryKey(CustomerSolution customerSolution);

	int updateByPrimaryKeySelective(Long id);

	Integer insert(CustomerSolution customerSolution);

	List<CustomerSolutionVo> getAllSolutionName();

	/**
	 * 查询所有待生效的方案
	 * @return
	 */
	List<CustomerSolution> getAllCustomerSolutionUpgrade();

	/**
	 * 根据方案编号查询版本号最大的
	 * @param solutionNo
	 * @return
	 */
	CustomerSolutionVo maxVersion(@Param("solutionNo")String solutionNo);
	/**
	 * 根据方案编号查询供应商*/
	CustomerSolution getMaxSolution(@Param("solutionNo") String solutionNo);
	List<CustomerSolution> getAllSolution(@Param("solutionNo") String solutionNo);
	CustomerSolutionPriceVo getSolutionPrice(@Param("solutionId") Long solutionId);

	/**
	 * 根据方案编号查询方案状态为待生效有一条前台做效验
	 * @param solutionNo
	 * @return
	 */
	Long getMaxStatus(@Param("solutionNo") String solutionNo);

	int updateSolutionStatus(@Param("ids") List<Long> idList,@Param("updater") String updater);
	CustomerSolutionVo selectAllSoultionBysolutionNo(String solutionNo);

	List<CustomerSolutionVo> getAllSupplierId();

	List<CustomerSolutionVo> getAllSolutionNameByCustId(@Param("custId") Long custId);

	List<Long> getAllCommercePriceId();

	List<CustomerSolutionVo> selectBySolutionNoList(@Param("soluNoList") List<String> soluNoList);

	/** 根据soluNoList 没有限制条件的查询 */
	List<CustomerSolutionVo> selectAllBySolutionNoList(@Param("soluNoList") List<String> solutionNoList);

	List<Integer> getPayMethodByNo(@Param("solutionNos")List<String> solutionNos);



}
