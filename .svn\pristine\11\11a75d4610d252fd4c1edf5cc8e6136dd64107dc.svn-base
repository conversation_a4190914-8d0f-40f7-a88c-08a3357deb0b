var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).extend({dtree: '{/}../../layui_ext/dtree/dtree'})
    .use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect', 'dtree'], function () {
        var table = layui.table,
            $ = layui.$,
            form = layui.form,
            laydate = layui.laydate,
            tableSelect = layui.tableSelect,
            layer = layui.layer,
            dtree = layui.dtree,
            layer = parent.layer === undefined ? layui.layer : parent.layer;

        // 页面 加载事件
        $(document).ready(function () {
            //获取组织树形机构
            ML.ajax("/sys/org/getTree", null, function (data) {
                dtree.render({
                    elem: "#demoTree",
                    data: data,
                    initLevel: "1",///初始只显示1级
                    checkbarType: "all", //all上下级联  self独立  only单选
                    dot: false,///取消没有子节点的 节点 前面的点
                    skin: 'zdy',
                });
            }, "POST");

            //判断打开页面是修改还是添加
            if ($("#type").val() === 'update'||$("#type").val() === 'addVirtual') {
                positionList();
                getLeaders($("#orgCode").val(), $("#positionCode").val(), $("#parentId").val());
            }
        });

        //点击机构输入框
        $("#orgName").click(function () {
            var displayValue = $("#OrgTree").css("display");
            if (displayValue === "none") {
                $("#OrgTree").css("display", "inline-block");
            } else {
                $("#OrgTree").css("display", "none");
            }
        });

        //选取树形菜单节点
        dtree.on("node('demoTree')", function (obj) {
            if(obj.param.nodeId.includes("C")){
                return layer.msg("分公司下面不能建岗位！");
            }else {
                $("#OrgTree").css("display", "none");
                $("#orgName").val(obj.param.context);
                $("#orgCode").val(obj.param.nodeId);
                //机构名称改变删除岗位名称和父级定岗旧值
                $("#parentId").val("");
                $("#parentName").val("");
                $("#positionCode").val("");
                positionList();
            }
        });

        //获取所有职位
        function positionList() {
            //获取所有职位
            ML.ajax("/sys/position/positionList", null, function (result) {
                result.data.forEach(function (position) {
                    var positionObj = "<option class='positionCode' id=" + position.positionCode + " value=" + position.positionCode + ">" + position.positionName + "</option>";
                    $("#positionList").append(positionObj);
                    $("#positionList").val($("#positionCode").val());
                    //渲染表单的下拉框
                    form.render('select');
                });
            });
        }

        //岗位名称选择框事件
        form.on('select(positionFilter)', function (data) {
            $("#positionCode").val(data.value);
            $("#parentId").val("");
            $("#parentName").val("");
        });

        //根据职位和机构获取所有领导
        function getLeaders(orgCode, positionCode, parentId) {
            if (!positionCode) {
                return false;
            }
            if (!orgCode) {
                return false;
            }
            if (!parentId) {
                return false;
            }
            ML.ajax("/sys/orgPosition/getUserOrgPosList", {
                "orgCode": orgCode,
                "positionCode": positionCode,
                "parentId": parentId
            }, function (result) {
                if (result.code == 0) {
                    if (result.data.length > 0) {
                        result.data.forEach(function (leader) {
                            $("#parentName").val(leader.parentName);
                            $("#parentId").val(leader.parentId);
                            //渲染表单的下拉框
                            form.render('select');
                        });
                    }
                    if (result.data.length == 0) {
                        $(".positionCode").remove();
                        form.render('select');
                    }
                }
            });
        }

        //保存或者修改机构定岗
        form.on("submit(save)", function (data) {
            var id = $("#id").val();
            data.field['type'] = $("#type").val();
            data.field['id'] = id;
            $.ajax({
                type: 'POST',
                url: ctx + "/sys/orgPosition/saveVirtualOrgPosition",
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',//添加这句话
                success: function (results) {
                    if ($("#type").val() === 'save') {
                        if (results.data) {
                            layer.msg(results.data);
                        } else {
                            layer.msg("保存成功！");
                            layer.closeAll('iframe'); //关闭弹窗
                        }
                    } else {
                        layer.msg("修改成功！");
                        layer.closeAll('iframe'); //关闭弹窗
                    }
                },
                error: function (resp, textStatus, errorThrown) {
                    ML.ajaxErrorCallback(resp, textStatus, errorThrown);
                }
            });
            return false;
        });

        //取消按钮事件
        $("#close").click(function () {
            layer.closeAll('iframe'); //关闭弹窗
        });

        //父级定岗输入框点击事件
        $("#parentName").click(function () {
            //打开页面窗口
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                title: "父级定岗查询",
                area: ['60%', '60%'],
                shade: 0,
                maxmin: true,
                offset: 'auto',
                shade: [0.8, '#393D49'],
                content: ctx + "/sys/orgPosition/gotoLeaderQueryView",
                success: function (layero, index) {
                    var body = layer.getChildFrame('body', index);
                    body.find("#orgCode").val($("#orgCode").val());
                    body.find("#positionCode").val($("#positionCode").val());
                    var parentIndex = layer.getFrameIndex(window.name);
                    body.find("#parentIframeIndex").val(parentIndex);
                }
            });
        });
    });