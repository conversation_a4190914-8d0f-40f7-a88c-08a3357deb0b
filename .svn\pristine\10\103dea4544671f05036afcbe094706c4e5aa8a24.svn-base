<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.dao.sys.RolePositionMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.entity.sys.RolePosition">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="position_code" jdbcType="VARCHAR" property="positionCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <insert id="insert" parameterType="com.reon.hr.sp.entity.sys.RolePosition">
    insert into role_position (id, role_id, position_code, 
      create_time, creator, update_time, 
      updater)
    values (#{id,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, #{positionCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.entity.sys.RolePosition">
    insert into role_position
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="positionCode != null">
        position_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="positionCode != null">
        #{positionCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByRolePosition">
    update role_position rp

    <set>
      rp.role_id = #{roleId,jdbcType=BIGINT}
    </set>
    <where>
      and  rp.position_code = #{positionCode,jdbcType=VARCHAR}
    </where>
  </update>
  <select id="findByPositionCode" resultMap="BaseResultMap">
      SELECT
      id, role_id, position_code,
      create_time, creator, update_time,
      updater
       from role_position rp where rp.position_code = #{positionCode,jdbcType=VARCHAR}
  </select>
  <select id="findroleIdListByPositionCode" resultType="java.lang.Long">
    SELECT role_id
    from role_position where position_code = #{positionCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPosition">
    delete from role_position
    where position_code = #{posCode}
  </delete>
  <insert id="saveGrantRoles">
    insert into role_position(role_id,position_code,creator,updater)
    values
    <foreach collection="roleIds" item="item" separator=",">
      (#{item,jdbcType=BIGINT}, #{positionCode,jdbcType=VARCHAR}, #{opreator,jdbcType=VARCHAR},
      #{opreator,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <insert id="insertByPositionCodeAndRoleIdList">
    insert into role_position(role_id,position_code)
    values
    <foreach collection="roleIdList" item="roleId" separator=",">
      (
       #{roleId,jdbcType=BIGINT}, #{positionCode,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>
