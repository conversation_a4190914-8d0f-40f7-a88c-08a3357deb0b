<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/22
  Time: 14:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>修改员工合同</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-onlySelf {
            width: 125px;
        }

        .layui-tab-item {
            position: unset
        }

        .layui-btn layui-btn-sm tableSelect_btn_select reset{
            display:none!important;
        }


    </style>
</head>
<body>

<div class="layui-tab-item layui-show" style="margin-top: 5px">
    <div class="layui-fluid">
        <div class="layui-card">
            <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
                <%--隐藏域--%>
                <input type="hidden" id="empContractNo" name="empContractNo" value="${vo.empContractNo}"/>
                <input  type="hidden" id="fileIdCache" value="${vo.fileId}"/>
                <input  type="hidden" id="signStatus" value="${vo.signStatus}"/>
                <input  type="hidden" id="workMethod1" value="${vo.workMethod}"/>
                <input  type="hidden" id="tempType1" value="${vo.tempType}"/>
                <input  type="hidden" id="empContractType1" value="${vo.empContractType}"/>
                <input  type="hidden" id="empId" name="empId" value="${vo.empId}"/>
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label title="证件号码:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>证件号码:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="certNo" readonly disabled
                                   name="certNo" lay-filter="certNoFilter" autocomplete="off" placeholder=""  lay-verify="required" value="${vo.certNo}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="证件类型:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>证件类型:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="certType"
                                   lay-filter="certTypeFilter" autocomplete="off" readonly value="${vo.certType}" disabled
                                   placeholder="">
                        </div>

                    </div>

                    <div class="layui-inline">
                        <label title="雇员姓名:" class="layui-form-label  layui-onlySelf">雇员姓名:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="name" value="${vo.name}" disabled
                                   name="" lay-filter="nameFilter" autocomplete="off" placeholder="" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="唯一号:" class="layui-form-label  layui-onlySelf">唯一号:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="employeeNo"
                                   name="" lay-filter="employeeNoFilter" autocomplete="off" value="${vo.employeeNo}" disabled
                                   placeholder="请选择" readonly>
                        </div>
                    </div>



                    <div class="layui-inline">
                        <label title="客户编号:" class="layui-form-label  layui-onlySelf">客户编号:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="custNo" readonly value="${vo.custNo}" disabled
                                   name="" lay-filter="custNoFilter" autocomplete="off" placeholder="请选择" >
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="客户名称:" class="layui-form-label  layui-onlySelf">客户名称:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="custName" readonly value="${vo.custName}" disabled
                                   name="" lay-filter="custNameFilter" autocomplete="off"
                                   placeholder="">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同编号:" class="layui-form-label  layui-onlySelf">合同编号:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="contractNo" readonly value="${vo.contractNo}" disabled
                                   name="contractNo" lay-filter="" autocomplete="off"
                                   placeholder="">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同编号:" class="layui-form-label  layui-onlySelf">合同名称:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="contractName" readonly value="${vo.contractName}" disabled
                                   name="" lay-filter="" autocomplete="off"
                                   placeholder="">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="签署状态:" class="layui-form-label  layui-onlySelf">签署状态:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis" name="" DICT_TYPE="SIGN_STATUS"
                                    id="signStatus1" placeholder="无法选择" readonly disabled>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="正式工资:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>正式工资:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input layui-input-disposable dis" id="formalSalary" lay-verify="required"
                                   name="formalSalary" lay-filter="formalSalaryFilter" autocomplete="off" value="${vo.formalSalary}"
                                   placeholder="请选择">
                        </div>
                    </div>






                    <div class="layui-inline">
                        <label title="签署日期:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>签署日期:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="signDate" lay-verify="required"
                                   name="signDate" lay-filter="signDateFilter" autocomplete="off" value="${vo.signDate}"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="工作制:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>工作制:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis"  lay-verify="required" name="workMethod" id="workMethod" DICT_TYPE="WORKING_SYSTEM" placeholder="请选择" >
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同类别:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同类别:</label>
                        <div class="layui-input-inline">
                            <select class="layui-select dis"  lay-verify="required" lay-filter="empContractType" name="empContractType" DICT_TYPE="LABOR_CONTRACT_KIND"
                                    id="empContractType" placeholder="请选择"  >
                                <option value=""></option>
                            </select>
                        </div>
                    </div>



                    <div class="layui-inline">
                        <label title="劳务合同起:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>劳务合同起:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="startDate" lay-verify="required"
                                   name="startDate" lay-filter="startDateFilter" autocomplete="off" value="${vo.startDate}"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="劳务合同止:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>劳务合同止:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis requiredEmpContractType" id="endDate" lay-verify="required"
                                   name="endDate" lay-filter="endDateFilter" autocomplete="off" value="${vo.endDate}"
                                   placeholder="请选择">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label title="合同签订地:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同签订地:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="signPlace1" readonly
                                   lay-filter="signPlace1Filter" autocomplete="off"
                                   placeholder="请选择" lay-verify="required">
                            <input type="hidden" id="signPlace" name="signPlace" value="${vo.signPlace}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同版本地:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同版本地:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="tempPlace1" readonly
                                   lay-filter="tempPlace1Filter" autocomplete="off"
                                   placeholder="请选择" lay-verify="required">
                            <input type="hidden" id="tempPlace" name="tempPlace" value="${vo.tempPlace}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同版本:" class="layui-form-label  layui-onlySelf"><i style="color: red">*</i>合同版本:</label>
                        <div class="layui-input-inline">
                            <select  class="layui-select dis" id="tempType" DICT_TYPE="LABOR_CONTRACT_TEMP"
                                     name="tempType" lay-filter="tempTypeFilter" autocomplete="off"
                                     placeholder="请选择" lay-verify="required">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="合同原则:" class="layui-form-label  layui-onlySelf">合同原则:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dis" id="principle"
                                   name="principle" lay-filter="principleFilter" autocomplete="off" value="${vo.principle}"
                                   placeholder="请选择">
                        </div>
                    </div>



                    <div class="layui-inline">
                        <label title="用工单位:" class="layui-form-label  layui-onlySelf"><i style="color: red" class ="layui-hide disStart">*</i>用工单位:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable dispatch" id="employingUnit"
                                   name="employingUnit" lay-filter="employingUnitFilter" autocomplete="off" value="${vo.employingUnit}"
                                   placeholder="请选择">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label title="工作地（省/市）:" class="layui-form-label  layui-onlySelf">工作地（省/市）:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="workPlace"
                                   name="workPlace" autocomplete="off" value="${vo.workPlace}"
                                   placeholder="请输入">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label title="工作岗位:" class="layui-form-label  layui-onlySelf">工作岗位:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input layui-input-disposable" id="jobPosition"
                                   name="jobPosition"  autocomplete="off"  value="${vo.jobPosition}"
                                   placeholder="请输入">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>文件上传</legend>
                    </fieldset>
                    <div class="layui-upload">
                        <div class="layui-input-block" style="width: 1166px;">
                            <button type="button" id="employContractUpload" class="layui-btn layui-btn-normal dis">选择文件
                            </button>
                            <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                                预览图：
                                <div class="layui-upload-list" id="upload"></div>
                            </blockquote>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>备注</legend>
                    </fieldset>
                    <div class="layui-input-block" style="width: 1166px;">
                        <textarea placeholder="请输入内容" name="remark" id="remark" class="layui-textarea queryDis"
                                  style="min-width: 55px">${vo.remark}</textarea>
                    </div>

                </div>
                <div class="layui-form-item">
                    <div class="layui-inline" style="float: right;padding:10px">
                        <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="save"
                                lay-filter="save" lay-submit="">保存
                        </button>
                        <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="close"
                                lay-filter="closeFilter">取消
                        </button>
                    </div>
                </div>
        </form>
    </div>
</div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/laborContract/editLaborContractPage.js?v=${publishVersion}"></script>
</body>
</html>
