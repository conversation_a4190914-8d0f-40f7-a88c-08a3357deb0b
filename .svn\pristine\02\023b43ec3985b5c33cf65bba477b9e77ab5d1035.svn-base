package com.reon.hr.api.customer.vo;

import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SalaryCategoryVo implements Serializable {

    /**
    * 其它表数据
    * */
  private String custName;
  /**
   *客户编号
   * 在DTO和 entity中是没有的, 在 salaryItem展示中使用
   * */
  private String custNo;
  private String contractName;
  private String contractAreaName;
  private String commissioner;
  private String salaryCommissioner;
  private String receivingMan;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户Id
     */
    private Long custId;


    /**
     * 薪资类别编号
     */
    private String categoryNo;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 
     */
    private Long taxListId;

    private String taxListName;
    /**
     * 
     */
    private Byte pullFlag;

    /**
     * 
     */
    private Byte pullMonth;

    /**
     * 社保依据(1、账单，2、社保数据，3、服务月)
     */
    private Byte basisType;

    /**
     * 工资单格式（1、不提供，2、纸质，3、邮件正文）
     */
    private Byte listFormat;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 账单模板
     */
    private Long templetId;

    private String templetName;

    private String templetNo;
    /**
     * 增值税率类型（1、6%，2、5%，3、3%，4、0%）
     */
    private Byte vatRateType;

    /**
     * 增值税率值
     */
    private BigDecimal vatRate;

    /**
     * 附加税率
     */
    private BigDecimal addRate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;


    private Byte status;
    private Integer scCompetitionFlag;
    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    /**
     * 小合同编号(多个小合同编号以逗号分隔)
     */
    private String contractAreaNo;

    private List<OrgPositionDto> userOrgPositionDtoList;
    private String salaryCommOrg;

    /**
     * 主键ID
     * @return id 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键ID
     * @param id 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 
     * @return cust_id 
     */
    public Long getCustId() {
        return custId;
    }

    /**
     * 
     * @param custId 
     */
    public void setCustId(Long custId) {
        this.custId = custId;
    }

    /**
     * 
     * @return category_no 
     */
    public String getCategoryNo() {
        return categoryNo;
    }

    /**
     * 
     * @param categoryNo 
     */
    public void setCategoryNo(String categoryNo) {
        this.categoryNo = categoryNo == null ? null : categoryNo.trim();
    }

    /**
     * 
     * @return category_name 
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 
     * @param categoryName 
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    /**
     * 
     * @return tax_list_id 
     */
    public Long getTaxListId() {
        return taxListId;
    }

    /**
     * 
     * @param taxListId 
     */
    public void setTaxListId(Long taxListId) {
        this.taxListId = taxListId;
    }


    /**
     * 
     * @return pull_flag 
     */
    public Byte getPullFlag() {
        return pullFlag;
    }

    /**
     * 
     * @param pullFlag 
     */
    public void setPullFlag(Byte pullFlag) {
        this.pullFlag = pullFlag;
    }

    /**
     * 
     * @return pull_month 
     */
    public Byte getPullMonth() {
        return pullMonth;
    }

    /**
     * 
     * @param pullMonth 
     */
    public void setPullMonth(Byte pullMonth) {
        this.pullMonth = pullMonth;
    }

    /**
     * 社保依据(1、账单，2、社保数据，3、服务月)
     * @return basis_type 社保依据(1、账单，2、社保数据，3、服务月)
     */
    public Byte getBasisType() {
        return basisType;
    }

    /**
     * 社保依据(1、账单，2、社保数据，3、服务月)
     * @param basisType 社保依据(1、账单，2、社保数据，3、服务月)
     */
    public void setBasisType(Byte basisType) {
        this.basisType = basisType;
    }

    /**
     * 工资单格式（1、不提供，2、纸质，3、邮件正文）
     * @return list_format 工资单格式（1、不提供，2、纸质，3、邮件正文）
     */
    public Byte getListFormat() {
        return listFormat;
    }

    /**
     * 工资单格式（1、不提供，2、纸质，3、邮件正文）
     * @param listFormat 工资单格式（1、不提供，2、纸质，3、邮件正文）
     */
    public void setListFormat(Byte listFormat) {
        this.listFormat = listFormat;
    }

    /**
     * 合同编号
     * @return contract_no 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    /**
     * 合同编号
     * @param contractNo 合同编号
     */
    public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }

    /**
     * 账单模板
     * @return templet_id 账单模板
     */
    public Long getTempletId() {
        return templetId;
    }

    /**
     * 账单模板
     * @param templetId 账单模板
     */
    public void setTempletId(Long templetId) {
        this.templetId = templetId;
    }

    /**
     * 增值税率类型（1、6%，2、5%，3、3%，4、0%）
     * @return vat_rate_type 增值税率类型（1、6%，2、5%，3、3%，4、0%）
     */
    public Byte getVatRateType() {
        return vatRateType;
    }

    /**
     * 增值税率类型（1、6%，2、5%，3、3%，4、0%）
     * @param vatRateType 增值税率类型（1、6%，2、5%，3、3%，4、0%）
     */
    public void setVatRateType(Byte vatRateType) {
        this.vatRateType = vatRateType;
    }

    /**
     * 增值税率值
     * @return vat_rate 增值税率值
     */
    public BigDecimal getVatRate() {
        return vatRate;
    }

    /**
     * 增值税率值
     * @param vatRate 增值税率值
     */
    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    /**
     * 附加税率
     * @return add_rate 附加税率
     */
    public BigDecimal getAddRate() {
        return addRate;
    }

    /**
     * 附加税率
     * @param addRate 附加税率
     */
    public void setAddRate(BigDecimal addRate) {
        this.addRate = addRate;
    }

    /**
     * 创建人
     * @return creator 创建人
     */
    public String getCreator() {
        return creator;
    }

    /**
     * 创建人
     * @param creator 创建人
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人
     * @return updater 修改人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 修改人
     * @param updater 修改人
     */
    public void setUpdater(String updater) {
        this.updater = updater == null ? null : updater.trim();
    }

    /**
     * 修改时间
     * @return update_time 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 删除标识(Y:已删除，N:未删除)
     * @return del_flag 删除标识(Y:已删除，N:未删除)
     */
    public String getDelFlag() {
        return delFlag;
    }

    /**
     * 删除标识(Y:已删除，N:未删除)
     * @param delFlag 删除标识(Y:已删除，N:未删除)
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    /**
     * 小合同编号(多个小合同编号以逗号分隔)
     * @return contract_area_no 小合同编号(多个小合同编号以逗号分隔)
     */
    public String getContractAreaNo() {
        return contractAreaNo;
    }

    /**
     * 小合同编号(多个小合同编号以逗号分隔)
     * @param contractAreaNo 小合同编号(多个小合同编号以逗号分隔)
     */
    public void setContractAreaNo(String contractAreaNo) {
        this.contractAreaNo = contractAreaNo == null ? null : contractAreaNo.trim();
    }
}