package com.reon.hr.api.bill.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ProjectName: truck2.0
 * @Package: com.reon.hr.api.bill.vo
 * @ClassName: InsuranceBillDifferentVo
 * @Author: Administrator
 * @Description:
 * @Date: 2023/6/27 15:22
 * @Version: 1.0
 */
@Data
public class InsuranceBillDifferentVo implements Serializable {
    @ColumnWidth(25)//设置单元格宽度
    @ExcelProperty(value = "合同编号")
    private String contractNo;//'合同编号',
    @ColumnWidth(25)//设置单元格宽度
    @ExcelProperty(value = "账单模板ID")
    private Long templetId;//'账单模板ID',
    @ColumnWidth(50)//设置单元格宽度
    @ExcelProperty(value = "客户名称")
    private String custName;//'客户名称',
    @ColumnWidth(50)//设置单元格宽度
    @ExcelProperty(value = "合同名称")
    private String contractName;//'合同名称',
    @ExcelProperty(value = "合同类型")
    private String contractType;//'合同类型',
    @ExcelIgnore
    private Long custId;//'客户ID',
    @ColumnWidth(50)//设置单元格宽度
    @ExcelProperty(value = "账单模板名称")
    private String templetName;//'账单模板名称',
    @ExcelIgnore
    private String billName;//'账单名称',
    @ExcelProperty(value = "201账单人数")
    private Integer oldBillNum;//生产账单人数
    @ExcelProperty(value = "201账单金额")
    private BigDecimal oldBillAmt;//生产账单金额
    @ExcelProperty(value = "211账单人数")
    private Integer newBillNum;//新版本账单人数
    @ExcelProperty(value = "211账单金额")
    private BigDecimal newBillAmt;//新版本账单人数
    @ExcelProperty(value = "人数差异")
    private Integer popVariance;//人数差异
    @ExcelProperty(value = "金额差异")
    private BigDecimal amtVariance;//金额差异

}
