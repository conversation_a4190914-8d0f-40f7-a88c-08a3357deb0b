<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.salary.SalaryPayBatch2ReceivedMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.salary.SalaryPayBatch2Received">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="received_id" jdbcType="BIGINT" property="receivedId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, batch_id, received_id,creator, create_time, updater, update_time, del_flag
  </sql>
  <insert id="insertList">
    insert into salary_pay_batch_2_received (batch_id,received_id,create_time,creator) values
    <trim suffixOverrides="," >
      <foreach collection="list" item="item" separator=",">
        (#{item.batchId},#{item.receivedId},#{item.createTime},#{item.creator})
      </foreach>
    </trim>
  </insert>
  <update id="updateReceivedIdByList">
    update salary_pay_batch_2_received
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="received_id = case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.receivedId != null">
            when id = #{item.id}
            then #{item.receivedId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from salary_pay_batch_2_received
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="getByBatchIdList" resultType="com.reon.hr.api.bill.vo.salary.SalaryPayBatch2ReceivedVo">
    select
    <include refid="Base_Column_List" />
    from salary_pay_batch_2_received
    where batch_id in
    <foreach collection="list" item="item" close=")" separator="," open="(">
      #{item}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey">
    update salary_pay_batch_2_received
    set del_flag ='Y',
    updater =#{updater},
    update_time =now()
    where id in
    <foreach close=")" collection="ids" item="id" open="(" separator=", ">
      #{id}
    </foreach>

  </delete>
  <delete id="deleteByBatchIdList">
    delete from salary_pay_batch_2_received
           where batch_id in
                 <foreach collection="list" item="item" close=")" separator="," open="(">
                   #{item}
                 </foreach>
  </delete>
</mapper>