var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


    /**
     * 获取所有系统项目
     * itemKind = 1 表示系统项目
     *
     * */
    table.render({
        id: 'checkCommissionerGrid',
        elem: '#checkCommissionerTable',
        url: ML.contextPath + '/sys/user/getUserByOrgCode',
        where: {orgCode: $("#comCode").val()},
        data: [],
        method: 'get',
        page: true,//默认不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: 'auto',
        limit: 50,
        text: {
            none: '暂无数据'
        },
        cols: [[
            {field: 'userName', title: '姓名', align: 'center', width: '25%'},
            {field: 'tel', title: '电话', align: 'center', width: '25%'},
            {field: 'email', title: '邮箱', align: 'center', width: '25%'},
            {field: 'orgName', title: '所属公司', align: 'center', width: '25%'}
        ]]

    });


    //关闭按钮
    $(document).on("click", "#close", function () {
        layer.closeAll('iframe')
    });

    function reload() {
        table.reload('salaryItemGrid', {
            curr: 1
        });
    }

    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('checkCommissionerGrid', {
            where: {custName: $("#custName").val()},
            curr: 1
        })
    });
});