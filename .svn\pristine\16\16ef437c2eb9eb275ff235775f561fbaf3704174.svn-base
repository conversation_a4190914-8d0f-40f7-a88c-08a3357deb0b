<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /*去掉type=number时的上下加减按钮*/
        /* 谷歌 */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        /* 火狐 */
        input{
            -moz-appearance:textfield;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post">
    <table class="layui-table" lay-skin="nob" style="width: 65%;">
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>城市：</td>
            <td width="60%">
                <select class="layui-select layui-select-disabled" name="cityCode" id="cityCode" lay-filter="cityCode" lay-search AREA_TYPE lay-verify="required">
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;"></i>残障金比率：</td>
            <td><input class="layui-input" placeholder="例：0.015" type="number" oninput="if(value>=1||value<0)value=0;if(value.length>8)value=value.slice(0,8);"  name="rate" autocomplete="off" id="rate" lay-verify=""></td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>扣缴义务人类型：</td>
            <td>
                <select class="layui-select layui-select-disabled" name="type" id="type" lay-filter="type" lay-search DICT_TYPE="WITHHOLDING_AGENT_TYPE"   lay-verify="required">
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>扣缴义务人名称：</td>
            <td>
                <select class="layui-select layui-select-disabled" name="orgCode" id="withholdingAgentName" lay-filter="withholdingAgentName" lay-search disabled>
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>合同名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="contractName" id="contractName" lay-verify="required" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>合同编号：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="contractNo" id="contractNo" lay-verify="required" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>客户名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="" id="custName" lay-verify="required" readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>计算方式：</td>
            <td width="60%">
                <select class="layui-select layui-select-disabled" name="specialType" id="specialType"  lay-verify="required" DICT_TYPE="SPECIAL_TYPE">
                    <option value=""></option>
                </select>
            </td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;"></i>固定金额：</td>
            <td><input class="layui-input" type="number"  name="fixed" autocomplete="off" id="fixed"></td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;"></i>reon系统封顶金额：</td>
            <td><input class="layui-input" type="number"  name="reonUpperLimit" autocomplete="off" id="reonUpperLimit" ></td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;"></i>收取上限：</td>
            <td><input class="layui-input"  type="number"   name="upperLimit" autocomplete="off" id="upperLimit" ></td>
        </tr>
    </table>
    <div style="float: right; margin-right: 40%;">
        <button class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn" >保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/specialDisabilityGoldRate/addSpecialDisabilityGoldRatePage.js?v=${publishVersion}"></script>
</body>
</html>