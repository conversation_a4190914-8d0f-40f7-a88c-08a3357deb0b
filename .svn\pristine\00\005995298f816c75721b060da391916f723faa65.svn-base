package com.reon.ehr.web.service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.reon.ehr.api.sys.dubbo.service.rpc.ISysMenuWrapperService;
import com.reon.ehr.api.sys.dubbo.service.rpc.ISysRoleWrapperService;
import com.reon.ehr.api.sys.vo.SysRoleVo;
import com.reon.ehr.api.sys.vo.SysUserVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 用户权限处理
 * 
 * <AUTHOR>
 */
@Component
public class SysPermissionService
{
     @DubboReference(check = false)
    private ISysRoleWrapperService roleService;

     @DubboReference(check = false)
    private ISysMenuWrapperService menuService;

    /**
     * 获取角色数据权限
     * 
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUserVo user)
    {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdminFlag())
        {
            roles.add("admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUserVo user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdminFlag())
        {
            perms.add("*:*:*");
        }
        else
        {
            List<SysRoleVo> roles = user.getRoles();
            if (roles !=null && !roles.isEmpty() && roles.size() > 1)
            {
                // 多角色设置permissions属性，以便数据权限匹配权限
                for (SysRoleVo role : roles)
                {
                    Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
                    role.setPermissions(rolePerms);
                    perms.addAll(rolePerms);
                }
            }
            else
            {
                perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
            }
        }
        return perms;
    }
}
