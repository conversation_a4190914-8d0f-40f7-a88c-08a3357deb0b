package com.reon.hr.sp.bill.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.vo.InsuranceBillLogVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.sp.bill.dao.bill.InsuranceBillLogMapper;
import com.reon.hr.sp.bill.entity.bill.InsuranceBillLog;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InsuranceBillLogServiceImpl implements IInsuranceBillLogService {

    @Autowired
    private InsuranceBillLogMapper billLogMapper;
    @Autowired
    private IUserWrapperService iUserWrapperService;

    @Override
    public int insertSelective(InsuranceBillLog billLog) {
        return billLogMapper.insertSelective(billLog);
    }

    @Override
    public Page<InsuranceBillLogVo> getListByBillId(Integer page, Integer limit, Long billId) {
        Page<InsuranceBillLogVo> billLogVoPage = new Page<>(page, limit);
        List<InsuranceBillLogVo> billLogVos = billLogMapper.getListByBillId(billLogVoPage, billId);
        Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
        for (InsuranceBillLogVo item : billLogVos) {
            String creator = allUserMap.get(item.getCreator());
            if (creator != null)  item.setCreator(creator);
        }
        billLogVoPage.setRecords(billLogVos);
        return billLogVoPage;
    }

}
