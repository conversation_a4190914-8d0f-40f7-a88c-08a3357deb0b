package com.reon.hr.api.customer.enums.importData;

import com.reon.hr.api.customer.enums.BaseEnum;

public enum ImportDataType implements BaseEnum {

    BATCH_ADD_EMPLOYEE(1,"批量增员导入"),
    BATCH_ADD_EMPLOYEE_DIMISSION(2,"批量离职申请导入"),
    ORDER_BASIC_INFORMATION(3,"批量个人订单修改"),
    BATCH_ADD_COMM_INSUR_ORDER_EMPLOYEE(4,"商保订单批量导入"),
    SALARY_EMPLOYEE(5,"批量纯代发人员增员"),
    SALARY_DEDUCTION(6,"工资抵扣项导入"),
    SALARY_PAY(7,"薪资导入"),
   	BATCH_ADD_SALARY_EMPLOYEE_BANK_CARD(8,"工资批量导入员工银行卡"),
    EMPLOYEE_CONTRACT(9,"员工合同导入"),
    SALARY_DELAYED(10,"批量缓发导入"),
   	BATCH_ADD_EMPLOYEE_ACCOUNT(11,"雇员社保公积金账号导入"),
    MAKE_A_SUPPLEMENTARY_PAYMENT(12,"补缴社保公积金导入"),
	BATCH_ADD_IND_TAX_APPLY_INFO(13,"批量个税申报数据导入"),
    BATCH_ADD_COMMER_CHG_TEMPLET_INFO(14, "批量商保订单修改帐单模板导入"),
    COMM_INSUR_BATCH_IMPORT(15, "商保批量减员导入"),

    TEL_RECORD_BATCH_IMPORT(16,"电话客户记录导入"),
    ACTUAL_VARIANCE_BATCH_IMPORT(17, "实做差异导入"),

    BATCH_ADD_DISTRIBUTION(19,"批量新增发放"),
    BATCH_SALARY_PAY(20,"批量薪资导入"),

    BATCH_ADD_RECEIVINGMAN(21,"服务网点接单客户导入"),
    BATCH_EDIT_BILLSTARTMONTH(22,"批量变更收费开始月"),
    BATCH_ADD_PERSONAL_PRODUCTS(23,"批量个人产品导入"),
    BATCH_ADD_ADJUST_ORDER(24,"批量调整订单导入"),
    BATCH_EDIT_KEY_CUSTOMER(25,"批量修改重点客户导入"),
    BATCH_ADD_TODO_ITEMS(26,"批量新增待办事项导入"),
    BATCH_ADD_SALARY_SPECIAL_EMP(27,"工资特殊人员信息导入"),
    BATCH_ADD_ONCE_FEE_EMP(28,"批量导入一次费用"),
    BATCH_UPDATE_INSURANCE_RATIO(29,"批量维护社保比例"),
    BATCH_ADD_EHR_EMPLOYEE(30,"企业端批量增员受理导入"),
    BATCH_ADD_EHR_EMPLOYEE_DIMISSION(31,"企业端批量减员受理导入"),
    BATCH_SALARY_CALCULATION(32,"批量薪资计算导入"),
    BATCH_EDIT_BILL_STARTMONTH(33,"批量修改账单起始月导入"),
    BATCH_EDIT_PROD_EXPIREMONTH(34,"批量修改产品截止月导入"),
    IMPLEMENT_FEEDBACK_IMPORT(35,"实做办理反馈导入/实做办理反馈产品失败截图导入"),
    SALARY_ACTUAL_INFO_IMPORT(36,"工资实发数据导入"),
    BATCH_ADJUSTMENT_IMPORT(37,"批量实做调整"),
    SUPPLIER_PRAC_ADJUST(39,"供应商实做调整"),
    BATH_TRANSFER(40,"批量申报转移"),
    SUPPLIER_SALARY_IMPORT(41,"供应商薪资导入"),
    SUP_BATCH_UPDATE_BILL_START_MONTH(42,"供应商实做账单起始月批量修改"),
    BATCH_IMPORT_LABOR_CONTRACT(43,"批量导入劳务合同"),
    BATCH_SALARY_UPDATE(44,"批量薪资修改"),
    BATCH_IMPORT_ORDER_REMARK(45,"批量导入未进入账单订单备注"),
    SUP_BATCH_UPDATE_SERVICE_START_MONTH(46,"供应商实做服务费起始月批量修改"),
    TRANSFER_BATCH_IMPORT_EMP_CONTRACT(47,"申报转移批量导入员工合同"),
    SUP_BATCH_UPDATE_PROD_START_MONTH(48,"供应商实做产品起始月批量修改"),
    INSURANCE_PRACTICE_DIFFERENCE_IMPORT(49,"实做支付导入差异数据"),
    ;
    private int code;
    private String name;

    ImportDataType(int code,String name) {
        this.name = name;
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
