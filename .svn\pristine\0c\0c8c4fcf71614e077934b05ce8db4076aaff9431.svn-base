<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeContractMapper">
  <resultMap id="BaseResultMap" type="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="emp_contract_no" jdbcType="VARCHAR" property="empContractNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sign_form" jdbcType="VARCHAR" property="signForm" />
    <result column="emp_contract_type" jdbcType="INTEGER" property="empContractType" />
    <result column="temp_type" jdbcType="INTEGER" property="tempType" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="work_place" jdbcType="VARCHAR" property="workPlace" />
    <result column="job_position" jdbcType="VARCHAR" property="jobPosition" />
    <result column="work_method" jdbcType="VARCHAR" property="workMethod" />
    <result column="employing_unit" jdbcType="VARCHAR" property="employingUnit" />
    <result column="proba_start" jdbcType="DATE" property="probaStart" />
    <result column="proba_end" jdbcType="DATE" property="probaEnd" />
    <result column="sign_date" jdbcType="DATE" property="signDate" />
    <result column="proba_salary" jdbcType="DECIMAL" property="probaSalary" />
    <result column="dispatch_start" jdbcType="DATE" property="dispatchStart" />
    <result column="dispatch_end" jdbcType="DATE" property="dispatchEnd" />
    <result column="formal_salary" jdbcType="DECIMAL" property="formalSalary" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, emp_contract_no, order_no, sign_form, emp_contract_type, temp_type, 
    start_date, end_date, work_place, job_position, work_method, employing_unit, proba_start, 
    proba_end, sign_date, proba_salary, dispatch_start, dispatch_end, formal_salary, 
    remark, creator, create_time, updater, update_time, del_flag,probation_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from `reon-ehrdb`.ehr_employee_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ehr_employee_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByOrderId">
    delete from `reon-ehrdb`.ehr_employee_contract
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract" useGeneratedKeys="true">
    insert into ehr_employee_contract (order_id, emp_contract_no, order_no, 
      sign_form, emp_contract_type, temp_type, 
      start_date, end_date, work_place, 
      job_position, work_method, employing_unit, 
      proba_start, proba_end, sign_date, 
      proba_salary, dispatch_start, dispatch_end, 
      formal_salary, remark, creator, 
      create_time, updater, update_time, 
      del_flag)
    values (#{orderId,jdbcType=BIGINT}, #{empContractNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{signForm,jdbcType=VARCHAR}, #{empContractType,jdbcType=INTEGER}, #{tempType,jdbcType=INTEGER}, 
      #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{workPlace,jdbcType=VARCHAR}, 
      #{jobPosition,jdbcType=VARCHAR}, #{workMethod,jdbcType=VARCHAR}, #{employingUnit,jdbcType=VARCHAR}, 
      #{probaStart,jdbcType=DATE}, #{probaEnd,jdbcType=DATE}, #{signDate,jdbcType=DATE}, 
      #{probaSalary,jdbcType=DECIMAL}, #{dispatchStart,jdbcType=DATE}, #{dispatchEnd,jdbcType=DATE}, 
      #{formalSalary,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract" useGeneratedKeys="true">
      insert into `reon-ehrdb`.ehr_employee_contract
      <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="orderId != null">
              order_id,
          </if>
          <if test="empContractNo != null">
              emp_contract_no,
          </if>
          <if test="orderNo != null">
              order_no,
          </if>
          <if test="signForm != null">
              sign_form,
          </if>
          <if test="empContractType != null">
              emp_contract_type,
          </if>
          <if test="tempType != null">
              temp_type,
          </if>
          <if test="startDate != null">
              start_date,
          </if>
          <if test="endDate != null">
              end_date,
          </if>
          <if test="workPlace != null">
              work_place,
          </if>
          <if test="jobPosition != null">
              job_position,
          </if>
          <if test="workMethod != null">
              work_method,
          </if>
          <if test="employingUnit != null">
              employing_unit,
          </if>
          <if test="probaStart != null">
              proba_start,
          </if>
          <if test="probaEnd != null">
              proba_end,
          </if>
          <if test="signDate != null">
              sign_date,
          </if>
          <if test="probaSalary != null">
              proba_salary,
          </if>
          <if test="dispatchStart != null">
              dispatch_start,
          </if>
          <if test="dispatchEnd != null">
              dispatch_end,
          </if>
          <if test="formalSalary != null">
              formal_salary,
          </if>
          <if test="remark != null">
              remark,
          </if>
          <if test="creator != null">
              creator,
          </if>
          <if test="createTime != null">
              create_time,
          </if>
          <if test="updater != null">
              updater,
          </if>
          <if test="updateTime != null">
              update_time,
          </if>
          <if test="delFlag != null">
              del_flag,
          </if>
          <if test="probationFlag != null">
              probation_flag,
          </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="orderId != null">
              #{orderId,jdbcType=BIGINT},
          </if>
          <if test="empContractNo != null">
              #{empContractNo,jdbcType=VARCHAR},
          </if>
          <if test="orderNo != null">
              #{orderNo,jdbcType=VARCHAR},
          </if>
          <if test="signForm != null">
              #{signForm,jdbcType=VARCHAR},
          </if>
          <if test="empContractType != null">
              #{empContractType,jdbcType=INTEGER},
          </if>
          <if test="tempType != null">
              #{tempType,jdbcType=INTEGER},
          </if>
          <if test="startDate != null">
              #{startDate,jdbcType=DATE},
          </if>
          <if test="endDate != null">
              #{endDate,jdbcType=DATE},
          </if>
          <if test="workPlace != null">
              #{workPlace,jdbcType=VARCHAR},
          </if>
          <if test="jobPosition != null">
              #{jobPosition,jdbcType=VARCHAR},
          </if>
          <if test="workMethod != null">
              #{workMethod,jdbcType=VARCHAR},
          </if>
          <if test="employingUnit != null">
              #{employingUnit,jdbcType=VARCHAR},
          </if>
          <if test="probaStart != null">
              #{probaStart,jdbcType=DATE},
          </if>
          <if test="probaEnd != null">
              #{probaEnd,jdbcType=DATE},
          </if>
          <if test="signDate != null">
              #{signDate,jdbcType=DATE},
          </if>
          <if test="probaSalary != null">
              #{probaSalary,jdbcType=DECIMAL},
          </if>
          <if test="dispatchStart != null">
              #{dispatchStart,jdbcType=DATE},
          </if>
          <if test="dispatchEnd != null">
              #{dispatchEnd,jdbcType=DATE},
          </if>
          <if test="formalSalary != null">
              #{formalSalary,jdbcType=DECIMAL},
          </if>
          <if test="remark != null">
              #{remark,jdbcType=VARCHAR},
          </if>
          <if test="creator != null">
              #{creator,jdbcType=VARCHAR},
          </if>
          <if test="createTime != null">
              #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updater != null">
              #{updater,jdbcType=VARCHAR},
          </if>
          <if test="updateTime != null">
              #{updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="delFlag != null">
              #{delFlag,jdbcType=CHAR},
          </if>
          <if test="probationFlag != null">
              #{probationFlag,jdbcType=INTEGER}
          </if>
      </trim>
  </insert>
    <insert id="insertVoList" keyColumn="id" keyProperty="id" parameterType="com.reon.ehr.api.sys.vo.order.EhrEmployeeContractVo" useGeneratedKeys="true">
      insert into `reon-ehrdb`.ehr_employee_contract (order_id, emp_contract_no, order_no,
                                         sign_form, emp_contract_type, temp_type,
                                         start_date, end_date, work_place,
                                         job_position, work_method, employing_unit,
                                         proba_start, proba_end, sign_date,
                                         proba_salary, dispatch_start, dispatch_end,
                                         formal_salary, remark, creator,
                                         create_time, updater, update_time,
                                         del_flag,probation_flag)
      values
      <foreach collection="list" item="item" separator=",">
        (#{item.orderId,jdbcType=BIGINT}, #{item.empContractNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR},
        #{item.signForm,jdbcType=VARCHAR}, #{item.empContractType,jdbcType=INTEGER}, #{item.tempType,jdbcType=INTEGER},
        #{item.startDate,jdbcType=DATE}, #{item.endDate,jdbcType=DATE}, #{item.workPlace,jdbcType=VARCHAR},
        #{item.jobPosition,jdbcType=VARCHAR}, #{item.workMethod,jdbcType=VARCHAR}, #{item.employingUnit,jdbcType=VARCHAR},
        #{item.probaStart,jdbcType=DATE}, #{item.probaEnd,jdbcType=DATE}, #{item.signDate,jdbcType=DATE},
        #{item.probaSalary,jdbcType=DECIMAL}, #{item.dispatchStart,jdbcType=DATE}, #{item.dispatchEnd,jdbcType=DATE},
        #{item.formalSalary,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.delFlag,jdbcType=CHAR}, #{item.probationFlag})
      </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract">
        update `reon-ehrdb`.ehr_employee_contract
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="empContractNo != null">
                emp_contract_no = #{empContractNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="signForm != null">
                sign_form = #{signForm,jdbcType=VARCHAR},
            </if>
            <if test="empContractType != null">
                emp_contract_type = #{empContractType,jdbcType=INTEGER},
            </if>
            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=INTEGER},
            </if>
            <if test="startDate != null">
                start_date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=DATE},
            </if>
            <if test="workPlace != null">
                work_place = #{workPlace,jdbcType=VARCHAR},
            </if>
            <if test="jobPosition != null">
                job_position = #{jobPosition,jdbcType=VARCHAR},
            </if>
            <if test="workMethod != null">
                work_method = #{workMethod,jdbcType=VARCHAR},
            </if>
            <if test="employingUnit != null">
                employing_unit = #{employingUnit,jdbcType=VARCHAR},
            </if>
            <if test="probaStart != null">
                proba_start = #{probaStart,jdbcType=DATE},
            </if>
            <if test="probaEnd != null">
                proba_end = #{probaEnd,jdbcType=DATE},
            </if>
            <if test="signDate != null">
                sign_date = #{signDate,jdbcType=DATE},
            </if>
            <if test="probaSalary != null">
                proba_salary = #{probaSalary,jdbcType=DECIMAL},
            </if>
            <if test="dispatchStart != null">
                dispatch_start = #{dispatchStart,jdbcType=DATE},
            </if>
            <if test="dispatchEnd != null">
                dispatch_end = #{dispatchEnd,jdbcType=DATE},
            </if>
            <if test="formalSalary != null">
                formal_salary = #{formalSalary,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="probationFlag != null">
                probation_flag = #{probationFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract">
    update `reon-ehrdb`.ehr_employee_contract
    set order_id = #{orderId,jdbcType=BIGINT},
      emp_contract_no = #{empContractNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      sign_form = #{signForm,jdbcType=VARCHAR},
      emp_contract_type = #{empContractType,jdbcType=INTEGER},
      temp_type = #{tempType,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      work_place = #{workPlace,jdbcType=VARCHAR},
      job_position = #{jobPosition,jdbcType=VARCHAR},
      work_method = #{workMethod,jdbcType=VARCHAR},
      employing_unit = #{employingUnit,jdbcType=VARCHAR},
      proba_start = #{probaStart,jdbcType=DATE},
      proba_end = #{probaEnd,jdbcType=DATE},
      sign_date = #{signDate,jdbcType=DATE},
      proba_salary = #{probaSalary,jdbcType=DECIMAL},
      dispatch_start = #{dispatchStart,jdbcType=DATE},
      dispatch_end = #{dispatchEnd,jdbcType=DATE},
      formal_salary = #{formalSalary,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR},probation_flag=#{probationFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByOrderIdSelective" parameterType="com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeContract">
    update `reon-ehrdb`.ehr_employee_contract
    <set>
      <if test="empContractNo != null">
        emp_contract_no = #{empContractNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="signForm != null">
        sign_form = #{signForm,jdbcType=VARCHAR},
      </if>
      <if test="empContractType != null">
        emp_contract_type = #{empContractType,jdbcType=INTEGER},
      </if>
      <if test="tempType != null">
        temp_type = #{tempType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="workPlace != null">
        work_place = #{workPlace,jdbcType=VARCHAR},
      </if>
      <if test="jobPosition != null">
        job_position = #{jobPosition,jdbcType=VARCHAR},
      </if>
      <if test="workMethod != null">
        work_method = #{workMethod,jdbcType=VARCHAR},
      </if>
      <if test="employingUnit != null">
        employing_unit = #{employingUnit,jdbcType=VARCHAR},
      </if>
      <if test="probaStart != null">
        proba_start = #{probaStart,jdbcType=DATE},
      </if>
      <if test="probaEnd != null">
        proba_end = #{probaEnd,jdbcType=DATE},
      </if>
      <if test="signDate != null">
        sign_date = #{signDate,jdbcType=DATE},
      </if>
      <if test="probaSalary != null">
        proba_salary = #{probaSalary,jdbcType=DECIMAL},
      </if>
      <if test="dispatchStart != null">
        dispatch_start = #{dispatchStart,jdbcType=DATE},
      </if>
      <if test="dispatchEnd != null">
        dispatch_end = #{dispatchEnd,jdbcType=DATE},
      </if>
      <if test="formalSalary != null">
        formal_salary = #{formalSalary,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
       <if test="probationFlag != null">
            probation_flag = #{probationFlag,jdbcType=CHAR},
       </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

  <delete id="emptyProbationData">
      update `reon-ehrdb`.ehr_employee_contract
      <set>
          proba_start  = null,
          proba_end=null,
          proba_salary = null
      </set>
      where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
</mapper>