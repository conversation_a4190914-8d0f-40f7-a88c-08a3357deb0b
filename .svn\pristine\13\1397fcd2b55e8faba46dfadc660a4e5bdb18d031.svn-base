layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
    var window2 = window.parent[2].document;
    var elementGroupCode = window2.getElementById("groupCode");
    var elementId = window2.getElementById("id");
    var elementGroupCodeClick = window2.getElementById("groupCodeClick");
    var elementOrgCodeClick = window2.getElementById("orgCodeClick");
    var elementCityCode = window2.getElementById("cityCode");
    var elementAcctType = window2.getElementById("acctType");
    var elementRemark = window2.getElementById("remark");
    var cityCode = [];
    var groupType = [];
    var groupCode="";
    var orgCode="";
    $("#groupCodeClick").on("click",function () {
        aboutCityCode();
        var acctType = elementAcctType.value;
        if (acctType == null || acctType == "") {
            $("#groupCode").empty();
            $("<option value=''></option>").appendTo("#groupCode")
            form.render();
            return layer.msg("请先选择账号类型！")
        }
    })
    function aboutCityCode(){
        var cityCodeVal = elementCityCode.value;
        if (cityCodeVal == null || cityCodeVal == "") {
            $("#groupCode").empty();
            $("<option value=''></option>").appendTo("#groupCode")
            form.render();
            return layer.msg("请先选择城市！")
        } else {
            cityCode.push(cityCodeVal)
        }
    }
    $("#orgCodeClick").on("click",function () {
        aboutCityCode();
    })

    function insuranceGroup() {
        cityCode = [];
        groupType = [];
        var cityCodeVal = elementCityCode.value;
        if (cityCodeVal == null || cityCodeVal == "") {
            return false;
        } else {
            cityCode.push(cityCodeVal)
        }
        var acctType = elementAcctType.value;
        if (acctType == null || acctType == "") {
            return false
        }
        groupType.push(acctType);
        if (acctType == "1") {
            groupType.push(3)
        }
        $.ajax({
            url: ML.contextPath + "/sys/InsuranceGroup/getInsuranceGroupByCityCode?cityCode=" + cityCode + "&groupType=" + groupType,
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            success: function (result) {
                $("#groupCode").empty();
                $("<option value=''></option>").appendTo("#groupCode")
                if (result==null||result=="") {
                    layer.msg("该城市下没有该账号类型的社保组！")
                }
                for (var i in result) {
                    var obj = result[i];
                    $("<option value=" + obj.groupCode + ">" + obj.groupName + "</option>").appendTo("#groupCode")
                }
                if(groupCode!=null){
                    $("#groupCode").find("option[value="+groupCode+"]").attr("selected",true);
                    $("#groupCode").attr("disabled", true);
                }
                form.render();
            }, error: function (result) {
                layer.msg("错误!");
            }
        })
    }
    function cityCodeAbout(){
    insuranceGroup();
    if(cityCode==null||cityCode==""){
        return false;
    }
    $.ajax({
        url: ML.contextPath + "/sys/comAcctInfo/getOrganizationList?cityCodeList=" + cityCode ,
        type: 'GET',
        dataType: 'json',
        contentType: 'application/json',
        success: function (result) {
            $("#orgCode").empty();
            $("<option value=''></option>").appendTo("#orgCode")
            if (result==null||result=="") {
                layer.msg("该城市下没有办理方！")
            }
            for (var i in result) {
                var obj = result[i];
                $("<option value=" + obj.orgCode + ">" + obj.orgName + "</option>").appendTo("#orgCode")
            }
            if(orgCode!=null){
                $("#orgCode").find("option[value="+orgCode+"]").attr("selected",true);
                $("#orgCode").attr("disabled", true);
            }
            form.render();
        }, error: function (result) {
            layer.msg("错误!");
        }
    })
}
    var $input = $(":text");
    for (let i = 0; i < $input.length; i++) {
        if($input[i].id!=null||$input[i].id!=''){
            $input[i].oninput=function (data) {
                var value = $(this).context.value;
                if(value.length>50){
                    layer.msg("输入的长度请不要大于50!")
                    $("#"+$input[i].id).val(value.slice(0,50))
                }
            }
        }
    }
    form.on("input(remark)",function () {
        var value = $(this).context.value;
        if(value.length>255){
            layer.msg("输入的长度请不要大于255!")
            $("#remark").val(value.slice(0,255))
        }
    })
    form.on("select(acctType)", function (data) {
        insuranceGroup();
    })
    form.on("select(cityCode)", function (data) {
        cityCodeAbout();
    })
    form.on("select(bankCode)", function (data) {
        var arr=window.top['dictCachePool']['BANK'];
        var code="";
        for(var i in arr){
            var obj = arr[i];
            if(obj.name=="其它银行"){
                code=obj.code;
            }
        }
        if(data.value==code){
            $("#otherBank").removeAttr("disabled");
            $("#otherBank").attr("placeholder","请输入");
            $("#otherBank").attr("lay-verify","required");
        }else {
            //设置其他银行不可输入
            $("#otherBank").val("");
            $("#otherBank").attr("disabled", true);
            $("#otherBank").attr("placeholder","");
            $("#otherBank").attr("lay-verify","");
        }
    })
    function readonlyDiv(){
        $('#cityCodeDiv').find('input').attr("readonly","readonly");
        $('#acctTypeDiv').find('input').attr("readonly","readonly");
        $('#groupCodeClick').find('input').attr("readonly","readonly");
        $('#orgCodeClick').find('input').attr("readonly","readonly");
    }
    $(window2).mousemove(function(e){
        readonlyDiv();
    });
    var idVal = elementId.value;
    $.ajax({
        url: ML.contextPath + "/sys/comAcctInfo/getComAcctInfoById?id=" + idVal ,
        type: 'GET',
        dataType: 'json',
        contentType: 'application/json',
        success: function (result) {
            $("#cityCode").val(result.cityCode);
            $("#cityCode").attr("disabled", true);
            $("#acctType").val(result.acctType);
            $("#acctType").attr("disabled", true);
            groupCode=result.groupCode;
            orgCode=result.orgCode;
            $("#acctNo").val(result.acctNo);
            $("#bankCode").val(result.bankCode);
            $("#otherBank").val(result.otherBank);
            $("#openBank").val(result.openBank);
            $("#openName").val(result.openName);
            $("#bankAcctNo").val(result.bankAcctNo);
            $("#payee").val(result.payee);
            $("#lastDay").val(result.lastDay);
            $("#remark").val(result.remark);
            form.render();
            cityCodeAbout();
        }, error: function (result) {
            layer.msg("错误!");
        }
    })
    //保存
    form.on("submit(save)", function (data) {
        ML.layuiButtonDisabled($('#save'));// 禁用
        $.ajax({
            url: ML.contextPath + "/sys/comAcctInfo/editComAcctInfo",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                if (result) {
                    layer.closeAll('iframe');
                    layer.msg("修改成功！")
                }else {
                    layer.msg("修改失败!");
                }
                ML.layuiButtonDisabled($('#save'), 'true');
            },
            error: function (data) {
                layer.msg("修改失败!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });
        return false;
    });

    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });
});
