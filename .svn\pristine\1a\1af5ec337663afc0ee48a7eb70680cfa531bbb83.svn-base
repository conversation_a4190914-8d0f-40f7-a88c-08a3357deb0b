layui.use(['jquery', 'form', 'layer', 'element', 'table', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 代收代付表格数据源
    var collectionCostList = []

    // 一次性表格数据源
    var disposableItemApprovalVoList = []

    // 服务费数据源
    var serviceCostList = []

    // 分公司数据源
    var company = []

    // 开票明细总额
    var invoiceDetailAmt = 0;

    // 渲染服务费数据
    table.render({
        id: 'serviceChangeGridTable',
        elem: '#serviceChangeGridTable',
        page: false,
        data: serviceCostList,
        toolbar: false,
        defaultToolbar: [],
        limit: Number.MAX_VALUE, // 默认显示最大值
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            {
                field: 'productType', title: '产品类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PRODUCT_KIND", d.productType);
                }
            },
            { field: 'billMonth', title: '账单年月', width: '120', align: 'center' },
            { field: 'receivableMonth', title: '财务应收账单年月', width: '150', align: 'center' },
            { field: 'templetName', title: '客户帐套', width: '180', align: 'center' },
            { field: 'contractNo', title: '合同编号', width: '180', align: 'center' },
            { field: 'invoiceAmt', title: '总金额', width: '100', align: 'center' },
            { field: 'unTaxAmt', title: '金额', width: '100', align: 'center' },
            { field: 'receiveValTax', title: '增值税金额', width: '100', align: 'center' },
            { field: 'valTaxRate', title: '增值税税率', width: '100', align: 'center' },
            { field: 'number', title: '人数', width: '80', align: 'center' },
            {
                field: 'contractType', title: '大合同类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            },
            {
                field: 'signCom', title: '签约方抬头', width: '200', align: 'center', templet: function (d) {
                    return companyCodeToName(d.signCom);
                }
            }
        ]]
    })

    // 渲染代收代付表格
    table.render({
        id: 'collectionGridTable',
        elem: '#collectionGridTable',
        page: false,
        data: collectionCostList,
        toolbar: false,
        defaultToolbar: [],
        totalRow: true, // 开启合计行
        limit: Number.MAX_VALUE, // 默认显示最大值
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            { field: 'billMonth', title: '账单年月', width: '120', align: 'center', totalRowText: '合计' },
            { field: 'receivableMonth', title: '财务应收账单年月', width: '150', align: 'center' },
            { field: 'templetName', title: '客户帐套', width: '180', align: 'center' },
            { field: 'contractNo', title: '合同编号', width: '180', align: 'center' },
            { field: 'invoiceAmt', title: '金额', width: '120', align: 'center', totalRow: true },
            { field: 'indAmt', title: '个人金额', width: '120', align: 'center', totalRow: true },
            { field: 'comAmt', title: '企业金额', width: '120', align: 'center', totalRow: true },
            {
                field: 'productType', title: '类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PRODUCT_KIND", d.productType);
                }
            },
            {
                field: 'contractType', title: '大合同类型', width: '120', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            },
            {
                field: 'signCom', title: '签约方抬头', width: '200', align: 'center', templet: function (d) {
                    return companyCodeToName(d.signCom);
                }
            }
        ]]
    })


    // 渲染一次性收费表格
    table.render({
        id: 'disposableGridTable',
        elem: '#disposableGridTable',
        page: false,
        data: disposableItemApprovalVoList,
        toolbar: false,
        defaultToolbar: [], // 默认显示最大值
        limit: Number.MAX_VALUE,
        text: {
            none: '暂无数据' //无数据时展示
        },
        done: function (res, curr, count) {
            ML.hideNoAuth();
        },
        cols: [[
            {
                field: 'prodType', title: '产品类型', width: '150', align: 'center', fixed: 'left', templet: function (d) {
                    return ML.dictFormatter("DISPOSAL_TYPE", d.prodType);
                }
            },
            {
                field: 'prodKind', title: '产品', width: '150', align: 'center', fixed: 'left', templet: function (d) {
                    return ML.getDictSubByParentCodeAndSelfCode(d.prodType, "DISPOSAL_TYPE", d.prodKind);
                }
            },
            { field: 'invoiceAmt', title: '总金额', width: '150', align: 'center' },
            { field: 'taxFreeAmt', title: '金额', width: '150', align: 'center' },
            { field: 'tax', title: '增值税金额', width: '150', align: 'center' },
            {
                field: 'disposableTaxRatio', title: '增值税税率', width: '150', align: 'center', templet: function (d) {
                    if (ML.isEmpty(d.disposableTaxRatio))
                        return "0%";
                    else
                        return d.disposableTaxRatio + "%"
                }
            },
            { field: 'peopleNum', title: '人数', width: '150', align: 'center' },
            { field: 'remark', title: '备注', width: '150', align: 'center' }
        ]]
    })

    // 页面初始化函数
    $(document).ready(function () {
        setTimeout(function () {
            getOrgData();
            getTableData();
            $("#voucherTypes").val(ML.dictFormatter("INVOICE_TYPE", $("#voucherType").val()))
        }, 100);
    })

    // 获取分公司数据并渲染开票分公司下拉列表框
    function getOrgData() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                company = data.data;
                var hiddenOrgCOde = $("#orgCode").val()
                $.each(company, function (i, item) {
                    if (hiddenOrgCOde == item.orgCode) {
                        $("#orgCodeName").val(item.orgName)
                    }
                });

                form.render('select');
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    // 获取表格数据
    function getTableData() {
        var billIdStr = $("#billId").val()
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/invoice/getCostInfoByBillId?billId=" + billIdStr + '&invoiceId=' + $("#invoiceId").val(),
            dataType: 'json',
            success: function (data) {
                // 赋值数据源
                collectionCostList = data.data.collectionCostList
                disposableItemApprovalVoList = data.data.disposableItemApprovalVoList
                serviceCostList = data.data.serviceCostList

                // 汇总代收代付总金额
                for (var i = 0; i < collectionCostList.length; i++) {
                    invoiceDetailAmt += collectionCostList[i].invoiceAmt * 1;
                }

                // 汇总一次性收费总金额
                for (var i = 0; i < disposableItemApprovalVoList.length; i++) {
                    invoiceDetailAmt += disposableItemApprovalVoList[i].invoiceAmt * 1;
                }

                // 汇总服务非总金额
                for (var i = 0; i < serviceCostList.length; i++) {
                    serviceCostList[i].unTaxAmt = 0;
                    serviceCostList[i].receiveValTax = 0;
                    invoiceDetailAmt += serviceCostList[i].invoiceAmt * 1
                }

                // 赋值给开票明细总额
                $("#invoiceDetailAmt").val(roundKeepTwo(invoiceDetailAmt))

                // 重载表格
                table.reload('collectionGridTable', { data: collectionCostList })
                table.reload('disposableGridTable', { data: disposableItemApprovalVoList })
                table.reload('serviceChangeGridTable', { data: serviceCostList })
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    //将code转换成名字
    function companyCodeToName(code) {
        for (var i = 0; i < company.length; i++) {
            if (company[i].orgCode === code) {
                return company[i].orgName;
            }
        }
    }

    // 取消按钮
    $(document).on('click', '#invoiceCancel', function () {
        layer.closeAll('iframe');
    });
})