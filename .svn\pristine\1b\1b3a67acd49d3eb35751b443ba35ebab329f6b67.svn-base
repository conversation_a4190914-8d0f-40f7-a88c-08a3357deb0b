package com.reon.hr.common.enums;

import javafx.geometry.Pos;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年11月29日
 * @Version 1.0
 */
@Getter
public enum DepartmentEnum {

        CUSTOMER_SERVICE("客服部"),
        SALES("销售部");

        private String name;

    DepartmentEnum(String name){
            this.name = name;
        }
    public static String getName(int type) {
            ArrayList<Integer> saleList = new ArrayList<>();
            saleList.add(PositionType.SALE.getCode());
            saleList.add(PositionType.UNDERWRITERS.getCode());
            ArrayList<Integer> custList = new ArrayList<>();
            custList.add(PositionType.PROJECT_SERVICE.getCode());
            custList.add(PositionType.C_S.getCode());
            custList.add(PositionType.S_C_S.getCode());
            custList.add(PositionType.LATER_SERVICE.getCode());

            if (custList.contains(type)) {
                return DepartmentEnum.CUSTOMER_SERVICE.name;
            } else if (saleList.contains(type)) {
                return DepartmentEnum.SALES.name;
            } else{
                return null;
            }
        }



}
