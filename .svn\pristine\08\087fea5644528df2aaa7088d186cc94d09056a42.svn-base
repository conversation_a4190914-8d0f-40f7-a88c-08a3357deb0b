var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    //查询商保绑定账单数据
    table.render({
        id: 'contractTempletQueryGrid',
        elem: '#contractTempletQueryGrid',
        url: ML.contextPath + '/customer/contractTemplet/getContractTempletListPage',
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: '600',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: 'contractNo', title: '合同编号', width: '15%', align: 'center', fixed: 'left'},
            {field: 'contractName', title: '合同名称', width: '10%', align: 'center'},
            {
                field: 'contractType', title: '合同类型', width: '10%', align: 'center'
                , templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            },
            // {field: 'templetName', title: '账单模板', width: '5%', align: 'center'},
            // {field: 'feeName', title: '收费模板', width: '10%', align: 'center'},
            {field: 'quoteNo', title: '报价单编号', width: '10%', align: 'center'},
            {field: 'quoteName', title: '报价单名称', width: '10%', align: 'center'},
            {field: 'custNo', title: '客户编号', width: '10%', align: 'center'},
            {field: 'custName', title: '客户名称', width: '10%', align: 'center'},
            {
                field: 'seller', title: '销售', width: '8%', align: 'center'
                , templet: function (d) {
                    return ML.loginNameFormater(d.seller)
                }
            },
            {
                field: 'commissioner',
                title: '负责客服',
                width: '8%',
                align: 'center',
                fixed: 'right',
                templet: function (d) {
                    return ML.loginNameFormater(d.commissioner)
                }
            }
        ]],
        done: function (res) {
            ML.hideNoAuth();
        }
    });

    //监听商保绑定账单表格上方按钮
    table.on('toolbar(contractTempletQueryGridFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            //商保绑定账单
            case 'bindBillTemplate':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                open("商保绑定账单", "bindBillTemplate", ['50%', '60%'], checkStatus.data[0]);
                break;
        }
    });

    //打开窗口
    function open(title, optType, area, data) {
        var url;
        if (optType == "bindBillTemplate") {
            url = "/customer/contractTemplet/gotoBindContractTempletView?contractNo=" + data.contractNo;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#custId").val(data.custId);
                body.find("#tempName").val(data.templetName);
                body.find("#revTempName").val(data.feeName);
                body.find("#tid").val(data.tempId);
                body.find("#rtid").val(data.revTempId);
            },
            end: function () {
                reloadTable();
            }
        });
    }

    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            $("#custId").val(id);
        }
    });

    //重载数据
    function reloadTable() {
        table.reload('contractTempletQueryGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    form.on('submit(btnQuery)', function (data) {
        table.reload('contractTempletQueryGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });
});
