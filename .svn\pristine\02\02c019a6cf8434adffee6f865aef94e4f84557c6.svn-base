package com.reon.hr.sp.bill.entity.supplierPractice;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.bill.entity.supplierPractice
 * @ClassName: SupplierPracticeBill
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/5 11:12
 * @Version: 1.0
 */
@Accessors(chain = true)
@Data
public class SupplierPracticeBill implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 供应商id
     */
    private Long supplierId;


    /**
     * 账单模板id
     */
    private Long templetId;

    /**
     * 账单金额
     */
    private BigDecimal billAmt;

    private BigDecimal amtOneTime;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 一次性服务费
     */
    private BigDecimal serviceFeeOne;




    /**
     * 服务人次
     */
    private Integer serviceNum;

    /**
     * 账单年月
     */
    private Integer billMonth;

    /**
     * 生成状态(0:生成中，1:成功，2:失败)
     */
    private Integer genStatus;

    /**
     * 实际锁定时间
     */
    private String lockMan;

    /**
     * 锁定状态(1，未锁定，2、已锁定)
     */
    private Integer lockStatus;

    /**
     * 支付状态(1：未支付，2：支付中，3：已支付，4：支付失败)
     */
    private Integer payStatus;

    /**
     * 实际锁定时间
     */
    private Date lockTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(y:已删除，n:未删除)
     */
    private String delFlag;
}
