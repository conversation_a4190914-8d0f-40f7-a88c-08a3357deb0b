package com.reon.hr.sp.bill.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.dto.BillCheckAbolishForIncomeReportDto;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IBillCheckApprovalWrapperService;
import com.reon.hr.api.bill.vo.CustomerInfoVo;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.InvoiceBillVo;
import com.reon.hr.api.bill.vo.PaymentCustomerVo;
import com.reon.hr.api.bill.vo.check.*;
import com.reon.hr.sp.bill.service.bill.BillCheckAbolishForIncomeService;
import com.reon.hr.sp.bill.service.bill.IBillCheckApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service("billCheckApprovalDubboService")
public class BillCheckApprovalWrapperServiceImpl implements IBillCheckApprovalWrapperService {
    @Autowired
    private IBillCheckApprovalService billCheckService;
     @Autowired
     private BillCheckAbolishForIncomeService billCheckAbolishForIncomeService;

    @Override
    public Page<CustomerInfoVo> getUnApprovalCust(Integer page, Integer limit, String companyCode, Integer type, String keyword) {
        return billCheckService.getUnApprovalCust(page, limit, companyCode, type, keyword);
    }

    @Override
    public Page<BillCheckApprovalVo> getCheckUnApproval(Integer page, Integer limit, CheckSearchParmVo parm) {
        return billCheckService.getCheckUnApproval(page, limit, parm);
    }

    @Override
    public void updateStatusByIds(Long[] ids, String optType, String updater) {
        billCheckService.updateStatusByIds(ids, optType, updater);
    }

    @Override
    public Map<String, Object> getPayCustAndInvoice(Long checkId) {

        return billCheckService.getPayCustByCheckId(checkId);
    }

    @Override
    public BigDecimal getCheckAmtBySubCheckId(Long subCheckId) {
        return billCheckService.getCheckAmtBySubCheckId(subCheckId);
    }

    @Override
    public BigDecimal getAllCheckAmtByBillId(Long billId) {
        return billCheckService.getAllCheckAmtByBillId(billId);
    }

    @Override
    public void abolishCheck(List<Long> checkIds, String loginName) {
        billCheckService.updateAbolishCheck(checkIds,loginName);
    }

    @Override
    public List<BillCheckVo> getBillCheckByBillIdList(List<Long> billIdList) {
        return billCheckService.getBillCheckByBillIdList(billIdList);
    }

    @Override
    public Page<InsuranceBillVo> getAbolishCheckData(InsuranceBillVo param, Integer limit, Integer page) {
        return billCheckService.getAbolishCheckData(param, limit, page);
    }

    @Override
    public List<BillCheckVo> getInsuranceBillAndCheckIdByBillId(Long billId) {
        return billCheckService.getInsuranceBillAndCheckIdByBillId(billId);
    }

    @Override
    public void testCode() {
        billCheckService.testCode();
    }

    @Override
    public List<BillSubCheckVo> getBillCheckSubData(String contractNo, Long templetId, Integer billMonth, Date startDate, Date endDate) {
        return billCheckService.getBillCheckSubData(contractNo, templetId, billMonth, startDate, endDate);
    }

    @Override
    public Map<Long, String> getCheckIdAndPayCustNameMapByCheckId(List<Long> checkIdList) {
        return billCheckService.getCheckIdAndPayCustNameMapByCheckId(checkIdList);
    }

    @Override
    public Integer getBillCheckStatusByBillId(Long billId) {
        return billCheckService.getBillCheckStatusByBillId(billId);
    }

    @Override
    public Map<Long, Integer> getBillCheckStatusByBillIdList(List<Long> billIdList) {
        return billCheckService.getBillCheckStatusByBillIdList(billIdList);
    }

    @Override
    public Map<Long, Long> getMaxBillCheckIdByBillId(List<Long> billIdList) {
        return billCheckService.getMaxBillCheckIdByBillId(billIdList);
    }

    @Override
    public Long getMaxBillCheckIdByBillId(Long billId) {
        return billCheckService.getMaxBillCheckIdByBillId(billId);
    }

    @Override
    public List<BillCheckVo> getBillCheckByTempletIdAndBillMonth(List<InsuranceBillVo> insuranceBillVoList) {
        return billCheckService.getBillCheckByTempletIdAndBillMonth(insuranceBillVoList);
    }

    @Override
    public List<InvoiceBillVo> getBillInvoiceByTempletIdAndBillMonth(InsuranceBillVo insuranceBillVo) {
        return billCheckService.getBillInvoiceByTempletIdAndBillMonth(insuranceBillVo);
    }

    @Override
    public List<BillCheckVo> getByCheckIdList(List<Long> billCheckIdList) {
        return billCheckService.getByCheckIdList(billCheckIdList);
    }

    @Override
    public void checkInvoiceAll(List<Long> billIdList, List<PaymentCustomerVo> paymentCustomerVoList, Map<String, Object> conditonMap) {
        billCheckService.handleCheckInvoiceAll(billIdList, paymentCustomerVoList, conditonMap);
    }

    @Override
    public void invoiceAll(List<Long> billIdList, Map<String, Object> conditonMap) {
        billCheckService.handleInvoiceAll(billIdList, conditonMap, true);
    }

    @Override
    public void checkAll(List<Long> billIdList, List<PaymentCustomerVo> paymentCustomerVoList, Map<String, Object> conditonMap) {
        billCheckService.handleCheckAll(billIdList, paymentCustomerVoList, conditonMap);
    }

    @Override
    public List<BillCheckVo> selectCheckDataByCheckIds(List<Long> checkIds) {
        return billCheckService.selectCheckDataByCheckIds(checkIds);
    }

    @Override
    public void updateAbolishCheckByBillId(Long checkId, List<Long> biliIdList, String loginName) {
        billCheckService.updateAbolishCheckByBillId(checkId, biliIdList,loginName);
    }

    @Override
    public BillSubCheckVo getMaxIdBillSubCheckVoByBillId(Long billId) {
        return billCheckService.getMaxIdBillSubCheckVoByBillId(billId);
    }

    @Override
    public Map<Long, BillSubCheckVo> getBillSubCheckByBillIdListAndAdjustFlag(List<Long> billIdList, int adjustFlag) {
        return billCheckService.getBillSubCheckByBillIdListAndAdjustFlag(billIdList, adjustFlag);
    }

    @Override
    public List<BillCheckAbolishForIncomeVo> getEffectiveDateByBillId(Long billId) {
     return billCheckAbolishForIncomeService.getEffectiveDateByBillId(billId);

    }

    @Override
    public Integer updateDateById(BillCheckAbolishForIncomeVo billCheckAbolishForIncomeVo) {
        return billCheckAbolishForIncomeService.updateDateById(billCheckAbolishForIncomeVo);
    }

    @Override
    public List<BillCheckAbolishForIncomeReportDto> getBillCheckAbolishForIncomeReportByBillId(Set<Long> billIdSet) {
        return billCheckAbolishForIncomeService.getBillCheckAbolishForIncomeReportByBillId(billIdSet);
    }
}
