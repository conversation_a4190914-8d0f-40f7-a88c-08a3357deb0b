var ctx = ML.contextPath;
layui.config({
    base : ctx+"/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect;
    layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;




    $(document).ready(function () {
       setQysSignatory(JSON.parse($("#signatories").val()));
        setQysOperation(JSON.parse($("#operations").val()))
        setQysContractApprovalVos(JSON.parse($("#qysContractApprovalVos").val()))
        form.render();
    });




    function setQysSignatory(data) {
        table.render({
            id: 'qysSignatoryGrid',
            elem: '#qysSignatoryGrid',
            data: data,
            page: false, //默认为不开启
            limit: Number.MAX_VALUE,
            //height: 250,
            title: "签署方信息",
            toolbar: '#toolDemo',
            text: {
                none: '暂无数据' //无数据时展示
            },
            defaultToolbar: {},
            cols: [[
                {
                    field: 'tenantName',
                    title: '签署方名称',
                    width: '20%',
                    align: 'center',
                    fixed: 'left'

                },
                {
                    field: 'tenantType',
                    title: '签署方类型',
                    width: '20%',
                    align: "center",

                },
                {
                    field: 'status',
                    title: '签署方状态',
                    width: '20%',
                    align: "center",
                },


                {
                    field: 'complateTime',
                    title: '签署完成时间',
                    width: '20%',
                    align: 'center',

                },


                {
                    field: 'serialNo',
                    title: '签署顺序',
                    width: '20%',
                    align: 'center',

                },
            ]],
        });
    }
    function setQysOperation(data) {
        table.render({
            id: 'qysOperationGrid',
            elem: '#qysOperationGrid',
            data: data,
            page: false, //默认为不开启
            limit: Number.MAX_VALUE,
            //height: 250,
            title: "签署方信息",
            toolbar: '#toolDemo',
            text: {
                none: '暂无数据' //无数据时展示
            },
            defaultToolbar: {},
            cols: [[
                {
                    field: 'userName',
                    title: '操作人姓名',
                    width: '10%',
                    align: 'center',
                    fixed: 'left'

                },
                {
                    field: 'contact',
                    title: '操作人联系方式',
                    width: '10%',
                    align: 'center',
                    fixed: 'left'

                },
                {
                    field: 'operateType',
                    title: '操作类型',
                    width: '10%',
                    align: "center",

                },
                {
                    field: 'operateTimeS',
                    title: '操作时间',
                    width: '15%',
                    align: "center",
                },


                {
                    field: 'comments',
                    title: '操作详情',
                    width: '20%',
                    align: 'center',

                },


                {
                    field: 'cnOperateDesc',
                    title: '操作简述',
                    width: '15%',
                    align: 'center',

                },
                {
                    field: 'client',
                    title: '客户端类型',
                    width: '10%',
                    align: 'center',

                },
                {
                    field: 'browser',
                    title: '浏览器类型',
                    width: '10%',
                    align: 'center',

                },
            ]],
        });
    }


    function setQysContractApprovalVos(data) {
        table.render({
            id: 'reonContractAppGrid',
            elem: '#reonContractAppGrid',
            data: data,
            page: false, //默认为不开启
            limit: Number.MAX_VALUE,
            //height: 250,
            title: "签署方信息",
            toolbar: '#toolDemo',
            text: {
                none: '暂无数据' //无数据时展示
            },
            defaultToolbar: {},
            cols: [[
                {
                    field: 'appCreator',
                    title: '审批人',
                    width: '15%',
                    align: 'center',
                    fixed: 'left',
                    templet: function (d) {
                        var loginNameFormater = ML.loginNameFormater( d.appCreator);
                        if (loginNameFormater==""){
                            return d.appCreator;
                        }else {
                            return loginNameFormater;
                        }
                    }

                },

                {
                    field: 'current',
                    title: '是否当前审批人',
                    width: '15%',
                    align: 'center',
                    fixed: 'left',
                    templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.current)
                    }

                },
                {
                    field: 'status',
                    title: '是否已审批',
                    width: '15%',
                    align: "center",
                    templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.status)
                    }

                },
                {
                    field: 'passTime',
                    title: '审批时间',
                    width: '20%',
                    align: "center",
                },


                {
                    field: 'autoAppr',
                    title: '是否自动审批',
                    width: '15%',
                    align: 'center',
                    templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.autoAppr)
                    }


                },
                {
                    field: 'remark',
                    title: '详情',
                    width: '20%',
                    align: 'center',


                },

            ]],
        });
    }



});