/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/4/5
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.ehr.sp.sys.dubbo.rpc.impl;

import com.reon.ehr.api.sys.dubbo.service.rpc.ISysMenuWrapperService;
import com.reon.ehr.api.sys.vo.SysMenuVo;
import com.reon.ehr.sp.sys.domain.entity.SysMenu;
import com.reon.ehr.sp.sys.service.sys.ISysMenuService;
import com.reon.hr.common.utils.VoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SysMenuWrapperServiceImpl
 * @description TODO
 * @date 2023/4/5 17:38
 */
@Service("sysMenuWrapperService")
public class SysMenuWrapperServiceImpl implements ISysMenuWrapperService {

    @Autowired
    private ISysMenuService sysMenuService;
    @Override
    public List<SysMenuVo> selectMenuList(Long userId) {
        return VoUtil.copyProperties(sysMenuService.selectMenuList(userId), SysMenuVo.class);
    }

    @Override
    public List<SysMenuVo> selectMenuList(SysMenuVo menu, Long userId) {
        SysMenu sysMenu = VoUtil.copyProperties(menu, SysMenu.class);
        return VoUtil.copyProperties(sysMenuService.selectMenuList(sysMenu,userId), SysMenuVo.class);
    }

    @Override
    public Set<String> selectMenuPermsByUserId(Long userId) {
        return sysMenuService.selectMenuPermsByUserId(userId);
    }

    @Override
    public Set<String> selectMenuPermsByRoleId(Long roleId) {
        return sysMenuService.selectMenuPermsByRoleId(roleId);
    }

    @Override
    public List<SysMenuVo> selectMenuTreeByUserId(Long userId) {
        List<SysMenu> sysMenus =sysMenuService.selectMenuTreeByUserId(userId);
        return VoUtil.copyProperties(sysMenus, SysMenuVo.class);
    }

    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        return sysMenuService.selectMenuListByRoleId(roleId);
    }

    @Override
    public SysMenuVo selectMenuById(Long menuId) {
        return VoUtil.copyProperties(sysMenuService.selectMenuById(menuId), SysMenuVo.class);
    }

    @Override
    public boolean hasChildByMenuId(Long menuId) {
        return sysMenuService.hasChildByMenuId(menuId);
    }

    @Override
    public boolean checkMenuExistRole(Long menuId) {
        return sysMenuService.checkMenuExistRole(menuId);
    }

    @Override
    public int insertMenu(SysMenuVo menu) {
        return sysMenuService.insertMenu(VoUtil.copyProperties(menu, SysMenu.class));
    }

    @Override
    public int updateMenu(SysMenuVo menu) {
        return sysMenuService.updateMenu(VoUtil.copyProperties(menu, SysMenu.class));
    }

    @Override
    public int deleteMenuById(Long menuId) {
        return 0;
    }

    @Override
    public boolean checkMenuNameUnique(SysMenuVo menu) {
        return sysMenuService.checkMenuNameUnique(VoUtil.copyProperties(menu, SysMenu.class));
    }
}
