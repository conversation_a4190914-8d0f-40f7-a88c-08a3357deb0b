<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<HEAD>
    <meta charset="utf-8">
    <TITLE>组织机构管理</TITLE>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui_ext/dtree/dtree.css?v=${publishVersion}"/>
    <link rel="stylesheet" href="${ctx}/layui_ext/dtree/font/dtreefont.css?v=${publishVersion}"/>
</HEAD>
<body>
<div class="content_wrap">
    <div class="left layui-inline" id="toolbarDiv"
         style="width: 23%;  display: inline-block; height: 750px;overflow-y:auto;">
        <ul id="demoTree" class="dtree" data-id="0"></ul>
    </div>
    <div style="width: 76%; height: 700px; float: right;">
        <fieldset class="layui-elem-field" style="width: 270px;">
            <legend>组织机构操作</legend>
            <div class="layui-field-box">
                <div class="layui-inline">
                    <button class="layui-btn" lay-filter="copyOrgPos" id="copyBtn" type="button"
                            style="border-left-width: 20px;padding-left: 20px;margin-left: 35px;margin-top: 10px;margin-bottom: 10px;">
                        复制
                    </button>
                    <button class="layui-btn" lay-filter="syncFilter" id="syncBtn" type="button"
                            style="border-left-width: 20px;padding-left: 20px;margin-left: 35px;margin-top: 10px;margin-bottom: 10px;">
                        同步
                    </button>
                </div>

            </div>
        </fieldset>
        <table class="layui-hide" id="orgGrid" lay-filter="orgFilter"></table>
    </div>
</div>

<%--<script type="text/jsp" id="updateStatus">
    <input type="checkbox" name="lock" value="{{d.status}}" title="{{d.ss}}" lay-filter="lockDemo" {{ d.status=="1" ? 'checked' : '' }}>
</script>--%>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/sys/organization/organization.js?v=${publishVersion}"></script>
</body>
</html>
