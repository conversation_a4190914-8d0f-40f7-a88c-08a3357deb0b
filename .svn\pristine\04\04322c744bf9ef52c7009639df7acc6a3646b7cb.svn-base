<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.reon.hr.sp.customer.dao.customer.CustContactorMapper">

    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.customer.CustContactor">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="cust_id" jdbcType="BIGINT" property="custId" />
        <result column="contactor" jdbcType="VARCHAR" property="contactor" />
        <result column="tel" jdbcType="VARCHAR" property="tel" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="position" jdbcType="VARCHAR" property="position" />
        <result column="default_flag" jdbcType="INTEGER" property="defaultFlag" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="base_column">
        id,cust_id,contactor,tel,email,position,default_flag,creator,create_time,updater,update_time
    </sql>
    <insert id="batchSaveCustContactor">
        insert into cust_contactor (
        cust_id,
        contactor,
        tel,
        email,
        position,
        default_flag,
        creator
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.custId},
            #{item.contactor},
            #{item.tel},
            #{item.email},
            #{item.position},
            #{item.defaultFlag},
            #{item.creator})
        </foreach>


    </insert>
    <insert id="saveDefaultDate">
        insert into cust_contactor  (
        cust_id,
        contactor,
        tel,
        email,
        position,
        default_flag,
        creator) values(
           #{custId},
            #{contactor},
            #{tel},
            #{email},
            #{position},
            #{defaultFlag},
            #{creator}
        )
    </insert>
    <update id="updateDefaultData">
        update cust_contactor set
        contactor = #{contactor},
        tel = #{tel},
        email = #{email}
            <if test="position != null and position != ''">
                ,position = #{position}
            </if>
         where default_flag = 2 and  cust_id=#{custId}
    </update>
    <update id="updateCustCustomer">
     update cust_contactor set
        contactor = #{contactor},
        tel = #{tel},
        email = #{email},
        position = #{position}
         where id=#{id}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        select <include refid="base_column"/>
        from cust_contactor
        where id = #{id}
    </select>
    <select id="getCustContactorListByCustId" resultType="com.reon.hr.api.customer.vo.CustContactorVo">
        select id,cust_id,contactor,tel,email,position,default_flag from cust_contactor where cust_id =
        #{custContactor.custId}
        <if test="custContactor.contactor !=null and custContactor.contactor!= ''">
            and (contactor like concat('%',#{custContactor.contactor},'%') or tel like
            concat('%',#{custContactor.contactor},'%'))
        </if>
        order by update_time desc,create_time desc
    </select>

</mapper>