/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2022/6/4
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.bill.strategy.concrete;
import com.reon.hr.common.utils.DateUtil;
import org.apache.commons.lang3.ArrayUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ThreeMonthsReceiveStrategy
 *
 * @date 2022/6/4 0:19
 */
public class ThreeMonthsReceiveStrategy extends AbstractReceiveStrategy {

    private static final int THREE_MONTHS = 3;

    //三月收
    public static final int COLLECT_FREQ_THREE_MONTH = 3;

    /**
     * 三月收类型
     */
    public static final int[] COLLECT_FREQ_THREE_MONTH_1 = {1, 4, 7, 10};
    public static final int[] COLLECT_FREQ_THREE_MONTH_2 = {2, 5, 8, 11};
    public static final int[] COLLECT_FREQ_THREE_MONTH_3 = {3, 6, 9, 12};

    private ReceiveInfo receiveInfo;

    public ThreeMonthsReceiveStrategy(ReceiveInfo receiveInfo){
        super(receiveInfo);
        this.receiveInfo = receiveInfo;
    }

    @Override
    protected int getServiceTypeMonths() {
        return THREE_MONTHS;
    }

    @Override
    protected int getReceiveType() {
        return COLLECT_FREQ_THREE_MONTH;
    }

    @Override
    protected int[] getCollectFreqArray() {
        int [] collectFreqArray = null;
        switch (receiveInfo.getCollectFreq()){
            case 1:
                collectFreqArray =COLLECT_FREQ_THREE_MONTH_1;
                break;
            case 2:
                collectFreqArray = COLLECT_FREQ_THREE_MONTH_2;
                break;
            case 3:
                collectFreqArray = COLLECT_FREQ_THREE_MONTH_3;
                break;
            default:
                break;
        }
        return collectFreqArray;
    }

    @Override
    protected boolean matchServiceMonthType() {

        return (ArrayUtils.contains(COLLECT_FREQ_THREE_MONTH_1, DateUtil.yearMonthToMonth(receiveInfo.getCurrentMonth())) && receiveInfo.getCollectFreq() == 1)
                || (ArrayUtils.contains(COLLECT_FREQ_THREE_MONTH_2, DateUtil.yearMonthToMonth(receiveInfo.getCurrentMonth())) && receiveInfo.getCollectFreq() == 2)
                || (ArrayUtils.contains(COLLECT_FREQ_THREE_MONTH_3, DateUtil.yearMonthToMonth(receiveInfo.getCurrentMonth())) && receiveInfo.getCollectFreq() == 3);
    }
}
