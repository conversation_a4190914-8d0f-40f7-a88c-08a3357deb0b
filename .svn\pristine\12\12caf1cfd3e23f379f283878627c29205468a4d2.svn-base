var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect', 'jquery'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        $ = layui.$,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


    getEmployeeContractData("page");

    function getEmployeeContractData(type) {
        table.render({
            id: 'employeeContractGrid',
            elem: '#employeeContractTable',
            data: [],
            method: 'get',
            height: '500px',
            page: true,
            limits: [20, 40, 80],
            defaultToolbar: [],
            toolbar: '#toolbarDemo',
            limit: 20,
            text: {
                none: '暂无数据'
            },
            cols: [[
                {type: 'checkbox', width: '3%', fixed: 'left'},
                {field: 'empContractNo', title: '员工合同编号', width: '8%', align: 'center'},
                {field: 'employeeNo', title: '唯一号', width: '8%', align: 'center'},
                {field: 'employeeName', title: '雇员姓名', width: '8%', align: 'center'},
                {
                    field: 'certType', title: '证件类型', width: '6%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("CERT_TYPE", d.certType)
                    }
                },
                {field: 'certNo', title: '证件号码', width: '8%', align: 'center'},
                {field: 'custNo', title: '客户编号', width: '8%', align: 'center'},
                {field: 'custName', title: '客户名称', width: '8%', align: 'center'},
                {
                    field: 'addReduceStatus', title: '增减员状态', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("EMP_ORDER_STATUS", d.addReduceStatus)
                    }
                },
                {
                    field: 'entryDimissionStatus', title: '入离职状态', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("IN_OUT_STATUS", d.entryDimissionStatus)
                    }
                },
                {field: 'formalSalary', title: '正式工资', width: '8%', align: 'center'},
                {
                    field: 'callFlag', title: '是否需要呼叫', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.callFlag)
                    }
                },
                {
                    field: 'creator', title: '签订操作人', width: '8%', align: 'center', templet: function (d) {
                        return ML.loginNameFormater(d.creator)
                    }
                },
                {
                    field: 'signStatus', title: '签署状态', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("SIGN_STATUS", d.signStatus)
                    }
                },
                {
                    field: 'signDate', title: '签署日期', width: '8%', align: 'center', templet: function (d) {
                        return getSplitDate(d.signDate);
                    }
                },
                {field: 'workMethod', title: '工作制', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("WORKING_SYSTEM", d.workMethod)
                    }},
                {
                    field: 'startDate', title: '劳动合同起始时间', width: '8%', align: 'center', templet: function (d) {
                        return getSplitDate(d.startDate);
                    }
                },
                {
                    field: 'endDate', title: '劳动合同结束时间', width: '8%', align: 'center', templet: function (d) {
                        return getSplitDate(d.endDate);
                    }
                },
                {
                    field: 'tempType', title: '合同版本', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("LABOR_CONTRACT_TEMP", d.tempType)
                    }
                },
                {

                    field: 'empContractType', title: '合同类型', width: '8%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("LABOR_CONTRACT_KIND", d.empContractType)
                    }
                },
                {field: 'termiReason', title: '终止聘用原因', width: '8%', align: 'center'},
                {
                    field: 'dispatchMan', title: '派单客服', width: '8%', align: 'center', templet: function (d) {
                        return ML.loginNameFormater(d.dispatchMan)
                    }
                }
            ]],
            done: function () {

            }
        });
        if (type == 'page') {
            table.reload('employeeContractGrid', {
                url: ML.contextPath + "/customer/employeeContract/getEmployeeContractAllPage",
                where: {paramData: JSON.stringify(serialize("searchForm"))},
                curr: 1
            });
        }
    }


    function getSplitDate(date) {
        if (date != null && date != "" && date != undefined) {
            return date.split(" ")[0]
        } else {
            return "";
        }
    }

    table.on('toolbar(employeeContractFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case  'leadingOutEvent':
                leadingOut(checkStatus);
                break;
        }
    });

    function leadingOut(checkStatus) {
        var param = serialize("searchForm");
        window.location.href = ML.contextPath + "/customer/employeeContract/exportAll?paramData=" + encodeURI(JSON.stringify(param));
    }


    var appd1 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" id = "custNo" type="text" name="custNo" placeholder="客户姓名/编号" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        chckedKey: 'id',
        appd: appd1,
        table: {
            url: ctx + '/customer/customer/getCustomerForEmployeeContract',
            // where: {searchParam: JSON.stringify({employeeName: $("#employeeName").val()})},
            cols: [[
                {type: 'radio'},
                {field: 'id', title: '客户Id', align: 'center', width: '33%'},
                {field: 'custNo', title: '客户编号', align: 'center', width: '33%'},
                {field: 'custName', title: '客户姓名', align: 'center', width: '33%'}
            ]]
        },
        done: function (elem, data) {
            let custName = "";
            let custId = "";
            if (data.data[0]) {
                custName = data.data[0].custName;
                custId = data.data[0].id;
                $("#custName").val(custName);
                $("#custId").val(custId);
                getEmployeeContractData('page')
            }
        }
    });


    var startDate = laydate.render({
        elem: '#startDate'
        , trigger: 'click'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , theme: 'grid'
        , calendar: true
        , format: 'yyyy-MM-dd'
        , showBottom: false
        , done: function (value, date) {
            endDate.config.min = {
                year: date.year,
                month: date.month - 1, //关键
                date: date.date
            }
        }
    });

    var endDate = laydate.render({
        elem: '#endDate'
        , showBottom: false
        , trigger: 'click'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , theme: 'grid'
        , calendar: true
        , format: 'yyyy-MM-dd'
        , done: function (value, date) {
            startDate.config.max = {
                year: date.year,
                month: date.month - 1, //关键
                date: date.date
            };
        }
    });


    var active = {
        reload: function () {
            table.reload('employeeContractGrid', {
                where: {
                    paramData: JSON.stringify(serialize("searchForm")),
                    page: 1 //重新从第 1 页开始
                }
            });
        }
    };
    $('#btnQuery').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    var signDateS = laydate.render({
        elem: '#signDateS'
        , trigger: 'click'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , theme: 'grid'
        , calendar: true
        , format: 'yyyy-MM-dd'
        , showBottom: false
        , done: function (value, date) {
            signDateE.config.min = {
                year: date.year,
                month: date.month - 1, //关键
                date: date.date
            }
        }
    });

    var signDateE = laydate.render({
        elem: '#signDateE'
        , showBottom: false
        , trigger: 'click'
        , min: '2010-01-01'
        , max: '2099-12-12'
        , theme: 'grid'
        , calendar: true
        , format: 'yyyy-MM-dd'
        , done: function (value, date) {
            signDateS.config.max = {
                year: date.year,
                month: date.month - 1, //关键
                date: date.date
            };
        }
    });

    function reload() {
        table.reload('employeeContractGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
                // page: 1 //重新从第 1 页开始
            }
        });
    }

// 双击查看
    table.on('rowDouble(employeeContractFilter)', function (obj) {
        var checkStatus = obj;
        check(checkStatus);
    });

    function check(checkStatus) {
        layer.open({
            type: 2,
            title: '查看',
            area: ['1600px', '850px'],
            maxmin: true,
            offset: 'auto',
            shade: ['0.8', '#393D49'],
            content: ML.contextPath + '/customer/employeeContract/gotoEmployeeContractCheck',
            success: function (layero, index) {
                let body = layer.getChildFrame("body", index);
                var parentIndex = layer.getChildFrame(window.name);
                body.find("#custNo").val(checkStatus.data.custNo);
                body.find("#employeeNo").val(checkStatus.data.employeeNo);
                body.find("#custName").val(checkStatus.data.custName);
                body.find("#name").val(checkStatus.data.employeeName);
                body.find("#certType").val(checkStatus.data.certType);
                body.find("#certType1").val(ML.dictFormatter("CERT_TYPE", checkStatus.data.certType));
                body.find("#certNo").val(checkStatus.data.certNo);
                body.find("#formalSalary").val(checkStatus.data.formalSalary);
                body.find("#callFlag").val(checkStatus.data.callFlag);
                body.find("#probationFlag").val(checkStatus.data.probationFlag);
                body.find("#signStatus").val(checkStatus.data.signStatus);
                body.find("#workMethod").val(checkStatus.data.workMethod);
                body.find("#empContractType").val(checkStatus.data.empContractType);
                body.find("#workMethod").val(checkStatus.data.workMethod);
                if (checkStatus.data.entryTime) {
                    body.find("#entryTime").val(checkStatus.data.entryTime.split(" ")[0]);
                }
                body.find("#tempType").val(checkStatus.data.tempType);
                body.find("#principle").val(checkStatus.data.principle);
                body.find("#remark").val(checkStatus.data.remark);
                body.find("#probaSalary").val(checkStatus.data.probaSalary);
                body.find("#signPlace").val(checkStatus.data.signPlace);
                body.find("#tempPlace").val(checkStatus.data.tempPlace);
                body.find("#fileIdCache").val(checkStatus.data.fileId);
                body.find("#empContractNo").val(checkStatus.data.empContractNo);
                body.find("#orderNo").val(checkStatus.data.orderNo);
            }
        })
    }

    $(document).on('blur', '#receivingManShow', function () {
        var src = $(this).val().trim();
        if (src) {
            var py = new PinYin();
            var target = py.convert(src);
            $('#receivingMan').val(target);
        } else {
            $('#receivingMan').val("");
        }
    });
    $(document).on('blur', '#dispatchManShow', function () {
        var src = $(this).val().trim();
        if (src) {
            var py = new PinYin();
            var target = py.convert(src);
            $('#dispatchMan').val(target);
        } else {
            $('#dispatchMan').val("");
        }
    });


});