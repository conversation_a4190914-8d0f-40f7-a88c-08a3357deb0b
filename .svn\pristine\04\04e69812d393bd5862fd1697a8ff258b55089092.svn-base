<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>税后扣除名单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td{
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }
    </style>
</head>
<body class="childrenBody">
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip">产品类型</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" name="productCode" id="productCode" lay-search>
                                <option value=""></option>
                                <option value="6">大病保险</option>
                                <option value="16">补充医疗保险</option>
                                <option value="18">个人长护险</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip">扣缴义务人</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" name="withholdingAgentNo" id="withholdingAgentNo" lay-search>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="除外客户"><i style="color: red"></i>除外客户</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" name="" id="exclusionCustName"
                                   autocomplete="off" class="layui-input"  placeholder="请选择" value=""/>
                            <input type="text" name="exclusionCustId" id="exclusionCustId" style="display: none;"/>
                        </div>
                    </div>

                </div>
             <div>


                 <!--style="float: right"-->
                 <div class="layui-inline">
                     <button class="layui-btn layui-btn-sm layuiadmin-btn-list " data-type="reload"  id="btnQuery" lay-filter="btnQuery">查询</button>
                     <button class="layui-btn layui-btn-sm layuiadmin-btn-list " type="reset" id="resetBtn">重置</button>
                     <button class="layui-btn layui-btn-sm layuiadmin-btn-list " id="exportAfterTaxDeductions" authURI="/customer/salary/afterTaxDeductions/exportAfterTaxDeductions">导出</button>
                 </div>


             </div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card-body">
    <table class="layui-hide" id="afterTaxDeductions" lay-filter="afterTaxDeductions"></table>
</div>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="add" lay-event="add" authURI="/customer/salary/afterTaxDeductions/gotoAfterTaxDeductionsAddPage">新增</button>
    <button class="layui-btn layui-btn-sm" id="edit" lay-event="edit" authURI="/customer/salary/afterTaxDeductions/gotoAfterTaxDeductionsEditPage">修改</button>
    <button class="layui-btn layui-btn-sm" id="delete" lay-event="delete" authURI="/customer/salary/afterTaxDeductions/deleteAfterTaxDeductions">删除</button>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/withholdingAgent/withholdingAgentNoSelect.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/afterTaxDeductions/afterTaxDeductionsPage.js?v=${publishVersion}"></script>
</body>
</html>
