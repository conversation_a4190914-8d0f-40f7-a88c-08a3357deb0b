package com.reon.hr.api.thirdpart.domain.invoice.nuonuo.vo.callback;

import lombok.Data;

import java.io.Serializable;

@Data
public class InvoiceItem implements Serializable {
    private String itemSpec;
    private String invoiceLineProperty;
    private String favouredPolicyName;
    private String itemUnit;
    private String itemCode;
    private String favouredPolicyFlag;
    private String itemSumAmount;
    private String itemCodeAbb;
    private String itemNum;
    private String itemName;
    private String isIncludeTax;
    private String deduction;
    private String itemSelfCode;
    private String zeroRateFlag;
    private String immediateTaxReturnType;
    private String itemIndex;
    private String itemPrice;
    private String itemTaxAmount;
    private String itemTaxRate;
}