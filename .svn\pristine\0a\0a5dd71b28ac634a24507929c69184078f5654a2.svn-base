/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/6/11
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.servicesitecfg.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IDictionaryWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IServiceSiteCfgWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.SscUpdateLogWrapperService;
import com.reon.hr.api.base.enums.PeopleIndTypeEnum;
import com.reon.hr.api.base.enums.SocialSecurityFundEnum;
import com.reon.hr.api.base.enums.UseStatusTypeEnum;
import com.reon.hr.api.base.enums.ValidFlagEnum;
import com.reon.hr.api.base.listener.ServiceSiteConfigationListener;
import com.reon.hr.api.base.utils.ExcelUtil;
import com.reon.hr.api.base.utils.ServiceSiteCfgExportUtil;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.enums.CommonBooleanTypeEnum;
import com.reon.hr.api.bill.utils.DataSetUtil;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dto.importData.ReceivingManImportVo;
import com.reon.hr.api.customer.dto.importData.ServerEditImportVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.listener.ImportExcelListener;
import com.reon.hr.api.customer.vo.org.OrgAndSupplierVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.file.dubbo.service.rpc.IFileSystemService;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ServiceSiteConfigationController
 * @date 2020/6/11 11:25
 */
@RestController
@RequestMapping("/serviceSiteCfg/")
public class ServiceSiteConfigationController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ServiceSiteConfigationController.class);

    @Autowired
    private ISupplierWrapperService supplierService;

    @Resource(name = "serviceSiteDubboCfgService")
    private IServiceSiteCfgWrapperService serviceSiteCfgService;

    @Autowired
    private IDictionaryWrapperService dictionaryWrapperService;

    @Autowired
    private ISequenceService isequenceService;
    @Autowired
    private IFileSystemService fileSystemService;

    @Resource(name = "batchImportDataDubboService")
    private IBatchImportDataWrapperService iBatchImportDataService;

    @Resource(name = "userDubboService")
    private IUserWrapperService dsUserService;

    @Resource(name = "sscUpdateLogWrapperService")
    private SscUpdateLogWrapperService sscUpdateLogWrapperService;

    Map<String, Map<String, Object>> selectMap = Maps.newHashMap();

    public static final Map<String, String> isApplyInsurFreqMap = Maps.newHashMap();

    public static final String categoryCodeName = "categoryCodeName";
    public static final String applyFundFreqName = "applyFundFreqName";
    public static final String applyInsurFreqName = "applyInsurFreqName";
    public static final String applyIllnessFreqName = "applyIllnessFreqName";
    public static final String billFeeRuleName = "billFeeRuleName";
    public static final String useStatusTypeStr = "useStatusTypeStr";
    public static final String additionFlag = "additionFlag";
    public static final String incumbencyFlagS = "incumbencyFlagS";
    public static final String receptionFlagS = "receptionFlagS";
    public static final String paymentAndDeclare = "paymentAndDeclare";
    public static final String mustPayment = "mustPayment";

    static {
        isApplyInsurFreqMap.put("indTypeCode", categoryCodeName);
        isApplyInsurFreqMap.put("applyFundFreq", applyFundFreqName);
        isApplyInsurFreqMap.put("applyInsurFreq", applyInsurFreqName);
        isApplyInsurFreqMap.put("applyIllnessFreq", applyIllnessFreqName);
        isApplyInsurFreqMap.put("billFeeRule", billFeeRuleName);
        isApplyInsurFreqMap.put("useStatusType", useStatusTypeStr);
        isApplyInsurFreqMap.put("additionFlag", additionFlag);
        isApplyInsurFreqMap.put("incumbencyFlag", incumbencyFlagS);
        isApplyInsurFreqMap.put("receptionFlag", receptionFlagS);
        isApplyInsurFreqMap.put("paymentAndDeclare", paymentAndDeclare);
        isApplyInsurFreqMap.put("mustPayment", mustPayment);
    }

    /**
     * 根据城市获取分公司或供应商
     *
     * @param cityCode
     * @return
     */

    @RequestMapping("getSupplierOrCompany")
    public Object getSupplierOrCompany(@RequestParam(value = "cityCode", required = false) Integer cityCode) {
        List<OrgAndSupplierVo> items = supplierService.getSupplierOrCompany(cityCode);
        return LayuiReplay.success(items);
    }


    /**
     * 服务网点操作信息
     *
     * @return view
     */
    @RequestMapping(value = "/gotoServiceSiteConfigationListPage", method = RequestMethod.GET)
    public ModelAndView gotoServiceSiteConfigationListPage() {
        return new ModelAndView("/serviceSiteCfg/serviceSiteConfigationListPage");
    }

    /**
     * 跳转到 查看/新增/修改/ 页面
     *
     * @return
     */
    @RequestMapping(value = "/addServiceSiteConfigation")
    public ModelAndView addServiceSiteConfigation() {
        return new ModelAndView("/serviceSiteCfg/addServiceSiteConfigation");
    }


    @RequestMapping(value = "/editServiceSiteConfigation")
    public ModelAndView editServiceSiteConfigation() {
        return new ModelAndView("/serviceSiteCfg/editServiceSiteConfigation");
    }

    @RequestMapping(value = "/checkServiceSiteConfigation")
    public ModelAndView checkServiceSiteConfigation() {
        return new ModelAndView("/serviceSiteCfg/editServiceSiteConfigation");
    }

    @RequestMapping(value = "/gotoImportReceivingManView")
    public ModelAndView gotoImportReceivingManView() {
        return new ModelAndView("/serviceSiteCfg/importReceivingManView");
    }

    @RequestMapping(value = "/gotoImportEditView")
    public ModelAndView gotoImportEditView() {
        return new ModelAndView("/serviceSiteCfg/importEdit");
    }

    @RequestMapping(value = "/gotoSscUpdateLogView")
    public ModelAndView gotoSscUpdateLogView(@RequestParam("sscId") Long sscId, Model model) {
        model.addAttribute("sscId", sscId);
        return new ModelAndView("/serviceSiteCfg/sscUpdateLog");
    }

    /**
     * 筛选服务网点
     *
     * @param serviceSiteCfgVo
     * @param page
     * @param limit
     * @return
     */

    @RequestMapping(value = "getServiceSiteConfigationList", method = RequestMethod.GET)
    public Object getServiceSiteConfigationList(ServiceSiteCfgVo serviceSiteCfgVo, Integer page, Integer limit) {
        log.info("当前页{}，每页数{}", page, limit);
        Page<ServiceSiteCfgVo> setVopage = serviceSiteCfgService.findServiceSiteCfgSetPage(serviceSiteCfgVo, page, limit);

        return LayuiReplay.success(setVopage.getTotal(), setVopage.getRecords());
    }

    @RequestMapping(value = "getServiceSiteDictList", method = RequestMethod.GET)
    public Object getServiceSiteDictList(@RequestParam(value = "cityCode") Integer cityCode) {
        List<ServiceSiteCfgVo> serviceSiteCfgSetPage = serviceSiteCfgService.findServiceSiteCfgSetPage(
                ServiceSiteCfgVo.builder()
                        .cityCode(cityCode)
                        .build());
        return LayuiReplay.success(serviceSiteCfgSetPage.stream()
                .filter(vo -> !Objects.equals(vo.getUseStatusType(), UseStatusTypeEnum.DISUSE.getCode()))
                .map(vo -> OrgAndSupplierVo.builder()
                        .id((vo.getServiceSiteCode().length() == 12 ? "COMPANY_" : "SUPPLIER_") + vo.getServiceSiteCode())
                        .name(vo.getServiceSiteName())
                        .orgType(vo.getOrgType())
                        .build())
                .collect(Collectors.toList()));
    }

    @RequestMapping(value = "exportEditTemplate", method = RequestMethod.GET)
    public void exportEditTemplate(HttpServletResponse response) {
        Map<String, Map<String, Object>> selectMap = serviceSiteCfgService.getSelectMap();
        String fileName = "服务网点修改导入模板";
        ExcelUtil<ServiceEditExportVo> excelUtil = new ExcelUtil<>(ServiceEditExportVo.class);
        List<ServiceEditExportVo> serviceSiteCfgExportUtils = Lists.newArrayList();
        Workbook workbook = excelUtil.exportExcel(serviceSiteCfgExportUtils, fileName);
        ServiceSiteCfgExportUtil.export((SXSSFWorkbook) workbook, selectMap);
        try {
            ExcelUtil.closeInfo(response, workbook, fileName);
        } catch (IOException e) {
            log.error("导出失败", e);
        }
    }

    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public Object importEdit(@RequestParam("file") CommonsMultipartFile file) {
        try {
            ImportExcelListener<ServerEditImportVo> importExcelListener = new ImportExcelListener<>();
            EasyExcel.read(file.getInputStream(), ServerEditImportVo.class, importExcelListener).sheet().doRead();

            ImportDataDto<ServerEditImportVo> importDataDto = importExcelListener.getImportDataDto();
            List<ServerEditImportVo> dataList = importDataDto.getDataList();

            if (CollectionUtils.isEmpty(dataList)) {
                return error("请至少填写一行数据");
            }

            if (hasEmptyRequiredFields(dataList)) {
                return error("必填项有空值!");
            }

            if (hasInsufficientFilledColumns(dataList)) {
                return error("非必填项至少填写一个!");
            }

            Map<String, Map<String, Object>> selectMap = serviceSiteCfgService.getSelectMap();
            /*List<ServiceSiteCfgVo> serviceSiteCfgVos = dataList.stream()
                    .map(vo -> mapToServiceSiteCfgVo(vo, selectMap))
                    .collect(Collectors.toList());*/
//            List<ServiceSiteCfgVo> serviceSiteCfgVos = Lists.newArrayList();
            dataList.forEach(vo -> {
                ServiceSiteCfgVo serviceSiteCfgVo = mapToServiceSiteCfgVo(vo, selectMap);
                if (serviceSiteCfgVo != null) {
                    try {
                        serviceSiteCfgVo.setOptType("update");
                        serviceSiteCfgService.saveOrUpdate(serviceSiteCfgVo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
//                    serviceSiteCfgVos.add(serviceSiteCfgVo);
                }
            });

//            serviceSiteCfgService.BatchUpdate(serviceSiteCfgVos);
            return LayuiReplay.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return error("系统异常");
        }
    }

    /**
     * 必填字段为空
     *
     * @param dataList 数据列表
     * @return boolean
     */
    private boolean hasEmptyRequiredFields(List<ServerEditImportVo> dataList) {
        return dataList.stream()
                .map(ServerEditImportVo::getErrorDescription)
                .anyMatch(map -> !map.isEmpty());
    }

    /**
     * 填充列不足
     *
     * @param dataList 数据列表
     * @return boolean
     */
    private boolean hasInsufficientFilledColumns(List<ServerEditImportVo> dataList) {
        return dataList.stream().anyMatch(vo -> {
            int hasValueColumn = (int) Arrays.stream(vo.getClass().getDeclaredFields())
                    .filter(field -> {
                        try {
                            field.setAccessible(true);
                            Object fieldValue = field.get(vo);
                            return StringUtils.isNotBlank(fieldValue != null ? fieldValue.toString() : "");
                        } catch (IllegalAccessException e) {
                            return false;
                        }
                    })
                    .count();
            return hasValueColumn < 4;
        });
    }

    private ServiceSiteCfgVo getQueryParam(ServerEditImportVo vo, Map<String, Map<String, Object>> selectMap) {
        Integer cityCode = (Integer) selectMap.get(IServiceSiteCfgWrapperService.CITY).get(vo.getCityName());
        String serviceSiteCode = (String) selectMap.get(IServiceSiteCfgWrapperService.SERVER).get(vo.getServiceSiteName());
        Integer indType = (Integer) selectMap.get(IServiceSiteCfgWrapperService.IND_TYPE).get(vo.getCategoryCodeName());
        return ServiceSiteCfgVo.builder()
                .cityCode(cityCode)
                .serviceSiteCode(serviceSiteCode)
                .indTypeCode(indType)
                .build();
    }

    /**
     * 映射到服务站点 cfg vo
     *
     * @param vo        VO
     * @param selectMap
     * @return {@link ServiceSiteCfgVo }
     */
    private ServiceSiteCfgVo mapToServiceSiteCfgVo(ServerEditImportVo vo, Map<String, Map<String, Object>> selectMap) {
        Integer cityCode = (Integer) selectMap.get(IServiceSiteCfgWrapperService.CITY).get(vo.getCityName());
        String serviceSiteCode = (String) selectMap.get(IServiceSiteCfgWrapperService.SERVER).get(vo.getServiceSiteName());
        Integer indType = (Integer) selectMap.get(IServiceSiteCfgWrapperService.IND_TYPE).get(vo.getCategoryCodeName());
        Map<String, Object> validMap = selectMap.get(IServiceSiteCfgWrapperService.VALID_FLAG);

        ServiceSiteCfgVo siteCfgVo = serviceSiteCfgService.findBySpecialField(ServiceSiteCfgVo.builder()
                .cityCode(cityCode)
                .serviceSiteCode(serviceSiteCode)
                .indTypeCode(indType).build());
        if (siteCfgVo == null) {
            return null;
        }

        List<SscUpdateLogVo> sscUpdateLogs = Lists.newArrayList();
        ServiceSiteCfgVo.ServiceSiteCfgVoBuilder builder = ServiceSiteCfgVo.builder();
        builder.id(siteCfgVo.getId());

        int updateColumnCount = 0;
        if (StringUtils.isNotBlank(vo.getUseStatusTypeStr())) {
            Integer useStatusType = (Integer) selectMap.get(IServiceSiteCfgWrapperService.USE_STATUS).get(vo.getUseStatusTypeStr());
            if (!Objects.equals(siteCfgVo.getUseStatusType(), useStatusType)) {
                builder.useStatusType(useStatusType);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("useStatusType")
                        .fieldCn("使用状态")
                        .beforeContent(UseStatusTypeEnum.getName(siteCfgVo.getUseStatusType()))
                        .afterContent(vo.getUseStatusTypeStr())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getUseStatusStr())) {
            if (!Objects.equals(siteCfgVo.getUseState(), vo.getUseStatusStr())) {
                builder.useState(vo.getUseStatusStr());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("useState")
                        .fieldCn("使用状态说明")
                        .beforeContent(siteCfgVo.getUseState())
                        .afterContent(vo.getUseStatusStr())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getApplyInsurFreqName())) {
            Integer applyInsurFreq = (Integer) selectMap.get(IServiceSiteCfgWrapperService.APPLY_INSUR_OR_FUND_FREQ).get(vo.getApplyInsurFreqName());
            if (!Objects.equals(siteCfgVo.getApplyInsurFreq(), applyInsurFreq)) {
                builder.applyInsurFreq(applyInsurFreq);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("applyInsurFreq")
                        .fieldCn("社保申报频率")
                        .beforeContent(SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(siteCfgVo.getApplyInsurFreq()))
                        .afterContent(vo.getApplyInsurFreqName())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getApplyFundFreqName())) {
            Integer applyFundFreq = (Integer) selectMap.get(IServiceSiteCfgWrapperService.APPLY_INSUR_OR_FUND_FREQ).get(vo.getApplyFundFreqName());
            if (!Objects.equals(siteCfgVo.getApplyFundFreq(), applyFundFreq)) {
                builder.applyFundFreq(applyFundFreq);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("applyFundFreq")
                        .fieldCn("公积金申报频率")
                        .beforeContent(SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(siteCfgVo.getApplyFundFreq()))
                        .afterContent(vo.getApplyFundFreqName())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getApplyIllnessFreqName())) {
            Integer applyIllnessFreq = (Integer) selectMap.get(IServiceSiteCfgWrapperService.APPLY_ILLNESS_FREQ).get(vo.getApplyIllnessFreqName());
            if (!Objects.equals(siteCfgVo.getApplyIllnessFreq(), applyIllnessFreq)) {
                builder.applyIllnessFreq(applyIllnessFreq);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("applyIllnessFreq")
                        .fieldCn("大病申报频率")
                        .beforeContent(SocialSecurityFundEnum.IsApplyIllnessFreqEnum.getName(siteCfgVo.getApplyIllnessFreq()))
                        .afterContent(vo.getApplyIllnessFreqName())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getBillFeeRuleName())) {
            Integer billFeeRule = (Integer) selectMap.get(IServiceSiteCfgWrapperService.BILL_FEE_RULE).get(vo.getBillFeeRuleName());
            if (!Objects.equals(siteCfgVo.getBillFeeRule(), billFeeRule)) {
                builder.billFeeRule(billFeeRule);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("billFeeRule")
                        .fieldCn("账单收付规则")
                        .beforeContent(SocialSecurityFundEnum.IsBillFeeRuleEnum.getName(siteCfgVo.getBillFeeRule()))
                        .afterContent(vo.getBillFeeRuleName())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getPaymentAndDeclare())) {
            Integer paymentAndDeclare = (Integer) validMap.get(vo.getPaymentAndDeclare());
            if (!Objects.equals(siteCfgVo.getPaymentAndDeclare(), paymentAndDeclare)) {
                builder.paymentAndDeclare(paymentAndDeclare);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("paymentAndDeclare")
                        .fieldCn("是否可以落地发薪和报税")
                        .beforeContent(ValidFlagEnum.getName(siteCfgVo.getPaymentAndDeclare()))
                        .afterContent(vo.getPaymentAndDeclare())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getMustPayment())) {
            Integer mustPayment = (Integer) validMap.get(vo.getMustPayment());
            if (!Objects.equals(siteCfgVo.getMustPayment(), mustPayment)) {
                builder.mustPayment(mustPayment);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("mustPayment")
                        .fieldCn("是否必须落地发薪")
                        .beforeContent(ValidFlagEnum.getName(siteCfgVo.getMustPayment()))
                        .afterContent(vo.getMustPayment())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getAdditionFlag())) {
            Integer additionFlag = (Integer) validMap.get(vo.getAdditionFlag());
            if (!Objects.equals(siteCfgVo.getAdditionFlag(), additionFlag)) {
                builder.additionFlag(additionFlag);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("additionFlag")
                        .fieldCn("是否离职补差")
                        .beforeContent(ValidFlagEnum.getName(siteCfgVo.getAdditionFlag()))
                        .afterContent(vo.getReceptionFlagS())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getReceptionFlagS())) {
            Integer receptionFlags = (Integer) validMap.get(vo.getReceptionFlagS());
            if (!Objects.equals(siteCfgVo.getReceptionFlag(), receptionFlags)) {
                builder.receptionFlag(receptionFlags);
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("receptionFlag")
                        .fieldCn("能否接收同行代理")
                        .beforeContent(ValidFlagEnum.getName(siteCfgVo.getReceptionFlag()))
                        .afterContent(vo.getReceptionFlagS())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getAdditionRule())) {
            if (!Objects.equals(siteCfgVo.getAdditionRule(), vo.getAdditionRule())) {
                builder.additionRule(vo.getAdditionRule());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("additionRule")
                        .fieldCn("离职补差规则")
                        .beforeContent(siteCfgVo.getAdditionRule())
                        .afterContent(vo.getAdditionRule())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getSpecialConsiderations())) {
            if (!Objects.equals(siteCfgVo.getSpecialConsiderations(), vo.getSpecialConsiderations())) {
                builder.specialConsiderations(vo.getSpecialConsiderations());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("specialConsiderations")
                        .fieldCn("特殊注意事项")
                        .beforeContent(siteCfgVo.getSpecialConsiderations())
                        .afterContent(vo.getSpecialConsiderations())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getAddInfo())) {
            if (!Objects.equals(siteCfgVo.getAddInfo(), vo.getAddInfo())) {
                builder.addInfo(vo.getAddInfo());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("addInfo")
                        .fieldCn("增员材料")
                        .beforeContent(siteCfgVo.getAddInfo())
                        .afterContent(vo.getAddInfo())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getDownInfo())) {
            if (!Objects.equals(siteCfgVo.getDownInfo(), vo.getDownInfo())) {
                builder.downInfo(vo.getDownInfo());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("downInfo")
                        .fieldCn("减员材料")
                        .beforeContent(siteCfgVo.getDownInfo())
                        .afterContent(vo.getDownInfo())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getAppendInfo())) {
            if (!Objects.equals(siteCfgVo.getAppendInfo(), vo.getAppendInfo())) {
                builder.appendInfo(vo.getAppendInfo());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("appendInfo")
                        .fieldCn("补缴材料")
                        .beforeContent(siteCfgVo.getAppendInfo())
                        .afterContent(vo.getAppendInfo())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getInjuryEnjoy())) {
            if (!Objects.equals(siteCfgVo.getInjuryEnjoy(), vo.getInjuryEnjoy())) {
                builder.injuryEnjoy(vo.getInjuryEnjoy());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("injuryEnjoy")
                        .fieldCn("工伤享受")
                        .beforeContent(siteCfgVo.getInjuryEnjoy())
                        .afterContent(vo.getInjuryEnjoy())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getAdditionStartMonth())) {
            if (!Objects.equals(siteCfgVo.getAdditionStartMonth(), vo.getAdditionStartMonth())) {
                builder.additionStartMonth(vo.getAdditionStartMonth());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("additionStartMonth")
                        .fieldCn("离职补差起始月")
                        .beforeContent(siteCfgVo.getAdditionStartMonth())
                        .afterContent(vo.getAdditionStartMonth())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getDisFundPolicyNorm())) {
            if (!Objects.equals(siteCfgVo.getDisFundPolicyNorm(), vo.getDisFundPolicyNorm())) {
                builder.disFundPolicyNorm(vo.getDisFundPolicyNorm());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("disFundPolicyNorm")
                        .fieldCn("残障金政策标准")
                        .beforeContent(siteCfgVo.getDisFundPolicyNorm())
                        .afterContent(vo.getDisFundPolicyNorm())
                        .build());
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getCompanyCollectNorm())) {
            if (!Objects.equals(siteCfgVo.getCompanyCollectNorm(), vo.getCompanyCollectNorm())) {
                builder.companyCollectNorm(vo.getCompanyCollectNorm());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("companyCollectNorm")
                        .fieldCn("残障金我司收取标准")
                        .beforeContent(siteCfgVo.getCompanyCollectNorm())
                        .afterContent(vo.getCompanyCollectNorm())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getPolicyLink())) {
            if (!Objects.equals(siteCfgVo.getPolicyLink(), vo.getPolicyLink())) {
                builder.policyLink(vo.getPolicyLink());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("policyLink")
                        .fieldCn("政策链接")
                        .beforeContent(siteCfgVo.getPolicyLink())
                        .afterContent(vo.getPolicyLink())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getPayBaseMethod())) {
            if (!Objects.equals(siteCfgVo.getPayBaseMethod(), vo.getPayBaseMethod())) {
                builder.payBaseMethod(vo.getPayBaseMethod());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("payBaseMethod")
                        .fieldCn("缴纳基本方式")
                        .beforeContent(siteCfgVo.getPayBaseMethod())
                        .afterContent(vo.getPayBaseMethod())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getBigAccountArea())) {
            if (!Objects.equals(siteCfgVo.getBigAccountArea(), vo.getBigAccountArea())) {
                builder.bigAccountArea(vo.getBigAccountArea());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("bigAccountArea")
                        .fieldCn("大户所在区")
                        .beforeContent(siteCfgVo.getBigAccountArea())
                        .afterContent(vo.getBigAccountArea())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (StringUtils.isNotBlank(vo.getSingleAccountCounty())) {
            if (!Objects.equals(siteCfgVo.getSingleAccountCounty(), vo.getSingleAccountCounty())) {
                builder.singleAccountCounty(vo.getSingleAccountCounty());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("singleAccountCounty")
                        .fieldCn("当地单立户可操作区县")
                        .beforeContent(siteCfgVo.getSingleAccountCounty())
                        .afterContent(vo.getSingleAccountCounty())
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (vo.getInsurAddDay() != null) {
            if (!Objects.equals(siteCfgVo.getInsurAddDay(), vo.getInsurAddDay())) {
                builder.insurAddDay(vo.getInsurAddDay());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("insurAddDay")
                        .fieldCn("社保增员截点")
                        .beforeContent(siteCfgVo.getInsurAddDay() + "")
                        .afterContent(vo.getInsurAddDay() + "")
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (vo.getInsurSubDay() != null) {
            if (!Objects.equals(siteCfgVo.getInsurSubDay(), vo.getInsurSubDay())) {
                builder.insurSubDay(vo.getInsurSubDay());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("insurSubDay")
                        .fieldCn("社保减员截点")
                        .beforeContent(siteCfgVo.getInsurSubDay() + "")
                        .afterContent(vo.getInsurSubDay() + "")
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (vo.getCrfAddDay() != null) {
            if (!Objects.equals(siteCfgVo.getCrfAddDay(), vo.getCrfAddDay())) {
                builder.crfAddDay(vo.getCrfAddDay());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("crfAddDay")
                        .fieldCn("公积金增员截点")
                        .beforeContent(siteCfgVo.getCrfAddDay() + "")
                        .afterContent(vo.getCrfAddDay() + "")
                        .build()
                );
                updateColumnCount++;
            }
        }

        if (vo.getCrfSubDay() != null) {
            if (!Objects.equals(siteCfgVo.getCrfSubDay(), vo.getCrfSubDay())) {
                builder.crfSubDay(vo.getCrfSubDay());
                sscUpdateLogs.add(SscUpdateLogVo.builder()
                        .fieldEn("crfSubDay")
                        .fieldCn("公积金减员截点")
                        .beforeContent(siteCfgVo.getCrfSubDay() + "")
                        .afterContent(vo.getCrfSubDay() + "")
                        .build()
                );
                updateColumnCount++;
            }
        }
        // 27个修改列,若无修改则不更新
        if (updateColumnCount == 0) {
            return null;
        }
        sscUpdateLogs.forEach(logs -> {
            logs.setSscId(siteCfgVo.getId());
            logs.setCreator(getSessionUser().getLoginName());
        });
        ServiceSiteCfgVo serviceSiteCfgVo = builder.build();
        serviceSiteCfgVo.setSscUpdateLogs(sscUpdateLogs);
        return serviceSiteCfgVo;
    }

    private Object error(String message) {
        return LayuiReplay.error(message);
    }


    @RequestMapping(value = "exportServiceSiteConfigationList", method = RequestMethod.GET)
    public void exportServiceSiteConfigationList(HttpServletResponse response) {
//        Map<String, List<DictVo>> dicmap = dictionaryWrapperService.findAllDictList();
        Map<Integer, String> peopleIndTypeMap = Arrays.stream(PeopleIndTypeEnum.values())
                .collect(Collectors.toMap(PeopleIndTypeEnum::getCode, PeopleIndTypeEnum::getName));

        Map<String, String> allUserMap = dsUserService.getAllUserMap();
        List<ServiceSiteCfgVo> serviceSiteCfgVoList = serviceSiteCfgService.findServiceSiteCfgSetPage(null);
        String oldOb = JSON.toJSONString(serviceSiteCfgVoList);
        List<ServiceSiteCfgExportVo> serviceSiteCfgExportVoList = JSON.parseArray(oldOb, ServiceSiteCfgExportVo.class);
        serviceSiteCfgExportVoList.forEach(serviceSiteCfgExportVo -> {
            Map<String, Object> keyMap = DataSetUtil.getValue(serviceSiteCfgExportVo, Lists.newArrayList(isApplyInsurFreqMap.keySet()));
            Map<String, Object> valueMap = Maps.newHashMap();
            for (Map.Entry<String, Object> entry : keyMap.entrySet()) {
                int initial_value = DataSetUtil.castObjectToInt(entry.getValue());
                Object confirmed_value = new Object();

                String valueField = isApplyInsurFreqMap.get(entry.getKey());
                switch (valueField) {
                    case categoryCodeName:
                        confirmed_value = serviceSiteCfgExportVo.getCityName() + "-" + peopleIndTypeMap.get(initial_value);
                        break;
                    case applyFundFreqName:
                    case applyInsurFreqName:
                    case applyIllnessFreqName:
                        confirmed_value = SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(initial_value);
                        break;
                    case billFeeRuleName:
                        confirmed_value = SocialSecurityFundEnum.IsBillFeeRuleEnum.getName(initial_value);
                        break;
                    case useStatusTypeStr:
                        confirmed_value = UseStatusTypeEnum.getName(initial_value);
                        break;
                    case additionFlag:
                    case incumbencyFlagS:
                    case receptionFlagS:
                        confirmed_value = CommonBooleanTypeEnum.getName(initial_value);
                        break;
                    case paymentAndDeclare:
                    case mustPayment:
                        confirmed_value = initial_value == 0 ? "是" : "否";
                        break;
                }
                valueMap.put(valueField, confirmed_value);
            }
            DataSetUtil.setValue(serviceSiteCfgExportVo, valueMap);
            serviceSiteCfgExportVo.setReceivingMan(allUserMap.get(serviceSiteCfgExportVo.getReceivingMan()));
        });
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = "服务网点操作信息" + f.format(new Date()) + ".xlsx";
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), "ISO8859-1"));
            EasyExcel.write(response.getOutputStream(), ServiceSiteCfgExportVo.class)
                    .sheet("服务网点操作信息.xlsx")
                    .doWrite(serviceSiteCfgExportVoList);
        } catch (Exception e) {
            log.error("服务网点操作信息导出失败！", e);
        }
    }

    /**
     * 增加/修改服务网点信息
     *
     * @param serviceSiteCfgVo
     * @return
     */
    @RequestMapping(value = "saveOrUpdate", method = RequestMethod.POST)
    public Object saveOrUpdate(@RequestBody ServiceSiteCfgVo serviceSiteCfgVo) {

        if ("update".equals(serviceSiteCfgVo.getOptType())) {
            try {
                if (CollectionUtils.isEmpty(serviceSiteCfgVo.getSscUpdateLogs())) {
                    return LayuiReplay.error("参数异常");
                }
                serviceSiteCfgVo.setUpdater(String.valueOf(getSessionUser().getLoginName()));
                serviceSiteCfgService.saveOrUpdate(serviceSiteCfgVo);
                return LayuiReplay.success();
            } catch (Exception e) {
                log.error("save or update ServiceSiteCfg set error", e);
                return LayuiReplay.error();
            }
        } else if ("add".equals(serviceSiteCfgVo.getOptType())) {
            int cityCode = serviceSiteCfgVo.getCityCode();
            String serviceSiteCode = serviceSiteCfgVo.getServiceSiteCode().split("_")[1];
            String categoryCode = serviceSiteCfgVo.getCategoryCode();
            Integer code = serviceSiteCfgService.findCityCodeAndServiceSiteCodeAndCategoryCode(cityCode, serviceSiteCode, categoryCode);
            if (code == 1) {
                log.error("find cityCode and serviceSiteCode error");
                return LayuiReplay.error("请勿重复插入城市,网点和用户类型");
            } else {
                try {
                    serviceSiteCfgVo.setCreator(String.valueOf(getSessionUser().getLoginName()));
                    serviceSiteCfgService.saveOrUpdate(serviceSiteCfgVo);
                    return LayuiReplay.success();
                } catch (Exception e) {
                    log.error("save or update ServiceSiteCfg set error", e);
                    return LayuiReplay.error();
                }
            }
        }
        return LayuiReplay.error();
    }

    /**
     * 新增ssc修改日志
     *
     * @param sscId SSC 编号
     * @return 新增结果
     */
    @RequestMapping(value = "getUpdateLogs", method = RequestMethod.GET)
    public Object getUpdateLogs(Long sscId) {
        return LayuiReplay.success(sscUpdateLogWrapperService.queryBySscId(sscId));
    }

    /**
     * @Description: 根据 城市 服务网点 人员类型 获取 公积金增减员截止日/社保增减员截止日
     * @Author: chenxiang
     * @Params: * @param serviceSiteCfgVo
     * @Returns:
     * @Since 2020/8/17 16:40
     */
    @RequestMapping(value = "getIncreaseAndDecrease", method = RequestMethod.GET)
    public Object getIncreaseAndDecrease(ServiceSiteCfgVo serviceSiteCfgVo) {
        ServiceSiteCfgVo setVopage = serviceSiteCfgService.getIncreaseAndDecrease(serviceSiteCfgVo);
        return LayuiReplay.success(setVopage);
    }

    @RequestMapping(value = "/serviceSiteConfigationImportExcel", method = RequestMethod.POST)
    public Object serviceSiteConfigationImportExcel(@RequestParam("file") CommonsMultipartFile file) {
        try {
            ServiceSiteConfigationListener serviceSiteConfigationListener = new ServiceSiteConfigationListener();
            EasyExcel.read(file.getInputStream(), ServiceSiteCfgImportVo.class, serviceSiteConfigationListener).sheet().doRead();
            List<ServiceSiteCfgImportVo> serviceSiteCfgImportVoList = serviceSiteConfigationListener.getExcelDataList();
            serviceSiteCfgService.saveServiceSiteCfgImportVo(serviceSiteCfgImportVoList);
        } catch (Exception e) {
            e.printStackTrace();
            return new LayuiReplay<String>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg(), e.getMessage());
        }
        return new LayuiReplay<String>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), 0);
    }

    @RequestMapping(value = "/gotoServiceSiteConfigationImportDataView", method = RequestMethod.GET)
    public ModelAndView gotoServiceSiteConfigationImportDataView() {
        return new ModelAndView("/serviceSiteCfg/serviceSiteConfigationImportData");
    }

    @RequestMapping(value = "/importReceivingManView", method = RequestMethod.POST)
    public LayuiReplay importReceivingManView(@RequestParam("file") MultipartFile file, String remark) {
        String fileId;
        CommonUserVo sessionUser = getSessionUser();
        String batchImportDataNo = isequenceService.getBatchImportDataNo();
        String fileName = file.getOriginalFilename();
        Map<Object, Object> map = Maps.newHashMap();
        try {
            fileId = fileSystemService.uploadFile(file.getBytes(), fileName);
            fileName = fileId + "," + fileName;
            ImportExcelListener<ReceivingManImportVo> importExcelListener = new ImportExcelListener<>();
            EasyExcel.read(file.getInputStream(), ReceivingManImportVo.class, importExcelListener).sheet().doRead();
            ImportDataDto<ReceivingManImportVo> importDataDto = importExcelListener.getImportDataDto();
            importDataDto.setFileId(fileName);
            importDataDto.setImportNo(batchImportDataNo);
            importDataDto.setLoginName(sessionUser.getLoginName());
            importDataDto.setRemark(remark);
            iBatchImportDataService.batchAddReceivingManImport(importDataDto);
        } catch (Exception e) {
            e.printStackTrace();
            return new LayuiReplay<String>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
        }
        map.put("code", 0);
        map.put("data", "");
        return new LayuiReplay<String>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), map);
    }
}


