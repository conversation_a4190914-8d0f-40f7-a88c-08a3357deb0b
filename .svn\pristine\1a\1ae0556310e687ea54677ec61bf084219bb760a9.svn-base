package com.reon.hr.sp.customer.service.impl.cus;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.vo.BillTempletSortVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.sp.customer.dao.cus.BillTempletMapper;
import com.reon.hr.sp.customer.dao.cus.BillTempletSortMapper;
import com.reon.hr.sp.customer.dao.cus.ContractMapper;
import com.reon.hr.sp.customer.entity.cus.BillTemplet;
import com.reon.hr.sp.customer.entity.cus.BillTempletSort;
import com.reon.hr.sp.customer.entity.cus.Contract;
import com.reon.hr.sp.customer.service.cus.BillTempletSortService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BillTempletSortServiceImpl extends ServiceImpl<BillTempletSortMapper, BillTempletSort> implements BillTempletSortService {
    @Autowired
    private BillTempletMapper billTempletMapper;

    @Override
    public int updateBatch(List<BillTempletSort> list) {
        return baseMapper.updateBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<BillTempletSort> list) {
        return baseMapper.batchInsert(list);
    }


    /**
     @return 获取排序好的 templetId
     */
    @Override
    public List<Long> getTempletSortTempletIdList(String contractNo, String loginName, boolean isAll) {
        if (StringUtils.isBlank(contractNo)) return Lists.newArrayList();

        return getTempletSortList(contractNo, loginName, isAll).stream().map(BillTempletSortVo::getTempletId).collect(Collectors.toList());
    }

    /**
     @param contractNo  合同编号
     @param loginName    登录名
     @param isAll      是否使用 项目客服验证  true 查全部,不使用验证 false 使用验证,去合同中验证项目客服
     @return
     */
    @Autowired
    private ContractMapper contractMapper;

    @Override
    public List<BillTempletSortVo> getTempletSortList(String contractNo, String loginName, boolean isAll) {
        List<BillTempletSortVo> returnList = Lists.newArrayList();
        try {
            if (!isAll) {
                Contract contract = new Contract();
                contract.setContractNo(contractNo);
                /** 如果isAll为 false 一定要填写 登录名 这样才可以进入合同查询项目客服进行验证 */
                if (StringUtils.isBlank(loginName)) return Lists.newArrayList();
                contract.setCommissioner(loginName);
                Contract contractForSearch = contractMapper.selectOne(contract);
                if (contractForSearch == null || StringUtils.isBlank(contractForSearch.getContractNo())) {
                    return returnList;
                }
            }
            List<BillTempletSort> billTempletSorts = baseMapper.selectList(new EntityWrapper<BillTempletSort>()
                    .eq(BillTempletSort.COL_CONTRACT_NO, contractNo).orderBy(BillTempletSort.COL_SORT_NO, true)
                    .eq(BillTempletSort.COL_DEL_FLAG, "N")
            );
            List<Long> templetIdList = billTempletSorts.stream().map(BillTempletSort::getTempletId).distinct().collect(Collectors.toList());

            Map<Long, String> templetIdAndNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(templetIdList)) {
                List<BillTempletVo> billTempletNameByTempIdList = billTempletMapper.getBillTempletNameByTempIdList(templetIdList);
                templetIdAndNameMap = billTempletNameByTempIdList.stream().collect(Collectors.toMap(BillTempletVo::getId, BillTempletVo::getTempletName));
            }
            List<BillTemplet> templetsByNotIntTempletIdList = billTempletMapper.getBillTempletNameNotIntTempletIdList(templetIdList, contractNo);

            int sortNo = 0;
            if (CollectionUtils.isNotEmpty(templetsByNotIntTempletIdList)) {
                for (BillTemplet billTemplet : templetsByNotIntTempletIdList) {
                    BillTempletSortVo billTempletSortVo = new BillTempletSortVo();
                    billTempletSortVo.setTempletId(billTemplet.getId());
                    billTempletSortVo.setContractNo(contractNo);
                    billTempletSortVo.setSortNo(sortNo++);
                    billTempletSortVo.setTempletName(billTemplet.getTempletName());
                    returnList.add(billTempletSortVo); // 将对象添加到返回列表中
                }
            }
            if (CollectionUtils.isNotEmpty(billTempletSorts)) {
                for (BillTempletSort billTempletSort : billTempletSorts) {
                    BillTempletSortVo billTempletSortVo = new BillTempletSortVo();
                    billTempletSortVo.setId(billTempletSort.getId());
                    billTempletSortVo.setTempletId(billTempletSort.getTempletId());
                    billTempletSortVo.setContractNo(billTempletSort.getContractNo());
                    billTempletSortVo.setSortNo(sortNo++);
                    billTempletSortVo.setTempletName(templetIdAndNameMap.get(billTempletSort.getTempletId()));
                    returnList.add(billTempletSortVo); // 将对象添加到返回列表中
                }
            }
        } catch (Exception e) {
            log.error("Failed to get templet sort list for contractNo: {}", contractNo, e);
            throw new RuntimeException("获取账单模板排序列表失败", e);
        }
        return returnList;
    }


    @Override
    public Integer saveBillTempletSort(List<BillTempletSortVo> itemCfgDTOList, String loginName) {
        if (itemCfgDTOList == null || itemCfgDTOList.isEmpty()) {
            throw new IllegalArgumentException("itemCfgDTOList cannot be null or empty");
        }
        if (loginName == null || loginName.isEmpty()) {
            throw new IllegalArgumentException("loginName cannot be null or empty");
        }

        List<BillTempletSort> insertList = Lists.newArrayList();
        List<BillTempletSort> updateList = Lists.newArrayList();

        for (BillTempletSortVo item : itemCfgDTOList) {
            BillTempletSort sort = new BillTempletSort();
            BeanUtils.copyProperties(item, sort);
            if (sort.getId() == null) {
                sort.setCreator(loginName);
                insertList.add(sort);
            } else {
                sort.setUpdater(loginName);
                updateList.add(sort);
            }
        }

        try {
            if (!insertList.isEmpty()) {
                baseMapper.batchInsert(insertList);
            }
            if (!updateList.isEmpty()) {
                baseMapper.updateBatch(updateList);
            }
        } catch (Exception e) {
            log.error("Failed to save or update BillTempletSort", e);
            throw new RuntimeException("操作失败", e);
        }

        return itemCfgDTOList.size();
    }


}
