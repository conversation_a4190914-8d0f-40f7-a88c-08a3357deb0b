package com.reon.hr.sp.customer.dao.qiyuesuo;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.qiyuesuo.QysSecretKeyVo;
import com.reon.hr.sp.customer.entity.qys.QysSecretKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface QysSecretKeyMapper extends BaseMapper<QysSecretKey> {
    int deleteByPrimaryKey(Long id);

    QysSecretKey selectByPrimaryKey(Long id);

    int updateByPrimaryKey(QysSecretKey record);

    @Select("select id, bu_clas_id, bu_clas_name, secret_key, creator, create_time, updater,  update_time, del_flag,stamp_name from `reon-customerdb`.qys_secret_key where bu_clas_id = #{buClasId} and del_flag='N'")
    QysSecretKeyVo getQysCategoryData(@Param("buClasId") Long buClasId);


    List<String> getAllSecretKey();

    List<QysSecretKeyVo> getAllCallBackKey(Page page, @Param("vo") QysSecretKeyVo qysSecretKeyVo);

    QysSecretKeyVo getCallBackKeyById(@Param("id") Long id);

    int addCallBackKey(QysSecretKeyVo qysSecretKeyVo);

    int updateByPrimaryKeySelective(QysSecretKeyVo qysSecretKeyVo);

    @Select("select bu_clas_id from `reon-customerdb`.qys_secret_key where bu_clas_name = #{buClassName} and del_flag = 'N'")
    Long getBuClassName(@Param("buClassName") String buClassName);


    List<QysSecretKeyVo> getAllQysSecretKeyVo();

    void batchUpdateByBusClasId(@Param("list") List<QysSecretKeyVo> qysSecretKeyVos);


    QysSecretKeyVo getQysSecretKeyVoBySecretKey(@Param("secretKey") String secretKey);

    @Select("select bu_clas_id,replace_name from `reon-customerdb`.qys_secret_key where bu_clas_name = #{buClassName} and del_flag = 'N'")
    List<QysSecretKeyVo> getDataBuClassName(@Param("buClassName") String buClassName);
}