package com.reon.hr.api.bill.enums;

public enum ExportPracticeReportTypeEnum {
    RECENTLY(1,"新进名单"),
    STOP_PAY(2,"停缴名单"),

    DETAIL(4,"明细单");
//    NORMAL_REMIT(3,"正常汇缴报表"),
    private Integer code;
    private String msg;

    ExportPracticeReportTypeEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsgByCode(Integer code){
        String msg = null;
        if (code != null){
            for (ExportPracticeReportTypeEnum billStatus : ExportPracticeReportTypeEnum.values()) {
                if (billStatus.getCode().equals(code)){
                    msg = billStatus.getMsg();
                }
            }
        }
        return msg;
    }
}
