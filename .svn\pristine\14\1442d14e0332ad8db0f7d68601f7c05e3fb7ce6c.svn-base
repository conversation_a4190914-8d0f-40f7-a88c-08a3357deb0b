package com.reon.hr.sp.customer.dubbo.service.rpc.impl.salary.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ITaxTalWrapperService;
import com.reon.hr.api.base.vo.TaxTabVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.salary.*;
import com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation.NaturalWagesDetailImportDto;
import com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation.TaxDeclarationInformationDto;
import com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation.TaxDeclarationInformationImportDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.enums.salary.TaxTableEnum;
import com.reon.hr.api.customer.vo.batchImport.SalaryEmpDelaryImport;
import com.reon.hr.api.customer.vo.employee.ehr.PeopleNumberInfoVo;
import com.reon.hr.api.customer.vo.export.salary.SalaryBatchDetailExportVo;
import com.reon.hr.api.customer.vo.salary.AnnualSalaryVo;
import com.reon.hr.api.customer.vo.salary.PayEmpBaseVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeVo;
import com.reon.hr.api.customer.vo.salary.pay.*;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.customer.ProducerScopeTypeCustomer;
import com.reon.hr.sp.customer.service.employee.salary.deduction.ISalaryDeductionImportService;
import com.reon.hr.sp.customer.service.employee.salary.pay.ISalaryBatchDetailService;
import com.reon.hr.sp.customer.service.employee.salary.pay.ISalaryPayService;
import com.reon.hr.sp.customer.service.employee.salary.pay.SalaryCalculatorService;
import com.reon.hr.sp.customer.service.employee.salary.taxDeclarationInformation.TaxDeclarationInformationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> guoqian
 * @date 2020/12/25 0025 9:37
 * @title
 * @modify
 */
@Service("salaryPayWrapperService")
public class SalaryPayWrapperServiceImpl implements ISalaryPayWrapperService {
    @Autowired
    private ISalaryPayService salaryPayService;
    @Autowired
    private ISalaryDeductionImportService iSalaryDeductionImportService;
    @Autowired
    private ISalaryBatchDetailService salaryBatchDetailService;
    @Autowired
    private ITaxTalWrapperService taxTalService;
    @Autowired
    private MqMessageSender mqMessageSender;
    @Autowired
    private TaxDeclarationInformationService taxDeclarationInformationService;
    @Autowired
    private SalaryCalculatorService salaryCalculatorService;
    @Override
    public Page<SalaryPayVo> selectSalaryPay(SalaryPaySearchVo vo) {
        return salaryPayService.selectSalaryPay(vo);
    }
    @Override
    public List<SalaryPayVo> selectSalaryPayList(SalaryPaySearchVo vo) {
        return salaryPayService.selectSalaryPayList(vo);
    }

    @Override
    public List<SalaryPayVo> getSalaryImportFileVoList(SalaryPaySearchVo vo) {
        return salaryPayService.getSalaryImportFileVoList (vo);
    }

    @Override
    public List<SalaryPayVo> getSalaryItemList(SalaryPaySearchVo vo) {
        return salaryPayService.getSalaryItemList (vo);
    }

    @Override
    public int saveSalaryPay(SalaryPayVo vo) {
        return salaryPayService.insertSalaryPay(vo);
    }


    @Override
    public int updatePayStatus(SalaryPayVo vo) {
        return salaryBatchDetailService.updatePayStatus(vo);
    }

    @Override
    public void updateSalaryBatchDetailAll(List<Long> ids, String updater) {
        salaryBatchDetailService.updateSalaryBatchDetailAll(ids,updater);
    }

    @Override
    public List<SalaryInfoVo> getEmpByPayId(Long payId, Integer status,Long batchId) {
        return salaryPayService.getEmpByPayId(payId,status,batchId);
    }

    @Override
    public Page<Map<String,Object>> selectSalaryEmp(String keyWord, Long payId, Integer limit, Integer page,Integer status) {
        return salaryPayService.selectSalaryEmp(keyWord,payId,limit,page,status);
    }
    @Override
    public Page<Map<String, Object>> selectPayBatchEmpSalary(String keyWord, List<Long> payIdList, String withholdingAgentNo,Integer status, Integer limit, Integer page) {
        return salaryPayService.selectPayBatchEmpSalary(keyWord, payIdList,withholdingAgentNo,status, limit, page);
    }
    @Override
    public List<Map<String, Object>> selectPayBatchEmpSalary(List<Long> payIdList) {
        return salaryPayService.selectPayBatchEmpSalary(payIdList);
    }
    @Override
    public List<Map<String, Object>> selectSalaryEmp(String keyWord, Long payId,Integer status) {
        return salaryPayService.selectSalaryEmp(keyWord,payId,status);
    }
    @Override
    public List<SalaryBatchDetailExportVo> selectSalaryEmpByBatchIdList(List<Long> batchIdList) {
        return salaryPayService.selectSalaryEmpByBatchIdList(batchIdList);
    }
    @Override
    public Page<Map<String, Object>>  selectSalaryEmpByBatchIdList(List<Long> batchIdList,SalaryPaymentRefundInputVo salaryPaymentRefundInputVo, Integer limit, Integer page) {
        return salaryPayService.selectSalaryEmpByBatchIdList(batchIdList,salaryPaymentRefundInputVo,limit,page);
    }

    @Override
    public List<SalaryEmpTitleVo> selectEmpTitle(Long payId) {
        return salaryPayService.selectEmpTitle(payId);
    }

    @Override
    public List<SalaryEmpTitleVo> getItemNameByItemNo(List<String> itemNo) {
        return salaryPayService.getItemNameByItemNo(itemNo);
    }

    @Override
    public List<PayItemDataVo> judgePayByEmp(Long payId) {
        return salaryPayService.judgePayByEmp(payId);
    }

    @Override
    public int payrollSalary(Long payId,String updater) {
        Map<String, String> mapMq =new HashMap<>();
        int count = payrollSalary(mapMq, payId, updater);
        mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeCustomer.REON_CUSTOMER_COMPUTED_GENERATED_SALARY_COMPLETED, JsonUtil.beanToJson(mapMq));
        return count;
    }
    @Override
    public Map<String, String> batchPayrollSalary(Long payId,String updater) {
        Map<String, String> mapMq =new HashMap<>();
        payrollSalary(mapMq, payId, updater);
        return mapMq;
    }
    public int payrollSalary(Map<String, String> mapMq,Long payId,String updater){
        //先删除
        salaryPayService.deleteEmpSalary(payId,updater);
        //后计算
        SalaryPayAndCategoryVo salaryPayAndCategoryVo=salaryPayService.getBasePayAndCategoryByPayId(payId);
        TaxTabVo taxTabVo = taxTalService.selectTaxTabById(salaryPayAndCategoryVo.getTaxListId());
        mapMq.put("custId", salaryPayAndCategoryVo.getCustId().toString());
        mapMq.put("contractNo", salaryPayAndCategoryVo.getContractNo());
        mapMq.put("templetId", salaryPayAndCategoryVo.getTempletId().toString());
        mapMq.put("billMonth", salaryPayAndCategoryVo.getBillMonth().toString());
        mapMq.put("loginName", updater);
        int count;
        if(taxTabVo!=null){
            if(TaxTableEnum.ANNUAL_LUMP_SUM_BONUS_TAX_RATE_SCHEDULE.getName().equals(taxTabVo.getTaxTabName())){
                count = salaryCalculatorService.annualBonus(payId, updater, taxTabVo, salaryPayAndCategoryVo);
            }else if(TaxTableEnum.LABOR_AND_WAGE_TAX_RATE_SCHEDULE.getName().equals(taxTabVo.getTaxTabName())){
                count = salaryCalculatorService.calculateLaborWages(payId, updater, taxTabVo, salaryPayAndCategoryVo);
            }else if(TaxTableEnum.TAX_RATE_SCHEDULE_OF_ECONOMIC_COMPENSATION.getName().equals(taxTabVo.getTaxTabName())){
                count = salaryCalculatorService.calculateEconomicCompensation(payId, updater, taxTabVo, salaryPayAndCategoryVo);
            }else {
                count = salaryCalculatorService.payrollSalary(payId, updater, taxTabVo, salaryPayAndCategoryVo);
            }
        }else {
            count = salaryCalculatorService.payrollSalary(payId, updater, taxTabVo, salaryPayAndCategoryVo);
        }
        return count;
    }
    @Override
    public void batchComputeEmpSalary(List<SalaryInfoVo> salaryInfoVoList,String updater,List<SalaryCalculationImporDto> salaryCalculationImporDtoList) {
        List<Long> orderlyPayIdList=new ArrayList<>();
        List<Long> disorderPayIdList=new ArrayList<>();
        //根据emp_id分组
        //（1）找出list.size()>1的pay_id集合，这些集合根据sp.tax_month,sp.create_time排序调用接口进行计算
        //（2）不在集合内的开线程调用接口进行计算
        Map<Long, List<SalaryInfoVo>> salaryInfoVoListMap =new HashMap<>();
        if(salaryCalculationImporDtoList!=null){
            List<String> truePayNoList = salaryCalculationImporDtoList.stream().map(SalaryCalculationImporDto::getPayNo).distinct().collect(Collectors.toList());
            salaryInfoVoListMap = salaryInfoVoList.stream().filter(s -> truePayNoList.contains(s.getPayNo())).collect(Collectors.groupingBy(SalaryInfoVo::getEmpId));
        }else {
            salaryInfoVoListMap = salaryInfoVoList.stream().collect(Collectors.groupingBy(SalaryInfoVo::getEmpId));
        }
        for (Long empId:salaryInfoVoListMap.keySet()) {
            List<SalaryInfoVo> infoVoList = salaryInfoVoListMap.get(empId);
            if(infoVoList.size()>1){
                orderlyPayIdList.addAll(infoVoList.stream().map(SalaryInfoVo::getPayId).distinct().collect(Collectors.toList()));
            }else {
                disorderPayIdList.add(infoVoList.get(0).getPayId());
            }
        }
        disorderPayIdList=disorderPayIdList.stream().distinct().collect(Collectors.toList());
        List<Map<String, String>> mapMqList=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(disorderPayIdList)){
            /*ExecutorService executor = Executors.newFixedThreadPool(disorderPayIdList.size());
            for (Long payId:disorderPayIdList) {
                executor.submit(() -> {
                    payrollSalary(payId,updater);
                });
            }
            executor.shutdown();*/
            for (Long payId:disorderPayIdList) {
                Map<String, String> stringStringMap = batchPayrollSalary(payId, updater);
                if(!mapMqList.contains(stringStringMap)){
                    mapMqList.add(stringStringMap);
                }
            }
        }
        List<Long> finalOrderlyPayIdList =orderlyPayIdList.stream().distinct().collect(Collectors.toList());
        Map<Long, SalaryInfoVo> orderlySalaryPayMap = salaryInfoVoList.stream().filter(s -> finalOrderlyPayIdList.contains(s.getPayId())).collect(Collectors.toMap(SalaryInfoVo::getPayId, Function.identity(), (v1, v2) -> v2));
        List<SalaryInfoVo> sortedList = orderlySalaryPayMap.values().stream().sorted((s1, s2) -> {
            if(Objects.equals(s2.getTaxMonth(), s1.getTaxMonth())){
                return Math.toIntExact(s1.getCreateTime().getTime() - s2.getCreateTime().getTime());
            }
            return s1.getTaxMonth()-s2.getTaxMonth();
        }).collect(Collectors.toList());
        for (SalaryInfoVo salaryInfoVo:sortedList) {
            Map<String, String> stringStringMap = batchPayrollSalary(salaryInfoVo.getPayId(), updater);
            if(!mapMqList.contains(stringStringMap)){
                mapMqList.add(stringStringMap);
            }
        }
        if(CollectionUtils.isNotEmpty(mapMqList)){
            for (Map<String, String> mapMq:mapMqList) {
                mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeCustomer.REON_CUSTOMER_COMPUTED_GENERATED_SALARY_COMPLETED, JsonUtil.beanToJson(mapMq));
            }
        }
    }

    @Override
    public List<SupplierSalaryBillVo> getEmpBill(SalaryBillSearchVo vo) {
        return salaryPayService.getEmpBill(vo);
    }

    @Override
    public void updatePay(List<Long> payIds,String updater,Integer confirmFlag) throws Exception {
        try{
            salaryPayService.updatePay(payIds,updater,confirmFlag);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public Page<EmpTaxDeductionVo> getEmpTaxDeduction(EmpTaxDeductionSearchVo vo) {
        return iSalaryDeductionImportService.getEmpTaxDeduction(vo);
    }

    @Override
    public Page<Map<String, Object>> selectSalaryEmpByBatch(String keyWord, Long payId,Long  batchId, Integer limit, Integer page) {
        return salaryPayService.selectSalaryEmpByBatch(keyWord,payId,batchId,limit,page);
    }
    @Override
    public Page<Map<String, Object>> selectSalaryEmpByPayment(String keyWord, Long paymentApplyId, Integer limit, Integer page) {
        return salaryPayService.selectSalaryEmpByPayment(keyWord,paymentApplyId,limit,page);
    }

    @Override
    public List<Map<String, Object>> selectSalaryEmpByPaymentExport(String keyWord, Long paymentApplyId,Integer type,Long batchId) {
        return salaryPayService.selectSalaryEmpByPaymentExport(keyWord, paymentApplyId,type,batchId);
    }

    @Override
    public List<SalaryInfoVo> selectSalaryCalculationByPayNoList(List<String> payNoList) {
        return salaryPayService.selectSalaryCalculationByPayNoList(payNoList);
    }

    @Override
    public void deleteNoPay(List<Long> salaryInfoIdList, SalaryInfoVo salaryInfoVo,List<SalaryInfoVo> salaryInfoVoList) {
        salaryPayService.deleteNoPay(salaryInfoIdList,salaryInfoVo,salaryInfoVoList);
    }

    @Override
    public List<SalaryInfoVo> getSalaryInfoByIds(List<Long> salaryInfoIdList) {
        return salaryPayService.getSalaryInfoByIds(salaryInfoIdList);
    }

    @Override
    public Page<AnnualSalaryImportDto> getAnnualSalaryListPage(Integer page, Integer limit, AnnualSalaryImportDto vo) {
        return salaryPayService.getAnnualSalaryListPage(page,limit,vo);
    }


    @Override
    public List<SalaryPayVo> getPayNameAndCategoryNameByPayIdList(SalaryPayVo salaryPayQueryVo) {
        return salaryPayService.getPayNameAndCategoryNameByPayIdList(salaryPayQueryVo);
    }

    @Override
    public List<SalaryInfoVo> getSalaryIdByBatchIdList(List<Long> batchIdList) {
        return salaryBatchDetailService.getSalaryIdByBatchIdList(batchIdList);
    }
    @Override
    
	public SalaryInfoVo getSalaryIdByBatchIdList(List<Long> batchIdList,String salaryBatchDetailId) {
        return salaryBatchDetailService.getSalaryIdByBatchIdList(batchIdList,salaryBatchDetailId);
    }

    @Override
    public List<SalaryBatchDetailExportVo> getColumnToBeDeletedList(List<SalaryBatchDetailExportVo> salaryPayDetailExportVoList) {
        return salaryPayService.getColumnToBeDeletedList(salaryPayDetailExportVoList);
    }

    @Override
    public List<SalaryPayVo> getPayIdListByList(List<SalaryPayVo> salaryPayQueryVoList) {
        return salaryPayService.getPayIdListByList(salaryPayQueryVoList);
    }

    @Override
    public List<SalaryBatchDetailExportVo> getAddTotalList(List<SalaryBatchDetailExportVo> salaryBatchDetailExportVos) {
        return salaryPayService.getAddTotalList(salaryBatchDetailExportVos);
    }

    @Override
    public int updateSalaryStatus(List<Long> salaryInfoIdList, Integer status, String updater) {
        return salaryPayService.updateSalaryStatus(salaryInfoIdList,status,updater);
    }

    @Override
	public List<SalaryInfoVo> selectSalaryInfoVoByCertNo(String certNo,Long cardId) {
        return salaryPayService.selectSalaryInfoVoByCertNo(certNo,cardId);
    }

    @Override
    public List<SalaryInfoVo> getSalaryInfoVoByEmpIdAndBillMonth(List<Long> empIds, Integer billMonth) {
        return salaryPayService.getSalaryInfoVoByEmpIdAndBillMonth(empIds,billMonth);
    }

    @Override
    public Page<EmpDelayVo> selectEmpDelay(EmpDelaySearchVo vo) {
        return salaryBatchDetailService.selectEmpDelay(vo);    }

    @Override
    public void updateDelay(EmpDelaySearchVo vo) {
        salaryBatchDetailService.updateDelay(vo);
    }

    @Override
    public int updateSalaryInfoStatus(EmpDelaySearchVo vo) {
        return salaryBatchDetailService.updateSalaryInfoStatus(vo);
    }

    @Override
    public Page<Map<String, Object>> getEmpAll(String keyWord, SalaryPaymentRefundInputVo vo, Integer limit, Integer page) {
        return salaryPayService.getEmpAll(keyWord,vo,limit,page);
    }

    @Override
    public List<GeneratedNetEmp> getEmpByBatchIdList(List<Long> batchIdList,Integer documentStatus) {
        return salaryPayService.getEmpByBatchIdList(batchIdList,documentStatus);
    }

    @Override
    public SalaryEmployeeVo getEmployeeByEmployeeNoandcertNo(String employeeNo, String certNo) {
        return salaryBatchDetailService.getEmployeeByEmployeeNoandcertNo(employeeNo,certNo);
    }

    @Override
    public SalaryInfoVo getSalaryByEmpId(Long payId, Long empId) {
        return salaryBatchDetailService.getSalaryByEmpId(payId,empId);
    }

    @Override
    public void updateSalaryDetail(List<SalaryEmpDelaryImport> imports) {
        salaryBatchDetailService.updateSalaryDetail(imports);

    }

    @Override
    public List<SalaryInfoVo> getSalaryInfoIdByPayIds(List<Long> payIds) {
        return salaryPayService.getSalaryInfoIdByPayIds(payIds);
    }

    @Override
    public boolean updateStatusAndFailReasonById(List<SalaryInfoVo> salaryInfoVoList) {
        return salaryPayService.updateStatusAndFailReasonById(salaryInfoVoList);
    }

    @Override
    public int updateByPrimaryKeySelective(SalaryPayVo salaryPay) {
        return salaryPayService.updateByPrimaryKeySelective(salaryPay);
    }

    @Override
    public SalaryPayVo getSalaryPayById(Long id) {
        return salaryPayService.getSalaryPayById(id);
    }

    @Override
    public void updateSalaryPay(SalaryPayVo salaryPayVo) {
        salaryPayService.updateSalaryPay(salaryPayVo);
    }

    @Override
    public void deleteSalaryPay(Long id,String loginName) {
        salaryPayService.deleteSalaryPay(id,loginName);
    }

    @Override
    public List<PayEmpBaseVo> judgePayByPayId(Long payId) {
        return salaryPayService.judgePayByPayId(payId);
    }

    @Override
    public List<PayEmpBaseVo> selectPayEmpBaseListByPayIdList(List<Long> payIdList) {
        return salaryPayService.selectPayEmpBaseListByPayIdList(payIdList);
    }

    @Override
    public Long getAllDataByPayIdAndWithholdingAgentNo(String payId, String withholdingAgentNo) {
        return salaryPayService.getAllDataByPayIdAndWithholdingAgentNo(payId,withholdingAgentNo);
    }

    @Override
    public SalaryPayVo getPayIdByPayNo(String payNo) {
        return salaryPayService.getPayIdByPayNo(payNo);
    }

    @Override
    public Page<SalaryResultImportDto> getSalaryResultListPage(Integer page, Integer limit, SalaryResultQueryDto salaryResultQueryDto) {
        return salaryPayService.getSalaryResultListPage(page,limit,salaryResultQueryDto);
    }

    @Override
    public List<SalaryResultImportDto> getSalaryResultListExport(SalaryResultQueryDto salaryResultQueryDto) {
        return salaryPayService.getSalaryResultListExport(salaryResultQueryDto);
    }

    @Override
    public List<AnnualSalaryVo> getSalaryResultListByAnnual(AnnualSalaryVo salaryVo) {
        return salaryPayService.getSalaryResultListByAnnual(salaryVo);
    }

    @Override
    public Page<IncomeTaxDifferencesImportDto> getIncomeTaxDifferencesListPage(Integer page, Integer limit, IncomeTaxDifferencesImportDto dto) {
        return salaryPayService.getIncomeTaxDifferencesListPage(page,limit,dto);
    }

    @Override
    public List<IncomeTaxDifferencesImportDto> getIncomeTaxDifferencesListExport(IncomeTaxDifferencesImportDto dto, Integer begin) {
        return salaryPayService.getIncomeTaxDifferencesListExport(dto,begin);
    }

    @Override
    public List<SalaryInfoVo> getNumByPeopleNumberInfoVo(PeopleNumberInfoVo vo) {
        return salaryPayService.getNumByPeopleNumberInfoVo(vo);
    }
    @Override
    public PeopleNumberInfoVo getSalaryInfoTotalVo(PeopleNumberInfoVo vo) {
        return salaryPayService.getSalaryInfoTotalVo(vo);
    }

    @Override
    public List<SalaryInfoVo> getSalaryInfoVoList(List<SalaryInfoVo> salaryInfoVoList) {
        return salaryPayService.getSalaryInfoVoList(salaryInfoVoList);
    }

    @Override
    public Integer updateSalaryInfoBankCard(List<Long> salaryInfoIdList,String loginName) {
        return salaryPayService.updateSalaryInfoBankCard(salaryInfoIdList,loginName);
    }

    @Override
    public List<SalaryInfoVo> getByPayIdAndEmpId(List<Long> payIdList, List<Long> employeeIdList) {
        return salaryPayService.getByPayIdAndEmpId(payIdList,employeeIdList);
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingAgentNoList(List<String> withholdingAgentNoList) {
        return salaryPayService.getWithholdingAgentNoList(withholdingAgentNoList);
    }

    @Override
    public List<SalaryPayVo> getBillTempletByPayIdList(List<Long> payIdList) {
        return salaryPayService.getBillTempletByPayIdList(payIdList);
    }

    @Override
    public Map<Long, Integer> getContractTypeByPayIdList(List<Long> payIdList) {
        return salaryPayService.getContractTypeByPayIdList(payIdList);
    }

    @Override
    public Page<SalaryPaymentRefundInputVo> selectSalaryPaymentRefundInputPage(SalaryPaymentRefundInputVo salaryPaymentRefundInputVo, Integer page, Integer limit) {
        return salaryPayService.selectSalaryPaymentRefundInputPage(salaryPaymentRefundInputVo,page,limit);
    }

    @Override
    public int selectSalaryPaymentRefundInputCount(String loginName) {
        return salaryPayService.selectSalaryPaymentRefundInputCount(loginName);
    }

    @Override
    public int saveSalaryPaymentRefundInput(List<SalaryPaymentRefundInputVo> salaryPaymentRefundInputVoList,String loginName) {
        return salaryBatchDetailService.saveSalaryPaymentRefundInput(salaryPaymentRefundInputVoList,loginName);
    }

    @Override
    public Page<TaxDeclarationInformationImportDto> getTaxDeclarationInformationListPage(Integer page, Integer limit, TaxDeclarationInformationDto vo) {
        return taxDeclarationInformationService.getTaxDeclarationInformationListPage(page,limit,vo);
    }

    @Override
    public List<TaxDeclarationInformationImportDto> getTaxDeclarationInformationListExport(TaxDeclarationInformationDto vo) {
        return taxDeclarationInformationService.getTaxDeclarationInformationListExport(vo);
    }
    @Override
    public Page<NaturalWagesDetailImportDto> getTaxDeclarationInformationDetailListPage(Integer page, Integer limit, TaxDeclarationInformationDto vo) {
        return taxDeclarationInformationService.getTaxDeclarationInformationDetailListPage(page,limit,vo);
    }

    @Override
    public List<Long> getPayIdList(String contractNo, Long templetId, Integer billMonth) {
        return salaryPayService.getPayIdList(contractNo,templetId,billMonth);
    }

    @Override
    public List<NaturalWagesDetailImportDto> getTaxDeclarationInformationDetailListExport(TaxDeclarationInformationDto vo) {
        return taxDeclarationInformationService.getTaxDeclarationInformationDetailListExport(vo);
    }

}
