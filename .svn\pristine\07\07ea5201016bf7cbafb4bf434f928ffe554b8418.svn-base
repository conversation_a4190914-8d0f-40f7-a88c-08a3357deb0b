package com.reon.hr.sp.base.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IPolicyFileWrapperService;
import com.reon.hr.api.base.vo.PolicyFileUploadVo;
import com.reon.hr.sp.base.service.sys.IPolicyFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName IPolicyFileWrapperServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/25 0025 13:51
 * @Version 1.0
 */
@Service(value = "policyFileWrapperService")
public class IPolicyFileWrapperServiceImpl implements IPolicyFileWrapperService {

    @Autowired
    private IPolicyFileService iPolicyFileService;
    @Override
    public int savePolicyFile(PolicyFileUploadVo policyFileUploadVo) {
        return iPolicyFileService.savePolicyFile(policyFileUploadVo);
    }

    @Override
    public int updatePolicyFile(PolicyFileUploadVo policyFileUploadVo) {
        return iPolicyFileService.updatePolicyFile(policyFileUploadVo);
    }

    @Override
    public int deleteFileByFileId(Long fileId) {
        return iPolicyFileService.deleteFileByFileId(fileId);
    }

    @Override
    public Page<PolicyFileUploadVo> getImportFileListPage(int page, int limit, PolicyFileUploadVo policyFileUploadVo) {
        return iPolicyFileService.getImportFileListPage(page,limit,policyFileUploadVo);
    }

    @Override
    public  Page<PolicyFileUploadVo> getHomePageFileByLogin( int page, int limit,List<String> scope,String paramData) {
        return iPolicyFileService.getHomePageFileByLogin(page,limit,scope,paramData);
    }

    @Override
    public PolicyFileUploadVo getFileByPosAndOrgCode(String fileId) {
        return iPolicyFileService.getFileByPosAndOrgCode(fileId);
    }

    @Override
    public PolicyFileUploadVo getPolicyFileDataById(Long id) {
        return iPolicyFileService.getPolicyFileDataById(id);
    }

    @Override
    public List<PolicyFileUploadVo> getImportFileList() {
        return iPolicyFileService.getImportFileList();
    }
}
