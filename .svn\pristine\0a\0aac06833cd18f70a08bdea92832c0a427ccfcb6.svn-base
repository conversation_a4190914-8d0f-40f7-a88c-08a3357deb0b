<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.reon.hr.sp.customer.dao.salary.SalaryActualInfoMapper">

    <sql id="BaseSql">
        import_no,id,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,creator,create_time,updater,update_time,del_flag,withholding_agent_no
    </sql>
    <select id="getSalaryActualInfoVoList"
            resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        SELECT import_no,sai.id,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,sai.creator,sai.create_time,sai.updater,sai.update_time,sai.del_flag,wa.withholding_agent_name
        from salary_actual_info sai
        left join withholding_agent wa on wa.withholding_agent_no=sai.withholding_agent_no
        left join employee e on e.id=sai.emp_id
        <where>
            sai.del_flag='N' and wa.del_flag ='N'
            <if test="salaryActualInfoVo.importNo != null and salaryActualInfoVo.importNo !='' ">
                and sai.import_no = #{salaryActualInfoVo.importNo,jdbcType=VARCHAR}
            </if>
            <if test="salaryActualInfoVo.dataMonth != null and salaryActualInfoVo.dataMonth !='' ">
                and sai.data_month = #{salaryActualInfoVo.dataMonth,jdbcType=INTEGER}
            </if>
            <if test="salaryActualInfoVo.certNo != null and salaryActualInfoVo.certNo !='' ">
                and e.cert_no = #{salaryActualInfoVo.certNo}
            </if>
            <if test="salaryActualInfoVo.acctName != null and salaryActualInfoVo.acctName !='' ">
                and sai.acct_name = #{salaryActualInfoVo.acctName}
            </if>
            <if test="salaryActualInfoVo.withholdingAgentNo != null and salaryActualInfoVo.withholdingAgentNo !='' ">
                and sai.withholding_agent_no = #{salaryActualInfoVo.withholdingAgentNo}
            </if>
        </where>
    </select>
    <insert id="saveSalaryActualInfoVoList" parameterType="map">
        insert into salary_actual_info
        (import_no,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,creator,create_time,updater,update_time,withholding_agent_no)
        values
            <foreach collection="list" item="item" separator=",">
                (
                #{item.importNo,jdbcType=VARCHAR}, #{item.empId},#{item.dataMonth},
                #{item.cardNo}, #{item.acctName}, #{item.amount},
                #{item.actualAmountWithheld},#{item.status}, #{item.annotation},
                #{item.referenceNumber},#{item.tips},#{item.openingBank},
                #{item.openingPlace}, #{item.administrativeDate}, #{item.expectationDate},
                #{item.receivingPaymentAccountName}, #{item.receivingPaymentAccountNo},#{item.usage},
                #{item.serviceReferenceNumber}, #{item.type},#{item.channelMark},
                #{item.creator}, #{item.createTime},#{item.updater},
                #{item.updateTime}, #{item.withholdingAgentNo}
                )
            </foreach>
    </insert>
    <select id="findByTaxMonthAndCertNo" resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        select <include refid="BaseSql"/> from salary_actual_info sai where sai.data_month=#{taxMonth,jdbcType=INTEGER} and sai.cert_no=#{certNo,jdbcType=VARCHAR}
    </select>
    <select id="findByDataMonthAndEmpIds" resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        select sai.withholding_agent_no,import_no,sai.id,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,sai.creator,sai.create_time,sai.updater,sai.update_time,sai.del_flag
        from salary_actual_info sai
        where sai.data_month=#{dataMonth,jdbcType=INTEGER} and sai.emp_id in
        <foreach collection="empIds" item="empId" open="(" separator="," close=")">
            #{empId}
        </foreach> and sai.withholding_agent_no in
        <foreach collection="withholdingAgentNoList" item="withholdingAgentNo" open="(" separator="," close=")">
            #{withholdingAgentNo}
        </foreach>
    </select>
    <select id="findByEmpIdsAndWithholdingAgentNos"
            resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        select import_no,sai.id,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,sai.creator,sai.create_time,sai.updater,sai.update_time,sai.del_flag
        from salary_actual_info sai
        where e.id in
        <foreach collection="empIds" item="empId" open="(" separator="," close=")">
            #{empId}
        </foreach> and sai.withholding_agent_no in
        <foreach collection="withholdingAgentNoList" item="withholdingAgentNo" open="(" separator="," close=")">
            #{withholdingAgentNo}
        </foreach>
    </select>
    <delete id="deleteByList">
        delete from salary_actual_info where
        <foreach collection="list" separator="or" item="item" close=")" open="(">
            (card_no = #{item.cardNo} and acct_name = #{item.acctName} and data_month = #{item.dataMonth} and withholding_agent_no = #{item.withholdingAgentNo})
        </foreach>
    </delete>

    <select id="findByEmpIdsAndWithholdingAgentNosAndTaxMonth"
            resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        SELECT a.emp_id,a.tax_month,a.withholding_agent_no,a.accu_add_tax,a.accu_added_tax,a.accu_dedu_tax
        FROM (
                SELECT e.id emp_id,sai.tax_month,sai.withholding_agent_no,sai.accu_add_tax,sai.accu_added_tax,sai.accu_dedu_tax
                FROM salary_actual_info sai
                LEFT JOIN employee e on sai.cert_no=e.cert_no and sai.staff_name=e.`name`
                where e.id is not null and sai.del_flag='N' and sai.item_type=1
                and LEFT(sai.tax_month,4) &gt;= LEFT(#{taxMonth},4) and sai.tax_month &lt;=#{taxMonth}
                and e.id in
                <foreach collection="empIds" item="empId" open="(" separator="," close=")">
                    #{empId}
                </foreach>
                and sai.withholding_agent_no in
                <foreach collection="withholdingAgentNoList" item="withholdingAgentNo" open="(" separator="," close=")">
                    #{withholdingAgentNo}
                </foreach>
                ORDER BY sai.tax_month desc LIMIT ********
            ) a
        GROUP BY a.emp_id,a.withholding_agent_no
    </select>
    <select id="newFindByEmpIdsAndWithholdingAgentNos"
            resultType="com.reon.hr.api.customer.vo.salary.SalaryActualInfoVo">
        select import_no,sai.id,emp_id,data_month,card_no,acct_name,amount,actual_amount_withheld,status,annotation,reference_number,
        tips,opening_bank,opening_place,administrative_date,expectation_date,receiving_payment_account_name,receiving_payment_account_no,
        `usage`,service_reference_number,type,channel_mark,sai.creator,sai.create_time,sai.updater,sai.update_time,sai.del_flag
        from salary_actual_info sai
        where e.id in
        <foreach collection="empIds" item="empId" open="(" separator="," close=")">
            #{empId}
        </foreach> and sai.withholding_agent_no in
        <foreach collection="withholdingAgentNoList" item="withholdingAgentNo" open="(" separator="," close=")">
            #{withholdingAgentNo}
        </foreach>
        <if test="taxMonth != null">
            and sai.tax_month=#{taxMonth}
        </if>
    </select>
</mapper>