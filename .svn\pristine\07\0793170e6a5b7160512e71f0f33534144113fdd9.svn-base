package com.reon.hr.api.customer.utils;

import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.vo.batchImport.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * @suthor:zousheng
 * @date:2021.05.12
 * @Description:批量导入社保账号工具类
 */
public class BatchSocialSecurityImportUtil extends BatchImportExcelCommonUtil {
    private static final Logger logger = LoggerFactory.getLogger(BatchSocialSecurityImportUtil.class);

    /**
     * 记入解析excel导入的数据并写入导入历史表
     *
     * @param socialSecurityExcelDataVo
     * @param batchCommonVo
     * @throws IOException
     */
    public void analyzeExcelData(SocialSecurityExcelDataVo socialSecurityExcelDataVo, BatchCommonVo batchCommonVo) throws Exception {
        checkFile(batchCommonVo.getFile());
        Workbook workbook = getWorkBook(batchCommonVo.getFile());
        batchCommonVo.setWorkbook(workbook);
        ImportDataVo importDataVo = getImportDataVo(batchCommonVo);
        checkImportDateType(batchCommonVo.getDataType(), importDataVo, batchCommonVo.getLoginName(), batchCommonVo.getRemark());
        if (batchCommonVo.getWorkbook() != null) {
            for (int sheetNum = 0; sheetNum < 1; sheetNum++) {
                Sheet sheet = batchCommonVo.getWorkbook().getSheetAt(sheetNum);//获得当前sheet工作表
                if (sheet == null) {
                    continue;
                }
                int firstRowNum = sheet.getFirstRowNum();//获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum();  //循环除了第二行的所有行
                if (lastRowNum > 0) {//判断模板是否为空
                    batchCommonVo.getService().insertSelective(importDataVo);
                    for (int rowNum = firstRowNum + 0; rowNum <= lastRowNum; rowNum++) {
                        if (rowNum > 3001 && batchCommonVo.getDataType() == 11) {
                            //超过3000条数据则停止解析
                           break;
                        }
                        Row row = sheet.getRow(rowNum);//获得当前行
                        if (row == null) {
                            continue;
                        }
                        if (rowNum == 0) {
                            //获取标题信息
                            for (int i = 0; i < row.getLastCellNum(); ++i) {
                                Cell cell = row.getCell(i);

                                String stringCellValue = cell.getStringCellValue();
                                if (stringCellValue.equals("")) {
                                    continue;
                                }
                                String value = stringCellValue.replaceAll("\n", "");
                                if (IBatchImportDataWrapperService.exportSocialSecurityHeaders != null) {
                                    IBatchImportDataWrapperService.exportSocialSecurityHeaders.add(value);
                                }
                                socialSecurityExcelDataVo.getCoheaders().add(value);
                                if (value.equals("错误描述")) {
                                    throw new RuntimeException("导入的模板【错误描述】这一列请删除！");
                                }
                            }
                        }
                        if (rowNum > 0 && checkDetermineImportedDataIsEmpty(row, socialSecurityExcelDataVo.getCoheaders())) {
                            ImportDataLogVo importDataLogVo = getImportDataLogVo(batchCommonVo, rowNum);
                            Map<String, Object> sump = getSump(batchCommonVo, importDataVo, rowNum);
                            String cellValue;
                            //循环当前行
                            for (int cellNum = 0; cellNum < socialSecurityExcelDataVo.getCoheaders().size(); cellNum++) {
                                logger.info("处理中=" + importDataLogVo.getImportNo() + "," + importDataLogVo.getCreator());
                                Cell cell = row.getCell(cellNum);

                                if (cell == null || "".equals(cell.toString())) {
                                    String value = "";
                                    sump.put("" + socialSecurityExcelDataVo.getCoheaders().get(cellNum) + "", "");
                                    value = getString(socialSecurityExcelDataVo.getCoheaders(), cellNum, value);
                                    validationDataType(importDataLogVo, sump, socialSecurityExcelDataVo.getCofamap(), cellNum, value, socialSecurityExcelDataVo.getCoheaders());
                                    continue;
                                }
                                cellValue = getCellValue(socialSecurityExcelDataVo.getCoheaders(), cellNum, cell);
                                if (cellNum == 1) {
                                    cellValue = cellValue.replaceAll(".0", "");
                                }
                                validationDataType(importDataLogVo, sump, socialSecurityExcelDataVo.getCofamap(), cellNum, cellValue, socialSecurityExcelDataVo.getCoheaders());
                            }
                            if (!socialSecurityExcelDataVo.getCofamap().isEmpty()) {
                                failNum++;
                            }
                            successNum++;
                            calculateNumberImport(importDataVo, successNum, failNum, importDataLogVo);
                         //   sump.put("社保公积金账号", sump.get("社保公积金账号").toString().replaceAll(".0", ""));
                            socialSecurityExcelDataVo.getList().add(sump);
                            socialSecurityExcelDataVo.getImportDataLogVoList().add(importDataLogVo);
                            //清除上条数据的错误记入
                            socialSecurityExcelDataVo.getCofamap().clear();
                        }
                    }
                } else {
                    throw new RuntimeException("导入的模板数据为空！");

                }
            }
            batchCommonVo.getWorkbook().close();
        }
        socialSecurityExcelDataVo.setImportNo(importDataVo.getImportNo());
        socialSecurityExcelDataVo.setSuccessNum(importDataVo.getSuccessNum());
        socialSecurityExcelDataVo.setFailNum(importDataVo.getFailNum());
    }

}
