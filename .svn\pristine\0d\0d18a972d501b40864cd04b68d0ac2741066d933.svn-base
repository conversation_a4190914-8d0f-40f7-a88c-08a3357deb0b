/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/6/17 0017
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.changeBase;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdjustDetailVo
 *
 * @date 2021/6/17 0017 10:31
 */
@Data
public class AdjustDetailVo implements Serializable {
    private static final long serialVersionUID = 810859598353246764L;
    private long id;// bigint not null auto_increment comment '主键ID',
    private long adjustId;// bigint comment '调整任务ID',
    private String empName;// varchar(50) comment '姓名',
    private String orderNo;// varchar(50) comment '订单编号',
    private int certType;// int comment '证件类型',
    private String certNo;// varchar(20) comment '证件号码',
    private String custNo;// varchar(50) comment '客户编号',
    private int dealStatus;// int comment '调整状态(1:成功,2:失败)',
    private String failureReason;// varchar(255) comment '失败原因',
    private String remind;// varchar(255) comment '提示信息',
    private String creator;// varchar(50) comment '创建人',
    private Date createTime;// datetime default CURRENT_TIMESTAMP comment '创建时间',
    private String updater;// varchar(50) comment '修改人',
    private Date updateTime;// datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '修改时间',
    private String delFlag;// char(1) default 'N' comment '删除标识(Y:已删除，N:未删除)',
}
