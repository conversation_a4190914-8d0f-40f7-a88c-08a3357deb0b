<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>财务-上传网银到款</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-tab">
    <ul class="layui-tab-title">
        <li class="layui-this">查看网银记录</li>
        <li>上传网银记录</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <iframe id="reinitIframeQuery" style="border: 0px;width: 100%;"
                    src="/bill/financial/gotoNetSilverQueryView"></iframe>
        </div>
        <div class="layui-tab-item">
            <iframe id="reinitIframeUpload" style="border: 0px;width: 100%;"
                    src="/bill/financial/gotoNetSilverUploadView"></iframe>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script>
    layui.use('element', function () {
        var $ = layui.jquery
            , element = layui.element;
    });

    //iframe高度自适应
    function reinitIframe() {
        var iframe = document.getElementById("reinitIframeQuery");
        try {
            var bHeight = iframe.contentWindow.document.body.scrollHeight;
            var dHeight = iframe.contentWindow.document.documentElement.scrollHeight;
            var height = Math.max(bHeight, dHeight);
            iframe.height = height;
        } catch (ex) {
        }
        var iframe = document.getElementById("reinitIframeUpload");
        try {
            var bHeight = iframe.contentWindow.document.body.scrollHeight;
            var dHeight = iframe.contentWindow.document.documentElement.scrollHeight;
            var height = Math.max(bHeight, dHeight);
            iframe.height = height;
        } catch (ex) {
        }
    }

    //刷新高度自适应方法
    window.setInterval("reinitIframe()", 100);
</script>
</body>
</html>
