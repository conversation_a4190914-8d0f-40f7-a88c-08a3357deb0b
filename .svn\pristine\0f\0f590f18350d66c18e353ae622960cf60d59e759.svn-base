package com.reon.hr.sp.bill.entity.banktrans;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("elect_return_order_file")
public class ElectReturnOrderFile {
    /**
     * 主键id
     */
    @TableId( type = IdType.AUTO)
    private Long id;
    /**
     * 打印Id
     */
    private String taskid;


    private String serviceCode;

    private String acctNo;

    /**
     * 回单编号
     */
    private String reOrderNo;


    /**
     * 开始日期
     * 是否必输: Y
     * 查询交易时间段的开始日期
     */
    @TableField(exist = false)
    private String beginDate;
    /**
     * 结束日期
     * 是否必输: Y
     * 查询交易时间段的结束日期
     */
    @TableField(exist = false)
    private String endDate;
    private String trxSeq;

    private String yurRef;
    /**
     * 下载路径
     */
    private String returl;

    private Date createTime;

    private Date updateTime;

}
