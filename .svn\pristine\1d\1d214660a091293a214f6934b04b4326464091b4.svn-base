var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','upload','element'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, tableSelect = layui.tableSelect
        ,upload = layui.upload,element = layui.element;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;
    // 记录当前窗口的layer.index
    var layerIndex = layer.index;
    element.on('tab(paymentApplyTabFilter)',function (data) {
        if(data.index == 1){
            var pid = $('#pid').val();
            if(pid){
                $("#workFlowImg").attr("src","/workflow/workflowGraph?pid="+pid);
                ML.ajax("/workflow/getWorkflowAuditLogList",{"pid":pid},function (res) {
                    var commentData = res.data;
                    table.render({
                        id: 'paymentApplyFlowTable',
                        elem: '#paymentApplyFlowTable',
                        data :commentData,
                        cols :[[
                            {title:'序号',type:'numbers'}
                            ,{field: 'userId',title:'处理人',align:'center',templet:function (d) {
                                    return ML.loginNameFormater(d.userId);
                                }}
                            ,{field: 'auditType',title:'处理类型',align:'center',templet: function(d){
                                    return ML.dictFormatter("AUDIT_TYPE",d.auditType);
                                }}
                            ,{field: 'comment',title:'审批意见',align:'center'}
                            ,{field: 'createTime',title:'审批时间',align:'center'}
                        ]]
                    });
                },'GET');
                /** 显示 当前审批人 */
                showNowApprover(pid)
            }

        }
    });

    /** 显示当前审批人 */
    function showNowApprover(pid) {
        ML.ajax("/workflow/getCurrentApprover", {"pid": pid}, function (res) {
            var curApp = res.data;
            if (curApp) {
                $("#currentItem").removeClass("layui-hide");
                document.getElementById('currentApproverName').innerHTML = curApp.name;
                $("#currentApproverAssignee").val(ML.loginNameFormater(curApp.assignee));
            }
        }, 'GET')
    }

    $(function () {

        //转换字典数据
        $("#payCom").attr("disabled", "disabled");
        //支付大类
        if ($("#payKind1").val()) {
            payKindV = $("#payKind1").val();
            $("#payKind").append($("<option/>").text(ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", payKindV)).attr("value", payKindV).attr("selected", "selected"));
        }
        $("#payKind").attr("disabled", "disabled");
        //支付子类
        if ($("#payType1").val()) {
            payTypeV = $("#payType1").val();
            $("#payType").append($("<option/>").text(ML.dictFormatter("PAYMENT_SUBCLASS", payTypeV)).attr("value", payTypeV).attr("selected", "selected"));
        }
        $("#payType").attr("disabled", "disabled");
        //支付类型
        if ($("#payMethod1").val()) {
            payMethodV = $("#payMethod1").val();
            $("#payMethod").append($("<option/>").text(ML.dictFormatter("MODE_OF_PAYMENT", payMethodV)).attr("value", payMethodV).attr("selected", "selected"));
        }
        $("#payMethod").attr("disabled", "disabled");
        //支付银行
        if ($("#bankType1").val()) {
            bankTypeV = $("#bankType1").val();
            $("#bankType").append($("<option/>").text(ML.dictFormatter("BANK", bankTypeV)).attr("value", bankTypeV).attr("selected", "selected"));
        }
        if ($("#payDetailType1").val()) {
            payDetailTypeV = $("#payDetailType1").val();
            $("#payDetailType").val(payDetailTypeV);
        }
        $("#bankType").attr("disabled", "disabled");
        $("#payCom").attr("disabled", "disabled");
        $("#payDetailType").attr("disabled", "disabled");
        $(setTimeout(function () {
            if ($('#fileId').val() != null) {
                $.ajaxData.getFileName($('#fileId').val(),"check")
            }
        }, 50));
    });


    //关闭弹窗
    $(document).on('click', '#close', function () {
        layer.close(layer.index); //它获取的始终是最新弹出的某个层，值是由layer内部动态递增计算的
    });
    $(document).on('click', '#commit', function () {
        var comment = $("#comment").val();
        var taskId=$('#taskId').val();
        ML.ajax('/workflow/submitTask',
            {'taskId':taskId,'comment':comment,'bizType':'payment_apply'}
            ,function (res) {
                layer.msg(res.msg);
                if (res.code == 0) {
                    layer.close(layerIndex);
                }
            },'POST');
    });

    $(document).on('click', '#rejected', function () {
        //审批意见
        var comment = $("#comment").val();
        //行id
        var bizId = $('#id').val();
        //任务id
        var taskId=$('#taskId').val();
        //流程id
        var pid=$('#pid').val();
        if (ML.isNotEmpty(comment) && length > 255) {
            layer.msg("审批意见过长,请缩短审批意见!");
        } else {
            ML.ajax('/workflow/rejectTask',
                {'pid': pid, 'bizId': bizId, 'taskId': taskId, 'comment': comment, 'bizType': 'payment_apply'}
                , function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layer.close(layerIndex);
                    }
                }, 'POST');
        }
    });

    // //支付抬头  查出所有自有公司
    // $(document).ready(function () {
    //     $("#custId").attr("disabled","disabled");
    //     $("#custName").attr("disabled","disabled");
    //     var payComList = [];
    //     $.ajax({
    //         type: "GET",
    //         param: {"payCom1": $("#payCom1").val()},
    //         url: ctx + "/customer/contract/orgList",
    //         dataType: 'json',
    //         success: function (data) {
    //             payComList = [];
    //             payComList = data.data;
    //             $.each(payComList, function (i, item) {
    //                 if (payCom1.defaultValue != item.orgCode) {
    //                     $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
    //                 } else {
    //                     $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode).attr('selected', 'selected').attr("disabled", "disabled"));
    //                 }
    //             });
    //             form.render('select');
    //         },
    //         error: function (data) {
    //             console.log("error");
    //         }
    //     });
    // });

    $(document).ready(function () {
        form.render('select');
    })


//隐藏提交文件按钮 删除文件按钮
    $("#paymentApplyUpload").attr("style", "display:none");
    $(".deleteFile").attr("style", "display:none");




    // // 搜索条件  客户下拉列表框
    // var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // // 客户下拉数据表格
    // tableSelect.render({
    //     elem: '#custName',
    //     checkedKey: 'id',
    //     appd: appd,
    //     table: {
    //         url: ML.contextPath + '/customer/contract/getCustomerByAll',
    //         cols: [[
    //             {type: 'radio'}
    //             , {field: 'id', title: '客户ID', align: 'center'}
    //             , {field: 'custNo', title: '客户编号', align: 'center'}
    //             , {field: 'custName', title: '客户名称', align: 'center'}
    //         ]]
    //     },
    //     done: function (elem, data) {
    //         var NEWJSON = [];
    //         var id = '';
    //         var name='';
    //         layui.each(data.data, function (index, item) {
    //             NEWJSON.push(item.custName)
    //             custNo = item.custNo;
    //             id = item.id;
    //             name=item.custName;
    //         });
    //         // 回填值
    //         elem.val(NEWJSON.join(","));
    //         $("#custId").val(id);
    //         $("#custName").val(name);
    //         $("#custName").text(name);
    //
    //     }
    // });



    // $(document).ready(function () {
    //     var id = $("#custId").val();
    //     if ($("#custId").val()) {
    //         $.ajax({
    //             type: 'GET',
    //             url: ML.contextPath + '/customer/customer/findById',
    //             data: {"id": id},
    //             dataType: 'json',
    //             success: function (data) {
    //                 $("#custName").val(data.data.custName);
    //             },
    //             error: function (resp, textStatus, errorThrown) {
    //                 console.log("ajax请求姓名错误!!");
    //             }
    //         });
    //
    //     }
    // });

});