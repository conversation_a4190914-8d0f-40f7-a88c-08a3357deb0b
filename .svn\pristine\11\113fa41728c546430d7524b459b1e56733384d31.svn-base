package com.reon.ehr.sp.sys.service.order;


import com.reon.ehr.api.sys.vo.order.EhrEmployeeOrderLogVo;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeOrderVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2023/7/4.
 */
public interface IEpEmployeeOrderLogService {
    public String getListByOrderIdAndOprType(Long orderId, Integer oprType);

    List<EhrEmployeeOrderLogVo> getListByEhrEmployeeOrderVoList(List<EhrEmployeeOrderVo> ehrEmployeeOrderVoList);

    Map<String,String> getRemarkStrMapByEhrEmployeeOrderVoList(List<EhrEmployeeOrderVo> ehrEmployeeOrderVoList,Boolean isAll);
}
