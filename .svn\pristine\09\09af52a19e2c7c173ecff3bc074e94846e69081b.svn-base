package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IinsuranceGroupWrapperService;
import com.reon.hr.api.base.vo.InsuranceGroupRatioVo;
import com.reon.hr.api.customer.dto.customer.BatchBillStartMonthDTO;
import com.reon.hr.api.customer.dto.customer.BatchEditBillStartMonthDTO;
import com.reon.hr.api.customer.dto.customer.BatchEditProdExpireMonthDTO;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IPersonOrderEditWrapperService;
import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.sp.customer.service.employee.IPersonOrderEditService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("personOrderEditDubboService")
public class PersonOrderEditWrapperServiceImpl implements IPersonOrderEditWrapperService {

    @Autowired
    private IPersonOrderEditService service;


    @Autowired
    private IinsuranceGroupWrapperService groupWrapperService;


    @Override
    public Page<PersonOrderEditVo> getListPage(Integer page, Integer limit, PersonOrderEditVo orderVo, String loginName) {
        return service.getListPage(page, limit, orderVo, loginName);
    }

    @Override
    public int personOrderEditSave(EmployeeOrderVo vo, String loginName, Integer roleType) {
        if (CollectionUtils.isNotEmpty(vo.getInsurances())) {
            for (int i = 0; i < vo.getInsurances().size(); i++) {
                OrderInsuranceCfgVo orderInsuranceVo = vo.getInsurances().get(i);
                if (orderInsuranceVo != null && StringUtils.isNotBlank(orderInsuranceVo.getGroupCode()) && StringUtils.isNotBlank(orderInsuranceVo.getRatioCode())) {
                    InsuranceGroupRatioVo groupRatioVo = groupWrapperService.getInsuranceGroupRatio(orderInsuranceVo.getGroupCode(), null, orderInsuranceVo.getRatioCode());
                    orderInsuranceVo.setGroupRatioId(groupRatioVo.getId());
                    vo.getInsurances().set(i, orderInsuranceVo);
                }
            }
        }
        return service.savePersonOrderChange(vo, loginName, roleType);
    }

    @Override
    public int handleOrderServiceChange(NoInsuranceVo vo, String loginName) throws Exception {
        return service.handleOrderServiceChange(vo, loginName);
    }

    @Override
    public int confirmOrderChange(ConfirmOrderChangeVo vo, String loginName) {
        return service.handleConfirmOrderChange(vo, loginName);
    }

    @Override
    public EmployeeOrderChangeVo searchByOrderNo(String orderNo, Integer chgType) {
        return service.searchByOrderNo(orderNo, chgType);
    }

    @Override
    public void batchPersonOrderEditSave(ImportDataDto<BatchBillStartMonthDTO> importDataDto) {
        service.batchPersonOrderEditSave(importDataDto);
    }

    @Override
    public EmployeeOrderChangeVo selectEmployeeOrderChange(String orderNo, List<Integer> chgMethod, Integer chgStatus, Integer chgType) {
        return service.selectEmployeeOrderChange(orderNo,chgMethod,chgStatus,chgType);
    }

    @Override
    public void handleRollBackDataByChangeIds(List<Long> changeIds) {
         service.handleRollBackDataByChangeIds(changeIds);
    }

    @Override
    public void batchEditProdExpireMonth(ImportDataDto<BatchEditProdExpireMonthDTO> importDataDto) {
        service.batchEditProdExpireMonth(importDataDto);
    }

    @Override
    public void batchEditBillStartMonth(ImportDataDto<BatchEditBillStartMonthDTO> importDataDto) {
        service.batchEditBillStartMonth(importDataDto);
    }
}
