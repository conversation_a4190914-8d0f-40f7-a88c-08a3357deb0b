package com.reon.hr.api.bill.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PerBillItemDto implements Serializable, Comparable<PerBillItemDto> {

    private Long perBillId;

    private Integer productCode;

    private String cityName;

    private BigDecimal total;

    private BigDecimal comBase;

    private BigDecimal comRatio;

    private BigDecimal comAmt;

    private BigDecimal indBase;

    private BigDecimal indRatio;

    private BigDecimal indAmt;

    public Long getPerBillId() {
        return perBillId;
    }

    public void setPerBillId(Long perBillId) {
        this.perBillId = perBillId;
    }

    public Integer getProductCode() {
        return productCode;
    }

    public void setProductCode(Integer productCode) {
        this.productCode = productCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getComBase() {
        return comBase;
    }

    public void setComBase(BigDecimal comBase) {
        this.comBase = comBase;
    }

    public BigDecimal getComRatio() {
        return comRatio;
    }

    public void setComRatio(BigDecimal comRatio) {
        this.comRatio = comRatio;
    }

    public BigDecimal getComAmt() {
        return comAmt;
    }

    public void setComAmt(BigDecimal comAmt) {
        this.comAmt = comAmt;
    }

    public BigDecimal getIndBase() {
        return indBase;
    }

    public void setIndBase(BigDecimal indBase) {
        this.indBase = indBase;
    }

    public BigDecimal getIndRatio() {
        return indRatio;
    }

    public void setIndRatio(BigDecimal indRatio) {
        this.indRatio = indRatio;
    }

    public BigDecimal getIndAmt() {
        return indAmt;
    }

    public void setIndAmt(BigDecimal indAmt) {
        this.indAmt = indAmt;
    }

    @Override
    public int compareTo(PerBillItemDto o) {
        if (o != null && o.getProductCode() != null && productCode != null) {
            if (o.productCode.equals(-1)) {
                return -1;
            } else if (productCode > o.productCode) {
                return 1;
            } else if (productCode < o.productCode) {
                return -1;
            }
        }
        return 0;
    }
}
