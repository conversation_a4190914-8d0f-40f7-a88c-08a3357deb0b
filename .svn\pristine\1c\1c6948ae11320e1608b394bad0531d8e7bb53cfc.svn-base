package com.reon.hr.sp.customer.service.employee.salary.salaryImport;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.importData.BatchImportSalaryDto;
import com.reon.hr.api.customer.dto.importData.BatchImportSalaryUpdateDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.vo.batchImport.ImportDataVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @datetime 2022/9/6 0006 10:18
 * @version: 1.0
 */
public interface ISalaryBatchImportService {

    Page<ImportDataVo> getBatchImportSalaryDataListPage(Integer page, Integer limit, Map<String, Object> map);

    void batchAddSalaryImport(ImportDataDto<BatchImportSalaryDto> importDataDto);
    void updateSalarybatchImport(ImportDataDto<BatchImportSalaryUpdateDto> importDataDto);

    List<Map<String, Object>> getImportTxtByImportNo(String importNo);
    List<Map<String, Object>> getImportDataLogSuccessMap(String importNo);

}
