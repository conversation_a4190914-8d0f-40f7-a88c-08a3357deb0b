package com.reon.hr.sp.customer.service.cus;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.customer.vo.SocialSysMandatoryInfoVo;
import com.reon.hr.sp.customer.entity.cus.SocialSysMandatoryInfo;

public interface SocialSysMandatoryInfoService extends IService<SocialSysMandatoryInfo> {


      Map<String, SocialSysMandatoryInfoVo> getDataByOrderNoList(List<String> orNoList) ;

    int updateBatch(List<SocialSysMandatoryInfo> list);

    int batchInsert(List<SocialSysMandatoryInfo> list);

    SocialSysMandatoryInfoVo getSocialSysMandatoryInfo(String orderNo);
}
