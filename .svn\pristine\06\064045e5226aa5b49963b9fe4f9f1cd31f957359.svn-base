/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/12/29
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.customer.controller.salary.send;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.salaryImport.ISalaryImportWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.send.IMailSendService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.send.ISalarySendQueryWrapperService;
import com.reon.hr.api.customer.thread.SalarySendQueryTask;
import com.reon.hr.api.customer.vo.salary.PayRelativeImportVo;
import com.reon.hr.api.customer.vo.salary.SendSalaryInfoMailVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayVo;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalarySendQueryController
 *
 * @date 2020/12/29 14:58
 */
@RestController
@RequestMapping(value = "/customer/salary/send")
public class SalarySendQueryController extends BaseController {

    @Autowired
    private ISalarySendQueryWrapperService iSalarySendQueryWrapperService;

    @Autowired
    private IMailSendService iMailSendService;

    @Autowired
    private ISalaryImportWrapperService iSalaryImportWrapperService;

    /**
     * 跳转到薪资发送查询页面
     *
     * @return
     */
    @RequestMapping(value = "/gotoSalarySendQueryView", method = RequestMethod.GET)
    public ModelAndView gotoSalarySendQueryView() {
        return new ModelAndView ("/customer/salary/send/salarySendQuery");
    }


    /**
     * 查询薪资发送邮件数据分页
     *
     * @param sendSalaryInfoMailVo
     * @param page
     * @param limit
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSendSalaryInfoMailVoListPage", method = RequestMethod.GET)
    public Object getSendSalaryInfoMailVoListPage(SendSalaryInfoMailVo sendSalaryInfoMailVo,
                                                  Integer page, Integer limit, HttpSession session) {
        List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>) session.getAttribute(Constants.SESSION_ORG_POSITION);
        sendSalaryInfoMailVo.setUserOrgPositionDtoList(userOrgPositionDtoList);
        Page<SendSalaryInfoMailVo> sendSalaryInfoMailVoPage = iSalarySendQueryWrapperService.getSendSalaryInfoMailVoListPage (sendSalaryInfoMailVo, page, limit);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), sendSalaryInfoMailVoPage.getTotal (), sendSalaryInfoMailVoPage.getRecords ());
    }


    /**
     * 批量发送邮件
     *
     * @param sendSalaryInfoMailVo
     * @return
     */
    @PostMapping(value = "/batchSend")
    @RepeatSubmit
    public Object batchSend(@RequestBody SendSalaryInfoMailVo sendSalaryInfoMailVo) {
        SalarySendQueryTask salarySendQueryTask = new SalarySendQueryTask (sendSalaryInfoMailVo,iSalaryImportWrapperService,
                iSalarySendQueryWrapperService,iMailSendService);
        iSalarySendQueryWrapperService.SE_FORK_JOIN_POOL.execute (salarySendQueryTask);
        return new LayuiReplay<SendSalaryInfoMailVo> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), "发送中。。。");
    }
}
