package com.reon.hr.api.customer.enums;

public enum CertType{
    ID_CARD(1,"身份证"),AR_MYMAN(2,"军人证"),HONG_KONG_MACAU_IDENTITY_CARD(3,"港澳身份证")
        ,TAIWAN_COMPATRIOTS_CERTIFICATE(4,"台胞证"),PASSPORT(5,"护照"),<PERSON><PERSON><PERSON>(6,"其他"),
    TAIWAN_RESIDENCE_PERMIT(7,"台湾居住证"),HONG_KONG_MACAU_RESIDENCE_PERMIT(8,"港澳居住证");
    int code;
    String name;
    CertType(int code,String name){
        this.code = code;
        this.name = name;
    }

    public static CertType getTypeByName(String name){
        CertType certType=CertType.OTHER;
        for(CertType cert:CertType.values()){
            if (cert.getName().equals(name)) {
                certType = cert;
            }

        }
        return certType;
    }

    public static String getNameByCode(Integer code){
        for(CertType cert:CertType.values()){
            if (cert.getCode()== code) {
                return cert.getName();
            }
        }
        return "其他";
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
