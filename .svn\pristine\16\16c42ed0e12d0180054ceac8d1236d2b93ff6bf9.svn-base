package com.reon.hr.sp.customer.dao.changeBase;

import com.reon.hr.api.customer.vo.changeBase.AdjustCfgVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdjustCfgMapper {
    void insertAdjustCfg(List<AdjustCfgVo> adjustCfgVoList);

    void upDataAdjustCfg(AdjustCfgVo adjustCfgVo);

    List<AdjustCfgVo> selectAdjustCfgByAdjustId(long adjustId);

    void deleteAdjustCfgByAdjustId(Long adjustId);
}
