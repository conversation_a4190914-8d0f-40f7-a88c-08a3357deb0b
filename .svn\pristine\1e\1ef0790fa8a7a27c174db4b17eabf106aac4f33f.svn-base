<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.supContractArea.SupContractAreaRelativeQuoteMapper">

    <delete id="deleteAll">
        truncate sup_contract_area_relative_quote
    </delete>

    <select id="getCountByContractAreaNo" resultType="int">
        select count(1) from sup_contract_area_relative_quote where contract_area_no =#{contractAreaNo}
    </select>
</mapper>
