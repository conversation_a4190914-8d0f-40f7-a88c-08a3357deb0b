package com.reon.hr.api.report.vo;

import com.reon.hr.api.report.enums.FinancialAccountsReceivableReportEnum;
import com.reon.hr.api.report.utils.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinancialAccountsReceivableReportVo implements Serializable {
    /**
     * 客户编号
     */
    private String custNo;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 所属行业
     */
    private Integer industryType;
    /**
     * 客户账单名称
     */
    private String templetName;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 签单方分公司抬头
     */
    private String signComTitle;
    /**
     * 签定日期
     */
    private Date startDate;
    /**
     * 合同起始日期
     */
    private Date contractStartDate;
    /**
     * 合同结束日期
     */
    private Date endDate;
    /**
     * 签单地
     */
    private String signPlace;
    /**
     * 销售
     */
    private String seller;
    /**
     * 客服专员
     */
    private String commissioner;
    /**
     * 合同类型
     */
    private Integer contractType;
    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 增值税
     */
    private BigDecimal valTax;
    /**
     * 社保
     */
    private BigDecimal socialSecurityAmt;
    /**
     * 公积金
     */
    private BigDecimal accumulationFundAmt;
    /**
     * 存档费
     */
    private BigDecimal archiveFee;

    /**
     * 约定到账日期
     */
    private Integer incomeDate;
    /**
     * 社保应出账单日
     */
    private Integer appGenDate;
    /**
     * 应收总额
     */
    private BigDecimal receiveAmt;
    /**
     * 实际出账单日
     */
    private Date actGenTime;
    /**
     * 服务人数
     */
    private Integer employeeNum;
    /**
     * 客户账单年月
     */
    private Integer billMonth;
    /**
     * 新增/存量标识
     */
    private Integer newFlag;
    /**
     * 账单方
     */
    private String genBill;
    /**
     * 合同创建人
     */
    private String creator;
    /**
     * 下面格式化需要转换的值
     */
    private String formatIndustryType;

    private String formatSignComTitle;

    private String formatStartDate;

    private String formatContractStartDate;

    private String formatEndDate;

    private String formatContractType;

    private String formatNewFlag;

    private String formatActGenTime;

    public String getFormatIndustryType() {
        if (this.industryType != null) {
            this.formatIndustryType = FinancialAccountsReceivableReportEnum.IsIndustryTypeEnum.getName (this.industryType);
        }
        return formatIndustryType;
    }
// update guoqian
//    public String getFormatSignComTitle() {
//        if (this.signComTitle != null) {
//            this.formatSignComTitle = FinancialAccountsReceivableReportEnum.IsSignComTitleEnum.getName (this.signComTitle);
//        }
//        return formatSignComTitle;
//    }

    public String getFormatStartDate() {
        if (this.startDate != null) {
            this.formatStartDate = DateUtil.getString (this.startDate, DateUtil.DATE_FORMAT_LONG);
        }
        return formatStartDate;
    }

    public String getFormatContractStartDate() {
        if (this.contractStartDate != null) {
            this.formatContractStartDate = DateUtil.getString (this.contractStartDate, DateUtil.DATE_FORMAT_LONG);
        }
        return formatContractStartDate;
    }

    public String getFormatEndDate() {
        if (this.endDate != null) {
            this.formatEndDate = DateUtil.getString (this.endDate, DateUtil.DATE_FORMAT_LONG);
        }
        return formatEndDate;
    }

    public String getFormatContractType() {
        if (this.contractType != null) {
            this.formatContractType = FinancialAccountsReceivableReportEnum.IsContractTypeEnum.getName (this.contractType);
        }
        return formatContractType;
    }

    public String getFormatNewFlag() {
        if (this.newFlag != null) {
            this.formatNewFlag = FinancialAccountsReceivableReportEnum.IsNewFlagEnum.getName (this.newFlag);
        }
        return formatNewFlag;
    }

    public String getFormatActGenTime() {
        if (this.actGenTime != null) {
            this.formatActGenTime = DateUtil.getString (this.actGenTime, DateUtil.DATE_FORMAT_LONG);
        }
        return formatActGenTime;
    }
}
