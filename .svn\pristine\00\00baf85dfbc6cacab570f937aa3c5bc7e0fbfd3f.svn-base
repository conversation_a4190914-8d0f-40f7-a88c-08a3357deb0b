package com.reon.hr.api.customer.vo.export.salary;

import com.reon.hr.api.customer.anno.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2021/10/20.
 */
@Data
public class SalaryBatchDetailExportVo implements Serializable {
    private Long batchId;
    private String custName;

    //@ExcelColumn(name = "员工编号",columnWidth = 4000)
    private String employeeNo;

    @ExcelColumn(name = "姓名",columnWidth = 1756)
    private String name;

    @ExcelColumn(name = "证件类型",columnWidth = 2394)
    private String certTypeName;
    private String certType;

    @ExcelColumn(name = "证件号码",columnWidth = 2574)
    private String certNo;

    @ExcelColumn(name = "工资所属月",columnWidth = 2969)
    private String salaryMonth;

    @ExcelColumn(name = "开户行",columnWidth = 2271)
    private String bankName;
    private String bank;

    //@ExcelColumn(name = "其他银行",columnWidth = 3000)
    private String otherBank;

    //@ExcelColumn(name = "开户分支行",columnWidth = 3000)
    private String subBank;

    //@ExcelColumn(name = "开户行省份",columnWidth = 3000)
    private String openProvince;

    //@ExcelColumn(name = "开户行城市",columnWidth = 3000)
    private String openingPlace;

    @ExcelColumn(name = "银行卡号",columnWidth = 2334)
    private String cardNo;

    //@ExcelColumn(name = "工资发放地",columnWidth = 3000)
    private String payPlace;

    @ExcelColumn(name = "扣缴义务人名称",columnWidth = 3800)
    private String withholdingAgentName;

    //@ExcelColumn(name = "发薪地是否变更",columnWidth = 3000)
    private String changePayPlace;

    //@ExcelColumn(name = "发薪地与参保地不一致",columnWidth = 3000)
    private String payInsuranceFlag;

    //@ExcelColumn(name = "发薪是否新增",columnWidth = 3000)
    private String newPay;

    //@ExcelColumn(name = "薪资发放状态",columnWidth = 3000)
    private String statusName;
    private String status;

    private Map<String, String> salaryPayDetailMap;//工资计算详情放置表头跟数据的
    private Long paymentId;
    private String paymentCustName;
    private List<Integer> columnToBeDeletedList=new ArrayList<>();
}
