<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.ehr.sp.sys.mapper.base.EhrImportDataMapper">
    <resultMap id="BaseResultMap" type="com.reon.ehr.sp.sys.domain.entity.base.EhrImportData">
        <result column="import_no" jdbcType="VARCHAR" property="importNo"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="opr_man" jdbcType="VARCHAR" property="oprMan"/>
        <result column="opr_time" jdbcType="TIMESTAMP" property="oprTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="success_num" jdbcType="INTEGER" property="successNum"/>
        <result column="fail_num" jdbcType="INTEGER" property="failNum"/>
        <result column="file_id" jdbcType="VARCHAR" property="fileId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <insert id="insert" parameterType="com.reon.ehr.sp.sys.domain.entity.base.EhrImportData">
    insert into import_data (import_no, data_type, opr_man, 
      opr_time, remark, success_num, 
      fail_num, file_id, status, 
      creator, create_time, updater, 
      update_time, del_flag)
    values (#{importNo,jdbcType=VARCHAR}, #{dataType,jdbcType=INTEGER}, #{oprMan,jdbcType=VARCHAR}, 
      #{oprTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{successNum,jdbcType=INTEGER}, 
      #{failNum,jdbcType=INTEGER}, #{fileId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.reon.ehr.sp.sys.domain.entity.base.EhrImportData">
        insert into import_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="importNo != null">
                import_no,
            </if>
            <if test="dataType != null">
                data_type,
            </if>
            <if test="oprMan != null">
                opr_man,
            </if>
            <if test="oprTime != null">
                opr_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="successNum != null">
                success_num,
            </if>
            <if test="failNum != null">
                fail_num,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="importNo != null">
                #{importNo,jdbcType=VARCHAR},
            </if>
            <if test="dataType != null">
                #{dataType,jdbcType=INTEGER},
            </if>
            <if test="oprMan != null">
                #{oprMan,jdbcType=VARCHAR},
            </if>
            <if test="oprTime != null">
                #{oprTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="successNum != null">
                #{successNum,jdbcType=INTEGER},
            </if>
            <if test="failNum != null">
                #{failNum,jdbcType=INTEGER},
            </if>
            <if test="fileId != null">
                #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByImportDataNo">
        update import_data SET status  = #{status,jdbcType=INTEGER}
            where import_no =#{importNo,jdbcType=VARCHAR}
  </update>

    <update id="updateByImportDataDelflag">
        UPDATE import_data SET del_flag='Y'
        WHERE import_no=#{importNo,jdbcType=VARCHAR};
    </update>

    <update id="updateByImportRecordNumber">
        UPDATE import_data SET success_num=#{successNum,jdbcType=INTEGER},
        fail_num=#{failNum,jdbcType=INTEGER}
        WHERE import_no=#{importNo,jdbcType=VARCHAR};
    </update>
    <update id="updateByImportRecordNumberAndFileId">
        UPDATE import_data SET success_num=#{successNum,jdbcType=INTEGER},
        fail_num=#{failNum,jdbcType=INTEGER},file_id=#{fileId}
        WHERE import_no=#{importNo,jdbcType=VARCHAR};
    </update>

    <select id="getImportDataListPage" resultType="com.reon.ehr.api.sys.vo.base.EhrImportDataVo">
        SELECT
        itd.import_no,
        itd.data_type,
        itd.opr_man,
        itd.opr_time,
        itd.remark,
        itd.success_num,
        itd.fail_num,
        itd.fail_num,
        itd.file_id,
        itd.status,
        itd.creator,
        itd.create_time,
        itd.updater,
        itd.update_time
        from import_data as itd
        WHERE itd.del_flag='N'
        <if test="dataType!=null and dataType!=''">
            and itd.data_type = #{dataType,jdbcType=INTEGER}
        </if>
        <if test="creator!=null and creator!=''">
            and itd.creator = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="importNo!=null and importNo!=''">
            and (itd.import_no like concat('%',#{importNo},'%'))
        </if>
        <if test="oprMan!=null and oprMan!=''">
            and itd.opr_man = #{oprMan}
        </if>
        <if test="params != null">
            <if test="params.beginOprTime != null and params.beginOprTime != ''">
                and itd.opr_time &gt;= #{params.beginOprTime}
            </if>
            <if test="params.endOprTime != null and params.endOprTime != ''">
                and itd.opr_time &lt;= #{params.endOprTime}
            </if>
        </if>
        ORDER BY itd.opr_time DESC
    </select>

    <select id="getById" resultType="com.reon.ehr.api.sys.vo.base.EhrImportDataVo">
        SELECT
        *
        from import_data
        WHERE import_no =#{importNo,jdbcType=VARCHAR} and del_flag='N'
    </select>
    <select id="getAllBatchImportData" resultType="com.reon.ehr.api.sys.vo.base.EhrImportDataVo">
        SELECT
        itd.import_no,
        itd.data_type,
        itd.opr_man,
        itd.opr_time,
        itd.remark,
        itd.success_num,
        itd.fail_num,
        itd.fail_num,
        itd.file_id,
        itd.status,
        itd.creator,
        itd.create_time,
        itd.updater,
        itd.update_time
        from import_data as itd
        WHERE itd.del_flag='N'
        <if test="dataType!=null and dataType!=''">
            and itd.data_type = #{dataType}
        </if>
        <if test="creator!=null and creator!=''">
            and itd.creator = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="importNo!=null and importNo!=''">
            and (itd.import_no like concat('%',#{importNo},'%'))
        </if>
        <if test="oprMan!=null and oprMan!=''">
            and (itd.opr_man like concat('%',#{oprMan},'%'))
        </if>
        <if test="startOprTime != null and startOprTime != ''">
            and itd.opr_time &gt;= #{startOprTime}
        </if>
        <if test="endOprTime != null and endOprTime != ''">
            and itd.opr_time &lt;= #{endOprTime}
        </if>
        ORDER BY itd.opr_time DESC
    </select>


</mapper>