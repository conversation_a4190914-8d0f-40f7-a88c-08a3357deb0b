package com.reon.hr.sp.bill.dao.bill;

import com.reon.hr.api.bill.vo.PerBillInfoVo;
import com.reon.hr.sp.bill.entity.bill.PerBillInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PerBillInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PerBillInfo record);

    int insertSelective(PerBillInfo record);

    PerBillInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PerBillInfo record);

    int updateByPrimaryKey(PerBillInfo record);

    /**
     * 查询是否为首版账单
     * @param custId
     * @param employeeId
     * @return
     */
    List<PerBillInfoVo> getByCustIdAndEmployeeId(@Param("custId") Long custId, @Param("employeeId") Long employeeId, @Param("productCode")Integer productCode);

    List<PerBillInfoVo> getByCustIdAndEmployeeIds(@Param("custId") Long custId, @Param("employeeIds") List<Long> employeeIds);

    List<PerBillInfoVo> getByCustIdAndOrderNos(@Param("custId") Long custId, @Param("orderNos") List<String> orderNos);

    List<PerBillInfoVo> getByCustIdAndOrderNo(@Param("custId") Long custId, @Param("orderNo") String orderNo, @Param("productCode") Integer productCode);

    List<PerBillInfoVo> getByCustIdAndOrderNoList(@Param("custId") Long custId,@Param("orderNoList")List<String> perCommerceItemVoOrderNoList,@Param("productCode") int serviceChargeProductcode);

    int deleteByPrimaryKeyList(@Param("idList") List<Long> perBillInfoVoIdList);

    int insertSelectiveList(@Param("list") List<PerBillInfo> perBillInfoList);

    List<PerBillInfoVo> getByOrderNoList(@Param("list")List<String> orderNoList);

	void deleteByBillMonthAndOrderNoList(@Param("orderNos") List<String> orderNos, @Param("billMonth") Integer billMonth);
}