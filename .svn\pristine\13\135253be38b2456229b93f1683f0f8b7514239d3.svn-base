var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    var custId = '';
    var tip_index = 0;
    var submitCom;

    window.top['disSupManList'] = [];
    getDisSupManList();

    function getDisSupManList() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/sys/user/getDisSupManList",
            dataType: 'json',
            success: function (result) {
                window.top['disSupManList'] = result.data;
            },
            error: function (resp, textStatus, errorThrown) {
                console.log("load userName pair error,state:" + textStatus);
            }
        });
    }


    //日期点击事件
    var initYear;
    laydate.render({
        elem: '#billMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#billMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    //提交开始日期事件
    var startDate = laydate.render({
        elem: '#startSubmitTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    //提交结束日期事件
    var endDate = laydate.render({
        elem: '#endSubmitTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });

    //查询审批数据
    table.render({
        id: 'billDisposableApprovalGrid',
        elem: '#billDisposableApprovalGrid',
        url: ctx + "/bill/billDisposableQuery/getBillDisposableApprovalPage",
        page: true, //默认为不开启

        limits: [10, 50, 100, 200],
        limit: 10,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {
                field: 'btn', width: '3%', align: 'center',
                templet: function (d) {
                    return '<a style="width: 100%; height: 100%;cursor: pointer;" lay-event="addRowTable">+</a>'
                }
            },
            {type: 'radio', width: '3%'},
            {
                field: 'submitCom', title: '提交分公司', width: '10%', align: 'center', templet: function (d) {
                    if (!d.submitCom || typeof (companyCodeToName(d.submitCom)) == "undefined") {
                        return "无提交分公司"
                    }
                    return companyCodeToName(d.submitCom);
                }
            },
            {
                field: 'submiter', title: '提交人', width: '10%', align: 'center', templet: function (d) {
                    if (typeof (d.submiter) == "undefined") {
                        return "无提交人"
                    }
                    return ML.loginNameFormater(d.submiter);
                }
            },
            {field: 'custName', title: '客户名称', width: '10%', align: 'center'},
            {field: 'templetName', title: '客户账套', width: '10%', align: 'center'},
            {field: 'billMonth', title: '账单年月', width: '5%', align: 'center'},
            {field: 'receivableMonth', title: '财务应收年月', width: '7%', align: 'center'},
            {field: 'receiveAmt', title: '应收金额', width: '5%', align: 'center'},
            {
                field: 'status', title: '账单状态', width: '10%', align: 'center', templet: function (d) {
                    if (!d.status) {
                        return "无"
                    }
                    return ML.dictFormatter("BILL_STATTUS", d.status);
                }
            },
            {field: 'submitTime', title: '提交时间', width: '12%', align: 'center'},
            {field: '', title: '查看历史数据', width: '12%', align: 'center',
                templet: function (d) {
                    if (d.billId!=null){
                        return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='queryHistoryData'>" + '点击查看' + "</a>";
                    }else {
                        return "暂无"
                    }
                }},
            {
                field: 'approvalStatus', title: '审批状态', width: '5%', align: 'center', templet: function (d) {
                    if (!d.approvalStatus) {
                        return "无"
                    }
                    return ML.dictFormatter("DISPOSAL_STATUS", d.approvalStatus);
                }
            }
        ]],
        done: function () {
            $("#isShow,#submit,#rejected").hide();
            table.on('tool(billDisposableApprovalFilter)', function (obj) {
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                // 异常不要用它原来的这个作为tr的dom
                // var tr = obj.tr; //获得当前行 tr 的DOM对象
                var $this = $(this);
                var tr = $this.parents('tr');
                var trIndex = tr.data('index');
                if (layEvent === 'addRowTable') {
                    // 外围的table的id + tableIn_ + 当前的tr的data-index
                    $(this).attr('lay-event', 'fold').html('-');
                    var tableId = 'tableOut_tableIn_' + trIndex;

                    var _html = [
                        '<tr class="table-item">',
                        '<td colspan="' + tr.find('td').length + '" style="padding: 6px 12px;">',
                        '<table id="' + tableId + '"></table>',
                        '</td>',
                        '</tr>'
                    ];
                    tr.after(_html.join('\n'));
                    // 渲染子table
                    table.render({
                        elem: '#' + tableId,
                        url: ctx + '/bill/billDisposableQuery/getBillDisposableApprovalRejectReason',
                        where: {"billId": data.billId},
                        method: 'post',
                        page: false,
                        cols: [[
                            {field: '', title: '', float: 'left', width: '10%'},
                            {field: 'rejectReason', title: '驳回原因', width: '90%', align: 'center'},
                        ]],
                    });
                } else if (layEvent === 'fold') {
                    $(this).attr('lay-event', 'addRowTable').html('+');
                    tr.next().remove();
                }


                if (obj.event === 'viewImage') {
                    // 查看/删除图片
                    let data = {};
                    for (let element of imageDataList) {
                        if (element.row == billDisposableRow) data = element;
                    }
                    if ('row' in data) {
                        if (data.uploadIds.length > 0) {
                            openImagePage(data);
                        } else
                            return layer.msg('当前没有文件!');
                    } else
                        return layer.msg('当前没有文件!');
                }


                if (obj.event == 'queryHistoryData') {
                    layer.open({
                        type: 2, //为1时弹出页面直接展示content中内容,为2时会向后台发送请求,content内容为请求地址
                        title: "查看历史信息",
                        content: ML.contextPath + '/bill/billDisposableQuery/gotoHistoryData?billId=' + data.billId,
                        area: ['1800px', '800px'],
                        success: function (layero, index) {
                        },
                        end: function () {
                        }
                    })
                }

            });
        }
    });



    //客户下拉数据表格
    var appd = '<input style="display:inline-block;width:1100px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号/合同名称/编号" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'contractNo',
        appd: appd,
        table: {
            url: ML.contextPath + '/bill/billDisposableQuery/getContractPageByName',
            cols: [[
                {type: 'radio'},
                {field: 'custNo', title: '客户编号', align: 'center', width: '15%'},
                {field: 'custName', title: '客户名称', align: 'center', width: '30%'},
                {field: 'contractNo', title: '合同编号', align: 'center', width: '20%'},
                {field: 'contractName', title: '合同名称', align: 'center', width: '40%'},
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custId = item.custId;
            })
            elem.val(NEWJSON.join(","));
            $("#custName").attr("ts-selected");
            $(".tempOptionId").remove();
            ML.ajax("/customer/billTemplet/getListByCustId", {'custId': custId}, function (res) {
                if (res.data.length > 0) {
                    var optionStr = "";
                    layui.each(res.data, function (i, item) {
                        optionStr += "<option class='tempOptionId' value='" + item.id + "' genDate='" + item.genDate + "' lockDate='" + item.lockDate + "'>" + item.templetName + "</option>";
                    });
                    $("#templetId").append(optionStr);
                    form.render('select');
                }
            }, "get");
        }
    });

    //监听审批表格复选框选择
    table.on('radio(billDisposableApprovalFilter)', function (obj) {
        var checkStatus = table.checkStatus('billDisposableApprovalGrid');
        if (obj.checked && checkStatus.data.length !== 1) {
            $("#isShow").hide();
            $("#submit,#rejected").hide();
            return layer.msg("只能选择一条数据！");
        } else if (!obj.checked) {
            $("#isShow").hide();
            $("#submit,#rejected").hide();
        } else {
            imageDataList = [];
            $("#isShow").show();
            if (checkStatus.data[0].approvalStatus == 1) {
                $("#submit,#rejected").show();
            } else {
                $("#submit,#rejected").hide();
            }
            ML.ajax("/bill/billDisposableQuery/getBillDisposableApproval", {
                'billId': obj.data.billId,
                'status': obj.data.approvalStatus
            }, function (result) {
                getbillDisposableItemApproval(result.data);
            });
        }

    });

    //驳回一次性数据
    $("#rejected").click(function () {
        var rejectReason = '';
        var checkStatus = table.checkStatus('billDisposableApprovalGrid');
        if (checkStatus.data.length !== 1) {
            return layer.msg("只能选择一条数据！");
        }
        if (checkStatus.data[0].approvalStatus != 1) {
            return layer.msg("状态为一！");
        }
        layer.prompt({
            formType: 2,
            title: '驳回原因',
            area: ['260px', '100px'] //自定义文本域宽高
        }, function (value, index, elem) {
            rejectReason = value;
            checkStatus.data[0]['rejectReason'] = rejectReason;
            layer.close(index);
            ML.layuiButtonDisabled($('#rejected'));// 禁用
            $.ajax({
                type: 'POST',
                url: ML.contextPath + "/bill/billDisposableQuery/updateApprovalStatus",
                data: JSON.stringify(checkStatus.data[0]),
                dataType: 'json',
                contentType: 'application/json',//添加这句话
                success: function (results) {
                    layer.msg("驳回成功！");
                    $("#isShow").hide();
                    $("#submit,#rejected").hide();
                    ML.layuiButtonDisabled($('#rejected'), true);// 取消禁用
                    reloadTable();
                    ML.layuiButtonDisabled($('#submit'), true);// 取消禁用
                },
                error: function (resp, textStatus, errorThrown) {
                    ML.layuiButtonDisabled($('#rejected'), true);// 取消禁用
                    ML.ajaxErrorCallback(resp, textStatus, errorThrown);
                }
            });
        });
    });

    //提交一次性数据
    $("#submit").click(function () {
        var checkStatus = table.checkStatus('billDisposableApprovalGrid');
        if (checkStatus.data.length !== 1) {
            return layer.msg("只能选择一条数据！");
        }
        if (checkStatus.data[0].approvalStatus != 1) {
            return layer.msg("状态为一！");
        }
        ML.layuiButtonDisabled($('#submit'));// 禁用
        $.ajax({
            type: 'POST',
            url: ML.contextPath + "/bill/billDisposableQuery/updateApprovalStatus",
            data: JSON.stringify(checkStatus.data[0]),
            dataType: 'json',
            contentType: 'application/json',//添加这句话
            success: function (results) {
                // if (results.data === 200100) {
                //     layer.msg("审批成功,请财务经理继续完成最后的审批");
                // }
                if (results.data === 300) {
                    layer.msg("审批完成");
                }
                $("#isShow").hide();
                $("#submit,#rejected").hide();
                ML.layuiButtonDisabled($('#submit'), true);// 取消禁用
                reloadTable();
                ML.layuiButtonDisabled($('#submit'), true);// 取消禁用
            },
            error: function (resp, textStatus, errorThrown) {
                ML.layuiButtonDisabled($('#submit'), true);// 取消禁用
                ML.ajaxErrorCallback(resp, textStatus, errorThrown);
            }
        });

    });

    var imageDataList = [];

    //查询一次性项目审核信息
    function getbillDisposableItemApproval(list) {
        for (let item in list) {
            if (ML.isNotEmpty(list[item].fileIdList)) {
                let imageData = {};
                imageData['row'] = item;
                var uploadCache = [];
                list[item].fileIdList.split(',').forEach(function (item) {
                    uploadCache.push({"fileId": item});
                })
                imageData['uploadIds'] = uploadCache;
                imageDataList.push(imageData);
            }
        }


        //一次性项目审核信息
        table.render({
            id: 'billDisposableItemApprovalGrid',
            elem: '#billDisposableItemApprovalGrid',
            data: list,
            page: false, //默认为不开启
            limit: Number.MAX_VALUE,
            totalRow: true,
            defaultToolbar: false,
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'numbers', title: '序号', width: 50, totalRowText: "合计"},
                {
                    field: 'prodType',
                    title: '一级类别',
                    align: "center",
                    width: '10%', templet: function (d) {
                        if (!d.prodType) {
                            return "无"
                        }
                        return ML.dictFormatter("DISPOSAL_TYPE", d.prodType);
                    }

                },
                {
                    field: 'prodKind',
                    title: '二级类别',
                    align: "center",
                    width: '10%', templet: function (d) {
                        if (!d.prodKind) {
                            return "无"
                        }
                        return ML.getDictSubByParentCodeAndSelfCode(d.prodType, "DISPOSAL_TYPE", d.prodKind);
                    }
                },
                {
                    field: 'amount', title: '金额', align: "center", width: '10%', totalRow: true
                },
                {
                    field: 'taxFreeAmt',
                    title: '金额（不含税）',
                    align: "center",
                    width: '10%',
                    totalRow: true
                },
                {
                    field: 'disposableTaxRatio',
                    title: '增值税率',
                    align: "center",
                    width: '10%', templet: function (d) {
                        if (ML.isEmpty(d.disposableTaxRatio))
                            return "0%";
                        else
                            return d.disposableTaxRatio + "%"
                    }
                },
                {
                    field: 'tax',
                    title: '增值税',
                    align: "center",
                    width: '10%'
                },
                {
                    field: 'peopleNum',
                    title: '总人次',
                    align: 'center',
                    width: '6%',
                    edit: 'text',
                    event: "inputPeopleNum"
                },
                {
                    field: 'supComNum',
                    title: '供应商人次',
                    align: 'center',
                    width: '6%',
                    edit: 'text',
                    event: "inputSupComNum"
                },
                {
                    field: 'supCost',
                    title: '供应商成本',
                    align: 'center',
                    width: '6%',
                    edit: 'text',
                    event: "inputSupCost"
                },
                {
                    field: 'disSupMan',
                    title: '一次性支持人员',
                    align: 'center',
                    width: '6%', templet: '#disSupMan'
                },

                {field: 'contractName', title: '大合同名称', align: 'center', width: '17%', edit: 'text'},
                {field: 'remark', title: '备注', align: 'center', width: '15%', edit: 'text'}
                , {field: '', title: '上传文件', toolbar: '#imageToolBar', width: '4%', align: 'center'}
            ]],
            done: function () {
            }
        });
    }

    //重载数据
    function reloadTable() {
        table.reload('billDisposableApprovalGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    //初始化表单
    form.on('submit(btnQuery)', function (data) {
        data.field.submitCom = submitCom;
        data.field.templetId = $("#templetId").val();
        table.reload('billDisposableApprovalGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    //派单方(分公司)
    var appd2 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#submitCom',
        checkedKey: 'owerCity',
        appd: appd2,
        table: {
            url: ML.contextPath + '/sys/org/getCompanyByName',
            cols: [[
                {type: 'radio'},
                {type: 'numbers', title: '序号', align: 'center'},
                {field: 'orgName', title: '分公司名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
                submitCom = item.orgCode;
            })
            elem.val(NEWJSON.join(","));
            $("#distCom").val($("#submitCom").attr("ts-selected"));
        }
    });

    //清除提交人
    $("#reset").click(function () {
        submitCom = null;
    });


    table.on('tool(billDisposableItemApprovalFilter)', function (obj) {
        var data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
        var $this = $(this);
        var tr = $this.parents('tr');
        var trIndex = tr.data('index');

        let billDisposableRow = parseInt(obj.tr.selector.split("=")[1].split("]")[0].split('"')[1]);

        if (obj.event === 'viewImage') {
            // 查看/删除图片
            let data = {};
            for (let element of imageDataList) {
                if (element.row == billDisposableRow) data = element;
            }
            if ('row' in data) {
                if (data.uploadIds.length > 0) {
                    openImagePage(data);
                } else
                    return layer.msg('当前没有文件!');
            } else
                return layer.msg('当前没有文件!');
        }

    });


    function openImagePage(data) {
        layer.open({
            type: 2,
            title: '查看',
            area: ['65%', '80%'],
            maxmin: true,
            offset: 'auto',
            shade: ['0.8', '#393D49'],
            content: ML.contextPath + '/bill/billDisposableQuery/gotoOpenImagePage',
            success: function (layero, index) {
                let body = layer.getChildFrame("body", index);
                body.find("#dataForBDQ").val(JSON.stringify(data));
                body.find("#status").val(2); // 表示 是查看
                // var parentIndex = parent.layer.getFrameIndex(window.name);
                // body.find("#parentWindowName").val(window.name);
            },
            end: function () {
                if (ML.isNotEmpty($("#data").val())) {
                    let row = JSON.parse($("#data").val()).row
                    let uploadIds = JSON.parse($("#data").val()).uploadIds
                    for (let item of imageDataList)
                        if (item.row == row) item.uploadIds = uploadIds;
                }
            }
        })

    }
});