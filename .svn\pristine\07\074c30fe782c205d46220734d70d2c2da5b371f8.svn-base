<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm">
        <table class="layui-table" lay-skin="nob" style="width: 80%">
            <tr>
                <td width="5%" align="right" style="font-weight:800">集团名称</td>
                <td width="8%">
                    <select class="layui-select" name="groupId" id="groupId" lay-search lay-filter="groupIdFilter">
                        <option value=""></option>
                    </select>
                </td>
                <td width="5%" align="right" style="font-weight:800">客户名称</td>
                <td width="8%">
                    <input class="layui-input" type="text" id="custName" placeholder="请选择" readonly>
                    <input class="layui-input" type="hidden" id="custId" name="custId">
                    <input class="layui-input" type="hidden" id="custGroupId">
                </td>
                <td width="12%">
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" authuri="/report/selectEmpTrackReportPage">查询</button>
                    <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button>
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnExport" lay-filter="btnExportFilter" authuri="/report/exportEmpTrackReport">导出</button>
                </td>
            </tr>
        </table>
    </form>
</blockquote>

<table class="layui-hide" id="empTrackReportGrid" lay-filter="empTrackReportFilter"></table>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" lay-event="billPrint">帐单打印</button>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/report/empTrackReport.js?v=${publishVersion}"></script>
</body>
</html>
