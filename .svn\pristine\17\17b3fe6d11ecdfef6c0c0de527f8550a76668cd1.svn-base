<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <title>批量导入模板</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>
    <style>
        .layui-input {
            padding-right: 30px;
        !important;
        }

        .layui-table-cell {
            padding: 0px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">

            <div class="layui-input-inline">
                <label class="layui-form-label" title="导入日期从" style="font-weight:800">导入日期从</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="startOprTime" id="startOprTime"
                           placeholder="yyyy-MM-dd" readonly>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入日期到" style="font-weight:800">导入日期到</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="endOprTime" id="endOprTime" placeholder="yyyy-MM-dd" readonly>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入人" style="font-weight:800">导入人</label>
                <div class="layui-input-inline">
                    <input type="text" maxlength="20" name="oprMan" id="oprMan" placeholder="请输入" class="layui-input"
                           autocomplete="off">
                </div>
            </div>


            <div class="layui-input-inline">
                <label class="layui-form-label" title="导入编号" style="font-weight:800">导入编号</label>
                <div class="layui-input-inline">
                    <input type="text" maxlength="20" name="importNo" id="importNo" placeholder="请输入"
                           class="layui-input" autocomplete="off">
                </div>
            </div>


            <a class="layui-btn" lay-submit id="btnQuery" lay-filter="btnQueryFilter">检索</a>
            <button class="layui-btn" id="reset" type="reset">重置</button>
        </div>

    </form>
    <%--endQuery--%>


</blockquote>
<%--startTable--%>
<table class="layui-hide" id="batchImportGrid" lay-filter="batchImportGridTable"></table>
<%--endTable--%>
<script type="text/jsp" id="btn">
<%--    <a href="javascript:void(0)" title="修改" lay-event="update" authURI="/customer/invoice/gotoEditPage"><i--%>
<%--            class="layui-icon layui-icon-edit"></i></a>&nbsp;--%>
<%--    <a href="javascript:void(0)" title="删除" lay-event="delete" authURI="/customer/batchImport/delete"><i
            class="layui-icon layui-icon-delete"></i></a>&nbsp;--%>
    <a href="javascript:void(0)" title="查看" lay-event="query"
       authURI="/customer/batchImport/gotoBatchImportedDataHistoryView"><i class="layui-icon layui-icon-search"></i></a>
</script>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="import" lay-event="import"
                authURI="/customer/import/importExcel">导入
        </button>
        <button class="layui-btn layui-btn-sm" id="batchAddDistributionTemplate" lay-event="batchAddDistributionTemplate"
                authURI="/customer/import/batchAddDistributionTemplate"
        >下载模板
        </button>
          <button class="layui-btn layui-btn-sm" id="export" lay-event="export"
                authURI="/customer/import/exportData"
        >导出数据
        </button>
    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/salaryPay/batchAddDistribution.js?v=${publishVersion}"></script>
</body>
</html>
