package com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.salaryItem;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.customer.ItemCfgDTO;
import com.reon.hr.api.customer.dto.customer.SalaryItemApplyDTO;
import com.reon.hr.api.customer.dto.customer.SalaryItemDTO;
import com.reon.hr.api.customer.dto.customer.SalaryItemLogDTO;
import com.reon.hr.api.customer.vo.SalaryItemLogVo;
import com.reon.hr.api.customer.vo.SalaryItemVo;

import java.util.List;

public interface ISalaryItemResourceWrapperService {
/**
*@Description:新增接口
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2021/1/5 17:56
*/
	Long saveSalaryItem(SalaryItemDTO salaryItemDTO, ItemCfgDTO itemCfgDTO);
/**
*@Description:修改接口
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2021/1/6 10:29
*/
	Integer editSalaryItem(SalaryItemDTO salaryItemDTO, ItemCfgDTO itemCfgDTO);

/**
*@Description:  提交接口
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2021/1/6 10:30
*/
	Long commitSalaryItem(SalaryItemDTO salaryItemDTO, ItemCfgDTO itemCfgDTO, SalaryItemApplyDTO salaryItemApplyDTO);

	Integer saveSalaryItemLog(SalaryItemLogDTO salaryItemLogDTO);
/**
*@Description: 根据 薪资项Id获取薪资项目信息
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2021/1/6 14:06
*/
	SalaryItemVo getSalaryItemById(Long itemId,Long salaryCategoryId);
	/**
	*@Description: 获取主页面数据
	*@Author: chenxiang
	*@Params:  * @param	null
	*@Returns:
	*@Since 2021/1/6 14:06
	*/
	Page<SalaryItemVo> getSalaryItem(Integer page, Integer limit, SalaryItemDTO salaryItemDTO);

	Integer saveItemCfg(ItemCfgDTO itemCfgDTO);

	Integer editItemCfg(ItemCfgDTO itemCfgDTO);

	/**
	*@Description: 获取薪资项目历史信息
	*@Author: chenxiang
	*@Params:  * @param	null
	*@Returns:
	*@Since 2021/1/11 17:41
	*/
	Page<SalaryItemLogVo> getSalaryItemLogListPage(Integer page, Integer limit, SalaryItemLogDTO salaryItemLogDTO);

	/**
	 * @Description: 保存审批数据
	 * @Author: chenxiang
	 * @Params: * @param	null
	 * @Returns:
	 * @Since 2021/1/12 17:12
	 */
	Integer saveSalaryItemApply(SalaryItemApplyDTO salaryItemApplyDTO);


	Integer editSalaryItemForCommit(SalaryItemDTO salaryItemDTO, ItemCfgDTO itemCfgDTO, SalaryItemApplyDTO salaryItemApplyDTO);

	Page<SalaryItemVo> getSalaryItemApproveListPage(Integer page, Integer limit, SalaryItemDTO salaryItemDTO);

	Page<SalaryItemVo> getSystemSalaryItemListPage(Integer page, Integer limit, SalaryItemDTO salaryItemDTO);

	List<SalaryItemVo> getAllSalaryItemByProperty(Long salaryCategoryId);

	Integer saveItemCfgList(List<ItemCfgDTO> itemCfgDtoList);

/**
*@Description: 获取所有当前登录人待审批的数据
*@Author: chenxiang
*@Params:  * @param	null
*@Returns:
*@Since 2021/1/20 13:06
*/
	Page<SalaryItemVo> getSalaryItemApplyListPage(Integer page, Integer limit, SalaryItemDTO salaryItemDTO);

	Integer updateStatus(List<Integer> itemList, String type);

    Boolean deleteSalaryItem(Long[] ids,String loginName);

	void insertSalaryItemSystem();

	List<SalaryItemVo> getSalaryItemByItemNameAndSalaryCategoryId(String itemName, Long salaryCategoryId);

	Boolean updateSalaryItemDisplay(ItemCfgDTO itemCfgDTO);
}
