/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/7/2
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.common;

import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IMessageWrapperService;
import com.reon.hr.api.base.vo.MessageVo;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebSocketServer
 *
 * @date 2020/7/2 10:59
 */
@ServerEndpoint("/webSocketServer")
public class WebSocketServer {
    private final static Logger logger = LoggerFactory.getLogger (WebSocketServer.class);

    //当前在线人数
    private static int onlineCount = 0;
    //用一个set集合保存几个websocket实例
    private static CopyOnWriteArraySet<WebSocketServer> wsSet = new CopyOnWriteArraySet<WebSocketServer> ();

    //websocket的session
    private Session session;

    private IMessageWrapperService iMessageWrapperService = (IMessageWrapperService) ContextLoader.getCurrentWebApplicationContext ().getBean ("messageWrapperDubboService");


    /**
     * 客户端新建websocket时会触发(握手协议后)
     * 并加入当前的set集合中
     *
     * @param session
     */
    @OnOpen
    public void wsOpen(Session session) {
        this.session = session;
        wsSet.add (this);//加入集合
        // 在线人数加1
        addOnlineCount ();
    }

    //当websocket退出的时候触发，并在set集合中删除当前websocket
    @OnClose
    public void wsClose() {
        wsSet.remove (this); //删除
        //在线人数-1
        subOnlineCount ();
    }

    /**
     * 接收到客户端发来的消息并处理，同时也像客户端发送消息
     *
     * @param message
     * @param session
     */
    @OnMessage
    public void wsMessage(String message, Session session) {
        logger.info ("=====客户端发来消息:" + message);
        logger.info ("======websocket 数量:" + wsSet.size ());
        //根据当前登入人查询消息信息
        List<MessageVo> messageWrapperList = iMessageWrapperService.getMessageWrapperList (message);
        List<String> list = Lists.newArrayList ();
        //指定用户单发消息
        for (WebSocketServer wss : wsSet) {
            if (wss.session.getId ().equals (session.getId ())) {
                if (!messageWrapperList.isEmpty()) {
                    messageWrapperList.forEach (vo -> {
                        String yellow="<svg class=\"icon\" aria-hidden=\"true\" ><use xlink:href=\"#icon-exclamation-point-yellow\"></use></svg>";
                        String red="<svg class=\"icon\" aria-hidden=\"true\" ><use xlink:href=\"#icon-exclamation-point-red\"></use></svg>";
                        switch (vo.getMsgType ()) {
                            case 0:
                                list.add (red+"你有" + vo.getCount () + "条待审批<span style='color:#E50000;'>数据</span>！");
                                break;
                            case 1:
                                switch (vo.getMsgTitle ()) {
                                    case "合同过期消息":
                                        list.add (red+"你有" + vo.getCount () + "条合同<span style='color:#E50000;'>已过期</span>！");
                                        break;
                                    case "合同即将过期消息":
                                        list.add (yellow+"你有" + vo.getCount () + "条合同<span style='color:#E59900;'>即将过期</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 2:
                                switch (vo.getMsgTitle()) {
                                    case "员工合同过期消息":
                                        list.add (red+"你有" + vo.getCount() + "条<span style='color:#2257c4;'>[员工]</span>合同<span style='color:#E50000;'>已过期</span>！");
                                        break;
                                    case "员工合同即将过期消息":
                                        list.add (yellow+"你有" + vo.getCount() + "条<span style='color:#2257c4;'>[员工]</span>合同<span style='color:#E59900;'>即将过期</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 3:
                                switch (vo.getMsgTitle()) {
                                    case "员工合同需要上传劳动合同消息":
                                        list.add (red+"你有" + vo.getCount() + "条<span style='color:#2257c4;'>[员工]</span>合同<span style='color:#E50000;'>需要上传劳动合同文件</span>！");
                                        break;
                                    case "员工合同需要上传解除证明消息":
                                        list.add (yellow+"你有" + vo.getCount() + "条<span style='color:#2257c4;'>[员工]</span>合同<span style='color:#E59900;'>需要上传解除证明文件</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 4:
                                switch (vo.getMsgTitle()) {
                                    case "新增未增反馈消息":
                                        list.add (red+"你有" + vo.getCount() + "条订单<span style='color:#E50000;'>上传了新的未增反馈</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 5:
                                switch (vo.getMsgTitle()) {
                                    case "工资支付退票录入消息":
                                        list.add (red+"你有" + vo.getCount() + "条<span style='color:#E50000;'>工资支付退票录入消息</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                                case 6:
                                switch (vo.getMsgTitle()) {
                                    case "临近退休人员需注意":
                                        list.add (yellow+"你有" + vo.getCount() + "条<span style='color:#E50000;'>临近退休人员需注意消息</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 7:
                                switch (vo.getMsgTitle()) {
                                    case "订单驳回/挂起":
                                        list.add (red+"你有" + vo.getCount() + "条<span style='color:#E50000;'>订单被驳回/挂起</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 8:
                                switch (vo.getMsgTitle()) {
                                    case "开票驳回":
                                        list.add (yellow+"你有" + vo.getCount() + "条<span style='color:#E50000;'>条开票信息被驳回</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 9:
                                switch (vo.getMsgTitle()) {
                                    case "供应商合同即将过期消息":
                                        list.add (yellow+"你有" + vo.getCount() + "条<span style='color:#E50000;'>条供应商合同即将到期</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 11:
                                switch (vo.getMsgTitle()) {
                                    case "供应商工资账单推送消息":
                                        list.add (red+"你有" + vo.getCount() + "条<span style='color:#E50000;'>供应商工资账单已推送</span>！");
                                        break;
                                    default:
                                        break;
                                }
                                break;


                            default:
                                break;


                        }
                    });
                    //向客户端发送消息
                    wss.sendMessage (JSONArray.fromObject (list).toString ());
                }
            }
        }
    }

    //websocket错误的时候丢出一个异常
    @OnError
    public void wsError(Session session, Throwable throwable) {
        // throw new IllegalArgumentException (throwable);
    }

    //send message  发送消息处理方法
    public void sendMessage(String message) {
        try {
            this.session.getBasicRemote ().sendText (message);
            logger.info ("发送了消息:" + message);
        } catch (Exception e) {
            // TODO: handle exception
            logger.error (e.getMessage ());
        }
    }

    // get onlinecount
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    // +1
    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
//        logger.info ("++++++++++++++上线人数+1:" + onlineCount);
    }

    //-1
    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
//        logger.info ("---------------线上人数-1:" + onlineCount);
    }
}
