package com.reon.hr.common.enums.invoice;

import lombok.Getter;

/**
 * 清单标志枚举
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Getter
public enum BillListFlagEnum {
    /**
     * 非清单
     */
    NOT_A_LIST("0", "非清单"),

    /**
     * 清单
     */
    IS_A_LIST("1", "清单");

    private final String code;
    private final String description;

    BillListFlagEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找对应的清单标志枚举项
     *
     * @param code 清单标志代码
     * @return 对应的枚举项，若找不到则返回null
     */
    public static BillListFlagEnum fromCode(String code) {
        for (BillListFlagEnum flag : values()) {
            if (flag.getCode().equals(code)) {
                return flag;
            }
        }
        return null;
    }
}
