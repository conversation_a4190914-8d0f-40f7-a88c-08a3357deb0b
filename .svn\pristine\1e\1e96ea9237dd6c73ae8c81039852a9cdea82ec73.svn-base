package com.reon.hr.sp.dubbo.service.rpc.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.dubbo.service.rpc.sys.IRoleWrapperService;
import com.reon.hr.api.vo.sys.RoleVo;
import com.reon.hr.sp.service.sys.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("sysRoleDubboService")
public class RoleWrapperServiceImpl implements IRoleWrapperService {
    @Autowired
    private IRoleService roleService;

    @Override
    public Page<RoleVo> getRoleListByTypeAndCode(Integer page, Integer limit, String refId, Integer type, String keyword) {
        return roleService.getRolesByTypeAndCode(page, limit, refId, type, keyword);
    }
}
