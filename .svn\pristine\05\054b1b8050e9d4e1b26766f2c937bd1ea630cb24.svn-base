package com.reon.hr.sp.bill.dubbo.service.rpc.impl.paymentApply;

import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.IPaymentApplyWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.paymentApply.ISalaryPaymentApplyWrapperService;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.salary.PayServiceSerialLogVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.common.cmb.dto.IntegrationDto;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.ISalaryPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.PayServiceSerialLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("salaryPaymentApplyDubboService")
public class SalaryPaymentApplyWrapperServiceImpl implements ISalaryPaymentApplyWrapperService {
    @Autowired
    private ISalaryPaymentApplyService iSalaryPaymentApplyService;

    @Autowired
    private PayServiceSerialLogService payServiceSerialLogService;
    @Override
    public List<IntegrationDto> getIntegrationDtoList(List<String> pidList, Integer documentStatus, Map<String, Map<String, Object>> oldVariablesMap, String updater) {
        return iSalaryPaymentApplyService.getIntegrationDtoList(pidList,documentStatus,oldVariablesMap,updater);
    }

    @Override
    public PaymentApplyVo getStartPaymentApplyWorkflowVariables(PaymentApplyVo paymentApplyVo, UserOrgPosVo userOrgPosVo) throws Exception {
        return iSalaryPaymentApplyService.getStartPaymentApplyWorkflowVariables(paymentApplyVo,userOrgPosVo);
    }

    @Override
    public TaskVo updatePaymentApplyAndSubmitTask(TaskVo taskVo) {
        return iSalaryPaymentApplyService.updatePaymentApplyAndSubmitTask(taskVo);
    }

    @Override
    public TaskVo getTaskByYurref(String yurRef) {
        return iSalaryPaymentApplyService.getTaskByYurref(yurRef);
    }
    @Override
    public void triggerReceiveTaskByYurref(String yurRef,String loginName, String comment) {
        iSalaryPaymentApplyService.triggerReceiveTaskByYurref(yurRef,loginName,comment);
    }

    @Override
    public String updateAndSendPayrollOrCorpSinglePay(List<String> pidList, List<String> taskIdList, Integer documentStatus,String loginName) {
        return iSalaryPaymentApplyService.updateAndSendPayrollOrCorpSinglePay(pidList,taskIdList,documentStatus,loginName);
    }

    @Override
    public PayServiceSerialLogVo getByYurref(String yurRef) {
        return payServiceSerialLogService.getByYurref(yurRef);
    }

    @Override
    public void updateSalaryPaymentApply(List<PaymentApplyVo> paymentApplyList,String loginUser) {
        iSalaryPaymentApplyService.updateSalaryPaymentApply(paymentApplyList,loginUser);
    }

    @Override
    public int updateSalaryPaymentVariable(String eventKey, Long paymentApplyId, String loginName) {
        return iSalaryPaymentApplyService.updateSalaryPaymentVariable(eventKey,paymentApplyId,loginName);
    }

    @Override
    public Map<String, Object> getVariables(Map<String, Object> varMaps, PaymentApplyVo paymentApplyVo) {
        return iSalaryPaymentApplyService.getVariables(varMaps,paymentApplyVo);
    }
}
