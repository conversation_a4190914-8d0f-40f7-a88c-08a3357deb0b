/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/1/12
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.customer.dto.customer;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalaryItemApplyDTO
 *
 * @date 2021/1/12 15:06
 */
@Data
public class SalaryItemApplyDTO implements Serializable {
	private static final long serialVersionUID = -8081411262848929817L;
	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 薪资项目ID
	 */
	private Long itemId;

	/**
	 * 申请时间
	 */
	private Date applyTime;

	/**
	 * 审批状态
	 */
	private Byte status;

	/**
	 * 审批人机构
	 */
	private String approverOrg;
	/**
	 * 审批人岗位
	 */
	private String approverPos;

	/**
	 * 审批时间
	 */
	private Date approvalTime;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改人
	 */
	private String updater;

	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 删除标识(Y:已删除，N:未删除)
	 */
	private String delFlag;

}
