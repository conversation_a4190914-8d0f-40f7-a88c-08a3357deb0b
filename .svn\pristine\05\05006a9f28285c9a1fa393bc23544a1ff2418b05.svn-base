package com.reon.hr.sp.customer.dao.sales;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.sales.VisitCompanyReportVo;
import com.reon.hr.api.customer.vo.sales.VisitCompanyVo;
import com.reon.hr.sp.customer.entity.sales.VisitCompany;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface VisitCompanyMapper  {

    Integer insert(VisitCompany record);

    List<VisitCompanyVo> getVisitCompanyInfo(@Param("page") Page page,@Param("visitCompany") VisitCompanyVo visitCompanyVo);


    VisitCompanyVo getVisitCompanyById(Long id);

    List<VisitCompanyReportVo> getVisitCompanyReport(@Param("visitCompany") VisitCompanyVo visitCompanyVo);

    void updateVisitCompany(VisitCompanyVo visitCompanyVo);
}
