package com.reon.hr.core.thread;

import com.reon.hr.api.bill.dubbo.service.rpc.bill.financial.INetSilverWrapperService;
import com.reon.hr.api.bill.utils.NetSilverUploadUtil;
import com.reon.hr.api.bill.vo.NetSilverParameterVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.core.vo.ServiceCommonVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.RecursiveAction;

public class NetSilverUploadTask extends RecursiveAction {
    private static final Logger logger = LoggerFactory.getLogger (NetSilverUploadTask.class);
    //上传人
    String loginName;

    String userName;
    //导入文件
    MultipartFile file;
    //导入文件ID
    String fileId;

    String orgCode;

    String orgName;
    //大区
    String zoneId;

    Integer cityCode;

    INetSilverWrapperService iNetSilverWrapperService;

    NetSilverUploadUtil netSilverUploadUtil;

    @Override
    public void compute() {
        try {
            NetSilverParameterVo netSilverParameterVo = new NetSilverParameterVo ();
            netSilverParameterVo.setFileId (fileId);
            netSilverParameterVo.setLoginName (loginName);
            netSilverParameterVo.setOrgCode (orgCode);
            netSilverParameterVo.setUserName (userName);
            netSilverParameterVo.setOrgName (orgName);
            netSilverParameterVo.setZoneId (zoneId);
            netSilverParameterVo.setFile (file);
            netSilverParameterVo.setINetSilverWrapperService (iNetSilverWrapperService);
            netSilverParameterVo.setCityCode (cityCode);
            netSilverUploadUtil.analyzeExcelData (netSilverParameterVo);
            INetSilverWrapperService.netSilverHashMap.remove (loginName);
        } catch (Exception e) {
            e.printStackTrace ();
            logger.error (e.getMessage ());
        }
    }

    public NetSilverUploadTask(ServiceCommonVo serviceCommonVo,
                               OrgVo orgVo, NetSilverUploadUtil netSilverUploadUtil) {
        this.file = serviceCommonVo.getFile ();
        this.loginName = serviceCommonVo.getLoginName ();
        this.fileId = serviceCommonVo.getFileId ();
        this.iNetSilverWrapperService = serviceCommonVo.getINetSilverWrapperService ();
        this.orgCode = orgVo.getOrgCode ();
        this.orgName = orgVo.getOrgName ();
        this.zoneId = orgVo.getParentCode ();
        this.userName = serviceCommonVo.getUserName ();
        this.cityCode = orgVo.getOwerCity ();
        this.netSilverUploadUtil = netSilverUploadUtil;
    }
}
