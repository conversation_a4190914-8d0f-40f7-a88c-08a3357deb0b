package com.reon.hr.api.customer.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.vo.batchImport.NoEntryBillOrderImportVo;
import com.reon.hr.api.customer.vo.employee.NoEntryBillOrderVo;

import java.util.List;


public interface NoEntryBillOrderWrapperService {

    Page<NoEntryBillOrderVo> getNoEntryBillOrderPage(NoEntryBillOrderVo args);
    void trimesterNoEntryBillIgnoreTransfer(String orderNo);


    int updateNoEntryBillOrder(NoEntryBillOrderVo args);

    void handleBatchUpdateNoEntryBillOrder(ImportDataDto<NoEntryBillOrderImportVo> importDataDto);
}
