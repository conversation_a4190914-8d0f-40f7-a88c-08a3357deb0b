<?xml version="1.0" encoding="UTF-8"?>
<!-- Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出。 -->
<!-- monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数。 -->
<Configuration status="warn">
	<Appenders>
		<!--这个输出控制台的配置 -->
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout
				pattern="[%p][%d{yyyy-MM-dd HH:mm:ss.SSS}] %c{2}[%L] - %m %n" />
		</Console>
	</Appenders>
	<!--然后定义logger，只有定义了logger并引入的appender，appender才会生效 -->
	<Loggers>
		<Logger name="org.springframework.*" level="DEBUG" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>
		<Logger name="com.alibaba.dubbo.rpc.protocol.rest.support.LoggingFilter" level="WARN" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>
		<Logger name="com.reon.hr.sp.report.*" level="DEBUG" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>
		<Logger name="com.reon.hr.sp.report.dao" level="DEBUG" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>
		
		<!--建立一个默认的root的logger -->
		<Root level="INFO">
			<AppenderRef ref="Console" />
		</Root>
	</Loggers>
</Configuration>
