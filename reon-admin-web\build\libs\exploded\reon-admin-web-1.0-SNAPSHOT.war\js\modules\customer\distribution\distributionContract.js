var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer,
        tableSelect = layui.tableSelect;

    // 城市数据集合
    var companyInfo = [];

    // 列表数据集合
    var pageList = [];

    // 渲染列表
    table.render({
        id: 'DistributionGrid',
        elem: '#DistributionGridTable'
        , data: pageList
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , where: { "paramData": JSON.stringify(serialize("searchForm")) }
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            ML.hideNoAuth();
        }
        , cols: [[
            { field: '', type: 'checkbox' }
            , { title: '序号', type: 'numbers' }
            , { field: 'custName', title: '客户名称', align: 'center' }
            , { field: 'contractNo', title: '合同编号', align: 'center' }
            , { field: 'contractName', title: '合同名称', align: 'center' }
            , {
                field: 'contractType', title: '合同类型', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CONTRACT_CATEGORY", d.contractType);
                }
            }
            , {
                field: 'salaryCommissioner', title: '薪资客服', align: 'center', templet: function (d) {
                    return ML.loginNameFormater(d.salaryCommissioner);
                }
            }
            , {
                field: 'distCom', title: '派单分公司', align: 'center', templet: function (d) {
                    return companyCodeToName(d.distCom)
                }
            }
            , {
                field: 'disCommissioner', title: '派单客服', align: 'center', templet: function (d) {
                    return ML.loginNameFormater(d.disCommissioner);
                }
            }
            , { field: 'validDate', title: '生效日期', align: 'center' }
        ]]
    });

    // 客服下拉列表框
    function renderingService(cityCode, type) {
        var service = ' <input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="loginName" placeholder="登录名称" autocomplete="off" class="layui-input">\n' +
            '            <input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="userName" placeholder="姓名" autocomplete="off" class="layui-input">' +
            '<input style="display:none" type="text" value="' + cityCode + '" name="cityCode" placeholder="城市" autocomplete="off" class="layui-input" >' +
            '<input style="display:none" type="text" value="' + type + '" name="type" placeholder="类型" autocomplete="off" class="layui-input" >'
        tableSelect.render({
            elem: '#service',
            checkedKey: 'loginName',
            appd: service,
            table: {
                url: ML.contextPath + '/customer/distribution/getService',
                where: { "searchParam": '{"loginName":"","userName":"","cityCode": '+'"' + cityCode + '"'+', "type": ' + type + '}' },
                cols: [[
                    { type: 'radio' },
                    , { field: 'loginName', title: '登录名称', align: 'center' }
                    , { field: 'userName', title: '姓名', align: 'center' }
                    , { field: 'positionName', title: '岗位名称', align: 'center' }
                ]]
            },
            done: function (elem, data) {
                var NEWJSON = [];
                layui.each(data.data, function (index, item) {
                    NEWJSON.push(item.loginName)
                })
                elem.val(NEWJSON.join(","))
            }
        })
    }


    // 监听表格上方按钮
    table.on('toolbar(DistributionGridTable)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        $("#csType").val(1)
        switch (obj.event) {
            case 'distibution':
                if (obj.event == 'distibution' && checkStatus.data.length == 0) {
                    return layer.msg("请选中一行");
                }
                distibution("分配项目客服", data,4);
                break;
            case 'distibutionSalary':
                if (checkStatus.data.length == 0) {
                    return layer.msg("请选中一行");
                }
                $("#csType").val(5)
                distibution("分配薪资客服", data,5);
                break;
            case 'distibutionAll':
                if (checkStatus.data.length == 0) {
                    return layer.msg("请选中一行");
                }
                distibution("分配薪资客服", data,6);
                break;
            case 'distributeHistory':
                if (obj.event == 'distributeHistory' && checkStatus.data.length != 1) {
                    return layer.msg("请选中一行");
                }
                distributeHistory("分配日志", data[0].relativeNo, data[0]);
                break;
        }
    });

    $('#btnQuery').on('click', function () {
        getPageList(JSON.stringify(serialize("searchForm")))
    });

    /*分配*/
    function distibution(title, obj,distType) {
        // 遍历obj, 获取需要分配的大合同编号 或 小合同编号
        var ids = [];
        $.each(obj, function (i, item) {
            ids.push(item.relativeNo);
        });
        // 获取分配的合同类型
        var csType = $("#csType").val();
        // 获取分配的分公司
        var distCom = $("#distCom").val();
        if($("#replaceAuth").val()==2){
            distCom =$("#signCom").val();
        }
        if (!csType || !distCom) {
            return layer.msg("请先选择派单分公司！");
        }

        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['50%', '50%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/customer/distribution/distibution?id=' + ids + '&csType=' + csType + '&distCom=' + distCom+ '&distType=' + distType,
            end: function () {
                getPageList(JSON.stringify(serialize("searchForm")));
            }
        });
    }

    /*分配历史日志*/
    function distributeHistory(title, id, data) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['80%', '70%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/customer/distribution/distributeHistory',
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);

                body.find("#relativeNo").val(id);
            },
        });
    }


    /*监听选择框*/
    form.on('select(replaceAuth)', function (data) {
       //切换 权限  是分配大合同还是小合同
        var replaceAuth = data.value;
       //客服类型 根据大合同还是小合同进行选择
        $("#csType").val(replaceAuth);
        if (data.value && $("#distCom").val()) {
            $('#service').attr("disabled", false);
            //客服下拉框
            renderingService($("#distCom").val(), data.value);
        } else {
            $('#service').attr("disabled", true);
        }
        form.render();
    });

    // 获取分公司资源
    function getAllCompany() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contractArea/getCompany",
            dataType: 'json',
            success: function (data) {
                companyInfo = [];
                companyInfo = data;
                $.each(companyInfo, function (i, item) {
                    $("#signCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
                    $("#distCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

    // 获取列表数据
    function getPageList(param) {
        $.ajax({
            type: "POST",
            url: ML.contextPath + "/customer/distribution/getPageList",
            data: { "param": param },
            dataType: 'json',
            success: function (data) {
                pageList = data.data;
                table.reload("DistributionGrid", { data: pageList })
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

    //将code转换成名字
    function companyCodeToName(code) {
        for (var i = 0; i < companyInfo.length; i++) {
            if (companyInfo[i].orgCode === code) {
                return companyInfo[i].orgName;
            }
        }
    }

    // 页面初始化时，调用getAllCompany方法获取分公司资源
    $(document).ready(function () {
        $("#distributionFlag").val("0");
        getAllCompany();
    });

    // 分公司change监控
    form.on('select(distCom)', function (data) {
        if (data.value && $("#replaceAuth").val()==1) {
            $('#service').attr("disabled", false);
            renderingService(data.value, $("#replaceAuth").val());
        } else {
            $('#service').attr("disabled", true);
        }
    });

    $('#reset').on('click', function (){
        $('#service').attr("disabled", true);
    });
});
