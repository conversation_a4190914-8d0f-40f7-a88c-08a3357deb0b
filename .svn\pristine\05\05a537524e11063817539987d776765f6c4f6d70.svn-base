layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 页面加载函数
    $(document).ready(function () {
        setTimeout(res=>{
            $("#cityCode").val(ML.areaFormatter($('#spCityCode').val()));
            $("#orgCodeName").val($("#spOrgCodeStr").val())
            $("#NewBusinessType").val($("#spBusinessType").val())
            $("#NewBusinessSubType").val($("#spBusinessSubTypeStr").val())
            document.getElementById("NewBusinessType").disabled = true;
            setDate()
            form.render('select');
        },100)

    })
    // 渲染表格
    function setDate() {
        table.render({
            id: 'specificBusinessTable',
            elem: '#specificBusinessTable',
            url: ML.contextPath + '/customer/injury/getSpecificBusiness',
            // data: tableData,
            page: false,
            toolbar: '#topbtn',
            height: 450,
            defaultToolbar: [],
            where: {"orgCode": $('#spOrgCode').val(),"businessType":$("#spBusinessType").val(),"businessSubType":$("#spBusinessSubType").val()},
            limit: 50,
            text: {
                none: '暂无数据' // 无数据时展示
            },
            cols: [[
                {type: 'checkbox', width: '50', fixed: 'left'},
                {type: 'id', width: '50', fixed: 'left',hide:true},
                {field: 'definiteBusinessStr', title: '具体业务', width: '12%', align: 'center',fixed: 'left'},
                {field: 'businessTimeLimit', title: '业务时限', width: '8%', align: 'center'},
                {field: 'handleTimeLimit', title: '办理时限', width: '8%', align: 'center'},
                {field: 'needPay', title: '是否需要支付', width: '6%', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.needPay);
                    }},
                {field: 'bankNo', title: '银行账户', width: '10%', align: 'center'},
                {field: 'bankName', title: '账户名称', width: '10%', align: 'center'},
                {field: 'bank', title: '开户行', width: '10%', align: 'center'},
                {field: 'payObject', title: '社保公积金中心支付对象', width: '10%', align: 'center'},
                {field: 'fileData', title: '办理资料', width: '9%', align: 'center',  templet: function (d) {

                        return "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoAgentContView'>" + '点击查看' + "</a>";

                    }},
                {
                    field: 'stopStatus', title: '停用状态', align: 'center', width: "8%", templet: function (d) {
                        if (d.stopStatus===1) {
                            return "未停用";
                        }else if (d.stopStatus===2){
                            return '停用';
                        }

                    }
                }
            ]] ,done: function () {
                ML.hideNoAuth();
            }
        });
    }


// 监听表格单元格点击
    table.on('tool(specificBusinessTableFilter)', function (obj) {

        switch (obj.event) {
            case 'gotoAgentContView':
                layer.open({
                    type: 2,
                    title: '办理资料',
                    area: ['60%', '70%'],
                    maxmin: true,
                    offset: 'auto',
                    shade: [0.8, '#393D49'],
                    content: ML.contextPath + '/customer/injury/getInjuryFileBaseInfoView',
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        body.find("#baseInjuryId").val(obj.data.id);
                    },
                    end: function () {
                        table.reload("employeeContractGrid")
                    },
                })
                break;

        }

    })

    table.on('toolbar(specificBusinessTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event){
            case 'update' :
                if (checkStatus.data.length !== 1){
                    return layer.msg("请选中一行");
                }
                openView("编辑", "/customer/injury/gotoUpdateInjuryBaseInfo?id=" + checkStatus.data[0].id);
                break;
            case 'stop' :
                if (checkStatus.data.length !== 1){
                    return layer.msg("请选中一行");
                }
                if (checkStatus.data[0].stopStatus === 2){
                    return layer.msg("此数据已经停用,不可再次停用");
                }
                stop("停用",  {"id": checkStatus.data[0].id,'status':2});
                break;
            case 'enable' :
                if (checkStatus.data.length !== 1){
                    return layer.msg("请选中一行");
                }
                if (checkStatus.data[0].stopStatus === 1){
                    return layer.msg("此数据已为启用状态,不可再次启用");
                }
                stop("启用",  {"id": checkStatus.data[0].id,'status':1});
                break;
            case 'addOtherBusiness' :
                openView("新增其他具体业务", "/customer/injury/gotoAddOtherBusinessBaseInfo");
                break;
            case 'del' :
                if (checkStatus.data.length !== 1){
                    return layer.msg("请选中一行");
                }
                del("删除",  {"id": checkStatus.data[0].id});
                break;
        }
    })

    function openView(title, url) {
        layer.open({
            type: 2, // 为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['80%', '90%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + url,
            success:function (layero, index) {
                var body = layer.getChildFrame('body', index);
                body.find("#orgCodeS").val($('#spOrgCode').val());
                body.find("#businessTypeS").val($("#spBusinessType").val());
                body.find("#businessSubTypeS").val($("#spBusinessSubType").val());
                body.find("#businessSubTypeStr").val($("#spBusinessSubTypeStr").val());
                body.find("#editCityCode").val($('#spCityCode').val());
                body.find("#orgCodeStr").val($("#spOrgCodeStr").val());

            },
            end: function () {
                // table.reload("customerGridTable")
                reloadTable()
            }
        });
    }






    function stop(title,param) {
        layer.confirm("确认要" + title + "吗？", {title: title + "确认"}, function (index) {
            layer.close(index);
            ML.ajax("/customer/injury/stopInjuryBaseInfo", param, function (result) {
                layer.msg(result.msg);
                reloadTable();
            });

        });
    }

    function del(title,param) {
        layer.confirm("确认要" + title + "吗？", {title: title + "确认"}, function (index) {
            layer.close(index);
            ML.ajax("/customer/injury/delInjuryBaseInfo", param, function (result) {
                layer.msg(result.msg);
                reloadTable();
            });

        });
    }

    function reloadTable() {
        setDate()
    }




})