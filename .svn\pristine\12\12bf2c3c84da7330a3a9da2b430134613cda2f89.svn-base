package com.reon.hr.sp.report.rabbitmq.listener;

import com.alibaba.fastjson2.JSONObject;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.TaskLogWrapperService;
import com.reon.hr.api.bill.enums.TaskTypeEnum;
import com.reon.hr.api.bill.vo.TaskLogVo;
import com.reon.hr.api.customer.dubbo.service.rpc.CustomerBatchDownLoadMqService;
import com.reon.hr.api.customer.dubbo.service.rpc.CustomerOrderAndInsuranceDiffMqService;
import com.reon.hr.rabbitmq.AbstractConsumerListener;
import com.reon.hr.rabbitmq.context.MqContext;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ConsumerScopeTypeBill;
import com.reon.hr.rabbitmq.enums.report.ConsumerScopeTypeReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年07月10日
 * @Version 1.0
 */

@Component
@Slf4j
public class BatchExportListener extends AbstractConsumerListener {
    private final static String QUEUE_NAME = "task.processing.queue";

    @Resource
    private MqContext mqContext;

    @Resource
    private TaskLogWrapperService taskLogWrapperService;

    @Resource
    private CustomerBatchDownLoadMqService customerBatchDownLoadMqService;

    @Resource
    private CustomerOrderAndInsuranceDiffMqService customerOrderAndInsuranceDiffMqService;



    @Override
    protected void doWork(String message) {
        log.info("任务处理器 start, message = :{}", message);
        TaskLogVo taskLogVo = JSONObject.parseObject(message, TaskLogVo.class);
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getByCode(taskLogVo.getTaskType());
        switch (taskTypeEnum){
            case ORDER_FEE_EXPORTS:
                customerBatchDownLoadMqService.generateDownLoadCustomerOrderFeeTask(message);
                break;
            case BULK_INVOICE_DOWNLOADS:
            case SOCIAL_SECURITY_PROVIDENT_FUND_EXPORTS:
            case INVOICING_REQUISITIONS_EXPORTS:
                taskLogWrapperService.processTask(message);
                break;
            case ORDER_INSURANCE_DIFF_EXPORTS:
                customerOrderAndInsuranceDiffMqService.generateDownLoadCustomerOrderAndInsuranceDiffTask(message);
                break;
            default:
        }
        log.info("任务处理器 end, message = :{}", message);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        super.init(ModuleType.REON_BATCH_EXPORT, ConsumerScopeTypeReport.REON_GENERATED_BATCH_DOWNLOADS_TASK, mqContext, QUEUE_NAME);
    }
}
