package com.reon.hr.sp.report.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

import com.reon.hr.sp.report.entity.SbspDetail;
import com.reon.hr.sp.report.dao.report.SbspDetailMapper;
import com.reon.hr.sp.report.service.report.SbspDetailService;
@Service
public class SbspDetailServiceImpl extends ServiceImpl<SbspDetailMapper, SbspDetail> implements SbspDetailService{

    @Override
    public Integer deleteByMonth(int month) {
        return baseMapper.deleteByMonth(month);
    }
}
