<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote" style="margin: 0 auto;">
    <form class="layui-form" id="searchForm" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" style="font-weight:800">城市</label>
                <div class="layui-input-inline" style="width: 50%;">
                    <select class="layui-select" lay-search name="cityCode" id="cityCode" lay-filter="cityFilter" AREA_TYPE >
                        <option value=""></option>
                    </select>
                </div>
            </div>
        </div>
        <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" >查询</button>
        <button class="layui-btn layui-btn-sm" type="reset">重置</button>
    </form>
</blockquote>
<div style="width: 70%; margin: 0 auto;">
    <table class="layui-hide" id="groupGrid" lay-filter="groupFilter"></table>
</div>

<input type="hidden" id="parentIframeIndex" >
<input type="hidden" id="parentWindowName" >
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/change/selectInsuranceGroup.js?v=${publishVersion}"></script>
<script type="text/jsp" id="select">
    <button class="layui-btn layui-btn-sm" id="selectOne" lay-event="select">选择</button>
</script>
</body>
</html>
