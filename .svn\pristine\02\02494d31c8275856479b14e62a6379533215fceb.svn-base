<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>拉取银行流水</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<%--隐藏域--%>
<input id="pcId" value="" style="display:none;"/>
<input id="pcBalance" value="" style="display:none;"/>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="财务应收年月">日期：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" maxlength="20" name="searchTime"
                               id="searchTime" placeholder="请选择" readonly>
                    </div>
                </div>

                <div class="layui-inline" id="timeFlag" style="display: none">
                    <label class="layui-form-label layui-elip" title="是否拉取银行流水">是否拉取银行流水</label>
                    <div class="layui-input-inline">
                        <%--                        <input type="checkbox" name="hasSearch" id="hasSearch" lay-text="选中">--%>
                        <input type="checkbox" name="hasSearch" id="hasSearch" title="开启|关闭" lay-skin="switch">
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                       lay-submit="">确认</a>
                    <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/preventAccuracyLoss.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/BigDecimal-all-last.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/defaultComputerJs.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/financial/searchBankLogPage.js?v=${publishVersion}"></script>
</body>
</html>
