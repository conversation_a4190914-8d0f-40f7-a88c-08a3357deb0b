package com.reon.ehr.api.sys.vo.base;


import com.reon.ehr.api.sys.vo.BaseVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EhrImportDataVo extends BaseVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 导入编号
     * */
    private String importNo;

    /**
     * 导入数据类型(1:批量客户导入，2.批量离职申请,3批量社保账号导入)
     */
    private Integer dataType;
    /**
     * 导入人
     */
    private String oprMan;
    /**
     * 导入时间
     */
    private Date oprTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 成功记录数
     */
    private Integer successNum;
    /**
     * 失败记录数
     */
    private Integer failNum;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 处理状态(0,未处理，1:处理中，2：已处理)
     */
    private Integer status;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;
    /**
     * 格式化导入数据类型(1:批量客户导入，2.批量离职申请)
     */
    private String iType;
    /**
     * 格式化处理状态(0,未处理，1:处理中，2：已处理)
     */
    private String iStatus;
    /**
     * 导入类型(1:代表唯一号导入,2:代表身份证导入)
     */
    private String importType;
    /**
     * 社保公积金组编号
     */
    private String groupCode;

    private String groupName;

    private String text;


    public String getiType() {
        if (getDataType () != null) {
            if (getDataType () == 1) {
                this.iType = "批量客户导入";
            } else if (getDataType () == 2) {
                this.iType = "批量离职申请";
            }
        }
        return iType;
    }

    public void setiType(String iType) {
        this.iType = iType;
    }

    public String getiStatus() {
        if (getStatus () != null) {
            if (getStatus () == 0) {
                this.iStatus = "未处理";
            } else if (getStatus () == 1) {
                this.iStatus = "处理中";
            } else if (getStatus () == 2) {
                this.iStatus = "已处理";
            }
        }
        return iStatus;
    }


}
