package com.reon.hr.sp.customer.job.customer;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAssignResourceWrapperService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Exception.class)
public class ContractAssignJob implements SimpleJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IContractAssignResourceWrapperService contractAssignResourceWrapperService;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            logger.info("======================"+shardingContext.getJobName()+" start=============================");
            //更新小合同
            contractAssignResourceWrapperService.updateContractArea();
            //更新大合同
            contractAssignResourceWrapperService.updateTheContract();
            logger.info("======================"+shardingContext.getJobName()+" end=============================");
        } catch (Exception e) {
            logger.error("ContractAssignJob exception:",e);
            throw new RuntimeException();
        }
    }


}
