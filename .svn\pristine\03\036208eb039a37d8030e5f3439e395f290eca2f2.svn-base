<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /*去掉type=number时的上下加减按钮*/
        /* 谷歌 */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        /* 火狐 */
        input{
            -moz-appearance:textfield;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post">
    <input type="hidden" id="optType">
    <table class="layui-table" lay-skin="nob" style="width: 65%;">
        <input id="vo" value='${vo}'  type="hidden">
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>公司名称：</td>
            <td><input class="layui-input" type="text" name="orgName" autocomplete="off" maxlength="50" id="orgName" lay-verify="required" disabled></td>
        </tr>
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>服务费：</td>
            <td><input class="layui-input" type="text" oninput = "value=value.replace(/[^\d]/g,'')" name="serviceAmt" id="serviceAmt" maxlength="22" minlength="10" autocomplete="off" lay-verify="required|number"></td>
        </tr>
    </table>
    <div id="logDiv" hidden="hidden">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>修改日志</legend>
        </fieldset>
        <div class="layui-input-block" style="width: 65%;">
            <table id="logTable" lay-filter="floatInsuranceFilter"></table>
        </div>
    </div>
    <div style="float: right; margin-right: 40%;" >
        <button class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn" >保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/insurancePractice/addInsurancePracticeServiceConfigPage.js?v=${publishVersion}"></script>
</body>
</html>