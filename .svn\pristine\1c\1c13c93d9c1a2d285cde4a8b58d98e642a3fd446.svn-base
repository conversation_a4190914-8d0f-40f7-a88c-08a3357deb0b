package com.reon.hr.api.vo.corp;

import java.io.Serializable;
import java.math.BigDecimal;

public class RechargeAuditVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 渠道编号
	 */
	private String channelNo;
	/**
	 * 渠道名称
	 */
	private String channelName;

	/**
	 * 审核状态(1:通过，2：拒绝)
	 */
	private String auditStatus;
	/**
	 * 申请人
	 */
	private String applicant;
	/**
	 * 审核人
	 */
	private String auditor;

	private String baseId;

	/**
	 * 审核时间
	 */
	private String auditTime;
	/**
	 * 申请时间
	 */
	private String applyTime;

	/**
	 * 充值类型
	 */
	private String rechargeType;
	/**
	 * 银行
	 */
	private String bankName;
	/**
	 * 银行卡号
	 */
	private String cardNo;
	/**
	 * 充值金额
	 */
	private BigDecimal rechargeAmt;
	/**
	 * 充值时间
	 */
	private String rechargeTime;

	/**
	 * 负责人
	 */
	private String principal;
	/**
	 * 充值记录的流水号
	 */
	private String rechargeRecordId;

	public String getChannelNo() {
		return channelNo;
	}

	public void setChannelNo(String channelNo) {
		this.channelNo = channelNo;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(String auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getApplicant() {
		return applicant;
	}

	public void setApplicant(String applicant) {
		this.applicant = applicant;
	}

	public String getAuditor() {
		return auditor;
	}

	public void setAuditor(String auditor) {
		this.auditor = auditor;
	}

	public String getBaseId() {
		return baseId;
	}

	public void setBaseId(String baseId) {
		this.baseId = baseId;
	}

	public String getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(String auditTime) {
		this.auditTime = auditTime;
	}

	public String getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	public String getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public BigDecimal getRechargeAmt() {
		return rechargeAmt;
	}

	public void setRechargeAmt(BigDecimal rechargeAmt) {
		this.rechargeAmt = rechargeAmt;
	}

	public String getRechargeTime() {
		return rechargeTime;
	}

	public void setRechargeTime(String rechargeTime) {
		this.rechargeTime = rechargeTime;
	}

	public String getPrincipal() {
		return principal;
	}

	public void setPrincipal(String principal) {
		this.principal = principal;
	}

	public String getRechargeRecordId() {
		return rechargeRecordId;
	}

	public void setRechargeRecordId(String rechargeRecordId) {
		this.rechargeRecordId = rechargeRecordId;
	}

}
