package com.reon.hr.api.customer.dto.customer;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import lombok.Data;

import java.io.Serializable;


@Data
@ExcelIgnoreUnannotated
public class BatchEditProdExpireMonthDTO extends BaseImportDto implements Serializable {


    /**
     订单编号*
     */
    @ExcelProperty("订单编号*")
    private String orderNo;

    /**
     客户编号
     */
    @ExcelProperty("客户编号*")
    private String custNo;

    /**
     产品名称
     */
    @ExcelProperty("产品类型*")
    private String productTypeName;
    /** 产品code */
    private Integer productCode;

    /**
     收费开始月
     */
    @ExcelProperty("产品截止月*(例:202210)")
    private String expireMonth;

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo == null ? null : custNo.trim();
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName == null ? null : productTypeName.trim();
    }

    public void setExpireMonth(String expireMonth) {
        this.expireMonth = expireMonth == null ? null : expireMonth.trim();
    }
}
