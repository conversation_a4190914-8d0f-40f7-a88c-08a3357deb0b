<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <div class="layui-inline queryTable">
        <form class="layui-form" id="searchForm" action="" method="post">
            <div class="layui-input-inline">
                <label class="layui-form-label" style="font-weight:800">所属城市</label>
                <div class="layui-input-block">
                    <select name="selectarea"  id="selectarea" lay-verify="selectarea" AREA_TYPE lay-search >
                        <option value="" selected>-请选择</option>
                    </select>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " style="font-weight:800">分类名称</label>
                <div class="layui-input-inline">
                 <input type="text"  name="searchName" maxlength="20" class="layui-input" autocomplete="off">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " style="font-weight:800">分类编码</label>
                <div class="layui-input-inline">
                <input type="text"  id="code" maxlength="20" name="searchCode" class="layui-input" autocomplete="off">
                </div>
            </div>
            <a class="layui-btn" data-type="reload"  lay-filter="btnQuery" authURI="/sys/IndividualCategory/getList" lay-submit="">检索</a>
            <button class="layui-btn" id="reset" type="reset" authURI="/sys/IndividualCategory/reset">重置</button>
        </form>
    </div>
</blockquote>
<table id="category" lay-filter="category"></table>

<script type="text/jsp" id="btn">
    <a id="check" href="javascript:void(0)" title="查看" authURI="/sys/IndividualCategory/gotoCheckCategoryView" lay-event="check"><i class="layui-icon layui-icon-search"></i></a>
    <a  id="update" href="javascript:void(0)" title="修改" authURI="/sys/IndividualCategory/addView" lay-event="update"><i class="layui-icon layui-icon-edit"></i></a>
    <a  id="del" href="javascript:void(0)" title="删除" authURI="/sys/IndividualCategory/delCategory" lay-event="del"><i class="layui-icon layui-icon-delete"></i></a>
</script>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="add" lay-event="add" authURI="/sys/IndividualCategory/addView">新增</button>
        <button class="layui-btn layui-btn-sm" lay-event="update" authURI="/sys/IndividualCategory/addView">修改</button>
        <button class="layui-btn layui-btn-sm" lay-event="del" authURI="/sys/IndividualCategory/delCategoryBatch">删除</button>
        <button class="layui-btn layui-btn-sm" lay-event="export" authURI="/sys/IndividualCategory/export">数据导出</button>
    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/sys/individualcategory/individualcategory.js?v=${publishVersion}"></script>
</body>
</html>
