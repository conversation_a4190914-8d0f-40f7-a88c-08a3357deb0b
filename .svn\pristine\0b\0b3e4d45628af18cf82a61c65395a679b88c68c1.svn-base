package com.reon.hr.sp.report.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.report.vo.NotInPracticeOrderVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年04月30日
 * @Version 1.0
 */
public interface NotInPracticeOrderService {


    void insertNotInPracticeOrder(NotInPracticeOrderVo notInPracticeOrderVo);

    Page<NotInPracticeOrderVo> getNotInPracticeOrderVoPage(Integer page, Integer limit, NotInPracticeOrderVo notInPracticeOrderVo);

    List<NotInPracticeOrderVo> getNotInPracticeOrderVos(NotInPracticeOrderVo notInPracticeOrderVo);
}
