package com.reon.hr.api.customer.vo;


import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 新增变更订单查询list */
@Data
public class ChangeTempletFindParaVo implements Serializable {


    /*查询上方表格参数*/

    /**
     客户ID
     */
    private Long custId;

    /**
     收费起始月
     */
    private Integer revStartMonth;

    /**
     账单模板ID
     */
    private Long templetId;

    /**
     收费模板ID
     */
    private Long revTempId;

    /**
     签约方公司抬头
     */
    private String signComTile;
    /**
     失败原因
     */
    private String failReason;


    /*查找下方表格参数*/
    private String employeeNo;

    private String employeeName;

    private Integer certType;

    private String certNo;

    private String contractAreaNo;

    private String contractAreaName;

    private String orderNo;

    private String contractNo;//大合同号

    private String locatInput;//唯一号/姓名/证件号码

    private Integer status;

    //将查出来的状态转成String返回前端
    private String statusStr;

    private List<OrderInsuranceCfgVo> productCodeAndTempletIdAndRevTempIdList;
    private Integer eedStatus;
}
