package com.reon.hr.sp.customer.entity.insurancePractice.practiceAdjust;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**g
 * @Author: Administrator
 * @Description:
 * @Date: 2022/12/6 14:08
 * @Version: 1.0
 */

@Data
public class PracAdjustCfg implements Serializable {
    private Long id;
    /**
     *调整id
     */
    private String adjustId;
    /**
     * 产品类型
     */
    private Integer prodType;
    /**
     * 旧比例Code
     */
    private String oldRatioCode;
    /**
     * 新比例Code
     */
    private String newRatioCode;
    /**
     * 企业金额
     */
    private BigDecimal comCol;
    /**
     * 个人金额
     */
    private BigDecimal indCol;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;
}
