var ctx = ML.contextPath;
layui.use(['form', 'layer', 'laydate', 'table'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    form.render('select');
    form.on('select(provinceFilter)',function (data) {
        $("#cityCode").attr("CITYS",data.value+"0000");
        ML.cityOption(data.value+"0000");
        form.render('select');
    });
    var  param = serialize("searchForm");
    param.supplierType =1;
    table.render({
        id: 'supplierQuoGrid',
        elem: '#supplierQuoGrid',
        url: ctx + '/customer/supplierQuo/getSupplierQuotationRemark',
        where: {"paramData": JSON.stringify(param)},
        method: 'post',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        limit:50,
        height:650,
        title: "供应商信息",
        toolbar: '#toolbarDemo',
        defaultToolbar: [],
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%'},
            {field: 'supplierName', title: '供应商名称', width: '12%',align:'center'},
            {field: 'supQuotationNo', title: '报价单编号', width: '12%',align:'center'},
            {field: 'cityName', title: '城市名称', width: '12%',align:'center'},
            {field: 'socialPrice', title: '社保委托服务费成本', width: '10%',align:'center'},
            {field: 'salaryPrice', title: '发薪服务费成本', width: '10%',align:'center'},
            {field: 'remark', title: '备注', width: '20%',align:'center'},
        ]],

    });






////查询
    form.on('submit(btnQueryFilter)', function (data) {
        reloadTable();
        return false;
    });

    function reloadTable() {
        param =  serialize("searchForm");
        param.supplierType =1;//社保供应商
        table.reload('supplierQuoGrid', {
            where: {
                paramData: JSON.stringify(param),
                page: {curr: 1} //重新从第 1 页开始
            }
        });
    }



});