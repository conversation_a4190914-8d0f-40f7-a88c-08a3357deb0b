package com.reon.hr.api.customer.vo.employee.ehr;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reon.hr.api.customer.dto.customer.CustomerDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2023/5/24.
 */
@EqualsAndHashCode()
@Data
public class BillInfoVo implements Serializable {
    /**
     * 客户id
     */
    private Long custId;
    private List<Long> custIdList;
    private List<CustomerDto> customerDtoList;

    /**
     * 往前查几个月，包含本月
     */
    private Integer beforeMonth;
    /**
     * 月份
     */
    private String month;

    /**
     * 服务费
     */
    private BigDecimal totalServiceFee;
    /**
     * 总额
     */
    private BigDecimal totalReceiveAmt;
    /**
     * 已核销金额
     */
    private BigDecimal totalCheckedAmt;
    /**
     * 未核销金额
     */
    private BigDecimal totalUncheckAmt;
    /** 搜索值 */
    @JsonIgnore
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;
}
