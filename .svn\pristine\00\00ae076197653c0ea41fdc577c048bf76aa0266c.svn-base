package com.reon.hr.api.customer.dubbo.service.rpc.customer;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.contract.GenetateNormContractDto;
import com.reon.hr.api.customer.dto.customer.*;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.commInsurOrder.ContractTempletVo;
import com.reon.hr.api.customer.vo.group.GroupVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IContractResourceWrapperService {

    /**获取page
     * @param page
     * @param limit
     * @param contractPageDTO 查询条件
     * @return
     */
    public Page<ContractPageVo> getPage(int page, int limit, ContractPageDTO contractPageDTO);
    public Page<ContractPageVo> getAppOverPage(int page, int limit, ContractPageDTO contractPageDTO);

    List<ContractPageVo> getAllSellerList(Integer type);
    List<ContractPageVo> getPage(ContractPageDTO contractPageDTO);

    Page<ContractPageVo> getContractSelectTable(int page, int limit, ContractPageDTO contractPage);

    /**
     * 保存合同信息
     * @param contractDTO
     */
    public ContractDTO saveFullContractInfo(ContractDTO contractDTO);

    /**
     * 根据流程Id，流程类型获取合同编号
     * @param pid 流程ID
     * @param contractProcType 流程类型
     * @return 合同编号
     */
    public String findContractNoByPidAndProcType(String pid,String contractProcType);

    /**
     * 更新合同流程信息
     * @param contractProcessDTO
     * @return 是否更新成功
     */
    public boolean updateContractProcessInfo(ContractProcessDTO contractProcessDTO);

    /**
     * 根据合同编号获取合同数据
     *
     * @param contractNo 合同编号
     * @return {@link ContractPageVo}
     */
    ContractPageVo getContractData(String contractNo);

    /**
     * 根据合同编号获取须知信息
     *
     * @param contractNo 合同编号
     * @return {@link String}
     */
    List<String> getNoticeInfo(String contractNo);
    ContractVo findbyContractNo (String contractNo);
    List<ContractVo> findbyContractNoList(List<String> contractNo);
    List<ContractVo> findbyAssociatedContractNoList(List<String> contractNoList);

    List<ContractVo> getByContractNoList(List<String> contractNoList);

    /**
     * 获取数据，判断是续签还是新增合同
     * @param contractNo
     * @param type
     * @return
     */
    ContractPageVo getContractData(String contractNo,String type,String pid);

    /**
     * 根据合同编号获取文件
     * @param contractNo
     * @return
     */
    List<ContractAttachmentVo> findAttachmentbyContractNo(String contractNo);

    /**
     * 更新合同信息
     * @param contractDTO
     * @return
     */
    public boolean updateFullContract(ContractDTO contractDTO);

    boolean delByContractNo(String contractNo);

    /**项目启动会议保存
     * @param contractPageVo
     * @param loginName
     * @return
     */
    boolean saveMeeting(ContractPageVo contractPageVo,String loginName);

    /**查找会议纪要信息
     * @param contractNo
     * @return
     */
    List<MeetingRecordVo> findOneByContractNo(String contractNo);

    /**
     * @param id
     * @return
     */
    CustomerVo findCustomerById(Long id);

    /**
     * @param customerVo
     * @return
     */
    boolean updateCustomerById(CustomerVo customerVo,String loginName);

    /**
     * @param contractVoList
     * @return
     */
    boolean delContractInfo(List<ContractVo> contractVoList);

    List<String> deleteRejectContract(List<ContractVo> contractVoList);

    Page<QuotationVo> searchSelectByOrderChange(int page, int limit, String param);


    Page<CustomerVo>  getCurrentLoginCustomer(int page,int limit,String param,String currLogin);

    Page<CustomerVo>  getCustomerByTemplateId(int page,int limit,String param);

    List<ContractPageVo> getListSelective(ContractPageVo contractPage);

    Page<ContractPageVo> getPageByName(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getPageByCustId(Integer page, Integer limit, String custId);

    Page<ContractPageVo> getCustomerPageByName(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 查询合同中使用的客户但过滤掉开票信息中已经存在的
     *
     * @param page                   页面
     * @param limit                  限制
     * @param name                   名字
     * @param loginName              登录名
     * @param userOrgPositionDtoList 用户组织位置dto列表
     * @return {@link Page}<{@link ContractPageVo}>
     */
    Page<ContractPageVo> getCustomerWithoutExists(Integer page, Integer limit, String name, String loginName, List<OrgPositionDto> userOrgPositionDtoList);

    boolean updateByContractNo(ContractPageVo contractPageVo);
    Integer updateArchFlagByContractNo(List<Long> contractProcessIdList, Integer type, String loginName, String remark);

    String getArchRemarkByContractNo(String contractNo, String pid);

    /**
     * 自动顺延一年
     * @param contractNo 合同号
     * @return 是否成功
     */
    boolean autoDefer(String contractNo);

    List<ContractPageVo> getContractNameByCustId(Long custId);

    int delByFileId(String fileId);

    /**
     * 根据开票信息维护id查询开票信息维护编号和客户编号
     * @param invoiceId
     * @return
     */
    CustNoAndInvoiceNoVo searchCustNoAndInvoiceNoByInvoiceId(String invoiceId);

    /**
     * 获取所有的客户
     */
    Page<CustomerVo> findCustomerByAll(Integer page, Integer limit, String param, Long groupId);

    /**
     * 根据groupId和合同初始时间、起始时间获取集团名称
     * @param contractVo
     * @param userOrgPositionDtoList
     * @return
     */
    List<GroupVo> getAllGroupNameByCustId(ContractVo contractVo, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 上传最终文件
     * @param fileList 文件id列表
     * @param loginName 用户名
     * @param contractNo 合同编号
     * @return
     */
    Boolean uploadFinalFile(List<String> fileList, String loginName, String contractNo);

    Boolean updateProcessByContract(String bizType, String contractNo);


    Page<ContractPageVo> getContractInfoByKeyword(Integer page, Integer limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getCustomerFromAuthContract(Integer page, Integer limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getContractInfoByKeywordForRoot(int page, int limit, String keyword);
    Page<ContractPageVo> getcontractNameAndNo(Integer page, Integer limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 根据合同终止月查询所有的过期合同
     * @return
     */
    List<ContractPageVo> getContractList();



     Page<CommInsuQuotationVo> getSelectPageByCustId(int page, int limit, String para, String currentLogin);

    /**
     * 根据合同终止月查询往后加一个月即将过期所有合同
     * @return
     */
    List<ContractPageVo> getContractOneMonthBeforeList();
    /*
     * add guoqian 查询出个人订单对应的供应商小合同号,
     *
     * */
    EmployeeContract   getSupplierContract (String contractAreaNo);

    /**
     * 保存续签合同进程和文件信息
     * @param contractDTO
     */
    public Long saveContractProcessAndFileInfo(ContractDTO contractDTO);

    public List<ContractPageVo> getContractList(ContractPageDTO contractPageDTO);


    List<ContractPageVo> getContractByPid(Map<String, Object> stringObjectMap, List<String> processInsList);
    List<ContractPageVo> getContractFlowByPid(List<String> processInsList,String key);
    List<TaskResultVo> getContractByContractName(String contractName);

    public void updateContractProcessAndFileInfo(ContractDTO contractDTO);


    /**
     * @Description
     * @param vo
     * @return com.reon.hr.api.customer.vo.CustomerCostReport
     * <AUTHOR>
     * @Date 2020/8/11 14:13
     */
    CustomerCostReport getQuotationContract(CustomerCostReport vo);

    /**
     * @Description
     * @param costReportList
     * @return com.reon.hr.api.customer.vo.CustomerCostReport
     * @Date 2022/8/24 11:13
     */
    List<CustomerCostReport> getQuotationContractByList(List<CustomerCostReport> costReportList);
    /**合同账单报表查询*/
    List<ContractBillReport> getUseContract(ContractBillReport vo);

    /**
     * 根据合同类型查询合同
     * @param contractTypeList
     * @return
     */
    Page<ContractPageVo> findbyContractTypeList(Integer page, Integer limit,List<Integer> contractTypeList, String param,String currLogin);

    /**
     * 查询所有商保合同
     * @param contractTempletVo
     * @param page
     * @param limit
     * @return
     */
    Page<ContractTempletVo> getContractTempletListPage(ContractTempletVo contractTempletVo,Integer page, Integer limit);

    /**
     * 根据合同类型加编号查询合同
     * @param contractType
     * @param contractNo
     * @return
     */
    List<ContractPageVo> findbyContractType(List<Integer> contractTypeList, String contractNo);


    Page<ContractPageVo> getPageByCustIdAndSearchDate(int page, int limit, ContractPageDTO contractPage, String loginName);

    /**
     * 查询分配完成的大合同
     * @param contractNo
     * @param currLogin
     * @return
     */
//    ContractPageVo getContractNo(String contractNo,String currLogin);


    /**
     * 获取所有的合同关联的账单模板
     * @return
     */
    List<ContractPageVo> getContractTemplates();
    Integer updatePeopleNum(List<ContractPageVo> list);

    ContractPageVo getDataByContractNo(String contractNo);

    List<ContractAreaVo> getGatherRevCsByContractAndTempletId(String contractNo, Long templetId);

    boolean updateSellerByContractNoList(List<ContractVo> contractVoList, int startMonth);

    List<EmployeeContract> getSupplierContractByContractAreaNoList(List<String> contractAreaNoList);

    Page<ContractPageVo> getPageByCustIdAndName(int page, int limit, String name, Long custId);

    Integer saveBindingQuotation(ContractRelativeQuotationVo contractRelativeQuotationVo);

    Integer existData(List<ContractRelativeQuotationVo> contractRelativeQuotationVoList);


    Integer updateContractDefaultQuoteNo();

    String generateNormContract(GenetateNormContractDto contractDto);

    void refresh();

    ContractContentInfoVo getEditItemData(String contractNo);



    void deleteContractInfoAndFileByContractNo(String contractNo);

    Integer insertOrUpdateContractAttachFileId(ContractAttachmentVo contractAttachmentVo);

    ContractProcessDTO getContractProcessById(Long id);

    int updateContractProcessPidAndAppStatus(String pid);

    List<ContractPageVo> getAllContractProcessDTOByStatus(Map<String, Object> stringObjectMap);

    Integer updateContractProcessInfoAndAttachment(ContractProcessDTO contractProcessDTO);

    Integer updateContractProcess(ContractDTO contractDTO);

    List<CustomerVo> getCustomerByCustIdList(List<Long> custIdList);

    List<ContractPageVo> getContractListByContractNoList(List<String> contractNoList);

    PrintContractDto getPrintContractData(Long contractProcessId);

    List<QuotationItemVo> getQuotationItemByContractNo(String contractNo);

    int updateNewFlagByNewFlagAndFirstBillDate(int newFlag,int oldNewFlag,String nowDate);

    List<CustomerEmpTrackReportVo> findEmpTrackReport();
    List<CustomerEmpTrackReportVo> findEmpTrackReport(CustomerEmpTrackReportVo customerEmpTrackReportVo);

    List<ContractVo> findBillReportByContractNoList(List<String> contractNoList);

    List<ContractPageVo> getEffectiveContractListByContractNoList(List<String> contractNoList);

    ContractVo getContractByContractNo(String contractNo);

    List<CustomerVo> findCustomerListAndGroupId();

    List<ContractVo> getContractNameByContractNoList(List<String> contractList);

    int updateNewFlagAndRemark(ContractVo contractVo);

    int setContractLog(Map<String, String> contractNoAndLoginNameMap);

    List<ContractProcessDTO> selectContractProcessByContractNoList(List<String> contractNoList);

    String editComRemark(ContractPageVo contractPageVo);

    List<CustomerContractorExportVo> getCustomerContractor(String loginName);

    List<QuotationItemVo> getQuotationListByContractNo(String contractNo);

    List<ContractVo> getByCustIdAndContractType(List<Long> custIdList, List<Integer> contractTypeList);


    void updateInitStartDate();

    Page getServiceInfo(Integer page, Integer limit, List<CustomerDto> customerDtoList, String userName,Long custId);

    List<Long> selectCustIdListByLoginName(String loginName);

    Map<String, Object> getCustGroupAndCustListByDefaultOrgPosCodeAndLoginNameFromContract(List<OrgPositionDto> orgPosCode );

    List<CustomerEmpTrackReportVo> findOldEmpTrackReportByDto(CustomerEmpTrackReportVo customerEmpTrackReportVo);

    Map<String,Integer>getContractTypeByContractNo(Set<String> contractNoList);

    int saveChangeQuotation(String contractNo,String quoteNo,String longinName);

    List<Map<String, String>> getServiceNature(List<Long> custIdList);

    int saveAddAssociation(String contractNo,String type,String pid,String loginName,List<String> fileIdList,String remark);

    Page<ContractPageVo> getContractPageByCustIdAndInBillTemplet(Integer page, Integer limit, String custId, String name);

    Integer getDataByContractTypeAndNoBelongLoginName(String seller, Integer contractType, Long custId);

    List<String> getContractNoByUserOrgPositionDtoList(List<String> contractNoList,List<OrgPositionDto> userOrgPositionDtoList);

    List<ContractAreaVo> getContractNoAndRecevingByUserOrgPositionDtoList(List<OrgPositionDto> userOrgPositionDtoList);

    Page<ContractPageVo> getSupplierContractInfoByKeyword(Integer page, Integer limit, String keyword,List<OrgPositionDto> userOrgPositionDtoList);

    Map<String, Object> getSupplierCustGroupAndCustListByLoginNameFromContract(List<OrgPositionDto> orgPosCode);

    int autoRefreshContractEndTime();


    Boolean uploadFinalFileByRenewContract(List<String> fileList, String loginName, String contractNo,Long pid);


    List<ContractVo> getContractNoAndCustNameByContractNoList(List<String> contractNoList);

    List<String> getContractNoByContractNoVoAndAuthList(ContractVo contractVo, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 查询外包代理派遣的合同
     * @return
     */
    List<ContractAreaVo> getContractVoByContractType();

    /**
     * 根据小合同号获取大合同类型
     */
    Integer selectContractTypeByContractAreaNo(String contractAreaNo);

    List<Long> selectCustIdListFromEhr();

    List<Long> selectCustIdListByLoginNameAndGroupByContractTypeAndLastComm(String loginName);

    Page<EditContractInfoWorkflowVo> getEditContractInfoList(Map<String, Object> paramMap);

    Page<ContractPageVo> getContractPageForSelect(int page, int limit, String keyword, List<OrgPositionDto> userOrgPositionDtoList);
    Page<ContractPageVo> getContractMySelfPageForSelect(int page, int limit, String keyword, String loginName);
    void saveChangeContractItem(ECInfoAddVo eCInfoAddVo);

    ECInfoAddVo getECIWDataById(Long id, String loginName);

    List<EditContractInfoWorkflowVo> getSpecialContractListByPid(Map<String, Object> map);

    boolean checkContractCanCommit(List<String> contractNoList);

    List<EditContractInfoWorkflowVo> getSpecialDataByContractNo(String contractNo);

    void updatePidByContractNO(String pid, String contractNo);

    void rejectChangeContractItem(String pid, String comment);
    boolean updateContractProcessInfoBySpecial(ContractProcessDTO contractProcessDTO);

    List<ContractVo> getAssociatedContractNoAndSignComTitleByContractNoList(List<String> contractNoList);

    List<PrintAccountExportVo> exportPrintAccount(Map<String, Object> conditionMap);

    void dealLogData();

    List<Map<String, String>> getArchivedOprateList();

    List<ContractVo> getDataByContractNoList(List<String> contractNoList);

    Map<String, Long> getConNoAndCustIdMapByContractNoList(List<String> salaryContractNoList);

    Integer getContractCompletionReminderCountByLoginName(String loginName);

    void dealContractFirst();    List<ContractVo> selectActualPaymentDataByContractNoSet(Set<String> contractNoList);}
