<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>工资支付数据比对页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="generateForm" style="padding-bottom: 100px">
            <div class="layui-form-item" title="生成工资支付个税对比结果">生成工资支付个税对比结果
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="计税月"><i
                            style="color: red">*</i>计税月</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="taxMonth" id="taxMonth" lay-verify="required"
                               autocomplete="off">
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-elip" title="个税申报类型">个税申报类型：</label>
                        <div class="layui-input-inline">
                            <select class="layui-select" name="taxComparisonType" id="taxComparisonType" DICT_TYPE="TAX_COMPARISON_TYPE" lay-search>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <button class="layui-btn layui-btn-sm" id="insertSalaryPaymentTaxComparison"
                            authURI="/customer/salary/salaryPayment/insertSalaryPaymentTaxComparison">生成个税比对数据
                    </button>
                </div>
            </div>
            <div class="layui-form-item" title="生成工资支付实发对比结果">生成工资支付实发对比结果
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="支付年月"><i
                            style="color: red">*</i>支付年月</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="paymentDate" id="paymentDate" lay-verify="required"
                               autocomplete="off">
                    </div>
                    <button class="layui-btn layui-btn-sm" id="insertSalaryPaymentComparison"
                            authURI="/customer/salary/salaryPayment/insertSalaryPaymentComparison">生成数据
                    </button>
                </div>
            </div>
        </form>
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="姓名">姓名：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable"
                               id="empName" name="empName" placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="证件号">证件号：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="certNo" maxlength="20" name="certNo"
                               placeholder="请输入"
                               class="layui-input layui-input-disposable" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="数据年月" style="font-weight:800; width:100px"><i style="color: red">*</i>数据年月：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input layui-input-disposable" lay-verify="required"
                               id="paymentDateSearch" name="paymentDateSearch" placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="扣缴义务人">扣缴义务人：</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="withholdingAgentNo" id="withholdingAgentNo" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                       lay-submit="">查询</a>
                    <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">重置</button>
                </div>
            </div>
        </form>

    </div>
</div>
<div class="layui-card-body">
    <table class="layui-hide" id="salaryPaymentComparisonGrid" lay-filter="salaryPaymentComparisonGridFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <button class="layui-btn layui-btn-sm" id="export" lay-event="export" authURl="/customer/salary/salaryPayment/exportSalaryPaymentComparisonList">导出</button>
    </script>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/withholdingAgent/withholdingAgentNoSelect.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/salaryPayment/salaryPaymentDataComparison.js?v=${publishVersion}"></script>
</body>
</html>
