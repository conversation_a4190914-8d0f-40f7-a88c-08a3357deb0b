<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<form class="layui-form" action="">
    <table class="layui-table" lay-skin="nob" style="width: 80%;">
        <tr>
            <td align="right"><i id="redRequired" style="color: red;">*</i>反馈备注</td>
            <td align="left">
                <textarea id="remark" name="remark" placeholder="请输入内容" class="layui-textarea" lay-verify="required"></textarea>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript">
    var ctx = ML.contextPath;
    layui.config({
        base: ctx + "/js/"
    }).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            tableSelect = layui.tableSelect;
        var layer = parent.layer === undefined ? layui.layer : parent.layer;

    })
</script>
</body>
</html>