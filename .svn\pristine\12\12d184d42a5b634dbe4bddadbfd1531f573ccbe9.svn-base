package com.reon.hr.api.customer.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;

/**
 * <AUTHOR> on 2021/10/13.
 */
public class CopyUtil {

    private static final Logger log = LoggerFactory.getLogger(CopyUtil.class);
    /**
     * 1、复制源对象和目标对象的属性值，需要命名一致
     */
    public static void copy(Object source, Object target) throws Exception {
        Class sourceClass = source.getClass();//得到对象的Class
        Class targetClass = target.getClass();
        //得到Class对象的所有属性，需要注意的是：getFields()只能获取public的字段，包括父类的。而getDeclaredFields()只能获取自己声明的各种字段，包括public，protected，private。
        Field[] sourceFields = sourceClass.getDeclaredFields();
        Field[] targetFields = targetClass.getDeclaredFields();
        for(Field sourceField : sourceFields){
            String name = sourceField.getName();//属性名
            String methodName = name.substring(0, 1).toUpperCase() + name.substring(1);
            try {
                Method getMethod = sourceClass.getMethod("get" + methodName);//得到属性对应get方法
                Object value = getMethod.invoke(source);//执行源对象的get方法得到属性值
                for(Field targetField : targetFields){
                    String targetName = targetField.getName();//目标对象的属性名
                    Class<?> type = targetField.getType();//属性类型,因为要全部转化为String类型，所以此处写死为String类型
                    if(targetName.equals(name)){
                        Method setMethod = targetClass.getMethod("set" + methodName, type);//属性对应的set方法
                        if(value!=null){
                            switch (type.getName()) {
                                case "java.util.Date":
                                    Object valueDate =value;
                                    value = DateUtil.parseStringToDate(value.toString(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
                                    if(value == null){
                                        value=valueDate;
                                    }
                                    break;
                                case "java.math.BigDecimal":
                                    value = new BigDecimal(value.toString()).setScale(2, BigDecimal.ROUND_DOWN);
                                    break;
                                case "int":
                                    value = Integer.parseInt(value.toString());
                                    break;
                                case "java.lang.Integer":
                                    value = Integer.parseInt(value.toString());
                                    break;
                            }
                            try {
                                setMethod.invoke(target, value);//执行目标对象的set方法
                            }catch (IllegalArgumentException e) {
                                log.info("CopyUtil:"+e.getMessage());
                            }
                        }
                        break;
                    }
                }
            }catch (NoSuchMethodException e){
                log.info("CopyUtil:"+e.getMessage());
            }
        }
    }
}
