package com.reon.hr.sp.customer.entity.employee;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("emp_card_info_log")
public class EmpCardInfoLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long cardId;

    private Integer type;


    private String snapData;


    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;


}
