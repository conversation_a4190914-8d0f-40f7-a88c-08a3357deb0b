package com.reon.hr.sp.customer.service.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.employee.EmployeeInfoQueryDto;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;

import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.api.customer.vo.employee.ehr.PeopleNumberInfoVo;

import java.util.List;
import java.util.Map;

public interface IPersonOrderQueryService {
    Page<PersonOrderQueryDto> getListPage(Integer page, Integer limit, PersonOrderQueryVo orderVo, String loginName, Integer type);

    List<PersonOrderQueryDto> getListPersonOrder(String loginName,List<String> region);

    void saveOrEditByOrderInfo(PersonOrderQueryVo personOrderQueryVo, String loginName);

    int editWorkAddrByEmpId(String wordAddr, String employeeNo);

    /**
     * 批量更新员工地址
     *
     * @param employeeVos 员工对象
     */
    void editWorkAddrByEmployeeVo(List<EmployeeVo> employeeVos);

    int updateEntryAndDeparture(String orderNo,
                                String entryDate,
                                String dimissionDate,
                                Integer dimissionReason,
                                String entryRemark,
                                String dimissionRemark);

    Page<PersonOrderQueryDto> getListChangeOrderConfirmationPage(Integer page, Integer limit, PersonOrderEditVo vo, String loginName);

    List<PersonOrderQueryDto> getListChangeOrderConfirmationPage(PersonOrderEditVo vo, String loginName);

    List<PersonOrderQueryDto> getListPersonOrderExpoet(PersonOrderQueryVo vo, String loginName, Integer type);

    //List<EmpRelativeFileVo> getCertFileIdByOrderNo(String orderNo);

    Page<PersonOrderQueryDto> getApplyTransferListPage(Integer page, Integer limit, PersonOrderQueryVo orderVo, String loginName);

    List<PeopleNumberInfoVo> getPersonnelNumberInformationOnJob(PeopleNumberInfoVo vo);

    List<PeopleNumberInfoVo> getPersonnelNumberInformationList(PeopleNumberInfoVo vo);

    Page<EmployeeInfoQueryDto> getEmployeeInfoQuery(Integer pageNum, Integer pageSize, PersonOrderQueryVo personOrderQueryVo);
    List<EmployeeInfoQueryDto> getEmployeeInfoQuery( PersonOrderQueryVo personOrderQueryVo);

    int selectChangeOrderCountByOrderNo(String orderNo);

    List<EmployeeOrderVo> getAgentEmployeeDimInformation(Map<String, Object> agentEmployeeDimInformationMap);


}
