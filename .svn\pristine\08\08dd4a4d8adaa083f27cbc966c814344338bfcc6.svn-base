package com.reon.hr.sp.customer.service.impl.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.SpecialCustomerVo;
import com.reon.hr.sp.customer.dao.cus.CustomerMapper;
import com.reon.hr.sp.customer.dao.cus.SpecialCustomerMapper;
import com.reon.hr.sp.customer.service.cus.SpecialCustomerService;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年05月24日
 * @Version 1.0
 */
@Service
public class SpecialCustomerServiceImpl implements SpecialCustomerService {

    @Resource
    private SpecialCustomerMapper specialCustomerMapper;

    @Resource
    private CustomerMapper customerMapper;


    @Override
    public Page<SpecialCustomerVo> getSpecialCustomerPage(SpecialCustomerVo specialCustomerVo, Integer limit, Integer page) {
        Page<SpecialCustomerVo> specialCustomerVoPage = new Page<>(page, limit);
        List<SpecialCustomerVo> specialCustomerPage = specialCustomerMapper.getSpecialCustomerPage(specialCustomerVoPage, specialCustomerVo);
        if (CollectionUtils.isNotEmpty(specialCustomerPage)){
            List<Long> custIdList = specialCustomerPage.stream().map(SpecialCustomerVo::getCustId).collect(Collectors.toList());
            List<CustomerVo> custNameByCustIdList = customerMapper.getCustNameByCustIdList(custIdList);
            Map<Long, String> custIdAndCustNameMap = custNameByCustIdList.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
            for (SpecialCustomerVo customerVo : specialCustomerPage) {
                customerVo.setCustName(custIdAndCustNameMap.get(customerVo.getCustId()));
            }
        }
        return specialCustomerVoPage.setRecords(specialCustomerPage);
    }

    @Override
    public SpecialCustomerVo getSpecialCustomerVoById(Long id) {
        SpecialCustomerVo specialCustomerVoById = specialCustomerMapper.getSpecialCustomerVoById(id);
        String custNameByCustId = customerMapper.findCustNameByCustId(specialCustomerVoById.getCustId());
        specialCustomerVoById.setCustName(custNameByCustId);
        return specialCustomerVoById;
    }

    @Override
    public int getCountById(Long custId, Integer type) {
        return specialCustomerMapper.getCountById(custId, type);
    }

    @Override
    public void addOrUpdateSpecialCustomer(SpecialCustomerVo specialCustomerVo) {
        specialCustomerVo.setCreateTime(new Date());
        specialCustomerVo.setUpdateTime(new Date());
        if ("add".equals(specialCustomerVo.getOptType())){
            specialCustomerMapper.addSpecialCustomer(specialCustomerVo);
        }else {
            specialCustomerMapper.updateSpecialCustomer(specialCustomerVo);
        }
    }

    @Override
    public void deleteSpecialCustomer(List<Long> ids) {
        specialCustomerMapper.deleteSpecialCustomer(ids);
    }

    @Override
    public List<Long> findCustIdByInvoiceUnApprovalCustId(List<Long> custIdList,Integer type) {
        return specialCustomerMapper.findCustIdByInvoiceUnApprovalCustId(custIdList,type);
    }
}
