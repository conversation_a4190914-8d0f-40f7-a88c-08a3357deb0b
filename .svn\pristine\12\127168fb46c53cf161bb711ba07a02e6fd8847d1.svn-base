<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.changeBase.AdjustDetailMapper">
    <insert id="insertAdjustDetail" useGeneratedKeys="true" keyProperty="id">
        insert into adjust_detail
        (
        adjust_id        ,
        emp_name         ,
        order_no         ,
        cert_type        ,
        cert_no          ,
        cust_no          ,
        deal_status      ,
        failure_reason   ,
        creator          ,
        create_time
        )
        values
        (
       #{adjustId}   ,
       #{empName}   ,
       #{orderNo}   ,
       #{certType}   ,
       #{certNo}   ,
       #{custNo}   ,
       #{dealStatus}   ,
       #{failureReason}   ,
       #{creator}   ,
       #{createTime}
     )
    </insert>
    <update id="updateAdjustDetail">
        update adjust_detail
        <set>
            <if test="dealStatus != null and dealStatus != ''">
                deal_status=#{dealStatus},
            </if>
            <if test="failureReason != null and failureReason != ''">
                failure_reason=#{failureReason},
            </if>
            <if test="remind != null and remind != ''">
                remind=#{remind}
            </if>
        </set>
        WHERE id=#{id}
    </update>


    <select id="selectAdjustDetail" resultType="com.reon.hr.api.customer.vo.changeBase.AdjustDetailVo">
        select id, adjust_id, emp_name, order_no, cert_type, cert_no, cust_no, deal_status, failure_reason, creator, create_time, updater, update_time, del_flag,remind from adjust_detail where adjust_id=#{adjustId}
    </select>
    <select id="selectAdjustDetailByIds" resultType="com.reon.hr.api.customer.vo.changeBase.AdjustDetailVo">
        select id, adjust_id, emp_name, order_no, cert_type, cert_no, cust_no, deal_status, failure_reason, creator, create_time, updater, update_time, del_flag from adjust_detail
               where adjust_id in
               <foreach collection="adjustIds" item="adjustId" open="(" close=")" separator=",">
                   #{adjustId}
               </foreach>
    </select>
</mapper>
