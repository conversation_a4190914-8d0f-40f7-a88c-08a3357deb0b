package com.reon.hr.sp.bill.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.reon.hr.api.report.dubbo.service.rpc.ActualPaymentAdvanceReportWrapperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 实做支付垫付报表定时任务
 * @Date 2025年06月09日
 * @Version 1.0
 */

@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ActualPaymentAdvanceReportJob implements SimpleJob {

    @Resource
    private ActualPaymentAdvanceReportWrapperService actualPaymentAdvanceReportWrapperService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("======================" + shardingContext.getJobName() + " start=============================");
        actualPaymentAdvanceReportWrapperService.insertLastMonthActualPaymentAdvanceReportVo();
        log.info("======================" + shardingContext.getJobName() + " end=============================");
    }
}
