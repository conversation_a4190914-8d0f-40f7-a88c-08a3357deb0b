package com.reon.hr.sp.bill.dao.insurancePractice;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailSuccessVo;
import com.reon.hr.sp.bill.entity.insurancePractice.PracticePayDetail;
import com.reon.hr.sp.bill.entity.insurancePractice.PracticePayDetailSuccess;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PracticePayDetailSuccessMapper extends BaseMapper<PracticePayDetailSuccess> {
    void insertPracticePayDetailSuccessVos(@Param("details") List<PracticePayDetailSuccessVo> detailVos);
    void insertPracticePayDetailVos(@Param("details") List<PracticePayDetail> detailVos);
}
