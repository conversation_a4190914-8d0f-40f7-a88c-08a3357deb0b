package com.reon.hr.api.customer.vo.batchImport;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Description:
 * @Date: 2022/9/23 11:34
 * @Version: 1.0
 */
@Data
public class SocialSecurityPaymentProdImportVo implements Serializable {
    /**
     * '补缴起始月',
     */
    @ExcelProperty("补缴起始月*")
    private Integer startMonth;
    /**
     * '补缴截止月',
     */
    @ExcelProperty("补缴截至月*")
    private Integer endMonth;
    /**
     * '企业基数',
     */
    @ExcelProperty("企业基数*")
    private BigDecimal comBase;
    /**
     * '个人基数',
     */
    @ExcelProperty("个人基数*")
    private BigDecimal indBase;
    /**
     * '企业金额'
     */
    @ExcelProperty("企业金额*")
    private BigDecimal comAmt;
    /**
     * '个人金额',
     */
    @ExcelProperty("个人金额*")
    private BigDecimal indAmt;
}
