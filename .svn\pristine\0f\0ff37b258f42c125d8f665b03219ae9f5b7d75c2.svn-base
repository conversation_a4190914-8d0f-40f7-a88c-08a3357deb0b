package com.reon.hr.sp.customer.dubbo.service.rpc.impl.salary.salaryPayment;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IMessageWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.enums.MsgTypeEnum;
import com.reon.hr.api.base.vo.MessageVo;
import com.reon.hr.api.customer.dto.customer.salary.TaxComparisonFeedbackRecordImportDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.salaryPayment.ITaxComparisonWrapperService;
import com.reon.hr.api.customer.enums.salary.TaxComparisonType;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.salary.IndTaxApplyInfoVo;
import com.reon.hr.api.customer.vo.salary.salaryComparison.TaxComparisonFeedbackRecordVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.sp.customer.dubbo.service.rpc.impl.IWithholdingAgentWrapperServiceImpl;
import com.reon.hr.sp.customer.service.employee.salary.salaryPayment.ISalaryPaymentComparisonService;
import com.reon.hr.sp.customer.service.employee.salary.salaryPayment.ITaxComparisonFeedbackRecordService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 */
@Service("taxComparisonWrapperService")
public class TaxComparisonWrapperServiceImpl implements ITaxComparisonWrapperService {
    @Autowired
    private ITaxComparisonFeedbackRecordService iTaxComparisonFeedbackRecordService;
    @Autowired
    private ISalaryPaymentComparisonService iSalaryPaymentComparisonService;
    @Resource
    private IMessageWrapperService iMessageWrapperService;

    @Resource
    private ISequenceService iSequenceService;

    @Autowired
    private IUserOrgPosWrapperService userOrgPosWrapperService;
    @Autowired
    private IWithholdingAgentWrapperServiceImpl withholdingAgentWrapperService;
    @Override
    public void saveSetFeedbackStatus(List<TaxComparisonFeedbackRecordVo> voList) {
        TaxComparisonFeedbackRecordVo taxComparisonFeedbackRecordVo = voList.get(0);
        Integer feedbackStatus = taxComparisonFeedbackRecordVo.getFeedbackStatus();
        String commissionerFeedbackRemark = taxComparisonFeedbackRecordVo.getCommissionerFeedbackRemark();
        String financeFeedbackRemark = taxComparisonFeedbackRecordVo.getFinanceFeedbackRemark();
        String updater = taxComparisonFeedbackRecordVo.getUpdater();

        List<TaxComparisonFeedbackRecordVo> taxComparisonFeedbackRecordVoList=iSalaryPaymentComparisonService.getSalaryPaymentComparisonListByList(voList);
        for (TaxComparisonFeedbackRecordVo vo:taxComparisonFeedbackRecordVoList) {
            vo.setFeedbackStatus(feedbackStatus);
            vo.setCommissionerFeedbackRemark(commissionerFeedbackRemark);
            vo.setCommissionerFeedbackTime(new Date());
            vo.setCreator(updater);
            vo.setUpdater(updater);
            if(StringUtils.isBlank(vo.getCertNo())){
                IndTaxApplyInfoVo indTaxApplyInfoVo = JSON.parseObject(vo.getIndTaxApplyInfo(), IndTaxApplyInfoVo.class);
                vo.setEmployeeName(indTaxApplyInfoVo.getStaffName());
                vo.setCertNo(indTaxApplyInfoVo.getCertNo());
            }
        }
        if(StringUtils.isNotBlank(commissionerFeedbackRemark)&&StringUtils.isBlank(financeFeedbackRemark)){
            iTaxComparisonFeedbackRecordService.insertByVoList(taxComparisonFeedbackRecordVoList);
            List<WithholdingAgentVo> withholdingAgentNoSelect = withholdingAgentWrapperService.getWithholdingAgentNoSelect(null);
            Map<String, WithholdingAgentVo> withholdingAgentVoMap = withholdingAgentNoSelect.stream().collect(Collectors.toMap(WithholdingAgentVo::getWithholdingAgentNo, Function.identity()));
            List<UserOrgPosVo> userOrgPosVoList = userOrgPosWrapperService.getByOrgCodeAndPosCode(null, PositionEnum.TREASURY_TAX_ATTACHE.getCode());
            Map<String, List<UserOrgPosVo>> uopListMap = userOrgPosVoList.stream().collect(groupingBy(UserOrgPosVo::getOrgCode));
            List<MessageVo> messageVoList=new ArrayList<>();
            for (TaxComparisonFeedbackRecordVo vo:taxComparisonFeedbackRecordVoList) {
                if(withholdingAgentVoMap.containsKey(vo.getWithholdingAgentNo())){
                    WithholdingAgentVo withholdingAgentVo = withholdingAgentVoMap.get(vo.getWithholdingAgentNo());
                    if(uopListMap.containsKey(withholdingAgentVo.getOrgCode())){
                        List<UserOrgPosVo> userOrgPosVos = uopListMap.get(withholdingAgentVo.getOrgCode());
                        for (UserOrgPosVo userOrgPosVo:userOrgPosVos) {
                            MessageVo messageVo = new MessageVo ();
                            messageVo.setContent(IMessageWrapperService.TAX_DIFFERENCE_FEEDBACK_REMINDER_TEMP
                                    .replace("{withholdingAgentName}",withholdingAgentVo.getWithholdingAgentName()).replace("{withholdingAgentNo}",vo.getWithholdingAgentNo())
                                    .replace("{name}",vo.getEmployeeName()).replace("{certNo}",vo.getCertNo())
                                    .replace("{taxComparisonType}", EnumsUtil.getNameByCode(vo.getTaxComparisonType(), TaxComparisonType.class))
                            );
                            messageVo.setMsgTitle(MsgTypeEnum.TAX_DIFFERENCE_FEEDBACK_REMINDER_TITLE[0]);
                            messageVo.setDelFlag("N");
                            messageVo.setCreateTime(new Date());
                            messageVo.setUpdateTime(new Date());
                            messageVo.setCreator(updater);
                            messageVo.setReceiver(userOrgPosVo.getLoginName());
                            messageVo.setMsgType(MsgTypeEnum.TAX_DIFFERENCE_FEEDBACK_REMINDER.getCode());
                            messageVo.setMsgNo(iSequenceService.getMsgNo ());
                            messageVo.setReadFlag(1);
                            messageVoList.add(messageVo);
                        }
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(messageVoList)){
                iMessageWrapperService.batchSave(messageVoList);
            }
        }else if(StringUtils.isNotBlank(financeFeedbackRemark)&&StringUtils.isBlank(commissionerFeedbackRemark)){
            taxComparisonFeedbackRecordVo.setFinanceFeedbackTime(new Date());
            //iTaxComparisonFeedbackRecordService.updateFeedbackStatusByFinance(taxComparisonFeedbackRecordVoList,taxComparisonFeedbackRecordVo);
            List<Long> idList = voList.stream().map(TaxComparisonFeedbackRecordVo::getId).collect(Collectors.toList());
            iTaxComparisonFeedbackRecordService.updateFinanceFeedbackStatusByIdList(idList,taxComparisonFeedbackRecordVo);
        }
        iSalaryPaymentComparisonService.updateFeedbackStatus(voList,feedbackStatus,updater);
    }

    @Override
    public Page<TaxComparisonFeedbackRecordImportDto> getTaxComparisonFeedbackRecordPage(Integer page, Integer limit, TaxComparisonFeedbackRecordImportDto dto) {
        return iTaxComparisonFeedbackRecordService.getTaxComparisonFeedbackRecordPage(page,limit,dto);
    }

    @Override
    public List<TaxComparisonFeedbackRecordImportDto> getTaxComparisonFeedbackRecord(TaxComparisonFeedbackRecordImportDto dto, Integer begin) {
        return iTaxComparisonFeedbackRecordService.getTaxComparisonFeedbackRecord(dto,begin);
    }
    @Override
    public List<TaxComparisonFeedbackRecordImportDto> getTaxComparisonFeedbackRecord(TaxComparisonFeedbackRecordImportDto dto) {
        return iTaxComparisonFeedbackRecordService.getTaxComparisonFeedbackRecord(dto);
    }

    @Override
    public int getTaxComparisonFeedbackRecordCount(TaxComparisonFeedbackRecordImportDto dto) {
        return iTaxComparisonFeedbackRecordService.getTaxComparisonFeedbackRecordCount(dto);
    }
}
