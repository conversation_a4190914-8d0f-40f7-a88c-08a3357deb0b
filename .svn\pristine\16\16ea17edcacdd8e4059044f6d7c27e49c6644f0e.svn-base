var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        upload = layui.upload,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer,element = layui.element;
        tableSelect = layui.tableSelect;

    var optType = $("#optType").val();
    //页面初始化
    $(document).ready(function () {
        var signDistCom = [];
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                signDistCom = [];
                signDistCom = data.data;
                $.each(signDistCom, function (i, item) {
                    $(".signDistCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode).attr("cityCode", item.owerCity));
                });
                form.render('select');
                // 获取页面显示数据
                getData()
            },
            error: function (data) {
                console.log("error")
            }
        });
    });

    function getData() {
        //获取主键
        var contractNo = $('#contractNo').val();
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getContractData?contractNo=" + contractNo,
            //data: {roleId: $('#roleId').val()},
            dataType: 'json',
            success: function (res) {
                setCommSupervisor(res.data.commissioner);
                uploadIds = res.data.uploadIdList;
                $('#quoteNoCheck').val(res.data.quoteNo);
                $('#quoteNo').val(res.data.quoteNo);
                $('#quoteType').val(res.data.quoteType);
                $('#contractName').val(res.data.contractName);
                $('#contractType').val(res.data.contractType);
                $('#billDate').val(res.data.billDate);
                $('#incomeDate').val(res.data.incomeDate);
                $('#startDate').val(res.data.startDate.substring(0, 10));
                $('#endDate').val(res.data.endDate.substring(0, 10));
                $('#custName').val(res.data.custName);
                $('#industryType').val(res.data.industryType);
                $('#contactor').val(res.data.contactor);
                $('#tel').val(res.data.tel);
                $('#signCom').val(res.data.signCom);
                $('#signPlace').val(ML.areaFormatter(res.data.signPlace));
                $('#distCom').val(res.data.distCom);
                $('#distPlace').val(ML.areaFormatter(res.data.distPlace));
                $('#commissioner').val(res.data.commissioner);
                $('#corpKind').val(res.data.corpKind);
                $('#signComTitle').val(res.data.signComTitle);
                $('#email').val(res.data.email);
                $('#cityCode').val(res.data.cityCode);
                $('#addr').val(res.data.addr);
                $('#remark').val(res.data.remark);
                $('#custId').val(res.data.custId);
                $('#contractKind').val(res.data.contractKind);
                $('#pid').val(res.data.pid);
                $('#posCode').val(res.data.posCode);
                form.render()
                var taskId = $('#taskId').val();
                if (!taskId) {
                    $('#approvalDiv').hide();
                    $('#submitProc').hide();
                    $('#rejectProc').hide();
                }

                for (var i = 0; i < uploadIds.length; i++) {
                    // 调用获取文件名的方法
                    uploadIds[i].fileIds.forEach(fileId => {
                        $.ajaxData.getFileName(fileId, optType)
                    })
                }
                $('#salaryPayDate').val(res.data.salaryPayDate);
                $('#salaryIncomeDate').val(res.data.salaryIncomeDate);
                $('#invoiceMethod').val(res.data.invoiceMethod);
                $('#receiveFreq').val(res.data.receiveFreq);
                $('#postponeFlag').val(res.data.postponeFlag);
                let contractSubTypes={};
                contractSubTypes['value'] = res.data.contractType;
                salaryContractKindFunction(contractSubTypes)
                let subTypes = JSON.parse(res.data.subTypes)
                xmRender.setValue(subTypes);

                $.ajax({
                    type: "GET",
                    url: ML.contextPath + "/customer/contract/getMeetingRecordData?contractNo=" + contractNo,
                    //data: {roleId: $('#roleId').val()},
                    dataType: 'json',
                    success: function (res) {
                        if (res.count === 0) {
                            return false;
                        }
                        var fileId = res.data.fileId;
                        $('#meetingTime').val(res.data.meetingTime);
                        $('#subject').val(res.data.subject);
                        $('#meetingRemark').val(res.data.remark);

                        var icons = fileId.split(".");
                        if (icons[icons.length - 1] == 'jpg' || icons[icons.length - 1] == 'png') {
                            $('#uploadMeeting').append('<img width="50px" height="50px" src="' + ML.fileServerUrl + fileId + '" alt="' + fileId + '" class="layui-upload-img">')
                        } else {
                            $('#uploadMeeting').append('  <a href="' + ML.fileServerUrl + fileId + '">' + fileId + '</a>')
                        }
                    },
                    error: function (data) {
                        console.log("error")
                    }
                });
                form.render()
            },
            error: function (data) {
                console.log("error")
            }
        });
    }

    function setCommSupervisor(loginName) {
        $("#commissioner").find("option:selected").text("");
        $("#commissioner").empty();
        $("#commissioner").append($("<option/>").text(ML.loginNameFormater(loginName)).attr("value", loginName));
        form.render('select');
    }

    $(document).on('click', '#submitProc', function () {
        if (uploadIds1.length == 0) {
            layer.msg('请先上传最终文件!', {icon:5});
            return;
        }
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var param = {
            'taskId': taskId,
            'comment': comment,
            'bizType': 'contract_flow',
            'fileList': uploadIds1,
            'contractNo': $('#contractNo').val()
        }
        $.ajax({
            url: ML.contextPath + "/customer/contract/uploadFinalFile",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(param),
            success: function (result) {
                layer.msg(result.msg);
                layer.closeAll('iframe');
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'), 'true');
            }
        });

    });


    var fileType = '';
    var fileName = '';
    var uploadIds1 = [];
    var uploadIds = [];
    //上传
    upload.render({
        elem: '#contractUpload' //绑定元素
        , auto: false
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: { contentType: false, processData: false }
        , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
        , choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileType = file.type;
                fileName = file.name;
                var size = file.size;
                var tip = true;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", { icon: 2 });
                    tip = false;
                    return;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds1.push(res.data.fileId);
                $('#upload1').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag"><a href="' + ML.fileServerUrl + res.data.fileId + '" target="_blank">' + fileName + '</a><a href="javascript:void(0)" class="deleteFile" title="删除"><i class="layui-icon layui-icon-delete"></i></a></span>&nbsp;&nbsp;')
                layer.msg('上传成功', { icon: 1 });
                $("#contractUpload").remove();
            }
        }
        , error: function () {
            //请求异常回调
            console.log("上传失败");
            // layer.msg('上传失败', { icon: 5 });
        }
    });

    element.on('tab(contractTabFilter)', function (data) {
        var len = $(".layui-tab-title").children("li").length;
        if (data.index == len - 1) {
            var pid = $('#pid').val();
            if (pid) {
                $("#workFlowImg").attr("src", "/workflow/workflowGraph?pid=" + pid);
                ML.ajax("/workflow/getWorkflowAuditLogList", { "pid": pid }, function (res) {
                    var commentData = res.data;
                    table.render({
                        id: 'contractFlowTable',
                        elem: '#contractFlowTable',
                        data: commentData,
                        cols: [[
                            { title: '序号', type: 'numbers' }
                            , {
                                field: 'userId', title: '处理人', align: 'center', templet: function (d) {
                                    return ML.loginNameFormater(d.userId);
                                }
                            }
                            , {
                                field: 'auditType', title: '处理类型', align: 'center', templet: function (d) {
                                    return ML.dictFormatter("AUDIT_TYPE", d.auditType);
                                }
                            }
                            , { field: 'comment', title: '审批意见', align: 'center' }
                            , { field: 'createTime', title: '审批时间', align: 'center' }
                        ]]
                    });
                }, 'GET');

            }

        }
    });

    // 合同查看时，点击报价单编号  弹出窗口显示报价单信息
    $("#clickQuote").on("click", function () {
        var quoteNo = $("#quoteNoCheck").val();
        var quoteTypeMark =1;
      if($("#contractType").val()==6||$("#contractType").val()==10||$("#contractType").val()==16){
          quoteTypeMark =2;
      }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看',
            area: ['80%', '70%'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/customer/quotation/gotoDetailQuotationPage?quoteNo=' + quoteNo+"&quoteTypeMark="+quoteTypeMark
        });
    });

    ////移除span  删除文件
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        ML.ajax("/customer/contract/delByFileId?fileId=" + fileId, {}, function (result) {
                if (result.code == 0) {
                    layer.msg("删除文件成功！")
                }
            },
            'POST');
        uploadIds1.splice(uploadIds1.indexOf($(this).parent()[0].id.split('-')[1]), 1);
        $(this).parent()[0].remove();
    });


    //关闭弹窗
    $(document).on('click', '.cancel', function () {
        layer.closeAll();
    });

    $(document).on('click', '#rejectProc', function () {
        var comment = $("#comment").val();
        var taskId = $('#taskId').val();
        var pid = $('#pid').val();
        var bizId = $('#contractNo').val();
        var type = 'add';
        var bizType = 'contract_flow';
        if (ML.isNotEmpty(comment) && comment.length > 255) {
            layer.msg("审批意见过长,请缩短审批意见!");
        } else {
            ML.ajax('/workflow/rejectTask',
                {'pid': pid, 'bizId': bizId, 'taskId': taskId, 'comment': comment, 'bizType': bizType, 'type': type}
                , function (res) {
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layer.closeAll();
                    }
                }, 'POST');
        }
    });


    var agencyArray = [1, 11];
    var xmRender = xmSelect.render({
        el: '#subTypes',
        empty: "当前无数据",
        language: 'zn',
        toolbar: {
            //工具条
            show: false,
        },
        prop: {
            name: 'name',
            value: 'code',
        },
        block: {
            showIcon: false,
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;
            if (data.change.length > 0) {
                let tip = false;
                //如果合同大类选择了全代理 单项人事代理   小类型选择了代发工资  则一定要选择
                $.each(data.arr, function (i, item) {
                    if (item.code == 1) {
                        tip = true;
                    }
                });
                if (ML.isNotEmpty($("#contractType").val()) && agencyArray.includes(parseInt($("#contractType").val()))) {
                    if (tip) {
                        $(".salaryMustSelect").attr("lay-verify", "required");
                        $(".salaryShow").addClass("layui-show");
                        $(".salaryShow").removeClass("layui-hide");
                    } else {
                        $(".salaryMustSelect").removeAttr("lay-verify", "required");
                        $(".salaryShow").addClass("layui-hide");
                        $(".salaryShow").removeClass("layui-show");
                    }
                }

                if (data.isAdd) {
                    contractName = $("#custName").val() + ML.dictFormatter('CONTRACT_CATEGORY', $("#contractType").val());
                    let subPro = xmRender.options.data;
                    xmRender.update({
                        data: subPro
                    });
                    $.each(subPro, function (i, item) {
                        if (item.selected) {
                            contractName = contractName + "+" + item.name;
                        }
                    });
                    addPx(contractName)
                    $("[name=contractName]").val(contractName);

                } else {
                    contractName = $("#custName").val() + ML.dictFormatter('CONTRACT_CATEGORY', $("#contractType").val());
                    let subPro = xmRender.options.data;
                    $.each(subPro, function (i, item1) {
                        $.each(data.change, function (i, item2) {
                            if (item1.code == item2.code) {
                                item1.selected = false;
                            }
                        })
                    })
                    xmRender.update({
                        data: subPro
                    });
                    $.each(subPro, function (i, item) {
                        if (item.selected) {
                            contractName = contractName + "+" + item.name;
                        }
                    });
                    addPx(contractName)
                    $("[name=contractName]").val(contractName);
                }
            }
        }
    });

    // 工资发放日 工资到款日 合同类型为工资时必选
    var salaryContractKindArray = [2, 3, 4, 9];
    function salaryContractKindFunction(data) {
        if (ML.isEmpty($("#custName").val())) {
            $("#contractType").val("");
            form.render('select');
            return layer.msg("请先选择客户名称,再选合同类型!");
        }
        var contractName = "";
        let subPro = ML.getSubDictByParent('CONTRACT_CATEGORY', data.value);
        contractName = $("#custName").val() + ML.dictFormatter('CONTRACT_CATEGORY', data.value);
        if (data.value == 11) {
            $("#subTypes").width(300);
        } else {
            $("#subTypes").width(190);
        }
        if (null != subPro && "" != subPro) {
            $.each(subPro, function (i, item) {
                if (item.parentCode != 11 && item.parentCode != 1) {
                    item['selected'] = true;
                    item['disabled'] = true;
                    contractName = contractName + "+" + item.name;
                } else {
                    item['selected'] = false;
                }
            });
            xmRender.update({
                data: subPro
            })
        } else {
            xmRender.update({
                data: []
            })
        }
        addPx(contractName)
        $("[name=contractName]").val(contractName);
        // 如果合同大类类型选择了 代发工资 派遣 外包1 外包2  则一定要填工资
        if (salaryContractKindArray.includes(parseInt(data.value))) {
            $(".salaryMustSelect").attr("lay-verify", "required");
            $(".salaryShow").addClass("layui-show");
            $(".salaryShow").removeClass("layui-hide");
        } else {
            $(".salaryMustSelect").removeAttr("lay-verify", "required");
            $(".salaryShow").addClass("layui-hide");
            $(".salaryShow").removeClass("layui-show");
        }
    }
//    如果合同名字加长 就增加input长度
    function addPx(contractName){
        var index = parent.layer.getFrameIndex(window.name);
        if (contractName.length > 13) {
            layer.style(index, {
                width: 1350+(contractName.length - 13) * 13
                // height: layerInitHeight
            });
            $(".contractNameI").width(190 + (contractName.length - 13) * 13);
            $(".contractNameC").width(178 + (contractName.length - 13) * 13);
        } else {
            $(".contractNameI").width(190);
            $(".contractNameC").width(178);
        }
    }

})