package com.reon.hr.sp.bill.dubbo.service.rpc.impl.supplierPractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.dubbo.service.supplierPractice.ISupPracticeBillReportWrapperService;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.billReport.ExportSupPracticeBillArgsVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.billReport.ExportSupPracticeBillReportVo;
import com.reon.hr.sp.bill.service.bill.supplierPractice.ISupPracticeBillReportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("iSupPracticeBillReportWrapperService")
public class ISupPracticeBillReportWrapperServiceImpl implements ISupPracticeBillReportWrapperService {

    @Resource
    private ISupPracticeBillReportService supPracticeBilReportService;

    @Override
    public Page<ExportSupPracticeBillReportVo> getSupPracticeBilReport(ExportSupPracticeBillArgsVo vo) {
        return supPracticeBilReportService.getSupPracticeBilReport(vo);
    }
}
