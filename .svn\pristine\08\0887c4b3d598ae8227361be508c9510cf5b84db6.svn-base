<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2020/6/11
  Time: 13:31
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>

<html>
<head>
  <title>工伤修改页面</title>
  <meta charset="utf-8">
  <title></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">


  <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
  <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
  <style>
    .layui-elip {
      width: 130px;
    }

    .layui-table th {
      text-align: center;
    }

    .layui-inline {
      margin: 10px 10px 10px 10px;
    }

    #divCc {
      margin-left: 30px;
    }

    .layui-form-item {
      margin: 0px 0px 0px 0px;
    }

    .inlineLong {
      width: 750px;
    }

    .textareaLong {
      width: 570px;
    }

    .inlineLongTwo {
      width: 720px;
      /*text-align:left;*/
    }

    form .layui-inline {
      padding-bottom: 10px;
    }

    .tableSelect_btn_search {
      visibility: hidden;
    }
  </style>
</head>
<body>
<div class="layui-tab layui-tab-card" style="height: 70%">
  <div class="layui-tab-content">
    <div class="layui-tab-item layui-show" style="margin-top: 5px">
      <div class="layui-fluid">
        <div class="layui-card">

          <form class="layui-form" id="searchForm">
            <div class="layui-form-item">
              <input type="hidden" id="checkCertType" value="${vo.certType}">
              <input type="hidden" id="checkEedStatus" value="${vo.eedStatus}">
              <input type="hidden" id="checkContractType" value="${vo.contractType}">
              <input type="hidden" id="checkReceiving" value="${vo.receiving}">
              <input type="hidden" id="checkReceivingMan" value="${vo.receivingMan}">
              <input type="hidden" id="checkCommissioner" value="${vo.commissioner}">
              <input type="hidden" id="checkBusinessType" value="${vo.businessType}">
              <input type="hidden" id="checkBusinessSubType" value="${vo.businessSubType}">
              <input type="hidden" id="checkDefiniteBusiness" value="${vo.definiteBusiness}">
              <input type="hidden" id="cityCode" value="${vo.cityCode}">
              <input type="hidden" id="id" value="${id}">
              <input type="hidden" id="checkFileData" value="${vo.fileData}">
              <input type="hidden" id="fileIdCache" value="${vo.fileId}">


              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="订单编号" lay-size="100px"><i
                        style="color: red">*</i>订单编号：</label>
                <div class="layui-input-inline">
                  <input type="text" id="orderNo"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required"
                         value="${vo.orderNo}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="员工姓名" lay-size="100px"><i
                        style="color: red">*</i>员工姓名：</label>
                <div class="layui-input-inline">
                  <input type="text" id="employeeName"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled
                         value="${vo.employeeName}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="证件类型" lay-size="100px"><i
                        style="color: red">*</i>证件类型：</label>
                <div class="layui-input-inline">
                  <input type="text" id="certType"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required"
                         DICT_TYPE="CERT_TYPE" disabled>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="证件号码" lay-size="100px"><i
                        style="color: red">*</i>证件号码：</label>
                <div class="layui-input-inline">
                  <input type="text" id="certNo"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled
                         value="${vo.certNo}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="入离职状态" lay-size="100px"><i
                        style="color: red">*</i>入离职状态：</label>
                <div class="layui-input-inline">
                  <input type="text" id="eedStatus"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="客户编号" lay-size="100px"><i
                        style="color: red">*</i>客户编号：</label>
                <div class="layui-input-inline">
                  <input type="text" id="custNo"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled
                         value="${vo.custNo}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="客户名称" lay-size="100px"><i
                        style="color: red">*</i>客户名称：</label>
                <div class="layui-input-inline">
                  <input type="text" id="custName"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled
                         value="${vo.custName}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="合同类型" lay-size="100px"><i
                        style="color: red">*</i>合同类型：</label>
                <div class="layui-input-inline">
                  <input type="text" id="contractType"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="小合同编号" lay-size="100px"><i
                        style="color: red">*</i>小合同编号：</label>
                <div class="layui-input-inline">
                  <input type="text" id="contractAreaNo"  placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="required" disabled value="${vo.contractAreaNo}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="小合同名称" lay-size="100px"><i
                        style="color: red">*</i>小合同名称：</label>
                <div class="layui-input-inline">
                  <input type="text" id="contractAreaName"  placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="required" disabled value="${vo.contractAreaName}">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="接单方" lay-size="100px"><i
                        style="color: red">*</i>接单方：</label>
                <div class="layui-input-inline">
                  <input type="text" id="receivingName"  placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="required" disabled value="${vo.receivingName}">

                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="接单客服" lay-size="100px"><i
                        style="color: red">*</i>接单客服：</label>
                <div class="layui-input-inline">
                  <input type="text" id="receivingMan"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled
                         value="${vo.receivingMan}">
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="派单客服" lay-size="100px"><i
                        style="color: red">*</i>派单客服：</label>
                <div class="layui-input-inline">
                  <input type="text" id="commissioner"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="城市" lay-size="100px"><i
                        style="color: red">*</i>城市：</label>
                <div class="layui-input-inline">
                  <input type="text" id="cityName"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled>
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip notNewline" title="拜访时间" lay-size="100px"><i
                        style="color: red">*</i>业务发生日期：</label>
                <div class="layui-input-inline">
                  <input type="text" class="layui-input layui-input-disposable"
                         name="businessHandlingDate" id="startDate" lay-verify="required"
                         placeholder="请输入" autocomplete="off" value="${vo.businessHandlingDate}" disabled>
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="票据金额" lay-size="100px"><i
                        style="color: red"></i>票据金额：</label>
                <div class="layui-input-inline">
                  <input type="number" id="billAmount" name="billAmount" placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="" value="${vo.billAmount}" lay-verify="required" disabled>
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="报销金额" lay-size="100px"><i
                        style="color: red"></i>报销金额：</label>
                <div class="layui-input-inline">
                  <input type="number" id="reimbursementAmount" name="reimbursementAmount"
                         placeholder="请输入" lay-verType="tips" autocomplete="off"
                         class="layui-input" lay-verify="" value="${vo.reimbursementAmount}" lay-verify="" disabled>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="业务大类"><i style="color: red">*</i>业务大类：</label>
                <div class="layui-input-inline">
                  <input type="text" id="businessType"  placeholder="请输入" lay-verType="tips"
                         autocomplete="off" class="layui-input" lay-verify="required" disabled>
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="业务小类"><i style="color: red">*</i>业务小类：</label>
                <div class="layui-input-inline">
                  <input type="text" id="businessSubType"  placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="required" disabled>
                </div>
              </div>

              <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="具体业务"><i style="color: red">*</i>具体业务：</label>
                <div class="layui-input-inline">
                  <input type="text" id="definiteBusiness"  placeholder="请输入"
                         lay-verType="tips" autocomplete="off" class="layui-input"
                         lay-verify="required" disabled>
                </div>
              </div>


              <div class="" id="jobTypeDiv">
                <label class="layui-form-label layui-elip" title="所需资料" disabled=""><i
                        style="color: red">*</i>所需资料：</label>

              </div>
              <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                  <legend>文件上传</legend>
                </fieldset>
                <div class="layui-upload">
                  <div class="layui-input-block" style="width: 1166px;">

                    <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                      预览图：
                      <div class="layui-upload-list" id="upload"></div>
                    </blockquote>
                  </div>
                </div>
              </div>


              <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                  <legend>备注</legend>
                </fieldset>
                <div class="layui-input-block" style="width: 1166px;">
                               <textarea placeholder="请输入内容" name="remark" id="cancelRemark"
                                         class="layui-textarea queryDis"
                                         style="min-width: 55px" value="">${vo.cancelRemark}</textarea>
                </div>

              </div>

              <div class="layui-inline" style="float: right;padding: 10px;">

                <button type="submit" class="layui-btn layuiadmin-btn-list" lay-submit=""
                        lay-filter="submitFilter" id="submit">立即提交
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

</body>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/workInjury/cancelWorkInjury.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js"></script>
</html>
