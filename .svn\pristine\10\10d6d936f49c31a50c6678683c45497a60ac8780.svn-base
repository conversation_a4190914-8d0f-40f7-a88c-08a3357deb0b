<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /*去掉type=number时的上下加减按钮*/
        /* 谷歌 */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        /* 火狐 */
        input{
            -moz-appearance:textfield;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form" method="post" id="searchForm">
    <table class="layui-table" lay-skin="nob" style="width: 65%;">
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>业务分类ID：</td>
            <td width="60%">
                <input type="number" class="layui-input" id="buClasId"   name="buClasId" lay-verify="required">
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>回调密钥：</td>
            <td width="60%">
                <input type="text" class="layui-input" id="secretKey"   name="secretKey" lay-verify="required">
            </td>
        </tr>
        <tr>
            <td align="right" width="20%">客户名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="" id="custName" lay-verify=""  readonly>
                <input class="layui-input" type="hidden" name="custId" id="custId"  readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%">集团名称：</td>
            <td width="60%">
                <input class="layui-input" type="text" name="" id="groupName"   readonly>
                <input class="layui-input" type="hidden" name="custGroupId" id="custGroupId"  readonly>
            </td>
        </tr>
        <tr>
            <td align="right" width="20%"> 替换名称：</td>
            <td width="60%">
                <input type="text" class="layui-input" id="replaceName"   name="replaceName" >
            </td>
        </tr>
        <tr>
            <td align="right" width="20%">章名称：</td>
            <td width="60%">
                <input type="text" class="layui-input" id="stampName" name="stampName"  >
            </td>
        </tr>
    </table>
    <div style="float: right; margin-right: 40%;" id="but">
        <button class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn" >保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/thirdPart/qys/addCallBackKey.js?v=${publishVersion}"></script>
</body>
</html>