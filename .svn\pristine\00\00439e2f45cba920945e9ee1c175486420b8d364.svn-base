package com.reon.hr.api.thirdpart.domain.invoice.nuonuo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 发票开具请求参数
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Order implements Serializable {
    /**
     * 购买方经办人姓名（数电票特有字段）
     */
    private String buyerManagerName;
    /**
     * 购方名称
     */
    private String buyerName;
    /**
     * 购方银行开户行及账号
     */
    private String buyerAccount;
    /**
     * 购方税号（企业要填，个人可为空；数电专票、二手车销售统一发票时必填）
     */
    private String buyerTaxNum;
    /**
     * 	购方电话（购方地址+电话总共不超100字符；二手车销售统一发票时必填）
     */
    private String buyerTel;
    /**
     * 购方地址（购方地址+电话总共不超100字符；二手车销售统一发票时必填）
     */
    private String buyerAddress;
    /**
     * 销方税号（使用沙箱环境请求时消息体参数salerTaxNum和消息头参数userTax填写339902999999789113）
     */
    private String salerTaxNum;
    /**
     * 	销方电话（在诺税通saas工作台配置过的可以不传，以传入的为准）
     */
    private String salerTel;
    /**
     * 销方地址
     */
    private String salerAddress;
    /**
     * 销方银行开户行及账号(二手车销售统一发票时必填)
     */
    private String salerAccount;
    /**
     * 不传默认为0：都不显示；传1：备注仅显示销方开户行及账号；传2：备注仅显示购方开户行及账号；传3：购销方开户行及账号都显示（此字段仅在数电普票和数电专票下生效）
     */
    private String showBankAccountType;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单时间
     */
    private String invoiceDate;
    /**
     * 	冲红时填写的对应蓝票发票代码（红票必填 10位或12 位， 11位的时候请左补 0）
     */
    private String invoiceCode;
    /**
     * 冲红时填写的对应蓝票发票号码（红票必填，不满8位请左补0）
     */
    private String invoiceNum;
    /**
     * 冲红原因：1:销货退回;2:开票有误;3:服务中止;4:发生销售折让(开具红票时且票种为p,c,e,f,r需要传--成品油发票除外；不传时默认为 1)
     */
    private String redReason;
    private String terminalNumber;
    /**
     * 清单标志：非清单:0；清单:1，默认:0，电票固定为0
     */
    private String listFlag;
    /**
     * 项目名称*人力资源服务*
     */
    private String listName;
    private String naturalPersonFlag;
    /**
     * 推送方式：-1,不推送;0,邮箱;1,手机（默认）;2,邮箱、手机
     */
    private String pushMode;
    private String buyerPhone;
    private String managerCardNo;
    private InvoiceBuildingInfo invoiceBuildingInfo;
    private String departmentId;
    private String showCheckerType;
    private String invoiceDifferenceType;
    private String paperInvoiceType;
    private String checker;
    private String invoiceNumEnd;
    private String payee;
    private List<InvoiceTravellerTransport> invoiceTravellerTransport;
    private String taxRebateProxy;
    private RealPropertySellInfo realPropertySellInfo;
    /**
     * 开票类型：1:蓝票;2:红票 （数电票冲红请对接数电快捷冲红接口）
     */
    private String invoiceType;
    private String specificFactor;
    private String machineCode;
    private String taxNumVerifyFlag;
    private String vehicleFlag;
    private String surveyAnswerType;
    private String certificateType;
    /**
     * 开票明细[]
     */
    private List<InvoiceDetail> invoiceDetail;
    private String nextInvoiceNum;
    private String clerkId;
    private String clerk;
    private String remark;
    private String managerCardType;
    /**
     * 发票种类：
     * p,普通发票(电票)(默认);
     * c,普通发票(纸票);
     * s,专用发票;
     * e,收购发票(电票);
     * f,收购发票(纸质);
     * r,普通发票(卷式);
     * b,增值税电子专用发票;
     * j,机动车销售统一发票;
     * u,二手车销售统一发票;
     * bs:电子发票(增值税专用发票)-即数电专票(电子),
     * pc:电子发票(普通发票)-即数电普票(电子),
     * es:数电纸质发票(增值税专用发票)-即数电专票(纸质);
     * ec:数电纸质发票(普通发票)-即数电普票(纸质)
     */
    private String invoiceLine;
    private SecondHandCarInfo secondHandCarInfo;
    private String email;
    private String naturalPersonVerifyFlag;
    /**
     * 差额征税
     */
    private List<DifferenceVoucherInfoList> differenceVoucherInfoList;
    private String callBackUrl;
    private String ccEmail;
    private List<AdditionalElementList> additionalElementList;
    private String billInfoNo;
    private VehicleInfo vehicleInfo;
    private String hiddenBmbbbh;
    private List<InvoiceGoodsTransport> invoiceGoodsTransports;
    private String bField1;
    private String nextInvoiceCode;
    private String extensionNumber;
    private String bField3;
    private String additionalElementName;
    private String bField2;
    private String forceFlag;
    private RealPropertyRentInfo realPropertyRentInfo;
    private String ccPhone;
    private String proxyInvoiceFlag;

    // getters and setters
}