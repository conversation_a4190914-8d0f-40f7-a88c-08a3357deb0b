package com.reon.hr.api.customer.vo.workInjury;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/2/16 0016 上午 10:51
 * @Version 1.0
 */
@Data
public class InjuryFileTextFormalInfoVo implements Serializable {
    /**
     * 工伤正式信息表id
     */
    private Long injuryFormalId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 资料模板id
     */
    private Long injuryBaseDataId;
}
