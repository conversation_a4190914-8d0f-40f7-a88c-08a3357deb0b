package com.reon.hr.api.customer.vo.salary.pay;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSalaryBillVo extends SalaryBillVo{
    private Long supplierSalaryInfoId;
    private String supplierQuotationNo;
    private String importSupplierQuotationNo;
    private BigDecimal supplierServiceFee;
    private String errorRemark;
    private BigDecimal supplierDisFund;
    private BigDecimal importSupplierDisFund;
    private BigDecimal supplierCrossBankHandlingFees;
    private BigDecimal supplierUnionFees;
    private BigDecimal supplierSalarySaleTax;
    private BigDecimal importSupplierSalarySaleTax;
    private BigDecimal supplierFeeTotal;//供应商费用合计
    private BigDecimal supplierCustPayAmt;//供应商企业支付总计

    private Integer billMonth;
    private Long billId;
    private Long supplierId;
    private Integer taxMonth;
    private Long salaryCategoryId;
    private Long taxListId;
    private Long salaryInfoId;
}
