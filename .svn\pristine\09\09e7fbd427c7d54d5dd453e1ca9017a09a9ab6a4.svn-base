package com.reon.hr.sp.customer.service.impl.salary.taxMonthWhiteList;

import com.reon.hr.api.customer.enums.salary.TaxComparisonType;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.vo.salary.TaxDateRangeVo;
import com.reon.hr.sp.customer.dao.salary.TaxDateRangeMapper;
import com.reon.hr.sp.customer.service.employee.salary.taxMonthWhiteList.ITaxDateRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class TaxDateRangeServiceImpl implements ITaxDateRangeService {
    @Autowired
    private TaxDateRangeMapper taxDateRangeMapper;

    @Override
    public List<TaxDateRangeVo> getAll() {
        return taxDateRangeMapper.getAll();
    }

    @Override
    public Integer getTaxComparisonTypeByDate(Integer date,List<TaxDateRangeVo> taxDateRangeVoList){
        Integer nextMonth = DateUtil.getYearMonthByCount(date, 1);
        Integer currYearMonth = DateUtil.getCurrYearMonth();
        Integer currentYearMonthDate = com.reon.hr.api.util.DateUtil.getCurrentYearMonthDate();
        //默认是预申报
        Integer taxComparisonType;
        //一般是202412生成支付年月202411的
        //如果当前年月<=(支付年月+1) 默认是预申报
        if(currYearMonth<=nextMonth){
            taxComparisonType= TaxComparisonType.PRE_DECLARATION.getCode();
        }else {
            //202412>202407
            taxComparisonType=TaxComparisonType.ACTUAL_DECLARATION.getCode();
        }
        for (TaxDateRangeVo taxDateRangeVo:taxDateRangeVoList) {
            if(taxDateRangeVo.getStartDate().startsWith(nextMonth.toString())){
                if(currentYearMonthDate>=Integer.parseInt(taxDateRangeVo.getSeventhBusinessDay())
                        &&currentYearMonthDate<Integer.parseInt(taxDateRangeVo.getTaxEndDate())){
                    taxComparisonType=TaxComparisonType.SAME_MONTH_DECLARATION.getCode();
                }else if(currentYearMonthDate>=Integer.parseInt(taxDateRangeVo.getTaxEndDate())){
                    taxComparisonType=TaxComparisonType.ACTUAL_DECLARATION.getCode();
                }
            }
        }
        return taxComparisonType;
    }
    @Override
    public Boolean isPreDeclarationDayByTaxMonth(Integer taxMonth,List<TaxDateRangeVo> taxDateRangeVoList){
        Integer nextMonth = DateUtil.getYearMonthByCount(taxMonth, 1);
        Integer currYearMonth = DateUtil.getCurrYearMonth();
        //当前年月就是申报该计税月的月份时
        if(Objects.equals(nextMonth, currYearMonth)){
            Integer currentYearMonthDate = com.reon.hr.api.util.DateUtil.getCurrentYearMonthDate();
            for (TaxDateRangeVo taxDateRangeVo:taxDateRangeVoList) {
                if(taxDateRangeVo.getStartDate().startsWith(nextMonth.toString())){
                    if(currentYearMonthDate==Integer.parseInt(taxDateRangeVo.getThirdBusinessDay())){
                        return true;
                    }else if(currentYearMonthDate>Integer.parseInt(taxDateRangeVo.getThirdBusinessDay())){
                        return false;
                    }
                }
            }
        }
        return null;
    }
}
