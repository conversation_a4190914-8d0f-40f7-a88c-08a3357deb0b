package com.reon.hr.sp.report.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021/03/19
 */
@Data
public class InvoiceCheckIntermidiate {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 账单id
     */
    private Long billId;

    /**
     * 开票编号
     */
    private Long invoiceId;

    /**
     * 核销编号
     */
    private Long checkId;

    /**
     * 核销金额
     */
    private BigDecimal checkAmt;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;
    private Long subCheckId;

}