package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.org.OrgAndSupplierVo;
import com.reon.hr.api.customer.vo.supplier.*;
import com.reon.hr.api.vo.sys.OrgVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ISupplierService {

    Page<SupplierVo> getSupplierListPage(SupplierSearchVo supplierSearchVo, Integer page, Integer limit);
    List<SupplierVo> getSupplierList(SupplierSearchVo supplierSearchVo);

    List<OrgAndSupplierVo> getSupplierListByArea(String provinceCode, String cityCode,Integer priorityFlag);

    List<OrgAndSupplierVo> getSuppliersByArea(String provinceCode, String cityCode,Integer priorityFlag);

    /**
     * 根据供应商集合和优选标识查询
     *
     * @param priorityFlag 优选标识
     * @param supplierVos 供应商集合
     * @return {@link List}<{@link OrgAndSupplierVo}>
     */
    List<OrgAndSupplierVo> getSupplierByCityCodeAndPriorityFlag(List<OrgAndSupplierVo> supplierVos,Integer priorityFlag);

    SupplierVo getSupplierInfoById(Long id,Integer enable);

    int delete(List<Long> ids, String updater);

    List<OrgVo> getSupplierOrCompany(String keyword, Integer cityCode);
    List<OrgVo> getSupplierOrCompanyOrCustomer(String keyword, Integer cityCode);

    void saveOrUpdate(SupplierVo supplierVo);

    int addSupplierContract(SupplierContractNewVo supplierContractNewVo);

    boolean autoContinueSupplierContract(SupplierContractNewVo supplierContractNewVo);
    void saveSupplierContractFile(SupplierContractNewVo supplierContractNewVo,String loginName);

    List<SupplierContractNewVo>getSupplierContractById(Long supplierId);

    List<SupplierContractNewVo>getSupplierContractByPid(Long supplierId, List<String> pidList,Integer procType);


    SupplierContractNewVo getSupplierContractByContractNo(String contractNo);

    Long getSupplierContractIdByPid(String pid,Integer procType);



    void updateStatusByPid(String pid,Integer status,String type);

    SupplierContractNewVo getSupplierVoBySupplierContractId(Long id,Integer procType);


    //分配供应商
    int updateSupplierById(SupplierVo supplierVo);


    List<SupplierVo> getAllSuppliers();
    List<SupplierVo> getAllSuppliersByName(String supplierName);

    List<SupplierVo> selectAllEnableSupplier();

    List<SupplierVo> selectAllEnableSupplierByType(List<Integer> typeList);
    Map<String,String> getSupplierName(Long  id);
    /**非社保供应商接口*/
    //List<SupplierVo> getNoSupplierById(Long id);//页面查询
    void  saveNoSupplier(NoSupplierVo vo);
    /**
     * 非社保供应商查询，外部调用*/
    List<SupplierVo> getNOSuppliers(String supplierName,Integer type);
    /**获取所有的非社保供应商下拉框*/
    List<SupplierVo> getAllNoSuppliers(String supplierName);
    /**
     * 供应商启用跟禁用*/
    void updateSaveSupplier(SupplierVo vo);

    Map<Long, SupplierVo> getAllSupplierNameToMap();

    SupplierVo getSupplierVoById(Long id);

    List<SupplierVo> getSupplierByIds(List<Long> ids);



    List<SupplierQuotationVo> getSupplierQuotationVoByQuotationNo(List<String>  quotationNo);

    void editSupplierQuotationByQuotationNo(SupplierQuotationVo supplierQuotationVo);

    void saveSupplierQuotation(SupplierQuotationVo supplierQuotationVo);

    List<SupplierVo> getSalarySupplier(List<OrgPositionDto> userOrgPositionDtoList);

    List<SupplierAreaVo> getAllAggregationCommissioner();

    List<Long> getSupplierIdByCommissioner(String loginName);

    List<SupplierVo> getAllSupplierName();

    List<String> getWithholdingAgentNoByCommissioner(String loginName);
}
