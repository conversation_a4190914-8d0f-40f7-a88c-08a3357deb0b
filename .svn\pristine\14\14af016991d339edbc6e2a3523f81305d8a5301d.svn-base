layui.use(
  ["jquery", "form", "layer", "element", "laydate", "table", "upload"],
  function () {
    var table = layui.table,
      upload = layui.upload,
      $ = layui.$,
      form = layui.form,
      layer = layui.layer,
      layer = parent.layer === undefined ? layui.layer : parent.layer;
    // 表格数据源
    var tableData = [];

    // 分公司数据源
    var company = [];

    var tableView = "";
    // 特殊说明附件
    var specialAttachmentList = [];
    // 渲染页面表格
    table.render({
      id: "addContractAreaGrid",
      elem: "#addContractAreaTable",
      page: false,
      limit: Number.MAX_VALUE, // 分页条数根据查询回来数据变化
      defaultToolbar: [],
      toolbar: "#topbtn",
      data: tableData,
      cols: [
        [
          { type: "checkbox", width: "50" },
          { title: "序号", type: "numbers", width: "50" },
          {
            field: "name",
            title: '<i style="color: red">*</i>小合同名称',
            align: "center",
            edit: "text",
            width: "150",
          },
          {
            field: "signCom",
            title: "签单方",
            align: "center",
            style: "overflow: hidden",
            width: "150",
            templet: function (d) {
              if (!d.signCom) {
                return "无签单方";
              }
              return companyCodeToName(d.signCom);
            },
          },
          {
            field: "distCom",
            title: '<i style="color: red">*</i>派单方',
            align: "center",
            width: "150",
            style: "overflow: hidden",
            templet: function (d) {
              if (!d.distCom) {
                return "无派单方";
              }
              return companyCodeToName(d.distCom);
            },
          },
          {
            field: "receiving",
            title: '<i style="color: red">*</i>接单方',
            align: "center",
            style: "overflow: hidden",
            width: "150",
            event: "inputReceiving",
            templet: function (d) {
              return typeof d.receiving == "undefined" ? "请选择" : d.receiving;
            },
          },
          {
            field: "cityCode",
            title: "城市",
            align: "center",
            width: "150",
            templet: function (d) {
              if (d.cityCode) {
                return ML.areaFormatter(d.cityCode);
              }
              return "待选择接单方";
            },
          },
          {
            field: "templetId",
            title: '<i style="color: red">*</i>客户账单模板',
            align: "center",
            templet: "#templet",
            width: "150",
          },
          {
            field: "genBillName",
            title: "账单方",
            align: "center",
            style: "overflow: hidden",
            width: "150",
            templet: function (d) {
              if (d.genBillName) {
                return d.genBillName;
              }
              return "待选择账单模板";
            },
          },
          {
            field: "receiverName",
            title: "收款方",
            align: "center",
            width: "150",
            style: "overflow: hidden",
            templet: function (d) {
              if (d.receiverName) {
                return d.receiverName;
              }
              return "待选择账单模板";
            },
          },
          {
            field: "dispatchType",
            title: '<i style="color: red">*</i>派单类型',
            align: "center",
            width: "150",
            templet: "#dispatchType",
          },
          {
            field: "commSupervisor",
            title: '<i style="color: red">*</i>派单方客服',
            align: "center",
            width: "150",
            templet: function (d) {
              return ML.loginNameFormater(d.commSupervisor);
            },
          },
          {
            field: "revMgr",
            title: '<i style="color: red">*</i>接单方客服',
            align: "center",
            width: "150",
            templet: function (d) {
              return ML.loginNameFormater(d.revMgr);
            },
          },
          {
            field: "status",
            title: '<i style="color: red">*</i>小合同状态',
            align: "center",
            width: "150",
            templet: "#status",
          },
          {
            field: "remark",
            title: "特殊说明",
            align: "center",
            edit: "text",
            width: "150",
          },
          {
            field: "hasLaborContract",
            title: '<i style="color: red">*</i>是否需要劳动合同',
            align: "center",
            templet: "#hasLaborContract",
            width: "150",
          },
          {
            field: "templetType",
            title: "劳动合同版本",
            align: "center",
            templet: "#templetType",
            width: "150",
          },
          {
            field: "archiveFlag",
            title: '<i style="color: red">*</i>是否存档',
            align: "center",
            templet: "#archiveFlag",
            width: "150",
          },
          {
            field: "telFlag",
            title: '<i style="color: red">*</i>是否外呼',
            align: "center",
            templet: "#telFlag",
            width: "150",
          },
          {
            field: "dispatchMan",
            title: "派单人",
            align: "center",
            edit: "text",
            width: "150",
          },
          {
            field: "attachment",
            title: "附件",
            align: "center",
            width: "150",
            templet: function (d) {
              // 动态生成附件列内容
              let html = "";
              if (d.attachmentId) {
                html +=
                  '<a class="upload-attachment" data-index="' +
                  d.LAY_TABLE_INDEX +
                  '"><i class="layui-icon layui-icon-file"></i>查看详情</a>';
              } else {
                html +=
                  '<a class="upload-attachment" data-index="' +
                  d.LAY_TABLE_INDEX +
                  '">上传附件</a>';
              }

              return html;
            },
          },
          {
            field: "accountFlag",
            title: '<i style="color: red">*</i>是否单立户',
            align: "center",
            templet: "#accountFlag",
            width: "150",
          },
          {
            field: "sinAccName",
            title: '<i style="color: red">*</i>选中单立户',
            align: "center",
            event: "inputAccountFlag",
            width: "150",
          },
          {
            field: "firstFlag",
            title: "是否优先",
            align: "center",
            templet: "#firstFlag",
            width: "150",
          },
          {
            field: "injuryFlag",
            title: '<i style="color: red">*</i>是否单工伤',
            align: "center",
            templet: "#injuryFlag",
            width: "150",
          },
          {
            field: "",
            title: "操作",
            toolbar: "#btn",
            align: "center",
            width: "150",
          },
        ],
      ],
      done: function (res) {
        tableView = this.elem.next(); // 当前表格渲染之后的视图
        var contractTypeHidden = $("#contractTypeHidden").val();
        //派遣、外包1、外包2
        if (
          contractTypeHidden == 2 ||
          contractTypeHidden == 3 ||
          contractTypeHidden == 4
        ) {
          var selectHasLaborContractList = document.getElementsByName(
            "selectHasLaborContract"
          );
          for (let i = 0; i < selectHasLaborContractList.length; i++) {
            selectHasLaborContractList[i].options[2].selected = true;
            selectHasLaborContractList[i].disabled = true;
            tableData[i].hasLaborContract =
              selectHasLaborContractList[i].options[2].value;
          }
          form.render("select");
        }
      },
    });

    /**
     * 监听接单方弹出层
     */
    table.on("tool(addContractAreaTableFilter)", function (obj) {
      switch (obj.event) {
        case "add":
          add();
          break;

        case "del":
          delOne(obj.tr[0].rowIndex);
          break;
      }
      var trDom = obj.tr;
      if (obj.event == "inputReceiving") {
        $(trDom).find("[data-field=receiving] input").attr("id", "receiving");
        // console.log(tableData)
        //下拉数据表格
        $("#receiving").each(function () {
          var index = trDom[0].dataset.index;
          if (!tableData[index].distributId) {
            layer.open({
              type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
              title: "接单方",
              area: ["40%", "70%"],
              shade: 0,
              maxmin: true,
              offset: "auto",
              shade: [0.8, "#393D49"],
              content: ML.contextPath + "/customer/contractArea/receiving",
              success: function (layero, index) {
                var body = layer.getChildFrame("body", index);
                var parentIndex = parent.layer.getFrameIndex(window.name);
                body.find("#parentIframeIndex").val(parentIndex);
              },
              end: function () {
                if ($("#selectedGroup").val() != "") {
                  var groupObj = JSON.parse($("#selectedGroup").val());
                  tableData[index]["receiving"] = groupObj.name;
                  tableData[index]["cityCode"] = groupObj.cityCode;
                  tableData[index]["receivingId"] = groupObj.id;
                  tableData[index]["revMgr"] = groupObj.commSupervisor;

                  table.reload("addContractAreaGrid", { data: tableData });
                  var dataScroll = tableView.find(
                    'tr[data-index="' + index + '"]'
                  );
                  dataScroll[0].scrollIntoViewIfNeeded();
                }
              },
            });
          }
        });
      } else if (obj.event == "inputAccountFlag") {
        let index = Number($(trDom).get(0).getAttribute("data-index"));
        if (tableData[index].accountFlag != 2) {
          return;
        }
        if (ML.isEmpty(tableData[index].cityCode)) {
          layer.msg("请选择城市！");
          return;
        }
        renderSingle(index);
      } else if (obj.event == "attachment") {
        let index = Number($(trDom).get(0).getAttribute("data-index"));
        let row = tableData[index];
      }
    });

    function renderSingle(dataIndex) {
      layer.open({
        type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
        title: "单立户",
        area: ["40%", "40%"],
        shade: 0,
        maxmin: true,
        offset: "auto",
        shade: [0.8, "#393D49"],
        content:
          ML.contextPath +
          "/prac/singleAccount/gotoSelectPracSingleAccountPage",
        success: function (layero, index) {
          var body = layer.getChildFrame("body", index);
          var parentIndex = parent.layer.getFrameIndex(window.name);
          body.find("#parentIframeIndex").val(parentIndex);
          var val = $("#custId").val();
          body.find("#custId").val(val);
          body.find("#cityCode").val(tableData[dataIndex].cityCode);
        },
        end: function () {
          var selectData = localStorage.getItem("data"); // 使用 localStorage 读取数据
          if (ML.isNotEmpty(selectData)) {
            let singAccount = JSON.parse(selectData);
            tableData[dataIndex].sinAccId = singAccount.id;
            tableData[dataIndex].sinAccName = singAccount.name;
            table.reload("addContractAreaGrid", { data: tableData });
            localStorage.removeItem("data");
          }
        },
      });
    }

    /*选择框的监听*/

    // 账单模板
    form.on("select(templet)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      var gen = $(data.elem).find("option:selected").attr("gen");
      var receiver = $(data.elem).find("option:selected").attr("receiver");
      tableData[index]["templetId"] = data.value;
      tableData[index]["genBill"] = gen;
      tableData[index]["receiverId"] = receiver;
      tableData[index]["genBillName"] = companyCodeToName(gen);
      tableData[index]["receiverName"] = companyCodeToName(receiver);
      table.reload("addContractAreaGrid", { data: tableData });
      var dataScroll = tableView.find('tr[data-index="' + index + '"]');
      dataScroll[0].scrollIntoViewIfNeeded();
    });
    // 派单类型
    form.on("select(selectDispatchType)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["dispatchType"] = data.value;
    });
    // 小合同状态
    form.on("select(selectStatus)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["status"] = data.value;
    });
    // 是否需要劳动合同
    form.on("select(selectHasLaborContract)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["hasLaborContract"] = data.value;

      form.render();
    });
    // 劳动合同版本
    form.on("select(selectTempletType)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["templetType"] = data.value;
    });
    // 是否存档
    form.on("select(selectArchiveFlag)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["archiveFlag"] = data.value;
    });
    // 是否外呼
    form.on("select(selectTelFlag)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["telFlag"] = data.value;
    });
    // 是否单立户
    form.on("select(selectAccountFlag)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["accountFlag"] = data.value;
      if (data.value != 2) {
        tableData[index]["sinAccName"] = null;
        tableData[index]["sinAccId"] = null;
        table.reload("addContractAreaGrid", { data: tableData });
      }
    });
    // 是否优先
    form.on("select(selectFirstFlag)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["firstFlag"] = data.value;
    });

    // 是否单工伤
    form.on("select(selectInjuryFlag)", function (data) {
      var index = $(data.elem).parent().parent().parent().attr("data-index");
      tableData[index]["injuryFlag"] = data.value;
    });

    /*监听编辑*/
    table.on("edit(addContractAreaTableFilter)", function (obj) {
      //小合同名称
      if (obj.field == "name") {
        var index = $(obj.tr).attr("data-index");
        tableData[index][obj.field] = obj.value;
      }
      //特殊说明
      if (obj.field == "remark") {
        var index = $(obj.tr).attr("data-index");
        tableData[index][obj.field] = obj.value;
      }
      //派单人
      if (obj.field == "dispatchMan") {
        var index = $(obj.tr).attr("data-index");
        tableData[index][obj.field] = obj.value;
      }
    });

    //动态表格监听操作

    /*监听表格*/
    table.on("toolbar(addContractAreaTableFilter)", function (obj) {
      var checkStatus = table.checkStatus(obj.config.id),
        data = checkStatus.data;
      switch (obj.event) {
        case "addGroup":
          /*加载表格*/
          add();
          break;
        case "delGroup":
          if (data.length === 0) {
            layer.msg("请选择一行");
          } else {
            del();
          }
          break;
        case "copy":
          if (data.length != 1) {
            layer.msg("请选择一行");
          } else {
            var arr = table.cache.addContractAreaGrid;
            for (var i = 0; i < arr.length; i++) {
              if (arr[i].LAY_CHECKED === true) {
                // 深拷贝 防止两个对象值引用同一个地址
                var newRowData = {};
                $.extend(true, newRowData, tableData[arr[i].LAY_TABLE_INDEX]);
                copy(newRowData);
                break;
              }
            }
          }
          break;
      }
    });

    function getSpecialAttachmentByContractType() {
      let contractType = $("#contractTypeHidden").val();
      $.ajax({
        type: "GET",
        url:
          ML.contextPath + "/customer/contractArea/querySpecialAttachmentList",
        data: { paramData: JSON.stringify({ contractType: contractType }) },
        dataType: "json",
        success: function (data) {
          specialAttachmentList = data.data;
        },
        error: function (data) {
          layer.msg(data);
          console.log("error");
        },
      });
    }

    form.on("submit(save)", function (data) {
      if (tableData == false) {
        return layer.msg("请添加表格内容后再提交哦！");
      }

      var addList = [];

      // 判断数据完整性
      for (var i = 0; i < tableData.length; i++) {
        if (!tableData[i].receivingId) {
          return layer.msg("请选择接单方后再保存！");
        }
        if (!tableData[i].name) {
          return layer.msg("请选择小合同名称后再保存！");
        }
        if (
          tableData[i].accountFlag == 2 &&
          ML.isEmpty(tableData[i].sinAccId)
        ) {
          return layer.msg(
            "第" +
              (i + 1) +
              "行的小合同类型是单立户，但是没有选择需要绑定的单立户！"
          );
        }
        var add = tableData[i];
        add["receiving"] = tableData[i].receivingId;
        addList.push(add);
      }

      // 检查是否需要上传附件
      if (specialAttachmentList && specialAttachmentList.length > 0) {
        let contractType = $("#contractTypeHidden").val();

        // 遍历每一行数据，检查是否需要附件
        for (let index = 0; index < addList.length; index++) {
          let needAttachment = false;
          let currentRow = addList[index];

          // 检查当前行是否匹配特殊附件要求
          for (let j = 0; j < specialAttachmentList.length; j++) {
            let specialItem = specialAttachmentList[j];
            if (
              specialItem.contractType == contractType &&
              specialItem.serviceSiteCode == currentRow.receiving &&
              specialItem.cityCode == currentRow.cityCode
            ) {
              needAttachment = true;
              break;
            }
          }

          // 如果需要附件但没有上传，立即返回错误提示
          if (needAttachment && !currentRow.attachmentId) {
            return layer.msg(
              "第" + (index + 1) + "行数据需要上传附件！请先上传附件后再保存。"
            );
          }
        }
      }

      // ML.layuiButtonDisabled($('#save'));// 禁用
      data.field["contractAreaList"] = addList;
      // console.log(data.field)
      // 发送请求
      $.ajax({
        url: ML.contextPath + "/customer/contractArea/saveContractArea",
        type: "POST",
        dataType: "json",
        data: { contractAreaJson: JSON.stringify(data.field) },
        success: function (result) {
          layer.closeAll("iframe");
          layer.msg(result.msg);
        },
        error: function (data) {
          layer.msg("系统繁忙，请稍后重试!");
          ML.layuiButtonDisabled($("#" + type), "true");
        },
      });
      return false;
    });

    // 关闭弹窗
    $(document).on("click", "#cancel", function () {
      layer.closeAll("iframe");
    });

    //增加延时
    setTimeout(function () {
      add();
    }, 200);

    // 新增
    function add() {
      var defaultData = {};
      defaultData["signCom"] = $("#belongCompany").val();
      defaultData["distCom"] = $("#distCom").val();
      defaultData["contractNo"] = $("#contractNo").val();
      defaultData["commSupervisor"] = $("#commSupervisor").val();
      tableData.push(defaultData);
      table.reload("addContractAreaGrid", { data: tableData });
      form.render();
    }

    // 删除
    function del() {
      layer.confirm("你确定要删除么？", { btn: ["确定", "取消"] }, function () {
        var obj = table.cache["addContractAreaGrid"];
        for (var i = obj.length - 1; i >= 0; i--) {
          if (obj[i]["LAY_CHECKED"]) {
            obj.splice(obj[i]["LAY_TABLE_INDEX"], 1);
          }
        }
        tableData = obj;
        layer.msg("删除成功", { time: 10 }, function () {
          table.reload("addContractAreaGrid", { data: tableData });
        });
      });
    }

    // 删除
    function delOne(index) {
      layer.confirm("你确定要删除么？", { btn: ["确定", "取消"] }, function () {
        tableData.splice(index, 1);
        layer.msg("删除成功", { time: 10 }, function () {
          table.reload("addContractAreaGrid", { data: tableData });
        });
      });
    }

    // 复制
    function copy(data) {
      tableData.push(data);
      table.reload("addContractAreaGrid", { data: tableData });
    }

    // 将code转换成名字
    function companyCodeToName(code) {
      for (var i = 0; i < company.length; i++) {
        if (company[i].orgCode === code) {
          return company[i].orgName;
        }
      }
    }

    /**
     * instance n.实例 情况
     * */
    // 获取所有的分公司
    function getAllCompany() {
      $.ajax({
        type: "GET",
        url: ML.contextPath + "/customer/contractArea/getCompany",
        dataType: "json",
        success: function (data) {
          company = data;
        },
        error: function (data) {
          layer.msg(data);
          console.log("error");
        },
      });
    }

    // 根据客户编号获取模板
    function findBillTempletList() {
      var contractNo = $("#contractNo").val();
      let templetType = 1;
      let contractTypeHidden = $("#contractTypeHidden").val();
      //如果没有其它情况就只显示社保账单模板
      //如果合同类型为 =9 也就是代发工资,那么就要只显示工资类型的账单模板
      if (contractTypeHidden == 9) {
        templetType = 2;
        //如果合同类型为 =6 也就是商保,那么就要只显示商保类型的账单模板
      } else if (contractTypeHidden == 6) {
        templetType = 3;
      }
      $.ajax({
        type: "GET",
        url: ML.contextPath + "/customer/contractArea/findBillTempletList",
        data: { contractNo: contractNo, templetType: templetType },
        dataType: "json",
        success: function (data) {
          billTemplet = data;
        },
        error: function (data) {
          layer.msg(data);
          console.log("error");
        },
      });
    }

    // 页面加载函数，在页面打开时执行
    $(document).ready(function () {
      getSpecialAttachmentByContractType();
      getAllCompany();
      $("#contractType").val(
        ML.dictFormatter("CONTRACT_CATEGORY", $("#contractTypeHidden").val())
      );
      //代发工资  为9   当合同类型为代发工资时需要只显示工资账单模板
      findBillTempletList();
    });

    // 模拟接口查询是否需要特殊附件
    function checkSpecialAttachment(contractType, receivingId, cityCode) {
      return new Promise((resolve) => {
        // 模拟接口返回结果
        setTimeout(() => {
          const result = {
            needAttachment: contractType === 9 && receivingId && cityCode, // 示例条件
            attachmentId: null,
            attachmentName: null,
          };
          resolve(result);
        }, 500);
      });
    }

    // 上传附件
    // function uploadAttachment(index) {
    //     layer.open({
    //         type: 2,
    //         title: "上传附件",
    //         area: ['40%', '40%'],
    //         content: ML.contextPath + "/customer/contractArea/uploadAttachment",
    //         success: function (layero, index) {
    //             var body = layer.getChildFrame('body', index);
    //             var parentIndex = parent.layer.getFrameIndex(window.name);
    //             body.find("#parentIframeIndex").val(parentIndex);
    //         },
    //         end: function () {
    //             const attachmentData = localStorage.getItem('attachment');
    //             if (attachmentData) {
    //                 const { attachmentId, attachmentName } = JSON.parse(attachmentData);
    //                 tableData[index].attachmentId = attachmentId;
    //                 tableData[index].attachmentName = attachmentName;
    //                 table.reload('addContractAreaGrid', { data: tableData });
    //                 localStorage.removeItem('attachment');
    //             }
    //         }
    //     });
    // }

    // 上传附件功能
    $(document).on("click", ".upload-attachment", function () {
      var index = $(this).data("index");
      var row = tableData[index];
      // 创建上传窗口
      layer.open({
        type: 1,
        title: "上传附件",
        area: ["600px", "400px"],
        content:
          '<div class="layui-upload">' +
          '<div class="layui-input-block" style="width: 100%;">' +
          '<button type="button" class="layui-btn layui-btn-normal" id="selectFile">选择文件</button>' +
          '<blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">' +
          "<p>预览图：</p>" +
          '<div class="layui-upload-list" id="uploadPreview" style="display: flex; flex-wrap: wrap;"></div>' +
          "</blockquote>" +
          '<div style="text-align: center; margin-top: 20px;">' +
          '<button type="button" class="layui-btn layui-btn-primary" id="cancelUpload">取消</button>' +
          '<button type="button" class="layui-btn layui-btn-normal" id="saveUpload">保存</button>' +
          "</div>" +
          "</div>" +
          "</div>",
        success: function (layero, layerIndex) {
          // 使用全局的upload变量，不要重新声明
          var fileListView = layero.find("#uploadPreview");
          var uploadedFiles = [];

          // 显示已有的附件
          if (row.attachmentIds && row.attachmentIds.length > 0) {
            row.attachmentIds.forEach((item) => {
              ML.ajax(
                "/sys/file/getFileName",
                { fileId: item.fileId },
                function (res) {
                  let fileName = res.data.fileName
                    ? res.data.fileName
                    : item.fileId;
                  uploadedFiles.push({
                    fileId: item.fileId,
                    fileName: fileName,
                  });
                  fileListView.append(
                    '<div style="margin: 5px; width: 60%; border: 1px solid #e6e6e6; padding: 8px; border-radius: 4px; background: #fff; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">' +
                      '<div style="flex: 1; min-width: 0; margin-right: 10px;">' +
                      '<a href="javascript:void(0)" class="download-attachment" data-file-id="' +
                      item.fileId +
                      '" style="color: #009688; text-decoration: none; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="点击下载: ' +
                      fileName +
                      '">' +
                      '<i class="layui-icon layui-icon-file" style="margin-right: 8px; color: #5FB878;"></i>' +
                      fileName +
                      "</a>" +
                      "</div>" +
                      '<a href="javascript:void(0)" class="delete-attachment" data-index="' +
                      item.fileId +
                      '" style="color: #FF5722; padding: 4px 6px; border-radius: 2px; transition: background-color 0.2s; flex-shrink: 0; display: flex; align-items: center; justify-content: center;" title="删除附件" onmouseover="this.style.backgroundColor=\'#ffebee\'" onmouseout="this.style.backgroundColor=\'transparent\'">' +
                      '<i class="layui-icon layui-icon-delete" style="font-size: 16px;"></i>' +
                      "</a>" +
                      "</div>"
                  );
                },
                "GET"
              );
            });
          } else if (row.attachmentId) {
            // 如果有attachmentId但没有attachmentIds，说明是以字符串形式保存的多个附件
            let fileIds = row.attachmentId.split(",");
            fileIds.forEach((i, index) => {
              ML.ajax(
                "/sys/file/getFileName",
                { fileId: i },
                function (res) {
                  let fileName = res.data.fileName ? res.data.fileName : i;
                  uploadedFiles.push({
                    fileId: i,
                    fileName: fileName,
                  });
                  fileListView.append(
                    '<div style="margin: 5px; width: 60%; border: 1px solid #e6e6e6; padding: 8px; border-radius: 4px; background: #fff; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">' +
                      '<div style="flex: 1; min-width: 0; margin-right: 10px;">' +
                      '<a href="javascript:void(0)" class="download-attachment" data-file-id="' +
                      i +
                      '" style="color: #009688; text-decoration: none; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="点击下载: ' +
                      fileName +
                      '">' +
                      '<i class="layui-icon layui-icon-file" style="margin-right: 8px; color: #5FB878;"></i>' +
                      fileName +
                      "</a>" +
                      "</div>" +
                      '<a href="javascript:void(0)" class="delete-attachment" data-index="' +
                      i +
                      '" style="color: #FF5722; padding: 4px 6px; border-radius: 2px; transition: background-color 0.2s; flex-shrink: 0; display: flex; align-items: center; justify-content: center;" title="删除附件" onmouseover="this.style.backgroundColor=\'#ffebee\'" onmouseout="this.style.backgroundColor=\'transparent\'">' +
                      '<i class="layui-icon layui-icon-delete" style="font-size: 16px;"></i>' +
                      "</a>" +
                      "</div>"
                  );
                },
                "GET"
              );
            });
          }

          // 下载附件事件
          layero.on("click", ".download-attachment", function () {
            var fileId = $(this).data("file-id");
            downloadAttachment(fileId);
          });

          // 删除附件事件
          layero.on("click", ".delete-attachment", function () {
            var deleteIndex = $(this).data("index");
            layer.confirm(
              "确定要删除这个附件吗？",
              { icon: 3, title: "提示" },
              function (confirmIndex) {
                // 从uploadedFiles数组中删除
                uploadedFiles.splice(deleteIndex, 1);

                // 重新渲染附件列表
                fileListView.empty();
                for (var i = 0; i < uploadedFiles.length; i++) {
                  fileListView.append(
                    '<div style="margin: 5px; width: 60%; border: 1px solid #e6e6e6; padding: 8px; border-radius: 4px; background: #fff; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">' +
                      '<div style="flex: 1; min-width: 0; margin-right: 10px;">' +
                      '<a href="javascript:void(0)" class="download-attachment" data-file-id="' +
                      uploadedFiles[i].fileId +
                      '" style="color: #009688; text-decoration: none; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="点击下载: ' +
                      uploadedFiles[i].fileName +
                      '">' +
                      '<i class="layui-icon layui-icon-file" style="margin-right: 8px; color: #5FB878;"></i>' +
                      uploadedFiles[i].fileName +
                      "</a>" +
                      "</div>" +
                      '<a href="javascript:void(0)" class="delete-attachment" data-index="' +
                      i +
                      '" style="color: #FF5722; padding: 4px 6px; border-radius: 2px; transition: background-color 0.2s; flex-shrink: 0; display: flex; align-items: center; justify-content: center;" title="删除附件" onmouseover="this.style.backgroundColor=\'#ffebee\'" onmouseout="this.style.backgroundColor=\'transparent\'">' +
                      '<i class="layui-icon layui-icon-delete" style="font-size: 16px;"></i>' +
                      "</a>" +
                      "</div>"
                  );
                }

                layer.close(confirmIndex);
                layer.msg("附件删除成功", { icon: 1 });
              }
            );
          });

          // 取消按钮
          layero.find("#cancelUpload").on("click", function () {
            layer.close(layerIndex);
          });

          // 保存按钮
          layero.find("#saveUpload").on("click", function () {
            // 保留重要字段的原始值
            var originalReceivingId = row.receivingId;
            var originalReceiving = row.receiving;

            // 将文件信息保存到行数据
            // 1. 保存attachmentIds用于显示
            row.attachmentIds = [];
            console.log(uploadedFiles);

            // 2. 保存attachmentId用于提交表单
            if (uploadedFiles && uploadedFiles.length > 0) {
              uploadedFiles.forEach((i) => {
                row.attachmentIds.push({
                  fileId: i.fileId,
                  fileName: i.fileName,
                });
              });

              // 将多个文件ID连接成字符串，格式为: 'fileId1,fileId2'
              let fileIds = [];
              let fileNames = [];
              for (var i = 0; i < uploadedFiles.length; i++) {
                fileIds.push(uploadedFiles[i].fileId);
                fileNames.push(uploadedFiles[i].fileName);
              }

              row.attachmentId = fileIds.join(",");
              row.attachmentName = fileNames.join(",");
              console.log(row.attachmentId);
              console.log(row.attachmentName);
            } else {
              // 如果没有附件，清空相关字段
              row.attachmentId = "";
              row.attachmentName = "";
              row.attachmentIds = [];
              console.log("清空附件字段");
            }

            // 确保重要字段不丢失
            if (originalReceivingId) {
              row.receivingId = originalReceivingId;
            }

            if (originalReceiving) {
              row.receiving = originalReceiving;
            }

            // 手动更新tableData数组中的对应项
            tableData[index] = row;

            // 刷新表格
            table.reload("addContractAreaGrid", { data: tableData });

            // 关闭弹窗
            layer.close(layerIndex);
            layer.msg("附件保存成功", { icon: 1 });
          });

          // 初始化上传
          upload.render({
            elem: layero.find("#selectFile"),
            url: ML.contextPath + "/sys/file/upload",
            accept: "file",
            multiple: true,
            auto: true, // 自动上传，选择文件后立即上传
            exts: "zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb",
            before: function () {
              // 上传前记录重要字段，确保上传过程中不会丢失
              if (row.receivingId) {
                row._receivingId_backup = row.receivingId;
              }
              if (row.receiving) {
                row._receiving_backup = row.receiving;
              }

              layer.load(); // 上传loading
              layer.msg("文件上传中，请稍候...");
            },
            done: function (res, index, upload) {
              layer.closeAll("loading");

              // 恢复备份的字段
              if (row._receivingId_backup) {
                row.receivingId = row._receivingId_backup;
                delete row._receivingId_backup;
              }

              if (row._receiving_backup) {
                row.receiving = row._receiving_backup;
                delete row._receiving_backup;
              }

              if (res.code == 0) {
                // 将文件添加到已上传文件列表
                let fileId = res.data.fileId;
                ML.ajax(
                  "/sys/file/getFileName",
                  { fileId: fileId },
                  function (rs) {
                    let fileName = rs.data.fileName ? rs.data.fileName : fileId;
                    uploadedFiles.push({
                      fileName: fileName,
                      fileId: fileId,
                    });

                    // 添加文件到预览区域 - 与编辑页面保持一致的样式
                    fileListView.append(
                      '<div style="margin: 5px; width: 60%; border: 1px solid #e6e6e6; padding: 8px; border-radius: 4px; background: #fff; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">' +
                        '<div style="flex: 1; min-width: 0; margin-right: 10px;">' +
                        '<a href="javascript:void(0)" class="download-attachment" data-file-id="' +
                        fileId +
                        '" style="color: #009688; text-decoration: none; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="点击下载: ' +
                        fileName +
                        '">' +
                        '<i class="layui-icon layui-icon-file" style="margin-right: 8px; color: #5FB878;"></i>' +
                        fileName +
                        "</a>" +
                        "</div>" +
                        '<a href="javascript:void(0)" class="delete-attachment" data-index="' +
                        (uploadedFiles.length - 1) +
                        '" style="color: #FF5722; padding: 4px 6px; border-radius: 2px; transition: background-color 0.2s; flex-shrink: 0; display: flex; align-items: center; justify-content: center;" title="删除附件" onmouseover="this.style.backgroundColor=\'#ffebee\'" onmouseout="this.style.backgroundColor=\'transparent\'">' +
                        '<i class="layui-icon layui-icon-delete" style="font-size: 16px;"></i>' +
                        "</a>" +
                        "</div>"
                    );
                    layer.msg("文件上传成功", { icon: 1 });
                  },
                  "GET"
                );
              } else {
                layer.msg("上传失败: " + (res.msg || "未知错误"), { icon: 2 });
              }
            },
            error: function () {
              // 恢复备份的字段
              if (row._receivingId_backup) {
                row.receivingId = row._receivingId_backup;
                delete row._receivingId_backup;
              }

              if (row._receiving_backup) {
                row.receiving = row._receiving_backup;
                delete row._receiving_backup;
              }

              layer.closeAll("loading");
              layer.msg("上传失败，请重试", { icon: 2 });
            },
          });
        },
      });
    });

    // 下载附件
    function downloadAttachment(attachmentId) {
      window.open(ML.fileServerUrl + attachmentId);
    }

    // 校验附件是否上传
    function validateAttachments() {
      for (let i = 0; i < tableData.length; i++) {
        if (tableData[i].needAttachment && !tableData[i].attachmentId) {
          return layer.msg(`第${i + 1}行需要上传附件，请完成后再保存！`);
        }
      }
      return true;
    }
  }
);
//
