var ctx = ML.contextPath;
var data ;

layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'upload', 'tableSelect', 'table'], function () {
    var
        table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer,
        tableSelect = layui.tableSelect,
        element = layui.element;


    var tableData = [];
    form.on("submit(submitFilter)", function (data) {
        saveForm(data);
        return false;
    });

    // 表单提交
    function saveForm (data) {
        let ratioFlag = true;
        if (tableData == false) {
            return layer.msg('请添加表格内容后再提交哦！');
        }
        if ($("#ratioPayFlag").val()==1&&$("#providentPracticePayFlag").val()==1){
            if (tableData.length!=1||tableData[0].providentPracticeType!=3){
                return layer.msg('公积金是否需要分不同比例支付选择否和公积金汇补缴是否分开支付选择否时只需维护一条缴费类型为汇缴+补缴的数据即可！');
            }
        }
        if ($("#ratioPayFlag").val()==1&&$("#providentPracticePayFlag").val()==2){
            ratioFlag=false;
            if (tableData.length!=2){
                return layer.msg('公积金是否需要分不同比例支付选择否和公积金汇补缴是否分开支付选择是时只需维护二条缴费类型分别为汇缴，补缴的数据即可！');
            }
        }
        let inputFlag =true;
        let typeFlag =true;
        let typeNoFlag =true;
        let addVoList = [];
        const types = [];
        tableData.forEach(function (obj) {
            if (obj.providentPayType!=1&&obj.providentPayType!=5){
                if (ML.isEmpty(obj.receivePaymentBankNo)||ML.isEmpty(obj.receivePaymentBankName)||ML.isEmpty(obj.receivePaymentBankType)||ML.isEmpty(obj.receivePaymentSubBank)){
                    inputFlag =false;

                }
            }
            types.push(String(obj.providentPracticeType));

            if ($('#providentPracticePayFlag').val()==1 && obj.providentPracticeType !=3){
                typeFlag = false;
            }
            if ($('#providentPracticePayFlag').val()==2 && obj.providentPracticeType ==3){
                typeNoFlag = false;
            }
            let add = {};
            add['ratio'] = obj.ratio;
            add['providentPayType'] = obj.providentPayType;
            add['providentPracticeType'] = obj.providentPracticeType;
            add['paymentBankNo'] = obj.paymentBankNo;
            add['paymentBankName'] = obj.paymentBankName;
            add['paymentBankType'] = obj.paymentBankType;
            add['receivePaymentBankNo'] = obj.receivePaymentBankNo;
            add['receivePaymentBankName'] = obj.receivePaymentBankName;
            add['receivePaymentSubBank'] = obj.receivePaymentSubBank;
            add['receivePaymentBankType'] = obj.receivePaymentBankType;
            addVoList.push(add);
        });
        if (!inputFlag){
            layer.msg('收款银行信息必填！');
            return false;
        }
        if (!typeFlag){
            layer.msg('公积金汇补缴是否分开支付选择否时，公积金缴费类型只能选择汇缴加补缴！');
            return false;
        }
        if (!typeNoFlag){
            layer.msg('公积金汇补缴是否分开支付选择是时，公积金缴费类型只能选择汇缴或者补缴！');
            return false;
        }
        if (!ratioFlag){
            if ( !types.includes("1") || !types.includes("2")){
                layer.msg('公积金是否需要分不同比例支付选择否和公积金汇补缴是否分开支付选择是时只需维护二条缴费类型分别为汇缴，补缴的数据即可');
                return false;
            }
        }
        data.field['insurancePracticePayBankDetailConfigVoList'] = addVoList;
        if(data.field.layTableCheckbox){
            delete data.field.layTableCheckbox;
        }
        $.ajax({
            url: ML.contextPath + "/base/insurancePracticeBank/updateInsurancePracticePayBankConfig",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                if (result.code == 0) {
                    layer.closeAll('iframe');
                    layer.msg(result.msg);
                } else if (result.code == -1) {
                    layer.msg(result.msg);
                } else {
                    layer.msg("系统繁忙，请稍后重试！");
                }
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
    };




    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');

    });

    //c.页面加载完成事件
    $(document).ready(function () {
        let configVo = JSON.parse($('#configVo').val());
        $('#orgName').val(configVo.orgName);
        $('#ratioFlag').val(configVo.ratioFlag);
        $('#ratioPayFlag').val(configVo.ratioPayFlag);
        $('#ratioReceiptFlag').val(configVo.ratioReceiptFlag);
        $('#socialPayFlag').val(configVo.socialPayFlag);
        $('#socialPracticePayFlag').val(configVo.socialPracticePayFlag);
        $('#socialPracticePayDetailFlag').val(configVo.socialPracticePayDetailFlag);
        $('#providentPracticePayFlag').val(configVo.providentPracticePayFlag);
        $('#providentPracticePayDetailFlag').val(configVo.providentPracticePayDetailFlag);
        $('#dispatchBankNo').val(configVo.dispatchBankNo);
        $('#dispatchBankName').val(configVo.dispatchBankName);
        $('#dispatchBankType').val(configVo.dispatchBankType);
        $('#receivingBankNo').val(configVo.receivingBankNo);
        $('#receivingBankName').val(configVo.receivingBankName);
        $('#receivingBankType').val(configVo.receivingBankType);
        if (configVo.ratioFlag==2){
            document.getElementById('ratioPayFlag').disabled = false;
            document.getElementById('ratioReceiptFlag').disabled = false;
        }
        if (configVo.providentPracticePayFlag==2){
            document.getElementById('providentPracticePayDetailFlag').disabled = false;
        }
        if (configVo.socialPracticePayFlag==2){
            document.getElementById('socialPracticePayDetailFlag').disabled = false;
        }
        form.render("select")
        JSON.parse($('#detailVoList').val()).forEach(function (obj) {
            tableData.push(obj);
        });
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/base/insurancePack/getInsurancePackCodeAndRatioCodeByOrgCode?orgCode="+configVo.orgCode,
            dataType: 'json',
            success: function (data) {
                ratioList = [];
                ratioList =data.data;
                table.reload("addGrid");
                form.render('select');
            },
            error: function (data) {
                console.log("error")
            }
        });
        if ($("#optType").val() == "check"){
            document.getElementById("butDiv").style.visibility = "hidden";
        }
    });







    table.render({
        id: 'addGrid',
        elem: '#addTable'
        , page: false
        , limit: 100
        , defaultToolbar: []
        , toolbar: '#topbtn'
        , data: JSON.parse($('#detailVoList').val())
        , cols: [[
            {type: 'checkbox', width: '3%'}
            , {title: '序号', type: 'numbers', width: '4%'}
            , {
                field: 'ratio', title: '<i style="color: red">*</i>公积金比例', align: 'center', width: '6%'
                , templet: '#ratio'
            }
            , {
                field: 'providentPayType', title: '<i style="color: red">*</i>公积金支付方式', align: 'center', width: '8%'
                , templet: '#payType'
            }
            , {
                field: 'providentPracticeType', title: '<i style="color: red">*</i>公积金缴费类型', align: 'center', width: '8%'
                , templet: '#payDetailType'
            }
            , {
                field: 'paymentBankNo', title: '<i style="color: red">*</i>公积金付款账户', align: 'center', width: '13%'
                , edit: 'text'
            }
            , {
                field: 'paymentBankName', title: '<i style="color: red">*</i>公积金付款账户名称', align: 'center', width: '14%'
                , edit: 'text'
            }
            , {
                field: 'paymentBankType', title: '<i style="color: red">*</i>公积金付款银行类型', align: 'center', width: '8%'
                , templet: '#bankType'
            }
            , {
                field: 'receivePaymentBankNo', title: '收款银行账户', align: 'center', width: '10%'
                , edit: 'text'
            }
            , {
                field: 'receivePaymentBankName', title: '收款银行名称', align: 'center', width: '10%'
                , edit: 'text'
            }
            , {
                field: 'receivePaymentSubBank', title: '收款银行支行信息', align: 'center', width: '10%'
                , edit: 'text'
            }
            , {
                field: 'receivePaymentBankType', title: '收款银行类型', align: 'center', width: '6%'
                , templet: '#rBankType'
            }

        ]]
    });


    table.on('toolbar(addTable)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id),
            data = checkStatus.data;
        switch (obj.event) {
            case 'addGroup':
                /*加载表格1*/
                add();
                break;
            case 'delGroup':
                if (data.length === 0) {
                    layer.msg('请选择一行');
                } else {
                    del(data)
                }
                break;

        }
    });


    function add() {
        // 定义一个新行的数据对象
        var newRow = {
            ratio: '',  // 公积金比例
            providentPayType: '',  // 公积金支付方式
            providentPracticeType: '',  // 公积金缴费类型
            paymentBankNo: '',  // 公积金付款账户
            paymentBankName: '',  // 公积金付款账户名称
            paymentBankType: '',  // 公积金付款银行类型
            receivePaymentBankNo: '',  // 收款银行账户（公积金中心）
            receivePaymentBankName: '',  // 收款银行名称（公积金中心）
            receivePaymentBankType: '' ,  // 收款银行类型（公积金中心）
            receivePaymentSubBank: ''   // 收款银行支行信息（公积金中心）
        };

        ;

        // 将新行数据添加到表格数据中
        tableData.push(newRow);

        // 刷新表格以显示新增的行
        table.reload('addGrid', {
            data: tableData
        });
    }

    function del(data) {
        if (data.length === 0) return; // 如果没有选择任何行，则直接返回

        // 获取当前表格的数据
        tableData = table.cache.addGrid;
        // 过滤掉被选中要删除的行
        for (var i = 0; i < tableData.length; i++) {
            var isRowSelected = false;
            for (var j = 0; j < data.length; j++) {
                if (tableData[i].LAY_CHECKED === true) {
                    tableData.splice(i,1);
                }
            }

        }

        // 刷新表格以移除已删除的行
        table.reload('addGrid', {
            data: tableData
        });
    }


    form.on('select(providentPayType)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['providentPayType'] = data.value;
    });

    form.on('select(providentPracticeType)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['providentPracticeType'] = data.value;
    });

    form.on('select(paymentBankType)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['paymentBankType'] = data.value;
    });

    form.on('select(receivePaymentBankType)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['receivePaymentBankType'] = data.value;
    });

    form.on('select(receivePaymentBankType)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['receivePaymentBankType'] = data.value;
    });

    form.on('select(orgCodeFilter)', function (data) {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/base/insurancePack/getInsurancePackCodeAndRatioCodeByOrgCode?orgCode="+data.value,
            dataType: 'json',
            success: function (data) {
                ratioList = [];
                ratioList =data.data;
                table.reload("addGrid");
                form.render('select');
            },
            error: function (data) {
                console.log("error")
            }
        });
    });

    form.on('select(selectRatio)', function (data) {
        let index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['ratio'] = data.value;

    });

    form.on('select(ratioFlag)', function (data) {
        if (data.value==2){
            $("#ratioPayFlag").val(2)
            $("#ratioReceiptFlag").val(2)
            document.getElementById('ratioPayFlag').disabled = false;
            document.getElementById('ratioReceiptFlag').disabled = false;
        }else {
            $("#ratioPayFlag").val(1)
            $("#ratioReceiptFlag").val(1)
            document.getElementById('ratioPayFlag').disabled = true;
            document.getElementById('ratioReceiptFlag').disabled = true;
        }
        form.render('select');


    });

    form.on('select(ratioPayFlag)', function (data) {
        if (data.value==1){
            layer.alert("您选择了否，新增公积金比例时，您可以任意选择公积金比例，系统不会根据比例计算。");
        }

    });

    form.on('select(socialPracticePayFlag)', function (data) {
        if (data.value==2){
            $("#socialPracticePayDetailFlag").val('')
            document.getElementById('socialPracticePayDetailFlag').disabled = false;
        }else {
            document.getElementById('socialPracticePayDetailFlag').disabled = true;
            $("#socialPracticePayDetailFlag").val(1)
        }
        form.render('select');
    });

    form.on('select(providentPracticePayFlag)', function (data) {
        if (data.value==2){
            $("#providentPracticePayDetailFlag").val('')
            document.getElementById('providentPracticePayDetailFlag').disabled = false;
        }else {
            document.getElementById('providentPracticePayDetailFlag').disabled = true;
            $("#providentPracticePayDetailFlag").val(1)
        }
        form.render('select');
    });


    table.on('edit(addTable)', function (obj) {
        var index = $(obj.tr).attr("data-index");
        tableData[index]['paymentBankNo'] = obj.data.paymentBankNo;
        tableData[index]['paymentBankName'] = obj.data.paymentBankName;
        tableData[index]['receivePaymentBankNo'] = obj.data.receivePaymentBankNo;
        tableData[index]['receivePaymentBankName'] = obj.data.receivePaymentBankName;
        tableData[index]['receivePaymentSubBank'] = obj.data.receivePaymentSubBank;
    });










});
