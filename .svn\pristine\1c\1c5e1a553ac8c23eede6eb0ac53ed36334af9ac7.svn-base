package com.reon.hr.sp.customer.entity.qys;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
    * 契约锁模板字段,和系统字段对应表
    */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class QysMappingField {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 契约锁字段(用于填充到模板文件)
    */
    private String qysFileField;

    /**
    * 自有系统字段
    */
    private String ourSystemField;

    /**
    * 分类类型(比如 通用,派遣,外包)
    */
    private Byte classificationType;

    /**
    * 系统字段解释(比如,员工合同表的员工姓名)
    */
    private String commentForField;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人
    */
    private String updater;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 删除标识(Y:已删除，N:未删除)
    */
    private String delFlag;
}