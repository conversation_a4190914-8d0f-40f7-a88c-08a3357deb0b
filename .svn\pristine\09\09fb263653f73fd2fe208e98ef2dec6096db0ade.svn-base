package com.reon.hr.sp.report.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.vo.CustomerEmpTrackReportVo;
import com.reon.hr.api.report.vo.EmpTrackReportVo;
import com.reon.hr.sp.report.service.report.EmpTrackReportService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class InsertEmpTrackReportJob implements SimpleJob {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IContractResourceWrapperService iContractResourceWrapperService;
    @Autowired
    private EmpTrackReportService empTrackReportService;
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            logger.info ("======================" + shardingContext.getJobName () + " start=============================");
            Integer dataMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.getString(new Date(), "yyyyMM")), -1);
            Integer lastDataMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.getString(new Date(),"yyyyMM")), -2);
            List<CustomerEmpTrackReportVo> customerEmpTrackReportVoList=iContractResourceWrapperService.findEmpTrackReport();
            List<EmpTrackReportVo> lastEmpTrackReportVoList=empTrackReportService.findEmpTrackReportByDataMonth(lastDataMonth);
            Map<String, EmpTrackReportVo> lastEmpTrackReportVoMap = lastEmpTrackReportVoList.stream().collect(Collectors.toMap(EmpTrackReportVo::getContractNo, Function.identity(), (key1, key2) -> key2));
            List<EmpTrackReportVo> empTrackReportVoList=new ArrayList<>();
            for (CustomerEmpTrackReportVo customerEmpTrackReportVo:customerEmpTrackReportVoList){
                EmpTrackReportVo empTrackReportVo = new EmpTrackReportVo();
                BeanUtils.copyProperties(customerEmpTrackReportVo,empTrackReportVo);
                EmpTrackReportVo lastEmpTrackReportVo = lastEmpTrackReportVoMap.get(empTrackReportVo.getContractNo());
                BigDecimal compareRatio=BigDecimal.ZERO;
                if(lastEmpTrackReportVo!=null){
                    BigDecimal lastEmpCnt = new BigDecimal(lastEmpTrackReportVo.getEmpCnt());
                    BigDecimal empCnt = new BigDecimal(empTrackReportVo.getEmpCnt());
                    compareRatio = empCnt.subtract(lastEmpCnt).divide(lastEmpCnt,4,BigDecimal.ROUND_HALF_UP);
                }
                empTrackReportVo.setDataMonth(dataMonth);
                empTrackReportVo.setCompareRatio(compareRatio);
                empTrackReportVoList.add(empTrackReportVo);
            }
            if(CollectionUtils.isNotEmpty(empTrackReportVoList)){
                empTrackReportService.deleteByDataMonth(dataMonth);
                empTrackReportService.insert(empTrackReportVoList);
            }
            logger.info ("======================" + shardingContext.getJobName () + " end=============================");
        }catch (Exception e){
            logger.error ("InsertEmpTrackReportJob exception:", e);
            throw new RuntimeException ();
        }
    }
}
