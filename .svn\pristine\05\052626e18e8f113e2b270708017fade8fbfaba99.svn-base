<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.GlobalCfgMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.base.entity.sys.GlobalCfg">
        <id column="cfg_key" jdbcType="VARCHAR" property="cfgKey"/>
        <result column="cfg_value" jdbcType="VARCHAR" property="cfgValue"/>
        <result column="cfg_desc" jdbcType="VARCHAR" property="cfgDesc"/>
        <result column="cfg_kind" jdbcType="VARCHAR" property="cfgKind"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
    cfg_key, cfg_value, cfg_desc, cfg_kind, creator, create_time, updater, update_time,
    del_flag
  </sql>
    <!--  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">-->
    <!--    select -->
    <!--    <include refid="Base_Column_List" />-->
    <!--    from global_cfg-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </select>-->
    <!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">-->
    <!--    delete from global_cfg-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </delete>-->
    <!--  <insert id="insert" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    insert into global_cfg (cfg_key, cfg_value, cfg_desc, -->
    <!--      cfg_kind, creator, create_time, -->
    <!--      updater, update_time, del_flag-->
    <!--      )-->
    <!--    values (#{cfgKey,jdbcType=VARCHAR}, #{cfgValue,jdbcType=VARCHAR}, #{cfgDesc,jdbcType=VARCHAR}, -->
    <!--      #{cfgKind,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, -->
    <!--      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}-->
    <!--      )-->
    <!--  </insert>-->
    <!--  <insert id="insertSelective" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    insert into global_cfg-->
    <!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
    <!--      <if test="cfgKey != null">-->
    <!--        cfg_key,-->
    <!--      </if>-->
    <!--      <if test="cfgValue != null">-->
    <!--        cfg_value,-->
    <!--      </if>-->
    <!--      <if test="cfgDesc != null">-->
    <!--        cfg_desc,-->
    <!--      </if>-->
    <!--      <if test="cfgKind != null">-->
    <!--        cfg_kind,-->
    <!--      </if>-->
    <!--      <if test="creator != null">-->
    <!--        creator,-->
    <!--      </if>-->
    <!--      <if test="createTime != null">-->
    <!--        create_time,-->
    <!--      </if>-->
    <!--      <if test="updater != null">-->
    <!--        updater,-->
    <!--      </if>-->
    <!--      <if test="updateTime != null">-->
    <!--        update_time,-->
    <!--      </if>-->
    <!--      <if test="delFlag != null">-->
    <!--        del_flag,-->
    <!--      </if>-->
    <!--    </trim>-->
    <!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
    <!--      <if test="cfgKey != null">-->
    <!--        #{cfgKey,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgValue != null">-->
    <!--        #{cfgValue,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgDesc != null">-->
    <!--        #{cfgDesc,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgKind != null">-->
    <!--        #{cfgKind,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="creator != null">-->
    <!--        #{creator,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="createTime != null">-->
    <!--        #{createTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="updater != null">-->
    <!--        #{updater,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="updateTime != null">-->
    <!--        #{updateTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="delFlag != null">-->
    <!--        #{delFlag,jdbcType=CHAR},-->
    <!--      </if>-->
    <!--    </trim>-->
    <!--  </insert>-->
    <!--  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    update global_cfg-->
    <!--    <set>-->
    <!--      <if test="cfgValue != null">-->
    <!--        cfg_value = #{cfgValue,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgDesc != null">-->
    <!--        cfg_desc = #{cfgDesc,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgKind != null">-->
    <!--        cfg_kind = #{cfgKind,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="creator != null">-->
    <!--        creator = #{creator,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="createTime != null">-->
    <!--        create_time = #{createTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="updater != null">-->
    <!--        updater = #{updater,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="updateTime != null">-->
    <!--        update_time = #{updateTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="delFlag != null">-->
    <!--        del_flag = #{delFlag,jdbcType=CHAR},-->
    <!--      </if>-->
    <!--    </set>-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </update>-->
    <!--  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    update global_cfg-->
    <!--    set cfg_value = #{cfgValue,jdbcType=VARCHAR},-->
    <!--      cfg_desc = #{cfgDesc,jdbcType=VARCHAR},-->
    <!--      cfg_kind = #{cfgKind,jdbcType=VARCHAR},-->
    <!--      creator = #{creator,jdbcType=VARCHAR},-->
    <!--      create_time = #{createTime,jdbcType=TIMESTAMP},-->
    <!--      updater = #{updater,jdbcType=VARCHAR},-->
    <!--      update_time = #{updateTime,jdbcType=TIMESTAMP},-->
    <!--      del_flag = #{delFlag,jdbcType=CHAR}-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </update>-->
    <!--  <resultMap id="BaseResultMap" type="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    <id column="cfg_key" jdbcType="VARCHAR" property="cfgKey" />-->
    <!--    <result column="cfg_value" jdbcType="VARCHAR" property="cfgValue" />-->
    <!--    <result column="cfg_desc" jdbcType="VARCHAR" property="cfgDesc" />-->
    <!--    <result column="cfg_kind" jdbcType="VARCHAR" property="cfgKind" />-->
    <!--    <result column="creator" jdbcType="VARCHAR" property="creator" />-->
    <!--    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
    <!--    <result column="updater" jdbcType="VARCHAR" property="updater" />-->
    <!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
    <!--    <result column="del_flag" jdbcType="CHAR" property="delFlag" />-->
    <!--  </resultMap>-->
    <!--  <sql id="Base_Column_List">-->
    <!--    cfg_key, cfg_value, cfg_desc, cfg_kind, creator, create_time, updater, update_time, -->
    <!--    del_flag-->
    <!--  </sql>-->
    <!--  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">-->
    <!--    select -->
    <!--    <include refid="Base_Column_List" />-->
    <!--    from global_cfg-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </select>-->
    <!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">-->
    <!--    delete from global_cfg-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </delete>-->
    <insert id="insert" parameterType="com.reon.hr.api.base.vo.GlobalCfgVo">
    insert into global_cfg (cfg_key, cfg_value, cfg_desc,
      cfg_kind, creator
      )
    values (#{cfgKey,jdbcType=VARCHAR}, #{cfgValue,jdbcType=VARCHAR}, #{cfgDesc,jdbcType=VARCHAR},
      #{cfgKind,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}
      )
  </insert>
    <!--  <insert id="insertSelective" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    insert into global_cfg-->
    <!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
    <!--      <if test="cfgKey != null">-->
    <!--        cfg_key,-->
    <!--      </if>-->
    <!--      <if test="cfgValue != null">-->
    <!--        cfg_value,-->
    <!--      </if>-->
    <!--      <if test="cfgDesc != null">-->
    <!--        cfg_desc,-->
    <!--      </if>-->
    <!--      <if test="cfgKind != null">-->
    <!--        cfg_kind,-->
    <!--      </if>-->
    <!--      <if test="creator != null">-->
    <!--        creator,-->
    <!--      </if>-->
    <!--      <if test="createTime != null">-->
    <!--        create_time,-->
    <!--      </if>-->
    <!--      <if test="updater != null">-->
    <!--        updater,-->
    <!--      </if>-->
    <!--      <if test="updateTime != null">-->
    <!--        update_time,-->
    <!--      </if>-->
    <!--      <if test="delFlag != null">-->
    <!--        del_flag,-->
    <!--      </if>-->
    <!--    </trim>-->
    <!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
    <!--      <if test="cfgKey != null">-->
    <!--        #{cfgKey,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgValue != null">-->
    <!--        #{cfgValue,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgDesc != null">-->
    <!--        #{cfgDesc,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="cfgKind != null">-->
    <!--        #{cfgKind,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="creator != null">-->
    <!--        #{creator,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="createTime != null">-->
    <!--        #{createTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="updater != null">-->
    <!--        #{updater,jdbcType=VARCHAR},-->
    <!--      </if>-->
    <!--      <if test="updateTime != null">-->
    <!--        #{updateTime,jdbcType=TIMESTAMP},-->
    <!--      </if>-->
    <!--      <if test="delFlag != null">-->
    <!--        #{delFlag,jdbcType=CHAR},-->
    <!--      </if>-->
    <!--    </trim>-->
    <!--  </insert>-->
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.api.base.vo.GlobalCfgVo">
        update global_cfg
        <set>
            <if test="cfgValue != null">
                cfg_value = #{cfgValue,jdbcType=VARCHAR},
            </if>
            <if test="cfgDesc != null">
                cfg_desc = #{cfgDesc,jdbcType=VARCHAR},
            </if>
            <if test="cfgKind != null">
                cfg_kind = #{cfgKind,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where cfg_key = #{cfgKey,jdbcType=VARCHAR}
    </update>
    <!--  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.base.entity.sys.GlobalCfg">-->
    <!--    update global_cfg-->
    <!--    set cfg_value = #{cfgValue,jdbcType=VARCHAR},-->
    <!--      cfg_desc = #{cfgDesc,jdbcType=VARCHAR},-->
    <!--      cfg_kind = #{cfgKind,jdbcType=VARCHAR},-->
    <!--      creator = #{creator,jdbcType=VARCHAR},-->
    <!--      create_time = #{createTime,jdbcType=TIMESTAMP},-->
    <!--      updater = #{updater,jdbcType=VARCHAR},-->
    <!--      update_time = #{updateTime,jdbcType=TIMESTAMP},-->
    <!--      del_flag = #{delFlag,jdbcType=CHAR}-->
    <!--    where cfg_key = #{cfgKey,jdbcType=VARCHAR}-->
    <!--  </update>-->

    <select id="getGlobalCfgListPage" parameterType="com.reon.hr.api.base.vo.GlobalCfgVo"
            resultType="com.reon.hr.api.base.vo.GlobalCfgVo">
        select
        <include refid="Base_Column_List"/>
        from global_cfg
        where del_flag = 'N'
        <if test="cfgKey != null and cfgKey != ''">
            and cfg_key = #{cfgKey}
        </if>
        <if test="cfgDesc != null and cfgDesc != ''">
            and cfg_desc like concat('%',#{cfgDesc},'%')
        </if>
    </select>

    <select id="getGlobalCfgKey" resultType="com.reon.hr.api.base.vo.GlobalCfgVo">
        select
        <include refid="Base_Column_List"/>
        from global_cfg
        where del_flag = 'N'
        <if test="cfgKey != null and cfgKey != ''">
            and cfg_key = #{cfgKey}
        </if>
    </select>

    <select id="getGlobalCfgKeys" resultType="com.reon.hr.api.base.vo.GlobalCfgVo">
        select
        <include refid="Base_Column_List"/>
        from global_cfg
        where del_flag = 'N'
          and cfg_key in
          <foreach collection="list" item="cfgKey" open="(" separator="," close=")">
              #{cfgKey}
          </foreach>
    </select>
</mapper>