package com.reon.hr.api.bill.enums;

/**
 * 支付操作类型枚举
 */
public enum PaymentOperationType {

    FREEZE(1, "冻结"),
    UNFREEZE(2, "解冻"),
    INVALIDATE(3, "作废"),
    RESTORE(4, "恢复有效"),
    INSERT(5, "新增")
    ;

    private final int code;
    private final String name;

    PaymentOperationType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举实例
     * @param code 操作类型编码
     * @return 对应的枚举实例，未找到时返回null
     */
    public static PaymentOperationType fromCode(int code) {
        for (PaymentOperationType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }
}
