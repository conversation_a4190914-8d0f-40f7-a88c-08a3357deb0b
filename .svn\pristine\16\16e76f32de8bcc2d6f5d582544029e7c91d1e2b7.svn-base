var ctx = ML.contextPath;
var billTableColumn = [];
var firstColumn = [
    {type: 'checkbox', width: '30', fixed: 'left',rowspan: 2},
    {field: 'name', title: '姓名', width: '90', align: 'center',rowspan: 2, fixed: 'left'},
    {field: 'empNo', title: '唯一号', width: '160', align: 'center',rowspan: 2, fixed: 'left'},
    {field: 'certNo', title: '身份证号码', width: '175', align: 'center',rowspan: 2, fixed: 'left'},
    {field: 'custName', title: '客户名称', width: '150',rowspan: 2, align: 'center'},
    {field: 'contractAreaName', title: '小合同名称', width: '150',rowspan: 2, align: 'center'},
    {
        field: 'empState', title: '员工状态', width: '100', align: 'center',rowspan: 2, templet: function (d) {
            return ML.dictFormatter("IN_OUT_STATUS", d.empState);
        }
    },
];
var secondColumn=[];
layui.use(['layer', 'table', 'upload'], function () {
    var table = layui.table, upload = layui.upload;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    var jobNo = $("#jobNo").val();
    // var publishFlag;// 是否公布
    var map = {};
    ML.ajax("/change/declare/getTableTitle", {'jobNo': jobNo}, function (res) {
        if (res.data){
            for (var i = 0; i < res.data.length; i++) {
                map[res.data[i].prodCode] = res.data[i];
                var colspan = 4;
                    if (res.data[i].comCol == res.data[i].indCol)
                        colspan = 2;
                    // if (res.data[i].comAuditFlag==1)
                    //     colspan += 2;
                    // if (res.data[i].indAuditFlag==1)
                    //     colspan += 2;
                    var title=ML.dictFormatter('PRODUCT_IND_TYPE', res.data[i].prodCode);
                    if (res.data[i].prodCode == 0){
                        title="社保";
                    }
                    var rowtitle = { field: '', title: title, align: 'center', colspan: colspan };
                    firstColumn.push(rowtitle);
                    if (res.data[i].comCol == res.data[i].indCol) {
                        secondColumn.push({ field: 'oldComBase' + res.data[i].prodCode, title: '原基数', width: '110', align: 'center' });
                    }else{
                        secondColumn.push({ field: 'oldComBase' + res.data[i].prodCode, title: '原单位基数', width: '110', align: 'center' });
                        secondColumn.push({ field: 'oldIndBase' + res.data[i].prodCode, title: '原个人基数', width: '110', align: 'center' });
                    }
                    // if (res.data[i].prodCode == 10) {
                    //     if (res.data[i].comAuditFlag==1)
                    //          secondColumn.push({ field: 'oldComRatio' + res.data[i].prodCode, title: '原单位比例', width: '110', align: 'center' });
                    //     if (res.data[i].indAuditFlag==1)
                    //          secondColumn.push({ field: 'oldIndRatio' + res.data[i].prodCode, title: '原个人比例', width: '110', align: 'center' });
                    // }

                    if (res.data[i].comCol == res.data[i].indCol) {
                        secondColumn.push({ field: 'newComBase' + res.data[i].prodCode, title: '新基数', width: '110', align: 'center' });
                    }else{
                        secondColumn.push({ field: 'newComBase' + res.data[i].prodCode, title: '新单位基数', width: '110', align: 'center' });
                        secondColumn.push({ field: 'newIndBase' + res.data[i].prodCode, title: '新个人基数', width: '110', align: 'center' });
                    }
                    // if (res.data[i].prodCode == 10) {
                    //     if (res.data[i].comAuditFlag == 1)
                    //          secondColumn.push({ field: 'newComRatio' + res.data[i].prodCode, title: '新单位比例', width: '110', align: 'center',edit:'text' });
                    //     if (res.data[i].indAuditFlag == 1)
                    //          secondColumn.push({ field: 'newIndRatio' + res.data[i].prodCode, title: '新个人比例', width: '110', align: 'center',edit:'text' });
                    // }
             }
            firstColumn.push({field: 'dataSource', title: '数据来源', width: '90',rowspan: 2, align: 'center',templet:function (d) {
                return ML.dictFormatter("DATA_SOURCE",d.dataSource);
                }});
            firstColumn.push({field: 'oprResultName', title: '调基结果', width: '90', align: 'center',rowspan: 2});
            firstColumn.push({field: 'updateTime', title: '操作时间', width: '120',rowspan: 2, align: 'center'});
            firstColumn.push({field: 'oprLog', title: '调基详情', width: '90',rowspan: 2,event: 'getChangeLog', align: 'center',templet:function (d) {
                    return '<a href="javascript:;;">查看</a>';
                }});
            billTableColumn.push(firstColumn);
            billTableColumn.push(secondColumn);
            layer.closeAll('loading');
            getTable();
        }
    }, "GET");
    // 渲染表格

    function getTable(keyword) {
        table.render({
            id: 'baseId',
            elem: '#baseId',
            url: ctx + '/change/adjust/getBaseTitle',
            where: {"jobNo": jobNo, "keyword": keyword},
            page: true, //默认为不开启
            height: 650,
            limits: [50, 100, 200],
            limit: 50,
            title: "申报员工信息",
            defaultToolbar: [],
            toolbar: '#toolbarDemo',
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: billTableColumn,
            done: function (res) {
                ML.hideNoAuth();
                $("#keyword").val(keyword);
                table.on('toolbar(baseFilter)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
                    switch (obj.event) {
                        case 'query':
                            getTable($("#keyword").val());
                            break;
                        // case 'export':
                        //     // 导出申报数据
                        //     window.location.href = ctx + "/change/declare/declareExport?jobNo=" + jobNo;
                        //     break;
                        // case 'exportPage':
                        //     // 导出页面数据
                        //     window.location.href = ctx + "/change/declare/declareExportPageData?jobNo=" + jobNo;
                        //     break;
                    }
                });

                table.on('tool(baseFilter)',function (obj) {
                    if (!obj.data.dataSource){
                        return layer.msg("暂无变更过程");
                    }
                    switch (obj.event) {
                        case 'getChangeLog':
                            layer.open({
                                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                                title: "基数变更过程",
                                area: ['48%', '50%'],
                                shade: 0,
                                maxmin: true,
                                offset: 'auto',
                                shade: [0.8, '#393D49'],
                                content: ctx + '/change/adjust/gotoChangeEmpPage?collectEmpId='+ obj.data.id,
                            });
                            break;
                    }
                });
                //监听单元格编辑
                table.on('edit(baseFilter)', function(obj){
                    var value = obj.value //得到修改后的值
                        ,data = obj.data //得到所在行所有键值
                        ,field = obj.field; //得到字段
                    //获取编辑前的值
                    var selector = obj.tr.selector+' td[data-field="'+ field +'"] div';
                    var oldtext = $(selector).text();
                    var msg = field.indexOf("Ratio") == -1 ? "请输入数字" : "请输入0-100的数字";
                    //判断数据类型
                    if(!isNumber(obj.value)) {
                        layer.msg(msg);
                        // 重点 赋值
                        $(obj.tr.selector + ' td[data-field="' + field + '"] input').val(oldtext);
                    }
                    // 小于下限==下限
                    var highVal = 0,lowVal = 0;
                    if (field.indexOf("Base") != -1 ){// 修改的是基数
                        for (var key in map){
                            if (map[key]){
                                highVal = map[key].highBase;
                                lowVal = map[key].lowBase;
                                break;
                            }
                        }
                    }
                    // if (field.indexOf("Ratio")!=-1){
                    //     highVal = 100;
                    //     lowVal = 0;
                    // }
                    if (value < lowVal){
                        $(obj.tr.selector + ' td[data-field="' + field + '"] input').val(lowVal);
                    }
                    // 大于上限==上限
                    if (value > highVal){
                        $(obj.tr.selector + ' td[data-field="' + field + '"] input').val(highVal);
                    }
                });
                //判断是非数字
                function isNumber(val) {
                    var regPos = /^\d+(\.\d+)?$/; //非负浮点数
                    var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
                    if (val && (regPos.test(val) || regNeg.test(val))) {
                        return true;
                    } else {
                        return false;
                    }
                }
                //上传
                upload.render({
                    elem: '#upload'
                    , url: ctx + '/change/declare/upload'
                    , accept: 'file' //普通文件
                    , auto: true
                    , exts: 'xls|xlsx'
                    , method: 'POST'
                    , before: function (obj) {
                        this.data = {'jobNo': jobNo};
                    }, choose: function (obj) {
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            if (file.size > (8 * 1024 * 1024)) {
                                layer.msg("上传文件大小不能超过8M", {icon: 2});
                                return;
                            }
                        });
                    }, done: function (res, index, upload) {
                        layer.msg(res.msg);
                        if (res.code == 0){
                            getTable($("#keyword").val());
                        }
                    }, error: function (index, upload) {
                        ML.layuiButtonDisabled($('#upload'), true);// 取消禁用
                        layer.msg("数据接口异常，上传失败！");
                    }
                });
            }
        });
    }





});