package com.reon.hr.modules.base.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ICompanyBankWrapperService;
import com.reon.hr.api.base.enums.CompanyBankDataType;
import com.reon.hr.api.base.vo.CompanyBankSearchVo;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR> guoqian
 * @date 2021/3/10 0010 17:30
 * @title 公司银行基础信息
 * @modify
 */
@RestController
@RequestMapping("/company/")
public class CompanyBankController extends BaseController {
    @Autowired
    ICompanyBankWrapperService iCompanyBankWrapperService;
    @RequestMapping("gotoCompBankPage")
    public ModelAndView gotoCompBankPage() {
        return new ModelAndView("base/companyBank/companyBankMainPage");
    }
    @RequestMapping("selectCompBank")
    public LayuiReplay selectCompBank(CompanyBankSearchVo vo) {
        Page<CompanyBankVo> page = iCompanyBankWrapperService.selectCompBank(vo);
        return LayuiReplay.success(page.getTotal(),page.getRecords());
    }
    @RequestMapping(value = {"editCompBankPage","lookCompBankPage"})
    public ModelAndView gotoEditCompBankPage(String id,String optype) {
        getRequest().setAttribute("id",id);
        getRequest().setAttribute("optype",optype);
        return new ModelAndView("base/companyBank/editCompanyBank");
    }
    @PostMapping("saveCompBank")
    public LayuiReplay saveCompBank(@RequestBody CompanyBankVo vo) {
       try {
           vo.setCreator(getSessionUser().getLoginName());
           vo.setUpdater(getSessionUser().getLoginName());
           iCompanyBankWrapperService.saveCompBank(vo);
       }catch (Exception e){
           e.printStackTrace();
           return LayuiReplay.error();
       }
        return LayuiReplay.success();
   }
    @RequestMapping("getCompBank")
    public LayuiReplay getCompBank(CompanyBankSearchVo vo) {
        CompanyBankVo companyBankVo = iCompanyBankWrapperService.selectCompById(vo);
        return LayuiReplay.success(companyBankVo);
    }


    @RequestMapping("/gotoAddSpecialCompanyBankPage")
    public ModelAndView gotoAddSpecialCompanyBankPage() {
        return new ModelAndView("base/companyBank/addSpecialCompanyBank");
    }
    @RequestMapping("/gotoUpdateSpecialCompanyBankPage")
    public ModelAndView gotoUpdateSpecialCompanyBankPage(String id) {
        getRequest().setAttribute("id",id);
        return new ModelAndView("base/companyBank/updateSpecialCompanyBank");
    }


    @RequestMapping("/addSpecialCompanyBank")
    public Object addSpecialCompanyBank(@RequestBody CompanyBankVo vo) {
        CompanyBankVo queryVo = new CompanyBankVo();
        queryVo.setCustId(vo.getCustId());
        queryVo.setType(CompanyBankDataType.SPECIAL.getCode());
        queryVo.setBankNo(vo.getBankNo());
        queryVo.setCompNo(vo.getCompNo());
        List<CompanyBankVo> queryVoList = iCompanyBankWrapperService.getCompanyBanks(queryVo);
        if (CollectionUtils.isNotEmpty(queryVoList)) {
            return LayuiReplay.error("此客户在同一分公司同一账号下已有特殊银行信息,请修改!");
        }
        vo.setCreator(getSessionUser().getLoginName());
        vo.setUpdater(getSessionUser().getLoginName());
        iCompanyBankWrapperService.addSpecialCompanyBank(vo);
        return LayuiReplay.success();
    }

    @RequestMapping("/updateSpecialCompanyBank")
    public Object updateSpecialCompanyBank(@RequestBody CompanyBankVo vo) {
        vo.setUpdater(getSessionUser().getLoginName());
        iCompanyBankWrapperService.updateSpecialCompanyBankById(vo);
        return LayuiReplay.success();
    }
}
