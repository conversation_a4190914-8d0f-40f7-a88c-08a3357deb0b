package com.reon.hr.api.customer.listener;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.utils.DateUtil;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

public class TwoHeaderImportExcelListener<T extends BaseImportDto> extends AnalysisEventListener<T> {

    private ImportDataDto<T> importDataDto = new ImportDataDto<>();
    /**
     * 存储Key
     */
    private Map<Integer, String> oneHeadMap = new HashMap<>();

    private static final List<SimpleDateFormat> formatList = Arrays.asList(
            new SimpleDateFormat("yyyy/MM/dd"),
            new SimpleDateFormat("yyyy-MM-dd"),
            new SimpleDateFormat("yyyyMMdd")
    );

    private static final String REQUIRED_FIELDS_NOT_FILLED = "必填项未填写";
    private static final String FORMAT_ERROR = "格式错误";
    private static final String INTEGER_CLASS_NAME = "class java.lang.Integer";
    private static final String BIGDECIMAL_CLASS_NAME = "class java.math.BigDecimal";
    private static final String DATE_CLASS_NAME = "class java.util.Date";

    public ImportDataDto<T> getImportDataDto() {
        return importDataDto;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Map<Integer, Cell> cellMap = readRowHolder.getCellMap();
        Integer rowIndex = readRowHolder.getRowIndex()+1;
        if(rowIndex==2){
            for (Integer key:oneHeadMap.keySet()) {
                String headName = oneHeadMap.get(key);
                String cellValue = getCellValue(cellMap.get(key));
                if(cellValue!=null){
                    if(headName!=null){
                        oneHeadMap.put(key,headName+"-"+ cellValue);
                    }else {
                        oneHeadMap.put(key,oneHeadMap.get(key-1).split("-")[0]+"-"+cellValue);
                    }
                }
            }
        }else {
            List<String> oneHeadMapValues = new ArrayList<>(oneHeadMap.values());
            data.setRowNum(rowIndex);
            Field[] fieldArray = data.getClass().getDeclaredFields();
            for (Field field : fieldArray) {
                field.setAccessible(true);
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                if (annotation != null) {
                    String[] annotationValues = annotation.value();
                    String value = annotationValues[0];
                    String fieldStr = "";
                    try {
                        Object object = field.get(data);
                        if (object != null) {
                            fieldStr = object.toString();
                        }else {
                            for (int i = 0; i < oneHeadMapValues.size(); i++) {
                                if(annotationValues.length==1){
                                    if(value.equals(oneHeadMapValues.get(i))){
                                        fieldStr = getCellValue(cellMap.get(i));
                                        if(fieldStr!=null){
                                            field.set(data,fieldStr);
                                        }
                                        break;
                                    }
                                }else {
                                    String[] oneHeadMapValuesSplit = oneHeadMapValues.get(i).split("-");
                                    if(value.equals(oneHeadMapValuesSplit[0])&&annotationValues[1].equals(oneHeadMapValuesSplit[1])){
                                        fieldStr = getCellValue(cellMap.get(i));
                                        if(fieldStr!=null){
                                            field.set(data,fieldStr);
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (IllegalAccessException ignored) {
                    }
                    boolean isTransform = true;
                    boolean isRequired = value.contains("*");
                    if (isRequired && StringUtils.isBlank(fieldStr)) {
                        isTransform = false;
                        data.updateError(REQUIRED_FIELDS_NOT_FILLED, annotationValues[0] + "未填写!");
                    }
                    String name = field.getName();
                    if (name.endsWith("Str") && isTransform && (isRequired || StringUtils.isNotBlank(fieldStr))) {
                        String str = name.substring(0, name.length() - 3);
                        for (Field field1 : fieldArray) {
                            if (field1.getName().equals(str)) {
                                switch (field1.getType().toString()){
                                    case INTEGER_CLASS_NAME:
                                        try {
                                            Integer fieldInt = Integer.parseInt(fieldStr);
                                            field1.setAccessible(true);
                                            field1.set(data, fieldInt);
                                        } catch (Exception e) {
                                            data.updateError(FORMAT_ERROR, annotationValues[0] + "格式错了");
                                        }
                                        break;
                                    case BIGDECIMAL_CLASS_NAME:
                                        try {
                                            BigDecimal bigDecimal = new BigDecimal(fieldStr);
                                            field1.setAccessible(true);
                                            field1.set(data, bigDecimal);
                                        } catch (Exception e) {
                                            data.updateError(FORMAT_ERROR, annotationValues[0] + "格式错了");
                                        }
                                        break;
                                    case DATE_CLASS_NAME:
                                        try {
                                            Date date = getDate(fieldStr);
                                            if (date != null) {
                                                field1.setAccessible(true);
                                                field1.set(data, date);
                                            } else {
                                                data.updateError(FORMAT_ERROR, annotationValues[0] + "时间格式有误(yyyy/mm/dd  yyyy-mm-dd  yyyymmdd)");
                                            }
                                        } catch (Exception e) {
                                            data.updateError(FORMAT_ERROR, annotationValues[0] + "时间格式有误(yyyy/mm/dd  yyyy-mm-dd  yyyymmdd)");
                                        }
                                        break;
                                    default:
                                }
                                break;
                            }
                        }
                    }
                }
            }
            importDataDto.getDataList().add(data);
            if (data.getErrorDescription().isEmpty()){
                importDataDto.setSuccessNum(importDataDto.getSuccessNum() + 1);
            }else {
                importDataDto.setFailNum(importDataDto.getFailNum() + 1);
                importDataDto.getErrorDesc().put (data.getRowNum(), JSONUtils.toJSONString(data.getErrorDescription()));
            }
        }
    }

	private Date getDate(String dateStr) {
		Date date = null;
		for (SimpleDateFormat simpleDateFormat : formatList) {
			try {
				date = simpleDateFormat.parse(dateStr);
				break;
			} catch (Exception e) {
			}
		}
		return date;
	}

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        oneHeadMap.putAll(headMap);
    }

    public String getCellValue(Cell cell){
        String cellValue=null;
        String string = JSON.toJSONString(cell);
        JSONObject jsonObject = JSON.parseObject(string);
        if(jsonObject!=null){
            String type = jsonObject.getString("type");
            switch (type){
                case "NUMBER":
                    cellValue=jsonObject.getString("numberValue");
                    String dataFormatString = jsonObject.getString("dataFormatString").replaceAll(";@","");
                    Integer dataFormat = Integer.parseInt(jsonObject.getString("dataFormat"));
                    if(dataFormatString!=null&&dataFormat!=0&&!"0.00_ ".equals(dataFormatString)){
                        Calendar calendar = new GregorianCalendar(1900, Calendar.JANUARY,-1);
                        Date d = calendar.getTime();
                        Date date= DateUtils.addDays(d,Integer.parseInt(cellValue));
                        switch (dataFormatString){
                            case "yyyy/m/d":
                                dataFormatString=DateUtil.DATE_FORMAT_YYYY_MM_DD;
                                break;
                        }
                        cellValue=DateUtil.formatDateToString(date,dataFormatString);
                    }
                    break;
                case "STRING":
                    cellValue=jsonObject.getString("stringValue");
                    break;
            }
        }
        return cellValue;
    }
}
