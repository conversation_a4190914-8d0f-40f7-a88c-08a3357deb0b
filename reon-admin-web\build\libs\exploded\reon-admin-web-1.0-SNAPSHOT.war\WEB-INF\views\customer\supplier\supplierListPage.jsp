<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../../common/taglibs.jsp" %>
<html>

<head>
    <meta charset="utf-8">
    <title>供应商管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <!-- import CSS -->
    <link rel="stylesheet" href="${ctx}/layui/vue/general.css?v=${publishVersion}" media="all">
    <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
    <!-- import JavaScript -->
    <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/icon.js?v=${publishVersion}"></script>

</head>

<body class="childrenBody">
<div id="app">
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="供应商/联系人:">
                <el-input class="width220" v-model="obj.queryParams.keyword" placeholder="请输入供应商名称或联系人"
                          clearable></el-input>
            </el-form-item>
            <el-form-item label="参保地省级:">
                <el-select class="width220" filterable v-model="obj.queryParams.provinceCode"
                           placeholder="请选择省份" @change="handleProvinceChange">
                    <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                               :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="参保地市级:">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode"
                           placeholder="请选择城市">
                    <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                               :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="客服负责人:">
                <el-input class="width220" v-model="obj.queryParams.commissioner" placeholder="请输入客服负责人"
                          clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                           @click="handleDelete">删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Setting" :disabled="obj.single"
                           @click="handleSupplierArea">社保供应商服务城市分配
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Check" :disabled="obj.multiple"
                           @click="handleEnable">启用
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Close" :disabled="obj.multiple" @click="handleDisable">禁用
                </el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip max-height="calc(100vh - 240px)" border
                  :data="obj.tableData" @selection-change="handleSelectionChange"
                  @row-dblclick="handleRowDoubleClick" row-key="id" style="width: 100%">
            <el-table-column type="selection" align="center" width="50"></el-table-column>
            <el-table-column label="供应商名称" align="center" prop="supplierName" width="200"></el-table-column>
            <el-table-column label="供应商类型" align="center" prop="supplierType" width="120">
                <template #default="scope">
                    {{ getSupplierTypeName(scope.row.supplierType) }}
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="supplierStatus" width="100">
                <template #default="scope">
                    {{ getSupplierStatusName(scope.row.supplierStatus) }}
                </template>
            </el-table-column>
            <el-table-column label="联系人" align="center" prop="contactor" width="100"></el-table-column>
            <el-table-column label="联系方式" align="center" prop="tel" width="120"></el-table-column>
            <el-table-column label="邮箱" align="center" prop="email" width="180"></el-table-column>
            <el-table-column label="地址" align="center" prop="addr" width="200"></el-table-column>
            <el-table-column label="注册时间" align="center" prop="createTime" width="150"></el-table-column>
            <el-table-column label="采购负责人" align="center" prop="purchaser" width="120">
                <template #default="scope">
                    {{ getLoginName(scope.row.purchaser) }}
                </template>
            </el-table-column>
            <el-table-column label="客服负责人" align="center" prop="commissioner" width="150">
                <template #default="scope">
                                <span v-if="scope.row.supplierType == 1" style="color: #409EFF; cursor: pointer;"
                                      @click="handleViewCommissioner(scope.row)">
                                    单击查看供应商客服负责人
                                </span>
                    <span v-else>{{ getLoginName(scope.row.commissioner) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" fixed="right">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                               size="small">查看
                    </el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                               size="small">修改
                    </el-button>
                    <el-button link type="warning" icon="Setting" @click="handleSupplierArea(scope.row)"
                               size="small">分配
                    </el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                               size="small">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination v-show="obj.total > 0" :current-page="obj.queryParams.page"
                       :page-size="obj.queryParams.limit" :page-sizes="[50, 100, 200]" :total="obj.total" background
                       layout="total, sizes, prev, pager, next, jumper" style="margin-top: 20px; text-align: right;"
                       @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>

        <!-- 新增/修改/查看弹窗 -->
        <el-dialog v-model="obj.dialogVisible" :title="obj.dialogTitle" width="60%"
                   :close-on-click-modal="false">
            <el-form :model="obj.formData" :rules="obj.formRules" ref="formRef" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="供应商名称" prop="supplierName">
                            <el-input v-model="obj.formData.supplierName" placeholder="请输入供应商名称"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商类型" prop="supplierType">
                            <el-select v-model="obj.formData.supplierType" placeholder="请选择供应商类型"
                                       :disabled="obj.isView" style="width: 100%">
                                <el-option v-for="item in obj.supplierTypeList" :key="item.code"
                                           :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="联系人" prop="contactor">
                            <el-input v-model="obj.formData.contactor" placeholder="请输入联系人"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="tel">
                            <el-input v-model="obj.formData.tel" placeholder="请输入联系方式"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="obj.formData.email" placeholder="请输入邮箱"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态" prop="supplierStatus">
                            <el-select v-model="obj.formData.supplierStatus" placeholder="请选择状态"
                                       :disabled="obj.isView" style="width: 100%">
                                <el-option v-for="item in obj.supplierStatusList" :key="item.code"
                                           :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="地址" prop="addr">
                            <el-input v-model="obj.formData.addr" placeholder="请输入地址"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="采购负责人" prop="purchaser">
                            <el-input v-model="obj.formData.purchaser" placeholder="请输入采购负责人"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客服负责人" prop="commissioner">
                            <el-input v-model="obj.formData.commissioner" placeholder="请输入客服负责人"
                                      :disabled="obj.isView"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                            <span class="dialog-footer">
                                <el-button @click="obj.dialogVisible = false">取消</el-button>
                                <el-button type="primary" @click="handleSave" v-if="!obj.isView">保存</el-button>
                            </span>
            </template>
        </el-dialog>

        <!-- 城市分配弹窗 -->
        <el-dialog v-model="obj.areaDialogVisible" title="社保供应商服务城市分配" width="70%"
                   :close-on-click-modal="false">
            <div style="margin-bottom: 20px;">
                <strong>供应商：</strong>{{ obj.currentSupplier.supplierName }}
            </div>

            <el-form :model="obj.areaQueryParams" inline label-width="auto">
                <el-form-item label="省份:">
                    <el-select v-model="obj.areaQueryParams.provinceCode" placeholder="请选择省份"
                               @change="handleAreaProvinceChange" style="width: 200px">
                        <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                                   :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="城市:">
                    <el-select v-model="obj.areaQueryParams.cityCode" placeholder="请选择城市"
                               style="width: 200px">
                        <el-option v-for="item in obj.areaCityList" :key="item.code" :label="item.name"
                                   :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleAreaQuery">查询</el-button>
                    <el-button type="success" @click="handleAreaAdd">添加</el-button>
                    <el-button type="danger" @click="handleAreaDelete">删除</el-button>
                </el-form-item>
            </el-form>

            <el-table :data="obj.areaTableData" @selection-change="handleAreaSelectionChange"
                      style="width: 100%">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="provinceName" label="省份" width="120"></el-table-column>
                <el-table-column prop="cityName" label="城市" width="120"></el-table-column>
                <el-table-column prop="createTime" label="分配时间" width="150"></el-table-column>
            </el-table>

            <template #footer>
                            <span class="dialog-footer">
                                <el-button @click="obj.areaDialogVisible = false">关闭</el-button>
                            </span>
            </template>
        </el-dialog>
    </div>
</div>

</body>
<%--suppress JSUnresolvedReference --%>
<script>
    const { createApp, reactive, ref, onMounted } = Vue
    const { ElMessage, ElMessageBox } = ElementPlus

    const app = createApp({
        setup() {
            const obj = reactive({
                multiple: true,//是否多选
                single: true,//是否单选
                loading: false,//加载中
                queryParams: {
                    page: 1,
                    limit: 50,
                    keyword: '',
                    provinceCode: '',
                    cityCode: '',
                    commissioner: '',
                    supplierType: 1 // 社保供应商
                },//查询表单
                total: 0,//总条数

                tableData: [],//表格数据
                ids: [],//选中的id

                provinceList: window.top['area'] || [],
                cityList: [],
                supplierTypeList: window.top['dictCachePool']['SUPPLIER_TYPE'] || [],
                supplierStatusList: window.top['dictCachePool']['SUPPLIER_STATUS'] || [],

                // 弹窗相关
                dialogVisible: false,
                dialogTitle: '',
                isView: false,
                formData: {
                    id: '',
                    supplierName: '',
                    supplierType: 1,
                    contactor: '',
                    tel: '',
                    email: '',
                    addr: '',
                    supplierStatus: 1,
                    purchaser: '',
                    commissioner: ''
                },
                formRules: {
                    supplierName: [
                        { required: true, message: '请输入供应商名称', trigger: 'blur' }
                    ],
                    supplierType: [
                        { required: true, message: '请选择供应商类型', trigger: 'change' }
                    ],
                    contactor: [
                        { required: true, message: '请输入联系人', trigger: 'blur' }
                    ],
                    tel: [
                        { required: true, message: '请输入联系方式', trigger: 'blur' }
                    ]
                },

                // 城市分配弹窗相关
                areaDialogVisible: false,
                currentSupplier: {},
                areaQueryParams: {
                    provinceCode: '',
                    cityCode: ''
                },
                areaCityList: [],
                areaTableData: [],
                areaSelectedIds: [],
            })

            /** 检查会话是否过期 */
            function checkSessionExpired(error) {
                if (error.response && error.response.status === 302) {
                    ElMessage.error('会话已过期，请重新登录');
                    setTimeout(() => {
                        window.location.href = ML.contextPath + '/login';
                    }, 2000);
                    return true;
                }
                return false;
            }

            /** 列表 */
            function getList() {
                obj.loading = true;

                const params = {
                    page: obj.queryParams.page,
                    limit: obj.queryParams.limit,
                    paramData:{
                        keyword: obj.queryParams.keyword,
                        provinceCode: obj.queryParams.provinceCode,
                        cityCode: obj.queryParams.cityCode,
                        supplierType: obj.queryParams.supplierType
                    },

                };

                params.paramData=JSON.stringify(params.paramData);

                axios.post(ML.contextPath + '/customer/supplier/getSupplierListPage', params, {
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                }).then((res) => {
                    if (res.data.code === 0) {
                        obj.tableData = res.data.data || [];
                        obj.total = res.data.count || 0;
                        obj.loading = false;
                    } else {
                        ElMessage.error(res.data.msg || '查询失败');
                        obj.loading = false;
                    }
                }).catch((error) => {
                    console.error('查询失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('查询失败，请检查网络连接');
                    }
                    obj.loading = false;
                });
            }

            /** 搜索按钮操作 */
            function handleQuery() {
                obj.queryParams.page = 1;
                getList();
            }

            /** 重置按钮操作 */
            function resetQuery() {
                obj.queryParams = {
                    page: 1,
                    limit: 50,
                    keyword: '',
                    provinceCode: '',
                    cityCode: '',
                    commissioner: '',
                    supplierType: 1
                }
                obj.cityList = [];
                handleQuery();
            }

            /** 多选框选中数据 */
            function handleSelectionChange(selection) {
                obj.ids = selection.map(item => item.id);
                obj.single = selection.length != 1;
                obj.multiple = !selection.length;
            }

            /** 省份变化处理 */
            function handleProvinceChange(provinceCode) {
                obj.queryParams.cityCode = '';
                obj.cityList = [];

                if (provinceCode) {
                    loadCityListByProvince(provinceCode);
                }
            }

            /** 根据省份加载城市列表 */
            function loadCityListByProvince(provinceCode) {
                const cities = window.top['area'].filter(item =>
                    item.code.startsWith(provinceCode.substring(0, 2)) &&
                    item.code.length === 6 &&
                    item.code !== provinceCode
                );
                obj.cityList = cities;
            }

            /** 新增按钮操作 */
            function handleAdd() {
                resetFormData();
                obj.dialogTitle = '新增供应商';
                obj.isView = false;
                obj.dialogVisible = true;
            }

            /** 重置表单数据 */
            function resetFormData() {
                obj.formData = {
                    id: '',
                    supplierName: '',
                    supplierType: 1,
                    contactor: '',
                    tel: '',
                    email: '',
                    addr: '',
                    supplierStatus: 1,
                    purchaser: '',
                    commissioner: ''
                };
            }

            /** 双击行查看 */
            function handleRowDoubleClick(row) {
                if (row.id != null) {
                    handleDetail(row);
                }
            }

            /** 查看详情 */
            function handleDetail(row) {
                if (row instanceof Event) {
                    row = null;
                }
                const data = row ? row : obj.tableData.find(item => item.id === obj.ids[0]);
                if (!data) {
                    ElMessage.warning('请选择一行');
                    return;
                }
                obj.formData = { ...data };
                obj.dialogTitle = '查看供应商';
                obj.isView = true;
                obj.dialogVisible = true;
            }

            /** 修改按钮操作 */
            function handleUpdate(row) {
                if (row instanceof Event) {
                    row = null;
                }
                const data = row ? row : obj.tableData.find(item => item.id === obj.ids[0]);
                if (!data) {
                    ElMessage.warning('请选择一行');
                    return;
                }
                obj.formData = { ...data };
                obj.dialogTitle = '修改供应商';
                obj.isView = false;
                obj.dialogVisible = true;
            }

            /** 删除操作 */
            function handleDelete(row) {
                let ids = [];
                if (row instanceof Event) {
                    if (obj.ids.length === 0) {
                        ElMessage.warning('请选择一行');
                        return;
                    }
                    ids = obj.ids;
                } else {
                    ids = [row.id];
                }

                ElMessageBox.confirm('确认要删除吗？', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post(ML.contextPath + '/customer/supplier/delete', {
                        ids: JSON.stringify(ids)
                    }).then((res) => {
                        ElMessage.success(res.data.msg || '删除成功');
                        if (res.data.code === 0) {
                            getList();
                        }
                    }).catch((error) => {
                        console.error('删除失败:', error);
                        if (!checkSessionExpired(error)) {
                            ElMessage.error('删除失败，请检查网络连接');
                        }
                    });
                });
            }

            /** 社保供应商服务城市分配 */
            function handleSupplierArea(row) {
                const data = row instanceof Event ? null : row;
                const supplier = data ? data : obj.tableData.find(item => item.id === obj.ids[0]);
                if (!supplier) {
                    ElMessage.warning('请选择一行');
                    return;
                }

                obj.currentSupplier = supplier;
                obj.areaQueryParams = {
                    provinceCode: '',
                    cityCode: ''
                };
                obj.areaCityList = [];
                obj.areaTableData = [];
                obj.areaDialogVisible = true;
                getAreaList();
            }

            /** 查看客服负责人 */
            function handleViewCommissioner(row) {
                if (row.supplierType == 1) {
                    handleSupplierArea(row);
                }
            }

            /** 获取城市分配列表 */
            function getAreaList() {
                if (!obj.currentSupplier.id) return;

                axios.post(ML.contextPath + '/customer/supplier/getSupplierAreaList', {
                    supplierId: obj.currentSupplier.id
                }).then((res) => {
                    if (res.data.code === 0) {
                        obj.areaTableData = res.data.data || [];
                    } else {
                        ElMessage.error(res.data.msg || '查询失败');
                    }
                }).catch((error) => {
                    console.error('查询失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('查询失败，请检查网络连接');
                    }
                });
            }

            /** 城市分配省份变化 */
            function handleAreaProvinceChange(provinceCode) {
                obj.areaQueryParams.cityCode = '';
                obj.areaCityList = [];

                if (provinceCode) {
                    const cities = obj.provinceList.filter(item =>
                        item.code.startsWith(provinceCode.substring(0, 2)) &&
                        item.code.length === 6 &&
                        item.code !== provinceCode
                    );
                    obj.areaCityList = cities;
                }
            }

            /** 城市分配查询 */
            function handleAreaQuery() {
                getAreaList();
            }

            /** 城市分配添加 */
            function handleAreaAdd() {
                if (!obj.areaQueryParams.cityCode) {
                    ElMessage.warning('请选择城市');
                    return;
                }

                axios.post(ML.contextPath + '/customer/supplier/addSupplierArea', {
                    supplierId: obj.currentSupplier.id,
                    cityCode: obj.areaQueryParams.cityCode
                }).then((res) => {
                    if (res.data.code === 0) {
                        ElMessage.success('添加成功');
                        getAreaList();
                    } else {
                        ElMessage.error(res.data.msg || '添加失败');
                    }
                }).catch((error) => {
                    console.error('添加失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('添加失败，请检查网络连接');
                    }
                });
            }

            /** 城市分配删除 */
            function handleAreaDelete() {
                if (obj.areaSelectedIds.length === 0) {
                    ElMessage.warning('请选择要删除的记录');
                    return;
                }

                ElMessageBox.confirm('确认要删除选中的城市分配吗？', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post(ML.contextPath + '/customer/supplier/deleteSupplierArea', {
                        ids: JSON.stringify(obj.areaSelectedIds)
                    }).then((res) => {
                        if (res.data.code === 0) {
                            ElMessage.success('删除成功');
                            getAreaList();
                        } else {
                            ElMessage.error(res.data.msg || '删除失败');
                        }
                    }).catch((error) => {
                        console.error('删除失败:', error);
                        if (!checkSessionExpired(error)) {
                            ElMessage.error('删除失败，请检查网络连接');
                        }
                    });
                });
            }

            /** 城市分配选择变化 */
            function handleAreaSelectionChange(selection) {
                obj.areaSelectedIds = selection.map(item => item.id);
            }

            /** 保存表单 */
            function handleSave() {
                const formRef = app._instance.refs.formRef;
                if (!formRef) return;

                formRef.validate((valid) => {
                    if (valid) {
                        const url = obj.formData.id ? '/customer/supplier/updateSupplier' : '/customer/supplier/saveSupplier';

                        axios.post(ML.contextPath + url, obj.formData).then((res) => {
                            if (res.data.code === 0) {
                                ElMessage.success(obj.formData.id ? '修改成功' : '新增成功');
                                obj.dialogVisible = false;
                                getList();
                            } else {
                                ElMessage.error(res.data.msg || '保存失败');
                            }
                        }).catch((error) => {
                            console.error('保存失败:', error);
                            if (!checkSessionExpired(error)) {
                                ElMessage.error('保存失败，请检查网络连接');
                            }
                        });
                    } else {
                        ElMessage.warning('请填写必填项');
                    }
                });
            }

            /** 启用操作 */
            function handleEnable() {
                if (obj.ids.length === 0) {
                    ElMessage.warning('请选择一行');
                    return;
                }

                updateStatus('begin', obj.ids);
            }

            /** 禁用操作 */
            function handleDisable() {
                if (obj.ids.length === 0) {
                    ElMessage.warning('请选择一行');
                    return;
                }

                updateStatus('disable', obj.ids);
            }

            /** 批量启用和禁用 */
            function updateStatus(optType, ids) {
                axios.post(ML.contextPath + '/customer/supplier/updateStatusNosupplier', {
                    ids: JSON.stringify(ids),
                    optType: optType
                }).then((res) => {
                    ElMessage.success(res.data.msg || '操作成功');
                    if (res.data.code === 0) {
                        getList();
                    }
                }).catch((error) => {
                    console.error('操作失败:', error);
                    if (!checkSessionExpired(error)) {
                        ElMessage.error('操作失败，请检查网络连接');
                    }
                });
            }

            /** 分页大小改变 */
            function handleSizeChange(val) {
                obj.queryParams.limit = val;
                obj.queryParams.page = 1;
                getList();
            }

            /** 当前页改变 */
            function handleCurrentChange(val) {
                obj.queryParams.page = val;
                getList();
            }

            /** 获取供应商类型名称 */
            function getSupplierTypeName(type) {
                const item = obj.supplierTypeList.find(item => item.code == type);
                return item ? item.name : type;
            }

            /** 获取供应商状态名称 */
            function getSupplierStatusName(status) {
                const item = obj.supplierStatusList.find(item => item.code == status);
                return item ? item.name : status;
            }

            /** 获取登录名 */
            function getLoginName(loginName) {
                // 这里需要根据实际情况实现用户名格式化
                return loginName || '';
            }

            /** 打开弹窗 */
            function openDialog(title, url, data) {
                // 使用layer弹窗，保持与原有系统一致
                if (typeof layer !== 'undefined') {
                    layer.open({
                        type: 2,
                        title: title,
                        area: ['80%', '90%'],
                        shade: 0,
                        maxmin: true,
                        offset: 'auto',
                        shade: [0.8, '#393D49'],
                        content: ML.contextPath + url,
                        success: function (layero, index) {
                            if (data) {
                                var body = layer.getChildFrame('body', index);
                                body.find("#supplierName").val(data.supplierName);
                                body.find("#purchaser").val(data.purchaser);
                                body.find("#supplierType").val(data.supplierType);
                                body.find("#supplierId").val(data.id);
                            }
                        },
                        end: function () {
                            getList();
                        }
                    });
                } else {
                    // 如果layer不可用，使用window.open
                    window.open(ML.contextPath + url, '_blank');
                }
            }

            // 加载图标
            if (typeof ElementPlusIconsVue !== 'undefined') {
                for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                    app.component(key, component)
                }
            } else {
                console.error('ElementPlusIconsVue 未定义，请检查图标文件是否正确加载')
                if (window.ElementPlusIconsVue) {
                    for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
                        app.component(key, component)
                    }
                }
            }

            onMounted(() => {
                getList()
            })

            return {
                obj,
                handleQuery,
                resetQuery,
                handleSelectionChange,
                handleProvinceChange,
                handleAdd,
                handleRowDoubleClick,
                handleDetail,
                handleUpdate,
                handleDelete,
                handleSupplierArea,
                handleViewCommissioner,
                handleEnable,
                handleDisable,
                handleSizeChange,
                handleCurrentChange,
                getSupplierTypeName,
                getSupplierStatusName,
                getLoginName,
                handleSave,
                handleAreaProvinceChange,
                handleAreaQuery,
                handleAreaAdd,
                handleAreaDelete,
                handleAreaSelectionChange
            }
        }
    })
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount('#app')
</script>

</html>