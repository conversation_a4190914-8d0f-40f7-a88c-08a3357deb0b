layui.use(['jquery', 'form', 'layer', 'element', 'table', 'laypage'], function () {
    var table = layui.table,
        layer = layui.layer,
        laypage = layui.laypage,
        form = layui.form,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var ctx = ML.contextPath;

    function reloadTable() {
        let param =  serialize("searchForm");
        param.limit = 50;
        param.page = 1;
        table.reload('insurancePracticeBankTable', {
            where: param,
            page: { curr: 1 } //重新从第 1 页开始
        });
    }
    $("#btnQuery").on('click',function () {
        let param =  serialize("searchForm");
        param.limit = 50;
        param.page = 1;
        table.reload('insurancePracticeBankTable', {
            where: param,
            page: { curr: 1 } //重新从第 1 页开始
        });
        return false;
    });
    // 渲染表格
    table.render({
        id: 'insurancePracticeBankTable',
        elem: '#insurancePracticeBankTable',
        url: ML.contextPath + '/base/insurancePracticeBank/getInsurancePracticePayBankConfigPage',
        page: true, //默认为不开启
        limits: [20, 50,100,200],
        toolbar: '#topbtn',
        height: 700,
        defaultToolbar: [],
        limit: 20,
        text: {
            none: '暂无数据' // 无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'}, // 使用百分比定义宽度
            {type: 'id', width: '0', hide: true}, // 隐藏列不占宽度
            {field: 'orgName', title: '公司名称', width: '10%', align: 'center', fixed: 'left'},
            {
                field: 'ratioFlag', title: '公积金是否多比例', width: '7%', align: 'center', fixed: 'left', templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.ratioFlag);
                }
            },
            {field: 'ratioPayFlag', title: '公积金是否需要分不同比例支付', width: '12%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.ratioPayFlag);
                }},
            {field: 'ratioReceiptFlag', title: '公积金不同比例收款账户是否一致', width: '12%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.ratioReceiptFlag);
                }},
            {field: 'socialPayFlag', title: '社保支付方式', width: '8%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("MODE_OF_PAYMENT", d.socialPayFlag);
                }},
            {field: 'socialPracticePayFlag', title: '社保汇补缴是否分开支付', width: '10%', align: 'center',  templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.socialPracticePayFlag);
                }},
            {field: 'socialPracticePayDetailFlag', title: '社保详细支付方式', width: '10%', align: 'center',  templet: function (d) {
                    return ML.dictFormatter("PRACTICE_PAY_DETAIL_TYPE", d.socialPracticePayDetailFlag);
                }},
            {field: 'providentPracticePayFlag', title: '公积金汇补缴是否分开支付', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.providentPracticePayFlag);
                }},
            {field: 'providentPracticePayDetailFlag', title: '公积金详细支付方式', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PRACTICE_PAY_DETAIL_TYPE", d.providentPracticePayDetailFlag);
                }},
            {field: 'dispatchBankNo', title: '派单方内部转账出款账户', width: '10%', align: 'center'},
            {field: 'dispatchBankName', title: '派单方内部转账出款账户名称', width: '10%', align: 'center'},
            {field: 'dispatchBankType', title: '派单方内部转账出款账户类型', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("BANK", d.dispatchBankType);
                }},
            {field: 'receivingBankNo', title: '接单方内部转账收款账户/社保付款账户', width: '15%', align: 'center'},
            {field: 'receivingBankName', title: '接单方内部转账收款账户名称/社保付款账户名称', width: '15%', align: 'center'},
            {field: 'receivingBankType', title: '接单方内部转账收款账户类型/社保付款账户类型', width: '15%', align: 'center', templet: function (d) {
                    return ML.dictFormatter("BANK", d.receivingBankType);
                }},
        ]],
        done: function () {
            ML.hideNoAuth();
            table.on('toolbar(insurancePracticeBankFilter)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                switch (obj.event){
                    case 'add' :
                        openView("新增","add","/base/insurancePracticeBank/gotoAddInsurancePracticePayBankConfigPage")
                        break;
                    case 'update' :
                        if (checkStatus.data.length !== 1){
                            return layer.msg("请选中一行");
                        }
                        openView("编辑","update", "/base/insurancePracticeBank/gotoUpdateInsurancePracticePayBankConfigPage?id=" + checkStatus.data[0].id);
                        break;
                    case 'check' :
                        if (checkStatus.data.length !== 1){
                            return layer.msg("请选中一行");
                        }
                        openView("查看","check", "/base/insurancePracticeBank/gotoUpdateInsurancePracticePayBankConfigPage?id=" + checkStatus.data[0].id);
                        break;

                }
            })
        }
    });






    function openView(title, type,url) {
        layer.open({
            type: 2, // 为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['80%', '90%'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + url,
            success:function(layero, index){
            var body = layer.getChildFrame('body', index);
                body.find("#optType").val(type)

            }
            ,end: function () {
                reloadTable()
            }
        });
    }


    $(document).ready(function () {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                $("#orgCode").append("<option value=''></option>");
                $.each(data.data, function (i, item) {
                    $("#orgCode").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
                });
                form.render('select');
            },
            error: function (data) {
                console.log("error")
            }
        });

    })


    //重载数据
    function reloadTable() {
        table.reload('insurancePracticeBankTable', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }






})