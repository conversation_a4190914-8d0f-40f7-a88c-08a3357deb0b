<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>修改增量标志和备注</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>

</head>
<body class="childrenBody" style="overflow: hidden">
<div class="layui-tab-content">
    <div class="layui-tab-item layui-show" style="margin-top: 5px">
        <form class="layui-form" method="post">
            <%--隐藏域--%>
            <input type="hidden" id="contractNo">
            <input type="hidden" id="contractType" >
            <input type="hidden" id="custId" >
            <input type="hidden" id="quoteNo" >

            <%--表单元素--%>
            <p>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="原默认报价单" style="width: 150px">&nbsp;原默认报价单：</label>
                <div class="layui-input-inline">
                    <input type="text"  name="oldQuotationNo" id="oldQuotationNo"  class="layui-input" autocomplete="off">
                </div>
            </div>
            </p>
            <p>
                <div class="layui-inline" style="padding-top: 20px">
                    <label class="layui-form-label layui-elip" title="原默认报价单" style="width: 150px">&nbsp;新默认报价单：</label>
                    <div class="layui-input-inline">
                        <input type="text"  name="newQuotationNo" id="newQuotationNo"  class="layui-input" autocomplete="off" lay-verify="required">
                    </div>
                </div>
            </p>



            <div style="margin-left:60% ">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="save" id="save" type="button">保存
                </button>
                <button class="layui-btn layui-btn-primary" type="button" id="cancel">取消</button>
            </div>
        </form>
    </div>


</div>


<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript"
        src="${ctx}/js/modules/customer/contract/changeQuotation.js?v=${publishVersion}"></script>
</body>
</html>
