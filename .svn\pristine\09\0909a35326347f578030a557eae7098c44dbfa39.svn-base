<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>社保公积金试算</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">


    <!-- import CSS -->
    <link rel="stylesheet" href="${ctx}/layui/element-plus/index.css?v=${publishVersion}" media="all">
    <!-- import JavaScript -->
    <script src="${ctx}/layui/vue/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/element-plus/zh-cn.js?v=${publishVersion}"></script>
    <script src="${ctx}/layui/axios/index.js?v=${publishVersion}"></script>
    <script src="${ctx}/js/axios.js?v=${publishVersion}"></script>

    <style>
        .query-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .city-display {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .batch-setting {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .batch-setting .el-aside {
            padding-right: 20px;
            border-right: 1px solid #e6e6e6;
        }

        .batch-setting .el-main {
            padding-left: 20px;
        }

        .data-table {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .width220 {
            width: 220px;
        }

        .width180 {
            width: 180px;
        }

        .mt20 {
            margin-top: 0;
        }

        .mb8 {
            margin-bottom: 8px;
        }

        .mb10 {
            margin-bottom: 10px;
        }

        .el-divider {
            margin: 15px 0;
        }

        .el-form-item {
            margin-bottom: 18px;
        }

        .el-card {
            border: 1px solid #e6e6e6;
        }

        .el-table {
            font-size: 13px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #e4e7ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .el-table th {
            background: linear-gradient(180deg, #f8f9fa 0%, #f1f3f4 100%);
            color: #303133;
            font-weight: 600;
            font-size: 13px;
            text-align: center;
            padding: 12px 8px;
            border-bottom: 2px solid #e4e7ed;
            white-space: nowrap;
        }

        .el-table td {
            border-bottom: 1px solid #f0f2f5;
            font-size: 13px;
            text-align: center;
            padding: 10px 8px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div id="app">
    <!-- 查询条件 -->
    <div class="query-form">
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="城市:" prop="typeCode">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable
                           filterable @change="handleCityChange">
                    <el-option v-for="item in cityList" :key="item.code" :label="item.name"
                               :value="item.code +'-'+ item.name"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="人员类型:" prop="typeCode" v-if="tags && tags.length<2">
                <el-select class="width220" v-model="obj.queryParams.personType" placeholder="请选择人员类型" clearable
                           @change="handlepersonTypeChange">
                    <el-option v-for="item in personType" :key="item.cityCode" :label="item.peopleType"
                               :value="item.categoryCode +'-'+ item.cityCode +'-'+ item.cityName"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </div>

    <!-- 城市显示 -->
    <div class="city-display">
        <el-card>
            <div>
                <strong>已选城市：</strong>
                <el-tag v-for="tag in tags" :key="tag.name" closable :type="tag.type"
                        @close="handleClose(tag)" style="margin-right: 8px;">
                    {{ tag.name }}
                </el-tag>
            </div>
        </el-card>
    </div>

    <!-- 批量设置 -->
    <div class="batch-setting">
        <el-divider content-position="left">批量设置</el-divider>
        <el-container>
            <el-aside width="400px" class="mt20">
                <el-form label-width="120px">
                    <el-form-item label="人数:">
                        <el-input class="width220" v-model="obj.queryParams.number" placeholder="请输入人数"></el-input>
                    </el-form-item>
                    <el-form-item label="社保缴费基数:">
                        <el-radio-group v-model="obj.queryParams.socialSecurityBase" size="default"
                                        @change="handleSocialSecurityChange">
                            <el-radio class="mb8" value="1" border>最低基数</el-radio>
                            <el-radio class="mb8" value="2" border>最高基数</el-radio>
                            <el-radio class="mb8" value="3" border>其他基数</el-radio>
                        </el-radio-group>
                        <el-input class="width180" v-model="obj.queryParams.socialSecurityAmount"
                                  :disabled="obj.disabled_socialSecurity" placeholder="请输入基数"></el-input>
                    </el-form-item>
                    <el-form-item label="公积金缴费基数:">
                        <el-radio-group v-model="obj.queryParams.providentFundBase" size="default"
                                        @change="handleProvidentFundChange">
                            <el-radio class="mb8" value="1" border>最低基数</el-radio>
                            <el-radio class="mb8" value="2" border>最高基数</el-radio>
                            <el-radio class="mb8" value="3" border>其他基数</el-radio>
                        </el-radio-group>
                        <el-input class="width180" v-model="obj.queryParams.providentFundAmount"
                                  :disabled="obj.disabled_providentFund" placeholder="请输入基数"></el-input>
                    </el-form-item>
                    <el-form-item label="到款日:">
                        <el-input class="width180" v-model="obj.queryParams.paymentDay"
                                  placeholder="请输入到款日"></el-input>
                    </el-form-item>
                </el-form>
            </el-aside>
            <el-main>
                <el-row :gutter="10" class="mb10">
                    <el-col :span="1.5">
                        <el-button type="primary" plain @click="handleResetCity">重置城市</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain @click="handleBatchSetting">批量设置</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain @click="handleBatchSettingSocialSecurity">批量设置社保基数为0
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain @click="handleBatchSettingProvidentFund">批量设置公积金基数为0
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain @click="handleCalculate">开始计算</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain @click="handleExportSingleCity" :disabled="tags.length>1">
                            单个城市导出
                        </el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.tableData" border stripe>
                    <el-table-column align="center" prop="index" width="60"></el-table-column>
                    <el-table-column label="城市" align="center" prop="city" width="200"></el-table-column>
                    <el-table-column label="公积金比例" align="center">
                        <template #default="scope">
                            <el-select v-model="scope.row.providentFoundProportion" placeholder="请选择公积金比例"
                                       @change="(value) => handleprovidentFoundProportionChange(value, scope.row)">
                                <el-option v-for="item in scope.row.providentFoundProportionList"
                                           :key="item.insuranceRatioCode" :label="item.ratioName"
                                           :value="item.cityCode +'-'+ item.insuranceRatioCode "></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="人数" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.number"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="社保缴费基数" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.socialSecurityBase"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="公积金缴费基数" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.providentFundBase"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </el-main>
        </el-container>
    </div>

    <!-- 数据表格 -->
    <div class="data-table" v-if="obj.fundRatioTable && obj.fundRatioTable.length>0">
        <el-divider content-position="left">基金比例试算表</el-divider>
        <el-table v-loading="obj.loading" :data="obj.fundRatioTable" border>
            <el-table-column label="产品名称" align="center" prop="productName"></el-table-column>
            <el-table-column label="单位基数" align="center" prop="unitBaseAA"></el-table-column>
            <el-table-column label="单位比例" align="center" prop="comRatioAA"></el-table-column>
            <el-table-column label="单位定值" align="center" prop="comAddAA"></el-table-column>
            <el-table-column label="个人基数" align="center" prop="personalBaseAA"></el-table-column>
            <el-table-column label="个人比例" align="center" prop="indRatioAA"></el-table-column>
            <el-table-column label="个人定值" align="center" prop="indlAddAA"></el-table-column>
            <el-table-column label="单位金额" align="center" prop="enterpriseSubtotalAA"></el-table-column>
            <el-table-column label="个人金额" align="center" prop="personalSubtotalAA"></el-table-column>
            <el-table-column label="单位+个人合计" align="center" prop="productSubtotalAA"></el-table-column>
            <el-table-column label="是否垫付" align="center" prop="isAdvances"></el-table-column>
            <el-table-column label="垫付金额" align="center" prop="advancesAmt"></el-table-column>
        </el-table>
    </div>
</div>
</body>
<script>
    const {createApp, reactive, ref, onMounted} = Vue
    const {ElMessage} = ElementPlus

    const app = createApp({
        setup() {
            // ==================== 响应式数据 ====================
            const personType = ref([]) // 人员类型
            const tags = ref([]) // 城市标签
            const obj = reactive({
                loading: false,
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    city: null,
                    personType: null,
                },
                tableData: [],//数据表格
                singleDataResult: null,//单个城市数据
                fundRatioTable: [],//基金比例试算表
                categoryCode: '',//类别编码
                paramsList: [],//参数列表
                tempFirstCity: null,//第一个城市
                disabled_socialSecurity: true,//社保基数禁用
                disabled_providentFund: true,//公积金基数禁用

                categoryCodeList: []
            })

            // ==================== 工具函数 ====================
            function getCodeName(type, val) {
                let arr = window.top['dictCachePool'][type];
                if (!arr) return "";
                for (let i in arr) {
                    let obj = arr[i];
                    if (val == obj.code) {
                        return obj.name;
                    }
                }
                return "";
            }

            function calculateBaseValues(responseData) {
                let highBaseInd = 0, highBaseCom = 0;
                let lowBaseCom = null, lowBaseInd = null;

                responseData.forEach((i) => {
                    if (i.highBaseInd) highBaseInd = Math.max(highBaseInd, i.highBaseInd);
                    if (i.highBaseCom) highBaseCom = Math.max(highBaseCom, i.highBaseCom);
                    if (i.lowBaseCom) {
                        lowBaseCom = lowBaseCom === null ? i.lowBaseCom : Math.min(lowBaseCom, i.lowBaseCom);
                    }
                    if (i.lowBaseInd) {
                        lowBaseInd = lowBaseInd === null ? i.lowBaseInd : Math.min(lowBaseInd, i.lowBaseInd);
                    }
                });

                return {
                    highBaseInd,
                    highBaseCom,
                    lowBaseCom: lowBaseCom || 0,
                    lowBaseInd: lowBaseInd || 0,
                };
            }

            // ==================== 城市管理 ====================
            /** 城市选择 */
            function handleCityChange(value) {
                if (!value) return false;

                const [cityCode, cityName] = value.split('-');

                // 检查城市是否已存在
                if (tags.value.some(item => item.name === cityName)) {
                    ElMessage.warning('城市已存在');
                    return;
                }

                if (tags.value.length === 0) {
                    // 第一次选择城市：获取人员类型，等待用户选择
                    handleFirstCitySelection(cityCode, cityName);
                } else {
                    // 后续选择城市：直接添加并获取相关数据
                    handleSubsequentCitySelection(cityCode, cityName);
                }
            }

            /** 处理第一次城市选择 */
            function handleFirstCitySelection(cityCode, cityName) {
                SocialSecurityAPI.getPersonTypeByCity(cityCode)
                    .then(res => {
                        const responseData = window.extractResponseData(res);
                        responseData.forEach(i => {
                            i.peopleType = i.cityName + '-' + getCodeName('PEOPLE_IND_TYPE', i.indTypeCode);
                        });
                        personType.value = responseData;

                        obj.tempFirstCity = {cityCode, cityName};
                        ElMessage.info('请选择人员类型后完成城市添加');
                    })
                    .catch(error => {
                        console.error('获取人员类型失败:', error);
                    });
            }

            /** 处理后续城市选择 */
            function handleSubsequentCitySelection(cityCode, cityName) {
                tags.value.push({name: cityName});
                obj.tableData.push({
                    index: tags.value.length,
                    cityCode,
                    city: cityName,
                });

                // 自动调用人员类型选择逻辑
                SocialSecurityAPI.getPersonTypeByCity(cityCode)
                    .then(res => {
                        const responseData = window.extractResponseData(res);
                        responseData.forEach(i => {
                            i.value = i.categoryCode + '-' + i.cityCode;
                            handlepersonTypeChange(i.value);
                        });
                    })
                    .catch(error => {
                        console.error('获取人员类型失败:', error);
                    });
            }

            /** 人员类型选择 */
            function handlepersonTypeChange(value) {
                if (!value) return false;

                const [categoryCode, cityCode] = value.split('-');
                obj.categoryCodeList.push(categoryCode)
                obj.categoryCode = categoryCode;

                SocialSecurityAPI.getRadioByCityCode({
                    cityCode,
                    ind: categoryCode
                }).then(res => {
                    const responseData = window.extractResponseData(res);
                    const baseValues = calculateBaseValues(responseData);

                    if (obj.tempFirstCity && tags.value.length === 0) {
                        // 第一次选择：添加城市到已选列表
                        handleFirstPersonTypeSelection(responseData, baseValues);
                    } else if (tags.value.length === 1) {
                        // 更新第一个城市的公积金比例信息
                        if (obj.tableData.length > 0) {
                            obj.tableData[0].providentFoundProportionList = responseData;
                        }
                    } else if (tags.value.length > 1) {
                        // 更新最后一个城市的数据
                        handleSubsequentPersonTypeSelection(responseData, baseValues);
                    }
                });
            }

            /** 处理第一次人员类型选择 */
            function handleFirstPersonTypeSelection(responseData, baseValues) {
                tags.value.push({
                    name: obj.tempFirstCity.cityName
                });

                obj.tableData.push({
                    index: 1,
                    cityCode: obj.tempFirstCity.cityCode,
                    city: obj.tempFirstCity.cityName,
                    providentFoundProportionList: responseData,
                    providentFoundProportion: '',
                    ...baseValues
                });

                obj.tempFirstCity = null;
                ElMessage.success('城市添加成功');
            }

            /** 处理后续人员类型选择 */
            function handleSubsequentPersonTypeSelection(responseData, baseValues) {
                const lastCityIndex = obj.tableData.length - 1;
                if (lastCityIndex >= 0) {
                    Object.assign(obj.tableData[lastCityIndex], {
                        providentFoundProportionList: responseData,
                        ...baseValues
                    });
                }
            }

            /**
             * 公积金比例选择处理
             * @param {string} value - 选择的值，格式为 "cityCode-insuranceRatioCode"
             * @param {Object} row - 当前行数据
             */
            function handleprovidentFoundProportionChange(value, row) {
                console.log('公积金比例选择:', value, row);

                if (!value || !row) {
                    return false;
                }

                const [cityCode, insuranceRatioCode] = value.split('-');

                // 查找是否已存在该城市的参数
                const existingIndex = obj.paramsList.findIndex(param => param.cityCode === cityCode);

                if (existingIndex !== -1) {
                    // 更新已存在的城市参数
                    obj.paramsList[existingIndex].insuranceRatioCode = insuranceRatioCode;
                } else {
                    // 添加新的城市参数
                    obj.paramsList.push({cityCode, insuranceRatioCode});
                }

                console.log('更新后的参数列表:', obj.paramsList);
            }

            /** 删除城市 */
            function handleClose(tag) {
                tags.value.splice(tags.value.indexOf(tag), 1);
                const index = obj.tableData.findIndex(item => item.city === tag.name);
                if (index !== -1) {
                    const removedCity = obj.tableData[index];
                    // 删除表格数据
                    obj.tableData.splice(index, 1);
                    obj.categoryCodeList.splice(index, 1);

                    // 同时删除对应的公积金比例参数
                    if (removedCity && removedCity.cityCode) {
                        const paramIndex = obj.paramsList.findIndex(param => param.cityCode === removedCity.cityCode);
                        if (paramIndex !== -1) {
                            obj.paramsList.splice(paramIndex, 1);
                            console.log('已删除城市对应的公积金比例参数:', removedCity.cityCode);
                        }
                    }
                }

                if (tags.value.length === 0) {
                    obj.queryParams = {};
                    obj.categoryCode = '';
                    // 清空所有参数
                    obj.paramsList = [];
                }
            }

            /** 重置按钮操作 */
            function handleResetCity() {
                obj.queryParams = {};
                tags.value = [];
                obj.tableData = [];
                obj.tempFirstCity = null;
                personType.value = [];
                // 清空公积金比例参数列表
                obj.paramsList = [];
                obj.categoryCodeList = [];
                ElMessage.success('重置成功');
            }

            // ==================== 计算相关 ====================
            /** 验证计算参数 */
            function validateCalculationParams() {
                const validations = [
                    {condition: !tags.value?.length, message: '请先选择城市'},
                    {condition: !obj.categoryCode, message: '请先选择人员类型'},
                    {condition: !obj.tableData?.length, message: '请先添加城市数据'},
                    {condition: !obj.paramsList?.length, message: '请先选择公积金比例'}
                ];

                for (const {condition, message} of validations) {
                    if (condition) {
                        ElMessage.warning(message);
                        return false;
                    }
                }
                return true;
            }

            /** 构建计算参数列表 */
            function buildCalculationList() {
                const list = [];

                // 初始化空字段
                obj.tableData.forEach((i) => {
                    if (!i.number) {
                        i.number = ''
                    }
                    if (!i.socialSecurityBase) {
                        i.socialSecurityBase = ''
                    }
                    if (!i.providentFundBase) {
                        i.providentFundBase = ''
                    }
                });

                // 检查每个城市是否都选择了公积金比例
                for (const tableItem of obj.tableData) {
                    const hasProvidentFundRatio = obj.paramsList.some(paramItem =>
                        paramItem.cityCode === tableItem.cityCode
                    );

                    if (!hasProvidentFundRatio) {
                        ElMessage.warning(tableItem.city + '还未选择公积金比例，请先选择公积金比例');
                        return null;
                    }
                }

                // 构建计算参数列表
                for (const tableItem of obj.tableData) {
                    for (const paramItem of obj.paramsList) {
                        if (tableItem.cityCode === paramItem.cityCode) {

                            // 验证必要字段
                            const fieldValidations = [
                                {field: tableItem.number, name: '人数'},
                                {field: tableItem.socialSecurityBase, name: '社保缴费基数'},
                                {field: tableItem.providentFundBase, name: '公积金缴费基数'}
                            ];

                            for (const {field, name} of fieldValidations) {
                                if (!field) {
                                    ElMessage.warning(tableItem.city + '的' + name + '不能为空');
                                    return null;
                                }
                            }

                            list.push({
                                cityCode: tableItem.cityCode,
                                insuranceRatioCode: paramItem.insuranceRatioCode,
                                number: tableItem.number,
                                socialSecurityBase: parseFloat(tableItem.socialSecurityBase),
                                providentFundBase: parseFloat(tableItem.providentFundBase),
                            });
                        }
                    }
                }

                if (!list.length) {
                    ElMessage.warning('计算参数列表不能为空，请确保已完成以下设置：\n1. 选择城市\n2. 选择人员类型\n3. 选择公积金比例\n4. 填写人数和缴费基数');
                    return null;
                }

                return list;
            }

            /** 计算请求 */
            async function calculationRequest(isExport = false) {
                if (!validateCalculationParams()) return -1;

                const list = buildCalculationList();

                if (!list) return -1;

                const params = {
                    categoryCode: obj.categoryCodeList[0] || '',
                    list,
                    paymentDay: obj.queryParams.paymentDay || '',
                };
                if (isExport) {
                    delete params.categoryCode;
                }

                try {
                    const res = await SocialSecurityAPI.calculate(params);
                    const responseData = window.extractResponseData(res);

                    if (responseData.code === 0) {
                        // ElMessage.success('计算成功');
                        return responseData.code;
                    } else {
                        ElMessage.error(responseData.msg || '计算失败');
                        return responseData.code;
                    }
                } catch (error) {
                    console.error('计算接口调用失败:', error);
                    ElMessage.error('计算接口调用失败');
                    return -1;
                }
            }

            /** 开始计算按钮操作 */
            async function handleCalculate() {
                const isSingleCity = tags.value?.length <= 1;
                const code = await calculationRequest(!isSingleCity);
                try {
                    if (code === 0) {
                        if (isSingleCity) {
                            obj.loading = true;
                            // 单城市：获取详细数据
                            const result = await SocialSecurityAPI.getSingleData();
                            obj.fundRatioTable = window.extractResponseData(result);
                            if (obj.fundRatioTable && obj.fundRatioTable.length > 0) {
                                obj.loading = false;
                            }
                        } else {
                            // 多城市：跳转导出页面
                            window.location.href = ML.contextPath + '/serviceSiteCfg/socialSecurityFundTC/exportFile';
                        }
                    }
                } catch (error) {
                    console.error('操作失败:', error);
                    ElMessage.error(isSingleCity ? '获取详细数据失败' : '导出数据失败');
                }
            }

            // ==================== 表单控制 ====================
            /** 社保缴费基数变化 */
            function handleSocialSecurityChange(value) {
                obj.disabled_socialSecurity = value !== '3';
            }

            /** 公积金缴费基数变化 */
            function handleProvidentFundChange(value) {
                obj.disabled_providentFund = value !== '3';
            }

            // ==================== 批量设置 ====================
            /** 获取基数值 */
            function getBaseValue(row, type, baseType) {
                const baseMap = {
                    social: {low: row.lowBaseInd, high: row.highBaseInd},
                    provident: {low: row.lowBaseCom, high: row.highBaseCom}
                };
                return baseMap[type][baseType];
            }

            /** 批量设置 */
            function handleBatchSetting() {
                if (!obj.tableData?.length) {
                    ElMessage.warning('请先选择城市');
                    return;
                }

                const {queryParams} = obj;
                const hasNumber = queryParams.number;
                const hasSocialSecurity = queryParams.socialSecurityBase;
                const hasProvidentFund = queryParams.providentFundBase;

                if (!hasNumber && !hasSocialSecurity && !hasProvidentFund) {
                    ElMessage.warning('请至少填写一个设置项（人数、社保缴费基数或公积金缴费基数）');
                    return;
                }

                const updateItems = [];

                obj.tableData.forEach(row => {
                    // 设置人数
                    if (hasNumber) {
                        row.number = queryParams.number;
                        if (!updateItems.includes('人数')) updateItems.push('人数');
                    }

                    // 设置社保缴费基数
                    if (hasSocialSecurity) {
                        let socialSecurityBaseValue = '';
                        if (queryParams.socialSecurityBase === '1') {
                            socialSecurityBaseValue = getBaseValue(row, 'social', 'low');
                        } else if (queryParams.socialSecurityBase === '2') {
                            socialSecurityBaseValue = getBaseValue(row, 'social', 'high');
                        } else if (queryParams.socialSecurityBase === '3') {
                            if (!queryParams.socialSecurityAmount) {
                                ElMessage.warning('请输入社保缴费基数');
                                return;
                            }
                            socialSecurityBaseValue = queryParams.socialSecurityAmount;
                        }
                        row.socialSecurityBase = socialSecurityBaseValue;
                        if (!updateItems.includes('社保缴费基数')) updateItems.push('社保缴费基数');
                    }

                    // 设置公积金缴费基数
                    if (hasProvidentFund) {
                        let providentFundBaseValue = '';
                        if (queryParams.providentFundBase === '1') {
                            providentFundBaseValue = getBaseValue(row, 'provident', 'low');
                        } else if (queryParams.providentFundBase === '2') {
                            providentFundBaseValue = getBaseValue(row, 'provident', 'high');
                        } else if (queryParams.providentFundBase === '3') {
                            if (!queryParams.providentFundAmount) {
                                ElMessage.warning('请输入公积金缴费基数');
                                return;
                            }
                            providentFundBaseValue = queryParams.providentFundAmount;
                        }
                        row.providentFundBase = providentFundBaseValue;
                        if (!updateItems.includes('公积金缴费基数')) updateItems.push('公积金缴费基数');
                    }
                });

                ElMessage.success('批量设置成功：' + updateItems.join('、'));
            }

            /** 批量设置社保基数为0 */
            function handleBatchSettingSocialSecurity() {
                if (!obj.tableData?.length) {
                    ElMessage.warning('请先选择城市');
                    return;
                }
                obj.tableData.forEach(row => row.socialSecurityBase = '0');
                ElMessage.success('批量设置社保基数为0成功');
            }

            /** 批量设置公积金基数为0 */
            function handleBatchSettingProvidentFund() {
                if (!obj.tableData?.length) {
                    ElMessage.warning('请先选择城市');
                    return;
                }
                obj.tableData.forEach(row => row.providentFundBase = '0');
                ElMessage.success('批量设置公积金基数为0成功');
            }

            // ==================== 导出功能 ====================
            /** 导出单城市数据 */
            async function handleExportSingleCity() {
                const code = await calculationRequest();
                if (code === 0) {
                    try {
                        window.location.href = ML.contextPath + '/serviceSiteCfg/socialSecurityFundTC/exportFile';
                    } catch (error) {
                        console.error('导出数据失败:', error);
                        ElMessage.error('导出数据失败');
                    }
                } else {
                    ElMessage.warning('请先完成计算后再导出');
                }
            }

            // ==================== 返回接口 ====================
            return {
                // 响应式数据
                tags,
                personType,
                obj,

                // 城市管理
                handleCityChange,
                handleClose,
                handleResetCity,

                // 表单控制
                handleSocialSecurityChange,
                handleProvidentFundChange,
                handlepersonTypeChange,
                handleprovidentFoundProportionChange,

                // 计算功能
                handleCalculate,

                // 批量设置
                handleBatchSetting,
                handleBatchSettingSocialSecurity,
                handleBatchSettingProvidentFund,

                // 导出功能
                handleExportSingleCity,

                // 外部数据
                cityList: window.top['area'],
            }
        }
    })
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount('#app')
</script>
</html>
