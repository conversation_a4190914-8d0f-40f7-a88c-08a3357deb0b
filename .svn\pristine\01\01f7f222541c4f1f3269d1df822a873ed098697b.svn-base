package com.reon.hr.sp.customer.dao.employee;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.customer.vo.employee.EmployeeContractOperationLogVo;
import com.reon.hr.sp.customer.entity.employee.EmployeeContractOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @datetime 2022/11/2 0002 17:27
 * @version: 1.0
 */
public interface EmployeeContractOperationLogMapper extends BaseMapper<EmployeeContractOperationLog> {

    int insertLog(EmployeeContractOperationLogVo employeeContractOperationLogVo);

    List<EmployeeContractOperationLogVo> getEmployeeContractOperationLogByEmpContractNo(String empContractNo);
    EmployeeContractOperationLogVo getEmployeeContractOperationLogByEmpContractNoAndOprType(@Param("empContractNo") String empContractNo,@Param("type") Integer type);
    List<EmployeeContractOperationLogVo> getEmployeeContractOperationLogByEmpContractNoList(@Param("list") List<String> empContractNoList);


}
