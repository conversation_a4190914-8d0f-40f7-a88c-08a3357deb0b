var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','tableDate'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        tableDate = layui.tableDate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    lay('#revStartMonth').each(function (i) {
        tableDate.render({
            dateElem: 'revStartMonth',
            row: i,
            object: this,
        });
    });

    lay('#billStartMonth').each(function (i) {
        tableDate.render({
            dateElem: 'billStartMonth',
            row: i,
            object: this,
        });
    });

    //查询商保订单减员确认管理数据
    table.render({
        id: 'commInsurOrderAttritionConfirmQueryGrid',
        elem: '#commInsurOrderAttritionConfirmQueryGrid',
        url: ML.contextPath + '/customer/commInsurOrder/getCommInsurOrderListPage',
        method: 'get',
        where : {
            "dealStatus":"5"
        },
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: '600',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: 'employeeNo', title: '唯一号', width: '15%', align: 'center', fixed: 'left'},
            {field: 'name', title: '姓名', width: '5%', align: 'center'},
            {field: 'certNo', title: '证件号码', width: '10%', align: 'center'},
            {field: 'dealStatus', title: '处理状态', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter('DEAL_STATUS', d.dealStatus);
                }},
            {field: 'custName', title: '客户名称', width: '10%', align: 'center'},
            {field: 'contractNo', title: '合同编号', width: '10%', align: 'center'},
            {field: 'solutionNo', title: '方案编号', width: '10%', align: 'center'},
            {field: 'prodType', title: '产品类型', width: '10%', align: 'center', templet: function (d) {
                    return ML.dictFormatter('PRODTYPE', d.prodType);
                }},
            {field: 'revStartMonth', title: '收费起始月', width: '10%', align: 'center'},
            {field: 'billStartMonth', title: '账单起始月', width: '8%', align: 'center'},
            {field: 'createTime', title: '创建时间', width: '8%', align: 'center'},
            {field: '', title: '操作', toolbar: '#btn', align: 'center', width: '10%', fixed: 'right'}
        ]],
        done: function (res) {
            ML.hideNoAuth();
            //监听商保订单减员确认表格操作里面的按钮
            table.on('tool(commInsurOrderAttritionConfirmQueryGridFilter)', function (obj) {
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                switch (layEvent) {
                    case 'check':
                        open("商保订单查看", 'check', ['80%', '100%'], data.orderNo);
                        break;
                }
            });
        }
    });

    //监听商保订单减员确认表格上方按钮
    table.on('toolbar(commInsurOrderAttritionConfirmQueryGridFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var orderNos = [];
        checkStatus.data.forEach(function (value) {
            orderNos.push(value.orderNo);
        });
        switch (obj.event) {
            //批量减员确认
            case 'batchAttrition':
                if (checkStatus.data.length == 0) {
                    return layer.msg("请至少选择一条数据！");
                }
                var param = {
                    "orderNos": JSON.stringify(orderNos),
                    "oprType": 7
                };
                ajax(param);
                break;
            //导出当前的减员数据
            case 'export':
                window.location.href = ctx + "/customer/commInsurOrder/export?type="+"batchAttrition";
                break;
            //驳回
            case 'rejected':
                if (checkStatus.data.length == 0) {
                    return layer.msg("请至少选择一条数据！");
                }
                var param = {
                    "orderNos": JSON.stringify(orderNos),
                    "oprType": 10
                };
                ajax(param);
                break;

        }
    });

    function ajax(param) {
        $.ajax({
            url: ML.contextPath + "/customer/commInsurOrder/saveOrCommit",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(param),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                layer.msg(result.msg);
                reloadTable();
            },
            error: function () {
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
    }

    //打开窗口
    function open(title, optType, area, data) {
        var url;
        if (optType == "check") {
            url = "/customer/commInsurOrder/gotoCommInsurOrderCheckView?orderNo="+data;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                if(optType == "check"){
                    body.find("input").attr("disabled","disabled");
                }
            }
        });
    }

    // 监听表格行双击事件
    table.on('rowDouble(commInsurOrderAttritionConfirmQueryGridFilter)', function (obj) {
        open("商保订单查看", "check", ['80%', '100%'], obj.data.orderNo);
        return false;
    });

    //重载数据
    function reloadTable() {
        table.reload('commInsurOrderAttritionConfirmQueryGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm")),
            }
        });
    }

    form.on('submit(btnQuery)', function (data) {
        table.reload('commInsurOrderAttritionConfirmQueryGrid', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    // 搜索条件  商保合同下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="合同编号" autocomplete="off" class="layui-input">';
    // 商保合同下拉数据表格
    tableSelect.render({
        elem: '#contractNo',
        checkedKey: 'contractNo',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/commInsurOrder/getContractTypeAll?optType=7',
            cols: [[
                {type: 'radio'}
                , {field: 'contractNo', title: '合同编号', align: 'center'}
                , {field: 'solutionNo', title: '方案编号', align: 'center'}
                , {field: 'contractName', title: '合同名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.contractNo);
            });
            // 回填值
            elem.val(NEWJSON.join(","));
        }
    });

    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            $("#custId").val(id);
        }
    });

    // 搜索条件  商保方案关联供应商信息下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="方案名称/编号" autocomplete="off" class="layui-input">';
    // 商保方案关联供应商信息下拉数据表格
    tableSelect.render({
        elem: '#solutionNo',
        checkedKey: 'solutionNo',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/commInsurOrder/findCustomerSolutionPageList',
            cols: [[
                {type: 'radio'}
                , {field: 'solutionNo', title: '方案编号', align: 'center'}
                , {field: 'solutionName', title: '方案名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.solutionNo);
            });
            // 回填值
            elem.val(NEWJSON.join(","));
        }
    });
});
