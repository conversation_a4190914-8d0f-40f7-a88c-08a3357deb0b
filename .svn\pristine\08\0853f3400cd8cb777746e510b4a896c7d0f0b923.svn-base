package com.reon.hr.sp.customer.entity.cus;

import lombok.Data;

import java.util.Date;

@Data
public class BillTempletFeeCfg {
    private Long id;

    private Long templetId;

    private String feeNo;

    private Integer beforeMonths;

    private Integer receiveMonthType;

    private Integer collectFreq;

    private String feeName;

    private Integer defaultFlag;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

}