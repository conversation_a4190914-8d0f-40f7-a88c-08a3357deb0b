/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/4/23
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.report.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.ehr.api.sys.utils.StringUtils;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.report.dubbo.service.rpc.IEmployeeReportWrapperService;
import com.reon.hr.api.report.utils.BillComparisonReportExcelUtil;
import com.reon.hr.api.report.utils.IncomeCountTableExcelUtil;
import com.reon.hr.api.report.vo.InsuranceBillComparisonDTO;
import com.reon.hr.api.report.vo.InsuranceBillComparisonReportVo;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import net.sf.json.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className InsuranceBillComparisonController
 *
 * @date 2021/4/23 15:53
 */
@RestController
@RequestMapping("/insuranceBillComparisonReport/")
public class InsuranceBillComparisonController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(InsuranceBillComparisonController.class);
	private static final String SETKEY = "MEMERY_DATE";

	@Autowired
	RedisTemplate redisTemplate;

	@Autowired
	IEmployeeReportWrapperService iEmployeeReportWrapperService;
	private static final Logger log = LoggerFactory.getLogger(InsuranceBillComparisonController.class);
	@Autowired
	private IContractResourceWrapperService contractResourceWrapperService;


	@RequestMapping("gotoInsuranceBillComparisonListPage")
	public ModelAndView gotoIncomeCountTableListPage() {
		return new ModelAndView("report/insuranceBillComparisonReportPage");
	}

	@RequestMapping(value = "getInsuranceBillListPage", method = RequestMethod.GET)
	public LayuiReplay<InsuranceBillComparisonDTO> getInsuranceBillComparisonListPage(@RequestParam("page") Integer page, @RequestParam("limit") Integer limit,
	                                                                                  @RequestParam("createTime") String createTime,
	                                                                                  @RequestParam(name = "billTypeList", required = false) String billTypeList,
	                                                                                  @RequestParam(name = "contractNo", required = false) String contractNo,
	                                                                                  @RequestParam(name = "templetId", required = false) Integer templetId,HttpSession session
	) {
		List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>) session.getAttribute(Constants.SESSION_ORG_POSITION);
		HashMap<String, Object> objectHashMap = new HashMap<>();
		List<String> contractNoList = new ArrayList<>();
		objectHashMap.put("createTime", createTime);
		if (StringUtil.isNotBlank(billTypeList)) {
			List<Integer> billType = JsonUtil.jsonToList(billTypeList, Integer.class);
			objectHashMap.put("billTypeList", billType);
		}
		objectHashMap.put("templetId",templetId);
		objectHashMap.put("userOrgPositionDtoList",userOrgPositionDtoList);
		if (StringUtils.isNotEmpty(contractNo)){
			contractNoList.add(contractNo);
		}
		Page<InsuranceBillComparisonDTO> insuranceBillComparisonListPage = new Page<>();
		List<String> contractNoByUserOrgPositionDtoList = contractResourceWrapperService.getContractNoByUserOrgPositionDtoList(contractNoList, userOrgPositionDtoList);
		if (CollectionUtils.isNotEmpty(contractNoByUserOrgPositionDtoList)){
			objectHashMap.put("contractNoList",contractNoByUserOrgPositionDtoList);
			insuranceBillComparisonListPage= iEmployeeReportWrapperService.getInsuranceBillComparisonListPage(page, limit, objectHashMap);
		}
		return new LayuiReplay<InsuranceBillComparisonDTO>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), insuranceBillComparisonListPage.getTotal(), insuranceBillComparisonListPage.getRecords());
	}


	@RequestMapping(value = "generationOfContrastData", method = RequestMethod.GET)
	@RepeatSubmit
	public Object generationOfContrastData(@RequestParam("createTime") String createTime) {

		String loginName = getSessionUser().getLoginName();
			Object size = iEmployeeReportWrapperService.generationOfContrastData(createTime, loginName);
			return new LayuiReplay<InsuranceBillComparisonDTO>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());

	}

	@RequestMapping(value = "exportFile", method = RequestMethod.GET)
	public void exportFile(HttpServletResponse response, String vot, HttpSession session) throws ParseException {
		List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>) session.getAttribute(Constants.SESSION_ORG_POSITION);
		JSONObject json = JSONObject.fromObject(vot);
		InsuranceBillComparisonReportVo vo = JSON.parseObject(vot, InsuranceBillComparisonReportVo.class);
		/**为了不执行分页*/
		String loginUser = getSessionUser().getUserName();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
		Date parse = simpleDateFormat.parse(json.getString("reportDate"));
		vo.setReportDate(parse);
		Date date = new Date();
		vo.setUserOrgPositionDtoList(userOrgPositionDtoList);
		List<InsuranceBillComparisonReportVo> costReportPage = iEmployeeReportWrapperService.getInsuranceBillComparisonListPageFromReport(vo);
		String name = loginUser + "_" + simpleDateFormat.format(date.getTime()) + "_" + "账单对比报表";
		SXSSFWorkbook sxssfWorkbook;
		try {
			sxssfWorkbook = BillComparisonReportExcelUtil.getDetailWorkBook(costReportPage, name);
			IncomeCountTableExcelUtil.closeInfo(response, sxssfWorkbook, name);
		} catch (Exception e) {
			e.printStackTrace();
			getLog("打印错误" + e.getMessage());
		}
	}

	/**
	 * 日志方法
	 */
	private void getLog(String LogData) {
		String loginName = getSessionUser().getLoginName();
		log.info(loginName + " -- " + LogData);
	}
}
