var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect', 'tableDate'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        tableDate = layui.tableDate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;
    // 页面初始化函数
    $(document).ready(function () {
        $("#businessType").val($("#strBusinessType").val());
        $("#validity").val($("#strValidity").val());
        $("#competitionFlag").val($("#competitionFlagStr").val());
        if ($("#bankName").val() != 8) {
            $("#otherBank").attr("disabled", "disabled");
        }else {
            $("#otherBank").removeAttr("disabled","disabled");
        }
        form.render('select');
    });

    //表单保存
    form.on("submit(commit)", function (data) {
        saveForm(data);
        return false;
    });

    form.on("select(bankNameFilter)", function (data) {
        if(data.value == 8){
            $("#otherBank").removeAttr("disabled", "disabled");
            var bankCardAttributionValue = bankCardAttribution(trim($("#cardNo").val()));
            $("#otherBank").val(bankCardAttributionValue.bankName);
        }else {
            $("#otherBank").val("");
            $("#otherBank").attr("disabled","disabled");
        }
    });

    //银行卡号输入事件
    $("#cardNo").blur(function () {
        var bankCardAttributionValue = bankCardAttribution(trim($("#cardNo").val()));
        if($("#bankName").val() == 8){
            $("#otherBank").val(bankCardAttributionValue.bankName);
        }
    });

    //删除左右两端的空格
    function trim(str) {
        return str.replace(/(^\s*)|(\s*$)/g, "");
    }

    // 发送保存请求，type为保存类型
    function saveForm(data) {
        if ($("#mobile").val() != '') {
            var phone = /^(0|86|17951)?(11[0-9]|12[0-9]|13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])[0-9]{8}$/;
            if (!(phone.test(data.field['mobile']))) {
                layer.msg("手机号格式不正确");
                return false;
            }
        }
        data.field['cardNo'] = trim(data.field.cardNo);
        $.ajax({
            url: ML.contextPath + "/customer/salary/employeeBankCard/save",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data.field),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                if (result.code != "-1") {
                    layer.closeAll('iframe');
                }
                layer.msg(result.msg);
            },
            error: function () {
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
    }

//表单关闭
    $(document).on("click", "#close", function () {
        layer.closeAll('iframe');
    });
});
