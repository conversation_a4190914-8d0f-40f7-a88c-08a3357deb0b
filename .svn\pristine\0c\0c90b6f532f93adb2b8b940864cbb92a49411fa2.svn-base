package com.reon.hr.sp.bill.service.impl.insurancePractice;

import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.bill.enums.OneFeeApproveStatusEnum;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.insurancePractice.InsurancePracticeDifferenceImportVo;
import com.reon.hr.api.bill.vo.insurancePractice.InsurancePracticeDifferenceProductImportVo;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.batchImport.ImportDataVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.common.utils.SensitiveReplaceUtil;
import com.reon.hr.sp.bill.dao.bill.*;
import com.reon.hr.sp.bill.service.bill.InsurancePracticeDisComPayService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IPracticePayDetailService;
import com.reon.hr.sp.bill.service.bill.insurancePractice.InsurancePracticeOneFeeService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年06月24日
 * @Version 1.0
 */
@Slf4j
@Service
public class InsurancePracticeOneFeeServiceImpl implements InsurancePracticeOneFeeService {
    private static final String DATA_ERROR = "数据错误";
    private static final String DATA_NOT_EXIST = "填写的{0}数据在系统中不存在";
    private static final String PRODUCT_NOT_EXIST_BY_ORDER = "根据此订单号未在此支付申请中查询到对应产品";
    private static final String PRODUCT_NAME_ERROR = "请检查填写的产品名称是否正确";
    private static final String NOT_UPDATE_PRODUCT_ERROR = "不允许修改模板的产品数量和名称";
    private static final String ORG_CODE_ERROR = "填写的福利办理方不是此支付申请的福利办理方";
    private static final String DIS_CODE_ERROR = "填写的签约方抬头不是此支付申请的签约方抬头";
    private static final String ORDER_REPEAT = "填写的订单号{0}在导入数据中重复出现多次";
    private static final String NOT_IN_PAY_APPLY = "填写的订单号{0}未在此支付申请中";
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMM");

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Resource
    private ICustomerWrapperService customerWrapperService;

    @Resource
    private IPracticePayDetailService payDetailService;

    @Resource
    private IPaymentApplyService paymentApplyService;

    @Resource
    private InsurancePracticeDisComPayService insurancePracticeDisComPayService;

    @Resource
    private IBatchImportDataWrapperService batchImportDataService;

    @Resource
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;

    @Resource
    private IUserWrapperService userWrapperService;

    @Resource
    private InsurancePracticeOneFeeMapper insurancePracticeOneFeeMapper;

    @Resource
    private InsurancePracticeOneFeeDetailMapper insurancePracticeOneFeeDetailMapper;
    @Resource
    private IBatchImportDataWrapperService iBatchImportDataService;

    @Resource
    private InsurancePracticeOneFeeBalanceMapper insurancePracticeOneFeeBalanceMapper;

    @Resource
    private InsurancePracticeOneBalanceMapper insurancePracticeOneBalanceMapper;

    @Resource
    private InsurancePracticeOneFeeChargeRecordMapper insurancePracticeOneFeeChargeRecordMapper;

    @Resource
    private IEmployeeOrderWrapperService employeeOrderWrapperService;

    @Resource
    private InsurancePracticeCustPayDetailMapper insurancePracticeCustPayDetailMapper;




    @Override
    public List<InsurancePracticeOneFeeBalanceVo> getDeductionAmtByPayComAndDisCom(String payCom, String disCom) {
        return insurancePracticeOneFeeBalanceMapper.getDeductionAmtByPayComAndDisCom(payCom, disCom);
    }

    @Override
    public void updateInsurancePracticeOneFeeBalanceAndRecordVo(DeductionBalanceCacheVo vo) {
        List<InsurancePracticeOneFeeBalanceVo> balanceUpdates = vo.getBalanceUpdates();
        List<InsurancePracticeOneFeeChargeRecordVo> chargeRecords = vo.getChargeRecords();
        insurancePracticeOneFeeBalanceMapper.batchUpdateInsurancePracticeOneFeeBalance(balanceUpdates);
        insurancePracticeOneFeeChargeRecordMapper.batchInsertInsurancePracticeOneFeeChargeRecord(chargeRecords);
    }

    @Override
    public void batchInsertPayCustDetailVos(List<InsurancePracticeCustPayDetailVo> vos) {
        insurancePracticeCustPayDetailMapper.batchInsertInsurancePracticeCustPayDetail(vos);
    }

    @Override
    public List<InsurancePracticeOneFeeBalanceVo> getDeductionAmtSumByCustIdSetAndPayId(String payCom, String disCom, Set<Long> custIdSet, Long payId) {
        return insurancePracticeOneFeeBalanceMapper.getDeductionAmtSumByCustIdSetAndPayId(payCom, disCom, custIdSet, payId);
    }

    @Override
    public List<InsurancePracticeOneFeeVo> getInsurancePracticeOneFeeListByPayIdSet(Set<Long> payIdSet) {
        return insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeListByPayIdSet(payIdSet);
    }

    @Override
    public List<InsurancePracticeOneFeeVo> getInsurancePracticeOneFeeDisComListByPayIdSet(Set<Long> payIdSet) {
        return insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeDisComListByPayIdSet(payIdSet);
    }

    @Override
    public void rollbackBalanceByPaymentId(Long paymentId,String loginName) {
        if (paymentId==null){
            return;
        }
        List<InsurancePracticeOneFeeChargeRecordVo> recordList = insurancePracticeOneFeeChargeRecordMapper.getInsurancePracticeOneFeeChargeRecordListByPayId(paymentId);
        if (CollectionUtils.isEmpty(recordList)){
            return;
        }
        for (InsurancePracticeOneFeeChargeRecordVo record : recordList) {
            //记录表中不删除数生成一条回退记录
            //扣款金额 回退时相反金额
            BigDecimal rollbackAmt = record.getChargeAmt().negate();
            Long balanceId = record.getBalanceId();

            // 生成回退记录
            InsurancePracticeOneFeeChargeRecordVo rollbackRecord = new InsurancePracticeOneFeeChargeRecordVo();
            rollbackRecord.setBalanceId(balanceId);
            rollbackRecord.setPaymentId(paymentId);
            rollbackRecord.setCustId(record.getCustId());
            rollbackRecord.setChargeAmt(rollbackAmt);
            rollbackRecord.setChargeStatus(2);
            rollbackRecord.setCreator(loginName);
            rollbackRecord.setCreateTime(new Date());
            insurancePracticeOneFeeChargeRecordMapper.insertInsurancePracticeOneFeeChargeRecord(rollbackRecord);

            // 更新余额表
            BigDecimal remainAmt = record.getChargeAmt();
            insurancePracticeOneFeeBalanceMapper.updateRollbackBalance(balanceId, rollbackAmt,remainAmt, loginName);
        }
    }

    @Override
    public List<OneFeeDiffDataExportVo> printBalanceDiff(Long payId,Integer type) {
        ArrayList<OneFeeDiffDataExportVo> oneFeeDiffDataExportVos = new ArrayList<>();
        String disCom = null;
        if (type.equals(BooleanTypeEnum.YES.getCode())){
            InsurancePracticeDisComPayVo insurancePracticeDisComPayById = insurancePracticeDisComPayService.getInsurancePracticeDisComPayById(payId);
            disCom = insurancePracticeDisComPayById.getDisCom();
            payId = insurancePracticeDisComPayById.getPayId();
        }
        List<InsurancePracticeOneFeeVo> insurancePracticeOneFeeVos = insurancePracticeOneFeeMapper.printBalanceDiff(payId,disCom);
        if (CollectionUtils.isEmpty(insurancePracticeOneFeeVos)){
            return oneFeeDiffDataExportVos;
        }
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> map = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        List<CustomerVo> allCustomerName = customerWrapperService.findAllCustomerName();
        Map<Long, String> custMap = allCustomerName.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));

        List<String> orderNoList = insurancePracticeOneFeeVos.stream().distinct().map(InsurancePracticeOneFeeVo::getOrderNo).collect(Collectors.toList());
        List<EmployeeOrderVo> contractTypeAndDisComByOrderNoList = employeeOrderWrapperService.getContractTypeAndDisComByOrderNoList(orderNoList);
        Map<String, Integer> orderNoAndContractTypeMap = contractTypeAndDisComByOrderNoList.stream().collect(Collectors.toMap(EmployeeOrderVo::getOrderNo, EmployeeOrderVo::getContractType));
        Map<String, List<InsurancePracticeOneFeeVo>> disComMap = insurancePracticeOneFeeVos.stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getDisCom));
        for (String disComCode : disComMap.keySet()) {
            String disComName = map.get(disComCode);
            String orgName = map.get(insurancePracticeOneFeeVos.get(0).getOrgCode());
            if (type.equals(BooleanTypeEnum.NO.getCode())){
                BigDecimal amt = disComMap.get(disComCode).stream().map(InsurancePracticeOneFeeVo::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                addOneFeeDiffDataExportVo(oneFeeDiffDataExportVos, orgName, disComName,null,null,amt,null);
                continue;
            }
            List<InsurancePracticeOneFeeVo> custIdList = disComMap.get(disComCode);
            Map<Long, List<InsurancePracticeOneFeeVo>> custIdMap = custIdList.stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getCustId));
            for (Long custId : custIdMap.keySet()) {
                List<InsurancePracticeOneFeeVo> vos = custIdMap.get(custId);
                Integer contractType = orderNoAndContractTypeMap.get(vos.get(0).getOrderNo());
                String contractTypeName = EnumsUtil.getNameByCode(contractType, ContractType.class);

                String custName = custMap.get(custId);

                //公积金
                List<InsurancePracticeOneFeeVo> providentList = vos.stream().filter(c -> c.getProductCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex()) ||
                        c.getProductCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())).collect(Collectors.toList());
                //社保
                List<InsurancePracticeOneFeeVo> socialList = vos.stream().filter(c -> !c.getProductCode().equals(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex()) &&
                        !c.getProductCode().equals(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(providentList)){
                    BigDecimal providentAmt = providentList.stream().map(InsurancePracticeOneFeeVo::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    addOneFeeDiffDataExportVo(oneFeeDiffDataExportVos, orgName, disComName,  SensitiveReplaceUtil.replace(contractTypeName, false),custName, providentAmt, "公积金");

                }
                if (CollectionUtils.isNotEmpty(socialList)){
                    BigDecimal socialAmt = socialList.stream().map(InsurancePracticeOneFeeVo::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    addOneFeeDiffDataExportVo(oneFeeDiffDataExportVos, orgName, disComName, SensitiveReplaceUtil.replace(contractTypeName, false),custName,  socialAmt, "社保");
                }

            }
        }
        return oneFeeDiffDataExportVos;
    }

    @Override
    public List<InsurancePracticeOneFeeVo> getApproveDifferencesPage(InsurancePracticeOneFeeVo vo) {
        List<InsurancePracticeOneFeeVo> approveDifferencesPage = insurancePracticeOneFeeMapper.getApproveDifferencesPage(vo);
        if ("liuwei".equals(vo.getCreator())||"huiruifeng".equals(vo.getCreator())){
            approveDifferencesPage = approveDifferencesPage.stream().filter(c -> c.getSecondApproveStatus().equals(OneFeeApproveStatusEnum.APPROVING.getCode())).collect(Collectors.toList());
        }else {
            approveDifferencesPage = approveDifferencesPage.stream().filter(c -> c.getFirstApproveStatus().equals(OneFeeApproveStatusEnum.APPROVING.getCode())).collect(Collectors.toList());
            approveDifferencesPage = approveDifferencesPage.stream().filter(c -> vo.getOrgAndPosCodeList().contains(c.getFirstApprove())).collect(Collectors.toList());
        }
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> map = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        approveDifferencesPage.forEach(c -> {
            c.setOrgCode(map.get(c.getOrgCode()));
            ImportDataVo importDataVo = iBatchImportDataService.getById(c.getImportNo());
            c.setFileId(importDataVo.getFileId());
            c.setCreator(importDataVo.getOprMan());
        });
        return approveDifferencesPage;
    }

    @Override
    public void approveDifferences(InsurancePracticeOneFeeVo vo) {
        Integer status = vo.getApproveStatus();
        String operator = vo.getCreator();
        Date now = new Date();

        for (Long id : vo.getIds()) {
            InsurancePracticeOneFeeVo updateVo = new InsurancePracticeOneFeeVo();
            updateVo.setId(id);
            updateVo.setUpdater(operator);

            InsurancePracticeOneFeeVo current = insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeById(id);

            if (OneFeeApproveStatusEnum.APPROVED.getCode().equals(status)) {
                handleApprove(updateVo, current, operator, now, id);
            } else if (OneFeeApproveStatusEnum.REJECTED.getCode().equals(status)) {
                handleReject(updateVo, current, operator, now);
            }

            insurancePracticeOneFeeMapper.updateInsurancePracticeOneFee(updateVo);
        }

    }


    @Override
    public List<InsurancePracticeOneFeeVo> getAllApproveDifferencesPage(InsurancePracticeOneFeeDetailVo vo) {
        ArrayList<InsurancePracticeOneFeeVo> insurancePracticeOneFeeVos = new ArrayList<>();
        List<InsurancePracticeOneFeeVo> allApproveDifferencesPage = insurancePracticeOneFeeMapper.getAllApproveDifferencesPage(vo);
        Map<String, String> allUserMap = userWrapperService.getAllUserMap();

        if (CollectionUtils.isNotEmpty(allApproveDifferencesPage)){

            List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
            Map<String, String> orgMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
            List<CustomerVo> allCustomerName = customerWrapperService.findAllCustomerName();
            Map<Long, String> custMap = allCustomerName.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));

            Map<Integer, List<InsurancePracticeOneFeeVo>> approveStatusMap = allApproveDifferencesPage.stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getApproveStatus));
            for (Integer status : approveStatusMap.keySet()) {
                Map<String, List<InsurancePracticeOneFeeVo>> orgCodeMap = approveStatusMap.get(status).stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getOrgCode));
                for (String orgCode : orgCodeMap.keySet()) {
                    Map<String, List<InsurancePracticeOneFeeVo>> disComMap = orgCodeMap.get(orgCode).stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getDisCom));
                    for (String disCom : disComMap.keySet()) {
                        Map<Long, List<InsurancePracticeOneFeeVo>> custIdMap = disComMap.get(disCom).stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeVo::getCustId));
                        for (Long custId : custIdMap.keySet()) {
                            List<InsurancePracticeOneFeeVo> custVos = custIdMap.get(custId);
                            InsurancePracticeOneFeeVo voCust = custVos.get(0);
                            InsurancePracticeOneFeeVo insurancePracticeOneFeeVo = new InsurancePracticeOneFeeVo();
                            insurancePracticeOneFeeVo.setDisCom(orgMap.get(disCom));
                            insurancePracticeOneFeeVo.setCustName(custMap.get(custId));
                            BigDecimal reduce = custVos.stream().map(InsurancePracticeOneFeeVo::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                            insurancePracticeOneFeeVo.setAmt(reduce);
                            insurancePracticeOneFeeVo.setApproveStatus(status);
                            insurancePracticeOneFeeVo.setOrgCode(orgMap.get(orgCode));
                            insurancePracticeOneFeeVo.setCustId(custId);
                            insurancePracticeOneFeeVo.setDisComCode(disCom);
                            insurancePracticeOneFeeVo.setPayCom(orgCode);
                            if (status.equals(OneFeeApproveStatusEnum.APPROVED.getCode())||status.equals(OneFeeApproveStatusEnum.REJECTED.getCode())){
                                insurancePracticeOneFeeVo.setFirstApprove(allUserMap.get(voCust.getFirstApprove()));
                                insurancePracticeOneFeeVo.setSecondApprove(allUserMap.get(voCust.getSecondApprove()));
                            }else {
                                if (voCust.getFirstApprove().contains(",")){
                                    String[] split = voCust.getFirstApprove().split(",");
                                    String areaName = userWrapperService.findAreaLoginNameByPosCodeAndOrgCode(split[0],split[1]);
                                    insurancePracticeOneFeeVo.setFirstApprove(allUserMap.get(areaName));
                                }else {
                                    insurancePracticeOneFeeVo.setFirstApprove(allUserMap.get(voCust.getFirstApprove()));
                                }
                                insurancePracticeOneFeeVo.setSecondApprove(allUserMap.get("liuwei")+"/"+allUserMap.get("huiruifeng"));
                            }

                            insurancePracticeOneFeeVo.setFirstApproveStatus(voCust.getFirstApproveStatus());

                            insurancePracticeOneFeeVo.setSecondApproveStatus(voCust.getSecondApproveStatus());

                            insurancePracticeOneFeeVos.add(insurancePracticeOneFeeVo);
                        }
                    }
                }

            }

        }
        insurancePracticeOneFeeVos.forEach(c -> {
            if (c.getApproveStatus().equals(OneFeeApproveStatusEnum.APPROVED.getCode())){
                InsurancePracticeOneFeeBalanceVo balanceVoByCustIdAndOrgCodeAndDisCom = insurancePracticeOneFeeBalanceMapper.getBalanceVoByCustIdAndOrgCodeAndDisCom(c.getCustId(), c.getPayCom(), c.getDisComCode());
                if (balanceVoByCustIdAndOrgCodeAndDisCom != null){
                    c.setUsedAmt(balanceVoByCustIdAndOrgCodeAndDisCom.getUsedAmt());
                    c.setRemainAmt(balanceVoByCustIdAndOrgCodeAndDisCom.getRemainAmt());
                }
            }

        });
        return insurancePracticeOneFeeVos;
    }

    @Override
    public Page<ImportDataVo> getImportDifferencesPage(Long paymentId,String loginName,Integer pageNum,Integer pageSize) {
        Page<ImportDataVo> importKeyCustomerDataListPage = iBatchImportDataService.getImportKeyBanlanceDataListPage(loginName, ImportDataType.INSURANCE_PRACTICE_DIFFERENCE_IMPORT.getCode(), pageNum, pageSize);
         List<InsurancePracticeOneFeeVo> insurancePracticeOneFees = insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeByPaymentId(paymentId);
        importKeyCustomerDataListPage.getRecords().forEach(c -> {
            insurancePracticeOneFees.forEach(insurancePracticeOneFee -> {
                if (insurancePracticeOneFee.getImportNo().equals(c.getImportNo())){
                    c.setApproveStatus(insurancePracticeOneFee.getApproveStatus());
                    c.setId(insurancePracticeOneFee.getId());
                }
            });
        });

        return importKeyCustomerDataListPage;
    }

    @Override
    public void updateInsurancePracticeOneFee(InsurancePracticeOneFeeVo insurancePracticeOneFeeVo) {
        insurancePracticeOneFeeMapper.updateInsurancePracticeOneFee(insurancePracticeOneFeeVo);
    }

    @Override
    public List<InsurancePracticeOneFeeVo> getInsurancePracticeOneFeeMessageRemind() {
        return insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeMessageRemind();
    }

    @Override
    @Transactional
    public void importDifferences(Long paymentId, ImportDataDto<InsurancePracticeDifferenceImportVo> importVoImportDataDto) {
        log.info("批量导入实做差异数据开始,导入编号{}", importVoImportDataDto.getImportNo());
        batchImportDataService.addImportData(importVoImportDataDto, ImportDataType.INSURANCE_PRACTICE_DIFFERENCE_IMPORT.getCode());
        checkImportData(paymentId, importVoImportDataDto);
        if (importVoImportDataDto.getErrorDesc().isEmpty()){
            PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(paymentId);
            deleteRejectedData(paymentId);
            List<InsurancePracticeOneFeeDetailVo> insurancePracticeOneFeeDetailVos = new ArrayList<>();
            List<InsurancePracticeDifferenceImportVo> dataList = importVoImportDataDto.getDataList();
            //多节点 多个签约方抬头  如果是签约方抬头和福利办理方一致则客户是主体
            Map<String, List<InsurancePracticeDifferenceImportVo>> disComMap = dataList.stream().collect(Collectors.groupingBy(InsurancePracticeDifferenceImportVo::getDisComName));
            BigDecimal totalAmt =BigDecimal.ZERO;
            for (String disComName : disComMap.keySet()) {
                List<InsurancePracticeDifferenceImportVo> disComList = disComMap.get(disComName);
                Map<Long, List<InsurancePracticeDifferenceImportVo>> custIdMap = disComList.stream().collect(Collectors.groupingBy(InsurancePracticeDifferenceImportVo::getCustId));
                for (Long custId : custIdMap.keySet()) {
                    for (InsurancePracticeDifferenceImportVo insurancePracticeDifferenceImportVo : custIdMap.get(custId)) {
                        Map<String, InsurancePracticeDifferenceProductImportVo> prodMap = insurancePracticeDifferenceImportVo.getProdMap();
                        for (String productName : prodMap.keySet()) {
                            InsurancePracticeOneFeeDetailVo insurancePracticeOneFeeDetailVo = new InsurancePracticeOneFeeDetailVo();
                            insurancePracticeOneFeeDetailVo.setOrderNo(insurancePracticeDifferenceImportVo.getOrderNo());
                            insurancePracticeOneFeeDetailVo.setPaymentId(paymentId);
                            insurancePracticeOneFeeDetailVo.setStartMonth(insurancePracticeDifferenceImportVo.getStartMonth());
                            insurancePracticeOneFeeDetailVo.setEndMonth(insurancePracticeDifferenceImportVo.getEndMonth());
                            insurancePracticeOneFeeDetailVo.setCreator(importVoImportDataDto.getLoginName());
                            insurancePracticeOneFeeDetailVo.setCreateTime(new Date());
                            insurancePracticeOneFeeDetailVo.setProductCode(InsuranceIRatioProductCodeEnum.getIndex(productName));
                            InsurancePracticeDifferenceProductImportVo insurancePracticeDifferenceProductImportVo = prodMap.get(productName);
                            BigDecimal comAmt = insurancePracticeDifferenceProductImportVo.getComAmt();
                            BigDecimal indAmt = insurancePracticeDifferenceProductImportVo.getIndAmt();
                            insurancePracticeOneFeeDetailVo.setIndAmt(indAmt);
                            insurancePracticeOneFeeDetailVo.setComAmt(comAmt);
                            insurancePracticeOneFeeDetailVo.setAmt(comAmt.add(indAmt));
                            insurancePracticeOneFeeDetailVo.setCustId(custId);
                            insurancePracticeOneFeeDetailVo.setDisCom(insurancePracticeDifferenceImportVo.getDisComName());
                            totalAmt = totalAmt.add(comAmt.add(indAmt));
                            try {
                                Date startDate = SDF.parse(insurancePracticeDifferenceImportVo.getStartMonth().toString());
                                Date endDate = SDF.parse(insurancePracticeDifferenceImportVo.getEndMonth().toString());
                                insurancePracticeOneFeeDetailVo.setStartMonth(Integer.parseInt(SDF.format(startDate)));
                                insurancePracticeOneFeeDetailVo.setEndMonth(Integer.parseInt(SDF.format(endDate)));
                            } catch (ParseException e) {
                                throw new RuntimeException(e);
                            }
                            insurancePracticeOneFeeDetailVos.add(insurancePracticeOneFeeDetailVo);
                        }

                    }
                }
            }

            Long oneId = insertInsurancePracticeOneFee(paymentId, paymentApplyVo.getPayCom(), totalAmt, importVoImportDataDto.getLoginName(),importVoImportDataDto.getImportNo());
            insurancePracticeOneFeeDetailVos.forEach(vo -> vo.setOneId(oneId));
            insurancePracticeOneFeeDetailMapper.batchInsert(insurancePracticeOneFeeDetailVos);

        }
        batchImportDataService.addAndupdateImportDatas(importVoImportDataDto);

    }

    @Override
    public InsurancePracticeOneFeeVo getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(Long paymentId,List<Integer> approveStatusList) {
        return insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(paymentId,approveStatusList);
    }


    public void checkImportData(Long paymentId, ImportDataDto<InsurancePracticeDifferenceImportVo> importVoImportDataDto){
        List<InsurancePracticeDifferenceImportVo> dataList = importVoImportDataDto.getDataList();
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgVoMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgName, OrgVo::getOrgCode));
        List<PracticePayDetailVo> practicePayDetailVos = payDetailService.selectPracticePayDetailListByPayId(paymentId);
        List<String> orderNoList = practicePayDetailVos.stream().map(PracticePayDetailVo::getOrderNo).collect(Collectors.toList());
        PaymentApplyVo paymentApplyVo = paymentApplyService.selectByPrimaryKey(paymentId);
        Map<String, Long> frequencyMap = dataList.stream()
                .map(InsurancePracticeDifferenceImportVo::getOrderNo)
                .collect(Collectors.groupingBy(orderNo -> orderNo, Collectors.counting()));

        for (InsurancePracticeDifferenceImportVo insurancePracticeDifferenceImportVo : dataList) {
            if (!insurancePracticeDifferenceImportVo.getErrorDescription().isEmpty()){
                continue;
            }
            if (!paymentApplyVo.getAppStatus().equals(BooleanTypeEnum.YES.getCode())){
                if (!orderNoList.contains(insurancePracticeDifferenceImportVo.getOrderNo())){
                    insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, MessageFormat.format(NOT_IN_PAY_APPLY, insurancePracticeDifferenceImportVo.getOrderNo()));
                    continue;
                }
                Set<Integer> productCodeSet = practicePayDetailVos.stream().map(PracticePayDetailVo::getProdCode).collect(Collectors.toSet());

                if (CollectionUtils.isEmpty(productCodeSet)){
                    insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, PRODUCT_NOT_EXIST_BY_ORDER);
                    continue;
                }
                Set<String> productNameSet = insurancePracticeDifferenceImportVo.getProdMap().keySet();
                if (productNameSet.size() != productCodeSet.size()){
                    insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, NOT_UPDATE_PRODUCT_ERROR);
                }
                for (Integer productCode : productCodeSet) {
                    String name = InsuranceIRatioProductCodeEnum.getName(productCode);
                    if (!productNameSet.contains(name)){
                        insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, PRODUCT_NAME_ERROR);
                    }
                }
            }
            if (frequencyMap.get(insurancePracticeDifferenceImportVo.getOrderNo())>1){
                insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, MessageFormat.format(ORDER_REPEAT, insurancePracticeDifferenceImportVo.getOrderNo()));
            }
            String orgCode = orgVoMap.get(insurancePracticeDifferenceImportVo.getOrgName().trim());
            if (StringUtils.isBlank(orgCode)){
                insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, MessageFormat.format(DATA_NOT_EXIST, "福利办理方"));
            }
            if (!paymentApplyVo.getPayCom().equals(orgCode)){
                insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, ORG_CODE_ERROR);
            }
            Long custId = customerWrapperService.getCustIdByCustName(insurancePracticeDifferenceImportVo.getCustName().trim());
            if (Objects.isNull(custId)){
                insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, MessageFormat.format(DATA_NOT_EXIST, "客户名称"));
            }
            String disComCode = orgVoMap.get(insurancePracticeDifferenceImportVo.getDisComName().trim());
            if (StringUtils.isBlank(disComCode)){
                insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, MessageFormat.format(DATA_NOT_EXIST, "签约方抬头"));
            }else {
                List<InsurancePracticeDisComPayVo> insurancePracticeDisComListByPayId = insurancePracticeDisComPayService.getInsurancePracticeDisComListByPayId(paymentId);
                if (CollectionUtils.isEmpty(insurancePracticeDisComListByPayId)){
                    if (!orgCode.equals(disComCode)){
                        insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, DIS_CODE_ERROR);
                    }
                }else {
                    List<String> disComList = insurancePracticeDisComListByPayId.stream().map(InsurancePracticeDisComPayVo::getDisCom).collect(Collectors.toList());
                    if (!disComList.contains(disComCode)){
                        insurancePracticeDifferenceImportVo.updateError(DATA_ERROR, DIS_CODE_ERROR);
                    }
                }
            }


            insurancePracticeDifferenceImportVo.setCustId(custId);
            insurancePracticeDifferenceImportVo.setOrgName(orgCode);
            insurancePracticeDifferenceImportVo.setDisComName(disComCode);
            if (!insurancePracticeDifferenceImportVo.getErrorDescription().isEmpty()) {
                importVoImportDataDto.recordError(insurancePracticeDifferenceImportVo.getRowNum(), JSONUtils.toJSONString(insurancePracticeDifferenceImportVo.getErrorDescription()));
            }
            importVoImportDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(insurancePracticeDifferenceImportVo, importVoImportDataDto.getImportNo(), importVoImportDataDto.getLoginName()));
        }

    }

    /**
     * 删除驳回数据
     *  @param paymentId
     */
    public void deleteRejectedData(Long paymentId){
        List<Integer> approveStatusList = new ArrayList<>();
        approveStatusList.add(OneFeeApproveStatusEnum.REJECTED.getCode());
        approveStatusList.add(OneFeeApproveStatusEnum.NOT_APPROVED.getCode());
        InsurancePracticeOneFeeVo insurancePracticeOneFeeVo = insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeByPaymentIdAndApproveStatus(paymentId, approveStatusList);
        if (insurancePracticeOneFeeVo!=null){
            insurancePracticeOneFeeMapper.deleteRejectedData(insurancePracticeOneFeeVo.getId());
            insurancePracticeOneFeeDetailMapper.deleteRejectedDataByOneId(insurancePracticeOneFeeVo.getId());
        }
    }

    /**
     * 新增
     * @param paymentId
     * @param payCom
     * @param totalAmt
     * @param loginName
     * @return
     */
    public Long insertInsurancePracticeOneFee(Long paymentId, String payCom, BigDecimal totalAmt, String loginName,String importNo) {
        UserOrgPosVo userOrgPosVo = iUserOrgPosWrapperService.getDefaultFlagOrgPosByLoginName(loginName);
        UserOrgPosVo largeUserVo = userWrapperService.getLargeDefaultFlagOrgPosByOrgCode(userOrgPosVo.getOrgCode());
        InsurancePracticeOneFeeVo insurancePracticeOneFeeVo = new InsurancePracticeOneFeeVo();
        insurancePracticeOneFeeVo.setOrgCode(payCom);
        insurancePracticeOneFeeVo.setPaymentId(paymentId);
        insurancePracticeOneFeeVo.setAmt(totalAmt);
        insurancePracticeOneFeeVo.setFirstApprove(largeUserVo.getOrgCode() + "," + largeUserVo.getPosCode());
        insurancePracticeOneFeeVo.setSecondApprove("liuwei" + "," + "huiruifeng");
        insurancePracticeOneFeeVo.setCreator(loginName);
        insurancePracticeOneFeeVo.setCreateTime(new Date());
        insurancePracticeOneFeeVo.setImportNo(importNo);
        insurancePracticeOneFeeMapper.insertInsurancePracticeOneFee(insurancePracticeOneFeeVo);
        return insurancePracticeOneFeeVo.getId();

    }


    public void insertOneFeeBalance(Long oneId,String loginName){

        InsurancePracticeOneFeeVo insurancePracticeOneFeeById = insurancePracticeOneFeeMapper.getInsurancePracticeOneFeeById(oneId);
        List<InsurancePracticeOneFeeDetailVo> insurancePracticeOneFeeDetailByOneId = insurancePracticeOneFeeDetailMapper.getInsurancePracticeOneFeeDetailByOneId(oneId);
        Map<String, List<InsurancePracticeOneFeeDetailVo>> disComMap = insurancePracticeOneFeeDetailByOneId.stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeDetailVo::getDisCom));
        for (String disCom : disComMap.keySet()) {
            Map<Long, List<InsurancePracticeOneFeeDetailVo>> custIdMap = disComMap.get(disCom).stream().collect(Collectors.groupingBy(InsurancePracticeOneFeeDetailVo::getCustId));
            for (Long custId : custIdMap.keySet()) {
                InsurancePracticeOneFeeBalanceVo insurancePracticeOneFeeBalanceVo = new InsurancePracticeOneFeeBalanceVo();
                List<InsurancePracticeOneFeeDetailVo> insurancePracticeOneFeeDetailVos = custIdMap.get(custId);
                insurancePracticeOneFeeBalanceVo.setCustId(custId);
                insurancePracticeOneFeeBalanceVo.setDisCom(disCom);
                insurancePracticeOneFeeBalanceVo.setOrgCode(insurancePracticeOneFeeById.getOrgCode());
                BigDecimal reduce = insurancePracticeOneFeeDetailVos.stream().map(InsurancePracticeOneFeeDetailVo::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                insurancePracticeOneFeeBalanceVo.setTotalAmt(reduce);
                insurancePracticeOneFeeBalanceVo.setRemainAmt(reduce);
                insurancePracticeOneFeeBalanceVo.setUsedAmt(BigDecimal.ZERO);
                insurancePracticeOneFeeBalanceVo.setCreator(loginName);
                insurancePracticeOneFeeBalanceVo.setCreateTime(new Date());
                checkInsertOrUpdate(oneId,insurancePracticeOneFeeById.getOrgCode(), disCom, custId,insurancePracticeOneFeeBalanceVo);
            }
        }
    }

    /**
     * 审批通过
     * @param updateVo
     * @param current
     * @param operator
     * @param now
     * @param id
     */
    private void handleApprove(InsurancePracticeOneFeeVo updateVo,
                               InsurancePracticeOneFeeVo current,
                               String operator,
                               Date now,
                               Long id) {

        if (OneFeeApproveStatusEnum.APPROVING.getCode().equals(current.getFirstApproveStatus())) {
            updateVo.setFirstApproveStatus(OneFeeApproveStatusEnum.APPROVED.getCode());
            updateVo.setSecondApproveStatus(OneFeeApproveStatusEnum.APPROVING.getCode());
            updateVo.setFirstApproveDate(now);
            updateVo.setFirstApprove(operator);

        } else if (OneFeeApproveStatusEnum.APPROVING.getCode().equals(current.getSecondApproveStatus())) {
            updateVo.setSecondApproveStatus(OneFeeApproveStatusEnum.APPROVED.getCode());
            updateVo.setApproveStatus(OneFeeApproveStatusEnum.APPROVED.getCode());
            updateVo.setSecondApproveDate(now);
            updateVo.setSecondApprove(operator);
            insertOneFeeBalance(id, current.getCreator());
        }
    }


    /**
     * 驳回
     * @param updateVo
     * @param current
     * @param operator
     * @param now
     */
    private void handleReject(InsurancePracticeOneFeeVo updateVo,
                              InsurancePracticeOneFeeVo current,
                              String operator,
                              Date now) {

        updateVo.setApproveStatus(OneFeeApproveStatusEnum.REJECTED.getCode());

        if (OneFeeApproveStatusEnum.APPROVING.getCode().equals(current.getFirstApproveStatus())) {
            updateVo.setFirstApproveStatus(OneFeeApproveStatusEnum.REJECTED.getCode());
            updateVo.setFirstApproveDate(now);
            updateVo.setFirstApprove(operator);

        } else if (OneFeeApproveStatusEnum.APPROVING.getCode().equals(current.getSecondApproveStatus())) {
            updateVo.setSecondApproveStatus(OneFeeApproveStatusEnum.REJECTED.getCode());
            updateVo.setSecondApproveDate(now);
            updateVo.setSecondApprove(operator);
        }
    }

    /**
     * 这个方法主要是校验是否已经有相同福利办理方 签约方 客户的数据在抵扣表中 有就累加待抵扣的金额
     * 同步插入关联表
     * @param oneId
     * @param orgCode
     * @param disCom
     * @param custId
     * @param insertBalanceVo
     */
    public void checkInsertOrUpdate(Long oneId,String orgCode,String disCom,Long custId,InsurancePracticeOneFeeBalanceVo insertBalanceVo){
        InsurancePracticeOneFeeBalanceVo balanceVo = insurancePracticeOneFeeBalanceMapper.getBalanceVoByCustIdAndOrgCodeAndDisCom(custId, orgCode, disCom);
        Long id = null;
        if (Objects.isNull(balanceVo)){
            insurancePracticeOneFeeBalanceMapper.insertInsurancePracticeOneFeeBalance(insertBalanceVo);
            id = insertBalanceVo.getId();
        }else {
            BigDecimal totalAmt = balanceVo.getTotalAmt().add(insertBalanceVo.getTotalAmt());
            BigDecimal remainAmt = balanceVo.getRemainAmt().add(insertBalanceVo.getTotalAmt());
            insurancePracticeOneFeeBalanceMapper.summationAmt(balanceVo.getId(), totalAmt, remainAmt);
            id = balanceVo.getId();
        }
        InsurancePracticeOneBalanceVo insurancePracticeOneBalanceVo = new InsurancePracticeOneBalanceVo();
        insurancePracticeOneBalanceVo.setOneId(oneId);
        insurancePracticeOneBalanceVo.setBalanceId(id);
        insurancePracticeOneBalanceVo.setAmt(insertBalanceVo.getTotalAmt());
        insurancePracticeOneBalanceVo.setCreator(insertBalanceVo.getCreator());
        insurancePracticeOneBalanceVo.setCreateTime(new Date());
        insurancePracticeOneBalanceMapper.insertInsurancePracticeOneBalance(insurancePracticeOneBalanceVo);

    }

    public void addOneFeeDiffDataExportVo(List<OneFeeDiffDataExportVo> oneFeeDiffDataExportVos
            ,String orgName,String disComName,String contractName,String custName,BigDecimal amt,String amtType) {
        OneFeeDiffDataExportVo oneFeeDiffDataExportVo = new OneFeeDiffDataExportVo();
        oneFeeDiffDataExportVo.setOrgCodeName(orgName);
        oneFeeDiffDataExportVo.setDisComName(disComName);
        oneFeeDiffDataExportVo.setContractType(contractName);
        oneFeeDiffDataExportVo.setCustName(custName);
        oneFeeDiffDataExportVo.setAmt(amt);
        oneFeeDiffDataExportVo.setAmtType(amtType);
        oneFeeDiffDataExportVos.add(oneFeeDiffDataExportVo);
    }


}

    