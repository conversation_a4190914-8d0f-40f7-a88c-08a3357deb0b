package com.reon.hr.sp.customer.service.impl.importService;

import com.alibaba.druid.support.json.JSONUtils;
import com.google.common.collect.Lists;
import com.reon.ehr.api.sys.dubbo.service.rpc.IEpEmployeeOrderWrapperService;
import com.reon.ehr.api.sys.enums.order.ServiceNature;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeOrderVo;
import com.reon.hr.api.base.dto.sys.ServiceSiteCfgDto;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.enums.SocialSecurityFundEnum;
import com.reon.hr.api.base.enums.StatusEnum;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.bill.enums.BillPrintConventionalCostsEnum;
import com.reon.hr.api.bill.enums.PaymentCustomerEnum;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.customer.dto.customer.QuotationDTO;
import com.reon.hr.api.customer.dto.importData.AddEhrEmployeeImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IQuotationResourceWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.CertType;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.enums.employee.*;
import com.reon.hr.api.customer.enums.employeeContract.TempTypeEnum;
import com.reon.hr.api.customer.enums.employeeContract.WorkMethodEnum;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.enums.quotation.QuotationSubType;
import com.reon.hr.api.customer.enums.quotation.QuotationTaxFlag;
import com.reon.hr.api.customer.exception.OrderException;
import com.reon.hr.api.customer.utils.*;
import com.reon.hr.api.customer.vo.CommInsurEmpVo;
import com.reon.hr.api.customer.vo.ContractAreaVo;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.QuotationItemVo;
import com.reon.hr.api.customer.vo.batchImport.CustomerOrderCacheVo;
import com.reon.hr.api.customer.vo.batchImport.ImportDataLogVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletFeeCfgVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.customer.vo.employee.OrderServiceChargeVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.sp.customer.dao.cus.ContractAreaMapper;
import com.reon.hr.sp.customer.dao.cus.CustomerMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeOrderMapper;
import com.reon.hr.sp.customer.entity.cus.Customer;
import com.reon.hr.sp.customer.entity.cus.SocialSysMandatoryInfo;
import com.reon.hr.sp.customer.service.ImportService.BatchAddEhrEmployeeImportService;
import com.reon.hr.sp.customer.service.contract.ContractRelativeQuotationService;
import com.reon.hr.sp.customer.service.cus.*;
import com.reon.hr.sp.customer.service.employee.IEmployeeOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BatchAddEhrEmployeeImportServiceImpl implements BatchAddEhrEmployeeImportService {

    private static final Logger logger = LoggerFactory.getLogger(BatchAddEhrEmployeeImportServiceImpl.class);

    private static final String REQUIRED_FIELDS_NOT_FILLED = "必填项未填写";
    private static final String DISOBEY_RULE = "违反规则";
    private static final String FORMAT_ERROR = "格式错误";
    private static final String SYSTEM_ERROR = "系统错误";
    private static final String IMPORT_DATA_DIFFERENT_SYSTEM_DATA = "导入信息与系统数据冲突";

    private static final String ERROR_MESSAGE_001 = "小合同编号未填写!";

    private static final String ERROR_MESSAGE_002 = "人员类型编号未填写!";

    private static final String ERROR_MESSAGE_003 = "社保套餐编号未填写!";

    private static final String ERROR_MESSAGE_004 = "员工姓名未填写!";

    private static final String ERROR_MESSAGE_005 = "证件类型未填写!";

    private static final String ERROR_MESSAGE_006 = "证件号码未填写!";

    private static final String ERROR_MESSAGE_007 = "身份证格式不正确!";

    private static final String ERROR_MESSAGE_008 = "联系电话和手机号码必须填一项!";

    private static final String ERROR_MESSAGE_009 = "入职时间未填写!";

    private static final String ERROR_MESSAGE_010 = "收费起始年月不可为空!";

    private static final String ERROR_MESSAGE_011 = "社保工资和公积金工资必填一个!";

    private static final String ERROR_MESSAGE_012 = "公积金企业比例与个人比例需全部填写或都不填写!";

    private static final String ERROR_MESSAGE_013 = "填写公积金比例，需填写公积金工资!";

    private static final String ERROR_MESSAGE_014 = "补充公积金企业比例与个人比例需全部填写或都不填写!";

    private static final String ERROR_MESSAGE_101 = "填写补充公积金比例，需填写公积金工资!";

    private static final String ERROR_MESSAGE_015 = "填写公积金工资，需填写公积金比例或者补充公积金比例!";

    private static final String ERROR_MESSAGE_016 = "员工状态是在职或者入职未生效不能添加！";

    private static final String ERROR_MESSAGE_017 = "填写的人员类型编号不存在！";

    private static final String ERROR_MESSAGE_018 = "填写的人员类型编号与小合同城市不匹配！";

    private static final String ERROR_MESSAGE_019 = "报价单编号不存在！";

    private static final String ERROR_MESSAGE_020 = "报价单编号不属于该合同！";

    private static final String ERROR_MESSAGE_021 = "账单模板编号不存在！";

    private static final String ERROR_MESSAGE_022 = "入职时间格式有误！";

    private static final String ERROR_MESSAGE_023 = "客户方内部编号不存在！";

    private static final String ERROR_MESSAGE_024 = "小合同编号不存在或者小合同未分配或者小合同未生效或者大合同已终止！";

    private static final String ERROR_MESSAGE_025 = "填写的人员类型编号和社保套餐人员类型编号不一致！";

    private static final String ERROR_MESSAGE_026 = "收费频率编号不存在！";

    private static final String ERROR_MESSAGE_027 = "账单模板中收费频率没有设置或者账单模板编号与小合同编号不属于同一客户！";

    private static final String ERROR_MESSAGE_028 = "社保套餐不存在或者状态不是有效状态";

    private static final String ERROR_MESSAGE_029 = "当前行填写的身份证号码和小合同编号两列的数据在excel其他行有重复，切勿填写重复数据！";

    private static final String ERROR_MESSAGE_030 = "派遣、外包员工合同类别不能为空！";

    private static final String ERROR_MESSAGE_031 = "派遣、外包员工合同类别不存在！";

    private static final String ERROR_MESSAGE_032 = "派遣、外包员工工作制不能为空！";

    private static final String ERROR_MESSAGE_033 = "派遣、外包员工合同开始时间不能为空！";

    private static final String ERROR_MESSAGE_034 = "派遣、外包员工合同开始时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";

    private static final String ERROR_MESSAGE_035 = "派遣、外包员工试用期月数和试用期结束时间至少有一个不能为空！";

    private static final String ERROR_MESSAGE_036 = "派遣、外包员工试用期月数只能输入数值类型！";

    private static final String ERROR_MESSAGE_037 = "派遣、外包员工合同终止时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";

    private static final String ERROR_MESSAGE_038 = "派遣、外包员工合同开始时间不能大于合同终止时间！";

    private static final String ERROR_MESSAGE_039 = "派遣、外包员工正式工资不能为空！";

    private static final String ERROR_MESSAGE_040 = "派遣、外包员工正式工资只能输入数值类型！";

    private static final String ERROR_MESSAGE_041 = "派遣开始时间不能为空！";

    private static final String ERROR_MESSAGE_042 = "派遣开始时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";

    private static final String ERROR_MESSAGE_043 = "派遣结束时间不能为空！";

    private static final String ERROR_MESSAGE_044 = "派遣结束时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";

    private static final String ERROR_MESSAGE_045 = "派遣开始时间不能大于派遣结束时间！";

    private static final String ERROR_MESSAGE_046 = "%s 产品的企业比例和个人比例在社保套餐维护生效时间段内不匹配！";

    private static final String ERROR_MESSAGE_047 = "填写收费起始月 %s 产品在社保套餐维护，比例名称为 %s 生效时间段内不匹配!";

    private static final String ERROR_MESSAGE_048 = "必填 %s 产品在社保套餐维护生效时间段内都不匹配！";
    private static final String ERROR_MESSAGE_048_RATIO_EXPIRE = "比例: %s 失效！";

    private static final String ERROR_MESSAGE_049 = "套餐没有比例！";

    private static final String ERROR_MESSAGE_050 = "解析存储过程失败,请联系管理员！";

    private static final String ERROR_MESSAGE_051 = "派遣员工合同开始日期格式化转换失败！";

    private static final String ERROR_MESSAGE_052 = "派遣员工合同截止日期格式化转换失败！";

    private static final String ERROR_MESSAGE_053 = "派遣开始日期格式化转换失败！";

    private static final String ERROR_MESSAGE_054 = "派遣截止日期格式化转换失败！";

    private static final String ERROR_MESSAGE_055 = "收费起始月格式有误！";

    private static final String ERROR_MESSAGE_056 = "账单起始月格式有误！";

    private static final String ERROR_MESSAGE_057 = "套餐内 %s 产品同收费时间存在多条比例！";

    private static final String ERROR_MESSAGE_058 = "套餐内不包含社保产品！";

    private static final String ERROR_MESSAGE_059 = "套餐内不包含公积金产品！";

    private static final String ERROR_MESSAGE_060 = "套餐内不包含补充公积金产品！";

    private static final String ERROR_MESSAGE_061 = "公积金企业比例只能输入带%数值！";

    private static final String ERROR_MESSAGE_062 = "公积金个人比例只能输入带%数值！";

    private static final String ERROR_MESSAGE_063 = "补充公积金企业比例只能输入带%数值！";

    private static final String ERROR_MESSAGE_064 = "补充公积金个人比例只能输入带%数值！";

    private static final String ERROR_MESSAGE_065 = "(员工有其他地区在职记录)";

    private static final String ERROR_MESSAGE_066 = "填写公积金工资员工必须填写公积金账号";

    private static final String ERROR_MESSAGE_067 = "套餐内分公司供应商与小合同接单方不一致!";

    private static final String ERROR_MESSAGE_068 = "联系电话号格式错误!";

    private static final String ERROR_MESSAGE_069 = "手机格式错误!";

    private static final String ERROR_MESSAGE_070 = "证件类型是身份证时户口性质需必填！";


    private static final String ERROR_MESSAGE_071 = "身份证号码不合法";
    private static final String ERROR_MESSAGE_075 = "在此身份证下你输入的姓名与已有的姓名不匹配";
    private static final String ERROR_MESSAGE_097 = "身份证类型不合法";
    private static final String ERROR_MESSAGE_072 = "（社保工资小于该城市的最低工资，已自动跳转为最低工资！）";
    private static final String ERROR_MESSAGE_073 = "（正式工资小于该城市的最低工资，已自动跳转为最低工资！）";
    private static final String ERROR_MESSAGE_074 = "（试用工资小于该城市的最低工资，已自动跳转为最低工资！）";

    private static final String ERROR_MESSAGE_093 = "签署日期格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";
    private static final String ERROR_MESSAGE_076 = "签署日期格式化转换失败！";

    private static final String ERROR_MESSAGE_077 = "派遣、外包员工是否有试用期不能为空！";
    private static final String ERROR_MESSAGE_078 = "派遣、外包员工是否有试用期所填值不对！";
    private static final String ERROR_MESSAGE_079 = "派遣、外包员工试用期起始时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";
    private static final String ERROR_MESSAGE_080 = "派遣、外包员工试用期起始时间格式化转换失败！";
    private static final String ERROR_MESSAGE_081 = "派遣、外包员工试用期起始时间不能为空！";
    private static final String ERROR_MESSAGE_082 = "派遣、外包员工试用期结束时间格式错误，必须是yyyyMMdd格式，年月日后面带-，/都可以(注意：有可能日不在当前月里)！";
    private static final String ERROR_MESSAGE_083 = "派遣、外包员工试用期结束时间格式化转换失败！";

    private static final String ERROR_MESSAGE_084 = "派遣、外包员工合同版本地不能为空！";
    private static final String ERROR_MESSAGE_085 = "派遣、外包员工合同版本地所填值不对！";

    private static final String ERROR_MESSAGE_086 = "派遣、外包员工合同签订地不能为空！";
    private static final String ERROR_MESSAGE_087 = "派遣、外包员工合同签订地所填值与系统数据不匹配！";

    private static final String ERROR_MESSAGE_088 = "派遣、外包员工合同版本地不能为空！";
    private static final String ERROR_MESSAGE_089 = "派遣、外包员工合同版本地所填值与系统数据不匹配！";

    private static final String ERROR_MESSAGE_090 = "派遣、外包员工试用工资不能为空！";
    private static final String ERROR_MESSAGE_091 = "派遣、外包员工试用工资只能输入数值类型！";

    private static final String ERROR_MESSAGE_092 = "派遣、外包员工工作制所填类型不存在！";

    private static final String ERROR_MESSAGE_094 = "试用期起始时间不能大于试用期结束时间！";

    private static final String ERROR_MESSAGE_095 = "合同终止时间未填！除了无固定期限劳动合同不必填，其它类型都必填！";

    private static final String ERROR_MESSAGE_096 = "（用工单位不是客户名称且也不是关联公司！）";

    private static final String ERROR_MESSAGE_098 = "套餐内包含社保产品必须填写社保工资！";

    private static final String ERROR_MESSAGE_099 = "派遣、外包员工工作地必填";

    private static final String ERROR_MESSAGE_100 = "派遣、外包员工工作岗位必填";

    private static final String ERROR_MESSAGE_102 = "填写的账单模板编号不属于该合同！";

    private static final String ERROR_MESSAGE_103 = "当前合同、账单模板下存在不同的税率，请联系管理员！";
    private static final String ERROR_MESSAGE_104 = "当前订单的税率与同一个合同、账单模板下面其他订单的税率不一致！";
    private static final String ERROR_MESSAGE_105 = "当前订单的含税标记与同一个合同、账单模板下面其他订单的不一致！";
    private static final String ERROR_MESSAGE_106 = "当前合同与账单模板下面存在不同的税率！";
    private static final String ERROR_MESSAGE_EHR_001 = "未匹配到企业端增员数据！";

    private static final String remindKey1 = "社保申报频率";

    private static final String remindKey2 = "公积金申报频率";

    private static final String remindKey3 = "补缴材料";

    private static final String remindKey4 = "特殊注意事项";

    private static final String remindKey5 = "过了增员截止点";

    private static final String remindKey6 = "用工单位";

    private static final String remindKey7 = "是否二次增员";

    private static final String remindKey8 = "工资是否一致";

    private static final String REMIND_MSG_01 = "存在";

    private static final String REMIND_MSG_02 = "不存在";

    @Autowired
    private IEmployeeOrderService employeeOrderService;

    @Autowired
    private IInsuranceSetWrapperService insuranceSetWrapperService;

    @Autowired
    private IBillTempletService billTempletService;

    @Autowired
    private ContractRelativeQuotationService contractRelativeQuotationServiceImpl;

    @Autowired
    private IQuotationResourceWrapperService quotationResourceWrapperService;

    @Autowired
    private IBatchImportDataService batchImportDataService;

    @Autowired
    private IInsuranceBaseWrapperService baseWrapperService;

    @Autowired
    private IIndCategoryWrapperService categoryWrapperService;

    @Autowired
    private ContractAreaService contractAreaService;

    @Autowired
    private IBillTempletFeeCfgService feeCfgService;

    @Autowired
    private EmployeeOrderMapper employeeOrderMapper;

    @Autowired
    private ICityMinimumWageWrapperService cityMinimumWageWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService iOrgnizationResourceWrapperService;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private ContractAreaMapper contractAreaMapper;
    @Resource(name = "serviceSiteDubboCfgService")
    private IServiceSiteCfgWrapperService serviceSiteCfgService;
    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private QuotationService quotationService;

    @Autowired
    private IEpEmployeeOrderWrapperService epEmployeeOrderWrapperService;
    @Autowired
    private ISocialWagsMaintainWrapperService iSocialWagsMaintainWrapperService;
    @Autowired
    private SocialSysMandatoryCityService socialSysMandatoryCityService;

    @Override
    public void addBatchEhrEmployeeImport(ImportDataDto<AddEhrEmployeeImportDto> importDataDto) {
        logger.info("企业端批量增员受理导入开始,导入编号{}", importDataDto.getImportNo());
        batchImportDataService.addImportData(importDataDto, ImportDataType.BATCH_ADD_EHR_EMPLOYEE.getCode());

        CustomerOrderCacheVo customerOrderCacheVo = new CustomerOrderCacheVo();
        List<String> successEmployeeList = Lists.newArrayList();
        List<OrgVo> allCompany = iOrgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgCodeMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgName, OrgVo::getOrgCode));
        Map<Long, List<CustomerVo>> customerVoListMap = new HashMap<>();

        List<String> contractAreaNoList = importDataDto.getDataList().stream().map(AddEhrEmployeeImportDto::getContractAreaNo).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(contractAreaNoList)){
            List<ContractAreaVo> contractAreaVoList = contractAreaMapper.getDistributionContractAreaList(contractAreaNoList,importDataDto.getLoginName());
            Map<String, List<ContractAreaVo>> contractAreaMap = contractAreaVoList.stream().collect(Collectors.groupingBy(ContractAreaVo::getContractAreaNo));
            customerOrderCacheVo.setContractAreaMap(contractAreaMap);
        }

        /** 用于判断 社保必填城市, 的必填信息是否填写,根据小合同中的城市判断 */
        Set<Integer> cityCodeSet = socialSysMandatoryCityService.getCityCodeSet();
        // 校验导入数据必填项是否填写
        List<AddEhrEmployeeImportDto> addEmployeeImportDtos = checkImportDataMustWriteField(importDataDto,customerOrderCacheVo);
        for (AddEhrEmployeeImportDto importDto : addEmployeeImportDtos) {
            try {
                EmployeeOrderVo employeeOrderVo = new EmployeeOrderVo();
                employeeOrderVo.setEhrEmployeeOrderId(importDto.getEhrEmployeeOrderId());
                SocialSysMandatoryInfo socialSysMandatoryInfo = new SocialSysMandatoryInfo();
                BeanUtils.copyProperties(importDto, socialSysMandatoryInfo);

                if (importDto.getCertType().equals(CertType.ID_CARD.getName())){
                    if (!IdCardUtils.isIdcard(importDto.getCertNo())){
                        importDto.updateError(FORMAT_ERROR,ERROR_MESSAGE_071);
                    }
                }
                // 根据身份证号判断当前小合同中是否已经存在该员工
                if (successEmployeeList.contains(importDto.getContractAreaNo() + "_" + importDto.getCertNo())) {
                    importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_029);
                    importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(importDto, importDataDto.getImportNo(), importDataDto.getLoginName()));
                } else {
                    ContractAreaVo contractArea = new ContractAreaVo();
                    OrderServiceChargeVo serviceCharge = new OrderServiceChargeVo();
                    // 校验小合同相关数据,存入员工订单
                    checkContractAreaInformationAndSetEmployeeOrder(importDto, employeeOrderVo, customerOrderCacheVo, importDataDto, contractArea, serviceCharge,
                            orgCodeMap,customerVoListMap);

                    if (cityCodeSet.contains(contractArea.getCityCode()) && SocialSysMandatoryInfo.isAnyEmptyNoIncludeOrderNo(socialSysMandatoryInfo)) {importDto.updateError(DISOBEY_RULE, "该员工社保信息必填,请全部填写!");}
                    // 校验社保公积金相关数据,创建产品信息兵存入员工订单  创建并计算服务费等数据
                    checkSetInformationAndSetEmployeeOrderCfg(importDto, employeeOrderVo, customerOrderCacheVo, serviceCharge);
                    employeeOrderVo.setOptType("add");
                    employeeOrderVo.setCreator(importDataDto.getLoginName());
                    employeeOrderVo.setTotalFeeTime(Integer.parseInt(importDto.getRevStartMonth()));
                }
                //判断有错误就不添加当前这个订单
                if (importDto.getErrorDescription().isEmpty()) {
                    successEmployeeList.add(importDto.getContractAreaNo() + "_" + importDto.getCertNo());
                    List<ContractAreaVo> contractAreaList = customerOrderCacheVo.getContractAreaMap().get(importDto.getContractAreaNo());
                    ContractAreaVo contractAreaVo = contractAreaList.get(0);
                    String cityCode = contractAreaVo.getCityCode().toString();

                    Integer startDate=null;
                    if(ContractType.SOCIAL_ADD_SPECIAL_LIST.contains(contractAreaVo.getContractType())){
                        startDate = Integer.parseInt(employeeOrderVo.getStartDate().replaceAll("-","").replaceAll("/","").substring(0,6));
                    }
                    Integer totalFeeTime = employeeOrderVo.getTotalFeeTime();
                    SocialWagsMaintainVo vo = iSocialWagsMaintainWrapperService.getMinimumWageByCityCodeAndMonth(Integer.parseInt(cityCode), totalFeeTime,contractAreaVo.getReceiving());
                    if (vo!=null){
                        BigDecimal minimumWage = vo.getMinWage();
                        if(employeeOrderVo.getSocailSalary()!=null&&employeeOrderVo.getSocailSalary().compareTo(minimumWage)<0){
                            employeeOrderVo.setSocailSalary(minimumWage);
                            importDto.setEntryRemark(importDto.getEntryRemark() == null ? ERROR_MESSAGE_072 : importDto.getEntryRemark() + ERROR_MESSAGE_072);
                            employeeOrderVo.setEntryRemark(importDto.getEntryRemark());
                        }
                    }
                    if (startDate != null) {
                        SocialWagsMaintainVo minimumWageByCityCodeAndMonth = iSocialWagsMaintainWrapperService.getMinimumWageByCityCodeAndMonth(Integer.parseInt(cityCode), startDate,contractAreaVo.getReceiving());
                        if (minimumWageByCityCodeAndMonth != null) {
                            BigDecimal minimumWage = minimumWageByCityCodeAndMonth.getMinWage();
                            if (employeeOrderVo.getFormalSalary().compareTo(minimumWage) < 0) {
                                employeeOrderVo.setFormalSalary(minimumWage);
                                employeeOrderVo.setRemark(ERROR_MESSAGE_073);
                            }
                            if (employeeOrderVo.getProbaSalary() != null && employeeOrderVo.getProbaSalary().compareTo(minimumWage) < 0) {
                                employeeOrderVo.setProbaSalary(minimumWage);
                                employeeOrderVo.setRemark(employeeOrderVo.getRemark() + ERROR_MESSAGE_074);
                            }
                        }

                    }
                    setSocialSysInfoData(socialSysMandatoryInfo, employeeOrderVo);
                    // 将没问题的员工插入数据库
                    employeeOrderService.innerRolledBackSaveOrUpdate(employeeOrderVo);
                    //插入 提醒信息
//                    String remind = JSONUtils.toJSONString(importDto.getRemind());
//                    EmployeeRemindImport employeeRemindImport = new EmployeeRemindImport();
//                    employeeRemindImport.setCreator(importDataDto.getLoginName());
//                    employeeRemindImport.setUpdater(importDataDto.getLoginName());
//                    employeeRemindImport.setRemind(remind);
//                    employeeRemindImport.setImportNo(importDataDto.getImportNo());
//                    employeeRemindImportMapper.insert(employeeRemindImport);
                }
            } catch (OrderException e) {
                importDto.updateError(SYSTEM_ERROR, e.getMessage());
            } catch (Exception e) {
                importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_050);
                logger.error("批量增员导入数据解析过程中报错,导入编号 : {},错误行号 : {}", importDataDto.getImportNo(), importDto.getRowNum());
                e.printStackTrace();
            } finally {
                if (!importDto.getErrorDescription().isEmpty()) {
                    importDataDto.recordError(importDto.getRowNum(), JSONUtils.toJSONString(importDto.getErrorDescription()));
                }
            }
        }
        // 将错误信息插入日志
        batchImportDataService.addAndupdateImportData(importDataDto);
    }

    private static void setSocialSysInfoData(SocialSysMandatoryInfo ssmi, EmployeeOrderVo employeeOrderVo) {
        employeeOrderVo.setActualEmployer(ssmi.getActualEmployer());
        employeeOrderVo.setActualEmployerRegister(ssmi.getActualEmployerRegister());
        employeeOrderVo.setUnifiedCreditCode(ssmi.getUnifiedCreditCode());
        employeeOrderVo.setCompanyType(ssmi.getCompanyType());
        employeeOrderVo.setActualPayrollCompany(ssmi.getActualPayrollCompany());
        employeeOrderVo.setSsmiWorkCity(ssmi.getSsmiWorkCity());
        employeeOrderVo.setSsmiJobPosition(ssmi.getSsmiJobPosition());
        employeeOrderVo.setResidenceAddress(ssmi.getResidenceAddress());
        employeeOrderVo.setHouseholdRegister(ssmi.getHouseholdRegister());
    }
    /**
     * 必填项校验
     *
     * @param importDataDto
     */
    private List<AddEhrEmployeeImportDto> checkImportDataMustWriteField(ImportDataDto<AddEhrEmployeeImportDto> importDataDto,CustomerOrderCacheVo customerOrderCacheVo) {
        List<AddEhrEmployeeImportDto> addEmployeeImportDtoList = importDataDto.getDataList();
        Map<String, List<ContractAreaVo>> contractAreaMap = customerOrderCacheVo.getContractAreaMap();
        Map<String, EhrEmployeeOrderVo> ehrEmployeeOrderVoMap =new HashMap<>();
        List<EhrEmployeeOrderVo> queryList=new ArrayList<>();
        for (AddEhrEmployeeImportDto addEhrEmployeeImportDto:addEmployeeImportDtoList) {

            if (StringUtil.isBlank(addEhrEmployeeImportDto.getEmployeeName())) {
                addEhrEmployeeImportDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_004);
            }else {
                addEhrEmployeeImportDto.setEmployeeName(addEhrEmployeeImportDto.getEmployeeName().replace(" ",""));
            }

            Integer certType =0;
            if (StringUtil.isBlank(addEhrEmployeeImportDto.getCertType())) {
                addEhrEmployeeImportDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_005);
            } else {
                certType = BatchCommInsurOrderImportUtil.isCertTypeIf(addEhrEmployeeImportDto.getCertType());
                if(certType==EmployeeCertType.ID_CARD.getCode()){
                    if (StringUtil.isBlank(addEhrEmployeeImportDto.getHousehold())) {
                        addEhrEmployeeImportDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_070);
                    }
                }
                if (certType == 0) {
                    addEhrEmployeeImportDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_097);
                }
            }

            if (StringUtil.isBlank(addEhrEmployeeImportDto.getCertNo())) {
                addEhrEmployeeImportDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_006);
            }else {
                addEhrEmployeeImportDto.setCertNo(addEhrEmployeeImportDto.getCertNo().replace(" ",""));
                //二次增员
                addEhrEmployeeImportDto.updateRemind(remindKey7,PaymentCustomerEnum.BooleanType.NO.getName());
            }

            if (StringUtil.isNotBlank(addEhrEmployeeImportDto.getCertType()) && StringUtil.isNotBlank(addEhrEmployeeImportDto.getCertNo())) {
                if (certType == EmployeeCertType.ID_CARD.getCode() && !BatchCommInsurOrderImportUtil.isIdcard(addEhrEmployeeImportDto.getCertNo())) {
                    addEhrEmployeeImportDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_007);
                }
                String certNameByCertNo = employeeOrderService.findCertNameByCertNo(addEhrEmployeeImportDto.getCertNo());
                if (StringUtils.isNotEmpty(certNameByCertNo)&&!certNameByCertNo.equals(addEhrEmployeeImportDto.getEmployeeName())){
                    addEhrEmployeeImportDto.updateError(DISOBEY_RULE,ERROR_MESSAGE_075);
                }
                String certNo = addEhrEmployeeImportDto.getCertNo();
                if (certNo.endsWith("x")){
                    StringBuilder stringBuilder = new StringBuilder(certNo);
                    stringBuilder.replace(stringBuilder.length()-1,stringBuilder.length(),"X");
                    addEhrEmployeeImportDto.setCertNo(stringBuilder.toString());
                }
            }


            List<ContractAreaVo> contractAreaVoList = contractAreaMap.get(addEhrEmployeeImportDto.getContractAreaNo());
            if(CollectionUtils.isNotEmpty(contractAreaVoList)){
                EhrEmployeeOrderVo ehrEmployeeOrderVo = new EhrEmployeeOrderVo();
                BeanUtils.copyProperties(addEhrEmployeeImportDto,ehrEmployeeOrderVo);
                ehrEmployeeOrderVo.setName(addEhrEmployeeImportDto.getEmployeeName());

                ContractAreaVo contractAreaVo = contractAreaVoList.get(0);
                ehrEmployeeOrderVo.setServiceNature(ServiceNature.getServiceNature(contractAreaVo.getContractType()));
                BeanUtils.copyProperties(contractAreaVo,ehrEmployeeOrderVo,"name");
                ehrEmployeeOrderVo.setCertType(certType);
                queryList.add(ehrEmployeeOrderVo);
                addEhrEmployeeImportDto.setEhrEmployeeOrderVoMapKey(ehrEmployeeOrderVo.getName() + "-" + ehrEmployeeOrderVo.getCertType()
                        + "-" + ehrEmployeeOrderVo.getCertNo() + "-" + ehrEmployeeOrderVo.getServiceNature()+ "-" + ehrEmployeeOrderVo.getAccountFlag()
                + "-"+ ehrEmployeeOrderVo.getCustId());
            }
        }
        if(CollectionUtils.isNotEmpty(queryList)){
            List<EhrEmployeeOrderVo> ehrEmployeeOrderVoList = epEmployeeOrderWrapperService.selectListByList(queryList);
            ehrEmployeeOrderVoMap = ehrEmployeeOrderVoList.stream().collect(Collectors.toMap(
                    e -> e.getName() + "-" + e.getCertType() + "-" + e.getCertNo() + "-" + e.getServiceNature()+ "-" + e.getAccountFlag()
                            + "-"+ e.getCustId()
                    , Function.identity(), (a1, a2) -> a2));
        }
        Map<String, InsuranceSetRatioVo> ratioVoMap = insuranceSetWrapperService.selectAllInsuranceSetAcctRatio().stream().collect(Collectors.toMap(InsuranceSetRatioVo::getInsuranceRatioCode, Function.identity(),(v1,v2)->v2));
        for (AddEhrEmployeeImportDto importDto : addEmployeeImportDtoList) {
            EhrEmployeeOrderVo ehrEmployeeOrderVo = ehrEmployeeOrderVoMap.get(importDto.getEhrEmployeeOrderVoMapKey());
            if(ehrEmployeeOrderVo!=null){
                setImportDto(importDto,ehrEmployeeOrderVo,ratioVoMap);
                /*final CopyOptions copyOptions = CopyOptions.create();
                copyOptions.setIgnoreNullValue(true);
                copyOptions.setIgnoreError(true);
                BeanUtil.copyProperties(importDto,ehrEmployeeOrderVo, copyOptions);
                BeanUtil.copyProperties(ehrEmployeeOrderVo,importDto, copyOptions);*/
            }else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_EHR_001);
            }


            if (StringUtil.isBlank(importDto.getContractAreaNo())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_001);
            }

            if (StringUtil.isBlank(importDto.getEmployeeTypeNo())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_002);
            }

            if (StringUtil.isBlank(importDto.getSetCode())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_003);
            }



            if (StringUtils.isBlank(importDto.getNumber()) && StringUtils.isBlank(importDto.getMobile())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_008);
            }

            if (StringUtils.isNotBlank(importDto.getNumber()) && !BatchImportExcelCommonUtil.isMobile(importDto.getNumber())) {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_068);
            }
            if (StringUtils.isNotBlank(importDto.getMobile()) && !BatchImportExcelCommonUtil.isMobile(importDto.getMobile())) {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_069);
            }


            if (StringUtil.isBlank(importDto.getEntryDate())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_009);
            }

            // 收费起始年月不可为空
            if (StringUtil.isBlank(importDto.getRevStartMonth())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_010);
            } else {
                if (importDto.getRevStartMonth().length() != 6 || !BatchImportExcelCommonUtil.isNumbers(importDto.getRevStartMonth())) {
                    importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_055);
                }
            }



            if (StringUtil.isNotBlank(importDto.getBillStartMonth())) {
                if (importDto.getBillStartMonth().length() != 6 || !BatchImportExcelCommonUtil.isNumbers(importDto.getBillStartMonth())) {
                    importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_056);
                }
            }

            // 优先判断如果社保工资和公积金工资都未填写则直接报错,社保工资和公积金工资必填一个
            if (StringUtil.isBlank(importDto.getSocialInsuranceBase()) && StringUtil.isBlank(importDto.getAccumulationFundBase())) {
                importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_011);
            }

            boolean accumulationFundIsNull=true;
            // 公积金的比例与基数必须全部填写或都不填写
            if (!(StringUtils.isNotBlank(importDto.getComRatio()) && StringUtils.isNotBlank(importDto.getIndRatio()) )
                    && !(StringUtils.isBlank(importDto.getComRatio()) && StringUtils.isBlank(importDto.getIndRatio()) )) {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_012);
            } else {
                if (StringUtils.isNotBlank(importDto.getComRatio())) {
                    accumulationFundIsNull=false;
                    if(StringUtils.isBlank(importDto.getAccumulationFundBase())){
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_013);
                    }

                    importDto.setComRatio(importDto.getComRatio().replace("%", ""));
                    importDto.setIndRatio(importDto.getIndRatio().replace("%", ""));
                    if (!BatchImportExcelCommonUtil.isNumbers(importDto.getComRatio())) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_061);
                    }
                    if (!BatchImportExcelCommonUtil.isNumbers(importDto.getIndRatio())) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_062);
                    }
                }
            }

            boolean supplementAccumulationFundIsNull=true;
            // 补充公积金的比例与基数必须全部填写或都不填写
            if (!(StringUtils.isNotBlank(importDto.getSupplementComRatio()) && StringUtils.isNotBlank(importDto.getSupplementIndRatio()) )
                    && !(StringUtils.isBlank(importDto.getSupplementComRatio()) && StringUtils.isBlank(importDto.getSupplementIndRatio()) )) {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_014);
            } else {
                if (StringUtils.isNotBlank(importDto.getSupplementComRatio())) {
                    supplementAccumulationFundIsNull=false;
                    if(StringUtils.isBlank(importDto.getAccumulationFundBase())){
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_101);
                    }

                    importDto.setSupplementComRatio(importDto.getSupplementComRatio().replace("%", ""));
                    importDto.setSupplementIndRatio(importDto.getSupplementIndRatio().replace("%", ""));
                    if (!BatchImportExcelCommonUtil.isNumbers(importDto.getSupplementComRatio())) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_063);
                    }
                    if (!BatchImportExcelCommonUtil.isNumbers(importDto.getSupplementIndRatio())) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_064);
                    }

                }
            }
            if(accumulationFundIsNull&&supplementAccumulationFundIsNull&&StringUtils.isNotBlank(importDto.getAccumulationFundBase())){
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_015);
            }

            // 存在必填项未填写,记录错误信息.
            if (!importDto.getErrorDescription().isEmpty()) {
                importDataDto.recordError(importDto.getRowNum(), JSONUtils.toJSONString(importDto.getErrorDescription()));
                importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(importDto, importDataDto.getImportNo(), importDataDto.getLoginName()));

            }
        }
        return addEmployeeImportDtoList.stream().filter(importDto -> importDto.getErrorDescription().isEmpty()).collect(Collectors.toList());
    }



    /**
     * 校验与小合同相关数据并将通过检验的小合同数据赋值致员工订单
     */
    private void checkContractAreaInformationAndSetEmployeeOrder(AddEhrEmployeeImportDto importDto,
                                                                 EmployeeOrderVo employeeOrderVo,
                                                                 CustomerOrderCacheVo customerOrderCacheVo,
                                                                 ImportDataDto<AddEhrEmployeeImportDto> importDataDto, ContractAreaVo contractArea, OrderServiceChargeVo serviceCharge,
                                                                 Map<String, String> orgCodeMap,
                                                                 Map<Long, List<CustomerVo>> customerVoListMap) {
        //非社保公积金
        serviceCharge.setId(0L);
        serviceCharge.setArchiveFee(BigDecimal.ZERO);
        serviceCharge.setRevStartMonth(Integer.parseInt(importDto.getRevStartMonth()));
        if (StringUtils.isNotBlank(importDto.getBillStartMonth())) {
            serviceCharge.setBillStartMonth(Integer.parseInt(importDto.getBillStartMonth()));
        }
//        employeeOrderVo.setServiceCharge(serviceCharge);

        //通过账单id获取账单和收费信息
        String contractareaNo = importDto.getContractAreaNo();

        Long templetId = 0L;
        Long revTempId = 0L;
        ContractAreaVo contractAreaVo = new ContractAreaVo();
        contractAreaVo.setContractAreaNo(contractareaNo);
        List<ContractAreaVo> contractAreaList;
        if (customerOrderCacheVo.getContractAreaMap().containsKey(contractareaNo)) {
            contractAreaList = customerOrderCacheVo.getContractAreaMap().get(contractareaNo);
        } else {
            contractAreaList = contractAreaService.getDistributionContractAreaList(contractAreaVo, importDataDto.getLoginName());
            customerOrderCacheVo.getContractAreaMap().put(contractareaNo, contractAreaList);
        }

        if (CollectionUtils.isEmpty(contractAreaList)) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_024);
        } else {
            //设置人员
            String certNo = importDto.getCertNo();
            BeanUtils.copyProperties(contractAreaList.get(0), contractArea);
            employeeOrderVo.setStorageFlag(contractArea.getArchiveFlag());
            employeeOrderVo.setTempType(contractArea.getTempletType());

            //根据身份证判断员工状态为空添加
            List<EmployeeOrderVo> employeeOrderVoList = employeeOrderMapper.getEmployeeOrderVoByCertNo(certNo);
            employeeOrderVoList = CollectionUtils.isEmpty(employeeOrderVoList) ? employeeOrderVoList : employeeOrderVoList.stream().filter(c -> c.getOrderStatus() != EmployeeOrderStatus.SUB_FINISHED.getCode()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(employeeOrderVoList)) {
                boolean haveContractArea = false;
                for (EmployeeOrderVo orderVo : employeeOrderVoList) {
                    if (orderVo.getContractAreaNo().equals(contractareaNo)) {
                        haveContractArea = true;
                        break;
                    }
                }
                if (haveContractArea) {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_016);
                } else {
                    importDto.setEntryRemark(importDto.getEntryRemark() == null ? ERROR_MESSAGE_065 : importDto.getEntryRemark() + ERROR_MESSAGE_065);
                    importDto.updateRemind(remindKey7,PaymentCustomerEnum.BooleanType.Yes.getName());
                    setEmployeeVo(employeeOrderVo, importDto);
                }
            } else {
                setEmployeeVo(employeeOrderVo, importDto);
            }

            String employeeTypeNo = importDto.getEmployeeTypeNo();
            if (employeeTypeNo != null) {
                //通过人员编号获取人员信息
                IndCategoryVo indCategoryVo;
                if (customerOrderCacheVo.getIndCategoryVoMap().containsKey(employeeTypeNo)) {
                    indCategoryVo = customerOrderCacheVo.getIndCategoryVoMap().get(employeeTypeNo);
                } else {
                    indCategoryVo = categoryWrapperService.findOne(employeeTypeNo);
                    customerOrderCacheVo.getIndCategoryVoMap().put(employeeTypeNo, indCategoryVo);
                }
                if (indCategoryVo == null) {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_017);
                } else {
                    if (indCategoryVo.getCityCode().equals(contractArea.getCityCode())) {
                        employeeOrderVo.setCategoryCode(indCategoryVo.getCategoryCode());
                    } else {
                        importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_018);
                    }
                }
            }


            String templateIdStr = importDto.getTemplateId();
            String revTempIdStr = importDto.getRevTempId();
            if (StringUtils.isNotEmpty(templateIdStr)) {
                BillTempletVo oneByTempletNo;
                if (customerOrderCacheVo.getOneByTempletNoMap().containsKey(templateIdStr)) {
                    oneByTempletNo = customerOrderCacheVo.getOneByTempletNoMap().get(templateIdStr);
                } else {
                    oneByTempletNo = billTempletService.getOneByTempletNo(templateIdStr,contractArea.getContractNo());
                    customerOrderCacheVo.getOneByTempletNoMap().put(templateIdStr, oneByTempletNo);
                }
                if (oneByTempletNo == null) {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_102);
                } else {
                    templetId = oneByTempletNo.getId();
                    Integer defaultFlag = StringUtils.isEmpty(revTempIdStr) ? 2 : null;
                    revTempId = getBillTempletFeeCfgVos(customerOrderCacheVo,
                            templateIdStr, defaultFlag, serviceCharge, importDto, revTempIdStr, contractArea.getCustId());
                }
            } else {
                //判断账单编号如果是空的并是否和小合同有关联
                if (contractArea.getTempletId() != null && contractArea.getTempletId() != 0) {
                    if (importDto.getErrorDescription().isEmpty()) {
                        //判断账单编号填了的并是否和小合同有关联
                        BillTempletVo billTempletVo;
                        if (customerOrderCacheVo.getBillTempletVoMap().containsKey(contractArea.getTempletId())) {
                            billTempletVo = customerOrderCacheVo.getBillTempletVoMap().get(contractArea.getTempletId());
                        } else {
                            billTempletVo = billTempletService.getOneById(contractArea.getTempletId(),null);
                            customerOrderCacheVo.getBillTempletVoMap().put(contractArea.getTempletId(), billTempletVo);
                        }
                        templetId = billTempletVo.getId();
                        Integer defaultFlag = StringUtils.isEmpty(revTempIdStr) ? 2 : null;
                        revTempId = getBillTempletFeeCfgVos(customerOrderCacheVo,
                                billTempletVo.getTempletNo(), defaultFlag, serviceCharge, importDto,
                                revTempIdStr, contractArea.getCustId());
                    }
                } else {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_021);
                }
            }
            List<CustomerVo> customerVoList =new ArrayList<>();
            if(customerVoListMap.containsKey(contractArea.getCustId())){
                customerVoList = customerVoListMap.get(contractArea.getCustId());
            }else {
                customerVoList = customerMapper.getCustomerRelevanceByCustId(contractArea.getCustId());
                customerVoListMap.put(contractArea.getCustId(),customerVoList);
            }
            //判断大合同是否是派遣员工
            if (contractArea.getContractType() == ContractType.CONTRACT_TYPE_DISPATCH.getCode()) {
                setDispatchedWorkerAndOutsourcing(importDto, employeeOrderVo,orgCodeMap,customerVoList);
                // 设置派遣员工数据
                setDispatchedWorker(importDto, employeeOrderVo);
            } else if (contractArea.getContractType() == ContractType.CONTRACT_TYPE_OUTSOURCING_1.getCode() || contractArea.getContractType() == ContractType.CONTRACT_TYPE_OUTSOURCING_2.getCode()) {
                setDispatchedWorkerAndOutsourcing(importDto, employeeOrderVo,orgCodeMap,customerVoList);
                setOutsourcingWorker(importDto, employeeOrderVo);
            } else {
                employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.NO.getCode());
            }

            //判断小合同绑定客户编号是否和解析的客户编号相等
            if (StringUtils.isNotEmpty(importDto.getCustomerInternalNo())) {
                if (importDto.getCustomerInternalNo().equals(contractArea.getCustNo())) {
                    employeeOrderVo.setCustNo(importDto.getCustomerInternalNo());
                } else {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_023);
                }
            }

            BeanUtils.copyProperties(contractArea, employeeOrderVo);
            // 判断是否外呼
            if (StringUtils.isNotEmpty(importDto.getCallFlag())) {
                if (importDto.getCallFlag().equals(EmployeeOrderTelFlag.YES.getName())) {
                    employeeOrderVo.setTelFlag(EmployeeOrderTelFlag.YES.getCode());
                } else {
                    employeeOrderVo.setTelFlag(EmployeeOrderTelFlag.NO.getCode());
                }
            }

            //上面不知道什么情况 所以分开判断
            // 根据 小合同查询 服务网店 PaymentCustomerEnum SocialSecurityFundEnum customerMapper
            ContractAreaVo contractAreaVo1 = contractAreaMapper.fingByContractAreaNo(importDto.getContractAreaNo());
            if (contractAreaVo1 != null){
                ServiceSiteCfgDto serviceSiteCfgDto = new ServiceSiteCfgDto(contractAreaVo1.getCityCode(),contractAreaVo1.getReceiving());
                // 单个查询就只有一个
                List<ServiceSiteCfgDto> insurAddDayList = serviceSiteCfgService.getInsurAddDay(Collections.singletonList(serviceSiteCfgDto));

                ServiceSiteCfgDto insurAddDay = insurAddDayList.size() > 0 ? insurAddDayList.get(0) : null;
                //增员截点
                if(Objects.nonNull(insurAddDay) && Objects.nonNull(insurAddDay.getInsurAddDay())){
                    if (LocalDate.now().getDayOfMonth() > insurAddDay.getInsurAddDay()) {
                        importDto.updateRemind(remindKey5, PaymentCustomerEnum.BooleanType.Yes.getName());
                    }else {
                        importDto.updateRemind(remindKey5, PaymentCustomerEnum.BooleanType.NO.getName());
                    }
                }else {
                    importDto.updateRemind(remindKey5, "");
                }

                if(insurAddDay != null){
                    //补缴材料
                    if(StringUtils.isNotBlank(insurAddDay.getAppendInfo())){
                        importDto.updateRemind(remindKey3,insurAddDay.getAppendInfo());
                    }else {
                        importDto.updateRemind(remindKey3,"");
                    }
                    //特殊注意事项
                    if(StringUtils.isNotBlank(insurAddDay.getSpecialConsiderations())){
                        importDto.updateRemind(remindKey4,insurAddDay.getSpecialConsiderations());
                    }else {
                        importDto.updateRemind(remindKey4,"");
                    }
                    //社保频率
                    if (Objects.nonNull(insurAddDay.getApplyInsurFreq())){
                        importDto.updateRemind(remindKey1, SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(insurAddDay.getApplyInsurFreq()));
                    }else {
                        importDto.updateRemind(remindKey1, "");
                    }
                    //公积金频率
                    if (Objects.nonNull(insurAddDay.getApplyFundFreq())){
                        importDto.updateRemind(remindKey2, SocialSecurityFundEnum.IsApplyInsurFreqEnum.getName(insurAddDay.getApplyFundFreq()));
                    }else {
                        importDto.updateRemind(remindKey2, "");
                    }
                }
            }
            //社保工资
            String socialInsuranceBase = importDto.getSocialInsuranceBase();
            //公积金工资
            String accumulationFundBase = importDto.getAccumulationFundBase();
            if(StringUtils.isNotBlank(socialInsuranceBase) && StringUtils.isNotBlank(accumulationFundBase) ){
                if (socialInsuranceBase.equals(accumulationFundBase)){
                    importDto.updateRemind(remindKey8,PaymentCustomerEnum.BooleanType.Yes.getName());
                }else {
                    importDto.updateRemind(remindKey8,PaymentCustomerEnum.BooleanType.NO.getName());
                }
            }else {
                importDto.updateRemind(remindKey8,"");
            }
            /** 如果小合同是单立户,那么跳过
             非单立户
             那么需要判断白名单里面是否有,如果有,则跳过
             没有,则要判断当前小合同是否和当前的收费模板 提前几月收一致
             */
            if (contractAreaService.adjustCACanInsert(contractareaNo,revTempId, importDto.getEmployeeTypeNo())) {
                importDto.updateError(DISOBEY_RULE, "当前收费模板与服务网点的账单收付规则不一致,请调整后再提交!");
            }
        }
        ImportDataLogVo importDataLogVo = batchImportDataService.createImportDataLogVo(importDto, importDataDto.getImportNo(), importDataDto.getLoginName());
        importDataLogVo.setRemind(JSONUtils.toJSONString(importDto.getRemind()));
        importDataDto.getImportDataLogVoList().add(importDataLogVo);
        importDto.setTemplateId(templetId.toString());
        importDto.setRevTempId(revTempId.toString());
    }

    // 校验社保套餐数据并设置员工产品
    private void checkSetInformationAndSetEmployeeOrderCfg(AddEhrEmployeeImportDto importDto,
                                                           EmployeeOrderVo employeeOrderVo,
                                                           CustomerOrderCacheVo customerOrderCacheVo,  OrderServiceChargeVo serviceCharge) {
        ContractAreaVo contractAreaVo = null;
        //通过社保套餐编号获取社保、公积金、补充公积金信息
        String setcode = importDto.getSetCode();
        InsuranceSetVo insuranceSet;
        // 判断社保套餐编号为空不添加社保公积金
        if (customerOrderCacheVo.getInsuranceSetVoMap().containsKey(setcode)) {
            insuranceSet = customerOrderCacheVo.getInsuranceSetVoMap().get(setcode);
        } else {
            insuranceSet = insuranceSetWrapperService.findInsuranceSet(setcode);
            customerOrderCacheVo.getInsuranceSetVoMap().put(setcode, insuranceSet);
        }

        // 判断社保套餐是否有效
        // 新增判断社保套餐编号是否存在
        if (insuranceSet == null || insuranceSet.getStatus() != StatusEnum.TAKE_EFFECT.getCode()) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_028);
        } else {
            List<ContractAreaVo> contractAreaList = customerOrderCacheVo.getContractAreaMap().get(importDto.getContractAreaNo());
            if (CollectionUtils.isNotEmpty(contractAreaList)) {
                 contractAreaVo = contractAreaList.get(0);
                if (!contractAreaVo.getReceiving().equals(insuranceSet.getSupplierCode())&&!insuranceSet.getSupplierCode().equals(contractAreaVo.getCustId().toString())) {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_067);
                }
            }
            List<InsuranceSetRatioVo> setRatioVoList;
            // 判断填写的人员类型编号和社保套餐人员类型编号是否一致
            if (!insuranceSet.getIndTypeCode().equals(importDto.getEmployeeTypeNo())) {
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_025);
            }

            if (customerOrderCacheVo.getInsuranceSetRatioVoMap().containsKey(setcode)) {
                setRatioVoList = customerOrderCacheVo.getInsuranceSetRatioVoMap().get(setcode);
            } else {
                setRatioVoList = insuranceSetWrapperService.findSetRatiosBySetCode(setcode, null);
                customerOrderCacheVo.getInsuranceSetRatioVoMap().put(setcode, setRatioVoList);
            }

            if (CollectionUtils.isEmpty(setRatioVoList)) {
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_049);
            } else {
                List<OrderInsuranceCfgVo> orderInsuranceCfgVos = new ArrayList<>();
                Map<Integer, List<InsuranceSetRatioVo>> getSetRatioVoListByProductCodeMap = setRatioVoList.stream().collect(Collectors.groupingBy(InsuranceSetRatioVo::getProductCode));

                // 有填写公积金企业比例则套餐内必须有公积金产品
                if (StringUtil.isNotBlank(importDto.getComRatio())&&!importDto.getAccumulationFundBase().equals("0")) {
                    List<InsuranceSetRatioVo> setRatioVos = getSetRatioVoListByProductCodeMap.get(InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex());
                    if (CollectionUtils.isEmpty(setRatioVos)) {
                        importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_059);
                    } else {
                        if (StringUtil.isBlank(importDto.getAccuAcctNo())) {
                            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_066);
                        } else {
                            employeeOrderVo.setAccuAcctNo(importDto.getAccuAcctNo());
                            setOrderInsuranceCfg(importDto, InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex(), setRatioVos, importDto.getAccumulationFundBase(), customerOrderCacheVo, orderInsuranceCfgVos);
                        }
                    }
                }

                // 有填写补充公积金企业比例则套餐内必须有补充公积金产品
                if (StringUtil.isNotBlank(importDto.getSupplementComRatio())&&!importDto.getAccumulationFundBase().equals("0")) {
                    List<InsuranceSetRatioVo> setRatioVos = getSetRatioVoListByProductCodeMap.get(InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex());
                    if (CollectionUtils.isEmpty(setRatioVos)) {
                        importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_060);
                    } else {
                        setOrderInsuranceCfg(importDto, InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex(), setRatioVos, importDto.getAccumulationFundBase(), customerOrderCacheVo, orderInsuranceCfgVos);
                    }
                }

                // 有填写社保工资则套餐内必须有社保产品
                List<Map.Entry<Integer, List<InsuranceSetRatioVo>>> setRatioEntrys = getSetRatioVoListByProductCodeMap.entrySet().stream().filter(c -> InsuranceIRatioProductCodeEnum.isSocialSecurityProd(c.getKey())).collect(Collectors.toList());
                if (StringUtil.isNotBlank(importDto.getSocialInsuranceBase())) {
                    if (CollectionUtils.isEmpty(setRatioEntrys)) {
                        importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_058);
                    } else {
                        for (Map.Entry<Integer, List<InsuranceSetRatioVo>> setRatioEntry : setRatioEntrys) {
                            setOrderInsuranceCfg(importDto, setRatioEntry.getKey(), setRatioEntry.getValue(), importDto.getSocialInsuranceBase(), customerOrderCacheVo, orderInsuranceCfgVos);
                        }
                    }
                }else {
                    if(CollectionUtils.isNotEmpty(setRatioEntrys)){
                        importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_098);
                    }
                }
                employeeOrderVo.setInsuranceSetNo(importDto.getSetCode());
                if(StringUtil.isNotBlank(importDto.getSocialInsuranceBase())){
                    employeeOrderVo.setSocailSalary(new BigDecimal(importDto.getSocialInsuranceBase()));
                }
                if(StringUtil.isNotBlank(importDto.getAccumulationFundBase())){
                    employeeOrderVo.setAcctSalary(new BigDecimal(importDto.getAccumulationFundBase()));
                }else {
                    employeeOrderVo.setAcctSalary(BigDecimal.ZERO);
                }
                employeeOrderVo.setHousehold(importDto.getHousehold());
                employeeOrderVo.setInsurances(orderInsuranceCfgVos);
            }
        }
        String quoteNo = importDto.getQuoteNo();

        String realQuoteNo = null;
        if(contractAreaVo!=null){
            //如果报价单编号填了我们判断和合同的编号是否关联否则报错
            if (StringUtils.isNotEmpty(quoteNo)) {
                //根据合同号去合同报价单关联表查询出合同下所有的报价单号,如果Excel中填写的报价单编号不属于该合同则不让增员
                List<String> quteNoList = contractRelativeQuotationServiceImpl.getQuteNoByContractAreaNo(contractAreaVo.getContractNo());
                if (quteNoList.contains(quoteNo)) {
                    realQuoteNo = quoteNo;
                } else {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_020);
                }
            } else {
                //如果是报价单编号为空那就拿合同关联报价单编号
                if (StringUtils.isNotEmpty(contractAreaVo.getQuoteNo())) {
                    realQuoteNo = contractAreaVo.getQuoteNo();
                } else {
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_019);
                }
            }
        }

        //通过报价编号获取非社保公积金
        if (realQuoteNo != null) {
            employeeOrderVo.setQuoteNo(realQuoteNo);
            serviceCharge.setQuotationNo(realQuoteNo);
            QuotationDTO quotationInfo;
            if (customerOrderCacheVo.getQuotationMap().containsKey(realQuoteNo)) {
                quotationInfo = customerOrderCacheVo.getQuotationMap().get(realQuoteNo);
            } else {
                quotationInfo = quotationResourceWrapperService.findQuotationInfo(realQuoteNo);
                customerOrderCacheVo.getQuotationMap().put(realQuoteNo, quotationInfo);
            }
            for (QuotationItemVo qi : quotationInfo.getQuotationItemList()) {
                //               外包二单独计算金额
                if (qi.getProdType()!=null && qi.getProdType() != 0 && qi.getProdType().equals(QuotationSubType.OUTSOURCING_TWO.getParentCode()) && qi.getSubType().equals(QuotationSubType.OUTSOURCING_TWO.getCode())) {
                    /** 获取到所有产品的个人金额进行计算    ps   amt * 服务比例 * (1 + 税率)  */
                    List<OrderInsuranceCfgVo> insurances = employeeOrderVo.getInsurances();
                    if (CollectionUtils.isNotEmpty(insurances)) {
                        BigDecimal allIndAmt = insurances.stream().map(OrderInsuranceCfgVo::getIndAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        allIndAmt = insurances.stream().map(OrderInsuranceCfgVo::getComAmt).reduce(allIndAmt, BigDecimal::add);
                        BigDecimal taxRatio = quotationInfo.getTaxRatio();
                        BigDecimal feeRatio = quotationInfo.getFeeRatio();
                        BigDecimal price = BigDecimal.ZERO;
                        BigDecimal taxFreeAmt = BigDecimal.ZERO;
                        BigDecimal valTax = BigDecimal.ZERO;
                        if (quotationInfo.getTaxFlag() == QuotationTaxFlag.TAX_EXCLUSIVE.getCode()) {
                            //不含
                            price = allIndAmt.multiply(feeRatio).multiply(BigDecimal.ONE.add(taxRatio)).setScale(2, RoundingMode.HALF_UP);
                            taxFreeAmt = allIndAmt.multiply(feeRatio);
                            valTax = allIndAmt.multiply(feeRatio).multiply(taxRatio);
                        } else {
                            //含
                            price = allIndAmt;
                            taxFreeAmt = allIndAmt.divide(BigDecimal.ONE.add(taxRatio));
                            valTax = allIndAmt.subtract(taxFreeAmt);
                        }
                        serviceCharge.setAmount(price);            // 含税金额
                        serviceCharge.setValTaxRate(taxRatio);    // 增值税率
                        serviceCharge.setTaxfreeAmt(taxFreeAmt);  // 不含税
                        serviceCharge.setValTax(valTax);      //增值税
                    }
                } else {
                    //计算非社保公积金服务费总金额值
                    //公式：金额（不含税）=金额 - (金额/(1+增值税率))
                    //计算非社保公积金服务费增值税值
                    //公式：(金额（不含税）*增值税率)=增值税
                    BigDecimal price = qi.getPrice();
                    BigDecimal taxRatio = quotationInfo.getTaxRatio();
                    if (quotationInfo.getTaxFlag() == QuotationTaxFlag.TAX_EXCLUSIVE.getCode()) {
                        price = price.multiply(BigDecimal.ONE.add(taxRatio)).setScale(2, RoundingMode.HALF_UP);
                    }
                    BigDecimal taxFreeAmt = price.divide(BigDecimal.ONE.add(taxRatio), 2, RoundingMode.HALF_UP);
                    BigDecimal valTax = price.subtract(taxFreeAmt);
                    serviceCharge.setAmount(price);
                    serviceCharge.setValTaxRate(taxRatio);
                    serviceCharge.setTaxfreeAmt(taxFreeAmt);
                    serviceCharge.setValTax(valTax);
                }
            }

            List<OrderServiceChargeVo> serviceCharges = employeeOrderVo.getServiceCharges();
            serviceCharges.add(serviceCharge);
//            employeeOrderVo.setServiceCharge(serviceCharge);
        }
        checkTaxRatio(employeeOrderVo,importDto);
    }

    /**
     * @param importDto            导入对象
     * @param productCode          产品code
     * @param setRatioVos          产品对应的社保套餐比例集合
     * @param base                 导入的产品基数
     * @param customerOrderCacheVo 大量数据缓存对象
     * @param orderInsuranceCfgVos 通过校验的产品集合
     */
    private void setOrderInsuranceCfg(AddEhrEmployeeImportDto importDto,
                                      Integer productCode,
                                      List<InsuranceSetRatioVo> setRatioVos,
                                      String base,
                                      CustomerOrderCacheVo customerOrderCacheVo,
                                      List<OrderInsuranceCfgVo> orderInsuranceCfgVos) {

        Integer revStartMonth = Integer.parseInt(importDto.getRevStartMonth());

        if (InsuranceIRatioProductCodeEnum.isSocialSecurityProd(productCode)) {
            // 取setRatioVos中ExpiredDate最晚的那一条
            List<InsuranceSetRatioVo> collect = setRatioVos.stream().sorted(Comparator.comparingInt(InsuranceSetRatioVo::getExpiredDate).reversed()).collect(Collectors.toList());
            InsuranceSetRatioVo insuranceSetRatioVo1 = collect.get(0);
            if (insuranceSetRatioVo1.getExpiredDate() > revStartMonth) {
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, String.format(ERROR_MESSAGE_048_RATIO_EXPIRE, insuranceSetRatioVo1.getInsuranceRatioCode()));
                return;
            }
            setRatioVos = Lists.newArrayList(insuranceSetRatioVo1);
        } else {
            setRatioVos = setRatioVos.stream().filter(insuranceSetRatioVo -> revStartMonth >= insuranceSetRatioVo.getValidDate() && revStartMonth <= insuranceSetRatioVo.getExpiredDate()).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(setRatioVos)) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, String.format(ERROR_MESSAGE_048, BillPrintConventionalCostsEnum.IsProductIndTypeEnum.getName(productCode)));
            return;
        }
        // 公积金或补充公积金的比例都不匹配
        List<InsuranceSetRatioVo> insuranceSetRatioVoList = new ArrayList<>();
        for (InsuranceSetRatioVo insuranceSetRatioVo : setRatioVos) {
            if (productCode == InsuranceIRatioProductCodeEnum.ACCUMULATION_FUND.getIndex()) {
                //判断公积金企业比例和个人比例填写错误，报错
                if (new BigDecimal(importDto.getComRatio()).compareTo(insuranceSetRatioVo.getComRatio()) != 0 || new BigDecimal(importDto.getIndRatio()).compareTo(insuranceSetRatioVo.getIndRatio()) != 0) {
                    continue;
                }
            }
            if (productCode == InsuranceIRatioProductCodeEnum.REPLENISH_ACCUMULATION_FUND.getIndex()) {
                //判断补充公积金企业比例和个人比例填写错误，报错
                if (new BigDecimal(importDto.getSupplementComRatio()).compareTo(insuranceSetRatioVo.getComRatio()) != 0 || new BigDecimal(importDto.getSupplementIndRatio()).compareTo(insuranceSetRatioVo.getIndRatio()) != 0) {
                    continue;
                }
            }
            insuranceSetRatioVoList.add(insuranceSetRatioVo);
        }
        // 判断公积金产品名称生效时间段所有的个人和企业比例是否存在，不存在报错
        // 判断补充公积金产品名称生效时间段所有的个人和企业比例是否存在，不存在报错
        if (CollectionUtils.isEmpty(insuranceSetRatioVoList)) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, String.format(ERROR_MESSAGE_046, BillPrintConventionalCostsEnum.IsProductIndTypeEnum.getName(productCode)));
        } else if (insuranceSetRatioVoList.size() > 1) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, String.format(ERROR_MESSAGE_057, BillPrintConventionalCostsEnum.IsProductIndTypeEnum.getName(productCode)));
        } else {
            InsuranceSetRatioVo insuranceSetRatioVo = insuranceSetRatioVoList.get(0);
            //判断是否在社保套餐收费日期之内
            List<InsuranceBaseVo> insuranceBaseList;
            if (customerOrderCacheVo.getInsuranceBaseVoMapTwo().containsKey(insuranceSetRatioVo.getInsuranceRatioCode())) {
                insuranceBaseList = customerOrderCacheVo.getInsuranceBaseVoMapTwo().get(insuranceSetRatioVo.getInsuranceRatioCode());
            } else {
                insuranceBaseList = baseWrapperService.findInsuranceBaseByCode(insuranceSetRatioVo.getInsuranceRatioCode());
                customerOrderCacheVo.getInsuranceBaseVoMapTwo().put(insuranceSetRatioVo.getInsuranceRatioCode(), insuranceBaseList);
            }

            //判断收费起始月在社保比例收费年月里面
            insuranceBaseList = insuranceBaseList.stream().filter(insuranceBaseVo -> revStartMonth >= insuranceBaseVo.getValidFrom() && revStartMonth <= insuranceBaseVo.getValidTo()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(insuranceBaseList)) {
                // 比例的所有基数生效时间都不匹配
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, String.format(ERROR_MESSAGE_047, BillPrintConventionalCostsEnum.IsProductIndTypeEnum.getName(productCode), insuranceSetRatioVo.getRatioName()));
            } else {
                InsuranceBaseVo insuranceBaseVo = insuranceBaseList.get(0);
                OrderInsuranceCfgVo orderInsuranceCfgVo = new OrderInsuranceCfgVo();
                orderInsuranceCfgVo.setProductCode(productCode);
                orderInsuranceCfgVo.setId(0L);
                orderInsuranceCfgVo.setRevStartMonth(revStartMonth);
                if (StringUtils.isNotBlank(importDto.getBillStartMonth())) {
                    orderInsuranceCfgVo.setBillStartMonth(Integer.parseInt(importDto.getBillStartMonth()));
                }
                orderInsuranceCfgVo.setTempletId(Long.parseLong(importDto.getTemplateId()));
                orderInsuranceCfgVo.setRevTempId(Long.parseLong(importDto.getRevTempId()));
                calculateBaseAndRatio(orderInsuranceCfgVo, insuranceSetRatioVo, insuranceBaseVo, base);
                orderInsuranceCfgVos.add(orderInsuranceCfgVo);
            }
        }
    }

    /**
     * 设置派遣、外包员工合同数据
     *
     * @param importDto       导入对象
     * @param employeeOrderVo 员工订单对象
     */
    private void setDispatchedWorkerAndOutsourcing(AddEhrEmployeeImportDto importDto, EmployeeOrderVo employeeOrderVo,Map<String, String> orgCodeMap,
                                                   List<CustomerVo> customerVoList) {
        //合同开始时间
        String startDate = importDto.getContractStartDate();
        Date startDateTime=new Date();
        if (StringUtils.isNotBlank(startDate)) {
            if (!BatchImportExcelCommonUtil.isValidDate(startDate, null)) {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_034);
            } else {
                try {
                    startDateTime = BatchImportExcelCommonUtil.getDate(startDate);
                    employeeOrderVo.setStartDate(BatchImportExcelCommonUtil.getDefautlString(startDateTime));
                    employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
                } catch (ParseException e) {
                    importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_051);
                }
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_033);
        }
        String endDate = importDto.getContractEndDate();
        if (StringUtils.isNotBlank(endDate)) {
            if (!BatchImportExcelCommonUtil.isValidDate(endDate, null)) {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_037);
            } else {
                try {
                    Date parseEndDate = BatchImportExcelCommonUtil.getDate(endDate);
                    if (startDateTime!=null&&startDateTime.getTime() > parseEndDate.getTime()) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_038);
                    }
                    employeeOrderVo.setEndDate(BatchImportExcelCommonUtil.getDefautlString(parseEndDate));
                    employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
                } catch (ParseException e) {
                    importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_052);
                }
            }
        }else if(!EmployeeContractType.OPEN_TERM_LABOR_CONTRACT.getName().equals(importDto.getEmpContractType())){
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_095);
        }
        //签署日期
        String signDate = importDto.getSignDate();
        if (StringUtils.isNotBlank(signDate)) {
            if (!BatchImportExcelCommonUtil.isValidDate(signDate, null)) {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_093);
            } else {
                try {
                    employeeOrderVo.setSignDate(BatchImportExcelCommonUtil.getDefautlString(BatchImportExcelCommonUtil.getDate(signDate)));
                } catch (ParseException e) {
                    importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_076);
                }
            }
        }
        String workMethod = importDto.getWorkMethod();
        if (StringUtils.isNotBlank(workMethod)) {
            if(EnumsUtil.containName(workMethod, WorkMethodEnum.class)){
                employeeOrderVo.setWorkMethod(EnumsUtil.getCodeByName(workMethod, WorkMethodEnum.class).toString());
            }else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_092);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_032);
        }
        //是否有试用期
        String probationFlag = importDto.getProbationFlag();
        if (StringUtils.isNotBlank(probationFlag)) {
            if(EnumsUtil.containName(probationFlag,BooleanTypeEnum.class)){
                employeeOrderVo.setProbationFlag(EnumsUtil.getCodeByName(probationFlag,BooleanTypeEnum.class));
                //有试用期才校验
                if(probationFlag.equals(BooleanTypeEnum.YES.getName())){
                    //试用期起始时间
                    String probaStart = importDto.getProbaStart();
                    Date probaStartDate =new Date();
                    if (StringUtils.isNotBlank(probaStart)) {
                        if (!BatchImportExcelCommonUtil.isValidDate(probaStart, null)) {
                            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_079);
                        } else {
                            try {
                                probaStartDate = BatchImportExcelCommonUtil.getDate(probaStart);
                                employeeOrderVo.setProbaStart(BatchImportExcelCommonUtil.getDefautlString(probaStartDate));
                            } catch (ParseException e) {
                                importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_080);
                            }
                        }
                    }else {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_081);
                    }
                    Date probaEndDate =new Date();
                    //试用期月数
                    if(StringUtils.isNotBlank(employeeOrderVo.getProbaStart())){
                        String numberoFmonthSprobation = importDto.getProbationMonths();
                        if (StringUtils.isNotBlank(numberoFmonthSprobation)) {
                            if (!BatchImportExcelCommonUtil.isNumbers(numberoFmonthSprobation)) {
                                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_036);
                            } else {
                                if (importDto.getErrorDescription().isEmpty()) {
                                    //计算试用期月数
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(BatchImportExcelCommonUtil.getAppointDate(employeeOrderVo.getProbaStart()));
                                    if (numberoFmonthSprobation.contains(".")) {
                                        numberoFmonthSprobation = numberoFmonthSprobation.substring(0, numberoFmonthSprobation.indexOf("."));
                                    }
                                    employeeOrderVo.setProbationMonths(Integer.parseInt(numberoFmonthSprobation));
                                    calendar.add(Calendar.MONTH, Integer.valueOf(numberoFmonthSprobation));
                                    Date appointDate = calendar.getTime();
                                    probaEndDate=appointDate;
                                    employeeOrderVo.setProbaEnd(BatchImportExcelCommonUtil.getDefautlString(appointDate).substring(0,10));
                                }
                            }
                        }
                    }
                    //试用期结束时间
                    String probaEnd = importDto.getProbaEnd();
                    //有试用期月数时以填写的为准
                    if (StringUtils.isNotBlank(probaEnd)) {
                        if (!BatchImportExcelCommonUtil.isValidDate(probaEnd, null)) {
                            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_082);
                        } else {
                            try {
                                probaEndDate = BatchImportExcelCommonUtil.getDate(probaEnd);
                                employeeOrderVo.setProbaEnd(BatchImportExcelCommonUtil.getDefautlString(probaEndDate));
                                if(probaStartDate!=null&&probaStartDate.getTime()>probaEndDate.getTime()){
                                    importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_094);
                                }
                            } catch (ParseException e) {
                                importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_083);
                            }
                        }
                    }else {
                        if(employeeOrderVo.getProbationMonths()==null){
                            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_035);
                        }
                    }
                    //试用工资
                    String probaSalary = importDto.getProbaSalary();
                    if (StringUtils.isNotBlank(probaSalary)) {
                        if (BatchImportExcelCommonUtil.isNumbers(probaSalary)) {
                            employeeOrderVo.setProbaSalary(new BigDecimal(probaSalary));
                        } else {
                            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_091);
                        }
                    }else {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_090);
                    }
                }
            }else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_078);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_077);
        }
        //合同版本
        String tempType = importDto.getTempType();
        if (StringUtils.isNotBlank(tempType)) {
            if (EnumsUtil.containName(tempType, TempTypeEnum.class)) {
                employeeOrderVo.setTempType(EnumsUtil.getCodeByName(tempType, TempTypeEnum.class));
            } else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_085);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_084);
        }
        //合同签订地
        String signPlace = importDto.getSignPlace();
        if (StringUtils.isNotBlank(signPlace)) {
            if (orgCodeMap.containsKey(signPlace)) {
                employeeOrderVo.setSignPlace(orgCodeMap.get(signPlace));
            } else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_087);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_086);
        }
        //合同版本地
        String tempPlace = importDto.getTempPlace();
        if (StringUtils.isNotBlank(tempPlace)) {
            if (orgCodeMap.containsKey(tempPlace)) {
                employeeOrderVo.setTempPlace(orgCodeMap.get(tempPlace));
            } else {
                importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_089);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_088);
        }
        boolean employingUnitFlag=true;
        //用工单位
        String employingUnit = importDto.getEmployingUnit();
        if (StringUtils.isNotBlank(employingUnit)) {
            for (CustomerVo customerVo:customerVoList) {
                if (customerVo.getCustName().equals(employingUnit)) {
                    employingUnitFlag=false;
                    break;
                }
            }
            if(employingUnitFlag){
                importDto.setEntryRemark(importDto.getEntryRemark() == null ? ERROR_MESSAGE_096 : importDto.getEntryRemark() + ERROR_MESSAGE_096);
            }
        }
        //合同原则
        String principle = importDto.getPrinciple();
        if (StringUtils.isNotBlank(principle)) {
            employeeOrderVo.setPrinciple(principle);
        }
        //劳动合同备注
        String remark = importDto.getRemark();
        if (StringUtils.isNotBlank(remark)) {
            employeeOrderVo.setRemark(remark);
        }
        if(StringUtils.isNotBlank(importDto.getWorkPlace())){
            employeeOrderVo.setWorkPlace(importDto.getWorkPlace());
        }else {
            importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_099);
        }
        if(StringUtils.isNotBlank(importDto.getJobPosition())){
            employeeOrderVo.setJobPosition(importDto.getJobPosition());
        }else {
            importDto.updateError(REQUIRED_FIELDS_NOT_FILLED, ERROR_MESSAGE_100);
        }
        //判断  用工单位：填写的名称不是增员时小合同的客户名称 或者不是这个客户名称的关联公司名称
        if(StringUtils.isNotBlank(importDto.getContractAreaNo())){
            ContractAreaVo contractAreaVo = contractAreaMapper.fingByContractAreaNo(importDto.getContractAreaNo());
            if(contractAreaVo != null){
                String contractAreaNo = contractAreaVo.getContractAreaNo();

                Long custId = customerMapper.getCustIdByContractNo(contractAreaVo.getContractNo());
                //关联
                List<CustomerVo> customerList = customerMapper.getCustomerListByRefCustId(custId);
                //本身
                Customer customer = customerMapper.findById(custId);

                List<String> custNameList = customerList.stream().map(CustomerVo::getCustName).filter(Objects::nonNull).collect(Collectors.toList());
                custNameList.add(customer.getCustName());

                if (custNameList.contains(importDto.getEmployingUnit())){
                    importDto.updateRemind(remindKey6,REMIND_MSG_01);
                }else {
                    importDto.updateRemind(remindKey6,REMIND_MSG_02);
                }
            }
        }
        //社保工资
        String socialInsuranceBase = importDto.getSocialInsuranceBase();
        //公积金工资
        String accumulationFundBase = importDto.getAccumulationFundBase();
        //正式工资
        String formalSalary = importDto.getFormalSalary();
        //三个 工资判断一致
        if(StringUtils.isNotBlank(socialInsuranceBase) && StringUtils.isNotBlank(accumulationFundBase) && StringUtils.isNotBlank(formalSalary)){
            if(socialInsuranceBase.equals(accumulationFundBase) && accumulationFundBase.equals(formalSalary)){
                importDto.updateRemind(remindKey8,PaymentCustomerEnum.BooleanType.Yes.getName());
            }else{
                importDto.updateRemind(remindKey8,PaymentCustomerEnum.BooleanType.NO.getName());
            }
        }else {
            importDto.updateRemind(remindKey8,"");
        }

    }
    /**
     * 设置外包员工合同数据
     *
     * @param importDto       导入对象
     * @param employeeOrderVo 员工订单对象
     */
    private void setOutsourcingWorker(AddEhrEmployeeImportDto importDto, EmployeeOrderVo employeeOrderVo) {
        //设置派遣员工
        String contractType = importDto.getEmpContractType();
        if (StringUtils.isBlank(contractType)) {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_030);
        } else if (!EnumsUtil.containName(contractType, EmployeeContractType.class)) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_031);
        } else {
            employeeOrderVo.setEmpContractType(EnumsUtil.getCodeByName(contractType, EmployeeContractType.class));
            employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
        }






        String formalSalary = importDto.getFormalSalary();
        if (StringUtils.isNotBlank(formalSalary)) {
            if (BatchImportExcelCommonUtil.isNumbers(formalSalary)) {
                employeeOrderVo.setFormalSalary(new BigDecimal(formalSalary));
                employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
            } else {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_040);
            }
        }else {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_039);
        }

        String dispatchStartDate = importDto.getDispatchStartDate();
        Date parseDispatChstart =new Date();
        if (StringUtils.isNotBlank(dispatchStartDate)) {
            if (!BatchImportExcelCommonUtil.isValidDate(dispatchStartDate, null)) {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_042);
            } else {
                try {
                    parseDispatChstart = BatchImportExcelCommonUtil.getDate(dispatchStartDate);
                    employeeOrderVo.setDispatchStart(BatchImportExcelCommonUtil.getDefautlString(parseDispatChstart));
                    employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
                } catch (ParseException e) {
                    importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_053);
                }
            }
        }

        String dispatchEndDate = importDto.getDispatchEndDate();
        if (StringUtils.isNotBlank(dispatchEndDate)) {
            if (!BatchImportExcelCommonUtil.isValidDate(dispatchEndDate, null)) {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_044);
            } else {
                try {
                    Date parseDispatChend = BatchImportExcelCommonUtil.getDate(dispatchEndDate);
                    if (parseDispatChstart!=null&&parseDispatChstart.getTime() > parseDispatChend.getTime()) {
                        importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_045);
                    }
                    employeeOrderVo.setDispatchEnd(BatchImportExcelCommonUtil.getDefautlString(parseDispatChend));
                    employeeOrderVo.setSignFlag(EmployeeOrderSignFlag.YES.getCode());
                } catch (ParseException e) {
                    importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_054);
                }
            }
        }

        // 判断是否外呼
        if (StringUtils.isNotEmpty(importDto.getCallFlag())) {
            if (importDto.getCallFlag().equals(EmployeeOrderTelFlag.YES.getName())) {
                employeeOrderVo.setCallFlag(EmployeeOrderTelFlag.YES.getCode());
            } else {
                employeeOrderVo.setCallFlag(EmployeeOrderTelFlag.NO.getCode());
            }
        }
    }

    /**
     * 设置派遣员工数据
     *
     * @param importDto       导入对象
     * @param employeeOrderVo 员工订单对象
     */
    private void setDispatchedWorker(AddEhrEmployeeImportDto importDto,
                                     EmployeeOrderVo employeeOrderVo) {
        //设置派遣员工
        String contractType = importDto.getEmpContractType();
        if (StringUtils.isBlank(contractType)) {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_030);
        } else if (!EnumsUtil.containName(contractType, EmployeeContractType.class)) {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_031);
        } else {
            employeeOrderVo.setEmpContractType(EnumsUtil.getCodeByName(contractType, EmployeeContractType.class));
        }






        String formalSalary = importDto.getFormalSalary();
        if (StringUtils.isBlank(formalSalary)) {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_039);
        } else {
            if (BatchImportExcelCommonUtil.isNumbers(formalSalary)) {
                employeeOrderVo.setFormalSalary(new BigDecimal(formalSalary));
            } else {
                importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_040);
            }
        }

        String dispatchStartDate = importDto.getDispatchStartDate();
        Date parseDispatChstart =new Date();
        if (StringUtils.isBlank(dispatchStartDate)) {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_041);
        } else if (!BatchImportExcelCommonUtil.isValidDate(dispatchStartDate, null)) {
            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_042);
        } else {
            try {
                parseDispatChstart = BatchImportExcelCommonUtil.getDate(dispatchStartDate);
                employeeOrderVo.setDispatchStart(BatchImportExcelCommonUtil.getDefautlString(parseDispatChstart));
            } catch (ParseException e) {
                importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_053);
            }
        }

        String dispatchEndDate = importDto.getDispatchEndDate();
        if (StringUtils.isBlank(dispatchEndDate)) {
            importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_043);
        } else if (!BatchImportExcelCommonUtil.isValidDate(dispatchEndDate, null)) {
            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_044);
        } else {
            try {
                Date parseDispatChend = BatchImportExcelCommonUtil.getDate(dispatchEndDate);
                if (parseDispatChstart!=null&&parseDispatChstart.getTime() > parseDispatChend.getTime()) {
                    importDto.updateError(DISOBEY_RULE, ERROR_MESSAGE_045);
                }
                employeeOrderVo.setDispatchEnd(BatchImportExcelCommonUtil.getDefautlString(parseDispatChend));
            } catch (ParseException e) {
                importDto.updateError(SYSTEM_ERROR, ERROR_MESSAGE_054);
            }
        }

        // 判断是否外呼
        if (StringUtils.isNotEmpty(importDto.getCallFlag())) {
            if (importDto.getCallFlag().equals(EmployeeOrderTelFlag.YES.getName())) {
                employeeOrderVo.setCallFlag(EmployeeOrderTelFlag.YES.getCode());
            } else {
                employeeOrderVo.setCallFlag(EmployeeOrderTelFlag.NO.getCode());
            }
        }
    }

    /**
     * 获取收费频率
     *
     * @param customerOrderCacheVo 缓存对象
     * @param templetNo            账单模板编号
     * @param defaultFlag          是否默认
     * @param serviceCharge        服务费对象
     * @param importDto            导入对象
     * @param feelNo               收费模板编号
     * @param custId               客户ID
     * @return 收费模板ID
     */
    private Long getBillTempletFeeCfgVos(CustomerOrderCacheVo customerOrderCacheVo,
                                         String templetNo,
                                         Integer defaultFlag,
                                         OrderServiceChargeVo serviceCharge,
                                         AddEhrEmployeeImportDto importDto,
                                         String feelNo,
                                         Long custId) {
        Long revTempId = 0L;
        List<BillTempletFeeCfgVo> feeHzsByTempletIds;
        if (customerOrderCacheVo.getBillTempletFeeCfgVoMap().containsKey(templetNo)) {
            feeHzsByTempletIds = customerOrderCacheVo.getBillTempletFeeCfgVoMap().get(templetNo);
        } else {
            feeHzsByTempletIds = feeCfgService.getListByTempletNo(templetNo, defaultFlag, custId);
            customerOrderCacheVo.getBillTempletFeeCfgVoMap().put(templetNo, feeHzsByTempletIds);
        }
        if (CollectionUtils.isNotEmpty(feeHzsByTempletIds)) {
            if (defaultFlag == null) {
                feeHzsByTempletIds = feeHzsByTempletIds.stream().filter(feeHzsByTempletId -> feeHzsByTempletId.getFeeNo().equals(feelNo)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(feeHzsByTempletIds)) {
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_026);
            } else {
                for (BillTempletFeeCfgVo feeHzsByTempletId : feeHzsByTempletIds) {
                    serviceCharge.setTempletId(feeHzsByTempletId.getTempletId());
                    serviceCharge.setRevTempId(feeHzsByTempletId.getId());
                    serviceCharge.setBeforeMonths(feeHzsByTempletId.getBeforeMonths());
                    serviceCharge.setReceiveMonthType(feeHzsByTempletId.getReceiveMonthType());
                    serviceCharge.setCollectFreq(feeHzsByTempletId.getCollectFreq());
                    revTempId = feeHzsByTempletId.getId();
                }
            }
        } else {
            importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_027);
        }
        return revTempId;
    }

    /**
     * 设置员工数据
     *
     * @param employeeOrderVo 员工对象
     * @param importDto       导入对象
     */
    public void setEmployeeVo(EmployeeOrderVo employeeOrderVo, AddEhrEmployeeImportDto importDto) {
        employeeOrderVo.setOrderStatus(EmployeeOrderStatus.NEED_RECEIVING_COMPLETED.getCode());
        employeeOrderVo.setCityCode(importDto.getCity());
        employeeOrderVo.setEmployeeName(importDto.getEmployeeName());
        Integer certTypeIf = BatchImportExcelCommonUtil.isCertTypeIf(importDto.getCertType());
        employeeOrderVo.setCertType(certTypeIf);
        employeeOrderVo.setCertNo(importDto.getCertNo());
        employeeOrderVo.setWorkAddr(importDto.getWorkAddr());
        employeeOrderVo.setTel(importDto.getNumber());
        employeeOrderVo.setMobile(importDto.getMobile());
        employeeOrderVo.setRemark1(importDto.getRemark1());
        employeeOrderVo.setRemark2(importDto.getRemark2());
        employeeOrderVo.setRemark3(importDto.getRemark3());
        employeeOrderVo.setRemark4(importDto.getRemark4());
        employeeOrderVo.setRemark5(importDto.getRemark5());
        // 判断入职备注
        employeeOrderVo.setEntryRemark(importDto.getEntryRemark());
        employeeOrderVo.setRemark("");
        Date entryDate = null;
        try {
            entryDate = BatchImportExcelCommonUtil.getDate(importDto.getEntryDate());
        } catch (ParseException e) {
            importDto.updateError(FORMAT_ERROR, ERROR_MESSAGE_022);
        }
        if (entryDate != null) {
            employeeOrderVo.setEntryDate(BatchImportExcelCommonUtil.getDefautlString(entryDate));
        }
    }

    /**
     * 判断所有类型的基数
     * 计算社保公积金个人金额和企业金额
     *
     * @param orderInsuranceCfgVo 产品对象
     * @param insuranceSetRatioVo 套餐比例对象
     * @param insuranceBaseVo     基数对象
     * @param base                录入基数
     */
    private void calculateBaseAndRatio(OrderInsuranceCfgVo orderInsuranceCfgVo,
                                       InsuranceSetRatioVo insuranceSetRatioVo,
                                       InsuranceBaseVo insuranceBaseVo,
                                       String base) {
        /*//判断如果公积金工资或者补充公积金基数或者社保工资填0就是0
        if (BigDecimalUtil.equalsVal(Double.parseDouble(base), BigDecimal.ZERO)) {
            //企业基数和个人基数为空则默认为0
            orderInsuranceCfgVo.setComBase(BigDecimal.ZERO);//默认企业基数
            orderInsuranceCfgVo.setIndBase(BigDecimal.ZERO);//默认个人基数
        } else {

        }*/

        //判断公积金工资或者补充公积金基数或者社保工资填写的基数大于企业最高基数，则取企业最高基数
        if (BigDecimalUtil.greaterEqualVal(Double.parseDouble(base), insuranceBaseVo.getHighBaseCom())) {
            orderInsuranceCfgVo.setComBase(insuranceBaseVo.getHighBaseCom());//企业最高基数
        } else if (BigDecimalUtil.lessEqualVal(Double.parseDouble(base), insuranceBaseVo.getLowBaseCom())) {
            //判断公积金工资或者补充公积金基数或者社保工资填写的基数小于企业最低基数，则取企业最低基数
            orderInsuranceCfgVo.setComBase(insuranceBaseVo.getLowBaseCom());//企业最低基数
        } else {
            //判断公积金工资或者补充公积金基数或者社保工资填写的基数小于企业最大基数并且小于企业最低基数，则取填写的基数
            orderInsuranceCfgVo.setComBase(new BigDecimal(base));//填写的基数
        }

        //判断公积金工资或者补充公积金基数或者社保工资填写的基数大于个人最高基数，则取个人最高基数
        if (BigDecimalUtil.greaterEqualVal(Double.parseDouble(base), insuranceBaseVo.getHighBaseInd())) {
            orderInsuranceCfgVo.setIndBase(insuranceBaseVo.getHighBaseInd());//个人最高基数
        } else if (BigDecimalUtil.lessEqualVal(Double.parseDouble(base), insuranceBaseVo.getLowBaseInd())) {
            //判断公积金工资或者补充公积金基数或者社保工资填写的基数小于个人最低基数，则取个人最低基数
            orderInsuranceCfgVo.setIndBase(insuranceBaseVo.getLowBaseInd());//个人最低基数
        } else {
            //判断公积金工资或者补充公积金基数或者社保工资填写的基数小于个人最大基数并且小于个人最低基数，则取填写的基数
            orderInsuranceCfgVo.setIndBase(new BigDecimal(base));//填写的基数
        }

//        //计算个人订单个人金额
        BigDecimal indAdd = insuranceSetRatioVo.getIndlAdd() != null && insuranceSetRatioVo.getIndlAdd().compareTo(BigDecimal.ZERO) != 0 ? insuranceSetRatioVo.getIndlAdd() : null;
//        BigDecimal indAmt = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getIndBase(), insuranceSetRatioVo.getIndRatio(), indAdd, insuranceSetRatioVo.getIndCalcMode(), insuranceSetRatioVo.getIndExactVal());
//        orderInsuranceCfgVo.setIndAmt(indAmt);
//        //计算个人订单企业金额
        BigDecimal comAdd = insuranceSetRatioVo.getComAdd() != null && insuranceSetRatioVo.getComAdd().compareTo(BigDecimal.ZERO) != 0 ? insuranceSetRatioVo.getComAdd() : null;
//        BigDecimal comAmt = CalculateUtil.calculateAmt(orderInsuranceCfgVo.getComBase(), insuranceSetRatioVo.getComRatio(), comAdd, insuranceSetRatioVo.getComCalcMode(), insuranceSetRatioVo.getComExactVal());
//        orderInsuranceCfgVo.setComAmt(comAmt);

        CalculateArgs args = new CalculateArgs();
        args.setComArgs(orderInsuranceCfgVo.getComBase(), insuranceSetRatioVo.getComRatio(), comAdd, insuranceSetRatioVo.getComCalcMode(), insuranceSetRatioVo.getComExactVal());
        args.setIndArgs(orderInsuranceCfgVo.getIndBase(), insuranceSetRatioVo.getIndRatio(), indAdd, insuranceSetRatioVo.getIndCalcMode(), insuranceSetRatioVo.getIndExactVal());
        args.setSpecialFlag(insuranceSetRatioVo.getSpecialFlag());
        CalculateUtil.calculateAmt(args);
        orderInsuranceCfgVo.setComAmt(args.getComAmt());
        orderInsuranceCfgVo.setIndAmt(args.getIndAmt());
        orderInsuranceCfgVo.setGroupRatioId(insuranceSetRatioVo.getGroupRatioId());
    }


    private void checkTaxRatio(EmployeeOrderVo employe,AddEhrEmployeeImportDto importDto) {
        if (importDto.getErrorDescription().isEmpty()) {
            Long templetId = employe.getTempletId();
            String contractNo = employe.getContractNo();
            String quoteNo = employe.getQuoteNo();
            Map<Long, CommInsurEmpVo> map;
            try {
                map = employeeOrderService.getTaxRatioByContractNoAndTemId(contractNo, Lists.newArrayList(templetId));
            } catch (Exception e) {
                importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_106);
                return;
            }
            if(map.containsKey(templetId)){
                QuotationDTO quotationInfo = quotationService.findQuotationData(quoteNo);
                BigDecimal taxRatio = quotationInfo.getTaxRatio();
                CommInsurEmpVo commInsurEmpVo = map.get(templetId);
                BigDecimal taxRatios = commInsurEmpVo.getTaxRatio();
                if(taxRatio.compareTo(taxRatios) != 0){
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_104);
                }
                String taxFlagStr = commInsurEmpVo.getTaxFlagStr();
                String[] taxFlagArr = taxFlagStr.split(",");
                /**已经存在多个含税标记 或者含税标记不同*/
                if(taxFlagArr.length > 1 || (!quotationInfo.getTaxFlag().equals(Integer.parseInt(taxFlagArr[0])))){
                    importDto.updateError(IMPORT_DATA_DIFFERENT_SYSTEM_DATA, ERROR_MESSAGE_105);
                }
            }
        }

    }
    private void setImportDto(AddEhrEmployeeImportDto importDto, EhrEmployeeOrderVo ehrEmployeeOrderVo, Map<String, InsuranceSetRatioVo> ratioVoMap){
        importDto.setEhrEmployeeOrderId(ehrEmployeeOrderVo.getId());
        if(StringUtils.isBlank(importDto.getEmployeeTypeNo())){
            importDto.setEmployeeTypeNo(ehrEmployeeOrderVo.getCategoryCode());
        }
        if(StringUtils.isBlank(importDto.getMobile())){
            importDto.setMobile(ehrEmployeeOrderVo.getMobile());
        }
        if(StringUtils.isBlank(importDto.getWorkAddr())){
            importDto.setWorkAddr(ehrEmployeeOrderVo.getWorkAddr());
        }
        if(StringUtils.isBlank(importDto.getEntryDate())){
            importDto.setEntryDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getEntryDate()));
        }
        if(StringUtils.isBlank(importDto.getRevStartMonth())){
            importDto.setRevStartMonth(ehrEmployeeOrderVo.getSocailFeeTime().toString());
        }
        if(StringUtils.isBlank(importDto.getSocialInsuranceBase())&&ehrEmployeeOrderVo.getSocailSalary()!=null){
            importDto.setSocialInsuranceBase(ehrEmployeeOrderVo.getSocailSalary().toString());
        }
        if(StringUtils.isBlank(importDto.getAccumulationFundBase())&&ehrEmployeeOrderVo.getAcctSalary()!=null){
            importDto.setAccumulationFundBase(ehrEmployeeOrderVo.getAcctSalary().toString());
        }
        if(ehrEmployeeOrderVo.getAcctRatio()!=null){
            if(ratioVoMap.containsKey(ehrEmployeeOrderVo.getAcctRatio())){
                InsuranceSetRatioVo insuranceSetRatioVo = ratioVoMap.get(ehrEmployeeOrderVo.getAcctRatio());
                if(StringUtils.isBlank(importDto.getComRatio())){
                    importDto.setComRatio(insuranceSetRatioVo.getComRatio().toString());
                }
                if(StringUtils.isBlank(importDto.getIndRatio())){
                    importDto.setIndRatio(insuranceSetRatioVo.getIndRatio().toString());
                }
            }
        }
        if(ehrEmployeeOrderVo.getSupplementAcctRatio()!=null){
            if(ratioVoMap.containsKey(ehrEmployeeOrderVo.getSupplementAcctRatio())){
                InsuranceSetRatioVo insuranceSetRatioVo = ratioVoMap.get(ehrEmployeeOrderVo.getSupplementAcctRatio());
                if(StringUtils.isBlank(importDto.getSupplementComRatio())){
                    importDto.setSupplementComRatio(insuranceSetRatioVo.getComRatio().toString());
                }
                if(StringUtils.isBlank(importDto.getSupplementIndRatio())){
                    importDto.setSupplementIndRatio(insuranceSetRatioVo.getIndRatio().toString());
                }
            }
        }
        if(ehrEmployeeOrderVo.getServiceNature()!=ServiceNature.AGENCY.getCode()){
            if(StringUtils.isBlank(importDto.getWorkMethod())){
                importDto.setWorkMethod(ehrEmployeeOrderVo.getWorkMethod());
            }
            if(StringUtils.isBlank(importDto.getEmpContractType())){
                importDto.setEmpContractType(EnumsUtil.getNameByCode(ehrEmployeeOrderVo.getEmpContractType(),EmployeeContractType.class));
            }
            if(StringUtils.isBlank(importDto.getFormalSalary())){
                importDto.setFormalSalary(ehrEmployeeOrderVo.getFormalSalary().toString());
            }
            if(StringUtils.isBlank(importDto.getSignDate())&&ehrEmployeeOrderVo.getSignDate()!=null){
                importDto.setSignDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getSignDate()));
            }
            if (StringUtils.isBlank(importDto.getContractStartDate())){
                importDto.setContractStartDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getStartDate()));
            }
            if (StringUtils.isBlank(importDto.getContractEndDate())){
                importDto.setContractEndDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getEndDate()));
            }
            if(ehrEmployeeOrderVo.getServiceNature()==ServiceNature.DISPATCH.getCode()){
                if (StringUtils.isBlank(importDto.getDispatchStartDate())){
                    importDto.setDispatchStartDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getDispatchStart()));
                }
                if(StringUtils.isBlank(importDto.getDispatchEndDate())){
                    importDto.setDispatchEndDate(DateUtil.getDefautlString(ehrEmployeeOrderVo.getDispatchEnd()));
                }
            }
            if (StringUtils.isBlank(importDto.getProbationFlag())){
                if(ehrEmployeeOrderVo.getProbaStart()==null){
                    importDto.setProbationFlag(BooleanTypeEnum.NO.getName());
                }else {
                    importDto.setProbationFlag(BooleanTypeEnum.YES.getName());
                    if(StringUtils.isBlank(importDto.getProbaStart())){
                        importDto.setProbaStart(DateUtil.getDefautlString(ehrEmployeeOrderVo.getProbaStart()));
                    }
                    if(StringUtils.isBlank(importDto.getProbationMonths())&&StringUtils.isBlank(importDto.getProbaEnd())){
                        importDto.setProbaEnd(DateUtil.getDefautlString(ehrEmployeeOrderVo.getProbaEnd()));
                    }
                    if(StringUtils.isBlank(importDto.getProbaSalary())){
                        importDto.setProbaSalary(String.valueOf(ehrEmployeeOrderVo.getProbaSalary()));
                    }
                }
            }else if(importDto.getProbationFlag().equals(BooleanTypeEnum.YES.getName())){
                if(StringUtils.isBlank(importDto.getProbaStart())){
                    Optional.ofNullable(ehrEmployeeOrderVo.getProbaStart()).ifPresent(probaStart -> importDto.setProbaStart(DateUtil.getDefautlString(probaStart)));
                }
                if(StringUtils.isBlank(importDto.getProbationMonths())&&StringUtils.isBlank(importDto.getProbaEnd())){
                    Optional.ofNullable(ehrEmployeeOrderVo.getProbaEnd()).ifPresent(probaEnd -> importDto.setProbaEnd(DateUtil.getDefautlString(probaEnd)));
                }
                if(StringUtils.isBlank(importDto.getProbaSalary())){
                    importDto.setProbaSalary(String.valueOf(ehrEmployeeOrderVo.getProbaSalary()));
                }
            }
            if(StringUtils.isBlank(importDto.getTempType())){
                importDto.setTempType(EnumsUtil.getNameByCode(ehrEmployeeOrderVo.getTempType(), TempTypeEnum.class));
            }
            if(StringUtils.isBlank(importDto.getEmployingUnit())){
                importDto.setEmployingUnit(ehrEmployeeOrderVo.getEmployingUnit());
            }
            if(StringUtils.isBlank(importDto.getWorkPlace())){
                importDto.setWorkPlace(importDto.getWorkPlace());
            }
            if(StringUtils.isBlank(importDto.getJobPosition())){
                importDto.setJobPosition(ehrEmployeeOrderVo.getJobPosition());
            }
        }
    }
}
