package com.reon.hr.sp.customer.entity.salary;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TaxComparisonFeedbackRecord {
    private Long id;
    /**
     * 工资批次明细id
     */
    private String salaryBatchDetailIds;

    private Long empId;
    /**
     * 线下数据id
     */
    private String offlineDataIds;
    /**
     * 工资比较类型
     */
    private Integer salaryComparisonType;
    /**
     * 差异
     */
    private BigDecimal difference;
    /**
     * 备注
     */
    private String remark;
    private String empExcelAdd;
    private String indTaxApplyInfo;
    private Integer taxComparisonType;
    private Integer feedbackStatus;
    private Integer mustFeedbackFlag;
    private String commissionerFeedbackRemark;
    private Date commissionerFeedbackTime;
    private String financeFeedbackRemark;
    private Date financeFeedbackTime;
    /**
     * 支付年月
     */
    private Integer paymentDate;
    /**
     * 计税月
     */
    private Integer taxMonth;
    private String withholdingAgentNo;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}
