<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>生成个人订单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td{
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }
        .layui-input{
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm" method="post">
        <table class="layui-table" lay-skin="nob" style="width: 90%">
            <tr>
                <td width="5%" align="right" style="font-weight:800">唯一号</td>
                <td width="9%"><input type="text" class="layui-input" name="employeeNo" autocomplete="off"></td>
                <td width="4%" align="right" style="font-weight:800">雇员姓名</td>
                <td width="9%"><input type="text" class="layui-input" name="employeeName" autocomplete="off"></td>
                <td width="4%" align="right" style="font-weight:800">客户编号</td>
                <td width="9%"><input type="text" class="layui-input" name="custNo" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">客户名称</td>
                <td width="10%"><input type="text" class="layui-input" name="custName" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">小合同名称</td>
                <td width="9%"><input type="text" class="layui-input" name="contractAreaName" autocomplete="off"></td>
            </tr>
            <tr>
                <td align="right" style="font-weight:800">接单方客服</td>
                <td><input type="text" class="layui-input" id="receivingMan" autocomplete="off"></td>
                    <input type="hidden" name="receivingMan" id="receivingMan2">
                <td align="right" style="font-weight:800">接单方</td>
                <td>
                    <input type="text" class="layui-input" id="receivingName" readonly>
                    <input type="hidden" class="layui-input" name="receiving" id="receiving">
                </td>
                <td align="right" style="font-weight:800">派单方</td>
                <td>
                    <input type="text" class="layui-input" id="distComName" readonly>
                    <input type="hidden" class="layui-input" name="distCom" id="distCom">
                </td>
                <td align="right" style="font-weight:800">入离职状态</td>
                <td>
                    <select class="layui-select" name="eedStatus" id="status" DICT_TYPE="IN_OUT_STATUS">
                        <option value=""></option>
                    </select>
                </td>
                <td align="right" style="font-weight:800">雇员状态</td>
                <td>
                    <select class="layui-select" name= "orderStatus" id="orderStatus" DICT_TYPE="EMP_ORDER_STATUS">
                        <option value=""></option>
                    </select>
                </td>

            </tr>
            <tr>
                <td colspan="2" align="center">
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" authURI="/customer/personOrder/getListPage">查询</button>
                    <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button></td>
            </tr>
        </table>
        <input type="hidden" id="selectedGroup">
    </form>
</blockquote>
<table class="layui-hide" id="orderGrid" lay-filter="orderFilter"></table>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/personOrder/generatePersonOrder.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/pinyin.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="addOne" lay-event="add" authURI="/customer/personOrder/gotoSavePersonOrderPage" >新增</button>
    <button class="layui-btn layui-btn-sm" id="update" lay-event="update" authURI="/customer/personOrder/gotoEditPersonOrderPage">修改</button>
    <button class="layui-btn layui-btn-sm" id="delete" lay-event="delete" authURI="/customer/personOrder/delete">删除</button>
    <button class="layui-btn layui-btn-sm" id="commit" lay-event="commit" authURI="/customer/personOrder/batchCommit">提交确认</button>
    <button class="layui-btn layui-btn-sm" id="updateCfg" lay-event="updateCfg" authURI="/customer/personOrder/updateCfgAmt">产品金额修正</button>

    <%--<button class="layui-btn layui-btn-sm" id="export" lay-event="export">导出数据</button>--%>
</script>
<script type="text/jsp" id="toolDemo">
    {{# if(d.orderStatus == 1 ){ }}
    <a href="javascript:void(0)" title="修改" lay-event="update" authURI="/customer/personOrder/gotoEditPersonOrderPage"><i class="layui-icon layui-icon-edit"></i></a>&nbsp;
    <a href="javascript:void(0)" title="删除" lay-event="delete" authURI="/customer/personOrder/delete"><i class="layui-icon layui-icon-delete"></i></a>
    {{# } }}&nbsp;
    <a href="javascript:void(0)" title="查看" lay-event="query" authURI="/customer/personOrder/gotoQueryPersonOrderPage"><i class="layui-icon layui-icon-search"></i></a>
</script>
</body>
</html>