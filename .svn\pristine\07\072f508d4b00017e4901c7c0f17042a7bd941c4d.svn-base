/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/5/10
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.modules.customer.controller.insurancePractice.societyInsurance;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.ehr.api.sys.utils.StringUtils;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.utils.ListPageUtil;
import com.reon.hr.api.base.vo.InsurancePackDetailVo;
import com.reon.hr.api.base.vo.InsurancePackVo;
import com.reon.hr.api.bill.utils.BeanUtil;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeEntryDimissionWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IOrderInsuranceCfgWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IInsurancePracticeWrapperService;
import com.reon.hr.api.customer.enums.PersonOrderEnum;
import com.reon.hr.api.customer.enums.insurancePractice.InsurancePracticeEnum;
import com.reon.hr.api.customer.utils.ExcelUtil;
import com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo;
import com.reon.hr.api.customer.vo.InsurancePracticeVo;
import com.reon.hr.api.customer.vo.ProdHandleInfoVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.customer.vo.export.InsurancePracticeStopHandleExportVo;
import com.reon.hr.api.customer.vo.salary.AnnualSalaryVo;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.core.annotation.RepeatSubmit;
import com.reon.hr.modules.common.BaseController;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SocietyInsuranceApplicationController
 *
 * @date 2021/5/10 11:56
 */
@RestController
@RequestMapping("/customer/insurancePractice")
public class SocietyInsuranceStopHandleController extends BaseController {

	@Resource(name = "insurancePracticeWrapperService")
	IInsurancePracticeWrapperService iInsurancePracticeWrapperService;

	@Autowired
	IInsurancePackResourceWrapperService iInsurancePackResourceWrapperService;

	@Resource(name = "batchImportDataDubboService")
	IBatchImportDataWrapperService iBatchImportDataService;

	@Resource
	private IEmployeeEntryDimissionWrapperService employeeEntryDimissionWrapperService;

	@Resource
	private IOrderInsuranceCfgWrapperService orderInsuranceCfgWrapperService;

	private static final Logger log = LoggerFactory.getLogger(SocietyInsuranceStopHandleController.class);

	/**
	 * @Description: 社保停办查看主页面
	 * @Author: chenxiang
	 */
	@RequestMapping(value = "/societyInsuranceStopHandle/gotoSocietyInsuranceStopHandleListPage", method = RequestMethod.GET)
	public ModelAndView gotoSocietyInsuranceQueryListPage() {
		return new ModelAndView("/customer/insurancePractice/societyInsurance/societyInsuranceStopHandle/socialSecurityStopHandleListPage");
	}




	/**
	 * @Description: 获取停办页面信息
	 * @Author: chenxiang
	 * @Params: * @param	null
	 */
	@RequestMapping(value = {"/societyInsuranceStopHandle/getSocietyInsuranceStopHandleList","/commonReserveFundStopHandle/getCommonReserveFundStopHandleList"}, method = RequestMethod.GET)
	public Object getSocietyInsuranceApplicationList(@RequestParam("limit") Integer limit, @RequestParam("page") Integer page, @RequestParam("paramData") String paramData) {
		InsurancePracticeVo insurancePracticeVo = JsonUtil.jsonToBean(paramData, InsurancePracticeVo.class);

		List<InsurancePracticeVo> insurancePracticeVoPage = iInsurancePracticeWrapperService.getSocietyInsuranceStopHandleList(limit, page, insurancePracticeVo);
		ListPageUtil<InsurancePracticeVo> pageUtil = new ListPageUtil<>(insurancePracticeVoPage, limit);
		List<InsurancePracticeVo> pagedList = pageUtil.getPagedList(page);
		return new LayuiReplay(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), insurancePracticeVoPage.size(), pagedList);
	}


	@RequestMapping(value = {"/societyInsuranceStopHandle/gotoStopHandle","/commonReserveFundStopHandle/gotoStopHandle"}, method = RequestMethod.GET)
	public ModelAndView gotoStopHandle() {
		return new ModelAndView("/customer/insurancePractice/societyInsurance/societyInsuranceStopHandle/stopHandle");
	}

	/**
	 * @Description: 社保实作查看详情页面
	 * @Author: chenxiang
	 */
	@RequestMapping(value = {"/societyInsuranceStopHandle/gotoCheckDetail","/commonReserveFundStopHandle/gotoCheckDetail"}, method = RequestMethod.GET)
	public ModelAndView gotoCheckDetail() {
		return new ModelAndView("/customer/insurancePractice/societyInsurance/societyInsuranceQuery/checkApplicationHandleDetail");
	}

	//"/commonReserveFundStopHandle/stopHandle"
	@RequestMapping(value = {"/societyInsuranceStopHandle/stopHandle"}, method = RequestMethod.POST)
	public LayuiReplay stopHandle(@RequestParam("paramData") String paramData,@RequestParam("yearMonth")String yearMonth) {
		try {
			InsurancePracticeVo insurancePracticeVoParam = JsonUtil.jsonToBean(paramData, InsurancePracticeVo.class);
			List<Long> insurancePracticeIdList = insurancePracticeVoParam.getInsurancePracticeIdList();
			String loginName = getSessionUser().getLoginName();

//			DateTimeFormatter formatter=DateTimeFormatter.ofPattern("yyyyMM");
//			LocalDateTime now = LocalDateTime.now();
//			String format = formatter.format(now);
//			int currentYearMonth = Integer.parseInt(format);
//			if (currentYearMonth > Integer.parseInt(yearMonth)){
//				return new LayuiReplay<>(ResultEnum.ERR.getCode(), "只能选择当前及以后的时间");
//			}
//			int currentMonth = now.getMonthValue();  规则更改 不用判断实操减员截点  注释为相关代码
//			int currentDay = now.getDayOfMonth();
//			int month = Integer.parseInt(yearMonth.substring(4,6));
			for (Long id : insurancePracticeIdList) {

//				InsurancePracticeVo handleStatus = iInsurancePracticeWrapperService.selectByPrimaryKey(id);

//				if (handleStatus.getHandleStatus() != InsurancePracticeEnum.HandleStatusEnum.WAIT_STOP_PAYMENT.getIndex()) {

					//停办校验  当前日期要在实操减员截点之前 ； 订单截止月晚于选择的福利截止月，不能停办
					List<ProdHandleInfoVo> prodHandleInfoData = iInsurancePracticeWrapperService.getProdHandleInfoData(id);
					List<Integer> prodCodeList = prodHandleInfoData.stream().map(ProdHandleInfoVo::getProdCode).collect(Collectors.toList());
					//同一个实做id下，订单号一致
					String orderNo = prodHandleInfoData.get(0).getOrderNo();
					List<OrderInsuranceCfgVo> orderInsuranceCfgVos = iBatchImportDataService.geExpiredMonthByOrderNo(orderNo, prodCodeList);
					//获得最大的订收费截止月
					List<OrderInsuranceCfgVo> collect = orderInsuranceCfgVos.stream().filter(v -> v.getExpiredMonth() != null).collect(Collectors.toList());
					Integer max = collect.stream().max(Comparator.comparing(OrderInsuranceCfgVo::getExpiredMonth)).get().getExpiredMonth();
					if (max != Integer.parseInt(yearMonth)) {
						return new LayuiReplay<>(ResultEnum.ERR.getCode(), "订单中产品最晚收费截止月与福利截止月不同");
					}
					// 同一个福利包下， 收费截止月相同
//				Integer expiredMonth = orderInsuranceCfgVos.get(0).getExpiredMonth();
//
//				if (expiredMonth != Integer.parseInt(yearMonth)){
//					return new LayuiReplay<>(ResultEnum.ERR.getCode(), " 福利截止月和订单收费截止月 一致才能停办");
//				}
//				InsurancePackVo insurancePackVo = iInsurancePackResourceWrapperService.getInsurancePackByPackCode(singleHandleInfo.getPackCode());
//				Integer subPoint = insurancePackVo.getSubPoint();

//				if (subPoint < currentDay && month == currentMonth){
//					return new LayuiReplay<>(ResultEnum.ERR.getCode(), "超过实操减员截点的限制，请选下个月");
//				}
//				}
				InsurancePracticeVo insurancePracticeVo = new InsurancePracticeVo();
				insurancePracticeVo.setTermiDate(new Date());
				insurancePracticeVo.setId(id);
				insurancePracticeVo.setUpdater(loginName);
				insurancePracticeVo.setTermiMan(loginName);
				insurancePracticeVo.setEndMonth(Integer.valueOf(yearMonth));
				insurancePracticeVo.setTermiRemark(insurancePracticeVoParam.getTermiRemark());
				insurancePracticeVo.setHandleStatus(InsurancePracticeEnum.HandleStatusEnum.STOP_PAYMENT.getIndex());
				insurancePracticeVo.setProdHandleInfoVoList(prodHandleInfoData);
				Integer size = iInsurancePracticeWrapperService.handleStopHandle(insurancePracticeVo);
			}
			return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
		} catch (Exception e) {
			log.info("停办失败" + "  " + e.getMessage());
			return new LayuiReplay<>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
		}
	}

	///commonReserveFundStopHandle/exportStopHandle
	@RequestMapping(value ={"/societyInsuranceStopHandle/exportStopHandle"}, method = RequestMethod.GET)
	public void exportStopHandle(String paramData, HttpServletResponse response, HttpSession httpSession) {
		InsurancePracticeVo insurancePracticeVo = JsonUtil.jsonToBean(paramData, InsurancePracticeVo.class);
		List<InsurancePracticeStopHandleExportVo> exportList = iInsurancePracticeWrapperService.getStopHandleExport(insurancePracticeVo);
		DateFormat f = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String name = "";
		String userName = getSessionUser().getUserName();
		String filePath = userName + "_" + f.format(new Date()) + "_" + name + ".xls";
		try {
			ExcelUtil<InsurancePracticeStopHandleExportVo> util = new ExcelUtil<>(InsurancePracticeStopHandleExportVo.class);
			Workbook workbook = util.exportExcel(exportList, filePath);
			ExcelUtil.closeInfo(response, workbook, filePath);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@RepeatSubmit
	@PostMapping("/changePracticeStatus")
	public LayuiReplay changePracticeStatus(@RequestBody String params){
		if(StringUtils.isBlank(params)){
			return LayuiReplay.error("请选择需要改变的数据！");
		}

		String loginName = getSessionUser().getLoginName();
		List<InsurancePracticeVo> practiceVos = JsonUtil.jsonToList(params, InsurancePracticeVo.class);
		List<Integer> status = Lists.newArrayList(InsurancePracticeEnum.HandleStatusEnum.STOP_PAYMENT.getIndex(), InsurancePracticeEnum.HandleStatusEnum.EXPIRE.getIndex());
		status.add(InsurancePracticeEnum.HandleStatusEnum.WAIT_STOP_PAYMENT.getIndex());
		String msg = "部分变更成功： ";
		InsurancePracticeVo condition;
		for (int i = 0; i < practiceVos.size(); i++) {
			InsurancePracticeVo practiceVo = practiceVos.get(i);
			String orderNo = practiceVo.getOrderNo();
			if(Objects.isNull(practiceVo.getId())){
				String text = "第["+(i+1)+"]条缺少实做Id信息，请联系管理员！";
				if(i != 0){
					text = msg+text;
				}
				return LayuiReplay.error(text);
			}
			if(Objects.isNull(practiceVo.getOrderNo())){
				String text = "第["+(i+1)+"]条缺少订单号，请联系管理员！！";
				if(i != 0){
					text = msg+text;
				}
				return LayuiReplay.error(text);
			}
			if(Objects.isNull(practiceVo.getPackCode())){
				String text = "第["+(i+1)+"]条缺少福利包，请联系管理员！！";
				if(i != 0){
					text = msg+text;
				}
				return LayuiReplay.error(text);
			}
			List<InsurancePracticeVo> selectData = iInsurancePracticeWrapperService.getBaseInsurance(null, null, Lists.newArrayList(practiceVo.getOrderNo()), practiceVo.getPackCode());
			InsurancePracticeVo currData = selectData.stream().filter(vo -> vo.getId().equals(practiceVo.getId())).findFirst().orElse(null);
			boolean anyMatch = selectData.stream().filter(vo -> !vo.getId().equals(practiceVo.getId())).anyMatch(vo -> !status.contains(vo.getHandleStatus()));
			if(anyMatch){
				String text = "第["+(i+1)+"]条已经存在没有停缴状态的数据，无需恢复！";
				if(i != 0){
					text = msg+text;
				}
				return LayuiReplay.error(text);
			}

			EmployeeEntryDimissionVo entryDimissionVo = employeeEntryDimissionWrapperService.getEmployeeEntryDimissionVoByOrderNo(orderNo);
			if(entryDimissionVo.getStatus().equals(PersonOrderEnum.EntryDimissionStatus.DIMISSION.getCode())){
				String text = "第["+(i+1)+"]条订单已经离职无需变更！";
				if(i != 0){
					text = msg+text;
				}
				return LayuiReplay.error(text);

			}
			/***
			 * 只要订单的产品存在有效的才能转为在缴
			 */
			String packCode = currData.getPackCode();
			List<InsurancePackDetailVo> detailByPackCode = iInsurancePackResourceWrapperService.getInsurancePackDetailByPackCode(packCode);
			List<Integer> prodCodes = detailByPackCode.stream().map(InsurancePackDetailVo::getProductCode).filter(Objects::nonNull).collect(Collectors.toList());
			Map<String, List<OrderInsuranceCfgVo>> cfgMapByOrderNoList = orderInsuranceCfgWrapperService.getCfgMapByOrderNoList(Lists.newArrayList(orderNo));
			List<OrderInsuranceCfgVo> insuranceCfgVos = cfgMapByOrderNoList.getOrDefault(orderNo, Lists.newArrayList());
			Integer currentYearMonth = DateUtil.getCurrentYearMonth();
			List<OrderInsuranceCfgVo> collect = insuranceCfgVos.stream().filter(vo -> {
				boolean contains = prodCodes.contains(vo.getProductCode());
				Integer revStartMonth = vo.getRevStartMonth();
				Integer expiredMonth = Optional.ofNullable(vo.getExpiredMonth()).orElse(Integer.MAX_VALUE);
				return revStartMonth <= expiredMonth && contains && currentYearMonth <= expiredMonth;
			}).collect(Collectors.toList());
			if(collect.isEmpty()){
				String text = "第["+(i+1)+"]条订单福利包的产品都没有有效区间！";
				return LayuiReplay.error(text);
			}
			condition = new InsurancePracticeVo();
			condition.setId(practiceVo.getId());
			condition.setOrderNo(currData.getOrderNo());
			Integer state = currData.getAddMonth() != null ? InsurancePracticeEnum.HandleStatusEnum.IN_PAYMENT.getIndex()
					: InsurancePracticeEnum.HandleStatusEnum.WAIT_FOR_APPLICATION.getIndex();
			condition.setHandleStatus(state);
			condition.setUpdater(loginName);
			condition.setUpdateTime(new Date());
			iInsurancePracticeWrapperService.handleMatchPrac(condition);
//			iInsurancePracticeWrapperService.applicationHandle(condition);

		}
		return LayuiReplay.success();
	}


}
