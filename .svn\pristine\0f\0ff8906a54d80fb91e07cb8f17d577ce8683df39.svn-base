package com.reon.hr.rabbitmq.enums.report;

import com.reon.hr.rabbitmq.enums.BaseProducerScopeType;
import lombok.Getter;

@Getter
public enum ProducerScopeTypeReport implements BaseProducerScopeType {
    REON_INCOME_COUNT_TABLE("income_count_table", "reon.income.count.table.completed", "开票或核销后中间表插入"),
    REON_GENERATED_BATCH_DOWNLOADS_TASK("batchDownloads", "reon.report.generated.batch.downloads.task", "生成批量下载或导出任务"),
    ;


    private String code;
    private String routingKey;
    private String name;

    ProducerScopeTypeReport(String code, String routingKey, String name) {
        this.code = code;
        this.routingKey = routingKey;
        this.name = name;
    }

    public static ProducerScopeTypeReport from(String code) {
        if (code == null) {
            return null;
        }
        ProducerScopeTypeReport[] vs = ProducerScopeTypeReport.values();
        for (ProducerScopeTypeReport e : vs) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
