package com.reon.hr.sp.customer.dao.cus;


import com.reon.hr.api.customer.vo.QuotationItemVo;
import com.reon.hr.api.customer.vo.ContractRelativeQuotationVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.sp.customer.entity.cus.ContractRelativeQuotation;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.reon.hr.sp.customer.entity.cus.Quotation;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ContractRelativeQuotationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContractRelativeQuotation record);

    int insertSelective(ContractRelativeQuotation record);

    ContractRelativeQuotation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractRelativeQuotation record);

    int updateByPrimaryKey(ContractRelativeQuotation record);

    Integer saveBindingQuotation(@Param("CRQ") ContractRelativeQuotation contractRelativeQuotation);

    Integer existData(@Param("CRQ") ContractRelativeQuotationVo item);


    Integer selectContractRelativeQuotationByContractNo(String contractNo);

    Integer updateContractRelativeQuotationByContractNo(@Param("CRQ")ContractRelativeQuotation contractRelativeQuotation);

    Integer insertContractRelativeQuotation(@Param("CRQ")ContractRelativeQuotation contractRelativeQuotation);

  List<String> getQuteNoByContractAreaNo(String contractAreaNo);

  List<QuotationItemVo> getQuteNoByOrderNo(String orderNo);

    List<String> getQuotationNoByContractNo(@Param("contractNo") String contractNo);

    List<ContractRelativeQuotationVo> selectQuoteNo();

    List<String> getQuotationNoBySalaryCategoryId(Long salaryCategoryId);
    List<SalaryInfoVo> getQuotationNoBySalaryCategoryIds(@Param("ids") List<Long> salaryCategoryId);

    List<QuotationItemVo> getQuotationItemByContractNo(String contractNo);

    List<String> getSoluIdByContractNo(String contractNo);

    List<Long> getSalaryCategoryIdByPayNo(@Param("payNos") List<String> payNos);

	List<QuotationItemVo> getQuotationListByContractNo(@Param("contractNo") String contractNo);

    List<ContractRelativeQuotation> getDataListByContract(@Param("contractNo") String contractNo);
}