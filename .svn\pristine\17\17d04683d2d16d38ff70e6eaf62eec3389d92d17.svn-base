<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>个税申报数据导入</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <%--隐藏域--%>
    <input type="hidden" id="optType" name="optType" value="${optType}">
    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="导入编号">导入编号：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="importNo" placeholder="请输入" name="importNo" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="姓名">姓名：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="staffName" name="staffName" placeholder="请输入" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="证件号">证件号：</label>
                <div class="layui-input-inline">
                    <input type="text" id="certNo" maxlength="20" name="certNo"
                           placeholder="请输入"
                           class="layui-input layui-input-disposable" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="计税月" style="font-weight:800; width:100px"><i style="color: red">*</i>计税月：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" lay-verify="required"
                           id="taxMonth" name="taxMonth" placeholder="请选择" autocomplete="off" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="扣缴义务人">扣缴义务人：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="withholdingAgentNo" id="withholdingAgentNo" lay-search>
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="所得项目">所得项目：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="itemType" id="itemType" DICT_TYPE="ITEM_TYPE" lay-search>
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <input type="hidden" id="taxComparisonType" name="taxComparisonType" value="2">
            <div class="layui-inline">
                <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                   lay-submit="">查询</a>
            </div>
        </div>
    </form>
</div>

<div class="layui-card-body">
    <table class="layui-hide" id="indTaxApplyInfoImportGrid" lay-filter="indTaxApplyInfoImportGridFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <input type="button" class="layui-btn layui-btn-sm" id="import" lay-event="import"
               authURI="/customer/salary/indTaxApplyInfoImport/gotoIndTaxApplyInfoImportFileQueryView" value="导入个税申报数据">
        <input type="button" class="layui-btn layui-btn-sm" id="check" lay-event="check"
               authURI="/customer/salary/indTaxApplyInfoImport/gotoIndTaxApplyInfoImportFileCheckView" value="查看导入记录">
        <input type="button" class="layui-btn layui-btn-sm" id="export" lay-event="export"
               authURI="/customer/salary/indTaxApplyInfoImport/exportIndTaxApplyInfoImportInfoList" value="导出数据">
    </script>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/indTaxApplyInfoImport/indTaxApplyInfoImportQuery.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/withholdingAgent/withholdingAgentNoSelect.js?v=${publishVersion}"></script>
</body>
</html>

