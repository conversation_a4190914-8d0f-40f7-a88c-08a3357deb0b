layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
  var table = layui.table,
      $ = layui.$,
      form = layui.form,
      layer = layui.layer,
      layer = parent.layer === undefined ? layui.layer : parent.layer;

  // 取消按钮
  $(document).on('click', '#cancel', function () {
    layer.closeAll('iframe');
  });
  setTimeout(function () {
    form.render('select');
  }, 100);
  // 保存按钮
  form.on("submit(save)", function (data) {
    saveForm('save', data);
    return false;
  });
  var idreg = /^(\d{15})$|^(\d{17}(?:\d|x|X))$/;
  function saveForm(type, obj) {
    if (!idreg.test(obj.field.certNo)) {
      layer.msg("身份证号不合法");
      return false;
    }
    $.ajax({
      url: ML.contextPath + "/customer/commInsurOrder/updateEmployeeCertNoById",
      type: 'POST',
      dataType: 'json',
      contentType: 'application/json',
      data: JSON.stringify(obj.field),
      success: function (result) {
        layer.msg(result.msg);
        if (result.code==0) {
          layer.closeAll('iframe');//关闭弹窗
        }
      },
      error: function () {
        layer.alert('系统发生异常，请重试！');
      }
    });
  }


});