package com.reon.hr.sp.bill.entity.bill;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DisposableItemApproval {

    private Long serialNumber;

    private Long id;

    private Long disposeId;

    private Long billId;

    private Integer status;

    private Integer prodType;

    private Integer prodKind;

    private BigDecimal amount;

    private BigDecimal taxFreeAmt;
    private BigDecimal disposableTaxRatio;  // 一次性中的税率


    private BigDecimal tax;

    private String contractNo;

    private Integer peopleNum;

    private String contractName;

    private String remark;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private BigDecimal invoicedAmt;

    private BigDecimal uninvoiceAmt;

    private BigDecimal checkedAmt;

    private BigDecimal uncheckAmt;
    /**
     供应商人数
     */
    private Integer supComNum;

    /**
     一次性支持人员
     */
    private String disSupMan;

    /**
     供应商成本
     */
    private BigDecimal supCost;
    private String fileIdList;

    private Long supplierId;
}