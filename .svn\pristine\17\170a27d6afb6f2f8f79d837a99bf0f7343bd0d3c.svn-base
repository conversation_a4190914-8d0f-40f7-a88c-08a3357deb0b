/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/1/6
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.customer.thread;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.salaryImport.ISalaryImportWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.send.IMailSendService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.send.ISalarySendQueryWrapperService;
import com.reon.hr.api.customer.utils.BatchImportExcelCommonUtil;
import com.reon.hr.api.customer.vo.salary.PayRelativeImportVo;
import com.reon.hr.api.customer.vo.salary.SendMailPublicParameterVo;
import com.reon.hr.api.customer.vo.salary.SendSalaryInfoMailVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.RecursiveAction;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalarySendQueryTask
 *
 * @date 2021/1/6 9:51
 */
public class SalarySendQueryTask extends RecursiveAction {
    private static final Logger logger = LoggerFactory.getLogger (SalarySendQueryTask.class);


    SendSalaryInfoMailVo sendSalaryInfoMailVo;
    ISalaryImportWrapperService iSalaryImportWrapperService;
    ISalarySendQueryWrapperService iSalarySendQueryWrapperService;
    IMailSendService iMailSendService;


    @Override
    protected void compute() {
        try {
            List<SendSalaryInfoMailVo> sendSalaryInfoMailVoList = JSONObject.parseArray (sendSalaryInfoMailVo.getSendSalaryInfoMailVos (), SendSalaryInfoMailVo.class);
            for (SendSalaryInfoMailVo salaryInfoMailVo : sendSalaryInfoMailVoList) {
                List<String> recipients  = Lists.newArrayList ();
                PayRelativeImportVo payRelativeImportVo = new PayRelativeImportVo ();
                BeanUtils.copyProperties (salaryInfoMailVo,payRelativeImportVo);
                List<SalaryPayVo> itemColumnSnapshotList = iSalaryImportWrapperService.getItemColumnSnapshotListFlag (payRelativeImportVo);
                salaryInfoMailVo.setSalaryPayVoList (itemColumnSnapshotList);
                List<SalaryInfoVo> salaryJsonInfoList = iSalarySendQueryWrapperService.getSalaryJsonInfoList (salaryInfoMailVo);
                BeanUtils.copyProperties (salaryInfoMailVo,sendSalaryInfoMailVo);
                recipients.add (salaryInfoMailVo.getEmail ());
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat ("yyyyMM");
                Date parseSalMonth = simpleDateFormat.parse (String.valueOf (salaryInfoMailVo.getSalMonth ()));
                SalaryInfoVo salaryInfoVo = new SalaryInfoVo ();
                AtomicInteger atomicInteger = new AtomicInteger (sendSalaryInfoMailVo.getSendCnt ());
                salaryInfoVo.setSendCnt ( atomicInteger.incrementAndGet ());
                salaryInfoVo.setId (sendSalaryInfoMailVo.getId ());
                salaryInfoVo.setSendStatus (2);
                iMailSendService.send (new SendMailPublicParameterVo (recipients,
                        BatchImportExcelCommonUtil.getString (parseSalMonth, "yyyy年MM月") + "工资条",
                        salaryInfoMailVo.getName (),salaryJsonInfoList, salaryInfoVo));
            }
        }catch (Exception e){
            e.printStackTrace ();
            logger.error ("邮件发送失败！",e.getMessage ());
        }
    }


    public SalarySendQueryTask(SendSalaryInfoMailVo sendSalaryInfoMailVo, ISalaryImportWrapperService iSalaryImportWrapperService,
                               ISalarySendQueryWrapperService iSalarySendQueryWrapperService, IMailSendService iMailSendService) {
        this.sendSalaryInfoMailVo = sendSalaryInfoMailVo;
        this.iSalaryImportWrapperService = iSalaryImportWrapperService;
        this.iSalarySendQueryWrapperService = iSalarySendQueryWrapperService;
        this.iMailSendService = iMailSendService;
    }
}
