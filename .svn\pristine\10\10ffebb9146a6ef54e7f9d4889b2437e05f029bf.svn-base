package com.reon.hr.common.qys;

import lombok.Data;

import java.io.Serializable;

/**
 <AUTHOR>
 @Description 契约锁合同列表页面
 @Date 2024年04月26日
 @Version 1.0 */
@Data
public class QysContractListResultVo implements Serializable {

    private String orderNo;

    private String subject;

    private String creator;

    private String status;


    //必须用string返回否则前端渲染不了
    private String contractId;

    /**
     业务分类名称
     */
    private String buClasName;


    private Integer eedStatus;

    private Integer dimissionReason;
    private String loginName;

    private String certNo;
    private String custName;
    private String startTime;
    private String employeeName;
    private String currentApprover;
    private String createTimeStr;
}
