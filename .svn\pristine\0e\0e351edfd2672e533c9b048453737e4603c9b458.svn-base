package com.reon.hr.sp.customer.entity.employee;

import lombok.Data;

import java.util.Date;

@Data
public class Employee {
    private Long id;

    private String employeeNo;

    private String name;

    private Integer certType;

    private String certNo;

    private String workAddr;

    private String mobile;

    private String tel;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private String email;
    /**
     工资证件类型
     */
    private Integer salaryCertType;

    /**
     国籍(地区)
     */
    private Integer nationality;

    /**
     性别
     */
    private Integer gender;

    /**
     出生日期
     */
    private Date birthDate;

    /**
     涉税事由
     */
    private Integer taxRelatedMatters;

    /**
     出生国家(地区)
     */
    private Integer birthCountry;

    /**
     首次入境时间
     */
    private Date firstEntryTime;

    /**
     预计离境时间
     */
    private Date estimatedTimeOfDeparture;
    /**
     * 民族信息
     */
    private String ethnicInformation;

    /**
     其他证件号码
     */
    private String otherCertNo;

    /**
     工资其他证件类型
     */
    private Integer salaryCertTypeSubType;
    private Integer employmentType;

    private String otherName;
    /**
     * 任职受雇从业日期
     */
    private Date employmentDate;
    /**
     * 离职日期
     */
    private Date leaveDate;
    private Integer competitionFlag;

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
}
