package com.reon.hr.sp.customer.entity.insurancePractice;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021/05/11
 */
@Data
@TableName(value="prod_handle_info")
public class ProdHandleInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 社保实做ID
     */
    @TableField(value = "practice_id")
    private Long practiceId;

    /**
     * 订单编号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 产品类型
     */
    @TableField(value = "prod_code")
    private Integer prodCode;

    /**
     * 比例编码
     */
    @TableField(value = "ratio_code")
    private String ratioCode;

    /**
     * 福利起始月
     */
    @TableField(value = "start_month")
    private Integer startMonth;

    /**
     * 福利截止月
     */
    @TableField(value = "end_month")
    private Integer endMonth;

    /**
     * 企业基数
     */
    @TableField(value = "com_base")
    private BigDecimal comBase;

    /**
     * 个人基数
     */
    @TableField(value = "ind_base")
    private BigDecimal indBase;

    /**
     * 福利办理月
     */
    @TableField(value = "return_month")
    private Integer returnMonth;

    /**
     * 企业金额
     */
    @TableField(value = "com_amt")
    private BigDecimal comAmt;

    /**
     * 个人金额
     */
    @TableField(value = "ind_amt")
    private BigDecimal indAmt;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;


}