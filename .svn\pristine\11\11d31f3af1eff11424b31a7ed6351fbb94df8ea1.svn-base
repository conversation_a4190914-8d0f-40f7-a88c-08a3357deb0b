/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2017年4月16日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.rabbitmq.enums.report;


import com.reon.hr.rabbitmq.enums.BaseConsumerScopeType;

/**
 * @title 消费渠道（获取队列的路由）
 * <AUTHOR>
 * @version 1.0
 * @created 2017年4月16日
 */
public enum ConsumerScopeTypeReport implements BaseConsumerScopeType {
	ALL("trade.*", "所有范围"),
	REON_INCOME_COUNT_TABLE("reon.income.count.table.completed", "开票或核销后中间表插入"),
	REON_GENERATED_BATCH_DOWNLOADS_TASK("reon.report.generated.batch.downloads.task", "生成批量下载或导出任务"),
	;


	private String routingKey;
	private String name;
	
	ConsumerScopeTypeReport(String routingKey, String name) {
		this.routingKey = routingKey;
		this.name = name;
	}
	
	public String getRoutingKey() {
		return routingKey;
	}

	public String getName() {
		return name;
	}
	
	public static ConsumerScopeTypeReport from(String code){
        if(code==null){
        	return null;
        }
        ConsumerScopeTypeReport[] vs= ConsumerScopeTypeReport.values();
        for (ConsumerScopeTypeReport e : vs) {
             if(e.getRoutingKey().equals(code)){
                 return e;
             }
        }
        return null;
    }
}
