package com.reon.hr.sp.report.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


public class ThreadPoolManager {
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolManager.class);

    // 核心业务线程池
    private static volatile ThreadPoolTaskExecutor coreExecutor;

    // 初始化默认配置
    static {
        initCoreExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 2048, 60, "core-business-");
    }

    /**
     初始化核心线程池
     @param coreSize         核心线程数
     @param maxSize          最大线程数
     @param queueCapacity    队列容量
     @param keepAliveSeconds 空闲线程存活时间(秒)
     @param threadPrefix     线程名前缀
     */
    public static synchronized void initCoreExecutor(int coreSize, int maxSize, int queueCapacity,
                                                     int keepAliveSeconds, String threadPrefix) {
        if (coreExecutor != null) {
            coreExecutor.shutdown();
        }

        coreExecutor = new ThreadPoolTaskExecutor();
        coreExecutor.setCorePoolSize(coreSize);
        coreExecutor.setMaxPoolSize(maxSize);
        coreExecutor.setQueueCapacity(queueCapacity);
        coreExecutor.setKeepAliveSeconds(keepAliveSeconds);
        coreExecutor.setThreadFactory(new CustomThreadFactory(threadPrefix));
        coreExecutor.setRejectedExecutionHandler(new LogPolicy());
        coreExecutor.initialize();

        // 注册监控
        registerMonitor(coreExecutor.getThreadPoolExecutor());
    }

    /**
     获取核心业务线程池
     */
    public   Executor getCoreExecutor() {
        return coreExecutor;
    }

    /**
     动态调整核心线程数
     */
    public static void setCorePoolSize(int size) {
        if (coreExecutor != null) {
            coreExecutor.setCorePoolSize(size);
        }
    }

    /**
     自定义线程工厂（含异常捕获）
     */
    private static class CustomThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        CustomThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            t.setUncaughtExceptionHandler((thread, ex) ->
                    logger.error("ThreadPool {} got exception", thread.getName(), ex)
            );
            return t;
        }
    }

    /**
     拒绝策略（记录日志+触发告警）
     */
    private static class LogPolicy implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            logger.warn("ThreadPool rejected task: {}, ActiveThreads: {}, QueueSize: {}",
                    r.getClass().getSimpleName(),
                    executor.getActiveCount(),
                    executor.getQueue().size());

            // 触发告警（示例）
            sendAlert(executor);

            if (!executor.isShutdown()) {
                r.run(); // 降级策略：由调用线程直接执行
            }
        }
    }

    /**
     注册监控（示例：定期打印状态）
     */
    private static void registerMonitor(ThreadPoolExecutor executor) {
        ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
        monitor.scheduleAtFixedRate(() -> {
            logger.info("ThreadPool Status => CoreSize: {}, Active: {}, Queue: {}, Completed: {}",
                    executor.getCorePoolSize(),
                    executor.getActiveCount(),
                    executor.getQueue().size(),
                    executor.getCompletedTaskCount());
        }, 1, 60, TimeUnit.SECONDS); // 每30秒上报
    }

    /**
     告警触发
     */
    private static void sendAlert(ThreadPoolExecutor executor) {
        // 实现邮件
        logger.error("警告,线程池触发拒绝策略, ActiveThreads: {}, QueueSize: {}", executor.getActiveCount(), executor.getQueue().size());
    }

    /** shutdown */
    public static void shutdown() {
        if (coreExecutor != null) {
            coreExecutor.shutdown();
        }
    }
}
