<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>上传修改文件</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>

</head>

<body class="childrenBody">
<div class="layui-form-item" style="margin-top: 20px">
    <label class="layui-form-label">上传文件：</label>
    <div class="layui-input-block">
        <div class="layui-upload">
            <input type="text" name="title" id="selectFile" lay-verify="title" autocomplete="off" placeholder="请选择文件"
                   class="layui-input">
        </div>
    </div>
</div>

<div style="text-align: center; margin-top: 20px">
    <button type="button" class="layui-btn" id="upload" authURL="/serviceSiteCfg/import" >开始上传</button>
<%--    <button class="layui-btn" type="button" id="cancelBtn">取消</button>--%>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js"></script>
<%--<script type="text/javascript" src="${ctx}/js/modules/serviceSiteCfg/importReceivingManView.js?v=${publishVersion}"></script>--%>
<script type="text/javascript">
    var ctx = ML.contextPath;
    layui.use(['layer', 'upload'], function () {
        var layer = parent.layer === undefined ? layui.layer : parent.layer,
            upload = layui.upload;
        upload.render({
            elem: '#selectFile'
            , accept: 'file'
            , url: ctx + '/serviceSiteCfg/import'
            , auto: false
            //,multiple: true
            , bindAction: '#upload'
            , method: 'POST'
            , exts: 'xls|xlsx'
            , before: function (obj) { //obj参数包含的信息
                ML.layuiButtonDisabled($('#upload'));// 禁用
            }, choose: function (obj) {
                //读取本地文件
                obj.preview(function (index, file, result) {
                    $("#selectFile").val(file.name);
                });
            },
            done: function (res, index, upload) {
                if (res.msg == null) {
                    layer.msg("操作成功");
                    layer.closeAll('loading');
                    layer.closeAll('iframe');
                } else {
                    layer.msg(res.msg);
                    layer.closeAll('loading');
                    layer.closeAll('iframe');
                }
                setInterval('reloadView()', 5000);
            }, error: function (index, upload) {
                ML.layuiButtonDisabled($('#upload'), true);// 取消禁用
                layer.msg("数据接口异常，导入失败！");
            }
        });

        /*$('#cancelBtn').click(function() {
            layer.closeAll('iframe');
        })*/
    })
</script>
</body>
</html>