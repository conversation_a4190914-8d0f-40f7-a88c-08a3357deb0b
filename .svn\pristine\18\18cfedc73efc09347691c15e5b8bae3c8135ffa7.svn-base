package com.reon.hr.sp.base.dubbo.rpc.sys.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.TemporaryPolicyWrapperService;
import com.reon.hr.api.base.vo.TemporaryPolicyVo;
import com.reon.hr.sp.base.service.sys.TemporaryPolicyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 临时政策(TemporaryPolicy)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-05 11:09:06
 */
@Service("temporaryPolicyWrapperService")
public class TemporaryPolicyWrapperServiceImpl implements TemporaryPolicyWrapperService {
    @Resource
    private TemporaryPolicyService temporaryPolicyService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TemporaryPolicyVo queryById(Long id) {
        return temporaryPolicyService.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param temporaryPolicy 筛选条件
     * @param page            页面
     * @param limit           限制
     * @return 查询结果
     */
    @Override
    public Page<TemporaryPolicyVo> queryByPage(Integer page, Integer limit, TemporaryPolicyVo temporaryPolicy) {
        return temporaryPolicyService.queryByPage(page, limit, temporaryPolicy);
    }
    @Override
    public Page<TemporaryPolicyVo> queryByPageForRead(Integer page, Integer limit, TemporaryPolicyVo temporaryPolicy) {
        return temporaryPolicyService.queryByPageForRead(page, limit, temporaryPolicy);
    }

    @Override
    public int insertRead(Long id, Long userId) {
        return temporaryPolicyService.insertRead(id, userId);
    }

    /**
     * 新增数据
     *
     * @param temporaryPolicyVo 实例对象
     * @return 实例对象
     */
    @Override
    public TemporaryPolicyVo insert(TemporaryPolicyVo temporaryPolicyVo) {
        temporaryPolicyService.insert(temporaryPolicyVo);
        return temporaryPolicyVo;
    }

    @Override
    public int insertBatch(List<TemporaryPolicyVo> entities) {
        return temporaryPolicyService.insertBatch(entities);
    }

    /**
     * 修改数据
     *
     * @param temporaryPolicyVo 实例对象
     * @return 实例对象
     */
    @Override
    public TemporaryPolicyVo update(TemporaryPolicyVo temporaryPolicyVo) {
        temporaryPolicyService.update(temporaryPolicyVo);
        return this.queryById(temporaryPolicyVo.getId());
    }

    @Override
    public int updateBatch(List<TemporaryPolicyVo> entities) {
        return temporaryPolicyService.updateBatch(entities);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return temporaryPolicyService.deleteById(id);
    }
}
