package com.reon.ehr.api.sys.dubbo.service.rpc;


import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.vo.SysNoticeVo;

import java.util.List;

/**
 * 公告 服务层
 * 
 * <AUTHOR>
 */
public interface ISysNoticeWrapperService
{
    /**
     * 查询公告信息
     * 
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNoticeVo selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    public List<SysNoticeVo> selectNoticeList(SysNoticeVo notice);


    /**
     * 查询公告分页列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    public Page<SysNoticeVo> getNoticeListPage(Integer pageNum,Integer pageSize,SysNoticeVo notice);

    /**
     * 新增公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    public int insertNotice(SysNoticeVo notice);

    /**
     * 修改公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    public int updateNotice(SysNoticeVo notice);

    /**
     * 删除公告信息
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId);
    
    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(Long[] noticeIds);
}
