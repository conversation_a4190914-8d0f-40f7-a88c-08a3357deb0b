package com.reon.hr.api.base.enums;

import com.google.common.collect.Sets;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Set;

public enum InsuranceRatioChargeFreq {
    MONTH_PAY(1,"月缴"),
    SEASON_PAY(2,"季缴"),
    YEAR_PAY_LACK_TAKE_YEAR(3,"年缴(不足按年)"),
    YEAR_PAY_LACK_TAKE_MONTH(4,"年缴(不足按月)");

    private int code;
    private String name;

    InsuranceRatioChargeFreq(int code,String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /** 获取所有年缴的code */
    public static final Set<Integer> yearPayCodes = Sets.newHashSet(YEAR_PAY_LACK_TAKE_YEAR.getCode(), YEAR_PAY_LACK_TAKE_MONTH.getCode());
    /** 获取是年缴还是缴的 map */
    public static HashMap<Integer, String> getChargeFreqMap() {
        HashMap<Integer, String> hashMap = new HashMap<>();
        Arrays.stream(InsuranceRatioChargeFreq.values()).forEach(vo -> {
            String paymentType = InsuranceRatioChargeFreq.yearPayCodes.contains(vo.getCode()) ? "年繳" : "月繳";
            hashMap.put(vo.getCode(), paymentType);
        });
        return hashMap;
    }

    /**
     判断传入的两个频率是否不同  也就是 年缴和月缴 相同为 true 不同为false
     * @param oldFreq
     * @param newFreq
     * @return
     */
    public static boolean judgeChargeFreq(int oldFreq,int newFreq ){
        HashMap<Integer, String> chargeFreqMap = getChargeFreqMap();
        return chargeFreqMap.get(newFreq).equals(chargeFreqMap.get(newFreq));
    }

}
