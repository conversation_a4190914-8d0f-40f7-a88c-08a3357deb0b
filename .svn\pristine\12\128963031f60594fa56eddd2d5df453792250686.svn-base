package com.reon.hr.api.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年03月21日
 * @Version 1.0
 */
@Data
public class InsurancePracticeServiceConfigLogVo implements Serializable {

    private Long id;


    /**
     * 配置表id
     */
    private Long confId;

    /**
     * 原服务费
     */
    private BigDecimal oldServiceAmt;

    /**
     * 新服务费
     */
    private BigDecimal newServiceAmt;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;
}
