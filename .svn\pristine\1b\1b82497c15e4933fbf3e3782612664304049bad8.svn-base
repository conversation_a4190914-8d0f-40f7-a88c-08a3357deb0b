layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
    // 表格数据源
    var tableData = [];

    // 分公司数据源
    var company = [];

    var tableView ='';
    // 渲染页面表格
    table.render({
        id: 'addContractAreaGrid',
        elem: '#addContractAreaTable'
        , page: false
        , limit: Number.MAX_VALUE // 分页条数根据查询回来数据变化
        , defaultToolbar: []
        , toolbar: '#topbtn'
        , data: tableData
        , cols: [[
            { type: 'checkbox', width: '50' }
            , { title: '序号', type: 'numbers', width: '50' }
            , {
                field: 'name', title: '<i style="color: red">*</i>小合同名称', align: 'center'
                , edit: 'text', width: '150'
            }
            , {
                field: 'signCom', title: '签单方', align: 'center', style: "overflow: hidden", width: '150', templet: function (d) {
                    if (!d.signCom) {
                        return "无签单方"
                    }
                    return companyCodeToName(d.signCom);
                }
            }
            , {
                field: 'distCom',
                title: '<i style="color: red">*</i>派单方',
                align: 'center',
                width: '150',
                style: "overflow: hidden",
                templet: function (d) {
                    if (!d.distCom) {
                        return "无派单方"
                    }
                    return companyCodeToName(d.distCom);
                }
            }
            , {
                field: 'receiving', title: '<i style="color: red">*</i>接单方', align: 'center'
                , style: "overflow: hidden", width: '150', event: "inputReceiving", templet: function (d) {
                    return typeof (d.receiving) == 'undefined' ? '请选择' : d.receiving;
                }
            }
            , {
                field: 'cityCode', title: '城市', align: 'center', width: '150', templet: function (d) {
                    if (d.cityCode) {
                        return ML.areaFormatter(d.cityCode);
                    }
                    return "待选择接单方";
                }
            }
            , {
                field: 'templetId', title: '<i style="color: red">*</i>客户账单模板', align: 'center'
                , templet: '#templet', width: '150'
            }
            , {
                field: 'genBillName', title: '账单方', align: 'center', style: "overflow: hidden"
                , width: '150', templet: function (d) {
                    if (d.genBillName) {
                        return d.genBillName;
                    }
                    return "待选择账单模板"
                }
            }
            , {
                field: 'receiverName',
                title: '收款方',
                align: 'center',
                width: '150',
                style: "overflow: hidden",
                templet: function (d) {
                    if (d.receiverName) {
                        return d.receiverName;
                    }
                    return "待选择账单模板"
                }
            }
            , {
                field: 'dispatchType', title: '<i style="color: red">*</i>派单类型', align: 'center', width: '150'
                , templet: '#dispatchType'
            }
            , {
                field: 'commSupervisor',
                title: '<i style="color: red">*</i>派单方客服',
                align: 'center',
                width: '150',
                templet: function (d) {
                    return ML.loginNameFormater(d.commSupervisor);
                }
            }
            , {
                field: 'revMgr',
                title: '<i style="color: red">*</i>接单方客服',
                align: 'center',
                width: '150',
                templet: function (d) {
                    return ML.loginNameFormater(d.revMgr);
                }
            }
            , { field: 'status', title: '<i style="color: red">*</i>小合同状态', align: 'center', width: '150', templet: '#status' }
            , {
                field: 'remark', title: '特殊说明', align: 'center'
                , edit: 'text', width: '150'
            }
            , {
                field: 'hasLaborContract', title: '<i style="color: red">*</i>是否需要劳动合同', align: 'center'
                , templet: '#hasLaborContract', width: '150'
            }
            , {
                field: 'templetType', title: '劳动合同版本', align: 'center'
                , templet: '#templetType', width: '150'
            }
            , {
                field: 'archiveFlag', title: '<i style="color: red">*</i>是否存档', align: 'center'
                , templet: '#archiveFlag', width: '150'
            }
            , {
                field: 'telFlag', title: '<i style="color: red">*</i>是否外呼', align: 'center'
                , templet: '#telFlag', width: '150'
            }
            , {
                field: 'dispatchMan', title: '派单人', align: 'center'
                , edit: 'text', width: '150'
            }
            , {
                field: 'attachment', title: '<i style="color: red">*</i>附件', align: 'center', event: "attachment", width: '150'
            }
            , {
                field: 'accountFlag', title: '<i style="color: red">*</i>是否单立户', align: 'center'
                , templet: '#accountFlag', width: '150'
            }
            , {
                field: 'sinAccName', title: '<i style="color: red">*</i>选中单立户', align: 'center', event: "inputAccountFlag", width: '150'
            }
            , {
                field: 'firstFlag', title: '是否优先', align: 'center'
                , templet: '#firstFlag', width: '150'
            }
            , {
                field: 'injuryFlag', title: '<i style="color: red">*</i>是否单工伤', align: 'center'
                , templet: '#injuryFlag', width: '150'
            }
            , { field: '', title: '操作', toolbar: '#btn', align: 'center', width: '150' }
        ]],
        done:function (res) {
            tableView = this.elem.next(); // 当前表格渲染之后的视图
            var contractTypeHidden = $('#contractTypeHidden').val();
            //派遣、外包1、外包2
            if(contractTypeHidden==2||contractTypeHidden==3||contractTypeHidden==4){
                var selectHasLaborContractList = document.getElementsByName("selectHasLaborContract");
                for (let i = 0; i < selectHasLaborContractList.length; i++) {
                    selectHasLaborContractList[i].options[2].selected = true;
                    selectHasLaborContractList[i].disabled=true;
                    tableData[i].hasLaborContract=selectHasLaborContractList[i].options[2].value;
                }
                form.render('select')
            }
        }
    });

    /**
     * 监听接单方弹出层
     */
    table.on("tool(addContractAreaTableFilter)", function (obj) {
        switch (obj.event) {
            case 'add':
                add();
                break;

            case 'del':
                delOne(obj.tr[0].rowIndex);
                break;
        }
        var trDom = obj.tr;
        if (obj.event == "inputReceiving") {
            $(trDom).find("[data-field=receiving] input").attr("id", "receiving");
            // console.log(tableData)
            //下拉数据表格
            $("#receiving").each(function () {
                var index = trDom[0].dataset.index;
                if (!tableData[index].distributId) {
                    layer.open({
                        type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                        title: "接单方",
                        area: ['40%', '70%'],
                        shade: 0,
                        maxmin: true,
                        offset: 'auto',
                        shade: [0.8, '#393D49'],
                        content: ML.contextPath + "/customer/contractArea/receiving",
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var parentIndex = parent.layer.getFrameIndex(window.name);
                            body.find("#parentIframeIndex").val(parentIndex);
                        },
                        end: function () {
                            if ($("#selectedGroup").val() != '') {
                                var groupObj = JSON.parse($("#selectedGroup").val());
                                tableData[index]["receiving"] = groupObj.name;
                                tableData[index]["cityCode"] = groupObj.cityCode;
                                tableData[index]["receivingId"] = groupObj.id;
                                tableData[index]["revMgr"] = groupObj.commSupervisor;

                                table.reload('addContractAreaGrid', { data: tableData });
                                var dataScroll=tableView.find('tr[data-index="'+index+'"]')
                                dataScroll[0].scrollIntoViewIfNeeded()
                            }
                        }
                    });
                }
            })
        } else if (obj.event == "inputAccountFlag") {
            let index  = Number($(trDom).get(0).getAttribute("data-index"));
            if(tableData[index].accountFlag != 2){
                return;
            }
            if(ML.isEmpty(tableData[index].cityCode)){
                layer.msg("请选择城市！")
                return;
            }
            renderSingle(index)
        } else if (obj.event == 'attachment') {
            let index  = Number($(trDom).get(0).getAttribute("data-index"));
            let row = tableData[index];

        }
    });

    function renderSingle(dataIndex) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: "单立户",
            area: ['40%', '40%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + "/prac/singleAccount/gotoSelectPracSingleAccountPage",
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                var parentIndex = parent.layer.getFrameIndex(window.name);
                body.find("#parentIframeIndex").val(parentIndex);
                var val = $('#custId').val();
                body.find("#custId").val(val);
                body.find("#cityCode").val(tableData[dataIndex].cityCode);
            },
            end: function () {
                var selectData = localStorage.getItem('data'); // 使用 localStorage 读取数据
                if(ML.isNotEmpty(selectData)){
                    let singAccount = JSON.parse(selectData);
                    tableData[dataIndex].sinAccId = singAccount.id;
                    tableData[dataIndex].sinAccName = singAccount.name;
                    table.reload('addContractAreaGrid', { data: tableData });
                    localStorage.removeItem('data');
                }
            }
        });

    }

    /*选择框的监听*/

    // 账单模板
    form.on('select(templet)', function (data) {

        var index = $(data.elem).parent().parent().parent().attr("data-index");
        var gen = $(data.elem).find("option:selected").attr("gen");
        var receiver = $(data.elem).find("option:selected").attr("receiver");
        tableData[index]['templetId'] = data.value;
        tableData[index]['genBill'] = gen;
        tableData[index]['receiverId'] = receiver;
        tableData[index]['genBillName'] = companyCodeToName(gen);
        tableData[index]['receiverName'] = companyCodeToName(receiver);
        table.reload('addContractAreaGrid', { data: tableData });
        var dataScroll=tableView.find('tr[data-index="'+index+'"]')
        dataScroll[0].scrollIntoViewIfNeeded()
    });
    // 派单类型
    form.on('select(selectDispatchType)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['dispatchType'] = data.value;

    });
    // 小合同状态
    form.on('select(selectStatus)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['status'] = data.value;

    });
    // 是否需要劳动合同
    form.on('select(selectHasLaborContract)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['hasLaborContract'] = data.value;

        form.render();
    });
    // 劳动合同版本
    form.on('select(selectTempletType)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['templetType'] = data.value;

    });
    // 是否存档
    form.on('select(selectArchiveFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['archiveFlag'] = data.value;

    });
    // 是否外呼
    form.on('select(selectTelFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['telFlag'] = data.value;

    });
    // 是否单立户
    form.on('select(selectAccountFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['accountFlag'] = data.value;
        if(data.value != 2){
          tableData[index]['sinAccName'] = null;
          tableData[index]['sinAccId'] = null;
            table.reload('addContractAreaGrid', { data: tableData });
        }

    });
    // 是否优先
    form.on('select(selectFirstFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['firstFlag'] = data.value;
    });

    // 是否单工伤
    form.on('select(selectInjuryFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        tableData[index]['injuryFlag'] = data.value;
    });

    /*监听编辑*/
    table.on('edit(addContractAreaTableFilter)', function (obj) {
        //小合同名称
        if (obj.field == 'name') {
            var index = $(obj.tr).attr("data-index");
            tableData[index][obj.field] = obj.value;
        }
        //特殊说明
        if (obj.field == 'remark') {
            var index = $(obj.tr).attr("data-index");
            tableData[index][obj.field] = obj.value;
        }
        //派单人
        if (obj.field == 'dispatchMan') {
            var index = $(obj.tr).attr("data-index");
            tableData[index][obj.field] = obj.value;
        }
    });

    //动态表格监听操作

    /*监听表格*/
    table.on('toolbar(addContractAreaTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id),
            data = checkStatus.data;
        switch (obj.event) {
            case 'addGroup':
                /*加载表格*/
                add();
                break;
            case 'delGroup':
                if (data.length === 0) {
                    layer.msg('请选择一行');
                } else {
                    del()
                }
                break;
            case 'copy':
                if (data.length != 1) {
                    layer.msg('请选择一行');
                } else {
                    var arr = table.cache.addContractAreaGrid;
                    for (var i = 0; i < arr.length; i++) {
                        if (arr[i].LAY_CHECKED === true) {
                            // 深拷贝 防止两个对象值引用同一个地址
                            var newRowData = {};
                            $.extend(true, newRowData, tableData[arr[i].LAY_TABLE_INDEX]);
                            copy(newRowData);
                            break;
                        }
                    }
                }
                break;
        }
    });

    form.on("submit(save)", function (data) {
        if (!validateAttachments()) {
            return false;
        }
        if (tableData == false) {
            return layer.msg('请添加表格内容后再提交哦！');
        }
        var addList = [];
        // 判断数据完整性
        for (var i = 0; i < tableData.length; i++) {
            if (!tableData[i].receivingId) {
                return layer.msg("请选择接单方后再保存！")
            }
            if (!tableData[i].name) {
                return layer.msg("请选择小合同名称后再保存！")
            }
            if(tableData[i].accountFlag == 2 && ML.isEmpty(tableData[i].sinAccId)){
                return layer.msg("第"+(i+1)+"行的小合同类型是单立户，但是没有选择需要绑定的单立户！");
            }
            var add = tableData[i];
            add['receiving'] = tableData[i].receivingId;
            addList.push(add);
        }
        // ML.layuiButtonDisabled($('#save'));// 禁用
        data.field['contractAreaList'] = addList;
        // console.log(data.field)
        // 发送请求
        $.ajax({
            url: ML.contextPath + "/customer/contractArea/saveContractArea",
            type: 'POST',
            dataType: 'json',
            data: { 'contractAreaJson': JSON.stringify(data.field) },
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error: function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#' + type), 'true');
            }
        });
        return false;
    });

    // 关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });

    //增加延时
    setTimeout(function () {
        add()
    }, 200);
    // 新增
    function add() {
        var defaultData = {};
        defaultData['signCom'] = $('#belongCompany').val();
        defaultData['distCom'] = $('#distCom').val();
        defaultData['contractNo'] = $('#contractNo').val();
        defaultData['commSupervisor'] = $('#commSupervisor').val();
        tableData.push(defaultData);
        table.reload('addContractAreaGrid', { data: tableData });
        form.render()
    }

    // 删除
    function del() {
        layer.confirm("你确定要删除么？", { btn: ['确定', '取消'] }, function () {
            var obj = table.cache["addContractAreaGrid"];
            for (var i = obj.length - 1; i >= 0; i--) {
                if (obj[i]['LAY_CHECKED']) {
                    obj.splice(obj[i]['LAY_TABLE_INDEX'], 1);
                }
            }
            tableData = obj;
            layer.msg("删除成功", { time: 10 }, function () {
                table.reload('addContractAreaGrid', { data: tableData });
            });
        });
    }

    // 删除
    function delOne(index) {
        layer.confirm("你确定要删除么？", { btn: ['确定', '取消'] }, function () {
            tableData.splice(index, 1);
            layer.msg("删除成功", { time: 10 }, function () {
                table.reload('addContractAreaGrid', { data: tableData });
            });
        });
    }

    // 复制
    function copy(data) {
        tableData.push(data);
        table.reload('addContractAreaGrid', { data: tableData });
    }

    // 将code转换成名字
    function companyCodeToName(code) {
        for (var i = 0; i < company.length; i++) {
            if (company[i].orgCode === code) {
                return company[i].orgName;
            }
        }
    }

    /**
     * instance n.实例 情况
     * */
    // 获取所有的分公司
    function getAllCompany() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contractArea/getCompany",
            dataType: 'json',
            success: function (data) {
                company = data;
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

    // 根据客户编号获取模板
    function findBillTempletList() {
        var contractNo = $('#contractNo').val();
        let templetType = 1;
        let contractTypeHidden = $('#contractTypeHidden').val();
        //如果没有其它情况就只显示社保账单模板
        //如果合同类型为 =9 也就是代发工资,那么就要只显示工资类型的账单模板
        if (contractTypeHidden == 9) {
            templetType = 2;
        //如果合同类型为 =6 也就是商保,那么就要只显示商保类型的账单模板
        }else if(contractTypeHidden == 6){
            templetType = 3;
        }
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contractArea/findBillTempletList",
            data: {"contractNo": contractNo, "templetType": templetType},
            dataType: 'json',
            success: function (data) {
                billTemplet = data;
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

    // 页面加载函数，在页面打开时执行
    $(document).ready(function(){
        getAllCompany();
        $('#contractType').val(ML.dictFormatter("CONTRACT_CATEGORY", $('#contractTypeHidden').val()));
        //代发工资  为9   当合同类型为代发工资时需要只显示工资账单模板
        findBillTempletList();
    });

    // 模拟接口查询是否需要特殊附件
    function checkSpecialAttachment(contractType, receivingId, cityCode) {
        return new Promise((resolve) => {
            // 模拟接口返回结果
            setTimeout(() => {
                const result = {
                    needAttachment: contractType === 9 && receivingId && cityCode, // 示例条件
                    attachmentId: null,
                    attachmentName: null
                };
                resolve(result);
            }, 500);
        });
    }



    // 上传附件
    function uploadAttachment(index) {
        layer.open({
            type: 2,
            title: "上传附件",
            area: ['40%', '40%'],
            content: ML.contextPath + "/customer/contractArea/uploadAttachment",
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                var parentIndex = parent.layer.getFrameIndex(window.name);
                body.find("#parentIframeIndex").val(parentIndex);
            },
            end: function () {
                const attachmentData = localStorage.getItem('attachment');
                if (attachmentData) {
                    const { attachmentId, attachmentName } = JSON.parse(attachmentData);
                    tableData[index].attachmentId = attachmentId;
                    tableData[index].attachmentName = attachmentName;
                    table.reload('addContractAreaGrid', { data: tableData });
                    localStorage.removeItem('attachment');
                }
            }
        });
    }

    // 下载附件
    function downloadAttachment(attachmentId) {
        window.open(`${ML.contextPath}/customer/contractArea/downloadAttachment?attachmentId=${attachmentId}`);
    }

    // 校验附件是否上传
    function validateAttachments() {
        for (let i = 0; i < tableData.length; i++) {
            if (tableData[i].needAttachment && !tableData[i].attachmentId) {
                return layer.msg(`第${i + 1}行需要上传附件，请完成后再保存！`);
            }
        }
        return true;
    }

});
//