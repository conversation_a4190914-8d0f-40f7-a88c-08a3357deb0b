package com.reon.hr.api.customer.dto.exportData;

import com.alibaba.excel.annotation.ExcelProperty;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeBankCardVo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2021/9/23.
 */
@Data
public class AddSalaryEmployeeBankCardInfoExportDto extends BaseImportDto implements Serializable {

    @ExcelProperty("员工编号（必填）")
    private String employeeNo;

    @ExcelProperty("开户人姓名（必填）")
    private String acctName;

    @ExcelProperty("证件类型（必填）")
    private String certTypeName;

    @ExcelProperty("身份证号码（必填）")
    private String certNo;

    @ExcelProperty("开户银行（选填）")
    private String bankName;

    @ExcelProperty("其他银行（选填）")
    private String otherBank;

    @ExcelProperty("开户分支行（必填）")
    private String subBank;

    @ExcelProperty("银行卡号（必填）")
    private String cardNo;

    @ExcelProperty("开户银行所在省（选填）")
    private String openProvince;

    @ExcelProperty("开户银行所在城市（必填）")
    private String openingPlace;

    @ExcelProperty("邮箱")
    private String email;

    private SalaryEmployeeBankCardVo salaryEmployeeBankCardVo;
    private String importNo;//导入编号
    private Integer certType;
    private Long empId;
}
