package com.reon.hr.sp.base.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.InsurancePracticePayBankConfigVo;
import com.reon.hr.api.base.vo.InsurancePracticePayBankDetailConfigVo;
import com.reon.hr.api.base.vo.InsurancePracticePayBankDetailRemarkVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.sp.base.dao.sys.InsurancePracticePayBankConfigMapper;
import com.reon.hr.sp.base.dao.sys.InsurancePracticePayBankDetailConfigMapper;
import com.reon.hr.sp.base.dao.sys.InsurancePracticePayBankDetailRemarkMapper;
import com.reon.hr.sp.base.service.sys.InsurancePracticePayBankConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月06日
 * @Version 1.0
 */
@Service
public class InsurancePracticePayBankConfigServiceImpl implements InsurancePracticePayBankConfigService {

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Resource
    private InsurancePracticePayBankDetailConfigMapper insurancePracticePayBankDetailConfigMapper;

    @Resource
    private InsurancePracticePayBankConfigMapper insurancePracticePayBankConfigMapper;

    @Resource
    private InsurancePracticePayBankDetailRemarkMapper insurancePracticePayBankDetailRemarkMapper;

    @Override
    public Page<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigPage(InsurancePracticePayBankConfigVo vo) {
        Page<InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigVoPage = new Page<>(vo.getPage(), vo.getLimit());
        List<InsurancePracticePayBankConfigVo> insurancePracticePayBankConfigList = insurancePracticePayBankConfigMapper.getInsurancePracticePayBankConfigList(vo, insurancePracticePayBankConfigVoPage);
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgCodeAndNameMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        insurancePracticePayBankConfigList.forEach(v -> {
            v.setOrgName(orgCodeAndNameMap.get(v.getOrgCode()));
        });
        insurancePracticePayBankConfigVoPage.setRecords(insurancePracticePayBankConfigList);
        return insurancePracticePayBankConfigVoPage;
    }

    @Override
    public int updateInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo) {
        int i = insurancePracticePayBankConfigMapper.updateInsurancePracticePayBankConfig(insurancePracticePayBankConfigVo);
        insurancePracticePayBankDetailConfigMapper.deleteInsurancePracticePayBankDetailConfigByIppbcId(insurancePracticePayBankConfigVo.getId());
        List<InsurancePracticePayBankDetailConfigVo> insurancePracticePayBankDetailConfigVoList = insurancePracticePayBankConfigVo.getInsurancePracticePayBankDetailConfigVoList();
        insurancePracticePayBankDetailConfigVoList.forEach(v -> {
            v.setIppbcId(insurancePracticePayBankConfigVo.getId());
            v.setCreator(insurancePracticePayBankConfigVo.getUpdater());
            v.setUpdater(insurancePracticePayBankConfigVo.getUpdater());
            v.setCreateTime(new Date());
            v.setUpdateTime(new Date());
            insurancePracticePayBankDetailConfigMapper.insertInsurancePracticePayBankDetailConfig(v);
        });
        return i;
    }

    @Override
    public int addInsurancePracticePayBankConfig(InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo) {
        List<OrgVo> allCompany = orgnizationResourceWrapperService.findAllCompany();
        Map<String, String> orgCodeAndNameMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        insurancePracticePayBankConfigVo.setOrgName(orgCodeAndNameMap.get(insurancePracticePayBankConfigVo.getOrgCode()));
        int i = insurancePracticePayBankConfigMapper.insertInsurancePracticePayBankConfig(insurancePracticePayBankConfigVo);
        List<InsurancePracticePayBankDetailConfigVo> insurancePracticePayBankDetailConfigVoList = insurancePracticePayBankConfigVo.getInsurancePracticePayBankDetailConfigVoList();
        insurancePracticePayBankDetailConfigVoList.forEach(v -> {
            v.setIppbcId(insurancePracticePayBankConfigVo.getId());
            v.setCreator(insurancePracticePayBankConfigVo.getCreator());
            v.setCreateTime(new Date());
            insurancePracticePayBankDetailConfigMapper.insertInsurancePracticePayBankDetailConfig(v);
        });
        return i;
    }

    @Override
    public int getInsurancePracticePayBankConfigCountByOrgCode(String orgCode) {
        return insurancePracticePayBankConfigMapper.getInsurancePracticePayBankConfigCountByOrgCode(orgCode);
    }

    @Override
    public InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigById(Long id) {
        InsurancePracticePayBankConfigVo insurancePracticePayBankConfigVo = insurancePracticePayBankConfigMapper.getInsurancePracticePayBankConfigById(id);
        List<InsurancePracticePayBankDetailConfigVo> insurancePracticePayBankDetailConfigList = insurancePracticePayBankDetailConfigMapper.getInsurancePracticePayBankDetailConfigListByIppbcId(id);
        insurancePracticePayBankConfigVo.setInsurancePracticePayBankDetailConfigVoList(insurancePracticePayBankDetailConfigList);

        return insurancePracticePayBankConfigVo;
    }

    @Override
    public List<InsurancePracticePayBankDetailConfigVo> getInsurancePracticePayBankDetailConfigByOrgCode(String orgCode) {
        return insurancePracticePayBankDetailConfigMapper.getInsurancePracticePayBankDetailConfigByOrgCode(orgCode);
    }

    @Override
    public List<InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigByOrgCodeList(List<String> orgCodeList) {
        return insurancePracticePayBankConfigMapper.getInsurancePracticePayBankConfigByOrgCodeList(orgCodeList);
    }

    @Override
    public InsurancePracticePayBankConfigVo getInsurancePracticePayBankConfigByOrgCode(String orgCode) {
        return insurancePracticePayBankConfigMapper.getInsurancePracticePayBankConfigByOrgCode(orgCode);
    }

    @Override
    public Map<String, InsurancePracticePayBankConfigVo> getInsurancePracticePayBankConfigMap() {
        List<InsurancePracticePayBankConfigVo> allInsurancePracticePayBankConfig = insurancePracticePayBankConfigMapper.getAllInsurancePracticePayBankConfig();
        return allInsurancePracticePayBankConfig.stream().collect(Collectors.toMap(InsurancePracticePayBankConfigVo::getOrgCode, Function.identity()));
    }

    @Override
    public void deleteRemarkByPayId(Long payId) {
        insurancePracticePayBankDetailRemarkMapper.deleteRemarkByPayId(payId);
    }

    @Override
    public int addInsurancePracticePayBankDetailRemarkVo(InsurancePracticePayBankDetailRemarkVo insurancePracticePayBankDetailRemarkVo) {
        return insurancePracticePayBankDetailRemarkMapper.addInsurancePracticePayBankDetailRemarkVo(insurancePracticePayBankDetailRemarkVo);
    }

    @Override
    public int updateInsurancePracticePayBankDetailRemarkVo(InsurancePracticePayBankDetailRemarkVo insurancePracticePayBankDetailRemarkVo) {
        return insurancePracticePayBankDetailRemarkMapper.updateInsurancePracticePayBankDetailRemarkVo(insurancePracticePayBankDetailRemarkVo);
    }

    @Override
    public List<InsurancePracticePayBankDetailRemarkVo> getInsurancePracticePayBankDetailRemarkVoListByIppbcIdList(List<Long> ippbcIds, Integer lockMonth) {
        return insurancePracticePayBankDetailRemarkMapper.getInsurancePracticePayBankDetailRemarkVoListByIppbcIdList(ippbcIds, lockMonth);
    }
}
