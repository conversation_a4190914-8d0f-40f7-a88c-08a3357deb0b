package com.reon.hr.sp.base.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IWelfareDownloadWrapperService;
import com.reon.hr.api.base.enums.WelfareEnum;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.sp.base.service.sys.PolicyOffSiteMedicalCareService;
import com.reon.hr.sp.base.service.sys.PolicyUnemploymentService;
import com.reon.hr.sp.base.service.sys.PolicyWorkInjuryService;
import com.reon.hr.sp.base.service.sys.WelfareDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年09月07日
 * @Version 1.0
 */
@Slf4j
@Service(value = "welfareDownloadWrapperService")
public class IWelfareDownloadWrapperServiceImpl implements IWelfareDownloadWrapperService {


    @Autowired
    private WelfareDownloadService welfareDownloadService;

    @Resource
    private PolicyWorkInjuryService policyWorkInjuryService;

    @Resource
    private PolicyUnemploymentService policyUnemploymentService;

    @Resource
    private PolicyOffSiteMedicalCareService policyOffSiteMedicalCareService;

    @Resource
    private IAreaResourceWrapperService iAreaResourceWrapperService;

    @Override
    public Page getAllWelfarePage(Integer page, Integer limit, Integer type, Integer compType, String province, String city) {
        WelfareEnum welfareEnum = WelfareEnum.getWelfareEnum(type);
        if (Objects.isNull(welfareEnum)) {
            return null;
        }
        switch (welfareEnum) {
            case WORK_INJURY:
                PolicyWorkInjuryVo policyWorkInjuryVo = assembleTheQueryObject(PolicyWorkInjuryVo.class, compType, province, city);
                return policyWorkInjuryService.queryByPage(page, limit, policyWorkInjuryVo);
            case BENEFIT:
                return welfareDownloadService.getAllWelfarePage(page, limit, compType, province, city);
            case UNEMPLOYMENT:
                PolicyUnemploymentVo policyUnemploymentVo = assembleTheQueryObject(PolicyUnemploymentVo.class, compType, province, city);
                return policyUnemploymentService.queryByPage(page, limit, policyUnemploymentVo);
            case OFF_SITE_MEDICAL_CARE:
                PolicyOffSiteMedicalCareVo policyOffSiteMedicalCareVo = assembleTheQueryObject(PolicyOffSiteMedicalCareVo.class, compType, province, city);
                return policyOffSiteMedicalCareService.queryByPage(page, limit, policyOffSiteMedicalCareVo);
            default:
                return null;
        }
    }

    @Override
    public List exportWelfare(Integer type, Integer compType, List<String> province, List<String> city) {
        WelfareEnum welfareEnum = WelfareEnum.getWelfareEnum(type);
        if (Objects.isNull(welfareEnum)) {
            return null;
        }

        List<String> sources = city;
        if (CollectionUtils.isEmpty(city)) {
            sources = province;
        }
        switch (welfareEnum) {
            case WORK_INJURY:
                return queryList(sources, PolicyWorkInjuryVo.class, compType, policyWorkInjuryService::queryList, policyWorkInjuryService::queryList);
            case BENEFIT:
//                return welfareDownloadService.exportWelfare(type, compType, province, city);
                return Lists.newArrayList();
            case UNEMPLOYMENT:
                return queryList(sources, PolicyUnemploymentVo.class, compType, policyUnemploymentService::queryList, policyUnemploymentService::queryList);
            case OFF_SITE_MEDICAL_CARE:
                return queryList(sources, PolicyOffSiteMedicalCareVo.class, compType, policyOffSiteMedicalCareService::queryList, policyOffSiteMedicalCareService::queryList);
            default:
                return null;
        }
    }

    @Override
    public List exportBenefit(Integer type, Integer compType, List<String> cityList) {
        return welfareDownloadService.exportWelfare(type, compType, cityList);
    }

    public <T extends AbstractPolicyVo> T assembleTheQueryObject(Class<T> clazz, Integer compType, String province, String city) {
        T t = getT(clazz, compType, city);
        t.setProvince(province);
        return t;
    }

    /**
     * 查询列表
     *
     * @param sources         来源(城市)
     * @param clazz           类类型
     * @param compType        公司类型
     * @param queryListByList 集合参数查询方法
     * @param queryList       单个参数查询列表
     * @return {@link List}<{@link T}>
     */
    public <T extends AbstractPolicyVo> List<T> queryList(List<String> sources, Class<T> clazz, Integer compType, Function<List<T>, List<T>> queryListByList, Function<T, List<T>> queryList) {
        List<T> apply;
        // 有城市则创建List查询参数,没有则根据公司类型创建单个对象
        if (CollectionUtils.isNotEmpty(sources)) {
            apply = queryListByList.apply(sources.stream()
                    .map(source -> getT(clazz, compType, source))
                    .collect(Collectors.toList()));
        } else {
            T t = getT(clazz, compType, null);
            apply = queryList.apply(t);
        }
        getPolicyWorkInjuryVos(apply);
        return apply;
    }

    private static <T extends AbstractPolicyVo> T getT(Class<T> clazz, Integer compType, String source) {
        T t;
        try {
            t = clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("创建对象失败", e);
            throw new RuntimeException("查询福利待遇失败");
        }
        if (!Objects.isNull(source)) {
            t.setCity(source);
        }
        if (!Objects.isNull(compType)) {
            t.setCompType(compType);
        }
        return t;
    }

    private <T extends AbstractPolicyVo> void getPolicyWorkInjuryVos(List<T> results) {
        Map<Integer, String> cityMap = iAreaResourceWrapperService.getCityCodeNameMap();
        Map<Integer, String> provinceMap = iAreaResourceWrapperService.getProvinceCodeNameMap();
        results.forEach(result -> {
            result.setCityStr(cityMap.get(Integer.parseInt(result.getCity())));
            result.setProvinceStr(provinceMap.get(Integer.parseInt(result.getProvince())));
            result.setCompTypeStr(result.getCompType() != null ?  1 == result.getCompType() ? "供应商" : "自有公司" : "");
        });
    }


    @Override
    public void saveWelfare(String paramData, Integer type) {
        welfareDownloadService.saveWelfare(paramData, type);
    }

    @Override
    public void updateWelfare(String paramData, Integer type) {
        welfareDownloadService.updateWelfare(paramData, type);
    }

    @Override
    public boolean saveOrUpdateWelfare(String paramData, Integer type) {
        WelfareEnum welfareEnum = WelfareEnum.getWelfareEnum(type);
        if (welfareEnum == null) {
            return false;
        }
        switch (welfareEnum) {
            case WORK_INJURY:
                List<PolicyWorkInjuryVo> policyWorkInjuryVos = JsonUtil.jsonToList(paramData, PolicyWorkInjuryVo.class);
                if (CollectionUtils.isEmpty(policyWorkInjuryVos)) {
                    return false;
                }
                // 有id则为更新,没有则为新增
                if (policyWorkInjuryVos.get(0).getId() == null) {
                    policyWorkInjuryService.insertBatch(policyWorkInjuryVos);
                } else {
                    policyWorkInjuryService.updateBatch(policyWorkInjuryVos);
                }
                break;
            case BENEFIT:
                BirthBenefitVo birthBenefitVo = JsonUtil.jsonToBean(paramData, BirthBenefitVo.class);
                welfareDownloadService.saveBirthBenefitVo(birthBenefitVo);
            case UNEMPLOYMENT:
                List<PolicyUnemploymentVo> policyUnemploymentVos = JsonUtil.jsonToList(paramData, PolicyUnemploymentVo.class);
                if (CollectionUtils.isEmpty(policyUnemploymentVos)) {
                    return false;
                }
                // 有id则为更新,没有则为新增
                if (policyUnemploymentVos.get(0).getId() == null) {
                    policyUnemploymentService.insertBatch(policyUnemploymentVos);
                } else {
                    policyUnemploymentService.updateBatch(policyUnemploymentVos);
                }
                break;
            case OFF_SITE_MEDICAL_CARE:
                List<PolicyOffSiteMedicalCareVo> policyOffSiteMedicalCareVos = JsonUtil.jsonToList(paramData, PolicyOffSiteMedicalCareVo.class);
                if (CollectionUtils.isEmpty(policyOffSiteMedicalCareVos)) {
                    return false;
                }
                // 有id则为更新,没有则为新增
                if (policyOffSiteMedicalCareVos.get(0).getId() == null) {
                    policyOffSiteMedicalCareService.insertBatch(policyOffSiteMedicalCareVos);
                } else {
                    policyOffSiteMedicalCareService.updateBatch(policyOffSiteMedicalCareVos);
                }
                break;
            default:
        }
        return true;
    }

    @Override
    public WorkInjuryBenefitVo getWorkInjuryBenefitVoById(Long id) {
        return welfareDownloadService.getWorkInjuryBenefitVoById(id);
    }

    @Override
    public BirthBenefitVo getBirthBenefitVoById(Long id) {
        return welfareDownloadService.getBirthBenefitVoById(id);
    }


}
