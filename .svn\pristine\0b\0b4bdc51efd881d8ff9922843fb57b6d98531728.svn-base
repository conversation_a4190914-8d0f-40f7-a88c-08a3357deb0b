<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>
    <style type="text/css">
        .notNewline {
            white-space: nowrap;
            width: 100px;
        }

        .t .layui-input {
            padding-right: 30px !important;
        }

        .t1 {
            margin-left: 40px !important;
        }

        .layui-form {
            margin-top: 20px;
        !important;
        }
    </style>

</head>
<body class="childrenBody">
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form" id="searchForm">
            <input id="selectedGroup" type="hidden" value=""/>
            <div class="layui-form-item">
                <div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="发票开具公司:">发票开具公司：</label>
                        <div class="layui-input-inline t">
                            <select name="invoiceOrgCode" id="invoiceOrgCode" class="signDistCom"
                                    lay-filter="invoiceOrgCode" lay-search>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="提票日期起"><a style="color: red">*</a>提票日期起：</label>
                        <div class="layui-input-inline t">
                            <input class="layui-input buildInvoiceStartTime" type="text" id="buildInvoiceStartTime"
                                   name="voucherDateS"
                                   placeholder="年-月-日" readonly="" lay-key="1">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="开票日期起"><a style="color: red">*</a>开票日期起：</label>
                        <div class="layui-input-inline t">
                            <input class="layui-input actualInvoiceStartTime" type="text" id="actualInvoiceStartTime"
                                   name="actualInvoiceStartTime"
                                   placeholder="年-月-日" readonly="" lay-key="2">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline"
                               title="签约日期起始日期-起">签约日期起始日期-起：</label>
                        <div class="layui-input-inline t1">
                            <input class="layui-input signStartTime" type="text" id="signStartTime" name="startDate"
                                   placeholder="年-月-日" readonly="" lay-key="3">
                        </div>
                    </div>
                </div>

                <div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="销售所在公司:">销售所在公司：</label>
                        <div class="layui-input-inline t">
                            <select name="signCom" id="signCom" class="signDistCom" lay-filter="signCom" lay-search>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="提票日期止"><a style="color: red">*</a>提票日期止：</label>
                        <div class="layui-input-inline t">
                            <input class="layui-input buildInvoiceEndTime" type="text" id="buildInvoiceEndTime"
                                   name="voucherDateE"
                                   placeholder="年-月-日" readonly="" lay-key="4">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="开票日期止"><a style="color: red">*</a>开票日期止：</label>
                        <div class="layui-input-inline t">
                            <input class="layui-input actualInvoiceEndTime" type="text" id="actualInvoiceEndTime"
                                   name="actualInvoiceEndTime"
                                   placeholder="年-月-日" readonly="" lay-key="5">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline"
                               title="签约日期起始日期-止">签约日期起始日期-止：</label>
                        <div class="layui-input-inline t1">
                            <input class="layui-input signEndTime" type="text" id="signEndTime" name="endDate"
                                   placeholder="年-月-日" readonly="" lay-key="6">
                        </div>
                    </div>
                </div>
                <div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="销售所在城市">销售所在城市：</label>
                        <div class="layui-input-inline t">
                            <select name="cityCode" id="cityCode" lay-search="" AREA_TYPE>
                                <option value=""></option>
                            </select>
                        </div>
                        </td>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="产品类型">产品类型：</label>
                        <div class="layui-input-inline t">
                            <select name="contractType" DICT_TYPE="CONTRACT_CATEGORY"
                                    lay-verType="tips" id="contractType" lay-filter="contractTypeFilter">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="客户名称">客户名称：</label>
                        <div class="layui-input-inline t">
                            <input type="text" id="custName" placeholder="请选择" readonly
                                   autocomplete="off"
                                   class="layui-input"/>
                            <input type="text" name="custId" id="custId" style="display: none;"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label notNewline" title="签约部门:">签约部门：</label>
                        <div class="layui-input-inline t">
                            <select name="sellerPos" id="sellerPos" lay-filter="sellerPos" lay-search>
                                <option value="">请选择</option>
                                <option value="1">销售部</option>
                                <option value="2">客服部</option>
                                <option value="7">人事部</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="entryExportFilter">导出
                        </button>
                        <button class="layui-btn layuiadmin-btn-list" lay-submit
                                lay-filter="specialExportByCustomerService"
                                authURI="/report/specialExportByCustomerService">客服特殊导出
                        </button>
                        <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">重置</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript"
        src="${ctx}/js/modules/customer/invoice/customentLinkage.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/report/invoiceDetailsReport.js?v=${publishVersion}"></script>
</body>
</html>