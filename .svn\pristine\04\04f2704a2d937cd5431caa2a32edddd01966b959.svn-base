package com.reon.hr.api.customer.vo.supplier;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年03月26日
 * @Version 1.0
 */
@Data
public class SupplierOneFeeVo implements Serializable {

    private Long custId;

    private Integer billType;

    private String contractNo;

    private Long supplierId;

    private String contractName;

    private String supplierName;

    private String submitCom;

    private String creator;


    private String custName;

    private String custNo;

    private String templetName;

    private Integer billMonth;

    private Integer receivableMonth;

    private Integer prodType;

    private Integer prodKind;

    private BigDecimal receiveAmt;

    private Integer serviceNum;

    private Integer supComNum;

    private BigDecimal supCost;

    private String disSupMan;

    private String remark;

    private Integer cancelStatus;

    /**
     * 含税单价
     */
    private BigDecimal price;

    private Integer num;


    private BigDecimal disposableTaxRatio;

    private BigDecimal tax;

    private Long oneId;
    
}
