package com.reon.hr.sp.customer.service.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.employee.EmployeeOrderDto;
import com.reon.hr.api.customer.dto.employee.PersonOrderQueryDto;
import com.reon.hr.api.customer.dto.exportData.ServiceNumPeopleReportDto;
import com.reon.hr.api.customer.dto.qys.QysEmpConCusAndEedDto;
import com.reon.hr.api.customer.dto.report.EmployeeReportDto;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.employee.*;
import com.reon.hr.sp.customer.entity.employee.Employee;
import com.reon.hr.sp.customer.entity.employee.EmployeeOrder;
import com.reon.hr.sp.customer.entity.employee.EmployeeRemark;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface
IEmployeeOrderService {
    String getOrderNo(String areaNo, String employeeId);
    Page<EmployeeOrderViewVo> getListPage(Integer page, Integer limit, EmployeeOrderVo orderVo);
    EmployeeOrderVo getCommissionerAndReceivingManByOrderNo(String orderNo);
    Long selectCountByOrderNo(String orderNo);

    List<String> getAllOrderNoByEmpId(Long empId);
    List<EmployeeOrderVo> getRemark1();

    EmployeeOrderVo getOneById(String... orderNo);
    EmployeeOrderVo getOneConfirmGetOneById(String orderNo);

    /**
     * 获得员工数据为垫付
     *
     * @param areaNoList 区域没有列表
     * @return {@link EmployeeOrderVo}
     */
    List<EmployeeOrderVo> getEmpDataForAdvancePayment(List<String> areaNoList);

    List<EmployeeOrderVo> getOneByOrderNo(String orderNo, String certNo, String receivingName, String custNo);

    void saveOrUpdate(EmployeeOrderVo orderVo);
    void innerRolledBackSaveOrUpdate(EmployeeOrderVo orderVo);
    String findCertNameByCertNo(String certNo);
    void deleteByOrderNos(List<String> orderNos, String updater);

    List<EmployeeOrderDto> getChangeTempleteList(ChangeTempletFindParaVo changeTempletFindParaVo);

    Page<CompleteOrderViewVo> getCompleteOrderListPage(Integer page, Integer limit, CompleteOrderViewVo orderViewVo);

    List<CompleteOrderViewVo> getCompleteOrderListWorkFow(CompleteOrderViewVo orderViewVo);


    boolean updateStatusByOrderNo(String orderNo, Integer status, String rejectReason, String updater);

    /**
     * 批量修改订单状态：完善订单/确认订单/接单确认离职/派单确认离职
     *
     * @param orderNos
     * @param status
     * @param updater
     * @throws Exception
     */
    void updateStatusBatch(List<String> orderNos, Integer status, String updater);

    int getUseQuotationEmpOrderCount(String quotationNo);

    void updateSuspendOrder(List<String> orderNo, Integer suspendReason,  String suspendRemark, String updater);

    Set<Integer> getEmployeeStatus(String certNo);

    /**
     * 批量申请员工离职
     *
     * @param orderViewVos
     * @param updater
     * @return
     * @throws Exception
     */
    int updateBatchEmployeeLeave(List<CompleteOrderViewVo> orderViewVos, String updater);

    /**
     * 根据订单编号 和 组比例id 批量修改账单模板
     *
     * @return
     * @throws Exception
     */
    int updateBatchByOrderNoAndGroupRatioId(ChangeTempletInfoVo changeTempletInfoVo, String updater, Map<String, Long> orderNoAndCtoIdMap);

    Page<CompleteOrderViewVo> getLeaveDataListPage(Integer page, Integer limit, CompleteOrderViewVo completeOrderViewVo);
    void updateConfirmLeave(CompleteOrderViewVo orderViewVo);

    void updateByOrderNo(PersonOrderQueryVo vo);

    /**
     * 批量修改员工订单信息
     * @param employeeOrders 员工订单信息列表
     */
    void updateByOrderNoList(List<EmployeeOrder> employeeOrders);

    /**
     * 根据订单编号批量查询员工摘要
     * @param orderNoList 订单编号列表
     * @return List<EmployeeRemark>
     */
    List<EmployeeRemark> getEmployeeRemarkList(List<String> orderNoList);

    /**
     * 判断修改还是新增
     * @param vos 原数据
     * @param employeeRemarks 查到的数据库中数据
     * @param employeeRemarkList 初始化的雇员摘要信息
     * @param type 1代表修改,2代表新增
     */
    void insOrUpByRemark(List<PersonOrderQueryVo> vos,List<EmployeeRemark> employeeRemarks,List<EmployeeRemark> employeeRemarkList,String type);
    //void insOrUpByRemark(PersonOrderQueryVo vo,EmployeeRemark employeeRemark,EmployeeRemark remark,List<EmployeeRemark> remarkUp,List<EmployeeRemark> remarkIns);

    /**
     * 批量修改
     * @param remarkUp arg
     */
    void updateRemark(List<EmployeeRemark> remarkUp);

    /**
     * 批量添加
     * @param remarkIns arg
     */
    void insertRemark(List<EmployeeRemark> remarkIns);

    Object searchEmployeeByOrderNo(List<String> orderNoList);

    List<PersonOrderEditVo> getEmployeeByOrderNo(List<String> orderNoList);

    List<PerInsuranceBillVo> getListByCustIdAndTempletId(String contractNo, Long templetId, Set<String> orderNosFromChangeTempletInfo);


    //int batchUpdateRevCsByContractAreaNo(String revCs, String contractAreaNo);

    // 保存变更日志
    void saveEmployeeOrderLog(String loginName, String orderNo, Long employeeId, Integer status, String remark);

    /**
     * 查询离职报表
     *
     * @param applyDateS
     * @param applyDateE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @param commissioner
     * @param accountFlag
     * @param groupName
     * @param userOrgPositionDtoList
     * @return
     */
    List<EmployeeReportDto> getDimReportByApplyDimDateAndCust(String applyDateS, String applyDateE, String custName, Integer distPlace, Integer cityCode, String receiving,String commissioner,Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 查询入职报表
     *
     * @param applyEntryDateS
     * @param applyEntryDateE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @return
     */
    List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving);

    /**
     * 查询入职报表
     *
     * @param applyEntryDateS
     * @param applyEntryDateE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @param commissioner
     * @param accountFlag
     * @param groupName
     * @param userOrgPositionDtoList
     * @return
     */
    List<EmployeeReportDto> getEntryReportByApplyEntryDateAndCust1(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving, String commissioner, Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList);

    List<OrderInsuranceCfgVo> getComAmtAndIndAmt(String contractNo);

    /**
     * 汇总查询订单数据
     * @param page
     * @param limit
     * @param customerId 客户编号
     * @param signCom 签单分公司
     * @return
     */
    Page<SummaryOrderVo> summaryOrder(Integer page, Integer limit, String customerId, String signCom);

    /**
     * 根据条件检索员工数据
     */
    List<CollectQueryEmployeeVo> collectQueryEmployeeInfo(CollectQueryEmployeeVo collectQueryEmployeeVo, List<String> groupRatioIdList);

    boolean updateByPrimaryKeySelective(List<EmployeeOrderVo> record);
    int updateCfgAmt( EmployeeOrderVo order);

    Page<EmployeeOrderVo> getEmployeeDataForEmployeeContract(EmployeeOrderVo employeeOrderVo, Integer page, Integer limit);

    EmployeeOrderVo getEmployeeDataForEmployeeContractByProperty(EmployeeOrderVo employeeOrderVo);

    List<String> getEmployeeNoList();

    List<String> getAllEmployeeNoAndContractAreaNoList();

    List<String> getOrderNoByCustIdAndcontractAreaNo(String employeeNo, String contractAreaNo);

    Integer findGroupRatioId(Long groupRatioId);

    List<Long> getemployeeId(String certNo);

    BigDecimal getOneTimeFeeByEmployeeIdAndHappenMonth(Long employeeId, Integer happenMonth);

    List<StaffOnActiveDutyCustomerVo> getAllStaffOnActiveDutyByCityCode(String cityCode, Integer integer, String receiving, String custNo,Integer accountFlag,String groupName,String receivingMan,String commissioner);

    List<EmployeeOrderVo> getSocietyInsuranceApplicationDataByOrderNo(List<String> orderNoList);
    List<EmployeeOrderVo> getEmployeeOrderVosByOrderNoList(List<String> orderNoList);
    List<EmployeeOrderVo> getSocietyInsuranceDataByOrderNo(List<String> orderNoList);

    List<EmployeeOrderVo> findAll();

    List<String> getOrderNoList();

    List<EmployeeOrderVo> getEmployeeOrderList();

    List<EmployeeOrderVo> getAllEmployeeOrderPartData();

    /**
     * 根据orderNo列表遍历查询所有匹配项
     * @param orderNos 订单编号
     * @return EmployeeOrderVo列表
     */
    List<EmployeeOrderVo> getEmployeeOrderByOrderNos(List<String> orderNos);

    List<EmployeeOrderVo> getEmployeeOrderVoByCertNo(String certNo);
    Map<String, List<OrderInsuranceCfgVo>> getComAmtAndIndAmtListMap(List<String> contractNoList);

    void updateRejectOrder(String orderNo, String rejectRemark, String loginName);

    Page<NationwideEmployeeViewVo> getNationwideEmployee(Integer page, Integer limit, EmployeeOrderVo employeeOrderVo);

    void rejectedLeave(List<String> orderNos, String loginName, String dimReason);

    int cancelDimission(List<CompleteOrderViewVo> orders, String loginName);

    int editRejectDimission(List<CompleteOrderViewVo> orders, String loginName);


    List<OneTimeCharge> getUnusedOneTimeFeeByEmployeeIdAndHappenMonth(String orderNo, Integer happenMonth);

    void updateOrderOneChargeBillIdByIds(List<Integer> orderOneChargeIds, Long billId);

    void updateOrderOneChargeUnusedByBillIds(Set<Long> billIds);

    List<OneTimeCharge> getOrderOneChargeByBillId(Long billId);

    List<EmployeeOrderVo> getEmployeeOrderByOrderNoList(List<String> orderNoList);

    List<OneTimeCharge> getIndFeeAndOneFee(List<String> orderNoList);
    List<EmployeeOrderChangeVo> selectEmployeeOrderChangeByOrderNosAndChgTypeAndChgMethod(List<String> needGetForCacheNoList, Integer chgMethod, Integer chgType);

    List<EmployeeOrderVo> getEmployeeOrderVoByCertNoAndNonOrderNo(String certNo, String orderNo);

    List<EmployeeVo> getEmployeeListByEmployeeNoList(List<String> conditionEmployeeNoList);

    List<String> getAllEmployeeNoAndContractAreaNoListByEmpNoAndAreaNos(List<EmployeeOrderVo> employeeOrderVos);

    List<EmployeeOrderVo> getEmployeeOrderVoByCustIdAndcontractAreaNo(String employeeNo, String contractAreaNo);

    List<EmployeeOrderVo> getEmployeeAndInsuranceCfgDataByOrderNos(List<String> orderNoList);


    List<OneTimeCharge> getOneTimeChargeByOrderNos(List<String> orderNoList);

    List<EmployeeOrderVo> getAllEmployeeOrderByCustIdList(List<Long> custIdList);

    List<EmployeeOrderVo> getAllContractNoAndTempletId(Integer genDate);

    Map<String, String> getCertNoByOrderNoList(List<String> orderNoList);
    Map<String, EmployeeOrderVo> getCertNoByOrderNos(List<String> orderNoList);

    Map<String, String> getNameByOrderNoList(List<String> orderNoList);

    void updateByPrimaryKeySelective(Employee record);

    Map<String, Integer> getActiveEmployeeByContractNo(List<String> contractNoList);

    int insertSelective(EmployeeOrder record);

    List<EmployeeVo> getEmployeeListByEmployeeIdList(List<Long> empIdList);

    List<EmployeeOrderVo> getOrderNoAndCityCodeByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatus(int contractStatus, long start, long end);

    Long getAllEmployeeOrderNoByContractStatusCount(int contractStatus);

    List<EmployeeOrderVo> getAllEmployeeOrderNoByContractStatusAndOrderNoList(int contractStatus, List<String> dealOrderNos);

    Page<EmployeeOrderVo> getAllOrderNo(Integer page , Integer limit, EmployeeOrderVo employeeOrderVo);
    List<EmployeeOrderVo> getRemarkByOrderNoList(List<String> orderNoList);

    List<EmployeeOrderVo> getOrderNoAndCertNo();
    List<EmployeeOrderVo> getAllEmployeeOrderByStatus();
List<String> getAllOrderNoByContractNoAndTempletId(String contractNo, Long templetId);

    List<EmployeeOrderVo> getAllEmpOrderByCustId(InsuranceBillAndPracDiffArgs args);

    /**
     * 查询产品有效期大于等于 在当前时间 与当前向前3个月的订单
     * @return 订单数据
     */
    List<EmployeeOrderVo> getEmployeeOrderVoByMonthsRange(String preTime,Integer startMonth,Integer endMonth,List<String> orderNos);
    List<EmployeeOrderVo> getEmployeeOrderVoByCreateTime(String oneMonthBefore,String twoMonthBefore,String threeMonthBefore,String orderNo);

    Map<Long, CommInsurEmpVo> getTaxRatioByContractNoAndTemId(String contractNo, List<Long> templetId);
    Map<Long, List<CommInsurEmpVo>> getAllTaxRatioByContractNoAndTemId(String contractNo, List<Long> templetId);

    /**
     * 根据订单号获取税率
     * @param orderNos
     * @return
     */
    List<CommInsurEmpVo> getTaxRatioByOrderNos(List<String> orderNos);

    /**
     * 根据合同 账单模板获取税率
     * @param contractNo
     * @param templetId
     * @return
     */
    Map<Long, List<CommInsurEmpVo>> getTaxRatiosByContractNoAndTemId(String contractNo, List<Long> templetId);
    /**
     * 根据订单号获取emp_id
     * @param orderNoList
     * @return
     */
    List<EmployeeOrderVo> findEmployeeIdByOrderNos(List<String> orderNoList);

    List<EmployeeOrderVo> getEmployeeAndContractAreaByOrderNoList(List<String> orderNoList);

    /**
     * 根据社保套餐code 查询订单
     */
    List<EmployeeOrderVo> getEmployeeOrderByInsuranceSetNo(List<String> insuranceSetNo);


    List<String> getOrderNosByContractAreaNos(List<String> contractAreaNos);
    Page<CompleteOrderViewVo> getReductionPage(Integer page, Integer limit, CompleteOrderViewVo vo);
    List<CompleteOrderViewVo> getReductionPage(CompleteOrderViewVo vo);

    Map<Integer, Integer> getPeopleNumGroupByCityCode();

    List<PerInsuranceBillVo> getPibByOrderNoList(Set<String> orderNoList);





    List<OrderFeeVo> getOrderFee(OrderFeeParamVo orderFeeParamVo,Set<String> orderNo);

    int getOrderFeeCount(OrderFeeVo vo);

    List<InsurancePracticeVo> getStartMonthAndEndMonthByCertNo(List<String> certNo);

    int updateFileStatusByOrderNo(String orderNo, String columnName);

    List<ServiceNumPeopleReportDto> getServiceNum(Map<String, Object> conditonMap);

    List<EmployeeReportDto> getEntryOrderList();

    EmployeeVo getEmployeeDataByOrderNo(String orderNo);

    QysEmpConCusAndEedDto getEmployeeAndContractAndCustomerAndEEDDataByOrder(String orderNo);
    List<EmployeeOrderVo> getReceivingManNameByOrderNos( List<String> orderNos);

    String getCommissionerOrReceivingMan(String orderNo,String loginName);

    int getCountByCommissionerOrReceivingMan(String loginName,Integer status);

    List<String> getReceivingByOrderNoList(List<String> orderNoList);

    Page<PersonOrderQueryDto> selectReonOrderPage(Map<String, Object> conditionMap);

    int getRejectOrderCountByLoginName(String loginName);

    Boolean judgmentCollectToTempletId(Long templetId, String contractAreaNo);

    String selectReceivingManByOrderNo(String orderNo);

    List<OrderAndInsuranceDiffExportVo> getOrderFeeDetailByOrderNoSet(Set<String> orderNoList);

    List<String> getOrderNoListByReceivingList(List<String> contractAreaNoList);

    List<String> getAllOrderNoByOrderNo(String orderNo);

    List<EmployeeChangeReportExportVo> getEmployeeChangeReportData(String month);

    List<Long> getAllEmpIdByCommissionerOrReceivingMan(String loginName);

    List<EmployeeOrderVo> getContractSignComTitleByOrderNoList(List<String> orderNoList);


    List<EmployeeOrderVo> getContractTypeAndDisComByOrderNoList(List<String> orderNoList);


    List<EmployeeOrderVo> selectOrderByOrderNoList(Set<String> orderNoList);

    List<EmployeeOrderVo> selectContractTypeByOrderNoList(List<String> orderNoList);

}





