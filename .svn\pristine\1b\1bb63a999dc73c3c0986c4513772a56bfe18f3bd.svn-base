package com.reon.hr.modules.customer.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAssignResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.enums.CsTypeEnum;
import com.reon.hr.api.customer.enums.DistTypeEnum;
import com.reon.hr.api.customer.enums.ServiceSiteCommon;
import com.reon.hr.api.customer.enums.contract.ContractType;
import com.reon.hr.api.customer.exception.ContractAreaAssignException;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgPositionWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.*;
import com.reon.hr.common.enums.PositionType;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reon.hr.api.enums.PositionEnum.LATER_SPECIALIST;

@RestController
@RequestMapping("/customer/distribution")
public class ContractAssignController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "contractDubboService")
    private IContractResourceWrapperService contractResourceWrapperService;

    @Resource(name = "userDubboService")
    private IUserWrapperService userWrapperService;

    @Resource
    private IOrgPositionWrapperService organizationWrapperService;

    @Resource(name = "contractAssignDubboService")
    private IContractAssignResourceWrapperService contractAssignResourceWrapperService;


    @Autowired
    private IOrgnizationResourceWrapperService orgnizationService;

    @Resource
    private ISupplierWrapperService supplierWrapperService;

    /*view区域*/
    @RequestMapping(value = "/gotoDistributionView")
    public ModelAndView gotoDistributionPage() {
        return new ModelAndView("/customer/distribution/distributionListPage");
    }

    @RequestMapping(value = "/gotoDistributionContract")
    public ModelAndView gotoDistributionContractPage() {
        return new ModelAndView("/customer/distribution/distributionContract");
    }

    @RequestMapping(value = "/gotoDistributionContractSalary")
    public ModelAndView gotoDistributionContractSalaryPage() {
        return new ModelAndView("/customer/salary/distribution/distributionContract");
    }

    /**
     * @param id
     * @param model
     * @param csType
     * @param distCom
     * @param distType 1接单、后道都分配  2：接单分配  3：后道分配
     * @return
     */
    @RequestMapping(value = "/distibution")
    public ModelAndView distibution(String id, Model model, String csType, String distCom,String distType) {
        model.addAttribute("relativeNos", id);
        model.addAttribute("csType", csType);
        model.addAttribute("distCom", distCom);
        model.addAttribute("distType", distType);
        return new ModelAndView("/customer/distribution/distribution");
    }
    /**
     * @param id
     * @param model
     * @param csType
     * @param distCom
     * @param distType 1接单、后道都分配  2：接单分配  3：后道分配  4:薪资分配
     * @return
     */
    @RequestMapping(value = "/distibutionSalary")
    public ModelAndView distibutionSalary(String id, Model model, String csType, String distCom,String distType) {
        model.addAttribute("relativeNos", id);
        model.addAttribute("csType", csType);
        model.addAttribute("distCom", distCom);
        model.addAttribute("distType", distType);
        return new ModelAndView("/customer/salary/distribution/distribution");
    }

    @RequestMapping(value = "/distributeHistory")
    public ModelAndView distributeHistory() {
        return new ModelAndView("/customer/distribution/distributionLog");
    }


    /*业务区域*/

    /**
     * 获取客服下拉值
     *
     * @param page
     * @param limit
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "/getService", method = RequestMethod.POST)
    public LayuiReplay<OrgUserVo> getService(@RequestParam("page") int page, @RequestParam("limit") int limit, @RequestParam(value = "searchParam", required = false) String searchParam) {
        Page<OrgUserVo> orgUserInfo = userWrapperService.getOrgUserInfo(page, limit, searchParam);
        return new LayuiReplay<OrgUserVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), orgUserInfo.getTotal(), orgUserInfo.getRecords());
    }

    /**
     * 根据服务网点获取接单客户
     * @param page
     * @param limit
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "/getServiceByCompany", method = RequestMethod.POST)
    public LayuiReplay getServiceByCompany(@RequestParam("page") int page, @RequestParam("limit") int limit,
                                                   @RequestParam(value = "searchParam", required = false) String searchParam,
                                                   @RequestParam(value = "orgType")String orgType) {
        Page<OrgUserVo> result = new Page<>();
        if(ServiceSiteCommon.orgTypeEnum.SUPPLIER_COMPANY.getIndex() == Integer.parseInt(orgType)){
        OrgUserVo orgUser = JSON.parseObject (searchParam, OrgUserVo.class);
            List<String> commissionerList = supplierWrapperService.getCommissionerBySupplierId(Long.parseLong(orgUser.getCityCode()));
            if(CollectionUtils.isNotEmpty(commissionerList)){
                List<OrgUserVo> orgUserVoData = Lists.newArrayList();
                List<Map<String, String>> allUserName = userWrapperService.getAllUserName();
                OrgUserVo orgUserVo;
                for (String commissioner : commissionerList) {
                        orgUserVo = new OrgUserVo();
                        orgUserVo.setLoginName(commissioner);
                        Map<String, String> orgUserVoMap = allUserName.stream().filter(item -> item.containsValue(commissioner)).findFirst().orElse(new HashMap<>());
                        orgUserVo.setUserName(orgUserVoMap.get("userName"));
                        orgUserVo.setLoginName(orgUserVoMap.get("loginName"));
                        orgUserVoData.add(orgUserVo);
                }
                result.setRecords(orgUserVoData);
            }
        }else {
            result = userWrapperService.getOrgUserInfo(page, limit, searchParam);
        }
        return LayuiReplay.success(result.getTotal(),result.getRecords());
    }

    /**
     * 获取有账单模板的客户
     *
     * @param page
     * @param limit
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "/getCustomerByTemplateId", method = RequestMethod.POST)
    public LayuiReplay<CustomerVo> getCustomerByTemplateId(@RequestParam("page") int page, @RequestParam("limit") int limit, @RequestParam(value = "searchParam", required = false) String searchParam) {
        Page<CustomerVo> allCustomer = contractResourceWrapperService.getCustomerByTemplateId(page, limit, searchParam);
        return new LayuiReplay<CustomerVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), allCustomer.getTotal(), allCustomer.getRecords());
    }

    /**
     * 获取签单分公司列表
     *
     * @return 前台未调用
     */
    @RequestMapping(value = "/getSignComList", method = RequestMethod.GET)
    public LayuiReplay<ContractPageVo> getSignComList(ContractPageVo contractPageVo) {
        contractPageVo.setSignCom("2");
        List<ContractPageVo> list = contractResourceWrapperService.getListSelective(contractPageVo);
        return new LayuiReplay<ContractPageVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), list);
    }

    @RequestMapping(value = "/getAllCompany", method = RequestMethod.GET)
    public LayuiReplay<CustomerVo> getAllCompany() {
        List<OrgVo> allCompany = orgnizationService.findAllCompany();
        return new LayuiReplay<CustomerVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), allCompany);
    }

    @RequestMapping(value = "/getAllDepartment", method = RequestMethod.GET)
    public LayuiReplay<CustomerVo> getAllDepartment() {
        List<OrgVo> allDepartment = orgnizationService.findAllDepartment();
        return new LayuiReplay<CustomerVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), allDepartment);
    }


    /**
     * 获取派单分公司列表
     *
     * @param contractPageVo
     * @return 前台未调用
     */
    @RequestMapping(value = "/getDisComList", method = RequestMethod.GET)
    public LayuiReplay<ContractPageVo> getDisComList(ContractPageVo contractPageVo) {
        //标识 无实际意义
        contractPageVo.setDistCom("1");
        List<ContractPageVo> list = contractResourceWrapperService.getListSelective(contractPageVo);
        return new LayuiReplay<ContractPageVo>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), list);
    }

    /**
     * 社保 获取page
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public LayuiReplay<ContractAssignVo> getPageList(@RequestParam("param") String param, HttpSession session) {
        List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>)session.getAttribute(Constants.SESSION_ORG_POSITION);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(param);
        //查合同类型不为商保的
        jsonObject.put("ifNoCommercialInsurance","是");
        jsonObject.put("userOrgPositionDtoList",userOrgPositionDtoList);
        List<Integer> list = Lists.newArrayList(ContractType.SOCIAL_SALARY_LIST);
        list.addAll(ContractType.DISPOSABLE_SERVICE_LIST_NOT_EXAMINATION);
        jsonObject.put("contractTypeList", list);
        param= String.valueOf(jsonObject);
        List<ContractAssignVo> pageList = contractAssignResourceWrapperService.getPageList(param, getSessionUser().getLoginName(),userOrgPositionDtoList);
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), pageList);
    }

    /**
     * 获取分配对象
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getValue", method = RequestMethod.GET)
    public LayuiReplay<String> getValueByRelativeNo(@RequestParam("id") String id) {
        ContractAssignVo contractAssignVo = new ContractAssignVo();
        contractAssignVo.setRelativeNo(id);
        ContractAssignVo contractAssign = contractAssignResourceWrapperService.findByRelativeNo(contractAssignVo);
        return new LayuiReplay<String>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), contractAssign);
    }

    /**
     * 获取日志list
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getContractAssignLog", method = RequestMethod.POST)
    public LayuiReplay<String> getContractAssignLog(@RequestParam("id") String id,@RequestParam(value = "csType",required = false) Integer csType,HttpSession session) {
        ContractAssignLogVo contractAssignLog = new ContractAssignLogVo();
        List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>)session.getAttribute(Constants.SESSION_ORG_POSITION);
        contractAssignLog.setRelativeNo(id);
        contractAssignLog.setCsType(csType);
        if (id.indexOf("X") != -1) {
            contractAssignLog.setReplaceAuth("2");
        } else {
            contractAssignLog.setReplaceAuth("1");
        }
        contractAssignLog.setUserOrgPositionDtoList(userOrgPositionDtoList);
        List<ContractAssignLogVo> contractAssignList = contractAssignResourceWrapperService.getContractAssignList(contractAssignLog, getSessionUser().getLoginName());
        return new LayuiReplay<String>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), contractAssignList);
    }

    /**
     * 获取分配客服下的列表
     * csType :1 项目  2接单后道
     */
    @RequestMapping(value = "/getServiceList")
    public LayuiReplay<ContractAssignVo> findServiceList(@RequestParam("distCom") String distCom, Integer csType){
//        CommonUserVo user = userWrapperService.findUserByLoginName(getSessionUser().getLoginName());
        String positionCode = PositionEnum.getPositionCodeByCsType(csType);
        Integer positionTypeByCsType = getPositionTypeByCsType(csType);
        // 获取主管数据
        List<UserOrgPosVo> serviceList = userWrapperService.findSubordinateByLoginNameAndCity(getSessionUser().getLoginName(), distCom.substring(0,9), positionCode);
        serviceList = serviceList.stream().filter(vo -> vo.getPosType().equals(positionTypeByCsType)).collect(Collectors.toList());
        // 获取主管id
        List<Long> directorList = new LinkedList<>();
        serviceList.forEach(service -> directorList.add(service.getId()));
        // 获取专员数据
        if (!directorList.isEmpty()) {
            List<UserOrgPosVo> orgPosVos = userWrapperService.findSubordinateByParentIdList(directorList);
            Map<String, UserOrgPosVo> posVoMap = orgPosVos.stream().collect(Collectors.toMap(this::getKey, Function.identity(),(a,b) ->a));
            /**如果一个人同时具有专员 主管，只需要专员就行*/
            serviceList.forEach(vo ->{
                if(!posVoMap.containsKey(getKey(vo))){
                    orgPosVos.add(vo);
                }
            });
            serviceList = orgPosVos;
        }
        //过滤掉虚拟岗
        List<UserOrgPosVo> collect = serviceList.stream().filter(u -> u.getPosCode().charAt(0) == '2').collect(Collectors.toList());
        /**后道目前是没有主管人员，所以无法通过职级关系来找到后道专员，目前直接查出orgCode下面的全部后道*/
        if(csType.equals(CsTypeEnum.CUSTOMER_SERVICE_AFTER_RECEIVING_ORDERS.getIndex())){
            List<CommonUserVo> laterServices = userWrapperService.getServiceListByOrgCodeAndPositionCode(distCom.substring(0, 9), LATER_SPECIALIST.getCode());
            List<UserOrgPosVo> laterServiceList =  laterServices.stream().collect(
                    Collectors.toMap(CommonUserVo::getLoginName,Function.identity(),
                            (u1, u2) -> u1.getPositionCode().length() > u2.getPositionCode().length() ? u1 : u2)).values()
                    .stream().map(vo -> {
                        UserOrgPosVo orgPosVo = new UserOrgPosVo();
                        BeanUtils.copyProperties(vo, orgPosVo);
                        orgPosVo.setPosCode(vo.getPositionCode());
                        orgPosVo.setOrgCode(vo.getOrganCode());
                        orgPosVo.setPosType(PositionType.LATER_SERVICE.getCode());
                        return orgPosVo;
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(laterServiceList)){
                collect.addAll(laterServiceList);
            }
        }
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg(), collect);
    }

    /*
     * 保存
     * */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/saveDistribution")
    public LayuiReplay<String> saveDistribution(@RequestBody ContractAssignParamVo contractAssignParamVo) {
        String loginName = getSessionUser().getLoginName();
        LayuiReplay<String> msg = validationData(contractAssignParamVo);
        if(Objects.nonNull(msg)) {
            return msg;
        }
        // 保存分配日志
        try {
            /**分配小合同：单独分配后道或接单*/
            if(contractAssignParamVo.getCsType().equals(CsTypeEnum.CUSTOMER_SERVICE_AFTER_RECEIVING_ORDERS.getIndex()) &&
                    !contractAssignParamVo.getDistType().equals(DistTypeEnum.ALL_DIS.getCode())){
                contractAssignResourceWrapperService.handlePartDistributeContract(contractAssignParamVo, loginName);
            } else {
                boolean result = contractAssignResourceWrapperService.distributeContract(contractAssignParamVo, loginName);
                if (!result) {
                    logger.info("合同分配失败！");
                    return new LayuiReplay<>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
                }
            }
        }catch (ContractAreaAssignException contractAreaAssignException){
            logger.error("合同分配发生异常，异常消息:" + contractAreaAssignException.getMessage());
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), contractAreaAssignException.getMessage());
        }catch (Exception e) {
            logger.error("合同分配发生异常，异常消息:" + e.getMessage());
            return new LayuiReplay<>(ResultEnum.ERR.getCode(), ResultEnum.ERR.getMsg());
        }
        return new LayuiReplay<>(ResultEnum.OK.getCode(), ResultEnum.OK.getMsg());
    }

    private LayuiReplay<String> validationData(ContractAssignParamVo contractAssignParamVo) {
        Integer csType = contractAssignParamVo.getCsType();
        LayuiReplay<String> msg = null;
        if(!csType.equals(CsTypeEnum.CUSTOMER_SERVICE_AFTER_RECEIVING_ORDERS.getIndex())
                &&!csType.equals(CsTypeEnum.PROJECT_CUSTOMER_SERVICE.getIndex())){
            String commissioner = contractAssignParamVo.getCommissioner();
            if(StringUtils.isBlank(commissioner)){
                msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择新客服");
            }
            return msg;
        }
        Integer distType = contractAssignParamVo.getDistType();
        switch (distType) {
            case 1:
                StringBuilder sb = new StringBuilder();
                checkLaterComm(contractAssignParamVo,sb);
                if(sb.length() > 0){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), sb.toString());
                }
                break;
            case 2 :
                String commissioner = contractAssignParamVo.getCommissioner();
                if(StringUtils.isBlank(commissioner)){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择接单客服");
                }
                break;
            case 3:
                String laterComm = contractAssignParamVo.getLaterComm();
                if(StringUtils.isBlank(laterComm)){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择后道客服");
                }
                break;
            case 4 :
                commissioner = contractAssignParamVo.getCommissioner();
                if(StringUtils.isBlank(commissioner)){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择项目客服");
                }
                break;
            case 5:
                String salaryComm = contractAssignParamVo.getSalaryComm();
                if(StringUtils.isBlank(salaryComm)){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), "请选择薪资客服");
                }
                break;
            case 6:
                StringBuilder salarySb = new StringBuilder();
                checkSalaryComm(contractAssignParamVo,salarySb);
                if(salarySb.length() > 0){
                    msg = new LayuiReplay<>(ResultEnum.ERR.getCode(), salarySb.toString());
                }
                break;
            default :
                    break;
        }
        return msg;
    }

    private String getKey(UserOrgPosVo vo) {
        return vo.getLoginName();
    }


    private String getLaterCommStr(String commissioner) {
        String[] split = commissioner.split(",");
        String loginName = split[0];
        List<OrgPositionVo> vos = organizationWrapperService.findLoginNameByPosCodeAndOrgCode(loginName);
        OrgPositionVo orgPositionVo = vos.stream().filter(vo -> vo.getPosType().equals(PositionType.LATER_SERVICE.getCode().toString()))
                .max(Comparator.comparingInt(a -> a.getPosCode().length())).orElse(null);
        if(Objects.isNull(orgPositionVo)){
            return "";
        }
        return loginName + ',' + orgPositionVo.getPosCode() + "," +  orgPositionVo.getOrgCode();
    }

    private void checkLaterComm(ContractAssignParamVo contractAssignParamVo,StringBuilder sb) {
        if(contractAssignParamVo.getCsType().equals(CsTypeEnum.CUSTOMER_SERVICE_AFTER_RECEIVING_ORDERS.getIndex())
        && contractAssignParamVo.getIdenticalFlag() == null){
            sb.append("没有选择后道客服");
           return;
        }
        if(StringUtils.isBlank(contractAssignParamVo.getCommissioner())){
            sb.append("没有选择接单客服");
           return;
        }
        if(contractAssignParamVo.getIdenticalFlag() != null) {
            if(contractAssignParamVo.getIdenticalFlag() == 1){
                String laterCommStr = contractAssignParamVo.getLaterComm();
                if(StringUtils.isBlank(laterCommStr)){
                    sb.append("没有选择后道客服");
                    return;
                }
                String[] laterCommArr = laterCommStr.split(",");
                String loginName = laterCommArr[0];
                List<OrgPositionVo> orgPositionVos = organizationWrapperService.findLoginNameByPosCodeAndOrgCode(loginName);
                boolean anyMatch = orgPositionVos.stream().anyMatch(vo -> vo.getPosCode().equals(LATER_SPECIALIST.getCode()) || vo.getPosCode().equals(PositionEnum.LATER_SUPERVISOR.getCode()));
                if (!anyMatch) {
                    sb.append(loginName).append("不是后道客服");
                }
            } else {
                String laterCommStr = getLaterCommStr(contractAssignParamVo.getCommissioner());
                if(StringUtils.isBlank(laterCommStr)){
                    sb.append("后道客服选择有误！！");
                }
                contractAssignParamVo.setLaterComm(laterCommStr);
            }
        }
    }
    private void checkSalaryComm(ContractAssignParamVo contractAssignParamVo,StringBuilder sb) {
        if(contractAssignParamVo.getCsType().equals(CsTypeEnum.PROJECT_CUSTOMER_SERVICE.getIndex())
                && contractAssignParamVo.getIdenticalSalaryFlag() == null){
            sb.append("没有选择薪资客服");
            return;
        }
        if(StringUtils.isBlank(contractAssignParamVo.getCommissioner())){
            sb.append("没有选择项目客服");
            return;
        }
        if(contractAssignParamVo.getIdenticalSalaryFlag() != null) {
            if(contractAssignParamVo.getIdenticalSalaryFlag() == 1){
                String salaryCommStr = contractAssignParamVo.getSalaryComm();
                if(StringUtils.isBlank(salaryCommStr)){
                    sb.append("没有选择薪资客服");
                    return;
                }
            } else {
                contractAssignParamVo.setSalaryComm(contractAssignParamVo.getCommissioner());
            }
        }
    }


    public Integer getPositionTypeByCsType(Integer csType) {
        if (csType == 1) {
            return PositionType.PROJECT_SERVICE.getCode();
        } else if (csType == 2) {
            return PositionType.C_S.getCode();
        } else if (csType == 3) {
            return PositionType.UNDERWRITERS.getCode();
        } else if (csType == 4) {
            return PositionType.LATER_SERVICE.getCode();
        } else if (csType == 5) {
            return PositionType.SALARY_SERVICE.getCode();
        }
        return null;
    }

}
