var ctx = ML.contextPath;
layui.config({
    base : ctx+"/js/"
}).use(['form', 'layer', 'table', 'upload', 'laydate','tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, upload = layui.upload,tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;
    form.render('select');
    form.on("submit(saveFilter)", function (data) {
        var field = data.field;
        if (field.invoiceMethod == 1) {
            if (!field.email) {
                layer.msg("发票方式为电子发票时,邮箱必填")
                return false;
            }
        }
        if (field.email && !verifyEmail(field.email) && field.invoiceMethod != 0) {
            layer.msg("邮箱格式不正确")
            return false;
        }
        ML.layuiButtonDisabled($('#saveBtn'));// 禁用
        data.field['optType'] = "add";
        $.ajax({
            url: ctx + "/customer/invoice/save",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    layer.closeAll('iframe');//关闭弹窗
                } else if (result.code == -1) {
                    ML.layuiButtonDisabled($('#' + btnId + ''), true);// 禁用
                }
            }
        });
        return false;
    });

    //付款方变动时,开票抬头和开户名作相应变动
    /*form.on("select(payCustIdFilter)", function (data) {
        var payCustText = data.elem[data.elem.selectedIndex].text;
        var payCustId = data.elem[data.elem.selectedIndex].value;
        getCorpKind(custData,payCustId);
        $("#title").val(payCustText);
       $("#acctName").val(payCustText);
    });

    function getCorpKind(custDataList,payCustId) {
        layui.each(custDataList,function (i,item) {
            if (item.id == payCustId) {
                $("#corpKind").html(
                    "<option class='corpKind' value='"
                    + item.corpKind + "'>" + ML.dictFormatter("CORP_KIND", item.corpKind) + "</option>"
                );
                form.render('select');
                editRequired(item.corpKind);
            }
        });
    }*/

    function editRequired(corpKind) {
        if (corpKind == 2) {
            $("#acctNo").attr("lay-verify","required");
            $("#acctNoI").html("*")
            $("#acctName").attr("lay-verify","required");
            $("#acctNameI").html("*")
            $("#bankName").attr("lay-verify","required");
            $("#bankNameI").html("*")
            $("#tel").attr("lay-verify","required");
            $("#telI").html("*")
            $("#addr").attr("lay-verify","required");
            $("#addrI").html("*")
        }else if (corpKind == 1) {
            $("#acctNo").attr("lay-verify","");
            $("#acctNoI").html("")
            $("#acctName").attr("lay-verify","");
            $("#acctNameI").html("")
            $("#bankName").attr("lay-verify","");
            $("#bankNameI").html("")
            $("#tel").attr("lay-verify","");
            $("#telI").html("")
            $("#addr").attr("lay-verify","");
            $("#addrI").html("")
        }
    }

    $("#cancelBtn").click(function () {
        layer.closeAll('iframe'); //关闭弹窗
    });

    form.on("select(invoiceMethodFilter)", function (data) {
        const invoiceMethod = data.value;
        if (invoiceMethod == 0) {
            $("#email").removeAttr("lay-verify","email");
            clearInput("#acctNo");
            clearInput("#acctName");
            clearInput("#bankName");
            clearInput("#tel");
            clearInput("#addr");
            clearInput("#email");
        }
        if (invoiceMethod == 1) {
            $("#email").attr("lay-verify","required");
        }
        if (invoiceMethod == 2) {
            $("#email").removeAttr("lay-verify","required");
        }
    })

    function clearInput(selector) {
        if (!$(selector).val()) {
            $(selector).val("/");
        }
    }

    form.on("select(corpKindFilter)", function (data) {
        const corpKindVal = data.value;
        if (corpKindVal == 2) {
            $("#acctNo").attr("lay-verify","required");
            $("#acctNoI").html("*")
            $("#acctName").attr("lay-verify","required");
            $("#acctNameI").html("*")
            $("#bankName").attr("lay-verify","required");
            $("#bankNameI").html("*")
            $("#tel").attr("lay-verify","required");
            $("#telI").html("*")
            $("#addr").attr("lay-verify","required");
            $("#addrI").html("*")
        }
    })

    var custData = [];
    var appd = '<input style="display:inline-block;width:220px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'custId',
        appd:appd,
        table: {
            url:ctx+'/customer/contract/getCustomerWithoutExists',
            cols: [[
                { type: 'radio', width: '50' },
                {field: 'custNo',title:'客户编号',align:'center',width:'300'},
                {field: 'custName',title:'客户名称',align:'center',width: '350'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var custId = "";
            let name="";
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custId = item.custId
                name = item.custName;
            })
            $("#custId").val(custId);
            elem.val(NEWJSON.join(","))
            $("#title").val(name);
            $("#acctName").val(name);

        }
    });
});