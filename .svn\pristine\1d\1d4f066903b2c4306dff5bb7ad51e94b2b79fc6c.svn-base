package com.reon.hr.api.customer.utils;

import com.reon.hr.api.customer.dubbo.service.rpc.customer.IBatchImportDataWrapperService;
import com.reon.hr.api.customer.vo.batchImport.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * @suthor:chenyi
 * @date:2019.06.18
 * @Description:批量导入离职申请工具类
 */
public class BatchResignationApplicationImportUtil extends BatchImportExcelCommonUtil {
    private static final Logger logger = LoggerFactory.getLogger (BatchResignationApplicationImportUtil.class);

    /**
     * 记入解析excel导入的数据并写入导入历史表
     *
     * @param raedv
     * @param batchCommonVo
     * @throws IOException
     */
    public void analyzeExcelData(ResignationApplicationExcelDataVo raedv, BatchCommonVo batchCommonVo) throws IOException {
        checkFile (batchCommonVo.getFile ());
        Workbook workbook = getWorkBook (batchCommonVo.getFile ());
        batchCommonVo.setWorkbook (workbook);
        ImportDataVo importDataVo = getImportDataVo (batchCommonVo);
        checkImportDateType (batchCommonVo.getDataType (), importDataVo, batchCommonVo.getLoginName (), batchCommonVo.getRemark ());
        if (batchCommonVo.getWorkbook () != null) {
            for (int sheetNum = 0; sheetNum < 1; sheetNum++) {
                Sheet sheet = batchCommonVo.getWorkbook ().getSheetAt (sheetNum);//获得当前sheet工作表
                if (sheet == null) {
                    continue;
                }
                int firstRowNum = sheet.getFirstRowNum ();//获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum ();  //循环除了第二行的所有行
                if (lastRowNum > 0) {//判断模板是否为空
                    batchCommonVo.getService ().insertSelective (importDataVo);
                    for (int rowNum = firstRowNum + 0; rowNum <= lastRowNum; rowNum++) {
                        Row row = sheet.getRow (rowNum);//获得当前行
                        if (row == null) {
                            continue;
                        }
                        if (rowNum == 0) {
                            //获取标题信息
                            for (int i = 0; i < row.getLastCellNum (); ++i) {
                                Cell cell = row.getCell (i);
                                String stringCellValue = cell.getStringCellValue ();
                                String value = stringCellValue.replaceAll ("\n", "");
                                if (IBatchImportDataWrapperService.exportResignationApplicationHeaders != null) {
                                    IBatchImportDataWrapperService.exportResignationApplicationHeaders.add (value);
                                }
                                raedv.getRaheaders ().add (value);
                                if(value.equals ("错误描述")){
                                    throw new RuntimeException ("导入的模板【错误描述】这一列请删除！");
                                }
                            }
                        }
                        if (rowNum > 0 && checkDetermineImportedDataIsEmpty (row, raedv.getRaheaders ())) {
                            ImportDataLogVo importDataLogVo = getImportDataLogVo (batchCommonVo, rowNum);
                            Map<String, Object> sump = getSump (batchCommonVo, importDataVo, rowNum);
                            String cellValue;
                            //循环当前行
                            for (int cellNum = 0; cellNum < raedv.getRaheaders ().size (); cellNum++) {
                                logger.info ("处理中=" + importDataLogVo.getImportNo () + "," + importDataLogVo.getCreator ());
                                Cell cell = row.getCell (cellNum);
                                if (cell == null || "".equals (cell.toString ())) {
                                    String value = "";
                                    sump.put ("" + raedv.getRaheaders ().get (cellNum) + "", "");
                                    value = getString (raedv.getRaheaders (), cellNum, value);
                                    validationDataType (importDataLogVo, sump, raedv.getRafamap (), cellNum, value, raedv.getRaheaders ());
                                    continue;
                                }
                                cellValue = getCellValue (raedv.getRaheaders (), cellNum, cell);
                                validationDataType (importDataLogVo, sump, raedv.getRafamap (), cellNum, cellValue, raedv.getRaheaders ());
                            }
                            if (!raedv.getRafamap ().isEmpty ()) {
                                failNum++;
                            }
                            successNum++;
                            calculateNumberImport (importDataVo, successNum, failNum, importDataLogVo);
                            raedv.getList ().add (sump);
                            raedv.getImportDataLogVoList ().add (importDataLogVo);
                            //清除上条数据的错误记入
                            raedv.getRafamap ().clear ();
                        }

                    }
                } else {
                    throw new RuntimeException ("导入的模板数据为空！");

                }
            }
            batchCommonVo.getWorkbook ().close ();
        }
        raedv.setImportNo (importDataVo.getImportNo ());
        raedv.setSuccessNum (importDataVo.getSuccessNum ());
        raedv.setFailNum (importDataVo.getFailNum ());
    }

}
