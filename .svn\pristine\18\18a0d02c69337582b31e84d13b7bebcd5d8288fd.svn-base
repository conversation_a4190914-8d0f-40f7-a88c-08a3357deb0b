var ctx = ML.contextPath;
layui.config(
    {base: ctx + "/js/"}
).use(['form', 'layer', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;
    table.render({
        id: 'customerSolutionQueryGridTableId',
        elem: '#customerSolutionQueryGridTableId',
        method: 'GET',
        data:[],
        page: true,   //默认不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: '600',
        toolbar: '#toolbarDemo',
        where:{"paramData":JSON.stringify(serialize("searchForm"))},
        limit: 50,
        text: {
            none: '暂无数据'  //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left', align: 'center'},
            {field: 'custName', title: '客户名称', width: '250', align: 'center'},
            {field: 'custId', title: '客户ID', width: '250', align: 'center'},
            {field: 'solutionNo', title: '方案编号', width: '250', align: 'center'},
            {field: 'version',title:'版本',width:'70',align:'center'},
            {field: 'solutionName', title: '方案名称', width: '300', align: 'center'},
            {field: 'supplierId', title: '供应商ID', width: '280', align: 'center'},
            {field: 'supplierName', title: '供应商', width: '280', align: 'center'},
            {
                field: 'solutionType', title: '方案类型', width: '130', align: 'center', templet: function (d) {
                    return ML.dictFormatter("SOLUTION_TYPE", d.solutionType);
                }
            },
            {
                field: 'status', title: '状态', width: '130', align: 'center', templet: function (d) {
                    return ML.dictFormatter("CUST_SOLU_STATUS", d.status);
                }
            },
            {
                field: 'payMethod', title: '付费方式', width: '130', align: 'center', templet: function (d) {
                    return ML.dictFormatter("PAY_ORDER_TYPE", d.payMethod);
                }
            },
            {field: 'cost', title: '方案成本价', width: '130', align: 'center'},
            {field: 'price', title: '方案指导价', width: '130', align: 'center'},
        ]], done: function (res) {
            $("[data-field='custId']").css('display', 'none');
            $("[data-field='supplierId']").css('display', 'none');
        }
    });

    form.on('submit(btnQueryFilter)', function (data) {
      if($("#custId").val()){
          table.reload('customerSolutionQueryGridTableId', {
              url: ML.contextPath + 'getCustomerSolutionQueryList',
              where: data.field,
              page: {curr: 1}
          });
      }else{
          layer.msg("请选择客户姓名再查询!!");
      }

        return false;
    });

    function getCheckSolution(solutionNo) {
        $.ajax({
            type: 'GET',
            url: ctx + "/customer/quotation/selectSolutionNoReferenced",
            data: {"soluNo": solutionNo},
            dataType: 'json',
            async: false,
            success: function (data) {
                $("#checkSolutionCache").val(data.data.length);
            },
            error: function (resp, textStatus, errorThrown) {
                ML.ajaxErrorCallback(resp, textStatus, errorThrown);
            }
        });
    }



    table.on('toolbar(customerSolutionQueryGridTableId)', function (obj) {
        var data = obj.data;
        var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
        var ids = [];
        data.forEach(function (data) {
            ids.push(data.id)
        });
        switch (obj.event) {
            case 'query':
                editOne("查看客户方案",['70%', '70%'], 'query', data);
                break;
            case 'add':
                editOne("新增客户方案", ['70%', '70%'], 'add', null);
                break;
            case 'hide':
                if(data.length<1){
                    layer.msg("请选择一条!!");
                }else{
                    optOneOrMany("隐藏",{"ids": JSON.stringify(ids)});
                }
                break;
            case 'update':
                if(data.length<1){
                    layer.msg("请先搜索并选择客户方案!!");
                }else{
                    var solutionNo = data[0].solutionNo;
                    getCheckSolution(solutionNo);
                    var checkSolution = $("#checkSolutionCache").val();
                    if (data.length == 0) {
                        layer.msg('请选中一行');
                    } else if (data.length > 1) {
                        layer.msg('只能同时选一个');
                    } else if(data[0].status == 2){
                        layer.msg("禁用的方案不可以修改!");
                    }else if(data[0].status == 3){
                        layer.msg("待生效的方案不可以修改!");
                    } else if (checkSolution != "" && checkSolution != null && checkSolution > 0) {
                        layer.msg("已经被报价单使用的方案不可修改!");
                    }
                    else {
                        editOne("修改客户方案", ['70%', '70%'], 'update', data);
                    }
                    break;
                }
        }
    });
    function optOneOrMany(title, params) {
        layer.confirm("确认要" + title + "吗？", {title: title + "确认"}, function (index) {
            layer.close(index);
            ML.ajax("/customer/customerSolution/hideCustomerSolution", params, function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    layer.closeAll('iframe');//关闭弹窗
                }
                reloadTable();
            });
        });
    }

    function editOne(title, area, optType, data) {
        var url = "";
        if (optType === 'query') {
            url = "/customer/customerSolution/gotoCustomerSolutionQuery";
        } else if (optType === 'add') {
            url = "/customer/customerSolution/gotoCustomerSolutionAdd";
        } else if (optType === 'update') {
            url = "/customer/customerSolution/gotoCustomerSolutionUpdate";
        }
        if (optType === 'update' || optType === 'query') {
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容,为2时会向后台发送请求,content内容为请求地址
                title: title,
                area: ['1800px', '800px'],
                shade: [0.8, '#393D49'],
                maxmin: true,
                offset: 'auto',
                content: ctx + url,
                success: function (layero, index) {
                  var body = layer.getChildFrame('body', index);
                  body.find("#optType").val(optType);
                  // if (optType === 'update' || optType === 'query') {
                      //方案Id
                      body.find('#custSoluId').val(data[0].id);
                      body.find("#commInsurId").val(data[0].commInsurId);
                      body.find('#status').val(data[0].status);
                      body.find('#custId').val(data[0].custId);
                      body.find("#custName").val(data[0].custName);
                      body.find("#solutionNo").val(data[0].solutionNo);
                      body.find("#solutionName").val(data[0].solutionName);
                      body.find("#payMethod").val(data[0].payMethod);
                      body.find("#cost").val(data[0].cost);
                      body.find("#price").val(data[0].price);
                      body.find("#supplierIdReturn").val(data[0].supplierId);
                      body.find("#prodType").val(data[0].prodType);
                     // if(optType === 'update'){
                     //  body.find("#optType").val("update");
                     // }else if(optType === 'query'){
                     //     body.find("#optType").val("query");
                     // }
                  // }
                },
                end: function () {
                    layer.closeAll('iframe');
                    reloadTable();
                }
            })
        } else if (optType === 'add') {
            layer.open({
                type: 2, //为1时弹出页面直接展示content中内容,为2时会向后台发送请求,content内容为请求地址
                title: title,
                area: ['1800px', '800px'],
                shade: [0.8, '#393D49'],
                maxmin: true,
                offset: 'auto',
                content: ctx + url,
                success: function (layero, index) {
                    var body = layer.getChildFrame('body', index);
                    body.find("#optType").val(optType);
                },
                end: function () {
                    layer.closeAll('iframe');
                    reloadTable();
                }
            })
        }

    }

    $(document).ready(function () {
        //查询所有方案名称

        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/customerSolution/getAllSolutionName",
            dataType: 'json',
            success: function (data) {
                var solutionNameList;
                solutionNameList = data.data;
                $.each(solutionNameList, function (i, item) {
                    $("#solutionName").append($("<option/>").text(item.solutionName).attr("value", item.solutionName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });

        ML.ajax(ML.contextPath + "/customer/customerSolution/getAllSupplierIdAndName", "", function (data) {
            var supplierList;
            supplierList = data.data;
            $.each(supplierList, function (index, item) {
                $("#supplierId").append($("<option/>").text(item.supplierName).attr("value", item.supplierId))
            });
            form.render('select');
        }, "GET")

    });


    //获取客户
    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName);
                custNo = item.custNo;
                id = item.id;
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            $("#custId").val(id);
        }
    });



    table.on('rowDouble(customerSolutionQueryGridTableId)',function(obj){
        var data =[];
        data.push(obj.data);
        editOne("查看客户方案",['70%', '70%'], 'query', data);
        });



//重载数据
    function reloadTable() {
        table.reload('customerSolutionQueryGridTableId', {
                where: {
                    url: ML.contextPath + 'getCustomerSolutionQueryList',
                    paramData: JSON.stringify(serialize("searchForm"))
                }});}


});