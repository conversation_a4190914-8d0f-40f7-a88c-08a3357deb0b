package com.reon.hr.sp.customer.service.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.SpecialCustomerVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年05月24日
 * @Version 1.0
 */
public interface SpecialCustomerService {

    Page<SpecialCustomerVo> getSpecialCustomerPage(SpecialCustomerVo specialCustomerVo, Integer limit, Integer page);

    SpecialCustomerVo getSpecialCustomerVoById(Long id);

    int getCountById(Long custId,Integer type);

    void addOrUpdateSpecialCustomer(SpecialCustomerVo specialCustomerVo);

    void deleteSpecialCustomer(List<Long> ids);

    List<Long> findCustIdByInvoiceUnApprovalCustId(List<Long> custIdList,Integer type);
}
