package com.reon.hr.api.bill.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.bill.dto.BillPrintDto;
import com.reon.hr.api.bill.dto.BillReportSecondSheetDto;
import com.reon.hr.api.bill.dto.PerBillItemDto;
import com.reon.hr.api.bill.dto.PerBillPrintDto;
import com.reon.hr.api.bill.enums.ContractType;
import com.reon.hr.api.bill.enums.InsuranceIRatioProductCode;
import com.reon.hr.api.bill.vo.DisposableItemApprovalVo;
import com.sun.org.apache.bcel.internal.generic.IF_ACMPEQ;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

public class NewBillPrintExcelUtil {

    public static final String[] DETAIL_BASE_HEAD = {"序号", "客户名称","合同名称","账单模板名称", "员工姓名", "身份证号", "未增反馈", "机动分类2", "机动分类3", "机动分类4", "机动分类5", "公积金账号", "参保地", "服务年月"};
    public static final String[] DETAIL_BASE_FIELD = {"index", "custName","contractName","templetName", "empName", "certNo", "remark1", "remark2", "remark3", "remark4", "remark5", "accuAcctNo", "cityName", "receivableMonth"};
    public static final Integer[] DETAIL_BASE_HEAD_COLUMN_WIDTH = {7, 12,12, 12, 7, 15, 7, 7, 7, 7, 7, 7, 7, 7, 10, 7, 7};

    public static List<Integer> F_TO_K_COLUMN = Lists.newArrayList(4, 5, 6, 7, 8, 9);
    public static Map<Integer, Integer> DETAIL_DYNAMIC_COLUMN_MAP = Maps.newHashMap();

    public static final String[] DETAIL_HEAD = {"总额", "企业基数", "企业比例", "企业金额", "个人基数", "个人比例", "个人金额"};
    public static final String[] DETAIL_FIELD = {"total", "comBase", "comRatio", "comAmt", "indBase", "indRatio", "indAmt"};
    public static final Integer[] DETAIL_HEAD_COLUMN_WIDTH = {7, 7, 7, 7, 7, 7, 7};

    public static Map<Integer, Integer> DETAIL_COLUMN_MAP = Maps.newHashMap();

    public static final String[] DETAIL_BASE_END_HEAD = {"不含税服务费", "税费", "服务费", "档案费", "税点成本", "社保公积金企业小计", "社保公积金个人小计", "小计"};
    public static final String[] DETAIL_BASE_END_FIELD = {"noHaveTax", "taxAmt", "serviceFee", "archiveFee", "taxCost", "comSubtotal", "indSubtotal", "indTotal"};
    public static final Integer[] DETAIL_BASE_END_HEAD_COLUMN_WIDTH = {7, 7, 7, 7, 7, 7, 7, 7};

    public static Map<Integer, Integer> DETAIL_BASE_END_COLUMN_MAP = Maps.newHashMap();

    public static final String[] DISPOSABLE_HEAD = {"序号", "产品大类", "产品名称", "金额", "增值税", "合计", "人数", "说明"};
    public static final String[] DISPOSABLE_FIELD = {"index", "formatProdType", "formatProdKind", "taxFreeAmt", "tax", "amount", "peopleNum", "remark"};
    public static final int ROW_HEIGHT = 460;

    public static final String[] SECOND_HEAD = {"序号", "账单模板", "客户名称", "合同类型", "人数", "人次", "社保/公积金企业金额", "社保/公积金个人金额", "工资+个税总额", "一次性其他费用", "服务费金额", "税费", "一次性服务费", "账单合计"};
    public static final String[] SECOND_FIELD = {"index", "templetName", "custName", "contractTypeName", "population", "personTime", "comSubAllTotal", "indSubAllTotal", "salaryAndTaxTotal", "oneChargesOtherAmtTotal", "serviceAmtTotal", "taxTotal", "oneChargeServiceAmtTotal", "receiveAmt"};
    public static final Integer[] SECOND_FIELD_COLUMN_WIDTH = {7, 14, 14, 7, 7, 7, 21, 21, 21, 21, 21, 7, 14, 14};


    /** 集团打印或者客户打印使用  会把汇款信息显示到每一行 */
    public static final String[] SECOND_HEAD_GROUP = {"序号", "账户全称", "开户银行", "银行账号", "账单模板", "客户名称", "合同类型", "人数", "人次", "社保/公积金企业金额", "社保/公积金个人金额", "工资+个税总额", "一次性其他费用", "服务费金额", "税费", "一次性服务费", "账单合计"};
    public static final String[] SECOND_FIELD_GROUP = {"index", "compName", "bankName", "bankNo", "templetName", "custName", "contractTypeName", "population", "personTime", "comSubAllTotal", "indSubAllTotal", "salaryAndTaxTotal", "oneChargesOtherAmtTotal", "serviceAmtTotal", "taxTotal", "oneChargeServiceAmtTotal", "receiveAmt"};
    public static final Integer[] SECOND_FIELD_COLUMN_WIDTH_GROUP = {7, 14, 14, 14, 14, 14, 7, 7, 7, 21, 21, 21, 21, 21, 7, 14, 14};

    // key代表客户端保存的字段的字典value
    // value代表客户端字典value代表的字段在上面数组中的下标位置
    static {
        DETAIL_COLUMN_MAP.put(1, 1);
        DETAIL_COLUMN_MAP.put(2, 2);
        DETAIL_COLUMN_MAP.put(3, 3);
        DETAIL_COLUMN_MAP.put(4, 4);
        DETAIL_COLUMN_MAP.put(5, 5);
        DETAIL_COLUMN_MAP.put(6, 6);
        DETAIL_BASE_END_COLUMN_MAP.put(7, 5);
        DETAIL_BASE_END_COLUMN_MAP.put(8, 6);
        DETAIL_DYNAMIC_COLUMN_MAP.put(9, 4);
        DETAIL_DYNAMIC_COLUMN_MAP.put(10, 5);
        DETAIL_DYNAMIC_COLUMN_MAP.put(11, 6);
        DETAIL_DYNAMIC_COLUMN_MAP.put(12, 7);
        DETAIL_DYNAMIC_COLUMN_MAP.put(13, 8);
        DETAIL_DYNAMIC_COLUMN_MAP.put(14, 9);
    }

    public static SXSSFWorkbook getBillPrintWorkBook(BillPrintDto billPrintDto) {
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        if (CollectionUtils.isEmpty(billPrintDto.getPerBillPrintDtoList()) && CollectionUtils.isEmpty(billPrintDto.getBillReportSecondSheetDtoList())) {
            return sxssfWorkbook;
        }
        CellStyle headCellStyle = getFieldCellStyle(sxssfWorkbook, true);
        CellStyle dataCellStyle = getFieldCellStyle(sxssfWorkbook, false);
        CellStyle numDataCellStyle = getFieldCellStyle(sxssfWorkbook, false);
        numDataCellStyle.setDataFormat(sxssfWorkbook.createDataFormat().getFormat("0.00_"));

        StringBuilder sb = new StringBuilder();
        String nameByCode = "";
        if (billPrintDto.getContractType() != null) {
            nameByCode = ContractType.getNameByCode(billPrintDto.getContractType());
        }
        if (billPrintDto.getCustGroupTip()) {
//            billPrintDto.get
            sb.append("集团名称: ").append(billPrintDto.getCustGroupName());
        } else {
            sb.append("客户名称: ").append(billPrintDto.getCustName())
                    .append(" 合同类型: ").append(nameByCode);
        }
        // 创建表并设置标题与打印时间 创建第一个sheet表
        SXSSFSheet sheetFirst = sxssfWorkbook.createSheet();
        SXSSFSheet firstSheet = createFirstSheetAndSetTitleAndTime(sxssfWorkbook, sheetFirst, billPrintDto.getTempletName(), billPrintDto.getBillMonth(), sb.toString());
        int dataStartRow = 3;

        int perBillEndRow = setPerBillData(sxssfWorkbook, firstSheet, billPrintDto, dataStartRow, headCellStyle, dataCellStyle, numDataCellStyle, null);
        int disposableEndRow = setDisposableData(sxssfWorkbook, firstSheet, billPrintDto, perBillEndRow + 1, headCellStyle, dataCellStyle, numDataCellStyle);
        // 创建第二sheet表
        String sheetTitleName = "List";
        SXSSFSheet sheetSecond = sxssfWorkbook.createSheet();
        SXSSFSheet secondSheet = createSecondSheetAndSetTitle(sxssfWorkbook, sheetSecond, sheetTitleName);
        int secondSheetDataStartRow = 0;
        int secondSheetDataEndRow = setTableData(sxssfWorkbook, secondSheet, billPrintDto.getBillReportSecondSheetDtoList(), secondSheetDataStartRow, headCellStyle, dataCellStyle, numDataCellStyle, billPrintDto.getCustGroupTip());
        setEndData(sxssfWorkbook, secondSheet, secondSheetDataEndRow, billPrintDto);
        return sxssfWorkbook;
    }

    public static SXSSFWorkbook getBillPrintWorkBookByList(List<BillPrintDto> billPrintDtoList, List<Integer> columns) {
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook();
        if (CollectionUtils.isEmpty(billPrintDtoList)) {
            return sxssfWorkbook;
        }
        CellStyle headCellStyle = getFieldCellStyle(sxssfWorkbook, true);
        CellStyle dataCellStyle = getFieldCellStyle(sxssfWorkbook, false);
        CellStyle numDataCellStyle = getFieldCellStyle(sxssfWorkbook, false);
        numDataCellStyle.setDataFormat(sxssfWorkbook.createDataFormat().getFormat("0.00_"));
        for (BillPrintDto billPrintDto : billPrintDtoList) {
            StringBuilder sb = new StringBuilder();
            String nameByCode = "";
            if (billPrintDto.getContractType() != null)
                nameByCode = ContractType.getNameByCode(billPrintDto.getContractType());
            sb.append("客户名称: ").append(billPrintDto.getCustName()).append(" 合同类型: ").append(nameByCode);
            SXSSFSheet sheetFirst = sxssfWorkbook.createSheet();
            // 创建表并设置标题与打印时间 创建第一个sheet表
            SXSSFSheet firstSheet = createFirstSheetAndSetTitleAndTime(sxssfWorkbook, sheetFirst, billPrintDto.getTempletName(), billPrintDto.getBillMonth(), sb.toString());
            int dataStartRow = 3;
            int perBillEndRow = setPerBillData(sxssfWorkbook, firstSheet, billPrintDto, dataStartRow, headCellStyle, dataCellStyle, numDataCellStyle, columns);
            int disposableEndRow = setDisposableData(sxssfWorkbook, firstSheet, billPrintDto, perBillEndRow + 1, headCellStyle, dataCellStyle, numDataCellStyle);
        }
        // 创建第二sheet表
        String sheetTitleName = "List";
        SXSSFSheet sheetSecond = sxssfWorkbook.createSheet();
        SXSSFSheet secondSheet = createSecondSheetAndSetTitle(sxssfWorkbook, sheetSecond, sheetTitleName);
        int secondSheetDataStartRow = 0;
        List<BillReportSecondSheetDto> disPosList = billPrintDtoList.stream().map(BillPrintDto::getBillReportSecondSheetDtoList).flatMap(Collection::stream).collect(Collectors.toList());
        int secondSheetDataEndRow = setTableData(sxssfWorkbook, secondSheet, disPosList, secondSheetDataStartRow, headCellStyle, dataCellStyle, numDataCellStyle, false);
        billPrintDtoList.get(0).setTotalAmount(billPrintDtoList.stream().map(BillPrintDto::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        setEndData(sxssfWorkbook, secondSheet, secondSheetDataEndRow, billPrintDtoList.get(0));
        return sxssfWorkbook;
    }

    private static int setTableData(SXSSFWorkbook sxssfWorkbook, SXSSFSheet secondSheet, List<BillReportSecondSheetDto> billReportSecondSheetDtoList, int secondSheetDataStartRow, CellStyle headCellStyle, CellStyle dataCellStyle, CellStyle numDataCellStyle, Boolean custGroupTip) {
        /** 如果时集团或者客户打印,可能会有不同的汇款信息,那么汇款就在 每一行显示 */
        String[] SECOND_HEAD_USE = {};
        String[] SECOND_FIELD_USE = {};
        Integer[] SECOND_FIELD_COLUMN_WIDTH_USE = {};

        if (custGroupTip) {
            SECOND_HEAD_USE = SECOND_HEAD_GROUP;
            SECOND_FIELD_USE = SECOND_FIELD_GROUP;
            SECOND_FIELD_COLUMN_WIDTH_USE = SECOND_FIELD_COLUMN_WIDTH_GROUP;
        } else {
            SECOND_HEAD_USE = SECOND_HEAD;
            SECOND_FIELD_USE = SECOND_FIELD;
            SECOND_FIELD_COLUMN_WIDTH_USE = SECOND_FIELD_COLUMN_WIDTH;
        }


        // 创建table头部
        SXSSFRow row = secondSheet.createRow(++secondSheetDataStartRow);
        row.setHeight((short) 460);
        int headCellIndex = 0;
        // 设置表头信息
        for (int i = 0; i < SECOND_HEAD_USE.length; i++) {
            SXSSFCell cell = row.createCell(++headCellIndex);
            //            cell.setCellStyle(headCellStyle);
            cell.setCellValue(SECOND_HEAD_USE[i]);
            cell.setCellStyle(headCellStyle);
            secondSheet.setColumnWidth(i + 1, SECOND_FIELD_COLUMN_WIDTH_USE[i] * 400);
        }
        // 设置详细数据
        if (CollectionUtils.isNotEmpty(billReportSecondSheetDtoList)) {
            //序号
            int index = 0;
            //从第几行开始
            for (BillReportSecondSheetDto billReportSecondSheetDto : billReportSecondSheetDtoList) {
                int dataCellIndex = 0;
                SXSSFRow dataRow = secondSheet.createRow(++secondSheetDataStartRow);
                for (int i = 0; i < SECOND_FIELD_USE.length; i++) {
                    SXSSFCell cell = dataRow.createCell(++dataCellIndex);
                    cell.setCellStyle(dataCellStyle);
                    if (i == 0) {
                        cell.setCellValue(++index);
                    } else {
                        Object value = getGetMethod(billReportSecondSheetDto, SECOND_FIELD_USE[i]);
                        if (value instanceof BigDecimal) {
                            cell.setCellStyle(numDataCellStyle);
                            cell.setCellValue(((BigDecimal) value).doubleValue());
                        } else {
                            cell.setCellStyle(dataCellStyle);
                            cell.setCellValue(value == null ? "" : value.toString());
                        }
                    }
                }
            }
        }
        return secondSheetDataStartRow;
    }

    private static SXSSFSheet createSecondSheetAndSetTitle(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, String sheetTitleName) {
        sheet.setDisplayGridlines(false);
        // 设置secondSheet名
        setSecondSheetTitle(sxssfWorkbook, sheet, 0, 1, 12, sheetTitleName, true);
        return sheet;
    }

    private static void setSecondSheetTitle(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, int row, int firstCol, int lastCol, String sheetTitleName, boolean isBold) {
        sheet.addMergedRegion(new CellRangeAddress(row, row, firstCol, lastCol));
        SXSSFRow currentRow = sheet.createRow(row);
        currentRow.setHeight((short) 450);
        SXSSFCell currentRowCell = currentRow.createCell(firstCol);
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        Font font = sxssfWorkbook.createFont();
        font.setBold(isBold);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 13);
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        currentRowCell.setCellStyle(cellStyle);
        currentRowCell.setCellValue(sheetTitleName);
    }

    private static SXSSFSheet createFirstSheetAndSetTitleAndTime(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, String templateName, Integer billMonth, String custNameAndContractType) {
        // 删除栅格线
        sheet.setDisplayGridlines(false);
        //设置客户名称和合同类型
        setText(sxssfWorkbook, sheet, 0, 1, 6, custNameAndContractType, true);
        // 设置账单名
        setText(sxssfWorkbook, sheet, 1, 1, 6, "账单名：" + templateName, true);
        // 设置账单年月
        setText(sxssfWorkbook, sheet, 2, 1, 6, "账单年月 : " + billMonth, false);
        // 设置常规费用
        setText(sxssfWorkbook, sheet, 3, 1, 2, "常规费用：", true);
        return sheet;
    }

    private static int setPerBillData(
            SXSSFWorkbook sxssfWorkbook,
            SXSSFSheet sheet,
            BillPrintDto billPrintDto,
            int perBillRow,
            CellStyle headCellStyle,
            CellStyle dataCellStyle,
            CellStyle numDataCellStyle,
            List<Integer> columns
    ) {
        List<Integer> prodCodeList = new ArrayList<>();
        List<PerBillPrintDto> perBillPrintDtoList = billPrintDto.getPerBillPrintDtoList();
        if (CollectionUtils.isNotEmpty(perBillPrintDtoList)) {
            for (PerBillPrintDto perBillPrintDto : perBillPrintDtoList) {
                prodCodeList.addAll(perBillPrintDto.getPerBIllItemByProductCodeMap().keySet());
            }
            prodCodeList = prodCodeList.stream().distinct().sorted(Comparator.comparingInt(c -> c)).collect(Collectors.toList());

        }
        // 设置请求头与列宽
        SXSSFRow headRow = sheet.createRow(++perBillRow);
        SXSSFRow headRow2 = sheet.createRow(++perBillRow);
        headRow.setHeight((short) ROW_HEIGHT);
        int head1CellIndex = 0;

        List<String> detailHead = Lists.newArrayList(DETAIL_HEAD);
        List<String> detailField = Lists.newArrayList(DETAIL_FIELD);
        List<Integer> detailHeadColumnWidth = Lists.newArrayList(DETAIL_HEAD_COLUMN_WIDTH);

        List<String> detail_base_end_head = Lists.newArrayList(DETAIL_BASE_END_HEAD);
        List<String> detail_base_end_field = Lists.newArrayList(DETAIL_BASE_END_FIELD);
        List<Integer> detail_base_end_head_column_width = Lists.newArrayList(DETAIL_BASE_END_HEAD_COLUMN_WIDTH);

        List<String> detail_base_head = Lists.newArrayList(DETAIL_BASE_HEAD);
        List<String> detail_base_field = Lists.newArrayList(DETAIL_BASE_FIELD);
        List<Integer> detail_base_head_column_width = Lists.newArrayList(DETAIL_BASE_HEAD_COLUMN_WIDTH);

        // reon接口会传null值,客户端接口保底会传个size为0的list
        if (CollectionUtils.isNotEmpty(columns)) {

            List<Integer> detail_column_list = Lists.newArrayList(DETAIL_COLUMN_MAP.values());

            List<Integer> detail_base_column_list = Lists.newArrayList(DETAIL_BASE_END_COLUMN_MAP.values());

            List<Integer> detail_dynamic_column_list = Lists.newArrayList(DETAIL_DYNAMIC_COLUMN_MAP.values());

            for (Integer column : columns) {
                if (DETAIL_COLUMN_MAP.containsKey(column)) {
                    // 产品列去除选中的 剩下要去除的
                    detail_column_list.remove(DETAIL_COLUMN_MAP.get(column));
                } else if (DETAIL_BASE_END_COLUMN_MAP.containsKey(column)) {
                    detail_base_column_list.remove(DETAIL_BASE_END_COLUMN_MAP.get(column));
                } else if (DETAIL_DYNAMIC_COLUMN_MAP.containsKey(column)) {
                    detail_dynamic_column_list.remove(DETAIL_DYNAMIC_COLUMN_MAP.get(column));
                }
            }

            detail_column_list.forEach(a -> {
                detailHead.remove(DETAIL_HEAD[a]);
                detailField.remove(DETAIL_FIELD[a]);
                detailHeadColumnWidth.remove(DETAIL_HEAD_COLUMN_WIDTH[a]);
            });

            detail_base_column_list.forEach(a -> {
                detail_base_end_head.remove(DETAIL_BASE_END_HEAD[a]);
                detail_base_end_field.remove(DETAIL_BASE_END_FIELD[a]);
                detail_base_end_head_column_width.remove(DETAIL_BASE_END_HEAD_COLUMN_WIDTH[a]);
            });

            detail_dynamic_column_list.forEach(a -> {
                detail_base_head.remove(DETAIL_BASE_HEAD[a]);
                detail_base_field.remove(DETAIL_BASE_FIELD[a]);
                detail_base_head_column_width.remove(DETAIL_BASE_HEAD_COLUMN_WIDTH[a]);
            });
        }
        if (columns != null && columns.isEmpty()) {
            F_TO_K_COLUMN.forEach(a -> {
                detail_base_head.remove(DETAIL_BASE_HEAD[a]);
                detail_base_field.remove(DETAIL_BASE_FIELD[a]);
                detail_base_head_column_width.remove(DETAIL_BASE_HEAD_COLUMN_WIDTH[a]);
            });
        }
        // 设置基础表头信息
        for (int i = 0; i < detail_base_head.size(); i++) {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(4, 5, i + 1, i + 1);
            sheet.addMergedRegion(cellRangeAddress);
            regionStyle(cellRangeAddress, sheet);
            SXSSFCell cell = headRow.createCell(++head1CellIndex);
            cell.setCellValue(detail_base_head.get(i));
            cell.setCellStyle(headCellStyle);
            sheet.setColumnWidth(i + 1, detail_base_head_column_width.get(i) * 400);
        }
        // 设置 明细表头
        for (Integer prodCode : prodCodeList) {
            String name = InsuranceIRatioProductCode.getName(prodCode);
            int startIndex = head1CellIndex + 1;
            int endIndex = startIndex + (detailHead.size() - 1);
            CellRangeAddress cellRangeAddress = new CellRangeAddress(4, 4, startIndex, endIndex);
            regionStyle(cellRangeAddress, sheet);
            sheet.addMergedRegion(cellRangeAddress);
            SXSSFCell cell1 = headRow.createCell(startIndex);
            cell1.setCellStyle(headCellStyle);
            cell1.setCellValue(name);
            for (int i = 0; i < detailHead.size(); i++) {
                SXSSFCell cell = headRow2.createCell(++head1CellIndex);
                cell.setCellValue(detailHead.get(i));
                cell.setCellStyle(headCellStyle);
                sheet.setColumnWidth(head1CellIndex, detailHeadColumnWidth.get(i) * 400);
            }
        }

        for (int i = 0; i < detail_base_end_head.size(); i++) {
            head1CellIndex++;
            CellRangeAddress cellRangeAddress = new CellRangeAddress(4, 5, head1CellIndex, head1CellIndex);
            sheet.addMergedRegion(cellRangeAddress);
            regionStyle(cellRangeAddress, sheet);
            SXSSFCell cell = headRow.createCell(head1CellIndex);
            cell.setCellValue(detail_base_end_head.get(i));
            cell.setCellStyle(headCellStyle);
            sheet.setColumnWidth(head1CellIndex, detail_base_end_head_column_width.get(i) * 400);
        }

        // 设置详细信息
        if (CollectionUtils.isNotEmpty(perBillPrintDtoList)) {
            int index = 0;
            for (int i = 0; i < perBillPrintDtoList.size(); i++) {
                SXSSFRow row = sheet.createRow(++perBillRow);
                row.setHeight((short) ROW_HEIGHT);
                PerBillPrintDto perBillPrintDto = perBillPrintDtoList.get(i);
                int detailCellIndex = 0;

                // 如果是最后一条基础信息展示为合计
                if (i == perBillPrintDtoList.size() - 1) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(
                            perBillRow,
                            perBillRow,
                            1,
                            detail_base_field.size()
                    );
                    sheet.addMergedRegion(cellRangeAddress);
                    regionStyle(cellRangeAddress, sheet);
                    SXSSFCell cell = row.createCell(1);
                    CellStyle fieldCellStyle = getFieldCellStyle(sxssfWorkbook, true);
                    fieldCellStyle.setAlignment(HorizontalAlignment.LEFT);
                    cell.setCellStyle(fieldCellStyle);
                    cell.setCellValue("常规费用合计： RMB   " + ConvertUpMoney.toChinese(String.valueOf(perBillPrintDto.getIndTotal())));
                    detailCellIndex = detail_base_field.size();
                } else {
                    // 填入基本信息
                    for (int i1 = 0; i1 < detail_base_field.size(); i1++) {
                        SXSSFCell cell = row.createCell(++detailCellIndex);
                        cell.setCellStyle(dataCellStyle);
                        if (i1 == 0) {
                            cell.setCellValue(++index);
                        } else {
                            Object value = getGetMethod(perBillPrintDto, detail_base_field.get(i1));
                            cell.setCellValue(value == null ? "" : value.toString());
                        }
                    }
                }

                // 填入产品明细信息
                for (Integer prodCode : prodCodeList) {
                    PerBillItemDto practiceReportDetailVo = perBillPrintDto.getPerBIllItemByProductCodeMap()
                            .getOrDefault(prodCode, new PerBillItemDto());
                    for (String fieldName : detailField) {
                        SXSSFCell cell = row.createCell(++detailCellIndex);
                        Object value = getGetMethod(practiceReportDetailVo, fieldName);
                        if (value instanceof BigDecimal) {
                            cell.setCellStyle(numDataCellStyle);
                            cell.setCellValue(((BigDecimal) value).doubleValue());
                        } else {
                            cell.setCellStyle(dataCellStyle);
                            cell.setCellValue(value == null ? "" : value.toString());
                        }
                    }
                }

                // 填入基本信息
                for (String field : detail_base_end_field) {
                    SXSSFCell cell = row.createCell(++detailCellIndex);
                    Object value = getGetMethod(perBillPrintDto, field);
                    if (value instanceof BigDecimal) {
                        cell.setCellStyle(numDataCellStyle);
                        cell.setCellValue(((BigDecimal) value).doubleValue());
                    } else {
                        cell.setCellStyle(dataCellStyle);
                        cell.setCellValue(value == null ? "" : value.toString());
                    }
                }
            }
        }

        return perBillRow;
    }

    private static int setDisposableData(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, BillPrintDto billPrintDto, int disposableRow, CellStyle headCellStyle, CellStyle dataCellStyle, CellStyle numDataCellStyle) {
        List<DisposableItemApprovalVo> billDisposableApprovalList = billPrintDto.getBillDisposableApprovalList();
        DisposableItemApprovalVo disposableItemApprovalVoTotal = new DisposableItemApprovalVo();
        BigDecimal tax = BigDecimal.ZERO;
        BigDecimal amount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(billDisposableApprovalList)) {
            for (DisposableItemApprovalVo disposableItemApprovalVo : billDisposableApprovalList) {
                tax = tax.add(disposableItemApprovalVo.getTax());
                amount = amount.add(disposableItemApprovalVo.getAmount());
            }
        }
        disposableItemApprovalVoTotal.setTax(tax);
        disposableItemApprovalVoTotal.setAmount(amount);
        billDisposableApprovalList.add(disposableItemApprovalVoTotal);

        setText(sxssfWorkbook, sheet, ++disposableRow, 1, 2, "一次性费用：", true);
        SXSSFRow disposableHeadRow = sheet.createRow(++disposableRow);
        disposableHeadRow.setHeight((short) ROW_HEIGHT);
        int disposableHeadCellIndex = 0;
        for (int i1 = 0; i1 < DISPOSABLE_HEAD.length; i1++) {
            SXSSFCell cell = disposableHeadRow.createCell(++disposableHeadCellIndex);
            cell.setCellStyle(headCellStyle);
            cell.setCellValue(DISPOSABLE_HEAD[i1]);
        }
        int disposableIndex = 0;
        for (int i = 0; i < billDisposableApprovalList.size(); i++) {
            int disposableDetailCellIndex = 0;
            SXSSFRow disposableDetailRow = sheet.createRow(++disposableRow);
            disposableDetailRow.setHeight((short) ROW_HEIGHT);
            DisposableItemApprovalVo disposableItemApprovalVo = billDisposableApprovalList.get(i);
            if (i == billDisposableApprovalList.size() - 1) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(disposableRow, disposableRow, 1, 3);
                sheet.addMergedRegion(cellRangeAddress);
                regionStyle(cellRangeAddress, sheet);
                SXSSFCell cell = disposableDetailRow.createCell(1);
                CellStyle fieldCellStyle = getFieldCellStyle(sxssfWorkbook, true);
                fieldCellStyle.setAlignment(HorizontalAlignment.LEFT);
                cell.setCellStyle(fieldCellStyle);
                cell.setCellValue("一次性费用合计：RMB  " + ConvertUpMoney.toChinese(amount.toString()));
                disposableDetailCellIndex = 3;
            }
            for (int i1 = disposableDetailCellIndex; i1 < DISPOSABLE_FIELD.length; i1++) {
                SXSSFCell cell = disposableDetailRow.createCell(++disposableDetailCellIndex);
                if (i1 == 0) {
                    cell.setCellStyle(dataCellStyle);
                    cell.setCellValue(++disposableIndex);
                } else {
                    Object value = getGetMethod(disposableItemApprovalVo, DISPOSABLE_FIELD[i1]);
                    if (value instanceof BigDecimal) {
                        cell.setCellStyle(numDataCellStyle);
                        cell.setCellValue(((BigDecimal) value).doubleValue());
                    } else {
                        cell.setCellStyle(dataCellStyle);
                        cell.setCellValue(value == null ? "" : value.toString());
                    }
                }
            }
        }
        return disposableRow;
    }

    private static void setEndData(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, int startRow, BillPrintDto billPrintDto) {
        CellStyle textStyle = sxssfWorkbook.createCellStyle();
        Font textFont = sxssfWorkbook.createFont();
        textFont.setFontName("宋体");
        textFont.setBold(true);
        textFont.setFontHeightInPoints((short) 13);
        textStyle.setFont(textFont);
        textStyle.setAlignment(HorizontalAlignment.LEFT);

        sheet.addMergedRegion(new CellRangeAddress(startRow + 2, startRow + 2, 3, 5));
        SXSSFRow billTotalRow = sheet.createRow(startRow + 2);
        billTotalRow.setHeight((short) 460);
        SXSSFCell billTotalCell1 = billTotalRow.createCell(1);
        billTotalCell1.setCellStyle(textStyle);
        billTotalCell1.setCellValue("账单总计：");
        SXSSFCell billTotalCell2 = billTotalRow.createCell(2);
        billTotalCell2.setCellStyle(textStyle);
        billTotalCell2.setCellValue(billPrintDto.getTotalAmount().toString());
        SXSSFCell billTotalCell3 = billTotalRow.createCell(3);
        billTotalCell3.setCellStyle(textStyle);
        billTotalCell3.setCellValue(ConvertUpMoney.toChinese(String.valueOf(billPrintDto.getTotalAmount())));


        if (!billPrintDto.getCustGroupTip()) // 集团打印,就不需要最后一列 也就是为false时才打印付款信息
        {
            sheet.addMergedRegion(new CellRangeAddress(startRow + 4, startRow + 4, 1, 3));
            SXSSFRow remittanceInformationRow = sheet.createRow(startRow + 4);
            remittanceInformationRow.setHeight((short) 460);
            SXSSFCell remittanceCell = remittanceInformationRow.createCell(1);
            remittanceCell.setCellStyle(textStyle);
            remittanceCell.setCellValue("汇款信息------");

            sheet.addMergedRegion(new CellRangeAddress(startRow + 5, startRow + 5, 1, 3));
            SXSSFRow custRow = sheet.createRow(startRow + 5);
            custRow.setHeight((short) 460);
            SXSSFCell custCell = custRow.createCell(1);
            custCell.setCellStyle(textStyle);
            custCell.setCellValue("账户全称:" + billPrintDto.getCompName());

            sheet.addMergedRegion(new CellRangeAddress(startRow + 6, startRow + 6, 1, 3));
            SXSSFRow bankRow = sheet.createRow(startRow + 6);
            bankRow.setHeight((short) 460);
            SXSSFCell bankCell = bankRow.createCell(1);
            bankCell.setCellStyle(textStyle);
            bankCell.setCellValue("开户银行：" + billPrintDto.getBankName());

            sheet.addMergedRegion(new CellRangeAddress(startRow + 7, startRow + 7, 1, 3));
            SXSSFRow bankNoRow = sheet.createRow(startRow + 7);
            bankNoRow.setHeight((short) 460);
            SXSSFCell bankNoCell = bankNoRow.createCell(1);
            bankNoCell.setCellStyle(textStyle);
            bankNoCell.setCellValue("银行账号：" + billPrintDto.getBankNo());
        }
    }

    private static void regionStyle(CellRangeAddress cellRangeAddress, SXSSFSheet sheet) {
        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sheet); // 下边框
        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sheet); // 左边框
        RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sheet); // 有边框
        RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sheet); // 上边框
        RegionUtil.setBottomBorderColor(IndexedColors.DARK_BLUE.getIndex(), cellRangeAddress, sheet);
        RegionUtil.setLeftBorderColor(IndexedColors.DARK_BLUE.getIndex(), cellRangeAddress, sheet);
        RegionUtil.setRightBorderColor(IndexedColors.DARK_BLUE.getIndex(), cellRangeAddress, sheet);
        RegionUtil.setTopBorderColor(IndexedColors.DARK_BLUE.getIndex(), cellRangeAddress, sheet);
    }

    private static void setText(SXSSFWorkbook sxssfWorkbook, SXSSFSheet sheet, int row, int firstCol, int lastCol, String text, boolean isBold) {
        sheet.addMergedRegion(new CellRangeAddress(row, row, firstCol, lastCol));
        SXSSFRow textRow = sheet.createRow(row);
        textRow.setHeight((short) 450);
        SXSSFCell textCell = textRow.createCell(firstCol);
        CellStyle textStyle = sxssfWorkbook.createCellStyle();
        Font textFont = sxssfWorkbook.createFont();
        textFont.setFontName("宋体");
        textFont.setBold(isBold);
        textFont.setFontHeightInPoints((short) 13);
        textStyle.setFont(textFont);
        textStyle.setAlignment(HorizontalAlignment.LEFT);
        textCell.setCellStyle(textStyle);
        textCell.setCellValue(text);
    }


    private static CellStyle getFieldCellStyle(SXSSFWorkbook wb, Boolean isHead) {
        CellStyle cellStyle = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontName("宋体");
        if (isHead) {
            font.setBold(true);
            font.setFontHeightInPoints((short) 11);
            cellStyle.setWrapText(true);
        } else {
            font.setFontHeightInPoints((short) 10);
        }
        cellStyle.setFont(font);
        /** 如果有双击单元格合并问题,这三个要一起用 大概率可以解决问题 */
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        cellStyle.setWrapText(true);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);// 左边框
        cellStyle.setBorderTop(BorderStyle.THIN);// 上边框
        cellStyle.setBorderRight(BorderStyle.THIN);// 右边框
        cellStyle.setBottomBorderColor(IndexedColors.DARK_BLUE.getIndex());
        cellStyle.setLeftBorderColor(IndexedColors.DARK_BLUE.getIndex());
        cellStyle.setTopBorderColor(IndexedColors.DARK_BLUE.getIndex());
        cellStyle.setRightBorderColor(IndexedColors.DARK_BLUE.getIndex());

        return cellStyle;
    }

    private static Object getGetMethod(Object ob, String name) {
        try {
            Method[] m = ob.getClass().getMethods();
            for (int i = 0; i < m.length; i++) {
                if (("get" + name).toLowerCase().equals(m[i].getName().toLowerCase())) {
                    return m[i].invoke(ob);
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     关闭流等
     */
    public static void closeInfo(HttpServletResponse response, SXSSFWorkbook sxssfWorkbook, String fileName) throws IOException {
        // 设置头信息
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        // 设置成xlsx格式
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
        // 创建一个输出流
        ServletOutputStream outputStream = response.getOutputStream();
        // 写入数据
        sxssfWorkbook.write(outputStream);
        // 关闭
        outputStream.close();
        sxssfWorkbook.close();
    }
}
