package com.reon.hr.sp.report.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ServiceNumPeopleReport {
    private Long id;

    /**
     集团id
     */
    private Long custGroupId;

    /**
     客户id
     */
    private Long custId;

    /**
     合同编号
     */
    private String contractNo;

    /**
     销售专员
     */
    private String seller;

    /**
     客服专员
     */
    private String commissioner;

    /**
     所属部门
     */
    private Integer sellerDepartment;

    /**
     服务人数
     */
    private Integer num;

    /**
     年月
     */
    private Integer yearMonthForSnp;

    /**
     创建人
     */
    private String creator;

    /**
     创建时间
     */
    private Date createTime;

    /**
     修改人
     */
    private String updater;

    /**
     修改时间
     */
    private Date updateTime;
    /** 签单地,也就是销售所在城市 */
    private String signPlace;
    /**
     删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    private Integer contractType;
}