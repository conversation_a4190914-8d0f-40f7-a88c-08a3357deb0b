package com.reon.hr.sp.customer.dao.insurancePractice;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.customer.dto.insurancePractice.ReportPullInsurancePracticeDto;
import com.reon.hr.api.customer.vo.ProdHandleInfoVo;
import com.reon.hr.api.customer.vo.insurancePractice.PracticeCollectPayVo;
import com.reon.hr.sp.customer.entity.insurancePractice.ProdHandleInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ProdHandleInfoMapper extends BaseMapper<ProdHandleInfo> {
    int deleteByPrimaryKey(Long id);

    Integer insert(ProdHandleInfo record);

    int insertSelective(ProdHandleInfo record);

    ProdHandleInfo selectByPrimaryKey(Long id);
    List<ProdHandleInfo> selectByPracticeIdAndProdCode(@Param("practiceId") String practiceId,@Param("ratio") String ratio ,@Param("yearMonth")Integer yearMonth);
    List<ProdHandleInfo> selectByOrderNoAndProdCode(@Param("orderNo") String orderNo,@Param("prodCode") Integer prodCode,@Param("yearMonth")Integer yearMonth);

    int updateByPrimaryKeySelective(ProdHandleInfo record);
    int updateProdByPrimaryKeySelective(ProdHandleInfoVo record);

    int updateEndMonthById(@Param("endMonth") Integer endMonth,@Param("id") Long id);

    int updateByPrimaryKey(ProdHandleInfo record);

    List<PracticeCollectPayVo> selectByReportPullCollectPayParam(ReportPullInsurancePracticeDto reportPullInsurancePracticeDto);
    List<PracticeCollectPayVo> selectByReportPullCollectByPracticeIds(@Param("ids")List<Long> practiceIds);
    List<ProdHandleInfoVo> selectProdHanleInfoByPracticeIds(@Param("ids")List<Long> practiceIds);

    List<ProdHandleInfoVo> getProdHandleInfoData(Long  id);

    int updateByInsurancePracticeId(ProdHandleInfo record);
    int updateByInsurancePracticeIds(@Param("ids")List<Long> practIds,@Param("endMonth") Integer endMonth,@Param("updater") String updater);

	Integer insertList(@Param("list") List<ProdHandleInfo> insertProdHandleInfos);


    int updateBatchProHandlerInfo(@Param("list")List<ProdHandleInfoVo> prodHandleInfoVoList);
    int updateBatchStopProHandlerInfo(@Param("list")List<ProdHandleInfoVo> prodHandleInfoVoList);

	Boolean delByPracticeIdList(@Param("list") List<Long> delIdList);

    int deleteByPhysics(@Param("list") List<Long> id);

    int deleteByProdIds(@Param("list") List<Long> id);
    Set<String> getRatioCodeSet(@Param("list") List<String> ratioList);

    List<String> selectOrderNoByRatioCode(@Param("rationCode") String rationCode,@Param("orderNoSet") Set<String> orderNoSet);
}