package com.reon.hr.sp.bill.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Exception.class)
public class PerformInsuranceBillMonitorJob implements SimpleJob {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private IInsuranceBillService insuranceBillService;

	@Override
	public void execute(ShardingContext shardingContext) {
		try {
			logger.info("======================" + shardingContext.getJobName() + " start=============================");
			/** 执行监测表中数据 */
			insuranceBillService.performInsuranceBillMonitor();
			logger.info("======================" + shardingContext.getJobName() + " end=============================");
		} catch (Exception e) {
			logger.error("monitor insurance bill exception:", e);
		}
	}
}
