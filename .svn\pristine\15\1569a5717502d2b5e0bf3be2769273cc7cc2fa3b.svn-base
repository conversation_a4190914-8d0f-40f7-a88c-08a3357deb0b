package com.reon.hr.api.bill.vo.insurancePractice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PracticePayDetailVo implements Serializable {
    /**
     * '主键ID'
     */
    private Long id;
    /**
     * 实做报表Id
     */
    private Long billId;
    /**
     * '支付申请ID'
     */
    private Long payApplyId;
    private String appCom;
    private String appComName;
    /**
     * '报表ID'
     */
    private Long reportId;

    /**
     * 审批状态
     */
    private Integer appStatus;
    /**
     * '报表年月'
     */
    private Integer reportMonth;
    /**
     * '实做ID'
     */
    private Long practiceId;
    /**
     * '福利包名称'
     */
    private String packCode;
    private List<String> packCodes;
    /**
     * '福利办理方'
     */
    private String orgCode;
    private List<String> orgCodes;
    /**
     * '客户ID'
     */
    private Long custId;
    /**
     * '订单号'
     */
    private String orderNo;
    private String empName;
    private String certNo;
    /**
     * '产品类型'
     */
    private Integer prodCode;
    /**
     * '费用类型(1:汇缴，2：补缴)'
     */
    private Integer feeType;
    /**
     * '金额类型(1:企业金额，2：个人金额，3：企业滞纳金，4：个人滞纳金)'
     */
    private Integer amtType;
    /**
     * '金额'
     */
    private BigDecimal amount;
    /**
     * 记录生成时间
     */
    private Long operateTime;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;


    private List<Long> ids;


}
