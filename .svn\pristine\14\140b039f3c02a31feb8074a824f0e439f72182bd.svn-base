package com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice
 * @ClassName: SupplierPractice
 * @Author: Administrator
 * @Description:
 * @Date: 2023/5/5 15:08
 * @Version: 1.0
 */
@Data
public class SupplierPractice implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 账单模板id
     */
    private Long templetId;

    /**
     * 收费模板id
     */
    private Long revTempId;

    /**
     * 城市code
     */
    private Integer cityCode;

    /**
     * 客户id
     */
    private Long custId;

    /**
     * 员工id
     */
    private Long empId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 增员确认人
     */
    private String handler;

    /**
     * 增员确认时间
     */
    private Date procTime;

    /**
     * 增员确认月
     */
    private Integer addMonth;

    /**
     * 增员备注
     */
    private String addRemark;

    /**
     * 办停人
     */
    private String termiMan;

    /**
     * 停办备注
     */
    private String termiRemark;

    /**
     * 办停时间
     */
    private Date termiDate;

    /**
     * 福利起始月
     */
    private Integer startMonth;

    /**
     * 福利截止月
     */
    private Integer endMonth;

    /**
     * 办理状态(1、增员待确认，2、增员已确认，3、减员待确认，4、减员已确认)
     */
    private int handleStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(y:已删除，n:未删除)
     */
    private String delFlag;

    private Integer editFlag;
}
