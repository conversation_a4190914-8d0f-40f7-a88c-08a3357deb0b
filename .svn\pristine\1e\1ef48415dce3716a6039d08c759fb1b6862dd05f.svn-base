package com.reon.hr.api.customer.vo.insurancePractice;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@TableName("practice_update_log")
public class PracticeUpdateLogVo implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long practiceId;

    private String updateRemark;

    private String updateProcess;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}
