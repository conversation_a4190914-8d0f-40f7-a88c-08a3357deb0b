var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        $ = layui.$,
        tableSelect = layui.tableSelect;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


    $(document).ready(function () {
        setTimeout(function () {
            form.render('select');
        }, 200);
        form.render('select');
    });


    form.on('submit(commit)', function (data) {
        saveForm('commit', data);
    });
    //保存函数
    function saveForm(type, data) {
        //系统
        if (data.field.optType=='listFlag') {
            //公式
            data.field.listFlag = data.field.displayFlag
            data.field.displayFlag=null
        }

        var url = "";
        if (type === 'commit') {
            url=ML.contextPath + "/customer/salary/salaryItem/updateSalaryItemDisplay"
        }
        ML.ajax(url, {'paramData': JSON.stringify(data.field)}, function (data) {
            if (data.code == 0) {
                layer.msg("保存成功");
                layer.closeAll('iframe');
            } else if (data.code == -1) {
                return layer.msg(data.msg);
            }else{return layer.msg("未知错误!!")}
        }, 'POST')

    }

    //关闭按钮
    $(document).on("click", "#close", function () {
        layer.closeAll('iframe');
    });


});