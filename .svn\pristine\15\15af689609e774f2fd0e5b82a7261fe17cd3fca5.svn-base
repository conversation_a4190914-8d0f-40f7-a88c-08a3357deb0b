package com.reon.hr.api.customer.vo.export;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InsurancePracticeExportVo implements Serializable  {

    private static final long serialVersionUID = 356153794823135693L;

    @Excel(name="雇员姓名")
    private String empName;
    @Excel(name="证件号码")
    private String certNo;
    @Excel(name="手机号码")
    private String mobile;
    @Excel(name="福利办理方")
    private String orgName;
    @Excel(name="福利包名称")
    private String packName;
    @Excel(name="福利包编号")
    private String packCode;
    @Excel(name="订单编号")
    private String orderNo;
    @Excel(name="客户名称")
    private String custName;
    @Excel(name="客户编号")
    private String custNo;
    @Excel(name="福利办理月")
    private Integer addMonth;
    @Excel(name="福利起始月")
    private Integer startMonth;
    @Excel(name = "服务区域类型", readConverterExp = "1=本地,2=全国")
    private Integer dispatchType;
    @Excel(name="小合同")
    private String contractAreaName;
    @Excel(name="派单方")
    private String signComName;
    @Excel(name="接单方")
    private String receivingName;
    @Excel(name="派单方客服")
    private String commissioner;
    @Excel(name="接单方客服")
    private String receivingMan;
    @Excel(name="申请时间",dateFormat = "yyyy-MM-dd")
    private Date applyTime;
    @Excel(name="申请人")
    private String applyMan;
    @Excel(name = "账号")
    private String acctNo;
    @Excel(name="新增方式",readConverterExp="1=转入,2=新开,3=一次性补缴")
    private Byte addMethod;
//    @Excel(name="办理月")
//    private Integer addMonth;
    @Excel(name="备注")
    private String firstRemark;
//    @Excel(name="入职日期",dateFormat = "yyyy-MM-dd")
//    private Date entryDate;
}
