package com.reon.hr.api.customer.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年09月26日
 * @Version 1.0
 */
@Data
public class CustomerOperateLogVo implements Serializable {

    private String custNo;
    /**
     * cust_id
     */
    private Long custId;

    /**
     * 修改前名字
     */
    private String oldCustName;

    private String newCustName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作类型 1:创建错误 2:工商名称变动
     */
    private Integer oprType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 文件
     */
    private String fileId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String custName;

}
