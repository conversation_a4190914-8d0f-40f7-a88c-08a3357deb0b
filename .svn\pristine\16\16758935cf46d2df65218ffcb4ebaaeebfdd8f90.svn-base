package com.reon.ehr;

import com.reon.ehr.api.sys.utils.SecurityUtils;
import com.reon.ehr.sp.sys.domain.entity.SysUser;
import com.reon.ehr.sp.sys.mapper.SysUserMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring-test-config-cx.xml")
public class DealPassword {
    Logger log = LoggerFactory.getLogger(DealPassword.class);
    @Resource
    private SysUserMapper sysUserMapper;



    @Test
    public void dealPassword() {
        /** 把用户密码全部置为123456 */
        String password = SecurityUtils.encryptPassword("123456");
        List<SysUser> sysUsers = sysUserMapper.selectUserList(null);
        for (SysUser sysUser : sysUsers) {
            sysUserMapper.resetUserPwd(sysUser.getUserName(),password);
        }
    }
}
