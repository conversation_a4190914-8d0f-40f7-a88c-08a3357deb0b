package com.reon.hr.modules.base.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISocialWagsMaintainWrapperService;
import com.reon.hr.api.base.utils.ExcelUtil;
import com.reon.hr.api.base.vo.MinimumWageChildVo;
import com.reon.hr.api.base.vo.PolicyFileUploadVo;
import com.reon.hr.api.base.vo.SocialWagsMaintainVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.modules.common.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 社保和最低工资维护
 * @Date 2023/1/29 0029 下午 02:08
 * @Version 1.0
 */

@RestController
@RequestMapping("/socialWagsMaintain")
public class SocialWagsMaintainController extends BaseController {


    @Autowired
    private ISocialWagsMaintainWrapperService iSocialWagsMaintainWrapperService;

    @Autowired
    private IAreaResourceWrapperService areaResourceWrapperService;
    @Autowired
    private IUserWrapperService iUserWrapperService;


    /**
     * 转到社保最低工资维护主页面
     *
     * @return {@link Object}
     */
    @RequestMapping("/gotoSocialWagsMaintainPage")
    public ModelAndView gotoSocialWagsMaintainPage() {
        return new ModelAndView("/base/socialWagsMaintain/socialWagsMaintainPage");
    }

    /**
     * 新增信息页面
     *
     * @return {@link ModelAndView}
     */
    @RequestMapping("/gotoAddSocialWagsMaintainPage")
    public ModelAndView gotoAddSocialWagsMaintainPage() {
        return new ModelAndView("/base/socialWagsMaintain/addSocialWagsMaintainPage");
    }


    /**
     * 修改信息页面
     *
     * @return {@link ModelAndView}
     */
    @RequestMapping("/gotoUpdateSocialWagsMaintainPage")
    public ModelAndView gotoUpdateSocialWagsMaintainPage() {
        return new ModelAndView("/base/socialWagsMaintain/updateSocialWagsMaintainPage");
    }


    /**
     * 查询接口
     *
     * @param page  页面
     * @param limit 限制
     * @return {@link Object}
     */
    @RequestMapping("/getAllSocialWagsMaintain")
    public Object getAllSocialWagsMaintain(@RequestParam("page") int page, @RequestParam("limit") int limit, SocialWagsMaintainVo socialWagsMaintainVo, HttpSession session) {
        //把查询条件存入session,方便下面方法导出结果一致
        session.setAttribute("socialWagsMaintainVo", socialWagsMaintainVo);
        if (StringUtils.isNotBlank(socialWagsMaintainVo.getServiceSiteCode())) {
            String serviceSiteCode = socialWagsMaintainVo.getServiceSiteCode().split("_")[1];
            socialWagsMaintainVo.setServiceSiteCode(serviceSiteCode);
        }
        Page<SocialWagsMaintainVo> allSocialWagsMaintain = iSocialWagsMaintainWrapperService.getAllSocialWagsMaintain(page, limit, socialWagsMaintainVo);
        return LayuiReplay.success(allSocialWagsMaintain.getTotal(), allSocialWagsMaintain.getRecords());
    }


    /**
     * 增加/修改
     *
     * @param socialWagsMaintainVo
     * @return
     */
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Object saveOrUpdate(@RequestBody SocialWagsMaintainVo socialWagsMaintainVo) {
        String cityName = areaResourceWrapperService.getCityName(socialWagsMaintainVo.getCityCode());
        String loginName = getSessionUser().getLoginName();
        socialWagsMaintainVo.setCityName(cityName);
        socialWagsMaintainVo.setCreator(loginName);
        for (int i = 0; i < socialWagsMaintainVo.getWagsMaintainChildList().size(); i++) {
            if (socialWagsMaintainVo.getWagsMaintainChildList().get(i).getEndTime() < socialWagsMaintainVo.getWagsMaintainChildList().get(i).getStartTime()) {
                return LayuiReplay.error("结束时间不可小于开始时间!!!");
            }
            for (int j = i + 1; j < socialWagsMaintainVo.getWagsMaintainChildList().size(); j++) {
                MinimumWageChildVo slotA = socialWagsMaintainVo.getWagsMaintainChildList().get(i);
                MinimumWageChildVo slotB = socialWagsMaintainVo.getWagsMaintainChildList().get(j);
                if (slotA.overlapsWith(slotB)) {
                    return LayuiReplay.error("同一个服务网点和城市下,每条数据之间的开始时间和结束时间不能重叠!!!");
                }
            }
        }
        if (socialWagsMaintainVo.getOptType().equals("add")) {
            String serviceSiteCode = socialWagsMaintainVo.getServiceSiteCode().split("_")[1];
            int count = iSocialWagsMaintainWrapperService.selectCountByCityCodeAndServiceSiteCode(socialWagsMaintainVo.getCityCode(), serviceSiteCode);
            if (count > 0) {
                return LayuiReplay.error("此城市和网点已经存在,请去修改页面新增");
            }
            socialWagsMaintainVo.setServiceSiteCode(serviceSiteCode);
            iSocialWagsMaintainWrapperService.insertSocialWagsMaintain(socialWagsMaintainVo);

        } else if (socialWagsMaintainVo.getOptType().equals("update")) {
            iSocialWagsMaintainWrapperService.updateSocialWagsMaintainById(socialWagsMaintainVo);
        }
        return LayuiReplay.success();
    }




    /**
     * 删除一个或多个社保套餐
     *
     * @param id
     * @param type 1:删除主表数据 2删除子表数据
     * @return
     */
    @RequestMapping("delete")
    public Object delete(@RequestParam(value = "id")Long id,@RequestParam("type")Integer type) {
        boolean flag = iSocialWagsMaintainWrapperService.deleteMinimumById(id, type);
        if (flag) {
            return LayuiReplay.success();
        }
        return LayuiReplay.error();
    }

    @RequestMapping("/export")
    public void export(HttpSession session, HttpServletResponse response) throws IOException {
        SocialWagsMaintainVo vo = (SocialWagsMaintainVo) session.getAttribute("socialWagsMaintainVo");
        List<SocialWagsMaintainVo> allSocialWagsMaintain = iSocialWagsMaintainWrapperService.getAllSocialWagsMaintain(vo);
        Map<String, String> userNameMap = iUserWrapperService.getAllUserMap();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);

        for (SocialWagsMaintainVo socialWagsMaintainVo : allSocialWagsMaintain) {
            socialWagsMaintainVo.setCreator(userNameMap.get(socialWagsMaintainVo.getCreator()));
            socialWagsMaintainVo.setUpdater(userNameMap.get(socialWagsMaintainVo.getUpdater()));
            socialWagsMaintainVo.setCreateTimeS(DateUtil.format(socialWagsMaintainVo.getCreateTime(), DateUtil.DATE_FORMAT_LONG));
            socialWagsMaintainVo.setUpdateTimeS(DateUtil.format(socialWagsMaintainVo.getUpdateTime(), DateUtil.DATE_FORMAT_LONG));
            socialWagsMaintainVo.setMwCreator(userNameMap.get(socialWagsMaintainVo.getMwCreator()));
            socialWagsMaintainVo.setMwUpdater(userNameMap.get(socialWagsMaintainVo.getMwUpdater()));
            socialWagsMaintainVo.setMwCreateTimeS(DateUtil.format(socialWagsMaintainVo.getMwCreateTime(), DateUtil.DATE_FORMAT_LONG));
            socialWagsMaintainVo.setMwUpdateTimeS(DateUtil.format(socialWagsMaintainVo.getMwUpdateTime(), DateUtil.DATE_FORMAT_LONG));
        }
        String fileName = getSessionUser().getUserName() + "-社保和最低工资维护-" + DateUtil.format(new Date(), DateUtil.DATE_FORMAT_YYYYMMDD);
        try {
            ExcelUtil<SocialWagsMaintainVo> excelUtil = new ExcelUtil<>(SocialWagsMaintainVo.class);
            Workbook workbook = excelUtil.exportExcel(allSocialWagsMaintain, fileName);
            ExcelUtil.closeInfo(response, workbook, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("/getDataById")
    @ResponseBody
    public LayuiReplay getDataById(Long id){
        SocialWagsMaintainVo socialWagsMaintainById = iSocialWagsMaintainWrapperService.getSocialWagsMaintainById(id);
        return LayuiReplay.success(socialWagsMaintainById);
    }

    @RequestMapping("/getMinimumWageByCityCodeAndMonth")
    public Object egetMinimumWageByCityCodeAndMonth(Integer cityCode,Integer month,String receiving){
        SocialWagsMaintainVo minimumWageByCityCodeAndMonth = iSocialWagsMaintainWrapperService.getMinimumWageByCityCodeAndMonth(cityCode, month,receiving);
        if (minimumWageByCityCodeAndMonth==null){
            return new LayuiReplay<>(ResultEnum.ERR.getCode(),ResultEnum.ERR.getMsg());
        }
        return new LayuiReplay<>(ResultEnum.OK.getCode(),ResultEnum.OK.getMsg(),minimumWageByCityCodeAndMonth);
    }
}
