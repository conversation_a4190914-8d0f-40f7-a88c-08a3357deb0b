package com.reon.hr.api.customer.vo;

import com.reon.hr.api.customer.anno.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年02月08日
 * @Version 1.0
 */

@Data
public class OrderAndInsuranceDiffExportVo implements Serializable {

    private Long id;
    private Long custId;


    @ExcelColumn(name = "客户编号", columnWidth = 200)
    private String custNo;

    @ExcelColumn(name = "客户名称", columnWidth = 200)
    private String custName;

    @ExcelColumn(name = "订单号    ", columnWidth = 200)
    private String orderNo;

    @ExcelColumn(name = "员工姓名", columnWidth = 200)
    private String name;

    @ExcelColumn(name = "身份证号", columnWidth = 200)
    private String certNo;

    private Integer productCode;
    
    @ExcelColumn(name = "对比月份", columnWidth = 200)
    private Integer month;

    @ExcelColumn(name = "社保/公积金产品", columnWidth = 200)
    private String productName;

    @ExcelColumn(name = "订单金额", columnWidth = 200)
    private BigDecimal orderFee;

    @ExcelColumn(name = "实做金额", columnWidth = 200)
    private BigDecimal insuranceFee;

    @ExcelColumn(name = "汇缴金额", columnWidth = 200)
    private BigDecimal remittanceFee;

    @ExcelColumn(name = "补缴金额", columnWidth = 200)
    private BigDecimal supplementaryFee;

    @ExcelColumn(name = "是否有差异", columnWidth = 200)
    private String diffFlag;

    @ExcelColumn(name = "项目客服", columnWidth = 200)
    private String commissioner;

    @ExcelColumn(name = "接单客服", columnWidth = 200)
    private String receivingMan;

    private Byte feeType;

    
}
