/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/11/27
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.hr.common.cmb;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaySingleQueryReponseInfo
 * @description TODO
 * @date 2023/11/27 16:19
 */
@Data
public class QueryPaySingleReponseInfo extends BaseResponseInfo implements Serializable {
//    private List<PaySingleBackNoticeInfo> bb1payqrz1;
    private List<QueryPaySingleInfo> bb1payqrz1;
}
