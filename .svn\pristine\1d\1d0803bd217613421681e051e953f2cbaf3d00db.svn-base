package com.reon.hr.sp.customer.service.ImportService;

import com.reon.hr.api.customer.dto.importData.EmployeeAccountImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.vo.employee.EmployeeQueryVo;

public interface BatchAddEmployeeAccountImportService {

    void batchAddEmployeeAccountImport(ImportDataDto<EmployeeAccountImportDto> importDataDto, EmployeeQueryVo queryVo);

}
