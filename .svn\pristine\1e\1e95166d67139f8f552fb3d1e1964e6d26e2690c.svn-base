<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.qiyuesuo.QysMappingFieldMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.qys.QysMappingField">
    <!--@mbg.generated-->
    <!--@Table `reon-customerdb`.qys_mapping_field-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="qys_file_field" jdbcType="VARCHAR" property="qysFileField" />
    <result column="our_system_field" jdbcType="VARCHAR" property="ourSystemField" />
    <result column="classification_type" jdbcType="TINYINT" property="classificationType" />
    <result column="comment_for_field" jdbcType="VARCHAR" property="commentForField" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, qys_file_field, our_system_field, classification_type, comment_for_field, creator, 
    create_time, updater, update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `reon-customerdb`.qys_mapping_field
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `reon-customerdb`.qys_mapping_field
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.customer.entity.qys.QysMappingField" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-customerdb`.qys_mapping_field (qys_file_field, our_system_field, classification_type, 
      comment_for_field, creator, create_time, 
      updater, update_time, del_flag
      )
    values (#{qysFileField,jdbcType=VARCHAR}, #{ourSystemField,jdbcType=VARCHAR}, #{classificationType,jdbcType=TINYINT}, 
      #{commentForField,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.sp.customer.entity.qys.QysMappingField" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `reon-customerdb`.qys_mapping_field
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="qysFileField != null">
        qys_file_field,
      </if>
      <if test="ourSystemField != null">
        our_system_field,
      </if>
      <if test="classificationType != null">
        classification_type,
      </if>
      <if test="commentForField != null">
        comment_for_field,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="qysFileField != null">
        #{qysFileField,jdbcType=VARCHAR},
      </if>
      <if test="ourSystemField != null">
        #{ourSystemField,jdbcType=VARCHAR},
      </if>
      <if test="classificationType != null">
        #{classificationType,jdbcType=TINYINT},
      </if>
      <if test="commentForField != null">
        #{commentForField,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.customer.entity.qys.QysMappingField">
    <!--@mbg.generated-->
    update `reon-customerdb`.qys_mapping_field
    <set>
      <if test="qysFileField != null">
        qys_file_field = #{qysFileField,jdbcType=VARCHAR},
      </if>
      <if test="ourSystemField != null">
        our_system_field = #{ourSystemField,jdbcType=VARCHAR},
      </if>
      <if test="classificationType != null">
        classification_type = #{classificationType,jdbcType=TINYINT},
      </if>
      <if test="commentForField != null">
        comment_for_field = #{commentForField,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.customer.entity.qys.QysMappingField">
    <!--@mbg.generated-->
    update `reon-customerdb`.qys_mapping_field
    set qys_file_field = #{qysFileField,jdbcType=VARCHAR},
      our_system_field = #{ourSystemField,jdbcType=VARCHAR},
      classification_type = #{classificationType,jdbcType=TINYINT},
      comment_for_field = #{commentForField,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>