package com.reon.hr.api.customer.enums.employee;

import com.reon.hr.api.customer.enums.BaseEnum;

public enum EmployeeContractProbationFlag implements BaseEnum {

    NO(1, "否"),
    YES(2, "是");

    private int code;
    private String name;

    EmployeeContractProbationFlag(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
