package com.reon.hr.sp.bill.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.bill.constant.BillEnum;
import com.reon.hr.api.bill.constant.ProductTypeEnum;
import com.reon.hr.api.bill.enums.*;
import com.reon.hr.api.bill.exception.BillCheckException;
import com.reon.hr.api.bill.exception.BillException;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.InsuranceDisposBillVo;
import com.reon.hr.api.bill.vo.bill.OneServiceBillCfgVo;
import com.reon.hr.api.customer.dto.customer.QuotationDTO;
import com.reon.hr.api.customer.dubbo.service.rpc.IBillTempletWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IQuotationResourceWrapperService;
import com.reon.hr.api.customer.enums.billTemplet.BillTempletType;
import com.reon.hr.api.customer.enums.quotation.QuotationTaxFlag;
import com.reon.hr.api.customer.enums.quotation.ReceiveFreqEnum;
import com.reon.hr.api.customer.vo.ContractPageVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.sp.bill.dao.bill.*;
import com.reon.hr.sp.bill.dao.cus.BillCostMapper;
import com.reon.hr.sp.bill.entity.bill.*;
import com.reon.hr.sp.bill.entity.cus.BillCost;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillService;
import com.reon.hr.sp.bill.service.bill.IInsuranceDisposBillService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InsuranceDisposBillServiceImpl implements IInsuranceDisposBillService {
    @Autowired
    private IBillTempletWrapperService templetWrapperService;
    @Autowired
    private InsuranceBillMapper insuranceBillMapper;
    @Autowired
    private IContractResourceWrapperService contractWrapperService;
    @Autowired
    private ContractMapper contractMapper;
    @Autowired
    private BillCheckInvoiceMapper billCheckInvoiceMapper;
    @Autowired
    private InsuranceBillDetailMapper billDetailMapper;
    @Autowired
    private BillServiceNumInfoMapper billServiceNumInfoMapper;
    @Autowired
    private BillCostMapper billCostMapper;
    @Autowired
    private OneServiceBillCfgMapper oneServiceBillCfgMapper;
    @Autowired
    private IQuotationResourceWrapperService iQuotationResourceWrapperService;
    @Autowired
    private IInsuranceBillService insuranceBillService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void generateBillByDisposBill(InsuranceDisposBillVo insuranceDisposBillVo) throws Exception {
        /** 先存入 insurance_bill*/
        logger.info("==============开始生成账单==============");
        // region 获取公共数据
        String contractNo = insuranceDisposBillVo.getContractNo();
        Long templetId = insuranceDisposBillVo.getTempletId();
        Integer billMonth = insuranceDisposBillVo.getBillMonth();
        String loginName = insuranceDisposBillVo.getLoginName();
        List<Map<String, String>> quoteDataList = insuranceDisposBillVo.getQuoteDataList();
        BigDecimal taxRatio = insuranceDisposBillVo.getTaxRatio() == null ? BigDecimal.ZERO : insuranceDisposBillVo.getTaxRatio();
        Integer taxFlag = insuranceDisposBillVo.getTaxFlag();

        // endregion

        //region      t.id,
        //		      t.templet_name,
        //		      t.templet_no,
        //		      t.lock_date,
        //		      t.templet_type,
        //		      t.pay_date
        //		      t.payment_id,
        //		      c.cust_name,
        //		      c.id as custId,
        //endregion  ci.title as paymentName,
        /** 获取bill_templet customer cutomer_invoice */
        BillTempletVo billTempletVo = templetWrapperService.getListTempletByContractNoForDispos(templetId, BillTempletType.DISPOSE_INSURANCE.getCode());
        /** 获取contract */
        ContractPageVo contractData = contractWrapperService.getDataByContractNo(contractNo);
        billTempletVo.setGenDate(contractData.getBillDate());

        /** insurance_bill  contract  bill_check_invoice */
        InsuranceBillVo insuranceBillVo = insuranceBillMapper.getByCustIdAndTempletIdAndBillMonthForDispos(contractNo, templetId, billMonth, BillEnum.billType.dispose_insurance.getCode());

        if (ObjectUtils.allNotNull(insuranceBillVo)) {
            if (InsuranceBillEnum.InsuranceBillStatusEnum.LOCK_STATUS_CODE.getIndex().equals(insuranceBillVo.getStatus())) {
                StringBuilder sb = new StringBuilder("[contractNo: ").append(contractNo).append(" templetId: ").append(templetId).append("]</br>已锁定");
                throw new BillException(sb.toString());
            }
            if (insuranceBillVo.getGenTimes() >= InsuranceBillEnum.EVERY_HIGH_GEN_TIMES.getIndex()) {
                StringBuilder sb = new StringBuilder("[contractNo: ").append(contractNo).append(" templetId: ").append(templetId).append("]</br>今日已生成十次");
                throw new BillException(sb.toString());
            }
        }
        Long billId = null;
        if (!ObjectUtils.allNotNull(insuranceBillVo)) {
            // 该月第一次生成账单 新增
            InsuranceBill insuranceBill = new InsuranceBill();
            insuranceBill.setCustId(contractData.getCustId()).setCustName(contractData.getCustName());
            /** 为社保账单对象赋值  保存 bill合同 */
            setInsuranceBillAndSaveContractForDispos(insuranceBill, billTempletVo, contractNo, billMonth, loginName);
            logger.info("账单新增insuranceBill.getGenTimes()" + insuranceBill.getGenTimes());
            insuranceBillMapper.insertSelective(insuranceBill);

            /** 保存核销开票表   插入 bill_check_invoice */
            BillCheckInvoice billCheckInvoice = new BillCheckInvoice()
                    .setId(insuranceBill.getId())
                    .setInvoiceStatus(InvoiceStatusEnum.UNINVOICE.getCode()).setCancelStatus(BillCheckStatusEnum.UNCHECK.getCode())
                    .setInvoiceId(billTempletVo.getPaymentId()).setInvoiceTitle(billTempletVo.getPaymentName()) // 开票信息中的开票抬头
                    .setCreator(loginName);
            billCheckInvoiceMapper.insertSelective(billCheckInvoice);
            billId = insuranceBill.getId();
        } else {
            // 当月非第一次生成
            InsuranceBill insuranceBill = new InsuranceBill().setId(insuranceBillVo.getId())
                    .setGenTimes(getGenTimes(insuranceBillVo))
                    .setActGenTime(new Date()).setGenStatus(BillEnum.GenerateBillStatus.KEEPING.getCode())
                    .setEmployeeNum(0).setReceiveAmt(BigDecimal.ZERO).setRevCs(loginName);
            insuranceBillMapper.updateByPrimaryKeySelective(insuranceBill);
            billId = insuranceBill.getId();
        }
        if (CollectionUtils.isEmpty(quoteDataList)) {
            StringBuilder sb = new StringBuilder("[contractNo: ").append(contractNo).append(" templetId: ").append(templetId).append("]没有报价单信息!!");
            throw new BillException(sb.toString());
        }

        if (null == billId) {
            StringBuilder sb = new StringBuilder("[contractNo: ").append(contractNo).append(" templetId: ").append(templetId).append("]生成账单报错,没有billId!!");
            throw new BillException(sb.toString());
        }

        BigDecimal priceTotal = BigDecimal.ZERO;
        BigDecimal costTotal = BigDecimal.ZERO;
        Integer peopleNum = 0;

        List<BillServiceNumInfo> billServiceNumInfoList = Lists.newArrayList();
        Map<String, BillCost> costAndBillCostMap = Maps.newHashMap();  // 成本&&成本Vo表
        List<OneServiceBillCfg> oneServiceBillCfgInsertList = Lists.newArrayList();
        for (Map<String, String> stringStringMap : quoteDataList) {
            String quoteNo = stringStringMap.get("quoteNo");
            BigDecimal priceBD = new BigDecimal(stringStringMap.get("price"));   // 价格
            BigDecimal priceBDHaveTax = BigDecimal.ZERO;
            if (QuotationTaxFlag.TAX_EXCLUSIVE.getCode() == taxFlag) {
//                不含税
                priceBDHaveTax = priceBD.multiply(BigDecimal.ONE.add(taxRatio)).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (QuotationTaxFlag.TAX_INCLUSIVE.getCode() == taxFlag) {
                //含税
                priceBDHaveTax = priceBD;
            }

            BigDecimal numBD = new BigDecimal(stringStringMap.get("num"));     // 人次
            BigDecimal costBD = new BigDecimal(stringStringMap.get("cost"));    // 成本
            priceTotal = priceTotal.add(priceBDHaveTax.multiply(numBD));
            costTotal = costTotal.add(costBD.multiply(numBD));
            peopleNum = peopleNum + numBD.intValue();
            String supplierIdS = stringStringMap.get("supplierId");
            String support = stringStringMap.get("support");  // 支持人员
            String remark = stringStringMap.get("remark");    // 备注
            OneServiceBillCfg oneServiceBillCfg = new OneServiceBillCfg().setBillId(billId).setQuoteNo(quoteNo)
                    .setQuoteFlag(insuranceDisposBillVo.getQuoteFlag()).setNum(numBD.intValue())
                    .setPrice(priceBD).setCost(costBD).setCreator(loginName).setRemark(remark).setSupport(support);
            if (StringUtils.isNotEmpty(supplierIdS)) {
                oneServiceBillCfg.setSupplierId(Long.valueOf(supplierIdS));
            }
            oneServiceBillCfgInsertList.add(oneServiceBillCfg);

            BillServiceNumInfo billServiceNumInfo = new BillServiceNumInfo()
                    .setBillId(billId).setQuoteNo(quoteNo)
                    .setPrice(priceBDHaveTax).setServiceNum(numBD.intValue())
                    .setCreator(loginName).setCreateTime(new Date()).setDelFlag("N");
            billServiceNumInfoList.add(billServiceNumInfo);

            BillCost billCost = costAndBillCostMap.getOrDefault(stringStringMap.get("cost"),
                    new BillCost().setBillId(billId)
                            .setReceivingType(BillCostEnum.ReceivingTypeEnum.OWN_COMPANY.getIndex())
                            .setServiceFee(costBD).setServiceNum(numBD.intValue()).setTotalFee(costBD.multiply(numBD))
                            .setCreator(loginName)
            );
            if (costAndBillCostMap.containsKey(stringStringMap.get("cost"))) {
                billCost.setServiceNum(billCost.getServiceNum() + numBD.intValue());
                billCost.setTotalFee(costBD.multiply(new BigDecimal(billCost.getServiceNum())));
            }
            costAndBillCostMap.put(stringStringMap.get("cost"), billCost);
        }


        if (CollectionUtils.isNotEmpty(oneServiceBillCfgInsertList)) {
            Wrapper<OneServiceBillCfg> deleteWrapper = new EntityWrapper<>();
            deleteWrapper.eq("bill_id", billId);
            oneServiceBillCfgMapper.delete(deleteWrapper);
            Integer insertSize = oneServiceBillCfgMapper.batchInsert(oneServiceBillCfgInsertList);
        }

        InsuranceBillDetail billDetailInsurance = new InsuranceBillDetail().setBillId(billId)
                .setCreator(loginName).setUpdater(loginName)
                .setUncheckAmt(priceTotal).setUninvoiceAmt(priceTotal)
                .setProductType(ProductTypeEnum.SERVICE_FEE.getCode()).setDetailType(InsuranceBillDetailDetailTypeEnum.SERVICE_CHARGE.getCode())
                .setReceiveAmt(priceTotal).setPeopleNum(peopleNum).setValTaxRate(taxRatio);
        /**
         * 报价单中 是否含税  1.否  2.是
         * 不含税 */
        BigDecimal valTaxAmt = null;
        /** 含税不含税,到这里都是含税的 的税金*/
        valTaxAmt = priceTotal.divide(BigDecimal.ONE.add(taxRatio), 2, BigDecimal.ROUND_HALF_UP).multiply(taxRatio).setScale(2, BigDecimal.ROUND_HALF_UP);

        billDetailInsurance.setReceiveValTax(valTaxAmt).setUncheckValTax(valTaxAmt).setUninvoiceValTax(valTaxAmt);
        insuranceBillService.deleteInsuranceBillDetail(billId);
//        billDetailMapper.deleteBillDetailByBillId(billId);
        billDetailMapper.insertSelective(billDetailInsurance);

        if (CollectionUtils.isNotEmpty(billServiceNumInfoList)) {
            billServiceNumInfoMapper.deleteByBillId(billId);
            billServiceNumInfoMapper.batchInsert(billServiceNumInfoList);
        }
        List<BillCost> billCostList = new ArrayList<>(costAndBillCostMap.values());
        if (CollectionUtils.isNotEmpty(billCostList)) {
            billCostMapper.deleteByBillId(billId);
            billCostMapper.batchInsert(billCostList);
        }

        BillCheckInvoice billCheckInvoice = new BillCheckInvoice()
                .setId(billId).setUpdater(loginName)
                .setUninvoiceAmt(priceTotal).setUncheckAmt(priceTotal);
        billCheckInvoiceMapper.updateByPrimaryKeySelective(billCheckInvoice);

        InsuranceBill insuranceBill = new InsuranceBill();
        insuranceBill.setId(billId)
                .setEmployeeNum(peopleNum).setReceiveAmt(priceTotal).setGenStatus(BillEnum.GenerateBillStatus.SUCCESSED.getCode())
                .setServiceNum(peopleNum).setSeviceFee(priceTotal).setCreator(loginName);
        insuranceBillMapper.updateByPrimaryKeySelective(insuranceBill);


    }

    @Override
    public Map<String, List<OneServiceBillCfgVo>> getOneServiceBillCfgList(String contractNo, Long templetId, Integer billMonth) {
        InsuranceBillVo insuranceBillVo = insuranceBillMapper.getInsuranceBillByContractNoAndTempletIdAndBillMonth(contractNo, templetId, billMonth);
        if (insuranceBillVo != null) {
            Map<String, List<OneServiceBillCfgVo>> resultMap = Maps.newHashMap();
            Long billId = insuranceBillVo.getId();
            Wrapper<OneServiceBillCfg> wrapper = new EntityWrapper<>();
            wrapper.eq("bill_id", billId).eq("del_flag", "N");
            List<OneServiceBillCfg> oneServiceBillCfgs = oneServiceBillCfgMapper.selectList(wrapper);
            List<String> quoteNoList = oneServiceBillCfgs.stream().map(OneServiceBillCfg::getQuoteNo).collect(Collectors.toList());
            Map<String, String> quoteNoAndQuoteNameMap = iQuotationResourceWrapperService.getQuoteNameByQuoteNoList(quoteNoList);
            Map<String, String> quoteNoAndPriceMap = iQuotationResourceWrapperService.getQuotePriceByQuoteNoList(quoteNoList);
            Map<String, QuotationDTO> quoteMapByQuoteNoMap = iQuotationResourceWrapperService.getQuoteAmtByQuoteNoList(quoteNoList);
            List<OneServiceBillCfgVo> resultEditList = oneServiceBillCfgs.stream().map(item -> {
                OneServiceBillCfgVo oneServiceBillCfgVo = new OneServiceBillCfgVo();
                BeanUtils.copyProperties(item, oneServiceBillCfgVo);
                oneServiceBillCfgVo.setQuoteName(quoteNoAndQuoteNameMap.get(oneServiceBillCfgVo.getQuoteNo()));
                QuotationDTO quotationDTO = quoteMapByQuoteNoMap.get(item.getQuoteNo());
                if (quotationDTO != null) {
                    oneServiceBillCfgVo.setTaxRatio(quotationDTO.getTaxRatio());
                    oneServiceBillCfgVo.setTaxFlag(quotationDTO.getTaxFlag());
                }
                return oneServiceBillCfgVo;
            }).collect(Collectors.toList());
            List<OneServiceBillCfgVo> dataQuotationResource = oneServiceBillCfgs.stream().map(item -> {
                OneServiceBillCfgVo oneServiceBillCfgVo = new OneServiceBillCfgVo();
                BeanUtils.copyProperties(item, oneServiceBillCfgVo);
                oneServiceBillCfgVo.setQuoteName(quoteNoAndQuoteNameMap.get(oneServiceBillCfgVo.getQuoteNo()));
                if (quoteNoAndPriceMap.containsKey(oneServiceBillCfgVo.getQuoteNo())) {
                    String[] priceAndNum = quoteNoAndPriceMap.get(oneServiceBillCfgVo.getQuoteNo()).split(",");
                    oneServiceBillCfgVo.setPrice(new BigDecimal(priceAndNum[0]));
                    oneServiceBillCfgVo.setNum(Integer.parseInt(priceAndNum[1]));
                    QuotationDTO quotationDTO = quoteMapByQuoteNoMap.get(item.getQuoteNo());
                    if (quotationDTO != null) {
                        oneServiceBillCfgVo.setTaxRatio(quotationDTO.getTaxRatio());
                        oneServiceBillCfgVo.setTaxFlag(quotationDTO.getTaxFlag());
                    }
                }
                return oneServiceBillCfgVo;
            }).collect(Collectors.toList());
            resultMap.put("dataQuotationEdit", resultEditList);
            resultMap.put("dataQuotationResource", dataQuotationResource);

            return resultMap;
        } else {
            return Maps.newHashMap();
        }
    }

    @Override
    public InsuranceBillVo getBillIdByContractNoAndTempletIdAndBillMonth(String contractNo, Long templetId, Integer billMonth) {
        return insuranceBillMapper.getInsuranceBillByContractNoAndTempletIdAndBillMonth(contractNo, templetId, billMonth);
    }

    @Override
    public List<OneServiceBillCfg> getInsuranceDisposData(Long billId) {
        Wrapper<OneServiceBillCfg> wrapper = new EntityWrapper<>();
        wrapper.eq("bill_id", billId).eq("del_flag", "N");
        List<OneServiceBillCfg> oneServiceBillCfgs = oneServiceBillCfgMapper.selectList(wrapper);
        return oneServiceBillCfgs;
    }

    @Override
    public Map<Long, BigDecimal> getBillIdAndCostMapByBillId(Set<Long> disposeInsuranceBillIdSet) {
        Map<Long, BigDecimal> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(disposeInsuranceBillIdSet))
            return resultMap;
        List<OneServiceBillCfg> oneServiceBillCfgs = oneServiceBillCfgMapper.selectByBillId(disposeInsuranceBillIdSet);
        if (CollectionUtils.isNotEmpty(oneServiceBillCfgs))
            resultMap = oneServiceBillCfgs.stream().collect(Collectors.toMap(OneServiceBillCfg::getBillId, OneServiceBillCfg::getCost));
        return resultMap;
    }

    /** return Map<billId, Map<"remark"/"support", data>> */
    @Override
    public Map<Long, Map<String, String>> getOneBillBillIdAndDataMap(Set<Long> oneBillIdSet) {
        if (CollectionUtils.isEmpty(oneBillIdSet))
            return Maps.newHashMap();
        Wrapper<OneServiceBillCfg> wrapper = new EntityWrapper<>();
        wrapper.in("bill_id", oneBillIdSet).eq("del_flag", "N");
        List<OneServiceBillCfg> oneServiceBillCfgs = oneServiceBillCfgMapper.selectList(wrapper);
        Map<Long, Map<String, String>> resultMap = new HashMap<>();
        oneServiceBillCfgs.stream()
                .collect(Collectors.groupingBy(
                        OneServiceBillCfg::getBillId,
                        Collectors.toSet()
                ))
                .forEach((billId, cfgSet) -> {
                    if (CollectionUtils.isEmpty(cfgSet)) return;
                    Map<String, String> fieldMap = new HashMap<>(2);
                    // 处理 remark 字段
                    String remarks = cfgSet.stream()
                            .map(OneServiceBillCfg::getRemark)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(" | "));
                    if (!remarks.isEmpty()) {
                        fieldMap.put("remark", remarks);
                    }
                    // 处理 support 字段
                    String supports = cfgSet.stream()
                            .map(OneServiceBillCfg::getSupport)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(" | "));
                    if (!supports.isEmpty()) {
                        fieldMap.put("support", supports);
                    }
                    if (!fieldMap.isEmpty()) {
                        resultMap.put(billId, fieldMap);
                    }
                });
        return resultMap;


    }

    private int getGenTimes(InsuranceBillVo insuranceBillVo) {
        int genTimes = BigDecimal.ZERO.intValue();
        if (ObjectUtils.allNotNull(insuranceBillVo)) {
            String actGenTime = DateUtil.showFormatedDate(DateUtil.DATE_FORMAT_YYYYMMDD, insuranceBillVo.getActGenTime());
            String currDate = DateUtil.showFormatedDate(DateUtil.DATE_FORMAT_YYYYMMDD, new Date());
            if (currDate.equals(actGenTime))
                genTimes = insuranceBillVo.getGenTimes() + 1;
            else
                genTimes = InsuranceBillEnum.GEN_FIRST_TIMES.getIndex();
        }
        return genTimes;
    }

    private void setInsuranceBillAndSaveContractForDispos(InsuranceBill insuranceBill, BillTempletVo templetVo, String contractNo, Integer currMonth, String loginName) throws Exception {
        ContractPageVo contractPageVo = contractWrapperService.getContractData(contractNo);

        if (contractPageVo != null) {
            Contract contract = new Contract();
            Contract contractPage = contractMapper.selectByPrimaryKey(contractNo);
            if (contractPage == null) {
                BeanUtils.copyProperties(contractPageVo, contract);
                contract.setCreateTime(new Date()).setCreator(loginName).setUpdater(loginName);
                contractMapper.insertSelective(contract);
            }
            insuranceBill.setId(null).setBillMonth(currMonth)
                    .setContractNo(contractNo)
                    .setSignTitle(contractPageVo.getSignComTitle())  /* 签约方抬头 --> 合同的 签约方公司抬头*/
                    .setTempletId(templetVo.getId())
                    .setAppGenDate(templetVo.getGenDate())
                    .setAppLockDate(templetVo.getLockDate())
                    .setBillOwner(templetVo.getGenBill())             /* 账单方 -> 账单模板 账单方code */
                    .setTempletName(templetVo.getTempletName())
                    .setPayDate(templetVo.getPayDate())
                    .setAcctCompany(contractPageVo.getSignComTitle())// 出账单分公司 --> 合同的签单方公司抬头 --> 账单打印下面的银行
                    .setCreator(loginName).setUpdater(loginName).setGenMan(loginName).setRevCs(loginName)
                    .setGenTimes(InsuranceBillEnum.GEN_FIRST_TIMES.getIndex())
                    .setGenFirstTime(new Date()).setActGenTime(new Date())
                    .setBillType(BillEnum.billType.dispose_insurance.getCode());

            if (contractPageVo.getReceiveFreq() == ReceiveFreqEnum.THE_SAME_MONTH.getCode()) {
                insuranceBill.setReceivableMonth(currMonth);
            } else {
                insuranceBill.setReceivableMonth(DateUtil.getNextYearMonth(currMonth));
            }
        } else {
            StringBuilder sbSecond = new StringBuilder("账单生成没有合同! contractNo: ").append(contractNo);
            throw new BillCheckException(sbSecond.toString());
        }
    }
}






