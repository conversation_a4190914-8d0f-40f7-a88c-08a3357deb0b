package com.reon.hr.api.customer.vo.insurancePractice;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @Date: 2022/12/6 15:56
 * @Version: 1.0
 */
@Data
public class PracAdjustDetailVo implements Serializable {
    private Long id;
    /**
     * 调整id
     */
    private Long adjustId;
    /**
     * 姓名
     */
    private String empName;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 证件类型
     */
    private Integer certType;
    /**
     * 证件号
     */
    private String certNo;
    /**
     * 客户号
     */
    private String custNo;
    /**
     * 调整状态(1:成功,2:失败)
     */
    private Integer dealStatus;
    /**
     * 失败原因
     */
    private String failureReason;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;
}
