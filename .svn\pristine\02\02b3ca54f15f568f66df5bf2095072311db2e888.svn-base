<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>机构定岗管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <script type="text/javascript">
        /*定义子类型变量*/
        var sunPro = [{}];
        var ratioArr = window.top['dictCachePool']['QUOTATION_TAX_RATIO']
    </script>
    <style>
        .layui-input-disposable {
            padding-right: 30px !important;
        }
    </style>
</head>
<body>
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm">
        <div class="layui-inline queryTable">
            <div>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="text" id="name" maxlength="20" name="name" placeholder="定岗名称/机构名称"
                               autocomplete="off"
                               class="layui-input layui-input-disposable">
                    </div>
                </div>

                <div class="layui-input-inline">
                    <div class="layui-input-block">
                        <a class="layui-btn" id="btnQuery" data-type="reload" lay-filter="btnQuery" lay-submit="">查询</a>
                        <a class="layui-btn" id="add">新增</a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</blockquote>

<blockquote class="layui-elem-quote orgPositionQueryTable">
    <table id="orgPositionQueryTable" lay-filter="orgPositionQueryTableFilter"></table>
</blockquote>
<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="addVirtual" lay-event="addVirtual" authURI="/sys/orgPosition/gotoOrgPositionAddVirtualPage" >新增虚拟机构定岗</button>
</script>
<script type="text/jsp" id="btn" >
    <a href="javascript:void(0)" title="修改" lay-event="update" authURI="/sys/orgPosition/gotoOrgPositionAddView"><i
            class="layui-icon layui-icon-edit"></i></a>&nbsp;
<%--    <a  href="javascript:void(0)" title="绑定大区" lay-event="region" authURI="/sys/orgPosition/gotoOrgPositionRegionView"><i--%>
<%--            class="layui-icon layui-icon-set-sm"></i></a>&nbsp;--%>
</script>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/sys/orgposition/orgPositionQuery.js?v=${publishVersion}"></script>
</body>
</html>
