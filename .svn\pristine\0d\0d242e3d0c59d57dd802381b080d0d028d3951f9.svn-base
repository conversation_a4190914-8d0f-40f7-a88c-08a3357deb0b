<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all" />
    <style type="text/css">
        .layui-input {
            height: 30px;
        }

        .layui-table td, .layui-table th {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <table class="layui-table" lay-skin="nob" style="width: 60%; margin: 0 auto;">
        <tr>
            <td align="right" width="15%">供应商名称：</td>
            <td width="30%"><input type="text"  class="layui-input" id="supplierName" readonly>
        </tr>
    </table>
</blockquote>
<table class="layui-hide" id="supplierAreaId" lay-filter="supplierAreaFilter"style="width: 80%;"></table>

<input type="hidden" id="supplierId">
<input type="hidden" id="purchaser">
<input type="hidden" id="supplierType">
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/supplier/supplierArea.js?v=${publishVersion}"></script>
<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="batchdistribute" lay-event="batchdistribute" authURI="/customer/supplier/gotoDistributePage">分配</button>
    &nbsp;
        <div class="layui-inline" style="margin-left: 10px">
            <input type="text" id="search" placeholder="请输入市名称" class="layui-input input" authURI="/customer/supplier/searchByCityName"/>
        </div>&nbsp;
        <div class="layui-inline">
             <a href="javascript:void(0)" lay-event="search" authURI="/customer/supplier/searchByCityName"><i class="layui-btn layui-btn-sm">检索</i></a>
        </div>&nbsp;
        <div class="layui-inline">
             <a href="javascript:void(0)" lay-event="noSearch" authURI="/customer/supplier/searchByCityName"><i class="layui-btn layui-btn-sm">取消检索</i></a>
        </div>&nbsp;
</script>
<script type="text/jsp" id="toolDemo">
    <button class="layui-btn layui-btn-sm" id="batchdistribute" lay-event="batchdistribute" authURI="/customer/supplier/gotoDistributePage">分配</button>
</script>
</body>
</html>