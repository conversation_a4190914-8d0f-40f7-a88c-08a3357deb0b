package com.reon.hr.api.base.dubbo.service.rpc.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.DisabilityGoldRateExportVo;
import com.reon.hr.api.base.vo.DisabilityGoldRateVo;
import com.reon.hr.api.base.vo.DisabilityRecordVo;

import java.util.List;

/**
 * <AUTHOR> on 2022/2/14.
 */
public interface IDisabilityGoldRateWrapperService {
    Page<DisabilityGoldRateVo> getDisabilityGoldRatePage(DisabilityGoldRateVo disabilityGoldRateVo);

    List<DisabilityGoldRateVo> selectByCityCodeList(List<String> cityCodeList);

    List<DisabilityGoldRateVo> selectByCityNameList(List<String> cityNameList);

    List<DisabilityGoldRateVo> selectAll();

    DisabilityGoldRateVo getDisabilityGoldRate(DisabilityGoldRateVo vo);

    String saveOrUpdateDisabilityGoldRate(DisabilityGoldRateVo vo);
    List<DisabilityGoldRateExportVo> getDisabilityGoldRateExportList(DisabilityGoldRateVo disabilityGoldRateVo);

    List<DisabilityRecordVo> getRemarkByContractNoList(List<String> contractNoList);

    void addSpecialDisabilityRecord(DisabilityGoldRateVo disabilityGoldRateVo);

    DisabilityGoldRateVo getUpperLimitAndReonUpperLimit(Integer cityCode,Integer type, String orgCode);

    DisabilityGoldRateVo getSpecialDisabilityGoldRateVoById(Long id);

    void updateSpecialDisabilityGoldRateVo(DisabilityGoldRateVo vo);
}
