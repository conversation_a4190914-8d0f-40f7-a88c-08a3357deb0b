var ctx = ML.contextPath;
let quotationVosData = [];
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    laydate.render({
        elem: '#billStartMonth',
        theme: 'grid',
        type: 'month',
        min: '2021-01-01',
        max: '2099-12-12',
        format: 'yyyyMM',
        // 点击即选中
        /* ready和change函数是为了实现选择年月时不用点确定直接关闭  */
        ready: function (date) { // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        },
        change: function (value, date, endDate) {
        },
        done: function (value, date, endDate) { // 点击确定按钮后触发
            let prodHandleInfoData = table.cache['prodHandleInfo'];
            let serviceChargeData = table.cache['serviceCharge'];
            for (let i = 0; i < prodHandleInfoData.length; i++) {
                prodHandleInfoData[i].billStartMonth = value;
            }
            for (let i = 0; i < serviceChargeData.length; i++) {
                serviceChargeData[i].billStartMonth = value;
            }
            table.reload('prodHandleInfo', {data: prodHandleInfoData});
            table.reload('serviceCharge', {data: serviceChargeData});
        }
    });

    table.render({
        id: 'prodHandleInfo',
        elem: '#prodHandleInfo',
        data: [],
        page: false, //默认为不开启
        limit: Number.MAX_VALUE,
        totalRow: true,
        title: "费用信息",
        // toolbar: '#toolbarProd',
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'numbers', title: '序号', width: 50, fixed: 'left'},
            {
                field: 'prodCode',
                title: '产品名称',
                width: 130,
                align: 'center',
                fixed: 'left',
                templet: function (d) {
                    return ML.dictFormatter("PRODUCT_IND_TYPE", d.prodCode);
                }
            },
            {
                field: '', title: '金额', width: 200, align: 'center', templet: function (d) {
                    d.indBase = d.indBase == null ? 0 : d.indBase;
                    d.indRatio = d.indRatio == null ? 0 : d.indRatio;
                    d.comBase = d.comBase == null ? 0 : d.comBase;
                    d.comRatio = d.comRatio == null ? 0 : d.comRatio;
                    let args = roundByRatio(d.comBase, d.comRatio, d.comAdd, d.comCalcMode, d.comExactVal,
                        d.indBase, d.indRatio, d.indlAdd, d.indCalcMode, d.indExactVal, d.specialFlag);
                    let comMoney = args['comAmt'];
                    let indMoney = args['indAmt'];
                    return numAdd(indMoney, comMoney) + "(" + comMoney + "，" + indMoney + ")";

                }
            },
            {
                field: 'startMonth', title: '<i style="color: red;">*</i> 收费起始月', width: 120, align: 'center',
                templet: function (d) {
                    var startMonth = '';
                    if (d.startMonth != undefined && d.startMonth != 0) {
                        startMonth = d.startMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell startMonth" style="text-align: center" id="startMonth" lay-verify="required" value="' + startMonth + '" placeholder="yyyyMM" readonly />';
                }
            },
            {
                field: 'endMonth', title: '收费截止月', width: 120, align: 'center',
                templet: function (d) {
                    var endMonth = '';
                    if (d.endMonth != undefined && d.endMonth != 0) {
                        endMonth = d.endMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell  endMonth"  style="text-align: center" id="endMonth" lay-verify="required" value="' + endMonth + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {
                field: 'billStartMonth', title: '<i style="color: red;">*</i> 账单起始月', width: 120, align: 'center',
                templet: function (d) {
                    var billStartMonth = '';
                    if (d.billStartMonth != undefined && d.billStartMonth != 0) {
                        billStartMonth = d.billStartMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell billStartMonth" style="text-align: center" id="billStartMonth" lay-verify="required" value="' + billStartMonth + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {field: 'ratioCode', title: '社保比例编号', align: 'center', width: '120', style: 'overflow:hidden;'},
            {field: 'ratioName', title: '比例名称', width: 350, align: 'center'},
            {
                field: 'comRatio', title: '企业比例', width: 100, align: 'center', templet: function (d) {
                    return d.comRatio + "%";
                }
            },
            {
                field: 'indRatio', title: '个人比例', width: 100, align: 'center', templet: function (d) {
                    return d.indRatio + "%";
                }
            },
            {field: 'comAdd', title: '企业附加', align: 'center', width: 100},
            {field: 'indlAdd', title: '个人附加', align: 'center', width: 100},
            {
                field: 'comBase',
                title: '<i style="color: red;">*</i>企业基数',
                width: 100,
                align: 'center',
                edit: "text"
            },
            {
                field: 'indBase',
                title: '<i style="color: red;">*</i>个人基数',
                width: 100,
                align: 'center',
                edit: "text"
            },
            {
                field: 'comAmt', title: '企业金额', width: 100, align: 'center', templet: function (d) {
                    if ((ML.isEmpty(d.comAmt) || d.comAmt == 0) && (ML.isEmpty(d.indAmt) || d.indAmt == 0)) {
                        return d.comAmt;
                    } else {
                        let args = roundByRatio(d.comBase, d.comRatio, d.comAdd, d.comCalcMode, d.comExactVal,
                            d.indBase, d.indRatio, d.indlAdd, d.indCalcMode, d.indExactVal, d.specialFlag);
                        return args['comAmt'];
                    }
                }
            },
            {
                field: 'indAmt', title: '个人金额', width: 100, align: 'center', templet: function (d) {
                    if ((ML.isEmpty(d.comAmt) || d.comAmt == 0) && (ML.isEmpty(d.indAmt) || d.indAmt == 0)) {
                        return d.indAmt;
                    } else {

                        let args = roundByRatio(d.comBase, d.comRatio, d.comAdd, d.comCalcMode, d.comExactVal,
                            d.indBase, d.indRatio, d.indlAdd, d.indCalcMode, d.indExactVal, d.specialFlag);
                        return args['indAmt'];
                    }
                }
            },
            // {field: 'feeName', title: '收费模板', width: 200, align: 'center'},
            // {field: 'templetName', title: '账单模板', width: 200, align: 'center'},
            {field: 'remark', title: '备注', width: 300, align: 'center', edit: "text"}

        ]],
        done: function (res, curr, count) {
            // renderBackgroundColor(res.data,orderStatus);
            lay('.startMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'startMonth': value});
                        }
                    }
                });
            });

            lay('.endMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'endMonth': value});
                        }
                    }
                });
            });

            lay('.billStartMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'billStartMonth': value});
                        }
                    }
                });
            });

            renderBackgroundColor(res.data);
        }
    });

    function renderBackgroundColor(data) {
        for (var i = 0; i < data.length; i++) {
            if (data[i].isLastData) {
                setColor(i, '#4186D5');
            }
        }
    }

    function setColor(i, color) {
        $(".layui-table-box tbody tr[data-index='" + i + "'] ").css('color', color);
        setColorById('revStartMonthInsurance', i, color);
        setColorById('expiredMonthInsurance', i, color);
        setColorById('billStartMonthInsurance', i, color);
        setColorById('templetIdInsurance', i, color);
        setColorById('revTempIdInsurance', i, color);
    }

    function setColorById(idStr, i, color) {
        var $input = $("input[id='" + idStr + "']");
        for (let j = 0; j < $input.length; j++) {
            var dataIndex = $input[j].parentNode.parentNode.parentNode.getAttribute("data-index");
            if (dataIndex == i) {
                $($input[j]).css('color', color);
            }
        }
    }

    table.render({
        id: 'serviceCharge',
        elem: '#serviceCharge',
        data: [],
        page: false, //默认为不开启
        limit: Number.MAX_VALUE,
        toolbar: '#toolbarService',
        defaultToolbar: [],
        title: "非社保",
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: 40, fixed: 'left'},
            {
                field: 'revStartMonth', title: '收费起始月', width: 120, align: 'center',
                templet: function (d) {
                    var revStartMonth = '';
                    if (d.revStartMonth != undefined && d.revStartMonth != 0) {
                        revStartMonth = d.revStartMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell revStartMonth" style="text-align: center" id="revStartMonth" lay-verify="required" value="' + revStartMonth + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {
                field: 'revEndMonth', title: '收费截止月', width: 120, align: 'center',
                templet: function (d) {
                    var revEndMonth = '';
                    if (d.revEndMonth != undefined && d.revEndMonth != 0) {
                        revEndMonth = d.revEndMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell revEndMonth" style="text-align: center" id="revEndMonth" lay-verify="required" value="' + revEndMonth + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {
                field: 'billStartMonth', title: '账单起始月', width: 120, align: 'center',
                templet: function (d) {
                    var billStartMonth2 = '';
                    if (d.billStartMonth != undefined && d.billStartMonth != 0) {
                        billStartMonth2 = d.billStartMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell billStartMonth2" style="text-align: center" id="billStartMonth2" lay-verify="required" value="' + billStartMonth2 + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {
                field: 'quotationNo', title: '报价单', width: 200, align: 'center', templet: '#quotationNoId'
            },
            {field: 'amount', title: '金额', width: 100, align: 'center'},
            {field: 'taxfreeAmt', title: '金额（不含税）', width: 150, align: 'center'},
            {field: 'valTax', title: '增值税', width: 100, align: 'center'},
            {
                field: 'valTaxRate', title: '增值税率', width: 120, align: 'center', templet: function (d) {
                    if (d.valTaxRate) {
                        return d.valTaxRate * 100 + "%";
                    }
                    return "0%";
                }
            },
            {field: 'remark', title: '备注', width: 100, align: 'center', edit: "text"}
        ]],
        done: function (res) {
            lay('.revStartMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'revStartMonth': value});
                        }
                    }
                });
            });

            lay('.revEndMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'revEndMonth': value});
                        }
                    }
                });
            });

            lay('.billStartMonth2').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            // revStartMonthValue = value;
                            $.extend(res.data[i], {'billStartMonth': value});
                        }
                    }
                });
            });
        }
    });


    table.render({
        id: 'oneTimeFee',
        elem: '#oneTimeFee',
        // url: ctx + '/customer/employee/gotoOneTimeFee?orderNo=' + orderNo + '&type=' + 1,
        data: [],
        page: false, //默认为不开启
        limit: Number.MAX_VALUE,
        toolbar: '#toolbarDemo',
        defaultToolbar: [],
        title: "一次性费用",
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: 40, fixed: 'left'},
            {
                field: 'happenMonth',
                title: '<i style="color: red;">*</i>发生月',
                width: 120,
                align: 'center',
                templet: function (d) {
                    var happenMonth = '';
                    if (d.happenMonth != undefined && d.happenMonth != 0) {
                        happenMonth = d.happenMonth;
                    }
                    return '<input type="text" class="layui-input layui-table-cell happenMonth" style="text-align: center" id="happenMonth" lay-verify="required" value="' + happenMonth + '" placeholder="yyyyMM" readonly  />';
                }
            },
            {
                field: 'oneTimeType',
                title: '<i style="color: red;">*</i>一次性类型',
                width: 120,
                align: 'center',
                templet: '#oneTimeTypeId'
            },
            {
                field: 'fee',
                title: '<i style="color: red;">*</i>企业一次性费用',
                width: 230,
                align: 'center',
                edit: "text"
            },
            {
                field: 'indFee',
                title: '<i style="color: red;">*</i>个人一次性费用',
                width: 230,
                align: 'center',
                edit: "text"
            },
            {field: 'remark', title: '备注', width: 200, align: 'center', edit: "text"}
        ]],
        done: function (res, curr, count) {
            lay('.happenMonth').each(function (i) {
                laydate.render({
                    elem: this,
                    type: 'month',
                    trigger: 'click',
                    min: '2010-01-01',
                    max: '2099-12-12',
                    theme: 'grid',
                    format: 'yyyyMM',
                    done: function (value, date, endDate) {
                        if (res && res.data[i]) {
                            $.extend(res.data[i], {'happenMonth': value});
                        }
                    }
                });
            });
        }
    });


    table.on('edit(prodHandleInfoFilter)', function (obj) {
        let field = obj.field
        if (field != 'remark') {
            let tartValue = obj.value;
            var numberRegex = /^-?\d+(\.\d+)?$/;
            if (!numberRegex.test(obj.value)) {
                tartValue = 0;
                layer.msg("亲，金额填写错误！")
            }
            var number = $(obj.tr).attr("data-index");

            let cacheData = table.cache['prodHandleInfo'];
            cacheData[number][field] = tartValue;
            let d = cacheData[number];
            let args = roundByRatio(d.comBase, d.comRatio, d.comAdd, d.comCalcMode, d.comExactVal,
                d.indBase, d.indRatio, d.indlAdd, d.indCalcMode, d.indExactVal, d.specialFlag);
            d['comAmt'] = args['comAmt'];
            d['indAmt'] = args['indAmt'];
            table.reload('prodHandleInfo', {data: cacheData});
            form.render();
        }
    });


    table.on('edit(oneTimeFeeFilter)', function (obj) {
        let field = obj.field
        if (field != 'remark') {
            let tartValue = obj.value;
            var numberRegex = /^-?\d+(\.\d+)?$/;
            if (!numberRegex.test(obj.value)) {
                tartValue = 0;
                layer.msg("亲，金额填写错误！")
            }
            var number = $(obj.tr).attr("data-index");
            let cacheData = table.cache['oneTimeFee'];
            cacheData[number][field] = tartValue;
            table.reload('oneTimeFee', {data: cacheData});
            form.render();
        }
    });


    $(document).on('click', '#confirm', function () {
        let newProdHandleInfo = table.cache['prodHandleInfo'];
        for (let i = 0; i < newProdHandleInfo.length; i++) {
            let item = newProdHandleInfo[i];
            if (ML.isEmpty(item.startMonth) || ML.isEmpty(item.billStartMonth)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行开始月，或账单开始月为空！");
            }
            delete item.LAY_TABLE_INDEX;
        }

        let oneTimeFeeData = table.cache['oneTimeFee'];
        for (let i = 0; i < oneTimeFeeData.length; i++) {
            let item = oneTimeFeeData[i];
            if (ML.isEmpty(item.happenMonth) || ML.isEmpty(item.oneTimeType) || ML.isEmpty(item.fee) || ML.isEmpty(item.indFee)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行一次性数据缺失！");
            }

            if (ML.isEmpty(item.orderNo)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行一次性数据订单号缺失！");
            }
            delete item.LAY_TABLE_INDEX;
        }

        let serviceChargeData = table.cache['serviceCharge'];
        for (let i = 0; i < serviceChargeData.length; i++) {
            let item = serviceChargeData[i];
            if (ML.isEmpty(item.quotationNo) || ML.isEmpty(item.revStartMonth)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行服务费数据缺失！");
            }

            if (ML.isEmpty(item.orderNo)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行服务费数据订单号缺失！");
            }

            if (ML.isEmpty(item.templetId)) {
                return layer.msg("第" + (item.LAY_TABLE_INDEX + 1) + "行服务费数据账单模板缺失！");
            }
            delete item.LAY_TABLE_INDEX;
        }

        let params = {
            'id': $('#id').val(),
            'orderNo': $('#orderNo').val(),
            'addMonth': $('#addMonth').val(),
            'oldProdHandleInfoVosJson': oldProdHandleInfoJson,
            'newProdHandleInfoVosJson': JSON.stringify(newProdHandleInfo),
            'oneTimeFeeJson': JSON.stringify(oneTimeFeeData),
            'serviceChargeJson': JSON.stringify(serviceChargeData)
        }
        ML.ajax("/supplier/practice/editSupplierPracticeInfo", params, function (data) {
            if (data.code == 0) {
                layer.msg("修改成功！");
                layer.closeAll('iframe');
            } else {
                layer.msg(data.msg)
            }
        })
    });

    function addOneTimeFeeData() {
        var newData = table.cache["oneTimeFee"];
        newData.push({'orderNo': $('#orderNo').val()});
        table.reload('oneTimeFee', {data: newData});
    }

    function deleteOneTimeData() {
        let newData = table.cache["oneTimeFee"];
        let arr = [];
        let flag = false;
        newData.forEach(function (item) {
            if (!item['LAY_CHECKED']) {
                arr.push(item);
            } else {
                if (ML.isNotEmpty(item.id)) {
                    flag = true;
                }
            }
        });
        if (flag) {
            return layer.msg("不能删除历史数据！")
        }
        table.reload('oneTimeFee', {data: arr});
    }

    //监听添加一次性项目审核信息表格上方按钮
    table.on('toolbar(oneTimeFeeFilter)', function (obj) {
        switch (obj.event) {
            case 'saveOneTime':
                addOneTimeFeeData();
                form.render("select");
                break;
            case 'deleteOneTime':
                deleteOneTimeData();
                break;
        }
    });


    function addServiceChaegeData() {
        var newData = table.cache["serviceCharge"];
        newData.push({
            'orderNo': $('#orderNo').val(), 'practiceId': $('#id').val(),
            templetId: $('#templetId').val(), revTempId: $('#revTempId').val()
        });
        table.reload('serviceCharge', {data: newData});
    }

    function deleteServiceChargeData() {
        let newData = table.cache["serviceCharge"];
        let arr = [];
        let flag = false;
        newData.forEach(function (item) {
            if (!item['LAY_CHECKED']) {
                arr.push(item);
            } else {
                if (ML.isNotEmpty(item.id)) {
                    flag = true;
                }
            }
        });
        if (flag) {
            return layer.msg("不能删除历史数据！")
        }
        table.reload('serviceCharge', {data: arr});
    }

    //监听添加一次性项目审核信息表格上方按钮
    table.on('toolbar(serviceChargeFilter)', function (obj) {
        switch (obj.event) {
            case 'saveService':
                addServiceChaegeData();
                form.render("select");
                break;
            case 'deleteService':
                deleteServiceChargeData();
                break;
        }
    });


    form.on('select(oneTimeType)', function (data) {
        var b = table.cache["oneTimeFee"];
        let oneTimeTypeStr = data.value;
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        b[index]['oneTimeType'] = oneTimeTypeStr;
    });

    form.on('select(quotationNoFilter)', function (data) {
        var b = table.cache["serviceCharge"];
        let quotationNo = data.value;
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        b[index]['quotationNo'] = quotationNo;
        let quotationItem = quotationMap.get(quotationNo);
        let taxIncluded;
        let taxExclude;
        let valTax;
        let valTaxRate;
        if (ML.isNotEmpty(quotationItem)) {
            let ratio = new BigDecimal((1 + quotationItem.taxRate).toString());
            let price = new BigDecimal(quotationItem.serviceFee.toString());
            if (quotationItem.taxFlag == 2) {
                taxIncluded = price
                taxExclude = price.divide(ratio, 2, BigDecimal.prototype.ROUND_HALF_UP);
            } else {
                taxExclude = price;
                taxIncluded = price.multiply(ratio).setScale(2, BigDecimal.prototype.ROUND_HALF_UP);
            }
            valTax = taxIncluded.subtract(taxExclude);
            valTaxRate = quotationItem.taxRate;
        } else {
            taxIncluded = 0;
            taxExclude = 0;
            valTax = 0;
            valTaxRate = 0;
        }

        b[index].amount = Number(taxIncluded.toString());
        b[index].taxfreeAmt = Number(taxExclude.toString());
        b[index].valTax = Number(valTax.toString());
        b[index].valTaxRate = valTaxRate;
        table.reload("serviceCharge", {data: b});
    });


    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
        location.reload();
    });

    let oldProdHandleInfoJson
    let quotationMap = new Map();
    $(document).ready(function () {
        oldProdHandleInfoJson = $('#prod').val();
        if (ML.isNotEmpty(oldProdHandleInfoJson)) {
            let oldProdHandleInfo = JSON.parse(oldProdHandleInfoJson);
            table.reload("prodHandleInfo", {data: oldProdHandleInfo});
        }

        let quotationVosStr = $('#quotationVos').val();
        if (ML.isNotEmpty(quotationVosStr)) {
            quotationVosData = JSON.parse(quotationVosStr);
            quotationVosData.forEach(item => {
                quotationMap.set(item.supQuotationNo, item);
            })
        }

        let serviceFeeJson = $('#serviceFee').val();
        if (ML.isNotEmpty(serviceFeeJson)) {
            let serviceFee = JSON.parse(serviceFeeJson);
            table.reload("serviceCharge", {data: serviceFee});
        }

        let oneChargeVosStr = $('#oneChargeVos').val();
        if (ML.isNotEmpty(oneChargeVosStr)) {
            let oneChargeVos = JSON.parse(oneChargeVosStr);
            table.reload("oneTimeFee", {data: oneChargeVos});
        }

        window.top['userOrgPosPool'].forEach(item => {
            if (item.posCode == 5000) {
                $("#addMonth").removeAttr("readonly");
            }
        })


    });

})