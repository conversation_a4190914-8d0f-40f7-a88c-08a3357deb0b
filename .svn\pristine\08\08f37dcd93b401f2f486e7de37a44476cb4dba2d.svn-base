<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>实做办理反馈导入</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css" media="all"/>
    <title>Title</title>
</head>
<style>
    .layui-table-body{
        height: 80%;
    }
</style>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" title="导入日期从" style="font-weight:800">导入日期从</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="startOprTime" id="startOprTime" placeholder="yyyy-MM-dd" readonly>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入日期到" style="font-weight:800">导入日期到</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="endOprTime" id="endOprTime" placeholder="yyyy-MM-dd" readonly>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入编号" style="font-weight:800">导入编号</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="importNo" id="importNo">
                </div>
            </div>

            <a class="layui-btn" lay-submit id="btnQuery" lay-filter="btnQueryFilter">查询</a>
        </div>

    </form>
    <%--endQuery--%>


</blockquote>
<%--startTable--%>
<table class="layui-hide" id="batchImportGrid" lay-filter="batchImportGridTable"></table>
<%--endTable--%>
<script type="text/jsp" id="btn">
  <a href="javascript:void(0)" title="查看" lay-event="query"
     authURI="/customer/batchImport/gotoBatchImportedDataHistoryView"><i class="layui-icon layui-icon-search"></i></a>

</script>
<script type="text/jsp" id="topbtn">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" id="import" lay-event="import">导入
    </button>
    <button class="layui-btn layui-btn-sm" id="downloadImplementFeedbackImportTemplate" lay-event="downloadTemplate"
    >下载模板
    </button>
     <button class="layui-btn layui-btn-sm" id="importZip" lay-event="importZip">导入失败截图
    </button>
  </div>
  <div>
    <span style="color: #ff2800;font-weight: 800;display: none;" id="failResult">存在实做办理反馈，申报结果为失败的数据，具体信息请到 实做办理反馈查询  页面查看</span>
  </div>
   <div>
    <span style="color: #ff2800;font-weight: 800;">导入失败截图时,请将订单号+产品名称代号,例如(YD-20240508000002-1)作为截图的名字,并使用压缩包上传!</span>
  </div>
    <div>
    <span style="color: #0078d7;font-weight: 800;">产品名称代号:1:养老&nbsp;&nbsp;2:医疗&nbsp;&nbsp;3:失业&nbsp;&nbsp;4:工伤&nbsp;&nbsp;6:大病&nbsp;&nbsp;10:公积金</span>
  </div>

</script>
<script type="text/javascript" src="${ctx}/layui/layui.js"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common.js"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/insurancePractice/societyInsurance/implementFeedbackImport/implementFeedbackImportPage.js?v=${publishVersion}"></script>
</body>
</html>
