package com.reon.hr.sp.customer.service.impl.employee;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.customer.enums.DimissionReasonTypeEnum;
import com.reon.hr.api.customer.enums.PersonOrderEnum;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderChangeEnum;
import com.reon.hr.api.customer.vo.EmployeeEntryDimissionVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.sp.customer.dao.employee.EmployeeEntryDimissionMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeOrderLogMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeOrderMapper;
import com.reon.hr.sp.customer.dao.employee.PersonOrderEditMapper;
import com.reon.hr.sp.customer.entity.employee.EmpOrderEntryDimissionsInsCfg;
import com.reon.hr.sp.customer.entity.employee.EmployeeEntryDimission;
import com.reon.hr.sp.customer.entity.employee.EmployeeOrderChange;
import com.reon.hr.sp.customer.entity.employee.EmployeeOrderLog;
import com.reon.hr.sp.customer.service.employee.IEmployeeEntryDimissionService;
import com.reon.hr.sp.customer.service.employee.IEmployeeOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service("employeeEntryDimissionService")
public class EmployeeEntryDimissionServiceImpl implements IEmployeeEntryDimissionService {
    @Autowired
    private EmployeeEntryDimissionMapper entryDimissionMapper;

    @Autowired
    private EmployeeOrderLogMapper employeeOrderLogMapper;

    @Autowired
    private IUserWrapperService iUserWrapperService;

    @Autowired
    private EmployeeOrderMapper employeeOrderMapper;
    @Autowired
    private IEmployeeOrderService employeeOrderService;
    @Autowired
    private PersonOrderEditMapper employeeOrderChangeMapper;

    @Override
    public int updateByOrderNoSelective(EmployeeEntryDimission record) {
        return entryDimissionMapper.updateByOrderNoSelective(record);
    }

    @Override
    public EmployeeEntryDimission getByOrderNo(String orderNo) {
        return entryDimissionMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<EmployeeEntryDimission> getByOrderNos(List<String> orderNo) {
        return entryDimissionMapper.selectByOrderNos(orderNo);
    }

    @Override
    public int editIdStatus(List<String> orderNo) {
        return entryDimissionMapper.updataIdStatus(orderNo);

    }

    @Override
    public List<EmployeeEntryDimissionVo> findAllDimissionRemarkByOrderNo(List<String> orderNoList) {
        return entryDimissionMapper.findAllDimissionRemarkByOrderNo(orderNoList);
    }

    @Override
    public int updateSaveTypeAndRemarkByOrderNo(Integer saveType, String remark, String orderNo, String loginName, String dimissionRemark) {
        EmployeeOrderLog employeeOrderLog = new EmployeeOrderLog();
        Map<String, String> allUserName = iUserWrapperService.getAllUserMap();
        Long employeeId = employeeOrderMapper.findEmployeeIdByOrderNo(orderNo);
        employeeOrderLog.setEmployeeId(employeeId);
        String name = allUserName.get(loginName);
        employeeOrderLog.setOrderNo(orderNo);
        SimpleDateFormat dnf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = dnf.format(new Date());

        employeeOrderLog.setRemark("保存" + "   " + name + "   " + format);
        employeeOrderLog.setOprType(2);
        employeeOrderLog.setCreator(loginName);
        employeeOrderLog.setUpdater(loginName);
        employeeOrderLog.setCreateTime(new Date());
        employeeOrderLog.setDelFlag("N");
        employeeOrderLogMapper.insertSelective(employeeOrderLog);
        employeeOrderService.saveEmployeeOrderLog(loginName, orderNo, employeeId, PersonOrderEnum.OrderLogOprType.DIMISSION_REMARK.getCode(), dimissionRemark.replaceAll("\n", "  ") + " ");
        return entryDimissionMapper.updateSaveTypeAndRemarkByOrderNo(saveType, remark, orderNo);
    }

    @Override
    public List<String> getAllOrderNoByContractNo(String contractNo) {
        return entryDimissionMapper.getAllOrderNoByContractNo(contractNo);
    }

    @Override
    public Map<String, Date> getEntryDateMapByOrderNoSet(Set<String> orderNoSet) {
        if (CollectionUtils.isEmpty(orderNoSet))
            return Maps.newHashMap();
        EntityWrapper<EmployeeEntryDimission> wrapper = new EntityWrapper<>();
        wrapper.setSqlSelect("order_no", "entry_date").eq("del_flag", "N").in("order_no", orderNoSet);
        List<EmployeeEntryDimission> employeeEntryDimissions = entryDimissionMapper.selectList(wrapper);
        Map<String, Date> resultMap = employeeEntryDimissions.stream().collect(Collectors.toMap(EmployeeEntryDimission::getOrderNo, EmployeeEntryDimission::getEntryDate));
        return resultMap;
    }

    @Override
    public Set<String> adjustDimissionByOrderNo(Set<String> dealCfgStartMonthAndExpireMonthOrderNoSet, String time) {
        if (CollectionUtils.isEmpty(dealCfgStartMonthAndExpireMonthOrderNoSet))
            return Sets.newHashSet();
        return entryDimissionMapper.adjustDimissionByOrderNo(dealCfgStartMonthAndExpireMonthOrderNoSet, time);
    }

    @Override
    public List<EmployeeEntryDimissionVo> selectByOrderNosAndStatus(List<String> orderNo, Integer status) {
        return entryDimissionMapper.selectByOrderNosAndStatus(orderNo, status);
    }

    @Override
    public String getDimissionReasonToCache(String orderNo) {
        EmployeeOrderChange employeeOrderChange = employeeOrderChangeMapper.selectEmployeeOrderChangeByOrdersAndMethodAndStatusAndType(orderNo,
                EmployeeOrderChangeEnum.ChgMethod.DEFAULT.getCode(),
                EmployeeOrderChangeEnum.ChgStatus.DEFAULT.getCode(),
                EmployeeOrderChangeEnum.ChangeType.EXIT_REJECTION_CACHE.getCode());
        if (employeeOrderChange != null) {
            EmpOrderEntryDimissionsInsCfg empOrderEntryDimissionsInsCfg = JsonUtil.jsonToBean(employeeOrderChange.getChgContent(), EmpOrderEntryDimissionsInsCfg.class);
            return DimissionReasonTypeEnum.getName(empOrderEntryDimissionsInsCfg.getNewEmployeeEntryDimission().getDimissionReason());
        }
        return "";
    }
}
