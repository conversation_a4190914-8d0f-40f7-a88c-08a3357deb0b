form input.layui-input[disabled]{ background:#f2f2f2; color:#595963!important; }
.user_left{ width:45%; float: left; margin:20px 0 0 5%; }
.user_right{ width:25%; float: left; margin:20px 0 0 5%; text-align: center; }
.user_right p{ margin:10px 0 25px; font-size: 12px; text-align: center; color: #FF5722;}
.user_right img#userFace{ width:200px; height:200px; }
.layui-table,.layui-table th{ text-align:center; }


body .layer-ext-myskin .layui-layer-content {
	overflow: visible;
}
.selectStyle{width: 300px; background: white; border: 1px #C9C9C9 solid; height: 200px; overflow-y:scroll; display: none; position: absolute; z-index: 1000; left: 0px}


/*用户列表*/
.news_list .layui-btn,.news_list .layui-btn+.layui-btn{ margin:2px 5px; }
#page{ text-align:right; }

/*修改密码*/
.changePwd{ width:30%; margin:3% 0 0 5%; }


/*适配*/
@media screen and (max-width:1050px){
	/*用户信息*/
	.user_left,.user_right,.changePwd{ width:100%; float:none; margin-left: 0; }
	.user_right{ margin-bottom: 20px; }
}
@media screen and (max-width: 750px){
	/*用户信息*/
	.user_left,.user_right,.changePwd{ width:100%; float:none; margin-left: 0; }
	.user_right{ margin-bottom: 20px; }
}
@media screen and (max-width:432px){
	/*用户信息*/
	.user_left,.user_right,.changePwd{ width:100%; float:none; margin-left: 0; }
	.user_right{ margin-bottom: 20px; }
}