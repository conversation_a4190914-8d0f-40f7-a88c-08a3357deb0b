package com.reon.hr.sp.report.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.reon.hr.api.bill.dto.SBSPBillDto;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.common.RegionAndCityDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAreaResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.vo.ContractAreaVo;
import com.reon.hr.api.customer.vo.ContractVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.common.enums.BooleanEnum;
import com.reon.hr.common.enums.ServiceBonusServicePersonEnum;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.vo.SbspDetailReportVo;
import com.reon.hr.common.vo.ServiceBonusServicePersonReportVo;
import com.reon.hr.sp.report.dao.report.ServiceBonusServicePersonMapper;
import com.reon.hr.sp.report.entity.SbspDetail;
import com.reon.hr.sp.report.entity.ServiceBonusServicePerson;
import com.reon.hr.sp.report.service.report.SbspDetailService;
import com.reon.hr.sp.report.service.report.ServiceBonusServicePersonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.reon.hr.sp.report.entity.ServiceBonusServicePerson.*;

@Service
@Slf4j
public class ServiceBonusServicePersonServiceImpl extends ServiceImpl<ServiceBonusServicePersonMapper, ServiceBonusServicePerson> implements ServiceBonusServicePersonService {
    @Autowired
    private IUserWrapperService userWrapperService;
    @Autowired
    private IInsuranceBillWrapperService insuranceBillWrapperService;
    @Autowired
    private ServiceBonusServicePersonMapper serviceBonusServicePersonMapper;
    @Autowired
    private SbspDetailService sbspDetailService;
    @Autowired
    private IContractResourceWrapperService contractResourceWrapperService;
    @Autowired
    private IContractAreaResourceWrapperService contractAreaResourceWrapperService;

    @Override
    public Map<String, Object> getServiceBonusServicePersonReportData(Map<String, Object> conditionMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        List<ServiceBonusServicePersonReportVo> resultList = Lists.newArrayList();
        Map<String, String> allUserMap = userWrapperService.getAllUserMap();
        Map<String, RegionAndCityDto> regionAndCityMap = userWrapperService.getRegionAndCityMap();
        Wrapper<ServiceBonusServicePerson> conditionWrapper = new EntityWrapper<>();
            conditionWrapper.eq(COL_DEL_FLAG, "N");
            conditionWrapper.eq(COL_MONTH, conditionMap.get("month"));
        if (conditionMap.get("loginName") != null) {
            conditionWrapper.eq(COL_LOGIN_NAME, conditionMap.get("loginName"));
        }
        conditionWrapper.ne(COL_LOGIN_NAME, "").isNotNull(COL_LOGIN_NAME);
        List<ServiceBonusServicePerson> serviceBonusServicePersonList = serviceBonusServicePersonMapper.selectList(conditionWrapper);
        Map<String, List<ServiceBonusServicePerson>> dataMap = serviceBonusServicePersonList.stream().filter(i -> i.getLoginName() != null).collect(Collectors.groupingBy(ServiceBonusServicePerson::getLoginName));
        Integer index = 1;
        Map<String, ServiceBonusServicePersonReportVo> resultDataMap = Maps.newHashMap();
        for (String loginName : dataMap.keySet()) {
            List<ServiceBonusServicePerson> seviceBonusServicePersonList = dataMap.get(loginName);
            for (ServiceBonusServicePerson serviceBonusServicePerson : seviceBonusServicePersonList) {

                ServiceBonusServicePersonReportVo serviceBonusServicePersonReportVo =
                        resultDataMap.containsKey(loginName) ? resultDataMap.get(loginName) : new ServiceBonusServicePersonReportVo();
                if (!resultDataMap.containsKey(loginName)) {
                    serviceBonusServicePersonReportVo.setIndex(String.valueOf(index));
                    serviceBonusServicePersonReportVo.setLoginName(loginName);
                    serviceBonusServicePersonReportVo.setUserName(allUserMap.get(loginName));
                    serviceBonusServicePersonReportVo.setRegion(regionAndCityMap.get(loginName).getRegion());
                    serviceBonusServicePersonReportVo.setCity(regionAndCityMap.get(loginName).getCity());
                }

                Table<String, String, Map<String, Integer>> data = HashBasedTable.create();
                serviceBonusServicePersonReportVo.getData().forEach((rowKey, columnMap) -> columnMap.forEach((columnKey, valueMap) -> data.put(rowKey, columnKey, valueMap)));
                Map<String, Integer> dMap = data.get(ServiceBonusServicePersonEnum.PostType.getNameByCode(serviceBonusServicePerson.getPostType()),
                        serviceBonusServicePerson.getProductType() == null ? "" : ServiceBonusServicePersonEnum.ProductType.getNameByCode(serviceBonusServicePerson.getProductType()));
                if (dMap != null) {
                    Integer serviceNumD = (serviceBonusServicePerson.getServiceNumD() == null ? 0 : serviceBonusServicePerson.getServiceNumD())+dMap.getOrDefault(ServiceBonusServicePersonEnum.ServiceNumSubType.D.getName(),0);
                    Integer serviceNumNotD = (serviceBonusServicePerson.getServiceNumNotD() == null ? 0 : serviceBonusServicePerson.getServiceNumNotD())+dMap.getOrDefault(ServiceBonusServicePersonEnum.ServiceNumSubType.NOT_D.getName(),0);

                    dMap.put(ServiceBonusServicePersonEnum.ServiceNumSubType.D.getName(), serviceNumD);
                    dMap.put(ServiceBonusServicePersonEnum.ServiceNumSubType.NOT_D.getName(), serviceNumNotD);
                    dMap.put(ServiceBonusServicePersonEnum.ServiceNumSubType.ALL_COUNT.getName(), serviceNumD + serviceNumNotD);
                }
                resultDataMap.put(loginName, serviceBonusServicePersonReportVo);
            }
            index += 1;
        }
        resultList.addAll(resultDataMap.values());
        List<SbspDetail> sbspDetails = sbspDetailService.selectList(new EntityWrapper<SbspDetail>()
                .eq("month", conditionMap.get("month"))
                .eq("del_flag", "N"));

        List<SbspDetailReportVo> secondSheetList = Lists.newArrayList();
        List<SbspDetailReportVo> thirdSheetList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(sbspDetails)) {
            // 初始化三个去重集合
            Set<String> contractNoSet = new LinkedHashSet<>();
            Set<String> contractAreaNoSet = new LinkedHashSet<>();
            Set<Long> custIdSet = new LinkedHashSet<>();
            sbspDetails.forEach(detail -> {
                contractNoSet.add(detail.getContractNo());
                contractAreaNoSet.add(detail.getContractAreaNo());
                custIdSet.add(detail.getCustId());
            });

            List<String> contractNoList = new ArrayList<>(contractNoSet);
            List<String> contractAreaNoList = new ArrayList<>(contractAreaNoSet);

            List<ContractVo> byContractNoList = contractResourceWrapperService.getByContractNoList(contractNoList);
            Map<String, String> contractNameMap = byContractNoList.stream().collect(Collectors.toMap(ContractVo::getContractNo, ContractVo::getContractName, (a, b) -> b));
            Map<Long, String> custNameMap = byContractNoList.stream().collect(Collectors.toMap(ContractVo::getCustId, ContractVo::getCustName, (a, b) -> b));
            List<ContractAreaVo> caList = contractAreaResourceWrapperService.getAllContractAreaByContractAreaNoList(contractAreaNoList);
            Map<String, String> caNameMap = caList.stream().collect(Collectors.toMap(ContractAreaVo::getContractAreaNo, ContractAreaVo::getName, (a, b) -> b));
            secondSheetList = sbspDetails.stream().map(it -> {
                SbspDetailReportVo sbspDetailReportVo = new SbspDetailReportVo();
                BeanUtils.copyProperties(it, sbspDetailReportVo);
                sbspDetailReportVo.setContractName(contractNameMap.get(it.getContractNo()));
                sbspDetailReportVo.setCustName(custNameMap.get(it.getCustId()));
                sbspDetailReportVo.setContractAreaName(caNameMap.get(it.getContractAreaNo()));
                sbspDetailReportVo.setCommissioner(getNameByLoginName(sbspDetailReportVo.getCommissioner(), allUserMap));
                sbspDetailReportVo.setSalaryCommissioner(getNameByLoginName(sbspDetailReportVo.getSalaryCommissioner(), allUserMap));
                sbspDetailReportVo.setLaterMan(getNameByLoginName(sbspDetailReportVo.getLaterMan(), allUserMap));
                sbspDetailReportVo.setSupperMan(getNameByLoginName(sbspDetailReportVo.getSupperMan(), allUserMap));
                sbspDetailReportVo.setPostTypeStr(ServiceBonusServicePersonEnum.PostType.getNameByCode(sbspDetailReportVo.getPostType()));
                sbspDetailReportVo.setProductTypeStr(ServiceBonusServicePersonEnum.ProductType.getNameByCode(sbspDetailReportVo.getProductType()));
                sbspDetailReportVo.setContractAssignStr(BooleanEnum.getEnumByCode(it.getContractAssign()));
                sbspDetailReportVo.setContractAreaAssignStr(BooleanEnum.getEnumByCode(it.getContractAreaAssign()));
                return sbspDetailReportVo;
            }).collect(Collectors.toList());
            thirdSheetList = secondSheetList.stream().filter(i -> (i.getContractAreaAssign() == BooleanEnum.TRUE.getCode() ||
                            i.getContractAssign() == BooleanEnum.TRUE.getCode())&&(
                            (i.getCommissioner() != null && i.getCommissioner().contains(",")) ||
                                    (i.getSalaryCommissioner() != null && i.getSalaryCommissioner().contains(",")) ||
                                    (i.getLaterMan() != null && i.getLaterMan().contains(",")) ||
                                    (i.getSupperMan() != null && i.getSupperMan().contains(","))
                    ))
                    .collect(Collectors.toList());
        }
        resultMap.put("first_sheet", resultList);
        resultMap.put("second_sheet", secondSheetList);
        resultMap.put("third_sheet", thirdSheetList);

        return resultMap;
    }

    private String getNameByLoginName(String loginName, Map<String, String> allUserMap) {
        return StringUtils.isNotBlank(loginName) ?
                Arrays.stream(loginName.split(","))
                        .map(i -> allUserMap.get(i))
                        .collect(Collectors.joining(",")) : null;
    }


    @Override
    public void updateExecute(Integer month) {
        log.info("----updateExecute( month : {}) 开始生成----",month);
        int preYearMonth;
        if (month == null)
            preYearMonth = DateUtil.getPreYearMonth(DateUtil.getCurrentYearMonth());
        else
            preYearMonth = month;
        List<SBSPBillDto> sbspBillDtos = insuranceBillWrapperService.getDataByBillMonth(preYearMonth);
        serviceBonusServicePersonMapper.deleteByMonth(preYearMonth);
        sbspDetailService.deleteByMonth(preYearMonth);
        List<SbspDetail> sbspDetailList = Lists.newArrayList();
        /** 处理详情表数据 */
        sbspBillDtos.forEach(sbsp -> {
            SbspDetail sbspDetail = new SbspDetail();
            sbspDetail.setMonth(sbsp.getMonth());
            sbspDetail.setContractNo(sbsp.getContractNo());
            /** 一般只有 社保这里 还有 供应商/还有实作 会有小合同 */
            sbspDetail.setContractAreaNo(sbsp.getContractAreaNo());
            sbspDetail.setProductType(ServiceBonusServicePersonEnum.ProductType.getCodeByName(sbsp.getProductType()));
            sbspDetail.setPostType(ServiceBonusServicePersonEnum.PostType.getCodeByName(sbsp.getPostType()));
            sbspDetail.setCustId(sbsp.getCustId());
            sbspDetail.setContractAssign(sbsp.getContractAssign());
            sbspDetail.setContractAreaAssign(sbsp.getContractAreaAssign());
            sbspDetail.setCommissioner(sbsp.getCommissioner());
            sbspDetail.setSalaryCommissioner(sbsp.getSalaryCommissioner());
            sbspDetail.setLaterMan(sbsp.getLaterMan());
            sbspDetail.setSupperMan(sbsp.getSupperMan());
            sbspDetail.setServiceNumD(sbsp.getServiceNumD());
            sbspDetail.setServiceNumNotD(sbsp.getServiceNumNotD());
            sbspDetail.setCreator("admin");
            sbspDetailList.add(sbspDetail);
        });
        if (CollectionUtils.isNotEmpty(sbspDetailList))
            sbspDetailService.insertBatch(sbspDetailList);
        /** 如果 没有任何 大合同,小合同进行分配,则数进入 主数据表,进行处理,如果,大合同或者小合同进行分配过,则数据 进入sheet2,单独显示 */
        List<SbspDetail> canInsertParentTableList = sbspDetailList.stream().filter(i ->
                i.getContractAreaAssign() != BooleanEnum.TRUE.getCode() && i.getContractAssign() != BooleanEnum.TRUE.getCode()
        ).collect(Collectors.toList());

        Map<String, ServiceBonusServicePerson> insertParentTableMap = Maps.newHashMap();

        for (SbspDetail sbspDetail : canInsertParentTableList) {
            String key = null;
            String loginName = null;
            /** 如果岗位是项目 客服或者 本地单(项目+后道) 那就只有 项目客服 commissioner*/
            /** 如果岗位是 薪资专员 那就只有薪资专员  salaryCommissioner*/
            /** 如果岗位 是 后道/接单  那就只有后道方客服  laterMan*/
            /** 如果岗位 是供应商客服,那就只有 供应商客服 supperMan*/
            if (sbspDetail.getPostType() == ServiceBonusServicePersonEnum.PostType.PROJECT_CUSTOMER_SERVICE.getCode()
                    || sbspDetail.getPostType() == ServiceBonusServicePersonEnum.PostType.LOCAL_ORDER_PROJECT_AFTER.getCode()) {
                key = sbspDetail.getCommissioner() + "##" + sbspDetail.getPostType() + "##" + sbspDetail.getProductType();
                loginName = sbspDetail.getCommissioner();
            }
            if (sbspDetail.getPostType() == ServiceBonusServicePersonEnum.PostType.SALARY_SPECIALIST.getCode()) {
                key = sbspDetail.getSalaryCommissioner() + "##" + sbspDetail.getPostType() + "##" + sbspDetail.getProductType();
                loginName = sbspDetail.getSalaryCommissioner();
            }
            if (sbspDetail.getPostType() == ServiceBonusServicePersonEnum.PostType.AFTER_PROCESS_ORDER.getCode()) {
                key = sbspDetail.getLaterMan() + "##" + sbspDetail.getPostType() + "##" + sbspDetail.getProductType();
                loginName = sbspDetail.getLaterMan();
            }
            if (sbspDetail.getPostType() == ServiceBonusServicePersonEnum.PostType.SUPPLIER_CUSTOMER_SERVICE.getCode()) {
                key = sbspDetail.getSupperMan() + "##" + sbspDetail.getPostType() + "##" + sbspDetail.getProductType();
                loginName = sbspDetail.getSupperMan();
            }

            if (key != null) {
                Integer serviceNumD = sbspDetail.getServiceNumD() == null ? 0 : sbspDetail.getServiceNumD();
                Integer serviceNumNotD = sbspDetail.getServiceNumNotD() == null ? 0 : sbspDetail.getServiceNumNotD();
                if (!insertParentTableMap.containsKey(key)) {
                    ServiceBonusServicePerson serviceBonusServicePerson = new ServiceBonusServicePerson();
                    serviceBonusServicePerson.setMonth(sbspDetail.getMonth());
                    serviceBonusServicePerson.setLoginName(loginName);
                    serviceBonusServicePerson.setPostType(sbspDetail.getPostType());
                    serviceBonusServicePerson.setProductType(sbspDetail.getProductType());
                    serviceBonusServicePerson.setServiceContent(sbspDetail.getProductType() !=
                            ServiceBonusServicePersonEnum.ProductType.SALARY_PAYMENT.getCode()
                            ? sbspDetail.getProductType() : null);   // 因为薪资发放没有服务内容 所以这里设置为空
                    serviceBonusServicePerson.setServiceNumD(serviceNumD);
                    serviceBonusServicePerson.setServiceNumNotD(serviceNumNotD);
                    serviceBonusServicePerson.setCreator("admin");
                    insertParentTableMap.put(key, serviceBonusServicePerson);
                } else {
                    ServiceBonusServicePerson serviceBonusServicePerson = insertParentTableMap.get(key);
                    serviceBonusServicePerson.setServiceNumD(serviceBonusServicePerson.getServiceNumD() + serviceNumD);
                    serviceBonusServicePerson.setServiceNumNotD(serviceBonusServicePerson.getServiceNumNotD() + serviceNumNotD);
                    insertParentTableMap.put(key,serviceBonusServicePerson);
                }
            }
        }
        List<ServiceBonusServicePerson> insertServiceBonusServicePersonList = Lists.newArrayList();
        insertServiceBonusServicePersonList.addAll(insertParentTableMap.values());
        if (CollectionUtils.isNotEmpty(insertServiceBonusServicePersonList)) {
            serviceBonusServicePersonMapper.insertBatch(insertServiceBonusServicePersonList);
        }
        log.info("----updateExecute( month : {}) 结束生成----",month);
    }
}

