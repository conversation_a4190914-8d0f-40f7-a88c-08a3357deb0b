package com.reon.hr.sp.bill.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.enums.ValidFlagEnum;
import com.reon.hr.api.bill.constant.ProductTypeEnum;
import com.reon.hr.api.bill.enums.*;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.bill.vo.*;
import com.reon.hr.api.bill.vo.bill.BillInvoiceFromVo;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.vo.ContractVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.enums.invoice.InvoiceFlagEnum;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.vo.RemoteInvoiceLogVo;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.nnInvoice.ProducerScopeTypeNNInvoice;
import com.reon.hr.sp.bill.dao.bill.*;
import com.reon.hr.sp.bill.dao.remoteInvoice.RemoteInvoiceLogMapper;
import com.reon.hr.sp.bill.entity.bill.*;
import com.reon.hr.sp.bill.service.bill.BillInvoiceService;
import com.reon.hr.sp.bill.service.bill.IBillInvoiceApprovalService;
import com.reon.hr.sp.bill.service.bill.IInsuranceBillService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BillInvoiceServiceImpl implements BillInvoiceService {

    @Autowired
    private BillInvoiceMapper billInvoiceMapper;

    @Autowired
    private BillInvoiceFromMapper billInvoiceFromMapper;

    @Autowired
    private BillInvoiceDetailMapper billInvoiceDetailMapper;

    @Autowired
    private BillInvoiceDetailDisposableMapper billInvoiceDetailDisposableMapper;

    @Autowired
    private InsuranceBillMapper insuranceBillMapper;

    @Autowired
    private InsuranceBillDetailMapper insuranceBillDetailMapper;

    @Autowired
    private DisposableItemApprovalMapper disposableItemApprovalMapper;
    @Autowired
    private BillCheckInvoiceMapper billCheckInvoiceMapper;

    @Resource
    private ISequenceService iSequenceService;

    @Resource
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;

    @Resource
    private IContractResourceWrapperService contractWrapperService;

    @Resource
    private RemoteInvoiceLogMapper remoteInvoiceLogMapper;

    @Resource
    private MqMessageSender mqMessageSender;

    @Resource
    private IInsuranceBillService insuranceBillService;

    @Resource
    private IBillInvoiceApprovalService billInvoiceApprovalService;

    private static final Logger log = LoggerFactory.getLogger(BillInvoiceServiceImpl.class);

    @Override
    public Page<InvoiceBillVo> getByPage(Integer page, Integer limit, InvoiceBillVo vo) {
        Page<InvoiceBillVo> quotationVoPage = new Page<>(page, limit);
        List<InvoiceBillVo> quotationList = billInvoiceMapper.getBillByPage(quotationVoPage, vo);
        quotationVoPage.setRecords(quotationList);
        return quotationVoPage;
    }

    @Override
    public List<InvoiceViewCancelVo> getCancelData(String[] billId) {
        return billInvoiceMapper.getCancelData(billId);
    }

    @Override
    public List<InvoiceBillVo> getInvoiceData(String[] billId) {
        return billInvoiceMapper.getInvoiceData(billId);
    }

    @Override
    public void saveBillInvoice(InvoiceSaveParamVo invoiceSaveParamVo, String loginName, String orgCode) {

        // 新增开票主表
        BillInvoice billInvoice = new BillInvoice();
        // 开票详细表
        BillInvoiceFrom billInvoiceFrom;
        // 开票明细表
        BillInvoiceDetail billInvoiceDetail;
        // 开票一次性收费表
        BillInvoiceDetailDisposable billInvoiceDetailDisposable;
        // 账单信息表
        InsuranceBill insuranceBill;
        //
        BillCheckInvoice billCheckInvoice;

        // 赋值BillInvoice
        setBillInvoice(invoiceSaveParamVo, loginName, orgCode, billInvoice);
        String invoiceNo = iSequenceService.getInvoiceNo();
        billInvoice.setInvoiceNo(invoiceNo);
        billInvoiceMapper.insertSelective(billInvoice);

        // 获取新增的开票ID
        Long billInvoiceId = billInvoice.getId();

        // 新增开票详表
        for (InvoiceFromSaveParamVo invoiceFromSaveParamVo : invoiceSaveParamVo.getBillInvoiceList()) {
            billInvoiceFrom = new BillInvoiceFrom();
            // 赋值BillInvoiceFrom
            setBillInvoiceFrom(invoiceFromSaveParamVo, billInvoiceFrom, loginName, billInvoiceId);
            billInvoiceFromMapper.insertSelective(billInvoiceFrom);

            Long billInvoiceFromId = billInvoiceFrom.getId();
            // 新增开票明细表
            for (ServiceAndCollectionSaveParamVo serviceAndCollectionSaveParamVo : invoiceFromSaveParamVo.getServiceAndCollectionList()) {
                billInvoiceDetail = new BillInvoiceDetail();
                // 赋值BillInvoiceDetail
                setBillInvoiceDetail(serviceAndCollectionSaveParamVo, billInvoiceDetail, loginName, billInvoiceFromId);
                billInvoiceDetailMapper.insertSelective(billInvoiceDetail);

                // 修改账单信息明细表 insuranceBillDetailMapper
                InsuranceBillDetail insuranceBillDetail = insuranceBillDetailMapper.selectByPrimaryKey(serviceAndCollectionSaveParamVo.getBillDetailId());
                setInsuranceBillDetail(insuranceBillDetail, serviceAndCollectionSaveParamVo, loginName);
                insuranceBillDetailMapper.updateByPrimaryKeySelective(insuranceBillDetail);
            }

            // 新增一次性收费表
            for (DisposableSaveParamVo disposableSaveParamVo : invoiceFromSaveParamVo.getDisposableList()) {
                billInvoiceDetailDisposable = new BillInvoiceDetailDisposable();
                // 赋值BillInvoiceDetailDisposable
                setBillInvoiceDetailDisposable(disposableSaveParamVo, billInvoiceDetailDisposable, loginName, billInvoiceFromId);
                billInvoiceDetailDisposableMapper.insertSelective(billInvoiceDetailDisposable);

                // 修改一次性项目表
                DisposableItemApproval disposableItemApproval = disposableItemApprovalMapper.selectByPrimaryKey(disposableSaveParamVo.getDisposableId());
                setDisposableItemApproval(disposableItemApproval, disposableSaveParamVo, loginName);
                disposableItemApprovalMapper.updateByPrimaryKeySelective(disposableItemApproval);
            }
            // 修改账单信息表
            InsuranceBillVo insuranceBillVo = insuranceBillMapper.selectByPrimaryKey(invoiceFromSaveParamVo.getBillId());
            insuranceBill = new InsuranceBill();
            // bill_check_invoice
            billCheckInvoice = new BillCheckInvoice();
            setInsuranceBill(insuranceBillVo, insuranceBill, billCheckInvoice, invoiceFromSaveParamVo, loginName, invoiceFromSaveParamVo.getInvoAmt());
//            insuranceBillMapper.updateByPrimaryKeySelective(insuranceBill);

            billCheckInvoiceMapper.updateByPrimaryKeySelective(billCheckInvoice);
        }
    }

    /**
     * 别动 别优化
     */
    @Override
    public void nestedSaveBillInvoice(InvoiceSaveParamVo invoiceSaveParamVo, String loginName, String orgCode) {
        // 新增开票主表
        BillInvoice billInvoice = new BillInvoice();
        // 开票详细表
        BillInvoiceFrom billInvoiceFrom;
        // 开票明细表
        BillInvoiceDetail billInvoiceDetail;
        // 开票一次性收费表
        BillInvoiceDetailDisposable billInvoiceDetailDisposable;
        // 账单信息表
        InsuranceBill insuranceBill;
        //
        BillCheckInvoice billCheckInvoice;

        // 赋值BillInvoice
        setBillInvoice(invoiceSaveParamVo, loginName, orgCode, billInvoice);
        String invoiceNo = iSequenceService.getInvoiceNo();
        billInvoice.setInvoiceNo(invoiceNo);
        billInvoiceMapper.insertSelective(billInvoice);

        // 获取新增的开票ID
        Long billInvoiceId = billInvoice.getId();

        // 新增开票详表
        for (InvoiceFromSaveParamVo invoiceFromSaveParamVo : invoiceSaveParamVo.getBillInvoiceList()) {
            billInvoiceFrom = new BillInvoiceFrom();
            // 赋值BillInvoiceFrom
            setBillInvoiceFrom(invoiceFromSaveParamVo, billInvoiceFrom, loginName, billInvoiceId);
            billInvoiceFromMapper.insertSelective(billInvoiceFrom);

            Long billInvoiceFromId = billInvoiceFrom.getId();
            // 新增开票明细表
            for (ServiceAndCollectionSaveParamVo serviceAndCollectionSaveParamVo : invoiceFromSaveParamVo.getServiceAndCollectionList()) {
                billInvoiceDetail = new BillInvoiceDetail();
                // 赋值BillInvoiceDetail
                setBillInvoiceDetail(serviceAndCollectionSaveParamVo, billInvoiceDetail, loginName, billInvoiceFromId);
                billInvoiceDetailMapper.insertSelective(billInvoiceDetail);

                // 修改账单信息明细表 insuranceBillDetailMapper
                InsuranceBillDetail insuranceBillDetail = insuranceBillDetailMapper.selectByPrimaryKey(serviceAndCollectionSaveParamVo.getBillDetailId());
                setInsuranceBillDetail(insuranceBillDetail, serviceAndCollectionSaveParamVo, loginName);
                insuranceBillDetailMapper.updateByPrimaryKeySelective(insuranceBillDetail);
            }

            // 新增一次性收费表
            for (DisposableSaveParamVo disposableSaveParamVo : invoiceFromSaveParamVo.getDisposableList()) {
                billInvoiceDetailDisposable = new BillInvoiceDetailDisposable();
                // 赋值BillInvoiceDetailDisposable
                setBillInvoiceDetailDisposable(disposableSaveParamVo, billInvoiceDetailDisposable, loginName, billInvoiceFromId);
                billInvoiceDetailDisposableMapper.insertSelective(billInvoiceDetailDisposable);

                // 修改一次性项目表
                DisposableItemApproval disposableItemApproval = disposableItemApprovalMapper.selectByPrimaryKey(disposableSaveParamVo.getDisposableId());
                setDisposableItemApproval(disposableItemApproval, disposableSaveParamVo, loginName);
                disposableItemApprovalMapper.updateByPrimaryKeySelective(disposableItemApproval);
            }
            // 修改账单信息表
            InsuranceBillVo insuranceBillVo = insuranceBillMapper.selectByPrimaryKey(invoiceFromSaveParamVo.getBillId());
            insuranceBill = new InsuranceBill();
            // bill_check_invoice
            billCheckInvoice = new BillCheckInvoice();
            setInsuranceBill(insuranceBillVo, insuranceBill, billCheckInvoice, invoiceFromSaveParamVo, loginName, invoiceFromSaveParamVo.getInvoAmt());
//            insuranceBillMapper.updateByPrimaryKeySelective(insuranceBill);

            billCheckInvoiceMapper.updateByPrimaryKeySelective(billCheckInvoice);
        }
    }

    @Override
    public Page<InvoiceBillVo> getInvoiceByPage(Integer page, Integer limit, InvoiceBillQueryVo vo, String companyCode) {
        Page<InvoiceBillVo> quotationVoPage = new Page<>(page, limit);
        List<InvoiceBillVo> quotationList = billInvoiceMapper.getInvoiceByPage(quotationVoPage, vo, companyCode);
        quotationVoPage.setRecords(quotationList);
        return quotationVoPage;
    }

    @Override
    public List<InvoiceBillVo> getInvoiceByPage(InvoiceBillQueryVo vo, String companyCode) {
        return billInvoiceMapper.getInvoiceByPage1(vo, companyCode);
    }

    @Override
    public List<InvoiceBillVo> getInvoiceByDateAndCustId(InvoiceBillQueryVo vo) {
        if (vo.getCityCode() != null) {
            List<OrgVo> orgVoList = orgnizationResourceWrapperService.getOrgByCityAndType(vo.getCityCode(), "1");
            List<String> orgCodes = orgVoList.stream().map(s -> s.getOrgCode().replace("COMPANY_", "")).collect(Collectors.toList());
            vo.setOrgCodes(orgCodes);
        }
        return billInvoiceMapper.getInvoiceByDateAndCustId(vo);
    }

    @Override
    public List<InvoiceBillVo> getInvoiceById(Long[] ids) {
        return billInvoiceMapper.getInvoiceUnApprovalById(ids, null);
    }

    /*public void getInvoiceDetail(InvoiceBillQueryVo vo) {
        List<InvoiceBillVo> invoiceBillVoList = billInvoiceMapper.getInvoiceByDateAndCustId(vo);
        for (InvoiceBillVo invoiceBillVo : invoiceBillVoList) {

        }
    }*/

    @Override
    public InvoiceBillVo getCustIdAndTempletByInvoiceId(Long invoiceId) {
        List<InvoiceBillVo> invoiceBillVos = billInvoiceFromMapper.getCustIdAndTempletByInvoiceId(invoiceId);
        return invoiceBillVos.size() > 0 ? invoiceBillVos.get(0) : null;
    }

    @Override
    public List<Long> getBillIdByInvoiceId(Long invoiceId) {
        return billInvoiceFromMapper.getBillIdByInvoiceId(invoiceId);
    }

    @Override
    public List<BillInvoiceFromVo> getBillIdListByInvoiceIdList(List<Long> list) {
        return billInvoiceFromMapper.getBillIdListByInvoiceIdList(list);
    }

    @Override
    public InvoiceSaveParamVo selectByPrimaryKey(Long id) {
        InvoiceSaveParamVo invoiceBillVo = new InvoiceSaveParamVo();
        BillInvoice billInvoice = billInvoiceMapper.selectByPrimaryKey(id);
        BeanUtils.copyProperties(billInvoice, invoiceBillVo);
        return invoiceBillVo;
    }

    @Override
    public BillInvoiceVo selectById(Long id) {
        return billInvoiceMapper.selectById(id);
    }

    @Override
    public Page<InvoiceBillVo> searchCustByOrgCode(Integer page, Integer limit, String companyCode, String custName) {
        Page<InvoiceBillVo> quotationVoPage = new Page<>(page, limit);
        List<InvoiceBillVo> quotationList = billInvoiceMapper.searchCustByOrgCode(quotationVoPage, companyCode, custName);
        quotationVoPage.setRecords(quotationList);
        return quotationVoPage;
    }

    @Override
    public IncomeCountTableReportDto getIncomeCountTableDate(Long subCheckId, Long invoiceId) {
        return billInvoiceMapper.getIncomeCountTableDate(subCheckId, invoiceId);
    }

    @Override
    public InvoiceBillVo getCheckAmtAndInvoiceAmtByBillCheckInvoice(Long billId) {
        return billInvoiceMapper.getCheckAmtAndInvoiceAmtByBillCheckInvoice(billId);
    }

    @Override
    public BigDecimal getCheckAmtByCheckDetail(Long id) {
        return billCheckInvoiceMapper.getCheckAmtByCheckDetail(id);
    }

    @Override
    public BigDecimal getCheckTotalAmt(Long id) {
        return billCheckInvoiceMapper.getCheckTotalAmt(id);
    }

    /**
     * 在实收开票查询选中开票主表信息点击红冲
     * 先查看是否有诺诺开票记录
     * 如果没有说明正常红冲
     * 如果有则说明是诺诺红冲
     * 将子表没有红冲过的蓝票为红冲审批中
     * 最终修改主表状态
     *
     * @param invoiceId 发票 ID
     * @param orderNo   订单编号
     * @param loginName 登录名称
     */
    @Override
    public void updateAbolishInvoice(Long invoiceId, String orderNo, String loginName) {
        Long[] invoiceBillIds = {invoiceId};
        ApprovalStatusEnum approvalStatusEnum;
        List<RemoteInvoiceLogVo> remoteInvoiceLogVoList = remoteInvoiceLogMapper.queryByInvoiceIds(Lists.newArrayList(invoiceId));
        if (CollectionUtils.isEmpty(remoteInvoiceLogVoList)) {
            approvalStatusEnum = ApprovalStatusEnum.ALL_ABOLITION_APPROVAL;
            log.error("未查询到该发票的远程日志信息");
        } else {
            // 查询remoteInvoiceLogVoList中批次号最大的数据
            RemoteInvoiceLogVo maxVo = remoteInvoiceLogVoList.stream()
                    .filter(vo -> vo.getBatchNo() != null && vo.getBatchNo() != 0)
                    .max(Comparator.comparing(RemoteInvoiceLogVo::getBatchNo))
                    .orElse(null);

            // 同一天的多次部分红冲,批次号一致,否则递增,初次红冲批次号为1
            Integer maxBatchNo;
            if (maxVo != null) {
                if (DateUtil.isSameDay(maxVo.getCreateTime(), new Date())) {
                    maxBatchNo = maxVo.getBatchNo();
                } else {
                    maxBatchNo = maxVo.getBatchNo() + 1;
                }
            } else {
                maxBatchNo = 1;
            }
            if (StringUtils.isEmpty(orderNo)) {
                approvalStatusEnum = ApprovalStatusEnum.ALL_ABOLITION_APPROVAL;

                List<RemoteInvoiceLogVo> batchUpdateVos = remoteInvoiceLogVoList.stream()
                        .filter(vo -> Objects.equals(vo.getInvoiceFlag(), Integer.valueOf(InvoiceFlagEnum.BLUE.getCode())))
                        .filter(vo -> vo.getBlueStatus().equals(BlueInvoiceStatusEnum.UN_ABOLISHED.getCode()))
                        .map(vo -> RemoteInvoiceLogVo.builder()
                                .orderNo(vo.getOrderNo())
                                .blueStatus(BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode())
                                .batchNo(maxBatchNo)
                                .updater(loginName)
                                .build())
                        .collect(Collectors.toList());
                remoteInvoiceLogMapper.updateBatch(batchUpdateVos);
            } else {
                List<RemoteInvoiceLogVo> collect = remoteInvoiceLogVoList.stream()
                        .filter(vo -> !vo.getOrderNo().equals(orderNo))
                        .filter(vo -> Objects.equals(vo.getInvoiceFlag(), Integer.valueOf(InvoiceFlagEnum.BLUE.getCode())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    boolean noneMatchUnAbolished = collect.stream()
                            .noneMatch(vo -> vo.getBlueStatus().equals(BlueInvoiceStatusEnum.UN_ABOLISHED.getCode()));
                    approvalStatusEnum = noneMatchUnAbolished ? ApprovalStatusEnum.ALL_ABOLITION_APPROVAL : ApprovalStatusEnum.PARTIALLY_ABOLITION_APPROVAL;
                } else {
                    approvalStatusEnum = ApprovalStatusEnum.ALL_ABOLITION_APPROVAL;
                }

                RemoteInvoiceLogVo remoteInvoiceLogVo = RemoteInvoiceLogVo.builder()
                        .invoiceId(invoiceId)
                        .orderNo(orderNo)
                        .blueStatus(BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode())
                        .batchNo(maxBatchNo)
                        .updater(loginName)
                        .build();
                remoteInvoiceLogMapper.updateByInvoiceId(remoteInvoiceLogVo);
            }
        }
        billInvoiceMapper.updateStatusByIds(invoiceBillIds, loginName, approvalStatusEnum.getCode(), 1);
    }

    @Override
    public void updateRejectAbolishInvoice(Long[] invoiceIdList, String loginName) {
        List<Long> invoiceIds = Arrays.asList(invoiceIdList);
        List<RemoteInvoiceLogVo> remoteInvoiceLogVoList = remoteInvoiceLogMapper.queryByInvoiceIds(invoiceIds);
        if (CollectionUtils.isEmpty(remoteInvoiceLogVoList)) {
            billInvoiceMapper.updateStatusByIds(invoiceIdList, loginName, ApprovalStatusEnum.INVOICE.getCode(), null);
        } else {
            boolean hasAbolished = remoteInvoiceLogVoList.stream()
                    .anyMatch(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.ABOLISHED.getCode());
            boolean allApproving = remoteInvoiceLogVoList.stream()
                    .allMatch(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode());
            boolean hasUnAbolished = remoteInvoiceLogVoList.stream()
                    .anyMatch(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.UN_ABOLISHED.getCode());
            // 当有红票已开票 说明当前是红冲另外一部分,驳回则改为部分红冲
            if (hasAbolished) {
                billInvoiceMapper.updateStatusByIds(invoiceIdList, loginName, ApprovalStatusEnum.HAS_BEEN_PARTIALLY_ABOLITION.getCode(), null);
            }
            // 当没有红票已开票,全部红冲审批或者部分红冲审批,驳回则改为已开票状态
            else if (allApproving || hasUnAbolished) {
                billInvoiceMapper.updateStatusByIds(invoiceIdList, loginName, ApprovalStatusEnum.INVOICE.getCode(), null);
            }
            List<RemoteInvoiceLogVo> remoteInvoiceLogVos = remoteInvoiceLogVoList.stream()
                    .filter(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode())
                    .map(vo -> RemoteInvoiceLogVo.builder()
                            .orderNo(vo.getOrderNo())
                            .blueStatus(BlueInvoiceStatusEnum.UN_ABOLISHED.getCode())
                            .batchNo(0)
                            .build())
                    .collect(Collectors.toList());
            remoteInvoiceLogMapper.updateBatch(remoteInvoiceLogVos);
        }
    }

    @Override
    public void updateInvoice(Long[] invoiceIdList, String loginName, Integer invoiceStatus) {
        billInvoiceMapper.updateStatusByIds(invoiceIdList, loginName, invoiceStatus, null);
    }

    @Override
    public void invoiceCallback(String orderNo) {
        mqMessageSender.send(ModuleType.REON_INVOICE, ProducerScopeTypeNNInvoice.REON_NN_RESP_INVOICE_CALLBACK_NOTIFY, orderNo);
    }

    @Override
    public String automaticInvoice(List<Long> invoiceIds, String loginName) {
        log.info("->>>>开始自动处理红冲发票,开票id集合为:{}", invoiceIds);
        List<BillInvoice> billInvoiceList = billInvoiceMapper.getBillInvoiceByIdList(invoiceIds);
        if (CollectionUtils.isEmpty(billInvoiceList)) {
            log.error("->>>>未查询到对应数据,开票id集合为:{}", invoiceIds);
            return "未查询到对应数据";
        }
        List<RemoteInvoiceLogVo> remoteInvoiceLogVoList = remoteInvoiceLogMapper.queryByInvoiceIds(invoiceIds);
        if (CollectionUtils.isEmpty(remoteInvoiceLogVoList)) {
            return "所有一键红票的数据必须有一键蓝票";
        } else {
            List<Long> remoteInvoiceIdList = remoteInvoiceLogVoList.stream()
                    .map(RemoteInvoiceLogVo::getInvoiceId)
                    .distinct()
                    .collect(Collectors.toList());
            if (remoteInvoiceIdList.size() != invoiceIds.size()) {
                return "所有一键红票的数据必须有一键蓝票";
            }
            /*boolean allHasData = remoteInvoiceLogVoList.stream()
                    .allMatch(vo -> invoiceIds.contains(vo.getInvoiceId()));
            if (!allHasData) {
                return "所有一键红票的数据必须有一键蓝票";
            }*/
        }
        List<BillInvoice> invoiceForUpdates = billInvoiceList.stream()
                .map(vo -> BillInvoice.builder()
                        .id(vo.getId())
                        .status(vo.getStatus() == ApprovalStatusEnum.ALL_ABOLITION_APPROVAL.getCode() ?
                                ApprovalStatusEnum.ALL_ABOLISHING.getCode() : ApprovalStatusEnum.PARTIALLY_ABOLISHING.getCode())
                        .build())
                .collect(Collectors.toList());
        billInvoiceMapper.updateStatus(invoiceForUpdates, loginName);


        List<RemoteInvoiceLogVo> abolishOrderList = remoteInvoiceLogVoList.stream()
                .filter(vo -> Objects.equals(vo.getInvoiceFlag(), Integer.valueOf(InvoiceFlagEnum.BLUE.getCode())))
                .filter(vo -> vo.getBlueStatus().equals(BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode()))
                .collect(Collectors.toList());
        String msg = JSONArray.toJSONString(abolishOrderList);
        mqMessageSender.send(ModuleType.REON_INVOICE, ProducerScopeTypeNNInvoice.REON_NN_RESP_INVOICE_RED_REQUEST_NOTIFY, msg);
        return "发票已提交";
    }

    @Override
    public int updateInvoiceGenFlag(Long invoiceId, Integer flag) {
        return billInvoiceMapper.updateInvoiceGenFlag(invoiceId, flag);
    }

    @Override
    public void updateRedInvoiceData(Long invoiceId, List<String> orderNos, String loginName) {
        Long[] invoiceIdList = {invoiceId};
        List<BillInvoiceFrom> billInvoiceFromByInvoiceIdList = billInvoiceFromMapper.getByInvoiceIds(invoiceIdList);

        Map<Long, List<BillInvoiceFrom>> invoiceIdBIFMap = new HashMap<>();
        List<Long> billIdList = new ArrayList<>();
        List<Long> bIFIds = new ArrayList<>();
        for (BillInvoiceFrom bif : billInvoiceFromByInvoiceIdList) {
            invoiceIdBIFMap.computeIfAbsent(bif.getBillInvoiceId(), k -> new ArrayList<>()).add(bif);
            billIdList.add(bif.getBillId());
            bIFIds.add(bif.getId());
        }

        List<String> contractNos = insuranceBillMapper.getContractNosByBillIds(billIdList);
        List<ContractVo> contractVos = contractWrapperService.getByContractNoList(contractNos);
        // 只有外包是将代收代付转成服务费开票的,再红冲的时候需要将服务费拆成原样回退
        boolean b = contractVos.stream()
                .map(ContractVo::getContractType)
                .distinct()
                .allMatch(ContractType.OUTSOURCING_INSURANCE_CODE_LIST::contains);

        // 获取到账单核销开票信息表中信息
        List<BillCheckInvoice> billCheckInvoiceList = billCheckInvoiceMapper.getBillCheckInvoiceByIdList(billIdList);
        Map<Long, BillCheckInvoice> billCheckInvoiceIdAndOwnMap = billCheckInvoiceList.stream()
                .collect(Collectors.toMap(BillCheckInvoice::getId, Function.identity()));

        // 根据bIFIds获取billInvoiceDetail集合
        List<BillInvoiceDetail> billInvoiceDetailList = billInvoiceDetailMapper.getByInvoiceFromIdList(bIFIds);
        boolean hasNormalBill = CollectionUtils.isNotEmpty(billInvoiceDetailList);
        // 以bIFIds分组得到开票明细集合
        Map<Long, List<BillInvoiceDetail>> invoiceIdAndBillInvoiceDetailMap = Maps.newHashMap();
        // 以bIFIds分组得到开票明细id集合
        Map<Long, List<Long>> bIFIdAndBillDetailIdsMap = Maps.newHashMap();
        Map<Long, InsuranceBillDetail> insuranceBillDetailMap = Maps.newHashMap();
        if (hasNormalBill) {
            // 根据开票明细集合得到账单明细id集合
            List<Long> billDetaillIdList = Lists.newArrayList();
            for (BillInvoiceDetail detail : billInvoiceDetailList) {
                Long invoiceFromId = detail.getInvoiceFromId();
                invoiceIdAndBillInvoiceDetailMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(detail);

                Long billDetailId = detail.getBillDetailId();
                bIFIdAndBillDetailIdsMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(billDetailId);

                billDetaillIdList.add(billDetailId);
            }
            // 根据账单明细id集合获取账单明细集合
            List<InsuranceBillDetail> insuranceBillDetailList = insuranceBillDetailMapper.getInsuranceBillDetailByIdList(billDetaillIdList);
            // 根据账单明细id获取账单明细的map
            insuranceBillDetailMap = insuranceBillDetailList.stream()
                    .collect(Collectors.toMap(InsuranceBillDetail::getId, Function.identity()));
        }

        // 根据bIFIds查询一次性账单开票明细
        List<BillInvoiceDetailDisposable> bIDDList = billInvoiceDetailDisposableMapper.getByInvoiceFromIds(bIFIds);
        // 根据bIFId分组一次性账单开票明细集合
        Map<Long, List<BillInvoiceDetailDisposable>> bIDDMap = Maps.newHashMap();
        // 根据bIFId分组一次性账单开票id集合
        Map<Long, List<Long>> bIFIdAndBDDIdsMap = Maps.newHashMap();
        Map<Long, DisposableItemApproval> disposableItemApprovalMap = Maps.newHashMap();
        boolean hasBIDD = CollectionUtils.isNotEmpty(bIDDList);
        if (hasBIDD) {
            // 获取一次性账单明细id集合
            List<Long> bDDIdList = new ArrayList<>();
            for (BillInvoiceDetailDisposable disposable : bIDDList) {
                Long invoiceFromId = disposable.getInvoiceFromId();
                bIDDMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(disposable);

                Long disposableId = disposable.getDisposableId();
                bIFIdAndBDDIdsMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(disposableId);

                bDDIdList.add(disposableId);
            }

            // 获取一次性账单明细集合
            List<DisposableItemApproval> disposableItemApprovalByIdList = disposableItemApprovalMapper.getDisposableItemApprovalByIdList(bDDIdList);
            // 根据一次性账单明细id获取一次性账单明细map
            disposableItemApprovalMap = disposableItemApprovalByIdList.stream()
                    .collect(Collectors.toMap(DisposableItemApproval::getId, Function.identity()));
        }

        List<BillCheckInvoice> updateBillCheckInvoice = Lists.newArrayList();
        List<InsuranceBillDetail> updateInsuranceBillDetailList = Lists.newArrayList();
        List<DisposableItemApproval> disposableItemApprovalList = Lists.newArrayList();
        List<Integer> prodTypeCodes = Lists.newArrayList();
        List<Integer> disProdTypeCodes = Lists.newArrayList();
        List<RemoteInvoiceLogVo> remoteInvoiceLogVoList = remoteInvoiceLogMapper.queryByOrderNos(orderNos);
        List<Integer> invoiceTypes = remoteInvoiceLogVoList.stream()
                .map(RemoteInvoiceLogVo::getInvoiceDetailType)
                .collect(Collectors.toList());
        if (invoiceTypes.contains(InsuranceBillDetailDetailTypeEnum.SERVICE_CHARGE.getCode()) || b) {
            prodTypeCodes.addAll(ProductTypeEnum.RED_SERVICE_LIST);
            disProdTypeCodes.addAll(ExportBillDisposableApprovalEnum.IsDisposalTypeEnum.INVOICE_SERVICE_LIST);
        }
        if (invoiceTypes.contains(InsuranceBillDetailDetailTypeEnum.AGENT_BUSINESS.getCode()) || b) {
            prodTypeCodes.addAll(ProductTypeEnum.RED_AGENT_LIST);
            disProdTypeCodes.addAll(ExportBillDisposableApprovalEnum.IsDisposalTypeEnum.INVOICE_AGENT_LIST);
        }

        // 根据主表Id获取 --> BillInvoiceFrom
        List<BillInvoiceFrom> billInvoiceFromList = invoiceIdBIFMap.get(invoiceId);

        //   处理主表金额   因为一次可能有多个账单  循环billList 来判断表中是否有多条数据 如果有要把 主表中的 其他的调整金额进行相减;
        // 一个开票主表可能由多个账单组成
        for (BillInvoiceFrom billInvoiceFrom : billInvoiceFromList) {
            log.info("当前处理的开票对应的账单Id为: {}", billInvoiceFrom.getBillId());
            Long billId = billInvoiceFrom.getBillId();

            BigDecimal billCheckInvoiceAmt = BigDecimal.ZERO;

            Long billInvoiceFormId = billInvoiceFrom.getId();

            // 处理开票明细逻辑
            if (hasNormalBill && invoiceIdAndBillInvoiceDetailMap.containsKey(billInvoiceFormId)) {
                List<BillInvoiceDetail> billInvoiceDetails = invoiceIdAndBillInvoiceDetailMap.get(billInvoiceFormId);
                // 开票明细id和金额map
                Map<Long, BigDecimal> billDetailIdAndAmtMap = billInvoiceDetails.stream()
                        .collect(Collectors.toMap(BillInvoiceDetail::getBillDetailId, BillInvoiceDetail::getAmt, BigDecimal::add));

                // 获取账单明细id集合
                List<Long> billDetailIdListOfBifId = bIFIdAndBillDetailIdsMap.get(billInvoiceFormId);
                for (Long billDetailId : billDetailIdListOfBifId) {
                    // 获取账单明细
                    InsuranceBillDetail insuranceBillDetail = insuranceBillDetailMap.get(billDetailId);
                    if (!prodTypeCodes.contains(insuranceBillDetail.getProductType())) {
                        continue;
                    }
                    // 获取开票明细金额
                    BigDecimal invoiceDetailAmt = billDetailIdAndAmtMap.get(billDetailId);
                    if (BigDecimalUtil.equalsZero(invoiceDetailAmt)) {
                        continue;
                    }
                    // 将账单明细恢复到初始状态
                    updateInsuranceBillDetailList.add(InsuranceBillDetail.builder()
                            .id(billDetailId)
                            .invoicedAmt(insuranceBillDetail.getInvoicedAmt().subtract(invoiceDetailAmt))
                            .uninvoiceAmt(insuranceBillDetail.getUninvoiceAmt().add(invoiceDetailAmt))
//                            .updater(loginName)
                            .build());
                    billCheckInvoiceAmt = billCheckInvoiceAmt.add(invoiceDetailAmt);
                }
            }
            // 当前账单如果是一次性账单
            if (hasBIDD && bIDDMap.containsKey(billInvoiceFormId)) {

                List<BillInvoiceDetailDisposable> billInvoiceDetailDisposables = bIDDMap.get(billInvoiceFormId);

                Map<Long, BigDecimal> disposableIdAndAmtMap = billInvoiceDetailDisposables.stream()
                        .collect(Collectors.toMap(BillInvoiceDetailDisposable::getDisposableId, BillInvoiceDetailDisposable::getAmt, BigDecimal::add));

                List<Long> bDDIds = bIFIdAndBDDIdsMap.get(billInvoiceFormId);
                for (Long dIAId : bDDIds) {
                    DisposableItemApproval itemApproval = disposableItemApprovalMap.get(dIAId);
                    if (!disProdTypeCodes.contains(itemApproval.getProdType())) {
                        continue;
                    }
                    BigDecimal invoiceDetailAmt = disposableIdAndAmtMap.get(dIAId);
                    if (BigDecimalUtil.equalsZero(invoiceDetailAmt)) {
                        continue;
                    }
                    disposableItemApprovalList.add(DisposableItemApproval.builder()
                            .id(dIAId)
                            .invoicedAmt(itemApproval.getInvoicedAmt().subtract(invoiceDetailAmt))
                            .uninvoiceAmt(itemApproval.getUninvoiceAmt().add(invoiceDetailAmt))
                            .updater(loginName)
                            .build());
                    billCheckInvoiceAmt = billCheckInvoiceAmt.add(invoiceDetailAmt);
                }
            }

            // 更新billCheckInvoice开票相关的状态和金额
            BillCheckInvoice billCheckInvoice = billCheckInvoiceIdAndOwnMap.get(billId);
            BigDecimal invoiceAmt = billCheckInvoice.getInvoicedAmt().subtract(billCheckInvoiceAmt);
            // 如果开票金额减去后小于零,那么金额有问题
            if (BigDecimalUtil.lessZero(invoiceAmt)) {
                throw new RuntimeException("该次废除有误,请检查 billId:" + billId.toString());
            }

            billCheckInvoice.setInvoiceStatus(BigDecimalUtil.equalsZero(invoiceAmt) ?
                    InvoiceStatusEnum.UNINVOICE.getCode() : InvoiceStatusEnum.INVOICE_PART.getCode());
            billCheckInvoice.setInvoicedAmt(invoiceAmt);
            BigDecimal unInvoiceAmt = billCheckInvoice.getUninvoiceAmt().add(billCheckInvoiceAmt);
            billCheckInvoice.setUninvoiceAmt(unInvoiceAmt);
            /*BigDecimal appInvoiceAmt = billCheckInvoice.getInvoiceApprovingAmt().subtract(billCheckInvoiceAmt);
            billCheckInvoice.setInvoiceApprovingAmt(appInvoiceAmt);*/
//            billCheckInvoice.setUpdater(loginName);
            updateBillCheckInvoice.add(billCheckInvoice);
        }
        List<RemoteInvoiceLogVo> vos = remoteInvoiceLogMapper.queryByInvoiceIds(Lists.newArrayList(invoiceId));
        // 判断所有蓝票的红冲状态,如果都是已冲红,则修改主表为全部红冲,否则为部分红冲
        boolean allAbolished = vos.stream()
                .filter(vo -> Objects.equals(vo.getInvoiceFlag(), Integer.valueOf(InvoiceFlagEnum.BLUE.getCode())))
                .allMatch(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.ABOLISHED.getCode());
        ApprovalStatusEnum approvalStatusEnum = allAbolished ? ApprovalStatusEnum.HAS_BEEN_ALL_ABOLISHED : ApprovalStatusEnum.HAS_BEEN_PARTIALLY_ABOLITION;
        log.info("修改红冲状态:" + approvalStatusEnum.getMsg() + ":" + Arrays.toString(invoiceIdList));
        billInvoiceMapper.updateStatusByIds(invoiceIdList, loginName, approvalStatusEnum.getCode(), 2);
//        billInvoiceMapper.updateBillInvoicesToAbolish(invoiceIdList, loginName);
//        billInvoiceMapper.updateAdjustAmtById(updateBillInvoiceList);
        log.info("回退bci开票金额和状态:" + Arrays.toString(invoiceIdList));
        BillInvoiceVo billInvoiceVo = billInvoiceMapper.selectById(invoiceId);
        // 若当前开票已经重新生成账单,则红冲的时候不需要回退金额
        if (billInvoiceVo.getVirtualRedStatus().equals(VirtualRedStatusEnum.PREPARE.getCode()) || billInvoiceVo.getVirtualRedStatus().equals(VirtualRedStatusEnum.REGENERATE.getCode())) {
            if (billInvoiceVo.getBillFlag().equals(ValidFlagEnum.VALID.getCode())) {
                billCheckInvoiceMapper.batchUpdateBillCheckInvoiceByList(updateBillCheckInvoice);
            }
        } else {
            billCheckInvoiceMapper.batchUpdateBillCheckInvoiceByList(updateBillCheckInvoice);
        }
        if (!updateInsuranceBillDetailList.isEmpty()) {
            insuranceBillDetailMapper.batchUpdateByPrimaryKeySelective(updateInsuranceBillDetailList);
        }
        if (!disposableItemApprovalList.isEmpty()) {
            disposableItemApprovalMapper.batchUpdateInvoiceDisposableItemApproval(disposableItemApprovalList);
        }
    }

    @Override
    public String updatePassAbolish(Long[] invoiceIdList, String loginName) {
        List<RemoteInvoiceLogVo> vos = remoteInvoiceLogMapper.queryByBeans(Arrays.asList(invoiceIdList));
        if (CollectionUtils.isNotEmpty(vos)) {
            boolean hasApproving = vos.stream()
                    .filter(vo -> Objects.equals(vo.getInvoiceFlag(), Integer.valueOf(InvoiceFlagEnum.BLUE.getCode())))
                    .anyMatch(vo -> vo.getBlueStatus() == BlueInvoiceStatusEnum.ABOLISH_APPROVING.getCode());
            if (hasApproving) {
                return "含有需要一键红冲的发票,不能手动通过";
            }
        }
        log.info("开始处理通过红冲发票,开票id集合为:{}", Arrays.toString(invoiceIdList));
        List<BillInvoiceFrom> billInvoiceFromByInvoiceIdList = billInvoiceFromMapper.getByInvoiceIds(invoiceIdList);

        Map<Long, List<BillInvoiceFrom>> invoiceIdBIFMap = new HashMap<>();
        List<Long> billIdList = new ArrayList<>();
        List<Long> bIFIds = new ArrayList<>();
        for (BillInvoiceFrom bif : billInvoiceFromByInvoiceIdList) {
            invoiceIdBIFMap.computeIfAbsent(bif.getBillInvoiceId(), k -> new ArrayList<>()).add(bif);
            billIdList.add(bif.getBillId());
            bIFIds.add(bif.getId());
        }

        // 获取到账单核销开票信息表中信息
        List<BillCheckInvoice> billCheckInvoiceList = billCheckInvoiceMapper.getBillCheckInvoiceByIdList(billIdList);
        Map<Long, BillCheckInvoice> billCheckInvoiceIdAndOwnMap = billCheckInvoiceList.stream()
                .collect(Collectors.toMap(BillCheckInvoice::getId, Function.identity()));

        // 根据bIFIds获取billInvoiceDetail集合
        List<BillInvoiceDetail> billInvoiceDetailList = billInvoiceDetailMapper.getByInvoiceFromIdList(bIFIds);
        boolean hasNormalBill = CollectionUtils.isNotEmpty(billInvoiceDetailList);
        // 以bIFIds分组得到开票明细集合
        Map<Long, List<BillInvoiceDetail>> invoiceIdAndBillInvoiceDetailMap = Maps.newHashMap();
        // 以bIFIds分组得到开票明细id集合
        Map<Long, List<Long>> bIFIdAndBillDetailIdsMap = Maps.newHashMap();
        Map<Long, InsuranceBillDetail> insuranceBillDetailMap = Maps.newHashMap();
        if (hasNormalBill) {
            // 根据开票明细集合得到账单明细id集合
            List<Long> billDetaillIdList = Lists.newArrayList();
            for (BillInvoiceDetail detail : billInvoiceDetailList) {
                Long invoiceFromId = detail.getInvoiceFromId();
                invoiceIdAndBillInvoiceDetailMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(detail);

                Long billDetailId = detail.getBillDetailId();
                bIFIdAndBillDetailIdsMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(billDetailId);

                billDetaillIdList.add(billDetailId);
            }
            // 根据账单明细id集合获取账单明细集合
            List<InsuranceBillDetail> insuranceBillDetailList = insuranceBillDetailMapper.getInsuranceBillDetailByIdList(billDetaillIdList);
            // 根据账单明细id获取账单明细的map
            insuranceBillDetailMap = insuranceBillDetailList.stream()
                    .collect(Collectors.toMap(InsuranceBillDetail::getId, Function.identity()));
        }

        // 根据bIFIds查询一次性账单开票明细
        List<BillInvoiceDetailDisposable> bIDDList = billInvoiceDetailDisposableMapper.getByInvoiceFromIds(bIFIds);
        // 根据bIFId分组一次性账单开票明细集合
        Map<Long, List<BillInvoiceDetailDisposable>> bIDDMap = Maps.newHashMap();
        // 根据bIFId分组一次性账单开票id集合
        Map<Long, List<Long>> bIFIdAndBDDIdsMap = Maps.newHashMap();
        Map<Long, DisposableItemApproval> disposableItemApprovalMap = Maps.newHashMap();
        boolean hasBIDD = CollectionUtils.isNotEmpty(bIDDList);
        if (hasBIDD) {
            // 获取一次性账单明细id集合
            List<Long> bDDIdList = new ArrayList<>();
            for (BillInvoiceDetailDisposable disposable : bIDDList) {
                Long invoiceFromId = disposable.getInvoiceFromId();
                bIDDMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(disposable);

                Long disposableId = disposable.getDisposableId();
                bIFIdAndBDDIdsMap.computeIfAbsent(invoiceFromId, k -> new ArrayList<>()).add(disposableId);

                bDDIdList.add(disposableId);
            }

            // 获取一次性账单明细集合
            List<DisposableItemApproval> disposableItemApprovalByIdList = disposableItemApprovalMapper.getDisposableItemApprovalByIdList(bDDIdList);
            // 根据一次性账单明细id获取一次性账单明细map
            disposableItemApprovalMap = disposableItemApprovalByIdList.stream()
                    .collect(Collectors.toMap(DisposableItemApproval::getId, Function.identity()));
        }

        List<BillCheckInvoice> updateBillCheckInvoice = Lists.newArrayList();
        List<InsuranceBillDetail> updateInsuranceBillDetailList = Lists.newArrayList();
        List<DisposableItemApproval> disposableItemApprovalList = Lists.newArrayList();

        for (Long invoiceId : invoiceIdList) {
            log.info("当前处理的开票id为{}", invoiceId);
            // 根据主表Id获取 --> BillInvoiceFrom
            List<BillInvoiceFrom> billInvoiceFromList = invoiceIdBIFMap.get(invoiceId);

            Map<Long, BigDecimal> billIdAndSumAmtMap = new HashMap<>();
            // 详情表中的账单Id
            List<Long> billIdInBillInvoiceFormList = new ArrayList<>();
            List<Long> billInvoiceFormIdList = new ArrayList<>();

            for (BillInvoiceFrom bif : billInvoiceFromList) {
                Long billId = bif.getBillId();
                BigDecimal invoAmt = bif.getInvoAmt();
                billIdAndSumAmtMap.merge(billId, invoAmt, BigDecimal::add);

                billIdInBillInvoiceFormList.add(billId);
                billInvoiceFormIdList.add(bif.getId());
            }

            log.info("当前废除开票主表id为: {} 涉及到的账单为: {} 涉及到的开票详情表Id为 {}", invoiceId, billIdInBillInvoiceFormList, billInvoiceFormIdList);

            //   处理主表金额   因为一次可能有多个账单  循环billList 来判断表中是否有多条数据 如果有要把 主表中的 其他的调整金额进行相减;
            // 一个开票主表可能由多个账单组成
            for (BillInvoiceFrom billInvoiceFrom : billInvoiceFromList) {
                log.info("当前处理的开票对应的账单Id为: {}", billInvoiceFrom.getBillId());
                Long billId = billInvoiceFrom.getBillId();

                // 更新billCheckInvoice开票相关的状态和金额
                BillCheckInvoice billCheckInvoice = billCheckInvoiceIdAndOwnMap.get(billId);
                BigDecimal billInvoiceSumAmt = billIdAndSumAmtMap.get(billId);
                BigDecimal invoiceAmt = billCheckInvoice.getInvoicedAmt().subtract(billInvoiceSumAmt);
                // 如果开票金额减去后小于零,那么金额有问题
                if (BigDecimalUtil.lessZero(invoiceAmt)) {
                    throw new RuntimeException("该次废除有误,请检查 billId:" + billId.toString());
                }

                billCheckInvoice.setInvoiceStatus(BigDecimalUtil.equalsZero(invoiceAmt) ?
                        InvoiceStatusEnum.UNINVOICE.getCode() : InvoiceStatusEnum.INVOICE_PART.getCode());
                billCheckInvoice.setInvoicedAmt(invoiceAmt);
                BigDecimal unInvoiceAmt = billCheckInvoice.getUninvoiceAmt().add(billInvoiceSumAmt);
                billCheckInvoice.setUninvoiceAmt(unInvoiceAmt);
                billCheckInvoice.setUpdater(loginName);
                updateBillCheckInvoice.add(billCheckInvoice);

                Long billInvoiceFormId = billInvoiceFrom.getId();

                // 处理开票明细逻辑
                if (hasNormalBill && invoiceIdAndBillInvoiceDetailMap.containsKey(billInvoiceFormId)) {
                    List<BillInvoiceDetail> billInvoiceDetails = invoiceIdAndBillInvoiceDetailMap.get(billInvoiceFormId);
                    // 开票明细id和金额map
                    Map<Long, BigDecimal> billDetailIdAndAmtMap = billInvoiceDetails.stream()
                            .collect(Collectors.toMap(BillInvoiceDetail::getBillDetailId, BillInvoiceDetail::getAmt, BigDecimal::add));

                    // 获取账单明细id集合
                    List<Long> billDetailIdListOfBifId = bIFIdAndBillDetailIdsMap.get(billInvoiceFormId);
                    for (Long billDetailId : billDetailIdListOfBifId) {
                        // 获取账单明细
                        InsuranceBillDetail insuranceBillDetail = insuranceBillDetailMap.get(billDetailId);
                        // 获取开票明细金额
                        BigDecimal invoiceDetailAmt = billDetailIdAndAmtMap.get(billDetailId);
                        // 将账单明细恢复到初始状态
                        updateInsuranceBillDetailList.add(InsuranceBillDetail.builder()
                                .id(billDetailId)
                                .invoicedAmt(insuranceBillDetail.getInvoicedAmt().subtract(invoiceDetailAmt))
                                .uninvoiceAmt(insuranceBillDetail.getUninvoiceAmt().add(invoiceDetailAmt))
                                .updater(loginName)
                                .build());
                    }
                }
                // 当前账单如果是一次性账单
                if (hasBIDD && bIDDMap.containsKey(billInvoiceFormId)) {

                    List<BillInvoiceDetailDisposable> billInvoiceDetailDisposables = bIDDMap.get(billInvoiceFormId);

                    Map<Long, BigDecimal> disposableIdAndAmtMap = billInvoiceDetailDisposables.stream()
                            .collect(Collectors.toMap(BillInvoiceDetailDisposable::getDisposableId, BillInvoiceDetailDisposable::getAmt, BigDecimal::add));

                    List<Long> bDDIds = bIFIdAndBDDIdsMap.get(billInvoiceFormId);
                    for (Long dIAId : bDDIds) {
                        DisposableItemApproval itemApproval = disposableItemApprovalMap.get(dIAId);
                        BigDecimal invoiceDetailAmt = disposableIdAndAmtMap.get(dIAId);
                        disposableItemApprovalList.add(DisposableItemApproval.builder()
                                .id(dIAId)
                                .invoicedAmt(itemApproval.getInvoicedAmt().subtract(invoiceDetailAmt))
                                .uninvoiceAmt(itemApproval.getUninvoiceAmt().add(invoiceDetailAmt))
                                .updater(loginName)
                                .build());
                    }
                }
            }
        }
        billInvoiceMapper.updateBillInvoicesToAbolish(invoiceIdList, loginName);
//        billInvoiceMapper.updateAdjustAmtById(updateBillInvoiceList);
        billCheckInvoiceMapper.batchUpdateBillCheckInvoiceByList(updateBillCheckInvoice);
        if (!updateInsuranceBillDetailList.isEmpty()) {
            insuranceBillDetailMapper.batchUpdateByPrimaryKeySelective(updateInsuranceBillDetailList);
        }
        if (!disposableItemApprovalList.isEmpty()) {
            disposableItemApprovalMapper.batchUpdateInvoiceDisposableItemApproval(disposableItemApprovalList);
        }
        //(之前)废除时删除开票回填数据
        //(现在)废除名字改为红冲发票,且发票回填明细复制一份(发票状态变红冲发票,发票时间变当前时间)
        billInvoiceMapper.copyInvoiceDetail(Lists.newArrayList(invoiceIdList));
        return "红冲完成";
    }

    /*@Override
    public void updatePassAbolish(Long[] invoiceIdList, String loginName) {
        for (Long invoiceId : invoiceIdList) {
            //开票主表状态修改为 废除
            BillInvoice billInvoice = new BillInvoice();
            billInvoice.setId(invoiceId);
            billInvoice.setStatus(ApprovalStatusEnum.HAS_BEEN_ABOLISHED.getCode());
            billInvoice.setUpdater(loginName);
            billInvoice.setAbolishInvoiceDate(DateUtil.getDateString(new Date()));
            billInvoiceMapper.updateByPrimaryKeySelective(billInvoice);
            //根据主表Id获取 --> BillInvoiceFrom
            List<BillInvoiceFrom> billInvoiceFromList = billInvoiceFromMapper.getByInvoiceId(invoiceId);
            //每个账单具体开票了多少钱
            Map<Long, BigDecimal> billIdAndSumAmtMap = billInvoiceFromList.stream().collect(Collectors.toMap(BillInvoiceFrom::getBillId, BillInvoiceFrom::getInvoAmt, BigDecimal::add));
            // 详情表中的账单Id
            List<Long> billIdInBillInvoiceFormList = billInvoiceFromList.stream().map(BillInvoiceFrom::getBillId).collect(Collectors.toList());
            //开票详表id
            List<Long> billInvoiceFormIdList = billInvoiceFromList.stream().map(BillInvoiceFrom::getId).collect(Collectors.toList());
            logger.info("当前废除开票主表id为: {} 涉及到的账单为: {} 涉及到的开票详情表Id为 {}", invoiceId, billIdInBillInvoiceFormList.toString(), billInvoiceFormIdList.toString());
            // 根据billId获取到详情表中的数据List
            List<BillInvoiceFrom> billInvoiceFromsByBillIdList = billInvoiceFromMapper.getBillInvoiceFormByBillIdList(billIdInBillInvoiceFormList);
            // 同一个billId下 billInvoiceFrom
            Map<Long, List<BillInvoiceFrom>> billIdAndBillInvoiceFormMap = billInvoiceFromsByBillIdList.stream()
                    .filter(x -> x.getBillInvoiceId().equals(invoiceId))
                    .collect(Collectors.toMap(BillInvoiceFrom::getBillId, Lists::newArrayList, (List<BillInvoiceFrom> olds, List<BillInvoiceFrom> news) -> {
                        olds.addAll(news);
                        return olds;
                    }));
            Map<Long, List<Long>> billIdAndBilInvoiceIdListMap = billInvoiceFromsByBillIdList.stream()
                    .filter(x -> x.getBillInvoiceId().equals(invoiceId))
                    .collect(Collectors.toMap(BillInvoiceFrom::getBillId, item -> Lists.newArrayList(item.getBillInvoiceId()), (List<Long> old, List<Long> news) -> {
                        old.addAll(news);
                        return old;
                    }));

            //获取到账单核销开票信息表中信息
            List<BillCheckInvoice> billCheckInvoiceList = billCheckInvoiceMapper.getBillCheckInvoiceByIdList(billIdInBillInvoiceFormList);
            Map<Long, BillCheckInvoice> billCheckInvoiceIdAndOwnMap = billCheckInvoiceList.stream()
                    .collect(Collectors.toMap(BillCheckInvoice::getId, Function.identity()));
            //   处理主表金额   因为一次可能有多个账单  循环billList 来判断表中是否有多条数据 如果有要把 主表中的 其他的调整金额进行相减;
            List<BillCheckInvoice> updateBillCheckInvoice = Lists.newArrayList();
            for (Long billId : billIdInBillInvoiceFormList) {
                logger.info("当前处理的账单Id为: {}", billId);
                List<BillInvoiceFrom> billInvoiceFroms = billIdAndBillInvoiceFormMap.get(billId);
                BigDecimal invoAmtSum = BigDecimal.ZERO;
                for (BillInvoiceFrom billInvoiceFrom : billInvoiceFroms) {
                    //只有在该账单下,并且详情表中 bill_invoice_id 和当前的billInvoiceId相同的才能累加
                    BigDecimal invoAmt = billInvoiceFrom.getInvoAmt();
                    invoAmtSum = invoAmtSum.add(invoAmt);
                    logger.info("{} 账单下的 开票Id: {} 该次废除的金额为: {}", billId, invoiceId, invoAmtSum);
                }

                List<Long> billInvoiceIdList = billIdAndBilInvoiceIdListMap.get(billId);
                //删除当前的invoiceId  因为 已被废除,不需要进行减去金额
                for (int i = billInvoiceIdList.size() - 1; i >= 0; i--) {
                    if (billInvoiceIdList.get(i).equals(invoiceId)) {
                        billInvoiceIdList.remove(i);
                    }
                }
                if (CollectionUtils.isNotEmpty(billInvoiceIdList)) {
                    List<BillInvoice> updateBillInvoiceList = billInvoiceMapper.getBillInvoiceByIdList(billInvoiceIdList);
                    for (BillInvoice invoice : updateBillInvoiceList) {
                        invoice.setAdjustAmt(invoice.getAdjustAmt().subtract(invoAmtSum));
                        invoice.setUpdater(loginName);
                    }
                    Integer updateNum = billInvoiceMapper.updateAdjustAmtById(updateBillInvoiceList);
                }

                //处理账单核销开票信息中的状态和金额问题
                if (billCheckInvoiceIdAndOwnMap.containsKey(billId)) {
                    BillCheckInvoice billCheckInvoice = billCheckInvoiceIdAndOwnMap.get(billId);
                    BigDecimal unInvoiceAmt = billCheckInvoice.getUninvoiceAmt().add(billIdAndSumAmtMap.get(billId));
                    BigDecimal invoiceAmt = billCheckInvoice.getInvoicedAmt().subtract(billIdAndSumAmtMap.get(billId));
                    billCheckInvoice.setUninvoiceAmt(unInvoiceAmt);
                    billCheckInvoice.setInvoicedAmt(invoiceAmt);
                    billCheckInvoice.setUpdater(loginName);
                    //如果开票金额减去后小于零,那么金额有问题
                    if (invoiceAmt.compareTo(BigDecimal.ZERO) < 0) {
                        throw new RuntimeException("该次废除有误,请检查 billId:" + billId.toString());
                    }
                    if (invoiceAmt.compareTo(BigDecimal.ZERO) == 0) {
                        billCheckInvoice.setInvoiceStatus(InvoiceStatusEnum.UNINVOICE.getCode());
                    } else {
                        billCheckInvoice.setInvoiceStatus(InvoiceStatusEnum.INVOICE_PART.getCode());
                    }
                    updateBillCheckInvoice.add(billCheckInvoice);
                }
            }
            Integer billCheckInvoiceUpdateNum = billCheckInvoiceMapper.batchUpdateBillCheckInvoiceByList(updateBillCheckInvoice);
            //            billInvoiceFromMapper.deleteByBillInvoiceFormIdList(billInvoiceFormIdList, loginName);

            //            处理账单明细表
            for (Long billInvoiceFormId : billInvoiceFormIdList) {
                List<BillInvoiceDetail> byInvoiceFromId = billInvoiceDetailMapper.getByInvoiceFromId(billInvoiceFormId);

                Map<Long, BigDecimal> billDetailIdAndAmtMap = byInvoiceFromId.stream()
                        .collect(Collectors.toMap(BillInvoiceDetail::getBillDetailId, BillInvoiceDetail::getAmt));

                List<Long> billDetaillIdList = byInvoiceFromId.stream()
                        .map(BillInvoiceDetail::getBillDetailId)
                        .collect(Collectors.toList());

                if (!billDetaillIdList.isEmpty()) {
                    List<InsuranceBillDetail> insuranceBillDetailList = insuranceBillDetailMapper.getInsuranceBillDetailByIdList(billDetaillIdList);
                    for (InsuranceBillDetail insuranceBillDetail : insuranceBillDetailList) {
                        InsuranceBillDetail insuranceBillDetailInsert = new InsuranceBillDetail();
                        insuranceBillDetailInsert.setId(insuranceBillDetail.getId());
                        insuranceBillDetailInsert.setInvoicedAmt(insuranceBillDetail.getInvoicedAmt().subtract(billDetailIdAndAmtMap.get(insuranceBillDetail.getId())));
                        insuranceBillDetailInsert.setUninvoiceAmt(insuranceBillDetail.getUninvoiceAmt().add(billDetailIdAndAmtMap.get(insuranceBillDetail.getId())));
                        insuranceBillDetailInsert.setUpdater(loginName);
                        int i = insuranceBillDetailMapper.updateByPrimaryKeySelective(insuranceBillDetailInsert);
                    }
                }
                //                处理一次性项目审核信息中的金额
                List<BillInvoiceDetailDisposable> billInvoiceDetailDisposableList = billInvoiceDetailDisposableMapper.getByInvoiceFromId(billInvoiceFormId);

                Map<Long, BigDecimal> disposableIdAndAmtMap = billInvoiceDetailDisposableList.stream()
                        .collect(Collectors.toMap(BillInvoiceDetailDisposable::getDisposableId, BillInvoiceDetailDisposable::getAmt, BigDecimal::add));

                List<Long> disposableItemApprovalIdInBillInvoiceDetailDisposable = billInvoiceDetailDisposableList.stream()
                        .map(BillInvoiceDetailDisposable::getDisposableId)
                        .collect(Collectors.toList());

                List<DisposableItemApproval> disposableItemApprovalList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(disposableItemApprovalIdInBillInvoiceDetailDisposable)) {
                    disposableItemApprovalList = disposableItemApprovalMapper.getDisposableItemApprovalByIdList(disposableItemApprovalIdInBillInvoiceDetailDisposable);
                }
                if (CollectionUtils.isNotEmpty(disposableItemApprovalList)) {
                    for (DisposableItemApproval itemApproval : disposableItemApprovalList) {
                        if (disposableIdAndAmtMap.containsKey(itemApproval.getId())) {
                            itemApproval.setUninvoiceAmt(itemApproval.getUninvoiceAmt().add(disposableIdAndAmtMap.get(itemApproval.getId())));
                            itemApproval.setInvoicedAmt(itemApproval.getInvoicedAmt().subtract(disposableIdAndAmtMap.get(itemApproval.getId())));
                            itemApproval.setUpdater(loginName);
                        }
                    }
                    Integer updateNum = disposableItemApprovalMapper.batchUpdateInvoiceDisposableItemApproval(disposableItemApprovalList);
                }
            }
        }
        //(之前)废除时删除开票回填数据
        //(现在)废除名字改为红冲发票,且发票回填明细复制一份(发票状态变红冲发票,发票时间变当前时间)
        billInvoiceMapper.copyInvoiceDetail(Lists.newArrayList(invoiceIdList));
    }*/

    @Override
    public String updatePrepareRedInvoice(Long invoiceId) {
        List<BillInvoiceFrom> billInvoiceFroms = billInvoiceFromMapper.getByInvoiceId(invoiceId);
        for (BillInvoiceFrom billInvoiceFrom : billInvoiceFroms) {
            InsuranceBillVo insuranceBillVo = insuranceBillMapper.selectInsuranceBillAndBillCheckInvoiceByBillId(billInvoiceFrom.getBillId());
            Integer billMonth = insuranceBillVo.getBillMonth();
            // billMonth为YYYYMM格式 判断是否为当月
            String currentYearMonth = LocalDate.now().toString().substring(0, 7).replace("-", "");
            // 将 billMonth 转换为字符串进行比较
            if (!String.valueOf(billMonth).equals(currentYearMonth)) {
                return "该开票中有账单不是当月,不能准备虚拟废除";
            }
            // 只需要校验账单状态不是部分开票即可
            if (insuranceBillVo.getInvoiceStatus() == InvoiceStatusEnum.INVOICE_PART.getCode()
                    // 账单金额不等于账单详情表中已开票金额
            || !BigDecimalUtil.equalsVal(insuranceBillVo.getReceiveAmt(), billInvoiceFrom.getInvoAmt())) {
                return "该开票中有账单不是全部开票,不能准备虚拟废除";
            }
        }
        billInvoiceMapper.updateVirRedInvoiceStatus(invoiceId, VirtualRedStatusEnum.PREPARE.getCode());
        billInvoiceMapper.updateInvoiceGenFlag(invoiceId, ValidFlagEnum.INVALID.getCode());
        return "准备虚拟红冲成功";
    }

    @Override
    public String cancelPrepareRedInvoice(Long invoiceId) {
        List<InsuranceBillVo> insuranceBillVos = insuranceBillMapper.getByInvoiceId(invoiceId);
        if (CollectionUtils.isEmpty(insuranceBillVos)) {
            return "该开票中没有账单";
        }
        for (InsuranceBillVo insuranceBillVo : insuranceBillVos) {
            if (insuranceBillVo.getStatus().equals(1)) {
                return "该开票中有账单未锁定不能取消准备虚拟红冲";
            }
        }
        billInvoiceMapper.updateVirRedInvoiceStatus(invoiceId, VirtualRedStatusEnum.NON_REV.getCode());
        return "取消虚拟红冲成功";
    }

    @Override
    public String confirmRedInvoice(Long invoiceId, String loginName) {
        String msg = billInvoiceApprovalService.updateInvoiceDataComparison(invoiceId, loginName);
        billInvoiceMapper.updateByPrimaryKeySelective(BillInvoice.builder()
                .id(invoiceId)
                .virtualRedStatus(StringUtils.isBlank(msg) ? VirtualRedStatusEnum.REV.getCode() : VirtualRedStatusEnum.REGENERATE.getCode())
                .build());
        if (StringUtils.isBlank(msg)) {
            msg = "虚拟红冲成功";
        }
        return msg;
    }

    @Override
    public List<InvoiceBillVo> getInvoiceBillVoByBillIdList(List<Long> billIdList) {
        return billInvoiceFromMapper.getInvoiceBillVoByBillIdList(billIdList);
    }

    @Override
    public BillCheckVo getAdjustAmtByInvoiceId(Long invoiceId) {
        return billInvoiceMapper.getAdjustAmtByInvoiceId(invoiceId);
    }

    @Override
    public List<BillCheckVo> getAdjustAmtByInvoiceId(List<Long> invoiceIdList) {
        return billInvoiceMapper.getAdjustAmtByInvoiceIdList(invoiceIdList);
    }

    // 赋值BillInvoice
    private void setBillInvoice(InvoiceSaveParamVo invoiceSaveParamVo, String loginName, String orgCode, BillInvoice billInvoice) {
        BeanUtils.copyProperties(invoiceSaveParamVo, billInvoice);
        billInvoice.setInvoiceAmt(invoiceSaveParamVo.getBillInvoiceList().stream().map(InvoiceFromSaveParamVo::getInvoAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        billInvoice.setCreateTime(new Date());
        billInvoice.setCreator(loginName);
        billInvoice.setPrjCs(loginName);
        billInvoice.setUpdater(loginName);
        billInvoice.setUpdateTime(new Date());
        billInvoice.setFinanceOrgCode(orgCode);
        billInvoice.setStatus(1);
    }

    // 赋值BillInvoiceFrom
    private void setBillInvoiceFrom(InvoiceFromSaveParamVo invoiceFromSaveParamVo, BillInvoiceFrom billInvoiceFrom, String loginName, Long billInvoiceId) {
        BeanUtils.copyProperties(invoiceFromSaveParamVo, billInvoiceFrom);
        billInvoiceFrom.setCreateTime(new Date());
        billInvoiceFrom.setCreator(loginName);
        billInvoiceFrom.setUpdater(loginName);
        billInvoiceFrom.setUpdateTime(new Date());
        billInvoiceFrom.setBillInvoiceId(billInvoiceId);
    }

    // 赋值BillInvoiceDetail
    private void setBillInvoiceDetail(ServiceAndCollectionSaveParamVo serviceAndCollectionSaveParamVo, BillInvoiceDetail billInvoiceDetail, String loginName, Long invoiceFromId) {
        BeanUtils.copyProperties(serviceAndCollectionSaveParamVo, billInvoiceDetail);
        billInvoiceDetail.setCreateTime(new Date());
        billInvoiceDetail.setCreator(loginName);
        billInvoiceDetail.setUpdater(loginName);
        billInvoiceDetail.setUpdateTime(new Date());
        billInvoiceDetail.setInvoiceFromId(invoiceFromId);
    }

    // 赋值BillInvoiceDetailDisposable
    private void setBillInvoiceDetailDisposable(DisposableSaveParamVo disposableSaveParamVo, BillInvoiceDetailDisposable billInvoiceDetailDisposable, String loginName, Long invoiceFromId) {
        BeanUtils.copyProperties(disposableSaveParamVo, billInvoiceDetailDisposable);
        billInvoiceDetailDisposable.setCreateTime(new Date());
        billInvoiceDetailDisposable.setCreator(loginName);
        billInvoiceDetailDisposable.setUpdater(loginName);
        billInvoiceDetailDisposable.setUpdateTime(new Date());
        billInvoiceDetailDisposable.setInvoiceFromId(invoiceFromId);
    }


    /**
     * 赋值InsuranceBill
     *
     * @param insuranceBillVo        账单信息vo表
     * @param insuranceBill          账单信息表
     * @param invoiceFromSaveParamVo 开票从表
     * @param loginName              当初操作人
     * @param invoiceAmt             开票总金额
     *                               x
     */
    private void setInsuranceBill(InsuranceBillVo insuranceBillVo,
                                  InsuranceBill insuranceBill,
                                  BillCheckInvoice billCheckInvoice,
                                  InvoiceFromSaveParamVo invoiceFromSaveParamVo,
                                  String loginName,
                                  BigDecimal invoiceAmt) {
//        BeanUtils.copyProperties(insuranceBillVo, insuranceBill);
        BeanUtils.copyProperties(insuranceBillVo, billCheckInvoice);
        // 判断 去除空指针
        // 开票附加金额
        BigDecimal addAmt = invoiceFromSaveParamVo.getAddAmt() == null ? BigDecimal.ZERO : invoiceFromSaveParamVo.getAddAmt();
        // 开票一次性金额
        BigDecimal invoiceDisposable = invoiceFromSaveParamVo.getInvoiceDisposable() == null ? BigDecimal.ZERO : invoiceFromSaveParamVo.getInvoiceDisposable();
        // 账单未开票金额
        BigDecimal unInvoiceAmt = billCheckInvoice.getUninvoiceAmt() == null ? BigDecimal.ZERO : billCheckInvoice.getUninvoiceAmt();
//        // 账单开票审批中金额
        BigDecimal invoiceApprovingAmt = billCheckInvoice.getInvoiceApprovingAmt() == null ? BigDecimal.ZERO : billCheckInvoice.getInvoiceApprovingAmt();

        // 未开票金额 + 附加金额 <= 开票金额，则全部开票，否则部分开票
        if (invoiceAmt.compareTo(unInvoiceAmt.add(addAmt)) < 0) {
            // 加上审批中金额
            billCheckInvoice.setInvoiceApprovingAmt(invoiceApprovingAmt.add(invoiceAmt));
            // 减去未开票金额
            billCheckInvoice.setUninvoiceAmt(unInvoiceAmt.subtract(invoiceAmt).add(addAmt));
        } else {
            // 审批中金额 + 未开票金额
            billCheckInvoice.setInvoiceApprovingAmt(invoiceApprovingAmt.add(unInvoiceAmt));

            // 未开票金额设置为零
            billCheckInvoice.setUninvoiceAmt(BigDecimal.ZERO);
        }
//        insuranceBill.setUpdater(loginName);
//        insuranceBill.setUpdateTime(new Date());
        billCheckInvoice.setUpdater(loginName);
    }

    // 赋值赋值InsuranceBillDetail
    private void setInsuranceBillDetail(InsuranceBillDetail insuranceBillDetail, ServiceAndCollectionSaveParamVo serviceAndCollectionSaveParamVo, String loginName) {
        insuranceBillDetail.setUpdater(loginName);
        insuranceBillDetail.setUpdateTime(new Date());
        // 未开票金额减去此次开票金额
        insuranceBillDetail.setUninvoiceAmt(insuranceBillDetail.getUninvoiceAmt().subtract(serviceAndCollectionSaveParamVo.getAmt()));
    }

    // 赋值DisposableItemApproval
    private void setDisposableItemApproval(DisposableItemApproval disposableItemApproval, DisposableSaveParamVo disposableSaveParamVo, String loginName) {
        // 一次性未开票金额
        BigDecimal unInvoiceAmt = disposableItemApproval.getUninvoiceAmt() == null ? BigDecimal.ZERO : disposableItemApproval.getUninvoiceAmt();
        // 一次性此次开票金额
        BigDecimal amt = disposableSaveParamVo.getAmt() == null ? BigDecimal.ZERO : disposableSaveParamVo.getAmt();

/*        if (unInvoiceAmt.compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("一次性未开票金额为0！");
        }*/
        disposableItemApproval.setUninvoiceAmt(unInvoiceAmt.subtract(amt));
        disposableItemApproval.setUpdater(loginName);
        disposableItemApproval.setUpdateTime(new Date());
    }
}
