package com.reon.hr.sp.service.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.reon.hr.api.common.RegionAndCityDto;
import com.reon.hr.api.vo.JsonResult;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.OrgPositionVo;
import com.reon.hr.api.vo.sys.OrgUserVo;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.sp.entity.sys.CommonUser;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-06-21
 */
public interface ICommonUserService extends IService<CommonUser> {


//    Page<CommonUser> selectUserListPage(CommonUserVo paramVo, Integer page, Integer limit);

    Page<CommonUserVo> selectUserVoListPage(CommonUserVo paramVo, Integer page, Integer limit);


    /**
     * 管理员修改或新增用户
     */
    boolean saveUser(CommonUserVo userVo, String operator);

    /**
     * 重新加载用户登录名与用户姓名缓存信息
     */
    public Map<String, String> reloadAllCommonUser();

    Map<String, String> selectAllUserEmail();

    /**
     * 获取用户登录名用户姓名Map
     *
     * @return 用户登录名用户姓名Map
     */
    public Map<String, String> getAllUserMap();


    Map<String, String> getAllUserMapRealTime();
    /**
     * 用户自行重置密码(用户自己修改)
     */
    JsonResult<Object> changeUserPwd(long userId, String oldPassword, String newPassword);

    /**
     * 根据条件查询用户
     *
     * @param queryMap
     * @return
     */
    CommonUser selectUserByParams(Map<String, Object> queryMap);

    /**
     * 用户登录
     */
    JsonResult<Object> doUserLogin(String loginName, String password, String lang, String ip);

/*    List<CommonUser> selectByUserOrg(Map<String, Object> queryMap);*/

    Page<OrgUserVo> findOrgUserList(String code, int startPage, int pageNum);

    int deleteUser(List<Long> ids);

    boolean updateUserStatus(Long id, String status);


    /**
     * 根据部门来获取销售部人员
     */
    List<OrgUserVo> searchSaleByOrgCode();

//    List<CommonUserVo> findUserByLeaderAndPostion(Long leader, String position);

    Page<OrgUserVo> getOrgUserInfo(int page, int limit, String param);

    CommonUserVo findUserByLoginName(String userName);

    /**
     * 根据userNames批量查询CommonUser
     *
     * @param userNames 用户名列表
     * @return CommonUser实体对象
     */
    List<CommonUserVo> findUsersByLoginNames(List<String> userNames);

//    Page<CommonUserVo> findByPositionPage(Integer page, Integer limit, String name);

//    Page<CommonUserVo> findByDistComPage(Integer page, Integer limit, String name);

    /**
     * 获取指定机构的相同城市指定机构类型指定职位的人员列表
     *
     * @param orgcode      指定机构
     * @param orgType      指定机构类型
     * @param positionCode 指定职位
     * @return
     */
    List<CommonUserVo> findUserBySameCityOrgAndPosition(String orgcode, String orgType, String positionCode);

    List<CommonUserVo> findLoginNameBySameCityOrgTypeAndPositionCode(Integer owerCity,String orgCode,String orgType, String positionCode);

//    List<CommonUserVo> findUserByOrgAndPosition(String orgcode, String positionCode);

//    CommonUserVo getTheHeadOfTheDepartmentAccordingPosition(String orgCode, Long id);

    CommonUserVo selectUserById(Long id);

    /**
     * 获取用户的领导List和组织机构岗位List
     *
     * @param userId
     * @return
     */
    CommonUserVo getLeaderListAndOrgInfo(Long userId);

    /**
     * 根据职位和机构查找登录名
     * @param orgCode
     * @param posCode
     * @return
             */
    String findLoginNameByPosCodeAndOrgCode(String orgCode, String posCode);

    List<OrgPositionVo> findLoginNameByPosCodeAndOrgCode(String loginName);

    List<CommonUserVo> findLoginNameByPosCodeAndOrgCodeList(String orgCode, String posCode);


    /**
     *  根据岗位和登录名或者机构数据
     * @param posCode
     * @param loginName
     * @return
     */
    UserOrgPosVo findOrgByPosCodeAndLoginName(String posCode, String orgCode, String loginName);

    /**
     * 根据登录名和城市编号查找下级主管
     * @param loginName
     * @param orgCode
     * @return
     */
    List<UserOrgPosVo> findSubordinateByLoginNameAndCity(String loginName, String orgCode,String positionCode);

    /**
     * 根据ParentId查找下级专员
     * @param directorList
     * @return
     */
    List<UserOrgPosVo> findSubordinateByParentIdList(List<Long> directorList);

    /**
     * 根据机构code,岗位code查找上级登录名
     * @param posCode
     * @param orgCode
     * @return
     */
    String findLeaderLoginNameByPosCodeAndOrgCode(String posCode, String orgCode);

    String getRevMgrByCityCode(String cityCode);

    CommonUserVo getProjectDistrictManagerBySubarea(String subarea);

   List<CommonUserVo> findOrgCodeByowerCity(Integer owerCity);

    Page<CommonUserVo> getUserByOrgCode(String orgCode, Integer page, Integer limit, String custName);

	List<OrgUserVo> getAllOrganization();

    List<OrgUserVo> getAllReceivingMan();

    List<Map<String, String>> getAllUserName();

    Page<OrgUserVo> getProjectServiceUserAndOrgNameByUserName(Integer page, Integer limit,String userName);

    List<CommonUserVo> getByLoginNameLIst(List<String> loginNames);

    Page<CommonUserVo> getAllUserForTableSelect(Integer page, Integer limit, CommonUserVo userVo);

    List<CommonUserVo> searchSellerByLoginName(String loginName);

    List<OrgPositionVo> searchOrgCodeByLoginName(String loginName);

    List<OrgUserVo> findCommercialInsuranceCustomerService();

	CommonUserVo getUserByLogingName(String loginName);

    Integer updateCommonUser(CommonUserVo commonUserVo);

    List<String> findLoginNameSameCityByPosCodeAndOrgCode(String orgCode, String posCode);

    List<String> findLoginNameSameAreaByPosCodeAndOrgCode(String orgCode, String posCode);

    List<CommonUserVo> getServiceListByOrgCodeAndPosCode(String orgCode, String positionCode);

    /**
     * 获取当前登录人大区的项目客服主管
     */
    String getSupervisorByArea(long id);


    List<OrgUserVo> findSellerList();

    /**
     * 获得所有接单客服
     * @return
     */
    Page<OrgUserVo> getReceivingManInfo(Integer page, Integer limit, CommonUserVo userVo);

	Map<String, String> getAllUserMapIgnoreDelFlag();

    List<CommonUserVo> getDisSupManList();

    List<UserOrgPosVo> getLoginUserPosType();

    List<String> getUrlByLoginName(String loginName);

    boolean updateUserBymain(CommonUserVo dsUserVo, String loginName);

    List<CommonUserVo> getAllDataByNameAndMobileList(List<Map<String, String>> queryMap);

    List<String> getOrgCodeByLoginName(String loginName);

    Map<String, RegionAndCityDto> getRegionAndCityMap();

}
