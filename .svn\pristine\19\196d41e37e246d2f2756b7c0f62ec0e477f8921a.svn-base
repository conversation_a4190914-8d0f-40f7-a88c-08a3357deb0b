package com.reon.hr.api.customer.dto.employee;

import com.reon.hr.api.customer.anno.Excel;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IPersonOrderQueryWrapperService;
import com.reon.hr.api.customer.enums.DimissionReasonTypeEnum;
import com.reon.hr.api.customer.enums.ExportPersonOrderEnum;
import com.reon.hr.api.customer.enums.PersonOrderEnum;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderStatus;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class PersonOrderQueryDto implements Serializable {

    /**
     * 唯一号
     */
    @Excel(name = "唯一号",width = 22)
    private String employeeNo;

    /**
     * 雇员名称
     */
    @Excel(name = "雇员名称",width = 10)
    private String employeeName;

    /**
     * 证件类型
     */

    private String certType;
    @Excel(name = "证件类型",width = 10)
    private String documentType; //格式化证件类型
    /**
     * 证件号码
     */
    @Excel(name = "证件号码",width = 22)
    private String certNo;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号",width = 22)
    private String orderNo;

    private String categoryCode;
    @Excel(name = "人员分类",width = 10)
    private String categoryCodeName;

    /**
     * 年龄
     */
    @Excel(name = "年龄",width = 6)
    private String age;


    /**
     * 退休日期
     * */
    @Excel(name = "性别",width = 10)
    private String sex;


    /**
     * 退休日期
     * */
    @Excel(name = "退休日期",width = 10)
    private String retireDate;


    /**
     * 社保工资
     */
    @Excel(name = "社保工资",width = 10)
    private BigDecimal socailSalary=BigDecimal.ZERO;

    /**
     * 公积金工资
     */
    @Excel(name = "公积金工资",width = 10)
    private BigDecimal acctSalary=BigDecimal.ZERO;


    /**
     * 户口性质
     */
    @Excel(name = "户口性质",width = 15)
    private String household;
    /**
     * 客户编号
     */
    @Excel(name = "客户编号",width = 22)
    private String custNo;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称",width = 26)
    private String custName;

    /**
     * 劳动合同是否上传
     */
    private Integer laborFileStatus;
    @Excel(name = "劳动合同是否上传",width = 14)
    private String laborFileStatusStr;//格式化劳动合同是否上传

    private Integer proxyFileStatus;
    @Excel(name = "代理合同是否上传",width = 14)
    private String proxyFileStatusStr;

    private Integer stopFileStatus;
    @Excel(name = "离职合同是否上传",width = 14)
    private String stopFileStatusStr;
    /**
     * 身份上传标识
     */
    @Excel(name = "身份证是否上传",width = 14)
    private String idStatus;


    /**
     * 人员分布
     */
    private String distributeType;
    @Excel(name = "人员分布",width = 8)
    private String personnelDistribution; //格式化人员分布

    /**
     * 合同类型
     */
    @Excel(name = "合同类型",width = 10)
    private String contractType;

    /**
     * 派单类型
     */
    private String dispatchType;
    @Excel(name = "派单类型",width = 14)
    private String singleType;//格式化派单类型
    /**
     * 手机
     */
    @Excel(name = "手机",width = 14)
    private String mobilePhone;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话",width = 14)
    private String tel;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号",width = 20)
    private String contractNo;

    /**
     * 小合同编号
     */
    @Excel(name = "小合同编号",width = 20)
    private String contractAreaNo;

    /**
     * 小合同名称
     */
    @Excel(name = "小合同名称",width = 24)
    private String contractAreaName;

    /**
     * 城市编码
     */
    @Excel(name = "城市编码",width = 10)
    private int cityCode;

    /**
     * 城市名称
     */
    @Excel(name = "城市名称",width = 10)
    private String cityName;
    /**
     * 派单方
     */

    private String distComName;
    @Excel(name = "派单方")
    private String sendOne;//格式化派单方

    /**
     * 派单方客服
     */
    @Excel(name = "派单方客服",width = 14)
    private String distComMan;

    /**
     * 接单方
     */
    @Excel(name = "接单方",width = 24)
    private String receivingName;


    /**
     * 接单方
     */
    @Excel(name = "自有/供应商",width = 24)
    private String recceivingTypeStr;


    /**
     * 接单方客服
     */
    @Excel(name = "接单方客服",width = 14)
    private String receivingMan;

    /**
     * 供应商客服
     */
    private String supplierMan;

    /**
     * 入职时间
     */
    private Date entryDate;
    @Excel(name = "入职时间",width = 20)
    private String dateOfEntry;//格式化入职日期


    /**
     * 申报入职日期
     */
    private Date applyEntryDate;
    @Excel(name = "申报入职日期",width = 20)
    private String applicationDate;//格式化申请入职日期

    private Date ehrApplyEntryTime;
    @Excel(name = "企业端申请入职日期",width = 25)
    private String ehrApplyEntryTimeStr;

    /**
     * 增员状态
     */
    private String orderStatus;
    @Excel(name = "增员状态",width = 8)
    private String memberState;//格式化增员状态
    /**
     * 入职备注
     */
    @Excel(name = "入职备注",width = 12)
    private String entryRemark;

    /**
     * 入离职状态
     */
    private String eedStatus;
    @Excel(name = "入离职状态",width = 14)
    private String InOutStatus;//格式化入离职状态

    /**
     * 变更状态
     */
    private String chgState;
    @Excel(name = "变更状态",width = 8)
    private String theStatus;//格式化变更状态

    /**
     * 申报离职日期
     */
    private Date applyQuitDate;
    @Excel(name = "申报离职日期",width = 20)
    private String dateOfDeclaration;//格式化申报离职日期

    private Date ehrApplyDimissionDate;
    @Excel(name = "企业端申报离职日期",width = 25)
    private String ehrApplyDimissionDateStr;

    /**
     * 离职日期
     */
    private Date quitDate;
    @Excel(name = "离职日期",width = 20)
    private String termDate;//格式化离职日期

    /**
     * 申报离职人
     */
    @Excel(name = "申报离职人",width = 12)
    private String applyDimissionMan;

    /**
     * 离职原因
     */
    private String dimissionReason;
    @Excel(name = "离职原因",width = 12)
    private String leavingReason;//格式化离职原因


    /**
     * 入职过程
     */
    @Excel(name = "增员确认过程",width = 20)
    private String onboardingProcess;
    /**
     * 增员确认过程
     */
    private String orderConfirmProcess;
    /**
     * 离职过程
     */
    @Excel(name = "减员确认过程",width = 20)
    private String resignationProcess;
    /**
     * 减员确认过程
     */
    private String reduceConfirmProcess;


    /**
     * 身份证是否上传
     */
    private String idCardUpload;

    /**
     * 收费起始月
     */
    @Excel(name = "收费起始月",width = 10)
    private int totalFeeTime;

    /**
     * 收费截止月
     */
    @Excel(name = "收费截止月",width = 10)
    private int expiredMonth;

    /**
     * 是否外呼
     */
    @Excel(name = "是否外呼",width = 8)
    private String callFlag;

    /**
     * 是否单立户
     */
    @Excel(name = "是否单立户",width = 10)
    private String accountFlag;
    /**
     * 选中单立户
     */
    @Excel(name = "选中单立户",width = 10)
    private String sinAccName;
    /**
     * 减员状态
     */
    private String reduceState;


    /**
     * 变更确认过程
     */
    @Excel(name = "变更确认过程",width = 50)
    private String chgConfirmProcess;


    /**
     * 银行卡是否上传
     */
    private String bankCardUpload;

    /**
     * 申请入职人
     */
    private String applyEntryMan;
    /**
     * 订单创建时间
     */
    private String createTime;


    /**
     * 变更确认过程
     */
    private String changeConfirmationProcess;

    /**
     *减员原因
     */
    private Integer reduceReason;

    /**
     * 是否单工伤
     * @return
     */
    private Integer injuryFlag;
    @Excel(name = "是否单工伤",width = 50)
    private String injuryFlagStr;

    //    private String idCardIsUploaded;//格式化身份证是否上传
    //    private String bankCardCanBeUploaded;//格式化银行卡是否上传


    public String getDateOfEntry() {
        if (this.entryDate != null) {
            this.dateOfEntry = DateUtil.getString (this.entryDate, DateUtil.DATE_FORMAT_LONG);
        }
        return dateOfEntry;
    }

    public String getApplicationDate() {
        if (this.applyEntryDate != null) {
            this.applicationDate = DateUtil.getString (this.applyEntryDate, DateUtil.DATE_FORMAT_LONG);
        }
        return applicationDate;
    }

    public String getDateOfDeclaration() {
        if (this.applyQuitDate != null) {
            this.dateOfDeclaration = DateUtil.getString (this.applyQuitDate, DateUtil.DATE_FORMAT_LONG);
        }
        return dateOfDeclaration;
    }

    public String getTermDate() {
        if (this.quitDate != null) {
            this.termDate = DateUtil.getString (this.quitDate, DateUtil.DATE_FORMAT_LONG);
        }
        return termDate;
    }

    public String getAge() {
        if (this.certNo != null) {
            try {
                Date birthDayByIdentityCard = DateUtil.getBirthDayByIdentityCard (this.certNo);
                int age = DateUtil.getAge (birthDayByIdentityCard);
                if (age != -1) {
                    this.age = String.valueOf (DateUtil.getAge (birthDayByIdentityCard));
                }
            } catch (Exception e) {
                e.printStackTrace ();
            }
        }
        return age;
    }

    public String getDocumentType() {
        if (this.certType != null) {
            this.documentType = ExportPersonOrderEnum.IsDocumentTypeEnum.getName (Integer.valueOf (this.certType));
        }
        return documentType;
    }

    public String getPersonnelDistribution() {
        if (this.distributeType != null) {
            this.personnelDistribution = ExportPersonOrderEnum.IsPersonnelDistributionTypeEnum.getName (Integer.valueOf (this.distributeType));
        }
        return personnelDistribution;
    }

    public String getSingleType() {
        if (this.dispatchType != null) {
            this.singleType = ExportPersonOrderEnum.IsSingleTypeTypeEnum.getName (Integer.valueOf (this.dispatchType));
        }
        return singleType;
    }


    public String getMemberState() {
        if (this.orderStatus != null) {
            this.memberState = EmployeeOrderStatus.getName (Integer.valueOf (this.orderStatus));
        }
        return memberState;
    }

    public String getInOutStatus() {
        if (this.eedStatus != null) {
            this.InOutStatus = PersonOrderEnum.EntryDimissionStatus.getName (Integer.valueOf (this.eedStatus));
        }
        return InOutStatus;
    }

    public String getTheStatus() {
        if (this.chgState != null) {
            this.theStatus = ExportPersonOrderEnum.IsTheStatusTypeEnum.getName (Integer.valueOf (this.chgState));
        }
        return theStatus;
    }
    public String getLeavingReason() {
        if (this.dimissionReason != null) {
            this.leavingReason = DimissionReasonTypeEnum.getName (Integer.valueOf (this.dimissionReason));
        }
        return leavingReason;
    }
//    public String getIdCardIsUploaded() {
//        if (this.idCardUpload != null) {
//            this.idCardIsUploaded = ExportPersonOrderEnum.IsWhetherTypeEnum.getName (Integer.valueOf (this.idCardUpload));
//        }
//        return idCardIsUploaded;
//    }
//
//    public String getBankCardCanBeUploaded() {
//        if (this.bankCardUpload != null) {
//            this.bankCardCanBeUploaded = ExportPersonOrderEnum.IsWhetherTypeEnum.getName (Integer.valueOf (this.bankCardUpload));
//        }
//        return bankCardCanBeUploaded;
//    }



    /**
     * 接单方公司类型
     */
    private String receivingType;

    /**
     * 接单方公司id
     */
    private String receivingId;




    /**
     * 客户id
     */
    private String custId;

    /**
     * 雇员ID
     */
    private String employeeId;

    /**
     * 未增反馈
     */
    @Excel(name = "未增反馈",width = 50)
    private String remark1;

    /**
     * 机动分类2
     */
    private String remark2;

    /**
     * 机动分类3
     */
    private String remark3;

    /**
     * 机动分类4
     */
    private String remark4;

    /**
     * 机动分类5
     */
    private String remark5;

    /** 接单分类 */
    private String receivingRemark;

    /**
     * 劳动关系单位
     */
    private String laborRelationsUnit;

    /**
     * 是否存档
     */
    private String storageFlag;

    /**
     * 是否有社保
     */
    private String cardFlag;

    /**
     * 是否有统筹医疗
     */
    private String medicalFlag;

    /**
     * 实际工作地
     */
    private String workAddr;

    /**
     * 社会参与地
     */
    private String cardAddr;

    /**
     * 离职备注
     */
    private String dimissionRemark;
    private String dimissionRemarkStr;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单创建者
     */
    private String creator;
    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 修改人
     */
    private String updater;

    private String agentFileId;


    private String terminationFileId;


    /**
     * 未办理实际做
     */
    private Integer noHandlePracNum;    /**
     * 是否连续转移标识
     */
    private Boolean isTransferFlag;
    @Excel(name = "是否连续转移标识",width = 30)
    private String isTransferFlagStr;

    private Integer chgMethod;
    private String dimMaterial;//离职材料
    private String dimMaterialName;//离职材料

    private Integer recceivingType;
    private Integer serviceNature;
    private String serviceNatureStr;
    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName == null ? null : employeeName.trim();
    }
}
