<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>社保公积金一览</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="消息编号">消息编号</label>
                    <div class="layui-input-inline">
                        <input type="text" id="msgNo" name="msgNo" class="layui-input"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="消息类型">消息类型</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" DICT_TYPE="MSG_TYPE" name="msgType"
                                id="msgType">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="是否已读">是否已读</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" DICT_TYPE="READ_FLAG" name="readFlag"
                                id="readFlag">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="创建时间起">创建时间起</label>
                    <div class="layui-input-inline">
                        <input type="text" id="createTimeS" name="createTimeS" class="layui-input" readonly
                               placeholder="请选择" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="创建时间止">创建时间止</label>
                    <div class="layui-input-inline">
                        <input type="text" id="createTimeE" name="createTimeE" class="layui-input" readonly
                               placeholder="请选择" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="消息内容">消息内容</label>
                    <div class="layui-input-inline">
                        <input type="text" id="content" name="content" class="layui-input"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline" style="float: right">
                    <a class="layui-btn layuiadmin-btn-list" id="btnQuery" data-type="reload" lay-filter="btnQuery"
                       lay-submit="">检索</a>
                    <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card-body">
    <table class="layui-hide" id="messageQueryTable" lay-filter="messageQueryTableFilter"></table>
    <script type="text/jsp" id="toolbarDemo">
        <button class="layui-btn layui-btn-sm" lay-event="update" authURI="/base/message/getMessageWrapperListPage">标记已读</button>
        <button class="layui-btn layui-btn-sm" lay-event="batchUpdate" authURI="/base/message/update">全部已读</button>
        <button class="layui-btn layui-btn-sm" lay-event="export" authURI="/base/message/export">导出数据</button>
    </script>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/message/messageQuery.js?v=${publishVersion}"></script>
</body>
</html>
