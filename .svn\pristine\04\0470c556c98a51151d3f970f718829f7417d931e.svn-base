package com.reon.ehr.sp.sys.service.impl.order;

import com.alibaba.druid.support.json.JSONUtils;
import com.google.common.collect.Sets;
import com.reon.ehr.api.sys.dubbo.service.rpc.IEpInsuranceSetWrapperService;
import com.reon.ehr.api.sys.dubbo.service.rpc.ISysCustomerWrapperService;
import com.reon.ehr.api.sys.enums.OprType;
import com.reon.ehr.api.sys.enums.base.EhrImportDataType;
import com.reon.ehr.api.sys.enums.order.SalaryFlag;
import com.reon.ehr.api.sys.enums.order.ServiceNature;
import com.reon.ehr.api.sys.enums.order.SignForm;
import com.reon.ehr.api.sys.enums.order.StaffingState;
import com.reon.ehr.api.sys.vo.base.EhrImportDataDto;
import com.reon.ehr.api.sys.vo.employee.EhrEmployeeVo;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeContractVo;
import com.reon.ehr.api.sys.vo.order.EhrEmployeeOrderVo;
import com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployee;
import com.reon.ehr.sp.sys.domain.entity.employee.EhrEmployeeOrder;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeContractMapper;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeMapper;
import com.reon.ehr.sp.sys.mapper.employee.EhrEmployeeOrderMapper;
import com.reon.ehr.sp.sys.service.base.EhrBatchImportDataService;
import com.reon.ehr.sp.sys.service.order.BatchAddEhrEmployeeOrderImportService;
import com.reon.ehr.sp.sys.service.order.IEpEmployeeOrderService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISocialSecurityFundTrialCalculationWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISocialWagsMaintainWrapperService;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.employee.EmployeeCertType;
import com.reon.hr.api.customer.enums.employee.EmployeeContractType;
import com.reon.hr.api.customer.enums.employeeContract.TempTypeEnum;
import com.reon.hr.api.customer.enums.employeeContract.WorkMethodEnum;
import com.reon.hr.api.customer.utils.BatchImportExcelCommonUtil;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.utils.IdCardUtils;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.common.enums.BooleanEnum;
import com.reon.hr.common.utils.BigDecimalUtil;
import com.reon.hr.common.utils.VoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.reon.ehr.api.sys.enums.order.ServiceNature.DISPATCH;
import static com.reon.ehr.api.sys.enums.order.ServiceNature.OUTSOURCING;

/**
 <AUTHOR> on 2023/7/26. */
@Service
public class BatchAddEhrEmployeeOrderImportServiceImpl implements BatchAddEhrEmployeeOrderImportService {
    private static final Logger logger = LoggerFactory.getLogger(BatchAddEhrEmployeeOrderImportServiceImpl.class);
    @Autowired
    private EhrEmployeeOrderMapper ehrEmployeeOrderMapper;
    @Autowired
    private EhrEmployeeContractMapper ehrEmployeeContractMapper;
    @Autowired
    private IEpEmployeeOrderService epEmployeeOrderService;
    @Autowired
    private EhrBatchImportDataService ehrBatchImportDataService;
    @DubboReference
    private IEpInsuranceSetWrapperService insuranceSetWrapperService;
    @Resource
    private ISocialSecurityFundTrialCalculationWrapperService iSocialSecurityFundTrialCalculationWrapperService;
    @DubboReference
    private ISocialWagsMaintainWrapperService socialWagsMaintainWrapperService;
    @Autowired
    private EhrEmployeeMapper ehrEmployeeMapper;
    @DubboReference
    private ISysCustomerWrapperService customerService;
    private static final String REQUIRED_FIELDS_NOT_FILLED = "必填项未填写";
    private static final String DISOBEY_RULE = "违反规则";
    private static final String FORMAT_ERROR = "格式错误";
    private static final String SYSTEM_ERROR = "系统错误";
    private static final String DATA_ERROR = "数据错误";
    private static final String IMPORT_DATA_DIFFERENT_SYSTEM_DATA = "导入信息与系统数据冲突";

    private static final String ERROR_MESSAGE_001 = "证件类型不匹配!";
    private static final String ERROR_MESSAGE_002 = "身份证不合法!";
    private static final String ERROR_MESSAGE_003 = "手机号码不合法!";
    private static final String ERROR_MESSAGE_004 = "服务性质不匹配!";
    private static final String ERROR_MESSAGE_005 = "缴费地区不匹配!";
    private static final String ERROR_MESSAGE_006 = "人员类别不匹配!";
    private static final String ERROR_MESSAGE_007 = "是否需要通知员工交资料不匹配!";
    private static final String ERROR_MESSAGE_008 = "是否单立户不匹配!";
    private static final String ERROR_MESSAGE_009 = "缴费实体未填写!";
    private static final String ERROR_MESSAGE_010 = "社保收费起始月格式不对!";
    private static final String ERROR_MESSAGE_011 = "社保基数标识不匹配!";
    private static final String ERROR_MESSAGE_012 = "社保其它基数未填写!";
    private static final String ERROR_MESSAGE_013 = "公积金收费起始月格式不对!";
    private static final String ERROR_MESSAGE_014 = "填写公积金比例请填写公积金收费起始月!";
    private static final String ERROR_MESSAGE_015 = "填写补充公积金比例请填写公积金收费起始月、公积金比例!";
    private static final String ERROR_MESSAGE_018 = "填写公积金基数标识请填写公积金收费起始月、公积金比例、补充公积金比例!";
    private static final String ERROR_MESSAGE_019 = "填写公积金其它基数请填写公积金收费起始月、公积金比例、补充公积金比例、公积金基数标识!";
    private static final String ERROR_MESSAGE_016 = "公积金基数标识不匹配!";
    private static final String ERROR_MESSAGE_017 = "公积金其它基数未填写!";
    private static final String ERROR_MESSAGE_020 = "服务性质为派遣的请填写派遣起始日、派遣结束日!";
    private static final String ERROR_MESSAGE_021 = "派遣起始日需小于派遣结束日!";
    private static final String ERROR_MESSAGE_022 = "合同签订形式不匹配!";
    private static final String ERROR_MESSAGE_023 = "合同版本名称不匹配!";
    private static final String ERROR_MESSAGE_024 = "服务性质为派遣、外包的请填写劳动合同起始时间、劳动合同结束时间!";
    private static final String ERROR_MESSAGE_025 = "劳动合同起始时间需小于劳动合同结束时间!";
    private static final String ERROR_MESSAGE_026 = "劳动合同类型不匹配!";
    private static final String ERROR_MESSAGE_027 = "是否有试用期为 [是] 请填写试用期起始时间、试用期结束时间!";
    private static final String ERROR_MESSAGE_028 = "试用期起始时间需小于试用期结束时间!";
    private static final String ERROR_MESSAGE_029 = "工作制不匹配!";
    private static final String ERROR_MESSAGE_030 = "服务性质为派遣、外包的请填写工作城市!";
    private static final String ERROR_MESSAGE_031 = "服务性质为派遣的请填写工作单位!";
    private static final String ERROR_MESSAGE_032 = "服务性质为派遣、外包的请填写合同薪资!";
    private static final String ERROR_MESSAGE_033 = "是否有试用期为 [是] 请填写试用期薪资!";
    private static final String ERROR_MESSAGE_034 = "服务性质为派遣、外包的请填写合同签订日期!";
    private static final String ERROR_MESSAGE_035 = "填写公积金收费起始月请填写公积金比例!";
    private static final String ERROR_MESSAGE_036 = "填写的公积金比例不匹配!";
    private static final String ERROR_MESSAGE_037 = "请填写公积金比例，不能填写补充公积金比例!";
    private static final String ERROR_MESSAGE_038 = "请填写补充公积金比例，不能填写公积金比例!";
    private static final String ERROR_MESSAGE_039 = "填写的补充公积金比例不匹配!";
    private static final String ERROR_MESSAGE_040 = "请将公积金比例、公积金基数标识等填写完整!";
    private static final String ERROR_MESSAGE_041 = "填写的公积金比例不在有效时间内!";
    private static final String ERROR_MESSAGE_042 = "填写的补充公积金比例不在有效时间内!";

    private static final String ERROR_MESSAGE_043 = "已存在该证件号，且与本次姓名不符！";
    private static final String ERROR_MESSAGE_044 = "工作单位不匹配！";
    private static final String ERROR_MESSAGE_045 = "薪资需要大于当地最低工资标准！";
    private static final String ERROR_MESSAGE_046 = "是否有试用期必填！";
    private static final String ERROR_MESSAGE_047 = "证件类型是身份证时户口性质需必填！";


    @Override
    public void saveEhrEmployeeOrderImport(ImportDataDto<EhrEmployeeOrderVo> importDataDto, Long custId) {
        EhrImportDataDto<EhrEmployeeOrderVo> ehrImportDataDto = new EhrImportDataDto<>();
        BeanUtils.copyProperties(importDataDto, ehrImportDataDto);
        logger.info("EHR批量订单导入开始,导入编号{}", ehrImportDataDto.getImportNo());
        ehrBatchImportDataService.addImportData(ehrImportDataDto, EhrImportDataType.BATCH_ADD_EMPLOYEE.getCode());

        ehrImportDataDto.setCustId(custId);
        List<EhrEmployeeOrderVo> dataImportList = checkImportData(ehrImportDataDto);
        setSocailAcctSalary(dataImportList);
        if (CollectionUtils.isNotEmpty(dataImportList)) {
            ehrEmployeeOrderMapper.insertVoList(dataImportList);
            List<EhrEmployeeOrder> ehrEmployeeOrderList = VoUtil.copyProperties(dataImportList, EhrEmployeeOrder.class);
            epEmployeeOrderService.insertEhrEmployeeOrderLogByEhrEmployeeOrder(ehrEmployeeOrderList, OprType.ENTRY_JOB.getCode());
            epEmployeeOrderService.insertEhrEmployeeEntryDimissionByOrder(ehrEmployeeOrderList);
            List<EhrEmployeeContractVo> ehrEmployeeContractVoList = VoUtil.copyProperties(dataImportList.stream().filter(e -> e.getServiceNature() != ServiceNature.AGENCY.getCode()).collect(Collectors.toList()), EhrEmployeeContractVo.class);
            if (CollectionUtils.isNotEmpty(ehrEmployeeContractVoList)) {
                for (EhrEmployeeContractVo ehrEmployeeContractVo : ehrEmployeeContractVoList) {
                    ehrEmployeeContractVo.setOrderId(ehrEmployeeContractVo.getId());
                    ehrEmployeeContractVo.setId(null);
                }
                ehrEmployeeContractMapper.insertVoList(ehrEmployeeContractVoList);
            }
        }

        // 将错误信息插入日志
        ehrBatchImportDataService.addAndupdateImportData(ehrImportDataDto);
    }

    private List<EhrEmployeeOrderVo> checkImportData(EhrImportDataDto<EhrEmployeeOrderVo> ehrImportDataDto) {

        List<String> customerRelevanceName = customerService
                .getCustomerRelevanceByCustId(ehrImportDataDto.getCustId()).stream()
                .map(CustomerVo::getCustName)
                .distinct()
                .collect(Collectors.toList());

        List<EhrEmployeeOrderVo> dataImportList = ehrImportDataDto.getDataList();
        List<IndCategoryVo> indCategoryVos = insuranceSetWrapperService.selectAll();
        Map<String, Integer> byCityNameMap = indCategoryVos.stream()
                .collect(Collectors.toMap(IndCategoryVo::getCityName, IndCategoryVo::getCityCode, (a1, a2) -> a2));

        Map<String, String> byCategoryNameMap = indCategoryVos.stream()
                .collect(Collectors.toMap(i -> i.getCategoryCodeName().replaceAll("-", "_"), IndCategoryVo::getCategoryCode, (a1, a2) -> a2));

        List<InsuranceSetRatioVo> insuranceSetRatioVoList = insuranceSetWrapperService.selectAllInsuranceSetAcctRatio();
        Map<String, List<String>> ratioNameMap = insuranceSetRatioVoList.stream()
                .collect(Collectors.groupingBy(InsuranceSetRatioVo::getIndTypeCode, Collectors.mapping(InsuranceSetRatioVo::getInsuranceRatioCode, Collectors.toList())));

        Map<String, List<String>> ratioCodeMap = new HashMap<>();
        for (EhrEmployeeOrderVo ehrEmployeeOrderVo : dataImportList) {

            Integer certType = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getCertTypeString(), EmployeeCertType.class);
            String certNo = ehrEmployeeOrderVo.getCertNo();
            if (certType == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_001);
            } else {
                ehrEmployeeOrderVo.setCertType(certType);
                if (certType == EmployeeCertType.ID_CARD.getCode()){
                    if (!IdCardUtils.isIdcard(certNo)) {
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_002);
                    }
                    if(StringUtils.isBlank(ehrEmployeeOrderVo.getHousehold())){
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_047);
                    }
                }
            }
            if (certNo != null) {
                List<EhrEmployeeVo> ehrEmployeeVos = ehrEmployeeMapper.selectEmployeeByCertNo(certNo);
                if (CollectionUtils.isNotEmpty(ehrEmployeeVos)) {
                    EhrEmployeeVo ehrEmployeeVo = ehrEmployeeVos.get(0);
                    String name = ehrEmployeeVo.getName();
                    if (name != null && name.equals(ehrEmployeeOrderVo.getName())) {
                        ehrEmployeeOrderVo.setEhrEmployeeId(ehrEmployeeVo.getId());
                    } else {
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_043);
                    }
                } else {
                    EhrEmployee ehrEmployee = VoUtil.copyProperties(ehrEmployeeOrderVo, EhrEmployee.class);
                    ehrEmployee.setId(null);
                    ehrEmployeeMapper.insertSelective(ehrEmployee);
                    ehrEmployeeOrderVo.setEhrEmployeeId(ehrEmployee.getId());
                }
            }
            if (ehrEmployeeOrderVo.getMobile() != null && !BatchImportExcelCommonUtil.isMobile(ehrEmployeeOrderVo.getMobile())) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_003);
            }

            Integer cityCode = byCityNameMap.get(ehrEmployeeOrderVo.getCityCodeString());
            if (cityCode == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_005);
            } else {
                ehrEmployeeOrderVo.setCityCode(cityCode);
            }

            ServiceNature serviceNatureEnum = null;
            if (ehrEmployeeOrderVo.getServiceNatureString() != null) {
                serviceNatureEnum = EnumsUtil.getEnumByName(ehrEmployeeOrderVo.getServiceNatureString(), ServiceNature.class);
                if (serviceNatureEnum == null) {
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_004);
                } else {
                    ehrEmployeeOrderVo.setServiceNature(serviceNatureEnum.getCode());
                    // 服务性质是派遣/外包,城市存在且劳动合同起始时间存在则校验薪资是否大于城市最低工资
                    if (ServiceNature.empContractServiceNatureList.contains(serviceNatureEnum.getCode())) {
                        if (StringUtils.isBlank(ehrEmployeeOrderVo.getStartDateStr())) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_024);
                        }
                    }
                }
            }


            List<String> ratioCodeList = new ArrayList<>();
            String categoryCode = byCategoryNameMap.get(ehrEmployeeOrderVo.getCategoryCodeString());
            if (categoryCode == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_006);
            } else {
                ehrEmployeeOrderVo.setCategoryCode(categoryCode);
                if (ratioNameMap.containsKey(categoryCode)) {
                    ratioCodeList = ratioNameMap.get(categoryCode);
                }
            }
            Integer telFlag = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getTelFlagString(), BooleanTypeEnum.class);
            if (telFlag == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_007);
            } else {
                ehrEmployeeOrderVo.setTelFlag(telFlag);
            }
            Integer accountFlag = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getAccountFlagString(), BooleanTypeEnum.class);
            if (accountFlag == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_008);
            } else {
                ehrEmployeeOrderVo.setAccountFlag(accountFlag);
                if (accountFlag == BooleanTypeEnum.YES.getCode() && StringUtil.isEmpty(ehrEmployeeOrderVo.getPayerEntity())) {
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_009);
                }
            }
            Integer socailFeeTime = ehrEmployeeOrderVo.getSocailFeeTime();
            if (socailFeeTime != null) {
                int socailFeeTimeStrLength = socailFeeTime.toString().length();
                if (socailFeeTimeStrLength < 6) {
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_010);
                } else {
                    ehrEmployeeOrderVo.setSocailFeeTime(Integer.parseInt(socailFeeTime.toString().substring(0, 6)));
                }
            }
            Integer socailSalaryFlag = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getSocailSalaryFlagString(), SalaryFlag.class);
            if (socailSalaryFlag == null) {
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_011);
            } else {
                ehrEmployeeOrderVo.setSocailSalaryFlag(socailSalaryFlag);
                if (socailSalaryFlag == SalaryFlag.OTHER_BASES.getCode()) {
                    if (ehrEmployeeOrderVo.getSocailSalary() == null) {
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_012);
                    }
                } else if (socailSalaryFlag == SalaryFlag.LOWEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setSocailSalary(new BigDecimal(-1));
                } else if (socailSalaryFlag == SalaryFlag.HIGHEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setSocailSalary(new BigDecimal(999999999));
                }
            }
            checkAcct(ehrEmployeeOrderVo);
            String ratioKey = ehrEmployeeOrderVo.getCityCode() + "-" + ehrEmployeeOrderVo.getAcctFeeTime();
            List<String> insuranceRatioCodeList = new ArrayList<>();
            if (ratioCodeMap.containsKey(ratioKey)) {
                insuranceRatioCodeList = ratioCodeMap.get(ratioKey);
            } else {
                List<SocialSecurityFundExportVo> radioByCityCode = iSocialSecurityFundTrialCalculationWrapperService.getRadioByCityCode(
                        ehrEmployeeOrderVo.getCityCode(),
                        ehrEmployeeOrderVo.getAcctFeeTime(),
                        null);
                if(CollectionUtils.isNotEmpty(radioByCityCode)){
                    ehrEmployeeOrderVo.setServiceSiteCode(radioByCityCode.get(0).getServiceSiteCode());
                }
                insuranceRatioCodeList = radioByCityCode.stream()
                        .map(SocialSecurityFundExportVo::getInsuranceRatioCode).collect(Collectors.toList());
                ratioCodeMap.put(ratioKey, insuranceRatioCodeList);
            }
            // 服务性质是派遣/外包,城市存在且劳动合同起始时间存在则校验薪资是否大于城市最低工资
            if (ServiceNature.empContractServiceNatureList.contains(ehrEmployeeOrderVo.getServiceNature())) {
                if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getStartDateStr())) {
                    Integer month = Integer.parseInt(extractNumbers(ehrEmployeeOrderVo.getStartDateStr().replaceAll("-", "").replaceAll("/", "")).substring(0, 6));
                    SocialWagsMaintainVo wageVo = socialWagsMaintainWrapperService.getMinimumWageByCityCodeAndMonth(cityCode, month,ehrEmployeeOrderVo.getServiceSiteCode());
                    BigDecimal minWage = BigDecimal.ZERO;
                    if (wageVo != null) {
                        minWage = wageVo.getMinWage();
                    }
                    ehrEmployeeOrderVo.setFormalSalary(BigDecimalUtil.max(ehrEmployeeOrderVo.getFormalSalary(), minWage));
                    ehrEmployeeOrderVo.setProbaSalary(BigDecimalUtil.max(ehrEmployeeOrderVo.getProbaSalary(), minWage));
                }
            }
            if (ehrEmployeeOrderVo.getAcctFeeTime() != null) {
                int acctFeeTimeStrLength = ehrEmployeeOrderVo.getAcctFeeTime().toString().length();
                if (acctFeeTimeStrLength < 6) {
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_013);
                } else {
                    ehrEmployeeOrderVo.setAcctFeeTime(Integer.parseInt(ehrEmployeeOrderVo.getAcctFeeTime().toString().substring(0, 6)));
                    if (StringUtil.isBlank(ehrEmployeeOrderVo.getAcctRatioStr())) {
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_035);
                    } else {
                        String[] acctRatioS = ehrEmployeeOrderVo.getAcctRatioStr().split("_");
                        String acctRatio = acctRatioS[acctRatioS.length - 1];
                        if (ratioCodeList.contains(acctRatio)) {
                            if (insuranceRatioCodeList.contains(acctRatio)) {
                                ehrEmployeeOrderVo.setAcctRatio(acctRatio);
                            } else {
                                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_041);
                            }
                        } else {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_036);
                        }
                    }
                    if (StringUtil.isNotBlank(ehrEmployeeOrderVo.getSupplementAcctRatioStr())) {
                        String[] supplementAcctRatioS = ehrEmployeeOrderVo.getSupplementAcctRatioStr().split("_");
                        String supplementAcctRatio = supplementAcctRatioS[supplementAcctRatioS.length - 1];
                        if (ratioCodeList.contains(supplementAcctRatio)) {
                            if (insuranceRatioCodeList.contains(supplementAcctRatio)) {
                                ehrEmployeeOrderVo.setSupplementAcctRatio(supplementAcctRatio);
                            } else {
                                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_042);
                            }
                        } else {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_039);
                        }
                    }
                }
            }/*else if(StringUtil.isNotBlank(ehrEmployeeOrderVo.getAcctRatioStr())){
                if(ehrEmployeeOrderVo.getSocailFeeTime()==null){
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_014);
                }else {
                    ehrEmployeeOrderVo.setAcctFeeTime(ehrEmployeeOrderVo.getSocailFeeTime());
                }
            }else if(StringUtil.isNotBlank(ehrEmployeeOrderVo.getSupplementAcctRatioStr())){
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_015);
            }else if(StringUtil.isNotBlank(ehrEmployeeOrderVo.getAcctSalaryFlagString())){
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_018);
            }else if(ehrEmployeeOrderVo.getAcctSalary()!=null){
                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_019);
            }*/


            Integer acctSalaryFlag = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getAcctSalaryFlagString(), SalaryFlag.class);
            if (acctSalaryFlag == null) {
                if (StringUtil.isNotBlank(ehrEmployeeOrderVo.getAcctSalaryFlagString())) {
                    ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_016);
                }
            } else {
                ehrEmployeeOrderVo.setAcctSalaryFlag(acctSalaryFlag);
                if (acctSalaryFlag == SalaryFlag.OTHER_BASES.getCode()) {
                    if (ehrEmployeeOrderVo.getAcctSalary() == null) {
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_017);
                    }
                } else if (acctSalaryFlag == SalaryFlag.LOWEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setAcctSalary(new BigDecimal(-1));
                } else if (acctSalaryFlag == SalaryFlag.HIGHEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setAcctSalary(new BigDecimal(999999999));
                }
            }
            if (serviceNatureEnum != null) {
                Set<ServiceNature> serviceNatures = Sets.newHashSet(DISPATCH, OUTSOURCING);
                if (serviceNatures.contains(serviceNatureEnum)) {
                    if(StringUtils.isBlank(ehrEmployeeOrderVo.getProbationFlagS())){
                        ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_046);
                    }else{
                      if (BooleanEnum.TRUE.getMsg().equals(ehrEmployeeOrderVo.getProbationFlagS())) {
                          ehrEmployeeOrderVo.setProbationFlag(BooleanEnum.TRUE.getCode());
                          if (ehrEmployeeOrderVo.getProbaStart() == null || ehrEmployeeOrderVo.getProbaEnd() == null) {
                              ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_027);
                          } else if (ehrEmployeeOrderVo.getProbaStart().getTime() > ehrEmployeeOrderVo.getProbaEnd().getTime()) {
                              ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_028);
                          }
                          if (ehrEmployeeOrderVo.getProbaSalary() == null) {
                              ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_033);
                          }
                      } else {
                          ehrEmployeeOrderVo.setProbationFlag(BooleanEnum.FALSE.getCode());
                          ehrEmployeeOrderVo.setProbaStart(null);
                          ehrEmployeeOrderVo.setProbaEnd(null);
                          ehrEmployeeOrderVo.setProbaSalary(null);
                          ehrEmployeeOrderVo.setProbaSalaryStr(null);
                          }
                    }
                }
                switch (serviceNatureEnum) {
                    case DISPATCH:
                        //TODO 不是必填项的日期是否解析
                        if (ehrEmployeeOrderVo.getDispatchStart() == null || ehrEmployeeOrderVo.getDispatchEnd() == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_020);
                        } else if (ehrEmployeeOrderVo.getDispatchEnd().getTime() < ehrEmployeeOrderVo.getDispatchStart().getTime()) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_021);
                        }
                        if (StringUtil.isBlank(ehrEmployeeOrderVo.getEmployingUnit())) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_031);
                        } else {
                            if (!customerRelevanceName.contains(ehrEmployeeOrderVo.getEmployingUnit())) {
                                ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_044);
                            }
                        }
                    case OUTSOURCING:
                        Integer signForm = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getSignFormString(), SignForm.class);
                        if (signForm == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_022);
                        } else {
                            ehrEmployeeOrderVo.setSignForm(signForm.toString());
                        }
                        Integer tempType = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getTempTypeString(), TempTypeEnum.class);
                        if (tempType == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_023);
                        } else {
                            ehrEmployeeOrderVo.setTempType(tempType);
                        }
                        if (ehrEmployeeOrderVo.getStartDate() == null || ehrEmployeeOrderVo.getEndDate() == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_024);
                        } else if (ehrEmployeeOrderVo.getStartDate().getTime() > ehrEmployeeOrderVo.getEndDate().getTime()) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_025);
                        }
                        Integer employeeContractType = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getEmpContractTypeString(), EmployeeContractType.class);
                        if (employeeContractType == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_026);
                        } else {
                            ehrEmployeeOrderVo.setEmpContractType(employeeContractType);
                        }


                        Integer workMethod = EnumsUtil.getCodeByName(ehrEmployeeOrderVo.getWorkMethodStr(), WorkMethodEnum.class);
                        if (workMethod == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_029);
                        } else {
                            ehrEmployeeOrderVo.setWorkMethod(workMethod.toString());
                        }
                        if (StringUtil.isBlank(ehrEmployeeOrderVo.getWorkPlace())) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_030);
                        }
                        if (ehrEmployeeOrderVo.getFormalSalary() == null) {
                            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_032);
                        }
                        break;
                }
            }

            ehrEmployeeOrderVo.setCustId(ehrImportDataDto.getCustId());
            ehrEmployeeOrderVo.setDelFlag("N");
            ehrEmployeeOrderVo.setCreateTime(new Date());
            ehrEmployeeOrderVo.setCreator(ehrImportDataDto.getLoginName());
            ehrEmployeeOrderVo.setStaffingState(StaffingState.SUBMITTED.getCode());

            ehrImportDataDto.getImportDataLogVoList().add(ehrBatchImportDataService.createEhrImportDataLogVo(ehrEmployeeOrderVo, ehrImportDataDto.getImportNo(), ehrImportDataDto.getLoginName()));

            // 记录错误信息.
            if (!ehrEmployeeOrderVo.getErrorDescription().isEmpty()) {
                ehrImportDataDto.recordError(ehrEmployeeOrderVo.getRowNum(), JSONUtils.toJSONString(ehrEmployeeOrderVo.getErrorDescription()));
            }
        }
        return dataImportList.stream().filter(importDto -> importDto.getErrorDescription().isEmpty()).collect(Collectors.toList());
    }

    public static String extractNumbers(String str) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return matcher.group();
        } else {
            return "";
        }
    }

    private void checkAcct(EhrEmployeeOrderVo ehrEmployeeOrderVo) {
        if (ehrEmployeeOrderVo.getSocailFeeTime() != null && ehrEmployeeOrderVo.getAcctRatioStr() != null) {
            ehrEmployeeOrderVo.setAcctFeeTime(ehrEmployeeOrderVo.getSocailFeeTime());
        }
        if ((ehrEmployeeOrderVo.getAcctRatioStr() == null && ehrEmployeeOrderVo.getAcctSalaryFlagString() != null)
                || (ehrEmployeeOrderVo.getAcctRatioStr() != null && ehrEmployeeOrderVo.getAcctSalaryFlagString() == null)) {
            ehrEmployeeOrderVo.updateError(DISOBEY_RULE, ERROR_MESSAGE_040);
        }
    }

    private void setSocailAcctSalary(List<EhrEmployeeOrderVo> dataImportList) {
        List<ParameVo> list = new ArrayList<>();
        Map<String, SocialSecurityFundSingleVo> map = new HashMap<>();
        SocialSecurityFundSingleVo singleVo = null;

        ParameVo parameVo = new ParameVo();
        BigDecimal base = BigDecimal.valueOf(-1);
        for (EhrEmployeeOrderVo ehrEmployeeOrderVo : dataImportList) {
            if (ehrEmployeeOrderVo.getSocailSalaryFlag() != SalaryFlag.OTHER_BASES.getCode()) {
                parameVo.setSocialSecurityBase(ehrEmployeeOrderVo.getSocailSalary());
                parameVo.setProvidentFundBase(base);
                parameVo.setCityCode(ehrEmployeeOrderVo.getCityCode());
                parameVo.setCurrentDate(ehrEmployeeOrderVo.getSocailFeeTime());
                if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getAcctRatio())) {
                    parameVo.setInsuranceRatioCode(ehrEmployeeOrderVo.getAcctRatio());
                }
                String key = parameVo.getSocialSecurityBase() + "-" + parameVo.getProvidentFundBase() + "-" + parameVo.getCurrentDate() +
                        "-" + parameVo.getCityCode() + "-" + parameVo.getInsuranceRatioCode();
                if (!map.containsKey(key)) {
                    list.add(parameVo);
                    try {
                        List<SocialSecurityFundSingleVo> singleDataList = iSocialSecurityFundTrialCalculationWrapperService.getSingleData(list, null);
                        singleVo = singleDataList.isEmpty() ? null : singleDataList.get(0);
                        map.put(key, singleVo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    singleVo = map.get(key);
                }
            }
            if (singleVo != null) {
                if (ehrEmployeeOrderVo.getSocailSalaryFlag() == SalaryFlag.LOWEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setSocailSalary(singleVo.getMinBase());
                }
                if (ehrEmployeeOrderVo.getSocailSalaryFlag() == SalaryFlag.HIGHEST_CARDINALITY.getCode()) {
                    ehrEmployeeOrderVo.setSocailSalary(singleVo.getMaxBase());
                }
            }

            if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getAcctRatio()) && ehrEmployeeOrderVo.getAcctSalaryFlag() != SalaryFlag.OTHER_BASES.getCode()) {
                list.clear();
                parameVo.setSocialSecurityBase(base);
                parameVo.setProvidentFundBase(ehrEmployeeOrderVo.getAcctSalary());
                parameVo.setCityCode(ehrEmployeeOrderVo.getCityCode());
                parameVo.setInsuranceRatioCode(ehrEmployeeOrderVo.getAcctRatio());
                parameVo.setCurrentDate(ehrEmployeeOrderVo.getAcctFeeTime());
                String key = parameVo.getSocialSecurityBase() + "-" + parameVo.getProvidentFundBase() + "-" + parameVo.getCurrentDate() +
                        "-" + parameVo.getCityCode() + "-" + parameVo.getInsuranceRatioCode();
                list.add(parameVo);

                if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getSupplementAcctRatio())) {
                    ParameVo supplementAcctParameVo = new ParameVo();
                    supplementAcctParameVo.setSocialSecurityBase(base);
                    supplementAcctParameVo.setProvidentFundBase(ehrEmployeeOrderVo.getAcctSalary());
                    supplementAcctParameVo.setCityCode(ehrEmployeeOrderVo.getCityCode());
                    supplementAcctParameVo.setInsuranceRatioCode(ehrEmployeeOrderVo.getAcctRatio());
                    supplementAcctParameVo.setCurrentDate(ehrEmployeeOrderVo.getAcctFeeTime());
                    key = "-" + supplementAcctParameVo.getSocialSecurityBase() + "-" + supplementAcctParameVo.getProvidentFundBase() + "-" + supplementAcctParameVo.getCurrentDate() +
                            "-" + supplementAcctParameVo.getCityCode() + "-" + supplementAcctParameVo.getInsuranceRatioCode();
                    list.add(supplementAcctParameVo);
                }
                if (!map.containsKey(key)) {
                    try {
                        List<SocialSecurityFundSingleVo> singleDataList = iSocialSecurityFundTrialCalculationWrapperService.getSingleData(list, null);
                        singleVo = singleDataList.isEmpty() ? null : singleDataList.get(0);
                        map.put(key, singleVo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    singleVo = map.get(key);
                }
            }
            if (singleVo != null && ehrEmployeeOrderVo.getAcctSalaryFlag() != null) {
                if (ehrEmployeeOrderVo.getAcctSalaryFlag() == SalaryFlag.LOWEST_CARDINALITY.getCode()) {
                    if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getSupplementAcctRatio())) {
                        ehrEmployeeOrderVo.setAcctSalary(singleVo.getMinFundsBase());
                    } else {
                        ehrEmployeeOrderVo.setAcctSalary(singleVo.getMinFundsBaseNot());
                    }
                }
                if (ehrEmployeeOrderVo.getAcctSalaryFlag() == SalaryFlag.HIGHEST_CARDINALITY.getCode()) {
                    if (StringUtils.isNotBlank(ehrEmployeeOrderVo.getSupplementAcctRatio())) {
                        ehrEmployeeOrderVo.setAcctSalary(singleVo.getMaxFundsBase());
                    } else {
                        ehrEmployeeOrderVo.setAcctSalary(singleVo.getMaxFundsBaseNot());
                    }
                }
            }
        }
    }
}
