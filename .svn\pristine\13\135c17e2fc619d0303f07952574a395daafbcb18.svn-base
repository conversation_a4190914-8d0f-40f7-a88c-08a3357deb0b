var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        laydate = layui.laydate,
        form = layui.form;
    layer = parent.layer === undefined ? layui.layer : parent.layer, tableSelect = layui.tableSelect;

    var tableView = '';
    var resDataLength = 0;
    //日期范围
    var startApplyTime = laydate.render({
        elem: '#startApplyTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {

        }
    });

    var lastDate = laydate.render({
        elem: '#lastDate',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });


    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('printFormTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    table.render({
        id: 'printFormTable',
        elem: '#printFormTable',
        url: ML.contextPath + '/bill/insurancePracticePayByCmb/getPrintReceivingApplicationFromPage',
        where: {"paramData": JSON.stringify(serialize("searchForm"))},
        method: 'get',
        toolbar: '#toolbarDemo',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        limit: 50,
        height: 650,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', fixed: 'left', align: 'center'},
            // {
            //     field: 'payType', title: '支付类型', align: 'center', width: '5%', templet: function (d) {
            //         if (d.payType) {
            //             return ML.dictFormatter("PAYMENT_SUBCLASS", d.payType);
            //         } else {
            //             return "无数据";
            //         }
            //     }
            // },
            {
                field: 'payMethod', title: '支付方式', align: 'center', width: '10%', templet: function (d) {
                    if (d.payMethod) {
                        return ML.dictFormatter('MODE_OF_PAYMENT', d.payMethod);
                    } else {
                        return "无数据"
                    }
                }
            },
            {field: 'packName', title: '金额类型',align:'center',width: '10%'},
            {field: 'payComName', title: '福利办理方', align: 'center', width: '13%'},
            {
                field: 'bankType', title: '社保付款银行', align: 'center', width: "6%", templet: function (d) {
                    return ML.dictFormatter('BANK', d.bankType);
                }
            },
            {field: 'bankNo', title: '社保付款银行账号', align: 'center', width: '15%'},
            {field: 'payBankName', title: '社保付款银行名称', align: 'center', width: '15%'},
            {
                field: 'actPayAmt', title: '实际支付金额', align: 'center', width: '10%', templet: function (d) {
                    return !isNaN(d.actPayAmt) ? Number(d.actPayAmt).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) : '无数据';
                }
            },
            {
                field: 'payAmt', title: '申请支付金额', align: 'center', width: '10%', templet: function (d) {
                    return !isNaN(d.payAmt) ? Number(d.payAmt).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) : '无数据';
                }
            },
            {
                field: 'balanceAmt', title: '差异金额', align: 'center', width: '10%', templet: function (d) {
                    return !isNaN(d.balanceAmt) ? Number(d.balanceAmt).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) : '无数据';
                }
            },
            {field: 'applyTime', title: '支付申请时间', align: 'center', width: '10%'},
            {field: 'lastDate', title: '最晚支付时间', align: 'center', width: '10%',sort:true},
            {field: 'payMonth', title: '支付所属年月', align: 'center', width: '10%'},
            {
                field: 'applicant', title: '申请人', align: 'center', width: '10%', templet: function (d) {
                    return ML.loginNameFormater(d.applicant)
                }
            },
            {field: '', title: '查看打款进度', toolbar: '#toolDemo2', align:'center',width: '10%', fixed: 'right'},

        ]],
        done: function (res) {
            ML.hideNoAuth();
            tableView = this.elem.next();
            resDataLength = res.data.length;
            for (var i = 0; i < res.data.length; i++) {
                if (res.data[i].oneFeeFlag == 2) {
                    $("tr").eq(i + 1).css("color", "red");
                    $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                }
            }
        }
    });


    table.on('toolbar(printFormTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;

        switch (obj.event) {
            case 'printForm':
                if (data.length != 1) {
                    return layer.msg("请选择一条数据！");
                }

                // 判断派单地打款状态
                if (data[0].printType != 2) {
                    return layer.confirm(
                        "派单地打款未完成，本次打印的数据仅供参考。是否继续？",
                        {
                            title: "提示",
                            btn: ['确定', '取消'],
                            icon: 0
                        },
                        function () {
                            openPrintDialog(data); // 点击“确定”继续执行打印逻辑
                        }
                    );
                } else {
                    openPrintDialog(data); // 打款完成也继续执行打印逻辑
                }
                break;

            case 'printDiff':
                if (data.length != 1) {
                    return layer.msg("请选择一条数据！");
                }
                if (data[0].printType != 2) {
                    return layer.msg("派单地打款未完成不允许打印！");
                }
                window.location.href = ctx + "/bill/insurancePracticePayByCmb/printBalanceDiff?payId=" + data[0].id + '&type=' + 1;
                break;
            case 'progress':
                if (data.length !== 1) {
                    return layer.msg("请选择一条数据！");
                }

                // 判断状态是否允许查看进度
                if (data[0].printTypr == 2) {
                    return layer.msg("该笔支付已完成全部转账！");
                }


                break;

        }
    });

    table.on('tool(printFormTableFilter)', function (obj) {
        var data = obj.data;
        if (obj.event === 'progress') {
            $.ajax({
                type: "GET",
                url: ctx + "/bill/insurancePracticePayByCmb/checkProgress?payId=" + data.id,
                dataType: 'json',
                success: function (response) {
                    if (!response.data || response.data.length === 0) {
                        return layer.msg("暂无支付进度信息！");
                    }

                    let tableHtml = `
                <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
                    <table class="layui-table" style="width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                       <thead style="background-color: #f5f5f5;">
                            <tr>
                                <th style="width: 40%;">派单公司</th>
                                <th style="width: 30%;">支付状态</th>
                                <th style="width: 30%;">支付方式</th>
                            </tr>
                        </thead>

                        <tbody>
                                    `;

                    $.each(response.data, function (i, item) {
                        const payStatusText = item.payFlag === 1 ? '未付款'
                            : item.payFlag === 2 ? '已付款'
                                : '未知状态';

                        const onlineFlagText = item.onlineFlag === 2 ? '银企直联'
                            : item.onlineFlag === 1 ? '线下转账'
                                : '未知方式';

                        tableHtml += `
                    <tr>
                        <td>${item.disCom || '-'}</td>
                        <td>${payStatusText}</td>
                        <td>${onlineFlagText}</td>
                    </tr>
                             `;
                    });

                    tableHtml += `
                        </tbody>
                    </table>
                    <div style="text-align: right; margin-top: 10px;">
                        <button class="layui-btn layui-btn-cancel">关闭</button>
                    </div>
                </div>
                         `;

                    layer.open({
                        type: 1,
                        title: '派单地转账详情',
                        area: ['60%', 'auto'],
                        content: tableHtml,
                        success: function (layero, index) {
                            layero.find('.layui-btn-cancel').on('click', function () {
                                layer.close(index);
                            });
                        },
                        btn: []
                    });
                },
                error: function () {
                    layer.msg("请求失败，请稍后重试！");
                }
            });

        }
    });


    form.on('submit(printFormTableFilter)', function (data) {
        table.reload('printFormTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });


    $(document).ready(function () {

        $.ajax({
            type: "GET",
            url: ctx + "/customer/contract/orgList",
            dataType: 'json',
            success: function (data) {
                $.each(data.data, function (i, item) {
                    $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));

                });
                form.render('select');
            },
            error: function (data) {
                console.log("error");
            }
        });
    });

    //重载数据
    function reloadTable() {
        table.reload('printFormTable', {
            where: {
                paramData: JSON.stringify(getSearchForm()),
            }
        });
    }

    function getSearchForm() {
        var value = serialize("searchForm");
        value.matchFlag = true;
        return value;
    }

    $("#reset").click(function () {
        $("#appCom").val(null);
        startApplyTime.config.min = {year: 2010, month: 0, date: 1};
        startApplyTime.config.max = {year: 2099, month: 11, date: 12};
        endApplyTime.config.min = {year: 2010, month: 0, date: 1};
        endApplyTime.config.max = {year: 2099, month: 11, date: 12};
        form.render();
    });

    table.on('rowDouble(printFormTableFilter)', function (obj) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看详情',
            area: ['58%', '82%'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + "/bill/paymentApply/checkPaymentApplyPage?id=" + obj.data.id,
            end: function () {
                reloadTable();
            }
        });
        return false;
    });


    function openPrintDialog(data) {
        // 自定义样式
        var style = `
        <style>
            .custom-layer-content {
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
            }
            .custom-layer-content label {
                font-size: 16px;
                font-weight: bold;
            }
            .custom-layer-content select {
                width: 100%;
                padding: 5px;
                margin-top: 10px;
                border-radius: 5px;
                border: 1px solid #ccc;
            }
            .custom-layer-content button {
                margin-top: 15px;
                background-color: #007BFF;
                color: white;
                border: none;
                padding: 8px 15px;
                cursor: pointer;
                border-radius: 5px;
                font-size: 16px;
            }
            .custom-layer-content button:hover {
                background-color: #0056b3;
            }
        </style>
    `;

        // 打开弹窗
        layer.open({
            type: 1,
            title: '请选择打印类型',
            area: ['350px', '280px'],
            content: style + `
            <div class="custom-layer-content">
                <label for="typeSelect">打印类型：</label>
                <select id="typeSelect">
                    <option value="" disabled selected style="font-weight: bold; color: gray;">请选择</option>
                    <option value="1">收款单</option>
                    <option value="2">出款单</option>
                </select>
                <br>
                <button id="confirmBtn">确定</button>
            </div>
        `,
            success: function (layero, index) {
                var confirmBtn = layero.find('#confirmBtn');
                confirmBtn.on('click', function () {
                    var selectedType = layero.find('#typeSelect').val();
                    if (!selectedType) {
                        return layer.msg("请选择一个类型！");
                    }

                    var param = {
                        'paymentApplyComId': data[0].payMentId,
                        'id': data[0].id,
                        'type': selectedType
                    };

                    window.location.href = ctx + "/bill/insurancePracticePayByCmb/printReceivingApplicationFrom?paramData=" + JSON.stringify(param);

                    layer.msg("打印中，请勿频繁点击");
                    layer.close(index);
                });
            }
        });
    }


});