var ctx = ML.contextPath;
layui.use(['form', 'layer', 'laydate', 'table', 'jquery'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, $ = layui.jquery;
    layer = parent.layer === undefined ? layui.layer : parent.layer;

    form.render('select');
    var templetId = $("#templetId").val();
    var fees = [];
    ML.ajax("/customer/supplierBillTemplet/getFeeHzsByTempletId", {'templetId': templetId}, function (result) {
        if (result.data && result.data.length > 0) {
            fees = result.data;
            setTimeout(function () {
                getTable(fees);
            }, 100);
        } else {
               setTimeout(function () {
                getTable(fees);
            }, 100);
        }
    }, 'GET');

    form.on('select(receiveMonthTypeFilter)', function (data) {
        //var oldData = table.cache["feeHzId"];
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        fees[index]["receiveMonthType"] = data.value;
        setCache();
        table.reload('feeHzId', {data: fees});
    });

    ////增加一行
    function addOne() {
        var data1 = {};
        data1['defaultFlag'] = 2;
        data1['beforeMonths'] = 0;
        fees.push(data1);
        setCache();
        table.reload('feeHzId', {data: fees});
    }

    function getTable(data) {
        table.render({
            id: 'feeHzId',
            elem: '#feeHzId',
            data: data,
            page: true, //默认为不开启
            limits: [20, 50, 100],
            limit: 20,
            title: "收费频率",
            toolbar: '#toolbarDemo',
            defaultToolbar: [],
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {type: 'checkbox', width: '4%'},
                {type: 'numbers', title: '序号', width: '6%'},
                {field: 'feeNo', title: '收费频率编号', align: 'center', width: '14%'},
                {
                    field: 'beforeMonths',
                    title: '<i style="color: red;">*</i>提前几月收',
                    align: 'center',
                    width: '20%',
                    templet: function (d) {
                        var beforeMonths = '';
                        if (d.beforeMonths != undefined) {
                            beforeMonths = d.beforeMonths;
                        }

                        return '<input type="tel" class="layui-input layui-table-cell" name="beforeMonths" maxlength="1" id="beforeMonths" autocomplete="off" oninput="this.value=this.value.replace(/[^0-1]/g, \'\')" lay-verify="required" value="' + beforeMonths + '" />';

                    }
                },


                {
                    field: 'feeName',
                    title: '<i style="color: red;">*</i>收费频率名称',
                    align: 'center',
                    width: '25%',
                    templet: function (d) {
                        var feeName = '';
                        if (d.feeName != undefined) {
                            feeName = d.feeName;
                        }
                        return '<input type="text" class="layui-input layui-table-cell" name="feeName" id="feeName" autocomplete="off" lay-verify="required" value="' + feeName + '" />';
                    }
                },
                {field: 'defaultFlag', title: '是否默认', align: 'center', width: '20%', templet: '#defaultFlagId'},
                /*{field: '', title: '操作', align: 'center', toolbar: '#toolDemo', width: '10%',}*/
            ]],
            done: function (res, curr, count) {
                ML.hideNoAuth();
                for (var i = 0; i < res.data.length; i++) {
                    if (res.data[i].used) {
                        $("tr[data-index = '" + i + "'] td[data-field='collectFreq'] input").css('background-color', 'lightgrey');
                        $("tr[data-index = '" + i + "'] td[data-field='receiveMonthType'] input").css('background-color', 'lightgrey');
                        $("tr[data-index = '" + i + "'] td[data-field='beforeMonths']").css('background-color', 'lightgrey');
                    }
                }
                table.on('toolbar(feeHzFilter)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data;
                    switch (obj.event) {
                        case "add":
                                addOne();

                            break;
                        case 'delete':
                            if (data.length !== 1) {
                                layer.msg('请选择一行');
                            } else {

                                layer.confirm("你确定要删除么？", {btn: ['确定', '取消']}, function (index) {
                                    if (data[0].id !== undefined) {

                                        ML.ajax("/customer/supplierBillTemplet/deleteBillTempletFeeCfg", {
                                            "id": data[0].id,

                                        }, function (result) {
                                            layer.msg(result.msg);
                                            if (result.code == 0) {
                                                deleteReload();
                                                layer.closeAll('iframe');//关闭弹窗
                                            } else {
                                                layer.msg("删除失败!");
                                            }

                                        })
                                    } else {
                                        deleteReload();
                                    }
                                });
                            }
                            break;
                        case 'save':
                            setCache();
                            if (fees.length == 0) {
                                layer.msg("请添加收费频率");
                                return false;
                            }
                            var feesTemp = [];
                            for (var i = 0; i < fees.length; i++) {
                                if (!fees[i].beforeMonths) {
                                    return layer.msg("请输入提前几月收");
                                }
                                if (!fees[i].feeName) {
                                    return layer.msg("请输入频率名称");
                                }


                                if (fees[i].defaultFlag == 2) {
                                    feesTemp.push(fees[i].defaultFlag);
                                }


                                fees[i]['templetId'] = templetId;
                                delete fees[i].LAY_TABLE_INDEX;
                            }
                            if (feesTemp.length > 1) {
                                layer.msg("只能默认一个");
                                return false;
                            } else if (feesTemp.length == 0) {
                                layer.msg("必须默认一条");
                                return false;
                            }

                            $.ajax({
                                url: ctx + "/customer/supplierBillTemplet/saveSetHzSupplierBillTemplet",
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify(fees),
                                success: function (result) {
                                    layer.msg(result.msg);
                                    if (result.code == 0) {
                                        layer.closeAll('iframe');//关闭弹窗
                                    } else if (result.code == -1) {
                                        ML.layuiButtonDisabled($('#saveBtn'), true);// 禁用
                                    }
                                }
                            });
                            break;
                        case 'cancel':
                            layer.closeAll('iframe'); //关闭弹窗
                            break;
                    }
                });
            }
        });
    }


    function deleteReload() {
        var oldData = table.cache["feeHzId"];
        setCache();
        for (var i = oldData.length - 1; i >= 0; i--) {
            if (oldData[i].LAY_CHECKED === true) {
                fees.splice(oldData[i]['LAY_TABLE_INDEX'], 1);
            }
        }
        layer.msg("删除成功", {time: 10}, function () {
            table.reload('feeHzId', {data: fees});
        });
    }

    // function show() {
    //     var row = '无法修改'; //获取显⽰内容
    //     //⼩tips
    //     layer.tips(row, t, {
    //         tips: [1, '#3595CC'],
    //         time: 2000
    //     })
    // }


    ///保存下拉框和输入框的值
    function setCache() {
        $("input[name='beforeMonths']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            fees[dataIndex]["beforeMonths"] = a.value;
        });
        $("select[name='receiveMonthType']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            fees[dataIndex]["receiveMonthType"] = a.value;
        });
        $("select[name='collectFreq']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            fees[dataIndex]["collectFreq"] = a.value;
        });
        $("select[name='defaultFlag']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            fees[dataIndex]["defaultFlag"] = a.value;
        });
        $("input[name='feeName']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            fees[dataIndex]["feeName"] = a.value;
        });
    }


});
