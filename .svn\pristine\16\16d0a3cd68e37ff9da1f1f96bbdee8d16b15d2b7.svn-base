package com.reon.hr.api.customer.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CommInsuQuotationVo implements Serializable {
    private String quoteNo;

    private Long custId;

    private String custName;

    private String quoteName;

    private Integer quoteType;

    private Integer valKind;

    private Integer prodType;

    private Integer subType;
    private List<Integer> subTypeList;

    private Integer distributeType;

    private Integer taxFlag;

    private Integer taxRatioType;

    private BigDecimal taxRatio;

    private Integer collectFreq;


    private Integer receiveDate;

    private String seller;

    private String saleSupervisor;

    private String saleMgr;

    private String commissioner;

    private String commSupervisor;

    private Integer status;

    private Integer approvalStatus;

    private String pid;

    private String remark;

//    private Integer ownerCity;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private BigDecimal feeRatio; // 服务比例
    private String sellerOrg;  // 销售专员所属机构
    private String sellerPos;  // 销售专员所属岗位
    private String saleMgrOrg;  // 销售经理所属机构
    private String saleMgrPos;  // 销售经理所属岗位


    private List<CommInsuQuotationItemVo> dataGrop;

    List<QuotationAttachmentVo> quotationAttachmentList;
    //流程任务ID
    private String taskId;

    List<String> uploadIdList;
    private Integer commitProc;

    private Boolean tip = false;

    private List<quotSolutionDto> addList;

    private Integer proratedFee;
}