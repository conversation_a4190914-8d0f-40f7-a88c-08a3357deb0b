package com.reon.hr.sp.customer.dao.cus;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.sp.customer.entity.cus.ContractCompletionReminder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface ContractCompletionReminderMapper extends BaseMapper<ContractCompletionReminder> {


    int updateApprovalStatusByContractNoAnd(@Param("status") Integer status,    @Param("contractNo") String contractNo, @Param("procType") String procType);

    @Update("update `reon-customerdb`.contract_completion_reminder set expected_days = ifnull(expected_days,0) + 1 where del_flag = 'N' and approval_status = 1;")
    void autoUpdateDays();

    @Select("select count(*) from `reon-customerdb`.contract_completion_reminder where del_flag = 'N' and approval_status = 1 and seller = #{loginName}")
    Integer getContractCompletionReminderCountByLoginName(String loginName);
}