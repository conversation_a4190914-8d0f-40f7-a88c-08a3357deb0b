package com.reon.hr.common.utils.poi;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 动态表头处理 抽象类,如果想要 自定义 填充 逻辑 ,就需要继承该类 ,并自己实现 fillSampleData
 todo 该抽象方法不要写 具体业务的实现, 具体业务的实现 由子类实现,此类只能写 共用方法 */
public abstract class DynamicHeaderExcelExporter<T> {
    // 提升到抽象类的公共字段
    protected Workbook workbook;
    protected Sheet sheet;
    protected List<ExcelHeader> headers;
    protected int startRow;   // 这个 startRow 是指 具体值的 开始 行坐标
    protected int colCount;   // 这个表示 表头的 总列数 也是值的总列数

    // 强制子类必须传递基础参数
    protected DynamicHeaderExcelExporter(Workbook workbook, Sheet sheet, List<ExcelHeader> headers) {
        validateParams(workbook, sheet, headers);

        this.workbook = workbook;
        this.sheet = sheet;
        if (CollectionUtils.isNotEmpty(headers)) {
            this.headers = new ArrayList<>(headers);
            initializeHeaderStructure();
        }
    }

    private void validateParams(Workbook wb, Sheet s, List<ExcelHeader> h) {
        if (wb == null || s == null) {
            throw new IllegalArgumentException("Workbook和Sheet不能为空");
        }

    }


    private void initializeHeaderStructure() {
        this.startRow = calculateHeaderDepth(headers);
        this.colCount = calculateTotalColumns(headers);
        buildHeader(workbook, sheet, headers);
    }

    public void buildHeader(Workbook workbook, Sheet sheet, List<ExcelHeader> headers) {
        List<Row> rows = IntStream.range(0, startRow).mapToObj(sheet::createRow).collect(Collectors.toList());
        drawHeader(workbook, sheet, headers, rows, 0, 0); // 初始列坐标从0开始
    }

    /** 计算最大层级 */
    public int calculateHeaderDepth(List<ExcelHeader> headers) {
        if (headers == null || headers.isEmpty()) return 0;

        int maxDepth = 0;
        for (ExcelHeader header : headers) {
            int currentDepth = 1; // 当前层至少1层
            if (header.getChildren() != null && !header.getChildren().isEmpty()) {
                currentDepth += calculateHeaderDepth(header.getChildren());
            }
            maxDepth = Math.max(maxDepth, currentDepth);
        }
        return maxDepth;
    }

    public int calculateTotalColumns(List<ExcelHeader> headers) {
        int total = 0;
        for (ExcelHeader header : headers) {
            // 递归计算子表头或直接累加
            if (header.getChildren() != null && !header.getChildren().isEmpty()) {
                total += calculateTotalColumns(header.getChildren());
            } else {
                total += header.getColSpan();
            }
        }
        return total;
    }


    private static int drawHeader(Workbook workbook, Sheet sheet, List<ExcelHeader> headers, List<Row> rows, int currentRow, int startCol) {
        int currentCol = startCol;
        int colIndex = 0;
        for (ExcelHeader header : headers) {
            // 绘制当前单元格
            Cell cell = rows.get(currentRow).createCell(currentCol);
            cell.setCellValue(header.getTitle());
            cell.setCellStyle(createOrGetHeaderStyle(workbook, "head"));

            // 处理合并区域
            if (header.getRowSpan() > 1 || header.getColSpan() > 1) {
                CellRangeAddress region = new CellRangeAddress(
                        currentRow, currentRow + header.getRowSpan() - 1,
                        currentCol, currentCol + header.getColSpan() - 1);
                sheet.addMergedRegion(region);
                processMergedCellStyle(sheet, region, cell.getCellStyle());
            }
            // 递归处理子表头
            if (header.getChildren() != null && !header.getChildren().isEmpty()) {
                currentCol = drawHeader(workbook, sheet, header.getChildren(), rows, currentRow + 1, currentCol);
            } else {
                currentCol += header.getColSpan(); // 列坐标自增
            }
        }
        return currentCol; // 返回当前处理到的列坐标
    }


    static Map<String, CellStyle> styleMap = new HashMap<String, CellStyle>();

    static CellStyle createOrGetHeaderStyle(Workbook workbook, String key) {

        if (!styleMap.containsKey("head")) {
            CellStyle style = workbook.createCellStyle();
            // 设置对齐方式
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置边框样式
            style.setBorderTop(BorderStyle.THIN);
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);

            // 设置背景颜色
            style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 设置字体样式
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(IndexedColors.WHITE.getIndex());
            font.setFontHeightInPoints((short) 12);
            style.setFont(font);
            styleMap.put("head", style);
        }
        if (!styleMap.containsKey("common")) {
            CellStyle style = workbook.createCellStyle();
            // 设置对齐方式
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置边框样式
            style.setBorderTop(BorderStyle.NONE);
            style.setBorderBottom(BorderStyle.NONE);
            style.setBorderLeft(BorderStyle.NONE);
            style.setBorderRight(BorderStyle.NONE);

            // 设置背景颜色
//            style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
//            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // 设置字体样式
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(IndexedColors.BLACK.getIndex());
            font.setFontHeightInPoints((short) 12);
            style.setFont(font);
            styleMap.put("common", style);
        }
        if (styleMap.containsKey(key)) {
            return styleMap.get(key);
        } else {
            return styleMap.get("common");
        }
    }

    // 修改后的单元格样式处理逻辑
    private static void processMergedCellStyle(Sheet sheet, CellRangeAddress region, CellStyle style) {
        // 遍历合并区域的所有单元格
        for (int rowNum = region.getFirstRow(); rowNum <= region.getLastRow(); rowNum++) {
            Row row = sheet.getRow(rowNum) != null ?
                    sheet.getRow(rowNum) : sheet.createRow(rowNum);

            for (int colNum = region.getFirstColumn(); colNum <= region.getLastColumn(); colNum++) {
                Cell cell = row.getCell(colNum) != null ?
                        row.getCell(colNum) : row.createCell(colNum);

                // 保留第一个单元格的值（避免覆盖）
                if (rowNum == region.getFirstRow() && colNum == region.getFirstColumn()) {
                    cell.setCellStyle(style); // 主单元格保留原样式
                } else {
                    // 创建副本样式避免覆盖
                    CellStyle clonedStyle = sheet.getWorkbook().createCellStyle();
                    clonedStyle.cloneStyleFrom(style);
                    cell.setCellStyle(clonedStyle);
                }
            }
        }
    }

    public abstract void fillSampleData(List<T> list) throws IllegalAccessException;


}
