import request from "@/utils/request";


export function updateByStaffReduction(data) {
    return request({
        url: '/order/updateByStaffReduction',
        method: 'post',
        data: data
    })
}
export function deleteDimMaterial(data) {
    return request({
        url: '/order/deleteDimMaterial',
        method: 'post',
        data: data
    })
}
export function updateDimMaterial(data) {
    return request({
        url: '/order/updateDimMaterial',
        method: 'post',
        data: data
    })
}
export function getReductionPage(query) {
    return request({
        url: '/order/getReductionPage',
        method: 'get',
        params: query
    })
}