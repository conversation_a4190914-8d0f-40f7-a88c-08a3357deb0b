<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.cus.SupplierTempletFeeCfgMapper">

    <sql id="Base_Column_List">
        id,
                templet_id,
                fee_no,
                before_months,
                fee_name,
                default_flag,
                creator,
                create_time,
                updater,
                update_time,
                del_flag
    </sql>

    <select id="getFeeHzsByTempletId" resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        select <include refid="Base_Column_List"/> from supplier_templet_fee_cfg where del_flag='N'  and templet_id = #{templetId}
    </select>

    <select id="getSupplierTempletFeeCfgVoById"  resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        select <include refid="Base_Column_List"/> from supplier_templet_fee_cfg where del_flag='N'  and id = #{id}
    </select>

    <select id="getSupplierTempletFeeCfgByIds"
            resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        select <include refid="Base_Column_List"/> from supplier_templet_fee_cfg where id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="getAllSupTempletFeeCfgVoToMap"
            resultType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        select <include refid="Base_Column_List"/> from supplier_templet_fee_cfg where del_flag = 'N'
    </select>

    <insert id="saveSetHzSupplierBillTemplet" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        INSERT INTO supplier_templet_fee_cfg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != templetId and '' != templetId">
                templet_id,
            </if>
            <if test="null != feeNo and '' != feeNo">
                fee_no,
            </if>
            <if test=" beforeMonths != null ">
                before_months,
            </if>
            <if test="null != feeName and '' != feeName">
                fee_name,
            </if>
            <if test="null != defaultFlag and '' != defaultFlag">
                default_flag,
            </if>
            <if test="null != creator and '' != creator">
                creator,
            </if>
                create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != templetId and '' != templetId">
                #{templetId},
            </if>
            <if test="null != feeNo and '' != feeNo">
                #{feeNo},
            </if>
            <if test="null != beforeMonths ">
                #{beforeMonths},
            </if>
            <if test="null != feeName and '' != feeName">
                #{feeName},
            </if>
            <if test="null != defaultFlag and '' != defaultFlag">
                #{defaultFlag},
            </if>
            <if test="null != creator and '' != creator">
                #{creator},
            </if>
           now()
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM supplier_templet_fee_cfg
        WHERE id = #{id}
    </delete>


    <update id="updateSetHzSupplierBillTemplet" parameterType="com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierTempletFeeCfgVo">
        UPDATE supplier_templet_fee_cfg
        <set>
            before_months = #{beforeMonths},
            <if test="null != feeName and '' != feeName">fee_name = #{feeName},</if>
            default_flag = #{defaultFlag},
            updater = #{updater},
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>



    <update id="deleteBillTempletFeeCfg">
        update supplier_templet_fee_cfg set updater=#{loginName},update_time=now(),del_flag='Y' where id=#{id}
    </update>
</mapper>
