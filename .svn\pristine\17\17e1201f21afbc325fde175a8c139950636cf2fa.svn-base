package com.reon.hr.api.customer.enums.quotation;

import com.reon.hr.api.customer.enums.BaseEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

public enum QuotationSubType implements BaseEnum {
	OUTSOURCING_ONE("外包1",1, 1),
	OUTSOURCING_TWO("外包2",2, 1),
	INDIVIDUAL_PERSONNEL_AGENT("单项人事代理",1, 2),
	SINGLE_COMPENSATION_AGENT ("单项薪酬代理",2, 2),
	FULL_AGENT ("全代理",3, 2),
	PHYSICAL_EXAMINATION_MANAGEMENT ("体检管理",2, 3),
	FESTIVAL_WELFARE ("年节福利",3, 3),
	CAMPUS_RECRUITMENT ("校园招聘",1, 4),
	SOCIAL_RECRUITMENT ("社会招聘",2, 4),
	EXTENDED_TRAINING  ("扩展培训",3, 4),
	ENTERPRISE_SERVICE_TRAINING ("企业服务培训",1, 5),
	PROFESSIONAL_TITLE_AND_PROFESSIONAL_QUALIFICATION_TRAINING  ("职称职业资格培训",2, 5),
	EXTENDED_TRAINING2 ("拓展培训",3, 5),
	DOMESTIC_CERTIFICATE_HANDLING ("国内证件办理",1, 6),
	CORPORATE_FISCAL_AND_TAX_SERVICES ("企业财税服务",2, 6),
	LEGAL_CONSULTATION  ("法务咨询",3, 6),
	FOREIGN_PERSONNEL_LICENSE_SERVICE ("外籍人员证照服务",4, 6),
    DISABILITY_SUB_PROJECT ("残疾人子项目",5, 6),
	SAFETY_EMPLOYMENT_FACTOR_REPORT ("“安全”用工系数报告",1, 7),
//	SOCIAL_SECURITY_SHORT_TERM_ADVANCE_PAYMENT ("社保短期垫付",1, 8),
	TALENT_DISPATCH  ("人才派遣",1, 9),
    DEFORMED_DISPATCH  ("残疾人派遣",2, 9),
	EMPLOYER_LIABILITY_INSURANCE ("雇主责任险",1, 10),
	ACCIDENT_SUPPLEMENTARY_INSURANCE  ("意外保险",2, 10),
	SUPPLEMENTARY_INSURANCE  ("补充医疗保险",3, 10);

	/**
	 * 报价子类型代码
	 */
	private int code;
	/**
	 * 报价类型名称
	 */
	private String name;
	/**
	 * 报价父类型代码
	 */
	private int parentCode;


	QuotationSubType(String name, int code, int parentCode) {
		this.code = code;
		this.name = name;
		this.parentCode = parentCode;
	}
	// 单项代理，单项薪酬集合
	public static final List<Integer> agentList = Arrays.asList(INDIVIDUAL_PERSONNEL_AGENT.code,SINGLE_COMPENSATION_AGENT.code);

	@Override
	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	@Override
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getParentCode() {
		return parentCode;
	}

	public void setParentCode(int parentCode) {
		this.parentCode = parentCode;
	}

	public static String getNameByCodeAndParentCode(int code,int parentCode) {
		String name = null;
		for (QuotationSubType value : QuotationSubType.values()) {
			if (code == value.code && parentCode == value.getParentCode()) {
				name = value.name;
			}
		}
		return name;
	}

}
