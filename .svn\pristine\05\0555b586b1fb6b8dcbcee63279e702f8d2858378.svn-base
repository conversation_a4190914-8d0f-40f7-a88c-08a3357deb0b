package com.reon.ehr.web.service;

import com.reon.ehr.api.sys.constant.CacheConstants;
import com.reon.ehr.api.sys.constant.Constants;
import com.reon.ehr.api.sys.dubbo.service.rpc.IRedisCacheWrapperService;
import com.reon.ehr.api.sys.enums.RoleEnum;
import com.reon.ehr.api.sys.utils.SecurityUtils;
import com.reon.ehr.api.sys.vo.SysRoleVo;
import com.reon.ehr.api.sys.vo.SysUserVo;
import com.reon.ehr.manager.AsyncManager;
import com.reon.ehr.manager.factory.AsyncFactory;
import com.reon.ehr.utils.MessageUtils;
import com.reon.ehr.web.exception.user.UserPasswordNotMatchException;
import com.reon.ehr.web.exception.user.UserPasswordRetryLimitExceedException;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 登录密码方法
 *
 * <AUTHOR>
 */
@Component
public class SysPasswordService {
    @DubboReference(check = false)
    private IRedisCacheWrapperService redisCache;

    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    /**
     * 登录账户密码错误次数缓存键名
     *
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username) {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    public void validate(SysUserVo user) {
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        String username = usernamePasswordAuthenticationToken.getName();
        String password = usernamePasswordAuthenticationToken.getCredentials().toString();

        Integer retryCount = redisCache.getCacheObject(getCacheKey(username));

        if (retryCount == null) {
            retryCount = 0;
        }

        if (retryCount >= Integer.valueOf(maxRetryCount).intValue()) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.retry.limit.exceed", maxRetryCount, lockTime)));
            throw new UserPasswordRetryLimitExceedException(maxRetryCount, lockTime);
        }

        if (!matches(user, password)) {
            retryCount = retryCount + 1;
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.retry.limit.count", retryCount)));
            redisCache.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            List<SysRoleVo> roles = user.getRoles();
            // 判断角色是否包含普通角色
            if (roles.stream().anyMatch(role -> RoleEnum.COMMON.getCode().equals(role.getRoleId()))) {
                // 将用户信息放到redis中,并设置忘记密码次数,初始为0,每次访问则增加1
                redisCache.setCacheObject(Constants.FORGET_PASSWORD_CNT_KEY + username, 0, 30, TimeUnit.MINUTES);
            }
            throw new UserPasswordNotMatchException();
        } else {
            clearLoginRecordCache(username);
        }
    }

    public boolean matches(SysUserVo user, String rawPassword) {
        return SecurityUtils.matchesPassword(rawPassword, user.getPassword());
    }

    public void clearLoginRecordCache(String loginName) {
        if (redisCache.hasKey(getCacheKey(loginName))) {
            redisCache.deleteObject(getCacheKey(loginName));
        }
    }
}
