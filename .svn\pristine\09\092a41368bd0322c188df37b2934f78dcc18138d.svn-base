package com.reon.hr.api.customer.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NationwideEmployeeViewVo implements Serializable {

	private static final long serialVersionUID = 943866727336538692L;

//	employee_order
	private String orderNo;

	private String custName;
	private Long custId;

	private String distComName;
	private String receivingName;
	private String distComMan;
	private String receivingMan;
	private String eedStatus;

	//	employee
	private String employeeNo;
	private Long employeeId;
	private String employeeName;
	private String certType;
	private String certNo;

	//contract_area
	private String contractAreaNo;
	private String contractAreaName;////小合同名称

	//contract
	private String contractNo;

	//	入离职信息
	private String processData;
}
