package com.reon.hr.sp.customer.entity.contract;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
    * edit_contract_info_workflow association 表 合同特殊审批关联表
    */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class EciwAsso {
    /**
    * 主键ID
    */
    private Long id;

    private Long eciwId;

    /**
    * 合同编号
    */
    private String contractNo;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人
    */
    private String updater;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 删除标识(Y:已删除，N:未删除)
    */
    private String delFlag;
}