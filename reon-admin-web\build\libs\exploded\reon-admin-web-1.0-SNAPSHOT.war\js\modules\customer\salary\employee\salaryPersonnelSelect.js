layui.use(['jquery','form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ =layui.$,
        form = layui.form,
        layer = layui.layer,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    var custName = $('#custName').val();
    var billMonth = $('#reportMonth').val();



    function getData(param) {
        table.render({
            id: 'categoryGrid',
            elem: '#category'
            ,url: ML.contextPath +'/customer/salary/pureAgent/getAllInformationOfPureAgentData'
            ,page :true
            ,toolbar: '#topbtn'
            ,defaultToolbar:[]
            ,limit:30
             ,where: param
            ,method: 'GET'
            ,limits: [30,50,100]
            ,text: {
                none: '暂无数据' //无数据时展示
            }
            ,cols :[[
                {field:'',type:'checkbox',align:'center'}
                ,{field:'custName',title:'客户名称',align:'center'}
                ,{field:'contractName',title:'合同名称',align:'center'}
                ,{field:'contractNo',title:'合同编号',align:'center'}
                ,{field:'signCom',title:'签单公司',align:'center'}
                ,{field:'distCom',title:'派单公司',align:'center'}
                ,{field:'commissioner',title:'派单客服',align:'center'}
                ,{field:'salaryCommissioner',title:'薪资客服',align:'center'}
                ,{field:'billMonth',title:'客户账单月',align:'center'}
                ,{field:'taxMonth',title:'工资计税月',align:'center'}
                ,{field:'payPlace',title:'发放地',align:'center'}
                ,{field:'withholdingAgentNo',title:'扣缴义务人编号',align:'center'}
                ,{field:'name',title:'员工姓名',align:'center'}
                ,{field:'certNo',title:'证件编号',align:'center'}
                ,{field:'taxRatio',title:'税率',align:'center'}
                ,{field:'serviceFee',title:'含税服务费',align:'center'}

            ]]
            ,done: function () {
                ML.hideNoAuth();
            }
        });
    }

    table.on('toolbar(category)', function(obj){
        var checkStatus = table.checkStatus('categoryGrid');
        switch(obj.event){
            case 'add':
                addView("add","新增",'add');
                break;
            case 'del':
                if ( obj.event == 'del' && checkStatus.data.length == 0){
                    return layer.msg("未选中删除行");
                }
                if (checkStatus.data.length > 1) {
                    var arr=[];
                    $.each(checkStatus.data,function (index,item) {
                        arr.push(item.categoryCode);
                    });
                    delBatch(JSON.stringify(arr));
                }else {
                    del('确定','取消',checkStatus.data[0].categoryCode);
                }
                break;
            case 'update':
                if ( obj.event == 'update' && checkStatus.data.length != 1){
                    return layer.msg("只能选中一行");
                }
                addView("update","编辑",checkStatus.data[0]);
                break;
            case 'export':
                window.location.href = ML.contextPath + "/customer/salary/pureAgent/exportInformationOfPureAgentData";
                break;
        }
    });

    table.on('tool(category)',function (obj) {
        switch (obj.event) {
            case 'update':
                addView("update","编辑",obj.data);
                break;
            case 'del':
                del('确定','取消',obj.data.categoryCode);
                break;
            case  'check':
                check(obj.data.categoryCode);
                break;
        }
    });



    var initYear;
    laydate.render({
        elem: '#reportMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#reportMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });



    /*新增/编辑*/
    function addView(oprType,title,data) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: ['550px', '300px'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/sys/IndividualCategory/addView',
            success: function (layero, index){

                var body = layer.getChildFrame('body', index);

                body.find("#categoryCode").val(data.categoryCode);
                body.find("#selectperson").val(data.indTypeCode);
                body.find("#typename").val(data.classifyName);

            },
            end: function () {
                reloadTable();
            }
        });
    }
    /*删除*/
    function del(btn1,btn2,id) {
        layer.confirm('确定删除？', {
            btn: [btn1,btn2] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: ML.contextPath + "/sys/IndividualCategory/delCategory",
                data: {code:id},
                dataType: "json",
                success: function(data){
                    layer.msg(data.msg,{icon: 1});
                    reloadTable();
                },
                error:function (data) {

                    layer.msg(data.msg,{icon:5})
                }
            });

        });
    }
    function check (id) {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看',
            area: ['600px', '400px'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ML.contextPath + '/sys/IndividualCategory/gotoCheckCategoryView?code='+id
        });
    }

    // function delBatch(ids) {
    //     layer.confirm('确定删除？', {
    //         btn: ['确定','取消'] //按钮
    //     }, function(){
    //         $.ajax({
    //             type: "POST",
    //             url: ML.contextPath + "/sys/IndividualCategory/delCategoryBatch",
    //             data: {codes:ids},
    //             dataType: "json",
    //             success: function(data){
    //                 layer.msg(data.msg,{icon: 1});
    //                 reloadTable();
    //             },
    //             error:function (data) {
    //
    //                 layer.msg(data.msg,{icon:5})
    //             }
    //         });
    //
    //     });
    // }
    //重载数据
    form.on('submit(btnQuery)', function (data) {
        var param = serialize("searchForm");
        if ($('#reportMonth').val() == '') {
            layer.msg("请选择报表年月");
            return false;
        } else {
            param.billMonth = $('#reportMonth').val()
            param.custName = $('#custName').val()
            param.commissioner = $('#commissioner').val()
            param.salaryCommissioner = $('#salaryCommissioner').val()
                getData(param)
        }
        return false;
    });
    $(document).ready(function () {
        /*param.createTime = $('#reportMonth').val();
        if ($('#reportMonth').val()) {
            getData(param);
        }*/
        var responsibleServiceInfo = [];
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getResponsibleService",
            dataType: 'json',
            success: function (data) {
                responsibleServiceInfo = [];
                responsibleServiceInfo = data.data;
                $.each(responsibleServiceInfo, function (i, item) {
                    $("#commissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
            }
        });
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getAllSalaryCommissioner",
            dataType: 'json',
            success: function (data) {
                $.each(data.data, function (i, item) {
                    $("#salaryCommissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
            }
        });
    })


});



