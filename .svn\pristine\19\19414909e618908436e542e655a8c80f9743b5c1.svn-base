package com.reon.hr.sp.change.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.change.dubbo.service.rpc.change.ICollectEmpLogWrapperService;
import com.reon.hr.api.change.vo.CollectEmpLogVo;
import com.reon.hr.sp.change.service.change.ICollectEmpLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("collectEmpLogDubboService")
public class CollectEmpLogWrapperServiceImpl implements ICollectEmpLogWrapperService {
    @Autowired
    private ICollectEmpLogService collectEmpLogService;

    @Override
    public Page<CollectEmpLogVo> getByCollectEmpId(Long collectEmpId, Integer page, Integer limit) {
        return collectEmpLogService.getByCollectEmpId(collectEmpId, page, limit);
    }
}
