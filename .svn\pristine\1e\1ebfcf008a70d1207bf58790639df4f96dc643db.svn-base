<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>商保订单修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .layui-table-cell {
            overflow: visible;
        }

        .layui-form-select {
            position: initial;
        }

        .layui-table-box {
            overflow: visible;
        }

        .layui-table-body {
            overflow: visible;
        }
        .tableSelect .layui-table-body{
            overflow: scroll;
        }
        .overflow-hidden{
            width: fit-content;
        }
        .overflow-hidden .layui-table-cell {
            overflow: hidden;
        }
        /* 设置下拉框的高度与表格单元相同 */
        td .layui-form-select {
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
            z-index: 50000;
        }
        .input{
            border: 0px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>商保订单信息</legend>
        </fieldset>
        <%--隐藏域--%>
        <input type="hidden" id="supplierId" name="supplierId" value="${commInsurOrder.supplierId}">
        <input type="hidden" id="dealStatus" name="dealStatus" value="${commInsurOrder.dealStatus}">
        <input type="hidden" id="custSoluId" name="custSoluId" value="${commInsurOrder.custSoluId}">
        <input type="hidden" id="orderNo" name="orderNo" value="${commInsurOrder.orderNo}">
        <input type="hidden" id="editEmpId" name="editEmpId" value="${commInsurOrder.editEmpId}">
        <input type="hidden" id="oprType" name="oprType" value="${oprType}">
        <input type="hidden" id="oldTempletId" name="oldTempletId" value="${commInsurOrder.templetId}">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="唯一号">唯一号：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="employeeNo" name="employeeNo" autocomplete="off" value="${commInsurOrder.employeeNo}" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="姓名"><i style="color: red">*</i>姓名：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="name" name="name" placeholder="请输入" autocomplete="off" value="${commInsurOrder.name}" lay-verify="required">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="证件类型"><i style="color: red">*</i>证件类型：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="certType" id="certType" lay-filter="certType" DICT_TYPE="CERT_TYPE" lay-verify="required" >
                        <option value=""></option>
                    </select>
                    <input type="text" class="layui-input layui-input-disposable" style="display: none" id="strCertType" autocomplete="off"
                           value="${commInsurOrder.certType}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="证件号码"><i style="color: red">*</i>证件号码：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="certNo" name="certNo" placeholder="请输入" autocomplete="off" value="${commInsurOrder.certNo}" lay-verify="required">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="手机号码">手机号码：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="mobile" name="mobile" placeholder="请输入" autocomplete="off" value="${commInsurOrder.mobile}" maxlength="11">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="合同编号"><i style="color: red">*</i>合同编号：</label>
                <div class="layui-input-inline">
                    <input type="text" id="contractNo" maxlength="20" name="contractNo"
                           placeholder="请选择"
                           class="layui-input layui-input-disposable" autocomplete="off" value="${commInsurOrder.contractNo}" lay-verify="required" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="方案编号"><i style="color: red">*</i>方案编号：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请选择" id="solutionNo" name="solutionNo" autocomplete="off" value="${commInsurOrder.solutionNo}" lay-verify="required" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="收费起始月"><i style="color: red">*</i>收费起始月：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请选择" id="revStartMonth" name="revStartMonth" autocomplete="off" readonly
                           value="${commInsurOrder.revStartMonth}" lay-verify="required">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="收费截止月">收费截止月：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="endMonth" name="endMonth" autocomplete="off" readonly
                           value="${commInsurOrder.endMonth}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="账单起始月"><i style="color: red">*</i>账单起始月：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请选择" id="billStartMonth" name="billStartMonth" autocomplete="off" readonly
                           value="${commInsurOrder.billStartMonth}" lay-verify="required">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="客户">客户：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" id="custName" readonly  lay-filter="custNameFilter"
                           autocomplete="off" class="layui-input" lay-verType="tips"  placeholder="请选择"  style="padding-left: 10px;width: 190px;" value="${commInsurOrder.custName}"/>
                    <input type="text" name="custId" id="custId" style="display: none;" value=""/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="工种">工种：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           id="workType" name="workType" placeholder="请输入" value="${commInsurOrder.workType}" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline" id="templetIdDiv">
                <label class="layui-form-label layui-elip" title="账单模板"><i style="color: red">*</i>账单模板：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="templetId" id="templetId" lay-filter="tempIdFilter" lay-search lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title">
            <legend>商保增员银行卡信息</legend>
        </fieldset>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="开户行">开户行：</label>
                <div class="layui-input-inline">
                    <select class="layui-select"  name="bankName" DICT_TYPE="BANK"
                            id="bankName" lay-filter="bankNameFilter" lay-search>
                        <option value=""></option>
                    </select>
                    <input type="text" class="layui-input layui-input-disposable" style="display: none" id="strBankName" autocomplete="off"
                           value="${commInsurOrder.bankName}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="开户行账号">开户行账号：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请输入" id="cardNo" name="cardNo" autocomplete="off"
                           value="${commInsurOrder.cardNo}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="开户地">开户地：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请输入" id="openingPlace" name="openingPlace" autocomplete="off"
                           value="${commInsurOrder.openingPlace}">
                </div>
            </div>
        </div>
        <fieldset class="layui-elem-field" title="商保增员关联人员信息（有身份证号的填写身份证号，没有的填写出生日期和性别）">
            <legend class="layui-field-title">商保增员关联人员信息</legend>
            <table class="layui-table" id="addEmpRelativeTable"  lay-filter="addEmpRelativeTableFilter"></table>
        </fieldset>
<%--        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="附带人姓名">附带人姓名：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请输入" id="associatedName" name="associatedName" autocomplete="off"
                           value="${commInsurOrder.associatedName}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="附带人证件类型">附带人证件类型：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="associatedCertType" id="associatedCertType" lay-filter="associatedCertType" DICT_TYPE="CERT_TYPE">
                        <option value=""></option>
                    </select>
                    <input type="text" class="layui-input layui-input-disposable" style="display: none" id="strAssociatedCertType" autocomplete="off"
                           value="${commInsurOrder.associatedCertType}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="附带人证件号码">附带人证件号码：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable"
                           placeholder="请输入" id="associatedCertNo" name="associatedCertNo" autocomplete="off"
                           value="${commInsurOrder.associatedCertNo}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="附带人手机号码">附带人手机号码：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable numberCheck"
                           placeholder="请输入" id="associatedMobile" name="associatedMobile" autocomplete="off"
                           value="${commInsurOrder.associatedMobile}" maxlength="11">
                </div>
            </div>
        </div>--%>
        <fieldset class="layui-elem-field layui-field-title">
            <legend>客户方案信息</legend>
        </fieldset>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="方案名称">方案名称：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable numberCheck"
                           id="solutionName" autocomplete="off" value="${commInsurOrder.solutionName}" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="付费方式">付费方式：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="payMethod" id="payMethod" lay-filter="payMethod" DICT_TYPE="PAY_ORDER_TYPE" disabled>
                        <option value=""></option>
                    </select>
                    <input type="text" class="layui-input layui-input-disposable" style="display: none" id="strPayMethod" autocomplete="off"
                           value="${commInsurOrder.payMethod}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="方案成本价">方案成本价：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                           id="cost"  value="${commInsurOrder.cost}" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="方案指导价">方案指导价：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                           id="price" value="${commInsurOrder.price}" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="供应商">供应商：</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                           id="supplierName" value="${commInsurOrder.supplierName}" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="产品类型">保险形式：</label>
                <div class="layui-input-inline">
                    <select class="layui-select" name="prodType" id="prodType" lay-filter="prodType" DICT_TYPE="INSUR_KIND" disabled>
                        <option value=""></option>
                    </select>
                    <input type="text" class="layui-input layui-input-disposable" style="display: none" id="strProdType" autocomplete="off"
                           value="${commInsurOrder.prodType}">
                </div>
            </div>
        </div>
        <div class="layui-card-body overflow-hidden">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>商保产品关联方案详情信息</legend>
            </fieldset>
            <table class="layui-hide" id="customerSolutionGrid" lay-filter="customerSolutionGridFilter"></table>
        </div>
        <div class="layui-inline" style="float: right;padding:10px ;">
            <button class="layui-btn layuiadmin-btn-list delAttr" id="commit" lay-filter="commit"
                    lay-submit="">提交
            </button>
            <button class="layui-btn layuiadmin-btn-list delAttr" id="save" lay-filter="save"
                    lay-submit="">保存
            </button>
            <button class="layui-btn layuiadmin-btn-list " id="close" lay-filter="close"
                    lay-submit="">取消
            </button>
        </div>
    </form>
</div>

<script type="text/jsp" id="topbtn">
    <div class="">
        <div class="layui-inline"><a href="javascript:void(0)" lay-event="addGroup"><i
                class="layui-btn layui-btn-sm">新增</i></a>
        </div>&nbsp;
        <div class="layui-inline"><a href="javascript:void(0)" lay-event="delGroup"><i
                class="layui-btn layui-btn-sm">删除</i></a>
        </div>
    </div>
</script>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/numberCheck.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/commInsurOrder/commInsurOrderEdit.js?v=${publishVersion}"></script>

<%--证件类型--%>
<script type="text/jsp" id="certTypeFlag">
    <select id="selectCertTypeFlag" name="selectCertTypeFlag" lay-verType="tips" dataId="{{d.certType}}" lay-filter="selectCertTypeFlag" lay-search>
        <option value=""></option>
        {{# layui.each(window.top['dictCachePool']['CERT_TYPE'], function(index, item){ }}
        <option value="{{item.code}}" {{ d.certType==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>
</script>
<%--性别--%>
<script type="text/jsp" id="sexFlag">
    <select id="selectSexFlag" name="selectSexFlag" lay-verType="tips" dataId="{{d.sex}}" lay-filter="selectSexFlag" lay-search>
        <option value=""></option>
        {{# layui.each(window.top['dictCachePool']['SEX'], function(index, item){ }}
        <option value="{{item.code}}" {{ d.sex==item.code?'selected':'' }}>{{ item.name }}</option>
        {{# }); }}
    </select>
</script>

</body>
</html>
