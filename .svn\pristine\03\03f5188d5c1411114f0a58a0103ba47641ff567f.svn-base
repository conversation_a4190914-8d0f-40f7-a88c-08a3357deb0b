package com.reon.hr.api.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年07月29日
 * @Version 1.0
 */

@Data
public class DisabilityGoldRateLogVo implements Serializable {

    private Long id ;
    /**
     * 残障金比率表id
     */
    private Long disabilityId;

    /**
     * 1:默认类型 2:特殊类型
     */
    private Integer disabilityType;

    /**
     * 1:新增 2:修改 3:删除
     */
    private Integer type;

    /**
     * 记录
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;
}
