package com.reon.hr.sp.workflow.dao;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.workflow.vo.ActHiVarinstVo;
import com.reon.hr.api.workflow.vo.WorkflowComentVo;
import com.reon.hr.sp.workflow.entity.ActHiVarinst;
import com.reon.hr.sp.workflow.entity.WorkflowAuditLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkflowAuditLogMapper extends BaseMapper<WorkflowAuditLog> {

    void insertSelective(WorkflowAuditLog workflowAuditLog);

    List<WorkflowComentVo> getWorkflowAuditLogList(@Param("processInstanceIdList") List<String> processInstanceIdList, @Param("bizKey") String bizKey);

    List<ActHiVarinst> getActHiVarinst(@Param("processInstanceIdList") List<String> processInstanceIdList);

    List<ActHiVarinstVo> getActHiVarinstByOvertimeFlag(@Param("processInstanceIdList") List<String> processInstanceIdList, @Param("overtimeFlag") Integer overtimeFlag);

    List<String> getLogDataByOrgPosCodeAndBizKeyOrLoginName(@Param("loginName") String loginName, @Param("orgPosCodeList") List<String> orgPosCodeList, @Param("defineKey") String defineKey);

    List<WorkflowAuditLog> selectByPidAndTaskId(@Param("pid") String processInstanceId);

    String getPidByTaskId(@Param("taskId") String taskId);
}