var ctx = ML.contextPath;
layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'upload'], function () {
    var table = layui.table,
        $ = layui.$,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

   var importNo= $("#importNo").val()
    //查询导入统计数据
    table.render({
        id: 'batchImportHistoryGrid'
        , elem: '#batchImportHistoryGrid'
        , url: ML.contextPath + '/customer/batchImport/getImportDataLogListPage'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , where: {"importNo": $("#importNo").val()}
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , cols: [[
            {field: '', type: 'numbers', width: '3%', fixed: 'left'}
            , {field: 'importNo', title: '导入编号', width: '10%', align: 'center'}
            , {field: 'rowNum', title: '行号', width: '5%', align: 'center'}
            , {field: 'errorDesc', title: '错误描述', width: '22%', align: 'center'}
            , {field: 'remind', title: '提醒描述', width: '22%', align: 'center'}
            , {field: 'importResultzh', title: '导入结果', align: 'center', width: '10%'}
            , {field: 'importTxt', title: '导入信息', align: 'center', width: '10%'}
            , {field: 'creator', title: '创建人', align: 'center', width: '10%',templet: function (d) {
                    if (typeof (d.creator) == "undefined") {
                        return "无创建人"
                    }
                    return ML.loginNameFormater(d.creator);
                }}
            , {field: 'createTime', title: '创建时间', align: 'center', width: '10%' ,fixed: 'right'}

        ]],
        done: function () {
            table.on('toolbar(batchImportHistoryGridTable)', function (obj) {
                switch (obj.event) {
                    case 'export':
                        exportImportedData(null, importNo);
                        break;
                }
            });
        }
    });

    //导出数据
    function exportImportedData(params, importNo) {
        window.location.href=ML.contextPath +"/customer/batchImport/exportExcel?importNo=" + importNo;
    }


    table.render({
        id: 'addGrid',
        elem: '#addTable'
        , page: false
        , url: ctx + '/customer/socialSecurityPayment/getSuppliPayCfgDetailData?importNo=' + importNo
        , limit: Number.MAX_VALUE
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , cols: [[
             {field: 'ratioName', title: '比例名称', align: 'center', width: '25%'}
            , {field: 'ratioCode', title: '比例编码', align: 'center', width: '25%', hide: true}
            , {
                field: 'productCode', title: '产品类型', align: 'center', width: '15%'
                , templet: function (d) {
                    if (!d.productCode) {
                        return "未选择比例"
                    }
                    return ML.dictFormatter("PRODUCT_IND_TYPE", d.productCode);
                }
            },
            {field: 'productCode', title: '比例编码', align: 'center', width: '25%', hide: true},
            {field: 'seqNum', title: '顺序', align: 'center', width: '12%'}
        ]],
        done: function (elem, data) {

        }
    });

});
