package com.reon.hr.sp.customer.dao.supplierPractice;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierPracticeLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: branch2.0
 * @Package: com.reon.hr.sp.customer.dao.supplierPractice
 * @ClassName: SupplierPracticeLogMapper
 * @Author: Administrator
 * @Description:
 * @Date: 2023/6/12 15:52
 * @Version: 1.0
 */
public interface SupplierPracticeLogMapper extends BaseMapper<SupplierPracticeLog> {

    /**
     * 记录实做修改日志
     * @param logs
     */
    void insertSupplierPracticeLogs(@Param("list") List<SupplierPracticeLog> logs);
    void updateLogByIds(@Param("ids") List<Long> ids,@Param("updater") String updater,@Param("status") Integer status);
}
