package com.reon.hr.sp.customer.entity.insurancePractice;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SingleAccountRelative {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 单立户ID
     */
    private Long sinAccId;
    @TableField(exist = false)
    private String sinAccName;
    /**
     * 关联code 小合同编号/福利包编码
     */
    private String relativeNo;

    /**
     * 关联类型 1：小合同编号 2：福利包Code
     */
    private Integer relativeType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;
}
