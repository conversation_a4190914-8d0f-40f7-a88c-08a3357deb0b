package com.reon.hr.api.customer.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 零费用枚举
 */
@Getter
public enum ZeroFeeEnum {
    ALLOW("允许", 2),
    NOT_ALLOW("不允许", 1),
    ;
    private String name;
    private int code;

    ZeroFeeEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String[] getNames(){
        List<String> nameList = Arrays.stream(ZeroFeeEnum.values()).map(vo -> vo.getName()).collect(Collectors.toList());
        return nameList.toArray(new String[nameList.size()]);
    }

    public static Map<String, Integer> getZeroAdjustEnumMap(){
        Map<String, Integer> map = Arrays.stream(ZeroFeeEnum.values()).collect(Collectors.toMap(ZeroFeeEnum::getName, ZeroFeeEnum::getCode));
        return map;
    }
}
