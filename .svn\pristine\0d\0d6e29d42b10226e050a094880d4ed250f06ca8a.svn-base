package com.reon.hr.api.base.dubbo.service.rpc.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.PolicyOffSiteMedicalCareVo;

import java.util.List;

/**
 * 异地医疗福利待遇(PolicyOffSiteMedicalCare)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-13 14:09:58
 */
public interface PolicyOffSiteMedicalCareWrapperService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PolicyOffSiteMedicalCareVo queryById(Long id);

    /**
     * 分页查询
     *
     * @param policyOffSiteMedicalCareVo 筛选条件
     * @param page                页面
     * @param limit               限制
     * @return 查询结果
     */
    Page<PolicyOffSiteMedicalCareVo> queryByPage(Integer page, Integer limit, PolicyOffSiteMedicalCareVo policyOffSiteMedicalCareVo);

    /**
     * 查询列表
     *
     * @param policyWorkInjuryVos 保单工伤VO
     * @return {@link List}<{@link PolicyOffSiteMedicalCareVo}>
     */
    List<PolicyOffSiteMedicalCareVo> queryList(List<PolicyOffSiteMedicalCareVo> policyWorkInjuryVos);

    /**
     * 新增数据
     *
     * @param policyOffSiteMedicalCareVo 实例对象
     * @return 实例对象
     */
    PolicyOffSiteMedicalCareVo insert(PolicyOffSiteMedicalCareVo policyOffSiteMedicalCareVo);
    
    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PolicyOffSiteMedicalCareVo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(List<PolicyOffSiteMedicalCareVo> entities);

    /**
     * 修改数据
     *
     * @param policyOffSiteMedicalCareVo 实例对象
     * @return 实例对象
     */
    PolicyOffSiteMedicalCareVo update(PolicyOffSiteMedicalCareVo policyOffSiteMedicalCareVo);
    
    /**
     * 批量修改数据（MyBatis原生foreach方法）
     *
     * @param entities List<PolicyOffSiteMedicalCareVo> 实例对象列表
     * @return 影响行数
     */
    int updateBatch(List<PolicyOffSiteMedicalCareVo> entities);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

}
