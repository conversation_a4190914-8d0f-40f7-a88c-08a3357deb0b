package com.reon.hr.api.customer.vo.salary;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 纯代发人员信息
 * @datetime 2022/9/20 0020 9:53
 * @version: 1.0
 */
@Data
public class SalaryPureAgentVo implements Serializable {


    /**
     * 客户名称
     */
    @Excel(name = "客户名称",width = 50)
    private String custName;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称",width = 50)
    private String contractName;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号",width = 50)
    private String contractNo;

    /**
     * 签单公司
     */
    @Excel(name = "签单公司",width = 50)
    private String signCom;

    /**
     * 派单公司
     */
    @Excel(name = "派单公司",width = 50)
    private String distCom;

    @Excel(name = "派单方客服",width = 22)
    private String commissioner;

    @Excel(name = "薪资客服",width = 22)
    private String salaryCommissioner;

    /**
     * 客户账单月
     */
    @Excel(name = "客户账单月",width = 20)
    private String billMonth;

    /**
     * 工资计税月
     */
    @Excel(name = "工资计税月",width = 20)
    private String taxMonth;

    /**
     * 发放地
     */
    @Excel(name = "发放地",width = 20)
    private String payPlace;

    /**
     * 扣缴义务人编号
     */
    @Excel(name = "扣缴义务人编号",width = 50)
    private String withholdingAgentNo;

    /**
     * 证件编号
     */
    @Excel(name = "证件编号",width = 50)
    private String certNo;

    /**
     * 员工姓名
     */
    @Excel(name = "员工姓名",width = 22)
    private String name;

    /**
     * 含税服务费
     */
    @Excel(name = "含税服务费",width = 22)
    private BigDecimal serviceFee;

    @Excel(name = "税率",width = 22 )
    private BigDecimal taxRatio;

    private Long custId;
}
