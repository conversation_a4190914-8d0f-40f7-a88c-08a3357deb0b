package com.reon.hr.sp.bill.dao.insurancePractice;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.api.bill.vo.insurancePractice.PracticePayDetailVo;
import com.reon.hr.api.bill.vo.insurancePractice.SearchPaymentDetailsVo;
import com.reon.hr.sp.bill.entity.insurancePractice.PracticePayDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PracticePayDetailMapper extends BaseMapper<PracticePayDetail> {
    Integer insertList(@Param("list") List<PracticePayDetail> detailVos);

//        <insert id="insertList" keyProperty="id" useGeneratedKeys="true">

    void updateOperateTimeById(@Param("ids") List<Long> id, @Param("operateTime") Long operateTime);

    List<PracticePayDetailVo> getPracticePayDetailsPage(@Param("params") SearchPaymentDetailsVo lockInfoVo);

    void updatePracticePayDetailVoByIds(PracticePayDetailVo condition);
    void updatePracticePayDetailVoByPayApplyIds(PracticePayDetailVo condition);

    List<PracticePayDetailVo> getPracticePayDetailsByCondition(@Param("vo") PracticePayDetailVo vo);

    void deletePracticePayDetailVosByBillIds(@Param("billIds") List<Long> billIds,@Param("reportIds") List<Long> reportIds);

    void deletePracticePayDetailVosByParams(@Param("params") List<String> params,@Param("reportMonth") Integer reportMonth);

    List<PracticePayDetailVo> selectDetailsByPayId(@Param("payId") Long payId);
    List<PracticePayDetailVo> selectPracticePayDetailListByPayId(@Param("payId") Long payId);

    List<String> selectPracticePayOrderNoListByPayId(@Param("payId") Long payId);

    List<PracticePayDetailVo> getProvidentDetailsByPayId(@Param("id") Long id);
}
