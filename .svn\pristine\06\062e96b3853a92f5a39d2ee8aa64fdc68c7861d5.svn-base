package com.reon.hr.sp.customer.dao.salary;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.salary.pay.EmpTaxDeductionSearchVo;
import com.reon.hr.api.customer.vo.salary.pay.EmpTaxDeductionVo;
import com.reon.hr.sp.customer.entity.salary.EmpTaxDeduction;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EmpTaxDeductionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(EmpTaxDeduction record);

    int insertSelective(EmpTaxDeductionVo record);

    EmpTaxDeduction selectByPrimaryKey(Long id);

    boolean updateByPrimaryKeySelective(EmpTaxDeductionVo record);

    int updateByPrimaryKey(EmpTaxDeduction record);
    int updateSatus(EmpTaxDeduction record);

    EmpTaxDeductionVo getSalaryEmpTaxDeduction(EmpTaxDeductionVo empTaxDeductionVo);
    /*
    查询未抵扣项
    **/
    List<EmpTaxDeductionVo> getSpecialProject(@Param ("empId") Long empId, @Param("billMonth") Integer billMonth,@Param ("custId") Long custId);
    /**
     * 页面查询抵扣信息*/
    List<EmpTaxDeductionVo> getEmpTaxDeduction(EmpTaxDeductionSearchVo vo, Page<EmpTaxDeductionVo> page);

    int updateByList(@Param("empTaxDeductionVoList") List<EmpTaxDeductionVo> empTaxDeductionVoList);

    List<EmpTaxDeductionVo> selectEmpIdsByMonthAndEmpIdList(@Param ("empIds")List<Long> empIds,@Param ("taxMonth") Integer taxMonth);

    int updateDateByList(@Param("list") List<EmpTaxDeductionVo> updateEmpTaxDeductionVoList);

    int insertDateByList(@Param("insertEmpTaxDeductionVoList")List<EmpTaxDeductionVo> insertEmpTaxDeductionVoList);
}
