package com.reon.hr.sp.bill.entity.bill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * non_monthly_prod
 * <AUTHOR>
@Data
public class NonMonthlyProd implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账单Id
     */
    private Long billId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 账单月
     */
    private Integer billMonth;

//    /**
//     * 起缴月
//     */
//    private Integer startMonth;

    /**
     * 产品类型
     */
    private Integer prodCode;

    /**
     * 社保比例code
     */
    private String insuranceCode;

    /**
     * 企业金额
     */
    private BigDecimal comAmt;

    /**
     * 个人金额
     */
    private BigDecimal indAmt;

    /**
     * 服务月
     */
    private Integer receivableMonth;

    /**
     * 账单类型(1:正常账单，2:补差账单)
     */
    private Integer billType;

    /**
     * 所属期（年缴样式为eg:2022）
     */
    private Integer period;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;

    private static final long serialVersionUID = 1L;
}