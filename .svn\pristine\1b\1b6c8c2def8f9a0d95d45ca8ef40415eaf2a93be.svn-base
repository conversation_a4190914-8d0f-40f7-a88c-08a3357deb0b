<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>

<head>
  <title>新增集团</title>
  <meta charset="utf-8">
  <title></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">

  <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all" />
  <style>
    .layui-input {
      padding-right: 30px !important;
    }
  </style>
</head>

<body class="childrenBody">
<div class="layui-tab-content">
  <div class="layui-tab-item layui-show" style="margin-top: 5px">
    <form class="layui-form" method="post">
      <table class="layui-table" lay-skin="nob" style="width: 65%;">
        <input type="hidden" id="custScales" name="custScales" value="${custScale}">
        <input type="hidden" id="id" name="id" value="${id}">
        <tr>
          <td align="right" width="30%"><i style="color: red; font-weight: bolder;">*</i>集团名称:</td>
          <td width="70%"><input class="layui-input" type="text" name="groupName" id="groupName" lay-verify="required" value="${groupName}" disabled></td>
        </tr>

        <tr>
          <td align="right" width="30%"><i style="color: red; font-weight: bolder;">*</i>客户规模:</td>
          <td width="70%">
            <select id = "custScale" name="custScale" DICT_TYPE="CUST_SCALE" lay-verify="required" lay-verType="tips" >
              <option value=""></option>
            </select>
          </td>
        </tr>
      </table>
      <div style="float: right; margin-right: 40%;">
        <button class="layui-btn" lay-submit lay-filter="save" id="save" type="button" authURI="/customer/group/editCustScale">保存</button>
        <button class="layui-btn" type="button" id="cancelBtn">取消</button>
      </div>
    </form>
  </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/groupManagement/editCustSCale.js?v=${publishVersion}"></script>
</body>

</html>