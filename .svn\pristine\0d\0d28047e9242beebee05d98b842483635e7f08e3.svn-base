<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>残障金比率表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="" style=" width: 127px;">城市</label>
                    <div class="layui-input-inline">
                        <select class="layui-select layui-select-disabled" name="cityCode" id="cityCode" lay-search AREA_TYPE>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="" style=" width: 127px;">计算方式</label>
                    <div class="layui-input-inline">
                        <select class="layui-select layui-select-disabled" name="specialType" id="specialType" DICT_TYPE="SPECIAL_TYPE">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="" style=" width: 127px;">合同名称</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" type="text" id="contractName" readonly>
                        <input class="layui-input" type="text" id="contractNo" name="contractNo" readonly style="display: none">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-list "  id="btnQuery" lay-filter="btnQueryFilter">查询</button>
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-list " type="reset" id="resetBtn">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
<table class="layui-hide" id="disabilityGoldRate" lay-filter="disabilityGoldRate"></table>
<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" id="add" lay-event="add" authURI="/specialDisabilityGoldRate/gotoAddSpecialDisabilityGoldRatePage">新增</button>
    <button class="layui-btn layui-btn-sm" id="add" lay-event="edit" authURI="/specialDisabilityGoldRate/gotoUpdateSpecialDisabilityGoldRatePage">修改</button>
    <button class="layui-btn layui-btn-sm" id="export" lay-event="export" authURI="/disabilityGoldRate/export">导出</button>
</script>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/specialDisabilityGoldRate/specialDisabilityGoldRatePage.js?v=${publishVersion}"></script>
</body>
</html>
