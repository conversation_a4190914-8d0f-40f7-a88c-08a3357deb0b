package com.reon.hr.api.customer.enums.salary;

import com.reon.hr.api.customer.enums.BaseEnum;

/**
 * <AUTHOR> on 2023/3/2.
 */
public enum OtherCircumstancesExplanations implements BaseEnum {
    INTEREST_DIVIDEND_BONUS(1,"扣缴申报利息股息红利所得"),
    ACCIDENTAL_INCOME(2,"扣缴申报偶然所得"),
    OTHER_INCOME(3,"申报其他所得")
    ;
    private int code;
    private String name;

    OtherCircumstancesExplanations(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static Integer getCodeByName(String name) {
        for (OtherCircumstancesExplanations value : OtherCircumstancesExplanations.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;

    }
}
