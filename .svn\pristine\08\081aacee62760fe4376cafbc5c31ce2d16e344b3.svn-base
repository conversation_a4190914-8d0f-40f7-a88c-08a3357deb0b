<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>临时政策维护</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input {
            padding-right: 30px;
        !important;
        }

        .layui-table-cell {
            padding: 0px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <input type="hidden" class="layui-input" id="type" name="type" value="1">
        <div class="layui-inline queryTable">


            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入人" style="font-weight:800">文件类型</label>
                <div class="layui-input-inline">
                    <select name="fileType" id="fileType" lay-search  >
                        <option value=""></option>
                        <option value="3">商务合同</option>
                        <option value="4">协议·函件</option>
                    </select>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="导入人" style="font-weight:800">文件描述</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="remark" name="remark">
                </div>
            </div>





            <a class="layui-btn" lay-submit id="btnQuery" lay-filter="btnQueryFilter">检索</a>
            <button class="layui-btn" id="reset" type="reset">重置</button>
        </div>

    </form>
    <%--endQuery--%>


</blockquote>
<%--startTable--%>
<table class="layui-hide" id="batchImportGrid" lay-filter="batchImportGridTable"></table>
<%--endTable--%>
<script type="text/jsp" id="btn">

    <a href="javascript:void(0)" title="删除" lay-event="delete" authURI=/sys/policy/delByFileId"><i
            class="layui-icon layui-icon-delete"></i></a>&nbsp
    <a href="javascript:void(0)" title="查看" lay-event="query"
       authURI="/customer/batchImport/gotoBatchImportedDataHistoryView"><i class="layui-icon layui-icon-search"></i></a>
</script>
<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="import" lay-event="import"
                >导入
        </button>
        <button class="layui-btn layui-btn-sm" id="delete" lay-event="delete"
                >删除
        </button>
         <button class="layui-btn layui-btn-sm" id="edit" lay-event="edit"
                >修改
        </button>

    </div>
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/base/textLibrary/textLibrary.js?v=${publishVersion}"></script>
</body>
</html>
