package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IDisabilityGoldRateWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.vo.DisabilityGoldRateVo;
import com.reon.hr.api.customer.dubbo.service.rpc.IWithholdingAgentService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.WithholdingAgentEnum;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.salary.ExcessCalculationAmountDetailVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.customer.vo.withholdingAgent.ExportWithholdingAgentVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.sp.customer.dao.cus.CustomerMapper;
import com.reon.hr.sp.customer.dao.cus.SupplierMapper;
import com.reon.hr.sp.customer.dao.salary.ExcessCalculationAmountDetailMapper;
import com.reon.hr.sp.customer.dao.withholdingAgent.WithholdingAgentMapper;
import com.reon.hr.sp.customer.entity.cus.Customer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @datetime 2022年 08月 29日 11:43
 * @version: 1.0
 */
@Service("iWithholdingAgentService")
public class IWithholdingAgentWrapperServiceImpl implements IWithholdingAgentService {

    @Autowired
    private WithholdingAgentMapper withholdingAgentMapper;

    @Autowired
    private SupplierMapper supplierMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private IOrgnizationResourceWrapperService iOrgnizationResourceWrapperService;

    @Autowired
    private IUserWrapperService iUserWrapperService;

    @Autowired
    private IAreaResourceWrapperService iAreaResourceWrapperService;

    @Autowired
    private IDisabilityGoldRateWrapperService disabilityGoldRateWrapperService;
    @Autowired
    private ISequenceService sequenceService;
    @Autowired
    private ExcessCalculationAmountDetailMapper excessCalculationAmountDetailMapper;


    @Override
    public Page<WithholdingAgentVo> getWithholdingAgentPage(WithholdingAgentVo withholdingAgentVo, Integer page, Integer limit) {
        Page<WithholdingAgentVo> voPage = new Page<>(page, limit);
        List<WithholdingAgentVo> withholdingAgentPage = withholdingAgentMapper.getWithholdingAgentPage(withholdingAgentVo, voPage);
        Map<String, OrgVo> orgVoMap = iOrgnizationResourceWrapperService.findAllCompany().stream().collect(Collectors.toMap(OrgVo::getOrgCode, Function.identity()));
        List<DisabilityGoldRateVo> disabilityGoldRateVos = disabilityGoldRateWrapperService.selectAll();
        Map<String, List<DisabilityGoldRateVo>> dgrMap = disabilityGoldRateVos.stream().collect(Collectors.groupingBy(DisabilityGoldRateVo::getCityCode));
        //转换名字
        Map<String, String> listSignTitle = iUserWrapperService.getAllUserMap();
        Set<String> strings = listSignTitle.keySet();
        for (WithholdingAgentVo agentVo : withholdingAgentPage) {
            for (String string : strings) {
                if (StringUtils.isNotBlank(agentVo.getCreator()) && string.equals(agentVo.getCreator())) {
                    agentVo.setCreator(listSignTitle.get(string));
                }
                if (StringUtils.isNotBlank(agentVo.getUpdater()) && agentVo.getUpdater().equals(string)) {
                    agentVo.setUpdater(listSignTitle.get(string));
                }
            }
            if (agentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE2.getIndex())) {
                if (agentVo.getRefCustId()==null){
                    String custNameByrefCustId = customerMapper.findCustNameByrefCustId(agentVo.getOrgCode());
                    agentVo.setOwnCompany(custNameByrefCustId);
                }else {
                    String allCustomerById = customerMapper.findCustNameByrefCustId(String.valueOf(agentVo.getRefCustId()));
                    agentVo.setOwnCompany(allCustomerById);
                }

            }
            if (agentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE3.getIndex())){
                SupplierVo supplierInfoById = supplierMapper.getSupplierInfoById(Long.valueOf(agentVo.getOrgCode()));
                if (supplierInfoById!=null){
                    agentVo.setOwnCompany(supplierInfoById.getSupplierName());
                }
            }
            if (agentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex())){
                if (orgVoMap.containsKey(agentVo.getOrgCode())){
                    OrgVo orgVo = orgVoMap.get(agentVo.getOrgCode());
                    agentVo.setOwnCompany(orgVo.getOrgName());
                    agentVo.setOpenFlag(orgVo.getOpenFlag());
                }
            }
            List<DisabilityGoldRateVo> disabilityVos = dgrMap.get(agentVo.getPayPlace());
            if (CollectionUtils.isNotEmpty(disabilityVos)) {
                for (DisabilityGoldRateVo disabilityVo : disabilityVos) {
                    if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),disabilityVo.getType())&&Objects.equals(agentVo.getWithholdingAgentType(),disabilityVo.getType())){
                        agentVo.setDisabilityGoldRate(disabilityVo.getRate().multiply(new BigDecimal(100)));
                    }else if (Objects.equals(agentVo.getWithholdingAgentType(),disabilityVo.getType())&&Objects.equals(agentVo.getOrgCode(),disabilityVo.getOrgCode())){
                        if(StringUtils.isBlank(disabilityVo.getWithholdingAgentNo())||disabilityVo.getWithholdingAgentNo().equals(agentVo.getWithholdingAgentNo())){
                            agentVo.setDisabilityGoldRate(disabilityVo.getRate().multiply(new BigDecimal(100)));
                        }
                    }
                }
            }


        }


        return voPage.setRecords(withholdingAgentPage);
    }

    @Override
    public int updateWithholdingAgentById(WithholdingAgentVo withholdingAgentVo) {
        List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList = withholdingAgentVo.getExcessCalculationAmountDetailVoList();
        if(CollectionUtils.isNotEmpty(excessCalculationAmountDetailVoList)){
            List<ExcessCalculationAmountDetailVo> insertList =new ArrayList<>();
             List<ExcessCalculationAmountDetailVo> updateList =new ArrayList<>();
            for (ExcessCalculationAmountDetailVo detailVo:excessCalculationAmountDetailVoList) {
                if(detailVo.getId()==null){
                    detailVo.setWithholdingAgentNo(withholdingAgentVo.getWithholdingAgentNo());
                    detailVo.setCreator(withholdingAgentVo.getUpdater());
                    insertList.add(detailVo);
                }else {
                    detailVo.setUpdater(withholdingAgentVo.getUpdater());
                    updateList.add(detailVo);
                }
                detailVo.setWithholdingAgentNo(withholdingAgentVo.getWithholdingAgentNo());
            }
            if(CollectionUtils.isNotEmpty(insertList)){
                excessCalculationAmountDetailMapper.insertVoList(insertList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                excessCalculationAmountDetailMapper.updateVoList(updateList);
            }
        }
        if (withholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex())){
            return withholdingAgentMapper.updateWithholdingAgent(withholdingAgentVo);
        }
        return withholdingAgentMapper.updateWithholdingAgentById(withholdingAgentVo);
    }

    @Override
    public int addWithholdingAgent(WithholdingAgentVo withholdingAgentVo) {
        String withholdingAgentNo = null;

        //大户
        withholdingAgentVo.setCreateTime(new Date());
        if (withholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex())) {
            //扣缴义务人编号
            withholdingAgentNo = "D" + withholdingAgentVo.getOrgCode();
            //单立户
            WithholdingAgentVo finallyWithholdingAgentVo = getFinallyWithholdingAgentVo(withholdingAgentNo, withholdingAgentVo);
            insertExcessCalculationAmountDetailList(finallyWithholdingAgentVo);
            return withholdingAgentMapper.insertWithholdingAgent(finallyWithholdingAgentVo);
        } else if (withholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE2.getIndex())) {
            //CustomerVo refCusIdByCustName = customerMapper.getRefCusIdByCustName(withholdingAgentVo.getWithholdingAgentName());
            if (Objects.isNull(withholdingAgentVo.getRefCustId())){
                withholdingAgentNo = "C" + withholdingAgentVo.getPayPlace();
            }else {
                withholdingAgentNo = "C" + withholdingAgentVo.getRefCustId() + withholdingAgentVo.getPayPlace();
            }

            Long orgCode = withholdingAgentVo.getRefCustId();
            String relCustId = withholdingAgentVo.getOrgCode();
            withholdingAgentVo.setRefCustId(Long.valueOf(relCustId));
            withholdingAgentVo.setOrgCode(String.valueOf(orgCode));
            //供应商
        } else if (withholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE3.getIndex())) {
            withholdingAgentNo = "S" + withholdingAgentVo.getOrgCode() + withholdingAgentVo.getPayPlace();
        }
        WithholdingAgentVo finallyWithholdingAgentVo = getFinallyWithholdingAgentVo(withholdingAgentNo, withholdingAgentVo);
        insertExcessCalculationAmountDetailList(finallyWithholdingAgentVo);
        return withholdingAgentMapper.addWithholdingAgent(finallyWithholdingAgentVo);
    }
    public WithholdingAgentVo getFinallyWithholdingAgentVo(String withholdingAgentNo,WithholdingAgentVo withholdingAgentVo){
        List<WithholdingAgentVo> byNamelist = withholdingAgentMapper.getCountByNameAndPayPlace(withholdingAgentVo);
        List<WithholdingAgentVo> byOrgCodelist = withholdingAgentMapper.getCountByOrgCodeAndPayPlace(withholdingAgentVo);
        if(CollectionUtils.isNotEmpty(byNamelist)||CollectionUtils.isNotEmpty(byOrgCodelist)){
            String withholdingAgentSuffixNo = sequenceService.getWithholdingAgentSuffixNo();
            withholdingAgentNo+=withholdingAgentSuffixNo;
            withholdingAgentVo.setWithholdingAgentName(withholdingAgentVo.getWithholdingAgentName()+withholdingAgentSuffixNo);
        }
        withholdingAgentVo.setWithholdingAgentNo(withholdingAgentNo);
        return withholdingAgentVo;
    }
    public void insertExcessCalculationAmountDetailList(WithholdingAgentVo withholdingAgentVo){
        List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList = withholdingAgentVo.getExcessCalculationAmountDetailVoList();
        if(CollectionUtils.isNotEmpty(excessCalculationAmountDetailVoList)){
            for (ExcessCalculationAmountDetailVo excessCalculationAmountDetailVo:excessCalculationAmountDetailVoList) {
                excessCalculationAmountDetailVo.setCreator(withholdingAgentVo.getCreator());
                excessCalculationAmountDetailVo.setWithholdingAgentNo(withholdingAgentVo.getWithholdingAgentNo());
            }
            excessCalculationAmountDetailMapper.insertVoList(excessCalculationAmountDetailVoList);
        }
    }

    @Override
    public WithholdingAgentVo getWithholdingAgentById(Long id) {
        WithholdingAgentVo withholdingAgentById = withholdingAgentMapper.getWithholdingAgentById(id);
        List<ExcessCalculationAmountDetailVo> excessCalculationAmountDetailVoList =
                excessCalculationAmountDetailMapper.getByWithholdingAgentNoList(Collections.singletonList(withholdingAgentById.getWithholdingAgentNo()));
        withholdingAgentById.setExcessCalculationAmountDetailVoList(excessCalculationAmountDetailVoList);
        if (Objects.isNull(withholdingAgentById.getRefCustId())){
            return withholdingAgentById;
        }
        Customer byId = customerMapper.findById(withholdingAgentById.getRefCustId());
        withholdingAgentById.setRefCustName(byId.getCustName());
        return withholdingAgentById;
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingAgent(WithholdingAgentVo withholdingAgentVo) {
        return withholdingAgentMapper.getWithholdingAgent(withholdingAgentVo);
    }

    @Override
    public List<ExportWithholdingAgentVo> exportWithholdingAgent(WithholdingAgentVo withholdingAgentVo) {
        List<ExportWithholdingAgentVo> withholdingAgentVos = withholdingAgentMapper.exportWithholdingAgentList(withholdingAgentVo);
        Map<String, OrgVo> orgVoMap = iOrgnizationResourceWrapperService.findAllCompany().stream().collect(Collectors.toMap(OrgVo::getOrgCode, Function.identity()));
        List<DisabilityGoldRateVo> disabilityGoldRateVos = disabilityGoldRateWrapperService.selectAll();
        Map<String, List<DisabilityGoldRateVo>> dgrMap = disabilityGoldRateVos.stream().collect(Collectors.groupingBy(DisabilityGoldRateVo::getCityCode));
        //所有的用户名
        Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
        Set<String> strings = allUserMap.keySet();
        ArrayList<ExportWithholdingAgentVo> exportWithholdingAgentVos = new ArrayList<>();
        for (ExportWithholdingAgentVo withholdingAgent : withholdingAgentVos) {
            ExportWithholdingAgentVo exportWithholdingAgentVo = new ExportWithholdingAgentVo();
            BeanUtils.copyProperties(withholdingAgent, exportWithholdingAgentVo);
            //转换扣缴义务人类型类型
            String name = WithholdingAgentEnum.WithholdingAgentTypeEnum.getName(Integer.valueOf(withholdingAgent.getWithholdingAgentType()));
            exportWithholdingAgentVo.setWithholdingAgentType(name);
                exportWithholdingAgentVo.setCreator(allUserMap.get(withholdingAgent.getCreator()));
                if (StringUtils.isNotEmpty(withholdingAgent.getUpdater())) {
                    exportWithholdingAgentVo.setUpdater(allUserMap.get(withholdingAgent.getUpdater()));
                }
                if (StringUtils.isNotEmpty(withholdingAgent.getAccounting())){
                    if (StringUtils.isNotEmpty(allUserMap.get(withholdingAgent.getAccounting()))){
                        exportWithholdingAgentVo.setAccounting(withholdingAgent.getAccounting());
                    }

                }
                if (StringUtils.isNotEmpty(withholdingAgent.getPayFinance())){
                    if (StringUtils.isNotEmpty(allUserMap.get(withholdingAgent.getPayFinance()))){
                        exportWithholdingAgentVo.setPayFinance(allUserMap.get(withholdingAgent.getPayFinance()));
                    }

                }

            if (!Objects.isNull(withholdingAgent.getPeerCardIssuance())){
                exportWithholdingAgentVo.setPeerCardIssuanceS(Objects.equals(1,withholdingAgent.getPeerCardIssuance())?"是":"否");
            }
            //转换城市
            String payPlace = iAreaResourceWrapperService.getAllCity(withholdingAgent.getPayPlace());
            exportWithholdingAgentVo.setPayPlace(payPlace);
            if (exportWithholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE2.getName())) {
                if (exportWithholdingAgentVo.getRefCustId()==null){
                    String custNameByrefCustId = customerMapper.findCustNameByrefCustId(exportWithholdingAgentVo.getOrgCode());
                    exportWithholdingAgentVo.setOwnCompany(custNameByrefCustId);
                }else {
                    String allCustomerById = customerMapper.findCustNameByrefCustId(String.valueOf(exportWithholdingAgentVo.getRefCustId()));
                    exportWithholdingAgentVo.setOwnCompany(allCustomerById);
                }

            }
            if (exportWithholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getName())) {
                if (orgVoMap.containsKey(withholdingAgent.getOrgCode())){
                    OrgVo orgVo = orgVoMap.get(withholdingAgent.getOrgCode());
                    exportWithholdingAgentVo.setOwnCompany(orgVo.getOrgName());
                    exportWithholdingAgentVo.setOpenFlagStr(BooleanTypeEnum.getName(orgVo.getOpenFlag()));
                }
            }
            if (exportWithholdingAgentVo.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE3.getName())) {
                SupplierVo supplierInfoById = supplierMapper.getSupplierInfoById(Long.valueOf(withholdingAgent.getOrgCode()));
                if (supplierInfoById!=null){
                    exportWithholdingAgentVo.setOwnCompany(supplierInfoById.getSupplierName());
                }

            }
            List<DisabilityGoldRateVo> disabilityVos = dgrMap.get(withholdingAgent.getPayPlace());
            if (CollectionUtils.isNotEmpty(disabilityVos)) {
                for (DisabilityGoldRateVo disabilityVo : disabilityVos) {
                    if (Objects.equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex(),disabilityVo.getType())&&Objects.equals(withholdingAgent.getWithholdingAgentType(),disabilityVo.getType().toString())){
                        exportWithholdingAgentVo.setDisabilityGoldRate(disabilityVo.getRate().multiply(new BigDecimal(100)));
                        exportWithholdingAgentVo.setDisabilityGoldRateStr(exportWithholdingAgentVo.getDisabilityGoldRate()+"%");
                    }else if (Objects.equals(withholdingAgent.getWithholdingAgentType(),disabilityVo.getType().toString())&&Objects.equals(withholdingAgent.getOrgCode(),disabilityVo.getOrgCode())){
                        if(StringUtils.isBlank(disabilityVo.getWithholdingAgentNo())||disabilityVo.getWithholdingAgentNo().equals(withholdingAgent.getWithholdingAgentNo())){
                            exportWithholdingAgentVo.setDisabilityGoldRate(disabilityVo.getRate().multiply(new BigDecimal(100)));
                            exportWithholdingAgentVo.setDisabilityGoldRateStr(exportWithholdingAgentVo.getDisabilityGoldRate()+"%");
                        }
                    }
                }
            }
            exportWithholdingAgentVos.add(exportWithholdingAgentVo);

        }
        return exportWithholdingAgentVos;
    }

    @Override
    public List<SupplierVo> getAllSupplierByType() {
        return supplierMapper.getAllSupplierByType();
    }

    @Override
    public List<CustomerVo> findAllCustomers() {
        return customerMapper.findAllCustomer();
    }

    @Override
    public List<CustomerVo> findAllCustomerById(Long custId) {
        return customerMapper.findAllCustomerById(custId);
    }

    @Override
    public int getCount(WithholdingAgentVo withholdingAgentVo) {
        return withholdingAgentMapper.getCount(withholdingAgentVo);
    }
    @Override
    public List<WithholdingAgentVo> getCountByNameAndPayPlace(WithholdingAgentVo withholdingAgentVo) {
        return withholdingAgentMapper.getCountByNameAndPayPlace(withholdingAgentVo);
    }

    @Override
    public String findCustNameByCustNo(String custNo) {
        return customerMapper.findCustNameByCustNo(custNo);
    }

    @Override
    public String findCustNameByrefCustId(String refCustId) {
        return customerMapper.findCustNameByrefCustId(refCustId);
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingAgentNoSelect(Integer withholdingAgentType) {
        return withholdingAgentMapper.getWithholdingAgentNoSelect(withholdingAgentType);
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingAgentNoAndPayPlace() {
        Map<Integer, String> cityCodeNameMap = iAreaResourceWrapperService.getCityCodeNameMap();
        List<WithholdingAgentVo> withholdingAgentNoAndPayPlace = withholdingAgentMapper.getWithholdingAgentNoAndPayPlace();
        List<String> allOpenedCompanyList = iOrgnizationResourceWrapperService.findAllOpenedCompany().stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
        withholdingAgentNoAndPayPlace = withholdingAgentNoAndPayPlace.stream().filter(w -> !Objects.equals(w.getWithholdingAgentType(), WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE1.getIndex()) ||
                allOpenedCompanyList.contains(w.getOrgCode())).collect(Collectors.toList());
        for (WithholdingAgentVo withholdingAgentVo : withholdingAgentNoAndPayPlace) {
            if (StringUtils.isNotBlank(withholdingAgentVo.getPayPlace())) {
                withholdingAgentVo.setPayPlaceName(cityCodeNameMap.get(Integer.parseInt(withholdingAgentVo.getPayPlace())));
            }
        }
        return withholdingAgentNoAndPayPlace;
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingAgentListByNoSet(Set<String> withholdingAgentNoSet) {
        if (CollectionUtils.isEmpty(withholdingAgentNoSet))
            return Lists.newArrayList();
        return withholdingAgentMapper.getWithholdingAgentListByNoSet(withholdingAgentNoSet);
    }

    @Override
    public List<WithholdingAgentVo> getWithholdingDataByCityCodeAndType(String cityCode, Integer type) {
        return withholdingAgentMapper.getWithholdingDataByCityCodeAndType(cityCode, type);
    }

    @Override
    public WithholdingAgentVo getWithholdingNameByOrgCode(String orgCode,String cityCode,String withholdingAgentNo) {
        List<WithholdingAgentVo> withholdingNameByOrgCode = withholdingAgentMapper.getWithholdingNameByOrgCode(orgCode, cityCode,withholdingAgentNo);
        List<WithholdingAgentVo> list = withholdingNameByOrgCode.stream().filter(c -> c.getRefCustId() == null).collect(Collectors.toList());
        WithholdingAgentVo withholdingAgentVo = new WithholdingAgentVo();
        if (CollectionUtils.isNotEmpty(list)){
            withholdingAgentVo = list.get(0);
        }else {
            if (CollectionUtils.isNotEmpty(withholdingNameByOrgCode)){
                withholdingAgentVo = withholdingNameByOrgCode.get(0);
            }

        }
        return withholdingAgentVo;
    }

    @Override
    public List<ExcessCalculationAmountDetailVo> getExcessCalculationAmountDetail(String withholdingAgentNo) {
        return excessCalculationAmountDetailMapper.getByWithholdingAgentNoList(Collections.singletonList(withholdingAgentNo));
    }
}
