<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
            http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/tx
            http://www.springframework.org/schema/tx/spring-tx.xsd
            http://www.springframework.org/schema/context
            http://www.springframework.org/schema/context/spring-context.xsd">

   <bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory" destroy-method="destroy">
		<property name="hostName" value="${redishost}" />
		<property name="port" value="${redisport}"/>
		<property name="password" value="${redispwd}"/>
		<property name="poolConfig" ref="jedisPoolConfig"/>
		<property name="usePool" value="${usePool}"/>
	</bean>
	
    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
    	<property name="maxTotal" value="${maxTotal}" />
    	<property name="maxIdle" value="${redismaxIdle}"/>
    	<property name="minIdle" value="${redisminIdle}"/>
    	<property name="maxWaitMillis" value="${maxWaitMillis}"/>
    	<property name="testOnBorrow" value="${testOnBorrow}"/>
    </bean>

	<!--redis操作模版,使用该对象可以操作redis  -->
	<bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate" >
		<property name="connectionFactory" ref="jedisConnectionFactory" />
		<!--如果不配置Serializer，那么存储的时候缺省使用String，如果用User类型存储，那么会提示错误User can't cast to String！！  -->
		<property name="keySerializer" >
			<bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />
		</property>
		<property name="valueSerializer" >
			<bean class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer" />
		</property>
		<property name="hashKeySerializer">
			<bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
		</property>
		<property name="hashValueSerializer">
			<bean class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>
		</property>
		<!--开启事务  -->
		<property name="enableTransactionSupport" value="true"></property>
	</bean >



    <bean id="stringRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
    	<constructor-arg index="0" ref="jedisConnectionFactory" />
    </bean>

	<!--自定义redis工具类,在需要缓存的地方注入此类  -->
	<bean id="redisUtil" class="com.reon.hr.sp.base.util.RedisUtil">
		<property name="redisTemplate" ref="redisTemplate" />
	</bean>

</beans>
