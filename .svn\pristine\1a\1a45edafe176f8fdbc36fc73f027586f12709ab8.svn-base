/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/5/18
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 <AUTHOR>
 @version 1.0 */
@Getter
public enum ReceiveMonthTypeAndCollectFreqEnum {

	TWO_MONTH_ONE(2, 1, Arrays.asList(1, 3, 5, 7, 9, 11)),
	TWO_MONTH_TWO(2, 2, Arrays.asList(2, 4, 6, 8, 10, 12)),

	THREE_MONTH_ONE(3, 1, Arrays.asList(1, 4, 7, 10)),
	THREE_MONTH_TWO(3, 2, Arrays.asList(2, 5, 8, 11)),
	THREE_MONTH_THREE(3, 3, Arrays.asList(3, 6, 9, 12)),

	HALF_YEAR_MONTH_ONE(4, 1, Lists.newArrayList(1, 7)),
	HALF_YEAR_MONTH_TWO(4, 2, Lists.newArrayList(2, 8)),
	HALF_YEAR_MONTH_THREE(4, 3, Lists.newArrayList(3, 9)),
	HALF_YEAR_MONTH_FOUR(4, 4, Lists.newArrayList(4, 10)),
	HALF_YEAR_MONTH_FIVE(4, 5, Lists.newArrayList(5, 11)),
	HALF_YEAR_MONTH_SIX(4, 6, Arrays.asList(6, 12));

	private Integer receiveMonthType;
	private Integer collectFreq;
	private List<Integer> chargeMonthList;

	private ReceiveMonthTypeAndCollectFreqEnum(Integer receiveMonthType, Integer collectFreq, List<Integer> chargeMonthList) {
		this.receiveMonthType = receiveMonthType;
		this.collectFreq = collectFreq;
		this.chargeMonthList = Lists.newArrayList(chargeMonthList);
	}

	public static List<Integer> getListByCode(Integer receiveMonthType, Integer collectFreq) {
		List<Integer> list = Lists.newArrayList();
		if (receiveMonthType != null && collectFreq != null) {
			for (ReceiveMonthTypeAndCollectFreqEnum value : ReceiveMonthTypeAndCollectFreqEnum.values()) {
				if (value.getReceiveMonthType().equals(receiveMonthType)&&value.getCollectFreq().equals(collectFreq)) {
					list = value.getChargeMonthList();
				}
			}
		}
		return list;
	}
}
