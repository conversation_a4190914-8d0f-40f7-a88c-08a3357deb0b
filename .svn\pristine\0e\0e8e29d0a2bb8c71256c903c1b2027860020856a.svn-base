var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        tableSelect = layui.tableSelect,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    // 查询派遣员工数据
    table.render({
        id: 'sendStaffQueryQueryTable',
        elem: '#sendStaffQueryQueryTable',
        data: [],
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        limit: 50,
        height: 495,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: '', title: '员工姓名', align: 'center', width: "10%", fixed: 'left'},
            {
                field: '', title: '证件类型', fixed: 'left', align: 'center', width: "10%"
            },
            {
                field: '', title: '证件编号', align: 'center', width: "10%"
            },
            {
                field: '', title: '客户名称', align: 'center', width: "10%"
            },
            {
                field: '', title: '合同类型', align: 'center', width: "10%"
            },
            {
                field: '', title: '劳动报酬', align: 'center', width: "10%"
            },
            {
                field: '', title: '社保基数', align: 'center', width: "10%"
            },
            {
                field: '', title: '公积金基数', align: 'center', width: "10%"
            },
            {
                field: '', title: '入职时间', align: 'center', width: "17%"
            },
        ]],
        done: function (res) {
            ML.hideNoAuth();
        }
    });


    form.on('submit(btnQuery)', function (data) {
        table.reload('sendStaffQueryQueryTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });


});