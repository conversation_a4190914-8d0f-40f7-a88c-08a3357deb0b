package com.reon.hr.api.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InsuTempCfgRecordVo implements Serializable {
    private Long id;

    private Long custId;

    private String custName;

    /**
     * 城市
     */
    private Integer cityCode;



    private Integer receivingType;

    /**
     * 接单方
     */
    private String receiving;
    private String receivingStr;

    /**
     * 合同号
     */
    private String contractNo;

    private String contractName;

    private Integer contractType;



    /**
     * 备案内容
     */
    private String remark;

    /**
     * 文件
     */
    private String fileId;
    private List<String> fileIdList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改人
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private String delFlag;


    private Integer limit;
    private Integer page;

}
