/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/12/23
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.dubbo.service.rpc.impl.salary.employee;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryEmployeeBankCardWrapperService;
import com.reon.hr.api.customer.vo.commInsurOrder.EmpCardInfoVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeBankCardVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.sp.customer.service.employee.salary.employee.ISalaryEmployeeBankCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalaryEmployeeBankCardWrapperServiceImpl
 *
 * @date 2020/12/23 14:28
 */
@Service("salaryEmployeeBankCardWrapperDubboService")
public class SalaryEmployeeBankCardWrapperServiceImpl implements ISalaryEmployeeBankCardWrapperService {

    @Autowired
    private ISalaryEmployeeBankCardService iSalaryEmployeeBankCardService;

    @Override
    public Page<SalaryEmployeeBankCardVo> getSalaryEmployeeBankCardListPage(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo, Integer page, Integer limit) {
        Page<SalaryEmployeeBankCardVo> pages = new Page<> (page, limit);
        List<SalaryEmployeeBankCardVo> salaryEmployeeBankCardVoList = iSalaryEmployeeBankCardService.getSalaryEmployeeBankCardListPage (pages, salaryEmployeeBankCardVo);
        pages.setRecords (salaryEmployeeBankCardVoList);
        return pages;
    }

    @Override
    public List<SalaryEmployeeBankCardVo> getSalaryEmployeeBankCardList(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo) {
        return iSalaryEmployeeBankCardService.getSalaryEmployeeBankCardList(salaryEmployeeBankCardVo);
    }

    @Override
    public SalaryEmployeeBankCardVo getSalaryEmployeeBankCard(Long cardId) {
        return iSalaryEmployeeBankCardService.getSalaryEmployeeBankCard(cardId);
    }

    @Override
    public int saveAndUpdate(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo) {
        return iSalaryEmployeeBankCardService.saveAndUpdate (salaryEmployeeBankCardVo);
    }
    @Override
    public int updateByCardNoAndEmpId(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo) {
        return iSalaryEmployeeBankCardService.updateByCardNoAndEmpId (salaryEmployeeBankCardVo);
    }

    @Override
    public EmpCardInfoVo selectById(Long cardId) {
        return iSalaryEmployeeBankCardService.selectById (cardId);
    }


    @Override
    public String verifyBankCard(String cardNo, Long empId,int code) {
        return iSalaryEmployeeBankCardService.verifyBankCard (cardNo, empId,code);
    }
    @Override
    public SalaryEmployeeBankCardVo getSalaryEmployeeBankCardVo(String cardNo, Long empId,int code) {
        return iSalaryEmployeeBankCardService.getSalaryEmployeeBankCardVo (cardNo, empId,code);
    }

    @Override
    public SalaryInfoVo getSalaryInfoVo(Long empId, Long payId) {
        return iSalaryEmployeeBankCardService.getSalaryInfoVo (empId,payId);
    }

    @Override
    public Integer getPureSalaryBankCardCount(String contractNo, String cardNo) {
        return iSalaryEmployeeBankCardService.getPureSalaryBankCardCount(contractNo,cardNo);
    }

    @Override
    public Page<SalaryEmployeeBankCardVo> getNonPurePayrollCreditBankCardListPage(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo, Integer page, Integer limit) {
        Page<SalaryEmployeeBankCardVo> pages = new Page<> (page, limit);
        List<SalaryEmployeeBankCardVo> nonPurePayrollCreditBankCardList = iSalaryEmployeeBankCardService.getNonPurePayrollCreditBankCardListPage(salaryEmployeeBankCardVo, pages);
        pages.setRecords(nonPurePayrollCreditBankCardList);
        return pages;
    }

    @Override
    public List<SalaryEmployeeBankCardVo> getNonPurePayrollCreditBankCardList(SalaryEmployeeBankCardVo salaryEmployeeBankCardVo) {
        return iSalaryEmployeeBankCardService.getNonPurePayrollCreditBankCardList(salaryEmployeeBankCardVo);
    }

    @Override
    public SalaryEmployeeBankCardVo getNonPurePayrollCreditBankCard(Long cardId) {
        return iSalaryEmployeeBankCardService.getNonPurePayrollCreditBankCard(cardId);
    }

    @Override
    public EmpCardInfoVo getEmployeeBankCard(Long employeeId) {
        return iSalaryEmployeeBankCardService.getEmployeeBankCard(employeeId);
    }

    @Override
    public List<EmpCardInfoVo> getEmployeeBankCards(List<String> employeeId) {
        return iSalaryEmployeeBankCardService.getEmployeeBankCards(employeeId);
    }
}
