package com.reon.hr.api.customer.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Table;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.vo.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ICustomerWrapperService {

    /**
     * 检查客户名字是否相同
     * @param customerName 客户名称
     * @return boolean
     */
    boolean checkCustomerName(String customerName);
    /**
     * 检查客户名字是否相同
     * @param customerName 客户名称
     * @return boolean
     */
    String checkCustomerName1(Long id);

    List<String> checkCustomerName2(Long customerName);

    /**
     * 找到批准信息
     *
     * @param customerName 客户名称
     * @return int
     */
    int findApprovalInfo(Long customerName);

    /**
     * 分页获取数据
     * @param customerPageVo 条件
     * @return Page
     */
    Page<CustomerPageVo> findCustomerByPage(CustomerPageVo  customerPageVo);

    /**
     * 获得批准者页面
     *
     * @param customerPageVo         客户页签证官
     * @param userOrgPositionDtoList 用户组织位置dto列表
     * @return {@link Page}<{@link CustomerPageVo}>
     */
    Page<CustomerPageVo> getApproverPage(CustomerPageVo customerPageVo, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 获得批准状态
     *
     * @param vo 签证官
     * @return {@link CustomerRefVo}
     */
    List<CustomerRefVo> getApprovalStatus(List<CustomerRefVo> vo);

    /**
     * 保存客户数据
     * @param customerVo customerVo
     * @return boolean
     */
    long saveCustomer(CustomerVo customerVo);

    /**
     * 批量保存客户
     *
     * @param customerVos 客户vos
     * @return boolean
     */
    boolean batchSaveCustomer(List<CustomerVo> customerVos);

    /**
     * 新增关联公司客户审批信息
     * @param customerApprovalVo
     * @return
     */
    long saveAuditCustomer(CustomerApprovalVo customerApprovalVo);

    /**
     * 新增关联公司统一接口(防止分布式事务问题)
     *
     * @param customerVo
     * @return
     */
    CustomerRefVo relUnifyCustomer(CustomerVo customerVo, String orgCode, String posCode, String loginName);

    /**
     * rel统一
     * 新增关联公司统一接口(防止分布式事务问题)
     *
     * @param customerVo 客户签证官
     * @param orgCode    组织代码
     * @param posCode    pos代码
     * @param loginName  登录名
     */
    void relUnify(CustomerVo customerVo, String orgCode, String posCode, String loginName);

    Integer findByCustomerNo(String customerNo);

    boolean editCustomer(CustomerVo customerVo);

    boolean auditCustInfo(CustomerApprovalVo customerApprovalVo);

    /**
     * 批量更新
     *
     * @param vo 签证官
     * @return int
     */
    boolean batchUpdate(CustomerRefVo vo);

    boolean updateByCustRefId(CustomerRefVo customerVo);

    CustomerVo findById(Long id);
    CustomerInvoiceVo findCropKindByCustId(Long custId);

    CustomerRefVo getContractByRefId(Long id);

    String findRejectionById(Long custRefId);

    boolean checkCustomerNo(String customerNo);

    List<CustomerVo> findAllCustomer();

    /**
     * 查找所有客户及其关联客户
     *
     * @return {@link List}<{@link CustomerRelVo}>
     */
    List<CustomerRelVo> findAllCustomerWithRel();

    /**
     * 重组所有客户关联vo导出对象
     *
     * @return {@link List}<{@link CustomerRelVo}>
     */
    List<CustomerRelVo> reassembleAllCustomerWithRel();

    Page<CustomerVo> getCustomerForEmployeeContract(Integer page, Integer limit, CustomerVo customerVo);

    List<CustomerVo> getCustomerListByIds(List<Long> custIdList);
    Map<Long,String> getCustomerMapByIds(List<Long> custIdList);

    List<CustomerRefVo> getCustomerListByRefCustId(Long refCustId);

    List<CustomerVo> getCustomerAndRelevanceCustomerById(Long id);

    List<CustomerVo> getListByCityCode(Integer cityCode, List<OrgPositionDto> userOrgPositionDtoList);

    List<CustContactorVo> getCustContactorListByCustId(CustContactorVo custContactorVo);

    Integer batchSaveCustContactor(List<CustContactorVo> custContactorVoList, String loginName);

    Integer editCustContactor(CustContactorVo custContactorVo, String loginName);

    Page<CustomerVo> selectCustNameByGroupIdAndName(String name, Long groupId, Integer page, Integer limit);

    /**
     * 根据集团获取客户id集合
     *
     * @param name 名字
     * @return {@link List}<{@link String}>
     */
    List<CustomerVo> getCustIdsByGroup(String name);

    List<CustomerVo> getCustNameByCustIdList(List<Long> custIdList);
    String getOrderNoByQuotatioNo(String quotationNo);

    List<CustomerVo> getCustomerRelevanceByCustId(Long custId);

	List<CustomerVo> selectOnlyCustomerList(List<Long> custIdList);

    void editCustomerNameById(CustomerOperateLogVo customerOperateLogVo);

    List<CustomerOperateLogVo> getEditNameLogPage(Long custId);


    List<Long> getCustIdsByGroupId(Long custGroup, Long custId);


    List<CustomerVo> findAllCustomerName();

    Map<Long, Set<Long>> getCustGroupIdAndCustIdList();

    Map<Long,String> getGroupNameByCustIdList(List<Long> custIdList,String groupName);

    List<Long> selectIdByCustGroupId( List<Long> groupIdList);

    boolean getGroupNameByCustId(Long custId);

    List<String>getPidByPidListAndTableName(List<String>pidList,String procType,String tableName,String delFlag);

    boolean getUpdateNameByCustId(Long custId,String loginName);

    List<Long> getCustIdByCustName(String custName);

    List<Long> getCustIdByCustNameNotLike(String custName);
}