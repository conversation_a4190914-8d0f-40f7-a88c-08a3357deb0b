package com.reon.hr.sp.customer.dao.contract;

import com.reon.hr.api.customer.vo.ContractContentInfoVo;
import com.reon.hr.sp.customer.entity.contract.ContractContentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractContentInfoMapper {
    Integer deleteByContractNo(@Param("contractNo") String contractNo);

	Integer insert(ContractContentInfo contractContentInfo);

    ContractContentInfo getByContractNo(@Param("contractNo") String contractNo);

	Integer insertSelective(ContractContentInfo contractContentInfo);

	Integer updateByPrimaryKey(ContractContentInfo contractContentInfo);

	List<ContractContentInfo> getListByContractNo(String contractNo);

	ContractContentInfoVo getEditItemData(@Param("contractNo") String contractNo);
}
