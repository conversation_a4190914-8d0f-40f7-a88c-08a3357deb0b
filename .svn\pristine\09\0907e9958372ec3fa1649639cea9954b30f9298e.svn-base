<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2020/12/24
  Time: 10:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>${title}</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-onlySelf {
            width: 125px;
        }
        .layui-form-checkbox{
            margin-top: unset!important;
            min-height: unset!important;
        }
        .layui-tab-card {
            border-style: unset;
            box-shadow: unset;
        }
        .layui-card-header .layui-icon {
            right:  unset!important;;
        }

    </style>
</head>
<body>
<div class="layui-tab layui-tab-card" style="height: 70%">

                    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
                        <input type="hidden" id="id" name="id" value="${id}">
                        <input type="hidden" id="optType" name="optType" value="${optType}">
                        <input type="hidden" id="cityCodeCache"   value="${cityCodeCache}">

                        <%--隐藏域--%>
                        <div class="layui-form-item">

                            <div class="layui-inline">
                                <label class="layui-form-label" style="font-weight:800" title="城市">城市</label>
                                <div class="layui-input-inline" style="width: 50%;">
                                    <select class="layui-select" lay-search name="cityCode" id="cityCode" lay-filter="cityFilter" AREA_TYPE >
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label title="备注:" class="layui-form-label layui-elip layui-onlySelf">备注:</label>
                                <div class="layui-input-inline">
                                    <textarea type="text" class="layui-input layui-input-disposable auto-trim" id="remark"
                                           name="remark"   placeholder="请输入"  >${remark}</textarea>
                                </div>
                            </div>
                            <div class="layui-inline" style="float: right;padding:10px">
                                <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="save" lay-filter="save" lay-submit="">保存</button>
                                <button class="layui-btn layuiadmin-btn-list delAttr" type="button"  id="close" lay-filter="closeFilter">取消</button>
                            </div>

                        </div>
                    </form>
                </div>




<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" >
    var ctx = ML.contextPath;
    layui.config({
        base: ctx + "/js/"
    }).use(['jquery', 'form', 'layer', 'element', 'table' ], function () {
        var table = layui.table,
            $ = layui.$,
            form = layui.form,

            layer = layui.layer,
            layer = parent.layer === undefined ? layui.layer : parent.layer;


        form.on('submit(save)', function (data) {
            saveForm('save', data);
        });

        function saveForm(type, data) {
            ML.layuiButtonDisabled($('#' + type));
            $.ajax({
                url: ML.contextPath + "/socialSysMandatory/saveOrUpdate",
                type: 'POST',
                dataType: 'json',
                data: {'paramData': JSON.stringify(data.field)},
                success: function (result) {
                    layer.closeAll('iframe');
                    layer.msg(result.msg);
                },
                error: function (data) {
                    layer.msg("系统保存失败，请稍后重试!");
                    ML.layuiButtonDisabled($('#' + type), 'true');
                }
            });
        }
        $("#cityCode").val($("#cityCodeCache").val())
        form.render("select");
    });

</script>
</body>
</html>
