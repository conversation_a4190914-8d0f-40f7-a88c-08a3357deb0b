package com.reon.hr.sp.report.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IIndCategoryWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceRatioWrapperService;
import com.reon.hr.api.base.vo.InsuranceRatioVo;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IBillMonthWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.report.IBillReportWrapperService;
import com.reon.hr.api.customer.dto.exportData.ServiceNumPeopleReportDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.CustomerGroupWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IOrderInsuranceCfgWrapperService;
import com.reon.hr.api.customer.enums.CertType;
import com.reon.hr.api.customer.enums.EmployeeReportEnum;
import com.reon.hr.api.customer.enums.employee.EmployeeEntryDimissionStatus;
import com.reon.hr.api.customer.enums.employeeContract.WorkMethodEnum;
import com.reon.hr.api.customer.utils.RetirementAgeUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.ContractVo;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.EmployeeChangeReportExportVo;
import com.reon.hr.api.customer.vo.FinancialRequireVo;
import com.reon.hr.api.customer.vo.employee.EmpOrderTransferDetailVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.report.dubbo.service.rpc.IEmployeeReportWrapperService;
import com.reon.hr.api.report.enums.FinancialAccountsReceivableReportEnum;
import com.reon.hr.api.report.vo.*;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.sp.report.entity.ServiceNumPeopleReport;
import com.reon.hr.sp.report.service.report.IEmployeeReportService;
import com.reon.hr.sp.report.service.report.InsuranceBillComparisonReportService;
import com.reon.hr.sp.report.service.report.ServiceNumPeopleReportService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reon.hr.api.customer.utils.DateUtil.IntToDate;
import static com.reon.hr.api.customer.utils.DateUtil.getPrevMonthDate;
import static java.util.stream.Collectors.*;


@Service("employeeReportDubboService")
public class EmployeeReportWrapperServiceImpl implements IEmployeeReportWrapperService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IEmployeeReportService employeeReportService;
    @Autowired
    private InsuranceBillComparisonReportService insuranceBillComparisonReportService;
    @Autowired
    IEmployeeOrderWrapperService iEmployeeOrderWrapperService;
    @Autowired
    ISupplierWrapperService iSupplierWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService orgnizationWrapperService;
    @Autowired
    private IBillReportWrapperService iBillReportWrapperService;

    @Autowired
    private IUserWrapperService iUserWrapperService;

    @Autowired
    private IAreaResourceWrapperService areaResourceWrapperService;
    @Autowired
    IAreaResourceWrapperService iAreaResourceWrapperService;
    @Resource(name = "insuranceRatioDubboService")
    IInsuranceRatioWrapperService iInsuranceRatioWrapperService;
    @Autowired
    private IIndCategoryWrapperService categoryWrapperService;
    @Autowired
    private ServiceNumPeopleReportService serviceNumPeopleReportService;
    @Autowired
    private ICustomerWrapperService iCustomerWrapperService;
    @Autowired
    private IContractResourceWrapperService contractResourceWrapperService;
    @Autowired
    private CustomerGroupWrapperService customerGroupWrapperService;

    @Autowired
    private IOrderInsuranceCfgWrapperService iOrderInsuranceCfgWrapperService;

    @Resource
    private IBillMonthWrapperService iBillMonthWrapperService;
    @Resource
    private IInsuranceBillWrapperService insuranceBillWrapperService;

    @Override
    public List<EmployeeDimissionReportVo> getDimissionEmployeesReport(String applyTimeS, String applyTimeE, String custName, Integer distPlace, Integer cityCode, String receiving, String commissioner, Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList) {
        return employeeReportService.getDimissionEmployeesReport(applyTimeS, applyTimeE, custName, distPlace, cityCode, receiving, commissioner, accountFlag, groupName, userOrgPositionDtoList);
    }

    @Override
    public List<EmployeeEntryReportVo> getEntryEmployeesReport(String applyEntryTimeS, String applyEntryTimeE, String custName, Integer distPlace, Integer cityCode, String receiving) {
        return employeeReportService.getEntryEmployeesReport(applyEntryTimeS, applyEntryTimeE, custName, distPlace, cityCode, receiving);
    }


    @Override
    public List<EmployeeEntryReportVo> getEntryEmployeesReport1(String applyEntryTimeS, String applyEntryTimeE, String custName, Integer distPlace, Integer cityCode, String receiving, String commissioner, Integer accountFlag, String groupName, List<OrgPositionDto> userOrgPositionDtoList) {
        List<EmployeeEntryReportVo> entryEmployeesReport1 = employeeReportService.getEntryEmployeesReport1(applyEntryTimeS, applyEntryTimeE, custName, distPlace, cityCode, receiving, commissioner, accountFlag, groupName, userOrgPositionDtoList);
        List<String> conditions = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(entryEmployeesReport1)) {
            Map<String, String> categoryCodeName = categoryWrapperService.selectCategoryCodeNameMap();
            List<String> orderNoList = entryEmployeesReport1.stream().map(EmployeeEntryReportVo::getOrderNo).collect(Collectors.toList());
            //employeeReportService.getEntryEmployeesReport1方法里已经去判断过user了没必要再去查一遍
            //Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
            Map<Long, InsuranceRatioVo> insuranceRatioMap = iInsuranceRatioWrapperService.getInsuranceRatioMapUnlimited();
            Map<String, List<OrderInsuranceCfgVo>> insuranceCfgsMap = iEmployeeOrderWrapperService.getInsuranceCfgsMapByOrderListAndNowTime(orderNoList);
//        List<EmployeeEntryReportVo> entryEmployeesReport1 = Lists.newArrayList();
//        entryEmployeesReport1.forEach(item -> {
//            EmployeeEntryReportVo employeeEntryReportVo = new EmployeeEntryReportVo();
//            BeanUtils.copyProperties(item,employeeEntryReportVo);
//            entryEmployeesReport1.add(employeeEntryReportVo);
//        });
            Set<Long> systemExistsGroupRatioId = new HashSet<>();

            for (EmployeeEntryReportVo item : entryEmployeesReport1) {
                if (StringUtils.isNotBlank(item.getWorkMethod())){
                    item.setWorkMethod(WorkMethodEnum.getMsgByCode(Integer.valueOf(item.getWorkMethod())));
                }
                item.setAccountFlagName(item.getAccountFlag() == 1 ? "否" : "是");
                if (item.getCertType().equals(CertType.ID_CARD.getCode())) {
                    String certNo = item.getCertNo();
                    Date birthDayByIdentityCard = null;
                    try {
                        birthDayByIdentityCard = com.reon.hr.api.customer.utils.DateUtil.getBirthDayByIdentityCard(certNo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    String retirementAge = RetirementAgeUtil.getRetirementAge(certNo);
                    item.setRetireDate(retirementAge);
                    int age = com.reon.hr.api.customer.utils.DateUtil.getAge(birthDayByIdentityCard);
                    item.setAge(age);
                    String sex = certNo.substring(14, 17);
                    if (Integer.parseInt(sex) % 2 == 0) {
                        item.setSex("女");
                    } else {
                        item.setSex("男");
                    }
                }
                item.setCategoryCodeName(categoryCodeName.get(item.getCategoryCode()));
            /*if (StringUtils.isNotBlank(item.getPrjCs())) {
                String prjCs = allUserMap.get(item.getPrjCs());
                item.setPrjCs(prjCs);
            }
            if (StringUtils.isNotBlank(item.getRevCs())) {
                String revCs = allUserMap.get(item.getRevCs());
                item.setRevCs(revCs);
            }*/
//            Map<String,List<OrderInsuranceCfgVo>>
                //然后根据 产品code获取到当前order_no下的所有产品 ,需要进行判断是否有 重复的产品
                String orderNo = item.getOrderNo();
                List<OrderInsuranceCfgVo> insuranceCfgVoList = insuranceCfgsMap.get(orderNo);
//如果prodCode相同 取rev_start_month 收费起始月 大的那个数据
                HashMap<Integer, OrderInsuranceCfgVo> orderInsuranceCfgVoMap = Maps.newHashMap();
                if (insuranceCfgVoList != null) {
                    insuranceCfgVoList.forEach(insuranceItem -> {
                        Integer productCode = insuranceItem.getProductCode();
                        if (orderInsuranceCfgVoMap.get(productCode) == null) {
                            orderInsuranceCfgVoMap.put(productCode, insuranceItem);
                        } else {
                            if (insuranceItem.getRevStartMonth() > orderInsuranceCfgVoMap.get(productCode).getRevStartMonth()) {
                                orderInsuranceCfgVoMap.put(productCode, insuranceItem);
                            }
                        }
                    });
                    insuranceCfgVoList = Lists.newArrayList(orderInsuranceCfgVoMap.values())
                            .stream()
                            .sorted(Comparator.comparing(OrderInsuranceCfgVo::getProductCode))
                            .collect(Collectors.toList());

                    //实时计算个人金额
                    for (OrderInsuranceCfgVo orderInsuranceCfgVo : insuranceCfgVoList) {
                        InsuranceRatioVo insuranceRatioVo = insuranceRatioMap.get(orderInsuranceCfgVo.getGroupRatioId());
                        if (null != insuranceRatioVo) {
//                            if (orderInsuranceCfgVo.getIndAmt() != null && BigDecimal.ZERO.compareTo(orderInsuranceCfgVo.getIndAmt()) != 0) {
//                                orderInsuranceCfgVo.setIndAmt(CalculateUtil.calculateAmt(orderInsuranceCfgVo.getIndBase(), insuranceRatioVo.getIndRatio(), insuranceRatioVo.getIndlAdd(), insuranceRatioVo.getIndCalcMode(), insuranceRatioVo.getIndExactVal()));
//                            }
//                            if (orderInsuranceCfgVo.getComAmt() != null && BigDecimal.ZERO.compareTo(orderInsuranceCfgVo.getComAmt()) != 0) {
//                                orderInsuranceCfgVo.setComAmt(CalculateUtil.calculateAmt(orderInsuranceCfgVo.getComBase(), insuranceRatioVo.getComRatio(), insuranceRatioVo.getComAdd(), insuranceRatioVo.getComCalcMode(), insuranceRatioVo.getComExactVal()));
//                            }
                            CalculateArgs args = new CalculateArgs();
                            args.setComArgs(orderInsuranceCfgVo.getComBase(), insuranceRatioVo.getComRatio(), insuranceRatioVo.getComAdd(), insuranceRatioVo.getComCalcMode(), insuranceRatioVo.getComExactVal());
                            args.setIndArgs(orderInsuranceCfgVo.getIndBase(), insuranceRatioVo.getIndRatio(), insuranceRatioVo.getIndlAdd(), insuranceRatioVo.getIndCalcMode(), insuranceRatioVo.getIndExactVal());
                            args.setSpecialFlag(insuranceRatioVo.getSpecialFlag());
                            CalculateUtil.calculateAmt(args);
                            if (!(orderInsuranceCfgVo.getIndAmt() != null && BigDecimal.ZERO.compareTo(orderInsuranceCfgVo.getIndAmt()) != 0)) {
                                args.setIndAmt(BigDecimal.ZERO);
                            }
                            if (!(orderInsuranceCfgVo.getComAmt() != null && BigDecimal.ZERO.compareTo(orderInsuranceCfgVo.getComAmt()) != 0)) {
                                args.setComAmt(BigDecimal.ZERO);
                            }
                            orderInsuranceCfgVo.setComAmt(args.getComAmt());
                            orderInsuranceCfgVo.setIndAmt(args.getIndAmt());
                        } else {
                            systemExistsGroupRatioId.add(orderInsuranceCfgVo.getGroupRatioId());
                            orderInsuranceCfgVo.setIndAmt(BigDecimal.ZERO);
                            orderInsuranceCfgVo.setComAmt(BigDecimal.ZERO);
                        }
                    }
                }

                HashMap<Integer, OrderInsuranceCfgReportVo> insurances = new HashMap<>(16);
//          社保公积金个人总计
                BigDecimal indTotal = BigDecimal.ZERO;
//            社保公积金企业总计
                BigDecimal comTotal = BigDecimal.ZERO;
//列小计
                BigDecimal rowTotal = BigDecimal.ZERO;
                if (null != insuranceCfgVoList) {
                    for (int i = 0; i < insuranceCfgVoList.size(); i++) {
                        OrderInsuranceCfgReportVo orderInsuranceCfgReportVo = new OrderInsuranceCfgReportVo();
                        if (null != insuranceCfgVoList.get(i).getIndAmt()) {
                            indTotal = indTotal.add(insuranceCfgVoList.get(i).getIndAmt());
                        }
                        if (insuranceCfgVoList.get(i).getComAmt() != null) {
                            comTotal = comTotal.add(insuranceCfgVoList.get(i).getComAmt());
                        }
                        //获取总额  如果报错 就是产品的金额没有
                        if (insuranceCfgVoList.get(i).getComAmt() != null && insuranceCfgVoList.get(i).getIndAmt() != null) {
                            insuranceCfgVoList.get(i).setProdTotal(insuranceCfgVoList.get(i).getComAmt().add(insuranceCfgVoList.get(i).getIndAmt()));
                        }
                        //根据GroupRatioId 获取比率
                        InsuranceRatioVo insuranceRatio = insuranceRatioMap.get(insuranceCfgVoList.get(i).getGroupRatioId());
                        if (null != insuranceRatio) {
                            insuranceCfgVoList.get(i).setComRatio(insuranceRatio.getComRatio());
                            insuranceCfgVoList.get(i).setIndRatio(insuranceRatio.getIndRatio());
                        }
                        BeanUtils.copyProperties(insuranceCfgVoList.get(i), orderInsuranceCfgReportVo);
                        insurances.put(insuranceCfgVoList.get(i).getProductCode(), orderInsuranceCfgReportVo);
                    }
                }
//
                item.setIndTotal(indTotal);
                item.setComTotal(comTotal);

                rowTotal = rowTotal.add(indTotal).add(comTotal);
//            rowTotal = rowTotal.add(comTotal);
                if (null != item.getArchiveFee()) {
                    rowTotal = rowTotal.add(item.getArchiveFee());
                }
                if (null != item.getAmount()) {
                    rowTotal = rowTotal.add(item.getAmount());
                }
                if (null != item.getOneFee()) {
                    rowTotal = rowTotal.add(item.getOneFee());
                }

                item.setRowTotal(rowTotal);
                item.setInsurances(insurances);
            }
            log.info("in_group_ratio 表中缺失以下数据 : id= {}", systemExistsGroupRatioId);
        }
        if (CollectionUtils.isNotEmpty(conditions)) {
            return entryEmployeesReport1.stream().filter(vo -> !conditions.contains(vo.getOrderNo())).collect(Collectors.toList());
        }
        return entryEmployeesReport1;
    }

    @Override
    public Page<CostReport> getCostReport(CostReport vo) {
        return employeeReportService.getCostReport(vo);
    }

    @Override
    public Page<BillContractReport> getBillContractReport(BillContractReport vo) {
        return employeeReportService.getBillContractReport(vo);
    }

    @Override
    public Page<InsuranceBillComparisonDTO> getInsuranceBillComparisonListPage(Integer page, Integer limit, Map<String, Object> stringObjectMap) {
        return insuranceBillComparisonReportService.getInsuranceBillComparisonListPage(page, limit, stringObjectMap);
    }

    @Override
    public Object generationOfContrastData(String createTime, String loginName) {
        return insuranceBillComparisonReportService.generationOfContrastData(createTime, loginName);
    }

    @Override
    public List<InsuranceBillComparisonReportVo> getInsuranceBillComparisonListPageFromReport(InsuranceBillComparisonReportVo vo) {
        return insuranceBillComparisonReportService.getInsuranceBillComparisonListPageFromReport(vo);
    }

    @Override
    public List<String> getReportDate() {
        return insuranceBillComparisonReportService.getReportDate();
    }

    @Override
    public List<EmployeeOrderVo> trimesterNoEntryBill() {
        /**目前不用考虑订单转移的情况，先不用这个方法，这个方法还存在一种情况没有判断：产品转移后只有两个月的有效期，已经截止，第三个月本来就不应该进入账单。此时订单前两个月 和最后一个本来就不应该进入的账单月
         * 加起来 也是3个月没有进入账单。 事实是他只有两个月的有效期*/
        Set<String> orderNosFromBill = Sets.newHashSet();
        Set<String> orderNosFromTransfer = Sets.newHashSet();
        Integer preYearMonth = DateUtil.getYearMonthByCount(Integer.parseInt(DateUtil.formatDateToString(new Date(), "yyyyMM")), -3);
        List<Integer> monthBetween = DateUtil.getMonthBetween(String.valueOf(preYearMonth), String.valueOf(DateUtil.getCurrentYearMonth()));
        /**最近三个月内转移过的数据  将一个人最近三个月进行转移的全部订单查询 然后看这些订单的账单月是否存在最近三个月的账单月*/
        Map<Long, Set<String>> empAndOrderNosMap = Maps.newHashMap();
        Integer startMonth = Integer.valueOf((getPrevMonthDate(IntToDate(preYearMonth), -1).replace("-", "")));
        List<EmpOrderTransferDetailVo> empOrderTransferDetailVos = iEmployeeOrderWrapperService.selectEmpOrderTransferDetailsByMonths(startMonth);
        for (EmpOrderTransferDetailVo vo : empOrderTransferDetailVos) {
            Set<String> orderNos = empAndOrderNosMap.computeIfAbsent(vo.getEmpId(), v -> Sets.newHashSet());
            orderNos.add(vo.getNewOrderNo());
            orderNos.add(vo.getOrderNo());

        }
        List<String> transferOrderNos = empAndOrderNosMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, Set<Integer>> orderNoAndBillMonthMap = iBillReportWrapperService.selectOrderNoAndBillMonths(transferOrderNos);
        for (Long empId : empAndOrderNosMap.keySet()) {
            Set<String> orderNos = empAndOrderNosMap.get(empId);
            boolean flag = false;
            for (String orderNo : orderNos) {
                Set<Integer> billMonths = orderNoAndBillMonthMap.getOrDefault(orderNo, new HashSet<>());
                boolean anyMatch = billMonths.stream().anyMatch(monthBetween::contains);
                if (anyMatch) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                orderNosFromTransfer.addAll(orderNos);
            }
        }

        /**可能存在已经离职但是没有截止月的订单，需要再次过滤*/
        List<EmployeeOrderVo> orderVoByMonthsRange = iEmployeeOrderWrapperService.getEmployeeOrderVoByMonthsRange(null, preYearMonth, DateUtil.getCurrentYearMonth(), Lists.newArrayList(transferOrderNos));
        Map<Boolean, List<EmployeeOrderVo>> isTransferMap = orderVoByMonthsRange.stream().collect(Collectors.groupingBy(vo -> transferOrderNos.contains(vo.getOrderNo())));
        List<EmployeeOrderVo> nonTransferOrder = isTransferMap.get(false);
        List<EmployeeOrderVo> transferOrder = isTransferMap.get(true);
        nonTransferOrder = getNormalOrderNos(orderNosFromBill, monthBetween, nonTransferOrder);
        List<EmployeeOrderVo> normalEmpOrderVos = nonTransferOrder.stream().filter(vo -> !orderNosFromBill.contains(vo.getOrderNo())).collect(Collectors.toList());
        List<EmployeeOrderVo> transferEmpOrderVos = transferOrder.stream().filter(vo -> !orderNosFromTransfer.contains(vo.getOrderNo())).collect(Collectors.toList());
        normalEmpOrderVos.addAll(transferEmpOrderVos);
        return normalEmpOrderVos;

    }

    @Override
    public List<EmployeeOrderVo> trimesterNoEntryBillIgnoreTransfer(String orderNo) {
        /**记录已经进入账单的订单号*/
        Set<String> orderNosFromBill = Sets.newHashSet();
        /**最近三个月内转移过的数据  将一个人最近三个月进行转移的全部订单查询 然后看这些订单的账单月是否存在最近三个月的账单月*/
        Calendar now = Calendar.getInstance();
        now.add(Calendar.MONTH, -2);
        String preTime = DateUtil.formatDateToString(now.getTime(), DateUtil.DATE_FORMAT_LONG);
        Integer preYearMonth = Integer.parseInt(DateUtil.formatDateToString(now.getTime(), "yyyyMM"));
        List<Integer> monthBetween = DateUtil.getMonthBetween(String.valueOf(preYearMonth), String.valueOf(DateUtil.getCurrentYearMonth()));
        long s = System.currentTimeMillis();
        List<EmployeeOrderVo> nonTransferOrder = iEmployeeOrderWrapperService.getEmployeeOrderVoByMonthsRange(preTime, preYearMonth, DateUtil.getCurrentYearMonth(), null);
        long e = System.currentTimeMillis();
        log.info("=========================>查询订单执行时间：{}",e-s);
        nonTransferOrder = getNormalOrderNos(orderNosFromBill, monthBetween, nonTransferOrder);
        return nonTransferOrder.stream().filter(vo -> !orderNosFromBill.contains(vo.getOrderNo())).collect(Collectors.toList());
    }


    private List<EmployeeOrderVo> getNormalOrderNos(Set<String> orderNosFromBill, List<Integer> monthBetween, List<EmployeeOrderVo> nonTransferOrder) {
        List<String> allOrderNos = Lists.newArrayList();
        nonTransferOrder = nonTransferOrder.stream().filter(vo -> {
            if (vo.getEedStatus().equals(EmployeeEntryDimissionStatus.DIMISSION.getCode())) {
                Date formattedDate = DateUtil.getFormattedDate(DateUtil.DATE_FORMAT_LONG, vo.getApplyDimissionDate());
                Integer dimissionMonth = com.reon.hr.api.customer.utils.DateUtil.getIntegerDate(formattedDate);
                if (dimissionMonth >= DateUtil.getCurrentYearMonth()) {
                    allOrderNos.add(vo.getOrderNo());
                    return true;
                }
                return false;
            }
            allOrderNos.add(vo.getOrderNo());
            return true;
        }).collect(Collectors.toList());
        /**获取最近三个月出过账单的订单号*/
        Map<String, Set<Integer>> entryBillOrderNos = getBillMonthsByOrderNos(allOrderNos);
        for (String orderNo : entryBillOrderNos.keySet()) {
            Set<Integer> billMonths = entryBillOrderNos.get(orderNo);
            billMonths.retainAll(monthBetween);
//            boolean anyMatch = billMonths.stream().anyMatch(monthBetween::contains);
//            if(anyMatch){
            if (!billMonths.isEmpty()) {
                orderNosFromBill.add(orderNo);
            }
        }
        return nonTransferOrder;
    }


    Map<String, Set<Integer>> getBillMonthsByOrderNos(List<String> collect) {
        final int commSize = 5000;
        Map<String, Set<Integer>> allList = Maps.newHashMap();
        if (collect.size() < commSize) {
            Map<String, Set<Integer>> exportPracticeReportDataList = iBillReportWrapperService.selectOrderNoAndBillMonths(collect);
            allList.putAll(exportPracticeReportDataList);
        } else {
            int callSize = collect.size() % commSize == 0 ? collect.size() / commSize : collect.size() / commSize + 1;
            try {
                List<CompletableFuture<Map<String, Set<Integer>>>> futures = Lists.newArrayList();
                for (int i = 0; i < callSize; i++) {
                    int finalI = i;
                    CompletableFuture<Map<String, Set<Integer>>> future = CompletableFuture.supplyAsync(
                            () -> iBillReportWrapperService.selectOrderNoAndBillMonths(collect.subList(finalI * commSize, Math.min(((finalI + 1) * commSize), collect.size()))));
                    futures.add(future);
                }
                for (CompletableFuture<Map<String, Set<Integer>>> future : futures) {
                    try {
                        allList.putAll(future.get());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return allList;
    }

    @Autowired
    private IOrgnizationResourceWrapperService orgnizationService;

    @Override
    public List<ServiceNumPeopleReportVo> getServiceNum(Integer yearMonthS, Integer yearMonthE, Map<String, Object> conditonMap) {
        List<ServiceNumPeopleReportVo> returnList = Lists.newArrayList();
        List<ServiceNumPeopleReportVo> serviceNumPeopleReportVoList = Lists.newArrayList();
        int currentYearMonth = DateUtil.getCurrentYearMonth();
        List<ServiceNumPeopleReport> snprList = serviceNumPeopleReportService.getDataByYearMonth(yearMonthS, yearMonthE, conditonMap);
        serviceNumPeopleReportVoList = snprList.stream().map(item -> {
            ServiceNumPeopleReportVo serviceNumPeopleReportVo = new ServiceNumPeopleReportVo();
            BeanUtils.copyProperties(item, serviceNumPeopleReportVo);
            return serviceNumPeopleReportVo;
        }).collect(Collectors.toList());
        if (yearMonthS <= currentYearMonth && currentYearMonth <= yearMonthE) {
            List<ServiceNumPeopleReportVo> currentVoList = Lists.newArrayList();
            serviceNumPeopleReportVoList = serviceNumPeopleReportVoList.stream().filter(item -> item.getYearMonthForSnp() != currentYearMonth).collect(Collectors.toList()); // 过滤掉当前月,如果有的话,一般情况下没有
            List<ServiceNumPeopleReportDto> serviceNumPeopleReportDtoList = iEmployeeOrderWrapperService.getServiceNum(conditonMap);
            currentVoList = serviceNumPeopleReportDtoList.stream().map(item -> {
                ServiceNumPeopleReportVo currentDataVo = new ServiceNumPeopleReportVo();
                BeanUtils.copyProperties(item, currentDataVo);
                currentDataVo.setYearMonthForSnp(currentYearMonth);
                return currentDataVo;
            }).collect(Collectors.toList());
            serviceNumPeopleReportVoList.addAll(currentVoList);
            serviceNumPeopleReportVoList = serviceNumPeopleReportVoList.stream().sorted(Comparator.comparing(ServiceNumPeopleReportVo::getYearMonthForSnp)).collect(Collectors.toList());
        }
        List<Long> custGroupIdList = serviceNumPeopleReportVoList.stream().filter(item -> item.getCustGroupId() != null).map(ServiceNumPeopleReportVo::getCustGroupId).collect(toList());
        List<String> contractNoList = serviceNumPeopleReportVoList.stream().map(ServiceNumPeopleReportVo::getContractNo).distinct().collect(toList());
        Map<String, List<ServiceNumPeopleReportVo>> custGroupIdAndContractNoAndSellerAndCommisMap = serviceNumPeopleReportVoList.stream()
                .filter(item -> item.getCustId() != null && StringUtils.isNotBlank(item.getContractNo()) && StringUtils.isNotBlank(item.getSeller()) && StringUtils.isNotBlank(item.getCommissioner()))
                .collect(Collectors.groupingBy(item -> {
                    return item.getCustGroupId() + "@_" + item.getCustId() + "@_" + item.getContractNo() + "@_" + item.getSeller() + "@_" + item.getCommissioner();
                }));
        Map<String, Map<Integer, Integer>> allMonthNumMap = serviceNumPeopleReportVoList.stream()
                .filter(item -> item.getCustId() != null && StringUtils.isNotBlank(item.getContractNo()) && StringUtils.isNotBlank(item.getSeller()) && StringUtils.isNotBlank(item.getCommissioner()))
                .collect(groupingBy(item -> item.getCustGroupId() + "@_" + item.getCustId() + "@_" + item.getContractNo() + "@_" + item.getSeller() + "@_" + item.getCommissioner()
                        , toMap(ServiceNumPeopleReportVo::getYearMonthForSnp, ServiceNumPeopleReportVo::getNum, (a, b) -> b)));
        Map<Integer, String> cityCodeNameMap = areaResourceWrapperService.getCityCodeNameMap();
        List<CustomerVo> allCustomerName = iCustomerWrapperService.findAllCustomerName();
        Map<Long, String> customerNameAndIdMap = allCustomerName.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
        Map<String, String> loginUserPosType = iUserWrapperService.getLoginUserPosType();
        Map<String, String> allUserMap = iUserWrapperService.getAllUserMap();
        List<ContractVo> contractList = contractResourceWrapperService.getByContractNoList(contractNoList);
        Map<String, ContractVo> contractNoAndVoMap = contractList.stream().collect(Collectors.toMap(ContractVo::getContractNo, Function.identity(), (a, b) -> b));
        Map<Long, String> groupIdAndNameMap = customerGroupWrapperService.getGroupIdAndNameMapById(custGroupIdList);
        List<OrgVo> allCompany = orgnizationWrapperService.findAllCompany();

        Map<String, String> orgCodeMap = allCompany.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        for (String item : custGroupIdAndContractNoAndSellerAndCommisMap.keySet()) {
            Map<Integer, Integer> monthNumMap = allMonthNumMap.getOrDefault(item, Maps.newHashMap());
            String[] result = item.split("@_");
            ServiceNumPeopleReportVo serviceNumPeopleReportVo = new ServiceNumPeopleReportVo();
            Long custGroupId = null;
            if ( StringUtil.isNumeric(result[0]))
                custGroupId = Long.valueOf(result[0]);
            Long custId = Long.valueOf(result[1]);
            String contractNo = String.valueOf(result[2]);
            String seller = String.valueOf(result[3]);
            String commissioner = String.valueOf(result[4]);
            ContractVo contractVo = contractNoAndVoMap.getOrDefault(contractNo, new ContractVo());
            if (custGroupId != null)
                serviceNumPeopleReportVo.setCustGroupName(groupIdAndNameMap.get(custGroupId));
            serviceNumPeopleReportVo.setCustName(customerNameAndIdMap.get(custId));
            serviceNumPeopleReportVo.setContractTypeStr(EmployeeReportEnum.ContractTypeEnum.getName(contractVo.getContractType()));
            serviceNumPeopleReportVo.setContractStartMonth(DateUtil.formatDateToString(contractVo.getStartDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD));
            serviceNumPeopleReportVo.setContractNo(contractNo);
            serviceNumPeopleReportVo.setSellerStr(allUserMap.get(seller));
            serviceNumPeopleReportVo.setCommissionerStr(allUserMap.get(commissioner));
            serviceNumPeopleReportVo.setNewFlagStr(FinancialAccountsReceivableReportEnum.IsNewFlagEnum.getName(contractVo.getNewFlag()));
            serviceNumPeopleReportVo.setMonthAndNumMap(monthNumMap);
            serviceNumPeopleReportVo.setSignPlaceStr(cityCodeNameMap.get(contractVo.getSignPlace()));
            serviceNumPeopleReportVo.setSellerDepartment(loginUserPosType.get(seller));
            returnList.add(serviceNumPeopleReportVo);
        }

        return returnList;
    }

    @Override
    public List<FinancialRequireVo> importFinancialRequireEmployee(  String yearMonth) {
        return iEmployeeOrderWrapperService.importFinancialRequireEmployee( yearMonth);
    }

    @Override
    public Boolean importFinancialRequireEmployeeData(List<FinancialRequireVo> dataList) {
        return iEmployeeOrderWrapperService.importFinancialRequireEmployeeData(dataList);
    }

    @Override
    public List<EmployeeChangeReportExportVo> getEmployeeChangeReportData(String month) {
        return iEmployeeOrderWrapperService.getEmployeeChangeReportData(month);
    }
}
