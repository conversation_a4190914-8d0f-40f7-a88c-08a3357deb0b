/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/7/9 0009
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.changeBase;

import com.alibaba.excel.annotation.ExcelProperty;
import com.reon.hr.api.customer.enums.BaseAdjustmentRow;
import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Field;


/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelDateVo
 *
 * @date 2021/7/9 0009 9:52
 */
@Data
public class ExcelDataVo implements Serializable {
  private static final long serialVersionUID = -4021066098469919355L;
  @ExcelProperty(index = 0)
  private String name;
//  @ExcelProperty(index = 1)
  private String orderNo;
//  @ExcelProperty(index = 2)
//  private String certType;
//  @ExcelProperty(index = 3)
//  private String certNo;
//  @ExcelProperty(index = 4)
//  private String custNo;

  @ExcelProperty(index = 1)
  private String certType;
  @ExcelProperty(index = 2)
  private String certNo;
  @ExcelProperty(index = 3)
  private String custNo;
  /**
   *  f-->z属性对应着Excel表格中的表头
   */
  @ExcelProperty(index = 5)
  private String f;
  @ExcelProperty(index = 6)
  private String g;
  @ExcelProperty(index = 7)
  private String h;
  @ExcelProperty(index = 8)
  private String i;
  @ExcelProperty(index = 9)
  private String j;
  @ExcelProperty(index = 10)
  private String k;
  @ExcelProperty(index = 11)
  private String l;
  @ExcelProperty(index = 12)
  private String m;
  @ExcelProperty(index = 13)
  private String n;
  @ExcelProperty(index = 14)
  private String o;
  @ExcelProperty(index = 15)
  private String p;
  @ExcelProperty(index = 16)
  private String q;
  @ExcelProperty(index = 17)
  private String r;
  @ExcelProperty(index = 18)
  private String s;
  @ExcelProperty(index = 19)
  private String t;
  @ExcelProperty(index = 20)
  private String u;
  @ExcelProperty(index = 21)
  private String v;
  @ExcelProperty(index = 22)
  private String w;
  @ExcelProperty(index = 23)
  private String x;
  @ExcelProperty(index = 24)
  private String y;
  @ExcelProperty(index = 25)
  private String z;


}
