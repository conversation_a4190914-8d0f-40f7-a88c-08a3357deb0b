package com.reon.hr.sp.bill.dao.banktrans;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.reon.hr.common.cmb.TransQueryAccInfo;
import com.reon.hr.sp.bill.entity.banktrans.BankTranInfParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankTranInfParamsMapper extends BaseMapper<BankTranInfParams> {

    List<BankTranInfParams> getLastBankTranInfParamsMapper();

    void insertList(@Param("list") List<TransQueryAccInfo> transQueryAccInfos);
}
