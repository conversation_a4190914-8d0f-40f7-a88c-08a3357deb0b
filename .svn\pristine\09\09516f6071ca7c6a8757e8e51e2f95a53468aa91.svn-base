package com.reon.hr.sp.bill.service.bill;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2022/12/8.
 */
public interface CustomerLocalRecordService {
    boolean saveOrUpdateCustomerLocalRecord(Integer billMonth);

	Integer getLocalFlagByCustIdAndBillMonth(Long custId, Integer billMonth);

	Map<String, Integer> getLocalFlagByCustIdAndBillMonthList(List<String> custIdAndBillIdList);

    Map<Long, Integer> getLocalFlagByBillMonthAndCustId(List<Long> custId, int preYearMonth);
}
