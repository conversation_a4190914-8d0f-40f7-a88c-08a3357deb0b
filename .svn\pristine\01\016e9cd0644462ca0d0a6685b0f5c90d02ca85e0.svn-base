package com.reon.hr.api.report.vo;

import com.reon.hr.api.report.anno.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class EmployeeEntryReportVo implements Serializable {

    private static final long serialVersionUID = -2152794924362522829L;

    @ExcelColumn(name = "客户编号", columnWidth = 4000)
    private String custNo;
    @ExcelColumn(name = "转移备注", columnWidth = 6000)
    private String transferRemark;

    @ExcelColumn(name = "客户名称", columnWidth = 6000)
    private String custName;

    @ExcelColumn(name = "订单编号", columnWidth = 6000)
    private String orderNo;

    @ExcelColumn(name = "大合同编号", columnWidth = 3800)
    private String contractNo;

    @ExcelColumn(name = "合同类型", columnWidth = 2000)
    private String contractTypeName;

    @ExcelColumn(name = "小合同编号", columnWidth = 3500)
    private String contractAreaNo;

    @ExcelColumn(name = "小合同名称", columnWidth = 6000)
    private String contractAreaName;

    @ExcelColumn(name = "参保城市", columnWidth = 2000)
    private String cityName;
    @ExcelColumn(name = "签约方抬头", columnWidth = 3500)
    private String signComTitle;

    @ExcelColumn(name = "未增反馈", columnWidth = 2000)
    private String remark1;
    @ExcelColumn(name = "机动2", columnWidth = 2000)
    private String remark2;
    @ExcelColumn(name = "机动3", columnWidth = 2000)
    private String remark3;
    @ExcelColumn(name = "机动4", columnWidth = 2000)
    private String remark4;
    @ExcelColumn(name = "机动5", columnWidth = 2000)
    private String remark5;

    @ExcelColumn(name = "公积金账号", columnWidth = 2200)
    private String accuAcctNo;

    @ExcelColumn(name = "雇员状态",columnWidth = 2200)
    private String orderStatus;

    @ExcelColumn(name = "员工姓名", columnWidth = 2200)
    private String employeeName;

    @ExcelColumn(name = "身份证号", columnWidth = 4200)
    private String certNo;

    @ExcelColumn(name = "唯一号", columnWidth = 3600)
    private String employeeNo;

    @ExcelColumn(name = "性别", columnWidth = 3600)
    private String sex;

    @ExcelColumn(name = "年龄", columnWidth = 3600)
    private Integer age;

    @ExcelColumn(name = "退休时间", columnWidth = 3600)
    private String retireDate;

    private String categoryCode;
    @ExcelColumn(name = "人员分类", columnWidth = 2600)
    private String categoryCodeName;

    @ExcelColumn(name = "联系电话1", columnWidth = 2600)
    private String mobile;

    @ExcelColumn(name = "联系电话2", columnWidth = 2600)
    private String tel;

    @ExcelColumn(name = "报入职日期", columnWidth = 2600)
    private String applyEntryTime;

    @ExcelColumn(name = "入职日期", columnWidth = 2600)
    private String entryDate;

    @ExcelColumn(name = "报入职人", columnWidth = 2000)
    private String applyEntryMan;

    @ExcelColumn(name = "正式工资", columnWidth = 2200)
    private String formalSalary;

    @ExcelColumn(name = "派遣起始日", columnWidth = 2500)
    private String dispatchStart;

    @ExcelColumn(name = "派遣截止日", columnWidth = 2500)
    private String dispatchEnd;

    @ExcelColumn(name = "合同开始日期", columnWidth = 2800)
    private String startDate;

    @ExcelColumn(name = "合同结束日期", columnWidth = 2800)
    private String endDate;

    @ExcelColumn(name = "试用期起始日", columnWidth = 2800)
    private String probaStart;

    @ExcelColumn(name = "试用期结束日", columnWidth = 2800)
    private String probaEnd;

    @ExcelColumn(name = "试用期工资", columnWidth = 2500)
    private String probaSalary;

    @ExcelColumn(name = "工作制", columnWidth = 2000)
    private String workMethod;

    @ExcelColumn(name = "派单分公司", columnWidth = 4000)
    private String distComName;

    @ExcelColumn(name = "派单客服", columnWidth = 2800)
    private String prjCs;

    @ExcelColumn(name = "接单分公司", columnWidth = 4000)
    private String receivingName;

    @ExcelColumn(name = "自有公司/供应商公司", columnWidth = 4000)
    private String recceivingType;

    @ExcelColumn(name = "接单客服", columnWidth = 2800)
    private String revCs;

    @ExcelColumn(name = "合同版本", columnWidth = 2000)
    private String tempTypeName;

    @ExcelColumn(name = "是否签订劳动合同", columnWidth = 2800)
    private String signFlagName;

    @ExcelColumn(name = "是否存档", columnWidth = 2000)
    private String storageFlagName;

    @ExcelColumn(name = "是否外呼", columnWidth = 2000)
    private String telFlagName;

    @ExcelColumn(name = "是否单立户", columnWidth = 2000)
    private String accountFlagName;

    @ExcelColumn(name = "单立户名称", columnWidth = 6000)
    private String sinAccName;

    @ExcelColumn(name = "实际工作地", columnWidth = 2500)
    private String workAddr;

    @ExcelColumn(name = "备注说明1", columnWidth = 4000)
    private String entryRemark;

    @ExcelColumn(name = "数据确认进程", columnWidth = 16000)
    private String entryProcess;
    private Integer accountFlag;
    /**
     * 社保工资
     */
    private String socailSalary;
    /**
     * 公积金工资
     */
    private String acctSalary;
    /**
     * 户口性质
     */
    private String household;

    private Integer certType;
    private String jobPosition;
    /**
     * 社保收费起始月
     */
    @ExcelColumn(name = "社保收费起始月",columnWidth = 4000)
    private Integer socialSecurityStartMonth;

    @ExcelColumn(name = "养老基数", columnWidth = 2000)
    private String endowmentBase;

    @ExcelColumn(name = "医疗基数", columnWidth = 2000)
    private String medicalBase;

    @ExcelColumn(name = "工伤基数", columnWidth = 2000)
    private String injuryBase;

    @ExcelColumn(name = "生育基数", columnWidth = 2000)
    private String maternityBase;

    @ExcelColumn(name = "失业基数", columnWidth = 2000)
    private String unemploymentBase;

    /**
     * 公积金收费起始月
     */
    @ExcelColumn(name = "公积金收费起始月",columnWidth = 4000)
    private Integer accumulationFundStartMonth;

    @ExcelColumn(name = "公积金基数", columnWidth = 2000)
    private String accFundBase;

    @ExcelColumn(name = "公积金个人比例", columnWidth = 2000)
    private String accFundIndRatio;

    @ExcelColumn(name = "公积金企业比例", columnWidth = 2000)
    private String accFundComRatio;

    private BigDecimal amount = BigDecimal.ZERO;   // 服务金额
    private BigDecimal archiveFee= BigDecimal.ZERO;//档案费
    private BigDecimal oneFee = BigDecimal.ZERO;// 一次性金额
    private BigDecimal comTotal= BigDecimal.ZERO; //企业小计
    private BigDecimal indTotal= BigDecimal.ZERO; //个人小计
    private BigDecimal rowTotal= BigDecimal.ZERO; // 列小计
    private Map<Integer, OrderInsuranceCfgReportVo> insurances;  //社保公积金产品
    private Map<Integer,OrderInsuranceCfgReportVo> totalPriceMap; //计算的总额



}
