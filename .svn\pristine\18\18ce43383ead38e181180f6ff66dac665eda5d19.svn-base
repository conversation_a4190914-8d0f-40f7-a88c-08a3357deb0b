var ws = null;

//创建WebSocket连接
function createWebSocket(layer, message, element) {
    //判断浏览器是否支持websocket
    if ("WebSocket" in window || window.WebSocket) {
        if (top.location.protocol == 'https:') {
            wsUri = "wss://" + top.location.hostname + ML.contextPath + "/webSocketServer"
        } else {
            wsUri = "ws://" + top.location.hostname + ':' + top.location.port + ML.contextPath + "/webSocketServer"
        }
        ws = new WebSocket(wsUri);

    } else {
        throw new Error("browser not support websocket...");
        return false;
    }
    ws.onopen = function (evt) {
        wsObj.wsOpen(evt, message);
    };
    ws.onmessage = function (evt) {
        wsObj.wsMsg(evt, layer, element);
    };
    ws.onclose = function (evt) {
        wsObj.wsClose(evt);
    };
    ws.onerror = function (evt) {
        wsObj.wsError(evt);
    };
}

var wsObj = {
    wsOpen: function (evt, message) {
        console.log(evt.type);
        console.log("websocket connect success...");
        ws.send(message);
    },
    wsMsg: function (msg, layer, element) {
        var layId = "", menu = [], flg = [];
        var data = JSON.parse(msg.data);
        $("#messageCenterData").val(msg.data);
        if (data.length > 0) {
            var msg = layer.open({
                type: 1
                , title: ["消息通知<a id='closeIconBtn' class='layui-icon layui-icon-close' style='float: right;color: #fff;'></a>","background-color: #2257C4;color: #fff;font-weight:600;"]
                , offset: "rb"
                , content: '<ul class="layui-timeline" style="width: 260px;height: 150px;">\n' +
                    '</ul>'
                , btn: '点击查看'
                , closeBtn:1
                , time: 60000 //1分钟消息框没有操作关闭消息框
                , btnAlign: 'c' //按钮居中
                , shade: [0.8, '#393D49']
                , yes: function () {
                    layer.close(msg);
                    $(".layui-tab-title.top_tab li").each(function () {
                        flg.push($(this).find("span").text());
                    });
                    if (flg.length == 0 || $.inArray("消息中心", flg) < 0) {
                        if (window.sessionStorage.getItem("menu")) {
                            menu = JSON.parse(window.sessionStorage.getItem("menu"));
                        }
                        element.tabAdd("bodyTab", {
                            title: "<span >消息中心</span><i class=\"layui-icon layui-unselect layui-tab-close\" data-id=\"1\">&#x1006;</i>",
                            content: "<iframe src='/base/message/gotoMessageQueryListPage' data-id='1'></frame>",
                            id: new Date().getTime()
                        });
                        var curmenu = {
                            "title": "消息中心",
                            "href": "/base/message/gotoMessageQueryListPage",
                            "layId": new Date().getTime()
                        }
                        menu.push(curmenu);
                        window.sessionStorage.setItem("menu", JSON.stringify(menu)); //打开的窗口
                        window.sessionStorage.setItem("curmenu", JSON.stringify(curmenu));  //当前的窗口
                        $(".layui-tab-title.top_tab li").each(function () {
                            layId = $(this).attr("lay-id");
                        });
                        if (layId) {
                            element.tabChange("bodyTab", layId);
                        }
                    } else {
                        //当前窗口内容
                        var curmenu = {
                            "title": "消息中心",
                            "href": "/base/message/gotoMessageQueryListPage",
                            "layId": new Date().getTime()
                        }
                        window.sessionStorage.setItem("curmenu", JSON.stringify(curmenu));  //当前的窗口
                        $(".layui-tab-title.top_tab li").each(function () {
                            layId = $(this).attr("lay-id");
                        });
                        if (layId) {
                            element.tabChange("bodyTab", layId);
                        }
                    }
                }
                , success: function () {
                    $(".layui-layer-close1").css("background","none");
                    for (var i = 0; i < data.length; i++) {
                        $(".layui-timeline").append('<li style="padding: 20px 0 0 10px">\n' +
                            '    <div class="layui-timeline-content layui-text">\n' +
                            '      <div id = ' + i + 'icon class="layui-timeline-title" >' + data[i] + '</div>\n' +
                            '    </div>\n' +
                            '  </li>\n')
                    }
                }
            });
            //按回车键关闭消息框
            $(document).keydown(function (event) {
                if (event.keyCode == 13) {
                    layer.close(msg);
                }
            });
            //关闭按钮
            $(document).on("click", "#closeIconBtn", function () {
                layer.close(msg);
            });
        }
    },
    wsClose: function (evt) {
        console.log("type:" + evt.type);
    },
    wsError: function (evt) {
        console.log("type:" + evt.type);
    }
};
var messageCount;
$.ajax({
    type: "GET",
    url: ML.contextPath + "/base/message/getMessageWrapperListPage?readFlag=1&limit=50&page=1",
    dataType: 'json',
    success: function (res) {
        messageCount=res.data.length;
    }
});
window.setInterval("flicker()",500);
window.flicker=function() {
    if(messageCount&&messageCount>0){
        /*if($("#iconMessage").css("display") == "none"){
            $("#iconMessage").show()
            $("#iconMessageRed").hide()
        }else {
            $("#iconMessage").hide()
            $("#iconMessageRed").show()
        }*/
        $("#iconMessage").hide()
        if($("#iconMessageRed").css("display") == "none"){
            $("#iconMessageRed").show()
        }else {
            $("#iconMessageRed").hide()
        }
    }else {
        $("#iconMessage").show()
        $("#iconMessageRed").hide()
    }
}

$("#messageCenterClick").on('click', function () {
    $(".layui-nav .layui-nav-item a").each(function () {
        if($(this)[0].innerText=="消息中心"){
            addTab($(this));
            $(this).parent("li").siblings().removeClass("layui-nav-itemed");
        }
    })
});

