package com.reon.hr.common.cmb.notify.payroll;

import com.reon.hr.common.cmb.notify.CMBBaseNotifyInfo;
import lombok.Data;

import java.util.List;

/**
 * 业务完成通知
 *
 * <AUTHOR>
 * <p>
 * 代发批次完成通知，当批次的明细有失败，且失败的明细小于500条时，返回失败的明细。
 */
@Data
public class PayrollBusinessCompletion extends CMBBaseNotifyInfo {

    /**
     * 当批次的明细有失败，且失败的明细小于500条时
     */
    private List<AgentBusComDetailInfo> detailInfo;

    /**
     * 批次信息
     */
    private AgcInfo agcInfo;

    @Override
    public String getYurRef() {
//        JsonObject jsonObject = new Gson().fromJson(msgData, JsonObject.class);
//        return jsonObject.getAsJsonObject("agcInfo").get("yurRef").getAsString();
        return this.agcInfo.getYurref();
    }
}
