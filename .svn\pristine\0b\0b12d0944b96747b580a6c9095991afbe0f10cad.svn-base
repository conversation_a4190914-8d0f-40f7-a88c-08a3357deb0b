package com.reon.hr.api.customer.enums.salary;

import com.reon.hr.api.customer.enums.BaseEnum;

/**
 * <AUTHOR> on 2022/9/13.
 */
public enum LaborWagesTypeEnum implements BaseEnum {
    CONTINUOUS(1,"连续(实习生)"),
    DISCONTINUOUS(2,"非连续");
    private int code;
    private String name;

    LaborWagesTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static Integer getCodeByName(String name) {
        for (LaborWagesTypeEnum value : LaborWagesTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;

    }
}
