package com.reon.hr.api.customer.vo.supplier;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023年11月16日
 * @Version 1.0
 */
@Data
public class SupplierContractRelatedCompanyVo implements Serializable {


    private Long id;
    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商合同编号
     */
    private String supplierContractNo;

    /**
     * 关联公司
     */
    private String company;
    private String companyName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识 Y 已删除 N 未删除
     */
    private String delFlag;
}
