<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-form .layui-table td {
            padding-left: 0px;
            padding-right: 0px;
        }
    </style>
</head>
<body class="childrenBody">

<fieldset class="layui-elem-field" style="width: 48%; float: left;">
    <legend>选择社保组</legend>
    <div class="layui-field-box">
        <form class="layui-form" id="searchForm" action="" method="post">
            <table class="layui-table" lay-skin="nob" style="margin: 0 auto;">
                <tr>
                    <td width="18%" align="right" style="font-weight:800">所属城市：</td>
                    <td width="23%">
                        <select class="layui-select" name="cityCode" id="cityCode" lay-search lay-filter="cityCodeFilter" AREA_TYPE>
                            <option value=""></option>
                        </select>
                    </td>
                    <td width="14%" align="right" style="font-weight:800">组类别：</td>
                    <td width="20%">
                        <select name="groupType" id="groupType" DICT_TYPE="INSURANCE_SET_TYPE">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td colspan="2" align="center" width="25%">
                        <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter"
                                authURI="/sys/InsuranceGroup/getGroupPage">查询
                        </button>
                        <button class="layui-btn layui-btn-sm" type="reset">重置</button>
                    </td>
                </tr>
            </table>
        </form>
        <table class="layui-hide" id="groupGrid" lay-filter="groupFilter"></table>
    </div>
</fieldset>

<fieldset class="layui-elem-field" style="width: 45%; float: right;">
    <legend>已选社保公积金组</legend>
    <div class="layui-field-box">
        <table class="layui-hide" id="selectedGrid" lay-filter="selectedFilter"></table>
    </div>
</fieldset>

<fieldset class="layui-elem-field" style="width: 45%; float: right;">
    <legend>社保公积金产品</legend>
    <div class="layui-field-box">
        <table class="layui-hide" id="productGrid" lay-filter="productFilter"></table>
    </div>
    <a class="layui-btn" lay-submit lay-filter="saveFilter" id="saveBtn">确定</a>
    <a class="layui-btn layui-btn-normal" id="closeBtn">关闭</a>
</fieldset>
<input type="hidden" id="parentIframeIndex">
<input type="hidden" id="insurances">
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/personOrder/insuranceGroup.js?v=${publishVersion}"></script>
</body>
</html>