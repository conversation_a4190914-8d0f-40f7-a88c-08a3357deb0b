var ctx = window.ML.contextPath;
// var orgCode = $("#organCode").val();////组织Code
// var positionCode = $("#positionCode").val();
var oprType = $("#oprType").val();
layui.config({
    base: "../../js/"
}).extend({dtree: '{/}../../layui_ext/dtree/dtree'})
    .use(['form', 'layer', 'table', 'dtree'], function () {
        var dtree = layui.dtree;
        var form = layui.form, layer = parent.layer === undefined ? layui.layer : parent.layer;
        // if (!oprType == 'add') {
        //     $("#status").find("option[value = '" + $('#statusVal').val() + "']").attr("selected", "selected");
        // }

        ///获取组织树形机构
        // function getTree() {
        //     $.ajax({
        //         type: "POST",
        //         url: ctx + "/sys/org/getTree",
        //         dataType: 'json',
        //         success: function (data) {
        //             dtree.render({
        //                 elem: "#demoTree",
        //                 data: data,
        //                 initLevel: "1",///初始只显示1级
        //                 checkbarType: "all", //all上下级联  self独立  only单选
        //                 dot: false,///取消没有子节点的 节点 前面的点
        //                 skin: 'zdy',
        //             });
        //         },
        //     });
        // }

        ////获取所有职位
/*        function getPosition() {
            ML.ajax("/sys/position/positionList", null, function (result) {
                result.data.forEach(function (position) {
                    var positionObj = "<option id=" + position.positionCode + " value=" + position.positionCode + ">" + position.positionName + "</option>";
                    $("#positionList").append(positionObj);
                    ///选中数据库中的值
                    if (oprType != "add") {
                        $("#positionList").val(positionCode);
                    }
                    ////渲染表单的下拉框
                    form.render('select');
                });
            });
        }*/

        ////根据职位和机构获取所有领导
        // function getLeaders(orgCode, positionCode) {
        //     if (!positionCode) {
        //         layer.msg("请选择岗位");
        //         return false;
        //     }
        //     if (!orgCode) {
        //         layer.msg("请选择机构");
        //         return false;
        //     }
        //     $(".oldOpt").remove();
        //     ML.ajax("/sys/user/getLeaders", {"orgCode": orgCode, "positionCode": positionCode}, function (result) {
        //         if (result.code == 0) {
        //             if (result.data.length>0){
        //                 result.data.forEach(function (leader) {
        //                     var obj = "<option class='oldOpt' value=" + leader.id + ">" + leader.userName + "</option>";
        //                     $("#leaderList").append(obj);
        //                     ///选中数据库中的值
        //                     if (oprType != "add") {
        //                         $("#leaderList").val($("#leaderId").val());
        //                     }
        //                     ////渲染表单的下拉框
        //                     form.render('select');
        //                 });
        //             }
        //             if (result.data.length==0) {
        //                 $(".oldOpt").remove();
        //                 form.render('select');
        //             }
        //         }
        //
        //     });
        // }

        ////点击机构输入框
/*        $("#organName").click(function () {
            var displayValue = $("#OrgTree").css("display");
            if (displayValue === "none") {
                $("#OrgTree").css("display", "inline-block");
            } else {
                $("#OrgTree").css("display", "none");
            }
        });*/
        ////选取树形菜单节点
        // dtree.on("node('demoTree')", function (obj) {
        //     $("#OrgTree").css("display", "none");
        //     $("#organName").val(obj.param.context);
        //     orgCode = obj.param.nodeId;
        //     getLeaders(orgCode,positionCode);
        // });

        // form.on('select(positionFilter)', function (data) {
        //     positionCode = data.value;
        //     getLeaders(orgCode,positionCode);
        // });

        // getTree();
        // getPosition();
        // if (oprType != 'add') {
        //     getLeaders(orgCode, positionCode);
        // }


        // 修改用户信息
        form.on("submit(addOrUpdateUser)", function (data) {
            // data.field['organCode'] = orgCode;
            ML.layuiButtonDisabled($('#addOrUpdateUser'));// 禁用
            if(data.field.loginPassword !== ""){
                shaPwd(data);
            }
            var url = data.field.oprType === 'update' ? "/sys/user/saveUpdateUser" : "/sys/user/saveAddUser";
            ML.ajax(url, data.field, function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    layer.closeAll('iframe'); //关闭弹窗
                } else if (result.code == -1) {
                    ML.layuiButtonDisabled($('#addOrUpdateUser'), true);
                }
            });
            return false;
        });
        $(document).on('click', '#cancel', function () {
            layer.closeAll('iframe'); //关闭弹窗
        });
    });



function setDisabled() {
    var oprType = $('#oprType').val();
    if (oprType === 'update') {
       /* $("#userName").attr("disabled", true);*/
        $("#loginName").attr("disabled", true);
        $('#userNameLabel').css('display', 'none');
        $('#loginNameLabel').css('display', 'none');
        $('#pwdTr').css('display', 'none');
        $('#newPwd').val("123456"); //设置一个值，以通过校验，后台操作时不更新字段
    }
}

$(function () {
    setDisabled();
});

var shaPwd = function (data) {
    data.field.loginPassword = sha256($('#newPwd').val());
};
if (oprType=='add'){
    var py = new PinYin();
    $(document).on('blur', '#userName', function () {
        var src = $(this).val();
        var target = py.convert(src);
        $('#loginName').val(target);
    });
}
