package com.reon.hr.api.customer.enums.quotation;

import com.reon.hr.api.customer.enums.BaseEnum;

public enum QuotationTaxFlag implements BaseEnum {

    TAX_EXCLUSIVE(1,"不含税"),
    TAX_INCLUSIVE (2,"含税");

    private int code;
    private String name;

    QuotationTaxFlag(int code,String name) {
        this.name = name;
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
