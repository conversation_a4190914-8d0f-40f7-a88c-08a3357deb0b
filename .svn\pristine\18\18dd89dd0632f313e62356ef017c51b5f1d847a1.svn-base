layui.use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    // 取消按钮
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });
    // setTimeout(function () {
    //     form.render('select');
    // }, 100);
    // 保存按钮
    form.on("submit(save)", function () {
        var empRelatives = table.cache["empRelativeGrid"];

        saveForm('save', empRelatives);
        return false;
    });
    let flag1 = false;

    var idreg = /^(\d{15})$|^(\d{17}(?:\d|x|X))$/;
    function saveForm(type, data) {
        layui.each(data,function (index,item){
            if (!idreg.test(item.certNo)) {
                return true;
            }
            delete data[index].LAY_TABLE_INDEX;
        })
       if (flag1){
           layer.msg("身份证号不合法");
       }

        $.ajax({
            url: ML.contextPath + "/customer/commInsurOrder/updateEmpRelative",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function (result) {
                layer.msg(result.msg);
                if (result.code==0) {
                    layer.closeAll('iframe');//关闭弹窗
                }
            },
            error: function () {
                layer.alert('系统发生异常，请重试！');
            }
        });
    }

    $(document).ready(function () {

        getEmpRelativeData();

    })

    function getEmpRelativeData() {
        ML.ajax("/customer/commInsurOrder/getEmpRelativeVo", {"empId": $("#empId").val()}, function (data) {

            table.reload('empRelativeGrid', {data: data});
        }, "GET");
    }



        table.render({
            id: 'empRelativeGrid',
            elem: '#empRelativeTable',
            page: false, //默认为不开启
            limit: Number.MAX_VALUE,
            title: "关联人信息",
            toolbar: '#topbtn',
            defaultToolbar: false,
            text: {
                none: '暂无数据' //无数据时展示
            },
            cols: [[
                {field: '', type: 'checkbox', width: '5%'}
                , { field: 'id',type: 'hidden',width: '0%',hide: true}
                , {
                    field: 'name', title: '关联人姓名', align: 'center'
                    , edit: 'text', width: '10%'
                }
                , {
                    field: 'certType', title: '关联人证件类型', align: 'center', width: '15%', templet: '#certTypeFlag'
                }
                , {
                    field: 'certNo',
                    title: '关联人证件号',
                    align: 'center',
                    width: '20%',
                    edit:'text'
                }
                , {
                    field: 'birthDate', title: '关联人出生日期', align: 'center',width: '30%',templet: function (d) {
                        var birthDate=d.birthDate!=undefined?d.birthDate.substr(0,10):'';
                        return '<input type="text" class="layui-input layui-table-cell birthDate" id="birthDateInput" value="'+birthDate+'" placeholder="yyyy-MM-dd"  autocomplete="off"  />';
                    }
                }
                , {
                    field: 'sex', title: '关联人性别', align: 'center'
                    , width: '10%' , templet: '#sexFlag'
                }
            ]],
            done: function (res) {
                var nowTime=new Date();
                var month=nowTime.getMonth()+1>12?12:nowTime.getMonth()+1
                nowTime=nowTime.getFullYear()+"-"+month+"-"+nowTime.getDate()
                lay('.birthDate').each(function (i) {
                    laydate.render({
                        elem: this
                        , trigger: 'click'
                        , min: '2010-01-01'
                        , max: nowTime
                        , showBottom: true
                        , theme: 'grid'
                        , calendar: true
                        , format: 'yyyy-MM-dd',
                        done: function (value, date, endDate) { //监听日期被切换
                            var empRelatives = table.cache["empRelativeGrid"];
                            $("input[id='birthDateInput']").each(function (i, a) {
                                var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
                                empRelatives[dataIndex]["birthDate"] = a.value+" 00:00:00";
                                // empRelatives[dataIndex]["birthDate"] = a.value;
                            });

                        }
                    });
                });
            }
        });


    // 监听表格上方的按钮
    table.on('toolbar(empRelativeTableFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'delete':
                if (checkStatus.data.length !== 1) {
                    layer.msg("请选择要删除的信息")
                    return false;
                }
                $.ajax({
                    type: "POST",
                    url: ML.contextPath + "/customer/commInsurOrder/deleteEmpRelative",
                    data: JSON.stringify(checkStatus.data[0].id),
                    contentType: 'application/json',//添加这句话
                    dataType: 'json',
                    success: function (results) {
                        if (results.code === 0) {
                            layer.msg("删除成功")
                            getEmpRelativeData();
                        } else {
                            layer.msg(results.msg)
                        }
                    }
                });
                break;
        }
    });
    //证件类型
    form.on('select(selectCertTypeFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        var empRelatives = table.cache["empRelativeGrid"];
        empRelatives[index]['certType'] = data.value;
    });
    //性别
    form.on('select(selectSexFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        var empRelatives = table.cache["empRelativeGrid"];
        empRelatives[index]['sex'] = data.value;
    });



});