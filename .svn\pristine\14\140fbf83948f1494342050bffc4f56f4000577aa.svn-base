package com.reon.hr.sp.customer.service.impl.insurancePractice;

import com.reon.hr.sp.customer.dao.insurancePractice.NotMathInsuranceTempMapper;
import com.reon.hr.sp.customer.entity.insurancePractice.NotMathInsuranceTemp;
import com.reon.hr.sp.customer.service.insurancePractice.NotMathInsuranceTempService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Administrator
 * @Description:
 * @Date: 2022/11/2 17:32
 * @Version: 1.0
 */
@Service
public class NotMathInsuranceTempServiceImpl implements NotMathInsuranceTempService {
    @Autowired
    private NotMathInsuranceTempMapper notMathInsuranceTempMapper;
    @Override
    public void updateStatus(List<Long> orderInsuranceCfgIds) {
        notMathInsuranceTempMapper.updateStatus(orderInsuranceCfgIds);
    }

    @Override
    public Integer batchInnertNotMathVo(List<NotMathInsuranceTemp> notMathInsuranceTemps) {
        return  notMathInsuranceTempMapper.batchInnertNotMathVo(notMathInsuranceTemps);
    }

    @Override
    public List<NotMathInsuranceTemp> queryAll() {
        return notMathInsuranceTempMapper.queryAll();
    }

    @Override
    public List<NotMathInsuranceTemp> selectAll() {
        return notMathInsuranceTempMapper.selectAll();
    }
}
