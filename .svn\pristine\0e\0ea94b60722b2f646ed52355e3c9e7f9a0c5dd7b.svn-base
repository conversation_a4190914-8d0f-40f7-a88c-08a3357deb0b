var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(
    ['jquery', 'form', 'layer', 'element', 'laydate', 'tableSelect', 'table'], function () {
        var table = layui.table,
            $ = layui.$,
            form = layui.form,
            laydate = layui.laydate,
            tableSelect = layui.tableSelect,
            layer = parent.layer === undefined ? layui.layer : parent.layer;

        //渲染表格
        table.render({
            id: 'taskGrid',
            elem: '#taskGrid',
            //查询待办任务列表
            url: ML.contextPath + '/workflow/getApprovedContractProcess',
            method: 'GET',
            where:{"paramData": JSON.stringify(serialize("searchForm"))},
            page: true, //默认为不开启
            limits: [50, 100, 200],
            limit: 50,
            text: {none: '暂无数据'},
            cols: [[
                {type: 'numbers',align:'center', fixed: 'left'},
                {field: 'custName',align:'center', title: '客户名称', fixed: "left", width: '500'},
                {field: 'contractName', align:'center',title: '合同名称', fixed: 'left', width: '800'},
                {field: 'name', align:'center',title: '节点名称', fixed: 'left', width: '200'},
                {field: 'seller', align:'center',title: '提交人', fixed: 'left', width: '200' , templet: function (d) {
                        return ML.loginNameFormater(d.seller)
                    }
                },
                {field: 'startTime', align:'center',title: '创建时间', fixed: 'left', width: '400'},
            ]]
        });







        //双击事件
        table.on('rowDouble(taskFilter)', function(obj){
                layer.open({
                    type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                    title: '查看',
                    area: ['80%', '70%'],
                    shade: 0,
                    maxmin: true,
                    offset: 'auto',
                    shade: [0.8, '#393D49'],
                    content: ML.contextPath + '/workflow/gotoApprovedContractProcessTimePage',
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        body.find("#pid").val(obj.data.procInstId)
                    },
                    end: function () {
                        reloadTable();
                    }
                });

            // check(obj.data.groupCode);
        });
        function reloadTable() {
            table.reload('taskGrid', {
                where: {
                    paramData: JSON.stringify(serialize("searchForm")),
                }
            });
        }


        // 搜索条件  客户下拉列表框
        var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
        // 客户下拉数据表格
        tableSelect.render({
            elem: '#custName',
            checkedKey: 'id',
            appd: appd,
            table: {
                url: ML.contextPath + '/customer/contract/getCustomerByAll',
                cols: [[
                    {type: 'radio'}
                    , {field: 'id', title: '客户ID', align: 'center'}
                    , {field: 'custNo', title: '客户编号', align: 'center'}
                    , {field: 'custName', title: '客户名称', align: 'center'}
                ]]
            },
            done: function (elem, data) {
                var NEWJSON = [];
                var id = '';
                layui.each(data.data, function (index, item) {
                    NEWJSON.push(item.custName)
                    custNo = item.custNo;
                    id = item.id;
                });
                // 回填值
                elem.val(NEWJSON.join(","));
                $("#custId").val(id);

            }
        });

        //重置按钮失效问题
        $(function () {
            $('#reset').click(function () {
                $('#seller').val("");
                $('#custId').val("");
                $('#custName').val("");
                $("#custName option").text("");
                $('#dateS').val("");
                $('#dateE').val("");

                // $("#categoryCode div").text("");
                // $('#categoryCode').empty();
                // $('#categoryCode').empty();
            })
        });



        //开始日期范围
        var startDate = laydate.render({
            elem: '#dateS',
            max: "2099-12-31",//设置一个默认最大值
            format:'yyyyMMdd',
            done: function (value, date) {
                if (null != value && '' != value) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date
                    };
                }
            }
        });

        //结束日期范围
        var endDate = laydate.render({
            elem: '#dateE',//选择器结束时间
            min: "1970-1-1",//设置min默认最小值
            format:'yyyyMMdd',
            done: function (value, date) {
                if (null != value && '' != value) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    }
                }
            }
        });


        //获取所有提交人姓名
        $(document).ready(function () {
            var info = [];
            $.ajax({
                type: "GET",
                url: ML.contextPath + "/workflow/allUserList",
                data:{"processType":2},
                dataType: 'json',
                success: function (data) {
                    info = [];
                    info = data.data;
                    $.each(info, function (i, item) {
                        $("#seller").append($("<option/>").text(item.userName).attr("value", item.loginName));
                    });
                    form.render('select');
                },
                error: function (data) {
                    layer.msg(data);
                    console.log("error")
                }
            });
        });

        form.on('submit(btnQueryFilter)', function (data) {
            reload()
            return false;
        });

        function reload() {
            table.reload('taskGrid', {
                where: {
                    paramData: JSON.stringify(serialize("searchForm")),
                }
            });
        }
    });