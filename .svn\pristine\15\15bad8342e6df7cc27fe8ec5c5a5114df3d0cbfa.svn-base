package com.reon.hr.sp.customer.entity.supplierBillTempletAndPractice;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @deprecated 服务费需求改造后不在使用
 */
@Data
public class SupServiceCharge {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;
    /**
     * 小合同编号
     */
    private String contractAreaNo;
    /**
     * 供应商报价单号
     */
    private String quoteNo;

    /**
     * 开始月
     */
    private Integer revStartMonth;

    /**
     * 起始月
     */
    private Integer revEndMonth;

    /**
     * 服务费
     */
    private BigDecimal amount;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}
