package com.reon.hr.api.customer.vo.salary.pay;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class SalaryBatchDetailVo implements Serializable {
    private Long id;

    private Long batchId;
    private  Long payId;

    private Long salaryId;
    private Integer status;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
}