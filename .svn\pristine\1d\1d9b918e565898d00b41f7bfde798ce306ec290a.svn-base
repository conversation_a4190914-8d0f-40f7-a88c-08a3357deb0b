package com.reon.hr.api.base.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年07月29日
 * @Version 1.0
 */

public class DisabilityGoldRateLogEnum {
    @Getter
    public enum OperationLogType{
        ADD("新增",1),
        UPDATE("修改",2),
        DELETE("删除",3);

        private String name;
        private Integer code;

        OperationLogType(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

    }

    @Getter
    public enum DisabilityType{
        DEFAULT("默认",1),
        SPECIAL("特殊",2);

        private String name;
        private Integer code;

        DisabilityType(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

    }
}
