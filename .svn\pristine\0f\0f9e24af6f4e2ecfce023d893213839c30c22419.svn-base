package com.reon.hr.sp.customer.service.impl.cus;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IinsuranceGroupWrapperService;
import com.reon.hr.api.base.vo.InsurancePackVo;
import com.reon.hr.api.bill.enums.ImportResultEnum;
import com.reon.hr.api.customer.dto.importData.AdjustBaseImportDto;
import com.reon.hr.api.customer.dto.importData.BaseImportDto;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.enums.PersonOrderEnum;
import com.reon.hr.api.customer.enums.importData.ImportDataLogImportResult;
import com.reon.hr.api.customer.enums.importData.ImportDataStatus;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.vo.batchImport.*;
import com.reon.hr.api.customer.vo.doIt.PaySocialSecurityVo;
import com.reon.hr.api.customer.vo.doIt.RepayTheImportInformationVo;
import com.reon.hr.api.customer.vo.employee.CompleteOrderViewVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderLogVo;
import com.reon.hr.sp.customer.dao.cus.ContractAreaMapper;
import com.reon.hr.sp.customer.dao.cus.CustomerMapper;
import com.reon.hr.sp.customer.dao.cus.ImportDataLogMapper;
import com.reon.hr.sp.customer.dao.cus.ImportDataMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeOrderLogMapper;
import com.reon.hr.sp.customer.dao.employee.EmployeeOrderMapper;
import com.reon.hr.sp.customer.entity.cus.Customer;
import com.reon.hr.sp.customer.entity.cus.ImportData;
import com.reon.hr.sp.customer.entity.cus.ImportDataLog;
import com.reon.hr.sp.customer.service.cus.IBatchImportDataService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BatchImportDataServiceImpl implements IBatchImportDataService {

    private static final Logger logger = LoggerFactory.getLogger(BatchImportDataServiceImpl.class);

    @Autowired
    private IBatchImportDataService batchImportDataService;
    @Autowired
    private ImportDataMapper importDataMapper;
    @Autowired
    private ImportDataLogMapper importDataLogMapper;
    @Autowired
    private ContractAreaMapper contractAreaMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private EmployeeOrderMapper employeeOrderMapper;

    @Autowired
    private EmployeeOrderLogMapper orderLogMapper;

    @Resource(name = "IinsuranceGroupDubboService")
    private IinsuranceGroupWrapperService insuranceGroupWrapperService;

    @Resource
    private IInsurancePackResourceWrapperService insurancePackResourceWrapperService;

    @Override
    public List<CompleteOrderViewVo> getCompleteOrderListPage(CompleteOrderViewVo orderViewVo) {
        List<CompleteOrderViewVo> orderList = employeeOrderMapper.getCompleteOrderListPage(orderViewVo);

        for (int i = 0; i < orderList.size(); i++) {
            String remarkStr = "";
            List<EmployeeOrderLogVo> orderLogs = orderLogMapper.getListByOrderNo(orderList.get(i).getOrderNo(), PersonOrderEnum.OrderLogOprType.ENTRY_JOB.getCode());
            for (int j = 0; j < orderLogs.size(); j++) {
                remarkStr += orderLogs.get(j).getRemark() + "\r\n";
            }
            orderList.get(i).setEntryProcess(remarkStr);
        }
        return orderList;
    }

    @Override
    public ImportDataLogVo getById(String importNo, Integer rowNum) {
        return importDataLogMapper.getById (importNo,rowNum);
    }

    @Override
    public Page<ImportDataVo> getImportDataListPage(Integer page, Integer limit, Map<String, Object> map) {
        Page<ImportDataVo> importDataVoPage = new Page<> (page, limit);
        List<ImportDataVo> importDataVoList = null;
        //11代表是社保组
        if ((Integer) map.get("dataType") == 11 || (Integer) map.get("dataType") == 12) {
            importDataVoList = importDataMapper.getSocialSecurityAccountImportDataListPage(importDataVoPage, map);
        } else {
            importDataVoList = importDataMapper.getImportDataListPage (importDataVoPage, map);
        }
        importDataVoPage.setRecords (importDataVoList);
        return importDataVoPage;
    }
    @Override
    public Page<ImportDataVo> getImportDataByTaxComparisonTypeListPage(Integer page, Integer limit, Map<String, Object> map) {
        Page<ImportDataVo> importDataVoPage = new Page<> (page, limit);
        List<ImportDataVo> importDataVoList = importDataMapper.getImportDataByTaxComparisonTypeListPage (importDataVoPage, map);
        importDataVoPage.setRecords (importDataVoList);
        return importDataVoPage;
    }

    @Override
    public Page<ImportDataVo> getImportKeyCustomerDataListPage(Integer page, Integer limit, Map<String, Object> map) {
        Page<ImportDataVo> importDataVoPage = new Page<> (page, limit);
        List<ImportDataVo> importKeyCustomerDataListPage = importDataMapper.getImportKeyCustomerDataListPage(importDataVoPage, map);
        return importDataVoPage.setRecords(importKeyCustomerDataListPage);
    }

    @Override
    public Page<EmployeeImportVo> getEmployeeImportDataListPage(Integer page, Integer limit, Map<String, Object> map) {
        Page<EmployeeImportVo> importDataVoPage = new Page<> (page, limit);
        List<EmployeeImportVo> importDataVoList = importDataMapper.getEmployeeImportDataListPage(importDataVoPage, map);
        importDataVoPage.setRecords (importDataVoList);
        return importDataVoPage;
    }

    @Override
    public Page<BatchAdjustQueryVo> getAdjustImportDataListPage(Integer page, Integer limit, Map<String, Object> map) {
        Page<BatchAdjustQueryVo> importDataVoPage = new Page<> (page, limit);
        List<BatchAdjustQueryVo> importDataVoList = importDataMapper.getAdjustImportDataListPage(importDataVoPage, map);
        importDataVoPage.setRecords (importDataVoList);
        return importDataVoPage;
    }

    @Override
    public Page<ImportDataLogVo> getImportDataLogListPage(Integer page, Integer limit, String importNo) {
        Page<ImportDataLogVo> importDataLogVoPage = new Page<> (page, limit);
        List<ImportDataLogVo> importDataLogVoList = importDataLogMapper.getImportDataLogListPage (importDataLogVoPage, importNo);
        importDataLogVoPage.setRecords (importDataLogVoList);
        return importDataLogVoPage;
    }

    @Override
    public int insertSelective(ImportData importData) {
        return importDataMapper.insertSelective (importData);
    }

    @Override
    public int insertSelective(List<ImportDataLog> record) {
        return importDataLogMapper.insertSelective (record);
    }

    @Override
    public boolean updateByImportDataNo(String importNo, Integer status) {
        return importDataMapper.updateByImportDataNo (importNo, status);
    }

    @Override
    public boolean updateByErrorDesc(List<ImportDataLogVo> record) {
        return importDataLogMapper.updateByErrorDesc (record);
    }

    @Override
    public boolean updateByImportDataAndLogNo(String importNo) throws Exception {
        ImportDataVo byId = importDataMapper.getById (importNo);
        if (byId.getImportNo () == null) {
            throw new Exception ("导入编号不存在！");
        }
        importDataMapper.updateByImportDataDelflag (importNo);
        importDataLogMapper.updateByImportDataLogDelflag (importNo);
        return true;
    }

    @Override
    public List<ImportDataLogVo> getImportDataLogList(String importNo) {
        return importDataLogMapper.getImportDataLogList (importNo);
    }



    @Override
    public ImportDataVo getById(String importNo) {
        return importDataMapper.getById (importNo);
    }

    @Override
    public boolean updateByImportRecordNumber(String importNo,Integer successNum,Integer failNum) {
        return importDataMapper.updateByImportRecordNumber (importNo,successNum,failNum);
    }

    @Override
    public Page<PaySocialSecurityVo> getImportDataPage(RepayTheImportInformationVo repayTheImportInformationVo) {
        Page<PaySocialSecurityVo> page = new Page<>(repayTheImportInformationVo.getPage(),repayTheImportInformationVo.getLimit());
        List<PaySocialSecurityVo> voList = importDataMapper.selectImportDataPage(page, repayTheImportInformationVo);
        List<String> packCodeList = voList.stream().map(PaySocialSecurityVo::getPackCode).distinct().collect(Collectors.toList());
        Map<String, InsurancePackVo> packNameMap = insurancePackResourceWrapperService.getPackNameByCode(packCodeList);
        for (PaySocialSecurityVo vo : voList) {
            String groupName = insuranceGroupWrapperService.getGroupNameByGroupCode(vo.getGroupCode());
            if(StringUtils.isNotBlank(vo.getCustId())){
                Customer customer = customerMapper.findById(Long.parseLong(vo.getCustId()));
                vo.setCustName(customer.getCustName());
            }
            vo.setGroupName(groupName);
            Optional.ofNullable(packNameMap.get(vo.getPackCode())).ifPresent(insurancePackVo -> {
                vo.setPackName(insurancePackVo.getPackName());
                vo.setSingleFlag(insurancePackVo.getSingleFlag());
            });
        }
        page.setRecords(voList);
        return page;
    }

    @Override
    public <T> void addAndupdateImportData(ImportDataDto<T> importDataDto) {
        addAndupdateImportDataAndLog(importDataDto);
        //更新importData表 中的成功失败数
        updateByImportRecordNumber (importDataDto.getImportNo(), importDataDto.getSuccessNum(), importDataDto.getFailNum());
    }

    @Override
    public <T> void addAndupdateImportDatas(ImportDataDto<T> importDataDto) {
        logger.info ("==========更新成功失败记数值==========");
        Map<Integer, String> errorDesc = importDataDto.getErrorDesc();
        Map<String, String> errorMsg = importDataDto.getErrorMsg();
        Map<Integer, String> remindDesc = importDataDto.getRemindDesc();
        List<ImportDataLog> importDataLogs = Lists.newArrayList();
        Integer successNum = 0,failNum = 0;
        for (ImportDataLogVo r : importDataDto.getImportDataLogVoList()) {
            ImportDataLog importDataLog = new ImportDataLog();
            BeanUtils.copyProperties(r, importDataLog);

            //更新错误记录
            if (errorDesc.containsKey(r.getRowNum())){
                importDataLog.setErrorDesc(errorDesc.get(r.getRowNum()));
                importDataLog.setImportResult (ImportDataLogImportResult.FAIL.getCode());
            }else{
                importDataLog.setImportResult (ImportDataLogImportResult.SUCCESS.getCode());
            }

            //更新错误记录
            if (errorMsg.containsKey(r.getUqKey())){
                importDataLog.setErrorDesc(errorMsg.get(r.getUqKey()));
                importDataLog.setImportResult (ImportDataLogImportResult.FAIL.getCode());
            }

            if(importDataLog.getImportResult() == ImportDataLogImportResult.FAIL.getCode()){
                failNum++;
            } else {
                successNum++;
            }
            if(remindDesc.containsKey(r.getRowNum())){
                importDataLog.setRemind(remindDesc.get(r.getRowNum()));
            }
            importDataLogs.add(importDataLog);
        }

        if(CollectionUtils.isNotEmpty(importDataLogs)){
            insertSelective(importDataLogs);
        }
        //更新importData表 中的成功失败数
        updateByImportRecordNumber (importDataDto.getImportNo(), successNum, failNum);
    }

    @Override
    public <T> void addAndUpdateIncludeReminderImportData(ImportDataDto<T> importDataDto) {
        addAndupdateImportDataAndLog(importDataDto);
        importDataMapper.updateIncludeReminderByImportRecordNumber(importDataDto.getImportNo(), importDataDto.getSuccessNum(), importDataDto.getFailNum(), importDataDto.getRemindNum());
    }

    @Override
    public <T> void updateByImportRecordNumberAndFileId(ImportDataDto<T> importDataDto) {
        addAndupdateImportDataAndLog(importDataDto);
        //更新importData表 中的成功失败数
        importDataMapper.updateByImportRecordNumberAndFileId (importDataDto.getImportNo(), importDataDto.getSuccessNum(), importDataDto.getFailNum(),importDataDto.getFileId());
    }

    private <T> void addAndupdateImportDataAndLog(ImportDataDto<T> importDataDto) {
        logger.info ("==========更新成功失败记数值==========");
        Map<Integer, String> errorDesc = importDataDto.getErrorDesc();
        Map<Integer, String> remindDesc = importDataDto.getRemindDesc();
        List<ImportDataLog> importDataLogs = Lists.newArrayList();
        importDataDto.getImportDataLogVoList().forEach(r -> {
            ImportDataLog importDataLog = new ImportDataLog();
            BeanUtils.copyProperties(r, importDataLog);
            //更新错误记录
            if (errorDesc.containsKey(r.getRowNum())){
                importDataLog.setErrorDesc(errorDesc.get(r.getRowNum()));
                importDataLog.setImportResult (ImportDataLogImportResult.FAIL.getCode());
            }else{
                importDataLog.setImportResult (ImportDataLogImportResult.SUCCESS.getCode());
            }
            if(remindDesc.containsKey(r.getRowNum())){
                importDataLog.setRemind(remindDesc.get(r.getRowNum()));
            }
            importDataLogs.add(importDataLog);
        });
        if(CollectionUtils.isNotEmpty(importDataLogs)){
            insertSelective(importDataLogs);
        }
    }

    /**
     * 创建并插入导入对象
     * @param importDataDto
     */
    public <T> void addImportData(ImportDataDto<T> importDataDto,Integer importDataType){
        ImportData importData = new ImportData();
        importData.setImportNo (importDataDto.getImportNo ());
        importData.setFileId (importDataDto.getFileId ());
        importData.setDataType(importDataType);
        importData.setStatus (ImportDataStatus.PROCESSED.getCode());
        importData.setSuccessNum (0);
        importData.setFailNum (0);
        importData.setOprMan (importDataDto.getLoginName());
        importData.setOprTime (new Date());
        importData.setCreateTime (new Date ());
        importData.setUpdateTime (new Date ());
        importData.setRemark (importDataDto.getRemark());
        importData.setCreator (importDataDto.getLoginName());
        importData.setDelFlag ("N");
        importDataMapper.insertSelective(importData);
    }

    @Override
    public ImportDataLogVo createImportDataLogVo(BaseImportDto importDto,String importNo,String loginName) {
        return getImportDataLogVo(importDto, importNo, loginName);
    }

    @Override
    public ImportDataLogVo createImportDataLogVos(AdjustBaseImportDto importDto, String importNo, String loginName) {
        ImportDataLogVo importDataLogVo = getImportDataLogVo(importDto, importNo, loginName);
        importDataLogVo.setUqKey(importDto.getUqKey());
        return importDataLogVo;
    }

    @Override
    public ImportDataLogVo createAdjustImportDataLogVos(BatchInsurancePracticeAdjustImportVo importDto, String importNo, String loginName) {
        ImportDataLogVo importDataLogVo = getImportDataLogVo(importDto, importNo, loginName);
        importDataLogVo.setUqKey(importDto.getUqKey());
        return importDataLogVo;
    }

    private ImportDataLogVo getImportDataLogVo(BaseImportDto importDto, String importNo, String loginName) {
        ImportDataLogVo importDataLogVo = new ImportDataLogVo();
        importDataLogVo.setRowNum(importDto.getRowNum());
        importDataLogVo.setImportNo(importNo);
        importDataLogVo.setDelFlag("N");
        importDataLogVo.setCreator(loginName);
        importDataLogVo.setCreateTime(new Date());
        importDataLogVo.setUpdater(loginName);
        importDataLogVo.setUpdateTime(new Date());
        importDataLogVo.setImportTxt(createImportTxt(importDto));
        return importDataLogVo;
    }

    @Override
    public String createImportTxt(BaseImportDto importDto) {
        Field[] fieldArray = importDto.getClass().getDeclaredFields();
        Map<String,String> importText = new HashMap<>();
        for(Field field : fieldArray){
            field.setAccessible(true);
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null){
                String fieldStr = "";
                try {
                    Object object = field.get(importDto);
                    if (object != null){
                        fieldStr = object.toString();
                    }
                } catch (IllegalAccessException ignored) {
                }
                if(annotation.value().length>1){
                    importText.put(annotation.value()[0].replace("\n","")+"-"+annotation.value()[1].replace("\n",""),fieldStr);
                }else {
                    importText.put(annotation.value()[0].replace("\n",""),fieldStr);
                }
            }else {
                if(field.getName().equals("dataMap")){
                    try {
                        Map<String,Map<String,String>> dataMap = (Map<String,Map<String,String>>)field.get(importDto);
                        dataMap.forEach((key,value)->{
                            importText.put(key,JSONUtils.toJSONString(value));
                        });
                    } catch (IllegalAccessException ignored) {
                    }
                }
            }
        }
        importText.put("行号",importDto.getRowNum().toString());
        String errorDescription = importDto.getErrorDescription().isEmpty() ? "" : JSONUtils.toJSONString(importDto.getErrorDescription());
        importText.put("错误描述",errorDescription);
        String remind = importDto.getRemind().isEmpty() ? "" : JSONUtils.toJSONString(importDto.getRemind());
        importText.put("提醒",remind);
        return JSONUtils.toJSONString(importText);
    }

    @Override
    public void updateSuccAndFailNum(String importNo) {
        ImportDataVo dataVo = importDataMapper.getById(importNo);
        Integer successNum = dataVo.getSuccessNum();
        Integer failNum = dataVo.getFailNum();
        importDataMapper.updateByImportRecordNumber (importNo, BigDecimal.ZERO.intValue(),(failNum + successNum));
        importDataLogMapper.updateByImportNo(importNo, ImportResultEnum.ERROR.getCode());
    }


}
