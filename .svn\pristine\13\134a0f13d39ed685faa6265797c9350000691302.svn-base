package com.reon.hr.api.customer.enums;

import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/2/2 0002 下午 04:03
 * @Version 1.0
 */

@Getter
public class InjuryEnum {

    @Getter
    public enum WorkInjuryStatusEnum{
        SAVE("保存",1),
         SUBMISSION("接单提交",2),
        BACK_CHANNEL_RECEPTION("后道接收",3),
        THE_LATTER_WAS_DISMISSEd("后道驳回",4),
        POST_COMMITTED("后道提交",5),
        IN_PROGRESS("办理中",6),
        RETURN("退回",7),
        THE_APPLICATION_WAS_SUCCESSFUL("办理成功",8),
        THE_APPLICATION_WAS_FAIL("办理失败",9),
        CANCELLATION("取消办理",10);
        private String name;
        private Integer code;

        WorkInjuryStatusEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.WorkInjuryStatusEnum c : InjuryEnum.WorkInjuryStatusEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static Integer getIndex(String name) {
            for (InjuryEnum.WorkInjuryStatusEnum c : InjuryEnum.WorkInjuryStatusEnum.values()) {
                if (c.getName().equals(name)) {
                    return c.code;
                }
            }
            return null;
        }
    }

    @Getter
    public enum InjuryBigBusinessEnum{
        SOCIAL_SECURITY_PROVIDENT_FUND_TREATMENT("社保公积金待遇",1);
        private String name;
        private Integer code;

        InjuryBigBusinessEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.InjuryBigBusinessEnum c : InjuryEnum.InjuryBigBusinessEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }


    @Getter
    public enum InjurySmallBusinessEnum{
        WORKERS_COMPENSATION_INSURANCE("工伤保险",1),
        MATERNITY_INSURANCE_NORMAL("生育保险(正常)",2),
        MATERNITY_INSURANCE_ABORTION("生育保险(流产)",3);
        private String name;
        private Integer code;

        InjurySmallBusinessEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.InjurySmallBusinessEnum c : InjuryEnum.InjurySmallBusinessEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

    @Getter
    public enum InjurySpecificBusinessEnum{
        WORK_INJURY_FILING("工伤备案（申告）",1),
        RECOGNITION_OF_WORK_RELATED_INJURIES("工伤认定",2),
        WORK_RELATED_INJURY_REIMBURSEMENT("工伤医疗待遇报销",3),
        WORKERS_COMPENSATION_INSURANCE("劳动能力鉴定",4),
        ONE_TIME_DISABILITY_BENEFIT("一次性伤残补助金",5),
        DISABILITY_ALLOWANCE("伤残津贴",6),
        LIVING_CARE_EXPENSES("生活护理费",7),
        ASSISTIVE_DEVICE_CONFIGURATION_FEE("辅助器具配置费",8),
        ONE_TIME_MEDICAL_BENEFIT("一次性医疗补助金",9);

        private String name;
        private Integer code;

        InjurySpecificBusinessEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }
        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.InjurySpecificBusinessEnum c : InjuryEnum.InjurySpecificBusinessEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

    /**
     * 生育保险(正常)具体业务枚举
     *
     * <AUTHOR>
     * @date 2023/02/07
     */
    @Getter
    public enum InjurySpecificBusinessBirthEnum{
        PRENATAL_CARE("产前检查",1),
        MATERNITY_HOSPITALIZATION_MEDICAL_EXPENSES("生育住院医疗费",2),
        MATERNITY_ALLOWANCE("生育津贴",3);
        private String name;
        private Integer code;

        InjurySpecificBusinessBirthEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }
        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.InjurySpecificBusinessBirthEnum c : InjuryEnum.InjurySpecificBusinessBirthEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

    /**
     * 生育保险(流产)具体业务枚举
     *
     * <AUTHOR>
     * @date 2023/02/07
     */
    @Getter
    public enum InjurySpecificBusinessBirthAbortionEnum{
        ABORTION_CLINIC_REIMBURSEMENT("流产门诊报销",1),
        ABORTION_HOSPITALIZATION_INSURANCE("流产住院保险",2),
        ABORTION_BENEFITS("流产津贴",3);
        private String name;
        private Integer code;

        InjurySpecificBusinessBirthAbortionEnum(String name, Integer code) {
            this.name = name;
            this.code = code;
        }
        // 普通方法
        public static String getName(int index) {
            for (InjuryEnum.InjurySpecificBusinessBirthAbortionEnum c : InjuryEnum.InjurySpecificBusinessBirthAbortionEnum.values()) {
                if (c.getCode() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

}
