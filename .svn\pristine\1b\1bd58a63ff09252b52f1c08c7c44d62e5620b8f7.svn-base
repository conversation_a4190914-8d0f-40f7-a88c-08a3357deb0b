<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>支付管理查看</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-elip{
            width: 125px;
        }
        #dataContainer {
            display: flex;
            flex-wrap: wrap;
            gap: 20px; /* 间隔 */
        }

        .row {
            display: flex;
            width: 100%;
            justify-content: space-between;
            margin-left: 10px;
        }

        .item {
            width: 23%; /* 每个条目占 1/4 */
            padding: 10px;
            border: 1px solid #ccc;
            background: #f9f9f9;
        }

        .red {
            color: red; /* 统一值的颜色 */
        }

        .remark-section {
            margin-top: 10px;
        }

        textarea {
            width: 100%;
            padding: 5px;
            margin-top: 5px;
        }

        button {
            display: block;
            margin-top: 5px;
            padding: 6px 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
<div class="layui-tab layui-tab-card " style="height: 70%" lay-filter="paymentApplyTabFilter">
    <ul class="layui-tab-title">
        <li class="layui-this">支付申请</li>
        <li>流程信息</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <div class="layui-fluid">
                <div class="layui-card">
                    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
                        <input id="paymentApplyJson" value='${paymentApplyJson}'  type="hidden">
                        <input id="pid" value="${paymentApply.pid}" type="hidden">
                        <input id="id" name="id" value="${paymentApply.id}" type="hidden">
                        <input id="fileName" name="fileName" value="${paymentApply.fileName}" type="hidden">
                        <input id="fileId" name="fileId" value="${paymentApply.fileId}" type="hidden">
                        <input type="hidden" id="posCode" name="posCode" value="${posCode}">
                        <input type="hidden" id="payKind1" value="${paymentApply.payKind}">
                        <input type="hidden" id="payType1" value="${paymentApply.payType}">
                        <input type="hidden" id="payMethod1" value="${paymentApply.payMethod}">
                        <input type="hidden" id="bankType1" value="${paymentApply.bankType}">
                        <input type="hidden" id="payCom1" value="${paymentApply.payCom}">
                        <input type="hidden" id="payDetailType1" value="${paymentApply.payDetailType}">
                        <input id="taskId" type="hidden" id="taskId" value="${taskId}">
                        <input id="appName" type="hidden">
                        <div class="layui-form-item">

                       <%-- TODO 讨论后增加 --%>

<%--                            <div class="layui-inline">        --%>
<%--                                <label class="layui-form-label layui-elip" title="支付大类">支付大类：</label>--%>
<%--                                <div class="layui-input-inline">--%>
<%--                                    <select class="layui-select layui-select-disabled" name="payKind" id="payKind"  DICT_TYPE="PAYMENT_BROAD_CATEGORIES"--%>
<%--                                            lay-verify="required">--%>
<%--                                        <option value=""></option>--%>
<%--                                    </select>--%>
<%--                                </div>--%>
<%--                            </div>--%>

                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="支付类型">支付类型：</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select layui-select-disabled" name="payType" id="payType" DICT_TYPE="PAYMENT_SUBCLASS"
                                            lay-verify="required">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
<%--                            <div class="layui-inline">--%>
<%--                                <label class="layui-form-label layui-elip layui-onlySelf" title="支付抬头">支付抬头：</label>--%>
<%--                                <div class="layui-input-inline">--%>
<%--                                    <select class="layui-select layui-select-disabled" name="payCom" id="payCom" lay-verify="required">--%>
<%--                                        <option value=""></option>--%>
<%--                                    </select>--%>
<%--                                </div>--%>
<%--                            </div>--%>

                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="福利办理方">福利办理方：</label>
                               <div class="layui-input-inline">
                                   <input class="layui-select" name="orgCode" id="orgCode"
                                          value="${orgName}" readonly lay-search>
                               </div>
                           </div>

                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="福利包名称">福利包名称</label>
                               <div class="layui-input-inline">
                                   <input class="layui-select" name="packCode" id="packCode"
                                          value="${packName}" readonly lay-search>
                               </div>
                           </div>

                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="报表年月">报表年月</label>
                               <div class="layui-input-inline">
                                   <input class="layui-select" name="lockMonth" id="lockMonth"
                                          value="${paymentApply.lockMonth}" readonly lay-search>
                               </div>
                           </div>

                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip" title="支付详细类型"><i style="color: red">*</i>支付详细类型：</label>
                               <div class="layui-input-inline">
                                   <select class="layui-select" name="payDetailType" id="payDetailType"
                                           dict_type="PAY_DETAIL_TYPE" lay-verify="required" >
                                   </select>
                               </div>
                           </div>

                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="支付方式">支付方式：</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select layui-select-disabled" name="payMethod" id="payMethod" DICT_TYPE="MODE_OF_PAYMENT"
                                            lay-filter="templetFilter" lay-verify="required">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="详细支付方式">详细支付方式：</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select" name="otherMethod" id="otherMethod" disabled
                                            dict_type="PRACTICE_PAY_DETAIL_TYPE"  lay-filter="">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
<%--                            <div class="layui-inline">--%>
<%--                                <label class="layui-form-label layui-elip layui-onlySelf" title="收款方">收款方：</label>--%>
<%--                                <div class="layui-input-inline">--%>
<%--                                    <input type="text" class="layui-input layui-input-disposable"--%>
<%--                                           placeholder="请输入" id="payee" name="payee" autocomplete="off"--%>
<%--                                           value="${paymentApply.payee}" readonly>--%>
<%--                                </div>--%>
<%--                            </div>--%>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="社保付款银行">社保付款银行：</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select layui-input-disposable"  name="bankType" DICT_TYPE="BANK"
                                            id="bankType">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="社保付款银行开户行">社保付款银行开户行：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable"
                                           placeholder="请输入" id="depoist" name="depoist" autocomplete="off"
                                           value="${paymentApply.depoist}" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="社保付款银行账号/支票号">社保付款银行账号/支票号：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable"
                                           placeholder="请输入" id="bankNo" name="bankNo" autocomplete="off"
                                           value="${paymentApply.bankNo}" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="单据数量">单据数量：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable"
                                           placeholder="请输入" id="listCnt" name="listCnt" autocomplete="off" maxlength="2"
                                           value="${paymentApply.listCnt}" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="申请支付总额">申请支付总额：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable"
                                           placeholder="请输入" id="applyAmt" name="applyAmt" autocomplete="off"
                                           value="${paymentApply.applyAmt}" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="应付总额">应付总额：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable"
                                           placeholder="请输入" id="payAmt" name="payAmt" autocomplete="off" value="${paymentApply.payAmt}" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="最晚支付日期">最晚支付日期：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                                           name="lastDate" id="lastDate" placeholder="请选择"
                                           value="${paymentApply.lastDate}" lay-verify="required" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="支付所属年月">支付所属年月：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable" maxlength="20"
                                           name="payMonth"
                                           id="payMonth" value="${paymentApply.payMonth}" placeholder="请选择" readonly>
                                </div>
                            </div>
<%--                            <div class="layui-inline">--%>
<%--                                <label class="layui-form-label layui-elip layui-onlySelf" title="客户">客户：</label>--%>
<%--                                <div class="layui-input-inline">--%>
<%--                                    <input type="text" class="layui-input layui-input-disposable" readonly id="custName"--%>
<%--                                           autocomplete="off" class="layui-input" lay-verType="tips"  placeholder="请选择" style="padding-left: 10px;width: 190px;"/>--%>
<%--                                    <input type="text" name="custId" id="custId" style="display: none;" value="${paymentApply.custId}" />--%>
<%--                                </div>--%>
<%--                            </div>--%>

                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="支付地">支付地</label>
                               <div class="layui-input-inline">
                                   <input class="layui-select" name="appComName" id="appComName"
                                          value="${appComName}" >
                               </div>
                           </div>

                           <div class="layui-inline" id="actPayAmtDiv">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="实际支付总额">实际支付总额</label>
                               <div class="layui-input-inline">
                                   <input type="number" class="layui-select" name="actPayAmt" id="actPayAmt" value="${paymentApply.actPayAmt}" lay-search>
                               </div>
                           </div>
                           <div class="layui-inline" >
                               <label class="layui-form-label layui-elip layui-onlySelf" title="收款账户">收款账户</label>
                               <div class="layui-input-inline">
                                   <input type="text" class="layui-select" name="" id="payBankNo" value="${paymentApply.payBankNo}" >
                               </div>
                           </div>
                           <div class="layui-inline">
                               <label class="layui-form-label layui-elip layui-onlySelf" title="收款账户名称">收款账户名称</label>
                               <div class="layui-input-inline">
                                   <input type="text" class="layui-select" name="" id="payBankName" value="${paymentApply.payBankName}" >
                               </div>
                           </div>

                            <div class="">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="付款用途">付款用途：</label>
                                <div class="layui-input-inline" style="width: 840px;">
                            <textarea placeholder="请输入内容" name="purpose" id="purpose"
                                      class="layui-textarea" autocomplete="off"  readonly>${paymentApply.purpose}</textarea>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label layui-elip layui-onlySelf" title="备注">备注：</label>
                                <div class="layui-input-inline" style="width: 840px;">
                            <textarea placeholder="请输入内容" name="remark" id="remark"  class="layui-textarea"
                                      autocomplete="off" readonly>${paymentApply.remark}</textarea>
                                </div>
                            </div>
                           <div id="dataContainerDiv" hidden="hidden">
                               <fieldset class="layui-elem-field layui-field-title">
                                   <legend>公积金多比例数据</legend>
                               </fieldset>
                               <div class="layui-input-block">
                                   <table id="dataContainer" lay-filter="dataContainerFilter"></table>
                               </div>
                           </div>
                           <div id="insurancePracticeDisComDiv" hidden="hidden">
                               <fieldset class="layui-elem-field layui-field-title">
                                   <legend>各地应付金额</legend>
                               </fieldset>
                               <div class="layui-input-block">
                                   <table id="insurancePracticeDisCom" lay-filter="floatInsuranceFilter"></table>
                               </div>
                           </div>
                            <fieldset class="layui-elem-field layui-field-title">
                                <legend>文件上传</legend>
                            </fieldset>
                            <div class="layui-upload">
                                <%--<button type="button" class="layui-btn layui-btn-normal" id="quotationUpload">选择文件</button>--%>
                                <button type="button" id="paymentApplyUpload" class="layui-btn layui-btn-normal">选择文件
                                </button>
                                <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                                    预览图：
                                    <div class="layui-upload-list" id="upload"></div>
                                </blockquote>
                            </div>
                            <div class="layui-form-item " id="approvalDiv" style="display: none">
                                <label class="layui-form-label ">&nbsp;审批意见：</label>
                                <div class="layui-input-block" style="width: 840px;">
                                    <textarea placeholder="请输入内容" name="comment" id="comment"
                                              class="layui-textarea" style="min-width: 55px"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="layui-inline" style="float: right;padding:10px;display: none" id="flg">
                        <button class="layui-btn layuiadmin-btn-list" id="commit" lay-filter="commit"
                                lay-submit="">提交
                        </button>
                        <button class="layui-btn layuiadmin-btn-list" id="rejected" lay-filter="rejected"
                                lay-submit="">驳回
                        </button>
                        <button class="layui-btn layuiadmin-btn-list" id="close" lay-filter="close"
                                lay-submit="">取消
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <%--流程信息--%>
        <div class="layui-tab-item">
            <div class="layui-fluid">
                <div class="layui-card">
                    <img id="workFlowImg" src=""/>
                    <div class="layui-form-item layui-hide"  style="margin-top: 15px;" id="currentItem">
                        <label class="layui-form-label filed-length" id="currentApproverName" ></label>
                        <div class="layui-input-inline">
                            <input type="text" id="currentApproverAssignee"
                                   readonly disabled
                                   class="layui-input layui-anim layui-anim-scaleSpring" value=""/>
                        </div>
                    </div>
                    <table class="layui-table" id="paymentApplyFlowTable" lay-filter="paymentApplyFlowTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/paymentApply/paymentApplyCheck.js?v=${publishVersion}"></script>
</body>
</html>
