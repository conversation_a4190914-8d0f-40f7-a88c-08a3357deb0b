package com.reon.hr.sp.bill.service.impl.paymentApply;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ICompanyBankWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISequenceService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ISystemConfigWrapperService;
import com.reon.hr.api.base.enums.CompanyBankDataType;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.api.bill.enums.PaymentApplyDocumentStatusEnum;
import com.reon.hr.api.bill.enums.PaymentApplyProcessStatus;
import com.reon.hr.api.bill.vo.PaymentApplyLastDateLogVo;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.salary.PayServiceSerialLogVo;
import com.reon.hr.api.bill.vo.salary.SalaryPaymentApplyVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.PayEnum;
import com.reon.hr.api.customer.enums.WithholdingAgentEnum;
import com.reon.hr.api.customer.enums.employee.EmpCardInfoBankNameEnum;
import com.reon.hr.api.customer.enums.salary.SalaryInfoStatus;
import com.reon.hr.api.customer.utils.EnumsUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.salary.onlineBankingFile.EmpExcelVo;
import com.reon.hr.api.customer.vo.salary.pay.EmpDelaySearchVo;
import com.reon.hr.api.customer.vo.salary.pay.GeneratedNetEmp;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.thirdpart.dubbo.service.rpc.bankcorp.cmb.ICMBpayWrapperService;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.api.workflow.constant.ManualAction;
import com.reon.hr.api.workflow.constant.ReonWorkflowType;
import com.reon.hr.api.workflow.dto.TaskQueryDTO;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.ActRuTaskVo;
import com.reon.hr.api.workflow.vo.TaskVo;
import com.reon.hr.common.cmb.*;
import com.reon.hr.common.cmb.dto.IntegrationDto;
import com.reon.hr.common.cmb.notify.payResult.PayBusinessCompletion;
import com.reon.hr.common.cmb.notify.payResult.PayBusinessCompletionMsg;
import com.reon.hr.common.cmb.notify.payResult.PayRefundMsg;
import com.reon.hr.common.cmb.notify.payroll.AgcInfo;
import com.reon.hr.common.cmb.notify.payroll.AgentRefundDetailInfo;
import com.reon.hr.common.cmb.notify.payroll.PayrollBusinessCompletion;
import com.reon.hr.common.cmb.notify.payroll.PayrollRefund;
import com.reon.hr.common.constant.CMBPayConstant;
import com.reon.hr.common.enums.DelFlagEnum;
import com.reon.hr.common.enums.PaymentApplyPayTypeEnum;
import com.reon.hr.common.enums.PrintFlagEnum;
import com.reon.hr.common.enums.SalaryPaymentWorkflowEnum;
import com.reon.hr.common.enums.pay.BusCodeEn;
import com.reon.hr.common.enums.pay.ReqStsEn;
import com.reon.hr.common.enums.pay.RtnFlgEn;
import com.reon.hr.common.enums.payrollBatchInfo.ResultEn;
import com.reon.hr.common.enums.salary.PayServiceSerialDetailType;
import com.reon.hr.common.enums.salary.PayServiceSerialType;
import com.reon.hr.common.utils.BigDecimalUtil;
import com.reon.hr.common.utils.CheckStrUtil;
import com.reon.hr.sp.bill.dao.bill.PaymentApplyLastDateLogMapper;
import com.reon.hr.sp.bill.dao.bill.PaymentApplyMapper;
import com.reon.hr.sp.bill.dao.bill.SalaryPaymentApplyMapper;
import com.reon.hr.sp.bill.dao.cus.BillCostMapper;
import com.reon.hr.sp.bill.dao.salary.*;
import com.reon.hr.sp.bill.entity.bill.PaymentApply;
import com.reon.hr.sp.bill.service.bill.insurancePractice.IPracticePayDetailService;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.paymentApply.ISalaryPaymentApplyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Service
public class SalaryPaymentApplyServiceImpl extends ServiceImpl<PaymentApplyMapper, PaymentApply> implements ISalaryPaymentApplyService {
    private static final Logger logger = LoggerFactory.getLogger(SalaryPaymentApplyServiceImpl.class);
    @Autowired
    private PaymentApplyMapper paymentApplyMapper;
    @Autowired
    private PaymentApplyLastDateLogMapper paymentApplyLastDateLogMapper;
    @Autowired
    private IPaymentApplyService iPaymentApplyService;
    @Autowired
    private PayApply2BatchMapper apply2BatchMapper;
    @Autowired
    private SalaryPayBatchMapper salaryPayBatchMapper;
    @Resource(name = "orgDubboService")
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Autowired
    private ISalaryPayWrapperService iSalaryPayWrapperService;
    @Autowired
    private ICustomerWrapperService customerWrapperService;
    @Autowired
    private ISupplierWrapperService supplierWrapperService;
    @Autowired
    private BillCostMapper billCostMapper;
    @Resource
    private IPracticePayDetailService practicePayDetailService;
    @Autowired
    private IUserWrapperService userWrapperService;
    @Autowired
    private PayServiceSerialLogMapper payServiceSerialLogMapper;
    @Autowired
    private ICompanyBankWrapperService companyBankWrapperService;
    @Autowired
    private ISystemConfigWrapperService iSystemConfigWrapperService;
    @Autowired
    private ISupplierWrapperService iSupplierWrapperService;
    @Autowired
    private IUserOrgPosWrapperService iUserOrgPosWrapperService;
    @Autowired
    private IWorkflowWrapperService workflowWrapperService;
    @Autowired
    private IPaymentApplyService paymentApplyService;
    @Autowired
    private ICMBpayWrapperService icmBpayWrapperService;
    @Autowired
    private SalaryPaymentApplyMapper salaryPaymentApplyMapper;

    private static final String ERR_MSG="支付中";
    private static final List<String> SPECIAL_APPROVA_LIST= Lists.newArrayList("liuwei");

    @Resource
    private ISequenceService iSequenceService;
    @Override
    public List<IntegrationDto> getIntegrationDtoList(List<String> pidList, Integer documentStatus, Map<String, Map<String, Object>> oldVariablesMap, String updater) {
        List<IntegrationDto> integrationDtoList=new ArrayList<>();
        if(documentStatus!=null){
            boolean reviewedFlag = Objects.equals(PaymentApplyDocumentStatusEnum.REVIEWED.getCode(), documentStatus);
            boolean ownReviewedFlag = Objects.equals(PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode(), documentStatus);

            boolean salaryDataRefundTestFlag = Boolean.parseBoolean(iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_DATA_REFUND_TEST_FLAG).getCfgValue());

            Integer queryDocumentStatus=reviewedFlag?PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode():PaymentApplyDocumentStatusEnum.REVIEWED.getCode();
            List<GeneratedNetEmp> generatedNetEmpList = iSalaryPayWrapperService.getEmpByBatchIdList(paymentApplyMapper.getBatchIdListByPidList(pidList), queryDocumentStatus);
            Map<String, GeneratedNetEmp> generatedNetEmpMap = generatedNetEmpList.stream().collect(toMap(GeneratedNetEmp::getPid, Function.identity()));
            List<Long> paymentIdList = generatedNetEmpList.stream().map(GeneratedNetEmp::getPaymentId).distinct().collect(Collectors.toList());
            Map<String, PayServiceSerialLogVo> payServiceSerialLogVoMap =new HashMap<>();
            if(CollectionUtils.isNotEmpty(paymentIdList)){
                List<PayServiceSerialLogVo> payServiceSerialLogVoList=payServiceSerialLogMapper.getByPaymentIdList(paymentIdList);
                payServiceSerialLogVoMap = payServiceSerialLogVoList.stream().collect(toMap(p -> p.getPaymentId() + "-" + p.getPayServiceSerialType() + "-" + p.getPayServiceSerialDetailType(), Function.identity()));
            }


            for (String pid:pidList) {
                if(oldVariablesMap.containsKey(pid)){
                    if(generatedNetEmpMap.containsKey(pid)){
                        GeneratedNetEmp generatedNetEmp = generatedNetEmpMap.get(pid);
                        if(DateUtil.getCurrentYearMonthDate()>Integer.parseInt(generatedNetEmp.getLastDate())){
                            throw new RuntimeException(ERR_MSG+"存在工资支付日期小于当前年月日的");
                        }
                    }
                    IntegrationDto integrationDto=new IntegrationDto();
                    YurrefArgs yurrefArgs = new YurrefArgs(Boolean.FALSE);
                    Map<String, Object> variables = oldVariablesMap.get(pid);
                    Integer payrollMethod= (Integer) variables.get("payrollMethod");
                    Integer dispatchBankPay= (Integer) variables.get("dispatchBankPay");
                    Integer receiveBankPay= (Integer) variables.get("receiveBankPay");
                    Integer normal= (Integer) variables.get("normal");
                    //正常发薪
                    if(!Objects.equals(normal, SalaryPaymentWorkflowEnum.Normal.SUPPLIER_REFUND.getCode())){
                        //派单地是招商银行，复核
                        if(reviewedFlag&&Objects.equals(dispatchBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                            //自有本地发薪，代发接口。
                            if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode())){
                                if(generatedNetEmpMap.containsKey(pid)){
                                    GeneratedNetEmp generatedNetEmp = generatedNetEmpMap.get(pid);
                                    List<EmpExcelVo> emps = generatedNetEmp.getEmps();
                                    if(CollectionUtils.isNotEmpty(emps)){
                                        EmpExcelVo empExcelVo0 = emps.get(0);
                                        List<PayrollDetailInfo> detailInfoList=new ArrayList<>();
                                        for (int i = 0; i < emps.size(); i++) {
                                            PayrollDetailInfo payrollDetailInfo = new PayrollDetailInfo();
                                            //payrollDetailInfo.setTrxseq(String.valueOf(i+1));
                                            EmpExcelVo empExcelVo = emps.get(i);
                                            payrollDetailInfo.setAccnbr(empExcelVo.getCardNo());
                                            payrollDetailInfo.setAccnam(empExcelVo.getAcctName());
                                            payrollDetailInfo.setTrsamt(BigDecimalUtil.getTwoDecimalPlacesStr(empExcelVo.getS007()));
                                            payrollDetailInfo.setEacbnk(empExcelVo.getBankName());
                                            payrollDetailInfo.setEaccty(empExcelVo.getOpeningPlace());
                                            String salaryBatchDetailId = empExcelVo.getSalaryBatchDetailId().toString();
                                            payrollDetailInfo.setTrxseq(salaryBatchDetailId.substring(Math.max(salaryBatchDetailId.length() - 8, 0)));
                                            payrollDetailInfo.setTrsdsp(empExcelVo.getRemittanceRemark());
                                            if(salaryDataRefundTestFlag){
                                                payrollDetailInfo.setBnkflg("N");
                                            }
                                            detailInfoList.add(payrollDetailInfo);

                                        }
                                        PayrollBatchInfo batchInfo=new PayrollBatchInfo();
                                        CompanyBankVo payComBankVo = generatedNetEmp.getPayComBankVo();
                                        if(payComBankVo==null){
                                            throw new RuntimeException(ERR_MSG+"存在出款方账户为null");
                                        }
                                        yurrefArgs.setBusCode(payComBankVo.getPayRollType());
                                        integrationDto.setYurrefArgs(yurrefArgs);
                                        batchInfo.setBegtag("Y");
                                        batchInfo.setEndtag("Y");
                                        batchInfo.setAccnbr(payComBankVo.getBankNo());
                                        batchInfo.setAccnam(payComBankVo.getAccountName());
                                        String subTotal = BigDecimalUtil.getTwoDecimalPlacesStr(generatedNetEmp.getSubTotal());
                                        batchInfo.setTtlamt(subTotal);
                                        String ttlcnt = String.valueOf(detailInfoList.size());
                                        batchInfo.setTtlcnt(ttlcnt);
                                        batchInfo.setTtlnum("1");
                                        batchInfo.setCuramt(subTotal);
                                        batchInfo.setCurcnt(ttlcnt);
                                        if(salaryDataRefundTestFlag){
                                            batchInfo.setChlflg("N");
                                        }
                                        batchInfo.setNusage(generatedNetEmp.getPaymentContentSummary());
                                        if(DateUtil.getCurrentYearMonthDate()>Integer.parseInt(generatedNetEmp.getLastDate())){
                                            throw new RuntimeException(ERR_MSG+"存在工资支付日期小于当前年月日的");
                                        }
                                        batchInfo.setEptdat(generatedNetEmp.getLastDate());
                                        PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM;
                                        String logKey = generatedNetEmp.getPaymentId() + "-" + payServiceSerialDetailType.getParentCode() + "-" + payServiceSerialDetailType.getCode();
                                        if(payServiceSerialLogVoMap.containsKey(logKey)){
                                            PayServiceSerialLogVo payServiceSerialLogVo = payServiceSerialLogVoMap.get(logKey);
                                            batchInfo.setYurref(payServiceSerialLogVo.getYurref());
                                        }else {
                                            throw new RuntimeException(ERR_MSG+"存在业务参考号为null的");
                                        }
                                        integrationDto.setBatchInfo(batchInfo);
                                        integrationDto.setDetailInfoList(detailInfoList);
                                        integrationDto.setType(payServiceSerialDetailType.getParentCode());
                                        integrationDto.setPaymentId(generatedNetEmp.getPaymentId());
                                        integrationDto.setPid(generatedNetEmp.getPid());
                                        integrationDtoList.add(integrationDto);
                                    }
                                }
                            }else {
                                //其它都是转账
                                if(generatedNetEmpMap.containsKey(pid)){
                                    GeneratedNetEmp generatedNetEmp = generatedNetEmpMap.get(pid);
                                    TransAcctInfo transAcctInfo = new TransAcctInfo();
                                    CompanyBankVo payComBankVo = generatedNetEmp.getPayComBankVo();
                                    if(payComBankVo==null){
                                        throw new RuntimeException(ERR_MSG+"存在出款方账户为null");
                                    }
                                    yurrefArgs.setBusCode(payComBankVo.getPayRollType());
                                    integrationDto.setYurrefArgs(yurrefArgs);
                                    transAcctInfo.setDbtAcc(payComBankVo.getBankNo());
                                    CompanyBankVo revComBankVo = generatedNetEmp.getRevComBankVo();
                                    if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_OFFSITE_SUPPLIER_PAYROLL.getCode())){
                                        revComBankVo=generatedNetEmp.getPayAssociatedComBankVo();
                                    }
                                    if(revComBankVo==null){
                                        throw new RuntimeException(ERR_MSG+"存在收款方账户为null");
                                    }
                                    transAcctInfo.setCrtAcc(revComBankVo.getBankNo());
                                    transAcctInfo.setCrtNam(revComBankVo.getAccountName());
                                    transAcctInfo.setCrtBnk(EnumsUtil.getNameByCode(revComBankVo.getBankType(), EmpCardInfoBankNameEnum.class));
                                    if(revComBankVo.getBankType()==EmpCardInfoBankNameEnum.OTHER_BANKS.getCode()){
                                        transAcctInfo.setCrtBnk(revComBankVo.getBankName());
                                    }
                                    transAcctInfo.setCrtAdr(revComBankVo.getOpeningPlace());
                                    transAcctInfo.setTrsAmt(BigDecimalUtil.getTwoDecimalPlacesStr(generatedNetEmp.getSubTotal()));
                                    transAcctInfo.setNusAge(generatedNetEmp.getPaymentContentSummary());
                                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_PAY_COM;
                                    String logKey = generatedNetEmp.getPaymentId() + "-" + payServiceSerialDetailType.getParentCode() + "-" + payServiceSerialDetailType.getCode();
                                    if(payServiceSerialLogVoMap.containsKey(logKey)){
                                        PayServiceSerialLogVo payServiceSerialLogVo = payServiceSerialLogVoMap.get(logKey);
                                        transAcctInfo.setYurRef(payServiceSerialLogVo.getYurref());
                                    }else {
                                        throw new RuntimeException(ERR_MSG+"存在业务参考号为null的");
                                    }
                                    integrationDto.setType(payServiceSerialDetailType.getParentCode());
                                    integrationDto.setTransAcctInfo(transAcctInfo);
                                    integrationDto.setPaymentId(generatedNetEmp.getPaymentId());
                                    integrationDto.setPid(generatedNetEmp.getPid());
                                    integrationDtoList.add(integrationDto);
                                }
                            }
                        }
                        //接单地是招商银行，自有复核
                        if(ownReviewedFlag&&Objects.equals(receiveBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                            //自有异地发薪，代发接口。
                            if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_OFFSITE_PAYROLL.getCode())){
                                if(generatedNetEmpMap.containsKey(pid)){
                                    GeneratedNetEmp generatedNetEmp = generatedNetEmpMap.get(pid);
                                    List<EmpExcelVo> emps = generatedNetEmp.getEmps();
                                    if(CollectionUtils.isNotEmpty(emps)){
                                        EmpExcelVo empExcelVo0 = emps.get(0);
                                        List<PayrollDetailInfo > detailInfoList=new ArrayList<>();
                                        for (int i = 0; i < emps.size(); i++) {
                                            PayrollDetailInfo payrollDetailInfo = new PayrollDetailInfo();
                                            //payrollDetailInfo.setTrxseq(String.valueOf(i+1));
                                            EmpExcelVo empExcelVo = emps.get(i);
                                            payrollDetailInfo.setAccnbr(empExcelVo.getCardNo());
                                            payrollDetailInfo.setAccnam(empExcelVo.getAcctName());
                                            payrollDetailInfo.setTrsamt(BigDecimalUtil.getTwoDecimalPlacesStr(empExcelVo.getS007()));
                                            payrollDetailInfo.setEacbnk(empExcelVo.getBankName());
                                            payrollDetailInfo.setEaccty(empExcelVo.getOpeningPlace());
                                            String salaryBatchDetailId = empExcelVo.getSalaryBatchDetailId().toString();
                                            payrollDetailInfo.setTrxseq(salaryBatchDetailId.substring(Math.max(salaryBatchDetailId.length() - 8, 0)));
                                            payrollDetailInfo.setTrsdsp(empExcelVo.getRemittanceRemark());
                                            if(salaryDataRefundTestFlag){
                                                payrollDetailInfo.setBnkflg("N");
                                            }
                                            detailInfoList.add(payrollDetailInfo);

                                        }
                                        PayrollBatchInfo batchInfo=new PayrollBatchInfo();
                                        CompanyBankVo revComBankVo = generatedNetEmp.getRevComBankVo();
                                        if(revComBankVo==null){
                                            throw new RuntimeException(ERR_MSG+"存在出款方账户为null");
                                        }
                                        yurrefArgs.setBusCode(revComBankVo.getPayRollType());
                                        integrationDto.setYurrefArgs(yurrefArgs);
                                        batchInfo.setBegtag("Y");
                                        batchInfo.setEndtag("Y");
                                        batchInfo.setAccnbr(revComBankVo.getBankNo());
                                        batchInfo.setAccnam(revComBankVo.getAccountName());
                                        String subTotal = BigDecimalUtil.getTwoDecimalPlacesStr(generatedNetEmp.getSubTotal());
                                        batchInfo.setTtlamt(subTotal);
                                        String ttlcnt = String.valueOf(detailInfoList.size());
                                        batchInfo.setTtlcnt(ttlcnt);
                                        batchInfo.setTtlnum("1");
                                        batchInfo.setCuramt(subTotal);
                                        batchInfo.setCurcnt(ttlcnt);
                                        if(salaryDataRefundTestFlag){
                                            batchInfo.setChlflg("N");
                                        }
                                        batchInfo.setNusage(generatedNetEmp.getPaymentContentSummary());
                                        if(DateUtil.getCurrentYearMonthDate()>Integer.parseInt(generatedNetEmp.getLastDate())){
                                            throw new RuntimeException(ERR_MSG+"存在工资支付日期小于当前年月日的");
                                        }
                                        batchInfo.setEptdat(generatedNetEmp.getLastDate());
                                        PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_REV_COM;
                                        String logKey = generatedNetEmp.getPaymentId() + "-" + payServiceSerialDetailType.getParentCode() + "-" + payServiceSerialDetailType.getCode();
                                        if(payServiceSerialLogVoMap.containsKey(logKey)){
                                            PayServiceSerialLogVo payServiceSerialLogVo = payServiceSerialLogVoMap.get(logKey);
                                            batchInfo.setYurref(payServiceSerialLogVo.getYurref());
                                        }else {
                                            throw new RuntimeException(ERR_MSG+"存在业务参考号为null的");
                                        }
                                        integrationDto.setBatchInfo(batchInfo);
                                        integrationDto.setDetailInfoList(detailInfoList);
                                        integrationDto.setType(payServiceSerialDetailType.getParentCode());
                                        integrationDto.setPaymentId(generatedNetEmp.getPaymentId());
                                        integrationDto.setPid(generatedNetEmp.getPid());
                                        integrationDtoList.add(integrationDto);
                                    }
                                }
                            }else {
                                //其它都是转账
                                if(generatedNetEmpMap.containsKey(pid)){
                                    GeneratedNetEmp generatedNetEmp = generatedNetEmpMap.get(pid);
                                    TransAcctInfo transAcctInfo = new TransAcctInfo();
                                    CompanyBankVo payAssociatedComBankVo = generatedNetEmp.getPayAssociatedComBankVo();
                                    if(payAssociatedComBankVo==null){
                                        throw new RuntimeException(ERR_MSG+"存在出款方账户为null");
                                    }
                                    yurrefArgs.setBusCode(payAssociatedComBankVo.getPayRollType());
                                    integrationDto.setYurrefArgs(yurrefArgs);
                                    transAcctInfo.setDbtAcc(payAssociatedComBankVo.getBankNo());
                                    CompanyBankVo revComBankVo = generatedNetEmp.getRevComBankVo();
                                    if(revComBankVo==null){
                                        throw new RuntimeException(ERR_MSG+"存在收款方账户为null");
                                    }
                                    transAcctInfo.setCrtAcc(revComBankVo.getBankNo());
                                    transAcctInfo.setCrtNam(revComBankVo.getAccountName());
                                    transAcctInfo.setCrtBnk(EnumsUtil.getNameByCode(revComBankVo.getBankType(), EmpCardInfoBankNameEnum.class));
                                    if(revComBankVo.getBankType()==EmpCardInfoBankNameEnum.OTHER_BANKS.getCode()){
                                        transAcctInfo.setCrtBnk(revComBankVo.getBankName());
                                    }
                                    transAcctInfo.setCrtAdr(revComBankVo.getOpeningPlace());
                                    transAcctInfo.setTrsAmt(generatedNetEmp.getSubTotal().toString());
                                    transAcctInfo.setNusAge(generatedNetEmp.getPaymentContentSummary());
                                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_REY_COM;
                                    String logKey = generatedNetEmp.getPaymentId() + "-" + payServiceSerialDetailType.getParentCode() + "-" + payServiceSerialDetailType.getCode();
                                    if(payServiceSerialLogVoMap.containsKey(logKey)){
                                        PayServiceSerialLogVo payServiceSerialLogVo = payServiceSerialLogVoMap.get(logKey);
                                        transAcctInfo.setYurRef(payServiceSerialLogVo.getYurref());
                                    }else {
                                        throw new RuntimeException(ERR_MSG+"存在业务参考号为null的");
                                    }
                                    integrationDto.setType(payServiceSerialDetailType.getParentCode());
                                    integrationDto.setTransAcctInfo(transAcctInfo);
                                    integrationDto.setPaymentId(generatedNetEmp.getPaymentId());
                                    integrationDto.setPid(generatedNetEmp.getPid());
                                    integrationDtoList.add(integrationDto);
                                }
                            }
                        }
                    }
                }
            }
        }
        return integrationDtoList;
    }

    @Override
    public PaymentApplyVo getStartPaymentApplyWorkflowVariables(PaymentApplyVo paymentApplyVo, UserOrgPosVo userOrgPosVo) throws Exception {
        List<PayServiceSerialLogVo> payServiceSerialLogVoList = new ArrayList<>();
        String[] salaryPaymentArea = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_AREA).getCfgValue().split(",");
        String[] salaryPaymentCom = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_COM).getCfgValue().split(",");
        String[] onlyOwnCompanyLocalPayroll = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.ONLY_OWN_COMPANY_LOCAL_PAYROLL).getCfgValue().split(",");
        String exceptionMsg="工资支付流程审批中组织人员缺失";
        Map<String, Object> varMaps = Maps.newHashMap();
        //供应商客服确认（供应商客服专员）
        String confirmMan = "";
        //供应商客服经理复核（供应商客服经理）
        String supplierManager = "";
        //客服经理
        String comManager;
        //财务制单（财务制单专员）
        String financialOfficer;
        //财务复核（财务复核专员）
        String financialManager;
        //发薪地财务制单（财务制单专员）
        String secondFinancialOfficer = "";
        //发薪地财务复核（财务复核专员）
        String secondFinancialManager = "";
        Integer ownerFlag = 1;
        Integer ownerOtherPlace = 2;
        // normal==0 派单地退票
        // normal==1 正常发薪
        // normal==2 接单地退票
        // normal==3 供应商退票
        Integer normal = SalaryPaymentWorkflowEnum.Normal.PAYROLL.getCode();
        Integer overtimeFlag = 0;
        // dispatchBankPay == 1 派单地是招商银行
        // dispatchBankPay == 0 派单地不是招商银行
        Integer dispatchBankPay = SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        // payrollMethod == 1 自有公司本地发薪
        // payrollMethod == 2 自有公司异地发薪
        // payrollMethod == 3 自有公司本地供应商发薪
        // payrollMethod == 4 自有公司异地供应商发薪
        Integer payrollMethod = SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode();
        // receiveBankPay == 1 接单地是招商银行
        // receiveBankPay == 0 接单地不是招商银行
        Integer receiveBankPay = SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        //申请人
        varMaps.put("starter", paymentApplyVo.getCreator());
        String orgCode = userOrgPosVo.getOrgCode().length() >= 9 ? userOrgPosVo.getOrgCode().substring(0, 9) : userOrgPosVo.getOrgCode();
        String posCode = userOrgPosVo.getPosCode().length() > 1 ? userOrgPosVo.getPosCode().substring(0, userOrgPosVo.getPosCode().length() - 1) : userOrgPosVo.getPosCode();
        String comManagerPosCode=PositionEnum.SALARY_MANAGER.getCode().contains(posCode)? posCode :PositionEnum.SALARY_MANAGER.getCode();
        if(comManagerPosCode.equals(PositionEnum.SALARY_DISTRICT_MANAGER.getCode())){
            //区域总监审批时使用approveOrgPosCode去查找
            String approveOrgPosCode = userOrgPosVo.getApproveOrgPosCode();
            if(StringUtils.isBlank(approveOrgPosCode)){
                orgCode=orgCode.substring(0,3);
            }else {
                String[] approveOrgPosCodeSplit = approveOrgPosCode.split(",");
                orgCode=approveOrgPosCodeSplit[0];
                comManagerPosCode=approveOrgPosCodeSplit[1];
            }
        }else if(comManagerPosCode.equals(PositionEnum.PROJECT_VP.getCode())){
            orgCode=orgCode.substring(0,1);
        }
        /*if(SPECIAL_APPROVA_LIST.contains(paymentApplyVo.getCreator())){
            orgCode=orgCode.substring(0,1);
            comManagerPosCode=PositionEnum.PROJECT_VP.getCode();
        }*/
        List<UserOrgPosVo> comManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(orgCode,comManagerPosCode);
        if(CollectionUtils.isNotEmpty(comManagerList)){
            comManager = getOrgPosCodeStrByOrgAndPosCode(orgCode, comManagerPosCode);
        }else {
            exceptionMsg+="客服经理";
            throw new Exception(exceptionMsg);
        }
        varMaps.put("comManager", comManager);
        String financialOfficerPosCode=PositionEnum.TREASURY_DOCUMENTATION_ATTACHE.getCode();
        String financialOrgCode = paymentApplyVo.getPayCom();
        List<UserOrgPosVo> financialOfficerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(financialOrgCode,financialOfficerPosCode);
        if(CollectionUtils.isNotEmpty(financialOfficerList)){
            financialOfficer = getOrgPosCodeStrByOrgAndPosCode(financialOfficerList.get(0).getOrgCode(), financialOfficerPosCode);
        }else {
            exceptionMsg+="财务制单（财务制单专员）";
            throw new Exception(exceptionMsg);
        }
        varMaps.put("financialOfficer", financialOfficer);
        String financialManagerPosCode=PositionEnum.TREASURY_RECHECK_ATTACHE.getCode();
        List<UserOrgPosVo> financialManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(financialOrgCode,financialManagerPosCode);
        if(CollectionUtils.isNotEmpty(financialManagerList)){
            financialManager = getOrgPosCodeStrByOrgAndPosCode(financialManagerList.get(0).getOrgCode(), financialManagerPosCode);
        }else {
            exceptionMsg+="财务复核（财务复核专员）";
            throw new Exception(exceptionMsg);
        }
        varMaps.put("financialManager", financialManager);
        List<CompanyBankVo> allCompanyBankVoList = companyBankWrapperService.getAllComp(null, null);
        Map<String, CompanyBankVo> companyBankVoMap =allCompanyBankVoList.stream().filter(c->c.getType()==null||c.getType().equals(CompanyBankDataType.CURRENCY.getCode())).collect(toMap(CompanyBankVo::getCompNo,Function.identity()));
        Map<String, CompanyBankVo> specialCompanyBankVoMap=allCompanyBankVoList.stream().filter(c->c.getType()!=null&&c.getType().equals(CompanyBankDataType.SPECIAL.getCode())).collect(toMap(c -> c.getBankNo() + "-" + c.getCustId(), Function.identity()));
        CompanyBankVo dispatchBankVo =new CompanyBankVo();
        String specialCompanyBankKey = paymentApplyVo.getBankNo() + "-" + paymentApplyVo.getCustId();
        if(companyBankVoMap.containsKey(paymentApplyVo.getPayCom())){
            CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getPayCom());
            if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
            }
            //开关  招商银行 上海地区的先启动
            CompanyBankVo finalCompanyBankVo = companyBankVo;
            if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                    &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                    &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.PAY_COM))
            ){
                dispatchBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                dispatchBankVo=companyBankVo;
            }
        }
        if(paymentApplyVo.getAnewPayFlag()== BooleanTypeEnum.YES.getCode()){
            normal=SalaryPaymentWorkflowEnum.Normal.PAY_COM_REFUND.getCode();
        }
        boolean checkStrResult = CheckStrUtil.checkStr(paymentApplyVo.getPaymentContentSummary(), CheckStrUtil.SUMMARY_RESTRICTION);
        if(Objects.equals(dispatchBankVo.getSummaryRestrictionFlag(),BooleanTypeEnum.YES.getCode())&&Objects.equals(dispatchBankVo.getPayRollType(), BusCodeEn.PAY_OTHERS.getCode())){
            if(checkStrResult){
                throw new Exception(ResultEnum.ERROR_PREFIX+"出款方银行卡为代发其他类型,且被银行提示支付内容摘要不能含"+ Arrays.toString(CheckStrUtil.SUMMARY_RESTRICTION) +"等字样,请修改");
            }
        }

        List<String> withholdingAgentNoList = new ArrayList<>();
        withholdingAgentNoList.add(paymentApplyVo.getWithholdingAgentNo());
        List<WithholdingAgentVo> withholdingAgentVoList = iSalaryPayWrapperService.getWithholdingAgentNoList(withholdingAgentNoList);
        CompanyBankVo receiveBankVo=new CompanyBankVo();
        if(CollectionUtils.isNotEmpty(withholdingAgentVoList)){
            WithholdingAgentVo withholdingAgentVo = withholdingAgentVoList.get(0);
            Integer withholdingAgentType = withholdingAgentVo.getWithholdingAgentType();
            String withholdingAgentOrgCode = withholdingAgentVo.getOrgCode();
            if(Objects.equals(withholdingAgentType, WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE2.getIndex())){
                withholdingAgentOrgCode=paymentApplyVo.getRevCom();
            }
            if(com.reon.hr.api.bill.enums.PaymentApplyPayTypeEnum.THEIR_OWN_SALARY.getCode().equals(paymentApplyVo.getPayType())){
                withholdingAgentOrgCode=withholdingAgentOrgCode.length()>=12?withholdingAgentOrgCode.substring(0,12):withholdingAgentOrgCode;
                List<UserOrgPosVo> confirmManList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(withholdingAgentOrgCode,financialOfficerPosCode);
                if(CollectionUtils.isNotEmpty(confirmManList)){
                    secondFinancialOfficer = getOrgPosCodeStrByOrgAndPosCode(confirmManList.get(0).getOrgCode(), financialOfficerPosCode);
                }else {
                    exceptionMsg+="发薪地财务制单（财务制单专员）";
                    throw new Exception(exceptionMsg);
                }
                List<UserOrgPosVo> secondFinancialManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(withholdingAgentOrgCode,financialManagerPosCode);
                if(CollectionUtils.isNotEmpty(secondFinancialManagerList)){
                    secondFinancialManager = getOrgPosCodeStrByOrgAndPosCode(secondFinancialManagerList.get(0).getOrgCode(), financialManagerPosCode);
                }else {
                    exceptionMsg+="发薪地财务复核（财务复核专员）";
                    throw new Exception(exceptionMsg);
                }
                ownerOtherPlace=1;
                payrollMethod=2;
                if(companyBankVoMap.containsKey(paymentApplyVo.getRevCom())){
                    CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getRevCom());
                    if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                        companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
                    }
                    //开关  招商银行 上海地区的先启动
                    CompanyBankVo finalCompanyBankVo = companyBankVo;
                    if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                            &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                            &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.REY_COM))
                    ){
                        receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                        receiveBankVo=companyBankVo;
                    }
                }
                if(paymentApplyVo.getAnewPayFlag()== BooleanTypeEnum.YES.getCode()){
                    normal=SalaryPaymentWorkflowEnum.Normal.REY_COM_REFUND.getCode();
                }

            }else if(com.reon.hr.api.bill.enums.PaymentApplyPayTypeEnum.SUPPLIER.getCode().equals(paymentApplyVo.getPayType())
                    || com.reon.hr.api.bill.enums.PaymentApplyPayTypeEnum.OFFSITE_SUPPLIER.getCode().equals(paymentApplyVo.getPayType())){
                SupplierVo supplierVo=iSupplierWrapperService.getSupplierAmtByWithholdingAgentNo(paymentApplyVo.getWithholdingAgentNo());
                if(supplierVo!=null&& StringUtils.isNotBlank(supplierVo.getCommissioner())){
                    confirmMan = supplierVo.getCommissioner();
                    ownerFlag=2;
                }else {
                    exceptionMsg+="供应商客服确认（供应商客服专员）";
                    throw new Exception(exceptionMsg);
                }
                List<UserOrgPosVo> supplierManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(supplierVo.getPurchaserOrg(),PositionEnum.SUPPLIER_MANAGER.getCode());
                if(CollectionUtils.isNotEmpty(supplierManagerList)){
                    supplierManager = getOrgPosCodeStrByOrgAndPosCode(supplierVo.getPurchaserOrg(),PositionEnum.SUPPLIER_MANAGER.getCode());
                }else {
                    exceptionMsg+="供应商客服经理";
                    throw new Exception(exceptionMsg);
                }
                if(paymentApplyVo.getAnewPayFlag()== BooleanTypeEnum.YES.getCode()){
                    normal=SalaryPaymentWorkflowEnum.Normal.SUPPLIER_REFUND.getCode();
                }
                if(com.reon.hr.api.bill.enums.PaymentApplyPayTypeEnum.SUPPLIER.getCode().equals(paymentApplyVo.getPayType())){
                    payrollMethod=SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_SUPPLIER_PAYROLL.getCode();
                }else {
                    //异地供应商发薪
                    payrollMethod=SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_OFFSITE_SUPPLIER_PAYROLL.getCode();
                    List<UserOrgPosVo> confirmManList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(paymentApplyVo.getPayAssociatedCom(),financialOfficerPosCode);
                    if(CollectionUtils.isNotEmpty(confirmManList)){
                        secondFinancialOfficer = getOrgPosCodeStrByOrgAndPosCode(confirmManList.get(0).getOrgCode(), financialOfficerPosCode);
                    }else {
                        exceptionMsg+="所选支付关联抬头 财务制单（财务制单专员）";
                        throw new Exception(exceptionMsg);
                    }
                    List<UserOrgPosVo> secondFinancialManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(paymentApplyVo.getPayAssociatedCom(),financialManagerPosCode);
                    if(CollectionUtils.isNotEmpty(secondFinancialManagerList)){
                        secondFinancialManager = getOrgPosCodeStrByOrgAndPosCode(secondFinancialManagerList.get(0).getOrgCode(), financialManagerPosCode);
                    }else {
                        exceptionMsg+="所选支付关联抬头 财务复核（财务复核专员）";
                        throw new Exception(exceptionMsg);
                    }
                    if(companyBankVoMap.containsKey(paymentApplyVo.getPayAssociatedCom())){
                        CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getPayAssociatedCom());
                        if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                            companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
                        }
                        //开关  招商银行 上海地区的先启动
                        CompanyBankVo finalCompanyBankVo = companyBankVo;
                        if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                                &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                                &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.REY_COM))
                        ){
                            receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                            receiveBankVo=companyBankVo;
                        }
                    }
                }
            }
        }
        if(Objects.equals(receiveBankVo.getSummaryRestrictionFlag(),BooleanTypeEnum.YES.getCode())&&Objects.equals(receiveBankVo.getPayRollType(), BusCodeEn.PAY_OTHERS.getCode())){
            if(checkStrResult){
                throw new Exception(ResultEnum.ERROR_PREFIX+"收款方银行卡为代发其他类型,且被银行提示支付内容摘要不能含"+ Arrays.toString(CheckStrUtil.SUMMARY_RESTRICTION) +"等字样,请修改");
            }
        }
        varMaps.put("secondFinancialOfficer", secondFinancialOfficer);
        varMaps.put("secondFinancialManager", secondFinancialManager);
        varMaps.put("ownerFlag", ownerFlag);
        varMaps.put("ownerOtherPlace", ownerOtherPlace);
        varMaps.put("confirmMan", confirmMan);
        varMaps.put("supplierManager", supplierManager);
        varMaps.put("loginName", paymentApplyVo.getCreator());
        if(Arrays.stream(onlyOwnCompanyLocalPayroll).anyMatch(s->(s.equals(paymentApplyVo.getPayCom())||s.equals(paymentApplyVo.getRevCom())||
                s.equals(paymentApplyVo.getPayAssociatedCom())))&& !payrollMethod.equals(SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode())){
            dispatchBankPay=SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
            receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        }
        varMaps.put("payrollMethod", payrollMethod);
        varMaps.put("dispatchBankPay", dispatchBankPay);
        varMaps.put("receiveBankPay", receiveBankPay);
        Integer currentYearMonthDate = DateUtil.getCurrentYearMonthDate();
        Integer yearMonthDateByCount = DateUtil.getYearMonthDateByCount(currentYearMonthDate, 1);
        Date now = new Date();
        //支付日期为当天，或者超过18点后的第二天都需要审批
        String lastDate = paymentApplyVo.getLastDate().replaceAll("-","");
        if(lastDate.equals(currentYearMonthDate.toString())||
                (now.getHours()>=18&& lastDate.equals(yearMonthDateByCount.toString()))
        ){
            overtimeFlag=1;
        }
        //申请支付金额为0，实发为0
        if(paymentApplyVo.getApplyAmt().compareTo(BigDecimal.ZERO)==0&&
                paymentApplyVo.getTotalActApy().compareTo(BigDecimal.ZERO)==0&&
                paymentApplyVo.getTotalCompensation().compareTo(BigDecimal.ZERO)==0&&
                paymentApplyVo.getTotalAnnualBonus().compareTo(BigDecimal.ZERO)==0&&
                paymentApplyVo.getTotalLaborWages().compareTo(BigDecimal.ZERO)==0){
            overtimeFlag=2;
        }
        varMaps.put("overtimeFlag", overtimeFlag);
        varMaps.put("normal", normal);

        //正常发薪
        if(!Objects.equals(normal, SalaryPaymentWorkflowEnum.Normal.SUPPLIER_REFUND.getCode())){
            //派单地是招商银行
            if(Objects.equals(dispatchBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                PayServiceSerialLogVo payServiceSerialLogVo = new PayServiceSerialLogVo();
                payServiceSerialLogVo.setCreator(paymentApplyVo.getCreator());
                //自有本地发薪，代发接口。
                if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode())){
                    String yurref = getYurref(dispatchBankVo);
                    payServiceSerialLogVo.setYurref(yurref);
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                } else {
                    //其它都是转账
                    payServiceSerialLogVo.setYurref(iSequenceService.getCMBSinglePayBusinessNo());
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_PAY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }
                payServiceSerialLogVoList.add(payServiceSerialLogVo);
            }
            //接单地是招商银行
            if(Objects.equals(receiveBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                PayServiceSerialLogVo payServiceSerialLogVo = new PayServiceSerialLogVo();
                payServiceSerialLogVo.setCreator(paymentApplyVo.getCreator());
                //自有异地发薪，代发接口。
                if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_OFFSITE_PAYROLL.getCode())){
                    String yurref = getYurref(receiveBankVo);
                    payServiceSerialLogVo.setYurref(yurref);
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_REV_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }else {
                    //其它都是转账
                    payServiceSerialLogVo.setYurref(iSequenceService.getCMBSinglePayBusinessNo());
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_REY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }
                payServiceSerialLogVoList.add(payServiceSerialLogVo);
            }
        }
        paymentApplyVo.setPayServiceSerialLogVoList(payServiceSerialLogVoList);
        paymentApplyVo.setVariables(varMaps);
        return paymentApplyVo;
    }
    @Override
    public Map<String, Object> getVariables(Map<String, Object> varMaps,PaymentApplyVo paymentApplyVo){
        Map<String, Object> variables=new HashMap<>();
        String[] salaryPaymentArea = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_AREA).getCfgValue().split(",");
        String[] salaryPaymentCom = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_COM).getCfgValue().split(",");
        String[] onlyOwnCompanyLocalPayroll = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.ONLY_OWN_COMPANY_LOCAL_PAYROLL).getCfgValue().split(",");
        // dispatchBankPay == 1 派单地是招商银行
        // dispatchBankPay == 0 派单地不是招商银行
        Integer dispatchBankPay = SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        // receiveBankPay == 1 接单地是招商银行
        // receiveBankPay == 0 接单地不是招商银行
        Integer receiveBankPay = SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        Integer payrollMethod=(Integer) varMaps.get("payrollMethod");
        Integer normal=(Integer) varMaps.get("normal");
        List<CompanyBankVo> allCompanyBankVoList = companyBankWrapperService.getAllComp(null, null);
        Map<String, CompanyBankVo> companyBankVoMap =allCompanyBankVoList.stream().filter(c->c.getType()==null||c.getType().equals(CompanyBankDataType.CURRENCY.getCode())).collect(toMap(CompanyBankVo::getCompNo,Function.identity()));
        Map<String, CompanyBankVo> specialCompanyBankVoMap=allCompanyBankVoList.stream().filter(c->c.getType()!=null&&c.getType().equals(CompanyBankDataType.SPECIAL.getCode())).collect(toMap(c -> c.getBankNo() + "-" + c.getCustId(), Function.identity()));
        CompanyBankVo dispatchBankVo =new CompanyBankVo();
        String specialCompanyBankKey = paymentApplyVo.getBankNo() + "-" + paymentApplyVo.getCustId();
        if(companyBankVoMap.containsKey(paymentApplyVo.getPayCom())){
            CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getPayCom());
            if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
            }
            //开关  招商银行 上海地区的先启动
            CompanyBankVo finalCompanyBankVo = companyBankVo;
            if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                    &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                    &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.PAY_COM))
            ){
                dispatchBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                dispatchBankVo=companyBankVo;
            }
        }
        variables.put("dispatchBankVo",dispatchBankVo);
        CompanyBankVo receiveBankVo=new CompanyBankVo();
        if(companyBankVoMap.containsKey(paymentApplyVo.getRevCom())){
            CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getRevCom());
            if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
            }
            //开关  招商银行 上海地区的先启动
            CompanyBankVo finalCompanyBankVo = companyBankVo;
            if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                    &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                    &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.REY_COM))
            ){
                receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                receiveBankVo=companyBankVo;
            }
        }
        if(paymentApplyVo.getPayAssociatedCom()!=null&&companyBankVoMap.containsKey(paymentApplyVo.getPayAssociatedCom())){
            CompanyBankVo companyBankVo = companyBankVoMap.get(paymentApplyVo.getPayAssociatedCom());
            if(specialCompanyBankVoMap.containsKey(specialCompanyBankKey)){
                companyBankVo=specialCompanyBankVoMap.get(specialCompanyBankKey);
            }
            //开关  招商银行 上海地区的先启动
            CompanyBankVo finalCompanyBankVo = companyBankVo;
            if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                    &&Arrays.stream(salaryPaymentArea).anyMatch(s -> finalCompanyBankVo.getCompNo().startsWith(s))
                    &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.REY_COM))
            ){
                receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
                receiveBankVo=companyBankVo;
            }
        }
        variables.put("receiveBankVo",receiveBankVo);
        if(Arrays.stream(onlyOwnCompanyLocalPayroll).anyMatch(s->(s.equals(paymentApplyVo.getPayCom())||s.equals(paymentApplyVo.getRevCom())||
                s.equals(paymentApplyVo.getPayAssociatedCom())))&& !payrollMethod.equals(SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode())){
            dispatchBankPay=SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
            receiveBankPay=SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        }
        variables.put("dispatchBankPay",dispatchBankPay);
        variables.put("receiveBankPay",receiveBankPay);
        variables.put("normal",normal);
        variables.put("payrollMethod",payrollMethod);
        return variables;
    }
    public List<PayServiceSerialLogVo> setPayServiceSerialLogVoList(Map<String, Object> varMaps,PaymentApplyVo paymentApplyVo,String creator){
        Map<String, Object> variables = getVariables(varMaps, paymentApplyVo);
        CompanyBankVo dispatchBankVo=(CompanyBankVo)variables.get("dispatchBankVo");
        CompanyBankVo receiveBankVo=(CompanyBankVo)variables.get("receiveBankVo");
        Integer dispatchBankPay=(Integer) variables.get("dispatchBankPay");
        Integer receiveBankPay=(Integer) variables.get("receiveBankPay");
        Integer normal=(Integer) variables.get("normal");
        Integer payrollMethod=(Integer) variables.get("payrollMethod");

        List<PayServiceSerialLogVo> payServiceSerialLogVoList = new ArrayList<>();
        //正常发薪
        if(!Objects.equals(normal, SalaryPaymentWorkflowEnum.Normal.SUPPLIER_REFUND.getCode())){
            //派单地是招商银行
            if(Objects.equals(dispatchBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                PayServiceSerialLogVo payServiceSerialLogVo = new PayServiceSerialLogVo();
                payServiceSerialLogVo.setCreator(creator);
                //自有本地发薪，代发接口。
                if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_LOCAL_PAYROLL.getCode())){
                    String yurref = getYurref(dispatchBankVo);
                    payServiceSerialLogVo.setYurref(yurref);
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                } else {
                    //其它都是转账
                    payServiceSerialLogVo.setYurref(iSequenceService.getCMBSinglePayBusinessNo());
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_PAY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }
                payServiceSerialLogVoList.add(payServiceSerialLogVo);
            }
            //接单地是招商银行
            if(Objects.equals(receiveBankPay, SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode())){
                PayServiceSerialLogVo payServiceSerialLogVo = new PayServiceSerialLogVo();
                payServiceSerialLogVo.setCreator(creator);
                //自有异地发薪，代发接口。
                if(Objects.equals(payrollMethod, SalaryPaymentWorkflowEnum.PayrollMethod.OWN_COMPANY_OFFSITE_PAYROLL.getCode())){
                    String yurref = getYurref(receiveBankVo);
                    payServiceSerialLogVo.setYurref(yurref);
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_REV_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }else {
                    //其它都是转账
                    payServiceSerialLogVo.setYurref(iSequenceService.getCMBSinglePayBusinessNo());
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_REY_COM;
                    payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                    payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                }
                payServiceSerialLogVoList.add(payServiceSerialLogVo);
            }
        }
        return payServiceSerialLogVoList;
    }

    private String getYurref(CompanyBankVo companyBankVo) {
//        Integer payRollType = companyBankVo.getPayRollType();
//        String yurref;
//        if(payRollType != null && payRollType.equals(BusCodeEn.PAY_OTHERS.getCode())){
//            yurref = iSequenceService.getCMBPayOtherBusinessNo();
//        } else {
//            yurref = iSequenceService.getCMBPayrollBusinessNo();
//        }
        return iSequenceService.getCMBSalaryBusinessNo(companyBankVo.getPayRollType());
    }

    @Override
    public TaskVo updatePaymentApplyAndSubmitTask(TaskVo taskVo) {
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO(ReonWorkflowType.SALARY_PAYMENT_APPLY.getDefineKey(), ReonWorkflowType.SALARY_PAYMENT_APPLY.getBussinessKey(), null,taskVo.getProcessInstanceId());
        TaskVo oldTask = workflowWrapperService.getOneTask(taskQueryDTO);
        String errMsg="错误信息:";
        String comment = taskVo.getComment();
        if (StringUtil.isBlank(comment)) {
            comment = ManualAction.PASS.getDescription();
        }else if(comment.getBytes().length>255){
            throw new RuntimeException(errMsg+"审批意见不得超过255个字节！");
        }
        String payAssociatedCom = taskVo.getPayAssociatedCom() ;
        Map<String, Object> variables=new HashMap<>();
        if(StringUtils.isNotBlank(payAssociatedCom)){
            List<CompanyBankVo> companyBankVoList = companyBankWrapperService.getAllComp(payAssociatedCom, CompanyBankDataType.CURRENCY.getCode());
            if(CollectionUtils.isEmpty(companyBankVoList)){
                throw new RuntimeException(errMsg+"支付关联抬头的银行卡信息为空!");
            }else {
                String[] salaryPaymentArea = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_AREA).getCfgValue().split(",");
                String[] salaryPaymentCom = iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.SALARY_PAYMENT_COM).getCfgValue().split(",");
                CompanyBankVo companyBankVo = companyBankVoList.get(0);
                if(PayEnum.BankName.BANK_NAME6.getCode()==companyBankVo.getBankType()
                        &&Arrays.stream(salaryPaymentArea).anyMatch(s -> companyBankVo.getCompNo().startsWith(s))
                        &&Arrays.stream(salaryPaymentCom).anyMatch(s -> s.contains(SalaryPaymentWorkflowEnum.REY_COM))){
                    variables.put("receiveBankPay",SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode());
                    List<PayServiceSerialLogVo> payServiceSerialLogVoList=payServiceSerialLogMapper.getByPaymentIdList(Collections.singletonList(taskVo.getPaymentApplyId()));
                    Map<String, PayServiceSerialLogVo> payServiceSerialLogVoMap = payServiceSerialLogVoList.stream().collect(toMap(p -> p.getPaymentId() + "-" + p.getPayServiceSerialType() + "-" + p.getPayServiceSerialDetailType(), Function.identity()));
                    PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.CORP_SINGLE_PAY_REY_COM;
                    String logKey = taskVo.getPaymentApplyId() + "-" + payServiceSerialDetailType.getParentCode() + "-" + payServiceSerialDetailType.getCode();
                    if(!payServiceSerialLogVoMap.containsKey(logKey)){
                        PayServiceSerialLogVo payServiceSerialLogVo=new PayServiceSerialLogVo();
                        payServiceSerialLogVo.setPaymentId(taskVo.getPaymentApplyId());
                        payServiceSerialLogVo.setYurref(iSequenceService.getCMBSinglePayBusinessNo());
                        payServiceSerialLogVo.setPayServiceSerialType(payServiceSerialDetailType.getParentCode());
                        payServiceSerialLogVo.setPayServiceSerialDetailType(payServiceSerialDetailType.getCode());
                        payServiceSerialLogVo.setCreator(taskVo.getUpdater());
                        payServiceSerialLogMapper.insertByList(Collections.singletonList(payServiceSerialLogVo));
                    }
                }
            }
            String exceptionMsg="工资支付流程审批中组织人员缺失";
            String financialOfficerPosCode=PositionEnum.TREASURY_DOCUMENTATION_ATTACHE.getCode();
            String financialManagerPosCode=PositionEnum.TREASURY_RECHECK_ATTACHE.getCode();
            //发薪地财务制单（财务制单专员）
            String secondFinancialOfficer = "";
            //发薪地财务复核（财务复核专员）
            String secondFinancialManager = "";
            List<UserOrgPosVo> confirmManList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(payAssociatedCom,financialOfficerPosCode);
            if(CollectionUtils.isNotEmpty(confirmManList)){
                secondFinancialOfficer = getOrgPosCodeStrByOrgAndPosCode(confirmManList.get(0).getOrgCode(), financialOfficerPosCode);
            }else {
                exceptionMsg+="所选支付关联抬头 财务制单（财务制单专员）";
                throw new RuntimeException(errMsg+exceptionMsg);
            }
            List<UserOrgPosVo> secondFinancialManagerList = iUserOrgPosWrapperService.getByOrgCodeLikeAndPosCode(payAssociatedCom,financialManagerPosCode);
            if(CollectionUtils.isNotEmpty(secondFinancialManagerList)){
                secondFinancialManager = getOrgPosCodeStrByOrgAndPosCode(secondFinancialManagerList.get(0).getOrgCode(), financialManagerPosCode);
            }else {
                exceptionMsg+="所选支付关联抬头 财务复核（财务复核专员）";
                throw new RuntimeException(errMsg+exceptionMsg);
            }
            variables.put("secondFinancialOfficer", secondFinancialOfficer);
            variables.put("secondFinancialManager", secondFinancialManager);
        }
        String paymentDate = taskVo.getPaymentDate();

        if(StringUtils.isNotBlank(paymentDate)){
            Integer paymentDateInt = Integer.parseInt(paymentDate.replaceAll("-",""));
            Integer currentYearMonthDate = DateUtil.getCurrentYearMonthDate();
            //供应商发薪时间必须大于等于当前时间
            if(paymentDateInt<currentYearMonthDate) {
                throw new RuntimeException(errMsg+"供应商发薪时间必须大于等于当前时间!");
            }
            PaymentApplyVo paymentApplyVo = new PaymentApplyVo();
            paymentApplyVo.setId(taskVo.getPaymentApplyId());
            paymentApplyVo.setPaymentDate(taskVo.getPaymentDate());
            if(StringUtils.isNotBlank(taskVo.getLastDate())&& "供应商客服确认".equals(oldTask.getName())){
                paymentApplyVo.setLastDate(taskVo.getLastDate());
                Integer overtimeFlag = 0;
                Integer yearMonthDateByCount = DateUtil.getYearMonthDateByCount(currentYearMonthDate, 1);
                Date now = new Date();
                //支付日期为当天，或者超过18点后的第二天都需要审批
                String lastDate = taskVo.getLastDate().replaceAll("-","");
                if(lastDate.equals(currentYearMonthDate.toString())||
                        (now.getHours()>=18&& lastDate.equals(yearMonthDateByCount.toString()))
                ){
                    overtimeFlag=1;
                }
                PaymentApplyVo queryVo = new PaymentApplyVo();
                queryVo.setId(taskVo.getPaymentApplyId());
                PaymentApplyVo oldPaymentApply = paymentApplyService.getPaymentApplyList(queryVo).get(0);
                //申请支付金额为0，实发为0
                if(oldPaymentApply.getApplyAmt().compareTo(BigDecimal.ZERO)==0&&
                        oldPaymentApply.getTotalActApy().compareTo(BigDecimal.ZERO)==0&&
                        oldPaymentApply.getTotalCompensation().compareTo(BigDecimal.ZERO)==0&&
                        oldPaymentApply.getTotalAnnualBonus().compareTo(BigDecimal.ZERO)==0&&
                        oldPaymentApply.getTotalLaborWages().compareTo(BigDecimal.ZERO)==0){
                    overtimeFlag=2;
                }
                variables.put("overtimeFlag", overtimeFlag);
            }
            paymentApplyVo.setPayAssociatedCom(taskVo.getPayAssociatedCom());
            paymentApplyVo.setUpdater(taskVo.getUpdater());
            paymentApplyMapper.updateByPrimaryKeySelective(paymentApplyVo);
        }
        variables.put("loginName", taskVo.getUpdater());
        taskVo.setComment(comment);
        taskVo.setVariablesMap(variables);
        return taskVo;
    }

    @Override
    public TaskVo getTaskByYurref(String yurRef) {
        PaymentApplyVo applyVo =payServiceSerialLogMapper.getPaymentApplyByYurref(yurRef);
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO(ReonWorkflowType.SALARY_PAYMENT_APPLY.getDefineKey(), ReonWorkflowType.SALARY_PAYMENT_APPLY.getBussinessKey(), null,applyVo.getPid());
        return workflowWrapperService.getOneTask(taskQueryDTO);
    }
    @Override
    public void triggerReceiveTaskByYurref(String yurRef,String loginName, String comment) {
        PaymentApplyVo applyVo =payServiceSerialLogMapper.getPaymentApplyByYurref(yurRef);
        workflowWrapperService.triggerReceiveTask(applyVo.getPid(),ReonWorkflowType.SALARY_PAYMENT_APPLY.getBussinessKey(),loginName,comment);
    }

    @Override
    public void handlePayBusinessCompletion(String message) {
        PayBusinessCompletionMsg payBusinessCompletionMsg = JsonUtil.jsonToBean(message, PayBusinessCompletionMsg.class);
        PayBusinessCompletion trsInfo = payBusinessCompletionMsg.getTrsInfo();
        try {
            if(trsInfo.getReqSts().equals(ReqStsEn.FIN.getCode())){
                if(trsInfo.getRtnFlg().equals(RtnFlgEn.S.getCode())){
                    triggerReceiveTaskByYurref(payBusinessCompletionMsg.getYurRef(),"转账通知","转账完成");
                }else if(RtnFlgEn.FAIL_LIST.contains(trsInfo.getRtnFlg())){
                    logger.info("转账失败 业务参考号,{}",payBusinessCompletionMsg.getYurRef());
                    //workflowWrapperService.rejectTask(task.getId(),EnumsUtil.getNameByCode(trsInfo.getRtnFlg(),RtnFlgEn.class),updater);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void handlePayRefund(String message) {
        PayRefundMsg payRefundMsg = com.reon.hr.common.utils.JsonUtil.jsonToBean(message, PayRefundMsg.class);
        logger.info("转账退票通知 业务参考号,{}",payRefundMsg.getYurRef());
        PaymentApplyVo applyVo = payServiceSerialLogMapper.getPaymentApplyByYurref(payRefundMsg.getYurRef());
        workflowWrapperService.insertWorkflowAuditLogSelective(applyVo.getPid(), ReonWorkflowType.SALARY_PAYMENT_APPLY.getDefineKey (), ManualAction.REJECT.getCode(),"转账退票",payRefundMsg.getBackInfo().getRtnNar());
    }

    @Override
    public void handlePayrollBusinessCompletion(String message) {
        //代发的请求发送后，流程节点就走完了。
        //员工的卡号是招商银行的会在成功通知里通知代发失败
        PayrollBusinessCompletion payrollBusinessCompletion = JsonUtil.jsonToBean(message, PayrollBusinessCompletion.class);
        AgcInfo agcInfo = payrollBusinessCompletion.getAgcInfo();
        if(agcInfo.getSucnum()!=Integer.parseInt(agcInfo.getTrsnum())){
            YurrefArgs yurrefArgs = new YurrefArgs(iSequenceService.getCMBQueryBusinessNo());
            yurrefArgs.setParamsYurref(payrollBusinessCompletion.getYurRef());
            ResponseMsg<QueryPayrollResponseInfo> responseMsg = icmBpayWrapperService.queryPayrollBatchAndDetail(yurrefArgs);
            QueryPayrollResponseInfo payrollResponseInfo = responseMsg.getResponseData();
            if (payrollResponseInfo != null) {
                List<QueryPayrollResponseInfo.QueryPayrollDetailResult> bb6bpdqyz2 = payrollResponseInfo.getBb6bpdqyz2();
                String resultcode = payrollResponseInfo.getHead().getResultcode();
                if (resultcode.equals(CMBPayConstant.SUCCESS)) {
                    try {
                        List<Long> batchIdList = payServiceSerialLogMapper.getBatchIdListByYurref(payrollBusinessCompletion.getYurRef());
                        List<SalaryInfoVo> salaryInfoVoList = iSalaryPayWrapperService.getSalaryIdByBatchIdList(batchIdList);
                        Map<String, SalaryInfoVo> salaryInfoVoMap = salaryInfoVoList.stream().collect(Collectors.toMap(s -> s.getSalaryBatchDetailId().toString().substring(Math.max(s.getSalaryBatchDetailId().toString().length() - 8, 0)), Function.identity()));
                        Map<String,List<QueryPayrollResponseInfo.QueryPayrollDetailResult>> detailResultListMap = bb6bpdqyz2.stream().filter(v -> v.getStscod().equals(ResultEn.Stscod.T.getCode())
                                &&StringUtils.isNotBlank(v.getEacbnk())&&salaryInfoVoMap.containsKey(v.getTrxseq())).collect(Collectors.groupingBy(QueryPayrollResponseInfo.QueryPayrollDetailResult::getErrtxt));
                        for (String errtxt:detailResultListMap.keySet()) {
                            List<Long> salaryIds = new ArrayList<>();
                            EmpDelaySearchVo vo = new EmpDelaySearchVo();
                            List<QueryPayrollResponseInfo.QueryPayrollDetailResult> detailResultList = detailResultListMap.get(errtxt);
                            for (QueryPayrollResponseInfo.QueryPayrollDetailResult detailResult:detailResultList) {
                                if (salaryInfoVoMap.containsKey(detailResult.getTrxseq())) {
                                    SalaryInfoVo salaryInfoVo = salaryInfoVoMap.get(detailResult.getTrxseq());
                                    salaryIds.add(salaryInfoVo.getId());
                                    vo.setBatchId(salaryInfoVo.getBatchId());
                                }
                            }
                            if(CollectionUtils.isNotEmpty(salaryIds)){
                                vo.setSalaryIds(salaryIds);
                                vo.setStatus(SalaryInfoStatus.PAY_FAILED.getCode());
                                vo.setRemark(errtxt);
                                vo.setSendTime(DateUtil.getCurrentYearMonthDate());
                                vo.setUpdater("代发通知：业务完成消息");
                                iSalaryPayWrapperService.updateSalaryInfoStatus(vo);
                            }
                        }
                    } catch (Exception e) {
                        logger.info("代发通知：业务完成消息内容==========================>处理数据失败,{}", message);
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Override
    public void handlePayrollRefund(String message) {
        PayrollRefund payrollRefund = JsonUtil.jsonToBean(message, PayrollRefund.class);
        AgentRefundDetailInfo detailInfo = payrollRefund.getDetailInfo();
        YurrefArgs yurrefArgs = new YurrefArgs(iSequenceService.getCMBQueryBusinessNo());
        yurrefArgs.setParamsYurref(payrollRefund.getYurRef());
        ResponseMsg<QueryPayrollResponseInfo> responseMsg = icmBpayWrapperService.queryPayrollBatchAndDetail(yurrefArgs);
        QueryPayrollResponseInfo payrollResponseInfo = responseMsg.getResponseData();
        if (payrollResponseInfo != null) {
            List<QueryPayrollResponseInfo.QueryPayrollDetailResult> bb6bpdqyz2 = payrollResponseInfo.getBb6bpdqyz2();
            String resultcode = payrollResponseInfo.getHead().getResultcode();
            if (resultcode.equals(CMBPayConstant.SUCCESS)) {
                try {
                    List<Long> batchIdList = payServiceSerialLogMapper.getBatchIdListByYurref(payrollRefund.getYurRef());
                    SalaryInfoVo salaryInfoVo = iSalaryPayWrapperService.getSalaryIdByBatchIdList(batchIdList, detailInfo.getDtlseq());
                    QueryPayrollResponseInfo.QueryPayrollDetailResult detailResult = bb6bpdqyz2.stream().filter(v -> v.getStscod().equals(ResultEn.Stscod.T.getCode())
                            &&v.getTrxseq().equals(detailInfo.getDtlseq())).collect(Collectors.toList()).get(0);
                    EmpDelaySearchVo vo = new EmpDelaySearchVo();
                    vo.setBatchId(salaryInfoVo.getBatchId());
                    vo.setSalaryId(salaryInfoVo.getId());
                    vo.setSalaryIds(Collections.singletonList(salaryInfoVo.getId()));
                    vo.setStatus(SalaryInfoStatus.PAY_FAILED.getCode());
                    vo.setRemark(detailResult.getErrtxt());
                    vo.setSendTime(Integer.parseInt(detailInfo.getTradat()));
                    vo.setUpdater("代发通知：代发退票消息");
                    iSalaryPayWrapperService.updateSalaryInfoStatus(vo);
                } catch (Exception e) {
                    logger.info("代发通知：代发退票消息内容==========================>处理数据失败,{}", message);
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    public String updateAndSendPayrollOrCorpSinglePay(List<String> pidList, List<String> taskIdList, Integer documentStatus, String loginName) {
        Map<String, Object> variables=new HashMap<>();
        variables.put("loginName",loginName);
        Map<String,Map<String, Object>> oldVariablesMap =workflowWrapperService.getVariablesByPidList(pidList);
        List<IntegrationDto> integrationDtoList=getIntegrationDtoList(pidList,PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode()==documentStatus?
                PaymentApplyDocumentStatusEnum.REVIEWED.getCode():PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode(),oldVariablesMap,loginName);
        //全非银企直连的
        if(CollectionUtils.isEmpty(integrationDtoList)){
            updateDocumentStatusAndExcuteTask(pidList,taskIdList,documentStatus,variables);
        }
        Map<String, IntegrationDto> integrationDtoMap = integrationDtoList.stream().collect(toMap(IntegrationDto::getPid, Function.identity()));
        List<QueryAccInfo> queryAccInfos=new ArrayList<>();
        for (IntegrationDto integrationDto:integrationDtoList) {
            QueryAccInfo queryAccInfo=new QueryAccInfo();
            queryAccInfo.setAccnbr(integrationDto.getAccnbr());
            queryAccInfos.add(queryAccInfo);
            queryAccInfos=queryAccInfos.stream().distinct().collect(toList());
        }
        Map<String, AcctInfo> acctInfoMap =new HashMap<>();
        boolean checkBalanceFlag = Boolean.parseBoolean(iSystemConfigWrapperService.getGlobalCfgKey(SalaryPaymentWorkflowEnum.CHECK_BALANCE_FLAG).getCfgValue());
        if(CollectionUtils.isNotEmpty(queryAccInfos)&&checkBalanceFlag){
            String cmbBusinessNo = iSequenceService.getCMBQueryBusinessNo();
            ResponseMsg<QueryAcctInfoResInfo> responseMsg = icmBpayWrapperService.queryAcctInfoList(queryAccInfos, cmbBusinessNo);
            QueryAcctInfoResInfo queryAcctInfoResInfo = responseMsg.getResponseData();
            List<AcctInfo> ntqacinfz = queryAcctInfoResInfo.getNtqacinfz().stream().distinct().collect(Collectors.toList());
            acctInfoMap = ntqacinfz.stream().collect(toMap(AcctInfo::getAccnbr, Function.identity()));
        }
        for (int i = 0; i < pidList.size(); i++){
            String pid = pidList.get(i);
            String taskId=null;
            if(CollectionUtils.isNotEmpty(taskIdList)){
                taskId = taskIdList.get(i);
            }
            //存在银企直连的就要单个处理,目前是有一个出错，后续的就不执行了，有需要可改成出错的跳过
            if(integrationDtoMap.containsKey(pid)){
                IntegrationDto integrationDto = integrationDtoMap.get(pid);
                if(checkBalanceFlag){
                    String errMsg = checkAndSetBalance(acctInfoMap, integrationDto);
                    if(errMsg!=null){
                        return errMsg;
                    }
                }
                Integer type = integrationDto.getType();
                if(type== PayServiceSerialType.SEND_PAYROLL.getCode()){
                    PayrollBatchInfo batchInfo = integrationDto.getBatchInfo();
                    ResponseMsg<PayrollResponseInfo> responseMsg = icmBpayWrapperService.sendPayroll(batchInfo, integrationDto.getDetailInfoList(), integrationDto.getYurrefArgs());
                    PayrollResponseInfo payrollResponseInfo = responseMsg.getResponseData();
                    if(payrollResponseInfo==null){
                        return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付发送代发请求失败！");
                    }else {
                        RespHead head = payrollResponseInfo.getHead();
                        if(Objects.equals(head.getResultcode(), CMBPayConstant.SUCCESS)){
                            List<PayrollResponseInfo.PayrollResult> resultList = payrollResponseInfo.getBb6cdcbhz1();
                            updateDocumentStatusAndExcuteTask(Collections.singletonList(pid), Collections.singletonList(taskId),documentStatus,variables);
                        }else {
                            return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付"+head.getResultmsg().split("-")[1]);
                        }
                    }
                }else if(type==PayServiceSerialType.CORP_SINGLE_PAY.getCode()){
                    TransAcctInfo transAcctInfo = integrationDto.getTransAcctInfo();
                    ResponseMsg<PaySingleResponseInfo> responseMsg = icmBpayWrapperService.corpSinglePay(transAcctInfo, integrationDto.getYurrefArgs());
                    PaySingleResponseInfo paySingleResponseInfo = responseMsg.getResponseData();
                    if(paySingleResponseInfo==null){
                        return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付发送转账请求失败！");
                    }else {
                        RespHead head = paySingleResponseInfo.getHead();
                        if(Objects.equals(head.getResultcode(), CMBPayConstant.SUCCESS)){
                            List<PaySingleResponseInfo.PaySingleResult> resultList = paySingleResponseInfo.getBb1payopz1();
                            String errTxt = resultList.stream().map(PaySingleResponseInfo.PaySingleResult::getErrTxt).filter(Objects::nonNull).collect(Collectors.joining());
                            if(StringUtils.isNotBlank(errTxt)){
                                return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付"+errTxt);
                            }else {
                                updateDocumentStatusAndExcuteTask(Collections.singletonList(pid), Collections.singletonList(taskId),documentStatus,variables);
                            }
                        }else {
                            return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付"+head.getResultmsg().split("-")[1]);
                        }
                    }
                }
            }else if(CollectionUtils.isNotEmpty(integrationDtoList)){
                updateDocumentStatusAndExcuteTask(Collections.singletonList(pid), Collections.singletonList(taskId),documentStatus,variables);
            }
        }
        return null;
    }

    @Override
    public void updateSalaryPaymentApply(List<PaymentApplyVo> paymentApplyList,String loginUser) {
        Map<Long,SalaryPaymentApplyVo> salaryPaymentApplyVoMap=new HashMap<>();
        for (PaymentApplyVo paymentApplyVo:paymentApplyList) {
            SalaryPaymentApplyVo salaryPaymentApplyVo = new SalaryPaymentApplyVo();
            Long payApplyId = paymentApplyVo.getId();
            if(salaryPaymentApplyVoMap.containsKey(payApplyId)){
                salaryPaymentApplyVo=salaryPaymentApplyVoMap.get(payApplyId);
            }
            salaryPaymentApplyVo.setPayApplyId(payApplyId);
            salaryPaymentApplyVo.setUpdater(loginUser);
            if(paymentApplyVo.getPayServiceSerialDetailType()==PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM.getCode()){
                salaryPaymentApplyVo.setDispatchPrintFlag(PrintFlagEnum.YES.getCode());
                if(Objects.equals(paymentApplyVo.getPayType(), PaymentApplyPayTypeEnum.SALARY.getCode())||Objects.equals(paymentApplyVo.getPayType(), PaymentApplyPayTypeEnum.SUPPLIER.getCode())){
                    salaryPaymentApplyVo.setReceivingPrintFlag(PrintFlagEnum.YES.getCode());
                }
            }else {
                salaryPaymentApplyVo.setReceivingPrintFlag(PrintFlagEnum.YES.getCode());
            }
            salaryPaymentApplyVoMap.put(payApplyId, salaryPaymentApplyVo);
        }
        List<SalaryPaymentApplyVo> salaryPaymentApplyVoList = salaryPaymentApplyVoMap.values().stream().distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(salaryPaymentApplyVoList)){
            salaryPaymentApplyMapper.updateSelectiveList(salaryPaymentApplyVoList);
        }
    }

    @Override
    public void updatePaymentLastDate(List<Long> idList, Date newLastDate, List<PaymentApplyLastDateLogVo> paymentApplyLastDateLogVoList) {
        paymentApplyMapper.updatePaymentLastDate(idList,newLastDate);
        paymentApplyLastDateLogMapper.insertByVoList(paymentApplyLastDateLogVoList);
    }

    @Override
    public int updateSalaryPaymentVariable(String eventKey, Long paymentApplyId, String loginName) {
        PayServiceSerialDetailType payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM;
        PaymentApplyVo applyVo = paymentApplyMapper.selectByPrimaryKey(paymentApplyId);
        if(applyVo.getAppStatus()!= PaymentApplyProcessStatus.PENDING_APPROVAL.getCode()){
            throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的审批状态不是【审批中】,无法设置");
        }
        ActRuTaskVo actRuTaskVo = workflowWrapperService.getCurrentApprover(applyVo.getPid());
        Integer documentStatus = null;
        String variablesName="dispatchBankPay";
        Integer variablesValue=SalaryPaymentWorkflowEnum.BankPay.NOT_CHINA_MERCHANTS_BANK.getCode();
        List<PayServiceSerialLogVo> psslInsertVoList=new ArrayList<>();
        String delFlag=DelFlagEnum.Y.getCode();
        //设置成非银企
        if(eventKey.contains("Not")){
            if(Objects.equals(eventKey, SalaryPaymentWorkflowEnum.EventKey.DISPATCH_NOT.getCode())){
                if(!applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode())
                &&!applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode())){
                    throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的制单状态不是【未制单】或【已制单】,无法设置");
                }
                if("财务复核".equals(actRuTaskVo.getName())){
                    documentStatus=PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode();
                }
            }else if(Objects.equals(eventKey, SalaryPaymentWorkflowEnum.EventKey.RECEIVING_NOT.getCode())){
                if(applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode())){
                    throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的制单状态是【自有工资已复核】,无法设置");
                }
                payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_REV_COM;
                variablesName="receiveBankPay";
                if("发薪地财务复核".equals(actRuTaskVo.getName())){
                    documentStatus=PaymentApplyDocumentStatusEnum.OWN_DOCUMENT_MADE.getCode();
                }
            }

        }else {
            //设置成银企
            List<PayServiceSerialLogVo> payServiceSerialLogVoList =payServiceSerialLogMapper.getIncludingDeletedByPaymentId(paymentApplyId);
            List<Integer> oldPayServiceSerialDetailTypeList = payServiceSerialLogVoList.stream().map(PayServiceSerialLogVo::getPayServiceSerialDetailType).collect(toList());
            variablesValue=SalaryPaymentWorkflowEnum.BankPay.CHINA_MERCHANTS_BANK.getCode();
            delFlag=DelFlagEnum.N.getCode();
            Map<String,Map<String, Object>> oldVariablesMap =workflowWrapperService.getVariablesByPidList(Collections.singletonList(applyVo.getPid()));
            Map<String, Object> variablesMap = oldVariablesMap.get(applyVo.getPid());
            if(Objects.equals(eventKey, SalaryPaymentWorkflowEnum.EventKey.DISPATCH_YES.getCode())){
                if(!applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode())
                        &&!applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode())){
                    throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的制单状态不是【未制单】或【已制单】,无法设置");
                }
                if("财务复核".equals(actRuTaskVo.getName())){
                    documentStatus=PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode();
                }
                //先查旧的，如果有旧的，修改del_flag就可以，如果没有就新增
                if(!oldPayServiceSerialDetailTypeList.contains(PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM.getCode())){
                    psslInsertVoList = setPayServiceSerialLogVoList(variablesMap, applyVo, loginName);
                    psslInsertVoList.removeIf(p->p.getPayServiceSerialDetailType().equals(PayServiceSerialDetailType.SEND_PAYROLL_REV_COM.getCode()));
                    if(CollectionUtils.isEmpty(psslInsertVoList)){
                        throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的【派单地】【银行卡】不支持银企,无法设置");
                    }
                }
            }else if(Objects.equals(eventKey, SalaryPaymentWorkflowEnum.EventKey.RECEIVING_YES.getCode())){
                if(applyVo.getDocumentStatus().equals(PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode())){
                    throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的制单状态是【自有工资已复核】,无法设置");
                }
                payServiceSerialDetailType = PayServiceSerialDetailType.SEND_PAYROLL_REV_COM;
                variablesName="receiveBankPay";
                if("发薪地财务复核".equals(actRuTaskVo.getName())){
                    documentStatus=PaymentApplyDocumentStatusEnum.REVIEWED.getCode();
                }
                //先查旧的，如果有旧的，修改del_flag就可以，如果没有就新增
                if(!oldPayServiceSerialDetailTypeList.contains(PayServiceSerialDetailType.SEND_PAYROLL_REV_COM.getCode())){
                    psslInsertVoList = setPayServiceSerialLogVoList(variablesMap, applyVo, loginName);
                    psslInsertVoList.removeIf(p->p.getPayServiceSerialDetailType().equals(PayServiceSerialDetailType.SEND_PAYROLL_PAY_COM.getCode()));
                    if(CollectionUtils.isEmpty(psslInsertVoList)){
                        throw new RuntimeException(ResultEnum.ERROR_PREFIX+"该支付的【接单地】【银行卡】不支持银企,无法设置");
                    }
                }
            }
            List<SalaryPaymentApplyVo> oldSalaryPaymentApplyVoList = salaryPaymentApplyMapper.getIncludingDeletedByPaymentId(paymentApplyId);
            if(CollectionUtils.isEmpty(oldSalaryPaymentApplyVoList)){
                SalaryPaymentApplyVo salaryPaymentApplyVo=new SalaryPaymentApplyVo();
                salaryPaymentApplyVo.setPayApplyId(paymentApplyId);
                salaryPaymentApplyVo.setDispatchPrintFlag(PrintFlagEnum.NO.getCode());
                salaryPaymentApplyVo.setReceivingPrintFlag(PrintFlagEnum.NO.getCode());
                salaryPaymentApplyVo.setCreator(loginName);
                salaryPaymentApplyMapper.insertSelective(salaryPaymentApplyVo);
            }

        }
        if(CollectionUtils.isNotEmpty(psslInsertVoList)){
            payServiceSerialLogMapper.insertByList(psslInsertVoList);
        }else {
            payServiceSerialLogMapper.updateByPaymentIdAndPayServiceSerialDetailType(paymentApplyId, delFlag,payServiceSerialDetailType.getCode(),loginName);
        }
        workflowWrapperService.updateVariablesByPid(applyVo.getPid(),variablesName,variablesValue);
        if(documentStatus!=null){
            paymentApplyMapper.updateDocumentStatusById(paymentApplyId,documentStatus,loginName);
        }
        return 0;
    }

    public void updateDocumentStatusAndExcuteTask(List<String> pidList,List<String> taskIdList,Integer documentStatus,Map<String, Object> variables){
        paymentApplyService.updateDocumentStatus(pidList,PaymentApplyDocumentStatusEnum.DOCUMENT_MADE.getCode()==documentStatus?
                PaymentApplyDocumentStatusEnum.REVIEWED.getCode():PaymentApplyDocumentStatusEnum.OWN_REVIEWED.getCode(),null);
        if(CollectionUtils.isNotEmpty(taskIdList)&&taskIdList.get(0)!=null){
            for (String taskId:taskIdList) {
                workflowWrapperService.excuteTask(taskId, variables, ManualAction.PASS,ManualAction.PASS.getDescription());
            }
        }
    }
    public String checkAndSetBalance(Map<String, AcctInfo> acctInfoMap,IntegrationDto integrationDto){
        if(acctInfoMap.containsKey(integrationDto.getAccnbr())){
            AcctInfo acctInfo = acctInfoMap.get(integrationDto.getAccnbr());
            BigDecimal onlblv = new BigDecimal(acctInfo.getOnlblv());
            BigDecimal avlblv = new BigDecimal(acctInfo.getAvlblv());
            //取可用余额和联机余额中最小的
            if(avlblv.compareTo(onlblv)<0){
                onlblv=avlblv;
            }
            BigDecimal balance = onlblv.subtract(integrationDto.getTrsamt());
            if(balance.compareTo(BigDecimal.ZERO)>=0){
                acctInfo.setOnlblv(balance.toString());
                acctInfoMap.put(integrationDto.getAccnbr(),acctInfo);
            }else {
                return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付对应账户余额不足！");
            }
        }else {
            return (ERR_MSG+"支付id:"+integrationDto.getPaymentId()+"这条支付对应账户未找到！");
        }
        return null;
    }

    /**
     @param orgCode 机构code
     @param posCode 岗位code
     @return orgCode, posCode
     */
    private String getOrgPosCodeStrByOrgAndPosCode(String orgCode, String posCode) {
        return new StringBuilder().append(orgCode).append(",").append(posCode).toString();
    }
}
