var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'jquery'], function () {
    var table = layui.table, form = layui.form, $ = layui.$, laydate = layui.laydate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;


    var getName = function () {
        var getNameResult = "";
        let InitCompanyOrgCode = $("#InitCompanyOrgCode").val();
        let InitCompanyName = $("#InitCompanyOrgCode option:selected").text()
        ML.ajax("/customer/employeeContract/getBuClassName", {
            orderNo: $("#orderNo").val(),
            companyNameType: $("#companyNameType").val(),
            signatureType: $("#signatureType").val(),
            empContractType: $("#empContractType").val(),
            InitCompanyOrgCode: InitCompanyOrgCode,
            InitCompanyName: InitCompanyName
        }, function (result) {
            if (result.code == 0) {
                getNameResult = "<span style='color:#05ee05'> " + result.msg + "</span>";
            }
            if (result.code == -1) {
                getNameResult = "<span style='color:red'> " + result.msg + "</span>";
            }

        }, "post", false)
        return getNameResult;
    }


    var option = "";
    let tempPlaceStr = "";

    function loadOrg() {
        return $.ajax({ // 添加return
            type: "GET",
            url: ML.contextPath + "/companyAddrList/getAllCompanyAddrMap",
            dataType: 'json',
            success: function (data) {
                $("#InitCompanyOrgCode").empty();
                $("#tempPlaceStr").empty();
                var signDistCom = data;
                $.each(signDistCom, function (code, name) {
                    if (code == $("#tempPlace").val()) $('<option value="' + code + '">' + name + '</option>').appendTo("#InitCompanyOrgCode")
                    if (code == $("#tempPlace").val()) {
                        $('<option selected value="' + code + '">' + name + '</option>').appendTo("#tempPlaceStr")
                    } else {
                        $('<option value="' + code + '">' + name + '</option>').appendTo("#tempPlaceStr")
                    }
                });
                let tempPlaceStr = $("#tempPlaceStr option:selected").text();
                if (ML.isEmpty(tempPlaceStr)) {
                    tempPlaceStr = "员工合同中合同版本地没有数据!请去维护";
                    $('<option value="' + null + '">' + tempPlaceStr + '</option>').appendTo("#tempPlaceStr")
                }
                form.render("select");
            },
            error: function (data) {
                console.log("error")
            }
        });
    }


    function submit(InitCompanyOrgCode, InitCompanyName) {
        $.ajax({
            type: 'post',
            url: ML.contextPath + '/customer/employeeContract/startQYSContract',
            timeout: 60000,
            data: {
                orderNo: $("#orderNo").val(),
                companyNameType: $("#companyNameType").val(),
                signatureType: $("#signatureType").val(),
                empContractType: $("#empContractType").val(),
                InitCompanyOrgCode: InitCompanyOrgCode,
                InitCompanyName: InitCompanyName,
                tempPlace: $("#tempPlaceStr").val(),
                tempPlaceName: $("#tempPlaceStr option:selected").text(),
                renewStart: $("#renewStart").val(),
                renewEnd: $("#renewEnd").val(),
                empContractNo: $("#empContractNo").val()
            },
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded;charset=UTF-8'
        }).done(function (result) {
            layer.msg(result.msg, {time: 6000})
            $("#submit").removeAttr("disabled");
            console.log("result: "+result);
            console.log("result.code: "+result.code);
            if (result.code == 1) {
                layer.closeAll();
                table.reload("employeeContractGrid")
                window.open(result.data, '_blank');
            }
            if (result.code == 0) layer.closeAll(); //如果设定了yes回调，需进行手工关闭
        });

        //
        // ML.ajax("/customer/employeeContract/startQYSContract", {
        //     orderNo: $("#orderNo").val(),
        //     companyNameType: $("#companyNameType").val(),
        //     signatureType: $("#signatureType").val(),
        //     empContractType: $("#empContractType").val(),
        //     InitCompanyOrgCode: InitCompanyOrgCode,
        //     InitCompanyName: InitCompanyName,
        //     tempPlace: $("#tempPlaceStr").val(),
        //     tempPlaceName: $("#tempPlaceStr option:selected").text(),
        //     renewStart: $("#renewStart").val(),
        //     renewEnd: $("#renewEnd").val(),
        //     empContractNo: $("#empContractNo").val()
        // }, null, "post", false).done(function (result) {
        //
        //
        // })

    }

    $("#submit").click(function () {
        let InitCompanyOrgCode = $("#InitCompanyOrgCode").val();
        let InitCompanyName = $("#InitCompanyOrgCode option:selected").text()

        if (ML.isEmpty($("#tempType").val())) {
            return layer.msg("员工合同中,合同版本为空,请维护后再发起!");
        }
        if ($("#signatureType").val() == 2 &&
            (ML.isEmpty($("#tempPlaceStr").val()) || ML.isEmpty($("#renewStart").val()) || ML.isEmpty($("#renewEnd").val()))) {
            return layer.msg("请填写完整的续签信息! 如: 版本地、续签开始时间、续签结束时间");
        }


        layer.confirm("确定要发起吗？", {btn: ['确定', '取消']}, function () {
            $("#submit").attr("disabled", "disabled");
            submit(InitCompanyOrgCode, InitCompanyName);
        })
    })
    $("#close").click(function () {
        layer.closeAll();
    })

    var buClassName = "";


    function dealCompanyNameType(companyNameType) {
        buClassName = "";
        if (companyNameType == 2) {
            $(".custNameShow").removeClass("layui-hide");
            $(".custNameShow").addClass("layui-show");
            buClassName = getName();
        } else {
            $(".custNameShow").addClass("layui-hide");
            $(".custNameShow").removeClass("layui-show");
        }
        $("#buClassName").html(buClassName);
        form.render()
    }

// 监听 标准/单位名称:
    form.on("select(companyNameTypeFilter)", function (data) {
        dealCompanyNameType($("#companyNameType").val());
    });

    // 监听 签署类别
    form.on("select(signatureTypeFilter)", function (data) {
        buClassName = "";
        if (data.value == 3) {
            $(".dimShow").removeClass("layui-hide");
            $(".dimShow").addClass("layui-show");
        } else {
            $(".dimShow").addClass("layui-hide");
            $(".dimShow").removeClass("layui-show");
        }
        // 如果是续签,需要 处理一系列 数据
        if (data.value == 2) {
            $(".renewShow").removeClass("layui-hide");
            $(".renewShow").addClass("layui-show");
            $("#tempPlaceStr").removeAttr("disabled");
        } else {
            loadOrg();  // 如果不是续签,需要恢复默认的版本地
            $(".renewShow").addClass("layui-hide");
            $(".renewShow").removeClass("layui-show");
            $("#tempPlaceStr").attr("disabled", "disabled");
        }

        // 如果是单位名称发起,则获取名称并展示
        if ($("#companyNameType").val() == 2) {
            buClassName = getName();
            $("#buClassName").html(buClassName);
        }
        form.render()
    });


    var startDate = $("#renewStartDateStr").val();
    var endDate = '';
    $(this).removeAttr("lay-key"); //  去掉lay-key属性，否则会导致laydate渲染两次
    laydate.render({
        elem: '#renewStart', // 指向第一个输入框
        trigger: 'click',
        done: function (value) {
            startDate = value;
            if ($("#renewStartDateStr").val() && startDate) {
                if (new Date(startDate) < new Date($("#renewStartDateStr").val())) {
                    layer.msg('起始日期不能早于上一份合同起始日');
                    $("#renewStart").val($("#renewStartDateStr").val());
                }
            }
            // 如果已存在结束日期且早于新起始日期
            if (startDate && endDate) {
                if (new Date(endDate) < new Date(startDate)) {
                    layer.msg('截止日期不能早于起始日期');
                    $("#renewStart").val("");
                }
            }
        }
    });
    laydate.render({
        elem: '#renewEnd', // 指向第一个输入框
        trigger: 'click',
        done: function (value) {
            endDate = value;
            // 如果已存在结束日期且早于新起始日期
            if (startDate && endDate) {
                if (new Date(endDate) < new Date(startDate)) {
                    layer.msg('截止日期不能早于起始日期');
                    $("#renewEnd").val("");
                }
            }
        }
    });

    $(document).ready(function () {
        loadOrg().done(function () {
            // 如果 是 客户范本 2  范本 1
            // 标准/单位名称 只能选择单位名称
            if ($("#tempType").val() == 2) {
                $("#companyNameType").val("2")
                $("#companyNameType").attr("disabled", true);
                dealCompanyNameType("2")
            }
        })

    });
});