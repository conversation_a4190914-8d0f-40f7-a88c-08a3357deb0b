package com.reon.hr.modules.change.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.vo.AreaVo;
import com.reon.hr.api.change.dubbo.service.rpc.change.ICollectWrapperService;
import com.reon.hr.api.change.enums.BaseEmployeeEnum;
import com.reon.hr.api.change.enums.CollectDataEnum;
import com.reon.hr.api.change.utils.ExportExcelUtil;
import com.reon.hr.api.change.vo.CollectEmpVo;
import com.reon.hr.api.change.vo.CollectVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractResourceWrapperService;
import com.reon.hr.api.customer.vo.ContractPageVo;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.OrgTypeEnum;
import com.reon.hr.api.enums.PositionEnum;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.modules.common.BaseController;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/collect")
public class CollectController extends BaseController {
    @Autowired
    private ICollectWrapperService iCollectWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService orgnizationService;

    @Resource(name = "userDubboService")
    private IUserWrapperService userWrapperService;

    @Resource(name = "contractDubboService")
    private IContractResourceWrapperService contractResourceWrapperService;

    @Autowired
    private IOrgnizationResourceWrapperService iOrgnizationResourceWrapperService;

    @Autowired
    private IAreaResourceWrapperService iAreaResourceWrapperService;

    /**
     * 跳转到收集查询页面
     *
     * @return
     */
    @RequestMapping("gotoCollectQueryView")
    public ModelAndView gotoCollectQueryView() {
        return new ModelAndView ("/change/collect/collectQuery");
    }

    /**
     * 查询调基收集数据
     *
     * @param page
     * @param limit
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getCollectWrapperListPage")
    public Object getCollectWrapperListPage(CollectVo collectVo, Integer page, Integer limit) {
        collectVo.setCommissioner (getSessionUser ().getLoginName ());
        Page<CollectVo> collectWrapperListPage = iCollectWrapperService.getCollectWrapperListPage (collectVo, page, limit);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), collectWrapperListPage.getTotal (), collectWrapperListPage.getRecords ());
    }

    /**
     * 获取客户信息
     *
     * @param page
     * @param limit
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "getContractPageByName")
    public LayuiReplay<ContractPageVo> getContractPageByName(@RequestParam("page") int page, @RequestParam("limit") int limit, @RequestParam(value = "searchParam", required = false) String searchParam, HttpSession session) {
        String name = "";
        if (StringUtils.isNotBlank (searchParam)) {
            JSONObject json = net.sf.json.JSONObject.fromObject (searchParam);
            name = json.getString ("name").trim ();
        }
        String commManager = userWrapperService.findLoginNameBySameCityOrgTypeAndPosition (orgnizationService.getOrgCode (getSessionUser ()).getOrgCode (), OrgTypeEnum.PART_COMPANY_DT.getCode (), PositionEnum.PROJECT_MANAGER.getCode ());
        List<OrgPositionDto> userOrgPositionDtoList = (List<OrgPositionDto>) session.getAttribute(Constants.SESSION_ORG_POSITION);
        Page<ContractPageVo> pageList = contractResourceWrapperService.getPageByName (page, limit, name, commManager,userOrgPositionDtoList);
        return new LayuiReplay<> (ResultEnum.OK.getCode (), ResultEnum.OK.getMsg (), pageList.getTotal (), pageList.getRecords ());
    }


    /**
     * 导出收集数据
     *
     * @param response
     */
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(HttpServletResponse response) {
        String loginUser = getSessionUser ().getUserName ();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat ("yyyyMMddHHmmssSSS");
        Date date = new Date ();
        String filePath = loginUser + "_" + simpleDateFormat.format (date.getTime ()) + "_" + "收集数据" + ".xls";
        CollectVo collectVo = new CollectVo ();
        collectVo.setCommissioner (getSessionUser ().getLoginName ());
        Page<CollectVo> collectWrapperListPage = iCollectWrapperService.getCollectWrapperListPage (collectVo, 0, 0);
        collectWrapperListPage.getRecords ().forEach (vo -> {
            vo.setCommissioner (loginUser);
            List<OrgVo> allCompany = iOrgnizationResourceWrapperService.findAllCompany ();
            for (OrgVo orgVo : allCompany) {
                if (vo.getReceiving ().equals (orgVo.getOrgCode ())) {
                    vo.setReceivingStr (orgVo.getOrgName ());
                }
            }
            List<AreaVo> provinceCitys = iAreaResourceWrapperService.findCityInfo ();
            provinceCitys.forEach (pc -> {
                if (String.valueOf (pc.getCode ()).equals (vo.getCityCode ())) {
                    vo.setCityCodeStr (pc.getName ());
                }
            });
        });
        try {
            HSSFWorkbook wb = new HSSFWorkbook ();
            ExportExcelUtil.export (collectWrapperListPage.getRecords (),
                    ExportExcelUtil.getCollectDataHeaders (),
                    "收集数据", CollectDataEnum.getValue (), wb);
            ExportExcelUtil.getOutputStream (response, filePath, wb);
        } catch (Exception e) {
            e.printStackTrace ();

        }

    }
}
