package com.reon.hr.api.customer.dto.importData;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;

import java.io.Serializable;
import java.util.List;

@ExcelIgnoreUnannotated
public class SalaryEmployeeImportDto extends BaseImportDto implements Serializable {

    @ExcelProperty("合同编号*")
    private String contractNo;

    private String custNo;

    @ExcelProperty("员工姓名*")
    private String name;

    @ExcelProperty("证件类型*")
    private String certType;

    @ExcelProperty("身份证号码*")
    private String certNo;

    @ExcelProperty("手机")
    private String mobile;

    @ExcelProperty("开户人姓名")
    private String acctName;

    @ExcelProperty("开户银行")
    private String bankName;

    @ExcelProperty("其他银行")
    private String otherBank;

    @ExcelProperty("开户分支行")
    private String subBank;

    @ExcelProperty("银行卡号")
    private String cardNo;

    @ExcelProperty("开户银行所在省")
    private String openProvince;

    @ExcelProperty("开户银行所在城市")
    private String openingPlace;

    @ExcelProperty("邮箱")
    private String email;
    @ExcelProperty("是否竞业员工")
    private String competitionFlagS;
    private Integer competitionFlag;


    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getOtherBank() {
        return otherBank;
    }

    public void setOtherBank(String otherBank) {
        this.otherBank = otherBank;
    }

    public String getSubBank() {
        return subBank;
    }

    public void setSubBank(String subBank) {
        this.subBank = subBank;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getOpenProvince() {
        return openProvince;
    }

    public void setOpenProvince(String openProvince) {
        this.openProvince = openProvince;
    }

    public String getOpeningPlace() {
        return openingPlace;
    }

    public void setOpeningPlace(String openingPlace) {
        this.openingPlace = openingPlace;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getCompetitionFlagS() {
        return competitionFlagS;
    }

    public void setCompetitionFlagS(String competitionFlagS) {
        this.competitionFlagS = competitionFlagS;
    }

    public Integer getCompetitionFlag() {
        return competitionFlag;
    }

    public void setCompetitionFlag(Integer competitionFlag) {
        this.competitionFlag = competitionFlag;
    }
}
