package com.reon.hr.api.customer.utils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AddEoCaWlVo implements Serializable {
    private Long id;

    /**
     小合同编号
     */
    @Excel(name = "小合同编号",height = 28,width = 20)
    private String contractAreaNo;
    @Excel(name = "小合同名称",height = 28,width = 20)
    private String contractAreaName;
    /**
     开始年月
     */
    @Excel(name = "起始日期",height = 28,width = 20)
    private Integer startMonth;

    /**
     文件ID
     */
    private String fileId;

    /**
     创建人
     */
    @Excel(name = "创建人",height = 28,width = 20)
    private String creator;

    /**
     创建时间
     */
    @Excel(name = "创建时间",height = 28,width = 20)
    private String createTimeStr;
    private Date createTime;

    /**
     修改人
     */
    private String updater;

    /**
     修改时间
     */
    private Date updateTime;

    /**
     删除标识(Y:已删除，N:未删除)
     */
    private String delFlag;
    @Excel(name = "备注",height = 28,width = 20)
    private String remark;
    private String contractAreaListStr ;
    private Set<String> contractAreaSet = new HashSet<>();
}
