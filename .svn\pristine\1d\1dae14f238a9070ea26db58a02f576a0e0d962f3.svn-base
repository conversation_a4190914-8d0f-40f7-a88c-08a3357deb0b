package com.reon.hr.api.customer.dto.insurancePractice;

import java.io.Serializable;

public class ReportPullInsurancePracticeDto  implements Serializable {

    private String packCode;
    private String orgCode;
    private String orderNo;

    private Long custId;

    private Integer lockMonth;

    private String updater;

    private Byte feeType;

    public Byte getFeeType() {
        return feeType;
    }

    public void setFeeType(Byte feeType) {
        this.feeType = feeType;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getPackCode() {
        return packCode;
    }

    public void setPackCode(String packCode) {
        this.packCode = packCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Integer getLockMonth() {
        return lockMonth;
    }

    public void setLockMonth(Integer lockMonth) {
        this.lockMonth = lockMonth;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return "ReportPullInsurancePracticeDto{" +
                ", custId=" + custId +
                ", lockMonth=" + lockMonth +
                ", updater='" + updater + '\'' +
                ", feeType=" + feeType +
                '}';
    }
}
