<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.cus.CommEmpPeriodMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    <id column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <id column="emp_id" jdbcType="VARCHAR" property="empId" />
    <result column="first_month" jdbcType="DATE" property="firstMonth" />
    <result column="current_month" jdbcType="DATE" property="currentMonth" />
    <result column="next_month" jdbcType="DATE" property="nextMonth" />
    <result column="creator" jdbcType="DATE" property="creator" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    order_no,emp_id, first_month, current_month, next_month, creator, create_time, updater, update_time,return_month
  </sql>
  <delete id="deleteByOrderNoAndFirstMontAndCurrentMonth">
    delete from comm_emp_period where order_no in <foreach collection="list" item="item" open="(" close=")" separator=",">
    #{item}
  </foreach> and first_month = #{billMonth} and current_month = #{billMonth}
  </delete>
  <select id="selectByPrimaryKey" resultType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    select 
    <include refid="Base_Column_List" />
    from comm_emp_period
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <insert id="insert" parameterType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    insert into comm_emp_period (order_no,emp_id, first_month, current_month,
      next_month, creator, create_time, 
      updater, update_time)
    values (#{orderNo,jdbcType=VARCHAR},#{empId,jdbcType=VARCHAR}, #{firstMonth,jdbcType=DATE}, #{currentMonth,jdbcType=DATE},
      #{nextMonth,jdbcType=DATE}, #{creator,jdbcType=DATE}, #{createTime,jdbcType=DATE}, 
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    insert into comm_emp_period
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="firstMonth != null">
        first_month,
      </if>
      <if test="currentMonth != null">
        current_month,
      </if>
      <if test="nextMonth != null">
        next_month,
      </if>
      <if test="returnMonth!=null">
        return_month,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=VARCHAR},
      </if>
      <if test="firstMonth != null">
        #{firstMonth,jdbcType=DATE},
      </if>
      <if test="currentMonth != null">
        #{currentMonth,jdbcType=DATE},
      </if>
      <if test="nextMonth != null">
        #{nextMonth,jdbcType=DATE},
      </if>
      <if test="returnMonth!=null">
        #{returnMonth},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    update comm_emp_period
    <set>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=VARCHAR},
      </if>
      <if test="firstMonth != null">
        first_month = #{firstMonth,jdbcType=DATE},
      </if>
      <if test="currentMonth != null">
        current_month = #{currentMonth,jdbcType=DATE},
      </if>
      <if test="nextMonth != null">
        next_month = #{nextMonth,jdbcType=DATE},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="returnMonth!=null">
        return_month =#{returnMonth},
      </if>
        update_time = now()
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </update>
  <select id="selectByPrimaryKeyList" resultType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    select
    <include refid="Base_Column_List" />
    from comm_emp_period
    where order_no in
     <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
       #{orderNo,jdbcType=VARCHAR}
     </foreach>
  </select>
  <update id="updateByPrimaryKeySelectiveList" parameterType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    update comm_emp_period
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="emp_id = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.empId != null">
            when order_no = #{item.orderNo}
            then #{item.empId}
          </if>
        </foreach>
      </trim>
      <trim prefix="first_month = case" suffix="else first_month end,">
        <foreach collection="list" item="item">
          <if test="item.firstMonth != null">
            when order_no = #{item.orderNo}
            then #{item.firstMonth}
          </if>
        </foreach>
      </trim>
      <trim prefix="current_month = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.currentMonth != null">
            when order_no = #{item.orderNo}
            then #{item.currentMonth}
          </if>
        </foreach>
      </trim>
      <trim prefix="next_month = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.nextMonth != null">
            when order_no = #{item.orderNo}
            then #{item.nextMonth}
          </if>
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.creator != null">
            when order_no = #{item.orderNo}
            then #{item.creator}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.createTime != null">
            when order_no = #{item.orderNo}
            then #{item.createTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.updater != null">
            when order_no = #{item.orderNo}
            then #{item.updater}
          </if>
        </foreach>
      </trim>
      <trim prefix="return_month = case" suffix="else return_month end,">
        <foreach collection="list" item="item">
          <if test="item.returnMonth != null">
            when order_no = #{item.orderNo}
            then #{item.returnMonth}
          </if>
        </foreach>
      </trim>
      update_time = now()
    </trim>
    where order_no in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item.orderNo,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="insertSelectiveList" parameterType="com.reon.hr.sp.bill.entity.cus.CommEmpPeriod">
    insert into comm_emp_period
    (
    order_no,emp_id,first_month,current_month,next_month,return_month,creator,updater
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.orderNo,jdbcType=VARCHAR},
        #{item.empId,jdbcType=VARCHAR},
        #{item.firstMonth,jdbcType=DATE},
        #{item.currentMonth,jdbcType=DATE},
        #{item.nextMonth,jdbcType=DATE},
        #{item.returnMonth},
        #{item.creator,jdbcType=DATE},
        #{item.updater,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateReturnMonthByOrderNo">
    update comm_emp_period set return_month =#{month}  where order_no =#{orderNo}
  </update>
</mapper>