package com.reon.hr.api.customer.dubbo.service.rpc.customer.commInsurOrder;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.CommInsurEmpVo;
import com.reon.hr.api.customer.vo.commInsurOrder.ComInsurOrderLogVo;
import com.reon.hr.api.customer.vo.commInsurOrder.CommInsurOrderExportVo;
import com.reon.hr.api.customer.vo.commInsurOrder.CommInsurOrderReportVo;
import com.reon.hr.api.customer.vo.commInsurOrder.CommInsurOrderVo;
import com.reon.hr.api.customer.vo.employee.EmpRelativeVo;
import com.reon.hr.api.customer.vo.employee.EmployeeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.concurrent.ForkJoinPool;

public interface ICommInsurOrderWrapperService {

    static final ForkJoinPool CIO_FORK_JOIN_POOL = new ForkJoinPool (1);
    /**
     * 商保订单分页查询
     * @param commInsurOrderVo
     * @param page
     * @param limit
     * @return
     */
    Page<CommInsurOrderVo> getCommInsurOrderVoListPage(CommInsurOrderVo commInsurOrderVo, Integer page, Integer limit);


    /**
     * 根据条件查询所有商保订单
     * @param commInsurOrderVo
     * @return
     */
    List<CommInsurOrderExportVo> getCommInsurOrderLists(CommInsurOrderVo commInsurOrderVo);


    /**
     * 商保订单保存和提交接口
     * @param record
     * @return
     */
    boolean save(CommInsurOrderVo record);

    /**
     * 根据订单编号查询商保订关联的数据
     * @param orderNo
     * @return
     */
   CommInsurOrderVo getCommInsurOrderVo(String orderNo);

    CommInsurOrderVo getEndMonthByOrderNo(String orderNo);

    /**
     * 修改商保订单接口
     * @param commInsurOrderVo
     * @return
     */
    boolean update(CommInsurOrderVo commInsurOrderVo);

    /**
     * 批量修改商保订单接口
     * @param commInsurOrderVo
     * @return
     */
    boolean batchUpdate(CommInsurOrderVo commInsurOrderVo);

    /**
     * 批量删除商保订单接口
     * @param commInsurOrderVo
     * @return
     */
    boolean batchDelete(CommInsurOrderVo commInsurOrderVo);

    /**
     * 减员申请接口
     * @param commInsurOrderVo
     * @return
     */
    boolean orderApply(CommInsurOrderVo commInsurOrderVo);


    /**
     * 验证员工身份证是否唯一
     * @param certNo
     * @return
     */
    String verificationCertNo(String certNo,Long id);


    /**
     * 验证关联员工身份证是否唯一
     * @param certNo
     * @return
     */
    String verificationAssociatedCertNo(String certNo,Long empId);


    /**
     * 查询历史日志
     * @param comInsurOrderLogVo
     * @param page
     * @param limit
     * @return
     */
    Page<ComInsurOrderLogVo> getCommInsurOrderHistoryLogListPage(ComInsurOrderLogVo comInsurOrderLogVo,Integer page, Integer limit);
    /**
     * 根据合同和客户查询相关的商保订单信息 ,
     * 生成账单调用*/
    List<CommInsurEmpVo> getCommercialEmpByCustId(Long custId,String ContractNo,Long templetId ,Integer billMonth);
    /**
     * 根据查询员工具体信息*/
    CommInsurEmpVo  getCommercialEmpByOrderNo(String orderNo);


    List<EmployeeVo> selectEmployeeByCertNo(String certNo);
    List<EmpRelativeVo> selectEmpRelativeByEmpRelativeCertNo(String empRelativeCertNo);

    List<EmpRelativeVo> selectEmpRelativeByEmpId(Long empId);
    void updateEmpRelativeById(Long id);

	List<CommInsurOrderReportVo> getCommInsuraDataForExport(CommInsurOrderVo commInsurOrderVo, Integer begin);
    List<CommInsurOrderReportVo> getStatusByOrderNoList(List<String> orderNoList);
    int updateEmployeeById(EmployeeVo employeeVo);

    List<EmpRelativeVo> selectEmpRelativeByEmpIdList(List<Long> empIdList);

    /**
     * 更新商保订单关联人信息
     * @param empRelativeVos
     */
    void updateByPrimaryKeySelectiveList(List<EmpRelativeVo> empRelativeVos);
}
