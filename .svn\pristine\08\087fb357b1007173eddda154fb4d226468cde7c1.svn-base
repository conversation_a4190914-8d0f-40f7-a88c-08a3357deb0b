package com.reon.hr.sp.bill.dao.supplierPractice;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.bill.vo.bill.SupplierPracticeBillReportVo;
import com.reon.hr.api.bill.vo.supplierPractice.InsuranceSupplierPracticeDetailVo;
import com.reon.hr.api.bill.vo.supplierPractice.InsuranceSupplierPracticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年01月10日
 * @Version 1.0
 */

public interface SupplierInsurancePracticeMapper {


    List<InsuranceSupplierPracticeVo> getInsuranceSupplierPracticeList();

    List<InsuranceSupplierPracticeVo> getInsuranceSupplierPracticeListPage(@Param("page") Page page, @Param("vo") InsuranceSupplierPracticeVo insuranceSupplierPracticeVo);


    int insertInsuranceSupplierPractice(InsuranceSupplierPracticeVo insuranceSupplierPracticeVo);

    int updateSupplierAndInsurance(InsuranceSupplierPracticeVo insuranceSupplierPracticeVo);

    int getOrgCodeCount(String orgCode);


    List<InsuranceSupplierPracticeVo> getInsuranceSupplierPractice(@Param("vo") InsuranceSupplierPracticeVo insuranceSupplierPracticeVo);
    Long getInsuranceSupplierPracticeId(@Param("vo") InsuranceSupplierPracticeVo insuranceSupplierPracticeVo);

    List<InsuranceSupplierPracticeDetailVo> getInsuranceSupplierPracticeByMonthList(@Param("orgCode") String orgCode, @Param("supBillId") Long supBillId, @Param("generatedMonth") Integer generatedMonth);

    int checkInsuranceSupplierPractice(@Param("month") Integer genMonth);

    List<InsuranceSupplierPracticeVo> exportDetail(@Param("id") Long id);
}
