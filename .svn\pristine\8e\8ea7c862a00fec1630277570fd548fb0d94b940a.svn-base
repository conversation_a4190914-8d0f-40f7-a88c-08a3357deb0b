var ctx = ML.contextPath;

/**
 * 获取浏览器版本
 * @returns
 */
function getBrowserInfo() {
    var agent = navigator.userAgent.toLowerCase();
    var regStr_ie = /msie [\d.]+;/gi;
    var regStr_ff = /firefox\/[\d.]+/gi;
    var regStr_chrome = /chrome\/[\d.]+/gi;
    var regStr_saf = /safari\/[\d.]+/gi;
    //IE
    if (agent.indexOf("msie") > 0) {
        return agent.match(regStr_ie);
    }

    //firefox
    if (agent.indexOf("firefox") > 0) {
        return agent.match(regStr_ff);
    }

    //Chrome
    if (agent.indexOf("chrome") > 0) {
        return agent.match(regStr_chrome);
    }

    //Safari
    if (agent.indexOf("safari") > 0 && agent.indexOf("chrome") < 0) {
        return agent.match(regStr_saf);
    }
}

/**
 * 获取操作系统版本
 * @returns {String}
 */
function detectOS() {
    var sUserAgent = navigator.userAgent;
    var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
    var isMac = (navigator.platform == "Mac68K") || (navigator.platform == "MacPPC") || (navigator.platform == "Macintosh") || (navigator.platform == "MacIntel");
    if (isMac) return "Mac";
    var isUnix = (navigator.platform == "X11") && !isWin && !isMac;
    if (isUnix) return "Unix";
    var isLinux = (String(navigator.platform).indexOf("Linux") > -1);
    if (isLinux) return "Linux";
    if (isWin) {
        var isWin2K = sUserAgent.indexOf("Windows NT 5.0") > -1 || sUserAgent.indexOf("Windows 2000") > -1;
        if (isWin2K) return "Win2000";
        var isWinXP = sUserAgent.indexOf("Windows NT 5.1") > -1 || sUserAgent.indexOf("Windows XP") > -1;
        if (isWinXP) return "WinXP";
        var isWin2003 = sUserAgent.indexOf("Windows NT 5.2") > -1 || sUserAgent.indexOf("Windows 2003") > -1;
        if (isWin2003) return "Win2003";
        var isWinVista = sUserAgent.indexOf("Windows NT 6.0") > -1 || sUserAgent.indexOf("Windows Vista") > -1;
        if (isWinVista) return "WinVista";
        var isWin7 = sUserAgent.indexOf("Windows NT 6.1") > -1 || sUserAgent.indexOf("Windows 7") > -1;
        if (isWin7) return "Win7";
    }
    return "other";
}

function showContent(content) {
    layer.open({
        type: 1,
        title: "内容详情",
        area: ['80%', '80%'],
        shade: 0.8,
        shadeClose: true,
        content: '<div style="padding:20px; word-break: break-word;">' + decodeURIComponent(content) + '</div>'
    });
}


/**
 * 获取屏幕分辨率
 * @returns {String}
 */
function getScreenResolution() {
    return window.screen.width + "*" + window.screen.height;
}

///判断字段数据是否存在
function nullData(data) {
    return (!data) ? "未知" : data;
}

layui.config({
    base: "js/"
}).use(['form', 'element', 'layer', 'jquery', 'upload', 'table', 'bodyTab', 'tabRightMenu'], function () {
    var form = layui.form,
        table = layui.table,
        layer = parent.layer === undefined ? layui.layer : parent.layer,
        element = layui.element,
        rightMenu = layui.tabRightMenu,
        $ = layui.jquery;

    $(".panel a").on("click", function () {
        window.parent.addTab($(this));
    })

    $("#tdOS").text(nullData(detectOS()));        			//操作系统
    $("#tdBowserName").text(nullData(getBrowserInfo()));        	//浏览器
    $("#tdBowserInfo").text(nullData(navigator.appVersion));  //当前版本
    $("#tdScreenResolution").text(nullData(getScreenResolution()));       //屏幕分辨率
// 页面 加载事件
    $(document).ready(function () {
        getToDoItemCount();
    });
    var uploadIds = [];
    table.render({
        id: 'batchImportGrid'
        , elem: '#batchImportGrid'
        , url: ML.contextPath + '/sys/policy/getHomePageFileByLogin'
        , page: false
        /*, toolbar: '#topbtn'
        , defaultToolbar: []*/
        , limit: 50
        , width: '100%'
        , method: 'GET'
        , limits: [50, 100, 200],
        height: 400
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left', hide: true}

            , {
                field: 'fileId', title: '文件ID', align: 'center', width: '0', bgcolor: '#ffffff', hide: true
            }
            , {
                field: 'fileTypeStr', title: '文件类型', align: 'center', width: '100', bgcolor: '#ffffff'
            }
            , {
                field: 'fileName', title: '文件名称', align: 'center', width: '350', bgcolor: '#ffffff'
            }, {
                field: 'createTime', title: '上传时间', align: 'center', width: '180', bgcolor: '#ffffff'
            }
            , {field: '', title: '操作', toolbar: '#btn', width: '164', align: 'center'}


        ]],
        done: function (res, curr, count) {
            /*$('tr').css({'background-color': '#ffffff', 'color': '#020202'});*/
        }

    });

    // 监听 查询结果表格 中 查看按钮
    table.on('tool(batchImportGridTable)', function (res) {
        downLoad(res.data.fileId)
    });

    function downLoad(id) {
        if (id) {
            window.location.href = ML.contextPath + "/sys/policy/upload?fileId=" + id;
        }
    }

    $("#btnQuery").click(function () {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看全部文件',
            area: ['1300px', '800px'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + '/sys/policy/gotoAllPolicyFileView?downType=' + 1,
            end: function () {
                closeInterval = setInterval('reloadView()', 5000);
            }
        });
    });

    $("#btnQuery2").click(function () {
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: '查看全部文件',
            area: ['1300px', '800px'],
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + '/sys/policy/gotoAllPolicyFileView?downType=' + 2,
            end: function () {
                closeInterval = setInterval('reloadView()', 5000);
            }
        });
    });

    var queryValue = 0;
    var currentLayerIndex;

    function getToDoItemCount() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/sys/workflowCount/getAllTaskCount",
            dataType: 'json',
            success: function (data) {
                var queryValueLink = document.getElementById("queryValueLink");
                // 动态生成表格的 HTML 内容
                var tableHtml = '<table class="layui-table">';
                tableHtml += '<thead><tr><th style="text-align:center;">序号</th><th style="text-align:center;">待办任务名称</th><th style="text-align:center;">待办任务数量</th></tr></thead>';
                tableHtml += '<tbody>';
                data.data.forEach(function (item) {
                    tableHtml += '<tr>';
                    tableHtml += '<td class="key_col" style="text-align:center;">' + '<br>' + item.key + '</td>';
                    tableHtml += '<td style="text-align:center;">' + '<br>' + item.keyName + '</td>';
                    if (item.key == 19) {   // 分配后自动开通 不需要跳转
                        tableHtml += '<td class="num_col" style="text-align:center;">' + '<br>' + item.num + '</td>';
                    } else {
                        tableHtml += '<td class="num_col" style="text-align:center;">' + '<br>' + "<a href='javascript:void(0);' style='color:blue;text-decoration: underline;' lay-event='gotoWorkContView'>" + item.num + "</a>" + '</td>';
                    }
                    tableHtml += '</tr>';
                });
                tableHtml += '</tbody></table>';
                queryValue = 0;
                if (data.data.length != 0) {
                    // 先关闭当前已打开的弹出层
                    if (currentLayerIndex != null) {
                        layer.close(currentLayerIndex);
                    }

                    queryValue = data.data[0].total;
                    queryValueLink.innerHTML = queryValue;
                    //$("#queryValueLink").on("click",function () {
                    //layer.close(currentLayerIndex);
                    // 打开新的对话框
                    currentLayerIndex = layer.open({
                        type: 1,
                        title: '代办任务详情',
                        offset: 'auto',
                        anim: 0,
                        area: ['50%', '500px'],
                        shade: 0.1,
                        content: tableHtml,
                        success: function (layero, index) {
                            var btn = layero.find('.num_col');
                            btn.on("click", function () {
                                layer.close(currentLayerIndex);
                                var rowIndex = $(this).closest('tr').index();
                                var rowData = data.data[rowIndex];
                                var message = {
                                    title: rowData.keyName,
                                    url: getUrl(rowData.key),
                                    type: 'openTab',
                                };
                                parent.postMessage(message, '*');
                            });
                        }
                    });
                    //})
                } else {
                    queryValueLink.innerHTML = queryValue;
                }


            },
            error: function (data) {
                layer.msg(data);
            }
        });
    }

    var cols = [
        {
            field: 'cityCode',
            title: '城市名称',
            width: '130',
            align: 'center',
            templet: function (d) {
                return ML.areaFormatter(d.cityCode)
            }
        },
        {
            field: 'serviceSiteName', title: '服务网点', width: '250', align: 'center'
        },
        {
            field: 'title', title: '标题', width: '150', align: 'center', templet: function (d) {
                return '<a href="javascript:void(0);" style="color:blue;text-decoration: underline;" onclick="showContent(\'' + encodeURIComponent(d.content) + '\')">' + d.title + '</a>';
            }
        },
        {
            field: 'publishTime', title: '发布时间', width: '150', align: 'center', templet: function (d) {
                return d.publishTime.substring(0, 10);
            }
        },
    ];

    table.render({
        id: 'temporaryPolicyTable',
        elem: '#temporaryPolicyTable',
        url: ML.contextPath + '/sys/temporaryPolicy/getTemporaryPolicyListPage',
        // data: data,
        where: {"paramData": JSON.stringify({"showType": 1})},
        //支持传入 laypage 组件的所有参数（某些参数除外，如：jump/elem） - 详见文档
        // toolbar: '#topbtn',
        // defaultToolbar: [],
        height: 400,
        // where: {"param": JSON.stringify(serialize("searchForm")) },
        limit: 10,
        limits: [10, 20, 30], // 可选每次加载数据
        text: {
            none: '暂无数据' // 无数据时展示
        },
        done: function (res, curr, count) {

            //加上这个后就可以通过判断加上是否显示 auth
            ML.hideNoAuth();

        },
        cols: [cols],
        page: true
    });

    function getUrl(key) {
        let url = '';
        switch (key) {
            case '1':
                url = '/workflow/gotoTaskListPageQuotation';
                break;
            case '2':
                url = '/workflow/gotoTaskListPageContract';
                break;
            case '3':
                url = '/workflow/gotoContractRenewList';
                break;
            case '4':
				url ='/bill/insurancePracticeApproval/gotoInsurancePracticeApprovalPage';
                break;
            case '5':
                url = '/bill/payBatch/gotoSalaryPaymentApplyApprovePage';
                break;
            case '6':
                url = '/workflow/gotoTaskListPageSupplierContract';
                break;
            case '7':
                url = '/workflow/gotoTaskListPageSupplierContractRenew';
                break;
            case '8':
                url = '/workflow/gotoTaskListPageSupplierContractAgreement';
                break;
            case '9':
                url = '/workflow/gotoTaskListPageContractAssociation';
                break;
            case '10':
                url = '/customer/distribution/gotoDistributionView';
                break;
            case '11':
                url = '/customer/distribution/gotoDistributionContract';
                break;
            case '12':
                url = '/customer/personOrderEdit/gotoChangeOrderConfirmationPage';
                break;
            case '13':
                url = '/customer/personOrder/gotoCompletePersonOrderPage';
                break;
            case '14':
                url = '/customer/personOrder/gotoConfirmPersonOrderPage';
                break;
            case '15':
                url = '/bill/payBatch/gotoSalaryPaymentRefundInputQueryPage';
                break;
            case '16':
                url = '/customer/employee/gotoRevConfirmEmployeeLeavePage';
                break;
            case '17':
                url = '/customer/employee/gotoPrjConfirmEmployeeLeavePage';
                break;
            case '18':
                url = '/supplierSalaryBill/gotoSupplierSalaryBillListPage';
                break;
            case '19':
                url = '/base/message/gotoMessageQueryListPage';
                break;
            case '20':
                url = '/customer/personOrder/gotoPersonOrderListPage';
                break;
            case '21':
                url = '/customer/ehrEmployeeOrder/gotoEhrEmployeeOrderListPage';
                break;
            case '22':
                url = '/customer/ehrEmployeeOrder/gotoEhrApplyEmployeeLeavePage';
                break;
            case '23':
                url = '/bill/billDisposableQuery/gotoBillDisposableApprovalPage';
                break;
            case '24':
                url = '/insuranceBillDeal/gotoBillApprovePage';
                break;
            case '25':
                url = '/customer/contract/gotoApprovalContractInfoListPage';
                break;
            case '26':
                url = '/workflow/gotoQysTaskListPage';
                break;
            case '27':
                url = '/customer/distribution/gotoDistributionContractSalary';
                break;
            case '28':
                url = "/customer/personOrderQuery/gotoOrderPage?type=1"
                break;
            case '29':
                url = "/customer/salary/taxComparison/gotoTaxComparisonFeedbackRecordView"
                break;
            case '30':
                url = "/customer/contract/gotoContractPage?type=1"
                break;
			case '31':
				url="/bill/insurancePracticePayByCmb/gotoInsurancePracticePayByCmbPage"
				break;
			case '32':
				url="/bill/oneFee/gotoApproveDifferencesPage"
				break;
            case '40':
                url = "/customer/salary/pay/gotoIncomeTaxDifferencesView"
                break;        }
        return url;
    }

    $('#bodyTab li', window.parent.document).click(function () {
        if ($(this).text() == ' 后台首页') {
            let span = document.getElementById("queryValueLink")
            span.innerHTML = "正在重新查询,请稍等!"
            getToDoItemCount()
        }
    });


})
