package com.reon.hr.api.bill.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 实做支付备款查询
 * @Date 2025年06月18日
 * @Version 1.0
 */
@Data
public class ReserveInquiryVo implements Serializable {

    private String payCom;

    private String payDate;


    private Integer isFlag;


    //原支付金额
    private BigDecimal payAmt;

    //抵扣金额
    private BigDecimal payAmtBalance;

    private BigDecimal serviceAmt;

    //实际支付金额
    private BigDecimal actPayAmt;

    private String payComCode;

    private String disCom;

}
