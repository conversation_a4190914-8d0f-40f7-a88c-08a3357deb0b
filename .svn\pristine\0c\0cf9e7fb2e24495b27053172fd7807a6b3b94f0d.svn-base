<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>

<head>
    <title>新增关联公司</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all" />
    <style>
        .layui-input {
            padding-right: 30px !important;
        }
    </style>
</head>

<body class="childrenBody">
<div class="layui-tab-content">
    <div class="layui-tab-item layui-show" style="margin-top: 5px">
        <input type="hidden" id="editIndustryType" >
        <input type="hidden" id="editCorpKind" >
        <input type="hidden" id="editCityCode" >
        <form class="layui-form" method="post">
            <input type="hidden" id="type" name="type" value="${type}" >
            <input type="hidden" id="custId" name = "custId" value="${customerVo.id}">
            <input type="hidden" name="custNo" value="${customerVo.custNo}">
            <input type="hidden" name="relCustId" value="${relCustId}">
            <input type="hidden" name="id" id="id" value="${id}">
            <input type="hidden" name="fileId" id="fileId" value="${fileId}">
            <div class="layui-inline queryTable" style="margin-bottom: 30px">
                <div class="layui-input-inline layui-hide isHide">
                    <div class="layui-input-inline" style="margin: 10px">
                        <label class="layui-form-label" title="客户名称" style="font-weight:800">客户名称:</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" name="custName" readonly="true" id="custName" value="${customerVo.custName}">
                        </div>
                    </div>
                    <div class="layui-input-inline" style="margin: 10px">
                        <label class="layui-form-label" title="关联公司" style="font-weight:800">关联公司:</label>
                        <div class="layui-input-inline">
                            <select id="refCustId" name="refCustId" lay-verify="required" lay-filter="refCustId" lay-verType="tips" lay-search>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-input-inline" style="margin: 10px">
                    <label class="layui-form-label" title="附件" style="font-weight:800">附件:</label>
                    <div class="layui-upload" style=" margin-left: 40px; margin-right: 40px;">
                        <button type="button" id="customerUpload" class="layui-btn layui-btn-normal">选择文件</button>
                        <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                            附件：
                            <div class="layui-upload-list" id="upload"></div>
                        </blockquote>
                    </div>
                </div>
            </div>
            <div style="float: right; margin-right: 40%;">
                <button class="layui-btn" lay-submit lay-filter="save" id="save" type="button" authURI="/customer/customer/save">提交</button>
                <button class="layui-btn" type="button" id="cancelBtn">取消</button>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/customer/relCustomer.js?v=${publishVersion}"></script>
</body>

</html>