package com.reon.hr.sp.bill.service.impl.salary;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.reon.hr.api.bill.enums.PaymentApplyDocumentStatusEnum;
import com.reon.hr.api.bill.exception.BillCheckException;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.bill.vo.salary.RecBankRelativeVo;
import com.reon.hr.api.bill.vo.salary.SalaryPayBatchVo;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.vo.salary.pay.SalaryPayVo;
import com.reon.hr.api.workflow.dubbo.service.rpc.IWorkflowWrapperService;
import com.reon.hr.api.workflow.vo.ActRuTaskVo;
import com.reon.hr.sp.bill.dao.bill.BillCheckMapper;
import com.reon.hr.sp.bill.dao.bill.InsuranceBillMapper;
import com.reon.hr.sp.bill.dao.bill.RecBankRelativeMapper;
import com.reon.hr.sp.bill.dao.salary.SalaryPayBatchMapper;
import com.reon.hr.sp.bill.entity.bill.InsuranceBill;
import com.reon.hr.sp.bill.entity.bill.RecBankRelative;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import com.reon.hr.sp.bill.service.bill.salary.IRecBankRelativeService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RecBankRelativeServiceImpl implements IRecBankRelativeService {
    @Autowired
    private RecBankRelativeMapper recBankRelativeMapper;
    @Autowired
    private SalaryPayBatchMapper salaryPayBatchMapper;
    @Autowired
    private InsuranceBillMapper insuranceBillMapper;
    @Autowired
    private BillCheckMapper billCheckMapper;
    @Autowired
    private ISalaryPayWrapperService iSalaryPayWrapperService;
    @Autowired
    private IPaymentApplyService paymentApplyService;
    @Autowired
    private IWorkflowWrapperService workflowWrapperService;
    @Override
    public List<RecBankRelativeVo> getVoListByRecIdList(List<Long> recIdList) {
        return recBankRelativeMapper.getVoListByRecIdList(recIdList);
    }

    @Override
    public int insertRecBankRelative(RecBankRelativeVo bankRelative) {
        RecBankRelative relative = new RecBankRelative();
        BeanUtils.copyProperties(bankRelative,relative);
        return recBankRelativeMapper.insertRecBankRelative(relative);
    }

    @Override
    public void updateAndCheck(List<Long> checkIds,String updater) {
        List<BillCheckVo> payCustByCheckIdList = billCheckMapper.getPayCustByCheckId(checkIds);
        List<Long> payCustIdList = payCustByCheckIdList.stream().map(BillCheckVo::getPayCustId).distinct().collect(Collectors.toList());
        List<Long> notPayIdList=salaryPayBatchMapper.getPayIdByPayCustIdList(payCustIdList);
        List<Long> batchIdList=salaryPayBatchMapper.getBatchIdByCheckIdList(checkIds);
        PaymentApplyVo paymentApplyVo=new PaymentApplyVo();
        paymentApplyVo.setBillCheckIdList(checkIds);
        paymentApplyVo.setDocumentStatus(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode());
        paymentApplyVo.setMatchFlag(true);
        paymentApplyVo.setUpdater(updater);
        List<PaymentApplyVo> paymentApplyList = paymentApplyService.getPaymentApplyList(paymentApplyVo);
        if(CollectionUtils.isNotEmpty(batchIdList)||CollectionUtils.isNotEmpty(notPayIdList)){
            throw new BillCheckException("所选数据存在工资支付，且已被财务处理，不能废除核销！");
        }else if(CollectionUtils.isNotEmpty(paymentApplyList)){
            List<ActRuTaskVo> currentApproverList = workflowWrapperService.getCurrentApproverList(paymentApplyList.stream().map(PaymentApplyVo::getPid).collect(Collectors.toList()));
            List<ActRuTaskVo> actRuTaskVoList = currentApproverList.stream().filter(a -> a.getName().equals("财务制单") || a.getName().equals("财务复核")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(actRuTaskVoList)){
                throw new BillCheckException("所选数据存在工资支付，且在财务处理界面，不能废除核销！若想废除核销，需要财务驳回支付");
            }else {
                salaryPayBatchMapper.updateBillCheckIdByBillCheckIdList(checkIds,updater);
            }
        }else {
            salaryPayBatchMapper.updateBillCheckIdByBillCheckIdList(checkIds,updater);
        }
    }

    /**
     * 返回能废除的billId
     * @param checkId
     * @param billIds
     * @param updater
     * @return
     */
    @Override
    public List<Long> updateAndCheck(Long checkId, List<Long> billIds, String updater) {
        EntityWrapper<InsuranceBill> wrapper = new EntityWrapper<>();
        wrapper.in("id",billIds);
        List<InsuranceBill> insuranceBillList = insuranceBillMapper.selectList(wrapper);
        List<SalaryPayVo> salaryPayQueryVoList=new ArrayList<>();
        for (InsuranceBill insuranceBill:insuranceBillList) {
            SalaryPayVo salaryPayVo = new SalaryPayVo();
            BeanUtils.copyProperties(insuranceBill,salaryPayVo);
            salaryPayQueryVoList.add(salaryPayVo);
        }
        if(CollectionUtils.isNotEmpty(salaryPayQueryVoList)){
            Map<String, Long> billIdMap = insuranceBillList.stream().collect(Collectors.toMap(v -> v.getContractNo() + "-" + v.getBillMonth() + "-" + v.getTempletId(), InsuranceBill::getId, (v1, v2) -> v1));
            List<SalaryPayVo> salaryPayVoList=iSalaryPayWrapperService.getPayIdListByList(salaryPayQueryVoList);
            Map<Long,String > payIdMap = salaryPayVoList.stream().collect(Collectors.toMap(SalaryPayVo::getId,v ->v.getContractNo() + "-" + v.getBillMonth() + "-" + v.getTempletId(), (v1, v2) -> v1));
            List<Long> payIdQueryList = new ArrayList<>(payIdMap.keySet());
            //存在工资支付，且已被财务处理，不能废除核销的payId
            if(CollectionUtils.isNotEmpty(payIdQueryList)){
                List<Long> billCheckIdList = Collections.singletonList(checkId);
                List<BillCheckVo> payCustByCheckIdList = billCheckMapper.getPayCustByCheckId(billCheckIdList);
                List<Long> payCustIdList = payCustByCheckIdList.stream().map(BillCheckVo::getPayCustId).distinct().collect(Collectors.toList());
                List<Long> notPayIdList=salaryPayBatchMapper.getPayIdByPayCustIdList(payCustIdList);
                notPayIdList.addAll(salaryPayBatchMapper.getPayIdByPayIdList(payIdQueryList));
                PaymentApplyVo paymentApplyVo=new PaymentApplyVo();
                paymentApplyVo.setBillCheckIdList(billCheckIdList);
                paymentApplyVo.setDocumentStatus(PaymentApplyDocumentStatusEnum.UNISSUED_DOCUMENT.getCode());
                paymentApplyVo.setMatchFlag(true);
                paymentApplyVo.setUpdater(updater);
                List<PaymentApplyVo> paymentApplyList = paymentApplyService.getPaymentApplyList(paymentApplyVo);
                paymentApplyList.removeIf(p-> p.getSalaryPayBatchVoList().stream().noneMatch(v->payIdQueryList.contains(v.getPayId())&&v.getBillCheckId().contains(checkId.toString())));
                if(CollectionUtils.isNotEmpty(paymentApplyList)){
                    //存在工资支付，且在财务处理界面，不能废除核销！若想废除核销，需要财务驳回支付
                    List<ActRuTaskVo> currentApproverList = workflowWrapperService.getCurrentApproverList(paymentApplyList.stream().map(PaymentApplyVo::getPid).collect(Collectors.toList()));
                    List<ActRuTaskVo> actRuTaskVoList = currentApproverList.stream().filter(a -> a.getName().equals("财务制单") || a.getName().equals("财务复核")).collect(Collectors.toList());
                    List<String> notPidList = actRuTaskVoList.stream().map(ActRuTaskVo::getPid).collect(Collectors.toList());
                    //不能废除核销的payId
                    List<Long> notPayIdListByPid = paymentApplyList.stream().filter(p -> notPidList.contains(p.getPid())).flatMap(p -> p.getSalaryPayBatchVoList().stream()).map(SalaryPayBatchVo::getPayId).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(notPayIdListByPid)){
                        notPayIdList.addAll(notPayIdListByPid);
                    }
                }
                for (Long payId:payIdMap.keySet()) {
                    String key = payIdMap.get(payId);
                    if (billIdMap.containsKey(key)) {
                        Long billId = billIdMap.get(key);
                        //不能废除的
                        if(notPayIdList.contains(payId)){
                            billIds.removeIf(b->b.equals(billId));
                            payIdQueryList.removeIf(p->p.equals(payId));
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(payIdQueryList)){
                    salaryPayBatchMapper.updateBillCheckIdByBillCheckAndPayIdList(checkId,payIdQueryList,updater);
                }
            }
        }

        return billIds;
    }
}
