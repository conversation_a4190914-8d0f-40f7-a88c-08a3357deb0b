var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'table', 'tableSelectWithoutSearch'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        tableSelectWithoutSearch = layui.tableSelectWithoutSearch;
    layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;

    table.render({
        id: 'companyAddrListGrid',
        elem: '#companyAddrListTable',
        // data:[],
        url: ML.contextPath + '/companyAddrList/getCompanyAddrListPage',
        method: 'get',
        page: true, //默认不开启
        limits: [50, 100, 200],
        defaultToolbar: [],
        height: '600',
        toolbar: '#toolbarDemo',
        limit: 50,
        text: {
            none: '暂无数据'
        },
        cols: [[
            {type: 'checkbox', width: '3%', fixed: 'left'},
            {field: 'comName', title: '公司名称', width: '20%', align: 'center'},
            {field: 'comTel', title: '公司电话', align: 'center', width: '20%'},
            {field: 'comAddr', title: '公司地址', width: '20%', align: 'center'},
            {field: 'taxerIdentNo', title: '纳税人识别号', width: '20%', align: 'center'},
            {field: 'postcode', title: '邮编', width: '20%', align: 'center'},
            {field: 'faxNumber', title: '传真', width: '20%', align: 'center'},
            {field: 'legalRep', title: '法人', width: '20%', align: 'center'},
            {
                field: '', title: '公司客服', width: '20%', align: 'center', templet: function () {
                    return "<button class='layui-btn-xs layui-btn-radius layui-btn-primary' lay-event='queryCommissioner'>查看</button>"
                }
            },
        ]],
        done: function (res) {
            ML.hideNoAuth();
        },
    });

    table.on('toolbar(companyAddrListFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'add':
                open("新增", "add", ['65%', '80%'], checkStatus);
                break;
            case 'edit':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选择一行!");
                }
                open("修改", "edit", ['65%', '80%'], checkStatus);
                break;
            case 'check':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选择一行!");
                }
                open("查看", "check", ['65%', '80%'], checkStatus);
                break;
            case 'downLoad':
                if (checkStatus.data.length != 1) {
                    return layer.msg("请选择一行!");
                }
                downLoad(checkStatus.data[0].id);
                break;
            case 'export':
                var url = ML.contextPath + "/companyAddrList/exportCompanyAddrList?1=1";
                var paramData = JSON.stringify(serialize("searchForm"));
                if (paramData != null && paramData !== '') {
                    url += "&paramData=" + paramData
                }
                window.open(url);
                break;
        }
    });

    function open(title, optType, area, checkStatus) {
        // var checkStatus = table.checkStatus(obj.config.id);
        var url;
        if (optType == 'add') {
            url = "/companyAddrList/gotoCompanyAddrListAdd"
        }
        if (optType == 'edit') {
            url = "/companyAddrList/gotoCompanyAddrListEdit"
        }
        if (optType == 'check') {
            url = "/companyAddrList/gotoCompanyAddrListCheck"
        }
        layer.open({
            type: 2, //1 直接弹出content内容 2发送请求
            title: title,
            area: area,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                var body = layer.getChildFrame('body', index);
                if (optType == 'edit' || optType == 'check') {
                    body.find("#comName").val(checkStatus.data[0].comName);
                    body.find("#comCode").val(checkStatus.data[0].comCode);
                    body.find("#comTel").val(checkStatus.data[0].comTel);
                    body.find("#comAddr").val(checkStatus.data[0].comAddr);
                    body.find("#comEmail").val(checkStatus.data[0].comEmail);

                    body.find("#taxerIdentNo").val(checkStatus.data[0].taxerIdentNo);
                    body.find("#postcode").val(checkStatus.data[0].postcode);
                    body.find("#faxNumber").val(checkStatus.data[0].faxNumber);
                    body.find("#legalRep").val(checkStatus.data[0].legalRep);
                    body.find("#fileIdCache").val(checkStatus.data[0].fileId);
                    body.find("#id").val(checkStatus.data[0].id);
                }
            },
            end: function () {
                reloadTable()
            }
        })
    }

    function reloadTable() {
        table.reload('companyAddrListGrid', {
            where: {
                paramData: JSON.stringify(serialize("searchForm"))
            },
            curr: 1
        })
    }

    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('companyAddrListGrid', {
            where: {paramData: JSON.stringify(data.field)},
            curr: 1
        })
    });

    var appd3 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelectWithoutSearch.render({
        delFlg: ["comName", "comCode"],
        elem: '#comName',
        checkedKey: 'orgCode',
        appd: appd3,
        table: {
            url: ctx + '/sys/org/getCompanyByName',
            cols: [[
                {type: 'radio'},
                {type: 'numbers', title: '序号'},
                {field: 'orgName', title: '分公司名称'}
            ]]
        },
        done: function (elem, data) {
            if (data.data.length > 0) {
                $('#comName').val(data.data[0].orgName);
                $('#comCode').val(data.data[0].orgCode);
            }
        }
    });

// 双击查看
    table.on('rowDouble(companyAddrListFilter)', function (obj) {
        if ($("#optType").val() != "noButton") {
            var checkStatus = obj;
            check(checkStatus);
        }
    });

    function check(checkStatus) {
        layer.open({
            type: 2,
            title: '查看',
            area: ['65%', '80%'],
            maxmin: true,
            offset: 'auto',
            shade: ['0.8', '#393D49'],
            content: ML.contextPath + '/companyAddrList/gotoCompanyAddrListCheck',
            success: function (layero, index) {
                let body = layer.getChildFrame("body", index);
                // var parentIndex = layer.getChildFrame(window.name);
                body.find("#comName").val(checkStatus.data.comName);
                body.find("#comCode").val(checkStatus.data.comCode);
                body.find("#comTel").val(checkStatus.data.comTel);
                body.find("#comAddr").val(checkStatus.data.comAddr);
                body.find("#comEmail").val(checkStatus.data.comEmail);
                body.find("#fileIdCache").val(checkStatus.data.fileId);

                body.find("#taxerIdentNo").val(checkStatus.data[0].taxerIdentNo);
                body.find("#postcode").val(checkStatus.data[0].postcode);
                body.find("#faxNumber").val(checkStatus.data[0].faxNumber);
                body.find("#legalRep").val(checkStatus.data[0].legalRep);
            }
        })
    }

    //列表按钮点击事件
    table.on('tool(companyAddrListFilter)', function (obj) {
        var trDom = obj.tr;
        switch (obj.event) {
            case 'queryCommissioner':
                if (obj.data.comCode) {
                    var comCode = obj.data.comCode;
                    layer.open({
                        type: 2,
                        title: "查看项目客服",
                        area: ['80%', '70%'],
                        shade: [0.8, '#393D49'],
                        maxmin: true,
                        offset: 'auto',
                        content: ML.contextPath + "/companyAddrList/gotoCheckCommission",
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            body.find("#comCode").val(comCode);
                        }
                    })
                } else {
                    return layer.msg("暂时无法查看!");
                }
                break;
        }
    });

    function downLoad(id) {
        if (id) {
            window.location.href = ML.contextPath + "/companyAddrList/downLoad?companyAddrListId=" + id;
        }
    }

    $("button").click(function () {

    });
});