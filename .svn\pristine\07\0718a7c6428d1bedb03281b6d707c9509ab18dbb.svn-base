package com.reon.hr.sp.bill.service.impl.insurancePractice;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IAreaResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsurancePackResourceWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IInsuranceRatioWrapperService;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IinsuranceGroupWrapperService;
import com.reon.hr.api.base.enums.InsuranceRatioChargeFreq;
import com.reon.hr.api.base.enums.InsuranceRatioEnum;
import com.reon.hr.api.base.vo.AreaVo;
import com.reon.hr.api.base.vo.InsurancePackVo;
import com.reon.hr.api.base.vo.InsuranceRatioVo;

import com.reon.hr.api.bill.dto.GeneratePracticeLockInfoDto;
import com.reon.hr.api.bill.dto.SearchInsuranceLockDto;
import com.reon.hr.api.bill.dto.SearchReportDto;
import com.reon.hr.api.bill.enums.*;
import com.reon.hr.api.bill.utils.BigDecimalUtil;
import com.reon.hr.api.bill.utils.DateUtil;
import com.reon.hr.api.bill.utils.ListPageUtil;
import com.reon.hr.api.bill.utils.QueryUtil;
import com.reon.hr.api.bill.vo.PaymentApplyVo;
import com.reon.hr.api.bill.vo.PracPaymentApplyVo;
import com.reon.hr.api.bill.vo.bill.PerPracticeBillVo;
import com.reon.hr.api.bill.vo.insurancePractice.*;
import com.reon.hr.api.customer.dto.insurancePractice.ReportPullInsurancePracticeDto;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAreaResourceWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IEmpInsurAcctWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.employee.IEmployeeOrderWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IInsurancePracticeWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IPracticeSuppliPayWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.PracSingleAccountWrapperService;
import com.reon.hr.api.customer.enums.CertType;
import com.reon.hr.api.customer.enums.DimissionReasonTypeEnum;
import com.reon.hr.api.customer.enums.EmployeeReportEnum;
import com.reon.hr.api.customer.enums.insurancePractice.InsurancePracticeAddMethodEnum;
import com.reon.hr.api.customer.enums.insurancePractice.InsurancePracticeEnum;
import com.reon.hr.api.customer.enums.insurancePractice.SingleFlagEnum;
import com.reon.hr.api.customer.enums.insurancePractice.SuppliPayTypeEnum;
import com.reon.hr.api.customer.utils.RetirementAgeUtil;
import com.reon.hr.api.customer.utils.StringUtil;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.api.customer.vo.doIt.EmpInsurAcctVO;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.insurancePractice.*;
import com.reon.hr.api.dubbo.service.rpc.sys.IOrgnizationResourceWrapperService;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserWrapperService;
import com.reon.hr.api.enums.ResultEnum;
import com.reon.hr.api.report.enums.IncomeCountTableReportEnum;
import com.reon.hr.api.util.JsonUtil;
import com.reon.hr.api.util.UUIDUtil;
import com.reon.hr.api.vo.LayuiReplay;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.api.vo.sys.OrgVo;
import com.reon.hr.common.bill.strategy.ChargeFreqStrategy;
import com.reon.hr.common.bill.strategy.IChargeFreqStrategyFactory;
import com.reon.hr.common.bill.strategy.concrete.ReceiveInfo;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.ChargeFreqInfo;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.ChargeFreqStrategyFactory;
import com.reon.hr.common.bill.strategy.concrete.chargefreq.vo.ProdAmtVo;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.CertUtils;
import com.reon.hr.common.utils.ReceiveMonthHelper;
import com.reon.hr.common.utils.VoUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ProducerScopeTypeBill;
import com.reon.hr.sp.bill.dao.insurancePractice.*;
import com.reon.hr.sp.bill.entity.bill.PaymentApply;
import com.reon.hr.sp.bill.entity.bill.PerPracticeBill;
import com.reon.hr.sp.bill.entity.insurancePractice.*;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.sp.bill.service.bill.insurancePractice.*;
import com.reon.hr.sp.bill.service.bill.paymentApply.IPaymentApplyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reon.hr.api.customer.utils.DateUtil.getPrevMonthDate;
import static com.reon.hr.common.utils.DateUtil.getMonthBetween;

@Service
public class InsurancePracticeBillServiceImpl implements IInsurancePracticeBillService {

    private static Logger logger = LoggerFactory.getLogger(InsurancePracticeBillServiceImpl.class);

    @Autowired
    private IinsuranceGroupWrapperService insuranceGroupWrapperService;
    @Autowired
    private IOrgnizationResourceWrapperService orgnizationResourceWrapperService;
    @Autowired
    private ICustomerWrapperService customerWrapperService;
    @Autowired
    private IInsurancePracticeWrapperService iInsurancePracticeWrapperService;
    @Autowired
    private IInsuranceRatioWrapperService insuranceRatioWrapperService;
    @Autowired
    private IPracticeSuppliPayWrapperService practiceSuppliPayWrapperService;
    @Autowired
    private MqMessageSender mqMessageSender;
    @Autowired
    private PracticeBillMapper practiceBillMapper;
    @Autowired
    private InsurancePracticeReportMapper insurancePracticeReportMapper;
    @Autowired
    private IEmpInsurAcctWrapperService empInsurAcctWrapperService;
    @Autowired
    private IUserWrapperService userWrapperService;

    @Resource
    private IEmployeeOrderWrapperService employeeOrderWrapperService;

    @Resource
    private PracticePayDetailSuccessMapper practicePayDetailSuccessMapper;
    @Resource
    private IInsurancePackResourceWrapperService insurancePackResourceWrapperService;

    @Resource
    private IPerPracticeBillService perPracticeBillService;

    @Resource
    private PracticeReportToPayMapper practiceReportToPayMapper;

    @Resource
    private IPracticePayDetailService practicePayDetailService;

    @Resource
    private IPaymentApplyService paymentApplyService;

    @Resource
    private RedisTemplate stringRedisTemplate;

    @Autowired
    private IndividualFeeLockService individualFeeLockService;

    @Autowired
    private PackAmtDiffMonthlyService packAmtDiffMonthlyService;

    @Autowired
    private PayFailedLogMapper payFailedLogMapper;
    @Resource
    private IAreaResourceWrapperService iAreaResourceWrapperService;

    @Autowired
    private PracSingleAccountWrapperService pracSingleAccountWrapperService;

    @Resource
    private IContractAreaResourceWrapperService contractAreaResourceWrapperService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;







    @Override
    public Page<PracticeLockInfoVo> searchInsuranceLock(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("查询社保公积金实做锁定:{}", searchInsuranceLockDto);
        Page<PracticeLockInfoVo> practiceLockInfoVoPage = new Page<>(searchInsuranceLockDto.getPage(), searchInsuranceLockDto.getLimit());
        List<PracticeLockInfoVo> practiceLockInfoVoList = practiceBillMapper.searchInsuranceLock(searchInsuranceLockDto);
        if (CollectionUtils.isNotEmpty(practiceLockInfoVoList)) {
            String sinAccName = searchInsuranceLockDto.getSinAccName();
            packPracticeLockInfoVo(practiceLockInfoVoList);
            if(StringUtils.isNotBlank(sinAccName)){
                practiceLockInfoVoList = practiceLockInfoVoList.stream().filter(item -> sinAccName.equals(item.getSinAccName())).collect(Collectors.toList());
            }
        }
        ListPageUtil<PracticeLockInfoVo> pageUtil = new ListPageUtil<>(practiceLockInfoVoList,searchInsuranceLockDto.getLimit());
        List<PracticeLockInfoVo> pagedList = pageUtil.getPagedList(searchInsuranceLockDto.getPage());
        practiceLockInfoVoPage.setRecords(pagedList);
        practiceLockInfoVoPage.setTotal(practiceLockInfoVoList.size());
        return practiceLockInfoVoPage;
    }

    @Override
    public PracticeLockInfoVo getById(Integer id) {
        return practiceBillMapper.getById(id);
    }

    @Override
    public List<PracticeLockInfoVo> getByIds(List<Integer> id) {
        return practiceBillMapper.getByIds(id);
    }

    @Override
    public List<PracticeLockInfoVo> getByGenerateParam(GeneratePracticeLockInfoDto generatePracticeLockInfoDto) {
        logger.info("根据生成报表参数查询实做锁定:{}", generatePracticeLockInfoDto);
        return practiceBillMapper.getByGenerateParam(generatePracticeLockInfoDto);
    }

    @Override
    public Map<Long, Set<Integer>> isLockByPracticeId(GeneratePracticeLockInfoDto generatePracticeLockInfoDto) {
        Map<Long, Set<Integer>> result = new HashMap<>();
        Integer lockMonth = generatePracticeLockInfoDto.getLockMonth();
        List<Long> practiceIds = generatePracticeLockInfoDto.getPracticeIdList();
        String endMonth = getPrevMonthDate(IntToDate(lockMonth), -5).replace("-", "");
        List<Integer> monthBetween = getMonthBetween(String.valueOf(lockMonth), endMonth);
        List<PracticeLockInfoVo> practiceLockInfoVos = practiceBillMapper.getBillByParams(generatePracticeLockInfoDto, monthBetween);
        List<PracticeLockInfoVo> isLockReports = practiceLockInfoVos.stream().filter(vo -> vo.getLockStatus().equals(PracticeBillLockStatusEnum.LOCK.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(isLockReports)) {
            List<Integer> lockMonths = isLockReports.stream().map(PracticeLockInfoVo::getLockMonth).distinct().collect(Collectors.toList());
            List<InsurancePracticeReportVo> practiceReportVos = insurancePracticeReportMapper.getInsurancePracticeReportBypracticeId(practiceIds, lockMonths);
            result = practiceReportVos.stream().collect(
                    Collectors.groupingBy(InsurancePracticeReportVo::getPracticeId, Collectors.mapping(InsurancePracticeReportVo::getReportMonth, Collectors.toSet())));
        }
        return result;
    }

    /**
     * 生成报表锁定
     *
     * @param generatePracticeLockInfoDto
     * @return
     */
    @Override
    public List<PracticeLockInfoVo> generateReportLock(GeneratePracticeLockInfoDto generatePracticeLockInfoDto) {
        logger.info("生成实做报表锁定:{}", generatePracticeLockInfoDto);
        List<PracticeLockInfoVo> practiceLockInfoVos = practiceBillMapper.getByGenerateParam(generatePracticeLockInfoDto);
        // 第一次生成插入新数据并生成数据,非第一次生成修改数据,删除报表数据重新生成
        boolean firstLock = CollectionUtils.isEmpty(practiceLockInfoVos);
        final int freeTypeSize = 2;
        Date now = new Date();
        if (firstLock) {
            for (int i = 0; i < freeTypeSize; i++) {
                PracticeLockInfoVo practiceLockInfoVo = new PracticeLockInfoVo();
                practiceLockInfoVo.setPayDetailType(getBillType(i));
                BeanUtils.copyProperties(generatePracticeLockInfoDto, practiceLockInfoVo);
                practiceLockInfoVo.init(generatePracticeLockInfoDto.getOperator(), now);
                practiceLockInfoVo.setOrgCode(generatePracticeLockInfoDto.getOrgCode());
                practiceLockInfoVos.add(practiceLockInfoVo);
            }
        }

        for (PracticeLockInfoVo practiceLockInfoVo : practiceLockInfoVos) {
            practiceLockInfoVo.setGenStatus(PracticeLockInfoGenStatusEnum.IN_GENERATED.getCode());
            practiceLockInfoVo.setUpdater(generatePracticeLockInfoDto.getOperator());
            practiceLockInfoVo.setUpdateTime(now);
            practiceLockInfoVo.setNormalAmt(BigDecimal.ZERO);
            practiceLockInfoVo.setAddAmt(BigDecimal.ZERO);
            practiceLockInfoVo.setPayAmt(BigDecimal.ZERO);
        }


        if (firstLock) {
            practiceBillMapper.insertList(practiceLockInfoVos);
        } else {
            practiceBillMapper.updateAgainGenerateDataByIds(practiceLockInfoVos);
            Integer lockMonth = generatePracticeLockInfoDto.getLockMonth();
            String packCode = generatePracticeLockInfoDto.getPackCode();
            SearchInsuranceLockDto infoDto = new SearchInsuranceLockDto();
            infoDto.setLockMonth(lockMonth);
            infoDto.setPackCodes(Lists.newArrayList(packCode));
            infoDto.setPayDetailType(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode());
            List<IndividualFeeLockVo> individualFeeLockVos = individualFeeLockService.getIndividualFeeLocksByCondition(infoDto);
            List<Long> lockPracticeIds = individualFeeLockVos.stream().map(IndividualFeeLockVo::getPracticeId).collect(Collectors.toList());
            SearchInsuranceLockDto lockDto = new SearchInsuranceLockDto();
            lockDto.setLockMonth(lockMonth);
            lockDto.setPackCodes(Lists.newArrayList(packCode));
            List<InsurancePracticeReportVo> perReportVos = insurancePracticeReportMapper.getInsurancePracticeReportByCondition(lockDto);
            if(CollectionUtils.isNotEmpty(perReportVos)){
                List<Long> practiceIds = perReportVos.stream().map(InsurancePracticeReportVo::getPracticeId).distinct().collect(Collectors.toList());
                List<PerPracticeBill> perPracticeBills= perPracticeBillService.queryPracticeBillByFirstMonth(lockMonth);
                /**删除上次生成的首版数据，不能删除个人汇缴被锁定的*/
                List<Long> ids = perPracticeBills.stream().filter(vo -> practiceIds.contains(vo.getPracticeId()) && !lockPracticeIds.contains(vo.getPracticeId())).map(PerPracticeBill::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(ids)){
                    perPracticeBillService.deletePerPracticeBillByIds(ids);
                }
            }
            insurancePracticeReportMapper.deleteByOrgCodeAndPackCode(practiceLockInfoVos.get(0));
        }
        String ids = practiceLockInfoVos.stream().map(PracticeLockInfoVo::getId).map(String::valueOf).collect(Collectors.joining("_"));
        mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeBill.REON_INSURANCE_PRACTICE_LOCK_GENERATE_COMPLETED, JsonUtil.beanToJson(ids));
        return practiceLockInfoVos;
    }

    /**
     * 生成报表锁定
     *
     * @param ids
     */
    @Override
    public void saveLockInsurancePracticeReport(List<Integer> ids) {
        List<PracticeLockInfoVo> practiceLockInfoVo = practiceBillMapper.getByIds(ids);
        boolean anyMatch = practiceLockInfoVo.stream().anyMatch(vo -> vo.getLockStatus().equals(PracticeBillLockStatusEnum.LOCK.getCode()));
        if(anyMatch){
            logger.error("==============报表已经锁定不能生成,{}==========>",ids.toString());
            return;
        }
        List<InsurancePracticeReport> insurancePracticeReports = Lists.newArrayList();
        ReportPullInsurancePracticeDto reportPullInsurancePracticeDto = new ReportPullInsurancePracticeDto();
        BeanUtils.copyProperties(practiceLockInfoVo.get(0), reportPullInsurancePracticeDto);
        /**将单独锁定的数据查询出来
         * 删除大病的首版
         * */
        SearchInsuranceLockDto infoDto = new SearchInsuranceLockDto();
        infoDto.setPackCodes(Lists.newArrayList(reportPullInsurancePracticeDto.getPackCode()));
        infoDto.setLockMonth(reportPullInsurancePracticeDto.getLockMonth());

        List<IndividualFeeLockVo> individualFeeLockVos = individualFeeLockService.getIndividualFeeLocksByCondition(infoDto);
        Map<Boolean, List<IndividualFeeLockVo>> booleanListMap = individualFeeLockVos.stream().collect(Collectors
                .partitioningBy(vo -> vo.getFeeType().equals(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode().intValue())));



        List<IndividualFeeLockVo> narmalList = booleanListMap.getOrDefault(true, Lists.newArrayList());
        List<IndividualFeeLockVo> suppliPayList = booleanListMap.getOrDefault(false, Lists.newArrayList());
        Map<Long, List<IndividualFeeLockVo>> narmalMap = narmalList.stream().collect(Collectors.groupingBy(IndividualFeeLockVo::getPracticeId));
        Map<Long, List<IndividualFeeLockVo>> suppliPayMap = suppliPayList.stream().collect(Collectors.groupingBy(IndividualFeeLockVo::getPracticeId));
        //拉取汇缴数据
        List<InsurancePracticeReport> collectPayReport = pullCollectPayReport(reportPullInsurancePracticeDto, practiceLockInfoVo.get(0).getLockMonth(),narmalMap);
        BigDecimal normalAmt = BigDecimal.ZERO,addAmt = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(narmalList)){
            normalAmt = narmalList.stream().map(IndividualFeeLockVo::getTotalAmt).reduce(BigDecimal::add).get();
        }
        if(CollectionUtils.isNotEmpty(suppliPayList)){
            addAmt = suppliPayList.stream().map(IndividualFeeLockVo::getTotalAmt).reduce(BigDecimal::add).get();
        }
        //拉取补缴数据
        List<InsurancePracticeReport> suppliPayReport = pullSuppliPayReport(reportPullInsurancePracticeDto,suppliPayMap);
        if (CollectionUtils.isNotEmpty(collectPayReport)) {
            insurancePracticeReports.addAll(collectPayReport);
            BigDecimal subNormalAmt = collectPayReport.stream().map(InsurancePracticeReport::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            normalAmt = normalAmt.add(subNormalAmt);
        }
        if (CollectionUtils.isNotEmpty(suppliPayReport)) {
            insurancePracticeReports.addAll(suppliPayReport);
            BigDecimal subAddAmt = suppliPayReport.stream().map(InsurancePracticeReport::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            addAmt = addAmt.add(subAddAmt);
        }
        for (PracticeLockInfoVo practiceLockInfo : practiceLockInfoVo) {
            if ((byte) practiceLockInfo.getPayDetailType().intValue() == PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode()) {
                practiceLockInfo.setPayAmt(normalAmt);
                initBillAmount(practiceLockInfo, normalAmt, BigDecimal.ZERO);
            } else {
                practiceLockInfo.setPayAmt(addAmt);
                initBillAmount(practiceLockInfo, BigDecimal.ZERO, addAmt);
            }
        }
        if (CollectionUtils.isNotEmpty(insurancePracticeReports)) {
            insurancePracticeReportMapper.insertList(insurancePracticeReports, practiceLockInfoVo.get(0).getLockMonth());
        }
        practiceBillMapper.updateGenerateEndByList(practiceLockInfoVo);
    }

    /**
     * 生成失败更新报表锁定状态为生成失败
     *
     * @param id
     */
    @Override
    public void saveLockInsurancePracticeReportFail(List<Integer> id) {
        List<PracticeLockInfoVo> practiceLockInfoVo = practiceBillMapper.getByIds(id);
        practiceLockInfoVo.forEach(vo -> vo.setGenStatus(PracticeLockInfoGenStatusEnum.FAIL.getCode()));
        practiceBillMapper.updateGenerateEndByList(practiceLockInfoVo);
    }

    /**
     * 拉取汇缴数据并转换为报表数据
     *
     * @param reportPullInsurancePracticeDto
     * @return
     */
    private List<InsurancePracticeReport> pullCollectPayReport(ReportPullInsurancePracticeDto reportPullInsurancePracticeDto, Integer lockMonth,Map<Long, List<IndividualFeeLockVo>> narmalMap) {
        logger.info("拉取实做汇缴数据:{}", reportPullInsurancePracticeDto);
        List<PracticeCollectPayVo> practiceCollectPayVoList = iInsurancePracticeWrapperService.reportPullCollectPayData(reportPullInsurancePracticeDto);
        /***如果产品有多个段只收取当前时间在的段*/
        Map<String, List<PracticeCollectPayVo>> practiceCollectPay = practiceCollectPayVoList.stream().filter(vo ->!narmalMap.containsKey(vo.getPracticeId())).collect(Collectors.groupingBy(vo -> vo.getPracticeId() + "-" + vo.getProdCode()));
        List<PracticeCollectPayVo> collPracticeList = practiceCollectPay.values().stream().flatMap(list -> {
            list = list.stream().filter(practiceCollectPayVo -> {
                Integer startMonth = practiceCollectPayVo.getStartMonth();
                Integer endMonth = practiceCollectPayVo.getEndMonth();
                return startMonth <= lockMonth && (endMonth == null || lockMonth <= endMonth);
            }).collect(Collectors.toList());
            if (list.size() > 1) {
                throw new RuntimeException(list.get(0).toString() + "产品同时存在两个有效数据");
            }
            return list.stream();
        }).collect(Collectors.toList());
        return buildCollectReportVo(reportPullInsurancePracticeDto.getUpdater(), lockMonth, collPracticeList, "add");
    }

    private List<InsurancePracticeReport> buildCollectReportVo(String updater, Integer lockMonth, List<PracticeCollectPayVo> practiceCollectPayVoList, String type) {
        List<InsurancePracticeReport> insurancePracticeReports = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(practiceCollectPayVoList)) {
            List<String> ratioCodeList = practiceCollectPayVoList.stream().map(PracticeCollectPayVo::getRatioCode).distinct().collect(Collectors.toList());
            List<InsuranceRatioVo> insuranceRatioVoList = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(ratioCodeList);
            Map<String, InsuranceRatioVo> getInsuranceRatioByRatioCodeMap = insuranceRatioVoList.stream().collect(Collectors.toMap(InsuranceRatioVo::getInsuranceRatioCode, c -> c));
            for (PracticeCollectPayVo practiceCollectPayVo : practiceCollectPayVoList) {
                if (getInsuranceRatioByRatioCodeMap.containsKey(practiceCollectPayVo.getRatioCode())) {
                    InsuranceRatioVo insuranceRatioVo = getInsuranceRatioByRatioCodeMap.get(practiceCollectPayVo.getRatioCode());
                    /**月缴*/
                    if (insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.MONTH_PAY.getCode()) {
                        CalculateArgs args = getAmt(practiceCollectPayVo, insuranceRatioVo);
                        practiceCollectPayVo.setComAmt(args.getComAmt());
                        practiceCollectPayVo.setIndAmt(args.getIndAmt());
                        InsurancePracticeReport insurancePracticeReport = getInsurancePracticeReport(updater, practiceCollectPayVo, insuranceRatioVo, null, null);
                        insurancePracticeReports.add(insurancePracticeReport);
                    }
                    /**年缴*/
                    else if (isYearPayment(insuranceRatioVo)) {
                        if (practiceCollectPayVo.getPracticeId() == null || practiceCollectPayVo.getProdCode() == null || lockMonth == null) {
                            throw new RuntimeException("查询账单首次生成数据 参数不能为空");
                        }
                        Integer month = lockMonth % 100;
                        Short payMonth = insuranceRatioVo.getPayMonth();
                        /** 在首款账单月表中找寻同一个年度是否已经生成数据
                         * */
                        Integer currYear = getYear(lockMonth / 100,payMonth,month);
                        Integer nextYearMonth = com.reon.hr.common.utils.DateUtil.getYearMonth((currYear+1)+"",payMonth);
                        List<PerPracticeBillVo> perPracticeBillVos = perPracticeBillService.queryPracticeBillByPracticeId(practiceCollectPayVo.getPracticeId(),practiceCollectPayVo.getProdCode().intValue());
                        List<PerPracticeBillVo> subPerPracticeBillVos = perPracticeBillVos.stream().filter(vo -> vo.getProdCode().equals(practiceCollectPayVo.getProdCode())).collect(Collectors.toList());
                        List<Integer> collectYear = getCollectYear(subPerPracticeBillVos, payMonth);
                        /**没有账单或者首次生成年月和锁定年月相同 */
                        if (!collectYear.contains(currYear) || month.equals(payMonth.intValue())) {
                            /**年缴（不足一年按年）*/
                            if (insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode()) {
                                CalculateArgs args = getAmt(practiceCollectPayVo, insuranceRatioVo);
                                InsurancePracticeReport insurancePracticeReport = getInsurancePracticeReport(updater, practiceCollectPayVo, insuranceRatioVo, args.getComAmt(), args.getIndAmt());
                                insurancePracticeReports.add(insurancePracticeReport);
                            }
                            /**年缴（不足一年按月）*/
                            if (insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode()) {
                                /***实做目前只有当收当*/
                                // 根据perBillInfo表 判断是否为首版账单
                                /** 当月收   */
                                Integer startMonth = practiceCollectPayVo.getStartMonth();
                                Integer endMonth = practiceCollectPayVo.getEndMonth();
                                BigDecimal comMonthlyFee = insuranceRatioVo.getComMonthlyFee();
                                BigDecimal indMonthlyFee = insuranceRatioVo.getIndMonthlyFee();
                                ReceiveInfo receiveInfo = new ReceiveInfo(1, 0, 1, lockMonth, startMonth, startMonth,
                                        endMonth, collectYear.isEmpty());
                                List<Integer> fullMonths;
                                try {
                                    fullMonths = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getServiceMonths();
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                BigDecimal comAmt = Optional.ofNullable(practiceCollectPayVo.getComAmt()).orElse(BigDecimal.ZERO);
                                BigDecimal indAmt = Optional.ofNullable(practiceCollectPayVo.getIndAmt()).orElse(BigDecimal.ZERO);
                                ChargeFreqInfo chargeFreqInfo = new ChargeFreqInfo(InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode(), payMonth, comAmt, indAmt,
                                        comMonthlyFee, indMonthlyFee, collectYear.isEmpty(), fullMonths, endMonth);
                                IChargeFreqStrategyFactory chargeFreqStrategyFactory = new ChargeFreqStrategyFactory();
                                ChargeFreqStrategy chargeFreqStrategy;
                                try {
                                    chargeFreqStrategy = chargeFreqStrategyFactory.createChargeFreqStrategy(chargeFreqInfo);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                Map<Integer, ProdAmtVo> map = chargeFreqStrategy.getProdAmt();
                                if(map.containsKey(currYear)){
                                    BigDecimal calculateComAmt = map.get(currYear).getComAmt();
                                    BigDecimal calculateIndAmt = map.get(currYear).getIndAmt();
                                    if(endMonth != null && endMonth < nextYearMonth){
                                        /**不算头尾*/
                                        int gapPayMonthCount = com.reon.hr.common.utils.DateUtil.getBetweenTwoYearMonthCount(endMonth, nextYearMonth)-2;
                                        if(gapPayMonthCount > 0){
                                            BigDecimal monthComAmt = chargeFreqInfo.getComMonthlyFee().multiply(new BigDecimal(gapPayMonthCount));
                                            BigDecimal monthIndAmt = chargeFreqInfo.getIndMonthlyFee().multiply(new BigDecimal(gapPayMonthCount));
                                            calculateComAmt = calculateComAmt.subtract(monthComAmt);
                                            calculateIndAmt = calculateIndAmt.subtract(monthIndAmt);
                                        }
                                    }
                                    InsurancePracticeReport insurancePracticeReport = getInsurancePracticeReport(updater, practiceCollectPayVo, insuranceRatioVo, calculateComAmt, calculateIndAmt);
                                    insurancePracticeReports.add(insurancePracticeReport);
                                }
                            }
                            if ("add".equals(type)) {
                                PerPracticeBill query = new PerPracticeBill(practiceCollectPayVo.getPracticeId(), practiceCollectPayVo.getProdCode(), lockMonth);
                                PerPracticeBill targetYearBill = perPracticeBillService.queryPracticeBill(query);
                                if (Objects.isNull(targetYearBill)) {
                                    PerPracticeBill practiceBill = new PerPracticeBill();
                                    practiceBill.setPracticeId(practiceCollectPayVo.getPracticeId());
                                    practiceBill.setProdCode(practiceCollectPayVo.getProdCode());
                                    practiceBill.init(lockMonth, updater);
                                    perPracticeBillService.insert(practiceBill);
                                } else {
                                    perPracticeBillService.updateTime(targetYearBill);
                                }
                            }

                        }
                    }
                }
            }
        }
        return insurancePracticeReports;
    }

    private List<Integer> getSubMonthList(Integer currYear, Short payMonth,List<Integer> fullMonths) {
        List<Integer> months = Lists.newArrayList();
        Integer startMonth = currYear*100+payMonth;
        Integer endMonth = (currYear+1)*100+payMonth;
        for (Integer month : fullMonths) {
            if (month >= startMonth && month < endMonth) {
                months.add(month);
            }
        }
        return months;
    }
    private List<Integer> getCollectYear(List<PerPracticeBillVo> perPracticeBillVoList,Short payMonth) {
        List<Integer> collectYears = Lists.newArrayList();
        for (PerPracticeBillVo perPracticeBillVo : perPracticeBillVoList) {
            Integer firstBillMonth = perPracticeBillVo.getFirstBillMonth();
            /***根据生成月份获取，收取费用的年份*/
            Integer month = firstBillMonth % 100;
            Integer year = firstBillMonth / 100;
            Integer currYear = getYear(year, payMonth, month);
            collectYears.add(currYear);
        }
        return collectYears;
    }

    public static Integer getYear(Integer year, Short payMonth, Integer month) {
        if (month < payMonth) {
            year = year-1;
        }
        return year;
    }

    private CalculateArgs getAmt(PracticeCollectPayVo practiceCollectPayVo, InsuranceRatioVo insuranceRatioVo) {
        CalculateArgs args = new CalculateArgs();
        args.setComArgs(practiceCollectPayVo.getComBase(), insuranceRatioVo.getComRatio()
                , insuranceRatioVo.getComAdd(), insuranceRatioVo.getComCalcMode(), insuranceRatioVo.getComExactVal());
        args.setIndArgs(practiceCollectPayVo.getIndBase(), insuranceRatioVo.getIndRatio()
                        , insuranceRatioVo.getIndlAdd(), insuranceRatioVo.getIndCalcMode(), insuranceRatioVo.getIndExactVal())
                .setSpecialFlag(insuranceRatioVo.getSpecialFlag());
        CalculateUtil.calculateAmt(args);
        return args;
    }

    private Integer getBillType(Integer index) {
        List<PracticeLockInfoFeeTypeEnum> feeTypeEnums = Lists.newArrayList(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT, PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT);
        return Integer.parseInt(String.valueOf(feeTypeEnums.get(index % feeTypeEnums.size()).getCode()));
    }

    private void initBillAmount(PracticeLockInfoVo practiceLockInfo, BigDecimal norMalAmt, BigDecimal addAmt) {
        practiceLockInfo.setNormalAmt(norMalAmt);
        practiceLockInfo.setAddAmt(addAmt);
        practiceLockInfo.setGenStatus(PracticeLockInfoGenStatusEnum.SUCCESS.getCode());
    }


    /***判断是否年缴*/
    private Boolean isYearPayment(InsuranceRatioVo insuranceRatioVo) {
        return insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode()
                || insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode();
    }

    private Boolean hasFirstBillMethod(PerPracticeBill targetYearBill, PerPracticeBill perOrNextYearBill, Short payMonth) {
        boolean hasFirstBill = false;
        boolean hasFirstBill2 = false;
        if (Objects.nonNull(targetYearBill) && targetYearBill.getFirstBillMonth() % 100 >= payMonth) {
            hasFirstBill = true;
        }
        if (Objects.nonNull(perOrNextYearBill) && perOrNextYearBill.getFirstBillMonth() % 100 < payMonth) {
            hasFirstBill2 = true;
        }

        if (hasFirstBill && hasFirstBill2) {
            logger.info("实做账单表中实做id,{}，prodCode,{}", targetYearBill.getPracticeId(), targetYearBill.getProdCode());
            throw new RuntimeException("账单首次月中，存在重复数据");
        }
        return hasFirstBill || hasFirstBill2;
    }

    /**
     * @param updater              更新人
     * @param practiceCollectPayVo 汇缴数据
     * @param insuranceRatioVo     产品比例
     * @param calculateComAmt      计算出的企业金额
     * @param calculateIndAmt      计算出个人金额
     * @return
     */
    private InsurancePracticeReport getInsurancePracticeReport(String updater, PracticeCollectPayVo practiceCollectPayVo, InsuranceRatioVo insuranceRatioVo, BigDecimal calculateComAmt, BigDecimal calculateIndAmt) {
        InsurancePracticeReport insurancePracticeReport = new InsurancePracticeReport();
        BeanUtils.copyProperties(practiceCollectPayVo, insurancePracticeReport);
        BigDecimal prodTotalAmt = practiceCollectPayVo.getComAmt().add(practiceCollectPayVo.getIndAmt());
        insurancePracticeReport.init(updater, new Date());
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal comAmt = BigDecimal.ZERO;
        BigDecimal intAmt = BigDecimal.ZERO;
        /**月缴*/
        if (insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.MONTH_PAY.getCode()) {
            comAmt = practiceCollectPayVo.getComAmt();
            intAmt = practiceCollectPayVo.getIndAmt();
            totalAmt = prodTotalAmt;
        }
        /**年缴*/
        if (insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_YEAR.getCode()
                || insuranceRatioVo.getChargeFreq() == InsuranceRatioChargeFreq.YEAR_PAY_LACK_TAKE_MONTH.getCode()) {
            BigDecimal calculateTotal = calculateComAmt.add(calculateIndAmt);
            if (calculateTotal.compareTo(prodTotalAmt) > 0) {
                comAmt = practiceCollectPayVo.getComAmt();
                intAmt = practiceCollectPayVo.getIndAmt();
                totalAmt = prodTotalAmt;
            } else {
                comAmt = calculateComAmt;
                intAmt = calculateIndAmt;
                totalAmt = calculateTotal;
            }
        }
        insurancePracticeReport.setComAmt(comAmt);
        insurancePracticeReport.setIndAmt(intAmt);
        insurancePracticeReport.setTotalAmt(totalAmt);
        insurancePracticeReport.setComRatio(insuranceRatioVo.getComRatio());
        insurancePracticeReport.setIndRatio(insuranceRatioVo.getIndRatio());
        insurancePracticeReport.setFeeType(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode());
        return insurancePracticeReport;
    }


    /**
     * 拉取补缴数据并转换为报表数据
     *
     * @param reportPullInsurancePracticeDto
     * @return
     */
    private List<InsurancePracticeReport> pullSuppliPayReport(ReportPullInsurancePracticeDto reportPullInsurancePracticeDto,Map<Long, List<IndividualFeeLockVo>> suppliPayMap) {
        logger.info("拉取实做补缴数据:{}", reportPullInsurancePracticeDto);
        List<PracticeSuppliPayVo> practiceSuppliPayVoList = practiceSuppliPayWrapperService.reportPullSuppliPayData(reportPullInsurancePracticeDto);
        practiceSuppliPayVoList = practiceSuppliPayVoList.stream().filter(vo ->!suppliPayMap.containsKey(vo.getPracticeId())
                && vo.getStartMonth()<= Optional.ofNullable(vo.getEndMonth()).orElse(Integer.MAX_VALUE)).collect(Collectors.toList());
        return buildSuppliPayReport(reportPullInsurancePracticeDto.getUpdater(), practiceSuppliPayVoList);
    }

    private List<InsurancePracticeReport> buildSuppliPayReport(String updater, List<PracticeSuppliPayVo> practiceSuppliPayVoList) {
        List<InsurancePracticeReport> insurancePracticeReports = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(practiceSuppliPayVoList)) {
            Date now = new Date();
            List<String> ratioCodeList = practiceSuppliPayVoList.stream().map(PracticeSuppliPayVo::getRatioCode).distinct().collect(Collectors.toList());
            List<InsuranceRatioVo> insuranceRatioVoList = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(ratioCodeList);
            Map<String, InsuranceRatioVo> getInsuranceRatioByRatioCodeMap = insuranceRatioVoList.stream().collect(Collectors.toMap(InsuranceRatioVo::getInsuranceRatioCode, c -> c));
            for (PracticeSuppliPayVo practiceSuppliPayVo : practiceSuppliPayVoList) {
                InsurancePracticeReport insurancePracticeReport = new InsurancePracticeReport();
                BeanUtils.copyProperties(practiceSuppliPayVo, insurancePracticeReport);
                insurancePracticeReport.init(updater, now);
                BigDecimal monthSuppliPayAmount = Optional.ofNullable(practiceSuppliPayVo.getComAmt()).orElse(BigDecimal.ZERO)
                        .add(Optional.ofNullable(practiceSuppliPayVo.getIndAmt()).orElse(BigDecimal.ZERO));
                /**补缴月份*/
                int suppliPayMonthCount = DateUtil.getMonthDiff(practiceSuppliPayVo.getStartMonth(), practiceSuppliPayVo.getEndMonth()) + 1;
                BigDecimal totalAmt;
                if (practiceSuppliPayVo.getAddType().equals(SuppliPayTypeEnum.NORMAL_PAYMENT.getCode())) {
                    /**个人金额加企业金额乘以补缴月份 加上个人、企业滞纳金*/
                    totalAmt = monthSuppliPayAmount.multiply(BigDecimal.valueOf(suppliPayMonthCount))
                            .add(Optional.ofNullable(practiceSuppliPayVo.getComLateFee()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(practiceSuppliPayVo.getIndLateFee()).orElse(BigDecimal.ZERO));
                } else {
                    totalAmt = monthSuppliPayAmount.add(Optional.ofNullable(practiceSuppliPayVo.getComLateFee()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(practiceSuppliPayVo.getIndLateFee()).orElse(BigDecimal.ZERO));
                }
                insurancePracticeReport.setTotalAmt(totalAmt);
                insurancePracticeReport.setMonths(suppliPayMonthCount);
                insurancePracticeReport.setProdCode(Byte.valueOf(practiceSuppliPayVo.getProdCode().toString()));
                if (getInsuranceRatioByRatioCodeMap.containsKey(practiceSuppliPayVo.getRatioCode())) {
                    InsuranceRatioVo insuranceRatioVo = getInsuranceRatioByRatioCodeMap.get(practiceSuppliPayVo.getRatioCode());
                    insurancePracticeReport.setComRatio(insuranceRatioVo.getComRatio());
                    insurancePracticeReport.setIndRatio(insuranceRatioVo.getIndRatio());
                }
                insurancePracticeReport.setFeeType(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode());
                insurancePracticeReports.add(insurancePracticeReport);
            }
        }
        return insurancePracticeReports;
    }

    /**
     * 根据ID将社保锁定数据更新为锁定状态
     *
     * @param id
     * @param operatorName
     * @return
     */
    @Override
    public Integer updateReportToLock(Integer id, String operatorName) {
        PracticeLockInfoVo practiceLockInfoVo = practiceBillMapper.getById(id);
        Date date = new Date();
        practiceLockInfoVo.setLockStatus(PracticeBillLockStatusEnum.LOCK.getCode());
        practiceLockInfoVo.setLockTime(date);
        practiceLockInfoVo.setLockMan(operatorName);
        practiceLockInfoVo.setUpdater(operatorName);
        practiceLockInfoVo.setUpdateTime(date);
        return practiceBillMapper.updateLockStatus(practiceLockInfoVo);
    }

    @Override
    public Integer updateReportToLockByIds(List<Integer> id, String operatorName, byte status) {
        PracticeLockInfoVo practiceLockInfoVo = new PracticeLockInfoVo();
        Date date = new Date();
        practiceLockInfoVo.setLockStatus(status);
        practiceLockInfoVo.setLockTime(date);
        practiceLockInfoVo.setLockMan(operatorName);
        practiceLockInfoVo.setUpdater(operatorName);
        practiceLockInfoVo.setUpdateTime(date);
        return practiceBillMapper.updateLockStatusByIds(practiceLockInfoVo, id);
    }

    /**
     * 根据ID将社保锁定数据更新为未锁定状态
     *
     * @param id
     * @param operatorName
     * @return
     */
    @Override
    public Integer updateReportToUnLock(Integer id, String operatorName) {
        PracticeLockInfoVo practiceLockInfoVo = practiceBillMapper.getById(id);
        Date date = new Date();
        practiceLockInfoVo.setLockStatus(PracticeBillLockStatusEnum.UNLOCKED.getCode());
        practiceLockInfoVo.setUpdater(operatorName);
        practiceLockInfoVo.setUpdateTime(date);
        return practiceBillMapper.updateLockStatus(practiceLockInfoVo);
    }

    @Override
    public Integer updateReportToInPaymentByIds(List<Long> practiceLockIds, String operatorName) {
        return practiceBillMapper.updatePaymentStatusByIds(practiceLockIds, operatorName, new Date(), PracticeLockInfoPayStatusEnum.IN_PAID.getCode());
    }

    @Override
    public List<PracticeLockInfoVo> getBySearch(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("根据搜索条件查询实做锁定数据:{}", searchInsuranceLockDto);
        return practiceBillMapper.getBySearch(searchInsuranceLockDto);
    }

    @Override
    public Integer updateReportToPaymentSuccessByIds(List<Long> practiceLockIds, PaymentApply paymentApply) {
        /**===================修改details 为支付完成状态======================*/
        Long paymentApplyId = paymentApply.getId();
        try {
            Integer payDetailType = paymentApply.getPayDetailType();
            String creator = paymentApply.getCreator();
            List<PracticePayDetail> practicePayDetailVosByPayId = practicePayDetailService.getPracticePayDetailVosByPayId(paymentApplyId);
            if(CollectionUtils.isEmpty(practicePayDetailVosByPayId)){
                logger.error(",{}没有查询到支付详细数据。。。。。。。。。。。。。",paymentApplyId);
                return 1;
            }
            /**还需要维护 insurance_practice_report_xx 与 individual_fee_lock*/
            if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode().intValue())){
                editReportAndIndividualFeeLock(paymentApplyId,practicePayDetailVosByPayId,PracticeLockInfoPayStatusEnum.HAVE_PAID.getCode(),creator);
            }
            paymentApply.setAppStatus(PaymentApplyProcessStatus.FINISHED.getCode());
            PracticePayDetailVo detailVo = new PracticePayDetailVo();
            detailVo.setPayApplyId(paymentApply.getId());
            detailVo.setAppStatus(paymentApply.getAppStatus());
            detailVo.setUpdater(paymentApply.getCreator());
            detailVo.setUpdateTime(new Date());
            practicePayDetailService.updatePracticePayDetailVoByPayApplyIds(detailVo);
            /**===================修改将冻结金额划到支付完成金额======================*/

            Map<Long, List<PracticePayDetail>> billIdsMap = practicePayDetailVosByPayId.stream().collect(Collectors.groupingBy(PracticePayDetail::getBillId));
            Map<Long, BigDecimal> billAmtMap = getAmtByBillIds(billIdsMap);
            List<Long> billIds = Lists.newArrayList(billIdsMap.keySet());
            List<PracticeLockInfoVo> byIds = practiceBillMapper.getByIds(billIds.stream().map(Long::intValue).collect(Collectors.toList()));
            Map<Long, PracticeLockInfoVo> lockInfoMap = byIds.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, Function.identity()));
            List<Long> editIds = Lists.newArrayList();
            for (Long billId : billIds) {
                BigDecimal currAmt = billAmtMap.getOrDefault(billId, BigDecimal.ZERO);
                PracticeLockInfoVo vo = new PracticeLockInfoVo();
                vo.setId(billId);
                PracticeLockInfoVo practiceLockInfoVo = lockInfoMap.get(billId);
                BigDecimal frozenAmt = Optional.ofNullable(practiceLockInfoVo.getFrozenAmt()).orElse(BigDecimal.ZERO);
                BigDecimal comPayAmt = Optional.ofNullable(practiceLockInfoVo.getComPayAmt()).orElse(BigDecimal.ZERO);
                vo.setFrozenAmt(frozenAmt.subtract(currAmt));
                vo.setComPayAmt(comPayAmt.add(currAmt));
                BigDecimal add = Optional.ofNullable(practiceLockInfoVo.getComPayAmt()).orElse(BigDecimal.ZERO).add(currAmt);
                BigDecimal payAmt = Optional.ofNullable(practiceLockInfoVo.getPayAmt()).orElse(BigDecimal.ZERO);
                if (BigDecimalUtil.equalsVal(add, payAmt)) {
                    vo.setPayStatus(PracticeLockInfoPayStatusEnum.HAVE_PAID.getCode());
                } else {
                    editIds.add(billId);
                }
                vo.setUpdater(paymentApply.getCreator());
                practiceBillMapper.updatePracticeLockInfoVAmt(vo);
            }
            if (CollectionUtils.isNotEmpty(editIds) && !payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode().intValue())) {
                editPayStatus(paymentApply.getCreator(), editIds);
            }
            /**===================修改实做账单的支付状态======================*/
            List<PracticePayDetailSuccessVo> successVos = practicePayDetailVosByPayId.stream().map(vo -> {
                PracticePayDetailSuccessVo successVo = new PracticePayDetailSuccessVo();
                BeanUtils.copyProperties(vo, successVo);
                return successVo;
            }).collect(Collectors.toList());
            practicePayDetailSuccessMapper.insertPracticePayDetailSuccessVos(successVos);
        } catch (Exception e) {
            PayFailedLog log = new PayFailedLog();
            log.setPayApplyId(paymentApplyId);
            log.setReason(e.getMessage());
            payFailedLogMapper.insert(log);
            throw new RuntimeException(e);
        }
        return 0;
    }


    @Override
    public List<ExportPracticeReportVo> getRecentlyPracticeReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("获取导出新进报表数据:{}", searchInsuranceLockDto);
        List<ExportPracticeReportVo> exportPracticeReportVoList = new ArrayList<>();
        List<InsurancePracticeReportVo> insurancePracticeReportList = insurancePracticeReportMapper.getInsurancePracticeReportBySearch(searchInsuranceLockDto);
        if (CollectionUtils.isNotEmpty(insurancePracticeReportList)) {
            Map<Long, List<InsurancePracticeReportVo>> getInsurancePracticeReportListByPracticeIdMap = insurancePracticeReportList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getPracticeId));
            Set<Long> practiceIdSet = getInsurancePracticeReportListByPracticeIdMap.keySet();
            /**开始截止时间*/
            Map<String, Object> map = new HashMap<>(3);
            map.put("startTime", searchInsuranceLockDto.getStartTime());
            map.put("endTime", searchInsuranceLockDto.getEndTime());
            map.put("addMonth", searchInsuranceLockDto.getLockMonth());
            List<ExportPraticeReportDataVo> exportPracticeReportDataList = iInsurancePracticeWrapperService.getRecentlyPracticeReportByPracticeIdsAndStartMonth(Lists.newArrayList(practiceIdSet), map);
            buildExportPracticeReport(searchInsuranceLockDto, exportPracticeReportVoList, getInsurancePracticeReportListByPracticeIdMap, practiceIdSet, exportPracticeReportDataList);
        }
        return exportPracticeReportVoList;
    }

    private void buildExportPracticeReport(SearchInsuranceLockDto search, List<ExportPracticeReportVo> exportPracticeReportVoList, Map<Long, List<InsurancePracticeReportVo>> practiceIdMap, Set<Long> practiceIdSet, List<ExportPraticeReportDataVo> exportPracticeReportDataList) {
        Map<Long, ExportPraticeReportDataVo> getExportPracticeReportVoByPracticeIdMap = exportPracticeReportDataList.stream().collect(Collectors.toMap(ExportPraticeReportDataVo::getPracticeId, c -> c));
        for (Long practiceId : practiceIdSet) {
            if (getExportPracticeReportVoByPracticeIdMap.containsKey(practiceId)) {
                ExportPracticeReportVo exportPracticeReportVo = new ExportPracticeReportVo();
                exportPracticeReportVo.setPracticeId(practiceId);
                ExportPraticeReportDataVo exportPraticeReportDataVo = getExportPracticeReportVoByPracticeIdMap.get(exportPracticeReportVo.getPracticeId());
                BeanUtils.copyProperties(exportPraticeReportDataVo, exportPracticeReportVo);
                List<InsurancePracticeReportVo> insurancePracticeReportVoList = practiceIdMap.get(practiceId);
                //设置产品信息
                exportPracticeReportVo.setPracticeReportDetailMap(getPracticeReportDetailMapByInsurancePracticeReportVo(insurancePracticeReportVoList));
                exportPracticeReportVo.setAmount(insurancePracticeReportVoList.stream().map(InsurancePracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (Objects.equals(exportPracticeReportVo.getCertType(),CertType.ID_CARD.getCode())){
                    try {
                        String retirementAge = RetirementAgeUtil.getRetirementAge(exportPracticeReportVo.getCertNo());
                        exportPracticeReportVo.setRetireDate(retirementAge);
                        boolean sexBool = RetirementAgeUtil.isFemale(exportPracticeReportVo.getCertNo());
                        if (sexBool){
                            exportPracticeReportVo.setSex("女");
                        }else {
                            exportPracticeReportVo.setSex("男");
                        }
                    } catch (Exception e) {
                        e.printStackTrace ();
                    }
                }
                exportPracticeReportVoList.add(exportPracticeReportVo);
            }
        }
        packExportPracticeReportVo(exportPracticeReportVoList, search.getPackCodes());
    }


    private Date IntToDate(Integer date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            return sdf.parse(String.valueOf(date));
        } catch (ParseException e) {
            throw new RuntimeException("转换日期出错了");
        }
    }

    @Override
    public List<ExportPracticeReportVo> getStopPayPracticeReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("获取导出停缴报表数据:{}", searchInsuranceLockDto);
        /** 停缴： 福利截止月为9月那么这个出现在10月的名单中*/
        Integer lockMonth = searchInsuranceLockDto.getLockMonth();
        Integer endMonth = Integer.valueOf((getPrevMonthDate(IntToDate(lockMonth), 1).replace("-", "")));
        searchInsuranceLockDto.setLockMonth(endMonth);
        List<ExportPracticeReportVo> exportPracticeReportVoList = new ArrayList<>();
        List<InsurancePracticeReportVo> insurancePracticeReportList = insurancePracticeReportMapper.getInsurancePracticeReportBySearch(searchInsuranceLockDto);
        /***如果实做没有进入过报表，这里是查不出来  所以将福利包全部的停缴查出，从中找到不存在的实做**/
        List<Long> practiceIdList = insurancePracticeReportList.stream().map(InsurancePracticeReportVo::getPracticeId).collect(Collectors.toList());
        List<InsurancePracticeReportVo> reportVOList = getInsurancePracticeReportVoByPracticeId(practiceIdList, searchInsuranceLockDto);
        if (CollectionUtils.isNotEmpty(reportVOList)) {
            insurancePracticeReportList.addAll(reportVOList);
        }
        if (CollectionUtils.isNotEmpty(insurancePracticeReportList)) {
            Map<Long, List<InsurancePracticeReportVo>> getInsurancePracticeReportListByPracticeIdMap = insurancePracticeReportList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getPracticeId));
            Set<Long> practiceIdSet = getInsurancePracticeReportListByPracticeIdMap.keySet();
            /**开始截止时间*/
            Map<String, Object> map = new HashMap<>(3);
            map.put("startTime", searchInsuranceLockDto.getStartTime());
            map.put("endTime", searchInsuranceLockDto.getEndTime());
            map.put("endMonth", endMonth);
            List<ExportPraticeReportDataVo> exportPracticeReportDataList = iInsurancePracticeWrapperService.getStopPayPracticeReportByPracticeIdsAndEndMonth(Lists.newArrayList(practiceIdSet), map);
            buildExportPracticeReport(searchInsuranceLockDto, exportPracticeReportVoList, getInsurancePracticeReportListByPracticeIdMap, practiceIdSet, exportPracticeReportDataList);
        }
        return exportPracticeReportVoList;
    }

    @Override
    public List<ExportPracticeReportVo> getPracticeReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("获取导出正常汇缴报表入参:{}", searchInsuranceLockDto);
        List<ExportPracticeReportVo> exportPracticeReportVoList = insurancePracticeReportMapper.getExportPracticeReportBySearch(searchInsuranceLockDto);
        if (CollectionUtils.isNotEmpty(exportPracticeReportVoList)) {
            List<Long> practiceIds = exportPracticeReportVoList.stream().map(ExportPracticeReportVo::getPracticeId).collect(Collectors.toList());
            List<ExportPraticeReportDataVo> exportPracticeReportDataList = iInsurancePracticeWrapperService.getPracticeReportByPracticeIds(practiceIds);
            if (CollectionUtils.isNotEmpty(exportPracticeReportDataList)) {
                Map<Long, ExportPraticeReportDataVo> getExportPracticeReportVoByPracticeIdMap = exportPracticeReportDataList.stream().collect(Collectors.toMap(ExportPraticeReportDataVo::getPracticeId, c -> c));
                for (ExportPracticeReportVo exportPracticeReportVo : exportPracticeReportVoList) {
                    ExportPraticeReportDataVo exportPraticeReportDataVo = getExportPracticeReportVoByPracticeIdMap.getOrDefault(exportPracticeReportVo.getPracticeId(), new ExportPraticeReportDataVo());
                    BeanUtils.copyProperties(exportPraticeReportDataVo, exportPracticeReportVo);
                }
                packExportPracticeReportVo(exportPracticeReportVoList, searchInsuranceLockDto.getPackCodes());
            }
        }
        return exportPracticeReportVoList;
    }

    @Override
    public List<ExportPracticeReportVo> getPracticeDetailReportBySearch(SearchInsuranceLockDto searchInsuranceLockDto) {
        logger.info("获取导出报表明细入参:{}", searchInsuranceLockDto);
        List<ExportPracticeReportVo> exportPracticeReportVoList = new ArrayList<>();
        List<InsurancePracticeReportVo> insurancePracticeReportList = insurancePracticeReportMapper.getInsurancePracticeReportBySearch(searchInsuranceLockDto);
        if (CollectionUtils.isNotEmpty(insurancePracticeReportList)) {
            Map<Long, List<InsurancePracticeReportVo>> getInsurancePracticeReportListByPracticeIdMap = insurancePracticeReportList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getPracticeId));
            Set<Long> practiceIdSet = getInsurancePracticeReportListByPracticeIdMap.keySet();
            List<ExportPraticeReportDataVo> exportPracticeReportDataList = iInsurancePracticeWrapperService.getPracticeReportByPracticeIds(new ArrayList<>(practiceIdSet));
//            List<ExportPraticeReportDataVo> exportPracticeReportDataList = getExportPraticeReportDataVoByPracticeIds(new ArrayList<>(practiceIdSet));
            buildExportPracticeReport(searchInsuranceLockDto, exportPracticeReportVoList, getInsurancePracticeReportListByPracticeIdMap, practiceIdSet, exportPracticeReportDataList);
        }
        return exportPracticeReportVoList;
    }


    @Override
    public List<InsurancePracticeReportVo> getInsurancePracticeReportBypracticeId(List<Long> practiceIds, List<Integer> months) {
        return insurancePracticeReportMapper.getInsurancePracticeReportBypracticeId(practiceIds, months);
    }

    @Override
    public List<PerPracticeBillVo> queryPracticeBillByPracticeIds(List<Long> practiceIds) {
        return perPracticeBillService.queryPracticeBillByPracticeIds(practiceIds);
    }

    @Override
    public List<PracticeLockInfoVo> queryPracticeBillByPracticeLockInfoVo(List<PracticeLockInfoVo> practiceLockInfoVos) {
        return practiceBillMapper.queryPracticeBillByPracticeLockInfoVo(practiceLockInfoVos);
    }

    List<ExportPraticeReportDataVo> getExportPraticeReportDataVoByPracticeIds(List<Long> practiceIds) {
        final int commSize = 3000;
        List<ExportPraticeReportDataVo> allList = Lists.newArrayList();
        if (practiceIds.size() < commSize) {
            List<ExportPraticeReportDataVo> exportPracticeReportDataList = iInsurancePracticeWrapperService.getPracticeReportByPracticeIds(practiceIds);
            allList.addAll(exportPracticeReportDataList);
        } else {
            int callSize = practiceIds.size() % commSize == 0 ? practiceIds.size() / commSize : practiceIds.size() / commSize + 1;
            ExecutorService threadPool = Executors.newFixedThreadPool(callSize);
            try {
                List<CompletableFuture<List<ExportPraticeReportDataVo>>> futures = Lists.newArrayList();
                for (int i = 0; i < callSize; i++) {
                    int finalI = i;
                    CompletableFuture<List<ExportPraticeReportDataVo>> future = CompletableFuture.supplyAsync(
                            () -> iInsurancePracticeWrapperService.getPracticeReportByPracticeIds(practiceIds.subList(finalI * commSize, Math.min(((finalI + 1) * commSize), practiceIds.size()))), threadPool);
                    futures.add(future);
                }
                for (CompletableFuture<List<ExportPraticeReportDataVo>> future : futures) {
                    try {
                        allList.addAll(future.get());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                threadPool.shutdown();
            }

        }
        return allList;
    }

    /**
     * 根据实做获取到报表数据
     **/
    private List<InsurancePracticeReportVo> getInsurancePracticeReportVoByPracticeId(List<Long> practiceIdSet, SearchInsuranceLockDto searchInsuranceLockDto) {
        List<InsurancePracticeReportVo> insurancePracticeReportVos = new ArrayList<>();
        Integer lockMonth = searchInsuranceLockDto.getLockMonth();
        InsurancePracticeVo queryCondition = new InsurancePracticeVo();
        queryCondition.setHandleStatus(InsurancePracticeEnum.HandleStatusEnum.STOP_PAYMENT.getIndex());
        queryCondition.setPackCodes(searchInsuranceLockDto.getPackCodes());
        queryCondition.setOrgCode(searchInsuranceLockDto.getOrgCode());
        List<InsurancePracticeVo> insurancePracticeVos = iInsurancePracticeWrapperService.queryInsurancePracticeVoByVos(queryCondition);
        List<InsurancePracticeVo> insurancePracticeVoList = insurancePracticeVos.stream().filter(vo -> !practiceIdSet.contains(vo.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insurancePracticeVoList)) {
            //汇缴数据
            List<Long> practiceIds = insurancePracticeVoList.stream().map(InsurancePracticeVo::getId).collect(Collectors.toList());
            List<PracticeCollectPayVo> practiceCollectPayVos = iInsurancePracticeWrapperService.selectByReportPullCollectByPracticeIds(practiceIds);
            //补缴数据
            List<InsurancePracticeReport> collectReports = buildCollectReportVo(null, lockMonth, practiceCollectPayVos, "temp");
            List<PracticeSuppliPayVo> practiceSuppliPayVos = practiceSuppliPayWrapperService.selectByReportPullSuppliPracticeIds(practiceIds, lockMonth);
            List<InsurancePracticeReport> suppliReports = buildSuppliPayReport(null, practiceSuppliPayVos);
            collectReports.addAll(suppliReports);
            insurancePracticeReportVos = collectReports.stream().map(vo -> {
                InsurancePracticeReportVo insurancePracticeReportVo = new InsurancePracticeReportVo();
                BeanUtils.copyProperties(vo, insurancePracticeReportVo);
                return insurancePracticeReportVo;
            }).collect(Collectors.toList());
        }
        return insurancePracticeReportVos;
    }


    @Override
    public Integer getLatestUnLockYears(SearchInsuranceLockDto searchInsuranceLockDto) {
        return practiceBillMapper.getLatestUnLockYears(searchInsuranceLockDto);
    }

    @Override
    public List<InsurancePracticeReportVo> getInsurancePracticeReportByOrderNos(List<String> orderNos, List<Integer> months) {
        return insurancePracticeReportMapper.getInsurancePracticeReportByOrderNos(orderNos, months);
    }

    @Override
    public PracticePayDetailDto getPracticePaymentDetails(SearchInsuranceLockDto lockInfoVo) {
        PracticePayDetailDto dto = new PracticePayDetailDto();
        SearchInsuranceLockDto condition = new SearchInsuranceLockDto();
        condition.setOrgCode(lockInfoVo.getOrgCode());
        condition.setPackCodes(lockInfoVo.getPackCodes());
        condition.setLockMonth(lockInfoVo.getLockMonth());
        Byte payDetailType = lockInfoVo.getPayDetailType();
        if (payDetailType.equals(PracticePayMentTypeEnum.COLLECT_PAYMENT.getCode()) ||
                payDetailType.equals(PracticePayMentTypeEnum.REPLENISH_PAYMENT.getCode())) {
            /**以前payDetailType与报表的freeType 一起使用，*/
            condition.setPayDetailType(payDetailType);
        } else {
            condition.setPayDetailType(null);
        }

        List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getByPackCodesAndOrgCode(condition);
        packPracticeLockInfoVo(lockInfoVoList);
        Long operateTime = lockInfoVo.getOperateTime();
        String loginName = lockInfoVo.getCreater();
        if (Objects.isNull(operateTime)) {
            operateTime = new Date().getTime();
        }
        List<PracticePayDetail> noPayLockInfoDetails = Lists.newArrayList();
        if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            noPayLockInfoDetails = getPracticePayDetails(lockInfoVo, lockInfoVoList, operateTime, loginName);
        } else {
            /**单个锁定也要修改 报表的支付状态，这里改动过多，在报表页面添加一个提示是否单个支付过*/
            Map<Boolean, List<PracticeLockInfoVo>> booleanListMap = lockInfoVoList.stream().collect(Collectors.groupingBy(nonPay()));
            List<PracticeLockInfoVo> payLockInfo = booleanListMap.getOrDefault(false, Lists.newArrayList());
            List<Long> payBillIds = payLockInfo.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList());
            List<PracticeLockInfoVo> noPayLockInfo = booleanListMap.getOrDefault(true, Lists.newArrayList());
            List<Long> noPayBillIds = noPayLockInfo.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList());
            /**过滤已经支付的数据*/
            List<Long> billIds = lockInfoVoList.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList());
            /**查询当前选中所有账单Id*/
            List<PracticePayDetail> allPayDetailVos = practicePayDetailService.getPracticePayDetailVosByBillIds(billIds);
            /**已经有过支付的账单的支付数据*/
            Map<Boolean, List<PracticePayDetail>> allPayDetailVoMap = allPayDetailVos.stream().collect(Collectors.partitioningBy(vo -> payBillIds.contains(vo.getBillId())));
            //有过支付 把没有支付的的部分找出    没有过支付的  把表中的删除，根据报表重新生成
            // 支付中的报表的，未支付部分数据，
            List<PracticePayDetail> payLockInfoDetails = allPayDetailVoMap.getOrDefault(true, Lists.newArrayList()).stream().filter(vo -> vo.getPayApplyId() == null).collect(Collectors.toList());
            /**账单未支付过 ，先删除  在拉取最新的数据*/
            if (CollectionUtils.isNotEmpty(noPayBillIds)) {
                practicePayDetailService.deletePracticePayDetailVosByBillIds(noPayBillIds);
                List<PracticePayDetail> allInsertData = Lists.newArrayList();
                for (PracticeLockInfoVo practiceLockInfoVo : noPayLockInfo) {
                    SearchInsuranceLockDto subCondition = new SearchInsuranceLockDto();
                    BeanUtils.copyProperties(condition, subCondition);
                    subCondition.setPackCodes(Lists.newArrayList(practiceLockInfoVo.getPackCode()));
                    subCondition.setPayDetailType(practiceLockInfoVo.getPayDetailType().byteValue());
                    /**排除个人锁定支付部分*/
                    subCondition.setLockStatus(PracticeBillLockStatusEnum.UNLOCKED.getCode().intValue());
                    List<InsurancePracticeReportVo> allReports =  insurancePracticeReportMapper.getInsurancePracticeReport(subCondition);
                    List<SearchInsuranceLockDto> conditions = Lists.newArrayList();
                    Map<String, PracticeLockInfoVo> billIdMap = getBillIdMap(lockInfoVoList, conditions);
                    /**根据报表数据生成支付数据*/
                    List<PracticePayDetail> result = genDetails(allReports, billIdMap, new HashMap<>(), null, operateTime, loginName);
                    if (CollectionUtils.isNotEmpty(result)) {
                        allInsertData.addAll(result);
                    }
                }
                if (CollectionUtils.isNotEmpty(allInsertData)) {
                    insertDetails(allInsertData,"detail");
                    noPayLockInfoDetails.addAll(allInsertData);
                }
            }
            if (CollectionUtils.isNotEmpty(payLockInfoDetails)) {
                List<Long> editId = Lists.newArrayList();
                for (PracticePayDetail detailVo : payLockInfoDetails) {
                    editId.add(detailVo.getId());
                    detailVo.setOperateTime(operateTime);
                }
                practicePayDetailService.updateOperateTimeById(editId, operateTime);
            }
            noPayLockInfoDetails.addAll(payLockInfoDetails);
        }

        dto.setOperateTime(operateTime);
        dto.setLockInfoVoList(lockInfoVoList);
        build(lockInfoVo, noPayLockInfoDetails, payDetailType, dto);
        if (!payDetailType.equals(PracticePayMentTypeEnum.EMP_PAY.getCode())) {
            saveDataToCache(dto, operateTime);
        }
        logger.info("======================执行完成======================");
        return dto;
    }

    private void saveDataToCache(PracticePayDetailDto dto, Long operateTime) {
        List<ProdPayAmtDto> prodData = dto.getProdData();
        if(CollectionUtils.isNotEmpty(prodData)){
            stringRedisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
            stringRedisTemplate.opsForList().rightPushAll(QueryUtil.getPayKey(operateTime), prodData);
        }
        prodData.forEach(vo -> vo.setDetailIds(null));
    }

    private void build(SearchInsuranceLockDto lockInfoVo, List<PracticePayDetail> noPayLockInfoDetails, Byte payDetailType, PracticePayDetailDto dto) {
        /**优化dto 中不要将id 都带到前段*/
        if (CollectionUtils.isNotEmpty(noPayLockInfoDetails)) {
            Map<String, ProdPayAmtDto> map = Maps.newHashMap();
            List<Byte> bytes = Lists.newArrayList(PracticePayMentTypeEnum.REPLENISH_PAYMENT.getCode(), PracticePayMentTypeEnum.COLLECT_PAYMENT.getCode(),
                    PracticePayMentTypeEnum.COLLECT_PAYMENT_AND_REPLENISH_PAYMENT.getCode());
            if (payDetailType.equals(PracticePayMentTypeEnum.PRODUCT_PAY.getCode())) {
                /**产品支付*/
                prodPay(noPayLockInfoDetails, map, dto);
            } else if (bytes.contains(payDetailType)) {
                /**汇缴 补缴支付*/
                collectAndReplenishPay(lockInfoVo, payDetailType, noPayLockInfoDetails, map, dto);
            } else if (payDetailType.equals(PracticePayMentTypeEnum.COMPANY_PERSON.getCode())) {
                /**企业与个人*/
                enterpriseOrIndividualPay(lockInfoVo, noPayLockInfoDetails, map, dto);
            } else if (payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())) {
                /**个人锁定*/
                individualLockPay(lockInfoVo, noPayLockInfoDetails, map, dto);
            }
        }
        if (Objects.isNull(dto.getProdData())) {
            dto.setProdData(Lists.newArrayList());
        }
    }


    @Override
    public PracticePayDetailDto getPracticePayDetailsByReject(SearchInsuranceLockDto lockInfoVo) {
        /***
         * 再次重新获取数据
         * 选中福利包的数据：没有支付状态，或者支付申请id 为当前的数据
         */
        PracticePayDetailDto dto = new PracticePayDetailDto();
        /**获取报表信息*/
        String loginName = lockInfoVo.getCreater();
        Long payApplyId = lockInfoVo.getPayApplyId();
        List<String> packCodes = lockInfoVo.getPackCodes();
        SearchInsuranceLockDto condition = new SearchInsuranceLockDto();
        condition.setOrgCode(lockInfoVo.getOrgCode());
        condition.setPackCodes(packCodes);
        condition.setLockMonth(lockInfoVo.getLockMonth());
        Byte payDetailType = lockInfoVo.getPayDetailType();
        List<PracticePayDetail> practicePayDetailVosByPayId = practicePayDetailService.getPracticePayDetailVosByPayId(payApplyId);
        Long operateTime = new Date().getTime();
        List<Long> perBillIds = practicePayDetailVosByPayId.stream().map(PracticePayDetail::getBillId).distinct().collect(Collectors.toList());
        /**新增福利包 和原来就选中的福利包*/
        if (payDetailType.equals(PracticePayMentTypeEnum.COLLECT_PAYMENT.getCode()) ||
                payDetailType.equals(PracticePayMentTypeEnum.REPLENISH_PAYMENT.getCode())) {
            /**以前payDetailType与报表的freeType 一起使用，*/
            condition.setPayDetailType(payDetailType);
        } else {
            condition.setPayDetailType(null);
        }
        List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getByPackCodes(condition);
        packPracticeLockInfoVo(lockInfoVoList);

        List<PracticePayDetail> allPayDetailVos = Lists.newArrayList();
        if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            List<PracticePayDetail> curr = practicePayDetailVosByPayId.stream().filter(vo -> packCodes.contains(vo.getPackCode())).collect(Collectors.toList());
            allPayDetailVos = getPracticePayDetails(lockInfoVo, lockInfoVoList, operateTime, loginName);
            allPayDetailVos.addAll(curr);
        } else {
            /**新增的福利包逻辑
             * ：如果已经支付过：直接查询没支付的数据
             * 没支付过： 得拉取报表的数据生成 支付数据
             * 没有新增福利包： 则直接拉取 福利包的未支付数据
             * */
            /**当前选中的福利包，和上次选中的福利包相同的部分： 直接查出数据*/
            Map<Boolean, List<PracticeLockInfoVo>> booleanListMap = lockInfoVoList.stream().collect(Collectors.partitioningBy(vo -> perBillIds.contains(vo.getId())));
            List<PracticeLockInfoVo> normalLockInfos = booleanListMap.getOrDefault(true, Lists.newArrayList());
            List<PracticeLockInfoVo> addLockInfos = booleanListMap.getOrDefault(false, Lists.newArrayList());

            if (CollectionUtils.isNotEmpty(normalLockInfos)) {
                List<Long> billIds = normalLockInfos.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList());
                List<PracticePayDetail> subList = practicePayDetailService.getPracticePayDetailVosByBillIds(billIds);
                subList = subList.stream().filter(vo -> vo.getAppStatus() == null || Objects.equals(vo.getPayApplyId(), payApplyId)).collect(Collectors.toList());
                allPayDetailVos.addAll(subList);
            }
            /**新选中的福利包:
             * 未支付过： 根据报表数据重新生成
             * 支付过： 查询出未支付部分的数据*/
            if (CollectionUtils.isNotEmpty(addLockInfos)) {
                List<SearchInsuranceLockDto> conditions = Lists.newArrayList();
                Map<String, PracticeLockInfoVo> billIdMap = getBillIdMap(addLockInfos, conditions);
                for (PracticeLockInfoVo addLockInfo : addLockInfos) {
                    /**未支付从报表拉取数据，*/
                    if (!PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode().equals(addLockInfo.getPayStatus())) {
                        List<PracticePayDetail> subList = practicePayDetailService.getPracticePayDetailVosByBillIds(Lists.newArrayList(addLockInfo.getId()));
                        subList = subList.stream().filter(vo -> vo.getPayApplyId() == null || payApplyId.equals(vo.getPayApplyId())).collect(Collectors.toList());
                        allPayDetailVos.addAll(subList);
                    } else {
                        SearchInsuranceLockDto conditionCopy = new SearchInsuranceLockDto();
                        BeanUtils.copyProperties(condition, conditionCopy);
                        conditionCopy.setPackCodes(Lists.newArrayList(addLockInfo.getPackCode()));
                        conditionCopy.setPayDetailType(addLockInfo.getPayDetailType().byteValue());
                        List<InsurancePracticeReportVo> subReports = insurancePracticeReportMapper.getInsurancePracticeReportBySearch(conditionCopy);
                        if (CollectionUtils.isNotEmpty(subReports)) {
                            /**先删 在增加 根据报表数据生成支付数据*/
                            Long billId = addLockInfo.getId();
                            practicePayDetailService.deletePracticePayDetailVosByBillIds(Lists.newArrayList(billId));
                            List<PracticePayDetail> subList = genDetails(subReports, billIdMap, new HashMap<>(), null, operateTime, loginName);
                            practicePayDetailService.insertPracticePayDetailVos(subList);
                            allPayDetailVos.addAll(subList);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(allPayDetailVos)) {
                List<Long> ids = allPayDetailVos.stream().map(PracticePayDetail::getId).collect(Collectors.toList());
                practicePayDetailService.updateOperateTimeById(ids, operateTime);
            }
        }
        /**注： 没有被再次选中福利包数据，在提交时候释放*/
        build(lockInfoVo, allPayDetailVos, payDetailType, dto);
        dto.setLockInfoVoList(lockInfoVoList);
        dto.setOperateTime(operateTime);
        if (!payDetailType.equals(PracticePayMentTypeEnum.EMP_PAY.getCode())) {
            saveDataToCache(dto, operateTime);
        }
        logger.info("======================执行完成======================");
        return dto;
    }

    private List<PracticePayDetail> getPracticePayDetails(SearchInsuranceLockDto lockInfoVo, List<PracticeLockInfoVo> lockInfoVoList, Long operateTime, String loginName) {
        List<PracticePayDetail> allPayDetailVos = Lists.newArrayList();
        List<String> packCodeList = lockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
        SearchInsuranceLockDto lockDto = new SearchInsuranceLockDto();
        lockDto.setPackCodes(packCodeList);
        lockDto.setOrgCode(lockInfoVo.getOrgCode());
        lockDto.setLockMonth(lockInfoVo.getLockMonth());
        lockDto.setLockStatus(PracticeBillLockStatusEnum.LOCK.getCode().intValue());
        /**还得过滤掉支付中，已经支付的*/
        List<Byte> status = Lists.newArrayList();
        status.add(PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode());
        lockDto.setPayStatusList(status);
        List<InsurancePracticeReportVo> subReports = insurancePracticeReportMapper.getInsurancePracticeReport(lockDto);
        if(CollectionUtils.isNotEmpty(subReports)){
            List<String> params = subReports.stream().map(vo ->vo.getPracticeId()+","+vo.getFeeType()).collect(Collectors.toList());
            practicePayDetailService.deletePracticePayDetailVosByParams(params,lockInfoVo.getLockMonth());
            Map<String, PracticeLockInfoVo> billIdMap = lockInfoVoList.stream().collect(Collectors.toMap(vo ->(vo.getPackCode() + vo.getLockMonth() + vo.getPayDetailType()), Function.identity()));
            /**根据报表数据生成支付数据*/
            allPayDetailVos = genDetails(subReports, billIdMap, new HashMap<>(), null, operateTime, loginName);
            insertDetails(allPayDetailVos,"detail");
        }
        return allPayDetailVos;
    }

    @Override
    public void updatePracticePayDetails(PracPaymentApplyVo paymentApplyVo) {
        Long payApplyVoId = paymentApplyVo.getId();
        String creator = paymentApplyVo.getCreator();
        SearchInsuranceLockDto searchInsuranceLockDto = paymentApplyVo.buildDto();
        List<PracticeLockInfoVo> practiceLockInfoVoList = paymentApplyVo.getPracticeLockInfoVoList();
        /***==========================处理payDetails======================================*/
        /**上次选中的Id*/
        List<PracticePayDetail> practicePayDetailVosByPayId = practicePayDetailService.getPracticePayDetailVosByPayId(payApplyVoId);
        List<Long> perDetailIds = practicePayDetailVosByPayId.stream().map(PracticePayDetail::getId).collect(Collectors.toList());
        /**获取上一次个账单的支付金额*/
        Map<Long, List<PracticePayDetail>> perBillIdsMap = practicePayDetailVosByPayId.stream().collect(Collectors.groupingBy(PracticePayDetail::getBillId));
        List<Long> perBillIds = Lists.newArrayList(perBillIdsMap.keySet());
        /**根据选中的数据获取支付详细数据*/
        List<Long> currDetailIds;
        List<Long> currBillIds;
        List<PracticePayDetail> currPayDetailVosByBillIds;
        Byte payDetailType = paymentApplyVo.getPayDetailType();
        if (paymentApplyVo.getIsSelectAmt()) {
            List<ProdPayAmtDto> prodPayAmtDtos = getPracticePayDetails(searchInsuranceLockDto,payDetailType);
            currDetailIds = prodPayAmtDtos.stream().flatMap(vo -> vo.getDetailIds().stream()).collect(Collectors.toList());
            currPayDetailVosByBillIds = QueryUtil.queryDataByStep(10, TimeUnit.SECONDS, 5000, currDetailIds, (params) -> practicePayDetailService.getPracticePayDetailVosByIds(params));
            currBillIds = currPayDetailVosByBillIds.stream().map(PracticePayDetail::getBillId).distinct().collect(Collectors.toList());
        } else {
            currDetailIds = new ArrayList<>(perDetailIds);
            currBillIds = new ArrayList<>(perBillIds);
            currPayDetailVosByBillIds = practicePayDetailVosByPayId;
        }
        /**在上一次 不在当前，被删除的
         * */
        List<Long> delIds = perDetailIds.stream().filter(vo -> !currDetailIds.contains(vo)).collect(Collectors.toList());
        List<Long> addIds = currDetailIds.stream().filter(vo -> !perDetailIds.contains(vo)).collect(Collectors.toList());
        /**还需要维护 insurance_practice_report_xx 与 individual_fee_lock*/
        if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            editReportAndIndividualFeeLockReject(payApplyVoId,practicePayDetailVosByPayId,currPayDetailVosByBillIds,creator,delIds,addIds);
        }
        if (CollectionUtils.isNotEmpty(currDetailIds)) {
            PracticePayDetailVo condition = new PracticePayDetailVo();
            condition.setIds(currDetailIds);
            condition.setPayApplyId(payApplyVoId);
            condition.setAppStatus(PaymentApplyProcessStatus.PENDING_APPROVAL.getCode());
            condition.setUpdater(paymentApplyVo.getCreator());
            condition.setUpdateTime(new Date());
            practicePayDetailService.updatePracticePayDetailVoByIds(condition);
        }
        /**修改details 的审批状态为null*/
        cancelDetails(paymentApplyVo, delIds);
        /**如果驳回重新提交，少选了某条lockInfo,且他也没有支付中，支付完成的数据**/
        List<Long> delBillIds = perBillIds.stream().filter(vo -> !currBillIds.contains(vo)).collect(Collectors.toList());
        List<Long> addBillIds = currBillIds.stream().filter(vo -> !perBillIds.contains(vo)).collect(Collectors.toList());

        Map<Long, List<PracticePayDetail>> currBillIdsMap = currPayDetailVosByBillIds.stream().collect(Collectors.groupingBy(PracticePayDetail::getBillId));
        if (CollectionUtils.isNotEmpty(delBillIds)) {
            /**修改实做账单的支付状态*/
            editPayStatus(creator, delBillIds);
        }

        /***==========================处理practice_bill======================================*/
        perBillIds.removeAll(currBillIds);
        if (CollectionUtils.isNotEmpty(perBillIds)) {
            List<Integer> perBillIdsInt = perBillIds.stream().map(Long::intValue).collect(Collectors.toList());
            List<PracticeLockInfoVo> byIds = practiceBillMapper.getByIds(perBillIdsInt);
            practiceLockInfoVoList.addAll(byIds);
        }
        Map<Long, PracticeLockInfoVo> allLockInfoVo = practiceLockInfoVoList.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, Function.identity()));
        Map<Long, BigDecimal> perBillAmtMap = getAmtByBillIds(perBillIdsMap);
        Map<Long, BigDecimal> currBillAmtMap = getAmtByBillIds(currBillIdsMap);
        Set<Long> allBillIds = Sets.newHashSet(perBillAmtMap.keySet());
        allBillIds.addAll(currBillAmtMap.keySet());
        for (Long billId : allBillIds) {
            PracticeLockInfoVo practiceLockInfoVo = allLockInfoVo.get(billId);
            BigDecimal perAmt = perBillAmtMap.getOrDefault(billId, BigDecimal.ZERO);
            BigDecimal currAmt = currBillAmtMap.getOrDefault(billId, BigDecimal.ZERO);
            BigDecimal diffAmt = currAmt.subtract(perAmt);
            BigDecimal frozenAmt = practiceLockInfoVo.getFrozenAmt();
            PracticeLockInfoVo vo = new PracticeLockInfoVo();
            vo.setFrozenAmt(frozenAmt.add(diffAmt));
            vo.setId(billId);
            if(!payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
                if (addBillIds.contains(billId) && BigDecimalUtil.greaterZero(frozenAmt.add(diffAmt))) {
                    vo.setPayStatus(PracticeLockInfoPayStatusEnum.IN_PAID.getCode());
                }
            }

            vo.setUpdater(creator);
            vo.setVersion(practiceLockInfoVo.getVersion());
            practiceBillMapper.updatePracticeLockInfoVAmt(vo);
        }

        /***==========================处理pack_amt_diff_monthly======================================*/
        editPackAmtDiffMonthly(paymentApplyVo);
        /***==========================处理practice_report_2_pay======================================*/
        List<PracticeReportToPay> practiceReportToPayList = new ArrayList<>();
        practiceReportToPayMapper.deletePracticeReportToPayByPayId(payApplyVoId);
        for (Long id : allBillIds) {
            PracticeReportToPay practiceReportToPay = new PracticeReportToPay();
            practiceReportToPay.setPayApplyId(paymentApplyVo.getId());
            practiceReportToPay.setPracticeLockId(id);
            practiceReportToPay.init(paymentApplyVo.getCreator(), new Date());
            practiceReportToPayList.add(practiceReportToPay);
        }
        practiceReportToPayMapper.insertList(practiceReportToPayList);
        logger.info("====================执行完成！=========================");
    }


    /**
     * 驳回提交维护 report 与individual_fee_lock表
     * @param preDetails
     * @param currDetail
     * @param creator
     * @param delBillIds
     * @param addBillIds
     */
    private void editReportAndIndividualFeeLockReject(Long payId,List<PracticePayDetail> preDetails, List<PracticePayDetail> currDetail, String creator,List<Long> delBillIds,List<Long> addBillIds) {
        if(CollectionUtils.isNotEmpty(delBillIds)){
            List<PracticePayDetail> detailVos = preDetails.stream().filter(vo -> delBillIds.contains(vo.getId())).collect(Collectors.toList());
            deleteReportAndIndividualFeeLock(detailVos,creator);
        }

        if(CollectionUtils.isNotEmpty(addBillIds)){
            List<PracticePayDetail> detailVos = currDetail.stream().filter(vo -> addBillIds.contains(vo.getId())).collect(Collectors.toList());
            editReportAndIndividualFeeLock(payId,detailVos,PracticeLockInfoPayStatusEnum.IN_PAID.getCode(),creator);
        }

    }


    private void editPackAmtDiffMonthly(PracPaymentApplyVo paymentApplyVo) {
        PackAmtDiffMonthly packAmtDiffMonthly = packAmtDiffMonthlyService.getPackAmtDiffMonthlyVoByPayId(paymentApplyVo.getId());
        BigDecimal payAmt = Optional.ofNullable(paymentApplyVo.getPayAmt()).orElse(BigDecimal.ZERO);
        BigDecimal applyAmt = Optional.ofNullable(paymentApplyVo.getApplyAmt()).orElse(BigDecimal.ZERO);
        BigDecimal diff = payAmt.subtract(applyAmt);
        if(BigDecimalUtil.equalsZero(diff) && packAmtDiffMonthly != null){
            packAmtDiffMonthlyService.deletePackAmtDiffMonthlyVo(paymentApplyVo.getId());
            return;
        }
        PackAmtDiffMonthlyVo vo = new PackAmtDiffMonthlyVo();
        /**需求： 支付多个福利包的时候，在差异记录在任意一个上面*/
        String packCode = paymentApplyVo.getPracticeLockInfoVoList().stream().map(PracticeLockInfoVo::getPackCode).findFirst().get();
        paymentApplyVo.setPackCode(packCode);
        vo.build(paymentApplyVo,diff);
        if(!BigDecimalUtil.equalsZero(diff) ){
            if(packAmtDiffMonthly != null){
                packAmtDiffMonthlyService.updatePackAmtDiffMonthlyVo(vo);
            } else {
                packAmtDiffMonthlyService.insertPackAmtDiffMonthlyVo(vo);
            }
        }
    }

    @Override
    public Boolean editPaymentApplyAndPrac(PaymentApplyVo paymentApplyVo) {
        /***==========================处理practice_bill======================================*/
        /**释放冻结金额*/
        Long payId = paymentApplyVo.getId();
        PaymentApplyVo paymentApply = paymentApplyService.selectByPrimaryKey(payId);
        String loginName = paymentApplyVo.getCreator();
        Byte payDetailType = paymentApply.getPayDetailType();
        List<PracticePayDetail> practicePayDetailVosByPayId = practicePayDetailService.getPracticePayDetailVosByPayId(payId);
        /**还需要维护 insurance_practice_report_xx 与 individual_fee_lock*/
        if(payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            deleteReportAndIndividualFeeLock(practicePayDetailVosByPayId,loginName);
        }
        Map<Long, List<PracticePayDetail>> perBillIdsMap = practicePayDetailVosByPayId.stream().collect(Collectors.groupingBy(PracticePayDetail::getBillId));
        List<Integer> ids = perBillIdsMap.keySet().stream().map(Long::intValue).collect(Collectors.toList());
        List<PracticeLockInfoVo> byIds = practiceBillMapper.getByIds(ids);
        Map<Long, PracticeLockInfoVo> lockInfoVoMap = byIds.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, Function.identity()));
        /**获取上一次个账单的支付金额*/
        for (Long billId : perBillIdsMap.keySet()) {
            PracticeLockInfoVo lockInfoVo = lockInfoVoMap.get(billId);
            BigDecimal totalAmt = perBillIdsMap.get(billId).stream().map(PracticePayDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if ((lockInfoVo.getFrozenAmt().subtract(totalAmt)).compareTo(BigDecimal.ZERO) < 0) {
                logger.info("账单Id:,{}的冻结金额，在payId,{}中不够扣除，请排查原因！ ", billId, payId);
                throw new RuntimeException("冻结金额修改为负，请查看原因！");
            }
            BigDecimal frozenAmt = lockInfoVo.getFrozenAmt();
            PracticeLockInfoVo vo = new PracticeLockInfoVo();
            vo.setFrozenAmt(frozenAmt.subtract(totalAmt));
            vo.setId(billId);
            vo.setUpdater(loginName);
            practiceBillMapper.updatePracticeLockInfoVAmt(vo);
        }
        List<Long> detailIds = practicePayDetailVosByPayId.stream().map(PracticePayDetail::getId).collect(Collectors.toList());
        /**修改details 的审批状态为null*/
        cancelDetails(paymentApplyVo, detailIds);
        /**如果第一次支付被终止，修改支付状态*/
        List<Long> billIds = Lists.newArrayList(perBillIdsMap.keySet());
        /**修改实做账单的支付状态*/
        if(!payDetailType.equals(PracticePayMentTypeEnum.INDIVIDUAL_LOCK.getCode())){
            editPayStatus(loginName, billIds);
        }
        paymentApplyService.updateByPrimaryKeySelective(paymentApplyVo);
        packAmtDiffMonthlyService.deletePackAmtDiffMonthlyVo(payId);
        return true;
    }

    @Override
    public List<PracticePayDetailVo> getPracticePayDetailVosByPayId(Long payId) {
        return VoUtil.copyProperties(practicePayDetailService.getPracticePayDetailVosByPayId(payId), PracticePayDetailVo.class);
    }

//    @Override
//    public List<PracticeReportPaymentDetailVo> exportPracReportPayDetail(PracReportPayDetailParams detailParams) {
//        List<PracticeReportPaymentDetailVo> result = Lists.newArrayList();
//        /**根据参数获取orgCode*/
//        String orgCode = detailParams.getOrgCode();
//        String cityCode = detailParams.getCityCode();
//        String area = detailParams.getArea();
//        String areaName = null;
//        if (StringUtil.isNotBlank(area)){
//            OrgVo orgById = orgnizationResourceWrapperService.findOrgById(area);
//            areaName = orgById.getOrgName();
//        }
//        Integer singleFlag = detailParams.getSingleFlag();
//        List<String> allOrgCode = Lists.newArrayList();
//        List<OrgVo> allOrgVo = Lists.newArrayList();
//        if (StringUtil.isNotBlank(orgCode)) {
//            allOrgCode.add(orgCode);
//            OrgVo orgVo = orgnizationResourceWrapperService.findOrgById(orgCode);
//            allOrgVo.add(orgVo);
//        } else if (StringUtils.isNotBlank(cityCode)) {
//            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionCompanyByName(null, cityCode);
//            List<String> collect = orgVos.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
//            allOrgCode.addAll(collect);
//            allOrgVo.addAll(orgVos);
//        } else {
//            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionCompanyByName(null, area);
//            List<String> collect = orgVos.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
//            allOrgCode.addAll(collect);
//            allOrgVo.addAll(orgVos);
//        }
//
//        Map<String, String> orgVoMap = allOrgVo.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
////        PracticePayDetailVo condition = new PracticePayDetailVo();
////        condition.setOrgCodes(allOrgCode);
////        condition.setReportMonth(detailParams.getReportMonth());
//        List<InsurancePackVo> insurancePackVoList = insurancePackResourceWrapperService.getAllInsuPackByOrgCodeList(allOrgCode,null);
//        Map<Integer, List<InsurancePackVo>> singFlagMap = insurancePackVoList.stream().filter(vo -> vo.getSingleFlag() != null).collect(Collectors.groupingBy(InsurancePackVo::getSingleFlag));
//        Map<String, Integer> cityCodeMap = insurancePackVoList.stream().collect(Collectors.toMap(InsurancePackVo::getPackCode, InsurancePackVo::getCity));
//        List<AreaVo> cityInfo = iAreaResourceWrapperService.findCityInfo();
//        Map<Integer, String> cityInfoMap = cityInfo.stream().collect(Collectors.toMap(AreaVo::getCode, AreaVo::getName, (a, b) -> a));
//        /**单立户大部分是不需要支付的，只能取实做报表中的数据*/
//        List<String> singleAccountPacks = Lists.newArrayList();
//        Map<String, List<InsurancePackVo>> orgCodeAndPackMap = Maps.newHashMap();
//        List<InsurancePackVo> data = singFlagMap.getOrDefault(SingleFlagEnum.SINGLE_FLAG_BIG.getCode(),Lists.newArrayList());
//        List<String> bigAccountPacks = data.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
//        Map<String, List<SingleAccountRelativeVo>> singFlagPackMap = Maps.newHashMap();
//        Map<String, SingleAccountRelativeVo> singFlagRelativeMap;
//        if(!(singleFlag != null && singleFlag.equals(SingleFlagEnum.SINGLE_FLAG_BIG.getCode()))){
//            List<InsurancePackVo> singFlagPackVos = singFlagMap.getOrDefault(SingleFlagEnum.SINGLE_FLAG_SINGLE.getCode(),Lists.newArrayList());
//            singleAccountPacks = singFlagPackVos.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
//            orgCodeAndPackMap = singFlagPackVos.stream().collect(Collectors.groupingBy(InsurancePackVo::getOrgCode));
//            List<SingleAccountRelativeVo> relatives = pracSingleAccountWrapperService.getSingleAccountRelatives(singleAccountPacks);
//            singFlagPackMap = relatives.stream().collect(Collectors.groupingBy(SingleAccountRelativeVo::getSinAccName));
//            singFlagRelativeMap = relatives.stream().collect(Collectors.toMap(SingleAccountRelativeVo::getRelativeNo,Function.identity()));
//        } else {
//            singFlagRelativeMap = Maps.newHashMap();
//        }
//     /*   if(CollectionUtils.isNotEmpty(bigAccountPacks)){
//            condition.setPackCodes(bigAccountPacks);
//            List<PracticePayDetailVo> practicePayDetailsByCondition = practicePayDetailService.getPracticePayDetailsByCondition(condition);
//            *//**过滤单立户选项*//*
//            List<String> orgCodes = practicePayDetailsByCondition.stream().map(PracticePayDetailVo::getOrgCode).distinct().filter(vo -> !allOrgCode.contains(vo)).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(orgCodes)){
//                List<OrgVo> codeList = orgnizationResourceWrapperService.getOrgListByOrgCodeList(orgCodes);
//                codeList.forEach(vo -> orgVoMap.put(vo.getOrgCode(),vo.getOrgName()));
//            }
//
//            Map<String, List<PracticePayDetailVo>> detailMap = practicePayDetailsByCondition.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getOrgCode));
//            PracticeReportPaymentDetailVo detailVo;
//            ProdPayAmtDto prodPayAmtDto;
//            for (String orgCodeFormMap : detailMap.keySet()) {
//                detailVo = new PracticeReportPaymentDetailVo();
//                BigDecimal totalAmt = BigDecimal.ZERO;
//                List<PracticePayDetailVo> detailVos = detailMap.get(orgCodeFormMap);
//                Map<Integer, List<PracticePayDetailVo>> allProdMap = detailVos.stream().collect(Collectors.groupingBy(PracticePayDetailVo::getProdCode));
//                for (Integer prodCode : allProdMap.keySet()) {
//                    prodPayAmtDto = new ProdPayAmtDto();
//                    List<PracticePayDetailVo> subDetailVos = allProdMap.get(prodCode);
//                    BigDecimal prodTotalAmt = subDetailVos.stream().map(PracticePayDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    totalAmt = totalAmt.add(prodTotalAmt);
//                    long count = subDetailVos.stream().map(PracticePayDetailVo::getPracticeId).count();
//                    prodPayAmtDto.setProdCode(prodCode);
//                    prodPayAmtDto.setProdCodeName(InsuranceRatioEnum.ProductCode.getName(prodCode.byteValue()));
//                    prodPayAmtDto.setTotalAmt(prodTotalAmt);
//                    prodPayAmtDto.setEmpNum(count);
//                    Map<Byte,ProdPayAmtDto> prodPayAmtDtos;
//                    if (MapUtils.isEmpty(detailVo.getProdPayAmtDtos())) {
//                        prodPayAmtDtos = Maps.newHashMap();
//                        PracticePayDetailVo firstData = subDetailVos.get(0);
////                        detailVo.setAppCom(firstData.getAppCom());
////                        detailVo.setAppComName(orgVoMap.get(firstData.getAppCom()));
//                        detailVo.setOrgCode(firstData.getOrgCode());
//                        detailVo.setOrgName(orgVoMap.get(firstData.getOrgCode()));
//                        detailVo.setSingFlagStr(SingleFlagEnum.SINGLE_FLAG_BIG.getName());
//                        Integer currCityCode = cityCodeMap.get(firstData.getPackCode());
//                        detailVo.setCityName(cityInfoMap.getOrDefault(currCityCode,""));
//                    } else {
//                        prodPayAmtDtos = detailVo.getProdPayAmtDtos();
//                    }
//                    prodPayAmtDtos.put(prodCode.byteValue(),prodPayAmtDto);
//                    detailVo.setProdPayAmtDtos(prodPayAmtDtos);
//                }
//                detailVo.setTotalAmt(totalAmt);
//                result.add(detailVo);
//            }
//        }*/
//
//        SearchInsuranceLockDto lockDto = new SearchInsuranceLockDto();
//        lockDto.setLockStatusList(Lists.newArrayList(PracticeBillLockStatusEnum.LOCK.getCode()));
//        lockDto.setLockMonth(detailParams.getReportMonth());
//
//        if(CollectionUtils.isNotEmpty(bigAccountPacks)){
//
//            lockDto.setPackCodes(bigAccountPacks);
//
//            List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getBySearch(lockDto);
//            if(CollectionUtils.isNotEmpty(lockInfoVoList)){
//                List<String> packCodes = lockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
//                lockDto.setPackCodes(packCodes);
//                lockDto.setLockMonth(detailParams.getReportMonth());
//                List<InsurancePracticeReportVo> reportVoList = insurancePracticeReportMapper.getInsurancePracticeReportByCondition(lockDto);
//                Map<String, List<InsurancePracticeReportVo>> detailMap = reportVoList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getOrgCode));
//                PracticeReportPaymentDetailVo detailVo;
//                ProdPayAmtDto prodPayAmtDto;
//                for (String orgCodeFormMap : detailMap.keySet()) {
//                    if (detailParams.getSingleFlag()!= null&&detailParams.getSingleFlag().equals(SingleFlagEnum.SINGLE_FLAG_SINGLE.getCode())){
//                        continue;
//                    }
//                    detailVo = new PracticeReportPaymentDetailVo();
//                    BigDecimal totalAmt = BigDecimal.ZERO;
//                    List<InsurancePracticeReportVo> detailVos = detailMap.get(orgCodeFormMap);
//                    Map<Byte, List<InsurancePracticeReportVo>> allProdMap = detailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getProdCode));
//                    for (Byte prodCode : allProdMap.keySet()) {
//                        prodPayAmtDto = new ProdPayAmtDto();
//                        List<InsurancePracticeReportVo> subDetailVos = allProdMap.get(prodCode);
//                        BigDecimal prodTotalAmt = subDetailVos.stream().map(InsurancePracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        totalAmt = totalAmt.add(prodTotalAmt);
//                        long count = subDetailVos.stream().map(InsurancePracticeReportVo::getPracticeId).count();
//                        prodPayAmtDto.setProdCode(Integer.valueOf(prodCode));
//                        prodPayAmtDto.setProdCodeName(InsuranceRatioEnum.ProductCode.getName(prodCode));
//                        prodPayAmtDto.setTotalAmt(prodTotalAmt);
//                        prodPayAmtDto.setEmpNum(count);
//                        Map<Byte,ProdPayAmtDto> prodPayAmtDtos;
//                        if (MapUtils.isEmpty(detailVo.getProdPayAmtDtos())) {
//                            prodPayAmtDtos = Maps.newHashMap();
//                            InsurancePracticeReportVo firstData = subDetailVos.get(0);
//                            detailVo.setOrgCode(firstData.getOrgCode());
//                            detailVo.setOrgName(orgVoMap.get(firstData.getOrgCode()));
//                            detailVo.setSingFlagStr(SingleFlagEnum.SINGLE_FLAG_BIG.getName());
//                            Integer currCityCode = cityCodeMap.get(firstData.getPackCode());
//                            detailVo.setBillReportMonth(detailParams.getReportMonth());
//                            detailVo.setAreaName(areaName);
//                            if (area!=null){
//
//                            }
//                            detailVo.setCityName(cityInfoMap.getOrDefault(currCityCode,""));
//                        } else {
//                            prodPayAmtDtos = detailVo.getProdPayAmtDtos();
//                        }
//                        prodPayAmtDtos.put(prodCode,prodPayAmtDto);
//                        detailVo.setProdPayAmtDtos(prodPayAmtDtos);
//                    }
//                    detailVo.setTotalAmt(totalAmt);
//                    result.add(detailVo);
//                }
//            }
//        }
//        if(CollectionUtils.isNotEmpty(singleAccountPacks)){
//            /**拉取这些福利包这个月已经锁定的数据*/
//
//            lockDto.setPackCodes(singleAccountPacks);
//
//            List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getBySearch(lockDto);
//            if(CollectionUtils.isNotEmpty(lockInfoVoList)){
//                List<String> packCodes = lockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
//                lockDto.setPackCodes(packCodes);
//                lockDto.setLockMonth(detailParams.getReportMonth());
//                List<InsurancePracticeReportVo> reportVoList = insurancePracticeReportMapper.getInsurancePracticeReportByCondition(lockDto);
//                Map<String, List<InsurancePracticeReportVo>> detailMap = reportVoList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getOrgCode));
//                PracticeReportPaymentDetailVo detailVo;
//                ProdPayAmtDto prodPayAmtDto;
//                for (String orgCodeFormMap : detailMap.keySet()) {
//                    /**找到org 下面的全部福利包的单立户，根据单立户进行生成*/
//                    List<InsurancePackVo> insurancePackVos = orgCodeAndPackMap.getOrDefault(orgCodeFormMap,Lists.newArrayList());
//                    insurancePackVos = insurancePackVos.stream().filter(vo -> packCodes.contains(vo.getPackCode())).collect(Collectors.toList());
//                    List<String> packCodeByOrg = insurancePackVos.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
//                    /**获取福利包对应几个单立户*/
//                    List<SingleAccountRelativeVo> allSingles = insurancePackVos.stream().map(vo -> singFlagRelativeMap.get(vo.getPackCode())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//                    List<String> singleNames = allSingles.stream().map(SingleAccountRelativeVo::getSinAccName).distinct().collect(Collectors.toList());
//                    /**获取org 下面全部的详细**/
//                    List<InsurancePracticeReportVo> allOrgDetailVos = detailMap.getOrDefault(orgCodeFormMap,Lists.newArrayList());
//                    Map<String, List<InsurancePracticeReportVo>> subReportMap = allOrgDetailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getPackCode));
//                    for (String singleName : singleNames) {
//                        detailVo = new PracticeReportPaymentDetailVo();
//                        BigDecimal totalAmt = BigDecimal.ZERO;
//                        List<SingleAccountRelativeVo> packCodeBySingle = singFlagPackMap.get(singleName).stream().filter(vo -> packCodeByOrg.contains(vo.getRelativeNo())).collect(Collectors.toList());
//                        List<InsurancePracticeReportVo> detailVos = packCodeBySingle.stream().flatMap(vo -> subReportMap.getOrDefault(vo.getRelativeNo(), Lists.newArrayList()).stream()).collect(Collectors.toList());
//                        Map<Byte, List<InsurancePracticeReportVo>> allProdMap = detailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getProdCode));
//                        for (Byte prodCode : allProdMap.keySet()) {
//                            prodPayAmtDto = new ProdPayAmtDto();
//                            List<InsurancePracticeReportVo> subDetailVos = allProdMap.get(prodCode);
//                            BigDecimal prodTotalAmt = subDetailVos.stream().map(InsurancePracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            totalAmt = totalAmt.add(prodTotalAmt);
//                            long count = subDetailVos.stream().map(InsurancePracticeReportVo::getPracticeId).count();
//                            prodPayAmtDto.setProdCode(prodCode.intValue());
//                            prodPayAmtDto.setProdCodeName(InsuranceRatioEnum.ProductCode.getName(prodCode));
//                            prodPayAmtDto.setTotalAmt(prodTotalAmt);
//                            prodPayAmtDto.setEmpNum(count);
//                            Map<Byte,ProdPayAmtDto> prodPayAmtDtos;
//                            if (MapUtils.isEmpty(detailVo.getProdPayAmtDtos())) {
//                                prodPayAmtDtos = Maps.newHashMap();
//                                InsurancePracticeReportVo firstData = subDetailVos.get(0);
//                                detailVo.setOrgCode(firstData.getOrgCode());
//                                detailVo.setOrgName(orgVoMap.get(firstData.getOrgCode()));
//                                detailVo.setSingFlagStr(SingleFlagEnum.SINGLE_FLAG_SINGLE.getName());
//                                Integer currCityCode = cityCodeMap.get(firstData.getPackCode());
//                                detailVo.setCityName(cityInfoMap.getOrDefault(currCityCode,""));
//                                detailVo.setSingFlagName(singleName);
//                                detailVo.setBillReportMonth(detailParams.getReportMonth());
//                                detailVo.setAreaName(areaName);
//                            } else {
//                                prodPayAmtDtos = detailVo.getProdPayAmtDtos();
//                            }
//                            prodPayAmtDtos.put(prodCode,prodPayAmtDto);
//                            detailVo.setProdPayAmtDtos(prodPayAmtDtos);
//                        }
//                        detailVo.setTotalAmt(totalAmt);
//                        result.add(detailVo);
//                    }
//                }
//            }
//        }
//
//        return result;
//    }

    @Override
    public List<PracticeReportPaymentDetailVo> exportPracReportPayDetail(PracReportPayDetailParams detailParams) {
        List<PracticeReportPaymentDetailVo> result = Lists.newArrayList();
        /**根据参数获取orgCode*/
        String orgCode = detailParams.getOrgCode();
        String cityCode = detailParams.getCityCode();
        String area = detailParams.getArea();
        String areaName = null;
        if (StringUtil.isNotBlank(area)){
            OrgVo orgById = orgnizationResourceWrapperService.findOrgById(area);
            areaName = orgById.getOrgName();
        }
        Integer singleFlag = detailParams.getSingleFlag();
        List<String> allOrgCode = Lists.newArrayList();
        List<OrgVo> allOrgVo = Lists.newArrayList();
        if (StringUtil.isNotBlank(orgCode)) {
            allOrgCode.add(orgCode);
            OrgVo orgVo = orgnizationResourceWrapperService.findOrgById(orgCode);
            allOrgVo.add(orgVo);
        } else if (StringUtils.isNotBlank(cityCode)) {
            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionCompanyByName(null, cityCode);
            List<String> collect = orgVos.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
            allOrgCode.addAll(collect);
            allOrgVo.addAll(orgVos);
        } else {
            List<OrgVo> orgVos = orgnizationResourceWrapperService.findRegionCompanyByName(null, area);
            List<String> collect = orgVos.stream().map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
            allOrgCode.addAll(collect);
            allOrgVo.addAll(orgVos);
        }

        Map<String, String> orgVoMap = allOrgVo.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));

        List<InsurancePackVo> insurancePackVoList = insurancePackResourceWrapperService.getAllInsuPackByOrgCodeList(allOrgCode,null);
        Map<Integer, List<InsurancePackVo>> singFlagMap = insurancePackVoList.stream().filter(vo -> vo.getSingleFlag() != null).collect(Collectors.groupingBy(InsurancePackVo::getSingleFlag));
        Map<String, Integer> cityCodeMap = insurancePackVoList.stream().collect(Collectors.toMap(InsurancePackVo::getPackCode, InsurancePackVo::getCity));
        List<AreaVo> cityInfo = iAreaResourceWrapperService.findCityInfo();
        Map<Integer, String> cityInfoMap = cityInfo.stream().collect(Collectors.toMap(AreaVo::getCode, AreaVo::getName, (a, b) -> a));
        /**单立户大部分是不需要支付的，只能取实做报表中的数据*/
        List<String> singleAccountPacks = Lists.newArrayList();
        Map<String, List<InsurancePackVo>> orgCodeAndPackMap = Maps.newHashMap();
        List<InsurancePackVo> data = singFlagMap.getOrDefault(SingleFlagEnum.SINGLE_FLAG_BIG.getCode(),Lists.newArrayList());
        List<String> bigAccountPacks = data.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
        Map<String, List<SingleAccountRelativeVo>> singFlagPackMap = Maps.newHashMap();
        Map<String, SingleAccountRelativeVo> singFlagRelativeMap;
        if(!(singleFlag != null && singleFlag.equals(SingleFlagEnum.SINGLE_FLAG_BIG.getCode()))){
            List<InsurancePackVo> singFlagPackVos = singFlagMap.getOrDefault(SingleFlagEnum.SINGLE_FLAG_SINGLE.getCode(),Lists.newArrayList());
            singleAccountPacks = singFlagPackVos.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
            orgCodeAndPackMap = singFlagPackVos.stream().collect(Collectors.groupingBy(InsurancePackVo::getOrgCode));
            List<SingleAccountRelativeVo> relatives = pracSingleAccountWrapperService.getSingleAccountRelatives(singleAccountPacks);
            singFlagPackMap = relatives.stream().collect(Collectors.groupingBy(SingleAccountRelativeVo::getSinAccName));
            singFlagRelativeMap = relatives.stream().collect(Collectors.toMap(SingleAccountRelativeVo::getRelativeNo,Function.identity()));
        } else {
            singFlagRelativeMap = Maps.newHashMap();
        }
        SearchInsuranceLockDto lockDto = new SearchInsuranceLockDto();
        lockDto.setLockStatusList(Lists.newArrayList(PracticeBillLockStatusEnum.LOCK.getCode()));
        lockDto.setProductCode(detailParams.getProductCode()==null?null:detailParams.getProductCode());
        lockDto.setLockTimeStart(StringUtils.isBlank(detailParams.getLockMonthStart())?null:detailParams.getLockMonthStart());
        lockDto.setLockTimeEnd(StringUtils.isBlank(detailParams.getLockMonthEnd())?null:detailParams.getLockMonthEnd());
        for (Integer month : detailParams.getMonthList()) {

            lockDto.setLockMonth(month);

            if(CollectionUtils.isNotEmpty(bigAccountPacks)){

                lockDto.setPackCodes(bigAccountPacks);

                List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getBySearch(lockDto);
                if(CollectionUtils.isNotEmpty(lockInfoVoList)){
                    Map<String, Date> latestLockDateMap = lockInfoVoList.stream()
                            .collect(Collectors.groupingBy(
                                    PracticeLockInfoVo::getOrgCode,
                                    Collectors.collectingAndThen(
                                            Collectors.maxBy(Comparator.comparing(PracticeLockInfoVo::getLockTime)),
                                            optional -> optional.map(PracticeLockInfoVo::getLockTime).orElse(null)
                                    )
                            ));
                    List<String> packCodes = lockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
                    lockDto.setPackCodes(packCodes);
                    List<InsurancePracticeReportVo> reportVoList = insurancePracticeReportMapper.getInsurancePracticeReportByConditionToReport(lockDto);
                    Map<String, List<InsurancePracticeReportVo>> detailMap = reportVoList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getOrgCode));
                    PracticeReportPaymentDetailVo detailVo;
                    ProdPayAmtDto prodPayAmtDto;
                    for (String orgCodeFormMap : detailMap.keySet()) {
                        if (detailParams.getSingleFlag()!= null&&detailParams.getSingleFlag().equals(SingleFlagEnum.SINGLE_FLAG_SINGLE.getCode())){
                            continue;
                        }
                        detailVo = new PracticeReportPaymentDetailVo();
                        detailVo.setLockTime(latestLockDateMap.get(orgCodeFormMap));
                        BigDecimal totalAmt = BigDecimal.ZERO;
                        List<InsurancePracticeReportVo> detailVos = detailMap.get(orgCodeFormMap);
                        Map<Byte, List<InsurancePracticeReportVo>> allProdMap = detailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getProdCode));
                        for (Byte prodCode : allProdMap.keySet()) {
                            prodPayAmtDto = new ProdPayAmtDto();
                            List<InsurancePracticeReportVo> subDetailVos = allProdMap.get(prodCode);
                            BigDecimal prodTotalAmt = subDetailVos.stream().map(InsurancePracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                            totalAmt = totalAmt.add(prodTotalAmt);
                            long count = subDetailVos.stream().map(InsurancePracticeReportVo::getPracticeId).count();
                            prodPayAmtDto.setProdCode(Integer.valueOf(prodCode));
                            prodPayAmtDto.setProdCodeName(InsuranceRatioEnum.ProductCode.getName(prodCode));
                            prodPayAmtDto.setTotalAmt(prodTotalAmt);
                            prodPayAmtDto.setEmpNum(count);
                            Map<Byte,ProdPayAmtDto> prodPayAmtDtos;
                            if (MapUtils.isEmpty(detailVo.getProdPayAmtDtos())) {
                                prodPayAmtDtos = Maps.newHashMap();
                                InsurancePracticeReportVo firstData = subDetailVos.get(0);
                                detailVo.setOrgCode(firstData.getOrgCode());
                                detailVo.setOrgName(orgVoMap.get(firstData.getOrgCode()));
                                detailVo.setSingFlagStr(SingleFlagEnum.SINGLE_FLAG_BIG.getName());
                                Integer currCityCode = cityCodeMap.get(firstData.getPackCode());
                                detailVo.setBillReportMonth(month);
                                detailVo.setAreaName(areaName);
                                if (area!=null){

                                }
                                detailVo.setCityName(cityInfoMap.getOrDefault(currCityCode,""));
                            } else {
                                prodPayAmtDtos = detailVo.getProdPayAmtDtos();
                            }
                            prodPayAmtDtos.put(prodCode,prodPayAmtDto);
                            detailVo.setProdPayAmtDtos(prodPayAmtDtos);
                        }
                        detailVo.setTotalAmt(totalAmt);
                        result.add(detailVo);
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(singleAccountPacks)){
                /**拉取这些福利包这个月已经锁定的数据*/

                lockDto.setPackCodes(singleAccountPacks);

                List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getBySearch(lockDto);
                if(CollectionUtils.isNotEmpty(lockInfoVoList)){
                    Map<String, Date> latestLockDateMap = lockInfoVoList.stream()
                            .collect(Collectors.groupingBy(
                                    PracticeLockInfoVo::getOrgCode,
                                    Collectors.collectingAndThen(
                                            Collectors.maxBy(Comparator.comparing(PracticeLockInfoVo::getLockTime)),
                                            optional -> optional.map(PracticeLockInfoVo::getLockTime).orElse(null)
                                    )
                            ));
                    List<String> packCodes = lockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
                    lockDto.setPackCodes(packCodes);
                    List<InsurancePracticeReportVo> reportVoList = insurancePracticeReportMapper.getInsurancePracticeReportByConditionToReport(lockDto);
                    Map<String, List<InsurancePracticeReportVo>> detailMap = reportVoList.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getOrgCode));
                    PracticeReportPaymentDetailVo detailVo;
                    ProdPayAmtDto prodPayAmtDto;
                    for (String orgCodeFormMap : detailMap.keySet()) {
                        /**找到org 下面的全部福利包的单立户，根据单立户进行生成*/
                        List<InsurancePackVo> insurancePackVos = orgCodeAndPackMap.getOrDefault(orgCodeFormMap,Lists.newArrayList());
                        insurancePackVos = insurancePackVos.stream().filter(vo -> packCodes.contains(vo.getPackCode())).collect(Collectors.toList());
                        List<String> packCodeByOrg = insurancePackVos.stream().map(InsurancePackVo::getPackCode).distinct().collect(Collectors.toList());
                        /**获取福利包对应几个单立户*/
                        List<SingleAccountRelativeVo> allSingles = insurancePackVos.stream().map(vo -> singFlagRelativeMap.get(vo.getPackCode())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        List<String> singleNames = allSingles.stream().map(SingleAccountRelativeVo::getSinAccName).distinct().collect(Collectors.toList());
                        /**获取org 下面全部的详细**/
                        List<InsurancePracticeReportVo> allOrgDetailVos = detailMap.getOrDefault(orgCodeFormMap,Lists.newArrayList());
                        Map<String, List<InsurancePracticeReportVo>> subReportMap = allOrgDetailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getPackCode));
                        for (String singleName : singleNames) {
                            detailVo = new PracticeReportPaymentDetailVo();
                            detailVo.setLockTime(latestLockDateMap.get(orgCodeFormMap));
                            BigDecimal totalAmt = BigDecimal.ZERO;
                            List<SingleAccountRelativeVo> packCodeBySingle = singFlagPackMap.get(singleName).stream().filter(vo -> packCodeByOrg.contains(vo.getRelativeNo())).collect(Collectors.toList());
                            List<InsurancePracticeReportVo> detailVos = packCodeBySingle.stream().flatMap(vo -> subReportMap.getOrDefault(vo.getRelativeNo(), Lists.newArrayList()).stream()).collect(Collectors.toList());
                            Map<Byte, List<InsurancePracticeReportVo>> allProdMap = detailVos.stream().collect(Collectors.groupingBy(InsurancePracticeReportVo::getProdCode));
                            for (Byte prodCode : allProdMap.keySet()) {
                                prodPayAmtDto = new ProdPayAmtDto();
                                List<InsurancePracticeReportVo> subDetailVos = allProdMap.get(prodCode);
                                BigDecimal prodTotalAmt = subDetailVos.stream().map(InsurancePracticeReportVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                                totalAmt = totalAmt.add(prodTotalAmt);
                                long count = subDetailVos.stream().map(InsurancePracticeReportVo::getPracticeId).count();
                                prodPayAmtDto.setProdCode(prodCode.intValue());
                                prodPayAmtDto.setProdCodeName(InsuranceRatioEnum.ProductCode.getName(prodCode));
                                prodPayAmtDto.setTotalAmt(prodTotalAmt);
                                prodPayAmtDto.setEmpNum(count);
                                Map<Byte,ProdPayAmtDto> prodPayAmtDtos;
                                if (MapUtils.isEmpty(detailVo.getProdPayAmtDtos())) {
                                    prodPayAmtDtos = Maps.newHashMap();
                                    InsurancePracticeReportVo firstData = subDetailVos.get(0);
                                    detailVo.setOrgCode(firstData.getOrgCode());
                                    detailVo.setOrgName(orgVoMap.get(firstData.getOrgCode()));
                                    detailVo.setSingFlagStr(SingleFlagEnum.SINGLE_FLAG_SINGLE.getName());
                                    Integer currCityCode = cityCodeMap.get(firstData.getPackCode());
                                    detailVo.setCityName(cityInfoMap.getOrDefault(currCityCode,""));
                                    detailVo.setSingFlagName(singleName);
                                    detailVo.setBillReportMonth(month);
                                    detailVo.setAreaName(areaName);
                                } else {
                                    prodPayAmtDtos = detailVo.getProdPayAmtDtos();
                                }
                                prodPayAmtDtos.put(prodCode,prodPayAmtDto);
                                detailVo.setProdPayAmtDtos(prodPayAmtDtos);
                            }
                            detailVo.setTotalAmt(totalAmt);
                            result.add(detailVo);
                        }
                    }
                }
            }
        }
        return result;

    }

    @Override
    public List<PracticeLockInfoVo> getLockInfosByPayId(Long id) {
        List<Long> lockIdByPaymentApplyId = practiceReportToPayMapper.getPracticeLockIdByPaymentApplyId(id);
        List<Integer> collect = lockIdByPaymentApplyId.stream().map(Long::intValue).collect(Collectors.toList());
        return practiceBillMapper.getByIds(collect);
    }

    @Override
    public List<InsurancePracticeReportVo> getIndividualFee(SearchReportDto lockInfoVo) {
        String orderNo = lockInfoVo.getOrderNo();
        if(StringUtils.isNotBlank(orderNo)){
            String[] split = orderNo.split(",");
            lockInfoVo.setOrderNos(Arrays.asList(split));
        }

        List<InsurancePracticeReportVo> insurancePracticeReportVos = insurancePracticeReportMapper.getIndividualFee(lockInfoVo);
        if(CollectionUtils.isEmpty(insurancePracticeReportVos)){
            return insurancePracticeReportVos;
        }
        if (StringUtils.isNotBlank(lockInfoVo.getInsuranceRatioCode())){
            Set<String> orderNoSet = insurancePracticeReportVos.stream().map(InsurancePracticeReportVo::getOrderNo).collect(Collectors.toSet());
            Set<Integer> prodCodeSet = insurancePracticeReportVos.stream()
                    .map(vo -> (int) vo.getProdCode())
                    .collect(Collectors.toSet());
            List<String> orderNoParam = iInsurancePracticeWrapperService.selectOrderNoByOrderNoSetAndRatioCode(orderNoSet, lockInfoVo.getInsuranceRatioCode(), lockInfoVo.getLockMonth(), prodCodeSet);
            if (CollectionUtils.isEmpty(orderNoParam)){
                return Collections.emptyList();
            }
            insurancePracticeReportVos =  insurancePracticeReportVos.stream().filter(vo -> orderNoParam.contains(vo.getOrderNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(insurancePracticeReportVos)){
                return insurancePracticeReportVos;
            }
        }

            List<String> orderNos = insurancePracticeReportVos.stream().map(InsurancePracticeReportVo::getOrderNo).distinct().collect(Collectors.toList());

            Map<String, EmployeeOrderVo> employeeOrderVoMap = employeeOrderWrapperService.getCertNoByOrderNos(orderNos);
            /**在individual_fee_lock中存在数据就表示被锁定*/
            insurancePracticeReportVos.forEach(vo ->{
                if(employeeOrderVoMap.containsKey(vo.getOrderNo())){
                    EmployeeOrderVo orderVo = employeeOrderVoMap.get(vo.getOrderNo());
                    vo.setEmpName(orderVo.getEmployeeName());
                    vo.setCertNo(orderVo.getCertNo());
                    if (vo.getLockStatus() != null){
                        vo.setLockStatusStr(PracticeBillLockStatusEnum.getMsgByCode(vo.getLockStatus().byteValue()));
                    } else {
                        vo.setLockStatus(PracticeBillLockStatusEnum.UNLOCKED.getCode().intValue());
                        vo.setLockStatusStr(PracticeBillLockStatusEnum.UNLOCKED.getMsg());
                    }
                    if (vo.getPayStatus() != null){
                        vo.setPayStatusStr(PracticeLockInfoPayStatusEnum.getMsgByCode(vo.getPayStatus().byteValue()));
                    } else {
                        vo.setPayStatus(PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode().intValue());
                        vo.setPayStatusStr(PracticeLockInfoPayStatusEnum.NON_PAYMENT.getMsg());
                    }
                }
            });


        insurancePracticeReportVos = insurancePracticeReportVos.stream().sorted(Comparator.comparing(InsurancePracticeReportVo::getOrderNo)).collect(Collectors.toList());
        if (StringUtils.isNotBlank(lockInfoVo.getCertNo())){
            insurancePracticeReportVos = insurancePracticeReportVos.stream().filter(vo ->vo.getCertNo().equals(lockInfoVo.getCertNo())).collect(Collectors.toList());
        }
        return insurancePracticeReportVos;
    }

    @Override
    public void updatePayLockStatus(List<Long> reportIds, Byte code, Integer reportMonth,String creator) {
        insurancePracticeReportMapper.updatePayLockStatus(reportIds,code,reportMonth,creator);
    }


    /**
     * 初次提交
     * @param detailVos
     * @param status
     * @param creator
     */
    @Override
    public void editReportAndIndividualFeeLock(Long payId,List<PracticePayDetail> detailVos, Byte status, String creator) {
        Integer reportMonth = detailVos.get(0).getReportMonth();
        List<Long> reportIds = detailVos.stream().map(PracticePayDetail::getReportId).collect(Collectors.toList());
        List<String> pracAndFeeType = detailVos.stream().map(vo ->vo.getPracticeId()+","+vo.getFeeType()).distinct().collect(Collectors.toList());
        individualFeeLockService.updateIndividualFee(payId,pracAndFeeType,status,reportMonth,creator);
        insurancePracticeReportMapper.updatePayLockStatus(reportIds,status,reportMonth,creator);
    }


    @Override
    public void deleteReportAndIndividualFeeLock(List<PracticePayDetail> detailVos, String creator) {
        Integer reportMonth = detailVos.get(0).getReportMonth();
        List<Long> reportIds = detailVos.stream().map(PracticePayDetail::getReportId).collect(Collectors.toList());
        List<String> pracAndFeeType = detailVos.stream().map(vo ->vo.getPracticeId()+","+vo.getFeeType()).distinct().collect(Collectors.toList());
        individualFeeLockService.updateIndividualFee(null,pracAndFeeType, PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode(),reportMonth,creator);
        insurancePracticeReportMapper.updatePayLockStatus(reportIds,PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode(),reportMonth,creator);
    }

    private void cancelDetails(PaymentApplyVo paymentApplyVo, List<Long> detailIds) {
        if (CollectionUtils.isNotEmpty(detailIds)) {
            PracticePayDetailVo condition = new PracticePayDetailVo();
            condition.setIds(detailIds);
            condition.setPayApplyId(null);
            condition.setAppStatus(null);
            condition.setUpdater(paymentApplyVo.getCreator());
            condition.setUpdateTime(new Date());
            practicePayDetailService.updatePracticePayDetailVoByIds(condition);
        }
    }

    private void editPayStatus(String creator, List<Long> billIds) {
        List<PracticePayDetail> payDetailVosByBillIds = practicePayDetailService.getPracticePayDetailVosByBillIds(billIds);
        Map<Long, Set<Integer>> hasOtherTaskListMap = payDetailVosByBillIds.stream().filter(vo -> vo.getAppStatus() != null)
                .collect(Collectors.groupingBy(PracticePayDetail::getBillId, Collectors.mapping(PracticePayDetail::getAppStatus, Collectors.toSet())));
        for (Long billId : billIds) {
            Set<Integer> appStatus = hasOtherTaskListMap.get(billId);
            if (Objects.isNull(appStatus) || appStatus.isEmpty()) {
                practiceBillMapper.updatePaymentStatusByIds(Lists.newArrayList(billId), creator, new Date(), PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode());
            } else if (appStatus.contains(PaymentApplyProcessStatus.PENDING_APPROVAL.getCode())) {
                practiceBillMapper.updatePaymentStatusByIds(Lists.newArrayList(billId), creator, new Date(), PracticeLockInfoPayStatusEnum.IN_PAID.getCode());
            } else if (appStatus.contains(PaymentApplyProcessStatus.FINISHED.getCode())) {
                practiceBillMapper.updatePaymentStatusByIds(Lists.newArrayList(billId), creator, new Date(), PracticeLockInfoPayStatusEnum.PART_PAID.getCode());
            } else if (appStatus.contains(PaymentApplyProcessStatus.REJECTED.getCode())) {
                practiceBillMapper.updatePaymentStatusByIds(Lists.newArrayList(billId), creator, new Date(), PracticeLockInfoPayStatusEnum.IN_PAID.getCode());
            }
        }
    }

    private Map<Long, BigDecimal> getAmtByBillIds(Map<Long, List<PracticePayDetail>> perBillIdsMap) {
        Map<Long, BigDecimal> map = Maps.newHashMap();
        for (Long billId : perBillIdsMap.keySet()) {
            BigDecimal totalAmt = perBillIdsMap.get(billId).stream().map(PracticePayDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            map.put(billId, totalAmt);
        }
        return map;
    }

    @Override
    public List<PracticePayDetailVo> getPracticePayDetailsPage(SearchPaymentDetailsVo lockInfoVo, Integer page, Integer limit) {
        return practicePayDetailService.getPracticePayDetailsPage(lockInfoVo, page, limit);
    }

    @Override
    public List<PracticePayDetailVo> checkPracPaymentAmt(SearchInsuranceLockDto lockInfoVo,Byte payDetailType) {
        /**根据选中的数据获取支付详细数据*/
        List<ProdPayAmtDto> prodPayAmtDtos = getPracticePayDetails(lockInfoVo,payDetailType);
        List<Long> currDetailIds = prodPayAmtDtos.stream().flatMap(vo -> vo.getDetailIds().stream()).collect(Collectors.toList());
        List<PracticePayDetail> practicePayDetails = QueryUtil.queryDataByStep(10, TimeUnit.SECONDS, 5000, currDetailIds, (params) -> practicePayDetailService.getPracticePayDetailVosByIds(params));
//        List<PracticePayDetail> practicePayDetails = practicePayDetailService.getPracticePayDetailVosByIds(currDetailIds);
        BigDecimal totalAmt = practicePayDetails.stream().map(PracticePayDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        /**支付金额是否相等*/
        if (!BigDecimalUtil.equalsVal(lockInfoVo.getPayAmt(), totalAmt)) {
            logger.info("============支付提交金额与支付选中金额不一致======================");
            return null;
        }
        /**报表中金额是否够扣除*/
        boolean flag = false;
        Map<Long, List<PracticePayDetail>> collect = practicePayDetails.stream().collect(Collectors.groupingBy(PracticePayDetail::getBillId));
        List<Integer> pracIds = collect.keySet().stream().map(Long::intValue).collect(Collectors.toList());
        List<PracticeLockInfoVo> lockInfoVos = practiceBillMapper.getByIds(pracIds);
        Map<Long, PracticeLockInfoVo> lockInfoVoMap = lockInfoVos.stream().collect(Collectors.toMap(PracticeLockInfoVo::getId, Function.identity()));
        for (Integer pracId : pracIds) {
            List<PracticePayDetail> payDetailVos = collect.get(pracId.longValue());
            BigDecimal frozenAmtTotal = payDetailVos.stream().map(PracticePayDetail::getAmount).reduce(BigDecimal::add).get();
            PracticeLockInfoVo practiceLockInfoVo = lockInfoVoMap.get(pracId.longValue());
            BigDecimal frozenAmt = Optional.ofNullable(practiceLockInfoVo.getFrozenAmt()).orElse(BigDecimal.ZERO);
            frozenAmt = frozenAmt.add(frozenAmtTotal);
            if (frozenAmt.compareTo(practiceLockInfoVo.getPayAmt()) > 0) {
                logger.info("============实做报表的冻结金额超过报表金额======================");
                flag = true;
                break;
            }
        }
        if (flag) {
            return null;
        }

        return practicePayDetails.stream().map(vo -> {
            PracticePayDetailVo detailVo = new PracticePayDetailVo();
            BeanUtils.copyProperties(vo, detailVo);
            return detailVo;
        }).collect(Collectors.toList());
    }

    private List<ProdPayAmtDto> getPracticePayDetails(SearchInsuranceLockDto lockInfoVo,Byte payDetailType) {
        String selectPaymentDetails = lockInfoVo.getSelectPaymentDetails();
        List<ProdPayAmtDto> prodPayAmtDtos = JsonUtil.jsonToList(selectPaymentDetails, ProdPayAmtDto.class);
        if (!payDetailType.equals(PracticePayMentTypeEnum.EMP_PAY.getCode())) {
            Long operateTime = lockInfoVo.getOperateTime();
            if(operateTime == null){
                throw new RuntimeException("缺少redis key！！！");
            }
            stringRedisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
            List<ProdPayAmtDto> allProdPayAmtDtos = stringRedisTemplate.opsForList().range(QueryUtil.getPayKey(operateTime), 0, -1);
            stringRedisTemplate.delete(QueryUtil.getPayKey(operateTime));
            Map<String, ProdPayAmtDto> prodPayAmtDtoMap = prodPayAmtDtos.stream().collect(Collectors.toMap(ProdPayAmtDto::getUuId, Function.identity()));
            prodPayAmtDtos = allProdPayAmtDtos.stream().filter(vo -> prodPayAmtDtoMap.containsKey(vo.getUuId())).collect(Collectors.toList());
        }
        return prodPayAmtDtos;
    }

    @Override
    public Integer updatePracticeLockInfoVAmt(List<PracticeLockInfoVo> condition) {
        for (PracticeLockInfoVo practiceLockInfoVo : condition) {
            practiceBillMapper.updatePracticeLockInfoVAmt(practiceLockInfoVo);
        }
        return condition.size();
    }

    @Override
    public void updatePracticePayDetailVoByIds(PracticePayDetailVo detailVoCondition) {
        practicePayDetailService.updatePracticePayDetailVoByIds(detailVoCondition);
    }

    @Override
    public void updatePracticePayDetailVoByPayIds(PaymentApply paymentApply) {
        PracticePayDetailVo detailVo = new PracticePayDetailVo();
        detailVo.setPayApplyId(paymentApply.getId());
        detailVo.setAppStatus(paymentApply.getAppStatus());
        detailVo.setUpdater(paymentApply.getCreator());
        detailVo.setUpdateTime(new Date());
        practicePayDetailService.updatePracticePayDetailVoByPayApplyIds(detailVo);
    }

    @Override
    public void updatePracticePayDetailVoByPayIds(PracticePayDetailVo condition) {
        practicePayDetailService.updatePracticePayDetailVoByPayApplyIds(condition);
    }

    @Override
    public void updatePracticePayDetailsByPayId(List<ProdPayAmtDto> prodPayAmtDtos) {
    }


    private static Function<PracticeLockInfoVo, Boolean> nonPay() {
        return vo -> vo.getPayStatus().equals(PracticeLockInfoPayStatusEnum.NON_PAYMENT.getCode()) || vo.getPayStatus().equals(PracticeLockInfoPayStatusEnum.PAID_FAIL.getCode());
    }

    @Override
    public List<String> insertPayDetail() {
        logger.info("===============开始执行========================");
        String loginName = "admin处理历史数据";
        EntityWrapper<PracticePayDetailSuccess> wrapper = new EntityWrapper<>();
//        wrapper.le("report_month",202311);
        practicePayDetailSuccessMapper.delete(wrapper);

        /**将202311月之前在支付中的报表数据，全部刷为支付完成*/
        SearchInsuranceLockDto lockDto = new SearchInsuranceLockDto();
        ArrayList<Byte> bytes = Lists.newArrayList(PracticeLockInfoPayStatusEnum.IN_PAID.getCode(), PracticeLockInfoPayStatusEnum.HAVE_PAID.getCode());
        lockDto.setPayStatusList(bytes);
        List<PracticeLockInfoVo> lockInfoVoList = practiceBillMapper.getBySearch(lockDto);
//        && vo.getLockMonth() < 202311
        lockInfoVoList = lockInfoVoList.stream().filter(vo ->Objects.nonNull(vo.getPayDetailType()) ).collect(Collectors.toList());
        List<SearchInsuranceLockDto> conditions = Lists.newArrayList();
        Map<Byte, List<PracticeLockInfoVo>> lockInfoMap = lockInfoVoList.stream().collect(Collectors.groupingBy(PracticeLockInfoVo::getPayStatus));
        List<PracticeReportToPayVo> allPracticeReportToPays = Lists.newArrayList();
        List<Long> inPaidBillIds = Lists.newArrayList();
        List<Long> inPaidPayIds = Lists.newArrayList();
        List<String> inPaidPIds = Lists.newArrayList();
        List<PracticeReportToPayVo> finalAllPracticeReportToPays = allPracticeReportToPays;
        lockInfoMap.forEach((payStatus, bill) -> {
            List<Long> billIds = bill.stream().map(PracticeLockInfoVo::getId).collect(Collectors.toList());
            if (payStatus == PracticeLockInfoPayStatusEnum.IN_PAID.getCode().intValue()) {
                List<Integer> inPaidStatus = Lists.newArrayList(PaymentApplyProcessStatus.REJECTED.getCode(), PaymentApplyProcessStatus.PENDING_APPROVAL.getCode());
                List<PracticeReportToPayVo> subPracticeReportToPays = practiceReportToPayMapper.getPracticeReportToPayVoByPracIds(inPaidStatus, billIds);

                List<Long> inPaidPayment = subPracticeReportToPays.stream().map(PracticeReportToPayVo::getPayApplyId).collect(Collectors.toList());
                List<String> inPaidPid = subPracticeReportToPays.stream().map(PracticeReportToPayVo::getPid).collect(Collectors.toList());

                inPaidPIds.addAll(inPaidPid);
                finalAllPracticeReportToPays.addAll(subPracticeReportToPays);
                inPaidPayIds.addAll(inPaidPayment);
                inPaidBillIds.addAll(billIds);
            }
            if (payStatus == PracticeLockInfoPayStatusEnum.HAVE_PAID.getCode().intValue()) {
                List<PracticeReportToPayVo> subPracticeReportToPays = practiceReportToPayMapper.getPracticeReportToPayVoByPracId(PaymentApplyProcessStatus.FINISHED.getCode(), billIds);
                finalAllPracticeReportToPays.addAll(subPracticeReportToPays);
            }
        });

        if(CollectionUtils.isNotEmpty(inPaidPayIds)){
            paymentApplyService.updatePaymentAppStatus(inPaidPayIds,PaymentApplyProcessStatus.FINISHED.getCode(),loginName);
        }
        if(CollectionUtils.isNotEmpty(inPaidBillIds)){
            practiceBillMapper.updatePaymentStatusByIds(inPaidBillIds,loginName,new Date(),PracticeLockInfoPayStatusEnum.HAVE_PAID.getCode());
        }

        allPracticeReportToPays = allPracticeReportToPays.stream ().collect (Collectors
                .collectingAndThen (Collectors.toCollection (() -> new TreeSet<> (Comparator.comparing (PracticeReportToPayVo::getPracticeLockId))), ArrayList::new));

        Map<Long, PracticeReportToPayVo> payMap = allPracticeReportToPays.stream().collect(Collectors.toMap(PracticeReportToPayVo::getPracticeLockId, Function.identity()));
        Map<String, PracticeLockInfoVo> billIdMap = getBillIdMap(lockInfoVoList, conditions);
        List<InsurancePracticeReportVo> allReports = Lists.newArrayList();
        Map<String, List<SearchInsuranceLockDto>> collect = conditions.stream().collect(Collectors.groupingBy(vo -> vo.getLockMonth() + "" + vo.getPayDetailType()));
        collect.forEach((lockMonth, packs) -> {
            List<String> packList = packs.stream().map(SearchInsuranceLockDto::getPackCode).distinct().collect(Collectors.toList());
            SearchInsuranceLockDto condition = packs.get(0);
            condition.setPackCodes(packList);
            List<InsurancePracticeReportVo> subReports = insurancePracticeReportMapper.getInsurancePracticeReportByCondition(condition);
            allReports.addAll(subReports);
        });

        List<PracticePayDetail> detailVos = genDetails(allReports, billIdMap, payMap, "admin", null, "admin");
        insertDetails(detailVos,"success");
        logger.info("===============执行结束!========================");

        return inPaidPIds;
    }


    private void enterpriseOrIndividualPay(SearchInsuranceLockDto lockInfoVo, List<PracticePayDetail> noPayLockInfoDetails, Map<String, ProdPayAmtDto> map, PracticePayDetailDto dto) {
        Map<String, InsurancePackVo> finalPackNameMap = getInsurancePackVoMap(lockInfoVo.getPackCodes());
        noPayLockInfoDetails.forEach(vo -> {
            String key = (vo.getAmtType().equals(PracticePayDetailAmtType.COM_AMT.getCode()) || vo.getAmtType().equals(PracticePayDetailAmtType.COM_LATE_AMT.getCode())) ? "com" : "ind";
            key = key+"_" +vo.getFeeType()+"_"+vo.getProdCode();
            ProdPayAmtDto prodPayAmtDto = map.get(key);
            if (Objects.isNull(prodPayAmtDto)) {
                prodPayAmtDto = new ProdPayAmtDto();
                String uuid = UUIDUtil.getUUID();
                prodPayAmtDto.setUuId(uuid);
                prodPayAmtDto.setPackCode(vo.getPackCode());
                prodPayAmtDto.setProdCode(vo.getProdCode());
                prodPayAmtDto.setFeeType(vo.getFeeType());
                prodPayAmtDto.setPackName(finalPackNameMap.getOrDefault(vo.getPackCode(), new InsurancePackVo()).getPackName());
                prodPayAmtDto.setAmtType(vo.getAmtType());
            }
            getDetailIds(vo, prodPayAmtDto);
            map.put(key, prodPayAmtDto);
        });
        List<ProdPayAmtDto> collect = map.values().stream().sorted(Comparator.comparingInt(ProdPayAmtDto::getProdCode)
                .thenComparing(Comparator.comparingInt(ProdPayAmtDto::getFeeType)
                        .thenComparing(Comparator.comparingInt(ProdPayAmtDto::getAmtType)))).collect(Collectors.toList());
        /**将金额与滞纳金 合为一条，如果以后不需要分开，直接注释代码就行*/
        dto.setProdData(collect);
    }


    private void individualLockPay(SearchInsuranceLockDto lockInfoVo, List<PracticePayDetail> noPayLockInfoDetails, Map<String, ProdPayAmtDto> map, PracticePayDetailDto dto) {
        Map<String, InsurancePackVo> finalPackNameMap = getInsurancePackVoMap(lockInfoVo.getPackCodes());
        List<String> orderNos = noPayLockInfoDetails.stream().map(PracticePayDetail::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, EmployeeOrderVo> certNoMap = employeeOrderWrapperService.getCertNoByOrderNos(orderNos);
        noPayLockInfoDetails.forEach(vo -> {
            String key = vo.getPracticeId()+"_"+vo.getFeeType();
            ProdPayAmtDto prodPayAmtDto = map.get(key);
            if (Objects.isNull(prodPayAmtDto)) {
                prodPayAmtDto = new ProdPayAmtDto();
                String uuid = UUIDUtil.getUUID();
                prodPayAmtDto.setUuId(uuid);
                prodPayAmtDto.setPackCode(vo.getPackCode());
                prodPayAmtDto.setOrderNo(vo.getOrderNo());
                prodPayAmtDto.setFeeType(vo.getFeeType());
                prodPayAmtDto.setPackName(finalPackNameMap.getOrDefault(vo.getPackCode(), new InsurancePackVo()).getPackName());
                if(certNoMap.containsKey(vo.getOrderNo())){
                    EmployeeOrderVo orderVo = certNoMap.get(vo.getOrderNo());
                    prodPayAmtDto.setEmpName(orderVo.getEmployeeName());
                    prodPayAmtDto.setCertNo(orderVo.getCertNo());
                }
            }
            getDetailIds(vo, prodPayAmtDto);
            map.put(key, prodPayAmtDto);
        });
        List<ProdPayAmtDto> collect = map.values().stream().sorted(Comparator.comparing(ProdPayAmtDto::getOrderNo)).collect(Collectors.toList());
        dto.setProdData(collect);
    }

    private void getDetailIds(PracticePayDetail vo, ProdPayAmtDto prodPayAmtDto) {
        List<Long> detailIds = Optional.ofNullable(prodPayAmtDto.getDetailIds()).orElse(Lists.newArrayList());
        detailIds.add(vo.getId());
        prodPayAmtDto.setDetailIds(detailIds);
        BigDecimal bigDecimal = Optional.ofNullable(prodPayAmtDto.getTotalAmt()).orElse(BigDecimal.ZERO);
        bigDecimal = bigDecimal.add(vo.getAmount());
        prodPayAmtDto.setTotalAmt(bigDecimal);
    }

    private void collectAndReplenishPay(SearchInsuranceLockDto lockInfoVo, Byte payDetailType, List<PracticePayDetail> noPayLockInfoDetails, Map<String, ProdPayAmtDto> map, PracticePayDetailDto dto) {
        Map<String, InsurancePackVo> finalPackNameMap = getInsurancePackVoMap(lockInfoVo.getPackCodes());
        String appKey = "";
        if (payDetailType.equals(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode())
                || payDetailType.equals(PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode())) {
            appKey = payDetailType.toString();
        }
        String finalAppKey = appKey;
        noPayLockInfoDetails.forEach(vo -> {
            ProdPayAmtDto prodPayAmtDto = map.get(vo.getPackCode() + "_" + finalAppKey);
            if (Objects.isNull(prodPayAmtDto)) {
                prodPayAmtDto = new ProdPayAmtDto();
                String uuid = UUIDUtil.getUUID();
                prodPayAmtDto.setUuId(uuid);
                prodPayAmtDto.setPackCode(vo.getPackCode());
                prodPayAmtDto.setFeeType(payDetailType.intValue());
                prodPayAmtDto.setPackName(finalPackNameMap.getOrDefault(vo.getPackCode(), new InsurancePackVo()).getPackName());
            }

            getDetailIds(vo, prodPayAmtDto);
            map.put(vo.getPackCode() + "_" + finalAppKey, prodPayAmtDto);
        });
        dto.setProdData(new ArrayList<>(map.values()));
    }

    private void prodPay(List<PracticePayDetail> noPayLockInfoDetails, Map<String, ProdPayAmtDto> map, PracticePayDetailDto dto) {
        noPayLockInfoDetails.forEach(vo -> {
            ProdPayAmtDto prodPayAmtDto = map.get(vo.getProdCode() + "_" + vo.getFeeType());
            if (Objects.isNull(prodPayAmtDto)) {
                prodPayAmtDto = new ProdPayAmtDto();
                String uuid = UUIDUtil.getUUID();
                prodPayAmtDto.setUuId(uuid);
                prodPayAmtDto.setProdCode(vo.getProdCode());
                prodPayAmtDto.setFeeType(vo.getFeeType());
            }
            getDetailIds(vo, prodPayAmtDto);
            map.put(vo.getProdCode() + "_" + vo.getFeeType(), prodPayAmtDto);
        });
        dto.setProdData(new ArrayList<>(map.values()));
    }

    private Map<String, InsurancePackVo> getInsurancePackVoMap(List<String> lockInfoVo) {
        Map<String, InsurancePackVo> packNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(lockInfoVo)) {
            packNameMap = insurancePackResourceWrapperService.getPackNameByCode(lockInfoVo);
        }
        return packNameMap;
    }


    private void insertDetails(List<PracticePayDetail> detailVos,String type) {
        if (CollectionUtils.isNotEmpty(detailVos)) {
            if ("detail".equals(type)) {
                // 防止 SubList 问题，拷贝一份
                List<PracticePayDetail> safeList = new ArrayList<>(detailVos);
                redisTemplate.opsForValue().set(
                        detailVos.get(0).getCreator(),
                        safeList,
                        Duration.ofMinutes(5)
                );
            }
            List<List<PracticePayDetail>> partition = Lists.partition(detailVos, 10000);
            int i = 0;
            for (List<PracticePayDetail> practicePayDetails : partition) {
                logger.info("数据大小:{},每次处理大小:{},处理次数:{}", practicePayDetails.size(), 10000, ++i);
                if("detail".equals(type)){
                    practicePayDetailService.insertPracticePayDetailVos(practicePayDetails);
                } else {
                    practicePayDetailService.insertPracticePayDetailVosSuccess(practicePayDetails);
                }
            }
        } else {
            logger.info("该次处理没有数据!!请检查!!");
        }
    }


    private Map<String, PracticeLockInfoVo> getBillIdMap(List<PracticeLockInfoVo> lockInfoVoList, List<SearchInsuranceLockDto> conditions) {
        Map<String, PracticeLockInfoVo> billIdMap = Maps.newHashMap();
        for (PracticeLockInfoVo practiceLockInfoVo : lockInfoVoList) {
            String packCode = practiceLockInfoVo.getPackCode();
            Integer lockMonth = practiceLockInfoVo.getLockMonth();
            Integer payDetailType = practiceLockInfoVo.getPayDetailType();
            String orgCode = practiceLockInfoVo.getOrgCode();
            SearchInsuranceLockDto condition = new SearchInsuranceLockDto();
            condition.setLockMonth(lockMonth);
            condition.setPayDetailType(payDetailType.byteValue());
            condition.setPackCode(packCode);
            condition.setOrgCode(orgCode);
            conditions.add(condition);
            billIdMap.put((packCode + lockMonth + payDetailType), practiceLockInfoVo);
        }
        return billIdMap;
    }

    private List<PracticePayDetail> genDetails(List<InsurancePracticeReportVo> allReports, Map<String, PracticeLockInfoVo> billIdMap, Map<Long, PracticeReportToPayVo> payMap, String type
            , Long operateTime, String loginName) {
        Date date = new Date();
        List<PracticePayDetail> detailVos = Lists.newArrayList();
        for (InsurancePracticeReportVo reportVo : allReports) {
            String packCode = reportVo.getPackCode();
            Integer lockMonth = reportVo.getReportMonth();
            int payDetailType = reportVo.getFeeType().intValue();
            PracticeLockInfoVo practiceLockInfoVo = billIdMap.get((packCode + lockMonth + payDetailType));
            if (!BigDecimalUtil.equalsZero(reportVo.getComAmt())) {
                buildDetail(reportVo, practiceLockInfoVo, date, detailVos, payMap, PracticePayDetailAmtType.COM_AMT.getCode(), reportVo.getComAmt(), type, operateTime, loginName);
            }
            if (!BigDecimalUtil.equalsZero(reportVo.getIndAmt())) {
                buildDetail(reportVo, practiceLockInfoVo, date, detailVos, payMap, PracticePayDetailAmtType.IND_AMT.getCode(), reportVo.getIndAmt(), type, operateTime, loginName);
            }
            if (!BigDecimalUtil.equalsZero(reportVo.getComLateFee())) {
                buildDetail(reportVo, practiceLockInfoVo, date, detailVos, payMap, PracticePayDetailAmtType.COM_LATE_AMT.getCode(), reportVo.getComLateFee(), type, operateTime, loginName);
            }
            if (!BigDecimalUtil.equalsZero(reportVo.getIndLateFee())) {
                buildDetail(reportVo, practiceLockInfoVo, date, detailVos, payMap, PracticePayDetailAmtType.IND_LATE_AMT.getCode(), reportVo.getIndLateFee(), type, operateTime, loginName);
            }
        }
        return detailVos;
    }


    private void buildDetail(InsurancePracticeReportVo reportVo, PracticeLockInfoVo practiceLockInfoVo, Date date, List<PracticePayDetail> detailVos,
                             Map<Long, PracticeReportToPayVo> payMap, Integer amtType, BigDecimal amount, String type, Long time, String loginName) {
        PracticePayDetail detailVo = new PracticePayDetail();
        detailVo.setBillId(practiceLockInfoVo.getId());
        PracticeReportToPayVo pay = payMap.get(practiceLockInfoVo.getId());
        //处理以往数据的时候不能为null
        if ("admin".equals(type) && pay == null) {
            logger.info("-=============================,{}", practiceLockInfoVo.getId());
            throw new RuntimeException();
        }

        /**补缴类型在办理页面添加的时候是 金额 = 单月金额
         * 在补缴导入的页面 金额 = 单月金额*补缴月份
         * */
        if(reportVo.getFeeType().equals(PracticeLockInfoFeeTypeEnum.REPLENISH_PAYMENT.getCode())){
            BigDecimal comAmt = Optional.ofNullable(reportVo.getComAmt()).orElse(BigDecimal.ZERO);
            BigDecimal indAmt = Optional.ofNullable(reportVo.getIndAmt()).orElse(BigDecimal.ZERO);
            BigDecimal comLateFee = Optional.ofNullable(reportVo.getComLateFee()).orElse(BigDecimal.ZERO);
            BigDecimal indLateFee = Optional.ofNullable(reportVo.getIndLateFee()).orElse(BigDecimal.ZERO);
            Integer months = Optional.ofNullable(reportVo.getMonths()).orElse(0);
            BigDecimal monthsB = BigDecimal.valueOf(months);
            ArrayList<Integer> integers = Lists.newArrayList(PracticePayDetailAmtType.IND_AMT.getCode(), PracticePayDetailAmtType.COM_AMT.getCode());
            BigDecimal totalAmt = comAmt.multiply(monthsB).add(indAmt.multiply(monthsB)).add(comLateFee).add(indLateFee);
            if(totalAmt.compareTo(reportVo.getTotalAmt()) == 0 && integers.contains(amtType)){
                amount = amount.multiply(BigDecimal.valueOf(reportVo.getMonths()));
            }
        }
        Long payApplyId = Objects.nonNull(pay) ? pay.getPayApplyId() : null;
        Integer appStatus = Objects.nonNull(pay) ? pay.getAppStatus() : null;
        detailVo.setPayApplyId(payApplyId);
        detailVo.setReportId(reportVo.getId());
        detailVo.setReportMonth(reportVo.getReportMonth());
        detailVo.setPracticeId(reportVo.getPracticeId());
        detailVo.setAppStatus(appStatus);
        detailVo.setPackCode(reportVo.getPackCode());
        detailVo.setOrgCode(reportVo.getOrgCode());
        detailVo.setCustId(reportVo.getCustId());
        detailVo.setOrderNo(reportVo.getOrderNo());
        detailVo.setProdCode(reportVo.getProdCode().intValue());
        detailVo.setFeeType(reportVo.getFeeType().intValue());
        detailVo.setAmtType(amtType);
        detailVo.setAmount(amount);
        detailVo.setOperateTime(time);
        detailVo.setCreator(loginName);
        detailVo.setCreateTime(date);
        detailVos.add(detailVo);
    }

    /**
     * 根据报表信息中的产品code拆分为具体的实做报表明细
     */
    private Map<Byte, PracticeReportDetailVo> getPracticeReportDetailMapByInsurancePracticeReportVo(List<InsurancePracticeReportVo> insurancePracticeReportList) {
        Map<Byte, PracticeReportDetailVo> getPracticeReportDetailVoByProdCodeMap = new HashMap<>();
        for (InsurancePracticeReportVo insurancePracticeReportVo : insurancePracticeReportList) {
            PracticeReportDetailVo practiceReportDetailVo;
            if (getPracticeReportDetailVoByProdCodeMap.containsKey(insurancePracticeReportVo.getProdCode())) {
                practiceReportDetailVo = getPracticeReportDetailVoByProdCodeMap.get(insurancePracticeReportVo.getProdCode());
            } else {
                practiceReportDetailVo = new PracticeReportDetailVo();
                practiceReportDetailVo.setProdCode(insurancePracticeReportVo.getProdCode());
                getPracticeReportDetailVoByProdCodeMap.put(insurancePracticeReportVo.getProdCode(), practiceReportDetailVo);
            }
            if (PracticeLockInfoFeeTypeEnum.COLLECT_PAYMENT.getCode().equals(insurancePracticeReportVo.getFeeType())) {
                practiceReportDetailVo.setComBase(insurancePracticeReportVo.getComBase());
                practiceReportDetailVo.setComAmt(insurancePracticeReportVo.getComAmt());
                practiceReportDetailVo.setComRatio(insurancePracticeReportVo.getComRatio());
                practiceReportDetailVo.setIndAmt(insurancePracticeReportVo.getIndAmt());
                practiceReportDetailVo.setIndRatio(insurancePracticeReportVo.getIndRatio());
                practiceReportDetailVo.setTotalAmt(insurancePracticeReportVo.getTotalAmt());
            } else {
                practiceReportDetailVo.setIndLateFee(Optional.ofNullable(practiceReportDetailVo.getIndLateFee()).orElse(BigDecimal.ZERO)
                        .add(Optional.ofNullable(insurancePracticeReportVo.getIndLateFee()).orElse(BigDecimal.ZERO)));
                practiceReportDetailVo.setComLateFee(
                        Optional.ofNullable(practiceReportDetailVo.getComLateFee()).orElse(BigDecimal.ZERO)
                                .add(Optional.ofNullable(insurancePracticeReportVo.getComLateFee()).orElse(BigDecimal.ZERO)));
                practiceReportDetailVo.setSuppliPayComAmt(
                        Optional.ofNullable(practiceReportDetailVo.getSuppliPayComAmt()).orElse(BigDecimal.ZERO)
                                .add(Optional.ofNullable(insurancePracticeReportVo.getComAmt()).orElse(BigDecimal.ZERO)));
                practiceReportDetailVo.setSuppliPayIndAmt(
                        Optional.ofNullable(practiceReportDetailVo.getSuppliPayIndAmt()).orElse(BigDecimal.ZERO)
                                .add(Optional.ofNullable(insurancePracticeReportVo.getIndAmt()).orElse(BigDecimal.ZERO)));
                practiceReportDetailVo.setSuppliPayTotalAmount(
                        Optional.ofNullable(practiceReportDetailVo.getSuppliPayTotalAmount()).orElse(BigDecimal.ZERO)
                                .add(Optional.ofNullable(insurancePracticeReportVo.getTotalAmt()).orElse(BigDecimal.ZERO)));
                practiceReportDetailVo.setMonths(Optional.ofNullable(practiceReportDetailVo.getMonths()).orElse(0) +
                        Optional.ofNullable(insurancePracticeReportVo.getMonths()).orElse(0));
            }
        }
        return getPracticeReportDetailVoByProdCodeMap;
    }

    /**
     * 报表导出公共参数包装
     */
    private void packExportPracticeReportVo(List<ExportPracticeReportVo> exportPracticeReportVoList, List<String> packCode) {
        if (CollectionUtils.isEmpty(exportPracticeReportVoList)) {
            return;
        }
        Set<String> csSet = new HashSet<>();
        Set<String> orgSet = new HashSet<>();
        Set<Long> custIdSet = new HashSet<>();
        for (ExportPracticeReportVo exportPracticeReportVo : exportPracticeReportVoList) {
            csSet.add(exportPracticeReportVo.getRevCs());
            csSet.add(exportPracticeReportVo.getPrjCs());
            orgSet.add(exportPracticeReportVo.getReceiving());
            orgSet.add(exportPracticeReportVo.getDistCom());
            orgSet.add(exportPracticeReportVo.getSignComTitle());
            custIdSet.add(exportPracticeReportVo.getCustId());
        }
        List<OrgVo> orgVoList = orgnizationResourceWrapperService.getOrgListByOrgCodeList(new ArrayList<>(orgSet));
        Map<String, String> getOrgNameByOrgCodeMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        List<CustomerVo> customerVoList = customerWrapperService.getCustomerListByIds(new ArrayList<>(custIdSet));
        Map<Long, String> getCustNameByCustIdMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
        List<CommonUserVo> commonUserVoList = userWrapperService.getByLoginNameLIst(new ArrayList<>(csSet));
        Map<String, String> getUserNameByLoginNameMap = commonUserVoList.stream().collect(Collectors.toMap(CommonUserVo::getLoginName, CommonUserVo::getUserName));
        List<Long> employeeIds = exportPracticeReportVoList.stream().map(ExportPracticeReportVo::getEmployeeId).collect(Collectors.toList());
        List<EmpInsurAcctVO> empInsurAcctVOList = empInsurAcctWrapperService.getEmpInsurAcctByEmployeeIdsAndPackCode(employeeIds, packCode);
        Map<Long, String> getAcctNoByEmpIdMap = new HashMap<>();
        for (EmpInsurAcctVO empInsurAcctVO : empInsurAcctVOList) {
            getAcctNoByEmpIdMap.put(empInsurAcctVO.getEmpId(), empInsurAcctVO.getAcctNo());
        }
        for (ExportPracticeReportVo exportPracticeReportVo : exportPracticeReportVoList) {
            exportPracticeReportVo.setAcctNo(getAcctNoByEmpIdMap.getOrDefault(exportPracticeReportVo.getEmployeeId(), ""));
            exportPracticeReportVo.setRevCsName(getUserNameByLoginNameMap.getOrDefault(exportPracticeReportVo.getRevCs(), ""));
            exportPracticeReportVo.setPrjCsName(getUserNameByLoginNameMap.getOrDefault(exportPracticeReportVo.getPrjCs(), ""));
            exportPracticeReportVo.setReceivingName(getOrgNameByOrgCodeMap.getOrDefault(exportPracticeReportVo.getReceiving(), ""));
            exportPracticeReportVo.setOrgName(getOrgNameByOrgCodeMap.getOrDefault(exportPracticeReportVo.getReceiving(), ""));
            exportPracticeReportVo.setSignComTitle(getOrgNameByOrgCodeMap.getOrDefault(exportPracticeReportVo.getSignComTitle(), ""));
            exportPracticeReportVo.setDistComName(getOrgNameByOrgCodeMap.getOrDefault(exportPracticeReportVo.getDistCom(), ""));
            exportPracticeReportVo.setCustName(getCustNameByCustIdMap.getOrDefault(exportPracticeReportVo.getCustId(), ""));
            exportPracticeReportVo.setContractTypeStr(EmployeeReportEnum.ContractTypeEnum.getName(exportPracticeReportVo.getContractType()));
            exportPracticeReportVo.setAddMethodStr(InsurancePracticeAddMethodEnum.getMsgByCode(exportPracticeReportVo.getAddMethod()));
            exportPracticeReportVo.setAccountFlagStr(IncomeCountTableReportEnum.AccountFlagEnum.getName(exportPracticeReportVo.getAccountFlag()));
            exportPracticeReportVo.setDimissionReasonStr(DimissionReasonTypeEnum.getName(exportPracticeReportVo.getDimissionReason()));
            String age = "";
            if (exportPracticeReportVo.getCertType() != null && exportPracticeReportVo.getCertType() == CertType.ID_CARD.getCode()) {
                age = CertUtils.IdNOToAge(exportPracticeReportVo.getCertNo());
            }
            exportPracticeReportVo.setAge(age);
        }
    }

    /**
     * 包装分页查询返回值对象
     *
     * @param practiceLockInfoVoList
     */
    private void packPracticeLockInfoVo(List<PracticeLockInfoVo> practiceLockInfoVoList) {
        List<String> packCodeList = practiceLockInfoVoList.stream().map(PracticeLockInfoVo::getPackCode).distinct().collect(Collectors.toList());
        Map<String, InsurancePackVo> packNameMap = Maps.newHashMap();
        Map<String, SingleAccountRelativeVo> accountRelativesMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(packCodeList)) {
            packNameMap = insurancePackResourceWrapperService.getPackNameByCode(packCodeList);
            List<SingleAccountRelativeVo> accountRelatives = pracSingleAccountWrapperService.getSingleAccountRelatives(packCodeList);
            accountRelativesMap = accountRelatives.stream().collect(Collectors.toMap(SingleAccountRelativeVo::getRelativeNo, Function.identity(), (a, b) -> a));
        }
        List<String> orgCodeList = practiceLockInfoVoList.stream().map(PracticeLockInfoVo::getOrgCode).distinct().collect(Collectors.toList());
        List<OrgVo> orgVoList = orgnizationResourceWrapperService.getOrgListByOrgCodeList(orgCodeList);
        Map<String, String> getOrgNameByOrgCodeMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName));
        List<Long> custIdList = practiceLockInfoVoList.stream().map(PracticeLockInfoVo::getCustId).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVoList = customerWrapperService.getCustomerListByIds(custIdList);
        Map<Long, String> getCustNameByCustIdMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
        for (PracticeLockInfoVo practiceLockInfoVo : practiceLockInfoVoList) {
//            practiceLockInfoVo.setFeeTypeStr(PracticeLockInfoFeeTypeEnum.getMsgByCode(practiceLockInfoVo.getFeeType()));
            practiceLockInfoVo.setLockStatusStr(PracticeBillLockStatusEnum.getMsgByCode(practiceLockInfoVo.getLockStatus()));
            practiceLockInfoVo.setGenStatusStr(PracticeLockInfoGenStatusEnum.getMsgByCode(practiceLockInfoVo.getGenStatus()));
            practiceLockInfoVo.setPayStatusStr(PracticeLockInfoPayStatusEnum.getMsgByCode(practiceLockInfoVo.getPayStatus()));
            if (packNameMap.containsKey(practiceLockInfoVo.getPackCode())) {
                practiceLockInfoVo.setPackName(packNameMap.get(practiceLockInfoVo.getPackCode()).getPackName());
                practiceLockInfoVo.setSingleFlag(packNameMap.get(practiceLockInfoVo.getPackCode()).getSingleFlag());
            }
            practiceLockInfoVo.setCustName(getCustNameByCustIdMap.get(practiceLockInfoVo.getCustId()));
            if ("0000".equals(practiceLockInfoVo.getOrgCode())) {
                practiceLockInfoVo.setOrgName("单立户");
                practiceLockInfoVo.setCustName(getCustNameByCustIdMap.get(practiceLockInfoVo.getCustId()));
            } else {
                practiceLockInfoVo.setOrgName(getOrgNameByOrgCodeMap.get(practiceLockInfoVo.getOrgCode()));
            }
            if (BigDecimalUtil.equalsZero(practiceLockInfoVo.getFrozenAmt())) {
                practiceLockInfoVo.setFrozenAmt(BigDecimal.ZERO);
            }
            if (BigDecimalUtil.equalsZero(practiceLockInfoVo.getComPayAmt())) {
                practiceLockInfoVo.setComPayAmt(BigDecimal.ZERO);
            }

            SingleAccountRelativeVo accountRelativeVo = accountRelativesMap.get(practiceLockInfoVo.getPackCode());
            if(accountRelativeVo != null){
                practiceLockInfoVo.setSinAccId(accountRelativeVo.getSinAccId());
                practiceLockInfoVo.setSinAccName(accountRelativeVo.getSinAccName());
            }

            practiceLockInfoVo.setLockTimeStr(DateUtil.getSimpleDateString(practiceLockInfoVo.getLockTime()));
        }
    }


    @Override
    public Map<Boolean,PracticeLockInfoVo> getPracticeBillVoByOrgCodeAndMonth(String orgCode, Integer month) {
        Map<Boolean, PracticeLockInfoVo> booleanListHashMap = new HashMap<>();
        PracticeLockInfoVo practiceLockInfoVo = new PracticeLockInfoVo();
        List<PracticeLockInfoVo> practiceBillVoByOrgCodeAndMonth = practiceBillMapper.getPracticeBillVoByOrgCodeAndMonth(orgCode, month);
        if (CollectionUtils.isEmpty(practiceBillVoByOrgCodeAndMonth)){
            booleanListHashMap.put(false,practiceLockInfoVo);
            return booleanListHashMap;
        }
        List<PracticeLockInfoVo> notLockList = practiceBillVoByOrgCodeAndMonth.stream().filter(vo -> Objects.equals(vo.getLockStatus(), PracticeBillLockStatusEnum.UNLOCKED.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notLockList)){
            booleanListHashMap.put(false,practiceLockInfoVo);
            return booleanListHashMap;
        }
        List<String> packCodeList = practiceBillVoByOrgCodeAndMonth.stream().map(PracticeLockInfoVo::getPackCode).collect(Collectors.toList());
        List<InsurancePracticeReportVo> countByPakCodeListAndOrgCode = insurancePracticeReportMapper.getCountByPakCodeListAndOrgCode(orgCode, packCodeList, month);
        InsurancePracticeReportVo insurancePracticeReportVo = countByPakCodeListAndOrgCode.stream().max(Comparator.comparingInt(InsurancePracticeReportVo::getCount)).get();
        String packNameByPackCode = insurancePackResourceWrapperService.getPackNameByPackCode(insurancePracticeReportVo.getPackCode());
        practiceLockInfoVo.setPackCode(insurancePracticeReportVo.getPackCode());
        practiceLockInfoVo.setPackName(packNameByPackCode);
        booleanListHashMap.put(true,practiceLockInfoVo);
        return booleanListHashMap;
    }

    @Override
    public List<InsurancePracticeReportVo> getOrderNoListByPakCodeListAndOrgCode(String orgCode, Integer lockMonth) {
        return insurancePracticeReportMapper.getOrderNoListByPakCodeListAndOrgCode(orgCode, lockMonth);
    }

    @Override
    public List<OrderAndInsuranceDiffExportVo> getInsurancePracticeBillByOrgCodeAndMonth(OrderAndInsuranceDiffParamVo paramVo) {
        return insurancePracticeReportMapper.getInsurancePracticeBillByOrgCodeAndMonth(paramVo);
    }

    @Override
    public Map<String, Map<String, Long>> getInsurancePracticeBillCountByLastMonth(Integer lastMonth, String contractAreaNo) {
        Map<String, Map<String, Long>> resultMap = new HashMap<>();
        List<InsurancePracticeReportVo> insurancePracticeBillCountByLastMonth = insurancePracticeReportMapper.getInsurancePracticeBillCountByLastMonth(lastMonth);
        if (CollectionUtils.isEmpty(insurancePracticeBillCountByLastMonth)) {
            return Collections.emptyMap();
        }
        Set<Long> practiceIdSet = insurancePracticeBillCountByLastMonth.stream().map(InsurancePracticeReportVo::getPracticeId).collect(Collectors.toSet());
        List<InsurancePracticeVo> insurancePracHandlerByIdSet = iInsurancePracticeWrapperService.getInsurancePracHandlerByIdSet(practiceIdSet, contractAreaNo);
        Map<String, List<InsurancePracticeVo>> contractAreaNoMap = insurancePracHandlerByIdSet.stream().collect(Collectors.groupingBy(InsurancePracticeVo::getContractAreaNo));
        contractAreaNoMap.forEach((key, insurancePracticeVos) -> {
            Map<String, Long> handlerCountMap = insurancePracticeVos.stream()
                    .filter(vo -> vo.getHandler() != null)
                    .collect(Collectors.groupingBy(
                            InsurancePracticeVo::getHandler,
                            Collectors.counting()
                    ));
            resultMap.put(key, handlerCountMap);
        });


        return resultMap;
    }
    @Override
    public List<InsurancePracticeReportVo> getDetailsByReportIdList(List<Long> reportIds, Integer month) {
        return insurancePracticeReportMapper.getDetailsByReportIdList(reportIds, month);
    }
}
