<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.NationalityMapper">
  
  <select id="findAllNationality" resultType="com.reon.hr.api.base.vo.NationalityVo" >
    select id, code, name, full_name from nationality
  </select>

    <select id="getNationalityByCode"  resultType="string">
        select name  from nationality where code = #{payPlace}
    </select>
</mapper>
