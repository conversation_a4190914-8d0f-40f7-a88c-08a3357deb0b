package com.reon.hr.sp.customer.dubbo.service.rpc.impl.insurancePractice;

import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.reon.hr.api.base.dubbo.service.rpc.sys.*;
import com.reon.hr.api.base.enums.InsuranceIRatioProductCodeEnum;
import com.reon.hr.api.base.enums.InsurancePackStatus;
import com.reon.hr.api.base.enums.SinAccRelativeEnum;
import com.reon.hr.api.base.enums.ValidFlagEnum;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.api.customer.dto.importData.ImportDataDto;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.PackAdjustWrapperService;
import com.reon.hr.api.customer.enums.AdjustTypeEnum;
import com.reon.hr.api.customer.enums.AdjustmentOfStatusEnum;
import com.reon.hr.api.customer.enums.CertType;
import com.reon.hr.api.customer.enums.CommInsurOrderEnum;
import com.reon.hr.api.customer.enums.billTemplet.BillTempletFeeCfgBeforeMonths;
import com.reon.hr.api.customer.enums.employee.EmployeeEntryDimissionStatus;
import com.reon.hr.api.customer.enums.employee.EmployeeOrderStatus;
import com.reon.hr.api.customer.enums.employee.noMatchPack.NoMatchPackStatus;
import com.reon.hr.api.customer.enums.importData.ImportDataType;
import com.reon.hr.api.customer.enums.insurancePractice.InsurancePracticeEnum;
import com.reon.hr.api.customer.utils.BatchImportExcelCommonUtil;
import com.reon.hr.api.customer.utils.DateUtil;
import com.reon.hr.api.customer.vo.BatchAdjustmentVo;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.InsurancePracticeVo;
import com.reon.hr.api.customer.vo.ProdHandleInfoVo;
import com.reon.hr.api.customer.vo.batchImport.BatchAdjustImportTextVo;
import com.reon.hr.api.customer.vo.batchImport.BatchAdjustImportVo;
import com.reon.hr.api.customer.vo.batchImport.BatchInsurancePracticeAdjustImportVo;
import com.reon.hr.api.customer.vo.changeBase.BatchAdjustVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.vo.employee.EmployeeVo;
import com.reon.hr.api.customer.vo.employee.OrderInsuranceCfgVo;
import com.reon.hr.api.customer.vo.insurancePractice.*;
import com.reon.hr.api.file.dubbo.service.rpc.IFileSystemService;
import com.reon.hr.api.report.enums.IncomeCountTableReportEnum;
import com.reon.hr.api.util.UUIDUtil;
import com.reon.hr.common.utils.CalculateUtil;
import com.reon.hr.common.utils.calculate.CalculateArgs;
import com.reon.hr.sp.customer.dao.insurancePractice.InsurancePracticeMapper;
import com.reon.hr.sp.customer.dao.insurancePractice.ProdHandleInfoMapper;
import com.reon.hr.sp.customer.dao.insurancePractice.practiceAdjust.PackAdjustMapper;
import com.reon.hr.sp.customer.entity.employee.NoMatchPack;
import com.reon.hr.sp.customer.entity.insurancePractice.InsurancePractice;
import com.reon.hr.sp.customer.entity.insurancePractice.ProdHandleInfo;
import com.reon.hr.sp.customer.entity.insurancePractice.SingleAccountRelative;
import com.reon.hr.sp.customer.entity.insurancePractice.practiceAdjust.PackAdjustJob;
import com.reon.hr.sp.customer.service.GeneratorSequenceService;
import com.reon.hr.sp.customer.service.cus.CustomerService;
import com.reon.hr.sp.customer.service.cus.IBatchImportDataService;
import com.reon.hr.sp.customer.service.employee.IEmployeeOrderService;
import com.reon.hr.sp.customer.service.insurancePractice.IInsurancePracticeService;
import com.reon.hr.sp.customer.service.insurancePractice.SingleAccountRelativeService;
import com.reon.hr.sp.customer.util.SequenceUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reon.hr.api.customer.enums.AdjustTypeEnum.BASE_ADJUSTMENT;
import static com.reon.hr.api.customer.enums.AdjustTypeEnum.SCALE_ADJUSTMENT;

/**
 * @Author: Administrator
 * @Description:
 * @Date: 2023/2/27 15:56
 * @Version: 1.0
 */
@Service("packAdjustWrapperService")
public class PackAdjustWrapperServiceImpl implements PackAdjustWrapperService {
    private static final Logger log = LoggerFactory.getLogger(PackAdjustWrapperServiceImpl.class);
    @Autowired
    private IInsurancePracticeService insurancePracticeService;

    @Autowired
    IInsurancePackResourceWrapperService insurancePackResourceWrapperService;

    @Autowired
    private PackAdjustMapper packAdjustMapper;
    @Autowired
    private GeneratorSequenceService generatorSequenceService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private IEmployeeOrderService employeeOrderService;

    @Autowired
    private IBatchImportDataService batchImportDataService;

    @Autowired
    private IDictionaryWrapperService dictionaryWrapperService;

    @Autowired
    private InsurancePracticeMapper insurancePracticeMapper;

    @Autowired
    private SingleAccountRelativeService singleAccountRelativeService;

    @Autowired
    private IInsuranceBaseWrapperService baseWrapperService;

    @Resource
    private ProdHandleInfoMapper prodHandleInfoMapper;

    @Resource(name = "insuranceRatioDubboService")
    private IInsuranceRatioWrapperService insuranceRatioWrapperService;
    private final static String EXIST_PROD = "在新福利包下面的实做已经存在该类型产品;";
    private final static String NOT_MATCH = "当前产品没有匹配上福利包;";
    private final static String COMMON_KEY = "-1";

    private final static String ERROR_DATA ="数据错误";
    private final static String RULE_DATA ="规则错误";
    private final static String ERROR_MESSAGE_01 ="填写的姓名与实做订单对应的姓名不匹配,请检查!!";
    private final static String ERROR_MESSAGE_02 ="填写的证件号与实做订单对应的证件号不匹配,请检查!!";
    private final static String ERROR_MESSAGE_03 ="选择的证件类型与实做订单对应的证件类型不匹配,请检查!!";
    private final static String ERROR_MESSAGE_04 ="基数调整时,个人基数和企业基数两列必填!!";
    private final static String ERROR_MESSAGE_05 ="调整开始月填写错误!!";
    private final static String ERROR_MESSAGE_06 ="产品数据填写有误!!";
    private final static String ERROR_MESSAGE_07 ="选择的福利包与实做中的福利包不匹配,请检查!!";
    private final static String ERROR_MESSAGE_08 ="选择的城市下未找到对应的福利包 ,请检查!!";
    private final static String ERROR_MESSAGE_09 ="调整类型选择错误!!";
    private final static String ERROR_MESSAGE_10 ="选择的旧比例不是此实做订单已有的比例,请检查!!";
    private final static String ERROR_MESSAGE_11 ="选择的新比例不是此福利包下已有的比例,请检查!!";
    private final static String ERROR_MESSAGE_12 ="选择的福利办理方和福利包不匹配,请检查!!";
    private final static String ERROR_MESSAGE_13 ="个人基数列不能为空!!";
    private final static String ERROR_MESSAGE_14 ="企业基数列不能为空!!";
    private final static String ERROR_MESSAGE_15 ="福利包数据填写有误!!";
    private final static String ERROR_MESSAGE_16 ="请检查比例是否填写!!";
    private final static String ERROR_MESSAGE_17 ="请检查是否有必填项未填写!!";
    private final static String ERROR_MESSAGE_18 ="当前操作人不是此订单的后道客服!!";
    private final static String ERROR_MESSAGE_19 ="此实做订单下未找到对应的产品信息!!";
    private final static String ERROR_MESSAGE_20 ="原比例不存在!!";
    private final static String ERROR_MESSAGE_21 ="实做订单中不存在有效数据!!";
    private final static String ERROR_MESSAGE_22 ="实做订单的原企业基数大于该比例的企业最高基数!!";
    private final static String ERROR_MESSAGE_23 ="实做订单的原企业基数小于该比例的企业最低基数!!";
    private final static String ERROR_MESSAGE_24 ="实做订单的原个人基数大于该比例的个人最高基数!!";
    private final static String ERROR_MESSAGE_25 ="实做订单的原个人基数小于该比例的个人最低基数!!";
    private final static String ERROR_MESSAGE_26 ="离职员工产品没有收费截止年月!!";
    private final static String ERROR_MESSAGE_27 ="离职员工调整产品不可超出产品最大收费截止年月!!";
    private final static String ERROR_MESSAGE_28 ="原比例不存在!!";

    private static final String ERROR_BIG = ">";
    private static final String ERROR_EQUAL = "=";
    private static final String LESS_THAN = "<";


    @Override
    public Page<PackAdjustJobVo> selectPackAdjustPage(Integer page, Integer limit, PackAdjustJobVo packAdjust) {
        Page<PackAdjustJobVo> adjustJobVoPage = new Page<>();
        List<PackAdjustJobVo> packAdjustJobs = packAdjustMapper.selectPackAdjustPage(adjustJobVoPage, packAdjust);
        setPackName(packAdjustJobs);
        adjustJobVoPage.setRecords(packAdjustJobs);
        return adjustJobVoPage;
    }

    @Override
    public PackAdjustJobVo selectJobByAdjustNo(String adjustNo) {
        PackAdjustJobVo adjustJobVo = packAdjustMapper.selectJobByAdjustNo(adjustNo);
        setPackName(Lists.newArrayList(adjustJobVo));
        return adjustJobVo;
    }

    @Override
    public Page<PackAdjustDetailVo> selectPackAdjustDetailPage(Integer page, Integer limit, String adjustNo) {
        Page<PackAdjustDetailVo> detailPage = new Page<>(page, limit);
        List<PackAdjustDetailVo> details = packAdjustMapper.selectDetailsByAdjustNo(detailPage, adjustNo);
        setPackNameAndCustName(details);
        detailPage.setRecords(details);
        return detailPage;
    }

    @Override
    public PackAdjustDetailVo selectDetailsById(Long dtailId) {
        return packAdjustMapper.selectDetailsById(dtailId);
    }

    private void setPackName(List<PackAdjustJobVo> packAdjustJobs) {
        if (CollectionUtils.isNotEmpty(packAdjustJobs)) {
            List<String> collect = packAdjustJobs.stream().map(PackAdjustJobVo::getPackCode).collect(Collectors.toList());
            Map<String, InsurancePackVo> packNameByCodeMap = insurancePackResourceWrapperService.getPackNameByCode(collect);
            for (PackAdjustJobVo adjustJobVo : packAdjustJobs) {
                InsurancePackVo packVo = packNameByCodeMap.getOrDefault(adjustJobVo.getPackCode(), new InsurancePackVo());
                adjustJobVo.setPackName(packVo.getPackName());
            }
        }
    }

    private void setPackNameAndCustName(List<PackAdjustDetailVo> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            /** 获取福利包名称 */
            List<String> packCodeList = details.stream().map(PackAdjustDetailVo::getNewPackCode).distinct().collect(Collectors.toList());
            Map<String, InsurancePackVo> packNameByCodeMap = insurancePackResourceWrapperService.getPackNameByCode(packCodeList);
            List<Long> custIdList = details.stream().map(PackAdjustDetailVo::getCustId).distinct().collect(Collectors.toList());
            List<CustomerVo> customerVos = customerService.getCustNameByCustIdList(custIdList);
            Map<Long, String> map = customerVos.stream().collect(Collectors.toMap(CustomerVo::getId, CustomerVo::getCustName));
            List<Long> empIds = details.stream().map(PackAdjustDetailVo::getEmpId).distinct().collect(Collectors.toList());
            List<EmployeeVo> employeeList = employeeOrderService.getEmployeeListByEmployeeIdList(empIds);
            Map<Long, EmployeeVo> employeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getId, Function.identity()));
            for (PackAdjustDetailVo vo : details) {
                if (packNameByCodeMap.containsKey(vo.getNewPackCode())) {
                    String packName = packNameByCodeMap.get(vo.getNewPackCode()).getPackName();
                    vo.setNewPackName(packName);
                }
                vo.setCustName(map.getOrDefault(vo.getCustId(), ""));
                if (employeMap.containsKey(vo.getEmpId())) {
                    EmployeeVo employeeVo = employeMap.get(vo.getEmpId());
                    vo.setEmpName(employeeVo.getName());
                    vo.setCertNo(employeeVo.getCertNo());
                }
            }
        }
    }

    /**
     * 根据福利包号将全部的的实做查询出来
     * 根据当前福利包的详细数据  查询这些实做能够匹配的福利包
     * <p>
     * 遍历全部的实做 如果实做中的产品能够匹配上其他福利包
     * 有两种情况
     * 1 已经存在实做  直接生成产品
     * 2 啥都没有 直接生成实做 和产品
     * <p>
     * 将原来全部实做的变为已过期 给全部有效的产品添加一个截止月。
     *
     * @param packAdjustJobVo
     */
    @Override
    public void handleAdjustPack(PackAdjustJobVo packAdjustJobVo) {
        String packCode = packAdjustJobVo.getPackCode();
        Integer startMonth = packAdjustJobVo.getStartMonth();
        Integer endMonth = Integer.parseInt(DateUtil.getPrevMonthDate(DateUtil.IntToDate(startMonth),1).replace("-",""));
        String loginName = packAdjustJobVo.getCreator();
        insertPackAdjustJob(packAdjustJobVo);
        List<PackAdjustDetailVo> detailVos = new ArrayList<>();
        InsurancePackVo insurancePackVo = insurancePackResourceWrapperService.getInsurancePackByPackCode(packCode);
        /** 未办理 已停办 是否需要转移过去*/
        List<InsurancePracticeVo> insurancePracticeVos = insurancePracticeService.queryInsurancePracticeVoByPack(packCode);
        /**过滤掉在调整月前停办的实做*/
        insurancePracticeVos = insurancePracticeVos.stream().filter(vo -> {
            if(vo.getHandleStatus().equals(InsurancePracticeEnum.HandleStatusEnum.STOP_PAYMENT.getIndex())){
                List<ProdHandleInfoVo> prodHandleInfoVoList = vo.getProdHandleInfoVoList();
                return prodHandleInfoVoList.stream().anyMatch(curr -> curr.getEndMonth() >= startMonth);
            }else {
                return true;
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insurancePracticeVos)) {
            log.info("当前福利包没有除已过期以外的实做");
            return;
        }
        /*** 把这个订单中除了当前福利包的全部有效实做查出来*/
        List<String> orderNos = insurancePracticeVos.stream().map(InsurancePracticeVo::getOrderNo).distinct().collect(Collectors.toList());
        List<InsurancePracticeVo> practiceVoList = insurancePracticeService.queryPracticeExcluePackCode(orderNos, packCode);
        Map<String, InsurancePracticeVo> orderAndPackMap = practiceVoList.stream().collect(Collectors.toMap(vo -> buildKey(vo.getOrderNo(), vo.getPackCode()), Function.identity()));
        String orgCode = insurancePackVo.getOrgCode();
        /**根据 福利办理方 福利包编号 单立户 客户id 去查询福利包*/
        List<InsurancePackVo> packList = getInsurancePackVos(orgCode,insurancePackVo.getSingleFlag(),packCode);
        Map<String, String> ratioAndPackMap = new HashMap<>();
        for (InsurancePackVo packVo : packList) {
            List<InsurancePackDetailVo> dataGrop = packVo.getDataGrop();
            if (CollectionUtils.isNotEmpty(dataGrop)) {
                for (InsurancePackDetailVo detailVo : dataGrop) {
                    ratioAndPackMap.put(detailVo.getRatioCode(), packVo.getPackCode());
                }
            }
        }
        /**
         * 根据已经存在的实做的 有效数据的产品数据，将这些数据的比例与福利包里面的比例进行比对，如果比对上了
         * 情况1  已经存在数据则直接添加一条产品数据
         * 情况2 没有数据则新增新的实做数据 以前实做什么状态之后的就是什么状态
         */
        InsurancePractice newPract;
        ProdHandleInfo newProd;
        PackAdjustDetailVo detail;
        StringBuilder sb = new StringBuilder();
        Date date = new Date();
        Map<String, InsurancePractice> packAndPracticeMap = Maps.newHashMap();
        Map<String, List<ProdHandleInfo>> packAndProd = Maps.newHashMap();
        /**修改之前的实做**/
        List<InsurancePracticeVo> editPracList = new ArrayList<>();
        List<Long> pracidList = new ArrayList<>();
        List<NoMatchPack> insertNoMatchPacks = new ArrayList<>();
        for (InsurancePracticeVo insuranceVo : insurancePracticeVos) {
            String orderNo = insuranceVo.getOrderNo();
            /**设置更新条件*/
            buildUpdateCndition(loginName, editPracList, pracidList, insuranceVo);
            List<ProdHandleInfoVo> prodHandleInfoVoList = insuranceVo.getProdHandleInfoVoList();
            /**只处理有效数据*/
            prodHandleInfoVoList = prodHandleInfoVoList.stream().filter(vo -> isValid(vo, startMonth)).collect(Collectors.toList());
            /**校验数据*/
            if (CollectionUtils.isNotEmpty(prodHandleInfoVoList)) {
                for (ProdHandleInfoVo prodHandleInfoVo : prodHandleInfoVoList) {
                    String findPackCode = ratioAndPackMap.get(prodHandleInfoVo.getRatioCode());
                    detail = new PackAdjustDetailVo();
                    detail.setAdjustNo(packAdjustJobVo.getAdjustNo());
                    sb.setLength(0);
                    if (Objects.nonNull(findPackCode)) {
                        /**匹配上福利包 查看实做是否存在如果存在则新增产品 没有则新增实做
                         * 根据订单号 和福利包去查看实做
                         * */
                        InsurancePracticeVo existPracticeVos = orderAndPackMap.get(buildKey(orderNo, findPackCode));
                        if (Objects.nonNull(existPracticeVos)) {
                            List<ProdHandleInfoVo> handleInfoVos = existPracticeVos.getProdHandleInfoVoList();
                            List<Integer> prodCode = handleInfoVos.stream().map(ProdHandleInfoVo::getProdCode).collect(Collectors.toList());
                            /**原来的实做中已经存在产品不做处理*/
                            if (!prodCode.contains(prodHandleInfoVo.getProdCode())) {
                                newProd = new ProdHandleInfo();
                                newProd.setId(null);
                                newProd.setPracticeId(existPracticeVos.getId());
                                Integer month = prodHandleInfoVo.getStartMonth() > startMonth ? prodHandleInfoVo.getStartMonth() : startMonth;
                                buildProd(month, loginName, prodHandleInfoVo, date, newProd);
                                List<ProdHandleInfo> prodHandleInfoVos = packAndProd.computeIfAbsent(COMMON_KEY, (key) -> new ArrayList<>());
                                prodHandleInfoVos.add(newProd);
                            } else {
                                sb.append(EXIST_PROD);
                            }
                            initDetails(detailVos, detail, existPracticeVos, prodHandleInfoVo, loginName, sb);
                        } else {
                            if (!packAndPracticeMap.containsKey(buildKey(orderNo, findPackCode))) {
                                /**新增实做 且不能重新新增*/
                                newPract = buildNewPrac(startMonth, loginName, date, insuranceVo, findPackCode);
                                packAndPracticeMap.put(buildKey(orderNo, findPackCode), newPract);
                            }
                            newProd = new ProdHandleInfo();
                            newProd.setId(null);
                            buildProd(startMonth, loginName, prodHandleInfoVo, date, newProd);
                            List<ProdHandleInfo> prodHandleInfoVos = packAndProd.computeIfAbsent(buildKey(orderNo, findPackCode), (key) -> new ArrayList<>());
                            prodHandleInfoVos.add(newProd);
                            initDetails(detailVos, detail, packAndPracticeMap.get(buildKey(orderNo, findPackCode)), prodHandleInfoVo, loginName, sb);
                        }
                    } else {
                        sb.append(NOT_MATCH);
                        bulidNoMatchPack(loginName, insurancePackVo, insertNoMatchPacks, insuranceVo, prodHandleInfoVo);
                        initDetails(detailVos, detail, insuranceVo, prodHandleInfoVo, loginName, sb);
                    }
                }
            }

        }


        /**修改之前的实做**/
        if (CollectionUtils.isNotEmpty(editPracList)) {
            insurancePracticeService.updateByPrimaryKeyBatch(editPracList);
        }
        /**修改之前的产品**/
        if (CollectionUtils.isNotEmpty(pracidList)) {
            insurancePracticeService.updateByInsurancePracticeIds(pracidList, endMonth, loginName);
        }

        /**新增实做**/
        Collection<InsurancePractice> values = packAndPracticeMap.values();
        if (CollectionUtils.isNotEmpty(values)) {
            insurancePracticeService.insertList(new ArrayList<>(values));
        }
        packAndPracticeMap.forEach((k, v) -> {
            if (!COMMON_KEY.equals(k)) {
                List<ProdHandleInfo> prodHandleInfos = packAndProd.get(k);
                prodHandleInfos.forEach(vo -> vo.setPracticeId(v.getId()));
            }
        });
        /**添加产品*/
        List<ProdHandleInfo> handleInfoList = packAndProd.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(handleInfoList)) {
            insurancePracticeService.insertProdList(handleInfoList);
        }

        /**添加明细*/
        if (CollectionUtils.isNotEmpty(detailVos)) {
            packAdjustMapper.insertPactAdjustDetails(detailVos);
        }
        /**添加未匹配*/
        if (CollectionUtils.isNotEmpty(insertNoMatchPacks)) {
            insurancePracticeService.insertNoMatchList(insertNoMatchPacks);
        }
    }


    private List<InsurancePackVo> getInsurancePackVos(String receiving, Integer accountFlag,String packCode) {
        List<InsurancePackVo> insurancePackVos = Lists.newArrayList();
        if (accountFlag.equals(IncomeCountTableReportEnum.AccountFlagEnum.BIG_ACCOUNT.getIndex())){
            insurancePackVos = insurancePackResourceWrapperService.getAllInsuPackByOrgCode(receiving,accountFlag,null, ValidFlagEnum.VALID.getCode());
        } else {
            List<SingleAccountRelativeVo> relatives = singleAccountRelativeService.getRelativeVosExcludeSelf(packCode, SinAccRelativeEnum.PACK.getCode());
            if(CollectionUtils.isNotEmpty(relatives)){
                List<String> packCodes = relatives.stream().map(SingleAccountRelativeVo::getRelativeNo).collect(Collectors.toList());
                List<InsurancePackVo> insurancePackByPackCode = insurancePackResourceWrapperService.getAllInsuPackByPackCodes(receiving,packCodes, InsurancePackStatus.ENABLE.getCode(),ValidFlagEnum.VALID.getCode());
                insurancePackVos.addAll(insurancePackByPackCode);
            }
        }
        return insurancePackVos;
    }
    private Boolean isValid(ProdHandleInfoVo vo, Integer startMonth) {
        return vo.getEndMonth() == null || (vo.getEndMonth() >= vo.getStartMonth() && vo.getEndMonth() >= startMonth);
    }

    private void bulidNoMatchPack(String loginName, InsurancePackVo insurancePackVo, List<NoMatchPack> insertNoMatchPacks, InsurancePracticeVo insuranceVo, ProdHandleInfoVo prodHandleInfoVo) {
        NoMatchPack noMatchPack = new NoMatchPack();
        noMatchPack.setOrderNo(insuranceVo.getOrderNo());
        noMatchPack.setOrgCode(insuranceVo.getOrgCode());
        noMatchPack.setSingleFlag(insurancePackVo.getSingleFlag());
        noMatchPack.setRatioCode(prodHandleInfoVo.getRatioCode());
        noMatchPack.setStatus(NoMatchPackStatus.UNTREATED.getCode());
        noMatchPack.setCreator(loginName);
        insertNoMatchPacks.add(noMatchPack);
    }


    private InsurancePractice buildNewPrac(Integer startMonth, String loginName, Date date, InsurancePracticeVo insuranceVo, String findPackCode) {
        InsurancePractice newPract;
        newPract = new InsurancePractice();
        BeanUtils.copyProperties(insuranceVo, newPract);
        newPract.setId(null);
        newPract.setPackCode(findPackCode);
        newPract.setProcTime(date);
        newPract.setStartMonth(startMonth);
        newPract.setCreator(loginName);
        newPract.setUpdateTime(date);
        newPract.setUpdater(loginName);
        return newPract;
    }

    private void buildUpdateCndition(String loginName, List<InsurancePracticeVo> editPracList, List<Long> pracidList, InsurancePracticeVo insuranceVo) {
        InsurancePracticeVo condition = new InsurancePracticeVo();
        condition.setId(insuranceVo.getId());
        condition.setHandleStatus(InsurancePracticeEnum.HandleStatusEnum.EXPIRE.getIndex());
        condition.setUpdater(loginName);
        editPracList.add(condition);
        pracidList.add(insuranceVo.getId());
    }

    private void initDetails(List<PackAdjustDetailVo> detailVos, PackAdjustDetailVo detail, InsurancePracticeVo existPracticeVos, ProdHandleInfoVo prodHandleInfoVo, String loginName, StringBuilder sb) {
        detail.setEmpId(existPracticeVos.getEmpId());
        detail.setCustId(existPracticeVos.getCustId());
        detail.setOrderNo(existPracticeVos.getOrderNo());
        if (Objects.nonNull(prodHandleInfoVo)) {
            detail.setProdCode(prodHandleInfoVo.getProdCode());
            detail.setRatioCode(prodHandleInfoVo.getRatioCode());
        }
        detail.setNewPackCode(existPracticeVos.getPackCode());
        detail.setDealStatus(AdjustmentOfStatusEnum.ADJUSTED.getCode());
        detail.setFailureReason(sb.toString());
        detail.setCreator(loginName);
        detail.setCreateTime(new Date());
        detail.setUpdater(loginName);
        detailVos.add(detail);
    }

    private void initDetails(List<PackAdjustDetailVo> detailVos, PackAdjustDetailVo detail, InsurancePractice existPracticeVos, ProdHandleInfoVo prodHandleInfoVo, String loginName, StringBuilder sb) {
        InsurancePracticeVo insurancePracticeVo = new InsurancePracticeVo();
        BeanUtils.copyProperties(existPracticeVos, insurancePracticeVo);
        initDetails(detailVos, detail, insurancePracticeVo, prodHandleInfoVo, loginName, sb);
    }

    private void insertPackAdjustJob(PackAdjustJobVo packAdjustJobVo) {
        packAdjustJobVo.setAdjustNo(generateAdjustNo());
        PackAdjustJob packAdjustJob = new PackAdjustJob();
        BeanUtils.copyProperties(packAdjustJobVo, packAdjustJob);
        packAdjustMapper.insert(packAdjustJob);
    }

    private void buildProd(Integer startMonth, String loginName, ProdHandleInfoVo prodHandleInfoVo, Date date, ProdHandleInfo newProd) {
        newProd.setOrderNo(prodHandleInfoVo.getOrderNo());
        newProd.setProdCode(prodHandleInfoVo.getProdCode());
        newProd.setRatioCode(prodHandleInfoVo.getRatioCode());
        newProd.setStartMonth(startMonth);
        newProd.setEndMonth(prodHandleInfoVo.getEndMonth());
        newProd.setComBase(prodHandleInfoVo.getComBase());
        newProd.setIndBase(prodHandleInfoVo.getIndBase());
        newProd.setReturnMonth(prodHandleInfoVo.getReturnMonth());
        newProd.setComAmt(prodHandleInfoVo.getComAmt());
        newProd.setIndAmt(prodHandleInfoVo.getIndAmt());
        newProd.setRemark(prodHandleInfoVo.getRemark());
        newProd.setCreator(loginName);
        newProd.setCreateTime(date);
        newProd.setUpdater(loginName);
    }


    private String generateAdjustNo() {
        String currentDate = DateUtil.getString(new Date(), "yyyyMMdd");
        Long employeeNum = generatorSequenceService.getIncrementNum("get_employee_next_sequence_" + "date_" + currentDate);
        String changeBaseCode = SequenceUtils.getSequence(employeeNum, 6);
        //调整任务编号
        return currentDate + changeBaseCode;


    }

    private String buildKey(String orderNo, String packCode) {
        return orderNo + "_" + packCode;
    }

    @Override
    public void batchInsurancePracticeAdjust(ImportDataDto<BatchInsurancePracticeAdjustImportVo> importDataDto) {
        log.info("=================开始批量调整实做====================");
        batchImportDataService.addImportData(importDataDto, ImportDataType.BATCH_ADJUSTMENT_IMPORT.getCode());
        List<BatchInsurancePracticeAdjustImportVo> dataList = importDataDto.getDataList();
        try {
            List<String> orderNoList = dataList.stream().map(BatchInsurancePracticeAdjustImportVo::getOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderNoList)){
                List<BatchAdjustmentVo> batchAdjustImportData = insurancePracticeMapper.getBatchAdjustImportData(orderNoList);
                /**
                 * 这里返回的是没有错误的导入信息
                 */
                List<BatchAdjustmentVo> noErrorBatchAdjustmentVos = checkImportData(batchAdjustImportData, importDataDto);
                List<BatchInsurancePracticeAdjustImportVo> adjustImportVos = dataList.stream().filter(vo -> vo.getErrorDescription().isEmpty()).map(vo ->{
                    BatchInsurancePracticeAdjustImportVo textVo = new BatchInsurancePracticeAdjustImportVo();
                    BeanUtils.copyProperties(vo,textVo);
                    return textVo;
                }).collect(Collectors.toList());
                Map<String, BatchInsurancePracticeAdjustImportVo> adjustVoMap = adjustImportVos.stream().collect(Collectors.toMap(BatchInsurancePracticeAdjustImportVo::getUuid, Function.identity()));
                List<String> newRatioCodeList = noErrorBatchAdjustmentVos.stream().map(BatchAdjustmentVo::getNewRatioCode).collect(Collectors.toList());
                //新比例数据
                Map<String, List<InsuranceBaseVo>> insuranceBaseByRatioCodeListMap = Maps.newHashMap();
                if(CollectionUtils.isNotEmpty(newRatioCodeList)){
                    List<InsuranceBaseVo> insuranceBaseByRatioCodeList = baseWrapperService.findInsuranceBaseByRatioCodeList(newRatioCodeList);
                    insuranceBaseByRatioCodeListMap = insuranceBaseByRatioCodeList.stream().collect(Collectors.groupingBy(InsuranceBaseVo::getInsuraceRatioCode));
                }
                for (BatchAdjustmentVo batchAdjustmentVo : noErrorBatchAdjustmentVos) {
                    BatchInsurancePracticeAdjustImportVo importData = adjustVoMap.get(batchAdjustmentVo.getUuid());
                    BatchInsurancePracticeAdjustImportVo batchInsurancePracticeAdjustImportVo = new BatchInsurancePracticeAdjustImportVo();
                    BeanUtils.copyProperties(importData,batchInsurancePracticeAdjustImportVo);
                    batchInsurancePracticeAdjustImportVo.setUqKey(UUIDUtil.getUUID());
                    Map<String, List<String>> errorDescription = importData.getErrorDescription();
                    Map<String, List<String>> map = Maps.newHashMap(errorDescription);
                    batchInsurancePracticeAdjustImportVo.setErrorDescription(map);
                    Integer expiredMonth = DateUtil.dateUtil(String.valueOf(batchAdjustmentVo.getStartMonth()));
                    int adjustStartMonth = batchAdjustmentVo.getStartMonth();
                    int adjustTypeInt = batchAdjustmentVo.getAdjustType();
                    AdjustTypeEnum adjustTypeEnum = AdjustTypeEnum.valueOf(adjustTypeInt);
                    /**
                     * 获取需要修改的订单数据,并进行校验
                     */
                    List<ProdHandleInfoVo> needAdjustInsurances = new ArrayList<>();
                    Integer accountFlag =null ;
                    if (batchAdjustmentVo.getAccountFlag()!=null){
                        accountFlag = batchAdjustmentVo.getAccountFlag();
                    }
                    List<ProdHandleInfoVo> adjustCfgList = insurancePracticeMapper.getInsurancePracticeByOrderNo(batchAdjustmentVo.getOrderNo(), batchAdjustmentVo.getProdCode(), batchAdjustmentVo.getPackCode(),accountFlag);
                    if (CollectionUtils.isNotEmpty(adjustCfgList)){
                        if (adjustTypeInt != BASE_ADJUSTMENT.getCode()) {
                            List<ProdHandleInfoVo> collect = adjustCfgList.stream().filter(o -> o.getRatioCode().equals(batchAdjustmentVo.getOldRatioCode())).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(collect)) {
                                batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_20);
                            }
                        }
                        Map<Boolean, List<ProdHandleInfoVo>> adjustOrderInsuranceCfgVos = getAdjustOrderInsuranceCfgVos(adjustCfgList, batchAdjustmentVo.getOldRatioCode(), batchAdjustmentVo.getStartMonth());
                        needAdjustInsurances = adjustOrderInsuranceCfgVos.getOrDefault(true,Lists.newArrayList());
                    }else {
                        batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA,ERROR_MESSAGE_19);
                    }
                    /**如果此次调整连匹配的产品都没有则不需要往下走了,直接结束*/
                    if (CollectionUtils.isEmpty(needAdjustInsurances)) {
                        batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA,  ERROR_MESSAGE_21);
                    }
                    for (ProdHandleInfoVo needAdjustInsurance : needAdjustInsurances) {
                        if(adjustTypeEnum==SCALE_ADJUSTMENT){
                            String msg = checkAndSetBase(needAdjustInsurance,batchAdjustmentVo.getNewRatioCode(), insuranceBaseByRatioCodeListMap);
                            if (StringUtils.isNotBlank(msg)) {
                                batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, InsuranceIRatioProductCodeEnum.getName(batchAdjustmentVo.getProdCode()) + "---->" + msg);
                            }
                        }
                        Integer oldEndMonth = needAdjustInsurance.getEndMonth();
                        if (isDimissionOrWaitConfirm(needAdjustInsurance)) {
                            if (oldEndMonth == null) {
                                batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_26);
                            } else {
                                if (batchAdjustmentVo.getStartMonth() > oldEndMonth) {
                                    batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_27);
                                }
                            }
                        }
                    }

                    /**没有错误信息开始调整*/
                    if (batchInsurancePracticeAdjustImportVo.getErrorDescription().isEmpty()) {
                        for (ProdHandleInfoVo needAdjustInsurance : needAdjustInsurances) {
                            int revStartMonth = needAdjustInsurance.getStartMonth();
                            Integer oldEndMonth = needAdjustInsurance.getEndMonth();
                            List<Integer> revStartMonthList = adjustCfgList.stream().map(ProdHandleInfoVo::getStartMonth).collect(Collectors.toList());
                            /**判断调整类型,调整类型(1:基数调整，2:比例替换，3:基数调整+比例替换)*/
                            switch (adjustTypeEnum) {
                                case BASE_ADJUSTMENT:
                                    baseAdjustment(needAdjustInsurance, adjustStartMonth,revStartMonth, oldEndMonth, batchAdjustmentVo,importDataDto.getLoginName());
                                    break;
                                case SCALE_ADJUSTMENT:
                                    scaleAdjustment(needAdjustInsurance, adjustStartMonth,revStartMonth, oldEndMonth, batchAdjustmentVo,importDataDto.getLoginName(), batchInsurancePracticeAdjustImportVo);
                                    break;
                                case BASE_AND_SCALE_ADJUSTMENT:
                                    baseRatioAdjustment(needAdjustInsurance, adjustStartMonth,revStartMonth, oldEndMonth, batchAdjustmentVo,importDataDto.getLoginName(), batchInsurancePracticeAdjustImportVo);
                                    break;
                                default:
                            }
                        }
                    }

                    recordLogImport(importDataDto, batchInsurancePracticeAdjustImportVo);
                }
            }

                /**记录导入日志*/
            batchImportDataService.addAndupdateImportDatas(importDataDto);


        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    public List<BatchAdjustmentVo> checkImportData(List<BatchAdjustmentVo> batchAdjustImportData,ImportDataDto<BatchInsurancePracticeAdjustImportVo> importDataDto){
        Map<Integer, String> productTypeMap = dictionaryWrapperService.getAllByCodeType("PRODUCT_IND_TYPE");
        Map<String, Integer> productMap = productTypeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
        Map<String, List<BatchAdjustmentVo>> orderAdjustMap = batchAdjustImportData.stream().collect(Collectors.groupingBy(BatchAdjustmentVo::getOrderNo));
        List<BatchInsurancePracticeAdjustImportVo> dataList = importDataDto.getDataList();
        ArrayList<BatchAdjustmentVo> batchAdjustmentVos = new ArrayList<>();
        for (BatchInsurancePracticeAdjustImportVo adjustImportVo : dataList) {
            String adjustType = adjustImportVo.getAdjustTypeStr();
            AdjustTypeEnum adjustTypeEnum = AdjustTypeEnum.getName(adjustType);
            String uuid = UUIDUtil.getUUID();
            adjustImportVo.setUuid(uuid);
            List<BatchAdjustmentVo> batchAdjustmentVos1 = orderAdjustMap.get(adjustImportVo.getOrderNo());
            if (CollectionUtils.isNotEmpty(batchAdjustmentVos1)){
                BatchAdjustmentVo batchAdjustmentVo1 = new BatchAdjustmentVo();
                BeanUtils.copyProperties(adjustImportVo,batchAdjustmentVo1);
                if (StringUtils.isNotEmpty(adjustImportVo.getAccountFlagStr())){
                    Integer accountFlag = IncomeCountTableReportEnum.AccountFlagEnum.getIndex(adjustImportVo.getAccountFlagStr());
                    batchAdjustmentVo1.setAccountFlag(accountFlag);
                }
                BatchAdjustmentVo batchAdjustmentVo = batchAdjustmentVos1.get(0);
                // boolean containsName = batchAdjustmentVos1.stream().map(BatchAdjustmentVo::getEmpName).anyMatch(name -> name.equals(adjustImportVo.getName()));
                if (!importDataDto.getLoginName().equals(batchAdjustmentVo.getLaterMan())){
                    adjustImportVo.updateError(RULE_DATA, ERROR_MESSAGE_18);
                }
                if (!batchAdjustmentVo.getEmpName().equals(adjustImportVo.getEmpName())){
                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_01);
                }
                if (!Objects.isNull(adjustImportVo.getCertNo())&&!adjustImportVo.getCertNo().equals(batchAdjustmentVo.getCertNo())){
                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_02);
                }
                if (!Objects.isNull(adjustImportVo.getCertTypeStr())){
                    CertType typeByName = CertType.getTypeByName(adjustImportVo.getCertTypeStr());
                    if (typeByName.getCode()!=batchAdjustmentVo.getCertType()){
                        adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_03);
                    }
                }
                if (!BatchImportExcelCommonUtil.isValidDate(String.valueOf(adjustImportVo.getStartMonth()), "type")) {
                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_05);
                }
                if (!Objects.isNull(adjustImportVo.getProdName())&&!Objects.isNull(adjustImportVo.getOrgName())&&!Objects.isNull(adjustImportVo.getPackName())){
                    String prodName = splitParams(adjustImportVo.getProdName());
                    Integer prodCode = productMap.getOrDefault(prodName, null);
                    batchAdjustmentVo1.setProdCode(prodCode);

                    String pack = splitParams(adjustImportVo.getPackName());
                    String packCode = pack.substring(0, 4) + "-" + pack.substring(4);
                    batchAdjustmentVo1.setPackCode(packCode);

                    boolean containData = batchAdjustmentVos1.stream().anyMatch(vo -> vo.getPackCode().equals(packCode) && Objects.equals(vo.getProdCode(), prodCode));
                    if (!containData){
                        adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_07);
                    }
                    if (adjustTypeEnum!=null){
                        if (adjustTypeEnum.getCode()!=AdjustTypeEnum.BASE_ADJUSTMENT.getCode()){
                            if (!Objects.isNull(adjustImportVo.getNewRatioName())&&!Objects.isNull(adjustImportVo.getOldRatioName())){
                                String newRatioCode = splitParams(adjustImportVo.getNewRatioName());
                                String oldRatioCode = splitParams(adjustImportVo.getOldRatioName());
                                batchAdjustmentVo1.setNewRatioCode(newRatioCode);
                                batchAdjustmentVo1.setOldRatioCode(oldRatioCode);
                                /**判断当前比例是否在 城市和福利包中存在**/
                                List<BatchInsurancePracticeAdjustVo> packNameAndRatioNameList = insurancePackResourceWrapperService.getBatchInsurancePracticeAdjustPackNameAndRatioName(adjustImportVo.getCityName());
                                Map<String, List<BatchInsurancePracticeAdjustVo>> packCodeMap = packNameAndRatioNameList.stream().collect(Collectors.groupingBy(BatchInsurancePracticeAdjustVo::getPackCode));
                                if (!packCodeMap.containsKey(packCode)){
                                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_08);
                                }
                                String orgCode = splitParams(adjustImportVo.getOrgName());
                                batchAdjustmentVo1.setOrgCode(orgCode);
                                boolean containOldRatioData = batchAdjustmentVos1.stream().anyMatch(vo -> vo.getPackCode().equals(packCode)
                                        && Objects.equals(vo.getProdCode(), prodCode)
                                        && vo.getRatioCode().equals(oldRatioCode)
                                        && vo.getOrgCode().equals(orgCode));
                                if (!containOldRatioData){
                                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_10);
                                }

                                List<BatchInsurancePracticeAdjustVo> batchInsurancePracticeAdjustVos = packCodeMap.get(packCode);
                                boolean containNewRatioData = batchInsurancePracticeAdjustVos.stream().anyMatch(vo -> vo.getPackCode().equals(packCode)
                                        && Objects.equals(vo.getProductCode(), prodCode)
                                        && vo.getInsuranceRatioCode().equals(newRatioCode)
                                        && vo.getOrgCode().equals(orgCode));
                                if (!containNewRatioData){
                                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_11);
                                }
                                boolean containOrgCodeData = batchInsurancePracticeAdjustVos.stream().anyMatch(vo ->vo.getOrgCode().equals(orgCode));
                                if (!containOrgCodeData){
                                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_12);
                                }
                            }else {
                                adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_16);
                            }

                        }
                        if (adjustTypeEnum.getCode()==AdjustTypeEnum.BASE_ADJUSTMENT.getCode()||adjustTypeEnum.getCode()==AdjustTypeEnum.BASE_AND_SCALE_ADJUSTMENT.getCode()){
                            if (Objects.isNull(adjustImportVo.getIndCol())){
                                adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_13);
                            }else {
                                batchAdjustmentVo1.setIndBase(new BigDecimal(adjustImportVo.getIndCol()));
                            }
                            if (Objects.isNull(adjustImportVo.getComCol())){
                                adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_14);
                            }else {
                                batchAdjustmentVo1.setComBase(new BigDecimal(adjustImportVo.getComCol()));
                            }
                        }
                        batchAdjustmentVo1.setAdjustType(adjustTypeEnum.getCode());
                    }else {
                        adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_09);
                    }
                }else {
                    adjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_17);
                }


                if (!adjustImportVo.getErrorDescription().isEmpty()) {
                    importDataDto.getImportDataLogVoList().add(batchImportDataService.createImportDataLogVo(adjustImportVo, importDataDto.getImportNo(), importDataDto.getLoginName()));
                    importDataDto.recordError(adjustImportVo.getRowNum(), JSONUtils.toJSONString(adjustImportVo.getErrorDescription()));
                } else {
                    batchAdjustmentVo1.setUuid(uuid);
                    batchAdjustmentVo1.setCreator(importDataDto.getLoginName());
                    batchAdjustmentVos.add(batchAdjustmentVo1);
                }

            }



        }
        return batchAdjustmentVos;
    }

    private String splitParams(String params) {
        String[] paramArr = params.split("_");
        return paramArr[paramArr.length - 1];
    }

    private Map<Boolean, List<ProdHandleInfoVo>> getAdjustOrderInsuranceCfgVos(List<ProdHandleInfoVo> adjustCfgList, String oldRatioCode, int startMonth2) {
        Map<Boolean, List<ProdHandleInfoVo>> collect = adjustCfgList.stream().collect(Collectors.groupingBy(vo -> (StringUtils.isEmpty(oldRatioCode) || oldRatioCode.equals(vo.getRatioCode())) &&
                (vo.getStartMonth() >= startMonth2 ||
                        (vo.getStartMonth() < startMonth2 && (vo.getEndMonth() == null || vo.getEndMonth() >= startMonth2)))));
        return collect;
    }

    private String checkAndSetBase(ProdHandleInfoVo insuranceCfgVo,String newRatioCode, Map<String, List<InsuranceBaseVo>> insuranceBaseByRatioCodeListMap) {
        String msg = "";
        if (insuranceBaseByRatioCodeListMap.containsKey(newRatioCode)) {
            List<InsuranceBaseVo> insuranceBaseList = insuranceBaseByRatioCodeListMap.get(newRatioCode);
            insuranceBaseList = insuranceBaseList.stream().filter(insuranceBaseVo -> insuranceCfgVo.getStartMonth() >= insuranceBaseVo.getValidFrom() && insuranceCfgVo.getStartMonth() <= insuranceBaseVo.getValidTo()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insuranceBaseList)) {
                InsuranceBaseVo insuranceBaseVo = insuranceBaseList.get(0);
                if (insuranceCfgVo.getComBase() != null) {
                    if (insuranceCfgVo.getComBase().compareTo(insuranceBaseVo.getHighBaseCom()) > 0) {
                        //企业最高基数
                        msg += ERROR_MESSAGE_22;
                    } else if (insuranceCfgVo.getComBase().compareTo(insuranceBaseVo.getLowBaseCom()) < 0) {
                        //企业最低基数
                        msg += ERROR_MESSAGE_23;
                    }
                }
                if (insuranceCfgVo.getIndBase() != null) {
                    if (insuranceCfgVo.getIndBase().compareTo(insuranceBaseVo.getHighBaseInd()) > 0) {
                        msg += ERROR_MESSAGE_24;
                    } else if (insuranceCfgVo.getIndBase().compareTo(insuranceBaseVo.getLowBaseInd()) < 0) {
                        msg += ERROR_MESSAGE_25;
                    }
                }
            }
        }
        return msg;
    }

    private void checkAndSetBase(BatchAdjustmentVo batchAdjustmentVo) {
        List<InsuranceBaseVo> insuranceBaseList = baseWrapperService.findInsuranceBaseByCode(batchAdjustmentVo.getOldRatioCode());
        insuranceBaseList = insuranceBaseList.stream()
                .filter(insuranceBaseVo -> batchAdjustmentVo.getStartMonth() >= insuranceBaseVo.getValidFrom() && batchAdjustmentVo.getStartMonth() <= insuranceBaseVo.getValidTo()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insuranceBaseList)) {
            InsuranceBaseVo insuranceBaseVo = insuranceBaseList.get(0);
            if (batchAdjustmentVo.getComBase() != null) {
                if (batchAdjustmentVo.getComBase().compareTo(insuranceBaseVo.getHighBaseCom()) > 0) {
                    batchAdjustmentVo.setComBase(insuranceBaseVo.getHighBaseCom());//企业最高基数
                } else if (batchAdjustmentVo.getComBase().compareTo(insuranceBaseVo.getLowBaseCom()) < 0) {
                    batchAdjustmentVo.setComBase(insuranceBaseVo.getLowBaseCom());//企业最低基数
                }
            }
            if (batchAdjustmentVo.getIndBase() != null) {
                if (batchAdjustmentVo.getIndBase().compareTo(insuranceBaseVo.getHighBaseInd()) > 0) {
                    batchAdjustmentVo.setIndBase(insuranceBaseVo.getHighBaseInd());
                } else if (batchAdjustmentVo.getIndBase().compareTo(insuranceBaseVo.getLowBaseInd()) < 0) {
                    batchAdjustmentVo.setIndBase(insuranceBaseVo.getLowBaseInd());
                }
            }
        }
    }

    private boolean isDimissionOrWaitConfirm(ProdHandleInfoVo batchAdjustmentVo) {
        return batchAdjustmentVo.getEedStatus() == EmployeeEntryDimissionStatus.DIMISSION.getCode()
                || batchAdjustmentVo.getOrderStatus() == EmployeeOrderStatus.SUB_NEED_RECEIVING_CONFIRM.getCode()
                || batchAdjustmentVo.getOrderStatus() == EmployeeOrderStatus.SUB_NEED_DISTDOM_CONFIRM.getCode();
    }


    /**
     * @param prodHandleInfo 要调整的主体信息
     * @param adjustStartMonth  调整开始月
     * @param revStartMonth 原始开始月
     * @param oldEndMonth 原截止月
     * @param batchAdjustmentVo 导入的调整的信息
     */
    public void baseAdjustment(ProdHandleInfoVo prodHandleInfo, Integer adjustStartMonth,Integer revStartMonth,Integer oldEndMonth,BatchAdjustmentVo batchAdjustmentVo,String loginName) {
        String mark = getMark(adjustStartMonth, revStartMonth);
        //根据社保组code 和 社保比例code  得到社保组比例
        InsuranceRatioVo insuranceRatioCode = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(new ArrayList<String>(Collections.singleton(prodHandleInfo.getRatioCode()))).stream().findFirst().get();
        batchAdjustmentVo.setOldRatioCode(insuranceRatioCode.getInsuranceRatioCode());
        if (mark.equals(ERROR_BIG)) {
            ProdHandleInfo info = new ProdHandleInfo();
            BeanUtils.copyProperties(prodHandleInfo,info);
            info.setCreator(loginName);
            info.setEndMonth(oldEndMonth);
            info.setStartMonth(adjustStartMonth);
            info.setId(null);
            //给原产品数据修改
            Integer newEndMonth = DateUtil.getPreYearMonth(adjustStartMonth);
            prodHandleInfoMapper.updateEndMonthById(newEndMonth, prodHandleInfo.getId());
            //新增一条产品数据
            //调整企业基数和个人基数
            checkAndSetBase(batchAdjustmentVo);
            info.setIndBase(batchAdjustmentVo.getIndBase());
            info.setComBase(batchAdjustmentVo.getComBase());
            //计算金额
            getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
            //新增一条产品数据
            info.setComAmt(batchAdjustmentVo.getComAmt());
            info.setIndAmt(batchAdjustmentVo.getIndAmt());
            prodHandleInfoMapper.insertSelective(info);
        }
        //如果调整起始月小于或者等于收费起始月,就直接给原数据的基数进行修改就好
        if (mark.equals(ERROR_EQUAL) || mark.equals(LESS_THAN)) {
            //计算金额
            checkAndSetBase(batchAdjustmentVo);
            prodHandleInfo.setComBase(batchAdjustmentVo.getComBase());
            prodHandleInfo.setIndBase(batchAdjustmentVo.getIndBase());
            getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
            prodHandleInfo.setIndAmt(batchAdjustmentVo.getIndAmt());
            prodHandleInfo.setComAmt(batchAdjustmentVo.getComAmt());
            prodHandleInfo.setUpdater(loginName);
            //给原产品数据修改
            prodHandleInfoMapper.updateProdByPrimaryKeySelective(prodHandleInfo);
        }
    }

    /**
     * @param prodHandleInfo 要调整的主体信息
     * @param adjustStartMonth  调整开始月
     * @param revStartMonth 原始开始月
     * @param oldEndMonth 原截止月
     * @param batchAdjustmentVo 导入的调整的信息
     */
    private void scaleAdjustment(ProdHandleInfoVo prodHandleInfo, Integer adjustStartMonth,Integer revStartMonth,Integer oldEndMonth,BatchAdjustmentVo batchAdjustmentVo,String loginName,BatchInsurancePracticeAdjustImportVo batchInsurancePracticeAdjustImportVo) {
        String mark = getMark(adjustStartMonth, revStartMonth);
        //获取原比例和新比例
        String oldRatioCode = batchAdjustmentVo.getOldRatioCode();
        String newRatioCode = batchAdjustmentVo.getNewRatioCode();
        //获取产品比例编号
        String ratioCode = prodHandleInfo.getRatioCode();
        if (oldRatioCode.equals(ratioCode)) {

            //根据社保组code 和 社保比例code  得到社保组比例
            InsuranceRatioVo insuranceRatioCode = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(new ArrayList<>(Collections.singleton(newRatioCode))).stream().findFirst().get();

            if (mark.equals(ERROR_BIG)) {
                ProdHandleInfo info = new ProdHandleInfo();
                BeanUtils.copyProperties(prodHandleInfo,info);
                info.setCreator(loginName);
                info.setEndMonth(oldEndMonth);
                info.setStartMonth(adjustStartMonth);
                info.setId(null);
                info.setRatioCode(batchAdjustmentVo.getNewRatioCode());
                //给原产品数据修改
                prodHandleInfoMapper.updateEndMonthById(DateUtil.getPreYearMonth(adjustStartMonth), prodHandleInfo.getId());
                //计算金额
                batchAdjustmentVo.setComBase(prodHandleInfo.getComBase());
                batchAdjustmentVo.setIndBase(prodHandleInfo.getIndBase());
                getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
                info.setIndAmt(batchAdjustmentVo.getIndAmt());
                info.setComAmt(batchAdjustmentVo.getIndAmt());
                //新增一条产品数据
                prodHandleInfoMapper.insertSelective(info);
            }
            if (mark.equals(ERROR_EQUAL) || mark.equals(LESS_THAN)) {
                prodHandleInfo.setRatioCode(newRatioCode);
                //根据社保组code 和 社保比例code  得到社保组比例
                insuranceRatioCode = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(new ArrayList<>(Collections.singleton(prodHandleInfo.getRatioCode()))).stream().findFirst().get();
                //计算金额
                batchAdjustmentVo.setComBase(prodHandleInfo.getComBase());
                batchAdjustmentVo.setIndBase(prodHandleInfo.getIndBase());
                getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
                //给原产品数据修改
                prodHandleInfo.setUpdater(loginName);
                prodHandleInfo.setIndAmt(batchAdjustmentVo.getIndAmt());
                prodHandleInfo.setComAmt(batchAdjustmentVo.getComAmt());
                prodHandleInfoMapper.updateProdByPrimaryKeySelective(prodHandleInfo);
            }
        } else {
            batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_20);
        }
    }


    public void baseRatioAdjustment(ProdHandleInfoVo prodHandleInfo, Integer adjustStartMonth,Integer revStartMonth,Integer oldEndMonth,BatchAdjustmentVo batchAdjustmentVo,String loginName,BatchInsurancePracticeAdjustImportVo batchInsurancePracticeAdjustImportVo) {
        if (batchAdjustmentVo.getOldRatioCode().equals(prodHandleInfo.getRatioCode())) {
            String mark = getMark(adjustStartMonth, revStartMonth);
            //调整成功新增调整明细
            //根据社保组code 和 社保比例code  得到社保组比例
            InsuranceRatioVo insuranceRatioCode = insuranceRatioWrapperService.getInsuranceRatioByRatioCodeList(new ArrayList<>(Collections.singleton(batchAdjustmentVo.getNewRatioCode()))).stream().findFirst().get();
            batchAdjustmentVo.setOldRatioCode(batchAdjustmentVo.getNewRatioCode());
            if (mark.equals(ERROR_BIG)) {
                ProdHandleInfo info = new ProdHandleInfo();
                BeanUtils.copyProperties(prodHandleInfo,info);
                info.setCreator(loginName);
                info.setEndMonth(oldEndMonth);
                info.setStartMonth(adjustStartMonth);
                info.setId(null);
                info.setRatioCode(batchAdjustmentVo.getNewRatioCode());
                //给原产品数据修改
                prodHandleInfoMapper.updateEndMonthById(DateUtil.getPreYearMonth(adjustStartMonth), prodHandleInfo.getId());
                //新增一条产品数据
                //调整企业基数和个人基数
                checkAndSetBase(batchAdjustmentVo);
                info.setComBase(batchAdjustmentVo.getComBase());
                info.setIndBase(batchAdjustmentVo.getIndBase());
                //计算金额
                getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
                info.setIndAmt(batchAdjustmentVo.getIndAmt());
                info.setComAmt(batchAdjustmentVo.getComAmt());
                prodHandleInfoMapper.insertSelective(info);
            }
            if (mark.equals(ERROR_EQUAL) || mark.equals(LESS_THAN)) {
                //调整企业基数和个人基数
                checkAndSetBase(batchAdjustmentVo);
                prodHandleInfo.setComBase(batchAdjustmentVo.getComBase());
                prodHandleInfo.setIndBase(batchAdjustmentVo.getIndBase());
                //计算金额
                getSocialSecurityRatioBaseAmount(batchAdjustmentVo, insuranceRatioCode);
                prodHandleInfo.setIndAmt(batchAdjustmentVo.getIndAmt());
                prodHandleInfo.setComAmt(batchAdjustmentVo.getComAmt());
                prodHandleInfo.setUpdater(loginName);
                //给原产品数据修改
                prodHandleInfoMapper.updateProdByPrimaryKeySelective(prodHandleInfo);
            }

        } else {
            batchInsurancePracticeAdjustImportVo.updateError(ERROR_DATA, ERROR_MESSAGE_20);
        }

    }


    private void getSocialSecurityRatioBaseAmount(BatchAdjustmentVo batchAdjustmentVo,
                                                  InsuranceRatioVo oneByParams) {
        CalculateArgs args = new CalculateArgs();
        args.setComArgs(batchAdjustmentVo.getComBase(), oneByParams.getComRatio(), oneByParams.getComAdd(), oneByParams.getComCalcMode(), oneByParams.getComExactVal());
        args.setIndArgs(batchAdjustmentVo.getIndBase(), oneByParams.getIndRatio(), oneByParams.getIndlAdd(), oneByParams.getIndCalcMode(), oneByParams.getIndExactVal());
        args.setSpecialFlag(oneByParams.getSpecialFlag());
        CalculateUtil.calculateAmt(args);
        batchAdjustmentVo.setComAmt(args.getComAmt());
        batchAdjustmentVo.setIndAmt(args.getIndAmt());
    }

    private String getMark(int startMonth, int revStartMonth) {
        String mark;
        if (startMonth > revStartMonth) {
            mark = ERROR_BIG;
        } else if (startMonth < revStartMonth) {
            mark = LESS_THAN;
        } else {
            mark = ERROR_EQUAL;
        }
        return mark;
    }

    private void recordLogImport(ImportDataDto<BatchInsurancePracticeAdjustImportVo> importDataDto, BatchInsurancePracticeAdjustImportVo batchAdjustImportVo) {
        importDataDto.getImportDataLogVoList().add(batchImportDataService.createAdjustImportDataLogVos(batchAdjustImportVo, importDataDto.getImportNo(), importDataDto.getLoginName()));
        if (!batchAdjustImportVo.getErrorDescription().isEmpty()) {
            importDataDto.recordErrorMsg(batchAdjustImportVo.getUqKey(), JSONUtils.toJSONString(batchAdjustImportVo.getErrorDescription()));
        }
    }
}
