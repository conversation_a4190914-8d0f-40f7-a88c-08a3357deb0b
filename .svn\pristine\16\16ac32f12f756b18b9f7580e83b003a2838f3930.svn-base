package com.reon.hr.api.bill.enums;


import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

@Getter
public enum ApprovalStatusEnum {
    /**
     * 审批中
     */
    APPROVING(1, "蓝票待开票"),
    /**
     * 驳回
     */
    REJECT(2, "蓝票开票驳回"),
    /**
     * 通过
     */
    PASS(3, "蓝票开票中"),
    /**
     * 开票
     */
    INVOICE(4, "蓝票已开票"),
    ALL_ABOLITION_APPROVAL(5, "红票待开票(全部)"),
    HAS_BEEN_ALL_ABOLISHED(6, "红票已开票(全部)"),
    ALL_ABOLISHING(7, "红票开票中(全部)"),
    PARTIALLY_ABOLITION_APPROVAL(8, "红票待开票(部分)"),
    HAS_BEEN_PARTIALLY_ABOLITION(9, "红票已开票(部分)"),
    PARTIALLY_ABOLISHING(10, "红票开票中(部分)"),
    ;

    private final int code;

    private final String msg;

    ApprovalStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public static String getMsgByCode(Integer code){
        String msg = null;
        if (code != null){
            for (ApprovalStatusEnum statusEnum : ApprovalStatusEnum.values()) {
                if (statusEnum.getCode() == code){
                    msg = statusEnum.getMsg();
                }
            }
        }
        return msg;
    }
    public static final List<Integer> all_list = Lists.newArrayList(
            APPROVING.code,
            PASS.code,
            INVOICE.code,
            ALL_ABOLITION_APPROVAL.code,
            ALL_ABOLISHING.code,
            PARTIALLY_ABOLITION_APPROVAL.code,
            HAS_BEEN_PARTIALLY_ABOLITION.code,
            PARTIALLY_ABOLISHING.code,
            REJECT.code
    );

    public static final List<Integer> ABOLISHED_LIST = Lists.newArrayList(
            ALL_ABOLITION_APPROVAL.code,
            HAS_BEEN_ALL_ABOLISHED.code,
            ALL_ABOLISHING.code,
            PARTIALLY_ABOLITION_APPROVAL.code,
            HAS_BEEN_PARTIALLY_ABOLITION.code,
            PARTIALLY_ABOLISHING.code
    );

    public static boolean hasBlueInvoice(List<Integer> statusList) {
        return statusList.stream().anyMatch(BLUE_INVOICE_LIST::contains);
    }

    public static final List<Integer> BLUE_INVOICE_LIST = Lists.newArrayList(
            APPROVING.code,
            PASS.code,
            INVOICE.code,
            ALL_ABOLITION_APPROVAL.code,
            ALL_ABOLISHING.code,
            PARTIALLY_ABOLITION_APPROVAL.code,
            HAS_BEEN_PARTIALLY_ABOLITION.code,
            PARTIALLY_ABOLISHING.code
    );

    public static Integer getFailedStatus(Integer code) {
        if (code == ApprovalStatusEnum.ALL_ABOLISHING.getCode()) {
            return ApprovalStatusEnum.ALL_ABOLITION_APPROVAL.getCode();
        } else if (code == ApprovalStatusEnum.PARTIALLY_ABOLISHING.getCode()) {
            return ApprovalStatusEnum.PARTIALLY_ABOLITION_APPROVAL.getCode();
        }
        return ApprovalStatusEnum.ALL_ABOLITION_APPROVAL.getCode();
    }
}
