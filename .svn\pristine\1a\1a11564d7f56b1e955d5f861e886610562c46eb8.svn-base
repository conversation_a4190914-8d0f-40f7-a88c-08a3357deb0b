package com.reon.hr.sp.base.dao.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.CompanyBankSearchVo;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.sp.base.entity.sys.CompanyBank;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyBankMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CompanyBank record);

    int insertSelective(CompanyBank record);

    CompanyBankVo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CompanyBank record);

    int updateByPrimaryKey(CompanyBank record);
    List<CompanyBankVo> selectCompBank(CompanyBankSearchVo vo, Page page);
    List<CompanyBank> selectByCompNo(@Param("compNo") String  compNo,@Param("type") Integer type);
    List<CompanyBankVo> getAllComp(@Param("compNo")String compNo,@Param("type") Integer type);
    List<CompanyBankVo> getAllComp();
    List<CompanyBankVo> getCompanyBanks(@Param("args") CompanyBankVo vo);

    void addSpecialCompanyBank(CompanyBankVo vo);

    CompanyBankVo getSpecialCompanyBankByCustId(@Param("custId") Long custId);

    void updateSpecialCompanyBankById(CompanyBankVo vo);
}