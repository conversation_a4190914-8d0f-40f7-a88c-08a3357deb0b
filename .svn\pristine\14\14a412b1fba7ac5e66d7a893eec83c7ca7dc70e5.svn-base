package com.reon.hr.sp.bill.service.impl.salary;

import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.base.dubbo.service.rpc.sys.IMessageWrapperService;
import com.reon.hr.api.base.enums.MsgTypeEnum;
import com.reon.hr.api.base.vo.MessageVo;
import com.reon.hr.api.bill.enums.SupplierGenBillStatus;
import com.reon.hr.api.bill.enums.SupplierVerificationStatus;
import com.reon.hr.api.bill.utils.JsonUtil;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.bill.vo.bill.PerSupplierSalaryBillVo;
import com.reon.hr.api.bill.vo.salary.SupplierSalaryBillVo;
import com.reon.hr.api.customer.dubbo.service.rpc.ISupplierWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISalaryPayWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.salary.employee.ISupplierSalaryInfoWrapperService;
import com.reon.hr.api.customer.enums.WithholdingAgentEnum;
import com.reon.hr.api.customer.vo.salary.pay.SalaryBillSearchVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryBillVo;
import com.reon.hr.api.customer.vo.salary.pay.SalaryBillVoDto;
import com.reon.hr.api.customer.vo.salary.pay.SalaryInfoVo;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;
import com.reon.hr.api.util.ListUtils;
import com.reon.hr.rabbitmq.MqMessageSender;
import com.reon.hr.rabbitmq.enums.ModuleType;
import com.reon.hr.rabbitmq.enums.bill.ProducerScopeTypeBill;
import com.reon.hr.sp.bill.dao.bill.InsuranceBillMapper;
import com.reon.hr.sp.bill.dao.cus.BillCostMapper;
import com.reon.hr.sp.bill.dao.cus.PerSalaryBillMapper;
import com.reon.hr.sp.bill.dao.salary.SupplierSalaryBillMapper;
import com.reon.hr.sp.bill.entity.cus.BillCost;
import com.reon.hr.sp.bill.entity.cus.PerSalaryBill;
import com.reon.hr.sp.bill.service.bill.salary.SupplierSalaryBillService;
import com.reon.hr.sp.bill.utils.GetTableName;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SupplierSalaryBillServiceImpl implements SupplierSalaryBillService {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private SupplierSalaryBillMapper supplierSalaryBillMapper;
    @Resource
    private MqMessageSender mqMessageSender;
    @Autowired
    private InsuranceBillMapper insuranceBillMapper;
    @Autowired
    private ISupplierSalaryInfoWrapperService supplierSalaryInfoWrapperService;
    @Autowired
    private BillCostMapper billCostMapper;
    @Autowired
    private ISupplierWrapperService supplierWrapperService;
    @Autowired
    private PerSalaryBillMapper perSalaryBillMapper;
    @Autowired
    private IMessageWrapperService iMessageWrapperService;

    @Resource
    private ISalaryPayWrapperService salaryPayWrapperService;

    private static final int OWN_COMPANY = 0;//自有公司
    private static final int SUPPLIER = 1;//供应商
    private static final int CUSTOMER = 2;//单立户

    @Override
    public void updateSupplierVerificationStatus(List<Long> ids, Integer code,String loginName) {
        if(CollectionUtils.isNotEmpty(ids)){
            supplierSalaryBillMapper.updateSupplierVerificationStatus(ids,code,loginName);
        }
    }

    @Override
    public void saveByIds(List<Long> billIds, Integer code,String loginName,List<String> billIdAndSupplierIdList,Integer supplierGenTimes) {
        boolean allGenerateFlag=true;
        logger.info("==================开始生成供应商工资账单=========================bill_ids" + billIds);
        List<SupplierAreaVo> supplierAreaVoList=supplierWrapperService.getAllAggregationCommissioner();
        Map<Long, SupplierAreaVo> supplierAreaVoMap = supplierAreaVoList.stream().collect(Collectors.toMap(SupplierAreaVo::getSupplierId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> mapMq = new HashMap<String, String>();
        if(CollectionUtils.isNotEmpty(billIds)){
            List<SupplierSalaryBillVo> supplierSalaryBillVoList=supplierSalaryBillMapper.selectByBillIdList(billIds);
            if(CollectionUtils.isEmpty(billIdAndSupplierIdList)){
                List<com.reon.hr.api.customer.vo.salary.pay.SupplierSalaryBillVo> queryVoList = ListUtils.copyListBean(supplierSalaryBillVoList, com.reon.hr.api.customer.vo.salary.pay.SupplierSalaryBillVo.class);
                billIdAndSupplierIdList=supplierSalaryInfoWrapperService.getBySupplierSalaryBillVoList(queryVoList);
            }else {
                allGenerateFlag=false;
            }

            Map<Long, String> templetNameByBillIdMap = supplierSalaryBillVoList.stream().collect(Collectors.toMap(SupplierSalaryBillVo::getBillId, SupplierSalaryBillVo::getTempletName, (v1, v2) -> v1));
            Map<String, SupplierSalaryBillVo> oldVoMap = supplierSalaryBillVoList.stream().filter(s->s.getSupplierId()!=null)
                    .collect(Collectors.toMap(s -> s.getBillId() + "-" + s.getSupplierId(), Function.identity()));
            Set<String> oldBillIdAndSupplierIds = oldVoMap.keySet();
            List<SupplierSalaryBillVo> newSupplierSalaryBillVoList=new ArrayList<>();
            List<SupplierSalaryBillVo> updateSupplierSalaryBillVoList=new ArrayList<>();
            List<SupplierSalaryBillVo> deleteSupplierSalaryBillVoList=new ArrayList<>();
            List<String> newBillIdAndSupplierIds=new ArrayList<>();
            Set<Long> newBillIds=new HashSet<>();
            for (String billIdAndSupplierId:billIdAndSupplierIdList) {
                String[] split = billIdAndSupplierId.split("-");
                Long billId=Long.parseLong(split[0]);
                Long supplierId=Long.parseLong(split[1]);
                if(!oldBillIdAndSupplierIds.contains(billIdAndSupplierId)){
                    //第一次推送，第一次生成
                    SupplierSalaryBillVo supplierSalaryBillVo = new SupplierSalaryBillVo();
                    supplierSalaryBillVo.setBillId(billId).setSupplierVerificationStatus(code).setSupplierEmployeeNum(0)
                                    .setSupplierReceiveAmt(BigDecimal.ZERO).setSupplierGenStatus(SupplierGenBillStatus.KEEPING.getCode())
                                    .setSupplierGenTimes(0).setCreator(loginName).setSupplierServiceNum(0).setSupplierId(supplierId)
                            .setTempletName(templetNameByBillIdMap.get(billId));
                    newSupplierSalaryBillVoList.add(supplierSalaryBillVo);
                    newBillIdAndSupplierIds.add(billIdAndSupplierId);
                    newBillIds.add(billId);
                }else {
                    //推送或者重复生成
                    SupplierSalaryBillVo oldVo = oldVoMap.get(billIdAndSupplierId);
                    oldVo.setSupplierEmployeeNum(0)
                            .setSupplierReceiveAmt(BigDecimal.ZERO).setSupplierGenStatus(SupplierGenBillStatus.KEEPING.getCode())
                            .setUpdater(loginName).setSupplierServiceNum(0);
                    if(oldVo.getSupplierGenTimes()>=10){
                        if(supplierGenTimes==null){
                            //超过次数不能再生成
                            continue;
                        }
                    }
                    if(code!=null){
                        if(!code.equals(SupplierVerificationStatus.CREATE_AFTER_DELETION)){
                            oldVo.setSupplierVerificationStatus(code);
                        }
                    }else {
                        oldVo.setSupplierGenMan(loginName);
                        if(supplierGenTimes==null){
                            oldVo.setSupplierGenTimes(oldVo.getSupplierGenTimes()+1);
                        }
                        if(oldVo.getSupplierGenFirstTime()==null){
                            oldVo.setSupplierGenFirstTime(new Date());
                        }
                        if(oldVo.getSupplierVerificationStatus()== SupplierVerificationStatus.SUPPLIER_CONFIRMATION.getCode()
                        ||oldVo.getSupplierVerificationStatus()== SupplierVerificationStatus.PROJECT_CONFIRMATION.getCode()){
                            //确认的不能再生成
                            continue;
                        }
                    }
                    updateSupplierSalaryBillVoList.add(oldVo);
                    newBillIdAndSupplierIds.add(billIdAndSupplierId);
                    newBillIds.add(billId);
                }
            }
            List<SupplierSalaryBillVo> needSendMessageVoList=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(newSupplierSalaryBillVoList)){
                supplierSalaryBillMapper.insertListVo(newSupplierSalaryBillVoList);
                needSendMessageVoList.addAll(newSupplierSalaryBillVoList);
            }
            if(CollectionUtils.isNotEmpty(updateSupplierSalaryBillVoList)){
                supplierSalaryBillMapper.updateListVo(updateSupplierSalaryBillVoList);
                needSendMessageVoList.addAll(updateSupplierSalaryBillVoList);
            }
            List<MessageVo> messageVoList=new ArrayList<>();
            if(code!=null&&code==SupplierVerificationStatus.PUSHED.getCode()){
                for (SupplierSalaryBillVo needSendMessageVo:needSendMessageVoList) {
                    SupplierAreaVo supplierAreaVo = supplierAreaVoMap.get(needSendMessageVo.getSupplierId());
                    String commissioner = supplierAreaVo.getCommissioner();
                    String creator=StringUtils.isNotBlank(needSendMessageVo.getUpdater())?needSendMessageVo.getUpdater():needSendMessageVo.getCreator();
                    String content = IMessageWrapperService.MESSAGE_SUPPLIER_SALARY_BILL_PUSH.replace("{supplierName}", supplierAreaVo.getSupplierName());
                    content = content.replace("{templetName}", needSendMessageVo.getTempletName());
                    MessageVo messageVo = iMessageWrapperService.onlySetMessageVo(MsgTypeEnum.SUPPLIER_SALARY_BILL_PUSH.getCode(), MsgTypeEnum.SUPPLIER_SALARY_BILL_PUSH_TITLE[0],
                            commissioner, content,creator);
                    messageVoList.add(messageVo);
                }
            }
            for (String key:oldBillIdAndSupplierIds) {
                String[] split = key.split("-");
                Long billId=Long.parseLong(split[0]);
                Long supplierId=Long.parseLong(split[1]);
                //之前有，新的没有，就要删掉之前的
                if(allGenerateFlag&&newBillIds.contains(billId)&&!newBillIdAndSupplierIds.contains(key)){
                    SupplierSalaryBillVo supplierSalaryBillVo = new SupplierSalaryBillVo();
                    supplierSalaryBillVo.setBillId(billId).setSupplierId(supplierId);
                    newSupplierSalaryBillVoList.add(supplierSalaryBillVo);
                    deleteSupplierSalaryBillVoList.add(supplierSalaryBillVo);
                }
            }
            if(CollectionUtils.isNotEmpty(deleteSupplierSalaryBillVoList)){
                supplierSalaryBillMapper.deletByListVo(deleteSupplierSalaryBillVoList);
            }
            for (String billIdAndSupplierId : newBillIdAndSupplierIds) {
                mapMq.put("billIdAndSupplierIds", JsonUtil.beanToJson(Collections.singletonList(billIdAndSupplierId)));
                mapMq.put("creator", loginName);
                mqMessageSender.sendMsgAfterCommit(ModuleType.REON_BILL, ProducerScopeTypeBill.REON_BILL_GENERATED_SUPPLIER_SALARY_COMPLETED, JsonUtil.beanToJson(mapMq));
            }
            if(CollectionUtils.isNotEmpty(messageVoList)){
                iMessageWrapperService.batchSave (messageVoList);
            }
        }
    }

    @Override
    public void updateBySelectiveVo(SupplierSalaryBillVo supplierSalaryBillVo) {
        supplierSalaryBillMapper.updateBySelectiveVo(supplierSalaryBillVo);
    }

    @Override
    public List<SupplierSalaryBillVo> getSupplierEmpSalaryInformation(List<String> billIdAndSupplierIds,String creator) {
        logger.info("==============开始查询生成供应商工资账单的准备数据==============");
        List<SupplierSalaryBillVo> billVoList = Lists.newArrayList();
        for (String billIdAndSupplierId : billIdAndSupplierIds) {
            String[] split = billIdAndSupplierId.split("-");
            Long billId=Long.parseLong(split[0]);
            Long supplierId=Long.parseLong(split[1]);
            InsuranceBillVo insuranceBillVo = insuranceBillMapper.selectByPrimaryKey(billId);
            SupplierSalaryBillVo billVo = new SupplierSalaryBillVo();
            BeanUtils.copyProperties(insuranceBillVo,billVo);
            billVo.setSupplierId(supplierId);
            SalaryBillSearchVo vo = new SalaryBillSearchVo();
            vo.setCustId(billVo.getCustId());
            vo.setContractNo(billVo.getContractNo());
            vo.setTempletId(billVo.getTempletId());
            vo.setBillMonth(billVo.getBillMonth());
            vo.setSupplierId(supplierId);
            vo.setCreator(creator);
            List<com.reon.hr.api.customer.vo.salary.pay.SupplierSalaryBillVo> listEmp = supplierSalaryInfoWrapperService.getSupplierEmpBill(vo);
            List<PerSupplierSalaryBillVo> perSalaryBillVos = Lists.newArrayList();
            for (com.reon.hr.api.customer.vo.salary.pay.SupplierSalaryBillVo salaryBillVo : listEmp) {
                PerSupplierSalaryBillVo perSalaryBillVo = new PerSupplierSalaryBillVo();
                BeanUtils.copyProperties(salaryBillVo, perSalaryBillVo);
                perSalaryBillVos.add(perSalaryBillVo);
            }
            billVo.setPerSupplierSalaryBillVos(perSalaryBillVos);
            billVoList.add(billVo);
        }
        return billVoList;
    }

    @Override
    public void savePerSupplierSalaryInsurance(SupplierSalaryBillVo vo) throws Exception {
        if (vo != null) {
            List<PerSupplierSalaryBillVo> perSupplierSalaryBillVos = vo.getPerSupplierSalaryBillVos();

            Map<String, List<PerSupplierSalaryBillVo>> listEmpMap = perSupplierSalaryBillVos.stream().
                    filter(salaryBillVo -> salaryBillVo.getSupplierServiceFee().compareTo(BigDecimal.ZERO) > 0).
                    collect(Collectors.groupingBy(p->p.getWithholdingAgentType()+"-"+p.getOrgCode()+"-"+p.getCityCode()+"-"+p.getSupplierQuotationNo()));
            List<BillCost> billCostList=new ArrayList<>();
            for (String key:listEmpMap.keySet()) {
                List<PerSupplierSalaryBillVo> salaryBillVos = listEmpMap.get(key);
                PerSupplierSalaryBillVo salaryBillVo = salaryBillVos.get(0);
                BillCost billCost = new BillCost();
                billCost.setBillId(vo.getId());
                billCost.setReceiving(salaryBillVo.getOrgCode());
                billCost.setServiceNum(salaryBillVos.size());
                billCost.setCreator(vo.getCreator());
                billCost.setCreateTime(new Date());
                billCost.setCityCode(salaryBillVo.getCityCode());
                BigDecimal salaryFee =BigDecimal.ZERO;
                billCost.setReceivingType(SUPPLIER);
                if(StringUtils.isNotBlank(salaryBillVo.getSupplierQuotationNo())){
                    salaryFee=salaryBillVo.getSupplierServiceFee();
                    billCost.setSupQuotationNo(salaryBillVo.getSupplierQuotationNo());
                }
                billCost.setErrorRemark(salaryBillVo.getErrorRemark());
                billCost.setServiceFee(salaryFee);
                if(salaryFee!=null){
                    billCost.setTotalFee(salaryFee.multiply(new BigDecimal(salaryBillVos.size())));
                    billCostList.add(billCost);
                }
            }
            BigDecimal supplierCost=BigDecimal.ZERO;
            Integer serviceNum=0;
            if(CollectionUtils.isNotEmpty(billCostList)){
                billCostMapper.deleteSupplierByBillIdAndSupplierId(vo.getId(),vo.getSupplierId());
                billCostMapper.batchInsert(billCostList);
                supplierCost = billCostList.stream().filter(billCost -> billCost.getReceivingType().equals(SUPPLIER)).map(BillCost::getTotalFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                serviceNum = billCostList.stream().filter(billCost -> billCost.getReceivingType().equals(SUPPLIER)).map(BillCost::getServiceNum).reduce(0,Integer::sum);
            }
            vo.setSupplierCost(supplierCost);
            vo.setSupplierServiceNum(serviceNum);
            vo.setSupplierEmployeeNum(perSupplierSalaryBillVos.stream().map(PerSupplierSalaryBillVo::getEmployeeId).distinct().collect(Collectors.toList()).size());
            vo.setSupplierReceiveAmt(perSupplierSalaryBillVos.stream().map(PerSupplierSalaryBillVo::getCustPayAmt).reduce(BigDecimal.ZERO,BigDecimal::add));

            //账单回写
            updateSupplierSalaryBill(vo);
        }
    }

    @Override
    public Page<SupplierSalaryBillVo> getListByParams(Integer page, Integer limit, Map<String, Object> params) {
        Page<SupplierSalaryBillVo> billVoPage = new Page<>(page, limit);
        List<SupplierAreaVo> supplierAreaVoList=supplierWrapperService.getAllAggregationCommissioner();
        List<Long> supplierIdList =null;
        if(!params.containsKey("userOrgPositionDtoList")){
            String loginName = params.get("loginName").toString();
            supplierIdList = supplierAreaVoList.stream().filter(s -> loginName.equals(s.getCommissioner()) || loginName.equals(s.getPurchaser())).map(SupplierAreaVo::getSupplierId).distinct().collect(Collectors.toList());
        }
        params.put("supplierIdList",supplierIdList);
        //if(supplierIdList!=null&& supplierIdList.size()==0){
        //    billVoPage.setRecords(new ArrayList<>());
        //    return billVoPage;
        //}
        Map<Long, SupplierAreaVo> supplierAreaVoMap = supplierAreaVoList.stream().collect(Collectors.toMap(SupplierAreaVo::getSupplierId, Function.identity(), (v1, v2) -> v1));
        List<SupplierSalaryBillVo> supplierSalaryBillVoList= supplierSalaryBillMapper.getListByParams(billVoPage,params);
        for (SupplierSalaryBillVo supplierSalaryBillVo:supplierSalaryBillVoList) {
            SupplierAreaVo supplierAreaVo = supplierAreaVoMap.get(supplierSalaryBillVo.getSupplierId());
            supplierSalaryBillVo.setSupplierName(supplierAreaVo.getSupplierName());
            supplierSalaryBillVo.setSupplierCommissioner(supplierAreaVo.getCommissioner());

        }
        billVoPage.setRecords(supplierSalaryBillVoList);
        return billVoPage;
    }
    @Override
    public List<SupplierSalaryBillVo> getListByParams(Map<String, Object> params) {
        List<SupplierAreaVo> supplierAreaVoList=supplierWrapperService.getAllAggregationCommissioner();
        List<Long> supplierIdList =null;
        if(!params.containsKey("userOrgPositionDtoList")){
            String loginName = params.get("loginName").toString();
            supplierIdList = supplierAreaVoList.stream().filter(s -> loginName.equals(s.getCommissioner()) || loginName.equals(s.getPurchaser())).map(SupplierAreaVo::getSupplierId).distinct().collect(Collectors.toList());
        }
        Object supplierCommissionerObject = params.get("supplierCommissioner");
        if(supplierCommissionerObject!=null){
            String supplierCommissioner = supplierCommissionerObject.toString();
            supplierIdList = supplierAreaVoList.stream().filter(s -> supplierCommissioner.equals(s.getCommissioner())).map(SupplierAreaVo::getSupplierId).distinct().collect(Collectors.toList());
            if(supplierIdList.size()==0){
                return new ArrayList<>();
            }
        }
        params.put("supplierIdList",supplierIdList);
        Map<Long, SupplierAreaVo> supplierAreaVoMap = supplierAreaVoList.stream().collect(Collectors.toMap(SupplierAreaVo::getSupplierId, Function.identity(), (v1, v2) -> v1));
        List<SupplierSalaryBillVo> supplierSalaryBillVoList= supplierSalaryBillMapper.getListByParams(params);
        for (SupplierSalaryBillVo supplierSalaryBillVo:supplierSalaryBillVoList) {
            SupplierAreaVo supplierAreaVo = supplierAreaVoMap.get(supplierSalaryBillVo.getSupplierId());
            supplierSalaryBillVo.setSupplierName(supplierAreaVo.getSupplierName());
            supplierSalaryBillVo.setSupplierCommissioner(supplierAreaVo.getCommissioner());

        }
        return supplierSalaryBillVoList;
    }

    @Override
    public void saveSupplierSalaryBill(SupplierSalaryBillVo supplierSalaryBillVo) {
        List<String> billIdAndSupplierIds=supplierSalaryBillMapper.getGenerateBillIdAndSupplierIdsByVo(supplierSalaryBillVo);
        if(CollectionUtils.isNotEmpty(billIdAndSupplierIds)){
            List<Long> billIds = billIdAndSupplierIds.stream().map(s -> Long.parseLong(s.split("-")[0])).distinct().collect(Collectors.toList());
            saveByIds(billIds,null,supplierSalaryBillVo.getCreator(),billIdAndSupplierIds,supplierSalaryBillVo.getSupplierGenTimes());
        }
    }

    @Override
    public List<SupplierSalaryBillVo> getByBillIds(List<Long> billIds) {
        return supplierSalaryBillMapper.getByBillIds(billIds);
    }

    @Override
    public void updateSupplierVerificationStatusByList(List<SupplierSalaryBillVo> supplierSalaryBillVoList) {
        if(CollectionUtils.isNotEmpty(supplierSalaryBillVoList)){

            supplierSalaryBillMapper.updateSupplierVerificationStatusByList(supplierSalaryBillVoList);
        }
    }

    @Override
    public List<SupplierSalaryBillVo> getSupplierSalaryTemplet(Map<String, Object> params) {
        return supplierSalaryBillMapper.getSupplierSalaryTemplet(params);
    }

    @Override
    public int updateSupplierGenTimes() {
        return supplierSalaryBillMapper.updateSupplierGenTimes();
    }

    @Override
    public SalaryBillVoDto getSalaryBillVoListByBillIdList(List<Long> billIdList, List<Long> supplierIdList) {
        SalaryBillVoDto dto = new SalaryBillVoDto();
        List<SupplierSalaryBillVo> supplierSalaryBillVoList = supplierSalaryBillMapper.selectByBillIdList(billIdList);
        Map<String, List<SupplierSalaryBillVo>> voListMap = supplierSalaryBillVoList.stream().filter(i->{
            if(CollectionUtils.isNotEmpty(supplierIdList)){
                return supplierIdList.contains(i.getSupplierId());
            }else {
                return true;
            }
        }).collect(Collectors.groupingBy(i->
                i.getContractNo()+"-"+i.getBillMonth()+"-"+i.getTempletId()+"-"+i.getCustId()+"-"+i.getSupplierId()));
        List<SalaryBillVo> salaryBillVoAllList=new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        int sum=0;
        for (String key:voListMap.keySet()) {
            SupplierSalaryBillVo vo = voListMap.get(key).get(0);
            List<PerSalaryBill> perSalaryEmpList = perSalaryBillMapper.getAllPerSalaryEmp(GetTableName.getTableNamePerSalaryBill(vo.getBillMonth()), vo.getContractNo(), vo.getTempletId(), vo.getCustId());
            Integer billMonth = vo.getBillMonth();
            List<Long> empId = perSalaryEmpList.stream().map(PerSalaryBill::getEmployeeId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(empId)){
                List<SalaryInfoVo> salaryInfoVoList = salaryPayWrapperService.getSalaryInfoVoByEmpIdAndBillMonth(empId, billMonth);
                List<String> withholdingAgentNos = salaryInfoVoList.stream().map(SalaryInfoVo::getWithholdingAgentNo).distinct().collect(Collectors.toList());
                List<WithholdingAgentVo> withholdingAgentList = salaryPayWrapperService.getWithholdingAgentNoList(withholdingAgentNos);
                boolean allMatch = withholdingAgentList.stream().noneMatch(item -> item.getWithholdingAgentType().equals(WithholdingAgentEnum.WithholdingAgentTypeEnum.WITHHOLDING_AGENT_TYPE3.getIndex()));
                if(allMatch){
                    sum+=1;
                    String templetName = vo.getTempletName();
                    sb.append(templetName).append(";");
                }
            }

            if(vo.getSupplierId()!=null){
                if(CollectionUtils.isNotEmpty(perSalaryEmpList)){
                    List<SalaryBillVo> salaryBillVoList = ListUtils.copyListBean(perSalaryEmpList, SalaryBillVo.class);
                    for (SalaryBillVo salaryBillVo:salaryBillVoList) {
                        salaryBillVo.setOrgCode(vo.getSupplierId().toString());
                    }
                    salaryBillVoAllList.addAll(salaryBillVoList);
                }
            }
        }
        dto.setSalaryBillVos(salaryBillVoAllList);
        String sbStr = sb.toString();
        if(sum>0){
            sbStr="共"+sum+"条数据没有供应商类型的扣缴义务人。账套名称依次为:"+sbStr;
        }
        dto.setMsg(sbStr);
        return dto;
    }

    @Override
    public List<SupplierSalaryBillVo> getSalaryBillVoListBySalaryEmpDetailVoList(List<Map<String, Object>> salaryEmpDetailVos) {
        return supplierSalaryBillMapper.getSalaryBillVoListBySalaryEmpDetailVoList(salaryEmpDetailVos);
    }

    @Override
    public boolean getPushed(Long billId) {
        return supplierSalaryBillMapper.getPushed(billId);
    }

    /**
     * 修改InsuranceBill和SupplierSalaryBill表中金额的某些属性
     *
     * @param billVo
     * @param
     */
    private void updateSupplierSalaryBill(SupplierSalaryBillVo billVo) {
        billVo.setBillId(billVo.getId());
        billVo.setSupplierGenStatus(SupplierGenBillStatus.SUCCESSED.getCode());
        supplierSalaryBillMapper.updateBySelectiveVo(billVo);
    }
}
