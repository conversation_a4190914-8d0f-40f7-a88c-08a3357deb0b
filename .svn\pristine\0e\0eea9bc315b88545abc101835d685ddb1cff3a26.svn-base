var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        laydate = layui.laydate,
        form = layui.form;
    layer = parent.layer === undefined ? layui.layer : parent.layer,tableSelect = layui.tableSelect;

    var tableView = '';
    var resDataLength = 0;


    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('paymentApplyTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });

    table.render({
        id: 'paymentApplyTable',
        elem: '#paymentApplyTable',
        url: ML.contextPath + '/bill/oneFee/getApproveDifferencesPage',
        where:  {"paramData": JSON.stringify(serialize("searchForm"))},
        method: 'get',
        toolbar: '#toolbarDemo',
        page: true, //默认为不开启
        limits: [10, 20, 50,100,200],
        limit: 10,
        height: 650,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', fixed: 'left', align: 'center'},
            {field: 'orgCode', title: '福利办理方',align:'center', width: '15%'},
            {field: 'amt', title: '导入差异金额',align:'center',width: '8%'},
            {field: 'importNo', title: '导入编号',align:'center',width: '10%'},
            {field: 'applyAmt', title: '支付申请金额',align:'center',width: '8%'},
            {field: 'actPayAmt', title: '实际支付金额',align:'center',width: '8%'},
            {field: 'fileId', title: '导入文件',align:'center',width: '13%', templet: function (d) {
                    var split = d.fileId.split(",");
                    return '<a href="' + ML.fileServerUrl + split[0] + '" target="_blank" class="layui-table-link" style="text-decoration:underline" >' + split[1] + '</a>';
                }},
            {field: 'creator', title: '导入人',align:'center',width: '8%', templet: function (d) {
                    return ML.loginNameFormater(d.creator)
                }},

        ]],
        done: function (res) {
            ML.hideNoAuth();
            tableView = this.elem.next();
            resDataLength = res.data.length;
        }
    });



    // 监听表格上方的按钮
    table.on('toolbar(paymentApplyFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;

        var url;
        var button;
        switch (obj.event) {
            case 'approved':
                if(data.length!=1){
                    return layer.msg("请选择一条数据！");
                }
                let param =[];
                data.forEach(function (item) {
                    param.push(item.id)
                });
                approve(param,"通过审批",3)
                break;
            case 'rejectTask':
                if(data.length==0){
                    return layer.msg("请选择一条数据！");
                }
                let is =[];
                data.forEach(function (item) {
                    is.push(item.id)
                });
                approve(is,"驳回",4)
                break;

        }
    })

    function approve(ids,title,type) {
        layer.confirm("你确定要"+title+"么？", {btn: ['确定', '取消']}, function () {
            $.ajax({
                type: "POST",
                url: ML.contextPath + "/bill/oneFee/approveDifferences",
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    ids: ids,
                    approveStatus: type
                }),
                success: function (data) {
                    layer.msg(data.msg);
                    reloadTable();
                },
                error: function (data) {
                    layer.msg(data);
                    console.log("error")
                }
            });
        });
    }


    form.on('submit(paymentApplyFilter)', function (data) {
        table.reload('paymentApplyTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });




    //重载数据
    function reloadTable() {
        table.reload('paymentApplyTable', {
            where: {
                paramData: JSON.stringify(getSearchForm()),
            }
        });
    }
    function getSearchForm() {
        var value = serialize("searchForm");
        value.matchFlag=true;
        return value;
    }
    $("#reset").click(function () {
        $("#distComName").removeAttr("ts-selected");
        form.render();
    });









});