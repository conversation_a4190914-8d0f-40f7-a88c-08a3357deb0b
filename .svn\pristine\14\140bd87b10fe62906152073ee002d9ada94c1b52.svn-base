<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<form class="layui-form">
    <table class="layui-table" lay-skin="nob" style="width: 100%">
    <input id="selectedGroup" type="hidden"/>
        <tr id="dimissId">
            <td width="12%" align="right" style="font-weight:800"><i style="color: red">*</i>报离职起始时间</td>
            <td width="10%"><input class="layui-input startTime" type="text" placeholder="yyyy-MM-dd" lay-verify="required" readonly></td>
            <td width="12%" align="right" style="font-weight:800">报离职截止时间</td>
            <td width="10%"><input class="layui-input endTime" type="text" placeholder="yyyy-MM-dd" readonly></td>
            <td width="10%" align="right" style="font-weight:800">客户名称</td>
            <td width="10%"><input type="text" class="layui-input custName" autocomplete="off"></td>
            <td width="10%" align="right" style="font-weight:800">项目城市</td>
            <td width="10%">
                <div class="layui-input-block" style="width: 120px;margin-left: 0px;">
                    <select name="distPlace" id="distPlace" lay-search="" AREA_TYPE>
                        <option value=""></option>
                    </select>
                </div></td>
        </tr>
        <tr>
            <td  align="right" style="font-weight:800">接单城市</td>
            <td >
                <div class="layui-input-block" style="width: 120px;margin-left: 0px;">
                    <select name="cityCode" id="cityCode" lay-search="" AREA_TYPE>
                        <option value=""></option>
                    </select>
                </div></td>
            <td align="right" style="font-weight:800">接单方</td>
            <td >
                <div class="layui-input-block" style="width: 120px;margin-left: 0px;">
                    <input type="text" class="layui-input" id="receiving" name="receiving" autocomplete="off" readonly>
                </div>
            </td>
            <td align="right" style="font-weight:900" >接单方客服</td>
            <td >
                <div class="layui-input-block" style="width: 100px;margin-left: 0px;">
                    <select class="layui-select" name="commissioner" id="commissioner" lay-search lay-filter="commissionerFilter">
                        <option value=""></option>
                    </select>
                </div>
            </td>
            <td>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="是否单立户" style="font-weight:800">是否单立户</label>
                    <div class="layui-input-block">
                        <select name="accountFlag" id="accountFlag" DICT_TYPE="BOOLEAN_TYPE">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
            </td>
            <td colspan="2" align="center">
                <button class="layui-btn layui-btn-sm" lay-submit lay-filter="dimExportFilter">导出</button>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/report/employeeDimReport.js?v=${publishVersion}"></script>
</body>
</html>