package com.reon.hr.sp.dubbo.service.rpc.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.dubbo.service.rpc.sys.IUserOrgPosWrapperService;
import com.reon.hr.api.vo.sys.UserOrgPosVo;
import com.reon.hr.sp.service.sys.IUserOrgPosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service("userOrgPosDubboService")
public class UserOrgPosWrapperServiceImpl implements IUserOrgPosWrapperService {

    @Autowired
    private IUserOrgPosService userOrgPosService;

    @Override
    public int saveMany(List<UserOrgPosVo> userOrgPos, String updater) {
        return userOrgPosService.saveMany(userOrgPos, updater);
    }

    @Override
    public Page<UserOrgPosVo> getPageByUserId(Integer page, Integer limit, Long userId, String position,String loginName) {
        return userOrgPosService.getPageByUserId(page, limit, userId, position,loginName);
    }

    @Override
    public List<UserOrgPosVo> getInfoByUserId(Long userId) {
        return userOrgPosService.getInfoByUserId(userId);
    }

    @Override
    public List<UserOrgPosVo> getByOrgCodeAndPosCode(String orgCode, String posCode) {
        return userOrgPosService.getByOrgCodeAndPosCode(orgCode, posCode);
    }

    @Override
    public List<String> getByCityCodeAndPosCode(Integer cityCode, String posCode, String orgType) {
        return userOrgPosService.getByCityCodeAndPosCode(cityCode, posCode, orgType);
    }

    @Override
    public  UserOrgPosVo getLeaderByOrgCodeAndPosCode(String orgCode, String posCode) {
        return userOrgPosService.getLeaderByOrgCodeAndPosCode(orgCode, posCode);
    }

    @Override
    public List<UserOrgPosVo> getFinanceByOrgCodeAndPosCode(String orgCode, String posCode) {
        return userOrgPosService.getFinanceByOrgCodeAndPosCode(orgCode, posCode);
    }

    @Override
    public  List<UserOrgPosVo> getLeaderByOrgCodeAndPosCodeList(List<UserOrgPosVo> userOrgPosVoList) {
        return userOrgPosService.getLeaderByOrgCodeAndPosCodeList(userOrgPosVoList);
    }



    @Override
    public UserOrgPosVo getSupplierCommissioner(String commissioner) {
        return userOrgPosService.getSupplierCommissioner(commissioner);
    }

    @Override    public UserOrgPosVo getDefaultFlagOrgPosByLoginName(String loginName) {
        return userOrgPosService.getDefaultFlagOrgPosByLoginName(loginName);
    }
    @Override
    public UserOrgPosVo getSalaryApproveOrgPos(String loginName, String orgCode) {
        // 判断orgCode如果是 12位则截取前9位
        orgCode = orgCode.length() == 12 ? orgCode.substring(0, 9) : orgCode;
        return userOrgPosService.getSalaryApproveOrgPos(loginName,orgCode);
    }

    @Override
    public UserOrgPosVo getInsurancePayApproveOrgPos(String loginName) {
        return userOrgPosService.getInsurancePayApproveOrgPos(loginName);
    }

    @Override
    public List<UserOrgPosVo> getAllOrgCodeAndPosCode(String loginName) {
        return userOrgPosService.getAllOrgCodeAndPosCode(loginName);
    }

    @Override
    public List<UserOrgPosVo> getByOrgCodeLikeAndPosCode(String orgCode, String posCode) {
        return userOrgPosService.getByOrgCodeLikeAndPosCode(orgCode, posCode);
    }
}
