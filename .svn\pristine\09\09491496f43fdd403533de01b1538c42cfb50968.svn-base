/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2022/6/8
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.bill.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 <AUTHOR>
 @version 1.0 */
@Data
public class BillReportSecondSheetDto implements Serializable {
    private String custName;
    private String contractName;
    private String contractNo;
    private String contractTypeName;
    private Long billId;
    /** 账单模板名称 */
    private String templetName;
    /** 人数 */
    private Integer population;
    /** 人次 */
    private Integer personTime;
    /** 社保/公积金企业金额 */
    private BigDecimal comSubAllTotal = BigDecimal.ZERO;
    /** 社保/公积金个人金额 */
    private BigDecimal indSubAllTotal = BigDecimal.ZERO;
    /** 工资+个税总额 */
    private BigDecimal salaryAndTaxTotal = BigDecimal.ZERO;
    /** 一次性其他费用 */
    private BigDecimal oneChargesOtherAmtTotal = BigDecimal.ZERO;
    /** 服务费金额 */
    private BigDecimal serviceAmtTotal = BigDecimal.ZERO;
    /** 税费 */
    private BigDecimal taxTotal = BigDecimal.ZERO;
    /** 一次性服务费 */
    private BigDecimal oneChargeServiceAmtTotal = BigDecimal.ZERO;
    /** 账单合计 */
    private BigDecimal receiveAmt = BigDecimal.ZERO;
    /** 账单Id Str */
    private String billIdStr;

    /** 账户全称 */
    private String compName;
    /** 开户银行 */
    private String bankName;
    /** 银行账号 */
    private String bankNo;
}
