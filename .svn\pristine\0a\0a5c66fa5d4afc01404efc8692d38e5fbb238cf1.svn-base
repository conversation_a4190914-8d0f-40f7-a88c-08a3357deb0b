package com.reon.hr.sp.customer.entity.employee;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeRemark {
    private Long id;

    private Long customerId;

    private String contractNo;

    private Long employeeId;

    private String remark1;

    private String remark2;

    private String remark3;

    private String remark4;

    private String remark5;
    private String remark6;
    private String remark7;
    private String remark8;
    private Integer remarkType;

    private String receivingRemark;
    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private String orderNo;

    private String accuAcctNo;


}