package com.reon.hr.sp.base.dubbo.rpc.sys.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.dubbo.service.rpc.sys.ICompanyBankWrapperService;
import com.reon.hr.api.base.vo.CompanyBankSearchVo;
import com.reon.hr.api.base.vo.CompanyBankVo;
import com.reon.hr.sp.base.service.sys.ICompanyBankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> guoqian
 * @date 2021/3/10 0010 18:08
 * @title
 * @modify
 */
@Service("companyBankWrapperService")
public class CompanyBankWrapperServiceImpl implements ICompanyBankWrapperService {
    @Autowired
    ICompanyBankService iCompanyBankService;
    @Override
    public Page<CompanyBankVo> selectCompBank(CompanyBankSearchVo vo) {
        return iCompanyBankService.selectCompBank(vo);
    }

    @Override
    public void saveCompBank(CompanyBankVo vo) {
        iCompanyBankService.saveCompBank(vo);

    }

    @Override
    public CompanyBankVo selectCompById(CompanyBankSearchVo vo) {
        return iCompanyBankService.selectCompById(vo);
    }

    @Override
    public List<CompanyBankVo> getAllComp(String compNo,Integer type) {
        return iCompanyBankService.getAllComp(compNo,type);
    }

    @Override
    public List<CompanyBankVo> getAllComp() {
        return iCompanyBankService.getAllComp();
    }

    @Override
    public List<CompanyBankVo> getCompanyBanks(CompanyBankVo vo) {
        return iCompanyBankService.getCompanyBanks(vo);
    }

    @Override
    public void addSpecialCompanyBank(CompanyBankVo vo) {
        iCompanyBankService.addSpecialCompanyBank(vo);
    }

    @Override
    public void updateSpecialCompanyBankById(CompanyBankVo vo) {
        iCompanyBankService.updateSpecialCompanyBankById(vo);
    }

    @Override
    public CompanyBankVo getSpecialCompanyBankByCustId(Long custId) {
        return iCompanyBankService.getSpecialCompanyBankByCustId(custId);
    }

    @Override
    public CompanyBankVo getCompanyBankByDisComToPay(String disCom) {
        return iCompanyBankService.getCompanyBankByDisComToPay(disCom);
    }
}