package com.reon.hr.sp.customer.dubbo.service.rpc.impl.insurancePractice;


import com.google.common.collect.Lists;
import com.reon.hr.api.customer.dubbo.service.rpc.ICustomerWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.insurancePractice.IPracticeSuppliPayCfgWrapperService;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.batchImport.NewSuppliPayCfgVo;
import com.reon.hr.api.customer.vo.batchImport.SuppliPayCfgVo;
import com.reon.hr.sp.customer.service.insurancePractice.IPracticeSuppliPayCfgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("practiceSuppliPayCfgWrapperService")
public class PracticeSuppliPayCfgWrapperService implements IPracticeSuppliPayCfgWrapperService {

    @Autowired
    private IPracticeSuppliPayCfgService practiceSuppliPayCfgService;

    @Autowired
    private ICustomerWrapperService customerService;


    @Override
    public void addSuppliPayCfg(SuppliPayCfgVo suppliPayCfgVo) {
        practiceSuppliPayCfgService.addSuppliPayCfg(suppliPayCfgVo);
    }

    @Override
    public NewSuppliPayCfgVo selectSuppliPayCfg(String importNo) {
        NewSuppliPayCfgVo suppliPayCfgVo = practiceSuppliPayCfgService.selectSuppliPayCfg(importNo);
        if(suppliPayCfgVo.getCustId() != null){
            long id = Long.parseLong(String.valueOf(suppliPayCfgVo.getCustId()));
            List <Long> custIds = Lists.newArrayList(id);
            List<CustomerVo> customers = customerService.getCustomerListByIds(custIds);
            String custName = customers.stream().filter(v -> v.getId() == id).findFirst().orElse(new CustomerVo()).getCustName();
            suppliPayCfgVo.setCustName(custName);
        }
        return suppliPayCfgVo;
    }
}
