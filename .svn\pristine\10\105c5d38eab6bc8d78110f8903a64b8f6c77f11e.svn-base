var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'element', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table,
        laydate = layui.laydate,
        form = layui.form;
    layer = parent.layer === undefined ? layui.layer : parent.layer,tableSelect = layui.tableSelect;


    //日期范围
    var startApplyTime = laydate.render({
        elem: '#startApplyTime',
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                endApplyTime.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });

    var endApplyTime = laydate.render({
        elem: '#endApplyTime',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {
            if (null != value && '' != value) {
                startApplyTime.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    var lastDate = laydate.render({
        elem: '#lastDate',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var paymentDateStr = laydate.render({
        elem: '#paymentDateStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var reviewTimeStr = laydate.render({
        elem: '#reviewTimeStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    var ownReviewTimeStr = laydate.render({
        elem: '#ownReviewTimeStr',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        done: function (value, date) {

        }
    });
    form.on('submit(btnQueryFilter)', function (data) {
        table.reload('paymentApplyTable', {
            where: data.field,
            page: {curr: 1} //重新从第 1 页开始
        });
        return false;
    });
    var tableView = '';
    var resDataLength = 0;
    table.render({
        id: 'paymentApplyTable',
        elem: '#paymentApplyTable',
        url: ML.contextPath + '/bill/payBatch/selectSalaryPaymentApplyQuery',
        where: {'processType': 5,'matchFlag':false,'appStatus':5,'documentStatusQuery':1},
        method: 'get',
        toolbar: '#toolbarDemo',
        page: true, //默认为不开启
        limits: [20, 50, 100,200],
        limit: 20,
        height: 650,
        totalRow:true,
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type:'numbers',align:'center', fixed: 'left',totalRowText:"合计"},
            {type: 'checkbox', fixed: 'left', align: 'center'},
            // {field: 'payKind', title: '支付大类',  width: '15%',templet: function (d) {
            //         if(d.payKind){
            //             return ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", d.payKind);
            //         }else{
            //             return "无数据";
            //         }}},
            {field: 'payComName', title: '出款公司名称',align:'center',width: '18%', fixed: 'left', sort: 'true'},
            {field: 'custName', title: '客户名称',align:'center',width: '18%', fixed: 'left', sort: 'true'},
            {field: 'revComName', title: '收款方',align:'center',width: '18%', fixed: 'left', sort: 'true'},
            {field: 'lastDate', title: '工资支付日期',align:'center',minWidth:110, fixed: 'left', sort: 'true'},
            {field: 'payAmt', title: '总计',align:'center',width: '12%', fixed: 'left', sort: 'true',templet: function (d) {
                    return formatCurrency(d.payAmt);
                },totalRow: formatCurrency('{{= d.payAmt }}'),},
            {field: 'contractTypeName', title: '产品方案',align:'center',width: '7%', sort: 'true'},
            {field: 'documentStatusStr', title: '制单状态',align:'center',width: '7%', sort: 'true'},
            {field: 'appStatusStr', title: '审批状态',align:'center',width: '7%',  sort: 'true'},
            {field: 'payMonth', title: '支付所属年月',align:'center',width: '7%', sort: 'true'},
            {field: 'totalActApy', title: '实付工资款',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalActApy);
                }},
            {field: 'totalTax', title: '个税款',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalTax);
                }},
            {field: 'totalCompensation', title: '补偿金',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalCompensation);
                }},
            {field: 'totalCompensationTax', title: '补偿金个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalCompensationTax);
                }},
            {field: 'totalAnnualBonus', title: '年终奖',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalAnnualBonus);
                }},
            {field: 'totalAnnualBonusTax', title: '年终奖个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalAnnualBonusTax);
                }},
            {field: 'totalLaborWages', title: '劳务工资',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalLaborWages);
                }},
            {field: 'totalLaborWagesTax', title: '劳务工资个税',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalLaborWagesTax);
                }},
            {field: 'totalSalaryFee', title: '服务费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalSalaryFee);
                }},
            {field: 'totalSupplierDisFund', title: '残障金',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalSupplierDisFund);
                }},
            {field: 'totalSupplierCrossBankHandlingFees', title: '跨行手续费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalSupplierCrossBankHandlingFees);
                }},
            {field: 'totalSupplierUnionFees', title: '工会费',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalSupplierUnionFees);
                }},
            {field: 'totalSupplierSalarySaleTax', title: '税金合计',align:'center',width: '15%', sort: 'true',templet: function (d) {
                    return formatCurrency(d.totalSupplierSalarySaleTax);
                }},
            {field: 'reviewTime', title: '复核时间',align:'center',width: '10%', sort: 'true'},
            {field: 'ownReviewTime', title: '自有工资复核时间',align:'center',width: '10%', sort: 'true'},
            {field: 'applicant', title: '申请人',align:'center',width: '15%',  sort: 'true'},
            {field: 'payTypeStr', title: '支付类型',align:'center', width: '13%', sort: 'true'},
            {field: 'anewPayFlagStr', title: '是否退票重发',align:'center',width: '7%', sort: 'true'},
            {field: 'paymentDate', title: '供应商发薪时间',align:'center',width: '7%', sort: 'true'},
            {field: 'payAssociatedComName', title: '支付关联抬头',align:'center',width: '7%', sort: 'true'},
            {field: 'paymentApplyId', title: '支付ID',align:'center',width: '5%', sort: 'true'},
            {title:'审批信息',toolbar: '#toolDemo', width: '8%', align: 'center',fixed: 'right'},
            {title:'查看人员明细',toolbar: '#toolDemo2', width: '8%', align: 'center',fixed: 'right'},
        ]],
        done: function (res) {
            ML.hideNoAuth();
            tableView = this.elem.next();
            resDataLength = res.data.length;
            for (var i = 0; i < resDataLength; i++) {
                if (res.data[i].appStatus === 1 || res.data[i].appStatus === 6) {
                    $("tr").eq(i + 1).css("color", "red");
                    $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                }
            }
        }
    });
    //收款方(分公司)
    var appd3 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="keyword" placeholder="分公司/供应商名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#revComName',
        checkedKey: 'orgCode',
        appd:appd3,
        table: {
            url:ML.contextPath+'/customer/supplier/getSupplierOrCompany',
            cols: [[
                { type: 'radio' },
                {type:'numbers',title:'序号',align:'center'},
                {field: 'orgName',title:'分公司名称',align:'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#revCom").val($("#revComName").attr("ts-selected"));
        }
    });
    //支付关联抬头
    var appd4 = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="orgName" placeholder="分公司名称" autocomplete="off" class="layui-input">';
    tableSelect.render({
        elem: '#payAssociatedComName',
        checkedKey: 'orgCode',
        appd:appd4,
        table: {
            url:ML.contextPath+'/sys/org/getCompanyByName',
            cols: [[
                { type: 'radio' },
                {type:'numbers',title:'序号',align:'center'},
                {field: 'orgName',title:'分公司名称',align:'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = []
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.orgName)
            })
            elem.val(NEWJSON.join(","));
            $("#payAssociatedCom").val($("#payAssociatedComName").attr("ts-selected"));
        }
    });
    //监听工具条
    table.on('tool(paymentApplyFilter)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
            case 'priceEvent'://查看支付详情
                openCurrPage("查看支付详情" ,"priceEvent",data);
                break;
            case 'personnelReview'://查看支付详情
                openCurrPage("查看人员详情" ,"personnelReview",data);
                break;
        }
    });
    function openCurrPage(title,optType,data){
        var  url="",area=['85%', '80%'];
        switch (optType) {
            case 'priceEvent':
                url="/bill/payBatch/lookSalaryPage?batchId="+data.id+"&payMentId="+data.paymentApplyId;
                area=['75%', '85%'];
                break;
            case 'personnelReview':
                url="/bill/payBatch/lookSalaryEmpByPayment?paymentApplyId="+data.paymentApplyId;
                break;
            case 'editReceivedPayment':
                url = "/bill/payBatch/gotoPayMentReceivingPage?opType=editReceivedPayment" + "&payMentId=" + data.paymentApplyId;
                break;
        }
        layer.open({
            type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
            title: title,
            area: area,
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + url,
            success: function (layero, index) {
                /*if(optType=='editReceivedPayment'){
                    var body = layer.getChildFrame('body', index);
                    body.find("#payBatchListVo").val(JSON.stringify(data));
                }*/
            },
            end: function () {
                reloadTable();
            }
        })

    }
    // 监听表格上方的按钮
    table.on('toolbar(paymentApplyFilter)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        let paymentApplyIds=[];
        let ids=[];
        switch (obj.event) {
            case 'export':
                window.open(ctx + '/bill/payBatch/exportSalaryPaymentApplyQuery?paramData='
                    +JSON.stringify(getSearchForm()));
                break;
            case'editReceivedPayment':
                if (data.length === 1) {
                    openCurrPage("编辑发放批次到款数据",'editReceivedPayment',data[0])
                }else{
                    layer.msg("请选择一条数据");
                }

                break;
            case 'rejectTask':
                var payAmt=0;
                for (let i = 0; i < data.length; i++) {
                    ids.push(data[i].id);
                    payAmt+=data[i].payAmt;
                    paymentApplyIds.push(data[i].paymentApplyId);
                }
                if(ids.length==0){
                    return layer.msg("驳回请至少选择一条数据！");
                }
                layer.open({
                    type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址
                    title: '确认驳回嘛',
                    area: ['26%', '33%'],
                    shade: 0,
                    maxmin: true,
                    offset: 'auto',
                    btn: ['确定', '取消'],
                    closeBtn:0,
                    shade: [0.8, '#393D49'],
                    content: ctx + '/bill/salaryPayRelated/gotoDocumentationOpenMsgPage?payAmt='+payAmt
                        +'&listCnt='+data.length+'&payComName='+data[0].payComName+'&custName='+data[0].custName
                        +'&revComName='+data[0].revComName+'&lastDate='+data[0].lastDate,
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                    },
                    yes: function(index, layero, that){
                        ML.layuiButtonDisabled(layero.find('.layui-layer-btn0'));
                        ML.layuiButtonDisabled(layero.find('.layui-layer-btn1'));
                        var body = layer.getChildFrame('body', index);
                        body.find('#tips').css("display","")
                        for (let i = 0; i < data.length; i++) {
                            var rejectTaskData=data[i];
                            ML.ajax('/workflow/rejectTask',
                                {'pid':rejectTaskData.processInstanceId,'bizId':rejectTaskData.paymentApplyId,'taskId':rejectTaskData.id,'comment':null,'bizType':'salary_payment_apply'}
                                ,function (res) {
                                    layer.msg(res.msg);
                                    if (i==(data.length-1)&&res.code == 0) {
                                        layer.close(index); // 关闭弹层
                                        reloadTable();
                                    }
                                },'POST');
                        }
                    },
                    end: function () {

                    }
                });
                break;
        }
    })

    table.on('checkbox(paymentApplyFilter)', function (obj) {
        var checked = obj.checked; //获取checkbox的选中状态，true或者false
        var tr = obj.tr; //获取到当前行的DOM对象
        setTr(checked, tr);
    });
    form.on('checkbox(layTableAllChoose)', function (res) {
        var checked = res.elem.checked;
        for (let i = 0; i < resDataLength; i++) {
            var tr = tableView.find('tr[data-index=' + i + ']');
            setTr(checked, tr);
        }
    });
    function setTr(checked, tr) {
        var style = tr.attr('style');
        var redFlag = style && style.indexOf('red') > 0;
        if (checked) {
            // 设置选中行的背景颜色
            tr.css('background-color', '#2257c4');
            if (!redFlag) {
                tr.css('color', '#fff');
            }
            tr.css('font-weight', '600');
        } else {
            // 取消选中行的背景颜色
            tr.css('background-color', '');
            if (!redFlag) {
                tr.css('color', '');
            }
            tr.css('font-weight', '');
        }
    }


    var customerList;
    //获取客户
    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/contract/getCustomerByAll',
            cols: [[
                {type: 'radio'}
                , {field: 'id', title: '客户ID', align: 'center'}
                , {field: 'custNo', title: '客户编号', align: 'center'}
                , {field: 'custName', title: '客户名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var id = '';
            var name = '';
            customerList=data.data
            layui.each(data.data, function (index, item) {
                id = item.id;
                name = item.custName;
            });
            // 回填值
            $("#custId").val(id);
            $("#custName").val(name);
        }
    });
    $(document).ready(function () {
        $("#payType option").each(function () {
            var value = $(this).val();
            if(value&&value!=3&&value!=12&&value!=18&&value!=19){
                $(this).remove();
            }
        })
        var info = [];
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/workflow/salaryPaymentApplyAllUserList",
            data:{"processType":5},
            dataType: 'json',
            success: function (data) {
                info = [];
                info = data.data;
                $.each(info, function (i, item) {
                    $("#applicant").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
        getCommissioner();
    });

    //重载数据
    function reloadTable() {
        table.reload('paymentApplyTable', {
            where: {
                paramData: JSON.stringify(getSearchForm()),
            }
        });
    }
    function getSearchForm() {
        var value = serialize("searchForm");
        value.matchFlag=false;
        value.appStatus=5;
        value.documentStatusQuery=1;
        return value;
    }
    $("#reset").click(function () {
        $("#custId").val(null);
        $("#distCom").val(null);
        $("#revCom").val(null);
        $("#revComName").removeAttr("ts-selected");
        $("#payAssociatedCom").val(null);
        $("#payAssociatedComName").removeAttr("ts-selected");
        startApplyTime.config.min = {year: 2010, month:0, date:1};
        startApplyTime.config.max = {year: 2099, month:11, date:12};
        endApplyTime.config.min = {year: 2010, month:0, date:1};
        endApplyTime.config.max = {year: 2099, month:11, date:12};
        form.render();
    });
    function getCommissioner() {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getResponsibleService",
            dataType: 'json',
            success: function (data) {
                $.each(data.data, function (i, item) {
                    $("#commissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
            }
        });
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/contract/getAllSalaryCommissioner",
            dataType: 'json',
            success: function (data) {
                $.each(data.data, function (i, item) {
                    $("#salaryCommissioner").append($("<option/>").text(item.userName).attr("value", item.loginName));
                });
                form.render('select');
            },
            error: function (data) {
                layer.msg(data);
            }
        });
    }
});