package com.reon.hr.sp.customer.entity.employee;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/12/27 0027 下午 02:49
 * @Version 1.0
 */
@Data
public class DistributionFileInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Long id;

    /**
     * emp_id
     */
    private Long empId;

    /**
     * 合同信息id
     */
    private String contFileId;

    /**
     * 身份文件id
     */
    private String certFileId;

    /**
     * 是否为最新文件1:是0:不是
     */
    private Integer newest;

    /**
     * creator
     */
    private String creator;

    /**
     * creator_time
     */
    private Date creatorTime;

    /**
     * updater
     */
    private String updater;

    /**
     * updater_time
     */
    private Date updaterTime;

    private String delFlag;
}
