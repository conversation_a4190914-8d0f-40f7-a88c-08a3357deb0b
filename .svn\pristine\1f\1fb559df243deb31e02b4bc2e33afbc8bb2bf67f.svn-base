package com.reon.hr.sp.bill.entity.salary;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class SalaryPayBatch {
    private Long id;

    private Long custId;

    private Long payId;

    private String batchNo;

    private String batchName;

    private BigDecimal totalActApy;

    private BigDecimal totalTax;

    private Integer applyCnt;

    private Date genTime;

    private Integer genFlag;
    private Integer anewPayFlag;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
    private Integer salaryMonth;//工资所属月
    private Integer billMonth ;//客户账单月
    private String withholdingAgentNo;
    private BigDecimal totalSalaryFee;
    private BigDecimal totalSupplierDisFund;
    private BigDecimal totalSupplierCrossBankHandlingFees;
    private BigDecimal totalSupplierUnionFees;
    private BigDecimal totalSupplierSalarySaleTax;
    /**
     * 补偿金
     */
    private BigDecimal totalCompensation;//补偿金
    /**
     * 补偿金个税
     */
    private BigDecimal totalCompensationTax;//补偿金个税
    /**
     * 年终奖
     */
    private BigDecimal totalAnnualBonus;//年终奖
    /**
     * 年终奖个税
     */
    private BigDecimal totalAnnualBonusTax;//年终奖个税
    /**
     * 劳务工资
     */
    private BigDecimal totalLaborWages;//劳务工资
    /**
     * 劳务工资个税
     */
    private BigDecimal totalLaborWagesTax;//劳务工资个税
    private String billCheckId;
}