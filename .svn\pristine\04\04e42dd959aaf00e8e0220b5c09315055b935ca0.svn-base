<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.report.dao.report.EmpTrackReportMapper">

  <sql id="Base_Column_List">
    id,group_id,group_name,cust_id,cust_name,contract_no,compare_ratio,emp_cnt,data_month, creator, create_time, updater, update_time, del_flag
  </sql>
  <delete id="deleteByDataMonth">
    delete from emp_track_report
    where data_month = #{dataMonth}
  </delete>
  <select id="findEmpTrackReportByDataMonth" resultType="com.reon.hr.api.report.vo.EmpTrackReportVo">
    select 
    <include refid="Base_Column_List" />
    from emp_track_report
    where data_month = #{dataMonth} and del_flag='N'
  </select>
  <select id="selectEmpTrackReportPage" resultType="com.reon.hr.api.report.vo.EmpTrackReportExportVo">
    select
    <include refid="Base_Column_List" />
    from emp_track_report
    where del_flag='N'
    <if test="empTrackReportVo.custIdList!=null and empTrackReportVo.custIdList.size()>0">
        and cust_id in
        <foreach collection="empTrackReportVo.custIdList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </if>
    <if test="empTrackReportVo.custId!=null">
        and cust_id=#{empTrackReportVo.custId}
    </if>
    <if test="empTrackReportVo.dataMonth!=null">
        and data_month &gt;= #{empTrackReportVo.dataMonth}
    </if>
  </select>
    <select id="selectEmpTrackReportList" resultType="com.reon.hr.api.report.vo.EmpTrackReportVo">
      select
      <include refid="Base_Column_List" />
      from emp_track_report
      where del_flag='N'
      <if test="vo.custIdList!=null and vo.custIdList.size()>0">
        AND cust_id in
        <foreach collection="vo.custIdList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="vo.dataMonth!=null">
        and data_month &gt;= #{vo.dataMonth}
      </if>
    </select>
    <insert id="insert" parameterType="com.reon.hr.api.report.vo.EmpTrackReportVo">
    insert into emp_track_report (
    group_id,group_name,cust_id,cust_name,contract_no,compare_ratio,emp_cnt,data_month
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.groupId},#{item.groupName},#{item.custId},#{item.custName},#{item.contractNo},#{item.compareRatio},
        #{item.empCnt},#{item.dataMonth}
      )
    </foreach>
  </insert>

  <update id="updateCustNameByCustId">
    update emp_track_report set cust_name =#{newCustName} ,updater=#{loginName} where cust_id =#{custId}
  </update>
</mapper>
