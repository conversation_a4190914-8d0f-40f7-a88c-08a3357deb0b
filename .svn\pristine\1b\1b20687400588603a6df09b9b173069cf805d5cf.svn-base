/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/1/31
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.bill.entity.bill;

import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 <AUTHOR>
 @version 1.0 */
@Data
@NoArgsConstructor
public class BeforeOrderServiceMonthUtilReturn {
	/**
	 Map<orderNo, Map < productCode, Map < max / min, receiveMonth>>>   用于获取最大最小收月
	 */
	private Map<String, Map<Integer, Map<String, Integer>>> orderNoAndProductCodeAndMaxMinReceiveMonthMap = Maps.newHashMap();
	/**
	 Map<orderNo, Map<receiveMonth, Set<billMonth>>>  调基 compute 计算的时候 需要用到
	 */
	private Map<String, Map<Integer, Set<Integer>>> orderNoAndReceiveAndBillMonthSetMap = Maps.newHashMap();

	/** Map<orderNo,Map<productCode,Function.identity>>  最后存储的时候用到*/
	private Map<String, Map<Integer, BeforeOrderServiceMonth>> orderNoAndProductCodeAndDataMap = Maps.newHashMap();
	/**
	 Map<orderNo, List<billMonth>>
	 */
	private Map<String, Set<Integer>> orderNoAndBillMonthList = Maps.newHashMap();

	/** 用在调基里面,用于看是否要补收 */
	private Map<String, Map<Integer, Set<Integer>>>  orderNoAndProductCodeAndReceiveMonthMap = Maps.newHashMap();
	/** Map<orderNo, Map<productCode, Map<receiveMonth, Set<billMonth>>>> */
	private Map<String, Map<Integer, Map<Integer, Set<Integer>>>> orderNoAndProductCodeAndReceiveMonthAndBillMonthMap;


}
