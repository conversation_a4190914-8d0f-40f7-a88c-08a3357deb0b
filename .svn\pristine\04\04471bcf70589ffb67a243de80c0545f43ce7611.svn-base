package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAssignResourceWrapperService;
import com.reon.hr.api.customer.vo.*;
import com.reon.hr.sp.customer.service.cus.ContractAreaService;
import com.reon.hr.sp.customer.service.cus.ContractAssignLogService;
import com.reon.hr.sp.customer.service.cus.ContractAssignService;
import com.reon.hr.sp.customer.service.cus.ContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

@Service("contractAssignDubboService")
public class IContractAssignResourceWrapperServiceImpl implements IContractAssignResourceWrapperService {

    @Autowired
    private ContractAssignService contractAssignService;

    @Autowired
    private ContractAssignLogService contractAssignLogService;

    @Autowired
    private ContractAreaService contractAreaService;

    @Autowired
    private ContractService contractService;

    @Override
    public List<ContractAssignVo> getPageList(String param,String currentLogin,List<OrgPositionDto> userOrgPositionDtoList) {
        if (StringUtils.isNotBlank(param)){
            return contractAssignService.getPageList(param,currentLogin,userOrgPositionDtoList);
        }
        return null;
    }

    @Override
    public List<ContractAssignVo> getContractWorkFlow(ContractAssignVo contractAssignVo) {
        return contractAssignService.getContractWorkFlow(contractAssignVo);
    }
    @Override
    public List<ContractAssignVo> getContractSalaryWorkFlow(ContractAssignVo contractAssignVo) {
        return contractAssignService.getContractSalaryWorkFlow(contractAssignVo);
    }

    @Override
    public List<ContractAssignVo> getContractAreaWorkFlow(ContractAssignVo contractAssignVo) {
        return contractAssignService.getContractAreaWorkFlow(contractAssignVo);
    }

    @Override
    public ContractAssignVo findByRelativeNo(ContractAssignVo contractAssignVo) {
        if (contractAssignVo != null){
            return contractAssignService.findByRelativeNo(contractAssignVo);
        }
        return null;
    }

    @Override
    public List<ContractAssignLogVo> getContractAssignList(ContractAssignLogVo contractAssignLogVo, String currentLogin) {

        return contractAssignLogService.getContractAssignList(contractAssignLogVo,currentLogin);
    }

    @Override
    public boolean updateTheContract() throws ParseException {
        try {
            return contractAssignService.updateTheContract();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean updateContractArea() throws ParseException {
        try {
            return contractAssignService.updateContractArea();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean distributeContract(ContractAssignParamVo contractAssignParamVo, String loginName){
        try {
            contractAssignService.handleDistributeContract(contractAssignParamVo, loginName);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public boolean distributeTransfer(ContractAssignParamVo contractAssignParamVo, String loginName) {
        try {
            contractAssignService.handleDistributeTransfer(contractAssignParamVo, loginName);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public List<ContractAssignLogVo> getContractAssignTransferList(ContractAssignLogVo contractAssignLogVo, String currentLogin) {

        return contractAssignLogService.getContractAssignTransferList (contractAssignLogVo,currentLogin);
    }

    @Override
    public void handlePartDistributeContract(ContractAssignParamVo contractAssignParamVo, String loginName) throws ParseException {
        contractAssignService.handlePartDistributeContract(contractAssignParamVo,loginName);
    }

    @Override
    public Map<String, Map<Integer, String>> getMapByNoListAndMonth(List<String> conAndCAList, Integer month) {
        return contractAssignLogService.getMapByNoListAndMonth(conAndCAList,month);
    }

    @Override
    public Map<String, Map<Integer, String>> getSocialAndSalaryContractCommissioner(List<String> conAndCAList, int billMonth) {
        return contractAssignLogService.getSocialAndSalaryContractCommissioner(conAndCAList,billMonth);
    }
}
