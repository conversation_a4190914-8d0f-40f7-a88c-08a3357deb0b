<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>添加</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body>
<div class="layui-inline" style="padding-top: 30px;">
    <input type="hidden" id="params" name="params">
    <input type="hidden" id="commitType">
    <div class="layui-inline">
        <fieldset class="layui-elem-field">
            <legend>实做报表信息</legend>
            <table id="queryGridTable" lay-filter="queryGridTable"></table>
        </fieldset>
        <fieldset class="layui-elem-field">
            <legend>支付详细</legend>
<%--            <form class="layui-form" id="searchForm">--%>
<%--                <div class="layui-form-item">--%>
<%--                    <div class="layui-inline queryTable">--%>
<%--                        <div class="layui-input-inline" style="width: 300px">--%>
<%--                            <label class="layui-form-label layui-elip" title="订单编号"--%>
<%--                                   style="font-weight:800">订单编号</label>--%>
<%--                            <div class="layui-input-inline">--%>
<%--                                <input class="layui-select" name="orderNo"--%>
<%--                                       id="orderNo" onkeyup="value=$.trim(value)"--%>
<%--                                       placeholder="请选择" autocomplete="off">--%>
<%--                            </div>--%>
<%--                        </div>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--            </form>--%>

            <table id="payDetailsGird" lay-filter="payDetailsGird"></table>
            <div style="margin-left:75%">
                <button class="layui-btn layui-btn-normal" lay-filter="save" id="save" type="button">确认</button>
                <button class="layui-btn layui-btn-normal" lay-filter="cancel" id="cancel" type="button">取消</button>
            </div>
        </fieldset>

<%--        <fieldset class="layui-elem-field">--%>

<%--        </fieldset>--%>


    </div>

</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/BigDecimal-all-last.min.js?v=${publishVersion}"></script>
<script type="text/javascript"
        src="${ctx}/js/modules/bill/insuracePractice/payDetails/individualLockPay.js?v=${publishVersion}"></script>
</body>
</html>
