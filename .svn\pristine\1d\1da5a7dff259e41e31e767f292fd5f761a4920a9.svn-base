package com.reon.hr.sp.customer.dao.salary;

import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.customer.dto.customer.salary.AnnualSalaryImportDto;
import com.reon.hr.api.customer.vo.WithholdingAgentSalaryNumVo;
import com.reon.hr.api.customer.vo.employee.ehr.PeopleNumberInfoVo;
import com.reon.hr.api.customer.vo.salary.SalaryEmployeeInfoVo;
import com.reon.hr.api.customer.vo.salary.SalaryPureAgentVo;
import com.reon.hr.api.customer.vo.salary.SpecImportInfoVo;
import com.reon.hr.api.customer.vo.salary.onlineBankingFile.EmpExcelAddVo;
import com.reon.hr.api.customer.vo.salary.pay.*;
import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.salary.SendSalaryInfoMailVo;
import com.reon.hr.sp.customer.entity.salary.SalaryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SalaryInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SalaryInfo record);

    int insertSelective(SalaryInfo record);

    SalaryInfo selectByPrimaryKey(Long id);

    boolean updateByPrimaryKeySelective(SalaryInfo record);

    int updateByPrimaryKeyWithBLOBs(SalaryInfo record);

    int updateByPrimaryKey(SalaryInfo record);
    //根据发放编号和ID 查询数据
    List<SalaryInfoVo> selectEmpByPayId(@Param("payId") Long payId,@Param("status") Integer status,@Param("batchId") Long batchId);

    SalaryInfoVo getSalaryJsonInfoList(@Param("id") Long id);


    List<Map<String,Object>> selectSalaryEmp(@Param("keyWord") String keyWord, @Param("payId") Long payId);
    List<Map<String,Object>> selectSalaryEmp(@Param("page") Page page,@Param("keyWord") String keyWord, @Param("payId") Long payId);

    List<SalaryInfoVo> selectSalaryInfoVoByCertNo(@Param("certNo") String certNo,@Param("cardId") Long cardId);

    List<Map<String,Object>> selectPayBatchEmpSalary(@Param("keyWord") String keyWord, @Param("payIdList") List<Long> payIdList,
                                                     @Param("withholdingAgentNo")String withholdingAgentNo,@Param("status")Integer status);
    List<Map<String,Object>> selectPayBatchEmpSalary(@Param("page") Page page,@Param("keyWord") String keyWord, @Param("payIdList") List<Long> payIdList,
                                                     @Param("withholdingAgentNo")String withholdingAgentNo,@Param("status")Integer status);
    List<SalaryInfoVo> getSalaryEmp( @Param("payId") Long payId);
    CalculateSalaryVo getEmpSalary(@Param("empId") Long empId, @Param("taxMonth") Integer taxMonth,@Param("taxListId") Long taxListId,@Param("withholdingAgentNo") String withholdingAgentNo);
    List<CalculateSalaryVo> getEmpSalaryList(@Param("empId") Long empId, @Param("lastTaxMonth") Integer lastTaxMonth,@Param("taxListId") Long taxListId,@Param("withholdingAgentNo") String withholdingAgentNo);

    List<SendSalaryInfoMailVo> getSendSalaryInfoMailVoListPage(Page page, Map<String, Object> map);
    List<SupplierSalaryBillVo> getEmpBill(SalaryBillSearchVo vo);
    boolean updateStatusById( List<SalaryBatchDetailVo> listDetail);
    boolean updateStatusAndFailReasonById(@Param("salaryInfoVoList") List<SalaryInfoVo> salaryInfoVoList);
    List<Map<String,Object>> selectSalaryEmpByBatch(@Param("keyWord") String keyWord, @Param("payId") Long payId,@Param("batchId")Long  batchId);
    List<Map<String,Object>> selectSalaryEmpByBatch(@Param("page") Page page,@Param("keyWord") String keyWord, @Param("payId") Long payId,@Param("batchId")Long  batchId);
    List<Map<String,Object>> selectSalaryEmpByBatchIdList(@Param("keyWord") String keyWord, @Param("batchIdList")List<Long> batchIdList);
    List<Map<String,Object>> selectRefundInputByBatchIdList(@Param("page")Page<Map<String, Object>> voPage,@Param("vo")SalaryPaymentRefundInputVo salaryPaymentRefundInputVo,@Param("batchIdList")List<Long> batchIdList);

    SalaryInfoVo getSalaryInfoVo(@Param ("empId") Long empId,@Param ("payId") Long payId);
    List<EmpDelayVo> selectEmpDelay(EmpDelaySearchVo vo,Page page);

    int updateStatusBySalaryId(Map<String,Object> map);
    List<SalaryInfoVo> getDalayByPayId(@Param("payId") Long payId);
    int updateSalaryInfoBySalaryId(Map<String,Object> map);
    int updateSalaryInfoByBatchId(Map<String,Object> map);
    List<Map<String,Object>> getEmpByBatchAndPay(@Param("page") Page page,@Param("keyWord") String keyWord,@Param("vo")SalaryPaymentRefundInputVo vo);

    List<EmpExcelAddVo> getEmpByBatchIdList(@Param("batchIdList") List<Long> batchIdList);
    List<EmpExcelAddVo> getEmpInfoByBatchIdList(@Param("batchIdList") List<Long> batchIdList);
    List<EmpExcelAddVo> getEmpInfoByTaxMonth(@Param("taxMonth") Integer taxMonth,@Param("withholdingAgentNo")String withholdingAgentNo,
                                             @Param("certNoList")List<String> certNoList,@Param("staffNameList")List<String> staffNameList);
    SalaryInfoVo  getSalaryEmpByEmpAndPayId( @Param("payId") Long payId,@Param("empId") Long empId);
    List<SalaryInfoVo> getSalaryInfoIdByPayIds(@Param("payIds")List<Long> payIds);

    List<SalaryInfoVo> selectByEmpIdsAndSalaryPayAndCategoryVo(@Param("list") List<Long> empIds,@Param("salaryPayAndCategoryVo")SalaryPayAndCategoryVo salaryPayAndCategoryVo,
                                                               @Param("withholdingAgentNoList")List<String> withholdingAgentNoList);

    /**
     * 查询多批计算的上一批次的计算数据
     * @param calculateSalaryVo
     * @param taxListId
     * @return
     */
    List<CalculateSalaryVo> getFirstEmpSalary(@Param("vo")CalculateSalaryVo calculateSalaryVo,@Param("taxListId") Long taxListId);
    /**
     * 查询多批计算的供应商工资上一批次的计算数据
     * @param calculateSalaryVo
     * @param taxListId
     * @return
     */
    CalculateSalaryVo getFirstSupplierEmpSalary(@Param("vo")CalculateSalaryVo calculateSalaryVo,@Param("taxListId") Long taxListId);

    int deleteBySalaryPayId(@Param("id")Long id,@Param("loginName")String loginName);

    int deletePhysicsBySalaryPayId(Long payId);


    List<SalaryPureAgentVo> selectSalaryPersonnel(Page page,@Param("custName")String custName,@Param("billMonth")String billMonth,@Param("commissioner")String commissioner,
                                                  @Param("salaryCommissioner")String salaryCommissioner,@Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList);

    int selectSalaryPersonnelCount(@Param("custName")String custName,@Param("billMonth")String billMonth,@Param("commissioner")String commissioner,
                                   @Param("salaryCommissioner")String salaryCommissioner,@Param("userOrgPositionDtoList")List<OrgPositionDto> userOrgPositionDtoList);

    Integer getLastEmpSalary(@Param("empId") Long empId, @Param("taxMonth") Integer taxMonth);

    List<SalaryInfoVo> getEmpBankCardBySalaryInfoIdList(@Param("salaryInfoIdList")List<Long> salaryInfoIdList);

    Integer updateSalaryInfoBankCard(@Param("list")List<SalaryInfoVo> salaryInfoVoList);

    List<SalaryInfoVo> getByPayIdAndEmpId(@Param("payIdList")List<Long> payIdList,@Param("employeeIdList") List<Long> employeeIdList);

    List<SalaryEmployeeInfoVo> getSalaryEmployeeInfo(@Param("vo") SalaryEmployeeInfoVo salaryEmployeeInfoVo);

    List<SalaryEmployeeInfoVo> getFileIds(@Param("page")Page page,@Param("empId") Long empId);

    List<SalaryInfoVo> getSalaryInfoByIds(@Param("salaryIds")List<Long> salaryIds);

    List<SalaryInfoVo> getTaxMonthByEmpIdList(@Param("empIds")List<Long> empIds,@Param("maxTaxMonth")Integer maxTaxMonth);

    List<SupplierSalaryInfoVo> getSalaryInfoByEmpIdAndPayIdList(@Param("list") List<Map<String, Object>> empIdAndPayIdMapList);

    List<SalaryInfoVo> getNumByPeopleNumberInfoVo(@Param("vo") PeopleNumberInfoVo vo);

    List<AnnualSalaryImportDto> getAnnualSalaryList(@Param("vo") AnnualSalaryImportDto vo);

    List<SalaryEmployeeInfoVo> getAllSalaryEmpByCertNo(@Param("vo") SalaryEmployeeInfoVo salaryEmployeeInfoVo);

    int getSalaryEmployeeCountByCertNo(String certMo);

    List<WithholdingAgentSalaryNumVo> getLastWithholdingAgentSalaryNum();

    List<SalaryInfoVo> getSalaryInfoVoByEmpIdAndBillMonth(@Param("empIds") List<Long> empIds,@Param("billMonth") Integer billMonth);

    List<Integer> getWithholdingAgentTypeList(@Param("keyWord") String keyWord, @Param("payIdList") List<Long> payIdList,
                                              @Param("withholdingAgentNo")String withholdingAgentNo,@Param("status")Integer status);

    List<SalaryEmployeeInfoVo> getSalaryInfoAllCertNo(@Param("certNo") String certNo);

    List<SalaryEmployeeInfoVo> getSalaryEmployeeAllCertNo(@Param("certNo") String certNo);

    int getCountByEmpIdAndContractNo(@Param("empId") Long empId,@Param("contractNo") String contractNo);
    int getSalaryInfoCountByEnpIdAndContractNo(@Param("empId") Long empId,@Param("contractNo") String contractNo);

    List<SalaryInfoVo> getByPayId(@Param("payId")Long payId);

    List<SalaryInfoVo> getPreviousData(@Param("payId")Long payId, @Param("taxMonth")Integer taxMonth, @Param("empIdList")List<Long> empIdList,
                                       @Param("withholdingAgentNoList") List<String> withholdingAgentNoList,@Param("taxListId") int taxListId);
    Integer updateSalaryJsonInfoList(@Param("list")List<SalaryInfoVo> salaryInfoVoList);

    List<SalaryInfoVo> getCompetitionFlagByList(@Param("SCL") List searchConditionList,
                                                @Param("withholdingAgentNo") String withholdingAgentNo);

    List<SalaryInfoVo> getLastMonthSalaryServiceNum(@Param("preYearMonth")Integer preYearMonth);
}
