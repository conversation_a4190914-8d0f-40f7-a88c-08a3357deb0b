var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','tableDate'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        tableDate = layui.tableDate;
    var layer = parent.layer === undefined ? layui.layer : parent.layer;

    var ids = [];
    var empRelatives = [];
    // 关联人员表格数据源
    var empRelativeTableData = [];
    var empRelativeTableView ='';
    // 渲染页面关联人员表格
    table.render({
        id: 'addEmpRelativeGrid',
        elem: '#addEmpRelativeTable'
        , page: false
        , limit: Number.MAX_VALUE // 分页条数根据查询回来数据变化
        , defaultToolbar: []
        , toolbar: '#topbtn'
        , data: empRelativeTableData
        , cols: [[
            { type: 'checkbox', width: '5%' }
            , { title: '序号', type: 'numbers', width: '5%' }
            , { field: 'id',type: 'hidden',width: '0%',hide: true}
            , {
                field: 'name', title: '关联人姓名', align: 'center'
                , edit: 'text', width: '10%'
            }
            , {
                field: 'certType', title: '关联人证件类型', align: 'center', width: '10%', templet: '#certTypeFlag'
            }
            , {
                field: 'certNo',
                title: '关联人证件号',
                align: 'center',
                width: '15%',
                edit:'text'
            }
            , {
                field: 'birthDate', title: '关联人出生日期', align: 'center',width: '10%',templet: function (d) {
                    var birthDate=d.birthDate!=undefined?d.birthDate:'';
                    return '<input type="text" class="layui-input layui-table-cell birthDate" id="birthDateInput" value="'+birthDate+'" placeholder="yyyy-MM-dd"  autocomplete="off" readonly />';
                }
            }
            , {
                field: 'sex', title: '关联人性别', align: 'center'
                , width: '10%' , templet: '#sexFlag'
            }
            // , {
            //     field: 'mobile', title: '关联人手机号', align: 'center'
            //     , width: '150' , edit: 'text'
            // }
        ]],
        done:function (res) {
            empRelativeTableView = this.elem.next(); // 当前表格渲染之后的视图
            var nowTime=new Date();
            var month=nowTime.getMonth()+1>12?12:nowTime.getMonth()+1
            nowTime=nowTime.getFullYear()+"-"+month+"-"+nowTime.getDate()
            lay('.birthDate').each(function (i) {
                laydate.render({
                    elem: this
                    , trigger: 'click'
                    , min: '2010-01-01'
                    , max: nowTime
                    , showBottom: true
                    , theme: 'grid'
                    , calendar: true
                    , format: 'yyyy-MM-dd'
                });
            });
        }
    });
    /**
     * 监听关联人员表格弹出层
     */
    table.on("toolbar(addEmpRelativeTableFilter)", function (obj) {
        var checkStatus = table.checkStatus(obj.config.id),
            data = checkStatus.data;

        switch (obj.event) {
            case 'addGroup':
                /*加载表格*/
                add();
                break;
            case 'delGroup':
                if (data.length === 0) {
                    layer.msg('请选择一行');
                } else {
                    del()
                }
                break;
        }
    });
    //新增
    function add() {
        setBirthDate();
        var oldData = table.cache["addEmpRelativeGrid"];
        oldData.forEach(function (addEmpRelativeGrid) {
            ids.push(addEmpRelativeGrid.id);
        });
        empRelatives.push({});
        ids.push(table.index);
        table.reload('addEmpRelativeGrid', {data: empRelatives});
        form.render()
    }
    //删除多个
    function del() {
        layer.confirm("你确定要删除么？", {btn: ['确定', '取消']}, function () {
            setBirthDate();
            var tableData = table.cache["addEmpRelativeGrid"];
            for (var i = tableData.length - 1; i >= 0; i--) {
                if (tableData[i]['LAY_CHECKED']) {
                    empRelatives.splice(tableData[i]['LAY_TABLE_INDEX'], 1);
                }
            }
            layer.msg("删除成功", {time: 5}, function () {
                table.reload('addEmpRelativeGrid', {data: empRelatives});
            });
        });
    }
    //证件类型
    form.on('select(selectCertTypeFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        empRelatives[index]['certType'] = data.value;
    });
    //性别
    form.on('select(selectSexFlag)', function (data) {
        var index = $(data.elem).parent().parent().parent().attr("data-index");
        empRelatives[index]['sex'] = data.value;
    });
    //账单模板
    $('#templetIdDiv').on('click', function (data) {
        if(!$('#contractNo').val()){
            layer.msg("请先选择合同编号！")
            return false
        }
    });
    /*监听编辑*/
    table.on('edit(addEmpRelativeTableFilter)', function (obj) {
        var index = $(obj.tr).attr("data-index");
        empRelatives[index][obj.field] = obj.value;
    });
    function setBirthDate() {
        $("input[id='birthDateInput']").each(function (i, a) {
            var dataIndex = a.parentNode.parentNode.parentNode.getAttribute("data-index");
            empRelatives[dataIndex]["birthDateStr"] = a.value;
        });
    }
    function getTempletIdData() {
        // 延时0.1秒后执行
        setTimeout(function () {
            var info = [];
            $.ajax({
                type: "GET",
                url: ML.contextPath + "/customer/contractTemplet/getTempletListByContractNo",
                data: {
                    "contractNo": $("#contractNo").val()
                },
                dataType: 'json',
                success: function (data) {
                    $("#templetId").empty();
                    $("#templetId").append($("<option/>"));
                    if(!data){
                        layer.msg("该合同下没有查到账单模板，请为合同绑定帐单模板！")
                    }
                    $.each(data, function (i, item) {
                        $("#templetId").append($("<option/>").text(item.templetName).attr("value", item.tempId));
                    });
                    form.render('select');
                },
                error: function (data) {
                    layer.msg("获取资源失败请重试！");
                }
            });
        }, 100);
    }
    // 页面初始化函数
    $(document).ready(function () {
        getTempletIdData();
    });
    var dataGrops;

    lay('#revStartMonth').each(function (i) {
        tableDate.render({
            dateElem: 'revStartMonth',
            row: i,
            object: this,
        });
    });

    lay('#billStartMonth').each(function (i) {
        tableDate.render({
            dateElem: 'billStartMonth',
            row: i,
            object: this,
        });
    });


    //表单保存
    form.on("submit(save)", function (data) {
        saveForm('save', data);
        return false;
    });

    //表单提交
    form.on("submit(commit)", function (data) {
        saveForm('commit', data);
        return false;
    });

    //表单关闭
    $(document).on("click", "#close", function () {
        layer.closeAll('iframe');
    });

    // 发送保存请求，type为保存类型
    function saveForm(type, data) {
        console.log(data);
        var phone = /^(0|86|17951)?(11[0-9]|12[0-9]|13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])[0-9]{8}$/;
        var idreg = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
        if ($("#certType").val() == 1) {
            if (!idreg.test(data.field['certNo'])) {
                layer.msg("身份证号不合法");
                return false;
            }
        }
        setBirthDate();
        var certNos=[]
        for (let i = 0; i <empRelatives.length ; i++) {
            var certType=empRelatives[i].certType;
            var certNo=empRelatives[i].certNo;
            if(!empRelatives[i].name){
                layer.msg("关联人员信息第"+(i+1)+"行,关联人姓名未填写");
                return false;
            }
            if(certNo){
                if(certNos.indexOf(certNo)>=0){
                    layer.msg("关联人员信息第"+(i+1)+"行,关联人证件号不得重复！");
                    return false;
                }else {
                    certNos.push(certNo)
                }
            }
            if (certType) {
                if (certType == 1) {
                    if (certNo) {
                        if (!idreg.test(certNo)) {
                            layer.msg("关联人员信息第"+(i+1)+"行,关联人身份证号不合法");
                            return false;
                        }
                    }else {
                        layer.msg("关联人员信息第"+(i+1)+"行,请填写关联人身份证号,或者不选择关联人身份证类型");
                        return false;
                    }
                    if($("#certType").val() == 1&&data.field['certNo'] ==certNo){
                        layer.msg("关联人员信息第"+(i+1)+"行,员工身份证不能和关联人身份证号一样");
                        return false;
                    }
                }else {
                    if(!certNo){
                        layer.msg("关联人员信息第"+(i+1)+"行,请填写关联人证件号,或者不选择关联人身份证类型");
                        return false;
                    }
                }
            }else {
                if(certNo){
                    layer.msg("关联人员信息第"+(i+1)+"行,请选择关联人身份证类型或者不填写关联人身份证号");
                    return false;
                }else if(!empRelatives[i].birthDateStr){
                    layer.msg("关联人员信息第"+(i+1)+"行,请选择出生日期");
                    return false;
                }else if(!empRelatives[i].sex){
                    layer.msg("关联人员信息第"+(i+1)+"行,请选择性别");
                    return false;
                }
            }
        }

        if ($("#mobile").val() != '') {
            if (!(phone.test(data.field['mobile']))) {
                layer.msg("手机号格式不正确");
                return false;
            }
        }
        /*if ($("#associatedMobile").val() != '') {
            if (!(phone.test(data.field['associatedMobile']))) {
                layer.msg("关联手机号格式不正确");
                return false;
            }
        }*/
        data.field['type'] = type;
        data.field['empRelativeVoList'] = empRelatives;
        delete data.field.selectCertTypeFlag;
        delete data.field.selectSexFlag;
        delete data.field.layTableCheckbox;
        delete data.field.oldTempletId;
        $.ajax({
            url: ML.contextPath + "/customer/commInsurOrder/saveOrCommit",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data.field),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                if (result.code != "-1") {
                    layer.closeAll('iframe');
                }
                layer.msg(result.msg);
            },
            error: function () {
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
    }

    // 搜索条件  商保合同下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="合同编号/方案编号/合同名称" autocomplete="off" class="layui-input">';
    // 商保合同下拉数据表格
    tableSelect.render({
        elem: '#contractNo',
        checkedKey: 'contractNo',
        appd: appd,
        table: {
            url: ML.contextPath + '/customer/commInsurOrder/getContractTypeAll?optType=1',
            cols: [[
                {type: 'radio'}
                , {field: 'contractNo', title: '合同编号', align: 'center'}
                , {field: 'solutionNo', title: '方案编号', align: 'center'}
                , {field: 'solutionName', title: '客户方案名称', align: 'center'}
                , {field: 'contractName', title: '合同名称', align: 'center'}
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.contractNo);
                $.ajax({
                    url: ML.contextPath + "/customer/commInsurOrder/getCustomerSolutionProdTypeList?soluNo=" + item.solutionNo,
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json;charset=UTF-8",
                    success: function (result) {
                        // 回填值
                        $("#solutionNo").val(result.data.solutionNo);
                        $("#solutionName").val(result.data.solutionName);
                        $("#payMethod").val(ML.dictFormatter("PAY_ORDER_TYPE", result.data.payMethod));
                        $("#cost").val(result.data.cost);
                        $("#price").val(result.data.price);
                        $("#supplierName").val(result.data.supplierName);
                        $("#prodType").val(ML.dictFormatter("INSUR_KIND", result.data.prodType));
                        $("#supplierId").val(result.data.supplierId);
                        getData(result.data.supplierId, result.data.prodType, result.data.custSoluId);
                    },
                    error: function () {
                        layer.msg("系统繁忙，请稍后重试!");
                    }
                });
                $.ajax({
                    url: ML.contextPath + "/customer/commInsurOrder/getCustName?custId=" + item.custId,
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json;charset=UTF-8",
                    success: function (result) {
                        $("#custId").val(item.custId);
                        $("#custName").val(result.data);
                    },
                    error: function () {
                        layer.msg("系统繁忙，请稍后重试!");
                    }
                });
            });
            // 回填值
            elem.val(NEWJSON.join(","));
            getTempletIdData();
        }
    });

    function getData(supplierId, prodType, custSoluId) {
        // if ($("#optType").val() === 'update' || $("#optType").val() === 'upgrade' || $("#optType").val() === 'query' || $("#optType").val() === 'check') {
        let commPriceId = "";
        var data = {"custSoluId": custSoluId, "commPriceId": commPriceId};

        //根据方案Id查询所有供应商详情
        $.ajax({
            url: ML.contextPath + "/customer/customerSolution/getAllCustomerSolutionDetail",
            type: 'POST',
            dataType: 'JSON',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function (result) {
                dataGrops = result.data;
                getCustomerSolutionGrid(dataGrops);
            },
            error: function (data) {
                layer.msg("获取资源失败请重试");
            }
        });
        // }
    }

    //查询方案产品信息
    function getCustomerSolutionGrid(dataGrop) {

        table.render({
            id: 'customerSolutionGridId',
            elem: '#customerSolutionGrid',
            data: dataGrops,
            page: true,   //默认不开启
            limit: 50,
            limits: [50, 100, 200],
            toolbar: '#toolbarDemo',
            defaultToolbar: false,
            text: {
                none: '暂无数据'  //无数据时展示
            },
            cols: [[
                {
                    field: 'btn', align: 'center',
                    templet: function (d) {
                        return '<a style="width: 100%; height: 100%;cursor: pointer;" lay-event="addRowTable">+</a>'
                    }
                },
                // {type: 'checkbox', width: '50', align: 'center'},
                {type: 'numbers', title: '序号', width: '40'},
                {field: 'supplierName', title: '供应商', width: '95', align: 'center'},
                {field: 'prodName', title: '产品名称', width: '95', align: 'center'},
                {
                    field: 'prodType', title: '产品类型', width: '95', align: 'center', templet: function (d) {
                        return ML.dictFormatter("PRODTYPE", d.prodType)
                    }
                },
                {
                    field: 'explainFlag', title: '基准保额是否依据说明', width: '150', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.explainFlag)
                    }
                },
                {field: 'remark', title: '说明', width: '95', align: 'center'},
                {field: 'baseVal', title: '基准保额', width: '95', align: 'center'},
                {
                    field: 'multiFlag', title: '保额是否可以加倍', width: '130', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.multiFlag)
                    }
                },
                {field: 'monthCost', title: '基准月付成本', width: '130', align: 'center'},
                {field: 'yearCost', title: '基准年付成本', width: '130', align: 'center'},
                {field: 'coeffi', title: '保额加倍系数', width: '130', align: 'center'},
                {
                    field: 'required', title: '是否为必选项', width: '130', align: 'center', templet: function (d) {
                        return ML.dictFormatter("BOOLEAN_TYPE", d.required)
                    }
                },
                {field: 'monthPrice', title: '基准月付指导价', width: '130', align: 'center'},
                {field: 'yearPrice', title: '基准年付指导价', width: '130', align: 'center'},
                {field: 'priceCoeffi', title: '指导价系数', width: '130', align: 'center'},
                {
                    field: 'amt', title: '保额(万元)', width: '95', align: 'center',
                    title: '<i style="color: red;">*</i>保额(万元)',
                    event: 'amtEvent',
                    style: "overflow:hidden",
                    templet: function (d) {
                        var amt = '';
                        if (d.amt != undefined) {
                            amt = d.amt
                        }
                        return amt;
                    }
                },
                {
                    field: 'amtMulti', title: '<i style="color: red;">*</i>保额倍数', width: '95', align: 'center',
                    event: 'amtMultiEvent',
                    edit: 'text',
                    style: "overflow:hidden",
                    templet: function (d) {
                        var amtMulti = '';
                        if (d.amtMulti != undefined) {
                            amtMulti = d.amtMulti
                        }
                        return amtMulti;
                    }
                },
                {
                    field: 'cost',
                    title: '<i style="color: red;">*</i>成本价',
                    width: '95',
                    align: 'center',
                    event: "costEvent",
                    templet: function (d) {
                        if (d.cost === undefined) {
                            return '<div style="text-align:center" id="cost" readonly>' + 0 + '</div>';
                        } else {
                            return '<div style="text-align: center" id="cost" readonly>' + d.cost + '</div>';
                        }
                    }
                },
                {
                    field: 'price',
                    title: '<i style="color: red;">*</i>指导价',
                    width: '95',
                    align: 'center',
                    event: "costEvent",
                    templet: function (d) {
                        if (d.price === undefined) {
                            return '<div style="text-align: center" id="price" readonly>' + 0 + '</div>';
                        } else {
                            return '<div style="text-align: center" id="price" readonly>' + d.price + '</div>';
                        }
                    }
                }
            ]],
            done: function (res) {
                //如果是固定保额（1），不能编辑直接填充方案成本价、方案指导价。
                if ($("#prodType").val() == 1) {
                    $("[data-field='amtMulti']").removeAttr('data-edit');
                    $("[data-field='amt']").css("display", "none");
                    $("[data-field='amtMulti']").css("display", "none");
                    $("[data-field='cost']").css("display", "none");
                    $("[data-field='price']").css("display", "none");
                    if (res.data[0]) {
                        //1为按年，2为按月
                        if ($("#payMethod").val() == 1) {
                            //方案成本价
                            $("#cost").val(res.data[0].yearCost);
                            //方案指导价
                            $("#price").val(res.data[0].yearPrice)
                        } else if ($("#payMethod").val() == 2) {
                            //方案成本价
                            $("#cost").val(res.data[0].monthCost);
                            //方案指导价
                            $("#price").val(res.data[0].monthPrice)
                        }
                    }
                }
            }
        });
    }


    //事件监听倍数和价格只能输入数字
    table.on("tool(customerSolutionGridFilter)", function (obj) {
        var trDom = obj.tr;
        var data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
        // 异常不要用它原来的这个作为tr的dom
        // var tr = obj.tr; //获得当前行 tr 的DOM对象
        var $this = $(this);
        var tr = $this.parents('tr');
        var trIndex = tr.data('index');
        if (layEvent === 'addRowTable') {
            // 外围的table的id + tableIn_ + 当前的tr的data-index
            $(this).attr('lay-event', 'fold').html('-');
            var tableId = 'tableOut_tableIn_' + trIndex;

            var _html = [
                '<tr class="table-item">',
                '<td colspan="' + tr.find('td').length + '" style="padding: 6px 12px;">',
                '<table id="' + tableId + '"></table>',
                '</td>',
                '</tr>'
            ];
            tr.after(_html.join('\n'));
            // 渲染子table
            table.render({
                elem: '#' + tableId,
                url: ctx + '/customer/supplier/getCommerceInsuranceByPid',
                where: {pid: data.id},
                method: 'POST',
                page: true,
                limits: [10, 20, 50],
                cols: [[
                    {field: '', title: '', float: 'left', width: '10%'},
                    {field: 'prodName', title: '产品名称', width: '10%', align: 'center'},
                    {
                        field: 'prodType', title: '产品类型', width: '10%', align: 'center', templet: function (d) {
                            return ML.dictFormatter("PRODTYPE", d.prodType)
                        }
                    },
                    {
                        field: 'explainFlag', title: '基准保额是否依据说明', width: '10%', align: 'center', templet: function (d) {
                            return ML.dictFormatter("BOOLEAN_TYPE", d.explainFlag)
                        }
                    },
                    {field: 'remark', title: '说明', width: '10%', align: 'center'},
                    {field: 'baseVal', title: '基准保额（万元）', width: '10%', align: 'center'},
                ]],
            });
        } else if (layEvent === 'fold') {
            $(this).attr('lay-event', 'addRowTable').html('+');
            tr.next().remove();
        }
    });


});
