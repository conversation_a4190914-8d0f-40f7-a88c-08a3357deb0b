/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2023/4/9
 *
 * Contributors:
 * 	   zhouzhengfa - initial implementation
 ****************************************/
package com.reon.ehr.web.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LogonBody
 * @description TODO
 * @date 2023/4/9 16:33
 */
@Data
public class LoginBody implements Serializable {
    /**
     * 用户名
     */
    private String username;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;
}
