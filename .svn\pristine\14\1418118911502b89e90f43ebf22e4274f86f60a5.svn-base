package com.reon.hr.api.workflow.dto;

import java.io.Serializable;

public class TaskQueryDTO implements Serializable {

    private String processDefinitionKey;

    private String businessKey;

    private String userId;

    private String processInstanceId;

    //提交人
    private String seller;
    //客户Id
    private Integer custId;
    //合同报价单名称
    private String quoName;
    //合同名称
    private String conName;

    private String actName;

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getConName() {
        return conName;
    }

    public void setConName(String conName) {
        this.conName = conName;
    }

    //开始时间
    private Integer dateS;
    //截止时间
    private Integer dateE;


    public String getSeller() {
        return seller;
    }

    public void setSeller(String seller) {
        this.seller = seller;
    }

    public Integer getCustId() {
        return custId;
    }

    public void setCustId(Integer custId) {
        this.custId = custId;
    }

    public String getQuoName() {
        return quoName;
    }

    public void setQuoName(String quoName) {
        this.quoName = quoName;
    }

    public Integer getDateS() {
        return dateS;
    }

    public void setDateS(Integer dateS) {
        this.dateS = dateS;
    }

    public TaskQueryDTO(String seller, Integer custId, String quoName, Integer dateS, Integer dateE) {
        this.seller = seller;
        this.custId = custId;
        this.quoName = quoName;
        this.dateS = dateS;
        this.dateE = dateE;
    }

    public Integer getDateE() {
        return dateE;
    }

    public void setDateE(Integer dateE) {
        this.dateE = dateE;
    }

    public TaskQueryDTO() {
    }

    public TaskQueryDTO(String processDefinitionKey, String businessKey, String userId) {
        this(processDefinitionKey, businessKey, userId, null);
    }

    public TaskQueryDTO(String processDefinitionKey, String businessKey, String userId, String processInstanceId) {
        this.processDefinitionKey = processDefinitionKey;
        this.businessKey = businessKey;
        this.userId = userId;
        this.processInstanceId = processInstanceId;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }
}
