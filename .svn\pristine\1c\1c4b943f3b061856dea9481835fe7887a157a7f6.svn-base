<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm">
        <input type="hidden" name="contractType" id="contractType">
        <table class="layui-table" lay-skin="nob" style="width: 75%">
            <tr>
                <td width="7%" align="right" style="font-weight:800"><i style="color: red">*</i>合同名称</td>
                <td width="8%"><input class="layui-input" type="text" id="contractName" lay-verify="required" readonly></td>
                <td width="7%" align="right" style="font-weight:800">客户帐套</td>
                <td width="8%">
                    <select class="layui-select" name="templetId" id="templetId" lay-search lay-filter="templetFilter">
                        <option value=""></option>
                    </select>
                </td>
                <td width="5%" align="right" style="font-weight:800"><i style="color: red">*</i>账单年月</td>
                <td width="8%"><input type="text" class="layui-input" name="billMonth" id="billMonth" lay-verify="required" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">是否锁定</td>
                <td width="8%">
                    <select class="layui-select" DICT_TYPE="BILL_STATTUS" name="status" id="status">
                        <option value=""></option>
                    </select>
                </td>
            </tr>
            <tr>
                <td align="right" style="font-weight:800">约定账单生成日</td>
                <td><input type="text" class="layui-input" id="genDate" readonly></td>
                <td align="right" style="font-weight:800">约定账单锁定日</td>
                <td><input type="text" class="layui-input" id="lockDate" readonly></td>
                <td colspan="4" align="center">
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter">查询</button>
                    <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button>
                    <a class="layui-btn layui-btn-sm" id = "generate" authURI="/bill/generateBill">生成账单</a>
                    <a class="layui-btn layui-btn-sm" id = "print" authURI="/bill/billPrint">账单打印</a>
                </td>
            </tr>
        </table>
    </form>
</blockquote>

<table class="layui-hide" id="billGrid" lay-filter="billFilter"></table>

<script type="text/jsp" id="toolbarDemo">
    <button class="layui-btn layui-btn-sm" lay-event="lock" authURI="/bill/lockBill">锁定</button>
    <button class="layui-btn layui-btn-sm" lay-event="unlock" authURI="/bill/unlockBill">解锁</button>
    <button class="layui-btn layui-btn-sm" lay-event="queryLog">查看账单日志</button>
</script>
<script type="text/jsp" id="toolDemo">
    {{# if(d.unStatus==1){  }}
    <a href="javascript:void(0)" class="layui-btn layui-btn-normal layui-btn-sm" title={{d.remark}} authURI="/bill/toExamine" lay-event="approval">审批</a>
    {{# } }}
</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/salaryHistoryBill/salaryHistoryBillList.js?v=${publishVersion}"></script>
</body>
</html>
