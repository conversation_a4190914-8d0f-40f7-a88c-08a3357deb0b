package com.reon.hr.sp.customer.service.impl.cus;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.reon.hr.api.customer.vo.ContractAttachmentVo;
import com.reon.hr.sp.customer.dao.cus.ContractAttachmentMapper;
import com.reon.hr.sp.customer.entity.cus.ContractAttachment;
import com.reon.hr.sp.customer.service.cus.ContractAttachmentService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ContractAttachmentServiceImpl extends ServiceImpl<ContractAttachmentMapper, ContractAttachment> implements ContractAttachmentService {
    private final String FILE_TYPE_FIRST = "1";
    private final String DEL_FLAG_N = "N";
    private final String FILE_TYPE_THIRD = "3";
    @Autowired
    private ContractAttachmentMapper contractAttachmentMapper;

    @Override
    public boolean saveFile(List<ContractAttachmentVo> contractAttachmentList, Integer fileType) {
        if (CollectionUtils.isNotEmpty(contractAttachmentList)){
           return contractAttachmentMapper.batchInsert(contractAttachmentList, fileType);
        }
        return false;
    }

    @Override
    public List<ContractAttachmentVo> findbyContractNo(String contractNo) {
        if (StringUtils.isNotBlank(contractNo)){
            return contractAttachmentMapper.findbyContractNo(contractNo);
        }
        return null;
    }

    @Override
    public List<ContractAttachmentVo> findFileIdByContractNoList(List<String> contractNoList) {
        return contractAttachmentMapper.findFileIdByContractNoList(contractNoList);
    }

    @Override
    public boolean delByContractNo(String contractNo,Integer typeStart,Integer typeEnd) {
        if (StringUtils.isNotBlank(contractNo)){
            return contractAttachmentMapper.delByContractNo(contractNo,typeStart,typeEnd);
        }
        return false;
    }

    @Override
    public int delByFileId(String fileId) {
        return contractAttachmentMapper.delByFileId (fileId);
    }

    @Override
    public Integer insertOrUpdateContractAttachFileId(ContractAttachmentVo contractAttachmentVo) {
        List<ContractAttachmentVo> contractAttachmentVoList = contractAttachmentMapper.findbyContractNoAndFileTypeEqualsOne(contractAttachmentVo.getContractNo());
        ContractAttachment contractAttachment = new ContractAttachment();
        BeanUtils.copyProperties(contractAttachmentVo, contractAttachment);
        contractAttachment.setFileType(FILE_TYPE_FIRST);
        if (contractAttachmentVoList.size() < 1) {
            contractAttachment.setDelFlag(DEL_FLAG_N);
            return contractAttachmentMapper.insert(contractAttachment);
        } else {
            for (ContractAttachmentVo attachmentVo : contractAttachmentVoList) {
                if (attachmentVo.getFileId().contains("[")){
                    return contractAttachmentMapper.updateContractAttachFileIdById(contractAttachmentVo.getFileId(),contractAttachment.getCreator(),attachmentVo.getId());
                }
            }
            contractAttachment.setDelFlag(DEL_FLAG_N);
            contractAttachmentMapper.insert(contractAttachment);
        }
        return 1;
    }

    @Override
    public Integer updateFileIdByContractNoAndProcessId(ContractAttachment contractAttachment) {
        Integer count = contractAttachmentMapper.getCountByContractAndProcessId(contractAttachment);
        contractAttachment.setFileType(FILE_TYPE_THIRD);
        if (count < 1) {
            return contractAttachmentMapper.insert(contractAttachment);
        } else {
            contractAttachment.setUpdater(contractAttachment.getCreator());
            return contractAttachmentMapper.updateFileIdByContractNoAndProcessId(contractAttachment);
        }
    }
}
