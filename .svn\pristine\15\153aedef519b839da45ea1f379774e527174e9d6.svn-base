package com.reon.hr.sp.customer.service.impl.cus;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.reon.hr.api.bill.exception.BillException;
import com.reon.hr.api.customer.dto.templetChange.ChangeTempletDto;
import com.reon.hr.api.customer.dto.templetChange.ChangeTempletServiceMonthDto;
import com.reon.hr.api.customer.dubbo.service.rpc.IBillTempletWrapperService;
import com.reon.hr.api.customer.vo.ChangeTempletOrderVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletFeeCfgVo;
import com.reon.hr.common.bill.strategy.IReceiveStrategyFactory;
import com.reon.hr.common.bill.strategy.concrete.ReceiveInfo;
import com.reon.hr.common.constants.CommonConstants;
import com.reon.hr.common.enums.ReceiveMonthTypeAndCollectFreqEnum;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.common.utils.ReceiveMonthHelper;
import com.reon.hr.sp.customer.dao.cus.ChangeTempletOrderMapper;
import com.reon.hr.sp.customer.entity.cus.ChangeTempletOrder;
import com.reon.hr.sp.customer.service.cus.ChangeTempletOrderService;
import com.reon.hr.sp.customer.service.employee.IOrderInsuranceCfgService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

@Service
public class ChangeTempletOrderServiceImpl extends ServiceImpl<ChangeTempletOrderMapper, ChangeTempletOrder> implements ChangeTempletOrderService {
    @Autowired
    private IOrderInsuranceCfgService orderInsuranceCfgService;

    @Autowired
    private ChangeTempletOrderMapper changeTempletOrderMapper;

    @Override
    public int insertOrUpdateSelective(ChangeTempletOrderVo record) {
        if (record != null) {
            return changeTempletOrderMapper.insertOrUpdateSelective(record);
        }
        return 0;
    }

    @Override
    public ChangeTempletOrderVo getChangeTempletOrderVoByChgNo(String chgNo) {
        EntityWrapper<ChangeTempletOrder> wrapper = new EntityWrapper<>();
        wrapper.eq("chg_no", chgNo);
        ChangeTempletOrderVo changeTempletOrderVo = new ChangeTempletOrderVo();
        List<ChangeTempletOrder> list = changeTempletOrderMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            BeanUtils.copyProperties(list.get(0), changeTempletOrderVo);
        }
        return changeTempletOrderVo;
    }

    @Override
    public Map<String, List<ChangeTempletDto>> getChangeTempletByOrderNos(List<String> orderNos, Integer status) {
        if (CollectionUtils.isEmpty(orderNos)) return Maps.newHashMap();
        List<ChangeTempletDto> changeTempletDtos = changeTempletOrderMapper.selectAllDataByOrderNos(orderNos, status);
        Map<String, List<ChangeTempletDto>> returnData = changeTempletDtos.stream().collect(Collectors.groupingBy(ChangeTempletDto::getOrderNo));
        return returnData;
    }

    /**
     @return
     @param orderNos                 订单编号
     @param billMonth                账单月
     @param receiveMonthByOrderNoMap Map<orderNo, Map < productCode, Map < max / min, receiveMonth>>>   用于获取最大最小收月
     */
    @Autowired
    private IBillTempletWrapperService templetWrapperService;

    @Override
    public Map<String, List<ChangeTempletServiceMonthDto>> getChangeTempletByTempletId(Set<String> orderNos, Integer billMonth, Map<String, Map<Integer, Map<String, Integer>>> receiveMonthByOrderNoMap) throws Exception {
        if (CollectionUtils.isEmpty(orderNos)) return Maps.newHashMap();

        Table<String, Integer, Long> orderNoAndTempletFeeCfgIdMap = orderInsuranceCfgService.getRevTempletIdByOrderNo(orderNos);
        /** 目前如果替换收费模板则是把当前产品的所有收费模板全部替换,而不是只替换某一个 */
        List<ChangeTempletDto> allChangeTempletDtos = changeTempletOrderMapper.getChangeTempletByTempletId(orderNos);
        /** 根据 orderNo和ProductCode进行分组 最后通过ChangeTempletInfo中的id进行排序
         为什么要那么分组，因为加入了收费模板变更这个维度，替换账单模板的时候，收费模板一定会被替换，并且可能不同产品可能有不同的收费模板*/
        Map<String, Map<Integer, List<ChangeTempletDto>>> orderNoAndProdCodeAndSortByIdListMap = allChangeTempletDtos.stream()
                .collect(Collectors.groupingBy(ChangeTempletDto::getOrderNo, Collectors.groupingBy(ChangeTempletDto::getProdCode, Collectors.collectingAndThen(toList(), list -> list.stream().sorted(Comparator.comparingLong(ChangeTempletDto::getId).reversed()).collect(toList())))));

        List<Long> revFeeIdList = Lists.newArrayList();
        revFeeIdList.addAll(Optional.ofNullable(allChangeTempletDtos).orElseGet(() -> Lists.newArrayList()).stream().map(ChangeTempletDto::getNewFeeId).collect(Collectors.toSet()));
        revFeeIdList.addAll(Optional.ofNullable(allChangeTempletDtos).orElseGet(() -> Lists.newArrayList()).stream().map(ChangeTempletDto::getOldFeeId).collect(Collectors.toSet()));
        Map<Long, BillTempletFeeCfgVo> billTempletFeeCfgByIdMap = templetWrapperService.getRevTempDataByIdList(revFeeIdList);

        /** 每个人,每个产品 不同的账单模板的收费时间 */
        Map<String, List<ChangeTempletServiceMonthDto>> returnMap = Maps.newHashMap();
        for (String orderNo : orderNoAndProdCodeAndSortByIdListMap.keySet()) {
            Map<Integer, List<ChangeTempletDto>> prodCodeAndCtDMap = orderNoAndProdCodeAndSortByIdListMap.get(orderNo);
            for (Integer productCode : prodCodeAndCtDMap.keySet()) {
                /** 不同订单下,不同的产品List */
                List<ChangeTempletDto> changeTempletDtos = prodCodeAndCtDMap.getOrDefault(productCode, Lists.newArrayList());  // 倒叙数据
                Long revTempId = orderNoAndTempletFeeCfgIdMap.get(orderNo, productCode);
                BillTempletFeeCfgVo billTempletFeeCfgVo = billTempletFeeCfgByIdMap.get(revTempId);
                // 获取最后一个账单靠近的应该收的最后一个收费月
                ReceiveInfo receiveInfo = new ReceiveInfo(billTempletFeeCfgVo.getReceiveMonthType(), billTempletFeeCfgVo.getBeforeMonths(), billTempletFeeCfgVo.getCollectFreq(), billMonth, billMonth, billMonth, null, false);
                Integer recentMonth = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getServiceMonths().stream().max(Integer::compareTo).orElseGet(() -> billMonth);
                Map<String, Integer> maxMinReceiveMap = receiveMonthByOrderNoMap.getOrDefault(orderNo, Maps.newHashMap()).getOrDefault(productCode, Maps.newHashMap());
                Integer minMonth = (maxMinReceiveMap.containsKey(CommonConstants.MIN) && maxMinReceiveMap.get(CommonConstants.MIN) != null) ? maxMinReceiveMap.get(CommonConstants.MIN) : billMonth;
                Integer maxMonth = (maxMinReceiveMap.containsKey(CommonConstants.MAX) && maxMinReceiveMap.get(CommonConstants.MAX) != null) ? maxMinReceiveMap.get(CommonConstants.MAX) : billMonth;
                /** 因为上面计算出的最大的收费月是 在不收费月 是为空的比如三月收 2,5,8,11  当前月为12月的时候,是计算不出月份的 所以要取已经收费的最大收费月,其它的交给调基*/
                if (recentMonth < maxMonth)
                    recentMonth = maxMonth;
                Integer endMonth = null;
                List<ChangeTempletServiceMonthDto> changeTempletServiceMonthDtoList = returnMap.getOrDefault(orderNo, Lists.newArrayList());
                Map<Long, ChangeTempletServiceMonthDto> templetIdAndDataMap = Maps.newHashMap();             /** 根据templetId分组 */
                Long lastRevTempletId = null;
                List<Integer> currentRecieveMonthSet = Lists.newArrayList();

                List<ChangeTempletDto> ctdLists = Lists.newArrayList();
                /** 获取到最大的那条先过滤当前数据 因为最大的那条作为最终数据 */
                if (CollectionUtils.isNotEmpty(changeTempletDtos)) {
                    /** 这是没有过滤之前最小的那条 */
                    ChangeTempletDto minChangeTempletDto = changeTempletDtos.get(changeTempletDtos.size() - 1);
                    /** 这是最大的那条 */
                    ChangeTempletDto changeTempletLastDto = changeTempletDtos.get(0);
                    /** 获取到id最大的那条 的startMonth进行过滤 为什么要过滤,因为 最大的那条 startmonth要是比前面的startmonth小的话,前面的 
                     startMonth比它大的都无效 */
                    Integer startMonthLast = changeTempletLastDto.getStartMonth();
                    List<ChangeTempletDto> filterateCTDList = changeTempletDtos.stream().filter(ctd -> ctd.getStartMonth() <= startMonthLast).collect(toList());

                    /** startMonth 进行分组,获取到 这个startMonth下Id最大的那条   然后根据startMonth进行排序 这样,每个start都只有一条,并且是最终的那一条   这是倒叙的数据 */
                    ctdLists = filterateCTDList.stream().collect(toMap(ChangeTempletDto::getStartMonth, Function.identity(), (a, b) -> {
                        if (a.getId() > b.getId()) return a;
                        else return b;
                    })).values().stream().sorted(Comparator.comparing(ChangeTempletDto::getStartMonth).reversed()).collect(toList());

                    for (int i = 0; i < ctdLists.size(); i++) {
                        ChangeTempletDto changeTempletDto = changeTempletDtos.get(i);
                        if (endMonth == null) {
                            lastRevTempletId = changeTempletDto.getNewFeeId();  // 这是最后一个收费模板
                            ReceiveInfo receiveInfoForRevId = new ReceiveInfo(billTempletFeeCfgVo.getReceiveMonthType(), billTempletFeeCfgVo.getBeforeMonths(), billTempletFeeCfgVo.getCollectFreq(), billMonth, billMonth, billMonth, null, false);
                            currentRecieveMonthSet = ReceiveMonthHelper.getReceiveStrategy(receiveInfoForRevId).getServiceMonths();
                            List<Integer> monthBetween = Lists.newArrayList();
                            if (changeTempletDto.getStartMonth() <= recentMonth)
                                monthBetween = DateUtil.getMonthBetween(changeTempletDto.getStartMonth().toString(), recentMonth.toString());
                            /** lastTemplet 为true表示当前的账单模板是当前订单的最后一个
                             startMonth 表示最后一个账单模板的起始月是哪个月  startMonth 最大的那条 */
                            ChangeTempletServiceMonthDto changeTempletServiceMonthLastDto = new ChangeTempletServiceMonthDto().setCurrentReceiveMonthSet(Sets.newHashSet(currentRecieveMonthSet)).setRevTempletId(lastRevTempletId).setProdCode(productCode).setTempletId(changeTempletDto.getNewTempletId()).setShouldChargeMonth(Sets.newHashSet(monthBetween)).setLastTempletId(true).setLastStartMonth(changeTempletDto.getStartMonth()).setOrderNo(changeTempletDto.getOrderNo());
                            changeTempletServiceMonthDtoList.add(changeTempletServiceMonthLastDto);
                            templetIdAndDataMap.put(changeTempletServiceMonthLastDto.getTempletId(), changeTempletServiceMonthLastDto);
                            endMonth = changeTempletDto.getStartMonth();  // 如果endMonth = null 表示第一次进入
                            if (ctdLists.size() == 1) {  /** 如果只有一条  那第一条就是最后一条 */
                                List<Integer> monthBetweenOld = Lists.newArrayList();
                                ChangeTempletServiceMonthDto changeTempletServiceMonthLastSecondDto;
                                if (templetIdAndDataMap.containsKey(changeTempletDto.getOldTempletId()))
                                    changeTempletServiceMonthLastSecondDto = templetIdAndDataMap.get(changeTempletDto.getOldTempletId());
                                else {
                                    changeTempletServiceMonthLastSecondDto = new ChangeTempletServiceMonthDto().setCurrentReceiveMonthSet(Sets.newHashSet(currentRecieveMonthSet)).setRevTempletId(lastRevTempletId).setProdCode(productCode).setTempletId(minChangeTempletDto.getOldTempletId()).setShouldChargeMonth(Sets.newHashSet()).setOrderNo(changeTempletDto.getOrderNo());
                                    changeTempletServiceMonthDtoList.add(changeTempletServiceMonthLastSecondDto);
                                }

                                Integer lastEndMonth = DateUtil.getYearMonthByCount(changeTempletDto.getStartMonth(), -1);
                                if (minMonth <= lastEndMonth)
                                    monthBetweenOld = DateUtil.getMonthBetween(minMonth.toString(), lastEndMonth.toString());
                                Set<Integer> combinSet = Sets.newHashSet(monthBetweenOld);
                                combinSet.addAll(changeTempletServiceMonthLastSecondDto.getShouldChargeMonth());
                                changeTempletServiceMonthLastSecondDto.setShouldChargeMonth(combinSet);
                                templetIdAndDataMap.put(changeTempletServiceMonthLastSecondDto.getTempletId(), changeTempletServiceMonthLastSecondDto);
                            } else {  /** 如果不是只有一条 需要考虑 最后一条的情况 也就是 startMonth最小的那条 并且id也是最小的那条 */
                                List<Integer> monthBetweenOld = Lists.newArrayList();
                                ChangeTempletServiceMonthDto changeTempletServiceMonthLastSecondDto;
                                if (templetIdAndDataMap.containsKey(minChangeTempletDto.getOldTempletId()))
                                    changeTempletServiceMonthLastSecondDto = templetIdAndDataMap.get(minChangeTempletDto.getOldTempletId());
                                else {
                                    changeTempletServiceMonthLastSecondDto = new ChangeTempletServiceMonthDto().setCurrentReceiveMonthSet(Sets.newHashSet(currentRecieveMonthSet)).setRevTempletId(lastRevTempletId).setProdCode(productCode).setTempletId(minChangeTempletDto.getOldTempletId()).setShouldChargeMonth(Sets.newHashSet()).setOrderNo(minChangeTempletDto.getOrderNo());
                                    changeTempletServiceMonthDtoList.add(changeTempletServiceMonthLastSecondDto);
                                }
                                Integer lastEndMonth = DateUtil.getYearMonthByCount(minChangeTempletDto.getStartMonth(), -1);
                                if (minMonth <= lastEndMonth)
                                    monthBetweenOld = DateUtil.getMonthBetween(minMonth.toString(), lastEndMonth.toString());
                                Set<Integer> combinSet = Sets.newHashSet(monthBetweenOld);
                                combinSet.addAll(changeTempletServiceMonthLastSecondDto.getShouldChargeMonth());
                                changeTempletServiceMonthLastSecondDto.setShouldChargeMonth(combinSet);

                                templetIdAndDataMap.put(changeTempletServiceMonthLastSecondDto.getTempletId(), changeTempletServiceMonthLastSecondDto);
                            }
                        } else {
                            if (minMonth == null) throw new BillException("获取minMonth有误!,请检查!!");
                            List<Integer> monthBetween = Lists.newArrayList();
                            if (changeTempletDto.getStartMonth() <= DateUtil.getYearMonthByCount(endMonth, -1))
                                monthBetween = DateUtil.getMonthBetween(changeTempletDto.getStartMonth().toString(), DateUtil.getYearMonthByCount(endMonth, -1).toString());
                            endMonth = changeTempletDto.getStartMonth();   // 倒叙的数据的startMonth 比endMonth小,那么就给它赋值
                            ChangeTempletServiceMonthDto changeTempletServiceMonthDto;
                            if (templetIdAndDataMap.containsKey(changeTempletDto.getNewTempletId()))
                                changeTempletServiceMonthDto = templetIdAndDataMap.get(changeTempletDto.getNewTempletId());
                            else {
                                changeTempletServiceMonthDto = new ChangeTempletServiceMonthDto();
                                changeTempletServiceMonthDtoList.add(changeTempletServiceMonthDto);
                            }
                            Set<Integer> combinSet = Sets.newHashSet(monthBetween);
                            combinSet.addAll(changeTempletServiceMonthDto.getShouldChargeMonth());
                            changeTempletServiceMonthDto.setCurrentReceiveMonthSet(Sets.newHashSet(currentRecieveMonthSet)).setRevTempletId(lastRevTempletId).setProdCode(productCode).setTempletId(changeTempletDto.getNewTempletId()).setShouldChargeMonth(combinSet).setOrderNo(changeTempletDto.getOrderNo());
                            templetIdAndDataMap.put(changeTempletServiceMonthDto.getTempletId(), changeTempletServiceMonthDto);
                        }
                        returnMap.put(orderNo, changeTempletServiceMonthDtoList);
                    }
                    /** 这里是为了处理,订单不属于这个账单模板,但是,在这个账单模板里收过费用,那么需要退出来
                     为什么会有这种情况,比如, 一个startMonth会在不同的账单模板间变更多次,但是,只有最后一次是有效的,但是可能已经在 那些无效的账单模板里面
                     生成了数据了,这样的数据,在以前的逻辑中是退不出来的, 这里做个补丁,将shouleReceiveMonth都置为 空的,那么实际收的有数据,最终,不该收取的数据会被退出来.
                     */
                    Set<Long> noExistsTempletId = changeTempletDtos.stream().map(item -> Sets.newHashSet(item.getOldTempletId(), item.getNewTempletId()))
                            .flatMap(Collection::stream).filter(item -> !templetIdAndDataMap.containsKey(item)).collect(toSet());
                    for (Long templetId : noExistsTempletId) {
                        ChangeTempletServiceMonthDto changeTempletServiceMonthLastSecondDto = new ChangeTempletServiceMonthDto()
                                .setCurrentReceiveMonthSet(Sets.newHashSet(currentRecieveMonthSet)).setRevTempletId(lastRevTempletId)
                                .setProdCode(productCode).setTempletId(templetId).setOrderNo(orderNo);
                        changeTempletServiceMonthDtoList.add(changeTempletServiceMonthLastSecondDto);
                    }
                }
            }
        }
        return returnMap;
    }

    private static Integer getRecentMonth(BillTempletFeeCfgVo billTempletFeeCfgVo, Integer billMonth) {
        Integer beforeMonth = billTempletFeeCfgVo.getBeforeMonths() == null ? 0 : billTempletFeeCfgVo.getBeforeMonths();
        Integer yearMonthByCount = com.reon.hr.common.utils.DateUtil.getYearMonthByCount(billMonth, beforeMonth);
        Integer receiveMonthType = billTempletFeeCfgVo.getReceiveMonthType();
        Integer collectFreq = billTempletFeeCfgVo.getCollectFreq();
        if (receiveMonthType.equals(IReceiveStrategyFactory.COLLECT_FREQ_ONE_MONTH) || receiveMonthType.equals(IReceiveStrategyFactory.COLLECT_FREQ_YEAR_MONTH))
            return yearMonthByCount;

        List<Integer> receiveMonthList = ReceiveMonthTypeAndCollectFreqEnum.getListByCode(receiveMonthType, collectFreq);
        Integer month = Integer.valueOf(yearMonthByCount.toString().substring(4));

        List<Integer> yearMonthList = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
        Integer count = 0;
        Integer min = receiveMonthList.stream().filter(item -> item > month).sorted().findFirst().orElse(receiveMonthList.get(0));
        if (min > month) {
            count = min - month;
        } else {
            count = (int) (yearMonthList.stream().filter(item -> item > month).count()) + (int) (yearMonthList.stream().filter(item -> item <= min).count());
        }
        Integer returnMonth = com.reon.hr.common.utils.DateUtil.getYearMonthByCount(yearMonthByCount, count);
        return returnMonth;
    }

    public static void main(String[] args) throws Exception {
        BillTempletFeeCfgVo billTempletFeeCfgVo = new BillTempletFeeCfgVo();
        billTempletFeeCfgVo.setBeforeMonths(2);
        billTempletFeeCfgVo.setCollectFreq(1);
        billTempletFeeCfgVo.setReceiveMonthType(3);
        Integer recentMonth = getRecentMonth(billTempletFeeCfgVo, 202310);
        System.out.println(recentMonth);

        ReceiveInfo receiveInfo = new ReceiveInfo(3, 2, 1, 202310, 202309, 202309, null, false);
        List<Integer> fullOfServiceMonths = ReceiveMonthHelper.getReceiveStrategy(receiveInfo).getServiceMonths();
        System.out.println(fullOfServiceMonths);

    }

    @Override
    public Set<String> getAllOrderNosAndTempletId(Long templetId) {
        return changeTempletOrderMapper.getAllOrderNosAndTempletId(templetId);
    }


    @Override
    public List<ChangeTempletDto> getChangeTempletByOrderNosAndStatus(List<String> orderNos, Integer status) {
        if (CollectionUtils.isEmpty(orderNos)) return Lists.newArrayList();
        List<ChangeTempletDto> changeTempletDtos = changeTempletOrderMapper.selectAllDataByOrderNos(orderNos, status);
        return changeTempletDtos;
    }

    @Override
    public int updateBatchByIds(List<Long> ids, Integer status) {
        return changeTempletOrderMapper.updateBatchByIds(ids, status);
    }
}
