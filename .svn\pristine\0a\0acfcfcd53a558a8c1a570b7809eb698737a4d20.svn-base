package com.reon.hr.sp.bill.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.reon.hr.api.bill.dubbo.service.rpc.bill.IInsuranceBillWrapperService;
import com.reon.hr.api.bill.vo.InsuranceBillVo;
import com.reon.hr.api.customer.dubbo.service.rpc.IBillTempletWrapperService;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IContractAreaResourceWrapperService;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletFeeCfgVo;
import com.reon.hr.api.customer.vo.billTemplet.BillTempletVo;
import com.reon.hr.api.util.DateUtil;
import com.reon.hr.sp.bill.utils.ServiceMonthUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Transactional(rollbackFor = Exception.class)
public class GenerateBillJob implements SimpleJob {

    private static Logger logger = LoggerFactory.getLogger(GenerateBillJob.class);
    @Autowired
    private IBillTempletWrapperService billTempletWrapperService;
    @Autowired
    private IInsuranceBillWrapperService billWrapperService;
    @Autowired
    private IContractAreaResourceWrapperService contractAreaResourceWrapperService;
    private static final int LOCK_STATUS_CODE = 2;// 锁定状态
    private static final int EVERY_HIGH_GEN_TIMES = 10;// 每日最高生成账单次数

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            logger.info("======================" + shardingContext.getJobName() + " start=============================");
            //查询所有的账单模板 是否有今天需要生成的账单
            List<BillTempletVo> templetList = billTempletWrapperService.searchByGenDate();
            String currDate = DateUtil.formatDateToString(new Date(), DateUtil.DATE_FORMAT_YYYYMMDD);
            //当前账单年月
            Integer billMonth = Integer.valueOf(currDate.substring(0, 6));
            if (CollectionUtils.isNotEmpty(templetList)) {
                for (BillTempletVo billTempletVo : templetList) {
                    List<BillTempletFeeCfgVo> feeCfgVos = billTempletWrapperService.getFeeHzsByTempletId(billTempletVo.getId());
                    // 如果账单模板下无收费模板则不出账单
                    if (CollectionUtils.isEmpty(feeCfgVos)) {
                        continue;
                    }
                    List<String> contractNos = contractAreaResourceWrapperService.getContractNoByTempletId(billTempletVo.getId());
                    for (String contractNo : contractNos) {
                        // 判断账单是否锁定
                        InsuranceBillVo billVo = billWrapperService.getInsuranceBillByContractNoAndTempletIdAndBillMonthAndBillType(contractNo, billTempletVo.getId(), billMonth, null);
                        if (billVo != null && billVo.getId() != null) {
                            logger.info("合同" + contractNo + "模板ID" + billTempletVo.getId() + "已经生成,不会自动生成");
                            continue;
                        }
//                        if (billVo!=null &&(billVo.getStatus() == LOCK_STATUS_CODE||billVo.getGenTimes() >= EVERY_HIGH_GEN_TIMES)) {
//                            logger.info("合同" + contractNo + "模板ID" + billTempletVo.getId() + "对应的账单已锁定或者当天生成次数超过十次,不再定时生成");
//                            continue;
//                        }
                        // 生成账单
                        billWrapperService.saveInsuranceBill(billTempletVo.getCustId(), contractNo, billTempletVo.getId(), billMonth, null, "1");
                    }
                }
            } else {
                logger.info("No bill can be generated " + currDate);
            }
            logger.info("======================" + shardingContext.getJobName() + " end=============================");

        } catch (
                Exception e) {
            logger.error("generate bill error:", e);
        }
    }

}
