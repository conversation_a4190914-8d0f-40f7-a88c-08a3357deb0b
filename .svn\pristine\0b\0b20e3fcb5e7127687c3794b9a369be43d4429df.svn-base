package com.reon.hr.sp.base.dao.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.*;
import com.reon.hr.sp.base.entity.sys.InsuranceSet;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InsuranceSetMapper {


    /**
     * 无调用
     *
     * @Description: 新增
     * @Author: chenxiang
     * @Params: * @param null
     * @Returns:
     * @Since 2020/8/13 14:32
     */
    int insert(InsuranceSet record);


    /**
     * 有调用  InsuranceSetController (saveOrUpdate -->insuranceSetWrapperService.saveOrUpdate)
     *
     * @Description:
     * @Author: chenxiang
     * @Params: * @param null
     * @Returns:
     * @Since 2020/8/13 14:32
     */
    int insertSelective(InsuranceSet record);

    /**
     * 有调用  InsuranceSetController --> (getInsuranceSetList --> insuranceSetWrapperService.findInsuranceSetPage)
     *
     * @Description: 分页数据查询
     * @Author: chenxiang
     * @Params: * @page 页面数据
     * @Params: * @searchData 查询条件
     * @Returns:
     * @Since 2020/8/13 14:51
     */
    List<InsuranceSetVo> findInsuranceSetPage(Page page, InsuranceSetVo searchData);

    /**
     * 有调用            BatchCustomOrderImportTask -->insuranceSetWrapperService.findInsuranceSet
     *
     * @Description: 根据社保套餐code  set_code查询 社保套餐
     * @Author: chenxiang
     * @Params: * @param null
     * @Returns:
     * @Since 2020/8/13 14:56
     */
    InsuranceSetVo findOneByCode(String setCode);

    /**
     * 有调用         InsuranceSetController (saveOrUpdate -->insuranceSetWrapperService.saveOrUpdate)
     * 根据setCode 修改社保套餐
     *
     * @param insuranceSet
     * @return
     */
    int update(InsuranceSet insuranceSet);


    /**
     * 有调用  InsuranceSetController --> (delete -->  insuranceSetWrapperService.deleteSets)
     * 逻辑删除一个或多个
     *
     * @param ids ID集合
     * @return
     */
    int delete(@Param("ids") List<String> ids, @Param("updater") String updater);


    /**
     * 有调用  InsuranceSetController  --> (findGroupRatioList --> insuranceSetWrapperService.findGroupRatioPage)
     * 分页、条件查询社保组、社保比例
     *
     * @return
     */
    List<InsuranceSetGroupRatioVo> findGroupRatioPage(@Param("cityCode") Integer cityCode, @Param("productCode") Integer productCode,
                                                      @Param("groupName") String groupName, @Param("ratioCode") String ratioCode,
                                                      @Param("groupType") Integer groupType, @Param("ratioName") String ratioName, Page page);


    /**
     * 有调用 SocialSecurityFundController  -->  iSocialSecurityFundWrapperService.getSocialSecurityFundData
     * //导出社保公积金试算数据
     * SocialSecurityFundTrialCalculationController  -->  iSocialSecurityFundTrialCalculationWrapperService.getSocialSecurityFundTrialCalculationData
     * --> iSocialSecurityFundTrialCalculationService.getSocialSecurityFundTrialCalculationData
     * (SocialSecurityFundTrialCalculationServiceImpl --> insuranceSetMapper.getSetCodeLists)
     *
     * @Description: 得到社保套餐中的相应数据
     * @Author: swx
     * @Params:
     * @Returns:
     * @Since 2020/8/13 15:55
     */
    List<SocialSecurityFundExportVo> getSetCodeLists(@Param("cityCode") String cityCode, @Param("indTypeCode") String indTypeCode, @Param("supplierCode") String supplierCode, @Param("singleFlag") Integer singleFlag, @Param("standardFlag") Integer standardFlag);

    List<SocialSecurityFundExportVo> getSetCodeListsByList(@Param("list") List<SocialSecurityFundExportVo> socialSecurityFundExportVo);

    List<SocialSecurityFundExportVo> getSetCodeListsByList1(@Param("list") List<SocialSecurityFundExportVo> socialSecurityFundExportVo, @Param("currentDate") Integer currentDate, @Param("singleFlag") Integer singleFlag);

    /**
     * @Description:
     * @Author: swx
     * @Params: * @param null
     * @Returns:
     * @Since 2020/8/13 16:10
     */
    List<SocialGfgSubMainVo> getSetCodeRatio(@Param("cityCode") Integer cityCode, @Param("indTypeCode") String indTypeCode, @Param("date") Integer date, @Param("serviceSiteCode") String serviceSiteCode, @Param("serviceSiteCodeNumber") String serviceSiteCodeNumber);


    //查询子项
    List<SocialGfgOneCitySubVo> getSocialSubMain(@Param("insuranceRatioCode") String insuranceRatioCode, @Param("groupCode") String groupCode, @Param("date") Integer date, @Param("setCode") String setCode);

    List<SocialGfgOneCitySubVo> getSocialSubMainByList(@Param("list") List<SocialGfgSubMainVo> list);

    void updateStatusBySetcode(@Param("setCodes") String[] setCodes, @Param("status") int status);

    List<InsuranceSetVo> getInsuranceSetNoByAccountFlag(@Param("accountFlag") int accountFlag, @Param("receiving") String receiving, @Param("categoryCode") String categoryCode, @Param("custId") Long custId);

    List<InsuranceSetRatioVo> selectAllInsuranceSetAcctRatio();
}