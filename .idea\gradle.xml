<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="delegatedBuild" value="false" />
        <option name="testRunner" value="PLATFORM" />
        <option name="distributionType" value="WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleHome" value="D:\java\gradle-6.8-all\gradle-6.8" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/reon-admin-api" />
            <option value="$PROJECT_DIR$/reon-admin-sp" />
            <option value="$PROJECT_DIR$/reon-admin-web" />
            <option value="$PROJECT_DIR$/reon-base-api" />
            <option value="$PROJECT_DIR$/reon-base-sp" />
            <option value="$PROJECT_DIR$/reon-bill-api" />
            <option value="$PROJECT_DIR$/reon-bill-sp" />
            <option value="$PROJECT_DIR$/reon-change-base-api" />
            <option value="$PROJECT_DIR$/reon-change-base-sp" />
            <option value="$PROJECT_DIR$/reon-common-utils" />
            <option value="$PROJECT_DIR$/reon-customer-api" />
            <option value="$PROJECT_DIR$/reon-customer-sp" />
            <option value="$PROJECT_DIR$/reon-ehr-api" />
            <option value="$PROJECT_DIR$/reon-ehr-gateway" />
            <option value="$PROJECT_DIR$/reon-ehr-sp" />
            <option value="$PROJECT_DIR$/reon-file-api" />
            <option value="$PROJECT_DIR$/reon-file-sp" />
            <option value="$PROJECT_DIR$/reon-rabbitmq-component" />
            <option value="$PROJECT_DIR$/reon-report-api" />
            <option value="$PROJECT_DIR$/reon-report-sp" />
            <option value="$PROJECT_DIR$/reon-thirdpart-api" />
            <option value="$PROJECT_DIR$/reon-thirdpart-common" />
            <option value="$PROJECT_DIR$/reon-thirdpart-gateway" />
            <option value="$PROJECT_DIR$/reon-thirdpart-sp" />
            <option value="$PROJECT_DIR$/reon-workflow-api" />
            <option value="$PROJECT_DIR$/reon-workflow-sp" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>