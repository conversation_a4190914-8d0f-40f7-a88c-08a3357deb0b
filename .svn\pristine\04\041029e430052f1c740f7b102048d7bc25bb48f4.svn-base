package com.reon.hr.api.bill.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillInvoiceVo implements Serializable {
    private Long id;
    private Long invoiceId;
    private BigDecimal invoiceAmt;
    private BigDecimal adjustAmt;
    private String orgCode;
    private String orgName;
    private Integer status;
    private String remark;
    private Integer voucherType;
    private String voucherNo;
    private Integer pushMode;
    private Integer billFlag;
    private Integer virtualRedStatus;
    /**
     * 提票时间
     */
    private String voucherDate;
    /**
     * 开票时间
     */
    private String invoiceDate;
    /**
     * 提票时间
     */
    private String abolishVoucherDate;
    /**
     * 开票时间
     */
    private String abolishInvoiceDate;
    /**
     * 特殊税率
     */
    private BigDecimal specialRate;
    /**
     * 服务费税收分类别名
     */
    private String serviceTaxType;
    /**
     * 代收代付税收分类别名
     */
    private String agentTaxType;
    /**
     * 税收分类ID
     */
    private Long taxTypeId;
    private String voucherRemark;
    private String prjCs;
    private String finance;
    private String financeOrgCode;
    private String financePosCode;
    private String creator;
    private String updater;
    private Date createTime;
    private Date updateTime;
    private String delFlag;
    private Long version;
    private Integer adjustFlag;
    private String invoiceNo;


}