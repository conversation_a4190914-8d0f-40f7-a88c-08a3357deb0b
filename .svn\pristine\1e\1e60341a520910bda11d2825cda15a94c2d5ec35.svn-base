var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'element', 'laydate', 'table'], function () {
    var table = layui.table,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    $('#btnQuery').on('click', function () {
        if (ML.isEmpty($("#primod").val())) {
            return layer.msg("请填写必填项")
        }
        if (ML.isEmpty($("#eacnbr").val())) {
            return layer.msg("请填写必填项")
        }
        if (ML.isEmpty($("#begdat").val())) {
            return layer.msg("请填写必填项")
        }
        if (ML.isEmpty($("#enddat").val())) {
            return layer.msg("请填写必填项")
        }
        var params = serialize("searchForm");
        var switchValue = $('#directFlag').prop('checked');
        params['directFlag'] = switchValue ? "ON" : "OFF"
        ML.ajax("/thirdPart/cmb/queryElectReturnOrder", JSON.stringify(params), function (data) {
            if (data.code == 0) {
                let msgObj = data.data;
                if (msgObj.head && msgObj.head.resultcode == 'SUC0000') {
                    let arr = [];
                    arr.push(msgObj.asycalhdz1)
                    layer.msg("查询成功，请根据打印Id 打印文件！");
                    table.reload('queryBusinessModalsPage', {data: arr});
                } else {
                    layer.msg("请求查询失败!" + msgObj.head.resultmsg);
                }
            } else {
                layer.msg(data.msg);
            }
        }, "POST", null, 'application/json;charset-UTF-8')

    });

    $(document).ready(function () {
        initFun();
    })

    function initFun(posCode) {
        window.top['userOrgPosPool'].forEach(item =>{
            if(item.posCode == 5000 || item.posCode == -1){
                $("#timeFlag").show();
            }
        })
    }
    table.render({
        id: 'queryBusinessModalsPage',
        elem: '#QueryGridTable'
        , data: []
        // , url: ctx + "/thirdPart/cmb/queryAcctTranLogList"
        // , where: serialize("searchForm")
        , height: 'full-200'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'rtncod', title: '返回码', align: 'center', width: '150'}
            , {field: 'rtnmsg', title: '返回信息', align: 'center', width: '150'}
            , {field: 'rtndat', title: '打印任务编号', align: 'center', width: '150'}


        ]]
    });


    //入职日期
    var addDateS = laydate.render({
        elem: '#begdat',
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        max: "2099-12-31",//设置一个默认最大值
        done: function (value, date) {
            if (null != value && '' != value) {
                addDateE.config.min = {
                    year: date.year,
                    month: date.month - 14, //关键
                    date: date.date
                };
            } else {
                addDateE.config.min = {
                    date: 1,
                    hours: 0,
                    minutes: 0,
                    month: 0,
                    seconds: 0,
                    year: 1900
                };
            }
        }
    });

    var addDateE = laydate.render({
        elem: '#enddat',//选择器结束时间
        min: "1970-01-01",//设置min默认最小值
        format: 'yyyy-MM-dd',
        btns: ['clear', 'confirm'],
        done: function (value, date) {
            if (null != value && '' != value) {
                addDateS.config.max = {
                    year: date.year,
                    month: date.month - 14,//关键
                    date: date.date
                }
            } else {
                addDateS.config.max = {
                    date: 31,
                    hours: 0,
                    minutes: 0,
                    month: 11,
                    seconds: 0,
                    year: 2099
                }
            }
        }
    });

    $('#btnQuery2').on('click', function () {
        table.reload('payrollRresultInfoGird', {where: serialize('searchForm2')});
    });


    table.render({
        id: 'payrollRresultInfoGird',
        elem: '#QueryGridTable2'
        , data: []
        , url: ctx + "/thirdPart/cmb/getElectReturnOrderFileVoPage"
        , where: serialize("searchForm2")
        , height: 'full-200'
        , page: true
        , toolbar: '#topbtn'
        , defaultToolbar: []
        , limit: 50
        , method: 'POST'
        , limits: [50, 100, 200]
        , text: {
            none: '暂无数据' //无数据时展示
        }
        , done: function (res, curr, count) {
            //监听行双击事件
            table.on('rowDouble(QueryGridTable)', function (obj) {

            });
            ML.hideNoAuth();
            if (res.data) {

            }

        }, error: function (res, msg) {
            // layer.msg(msg);
        }
        , cols: [[
            {field: '', type: 'checkbox', width: '50', fixed: 'left'}
            , {field: 'taskid', title: '打印编号', align: 'center', width: '220'}
            , {field: 'acctNo', title: '账户号', align: 'center', width: '150'}
            , {field: 'reOrderNo', title: '回单编号', align: 'center', width: '150'}
            , {field: 'trxSeq', title: '流水号', align: 'center', width: '150'}
            , {field: 'yurRef', title: '业务参观号', align: 'center', width: '150'}
            , {field: 'retcod', title: '查询标记', align: 'center', width: '150'}
            , {
                field: 'returl', title: '下载路径', align: 'center', width: '350', templet: function (d) {
                    if (ML.isNotEmpty(d.returl) || d.returl != 'null') {
                        return '<a href="' + ML.fileServerUrl + d.returl + '" target="_blank" class="layui-table-link" style="text-decoration:underline" >' + d.returl + '</a>';

                    }
                    return "暂无"
                }
            }

        ]]
    });


});