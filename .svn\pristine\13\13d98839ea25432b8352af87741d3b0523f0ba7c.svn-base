<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.perCommerceItemMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.api.bill.vo.perCommerceItemVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bill_id" jdbcType="BIGINT" property="billId"/>
        <result column="temp_id" jdbcType="BIGINT" property="tempId"/>
        <result column="rev_temp_id" jdbcType="BIGINT" property="revTempId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="solution_no" jdbcType="VARCHAR" property="solutionNo"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="emp_id" jdbcType="BIGINT" property="empId"/>
        <result column="bill_month" jdbcType="INTEGER" property="billMonth"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="cert_no" jdbcType="VARCHAR" property="certNo"/>
        <result column="cost" jdbcType="DECIMAL" property="cost"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="tax" jdbcType="DECIMAL" property="tax"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="tax_ratio" jdbcType="DECIMAL" property="taxRatio"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , bill_id, temp_id, rev_temp_id, order_no, contract_no, solution_no,
    emp_id, bill_month, emp_name, cert_no, exce_cost, price, tax, fee, tax_ratio, creator,
    create_time, updater, update_time,start_month,end_month,version,item_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey">
        delete
        from ${tableName}
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.reon.hr.api.bill.vo.perCommerceItemVo" useGeneratedKeys="true"
            keyProperty="id">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="billId != null">
                bill_id,
            </if>
            <if test="tempId != null">
                temp_id,
            </if>
            <if test="revTempId != null">
                rev_temp_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="contractNo != null">
                contract_no,
            </if>
            <if test="solutionNo != null">
                solution_no,
            </if>

            <if test="empId != null">
                emp_id,
            </if>
            <if test="itemType !=null">
                item_type,
            </if>
            <if test="billMonth != null">
                bill_month,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="certNo != null">
                cert_no,
            </if>
            <if test="exceCost != null">
                exce_cost,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="tax != null">
                tax,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="taxRatio != null">
                tax_ratio,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="startMonth != null">
                start_month,
            </if>
            <if test="endMonth != null">
                end_month,
            </if>
            <if test="version != null">
                version,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="billId != null">
                #{billId,jdbcType=BIGINT},
            </if>
            <if test="tempId != null">
                #{tempId,jdbcType=BIGINT},
            </if>
            <if test="revTempId != null">
                #{revTempId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="contractNo != null">
                #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="solutionNo != null">
                #{solutionNo,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=BIGINT},
            </if>
            <if test="itemType !=null">
                #{itemType,jdbcType=INTEGER},
            </if>
            <if test="billMonth != null">
                #{billMonth,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="certNo != null">
                #{certNo,jdbcType=VARCHAR},
            </if>
            <if test="exceCost != null">
                #{exceCost,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="tax != null">
                #{tax,jdbcType=DECIMAL},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="taxRatio != null">
                #{taxRatio,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startMonth != null">
                #{startMonth},
            </if>
            <if test="endMonth != null">
                #{endMonth},
            </if>
            <if test="version != null">
                #{version},
            </if>


        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        update ${tableName}
        <set>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=BIGINT},
            </if>
            <if test="tempId != null">
                temp_id = #{tempId,jdbcType=BIGINT},
            </if>
            <if test="revTempId != null">
                rev_temp_id = #{revTempId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="solutionNo != null">
                solution_no = #{solutionNo,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId,jdbcType=BIGINT},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=BIGINT},
            </if>
            <if test="billMonth != null">
                bill_month = #{billMonth,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="certNo != null">
                cert_no = #{certNo,jdbcType=VARCHAR},
            </if>
            <if test="cost != null">
                cost = #{cost,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="tax != null">
                tax = #{tax,jdbcType=DECIMAL},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="taxRatio != null">
                tax_ratio = #{taxRatio,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startMonth != null">
                start_month =#{startMonth},
            </if>
            <if test="endMonth != null">
                end_month = #{endMonth},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        update ${tableName}
        set bill_id     = #{billId,jdbcType=BIGINT},
            temp_id     = #{tempId,jdbcType=BIGINT},
            rev_temp_id = #{revTempId,jdbcType=BIGINT},
            order_no    = #{orderNo,jdbcType=VARCHAR},
            contract_no = #{contractNo,jdbcType=VARCHAR},
            solution_no = #{solutionNo,jdbcType=VARCHAR},
            supplier_id = #{supplierId,jdbcType=BIGINT},
            emp_id      = #{empId,jdbcType=BIGINT},
            bill_month  = #{billMonth,jdbcType=INTEGER},
            emp_name    = #{empName,jdbcType=VARCHAR},
            cert_no     = #{certNo,jdbcType=VARCHAR},
            cost        = #{cost,jdbcType=DECIMAL},
            price       = #{price,jdbcType=DECIMAL},
            tax         = #{tax,jdbcType=DECIMAL},
            fee         = #{fee,jdbcType=DECIMAL},
            tax_ratio   = #{taxRatio,jdbcType=DECIMAL},
            creator     = #{creator,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater     = #{updater,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="getPerCommerceItemByBillId" resultType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        select
        <include refid="Base_Column_List"/>
        from
        ${tableName}
        where
        <if test="billId !=null">
            bill_id = #{billId,jdbcType=BIGINT}
        </if>
    </select>
    <select id="getPerCommerceSum" resultType="com.reon.hr.api.bill.vo.PerCommerceSumVo">
        select
        sum(price) as receiveAmt,
        sum(exce_cost) as supplierCost,
        sum(fee) as serviceFee
        from
        ${tableName}
        where
        <if test="billId !=null">
            bill_id = #{billId,jdbcType=BIGINT}
        </if>

    </select>
    <select id="getPerCommerceEmpNum" resultType="java.lang.Integer">
        select
        count(distinct emp_id)
        from
        ${tableName}
        where price!=0
        <if test="billId !=null">
          and  bill_id = #{billId,jdbcType=BIGINT}
        </if>

    </select>


    <select id="getPerCommerceItemByOrderNo" resultType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        select
        <include refid="Base_Column_List"/>
        from
        ${tableName}
        where
        <if test="orderNo !=null">
            order_no = #{orderNo,jdbcType=BIGINT}
        </if>
    </select>
    <select id="getPerCommerceItemById" resultType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        select
        <include refid="Base_Column_List"/>
        from
        ${tableName} m
        where
        m.id =#{id}
        and exists(select t.item_id from ${tableCostName} t where t.item_id =m.id and status =2)
    </select>
    <delete id="deleteByPrimaryKeyList">
        delete from ${tableName}
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="id">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <select id="getPerCommerceItemByOrderNoList" resultType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        select
        <include refid="Base_Column_List"/>
        from
        ${tableName}
        where order_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.orderNo,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="getExceCostByContractNoAndStartMonth" resultType="com.reon.hr.api.bill.vo.perCommerceItemVo">
        select contract_no, exce_cost
        from  ${tableName}
        where contract_no = #{contractNo} and exce_cost !=0

group by contract_no
    </select>
    <insert id="insertSelectiveList" parameterType="com.reon.hr.api.bill.vo.perCommerceItemVo" useGeneratedKeys="true"
            keyProperty="id">
        insert into ${tableName}
        (
        bill_id,temp_id,rev_temp_id,order_no,contract_no,solution_no,emp_id,item_type,bill_month,emp_name,cert_no,
        exce_cost,price,tax,fee,tax_ratio,creator,updater,start_month,end_month,version
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.billId,jdbcType=BIGINT},
            #{item.tempId,jdbcType=BIGINT},
            #{item.revTempId,jdbcType=BIGINT},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.contractNo,jdbcType=VARCHAR},
            #{item.solutionNo,jdbcType=VARCHAR},
            #{item.empId,jdbcType=BIGINT},
            #{item.itemType,jdbcType=INTEGER},
            #{item.billMonth,jdbcType=INTEGER},
            #{item.empName,jdbcType=VARCHAR},
            #{item.certNo,jdbcType=VARCHAR},
            #{item.exceCost,jdbcType=DECIMAL},
            #{item.price,jdbcType=DECIMAL},
            #{item.tax,jdbcType=DECIMAL},
            #{item.fee,jdbcType=DECIMAL},
            #{item.taxRatio,jdbcType=DECIMAL},
            #{item.creator,jdbcType=VARCHAR},
            #{item.updater,jdbcType=VARCHAR},
            #{item.startMonth},
            #{item.endMonth},
            #{item.version}
            )
        </foreach>
    </insert>
</mapper>