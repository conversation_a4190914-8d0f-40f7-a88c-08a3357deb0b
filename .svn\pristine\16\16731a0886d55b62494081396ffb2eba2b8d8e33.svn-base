package com.reon.hr.sp.change.dao.change;

import com.reon.hr.sp.change.entity.change.CollectSubJob;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CollectSubJobMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CollectSubJob record);

    int insertSelective(CollectSubJob record);

    CollectSubJob selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CollectSubJob record);

    int updateByPrimaryKey(CollectSubJob record);

    int batchInsert(List<CollectSubJob> list);

    int updateByJobNoAndPrjCs(@Param("submit") Integer submit,
                               @Param("noSubmit") Integer noSubmit,
                               @Param("subJobId") Long subJobId,
                               @Param("status") Integer status);

    int updateByJobNoAndPrjCs1(@Param("loginName") String loginName,
                              @Param("count") Integer count,
                              @Param("jobNo") String jobNo);

    List<CollectSubJob> selectByCollectSubJob(@Param("jobNo") String jobNo);

   CollectSubJob selectByCollectSubJobCommissioner(@Param("jobNo") String jobNo,@Param("commissioner") String commissioner);

    int editCollectSubJobByJobNos(@Param("list") List<String> list);

    int countByLoginNameAndJobNo(@Param("loginName") String loginName,
                                 @Param("jobNo") String jobNo);
}