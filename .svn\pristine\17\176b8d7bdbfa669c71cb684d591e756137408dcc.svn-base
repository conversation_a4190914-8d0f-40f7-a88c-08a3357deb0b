<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
            http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
            http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
            http://www.springframework.org/schema/redis http://www.springframework.org/schema/redis/spring-redis.xsd">

    <!--配置加密机-->
    <bean id="environmentVariablesConfiguration"
          class="org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig">
        <property name="algorithm" value="PBEWithMD5AndDES"/>
        <property name="passwordEnvName" value="APP_ENCRYPTION_PASSWORD" />
    </bean>
    <!--配置加密器，将用于解密-->
    <bean id="configurationEncryptor" class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
        <property name="config" ref="environmentVariablesConfiguration"/>
    </bean>
    <!-- 配置文件 -->
    <bean id="placeholderConfig"
          class="org.jasypt.spring31.properties.EncryptablePropertyPlaceholderConfigurer">
        <constructor-arg ref="configurationEncryptor"/>
        <property name="locations">
            <list>
                <value>classpath*:*.properties</value>
            </list>
        </property>
    </bean>

    <context:annotation-config/>
    <context:component-scan base-package="com.reon.hr.sp.thirdpart,com.reon.hr.rabbitmq"></context:component-scan>
    <aop:aspectj-autoproxy/>

    <import resource="classpath:spring-config-redis.xml" />
</beans>
