package com.reon.hr.api.dubbo.service.rpc.sys;


import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.dto.OrgAndAreaDTO;
import com.reon.hr.api.vo.sys.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IOrgnizationResourceWrapperService {

      List<OrgVo> findAllOrgName();

    List<OrgAndAreaDTO> findOrgInfo(OrgVo orgVo);

    boolean saveOrUpdateOrg(OrgVo orgVo) throws Exception;

    /**
     * 根据参数查询组织机构
     *
     * @param orgVo
     * @return
     */
    List<NodeVo> findByParams(OrgVo orgVo);

    List<NodeVo> buildTree(List<NodeVo> root, List<String> ids);

    /**
     * 查找所有分公司
     *
     * @return
     */
    List<OrgVo> findAllCompany();

    /**
     * 查找所有已开通的分公司
     * @return
     */
    List<OrgVo> findAllOpenedCompany();
	List<OrgVo> findAllDepartment();


    List<OrgVo> getOrgByCityAndType(Integer cityCode, String orgType);
    List<OrgVo> getOpenedOrgByCityAndType(Integer cityCode, String orgType);
    List<OrgVo> getOrgByCityAndOrgType(Integer cityCode, String orgType);
    List<OrgVo> findAllOrg(OrgVo orgVo);

    Page<OrgVo> findAllCompanyByName(Integer page, Integer limit, String name);

    OrgVo findOrgById(String orCode);

    /**
     * 查询机构名称是否重复
     * @param orgName
     * @return
     */
    List<String> getCountByOrgName(String orgName);

    List<OrgVo> getCompanyByNameAndCityCode(String keyword,Integer cityCode);

    OrgVo findOrgByOwerCityAndOrgType (String cityCode,String orgType);

    /**
     * 获取当前大区总监下所有分公司
     * @param page
     * @param limit
     * @param name
     * @param loginName
     * @return
     */
    Page<OrgVo> findRegionCompanyByName(Integer page, Integer limit, String name,String loginName);

    /**
     * 多岗位角色分公司查询
     * @param commonUserVo
     * @return
     */
    List<OrgVo> getOrgCodes(CommonUserVo commonUserVo);

    /**
     * 获取机构下分公司
     * @param commonUserVo
     * @return
     */
    OrgVo getOrgCode(CommonUserVo commonUserVo);


    /**
     * 获取机构下所有的分公司
     * @return
     */
    List<OrgVo> findAllCompanyByName();

    /**
     * 根据orgType查找orgCode、orgName
     * @param orgType
     * @return
     */
    List<OrgVo> findRegionByOrgType(String orgType);

    List<OrgVo> findOrgByOwerCityListAndOrgType(List<Integer> cityCodeList, String code);

    List<OrgVo> getOrgListByOrgCodeList(List<String> orgCodeList);

    Map<String,OrgVo> getAllCompanyToMap();

	void updateCompanyStructure(String source, String target, String afterIndex);

    Map<String, List<OrgVo>> getAllDepartmentByCompanyReturnMap();

    int getOrgPosNumByOrgCode(String target);

	int updateSyncDepartment(String source, String target);


    List<OrgUserVo> findSellerList();

    /**
     * 查询orgCode是否重复
     * @param orgCode
     * @return
     */
    boolean getCountByOrgCode(String orgCode);

    /**
     * 查询城市名称
     * @param owerCity
     * @return
     */
    String  getAllCityName(Long owerCity);

    List<OrgVo> findRegionCompanyByName(String name, String orgCode);

    List<OrgVo> getCompServiceCityVoByArgs(CompServiceCityVo args);

    List<CommonUserVo> getLaterServiceList(String orgCode);


    List<Integer> selectCityCodeByParentCode(Integer parentCode);

    Set<String> getNorthRegionCompanyOrgCode();
}
