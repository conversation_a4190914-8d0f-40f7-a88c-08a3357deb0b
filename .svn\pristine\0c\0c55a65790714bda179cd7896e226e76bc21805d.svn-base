package com.reon.hr.api.customer.dto.customer.salary.taxDeclarationInformation;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class YearEndBonusDetailImportDto implements Serializable {

    private String employeeId;
    private String withholdingAgentNo;
    private String withholdingAgentName;
    private Integer withholdingAgentType;
    private Integer taxMonth;
    private String salaryJsonInfo;

    @Excel(name = "客户名称",width = 26)
    private String custName;

    @Excel(name = "工号",width = 10)
    private String staffNo;
    /**
     * 雇员名称
     */
    @Excel(name = "*姓名",width = 10)
    private String employeeName;

    /**
     * 证件类型
     */
    @Excel(name = "*证件类型",width = 10, readConverterExp = "1=居民身份证,2=其他,3=港澳居民来往内地通行证,4=台湾居民来往大陆通行证,5=中国护照,6=其他")
    private String certType;

    /**
     * 证件号码
     */
    @Excel(name = "*证件号码",width = 22)
    private String certNo;

    @Excel(name = "*全年一次性奖金额",width = 10)
    private BigDecimal currentIncome;

    @Excel(name = "免税收入",width = 10)
    private BigDecimal currentTaxFreeIncome;

    @Excel(name = "其他",width = 10)
    private BigDecimal other;

    @Excel(name = "准予扣除的捐赠额",width = 10)
    private BigDecimal deductionAllowedDonationsAmount;

    @Excel(name = "减免税额",width = 10)
    private BigDecimal taxDeduction;

    @Excel(name = "备注",width = 10)
    private String remark;

    private Long salaryId;
    @Excel(name = "工资支付日期",width = 10)
    private String lastDate;
    @Excel(name = "是否退票重发",width = 10)
    private String anewPayFlagStr;
}
