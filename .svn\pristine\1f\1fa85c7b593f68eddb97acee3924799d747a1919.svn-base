package com.reon.hr.api.base.dubbo.service.rpc.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.base.vo.MessageVo;

import java.util.List;
import java.util.Map;

public interface IMessageWrapperService {

    String MESSAGE_CONTENT_TEMP = "你有一个合同编号为{contractNo}{type}(客户编号:{custNo},客户名称:{custName}),请到合同管理进行续签合同！";
    String MESSAGE_EMPLOYEE_CONTENT_TEMP = "你有一个员工合同编号为{employeeContractNo}{type},身份证号为:{certNo},员工姓名:{name},客户编号:{custNo},客户名称:{custName},请到员工合同管理进行续签合同！";
    String MESSAGE_EMPLOYEE_CONTENT_UPLOAD_FILE_TEMP = "你有一个员工订单编号为{orderNo}需要上传{type}(员工姓名:{name},身份证号:{certNo},客户编号:{custNo},客户名称:{custName})，请到员工合同管理进行上传！";
    String MESSAGE_NO_FEEDBACK_ADDED_TEMP = "你有一个员工订单编号为{orderNo}导入了未增反馈，请在个人订单查询查看！";
    String MESSAGE_SALARY_PAYMENT_REFUND_INPUT = "你有一个员工证件号为{certNo}，姓名为{name},已工资支付退票录入！";
    String MESSAGE_SUPPLIER_SALARY_BILL_PUSH = "你有一条供应商工资账单，供应商名称为{supplierName}，客户帐套为{templetName},已推送！";
    String MESSAGE_EMPLOYEE_ORDER_WILL_RETIRE = "你有一个员工订单编号为{orderNo}临近退休，请到订单管理查看员工信息！";
    String MESSAGE_EMPLOYEE_ORDER_REJECT= "你有一条订单{orderNo}被驳回/挂起了,请在个人订单查询查看!";
    String MESSAGE_INVOICE_REJECT = "你有一条开票编号为:%s的开票申请被驳回,驳回原因:%s,请在实收开票查询查看!";
//    String MESSAGE_CONTRACT_BILLING_REJECT= "你有一条合同编号为:%s,账单年月为:%s,账单模板名称为:%s的开票申请被驳回,驳回原因:%s,请在实收开票查询查看!";
    String MESSAGE_SUPPLIER_CONTRACT_TEMP= "你有一条供应商合同编号为{contractNo}的合同即将到截止日期,,请在供应商管理进行合同续签或顺延!!";
    String SALARY_PAYMENT_WORK_FLOW_TEMP= "你有一条工资流程发放编号为{batchNo}的流程已到达发薪日，但是还未审批完!!若当天不发薪请修改工资支付日期";
    String TAX_DIFFERENCE_REMINDER_TEMP= "你有{differenceNum}条,扣缴义务人名称为{withholdingAgentName},扣缴义务人编号为{withholdingAgentNo},的{taxComparisonType}个税差异数据未处理!";
    String TAX_DIFFERENCE_FEEDBACK_REMINDER_TEMP= "在扣缴义务人名称为{withholdingAgentName},扣缴义务人编号为{withholdingAgentNo}下,存在{taxComparisonType}个税差异反馈数据未处理，员工证件号码为{certNo},员工姓名为{name}!";
    String TAX_PREDECLARATION_REMINDER_TEMP= "今天是该月份第三个工作日，需要完成扣缴义务人名称为{withholdingAgentName},扣缴义务人编号为{withholdingAgentNo},的税务预申报相关操作!";
    String EHR_CREATE_TEMP= "你有一个客户: {custName} ,大合同已经分配完成,ehr 自动创建完成!";
    String MESSAGE_EMPLOYEE_ORDER_NOT_UPLOAD_LATTER= "你有一个员工订单编号为{orderNo}进行了申报转移但未上传离职信，请去上传！";
    String SET_RATIO_EXPIRED_CONTENT_TEMP = "社保比例过期消息提醒(城市:%s,社保套餐%s:%s,产品:%s,社保比例%s:%s)";

    /**
     * 根据当前登入人分页查询消息信息
     * @param page 当前页
     * @param limit 当前条
     * @param messageVo 查询对象
     * @return
     */
    Page<MessageVo> getMessageWrapperListPage(Integer page, Integer limit,MessageVo messageVo);

    /**
     * 根据当前登入人查询消息信息
     * @param receiver 当前登入人
     * @return
     */
    List<MessageVo> getMessageWrapperList(String receiver);

    /**
     * 根据消息id修改是否已读
     * @param ids 消息id集合
     * @param updater 更新人
     * @return
     */
    boolean batchUpdateMessage(List<Long> ids,String updater);

    /**
     * 保存消息数据
     * @param record
     * @return
     */
    int save(MessageVo record);


    /**
     * 根据id查询消息数据
     * @param id
     * @return
     */
    MessageVo getMessage(Long id);

    List<MessageVo> getListMessageByMap(Map<String, Object> conditionMap);

    Integer getMessageCountByReadFlag(Integer readFlag,String loginName);

    /**
     * 批量保存消息数据
     * @param messageVoList
     * @return
     */
    int batchSave(List<MessageVo> messageVoList);

    /**
     * 设置添加消息数据
     *
     * @param msgType  消息类型
     * @param msgTitle 消息标题
     * @param receiver 消息接收人
     * @param content  消息内容
     * @return
     */
    MessageVo onlySetMessageVo(Integer msgType, String msgTitle,
                               String receiver, String content,String creator);
}
