package com.reon.hr.sp.bill.service.bill;

import com.reon.hr.api.bill.dto.PerInsuranceBillAndPerInsuranceBillItemDto;
import com.reon.hr.api.bill.vo.BillPrintVo;
import com.reon.hr.api.bill.vo.PerInsuranceBillItemVo;
import com.reon.hr.api.bill.vo.PerInsuranceBillVo;
import com.reon.hr.api.bill.vo.bill.BillSalaryDispatchAndEpibolyVo;
import com.reon.hr.api.bill.vo.bill.BillSalaryPrintParameterVo;
import com.reon.hr.sp.bill.entity.bill.PerInsuranceBillItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IPerInsuranceBillItemService {

    String PER_INSURANCE_BILL_ITEM_TABLE_PREFIX = "per_insurance_bill_item_";

    String PER_INSURSNCE_BILL_TABLE_PREFIX = "per_insurance_bill_";

    int insertSelective(PerInsuranceBillItem perInsuranceBillItem, Integer currMonth);

    int deleteByPerBillId(Long perBillId, Integer currMonth) ;

    int deleteByPerBillIdList(List<Long> perBillIdList,Integer currMonth);

    List<BillPrintVo> getAllBill(Map<String, Object> map);


    List<PerInsuranceBillItemVo> getByBillMonthAndPerBillIdAndRatioCode(Integer billMonth, Long perBillId, String ratioCode);

    /**
     * 根据订单编号和产品code 获取产品信息
     * @param orderNo    订单编号
     * @param productCode
     * @param billMonth
     * @return
     */
    List<PerInsuranceBillItemVo> getByEmployeeIdAndProductCode(String orderNo, Integer productCode, Integer billMonth);

    /**
     * 根据员工id和产品code查出当前月出账单的最大的服务年月
     * @param orderNo
     * @param productCode
     * @param billMonth
     * @return
     */
    Integer getMaxReceiveMonth(String orderNo,Integer productCode,Integer billMonth);

    List<BillSalaryDispatchAndEpibolyVo> getBillPerSalaryBillList(BillSalaryPrintParameterVo billSalaryPrintParameterVo);

    List<BillSalaryDispatchAndEpibolyVo> getBillSalaryList(BillSalaryPrintParameterVo billSalaryPrintParameterVo);


    List<PerInsuranceBillItemVo> getByEmployeeIds(List<Long> employeeIds,Integer billMonth);

    List<PerInsuranceBillItemVo> getByOrderNos(List<String> orderNos,Integer billMonth);

    List<PerInsuranceBillItemVo> getNormalBillItemByOrderNo(String orderNo, Integer billMonth);

    List<PerInsuranceBillItemVo> getPerBillItemByBillMonthAndOrderNoList(List<String> orderNoList, Integer billMont);







    List<PerInsuranceBillVo> getDisabilityPaymentStatementByBillMonth(Integer billMonth);

    BigDecimal getReceiveAmtByReMonthAndOrderNoAndProduct(List<Map<String, String>> nameMap, String orderNo, Integer productCode,Integer receiveMonth);

	List<PerInsuranceBillAndPerInsuranceBillItemDto> getPerInsuranceBillAndItemByOrderNo(Integer billMonthForSearchItem, Set<String> orderNoSetForMap);
	List<PerInsuranceBillVo> getPerInsuranceBillByOrderNo(Integer billMonthForSearchItem, Set<String> orderNoSetForMap);

    List<PerInsuranceBillVo> getPerInsuranceBillAll(Integer perBillTable);


}
