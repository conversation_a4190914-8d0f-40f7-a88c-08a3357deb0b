package com.reon.hr.api.customer.enums.salary;

import com.reon.hr.api.customer.enums.BaseEnum;

/**
 * <AUTHOR> on 2021/11/10.
 */
public enum SalaryInfoStatus implements BaseEnum {
    DELAY(0,"缓发"),
    FUTURE_PAY(1,"未发放"),
    PAYING(2,"发放中"),
    PAY_SUCCESS(3,"发放成功"),
    PAY_FAILED(4,"发放失败");
    private int code;
    private String name;

    SalaryInfoStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
