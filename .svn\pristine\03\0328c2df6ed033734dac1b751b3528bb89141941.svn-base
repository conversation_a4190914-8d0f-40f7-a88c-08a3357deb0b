package com.reon.ehr.sp.sys.service.impl.sys;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.ehr.api.sys.enums.DataScope;
import com.reon.ehr.api.sys.exception.ServiceException;
import com.reon.ehr.api.sys.utils.SecurityUtils;
import com.reon.ehr.api.sys.utils.StringUtils;
import com.reon.ehr.api.sys.vo.SysUserVo;
import com.reon.ehr.sp.sys.constant.UserConstants;
import com.reon.ehr.sp.sys.domain.entity.*;
import com.reon.ehr.sp.sys.mapper.*;
import com.reon.ehr.sp.sys.service.sys.ISysConfigService;
import com.reon.ehr.sp.sys.service.sys.ISysUserService;
import com.reon.hr.api.base.utils.ListPageUtil;
import com.reon.hr.api.customer.dto.customer.CustomerDto;
import com.reon.hr.common.utils.VoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysUserCustomerMapper userCustomerMapper;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public Page<SysUserVo> selectUserListPage(Integer page, Integer limit, SysUserVo user) {
        Page<SysUserVo> userPage = new Page<>(page, limit);
        List<SysUser> records = userMapper.getUserListPage(user);
        List<SysUserVo> sysUserVos = VoUtil.copyProperties(records, SysUserVo.class);
        if (sysUserVos != null) {
            sysUserVos.removeIf(sysUserVo -> !CollectionUtils.isEmpty(sysUserVo.getRoles()) &&
                    user.getDataScopeMin() >= sysUserVo.getRoles().stream().mapToInt(s -> Integer.parseInt(s.getDataScope())).min().getAsInt());
            userPage.setTotal(sysUserVos.size());
            ListPageUtil<SysUserVo> listPageUtil = new ListPageUtil<>(sysUserVos, limit);
            List<SysUserVo> pagedList = listPageUtil.getPagedList(page);
            userPage.setRecords(pagedList);
        }
        return userPage;
    }

    @Override
    public List<SysUserVo> selectUserList(SysUserVo user) {
        List<SysUser> records = userMapper.selectUserList(user);
        List<SysUserVo> sysUserVos = VoUtil.copyProperties(records, SysUserVo.class);
        if (sysUserVos != null) {
            sysUserVos.removeIf(sysUserVo -> !CollectionUtils.isEmpty(sysUserVo.getRoles()) &&
                    user.getDataScopeMin() >= sysUserVo.getRoles().stream().mapToInt(s -> Integer.parseInt(s.getDataScope())).min().getAsInt());
        }
        return sysUserVos;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    @Override
    public Page<SysUserVo> getAllocatedListPage(Integer pageNum, Integer pageSize, SysUserVo user) {
        Page<SysUserVo> page = new Page<>(pageNum, pageSize);
        List<SysUserVo> records = userMapper.getAllocatedListPage(page, user);
        page.setRecords(records);
        return page;
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    @Override
    public Page<SysUserVo> getUnallocatedListPage(Integer pageNum, Integer pageSize, SysUserVo user) {
        Page<SysUserVo> page = new Page<>(pageNum, pageSize);
        List<SysUserVo> records = userMapper.getUnallocatedListPage(page, user);
        page.setRecords(records);
        return page;
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        SysUser sysUser = userMapper.selectUserByUserName(userName);
        if (sysUser != null && !CollectionUtils.isEmpty(sysUser.getCustomers())) {
            List<CustomerDto> customerDtoList = new ArrayList<>();
            List<Long> custIdList = new ArrayList<>();
            for (SysCustomer sysCustomer : sysUser.getCustomers()) {
                CustomerDto customerDto = new CustomerDto();
                BeanUtils.copyProperties(sysCustomer, customerDto);
                if (sysCustomer.getCustId() != null) {
                    customerDtoList.add(customerDto);
                    custIdList.add(sysCustomer.getCustId());
                }
            }
            sysUser.setCustomerDtoList(customerDtoList);
            sysUser.setCustIdList(custIdList);
        }
        if (sysUser != null && !CollectionUtils.isEmpty(sysUser.getRoles())) {
            sysUser.setDataScopeMin(sysUser.getRoles().stream().mapToInt(s -> Integer.parseInt(s.getDataScope())).min().getAsInt());
            sysUser.setDataScopeMax(sysUser.getRoles().stream().mapToInt(s -> Integer.parseInt(s.getDataScope())).max().getAsInt());
            if (sysUser.getDataScopeMin() > DataScope.DIY.getCode()) {
                sysUser.setDefaultCustomerDtoList(sysUser.getCustomerDtoList());
            }
        }
        return sysUser;
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            //TODO
           /* List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }*/
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        // 新增用户与客户管理
        insertUserCustomer(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        // 删除用户与客户关联
        userCustomerMapper.deleteUserCustomerByUserId(userId);
        // 新增用户与客户管理
        insertUserCustomer(user);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户客户信息
     *
     * @param user 用户对象
     */
    @Override
    public void insertUserCustomer(SysUser user) {
        Long[] custIds = user.getCustIds();
        if (StringUtils.isNotEmpty(custIds)) {
            // 新增用户与客户管理
            List<SysUserCustomer> list = new ArrayList<SysUserCustomer>(custIds.length);
            for (Long custId : custIds) {
                SysUserCustomer up = new SysUserCustomer();
                up.setUserId(user.getUserId());
                up.setCustId(custId);
                list.add(up);
            }
            userCustomerMapper.insertUserCustomerList(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            /*checkUserDataScope(userId);*/
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        // 删除用户与客户关联
        userCustomerMapper.deleteUserCustomer(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {

        return null;
    }

    /**
     * 查询所有用户
     *
     * @return
     */
    @Override
    public Map<String, String> selectAll() {
        return userMapper.selectAll().stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName, (s1, s2) -> s2));
    }
}
