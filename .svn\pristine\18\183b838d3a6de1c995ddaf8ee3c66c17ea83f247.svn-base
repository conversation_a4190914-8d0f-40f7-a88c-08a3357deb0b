/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2020/9/29
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.reon.hr.api.customer.dubbo.service.rpc.customer.ICommInsuQuotationItemResourceWrapperService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ICommInsuQuotationResourceWrapperServiceImpl
 *
 * @date 2020/9/29 15:35
 */
@Service("commInsuQuotationItemService")
public class ICommInsuQuotationItemResourceWrapperServiceImpl implements ICommInsuQuotationItemResourceWrapperService {


}
