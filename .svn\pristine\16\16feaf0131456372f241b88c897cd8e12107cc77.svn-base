/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/2/4
 *
 * Contributors:
 * 	   Administrator - initial implementation
 ****************************************/
package com.reon.hr.api.customer.vo.batchImport;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EmployeeContractExcelDateVo
 *
 * @date 2021/2/4 13:17
 */
@Data
public class EmployeeContractExcelDataVo implements Serializable {
		private static final long serialVersionUID = 6700704275220071864L;
	//导入编号
	private String importNo;
	//解析数据
	private List<Map<String, Object>> list = Lists.newArrayList();
	//解析第一次导入日志数据
	private List<ImportDataLogVo> importDataLogVoList = Lists.newArrayList();
	//导入成功记录数
	private Integer successNum;
	//导入失败记录数
	private Integer failNum;
	//更新错误数据
	private Map<Integer, String> errorDesc = Maps.newHashMap();
	//记录错误数据
	private Map<String, String> dataJson = Maps.newHashMap();
	//转换错误记录为Json数据
	private Map<Integer, String> recordErrorDesc = Maps.newHashMap();
	//获取表头
	List<String> cioheaders = new ArrayList<>();
	//记录第一次解析的错误
	Map<String, Object> ciofamap = new HashMap<>();
}
