package com.reon.hr.sp.bill.entity.salary;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class SynchronizationSupplierSalaryFeeRecord {
    private Long id;

    private Long batchId;

    private Long supplierId;

    private String cityCode;

    private BigDecimal oldTotalSalaryFee;
    private BigDecimal oldSalaryFee;

    private BigDecimal newTotalSalaryFee;
    private BigDecimal newSalaryFee;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

}