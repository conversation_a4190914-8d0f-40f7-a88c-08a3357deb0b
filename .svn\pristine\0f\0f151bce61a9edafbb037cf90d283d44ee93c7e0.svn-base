package com.reon.hr.api.enums;

/**
 * <AUTHOR> on 2022/7/20.
 */
public enum PosKindEnum{
    NORMAL_POST(1,"正常岗位"),
    VIRTUAL_POST(2,"虚拟岗位");

    private Integer code;
    private String name;
    PosKindEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
