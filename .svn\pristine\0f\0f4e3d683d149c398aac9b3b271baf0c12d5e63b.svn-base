package com.reon.hr.api.bill.enums;

import lombok.AllArgsConstructor;

import lombok.Getter;
import lombok.NoArgsConstructor;

public enum BillCostEnum {
    ;

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    public enum ReceivingTypeEnum {
        OWN_COMPANY("自有公司", 0),
        SUPPLIER("供应商公司", 1);
        private String name;
        private Integer index;

        public static String getName(int index) {
            for (ReceivingTypeEnum c : ReceivingTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }
    }



}
