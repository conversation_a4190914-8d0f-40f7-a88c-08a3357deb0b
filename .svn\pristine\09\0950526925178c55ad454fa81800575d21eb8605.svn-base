package com.reon.hr.sp.customer.entity.cus;

import lombok.Data;

import java.util.Date;

@Data
public class CustomerInvoice {
    private Long id;

    private String customerInvoiceNo;

    private Long custId;

    private String acctNo;

    private String title;

    private String acctName;

    private String bankName;

    /**
     * 发票方式(1:电子发票,2:纸质发票)
     */
    private Integer invoiceMethod;

    /**
     * 邮箱
     */
    private String email;

    private String taxNo;

    private String tel;

    private String addr;

    private String remark;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;

    private String corpKind;

    private Long payCustId;
}