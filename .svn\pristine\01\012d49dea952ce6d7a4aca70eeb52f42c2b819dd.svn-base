package com.reon.hr.api.base.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;
import java.util.Date;


/**
 <AUTHOR> */
@Data
@HeadRowHeight(25)//设置标题头行高
@HeadStyle(fillForegroundColor = 17)//IndexedColors.BLUE.getIndex()
@HeadFontStyle(fontHeightInPoints = 9)//设置标题头字体大小
@ColumnWidth(55)//设置单元格宽度
@ContentRowHeight(25)//设置行高
@ContentFontStyle(fontHeightInPoints = 12)//设置excel文件内容字体大小
@ContentStyle(wrapped = true,horizontalAlignment = HorizontalAlignment.CENTER) //设置是否自动换行,内容居中
@ExcelIgnoreUnannotated
public class CompanyAddrListVo implements Serializable {
    private Long id;

    private String comCode;
    @ExcelProperty(value = "公司名称")
    private String comName;

    @ExcelProperty(value = "公司电话")
    private String comTel;

    @ExcelProperty(value = "公司地址")
    private String comAddr;

    private String comEmail;
    @ExcelProperty(value = "纳税人识别号")
    private String taxerIdentNo;

    private String delFlag;
    @ExcelProperty(value = "邮编")
    private String postcode;
    /** 邮编 */
    @ExcelProperty(value = "传真")
    private String faxNumber;
    /** 传真 */
    @ExcelProperty(value = "法人")
    private String legalRep;
    /** 法人 */

    private String fileId;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;


}