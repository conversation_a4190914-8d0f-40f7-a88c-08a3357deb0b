<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td{
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }
        .layui-input{
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <input type="hidden" id="cityCode" name="cityCode" value="${contractAreaVo.cityCode}">
        <input type="hidden" id="accountFlag" name="accountFlag" value="${contractAreaVo.accountFlag}">
        <input type="hidden" id="custId" name="custId" value="${contractAreaVo.custId}">
        <input type="hidden" id="contractTypeListStr" name="contractTypeListStr" value="${contractAreaVo.contractTypeListStr}">
        <table class="layui-table" lay-skin="nob" style="width: 90%; margin: 0 auto;">
            <tr>
                <td width="5%" align="right" style="font-weight:800">合同编号</td>
                <td width="10%"> <input type="text"   maxlength="20" name="contractNo" placeholder="请输入" class="layui-input" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">合同名称</td>
                <td width="10%"> <input type="text"  id="contractName" maxlength="20" name="contractName" placeholder="请输入" class="layui-input" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">合同类型</td>
                <td width="9%">
                    <select name="contractType"  lay-search DICT_TYPE="CONTRACT_CATEGORY">
                        <option value=""></option>
                    </select>
                </td>
                <td width="5%" align="right" style="font-weight:800">小合同编号</td>
                <td width="10%"> <input type="text"   maxlength="20" name="contractAreaNo" placeholder="请输入" class="layui-input" autocomplete="off"></td>
                <td width="5%" align="right" style="font-weight:800">小合同名称</td>
                <td width="10%"><input type="text"   maxlength="20" name="name" placeholder="请输入" class="layui-input" autocomplete="off"></td>
            </tr>
            <tr>
                <td align="right" style="font-weight:800">小合同状态</td>
                <td>
                    <select name="status"  lay-search DICT_TYPE="CONTRACT_AREA_STATUS">
                        <option value=""></option>
                    </select>
                </td>
                <td align="right" style="font-weight:800">派单方</td>
                <td>
                    <input type="text" id="distComName" class="layui-input" readonly>
                    <input type="hidden" class="layui-input" name="distCom" id="distCom">
                </td>
                <td align="right" style="font-weight:800">接单方</td>
                <td><input type="text" maxlength="20" id="receivingName" class="layui-input" readonly >
                    <input type="hidden" class="layui-input" name="receiving" id="receiving">
                </td>
                <td align="right" style="font-weight:800">派单类型</td>
                <td>
                    <select name="dispatchType"  lay-search DICT_TYPE="DISPATCH_TYPE">
                        <option value=""></option>
                    </select>
                </td>
                <td colspan="2" align="center">
                    <button class="layui-btn layui-btn-sm" lay-submit id="btnQuery" lay-filter="btnQueryFilter" authURI="/customer/contractArea/getAllContractArea">查询</button>
                    <button class="layui-btn layui-btn-sm" type="reset" id="resetBtn">重置</button>
                </td>
            </tr>
        </table>
    </form>
    <%--endQuery--%>
</blockquote>
<%--startTable--%>
<table id="contractAreaGrid" lay-filter="contractAreaGridTable"></table>

<input type="hidden" id="parentIframeIndex">
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/personOrder/contractArea.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>

<script type="text/jsp" id="toolDemo">
    <button class="layui-btn layui-btn-sm" id="selectOne" lay-event="select">选择</button>
</script>
</body>
</html>