<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>合同续签流程页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="提交人">提交人</label>
                    <div class="layui-input-inline">
                        <select name="creatorName" id="creatorName" lay-search>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户">客户</label>
                    <div class="layui-input-inline">
                        <input type="text" id="custName" name="custName" class="layui-input"
                               placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="合同编号">合同编号</label>
                    <div class="layui-input-inline">
                        <input type="text" id="contractNo" name="contractNo" class="layui-input"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="合同名称">合同名称</label>
                    <div class="layui-input-inline">
                        <input type="text" id="contractName" name="contractName" class="layui-input"
                               placeholder="请输入" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="创建时间起始">创建时间起始</label>
                    <div class="layui-input-inline">
                        <input type="text" id="startCreateTime" name="startCreateTime" class="layui-input"
                               placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="创建时间截止">创建时间截止</label>
                    <div class="layui-input-inline">
                        <input type="text" id="endCreateTime" name="endCreateTime" class="layui-input"
                               placeholder="请选择" autocomplete="off" readonly>
                    </div>
                </div>

                <div class="layui-inline">
                    <a class="layui-btn layuiadmin-btn-list" id="btnQueryFilter" data-type="reload" lay-filter="btnQueryFilter"
                       lay-submit="">查询</a>
                    <button class="layui-btn" id="reset" type="reset">重置</button>
                </div>
            </div>
        </form>
    </div>

    <div class="layui-card-body">
        <table class="layui-hide" id="contractRenewQueryTable" lay-filter="contractRenewQueryTableFilter"></table>
    </div>
</div>
<script type="text/jsp" id="tooltask">
    {{#  if(d.name != "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="taskApproval"  lay-event="approval">审批</a>
    {{#  } }}
    {{# if(d.name == "上传签章合同" ){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" id="updateFinalFile" lay-event="updateFinalFile">上传最终合同</a>
    {{# } }}

</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/workflow/contractRenewQuery.js?v=${publishVersion}"></script>
</body>
</html>
