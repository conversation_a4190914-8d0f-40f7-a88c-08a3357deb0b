<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>账单模板排序</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .layui-table-cell {
            overflow: visible;
        }

        /* 使得下拉框与单元格刚好合适 */
        .layui-table-box {
            overflow: visible;
        }

        .layui-table-body {
            overflow: visible;
        }

        td .layui-form-select {
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
        }

    </style>
</head>
<body class="childrenBody">
<form class="layui-form" id="searchForm" action="" method="get">
    <div class="layui-inline">
        <label class="layui-form-label layui-elip" title="" style=" width: 127px;">合同编号</label>
        <div class="layui-input-inline">
            <input class="layui-input" type="text" id="contractNo" lay-filter="contractNoFilter" readonly/>
        </div>
    </div>

    <div class="layui-fluid">
        <div class="layui-card-body">
            <table class="layui-hide" id="sortTempletTable" lay-filter="sortTempletTableFilter"></table>
        </div>
    </div>
</form>


<script type="text/jsp" id="toolbarDemo">
    <input type="button" class="layui-btn layui-btn-sm" id="up" lay-event="up"  value="上移">
    <input type="button" class="layui-btn layui-btn-sm" id="down" lay-event="down"   value="下移">
    <input type="button" class="layui-btn layui-btn-sm" id="toFirst" lay-event="toFirst"   value="置顶">
    <input type="button" class="layui-btn layui-btn-sm" id="toLast" lay-event="toLast"   value="置底">
    <input type="button" class="layui-btn layui-btn-sm" id="save" lay-event="save"   value="保存">



</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/tableSelect.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/salaryItem/List.js?v=${publishVersion}"></script>
<script type="text/javascript">
    var ctx = ML.contextPath;
    layui.use(['form', 'layer', 'laydate', 'table', 'jquery', 'tableSelect'], function () {
        var table = layui.table, form = layui.form, laydate = layui.laydate, $ = layui.jquery,
            tableSelect = layui.tableSelect;
        layer = parent.layer === undefined ? layui.layer : parent.layer;
        var appd = '<input style="display:inline-block;width:800px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号/合同名称/编号" autocomplete="off" class="layui-input">';


        tableSelect.render({
            elem: '#contractNo',
            checkedKey: 'contractNo',
            appd: appd,
            table: {
                url: ML.contextPath + '/customer/contract/getContractPageByName',
                limit: 8,
                limits: [8],
                width: 1200,
                height: 400,
                cols: [[
                    {type: 'radio'},
                    {field: 'custNo', title: '客户编号', align: 'center', width: '12%'},
                    {field: 'custName', title: '客户名称', align: 'center', width: '25%'},
                    {field: 'contractNo', title: '合同编号', align: 'center', width: '20%'},
                    {field: 'contractName', title: '合同名称', align: 'center', width: '35%'},
                ]]
            },
            done: function (elem, data) {
                $("#contractNo").val(data.data[0].contractName);
                updateData();
            }
        });


        //接口中查询到的数据
        var templetSortList = [];
        //存储的是调整顺序的缓存数据
        var list = new List();
        //moveIndsArr 要移动的元素下标数组
        var moveIndsArr = [];
        //moveToInd 目标下标值
        var moveToInd;
        //移动到目标值前/后（0/1）
        getTempSortPage(templetSortList);

        function getTempSortPage(data, dataSize) {
            table.render({
                id: 'tempSortPageGrid',
                elem: '#sortTempletTable',
                data: data,
                method: 'get',
                defaultToolbar: [],
                page: false,
                height: 'auto',
                limit: dataSize,
                autoSort: false,
                toolbar: '#toolbarDemo',
                text: {
                    none: '暂无数据'
                },
                cols: [[
                    {type: 'checkbox', width: '10%', fixed: 'left', hide: true},
                    {field: 'id', width: '40', title: '账单模板', align: 'center', hide: true},
                    {field: 'templetName', title: '账单模板名称', width: '100%', align: 'center'}
                ]],
                done: function (res) {

                }
                , end: function () {
                }
            });
        };


        //头工具栏事件
        table.on('rowDouble(sortTempletTableFilter)', function (obj) {
            var selector = obj.tr.selector;
            var rowIndex = selector.split("[")[1].split("]")[0].split("=")[1].replace(/"/g, "");
            layer.msg(rowIndex);
        });


        //点击就会选中/点击就会非选中
        table.on('row(sortTempletTableFilter)', function (obj) {
            var flag = !obj.tr.find(':checkbox:first').prop('checked');
            obj.tr.find(':checkbox').prop('checked', flag);
            if (flag) {
                obj.tr.find('.layui-form-checkbox').addClass('layui-form-checked');  //设置复选框选中样式
                $(obj.tr.selector).attr({"style": "background:cadetblue;color:white"});//改变当前tr背景颜色和字体颜色
                let IngIndex = getIndex(obj);
                //加入
                moveIndsArr.push(Number(IngIndex));
            } else {
                obj.tr.find('.layui-form-checkbox').removeClass('layui-form-checked');//取消复选框选中样式
                $(obj.tr.selector).attr({"style": "background:"});//取消当前tr颜色
                // moveIndsArr.
                let IngIndex = getIndex(obj);
                //移除
                moveIndsArr.forEach(function (item, index, array) {
                    if (Number(item) == Number(IngIndex)) {
                        moveIndsArr.splice(index, 1)
                    }
                })
            }
        });

        //单独控制选中
        function selected(obj, IngIndex) {
            let dateIndex = "[data-index=" + IngIndex + "]";
            let cache = $(dateIndex);
            var flag = !cache.find(':checkbox:first').prop('checked');
            cache.find(':checkbox').prop('checked', flag);
            cache.find('.layui-form-checkbox').addClass('layui-form-checked');  //设置复选框选中样式
            $(cache.selector).attr({"style": "background:cadetblue;color:white"});//改变当前tr背景颜色和字体颜色
            // IngIndex = getIndex(obj);
            // moveIndsArr.push(Number(IngIndex));
        }

        //单独控制非选中
        //单独控制选中
        function notSelected(obj, IngIndex) {
            var flag = !obj.tr.find(':checkbox:first').prop('checked');
            obj.tr.find(':checkbox').prop('checked', flag);
            obj.tr.find('.layui-form-checkbox').addClass('layui-form-checked');  //设置复选框选中样式
            $(obj.tr.selector).attr({"style": "background:cadetblue;color:white"});//改变当前tr背景颜色和字体颜色
            IngIndex = getIndex(obj);
            moveIndsArr.push(Number(IngIndex));
        }


        table.on('toolbar(sortTempletTableFilter)', function (obj) {
            switch (obj.event) {
                case 'up':
                    //将当前位置向上移一位
                    // list.prev();
                    if (!moveIndsArr.length < 1) {
                        layer.msg("向上");
                        let returnCache = list.moveArray(list.dataSouce, moveIndsArr, 0);
                        list.dataSouce = returnCache.arr;
                        let review = returnCache.selectCache;
                        moveIndsArr = review;
                        flashTable(list.dataSouce, list.length());
                        moveIndsArr.forEach(function (item, index, array) {
                            selected(obj, item)
                        });
                    } else {
                        layer.msg("请选中数据");
                    }
                    break;
                case 'down':
                    //将当前位置向下移一位
                    // list.next();
                    if (!moveIndsArr.length < 1) {
                        layer.msg("向下");
                        let returnCache = list.moveArray(list.dataSouce, moveIndsArr, 1);
                        list.dataSouce = returnCache.arr;
                        let review = returnCache.selectCache;
                        moveIndsArr = review;
                        flashTable(list.dataSouce, list.length());
                        moveIndsArr.forEach(function (item, index, array) {
                            selected(obj, item)
                        });
                    } else {
                        layer.msg("请选中数据");
                    }
                    break;
                case 'toFirst':
                    if (!moveIndsArr.length < 1) {
                        // list.front();
                        let returnCache = list.moveArray(list.dataSouce, moveIndsArr, 2);
                        list.dataSouce = returnCache.arr;
                        let review = returnCache.selectCache;
                        moveIndsArr = review;
                        flashTable(list.dataSouce, list.length());
                        moveIndsArr.forEach(function (item, index, array) {
                            selected(obj, item)
                        });
                        layer.msg("我到第一个了!");
                    } else {
                        layer.msg("请选中数据")
                    }
                    break;
                case 'toLast':
                    if (!moveIndsArr.length < 1) {
                        // list.end();
                        let returnCache = list.moveArray(list.dataSouce, moveIndsArr, 3);
                        list.dataSouce = returnCache.arr;
                        let review = returnCache.selectCache;
                        moveIndsArr = review;
                        flashTable(list.dataSouce, list.length());
                        moveIndsArr.forEach(function (item, index, array) {
                            selected(obj, item)
                        });
                        layer.msg("我到最后一个了!");
                    } else {
                        layer.msg("请选中数据");
                    }
                    break;
                case 'save':
                    layer.msg("我保存了!");
                    saveItemCfgList(list.dataSouce);
                    break;
            }
        });


//   重载数据
        function flashTable(data, dataSize) {
            table.reload('tempSortPageGrid', {
                data: data,
                limit: dataSize
            });
        }

        // table.cache["recordTable"];

//获取选中行的index
        function getIndex(obj) {
            if (obj.tr.selector) {
                let selector = obj.tr.selector;
                let rowIndex = selector.split("[")[1].split("]")[0].split("=")[1].replace(/"/g, "");
                return rowIndex;
            }
        }

        function saveItemCfgList(itemCfgList) {
            //表示上传的参数
            let dataGrop = [];
            if (ML.isEmpty($("#contractNo").attr("ts-selected"))) {
                return layer.msg("请选择合同");
            }
            itemCfgList.forEach(function (item, i) {
                item.contractNo = $("#contractNo").attr("ts-selected");
                item.itemId = item.id;
                item.sortNo = i;
                dataGrop.push(item);
            });
            ML.ajax("/customer/billTemplet/saveBillTempletSort", {paramData: JSON.stringify(dataGrop)}, function (d) {
                if (d == 0) {
                    layer.msg("保存成功");
                    updateData();
                    //    保存完成之后不仅要刷新数据,还要重置选择项目
                    moveIndsArr = [];
                } else if (d == -1) {
                    layer.msg("保存失败");
                }
            }, 'post');
        }

        function updateData() {
            ML.ajax("/customer/billTemplet/getTempletSortList", {contractNo: $("#contractNo").attr("ts-selected")}, function (result) {
                templetSortList = result.data;
                var dataSize = templetSortList.length;
                /** 根据顺序来获取到当前table中的所有数据
                 */
                if (dataSize > 0) {
                    list.insertList(templetSortList)
                }
                getTempSortPage(templetSortList, dataSize);
            }, 'get');
        }

    });

</script>

</body>
</html>
