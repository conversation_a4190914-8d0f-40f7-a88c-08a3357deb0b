<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="common/taglibs.jsp" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>首页-后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/modules/layer/default/layer.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/modules/layui-icon-extend/iconfont.css?v=${publishVersion}"
          media="all"/>
    <link rel="stylesheet" href="${ctx}/font_icon_ali/iconfont.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <style>
        #batchImportGrid {
            border: none;
        }

        .layui-table th {
            border-width: 0;
            border-style: none;

        }

        .layui-table-header {
            border-width: 0;
            border-style: none;

        }

        .layui-table td {
            border-width: 0;
            border-style: none;

        }

        #queryValueLink {
            font-size: 25px; /* 设置字体大小为20像素 */
            color: #007bff; /* 设置字体颜色为蓝色 */
            text-decoration: underline; /* 添加下划线 */
        }


    </style>
</head>
<body class="childrenBody">
<%--<div class="row">--%>
<%--<div class="sysNotice col">
    <blockquote class="layui-elem-quote title">增减报表图</blockquote>
    <div class="layui-elem-quote layui-quote-nm">
        <div id="echarts-records" style="background-color:#ffffff;min-height:400px;padding: 10px"></div>
        &lt;%&ndash;<h2># KPI监控</h2>
        <br><span class="layui-badge-dot"></span>暂无
        <br><span class="layui-badge-dot layui-bg-orange"></span>暂无
        <br><span class="layui-badge-dot layui-bg-green"></span>暂无
        <br><span class="layui-badge-dot layui-bg-cyan"></span>暂无
        <br><span class="layui-badge-dot layui-bg-blue"></span>暂无
        <br><span class="layui-badge-dot layui-bg-black"></span>暂无
        <br/><br/><br/>
        <h2># TODO待办事项</h2>
        <br><span class="layui-badge-dot"></span>
        <br> <br> <br> <br> <br><br>&ndash;%&gt;
    </div>
</div>--%>
<%--<div class="sysNotice col">

    <blockquote class="layui-elem-quote title">登录信息</blockquote>
    <table class="layui-table">
        <colgroup>
            <col width="150">
            <col>
        </colgroup>
        <tbody>
        <tr>
            <td>当前账号</td>
            <td>${session_user.loginName}</td>
        </tr>
        <tr>
            <td>用户名</td>
            <td id="userName">${session_user.userName}</td>
        </tr>
        &lt;%&ndash;            <tr>&ndash;%&gt;
        &lt;%&ndash;                <td>机构</td>&ndash;%&gt;
        &lt;%&ndash;                <td>${session_user.organName}</td>&ndash;%&gt;
        &lt;%&ndash;            </tr>&ndash;%&gt;
        &lt;%&ndash;            <tr>&ndash;%&gt;
        &lt;%&ndash;                <td>岗位</td>&ndash;%&gt;
        &lt;%&ndash;                <td>${session_user.positionName}</td>&ndash;%&gt;
        &lt;%&ndash;            </tr>&ndash;%&gt;
        <tr>
            <td>最近登录时间</td>
            <td style="color: red"><fmt:formatDate value="${session_user.lastLogintime}"
                                                   pattern="yyyy-MM-dd HH:mm:ss"/></td>
        </tr>
        <!-- 客户端信息 -->
        <tr>
            <td>操作系统</td>
            <td id="tdOS">未知</td>
        </tr>
        <tr>
            <td>浏览器名称</td>
            <td id="tdBowserName">未知</td>
        </tr>
        <tr>
            <td>浏览器信息</td>
            <td id="tdBowserInfo">未知</td>
        </tr>
        <tr>
            <td>屏幕分辨率</td>
            <td id="tdScreenResolution">未知</td>
        </tr>
        </tbody>
    </table>
</div>
<div class="sysNotice col">
    <div style="padding-top: 15px; padding-right: 80px; display: flex; justify-content: flex-end;" id="todoItem">
        <span style="font-size: 20px;color: red">待办事项总数量:</span>
        <span id="queryValueLink">请稍等....</span>
    </div>
</div>--%>


<%--</div>--%>
<%--<hr class="layui-border-blue">--%>
<%--<div>
    <blockquote class="layui-elem-quote title" style="float: left">政策文件</blockquote>
    <div id="but" style="display: inline">
        <button type="button" class="layui-btn layui-btn-xs" style="margin-top: 10px" lay-filter="btnQueryFilter" id="btnQuery">显示全部</button>
    </div>
    <div id="but1" style="display: inline">
        <button type="button" class="layui-btn layui-btn-xs" style="margin-top: 10px" lay-filter="btnQueryFilter" id="btnQuery2">文本库</button>
    </div>
    <table class="layui-table t1" id="batchImportGrid" lay-filter="batchImportGridTable" lay-skin="line" ></table>
</div>--%>
<div class="layui-bg-gray" style="padding: 16px;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">登录信息</div>
                <div class="layui-card-body" style="position: relative;">
                    <table class="layui-table">
                        <colgroup>
                            <col width="150">
                            <col>
                        </colgroup>
                        <tbody>
                        <tr>
                            <td>当前账号</td>
                            <td>${session_user.loginName}</td>
                        </tr>
                        <tr>
                            <td>用户名</td>
                            <td id="userName">${session_user.userName}</td>
                        </tr>
                        <%--            <tr>--%>
                        <%--                <td>机构</td>--%>
                        <%--                <td>${session_user.organName}</td>--%>
                        <%--            </tr>--%>
                        <%--            <tr>--%>
                        <%--                <td>岗位</td>--%>
                        <%--                <td>${session_user.positionName}</td>--%>
                        <%--            </tr>--%>
                        <tr>
                            <td>最近登录时间</td>
                            <td style="color: red"><fmt:formatDate value="${session_user.lastLogintime}"
                                                                   pattern="yyyy-MM-dd HH:mm:ss"/></td>
                        </tr>
                        <!-- 客户端信息 -->
                        <tr>
                            <td>操作系统</td>
                            <td id="tdOS">未知</td>
                        </tr>
                        <tr>
                            <td>浏览器名称</td>
                            <td id="tdBowserName">未知</td>
                        </tr>
                        <tr>
                            <td>浏览器信息</td>
                            <td id="tdBowserInfo">未知</td>
                        </tr>
                        <tr>
                            <td>屏幕分辨率</td>
                            <td id="tdScreenResolution">未知</td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="padding-top: 15px; padding-right: 80px; display: flex; justify-content: flex-end;"
                         id="todoItem">
                        <div style="position: absolute; top: 15px; right: 150px;">
                            <span style="font-size: 20px;color: red">待办事项总数量:</span>
                            <span id="queryValueLink">请稍等....</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    政策文件
                    <div style="margin-right: 30px;display: inline; float: right">
                        <button type="button" class="layui-btn layui-btn-xs"
                                lay-filter="btnQueryFilter" id="btnQuery">显示全部
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs"
                                lay-filter="btnQueryFilter" id="btnQuery2">文本库
                        </button>
                    </div>

                </div>
                <div class="layui-card-body">
                    <table class="layui-table t1" id="batchImportGrid" lay-filter="batchImportGridTable"
                           lay-skin="line"></table>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">临时政策</div>
                <div class="layui-card-body">
                    <table id="temporaryPolicyTable" lay-filter="temporaryPolicyTableFilter"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/jsp" id="btn">
    <div class="layui-btn-container">
        <div class="layui-inline"><a href="javascript:void(0)" lay-event="down"><i
                class="layui-btn layui-btn-sm">下载</i></a>
        </div>
    </div>
</script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/main.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/font_icon_ali/iconfont.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/getFileName.js?v=${publishVersion}"></script>
</body>
</html>
