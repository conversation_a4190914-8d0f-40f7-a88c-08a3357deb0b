group 'com.reon.hr'
version '1.0-SNAPSHOT'

apply plugin:'application'

sourceCompatibility = 1.8

repositories {
    maven {allowInsecureProtocol=true
        url "http://192.168.101.232:8081/nexus/content/groups/public/"
    }
    mavenCentral()
}

mainClassName = 'org.apache.dubbo.container.Main'
applicationDefaultJvmArgs = ['-Dfile.encoding=utf-8']

ext {
	try{
   	 	profile = project['profile']
    }catch(Throwable t){
    	profile='dev'
    }
}
tasks.withType(Copy).all {
    duplicatesStrategy 'include'
}

processResources {
	def profile_path = "${rootProject.projectDir}/profile/reon-admin-sp/${profile}"
	println "=====================>\u4f7f\u7528\u7684\u914d\u7f6e\u6587\u4ef6\u8def\u5f84\uff1a${profile_path}"
	
	from("${profile_path}")
	into "${buildDir}/resources/main"
}

dependencies {
    implementation project(':reon-admin-api')
    implementation project(':reon-base-api')
    implementation project(':reon-common-utils')

    implementation group: 'com.lowagie', name: 'itext', version: '2.1.7'

    implementation group: 'org.javassist', name: 'javassist', version: '3.19.0-GA'
    implementation group: 'org.apache.mina', name: 'mina-core', version: '1.1.7'
    implementation group: 'org.glassfish.grizzly', name: 'grizzly-core', version: '2.1.4'
    implementation group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5'
    implementation group: 'com.thoughtworks.xstream', name: 'xstream', version: '1.4.8'
    implementation group: 'org.apache.bsf', name: 'bsf-api', version: '3.1'
    
    implementation group: 'org.jboss.resteasy', name: 'resteasy-jaxrs', version: '3.0.16.Final'
    implementation group: 'org.jboss.resteasy', name: 'resteasy-jdk-http', version: '3.0.16.Final'
    implementation group: 'org.jboss.resteasy', name: 'resteasy-jackson-provider', version: '3.0.16.Final'
    implementation group: 'org.jboss.resteasy', name: 'resteasy-jaxb-provider', version: '3.0.16.Final'
    implementation group: 'org.jboss.resteasy', name: 'resteasy-client', version: '3.0.16.Final'

   	// redis
    implementation ("redis.clients:jedis:3.8.0",
            "org.springframework.data:spring-data-redis:2.3.9.RELEASE"
    )

    implementation group: 'javax.mail', name: 'mail', version: '1.4.7'


    // 其他
	implementation("javax.servlet:javax.servlet-api:4.0.1")
	//implementation ("org.apache.tomcat.embed:tomcat-embed-core:8.5.6",
   // 		 "org.apache.tomcat.embed:tomcat-embed-logging-juli:8.5.2")
   // implementation ("javax.validation:validation-api:1.0.0.GA")
   // implementation group: 'org.apache.httpcomponents', name: 'httpcore', version: '4.4.10'
   // implementation group: 'org.apache.httpcomponents', name: 'httpmime', version: '4.5.6'
}
