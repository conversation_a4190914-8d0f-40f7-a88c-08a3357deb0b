var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect','upload','element'], function () {
    var table = layui.table,
        form = layui.form,
        laydate = layui.laydate,
        tableSelect = layui.tableSelect,
        upload = layui.upload,
        element = layui.element;
          var layer = parent.layer === undefined ? layui.layer : parent.layer;



var s =ML.dictFormatter("PAYMENT_SUBCLASS",2);
    element.on('tab(paymentApplyTabFilter)',function (data) {
        if(data.index == 1){
            var pid = $('#pid').val();
            if(pid){
                $("#workFlowImg").attr("src","/workflow/workflowGraph?pid="+pid);
                ML.ajax("/workflow/getWorkflowAuditLogList",{"pid":pid},function (res) {
                    var commentData = res.data;
                    table.render({
                        id: 'paymentApplyFlowTable',
                        elem: '#paymentApplyFlowTable',
                        data :commentData,
                        cols :[[
                            {title:'序号',type:'numbers'}
                            ,{field: 'userId',title:'处理人',align:'center',templet:function (d) {
                                    return ML.loginNameFormater(d.userId);
                                }}
                            ,{field: 'auditType',title:'处理类型',align:'center',templet: function(d){
                                    return ML.dictFormatter("AUDIT_TYPE",d.auditType);
                                }}
                            ,{field: 'comment',title:'审批意见',align:'center'}
                            ,{field: 'createTime',title:'审批时间',align:'center'}
                        ]]
                    });
                },'GET');

            }

        }
    });




    //开始日期范围
    laydate.render({
        elem: '#lastDate',
        format: 'yyyy-MM-dd'
    });

    //日期点击事件
    var initYear;
    laydate.render({
        elem: '#payMonth',
        type: 'month',
        format: 'yyyyMM',
        min: '2010-01-01',
        max: '2099-12-12',
        theme: 'grid',
        // 控件在打开时触发，回调返回一个参数：初始的日期时间对象
        ready: function (date) {
            initYear = date.year;
        },
        // 年月日时间被切换时都会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
        change: function (value, date, endDate) {
            var selectYear = date.year;
            var differ = selectYear - initYear;
            if (differ == 0) {
                if ($(".layui-laydate").length) {
                    $("#payMonth").val(value);
                    $(".layui-laydate").remove();
                }
            }
            initYear = selectYear;
        }
    });

    //上传文件

    var uploadIds = [];
    var fileName = '';
    // var payTypeV;
    $(function () {
        //转换字典数据
        // payKindV = $("#payKind1").val();
        // payTypeV = $("#payType1").val();
        payMethodV = $("#payMethod1").val();
        bankTypeV = $("#bankType1").val();
        // payComV = $("#payCom1").val();

        //支付大类
        // if (payKindV != null && payKindV != '') {
        //     $("#payKind").append($("<option/>").text(ML.dictFormatter("PAYMENT_BROAD_CATEGORIES", payKindV)).attr("value",payKindV).attr("selected","selected"));
        // }
        //支付子类
        // if (payTypeV != null && payTypeV != '') {
        //     let type = ML.dictFormatter("PAYMENT_SUBCLASS", payTypeV);
        //     $('#payType').val(type);
        // }
        //支付方式
        if (payMethodV != null && payMethodV != '') {
            $("#payMethod").val(payMethodV);
        }
        //支付银行
        if (bankTypeV != null && bankTypeV != '') {
            $("#bankType").val(bankTypeV);
        }

        if ($('#fileId').val() != null) {
            // $("#paymentApplyUpload").attr("disabled","disabled");
            $.ajaxData.getIdFileName($('#fileId').val(),'edit','#upload');
            uploadIds.push({'fileId': $('#fileId').val()});
        }else {$("#paymentApplyUpload").removeAttr("disabled","disabled");}

        form.render('select');
    });

    //上传
    upload.render({
        elem: '#paymentApplyUpload' //绑定元素
        , url: ML.contextPath + '/sys/file/upload' //上传接口
        , accept: 'file'
        , headers: {contentType: false, processData: false}
        , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
        , field: 'file'
        , auto: false
        , choose: function (obj) {
            obj.preview(function (index, file, result) {
                fileName = file.name;
                var size = file.size;
                var tip = true;
                if (size > (8 * 1024 * 1024)) {
                    layer.msg("上传文件大小不能超过8M", {icon: 2});
                    return false;
                }
                if (tip) {
                    obj.upload(index, file);//文件上传
                }
            });
        }
        , done: function (res) {
            //上传完毕回调
            if (res.code == 0) {
                uploadIds.push({'fileId': res.data.fileId});
                $('#upload').append(' <span id="upload-' + res.data.fileId + '" class="fileFlag"><a href="' + ML.fileServerUrl + res.data.fileId + '" target="_blank">' + fileName + '</a><a href="javascript:void(0)" class="deleteFile" title="删除"><i class="layui-icon layui-icon-delete" style="\n' +
                    '    position: relative;\n' +
                    '    left: 1px;\n' +
                    '    top: 2px;\n' +
                    '"></i></a></span>&nbsp;&nbsp;');
                layer.msg('上传成功', {icon: 1});
                $("#paymentApplyUpload").attr("disabled","disabled");
            }
        }
        , error: function () {
            //请求异常回调
            console.log("error");
            layer.msg('上传失败', {icon: 5});
        }
    });

    ////移除span
    $(document).on("click", ".deleteFile", function () {
        var id = $(this).parent().attr('id');
        var split = id.split("upload-");
        var fileId = split[1];
        ML.ajax("/customer/contract/delByFileId?fileId=" + fileId, {}, function (result) {
                if (result.code == 0) {
                    layer.msg("删除文件成功！");
                    $("#paymentApplyUpload").removeAttr("disabled");
                  uploadIds = [];
                }
            },
            'POST');
        uploadIds.splice(uploadIds.indexOf($(this).parent()[0].id.split('-')[1]), 1);
        $(this).parent()[0].remove();
   $("#paymentApplyUpload").removeAttr("disabled","disabled");
    });

    // 表单保存
    form.on("submit(save)", function (data) {
        saveForm('save', data);
        return false;
    });
//提交表单
    form.on("submit(commit)", function (data) {
        saveForm('commit', data);
        return false;
    });

    // 发送保存请求，type为保存类型
    function saveForm(type, data) {
        // 判断是否已上传文件
        if (uploadIds == false ) {
            return layer.msg('请上传文件后再提交哦！');
        }
        ML.layuiButtonDisabled($('#' + type));// 禁用
        /*上传id*/
        uploadIds.forEach(function (obj) {
            data.field['fileId'] = obj.fileId;
            data.field['fileName'] = fileName;
        });
        data.field['type'] = type;
        data.field['posCode'] = $("#posCode").val();
        delete data.field['file'];
        //将String 转为 枚举int
        // data.field['payType'] = payTypeV;

        //转账需要 填写 收款方信息
        if (data.field['payMethod'] == 2){
            $('#payee').removeAttr("lay-verify","required");
        }

        $.ajax({
            url: ML.contextPath + "/bill/paymentApply/saveOrCommit",
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data.field),
            contentType: "application/json;charset=UTF-8",
            success: function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error: function (data) {
                $('#payee').attr("lay-verify","required");
                layer.msg("系统繁忙，请稍后重试!");
            }
        });
    }

    //支付抬头  查出所有自有公司
    $(document).ready(function () {
        // $("#custName").attr("disabled","disabled");
        // var payComList = [];
        // $.ajax({
        //     type: "GET",
        //     param:{"payCom1":$("#payCom1").val()},
        //     url: ctx + "/customer/contract/orgList",
        //     dataType: 'json',
        //     success: function (data) {
        //         payComList = [];
        //         payComList = data.data;
        //         $.each(payComList, function (i, item) {
        //             if(payCom1.defaultValue != item.orgCode){
        //             $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode));
        //             }else{
        //             $("#payCom").append($("<option/>").text(item.orgName).attr("value", item.orgCode).attr('selected','selected'));
        //             }
        //         });
        //         form.render('select');
        //     },
        //     error: function (data) {
        //         console.log("error");
        //     }
        // });
    });


    $(document).on("click","#close",function () {
        layer.closeAll('iframe');
    });


    $(document).ready(function () {
        //去除工资
        // $("#payType option[value='3'] ").remove()
        // var judge = $("#payType").val();
        // if("3"==judge){
        //     $("#custName").removeAttr("disabled");
        // }else{
        //     $("#custName").val("").text("");
        //     $("#custName").attr("disabled","disabled");
        // }
        //     var id = $("#custId").val();
        //     if ($("#custId").val()) {
        //         $.ajax({
        //             type: 'GET',
        //             url: ML.contextPath + '/customer/customer/findById',
        //             data: {"id": id},
        //             dataType: 'json',
        //             success: function (data) {
        //                 $("#custName").val(data.data.custName);
        //             },
        //             error: function (resp, textStatus, errorThrown) {
        //                 console.log("ajax请求姓名错误!!");
        //             }
        //         });
        //
        //     }
        }
    );



    // // 搜索条件  客户下拉列表框
    // var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // // 客户下拉数据表格
    // tableSelect.render({
    //     elem: '#custName',
    //     checkedKey: 'id',
    //     appd: appd,
    //     table: {
    //         url: ML.contextPath + '/customer/contract/getCustomerByAll',
    //         cols: [[
    //             {type: 'radio'}
    //             , {field: 'id', title: '客户ID', align: 'center'}
    //             , {field: 'custNo', title: '客户编号', align: 'center'}
    //             , {field: 'custName', title: '客户名称', align: 'center'}
    //         ]]
    //     },
    //     done: function (elem, data) {
    //         var NEWJSON = [];
    //         var id = '';
    //         var name = '';
    //         layui.each(data.data, function (index, item) {
    //             NEWJSON.push(item.custName)
    //             custNo = item.custNo;
    //             id = item.id;
    //             name = item.custName;
    //         });
    //         // 回填值
    //         elem.val(NEWJSON.join(","));
    //         $("#custId").val(id);
    //         $("#custName").val(name).text(name);
    //
    //     }
    // });

    // form.on('select(payTypeFilter)', function (data) {
    //     var judge = data.value;
    //     if("3" == judge){
    //         $("#custName").removeAttr("disabled");
    //     }else{
    //         $("#custName").val("");
    //         $("#custName").attr("disabled","disabled");
    //         $("#custId").val("");
    //     }
    // });

    //对于单据数量的验证
    $(document).on("input",function (e) {
        if(e){
            numberCheck("#listCnt","number")
        }
    });


    // 数字验证
    function numberCheck(param,type){
        $(param).on("input propertychange", function() {
            var val = $(this).val();
            //先把非数字的都替换掉，除了数字和.
            val = val.replace(/[^\d.]/g, "");
            if(type === "number"){
                //先把非数字的都替换掉，除了数字
                val = val.replace(/[^\d]/g, "");
            }
            //必须保证第一个为数字而不是.
            val = val.replace(/^\./g, "");

            //保证只有出现一个.而没有多个.
            val = val.replace(/\.{2,}/g, "");

            //保证.只出现一次，而不能出现两次以上
            val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            $(this).val(val);
        })
    }

});