var ctx = ML.contextPath;
var orders = [];
layui.config({
    base: ctx + "/js/"
}).use(['form', 'layer', 'laydate', 'table', 'tableSelect'], function () {
    var table = layui.table, form = layui.form, laydate = layui.laydate, tableSelect = layui.tableSelect;
    layer = parent.layer === undefined ? layui.layer : parent.layer;
    form.render('select');


    table.render({
        id: 'employeeGrid',
        elem: '#employeeGrid',
        url: ctx + '/customer/employee/getListPage',
        where: {'status': orderFlag},
        method: 'get',
        page: true, //默认为不开启
        limits: [50, 100, 200],
        limit: 50,
        height: 580,
        title: "确认员工离职信息",
        defaultToolbar: [],
        toolbar: '#toolbarDemo',
        text: {
            none: '暂无数据' //无数据时展示
        },
        cols: [[
            {type: 'checkbox', width: 50, fixed: 'left'},
            {field: 'employeeNo', title: '雇员编号', width: 160, align: 'center', fixed: 'left'},
            {field: 'orderNo', title: '订单编号', width: 160, align: 'center', fixed: 'left'},
            {field: 'employeeName', title: '雇员姓名', width: 100, align: 'center', fixed: 'left'},
            {field: 'certNo', title: '证件号码', width: 170, align: 'center'},
            {field: 'custNo', title: '客户编号', width: 170, align: 'center'},
            {field: 'custName', title: '客户名称', width: 160, align: 'center'},
            {
                field: 'distributeType', title: '人员分布', width: 100, align: 'center', templet: function (d) {
                    return ML.dictFormatter('EMP_DISTRIBUTE_TYPE', d.distributeType);
                }
            },
            {field: 'contractAreaName', title: '小合同名称', width: 160, align: 'center'},
            {
                field: 'accountFlag', title: '是否单立户', align: 'center', width: '100', templet: function (d) {
                    return ML.dictFormatter("BOOLEAN_TYPE", d.accountFlag);
                }
            },
            {field: 'distComName', title: '派单方', width: 160, align: 'center'},
            {
                field: 'prjCs', title: '派单方客服', width: 160, align: 'center', templet: function (d) {
                    if (d.prjCs) {
                        return ML.loginNameFormater(d.prjCs);
                    } else if (!d.prjCs && d.prjSupervisor) {
                        return ML.loginNameFormater(d.prjSupervisor);
                    } else if (!d.prjCs && !d.prjSupervisor && d.prjMangr) {
                        return ML.loginNameFormater(d.prjMangr);
                    } else {
                        return "";
                    }
                }
            },
            {field: 'receivingName', title: '接单方', width: 160, align: 'center'},
            {
                field: 'revCs', title: '接单方客服', width: 160, align: 'center', templet: function (d) {
                    if (d.revCs) {
                        return ML.loginNameFormater(d.revCs);
                    } else if (!d.revCs && d.revSupervisor) {
                        return ML.loginNameFormater(d.revSupervisor);
                    } else if (!d.revCs && !d.revSupervisor && d.revMangr) {
                        return ML.loginNameFormater(d.revMangr);
                    } else {
                        return "";
                    }
                }
            },
            {field: 'entryDate', title: '入职日期', width: 110, align: 'center'},
            {field: 'applyEntryTime', title: '申请入职时间', width: 160, align: 'center'},
            {
                field: 'eedStatus', title: '入离职状态', width: 100, align: 'center', templet: function (d) {
                    return ML.dictFormatter("IN_OUT_STATUS", d.eedStatus);
                }
            },
            {
                field: 'orderStatus', title: '离职确认状态', width: 160, align: 'center', templet: function (d) {
                    return ML.dictFormatter("EMP_ORDER_STATUS", d.orderStatus);
                }
            },
            /*{
                field: 'chgState', title: '变更状态', width: 120, align: 'center', templet: function (d) {
                    return ML.dictFormatter("ORDER_CHG_STATUS", d.chgState);
                }
            },*/
            {field: 'dimissionDate', title: '离职日期', width: 150, align: 'center',},
            {field: 'applyDimissionDate', title: '申请离职时间', width: 180, align: 'center',},
            {
                field: 'dimissionReason', title: '离职原因', width: 160, align: 'center', templet: function (d) {
                    return ML.dictFormatter("LEAVE_REASON", d.dimissionReason);
                }
            },
            {field: 'tel', title: '联系电话', width: 130, align: 'center'},
            {field: 'mobile', title: '手机', width: 120, align: 'center'}
        ]],
        done: function (res) {
            form.render('select');
            ML.hideNoAuth();
            if (orderFlag != '7') {
                $("#reject").addClass("layui-hide")
            }
            for (var i = 0; i < res.data.length; i++) {
                if (res.data[i].saveType !=null) {
                    $('.layui-table-main tr').eq(i).css("color", "red");
                    $(".layui-table-fixed").find("div").find("table").find("tr").eq(i + 1).css("color", "red");
                }
            }
            table.on('toolbar(employeeFilter)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id), data = checkStatus.data;
                switch (obj.event) {
                    case 'query':
                        if (data.length == 0) {
                            layer.msg("请选择一行");
                        } else if (data.length > 1) {
                            layer.msg("只能同时查看一个");
                        } else {
                            queryOne(data[0].orderNo);
                        }
                        break;
                    case 'confirm':
                        if (data.length === 0) {
                            layer.msg("请选择一行");
                        } else if (data.length > 1) {
                            layer.msg("只能同时选择一个");
                        } else {
                            layer.open({
                                type: 2,
                                title: "离职确认",
                                area: ['70%', '90%'],
                                shade: 0,
                                maxmin: true,
                                offset: 'auto',
                                shade: [0.8, '#393D49'],
                                content: ctx + "/customer/employee/gotoEntryConfrimOnePage?orderNo=" + data[0].orderNo + "&orderStatus=" + orderFlag,
                                success: function (layero, index) {
                                    var body = layer.getChildFrame('body', index);
                                    body.find("#insuranceList").val(JSON.stringify(data[0].insuranceList));
                                },
                                end: function () {
                                    reloadTable();
                                }
                            });
                        }
                        break;
                    case 'confirmBatch':
                        if (data.length === 0)
                            layer.msg('请选择一行');
                        else {
                            var map = {};
                            var orderNos = [];
                            var serviceSiteCfgList = [];
                            var day = new Date().getDate();
                            for (var i = 0; i < data.length; i++) {
                                orderNos.push(data[i].orderNo);
                                if(data[i].serviceSiteCfgDto){
                                    serviceSiteCfgList.push(data[i].serviceSiteCfgDto);
                                }
                            }
                            let res =false;
                            ML.ajax("/customer/workInjury/checkOrderNoWhetherExistInjury", {"orderNoList": JSON.stringify(orderNos),"type":1}, function (result) {
                                if (result !== 0) {
                                    res =true;
                                }
                            });

                            map['orderNos'] = orderNos;
                            map['orderStatus'] = orderFlag;
                            if(!serviceSiteCfgList||serviceSiteCfgList.length==0){
                                layer.confirm("该地区没有服务网点！！",{
                                    btn: ['确定', '取消'] //按钮
                                }, function (index) {
                                    if (!res){
                                        confirmBatch(map);
                                        layer.close(index);
                                    }else {
                                        layer.confirm("所选订单存在工伤,是否确认减员",{
                                            btn: ['确定', '取消'] //按钮
                                        }, function (index) {
                                            confirmBatch(map);
                                            layer.close(index);
                                        });
                                    }

                                });
                                return;
                            }
                            let serviceSiteCfg;
                            let  max = 32;
                            layui.each(serviceSiteCfgList,function (index,item){
                                if(item.insurAddDay < max){
                                    max = item.insurAddDay;
                                    serviceSiteCfg = item;
                                }
                            })

                            // 获取最小的  增减员截止点
                            if(day > serviceSiteCfg.insurSubDay){
                                layer.confirm("过了减员截止点 :"+serviceSiteCfg.insurSubDay +
                                    "; 公积金减员截点 :" +serviceSiteCfg.crfSubDay+
                                    "; 社保申报频率 :"+ serviceSiteCfg.applyInsurMsg+
                                    "; 公积金申报频率 :"+ serviceSiteCfg.applyFundMsg,{
                                    btn: ['确定', '取消'] //按钮
                                }, function (index) {
                                    if (!res){
                                        confirmBatch(map);
                                        layer.close(index);
                                    }else {
                                        layer.confirm("所选订单存在工伤,是否确认减员",{
                                            btn: ['确定', '取消'] //按钮
                                        }, function (index) {
                                            confirmBatch(map);
                                            layer.close(index);
                                        });
                                    }
                                });
                            }else {
                                if (res){
                                    layer.confirm("所选订单存在工伤,是否确认减员",{
                                        btn: ['确定', '取消'] //按钮
                                    }, function (index) {
                                        confirmBatch(map);
                                        layer.close(index);
                                    });
                                }else{
                                    confirmBatch(map);
                                }
                            }
                        }
                        break;
                    case 'rejected':
                        layer.msg("离职驳回");
                        if (data.length === 0)
                            layer.msg('请选择一行');
                        else {
                            var map = {};
                            var orderNos = [];
                            for (var i = 0; i < data.length; i++) {
                                orderNos.push(data[i].orderNo);
                            }
                            map['orderNos'] = orderNos;
                            map['orderStatus'] = orderFlag;
                            if (orderNos.length > 0) {
                                layer.prompt({title: '请输入驳回原因', formType: 2}, function (text, index) {
                                    map['dimReason'] = text;   // 离职原因
                                    $.ajax({
                                        url: ctx + "/customer/employee/rejectedLeave",
                                        type: 'POST',
                                        dataType: 'json',
                                        contentType: 'application/json',
                                        data: JSON.stringify(map),
                                        success: function (result) {
                                            layer.msg(result.msg);
                                            if (result.code == 0) {
                                                reloadTable();
                                                layer.close(index);
                                            }
                                        }
                                    })
                                });
                            }
                        }
                        break;
                    case 'save':
                        if (data.length === 0)
                            layer.msg('请至少选择一行');
                        else {

                            var orderNos = '';
                            for (var i = 0; i < data.length; i++) {
                                var order = data[i].orderNo
                                orderNos = orderNos+order+","
                            }

                            layer.open({
                                type: 2, //为1时弹出页面直接展示content中内容，为2时会向后台发送请求,content 内容为请求地址

                                ttitle: "保存",
                                area: ['40%', '40%'],
                                shade: 0,
                                maxmin: true,
                                offset: 'auto',

                                shade: [0.8, '#393D49'],
                                content: ctx + "/customer/employee/gotoRevConfirmEmployeeLeaveSavePage?orderNos="+orderNos,
                                success:function (layero, index) {
                                    var body = layer.getChildFrame('body', index);
                                    body.find("#orderStatus").val(data[0].orderStatus);
                                },
                                end: function () {
                                    reloadTable();

                            }

                            });

                        }
                        break;
                        reloadTable();
                        break;
                }
            });
        }

    });


    function confirmBatch(map) {
        $.ajax({
            url: ctx + "/customer/employee/confirmLeaveBatch",
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(map),
            success: function (result) {
                layer.msg(result.msg);
                if (result.code == 0) {
                    reloadTable();
                }
            }
        });
    }

    function queryOne(orderNo) {
        layer.open({
            type: 2,
            title: "订单详情",
            area: ['70%', '90%'],
            shade: 0,
            maxmin: true,
            offset: 'auto',
            shade: [0.8, '#393D49'],
            content: ctx + "/customer/employee/gotoOrderDetailPage?orderNo=" + orderNo,
        });
    }

    ////双击该行查看
    table.on('rowDouble(employeeFilter)', function (obj) {
        queryOne(obj.data.orderNo);
    });

    ////查询
    form.on('submit(btnQueryFilter)', function (data) {
        reloadTable();
        return false;
    });

    var reloadTable = function () {
        table.reload('employeeGrid', {
            where: {'searchData': JSON.stringify(serialize("searchForm"))},
        });
    }

//日期范围
    var startDate = laydate.render({
        elem: '#applyDimissionDateS',
        max: "2099-12-31",//设置一个默认最大值
        showBottom: false,
        done: function (value, date) {
            if (null != value && '' != value) {
                endDate.config.min = {
                    year: date.year,
                    month: date.month - 1, //关键
                    date: date.date
                };
            }
        }
    });
    var endDate = laydate.render({
        elem: '#applyDimissionDateE',//选择器结束时间
        min: "1970-1-1",//设置min默认最小值
        showBottom: false,
        done: function (value, date) {
            if (null != value && '' != value) {
                startDate.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date
                }
            }
        }
    });
    // 重置
    $("#resetBtn").on('click',function () {
        endDate.config.min = {year: 2010, month:0, date:1};
        endDate.config.max = {year: 2099, month:11, date:12};
        startDate.config.min = {year: 2010, month:0, date:1};
        startDate.config.max = {year: 2099, month:11, date:12};
        form.render();
    });

});
