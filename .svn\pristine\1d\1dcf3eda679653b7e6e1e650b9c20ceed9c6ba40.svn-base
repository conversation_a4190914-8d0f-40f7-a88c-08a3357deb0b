<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑社保套餐</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<form class="layui-form" method="post">
    <input type="hidden" name="setCode" id="setCode">
    <input type="hidden" id="personCode">
    <input type="hidden" name="cityName" id="cityName">
    <input type="hidden" name="optType" id="optType" >
    <input type="hidden" id="supplierId" >
    <table class="layui-table" lay-skin="nob" style="width: 90%;margin: 0 auto;">
        <tr>
            <td align="right"><i style="color: red; font-weight: bolder;">*</i>社保套餐名称</td>
            <td>
                <div style=" width: 220px;">
                <input class="layui-input" type="text" name="setName" id="setName" lay-verify="required"
                       autocomplete="off">
                </div>
            </td>

            <td colspan="2" align="right"><i style="color: red; font-weight: bolder;">*</i>是否社保必缴费</td>
            <td colspan="2">
                <select class="layui-select" name="insurRemitFlag" id="insurRemitFlag" DICT_TYPE="BOOLEAN_TYPE"
                        lay-verify="required">
<%--                    <option value=""></option>--%>
                </select>
            </td>

            <td width="12%" align="right" >社保减员截止日</td>
            <td width="10%"><input class="layui-input" type="text" id="insurSubDay" autocomplete="off" value=""></td>
        </tr>
        <tr>
            <%--            <td align="right" ><i style="color: red; font-weight: bolder;">*</i>社保套餐名称</td>--%>
            <%--            <td><input class="layui-input" type="text" name="setName" id="setName" lay-verify="required" autocomplete="off"></td>--%>
            <td width="10%" align="right"><i style="color: red; font-weight: bolder;">*</i>所属城市</td>
            <td width="15%">
           <div style="width: 220px;">
<%--                <div class="layui-form-select" style="width: 220px;">--%>
                <select class="layui-select" name="cityCode" id="cityCode" lay-filter="cityCodeFilter" AREA_TYPE lay-search
                        lay-verify="required">
                    <option value=""></option>
                </select>
           </div>
            </td>

            <td colspan="2" align="right"><i style="color: red; font-weight: bolder;">*</i>是否代理员工公积金必缴</td>
            <td colspan="2"><select class="layui-select" name="crfRemitFlag" id="crfRemitFlag" DICT_TYPE="BOOLEAN_TYPE"
                                    lay-verify="required">
<%--                <option value=""></option>--%>
            </select></td>

            <td align="right" >社保增员截止日</td>
            <td><input class="layui-input" type="text" id="insurAddDay" autocomplete="off" value=""></td>
        </tr>
        <tr>
            <td align="right" ><i style="color: red; font-weight: bolder;">*</i>人员类型名称</td>
            <td> <div style="width: 220px;"><select class="layui-select" name="indTypeCode" id="indTypeCode" lay-filter="indTypeCodeFilter"  lay-verify="required">
                <option value=""></option>
            </select></div></td>
            <td colspan="2" align="right"><i style="color: red; font-weight: bolder;">*</i>是否有补充公积金</td>
            <td colspan="2"><select class="layui-select" name="addCrfFlag" id="addCrfFlag" DICT_TYPE="BOOLEAN_TYPE"
                                    lay-verify="required">
<%--                <option value=""></option>--%>
            </select>
            </td>

            <td align="right" >公积金减员截止日</td>
            <td><input class="layui-input" type="text" id="crfSubDay" autocomplete="off" value=""></td>
        </tr>
        <tr>
            <td align="right"><i style="color: red">*</i>分公司/供应商</td>
            <td> <div style="width: 220px;"><select class="layui-select" name="supplierCode" id="supplierCode" lay-verify="required" lay-filter="supplierCodeFilter">
                <option value=""></option>
            </select></div></td>
            <td width="8%" align="right"><i style="color: red; font-weight: bolder;">*</i>标准套餐</td>
            <td width="10%"><select class="layui-select" name="standardFlag" id="standardFlag" lay-filter="standardFlag" DICT_TYPE="BOOLEAN_TYPE"
                                    lay-verify="required">
<%--                <option value=""></option>--%>
            </select></td>
            <td width="9%" align="right"><i style="color: red; font-weight: bolder;">*</i>是否单立户</td>
            <td width="10%"><select class="layui-select" name="singleFlag" id="singleFlag" lay-filter="singleFlag" DICT_TYPE="BOOLEAN_TYPE">
<%--                <option value=""></option>--%>
            </select></td>
            <td align="right" >公积金增员截止日</td>
            <td><input class="layui-input" type="text"  id="crfAddDay" autocomplete="off" value=""></td>
        </tr>
    </table>
<br><br>
    <div style="float: right; margin-right: 100px;">
        <%--<button class="layui-btn saveOne" lay-submit lay-filter="continueFilter" id="continueBtn">连续录入</button>--%>
        <a class="layui-btn saveOne" lay-submit lay-filter="vindicateFilter" id="vindicateBtn">保存并维护社保产品</a>
        <a class="layui-btn saveOne" lay-submit lay-filter="saveFilter" id="saveBtn">保存</a>
        <a class="layui-btn" id="cancelBtn">取消</a>
        <a class="layui-btn" id="closeBtn" style="display:none;">关闭</a>
    </div>
</form>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/sys/insuranceSet/editInsuranceSet.js?v=${publishVersion}"></script>
</body>
</html>