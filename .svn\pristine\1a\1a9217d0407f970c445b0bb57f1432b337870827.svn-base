<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.InsurancePracticeCustPayDetailMapper">


    <delete id="deleteInsurancePracticeCustPayDetailByPayId">
        delete from insurance_practice_cust_pay_detail where pay_id = #{payId}
    </delete>

    <insert id="batchInsertInsurancePracticeCustPayDetail">
        INSERT INTO insurance_practice_cust_pay_detail (
        pay_id,
        pay_com,
        dis_com,
        cust_id,
        social_amt,
        provident_amt,
        service_amt,
        total_amt,
        balance_amt,
        type,
        org_type,
        act_pay_amt,
        sum,
        contract_type
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.payId},
            #{item.payCom},
            #{item.disCom},
            #{item.custId},
            #{item.socialAmt},
            #{item.providentAmt},
            #{item.serviceAmt},
            #{item.totalAmt},
            #{item.balanceAmt},
            #{item.type},
            #{item.orgType},
            #{item.actPayAmt},
            #{item.sum},
            #{item.contractType}
            )
        </foreach>
    </insert>


    <select id="getInsurancePracticeCustPayDetailListByPayIdAndDisComList"
            resultType="com.reon.hr.api.bill.vo.InsurancePracticeCustPayDetailVo">
        select * from insurance_practice_cust_pay_detail where pay_id = #{payId}
        and dis_com in
        <foreach item="item" collection="disComList" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>

    <select id="getInsurancePracticeCustPayDetailListGroupByPayIdAndDisComList"
            resultType="com.reon.hr.api.bill.vo.InsurancePracticeCustPayDetailVo">
        SELECT
        pay_com,
        dis_com,
        SUM(social_amt) AS social_amt,
        SUM(provident_amt) AS provident_amt,
        SUM(service_amt) AS service_amt,
        SUM(total_amt) AS total_amt,
        SUM(balance_amt) AS balance_amt,
        SUM(act_pay_amt) AS act_pay_amt,
        SUM(sum) AS sum
        FROM insurance_practice_cust_pay_detail
        WHERE pay_id = #{payId}
        AND contract_type != 99
        AND dis_com IN
        <foreach item="item" collection="disComList" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY pay_com, dis_com
    </select>


    <select id="getAllDisComByPayId" resultType="string">
        select distinct dis_com from insurance_practice_cust_pay_detail where pay_id = #{payId}
    </select>


    <select id="getInsurancePracticeCustPayDetailListByPayMonthAndDisCom" resultType="com.reon.hr.api.bill.vo.InsurancePracticeCustPayDetailVo">
        select pay_com,
               cust_id,
               social_amt,
               contract_type,
               provident_amt,
               service_amt,
               total_amt,
               balance_amt,
               act_pay_amt
        from insurance_practice_cust_pay_detail
        where pay_id in (select id from payment_apply where pay_month = #{payMonth} and pay_type = 1 and app_status = 2)
          and dis_com = #{disCom} and pay_com !=#{disCom}
    </select>

</mapper>