package com.reon.hr.api.bill.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class DisposableSaveParamVo implements Serializable {

    /**
     * 一次性收费Id
     */
    private Long disposableId;

    /**
     * 总金额
     */
    private BigDecimal amt;

    /**
     *  人数
     */
    private Integer peopleNum;
}
