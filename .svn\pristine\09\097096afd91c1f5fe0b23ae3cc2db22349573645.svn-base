package com.reon.hr.api.customer.enums.employee;

import com.reon.hr.api.customer.enums.BaseEnum;

/**
 * <AUTHOR> on 2021/10/28.
 */
public enum EmployeeEntryDimissionReduceReason implements BaseEnum {

    NORMAL_ATTRITION(1, "正常减员"),
    SWITCH_TO_COMPETITOR(2, "转至竞争对手"),
    SERVICE_CAUSES(3, "服务原因"),
    PREPARE_CANCELLATIONS(4, "准备撤单+已报撤单正在减员中"),
    CUSTOMER_REASON(5, "客户原因"),
    OTHER_REASONS(6, "其他原因"),
    CHANGE_OF_CONTRACT_NAME(7, "变更合同名称"),
    CHANGE_SERVICE_ITEMS(8, "变更服务项目");

    private int code;
    private String name;

    EmployeeEntryDimissionReduceReason(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public static Integer getIndex(String name) {
        for (EmployeeEntryDimissionReduceReason c : EmployeeEntryDimissionReduceReason.values ()) {
            if (c.getName().equals(name)) {
                return c.code;
            }
        }
        return null;
    }
}
