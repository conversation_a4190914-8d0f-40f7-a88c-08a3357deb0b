package com.reon.hr.api.customer.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.CustomerVo;
import com.reon.hr.api.customer.vo.salary.ExcessCalculationAmountDetailVo;
import com.reon.hr.api.customer.vo.supplier.SupplierVo;
import com.reon.hr.api.customer.vo.withholdingAgent.ExportWithholdingAgentVo;
import com.reon.hr.api.customer.vo.withholdingAgent.WithholdingAgentVo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 扣缴义务人信息
 * @datetime 2022年 08月 29日 10:03
 * @version: 1.0
 */
public interface IWithholdingAgentService {

    /**
     * 分页查询
     *
     * @param withholdingAgentVo
     * @param page
     * @param limit
     * @return
     */
    Page<WithholdingAgentVo> getWithholdingAgentPage(WithholdingAgentVo withholdingAgentVo, Integer page, Integer limit);

    /**
     * 修改
     *
     * @param withholdingAgentVo
     * @return
     */
    int updateWithholdingAgentById(WithholdingAgentVo withholdingAgentVo);

    /**
     * 新增
     *
     * @param withholdingAgentVo
     * @return
     */
    int addWithholdingAgent(WithholdingAgentVo withholdingAgentVo);

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    WithholdingAgentVo getWithholdingAgentById(Long id);


    List<ExportWithholdingAgentVo> exportWithholdingAgent(WithholdingAgentVo withholdingAgentVo);
    List<WithholdingAgentVo> getWithholdingAgent(WithholdingAgentVo withholdingAgentVo);

    /**
     * 获取所有社保供应商
     *
     * @return
     */
    List<SupplierVo> getAllSupplierByType();

    /**
     * 获取所有的客户信息
     *
     * @return
     */
    List<CustomerVo> findAllCustomers();

    List<CustomerVo> findAllCustomerById(Long custId);

    /**
     * 插入时先查询此数据是否存在
     *
     * @param withholdingAgentVo
     * @return
     */
    int getCount(WithholdingAgentVo withholdingAgentVo);
    List<WithholdingAgentVo> getCountByNameAndPayPlace(WithholdingAgentVo withholdingAgentVo);


    String findCustNameByCustNo(String custNo);

    String findCustNameByrefCustId(String refCustId);

    List<WithholdingAgentVo> getWithholdingAgentNoSelect(Integer withholdingAgentType);

    List<WithholdingAgentVo> getWithholdingAgentNoAndPayPlace();

	List<WithholdingAgentVo> getWithholdingAgentListByNoSet(Set<String> withholdingAgentNoSet);

    List<WithholdingAgentVo> getWithholdingDataByCityCodeAndType(String cityCode,Integer type);

    WithholdingAgentVo getWithholdingNameByOrgCode(String orgCode,String cityCode,String withholdingAgentNo);

    List<ExcessCalculationAmountDetailVo> getExcessCalculationAmountDetail(String withholdingAgentNo);
}
