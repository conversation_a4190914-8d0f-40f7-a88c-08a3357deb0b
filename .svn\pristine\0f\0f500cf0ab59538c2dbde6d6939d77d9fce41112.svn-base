/****************************************
 * Copyright (c) 2019 dingshan.
 * All rights reserved.
 * Created on 2021/5/10 0010
 *
 * Contributors:
 * 	   ZouSheng - initial implementation
 ****************************************/
package com.reon.hr.sp.customer.dubbo.service.rpc.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.dubbo.service.rpc.customer.IEmpInsurAcctWrapperService;
import com.reon.hr.api.customer.vo.CustomerPageVo;
import com.reon.hr.api.customer.vo.doIt.EmpInsurAcctVO;
import com.reon.hr.sp.customer.service.employee.IEmpInsurAcctService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IEmpInsurAcctWrapperServiceImpl
 *
 * @date 2021/5/10 0010 9:27
 */
@Service("iEmpInsurAcctWrapperService")
public class IEmpInsurAcctWrapperServiceImpl implements IEmpInsurAcctWrapperService {
    @Resource
    private IEmpInsurAcctService iEmpInsurAcctService;
    @Override
    public Page<EmpInsurAcctVO>   getEmpInsurAcct(EmpInsurAcctVO empInsurAcctVO) {
        return iEmpInsurAcctService.getEmpInsurAcct(empInsurAcctVO);
    }

    @Override
    public List<EmpInsurAcctVO> getEmpInsurAcctList(EmpInsurAcctVO empInsurAcctVO) {
        return iEmpInsurAcctService.getEmpInsurAcctList(empInsurAcctVO);
    }

    @Override
    public List<EmpInsurAcctVO> getEmpInsurAcctValid(EmpInsurAcctVO empInsurAcctVO) {
        return iEmpInsurAcctService.getEmpInsurAcctValid(empInsurAcctVO);
    }

    @Override
    public Boolean addEmpInsurAcct(EmpInsurAcctVO empInsurAcctVO) {
         return iEmpInsurAcctService.addEmpInsurAcct(empInsurAcctVO);
    }
    @Override
    public void addOrEditEmpInsurAcct(EmpInsurAcctVO empInsurAcctVO) {
          iEmpInsurAcctService.addOrEditEmpInsurAcct(empInsurAcctVO);
    }

    @Override
    public void setValid(String[] ids,String date) {
        iEmpInsurAcctService.setValid(ids,date);
    }

    @Override
    public Long getEmpInsurAcctId(Long empId) {
        return iEmpInsurAcctService.getEmpInsurAcctId(empId);
    }

    @Override
    public int updateAcctNoById(Long id, String acctNo,String loginName) {
        return iEmpInsurAcctService.updateAcctNoById(id,acctNo,loginName);
    }

    @Override
    public int updateEmpInsurAcctById(EmpInsurAcctVO empInsurAcctVO) {
        return iEmpInsurAcctService.updateEmpInsurAcctById(empInsurAcctVO);
    }

    @Override
    public Long findEmpInsurAcctId(Long empId, String groupCode, String acctNo) {
        return iEmpInsurAcctService.findEmpInsurAcctId(empId,groupCode,acctNo);
    }

    @Override
    public EmpInsurAcctVO selectEmpInsurAcctVOById(Long empId) {
        return iEmpInsurAcctService.selectEmpInsurAcctVOById(empId);
    }

    @Override
    public List<EmpInsurAcctVO> getEmpInsurAcctByEmployeeIdsAndPackCode(List<Long> employeeIds, List<String> packCode) {
        return iEmpInsurAcctService.getEmpInsurAcctByEmployeeIdsAndPackCode(employeeIds,packCode);
    }
}
