<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>添加</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body>
<div class="layui-inline" style="padding-top: 30px;">
    <form class="layui-form" id="searchForm">
        <input type="hidden" name="contractType" id="contractType">
        <table class="layui-table" lay-skin="nob" style="width: 75%">
            <tr>
                <td width="7%" align="right" style="font-weight:800"><i style="color: red">*</i>公积金组</td>
                <td width="8%">
                    <select class="layui-select" name="insuranceGroup" id="insuranceGroup" lay-filter="insuranceGroupFilter">
                        <option value=""></option>
                    </select>
                </td>
                <td width="7%" align="right" style="font-weight:800"><i style="color: red">*</i>福利办理方</td>
                <td width="8%">
                    <select class="layui-select" name="welfareTransaction" id="welfareTransaction" lay-filter="welfareTransactionFilter">
                        <option value=""></option>
                    </select>
                </td>
                <td width="5%" align="right" style="font-weight:800">客户</td>
                <td width="8%">
                    <input type="text" class="layui-input" name="custName" id="custName" readonly autocomplete="off"
                           class="layui-input" lay-verType="tips" placeholder="请选择" disabled="disabled"/>
                    <input type="text" name="custId" id="custId" style="display: none;"/>
                </td>
                <td width="5%" align="right" style="font-weight:800"><i style="color: red">*</i>费用类型</td>
                <td width="8%">
                    <select class="layui-select" name="feeType" id="feeType" lay-filter="welfareTransactionFilter">
                        <option value=""></option>
                        <option value="1">汇缴</option>
                        <option value="2">补缴</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td align="right" style="font-weight:800"><i style="color: red">*</i>锁定年月</td>
                <td width="8%"><input type="text" class="layui-input" name="lockMonth" id="lockMonth" lay-verify="required" autocomplete="off"></td>
            </tr>
        </table>
    </form>
</div>

<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/bill/insuracePractice/generateAccumulationFundLockPage.js?v=${publishVersion}"></script>
</body>
</html>