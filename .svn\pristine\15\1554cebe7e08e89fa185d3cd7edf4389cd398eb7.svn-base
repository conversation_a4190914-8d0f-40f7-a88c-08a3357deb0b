<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-table td {
            position: relative;
            padding: 3px 5px;
            min-height: 10px;
            line-height: 10px;
            font-size: 13px;
        }

        .layui-form-label {
            padding: 5px 10px;
        }

        .layui-input {
            height: 30px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <form class="layui-form" id="searchForm">
        <div class="layui-form-item">
            <div class="layui-inline queryTable">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="大区" style="font-weight:800">大区</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="area" lay-search id="area" lay-filter="areaFilter"
                                lay-search="">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="城市" style="font-weight:800">城市</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="cityCode" lay-search id="cityCode"
                                lay-filter="cityCodeFilter"
                                lay-search="">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="福利办理方" style="font-weight:800">福利办理方</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="orgCode" lay-search id="orgCode" lay-filter="orgCodeFilter"
                                lay-search=""
                        <%--                                    placeholder="请选择" autocomplete="off">--%>
                        <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="大户/单立户"
                           style="font-weight:800">大户/单立户</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="singleFlag" id="singleFlag" lay-filter="singleFlagFilter"
                                DICT_TYPE="INSUR_PACK_SINGLE"
                                placeholder="请选择" autocomplete="off">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="报表年月起"><i
                            style="color: red">*</i>&nbsp;报表年月</label>
                    <div class="layui-input-inline">
                        <input type="text" name="reportMonthStart" id="reportMonthStart" lay-verify="required"
                               placeholder="请输入" class="layui-input" readonly />
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="报表年月止"><i
                            style="color: red">*</i>&nbsp;报表年月止</label>
                    <div class="layui-input-inline">
                        <input type="text" name="reportMonthEnd" id="reportMonthEnd" lay-verify="required"
                               placeholder="请输入" class="layui-input" readonly/>
                    </div>
                </div>





            </div>
            <div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="产品类型"
                    style="font-weight:800">产品类型</label>
                    <div class="layui-input-inline">
                        <select type="text" id="productCode" name="productCode" class="layui-input "  DICT_TYPE="PRODUCT_IND_TYPE">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="锁定时间起"   style="font-weight:800">锁定时间起</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lockMonthStart" id="lockMonthStart" lay-verify="required"
                               placeholder="请输入" class="layui-input" readonly/>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="锁定时间止"  style="font-weight:800">锁定时间止</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lockMonthEnd" id="lockMonthEnd" lay-verify="required"
                               placeholder="请输入" class="layui-input" readonly/>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-list" id="reset" type="reset">重置</button>
                    <%--                    <button class="layui-btn layuiadmin-btn-list" id="count">计算垫付</button>--%>
                    <button class="layui-btn layuiadmin-btn-list" type="button" id="export" lay-filter="export"
                            lay-submit>导出明细
                    </button>
                </div>
            </div>
        </div>

    </form>
</blockquote>

<%--<table class="layui-hide" id="billGrid" lay-filter="billFilter"></table>--%>

<script type="text/jsp" id="toolbarDemo">
<%--    <button class="layui-btn layui-btn-sm" lay-event="billPrint">帐单打印</button>--%>


</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common/selectModel.js?v=${publishVersion}"></script>
<script type="text/javascript"
        src="${ctx}/js/modules/report/insurancePractice/practiceCollectReportPage.js?v=${publishVersion}"></script>
</body>
</html>
