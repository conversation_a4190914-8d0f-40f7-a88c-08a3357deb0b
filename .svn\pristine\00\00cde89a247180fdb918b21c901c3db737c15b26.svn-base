<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.base.dao.sys.CompanyBankMapper">
  <resultMap id="BaseResultMap" type="com.reon.hr.sp.base.entity.sys.CompanyBank">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="comp_no" jdbcType="VARCHAR" property="compNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_type" jdbcType="INTEGER" property="bankType" />
    <result column="pay_roll_type" jdbcType="INTEGER" property="payRollType" />
    <result column="summary_restriction_flag" jdbcType="INTEGER" property="summaryRestrictionFlag" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="opening_place" jdbcType="VARCHAR" property="openingPlace" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, comp_no, bank_name,bank_type,pay_roll_type, bank_no, account_name, creator, create_time, updater, update_time,
    del_flag,opening_place,type,cust_id,summary_restriction_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.reon.hr.api.base.vo.CompanyBankVo">
    select 
    <include refid="Base_Column_List" />
    from company_bank
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectCompBank" resultType="com.reon.hr.api.base.vo.CompanyBankVo">
    select
    <include refid="Base_Column_List" />
    from company_bank
    where del_flag='N'
    <if test="bankName!=null and bankName!='' ">
     and bank_name like  concat('%',#{bankName},'%')
    </if>
    <if test="compNo!=null and compNo!=''">
      and comp_no=#{compNo}
    </if>
    <if test="type!=null and type!=''">
      and type=#{type}
    </if>
    order by update_time desc
  </select>
  <select id="selectByCompNo" resultType="com.reon.hr.sp.base.entity.sys.CompanyBank">
    select
    <include refid="Base_Column_List" />
    from company_bank
    where del_flag='N'
     and  comp_no=#{compNo} and type = #{type}
  </select>
  <select id="getAllComp" resultType="com.reon.hr.api.base.vo.CompanyBankVo">
    select
    id, comp_no, bank_name,bank_type,pay_roll_type, bank_no, account_name, creator, create_time, updater, update_time,
    del_flag,opening_place,type,cust_id,summary_restriction_flag
    from company_bank
    where del_flag='N'
    <if test="compNo!=null and compNo!=''">
      and  comp_no=#{compNo}
    </if>
    <if test="type!=null and type!=''">
      and  type=#{type}
    </if>
  </select>

  <select id="getCompanyBanks" resultType="com.reon.hr.api.base.vo.CompanyBankVo">
    select
    <include refid="Base_Column_List" />
    from company_bank
    where del_flag='N'
    <if test="args.bankType!=null and args.bankType!=''">
      and  bank_type=#{args.bankType}
    </if>
    <if test="args.compNo!=null and args.compNo!=''">
      and  comp_no=#{args.compNo}
    </if>
    <if test="args.type!=null and args.type!=''">
      and  type=#{args.type}
    </if>
    <if test="args.custId!=null and args.custId!=''">
      and  cust_id=#{args.custId}
    </if>
    <if test="args.bankNo!=null and args.bankNo!=''">
      and  bank_no=#{args.bankNo}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from company_bank
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.reon.hr.sp.base.entity.sys.CompanyBank">
    insert into company_bank (id, comp_no, bank_name, 
      bank_no, account_name, creator, 
      create_time, updater, update_time, 
      del_flag,bank_type,pay_roll_type,summary_restriction_flag,opening_place)
    values (#{id,jdbcType=BIGINT}, #{compNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankNo,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=CHAR},#{bankType,jdbcType=INTEGER},#{payRollType},#{summaryRestrictionFlag},#{openingPlace})
  </insert>
  <insert id="insertSelective" parameterType="com.reon.hr.sp.base.entity.sys.CompanyBank">
    insert into company_bank
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="compNo != null">
        comp_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankNo != null">
        bank_no,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="bankType != null">
        bank_type,
      </if>
      <if test="payRollType != null">
        pay_roll_type,
      </if>
      <if test="summaryRestrictionFlag != null">
        summary_restriction_flag,
      </if>
      <if test="openingPlace != null">
        opening_place,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="compNo != null">
        #{compNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="bankType != null">
        #{bankType,jdbcType=INTEGER},
      </if>
      <if test="payRollType != null">
        #{payRollType},
      </if>
      <if test="summaryRestrictionFlag != null">
        #{summaryRestrictionFlag},
      </if>
      <if test="openingPlace != null">
        #{openingPlace},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reon.hr.sp.base.entity.sys.CompanyBank">
    update company_bank
    <set>
      <if test="compNo != null">
        comp_no = #{compNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        bank_no = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null">
        bank_type = #{bankType,jdbcType=INTEGER},
      </if>
      <if test="payRollType != null">
        pay_roll_type = #{payRollType},
      </if>
      <if test="summaryRestrictionFlag != null">
        summary_restriction_flag = #{summaryRestrictionFlag},
      </if>
      <if test="openingPlace != null">
        opening_place = #{openingPlace},
      </if>
        update_time = now()

    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reon.hr.sp.base.entity.sys.CompanyBank">
    update company_bank
    set comp_no = #{compNo,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_no = #{bankNo,jdbcType=VARCHAR},
      account_name = #{accountName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR},
      bank_type = #{bankType,jdbcType=INTEGER},
      pay_roll_type = #{payRollType},
      summary_restriction_flag = #{summaryRestrictionFlag},
      opening_place = #{openingPlace}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getSpecialCompanyBankByCustId" resultType="com.reon.hr.api.base.vo.CompanyBankVo">
    select * from company_bank where cust_id =#{custId} and type=2
  </select>

  <insert id="addSpecialCompanyBank" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.reon.hr.api.base.vo.CompanyBankVo">
    INSERT INTO company_bank
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="null != compNo and '' != compNo">
        comp_no,
      </if>
      <if test="null != bankName and '' != bankName">
        bank_name,
      </if>
      <if test="null != bankType ">
        bank_type,
      </if>
      <if test="null != payRollType ">
        pay_roll_type,
      </if>
      <if test="null != summaryRestrictionFlag ">
        summary_restriction_flag,
      </if>
      <if test="null != bankNo and '' != bankNo">
        bank_no,
      </if>
      <if test="null != accountName and '' != accountName">
        account_name,
      </if>
      <if test="null != openingPlace and '' != openingPlace">
        opening_place,
      </if>
      <if test="null != type ">
        type,
      </if>
      <if test="null != custId ">
        cust_id,
      </if>
      <if test="null != creator and '' != creator">
        creator,
      </if>
      <if test="null != createTime ">
        create_time
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="null != compNo and '' != compNo">
        #{compNo},
      </if>
      <if test="null != bankName and '' != bankName">
        #{bankName},
      </if>
      <if test="null != bankType ">
        #{bankType},
      </if>
      <if test="null != payRollType ">
        #{payRollType},
      </if>
      <if test="null != summaryRestrictionFlag ">
        #{summaryRestrictionFlag},
      </if>
      <if test="null != bankNo and '' != bankNo">
        #{bankNo},
      </if>
      <if test="null != accountName and '' != accountName">
        #{accountName},
      </if>
      <if test="null != openingPlace and '' != openingPlace">
        #{openingPlace},
      </if>
      <if test="null != type ">
        #{type},
      </if>
      <if test="null != custId ">
        #{custId},
      </if>
      <if test="null != creator and '' != creator">
        #{creator},
      </if>
      <if test="null != createTime ">
        #{createTime}
      </if>


    </trim>
  </insert>

  <update id="updateSpecialCompanyBankById" parameterType="com.reon.hr.api.base.vo.CompanyBankVo">
    UPDATE company_bank
    <set>
      <if test="null != bankName and '' != bankName">bank_name = #{bankName},</if>
      <if test="null != bankType ">bank_type = #{bankType},</if>
      <if test="null != payRollType ">pay_roll_type = #{payRollType},</if>
      <if test="null != summaryRestrictionFlag ">summary_restriction_flag = #{summaryRestrictionFlag},</if>
      <if test="null != bankNo and '' != bankNo">bank_no = #{bankNo},</if>
      <if test="null != accountName and '' != accountName">account_name = #{accountName},</if>
      <if test="null != openingPlace and '' != openingPlace">opening_place = #{openingPlace},</if>
      <if test="null != updater and '' != updater">updater = #{updater},</if>
      update_time = now()
    </set>
    WHERE id = #{id}
  </update>
</mapper>