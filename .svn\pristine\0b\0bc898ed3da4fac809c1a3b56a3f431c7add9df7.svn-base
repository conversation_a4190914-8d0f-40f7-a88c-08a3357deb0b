package com.reon.hr.sp.customer.service.impl.cus;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.reon.hr.api.customer.enums.BooleanTypeEnum;
import com.reon.hr.api.customer.enums.SupplierQuotationStatusEnum;
import com.reon.hr.api.customer.enums.supplier.ServiceFeeType;
import com.reon.hr.api.customer.vo.supplier.SupplierAreaVo;
import com.reon.hr.api.customer.vo.supplier.SupplierContractNewVo;
import com.reon.hr.api.customer.vo.supplier.SupplierQuotationRemarkVo;
import com.reon.hr.api.customer.vo.supplier.SupplierQuotationVo;
import com.reon.hr.api.customer.vo.supplierBillTempletAndPractice.SupplierBillTempletVo;
import com.reon.hr.common.utils.DateUtil;
import com.reon.hr.sp.customer.dao.cus.SupplierQuotationMapper;
import com.reon.hr.sp.customer.entity.supContractArea.SupplierQuotation;
import com.reon.hr.sp.customer.service.cus.SupplierQuotationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SupplierQuotationServiceImpl implements SupplierQuotationService {
    @Autowired
    private SupplierQuotationMapper supplierQuotationMapper;


    @Override
    public List<SupplierQuotationVo> querySupQuotationVosBySupId(Long supplierId,Integer serviceFeeType) {
        EntityWrapper<SupplierQuotation> wrapper = new EntityWrapper<>();
        wrapper.eq("supplier_id",supplierId);
        if(Objects.nonNull(serviceFeeType)){
            wrapper.eq("service_fee_type",serviceFeeType);
        }
        wrapper.eq("status", SupplierQuotationStatusEnum.ENABLE.getCode());
        List<SupplierQuotation> quotationList = supplierQuotationMapper.selectList(wrapper);
        return entityToVo(quotationList);
    }

    @Override
    public List<SupplierQuotationVo> querySupQuotationVosBySupIds(List<Long> supplierId,Integer serviceFeeType) {
        EntityWrapper<SupplierQuotation> wrapper = new EntityWrapper<>();
        wrapper.in("supplier_id",supplierId);
        if(Objects.nonNull(serviceFeeType)){
            wrapper.eq("service_fee_type",serviceFeeType);
        }
        List<SupplierQuotation> quotationList = supplierQuotationMapper.selectList(wrapper);
        return entityToVo(quotationList);
    }

    @Override
    public void deleteSupplierQuotation(Long contractId) {
        supplierQuotationMapper.deleteSupplierQuotation(contractId);
    }

    @Override
    public List<SupplierQuotationVo> getSupplierQuotationByOrderNo(List<String> orderNos, List<Integer> type,String delFlag) {
        return supplierQuotationMapper.getSupplierQuotationByOrderNo(orderNos,type,delFlag);
    }

    @Override
    public List<SupplierQuotationVo> getSupplierQuotationVoByQuotationNos(List<String> quotationNos) {
        return supplierQuotationMapper.getSupplierQuotationVoByQuotationNos(quotationNos);
    }

    @Override
    public Page<SupplierQuotationRemarkVo> getSupplierQuotationRemarkPage(Integer page, Integer limit, SupplierQuotationRemarkVo supplierQuotationRemarkVo) {
        Page<SupplierQuotationRemarkVo> supplierQuotationRemarkPage = new Page<>(page, limit);
        if (StringUtils.isNotBlank(supplierQuotationRemarkVo.getCityCode())){
            supplierQuotationRemarkVo.setCityCode(supplierQuotationRemarkVo.getProvinceCode()+supplierQuotationRemarkVo.getCityCode());
        }
        List<SupplierQuotationRemarkVo> supplierQuotationRemarkPage1 = supplierQuotationMapper.getSupplierQuotationRemarkPage(supplierQuotationRemarkPage, supplierQuotationRemarkVo);
        supplierQuotationRemarkPage1.forEach(item ->{
            if (supplierQuotationRemarkVo.getPosType()==null){
            item.setSupplierName("********************");
            }
            if (item.getServiceFeeType().equals(ServiceFeeType.SOCIAL_SECURITY_SERVICE_FEE.getCode())){
                item.setSocialPrice(item.getServiceFee());
            }
            if (item.getServiceFeeType().equals(ServiceFeeType.PAYROLL_SERVICE_CHARGE.getCode())){
                item.setSalaryPrice(item.getServiceFee());
            }
        });
        return supplierQuotationRemarkPage.setRecords(supplierQuotationRemarkPage1);
    }


    @Override
    public int updateSupplierQuotationRemark(SupplierContractNewVo supplierQuotationRemarkVo) {
        List<SupplierQuotationVo> supplierQuotationByContractNo = supplierQuotationMapper.getSupplierQuotationByContractNo(supplierQuotationRemarkVo.getContractNo());
        List<String> quotationList = supplierQuotationByContractNo.stream().map(SupplierQuotationVo::getSupQuotationNo).collect(Collectors.toList());
        supplierQuotationMapper.updateSupplierQuotationRemark(quotationList,supplierQuotationRemarkVo.getQuoRemark());
        return 1;
    }

    @Override
    public List<SupplierQuotationVo> getQuotationBySupplierId(Long supplierId) {
        return supplierQuotationMapper.getQuotationBySupplierId(supplierId);
    }

    private static List<SupplierQuotationVo> entityToVo(List<SupplierQuotation> quotationList) {
        return quotationList.stream().map(item -> {
            SupplierQuotationVo quotationVo = new SupplierQuotationVo();
            BeanUtils.copyProperties(item, quotationVo);
            return quotationVo;
        }).collect(Collectors.toList());
    }

    @Override
    public SupplierQuotationVo selectOneByQuoteNo(String quoteNo) {
        SupplierQuotation args = new SupplierQuotation();
        args.setSupQuotationNo(quoteNo);
        SupplierQuotation quotation = supplierQuotationMapper.selectOne(args);
        SupplierQuotationVo quotationVo = new SupplierQuotationVo();
        BeanUtils.copyProperties(quotation,quotationVo);
        return quotationVo;
    }

    @Override
    public List<SupplierQuotationVo> queryChargesByQuoteNos(List<String> quoteNos) {
        if(CollectionUtils.isEmpty(quoteNos)){
            return Lists.newArrayList();
        }
        EntityWrapper<SupplierQuotation> entityWrapper = new EntityWrapper<>();
        entityWrapper.in("sup_quotation_no",quoteNos);
        List<SupplierQuotation> quotationList = supplierQuotationMapper.selectList(entityWrapper);
        return entityToVo(quotationList);
    }

    @Override
    public Map<String, SupplierQuotationVo> getSalaryFeeBySupplierVoList(List<SupplierAreaVo> supplierAreaVoList) {
        List<SupplierQuotationVo> supplierQuotationVoList=new ArrayList<>();
        Map<String, SupplierQuotationVo> supplierQuotationVoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(supplierAreaVoList)){
            Date nowDate = DateUtil.getBeginOfOneDay(new Date());
            long nowTime = nowDate.getTime();
            List<SupplierQuotationVo> salaryFeeBySupplierVoList = supplierQuotationMapper.getSalaryFeeBySupplierVoList(supplierAreaVoList);
            for (SupplierQuotationVo supplierQuotationVo:salaryFeeBySupplierVoList) {
                String[] cityCodes = supplierQuotationVo.getCityCode().split(",");
                for (String cityCode:cityCodes) {
                    SupplierQuotationVo salaryFeeVo = new SupplierQuotationVo();
                    BeanUtils.copyProperties(supplierQuotationVo,salaryFeeVo);
                    salaryFeeVo.setCityCode(cityCode);
                    /*if(salaryFeeVo.getStartTime().getTime()>nowTime||salaryFeeVo.getEndTime().getTime()<nowTime){
                        salaryFeeVo.setErrorRemark(salaryFeeVo.getSupplierContractNo()+":不在有效时间内;");
                    }else {
                        if(!ServiceFeeType.SALARY_SERVICE_FEE_TYPE_LIST.contains(salaryFeeVo.getServiceFeeType())){
                            salaryFeeVo.setErrorRemark(salaryFeeVo.getSupQuotationNo()+":服务费类型不是供应商工资服务费;");
                        }
                    }*/
                    if(!ServiceFeeType.SALARY_SERVICE_FEE_TYPE_LIST.contains(salaryFeeVo.getServiceFeeType())){
                        salaryFeeVo.setErrorRemark(salaryFeeVo.getSupQuotationNo()+":服务费类型不是供应商工资服务费;");
                    }
                    supplierQuotationVoList.add(salaryFeeVo);
                }
            }
        }
        Map<String, List<SupplierQuotationVo>> supplierQuotationVoListMap = supplierQuotationVoList.stream().collect(Collectors.groupingBy(s -> s.getSupplierId() + "-" + s.getCityCode()));
        for (String key:supplierQuotationVoListMap.keySet()) {
            List<SupplierQuotationVo> supplierQuotationVos = supplierQuotationVoListMap.get(key);
            List<SupplierQuotationVo> voList = supplierQuotationVos.stream().filter(s -> StringUtils.isBlank(s.getErrorRemark())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(voList)){
                SupplierQuotationVo supplierQuotationVo = voList.stream().filter(s -> s.getDefaultStatus() == BooleanTypeEnum.YES.getCode()).min(Comparator.comparing(SupplierQuotationVo::getServiceFee)).orElse(null);
                if(supplierQuotationVo==null){
                    supplierQuotationVo=voList.stream().min(Comparator.comparing(SupplierQuotationVo::getServiceFee)).orElse(null);
                }
                supplierQuotationVoMap.put(key,supplierQuotationVo);
            }else {
                voList = supplierQuotationVos.stream().filter(s -> StringUtils.isNotBlank(s.getErrorRemark())).collect(Collectors.toList());
                String errorRemarkToTal="";
                for (SupplierQuotationVo supplierQuotationVo:voList) {
                    if(!errorRemarkToTal.contains(supplierQuotationVo.getErrorRemark())){
                        errorRemarkToTal+=supplierQuotationVo.getErrorRemark();
                    }
                }
                SupplierQuotationVo supplierQuotationVo =new SupplierQuotationVo();
                supplierQuotationVo.setErrorRemark(errorRemarkToTal);
                supplierQuotationVoMap.put(key,supplierQuotationVo);
            }
        }
        return supplierQuotationVoMap;
    }

}
