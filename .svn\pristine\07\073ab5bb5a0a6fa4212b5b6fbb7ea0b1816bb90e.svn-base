package com.reon.hr.api.report.dubbo.service.rpc;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.EmployeeChangeReportExportVo;
import com.reon.hr.api.customer.vo.FinancialRequireVo;
import com.reon.hr.api.customer.vo.employee.EmployeeOrderVo;
import com.reon.hr.api.customer.dto.admin.OrgPositionDto;
import com.reon.hr.api.report.vo.*;

import java.util.List;
import java.util.Map;

public interface IEmployeeReportWrapperService {

    /**
     * 减员报表
     *
     * @param applyTimeS
     * @param applyTimeE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @return
     */
    List<EmployeeDimissionReportVo> getDimissionEmployeesReport(String applyTimeS, String applyTimeE, String custName, Integer distPlace, Integer cityCode, String receiving,String commissioner,Integer accountFlag, List<OrgPositionDto> userOrgPositionDtoList);

    /**
     * 查询入职报表
     *
     * @param applyEntryDateS
     * @param applyEntryDateE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @return
     */
    List<EmployeeEntryReportVo> getEntryEmployeesReport(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving);

    /**
     * 查询入职报表
     *
     * @param applyEntryDateS
     * @param applyEntryDateE
     * @param custName
     * @param distPlace
     * @param cityCode
     * @param receiving
     * @return
     */
    List<EmployeeEntryReportVo> getEntryEmployeesReport1(String applyEntryDateS, String applyEntryDateE, String custName, Integer distPlace, Integer cityCode, String receiving,String commissioner,Integer accountFlag,List<OrgPositionDto> userOrgPositionDtoList);
    /**
     * 查询成本核算报表*/
    Page<CostReport> getCostReport(CostReport vo);
    /**获取合同账单信息报表*/
    Page<BillContractReport> getBillContractReport(BillContractReport vo);

    Page<InsuranceBillComparisonDTO> getInsuranceBillComparisonListPage(Integer page, Integer limit, Map<String, Object> stringObjectMap);

    Object generationOfContrastData(String createTime, String loginName);

    List<InsuranceBillComparisonReportVo> getInsuranceBillComparisonListPageFromReport(InsuranceBillComparisonReportVo vo);

    List<String> getReportDate();

    /**
     * 不考虑转移过后的情况
     * @return
     */
    List<EmployeeOrderVo> trimesterNoEntryBillIgnoreTransfer(String orderNo);

    List<EmployeeOrderVo> trimesterNoEntryBill();

    List<ServiceNumPeopleReportVo> getServiceNum(Integer yearMonthS, Integer yearMonthE, Map<String, Object> conditonMap);

    List<FinancialRequireVo> importFinancialRequireEmployee( String yearMonth);

    Boolean importFinancialRequireEmployeeData(List<FinancialRequireVo> dataList);

    List<EmployeeChangeReportExportVo> getEmployeeChangeReportData(String month);
}
