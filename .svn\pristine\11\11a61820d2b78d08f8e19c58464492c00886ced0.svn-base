package com.reon.hr.sp.customer.dao.cus;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.SalaryItemLogVo;
import com.reon.hr.sp.customer.entity.cus.SalaryItemLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalaryItemLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SalaryItemLog record);

    int insertSelective(SalaryItemLog record);

    int insertList(@Param("list") List<SalaryItemLog> salaryItemLogList);

    SalaryItemLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SalaryItemLog record);

    int updateByPrimaryKey(SalaryItemLog record);

	List<SalaryItemLogVo> getSalaryItemLog(@Param("page") Page page, @Param("sil") SalaryItemLog salaryItemLog);

    List<SalaryItemLog> selectByItemIdList(@Param("itemIdList") List<Long> itemIdList);
}