var ctx = ML.contextPath;
layui.config({
    base: ctx + "/js/"
}).use(['jquery', 'form', 'layer', 'tableSelect'], function () {
    var $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        tableSelect = layui.tableSelect,
        layer = parent.layer === undefined ? layui.layer : parent.layer;


    var url = ML.contextPath + '/customer/contract/getCustomerFromAuthContract';
    if (window.location.href.indexOf("/customer/invoice/gotoInvoiceAddView") == -1) {
        url = ML.contextPath + '/customer/invoice/searchCustByOrgCode';
    }


    // 搜索条件  客户下拉列表框
    var appd = '<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="name" placeholder="客户名称/编号" autocomplete="off" class="layui-input">';
    // 客户下拉数据表格
    tableSelect.render({
        elem: '#custName',
        checkedKey: 'id',
        appd: appd,
        table: {
            url: url,
            cols: [[
                { type: 'radio' }
                , { field: 'custId', title: '客户ID', align: 'center' }
                , { field: 'custNo', title: '客户编号', align: 'center' }
                , { field: 'custName', title: '客户名称', align: 'center' }
            ]]
        },
        done: function (elem, data) {
            var NEWJSON = [];
            var custNo = '';
            var id = '';
            layui.each(data.data, function (index, item) {
                NEWJSON.push(item.custName)
                custNo = item.custNo;
                id = item.custId;
            });
            // 回填值
            elem.val(NEWJSON.join(","));

            // 将custId赋值到隐藏域中
            $("#custId").val(id)

            // 判断id为invoiceCustId的对象是否存在，存在则赋值
            if (document.getElementById("invoiceCustId")) {
                // 将编号回填到下面客户编号中
                $("#invoiceCustId").val(custNo)
            }

            // 清空开票信息维护数据
            $("#customerInvoiceNo").find("option").remove();

            // 清空客户帐套数据
            $("#templetId").find("option").remove();

            // 获取开票信息维护数据并渲染下拉列表框
            getPayCust(id);
        }
    });


    // 监听客户付款方
    form.on('select(customerInvoiceNoFilter)', function (data) {
        // 清空客户帐套数据
        $("#templetId").find("option").remove();

        // 获取客户付款方数据并渲染下拉列表框
        getTemplet(data.value);
    });

    // 获取客户付款方数据并渲染下拉列表框
    function getPayCust(id) {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/invoice/getListByCustId?custId=" + id,
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    var selDom = $("#customerInvoiceNo");//根据id获取select的jquery对象
                    selDom.append("<option value=''></option>");
                    // 将数据回填到付款账户下拉列表框中
                    for (var i = 0; i < data.data.length; i++) {
                        selDom.append("<option value=" + data.data[i].id + ">" + data.data[i].title + "</option>");
                    }

                    // 重新渲染表格
                    form.render('select');
                } else {
                    return layer.msg(data.msg);
                }
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

    // 获取客户帐套数据并渲染下拉列表框
    function getTemplet(id) {
        $.ajax({
            type: "GET",
            url: ML.contextPath + "/customer/billTemplet/searchByPaymentId?paymentId=" + id,
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    var selDom = $("#templetId");//根据id获取select的jquery对象
                    selDom.append("<option value=''></option>");
                    // 将数据回填到付款账户下拉列表框中
                    for (var i = 0; i < data.data.length; i++) {
                        selDom.append("<option value=" + data.data[i].id + ">" + data.data[i].templetName + "</option>");
                    }

                    // 重新渲染表格
                    form.render('select');
                } else {
                    return layer.msg(data.msg);
                }
            },
            error: function (data) {
                layer.msg(data);
                console.log("error")
            }
        });
    }

})