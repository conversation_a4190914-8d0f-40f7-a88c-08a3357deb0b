package com.reon.ehr.sp.sys.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户和客户关联 sys_user_customer
 */
public class SysUserCustomer
{
    /** 用户ID */
    private Long userId;
    
    /** 客户ID */
    private Long custId;

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getCustId()
    {
        return custId;
    }

    public void setCustId(Long custId)
    {
        this.custId = custId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("custId", getCustId())
            .toString();
    }
}
