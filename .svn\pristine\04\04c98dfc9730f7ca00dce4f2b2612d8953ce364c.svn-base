package com.reon.hr.api.customer.dto.customer.salary;

import com.reon.hr.api.customer.anno.Excel;
import lombok.Data;

import java.io.Serializable;


@Data
public class SalaryResultImportDto implements Serializable {

    private String employeeId;
    /**
     * 唯一号
     */
    @Excel(name = "唯一号",width = 22)
    private String employeeNo;

    /**
     * 雇员名称
     */
    @Excel(name = "姓名",width = 10)
    private String employeeName;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码",width = 22)
    private String certNo;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型",width = 10)
    private String certType;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码",width = 22)
    private String mobile;

    /**
     * 扣缴义务人
     */
    private String withholdingAgentNo;
    @Excel(name = "扣缴义务人",width = 22)
    private String withholdingAgentName;

    /**
     * 扣缴义务人类型
     */
    @Excel(name = "扣缴义务人类型",width =22)
    private String withholdingAgentType;

    /**
     * 客户编号
     */
    @Excel(name = "客户编号",width = 22)
    private String custNo;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称",width = 26)
    private String custName;

    @Excel(name = "合同编号",width = 22)
    private String contractNo;//合同编号
    @Excel(name = "合同名字",width = 22)
    private String contractName;//合同名字
    private Integer contractType;
    @Excel(name = "合同类型",width = 22)
    private String contractTypeString;

    /**
     * 薪资发放状态
     */
    @Excel(name = "薪资发放状态",width = 26)
    private String salaryInfoStatus;

    /**
     * 报税状态
     */
    @Excel(name = "报税状态",width = 26)
    private String declareDutiableGoods;

    /**
     * 薪资类别名称
     */
    @Excel(name = "薪资类别名称",width = 26)
    private String salaryCategoryName;

    @Excel(name = "账单模板编号",width = 26)
    private String templetNo;

    @Excel(name = "账单模板名称",width = 26)
    private String templetName;

    @Excel(name = "银行卡号",width = 26)
    private String cardNo;

    /**
     * 发放名称
     */
    @Excel(name = "发放名称",width = 26)
    private String salaryPayName;

    /**
     * 工资所属月
     */
    @Excel(name = "工资所属月",width = 26)
    private String salaryMonth;

    /**
     * 工资计税月
     */
    @Excel(name = "工资计税月",width = 26)
    private String taxMonth;

    /**
     * 客户账单月
     */
    @Excel(name = "客户账单月",width = 26)
    private String billMonth;

    /**
     * 收入合计
     */
    @Excel(name = "收入合计",width = 26)
    private String S027;

    /**
     * 养老个人
     */
    @Excel(name = "养老个人",width = 26)
    private String S031;

    /**
     * 医疗个人
     */
    @Excel(name = "医疗个人",width = 26)
    private String S032;

    @Excel(name = "个人长护险",width = 26)
    private String S061;

    @Excel(name = "大额医疗补助",width = 26)
    private String S062;
    @Excel(name = "补充医疗保险",width = 26)
    private String S063;
    @Excel(name = "门诊医疗(社保)",width = 26)
    private String S064;
    @Excel(name = "大病保险(税后扣除)",width = 26)
    private String S065;
    @Excel(name = "补充医疗保险(税后扣除)",width = 26)
    private String S066;
    @Excel(name = "个人长护险(税后扣除)",width = 26)
    private String S067;

    /**
     * 失业个人
     */
    @Excel(name = "失业个人",width = 26)
    private String S033;

    /**
     * 大病个人
     */
    @Excel(name = "大病个人",width = 26)
    private String S034;

    /**
     * 公积金个人
     */
    @Excel(name = "公积金个人",width = 26)
    private String S035;

    /**
     * 其他个人
     */
    @Excel(name = "其他个人",width = 26)
    private String S036;

    /**
     * 五险一金
     */
    @Excel(name = "五险一金",width = 26)
    private String S003;

    /**
     * 本次使用专项附加扣除
     */
    @Excel(name = "本次使用专项附加扣除",width = 26)
    private String S024;

    /**
     * 累计专项附加扣除
     */
    @Excel(name = "累计专项附加扣除",width = 26)
    private String S018;

    /**
     * 应税工资
     */
    @Excel(name = "应税工资",width = 26)
    private String S005;

    /**
     * 个人所得税
     */
    @Excel(name = "个人所得税",width = 26)
    private String S006;

    @Excel(name = "个税调",width = 26)
    private String S054;
    @Excel(name = "跨年个税调",width = 26)
    private String S068;

    /**
     * 申报数据个税
     */
    @Excel(name = "申报数据个税",width = 26)
    private String accuAddTax;

    /**
     * 个人所得税-申报数据个税
     */
    @Excel(name = "个人所得税-申报数据个税",width = 26)
    private String S006_accuAddTax;

    /**
     * 实发合计
     */
    @Excel(name = "实发合计",width = 26)
    private String S007;

    /**
     * 累计扣税（不包含当月）
     */
    @Excel(name = "累计扣税（不包含当月）",width = 26)
    private String S010;
    @Excel(name = "累计基本减除费用",width = 26)
    private String S017;

    @Excel(name = "已缴税额",width = 26)
    private String accuAddedTax;//已缴税额
    @Excel(name = "累计应扣缴税额",width = 26)
    private String accuDeduTax;//累计应扣缴税额
    @Excel(name = "工资服务费",width = 26)
    private String S002;
    @Excel(name = "经济补偿金",width = 26)
    private String S043;
    @Excel(name = "经济补偿金个税",width = 26)
    private String S051;
    @Excel(name = "经济补偿金实发金额",width = 26)
    private String S052;
    @Excel(name = "残障金",width = 26)
    private String S042;
    @Excel(name = "差额",width = 26)
    private String difference;//S006+S010+S054-(accuAddedTax>accuDeduTax?accuAddedTax:accuDeduTax)
    /**
     * 税率表
     */
    private String taxListId;
    private Integer laborWagesType;
    @Excel(name = "税率表",width = 26)
    private String taxListName;
    @Excel(name = "税率类型",width = 26)
    private String salaryType;

    /**
     * 派单方
     */
    @Excel(name = "派单方",width = 26)
    private String distCom;

    /**
     * 派单方客服
     */
    @Excel(name = "派单方客服")
    private String distComMan;

    /**
     * 薪资客服
     */
    @Excel(name = "薪资客服")
    private String salaryCommissioner;

    private Integer sendStatus;

    @Excel(name = "创建人")
    private String creator;

    private Long batchId;

    @Excel(name = "工资支付日期")
    private String lastDate;

    @Excel(name = "是否退票重发")
    private String anewPayFlag;
}
