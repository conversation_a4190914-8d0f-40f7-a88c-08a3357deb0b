package com.reon.hr.sp.customer.service.qiyuesuo;

import com.baomidou.mybatisplus.plugins.Page;
import com.reon.hr.api.customer.vo.qiyuesuo.QysContractApprovalVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysContractTableVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysMappingFieldVo;
import com.reon.hr.api.customer.vo.qiyuesuo.QysSecretKeyVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年04月25日
 * @Version 1.0
 */
public interface QysContractTableService {

    /**
     * 根据契约锁合同id获取
     * @param contractId
     * @return
     */
    QysContractTableVo getQysContractTableVoByContractId(Long contractId);

    /**
     * 根据id修改关联文件表的id
     * @param id
     * @return
     */
    int updateQysContractTableFileIdById(Long id,Long fileId);

    /**
     * 条件查询
     * @param qysContractTableVo
     * @return {@link List}<{@link QysContractTableVo}>
     */
    List<QysContractTableVo> getQysContractTableList(QysContractTableVo qysContractTableVo);

    Long saveQYSContract(QysContractTableVo qysContractTableVo, String type);

    QysSecretKeyVo getQysCategoryData(Long buClasId);

    int updateStatusByContractId(Integer status,Integer effective,Long contractId);

    List<QysMappingFieldVo> getQysMappingField();


    Long getBuClassName(String buClassName);

    Integer updateContractIdById(Long contractId, Long id);

    List<QysContractTableVo> getDataByOrderNoAndBuClasIdAndStatus(String orderNo, Long buClasId, Integer contractStatus);

    Page<QysContractApprovalVo> getQysTaskList(Map<String, Object> conditonMap);

    Map<String, Object> getConditionMap();


    String getBuClasNameByContractId(Long contractId);

    int deleteContractByContractId(Long contractId);


    List<QysSecretKeyVo> getDataBuClassName(String buClassName);

    Map<String, String> getBusNameByOrderNoList(List<String> orderNoList);
}
