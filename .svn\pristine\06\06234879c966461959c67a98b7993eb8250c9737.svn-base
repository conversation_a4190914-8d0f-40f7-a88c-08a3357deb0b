package com.reon.hr.sp.thirdpart.util;

import org.apache.commons.lang3.StringUtils;

public class NamingConvertUtil {
    /**
     将下划线命名的字符串转换为驼峰命名法的字符串。
     @param underscoreName 下划线命名的字符串
     @return 驼峰命名法的字符串
     */
    public static String toCamelCase(String underscoreName) {
        if (underscoreName == null) {
            return null;
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < underscoreName.length(); i++) {
            char ch = underscoreName.charAt(i);

            if (ch == '_') {
                nextUpperCase = true;
            } else if (nextUpperCase) {
                result.append(Character.toUpperCase(ch));
                nextUpperCase = false;
            } else {
                result.append(Character.toLowerCase(ch));
            }
        }

        return StringUtils.trim(result.toString());
    }

}
