<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../common/taglibs.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>合同流程审批</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <div class="layui-inline queryTable">
        <form class="layui-form" id="searchForm" action="" method="post">


            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="数据类型" style="width: 150px">审批数据类型:</label>
                    <div class="layui-input-inline">
                        <select class="layui-select" name="type" id="type" DICT_TYPE="NOUSE_TASK_TYPE" lay-search lay-filter="typeFilter">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="合同编号" style="width: 150px">合同编号:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="contractName" maxlength="20" name="contractNo" placeholder="请输入" class="layui-input" autocomplete="off" disabled>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="报价单编号" style="width: 150px">报价单编号:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="quotationName" maxlength="20" name="quotationNo" placeholder="请输入" class="layui-input" autocomplete="off" disabled>
                    </div>
                </div>
                <button class="layui-btn" lay-submit="" id="btnQuery" lay-filter="btnQueryFilter" type="button">查询</button>
                <button class="layui-btn" id="reset" type="reset" lay-filter="resetFilter" lay-event="resetEvent">重置</button>
            </div>


        </form>
    </div>


</blockquote>
<script type="text/html" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="delete"  authURI="/workflow/deleteContractTaskByPid">删除</button>
    </div>
</script>
<table class="layui-hide" id="taskGrid" lay-filter="taskFilter"></table>


<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/workflow/gotoDeleteNoUseTaskPage.js?v=${publishVersion}"></script>
</body>
</html>
