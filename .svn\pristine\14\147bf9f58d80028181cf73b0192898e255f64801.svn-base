package com.reon.hr.api.bill.vo.supplierPractice;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SupplierDisposableItemVo implements Serializable {
    private Long id;

    private Long billId;
    private Long supplierId;
    private String supplierName;
    private Long templetId;
    private String templetName;
    private Integer billMonth;

    private Integer peopleNum;
    private Integer status;

    private Integer prodType;

    private Integer prodKind;

    private BigDecimal amount;

    private BigDecimal taxFreeAmt;

    private BigDecimal tax;

    private BigDecimal disposableTaxRatio;  // 一次性中的税率

    private String remark;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
    /**
     * 一次性支持人员
     */
    private String disSupMan;
    private Integer lockStatus;
}
