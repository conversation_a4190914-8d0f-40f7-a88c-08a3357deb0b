/****************************************
 * Copyright (c) 2017 LYF.
 * All rights reserved.
 * Created on 2017年8月6日
 * 
 * Contributors:
 * 	   <PERSON> - initial implementation
 ****************************************/
package com.reon.hr.core.filter;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import com.reon.hr.api.dubbo.service.rpc.sys.IUserOpLogWrapperService;
import com.reon.hr.api.vo.sys.CommonUserVo;
import com.reon.hr.core.common.constant.Constants;
import com.reon.hr.core.utils.IpUtil;

/**
 * @title MlAuthFilter
 *
 * <AUTHOR> Luo
 * @version 1.0
 * @created 2017年8月6日
 */
public class MlAuthFilter implements Filter {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	private static final String JSESSIONID=";jsessionid=";
	private static final int HTTP_STATUS_SESSION_EXPIRE=801;//会话过期
	private static final int HTTP_STATUS_NO_AUTH=802; //权限不足
	
	public  String LOGIN_URL; //登录地址
	public  String DENIED_URL;//权限不足的页面提示
	
	private Set<String> ignoreL1ResourceSet=new HashSet<>();//父层的表达式，如文件夹
	private Set<String> ignoreL2ResourceSet=new HashSet<>();//具体的过滤资源，如jpg、controller
	
	private Set<String> commonL1ResourceSet=new HashSet<>();//所有角色都具有的资源，如文件夹 (需要登陆)
	private Set<String> commonL2ResourceSet=new HashSet<>();//所有角色都具有的资源，如jpg、controller (需要登陆)
	
	private IUserOpLogWrapperService userOpLogWrapperService;

	@SuppressWarnings("unchecked")
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest httpRequest=(HttpServletRequest) request;
		HttpServletResponse httpResponse=(HttpServletResponse) response;
		String ctx=httpRequest.getContextPath();
		String reqURI=httpRequest.getRequestURI();
		String getParamStr = null;
		//上下文根的访问直接去登陆页
		if(ctx.equals(reqURI) || (ctx+"/").equals(reqURI)) {
			httpResponse.sendRedirect(ctx+LOGIN_URL);
			return;
		}
		//other
		if(ctx.length()>1) {
			reqURI=reqURI.substring(ctx.length());
		}
		int pIdx=reqURI.indexOf("?");
		if(pIdx!=-1) {
			getParamStr = reqURI.substring(pIdx);
			reqURI=reqURI.substring(0, pIdx);
		}
		//不支持cooicke的情况，jsessionid处理
		int jsidIdx=reqURI.indexOf(JSESSIONID);
		if(jsidIdx!=-1) {
			reqURI=reqURI.substring(0, jsidIdx);
		}
		//检查是否是忽略的资源
		boolean isIgnore=false;
		for (String r1 : ignoreL1ResourceSet) {
			if(reqURI.startsWith(r1)) {
				isIgnore=true;
				break;
			}
		}
		if(!isIgnore) {
			isIgnore=ignoreL2ResourceSet.contains(reqURI);
		}
		//检查session & 权限
		boolean isAjax=checkAjax(httpRequest);
		if(!isIgnore) {
			//session
			CommonUserVo userVO=null;
			HttpSession session =httpRequest.getSession(false);
			if(session != null) {
				userVO=(CommonUserVo) session.getAttribute(Constants.SESSION_USER_KEY);
			}
			if(userVO==null) {
				sendURL(isAjax, ctx+LOGIN_URL, HTTP_STATUS_SESSION_EXPIRE, "会话已超时，将重新登录！", httpResponse);
				return;
			}
			
			//公共资源检查
			boolean isCommonL1Resource=false;
			if(!isIgnore) {
				for (String r1 : commonL1ResourceSet) {
					if(reqURI.startsWith(r1)) {
						isIgnore=true;
						break;
					}
				}
				if(!isIgnore) {
					isIgnore=commonL2ResourceSet.contains(reqURI);
				}
				isCommonL1Resource=isIgnore;
			}
			//超级管理员检查
			if(!isIgnore) {
				isIgnore = userVO.isSuperAdmin();
			}
			//其他非超级管理员且需要权限的资源检查
			if(!isIgnore) {
				//非公共资源，则需要检查资源权限，auth resource
				Map<String, String> authesources= (Map<String, String>) session.getAttribute(Constants.SESSION_MAP_RESOURCE_KEY);
				if(authesources!=null) {
					isIgnore=authesources.containsKey(reqURI);
				}
			}
			String ip = IpUtil.getIp(httpRequest);
			//访问参数
			StringBuilder ss=new StringBuilder("{");
			Enumeration<String> ns=request.getParameterNames();
			while(ns.hasMoreElements()) {
				String k=ns.nextElement();
				ss.append(k).append(":").append(request.getParameter(k));
				if(ns.hasMoreElements())
					ss.append(", ");
			}
			ss.append("}");
			ss.append(" && getParamStr: ").append(getParamStr);
			String reqParams=ss.toString();
			if(!isCommonL1Resource) {
				log.info("访问请求明细：[userId={}, userName={}, isAjax={}, isPass={}, URI={}, ip={}]  param:{}", userVO.getId(), userVO.getUserName(), isAjax, isIgnore, reqURI, ip, reqParams);
			}
			if(!isIgnore) {
				log.error("非法访问请求明细：[userId={}, userName={}, isAjax={}, isPass={}, URI={}, ip={}]  param:{}", userVO.getId(), userVO.getUserName(), isAjax, isIgnore, reqURI, ip, reqParams);
			}
			//记录log
			userOpLogWrapperService.doAddOpLog(userVO.getId(), userVO.getUserName(), new Date(), ip, reqURI, reqParams);
		}
		//经过权限匹配后再次检查
		if(!isIgnore) {
			sendURL(isAjax, ctx+DENIED_URL, HTTP_STATUS_NO_AUTH, "无权限访问该资源！", httpResponse);
			return;
		}
		chain.doFilter(request, response);
	}

	/**
	 * @see Filter#init(FilterConfig)
	 */
	public void init(FilterConfig fConfig) throws ServletException {
		LOGIN_URL=fConfig.getInitParameter("loginUrl");
		DENIED_URL=fConfig.getInitParameter("deniedUrl");
		String ignoreResource=fConfig.getInitParameter("ignoreResource");
		String commonResource=fConfig.getInitParameter("commonResource");
		
		Pattern p = Pattern.compile("\\s*|\t|\r|\n");
        Matcher m = p.matcher(ignoreResource);
        ignoreResource = m.replaceAll("");
        m = p.matcher(commonResource);
        commonResource = m.replaceAll("");
        
		log.info("启动auth，ignoreResource资源：{}", ignoreResource);
		log.info("启动auth，commonResource资源：{}", commonResource);
		
		String[] irs=ignoreResource.split(",");
		for (String r : irs) {
			r=r.trim();
			if(r.length()==0) {
				continue;
			}
			if(r.endsWith("/")) {
				ignoreL1ResourceSet.add(r);
			}else{
				ignoreL2ResourceSet.add(r);
			}
		}
		
		String[] crs=commonResource.split(",");
		for (String r : crs) {
			r=r.trim();
			if(r.length()==0) {
				continue;
			}
			if(r.endsWith("/")) {
				commonL1ResourceSet.add(r);
			}else{
				commonL2ResourceSet.add(r);
			}
		}
		
		//获取spring bean
		ServletContext context = fConfig.getServletContext();
		ApplicationContext ac = WebApplicationContextUtils.getWebApplicationContext(context);
		userOpLogWrapperService = (IUserOpLogWrapperService)ac.getBean(IUserOpLogWrapperService.class); 
		
	}
	
	private boolean checkAjax(HttpServletRequest request){
        return  (request.getHeader("X-Requested-With") != null  && "XMLHttpRequest".equals( request.getHeader("X-Requested-With").toString())   ) ;
    }
	
	private void sendURL(boolean isAjax, String redirectURL, int ajaxRespCode, String ajaxMessage, HttpServletResponse httpResponse) throws IOException{
		if(isAjax) {
			httpResponse.setContentType("text/html;charset=UTF-8");
			httpResponse.sendError(ajaxRespCode, ajaxMessage);
		}else {
			httpResponse.sendRedirect(redirectURL);
		}
	}

	/*
		*三，状态代码的作用
		*	1，HTTP1.1中可用的特定状态码  
		*	    100-199：信息性的标示用户应该采取的其他动作。  
		*	    200-299：表示请求成功。  
		*	    300-399：用于那些已经移走的文件，常常包括Location报头，指出新的地址。  
		*	    400-499：表明客户引发的错误。  
		*	    500-599：由服务器引发的错误。 
		*	2，Servlet与客户端交互使用的重要状态代码。  
		*	    100(Continue,继续):表示客户端程序在询问是否可以在随后的请求中向服务器发送附加文档。服务器使用100(SC_CONTINUE)表示继续，417(SC_EXPECTATION_FAILED)表示不接受该附件。  
		*	    200(OK, 一切正常)：200(SC_OK)表示一切正常。  
		*	    202(Accepted,已经接受)：202(SC_ACCEPTED)请求已经接受，但处理没有完成。  
		*	    204(No Content,没有新文档)：204(SC_NO_CONTENT)由于没有新的文档供显示，继续显示之前的文档。  
		*	    205(Rest Content，重置内容)：205(SC_REST_CONTENT)表示没有新的文档，但浏览器应该重置文档视图。用来指示浏览器清除表单的字段。  
		*	    301(Moved Permanently,被永久移动)：301(SC_MOVED_PERMANENTLY)表示所请求的文件已经被移动到别处。文档的新URL在Location响应报头中给出，浏览器应该重新连接到新URL。  
		*	    302(Found，找到)：302(SC_MOVED_PERMANENTLY)。301和302等同，区别在302将Location报头中给出的URL看作是非永久性的临时替代。浏览器自动重新连接到Location响应报头中给出的URL。  
		*	    304(Not Modified，未发生更改)：304(SC_NOT_MODIFIED)表示缓存的版本是否为最新。是的话客户端就应该使用它，否则服务器应该返回所请求的文档，设置正常状态码。  
		*	        Servlet不应该直接使用这个状态代码，应该实现getLastModified方法，由默认的Service方法来处理。  
		*	    307和303区别：接收到303响应则继续进行get和post请求的重定向，接受到307，对于get请求继续重定向，而post则不再继续。  
		*	    400：表明客户请求中含有语法错误。  
		*	    401：表客户程序试图访问密码保护的页面。  
		*	    403：表示服务器拒绝提供相关的资源，不管是否授权。  
		*	    404：(SC_NOT_FOUND)没有如何资源供访问。  
		*	    405：这个资源不容许使用请求方法访问。  
		*	    415：服务器不知道如何处理请求附加文件类型。  
		*	    417：回应100，告诉浏览器不接受该附件。  
		*	    500：表示服务器运行混乱。由CGI程序或Servlet崩溃或返回不正确格式的报头引起。  
		*	    501：表示服务器不支持该客户程序发送的命令。  
		*	    503：由于维护或超负荷工作引起服务器不能做出响应。  
		*	    505：表示服务器不支持请求行中给出的HTTP版本。  
		*
		*四，重新连接，重定位，刷新的区别：
		*    重新连接（转发）：不显示任何中间页面。
		*    刷新：的时候浏览器会临时显示一个中间页面。
		*    重定位（重定向）：由另外的Servlet或网页生成结果，而非该ServletB本身。
		*    重定位作用：预先知道目的地，可以验证相关数据后再转送到相关目的地。通过自己的网站公布用户需要访问的网站链接，跟踪用户的行为，统计访问等。得到相关信息后，再将用户重定向到实际需要访问的网站。 
		*五，进行重新定位和跳转到错误页面
		*    使用sendRedirect（String url）方法向浏览器发送302状态代码，浏览器自动重新连接到Location响应报头中sendRedirect方法给出的URL，重新定位到相关页面。
		*	使用sendError（response.SC_NOT_FOUND，String msg）方法设置输出的错误提示信息。
		*六，根据浏览器的不同将用户重新定位到相关页面的Servlet。
		*    获取User-Agent请求报头区分不同的浏览器类型，使用sendRedirect（String url）方法向浏览器发送302状态代码，浏览器自动重新连接到Location响应报头中sendRedirect方法给出的URL，重新定位到相关页面。
	 */
}
