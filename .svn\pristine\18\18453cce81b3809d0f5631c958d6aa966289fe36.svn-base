<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/1/4
  Time: 11:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../../common/taglibs.jsp" %>
<html>
<head>
    <title>修改薪资项目</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/css/queryFormAdaptive.css?v=${publishVersion}" media="all"/>
    <style>
        .inlineLong {
            width: 100%;
        }

        .textareaLong {
            width: 100%;
        }

        /*.layui-onlySelf {*/
        /*    width: 125px;*/
        /*}*/
        .layui-form-checkbox {
            margin-top: unset;
            min-height: unset;
        }
        .layui-tab-card {
            border-style: unset;
            box-shadow: unset;
        }
    </style>
</head>
<body>
<div class="layui-tab layui-tab-card" style="height: 70%">
    <ul class="layui-tab-title">
        <li class="layui-this">修改</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <div class="layui-fluid">
                <div class="layui-card">
                    <form class="layui-form layui-card-header layuiadmin-card-header-auto" id="searchForm">
                        <%--隐藏域--%>
                        <input type="hidden" name="salaryCategoryId" id="salaryCategoryId" value="${salaryCategoryId}">
                        <%--                       这个是当前行的数据的审批状态-->只有在修改的时候,并且审批状态为审批完成的时候才会把保存按钮去除--%>
                        <input type="hidden" id="approval" value="${approval}">
                        <input type="hidden" id="salaryCommOrg" name="salaryCommOrg" value="${salaryCommOrg}">
                            <input type="hidden" name="id" id="id" value="${itemId}">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label title="薪资项目名称" class="layui-form-label layui-elip layui-onlySelf"><i
                                        style="color: red">*</i>薪资项目名称:</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable system" id="itemName"
                                           name="itemName" lay-filter="itemNameFilter" autocomplete="off"
                                           placeholder="请输入" maxlength="20" lay-verify="required">
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="英文" class="layui-form-label layui-elip layui-onlySelf">英文:</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable system" id="engName"
                                           name="engName" lay-filter="engNameFilter" autocomplete="off" onkeyup="this.value=this.value.replace(/[^\w\.\/]/ig,'')"
                                           placeholder="请输入" >
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="薪资项目编号" class="layui-form-label layui-elip layui-onlySelf">薪资项目编号:</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable" id="itemNo" name="itemNo"
                                           autocomplete="off" placeholder="无法输入自动生成" disabled>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="是否文本"
                                       class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>是否文本:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select system" name="txtFlag" lay-filter="txtFlagFilter" id="txtFlag"
                                            autocomplete="off" placeholder="请选择" DICT_TYPE="TXT_FLAG"
                                            lay-verify="required" readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="是否扣税" class="layui-form-label layui-elip layui-onlySelf"><i
                                        style="color: red">*</i>是否扣税:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select system" name="taxFlag" lay-filter="taxFlagFilter" id="taxFlag"
                                            autocomplete="off" placeholder="请选择" DICT_TYPE="TAX_FLAG"
                                            lay-verify="required" readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="增减属性" class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>增减属性:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select system" name="increaseFlag" lay-filter="increaseFlagFilter"
                                            id="increaseFlag" autocomplete="off" placeholder="请选择"
                                            DICT_TYPE="INCREASE_FLAG" lay-verify="required" readonly >
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="小数位数" class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>小数位数:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select system" name="decimalCnt" lay-filter="dicimalCntFilter"
                                            id="decimalCnt" autocomplete="off" placeholder="请选择" DICT_TYPE="DECIMAL_CNT" lay-verify="required"
                                            readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="是否年终奖" class="layui-form-label layui-elip layui-onlySelf">是否年终奖:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select" name="bonusFlag" lay-filter="bonusFlagFilter"
                                            id="bonusFlag" autocomplete="off" placeholder="请选择" DICT_TYPE="BOOLEAN_TYPE"
                                            readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>


                            <div class="layui-inline">
                                <label title="是否劳务费" class="layui-form-label layui-elip layui-onlySelf">是否劳务费:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select" name="feeFlag" lay-filter="feeFlagFilter" id="feeFlag"
                                            autocomplete="off" placeholder="请选择" DICT_TYPE="BOOLEAN_TYPE" readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="是否显示" class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>是否显示:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select" name="displayFlag" lay-filter="displayFlagFilter"
                                            id="displayFlag" autocomplete="off" placeholder="请选择"
                                            DICT_TYPE="BOOLEAN_TYPE" readonly lay-verify="required">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="是否在薪资单中"
                                       class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>是否在薪资单中:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select" name="listFlag" lay-filter="listFlagFilter"
                                            id="listFlag" autocomplete="off" placeholder="请选择" DICT_TYPE="BOOLEAN_TYPE"
                                            readonly lay-verify="required">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="选择数据来源" class="layui-form-label layui-elip layui-onlySelf"><i style="color: red">*</i>选择数据来源:</label>
                                <div class="layui-input-inline">
                                    <select class="layui-select system" name="source" lay-filter="source" id="source"
                                            autocomplete="off" placeholder="请选择" DICT_TYPE="SELECT_DATA_SOURCE" lay-verify="required"
                                            readonly>
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label title="系统提供" class="layui-form-label layui-elip layui-onlySelf"><i class="redStart1" style="color: red">*</i>系统提供:</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input layui-input-disposable system" id="contentHidden" disabled
                                           autocomplete="off" placeholder="请选择" >
                                    <input type="text" class="layui-input layui-input-disposable system" id="content1"
                                           autocomplete="off" name="content1" placeholder="请选择"  style = "display:none">
                                </div>
                            </div>

                            <div class="layui-inline inlineLong">
                                <label title="公式" class="layui-form-label layui-elip layui-onlySelf"><i class="redStart2" style="color: red">*</i>公式:</label>
                                <div class="layui-input-inline">
                                    <textarea class="layui-textarea textareaLong system" name="content2" disabled
                                              lay-filter="content2Filter" id="content2" value=""></textarea>
                                </div>
                            </div>

                            <div class="layui-inline" style="float: right">
                                <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="save"
                                        lay-filter="save" lay-submit="">保存
                                </button>
                                <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="commit"
                                        lay-filter="commit" lay-submit="">提交
                                </button>
                                <button class="layui-btn layuiadmin-btn-list delAttr" type="button" id="close"
                                        lay-filter="closeFilter">取消
                                </button>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/salary/salaryItem/salaryItemEdit.js?v=${publishVersion}"></script>
</body>
</html>
