package com.reon.hr.api.customer.vo.supplier;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CommercePriceInfoVo implements Serializable {
    private Long id;

    private Long contractId;

    private Long commerceId;

    private BigDecimal monthCost;

    private BigDecimal yearCost;

    private BigDecimal monthPrice;
    private BigDecimal yearPrice;//年指导价
    private BigDecimal priceCoeffi;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private String delFlag;
    //产品信息
    private Long supplierId;

    private String prodName;

    private Byte prodType;

    private BigDecimal baseVal;

    private Byte multiFlag;
    private Byte explainFlag;


}