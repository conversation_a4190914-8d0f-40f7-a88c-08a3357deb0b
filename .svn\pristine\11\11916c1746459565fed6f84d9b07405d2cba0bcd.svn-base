<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>

<head>
    <title>新增客户</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all" />
    <style>
        .layui-input {
            padding-right: 30px !important;
        }
    </style>
</head>

<body class="childrenBody">
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" style="margin-top: 5px">
            <input type="hidden" id="type" value="${type}" >
            <input type="hidden" id="editIndustryType" value="${customerVo.industryType}" >
            <input type="hidden" id="editCorpKind" value="${customerVo.corpKind}" >
            <input type="hidden" id="editCityCode" value="${customerVo.cityCode}" >
            <input type="hidden" id="creator" value="${customerVo.creator}" >
            <form class="layui-form" method="post">
                <input type="hidden" name = "id" value="${customerVo.id}">
                <input type="hidden" name = "refCustId" value="${refCustId}">
                <input type="hidden" name="custNo" value="${customerVo.custNo}">
                <table class="layui-table" lay-skin="nob" style="width: 65%;">
                    <tr>
                        <td align="right" width="20%"><i style="color: red; font-weight: bolder;">*</i>客户名称:</td>
                        <td width="60%"><input class="layui-input" type="text" name="custName" id="custName" lay-verify="required" value="${customerVo.custName}"></td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>所属行业：</td>
                        <td>
                            <select id = "industryType" name="industryType" DICT_TYPE="NDUSTRY_TYPE" lay-verify="required" lay-verType="tips" >
                                <option value=""></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            <label class="layui-form-label filed-length" style="float: right" title="客户具体业务">客户具体业务：</label>
                        </td>
                        <td>
                            <input class="layui-input" type="text" name="bizType" autocomplete="off" maxlength="50" id="bizType" value="${customerVo.bizType}">
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            <label class="layui-form-label filed-length layui-hide industryAdd" style="float: right" title="行业补充信息">行业补充信息：</label>
                        </td>
                        <td class="layui-hide industryAdd">
                            <input class="layui-input" type="text" name="industryAdd" autocomplete="off" maxlength="50" id="industryAdd" value="${customerVo.industryAdd}">
                        </td>
                    </tr>
                    <div class="layui-show bizType">
                    </div>
                    <div class="layui-show industryAdd">
                    </div>

                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>所在城市：</td>
                        <td>
                            <select name="cityCode" id="cityCode" lay-verify="required" lay-verType="tips" lay-search AREA_TYPE>
                                <option value=""></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>客户联系人：</td>
                        <td><input class="layui-input" type="text" name="contactor" autocomplete="off" maxlength="50" id="contactor" lay-verify="required"  value="${customerVo.contactor}"></td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>客户联系电话：</td>
                        <td><input class="layui-input" type="text" name="tel" autocomplete="off" maxlength="20" id="tel" lay-verify="required"  value="${customerVo.tel}"></td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>客户邮箱：</td>
                        <td><input class="layui-input" type="text" name="email" autocomplete="off" id="email" lay-verify="required|email"  value="${customerVo.email}"></td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>客户联系地址：</td>
                        <td><input class="layui-input" type="text" name="addr" autocomplete="off" id="addr" lay-verify="required"  value="${customerVo.addr}"></td>
                    </tr>
                    <tr>
                        <td align="right"><i style="color: red; font-weight: bolder;">*</i>客户职位：</td>
                        <td><input class="layui-input" type="text" name="position" autocomplete="off" id="position"
                                   lay-verify="required" value="${customerVo.position}"></td>
                    </tr>
                </table>
                <div style="float: right; margin-right: 40%;">
                    <button class="layui-btn" lay-submit lay-filter="save" id="save" type="button" authURI="/customer/customer/save">保存</button>
                    <button class="layui-btn" type="button" id="cancelBtn">取消</button>
                </div>
            </form>
        </div>
    </div>
     <script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
    <script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
    <script type="text/javascript" src="${ctx}/js/modules/customer/customer/editCustomer.js?v=${publishVersion}"></script>
</body>

</html>