<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>

<head>
    <title>修改拜访客户</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/css/main.css?v=${publishVersion}" media="all"/>
    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style type="text/css">
        .layui-tab-card {
            border-style: unset;
            box-shadow: unset;
        }

        .layui-show {
            display: inline !important;
        }

    </style>
</head>

<body class="childrenBody">

<div class="layui-tab layui-tab-card" style="margin-top: 0px" lay-filter="quotationTabFilter" >

    <div class="layui-tab-item layui-show" style="margin-top: 5px">
        <form class="layui-form" method="post">
            <input type="hidden" id="visitId" name="visitId" value="${visitId}">
            <input type="hidden" id="username" name="username" value="${username}">
            <div class="layui-form-item" style="margin-top: 20px">

                <div class="layui-inline">
                    <label class="layui-form-label"><i style="color: red">*</i>&nbsp;拜访日期：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="visitDate" id="visitDate" placeholder="请选择" autocomplete="off" class="layui-input visitDate"
                               lay-verify="required"  readonly/>
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="拜访时间段"><i
                            style="color: red">*</i>&nbsp;拜访时间段：</label>
                    <div class="layui-input-inline">
                        <select name="periodTime" id="periodTime" DICT_TYPE="PERIOD_TIME" lay-verify="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="是否返回公司"><i
                            style="color: red">*</i>&nbsp;是否返回公司：</label>
                    <div class="layui-input-inline">
                        <select name="backFlag" id="backFlag" DICT_TYPE="BOOLEAN_TYPE" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="外出人"><i style="color: red">*</i>外出人</label>
                    <div class="layui-input-inline">
                        <input type="text" id="outPerson" name="outPerson" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top: 20px">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="小组"><i style="color: red">*</i>小组</label>
                    <div class="layui-input-inline">
                        <input type="text" id="team" name="team" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="陪同人">陪同人</label>
                    <div class="layui-input-inline">
                        <input type="text" id="accompanyPerson" name="accompanyPerson" class="layui-input " placeholder="请填入"
                               autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="是否意向客户"><i
                            style="color: red">*</i>&nbsp;是否意向客户：</label>
                    <div class="layui-input-inline">
                        <select name="intentionFlag" id="intentionFlag" DICT_TYPE="BOOLEAN_TYPE" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="意向产品线"><i
                            style="color: red">*</i>&nbsp;意向产品线：</label>
                    <div class="layui-input-inline">
                        <select name="intentionProd" id="intentionProd" DICT_TYPE="PROD_KIND" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top: 20px">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="本年度累计拜访次数"><i style="color: red">*</i>本年度累计拜访次数</label>
                    <div class="layui-input-inline">
                        <input type="text" id="total" name="total" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="拜访目的"><i
                            style="color: red">*</i>&nbsp;拜访目的：</label>
                    <div class="layui-input-inline">
                        <select name="visitAim" id="visitAim" DICT_TYPE="VISIT_AIM" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户类型"><i
                            style="color: red">*</i>&nbsp;客户类型：</label>
                    <div class="layui-input-inline">
                        <select name="custType" id="custType" DICT_TYPE="CUST_TYPE" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>


                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户公司名称"><i style="color: red">*</i>客户公司名称</label>
                    <div class="layui-input-inline">
                        <input type="text" id="companyName" name="companyName" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top: 20px">
                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户姓名"><i style="color: red">*</i>客户姓名</label>
                    <div class="layui-input-inline">
                        <input type="text" id="custName" name="custName" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户职位"><i style="color: red">*</i>客户职位</label>
                    <div class="layui-input-inline">
                        <input type="text" id="custPosition" name="custPosition" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户电话"><i style="color: red">*</i>客户电话</label>
                    <div class="layui-input-inline">
                        <input type="text" id="custTel" name="custTel" class="layui-input queryDis" placeholder="请填入"
                               autocomplete="off" lay-verify="required">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label layui-elip" title="客户性别"><i
                            style="color: red">*</i>&nbsp;客户性别：</label>
                    <div class="layui-input-inline">
                        <select name="custGender" id="custGender" DICT_TYPE="SEX" lay-verify="required" lay-filter="required"
                                lay-verType="tips" class="layui-select queryDis">
                            <option value=""></option>
                        </select>
                    </div>
                </div>
            </div>


            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="拜访结果"><i
                        style="color: red">*</i>&nbsp;拜访结果：</label>
                <div class="layui-input-inline">
                    <select name="visitResult" id="visitResult" DICT_TYPE="VISIT_RESULT" lay-verify="required" lay-filter="required"
                            lay-verType="tips" class="layui-select queryDis">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注：</label>
                <div class="layui-input-block" style="width: 1166px;">
                        <textarea placeholder="请输入内容" name="remark" id="remark" class="layui-textarea queryDis"
                                  style="min-width: 55px"></textarea>
                </div>
            </div>

            <div style="float: right; margin-right: 40%;">
                <button class="layui-btn" lay-submit lay-filter="save" id="save" type="button" authURI="/customer/sales/save">保存</button>
                <button class="layui-btn" type="button" id="cancelBtn" lay-filter="cancelBtn">取消</button>
            </div>
        </form>
    </div>
</div>



<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/sales/editVisitCompany.js?v=${publishVersion}"></script>
</body>
</html>


