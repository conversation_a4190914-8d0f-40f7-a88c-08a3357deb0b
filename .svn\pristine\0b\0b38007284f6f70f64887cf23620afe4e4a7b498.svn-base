<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@include file="../../common/taglibs.jsp" %>
<html>
<head>
    <title>合同</title>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="stylesheet" href="${ctx}/layui/css/layui.css?v=${publishVersion}" media="all"/>
    <style>
        .layui-input {
            padding-right: 30px;
        !important;
        }

        .layui-table-cell {
            padding: 0px;
        }
    </style>
</head>
<body class="childrenBody">
<blockquote class="layui-elem-quote">
    <input type="hidden" id="type" value="${type}">
    <%--startQuery--%>
    <form class="layui-form" id="searchForm" action="" method="post">
        <div class="layui-inline queryTable">
            <div class="layui-input-inline">
                <label class="layui-form-label" title="合同编号" style="font-weight:800">合同编号</label>
                <div class="layui-input-inline">
                    <input type="text" id="contractNo" maxlength="20" name="contractNo" placeholder="请输入" class="layui-input"
                           autocomplete="off">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="合同名称" style="font-weight:800">合同名称</label>
                <div class="layui-input-inline">
                    <input type="text" id="contractName" maxlength="20" name="contractName" placeholder="请输入"
                           class="layui-input" autocomplete="off">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label " title="客户名称" style="font-weight:800">客户名称</label>
                <div class="layui-input-inline">
                    <input type="text" maxlength="20" id="custName" name="custName" placeholder="请输入" class="layui-input"
                           autocomplete="off">
                </div>
            </div>


            <div class="layui-input-inline">
                <label class="layui-form-label " title="销售" style="font-weight:800">销售</label>
                <div class="layui-input-inline">
                    <select name="seller" id="sale" lay-search lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>


            <a class="layui-btn" id="btnQuery" data-type="reload">检索</a>
            <button class="layui-btn" id="reset" type="reset">重置</button>
            <a class="layui-btn" id="more">显示更多…</a>
        </div>
        <div class="layui-inline queryTable isShow" style="margin-top: 20px;display:none">

            <div class="layui-input-inline ">
                <label class="layui-form-label layui-elip" title="销售所属公司" style="font-weight:800">销售所属公司</label>
                <div class="layui-input-inline">
                    <select name="belongCompany" id="belongCompany" lay-search >
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" title="人员分布" style="font-weight:800">人员分布</label>
                <div class="layui-input-block">
                    <select name="distributeType" lay-search DICT_TYPE="EMP_DISTRIBUTE_TYPE" lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" title="合同类型" style="font-weight:800">合同类型</label>
                <div class="layui-input-block">
                    <select name="contractType" lay-search DICT_TYPE="CONTRACT_CATEGORY" lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" title="合同状态" style="font-weight:800">合同状态</label>
                <div class="layui-input-block">
                    <select name="status" lay-search DICT_TYPE="CONTRACT_STATUS" lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-inline queryTable isShow" style="margin-top: 20px;display:none">

            <div class="layui-inline">
                <label class="layui-form-label" title="负责客服" style="font-weight:800">负责客服</label>
                <div class="layui-input-block">
                    <select name="commissioner" id="commissioner" lay-search>
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" title="审批状态" style="font-weight:800">审批状态</label>
                <div class="layui-input-block">
                    <select name="approvalStatus" lay-search DICT_TYPE="CONTRACT_APPR_STATUS" lay-verify="required">
                        <option value=""></option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="审批通过日期（开始）"
                       style="font-weight:800">审批通过日期（开始）</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="passDateS" readonly>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="审批通过日期（结束）"
                       style="font-weight:800">审批通过日期（结束）</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="passDateE" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="大集团"
                       style="font-weight:800">大集团</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" placeholder="请点击" name="custGroupName"  >
                </div>
            </div>
        </div>

        <div class="layui-inline queryTable isShow" style="margin-top: 20px;display:none">

            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="终止服务日期（开始）"
                       style="font-weight:800">终止服务日期（开始）</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="stopDateS" readonly>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="终止服务日期（结束）"
                       style="font-weight:800">终止服务日期（结束）</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="stopDateE" readonly>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="是否上传会议纪要" style="font-weight:800">是否上传会议纪要</label>
                <div class="layui-input-block">
                    <select name="uploadFlag" DICT_TYPE="BOOLEAN_TYPE">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="合同起始日>="
                       style="font-weight:800">合同起始日>=</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="startDateS" readonly>
                </div>
            </div>

        </div>
        <div class="layui-inline queryTable isShow" style="margin-top: 30px;margin-right: 41px;display:none">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip " title="合同起始日<="
                       style="font-weight:800">合同起始日<=</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input contractDate" placeholder="请点击" name="startDateE" readonly>
                </div>
            </div>

        </div>
        <div class="layui-block queryTable isShow" style="margin-top: 30px;margin-right: 41px;display:none">
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="销售所属城市" style="font-weight:800">销售所属城市</label>
                <div class="layui-input-block">
                    <select name="ownerCity" id="ownerCity" lay-search >
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label layui-elip" title="人事处理状态" style="font-weight:800">人事处理状态</label>
                <div class="layui-input-block">
                    <select name="contractCompletionStatus" id="contractCompletionStatus" lay-search >
                        <option value=""></option>
                        <option value="1">未处理</option>
                        <option value="2">已处理</option>
                    </select>
                </div>
            </div>
        </div>

    </form>
    <%--endQuery--%>


</blockquote>
<%--startTable--%>
<table id="ContractGridTable" lay-filter="ContractGridTable"></table>
<%--endTable--%>

<script type="text/jsp" id="topbtn">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" id="add"  lay-event="add" authURI="/customer/contract/gotoAddContractPage">新增</button>
        <button class="layui-btn layui-btn-sm" lay-event="update" authURI="/customer/contract/gotoEditContractPage" >修改</button>
        <button class="layui-btn layui-btn-sm" lay-event="del" authURI="/customer/contract/delContract">删除</button>
        <button class="layui-btn layui-btn-sm" lay-event="addItion" authURI="/customer/contract/gotoAddAssociationPage">补充/关联协议审批</button>
        <button class="layui-btn layui-btn-sm" id="detail" lay-event="detail"  authURI="/customer/contract/gotoCheckContractPage">查看</button>
        <button class="layui-btn layui-btn-sm" lay-event="meeting" authURI="/customer/contract/gotoStartMeetingPage">项目启动会议记录</button>
            <button class="layui-btn layui-btn-sm" lay-event="careServiceInfo" authURI="/customer/contract/gotoCareServiceContractPage">维护客户信息</button>
        <button class="layui-btn layui-btn-sm" lay-event="stop" authURI="/customer/contract/stop">终止</button>
        <button class="layui-btn layui-btn-sm" lay-event="continue" authURI="/customer/contract/gotoEditContinue">续签</button>
        <button class="layui-btn layui-btn-sm" lay-event="autoDefer" authURI="/customer/contract/gotoEditContinue">自动顺延</button>
        <button class="layui-btn layui-btn-sm" lay-event="export" authURI="/customer/contract/exportContract">导出</button>
        <button class="layui-btn layui-btn-sm" lay-event="forwardSeller" authURI="/customer/contract/forwardSeller">转出</button>
        <button class="layui-btn layui-btn-sm" lay-event="binding" authURI="/customer/contract/gotoBindingPage">绑定报价单</button>
        <%--<button class="layui-btn layui-btn-sm" lay-event="printContract" authURI="/customer/contract/getPrintContractData">打印合同归档表</button>--%>
        <button class="layui-btn layui-btn-sm" lay-event="uploadFinalContract" authURI="/customer/contract/gotoEditContractPage">更新最终版合同</button>
        <button class="layui-btn layui-btn-sm" lay-event="updateNewFlagAndRemark"
                authURI="/customer/contract/gotoUpdateNewFlagAndRemark">修改存量标识与备注
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="updateComRemark"
                authURI="/customer/contract/editComRemark">修改项目客服备注
        </button>
         <button class="layui-btn layui-btn-sm" lay-event="blankContract"
                authURI="/customer/contract/gotoBlankContract">生成空白标准合同
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="changeQuotation"
                authURI="/customer/contract/gotoChangeQuotationPage">更换默认报价单
        </button>
       <button class="layui-btn layui-btn-sm" lay-event="changeContractItem"
                authURI="/customer/contract/gotoChangeContractItem">合同项修改审批
        </button>
       <button class="layui-btn layui-btn-sm" lay-event="exportCustomerContractor"
                authURI="/customer/contract/exportCustomerContractor">导出当前项目客服客户联系方式
        </button>
    </div>

</script>
<script type="text/javascript" src="${ctx}/layui/layui.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/jquery-1.10.2.min.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/common.js?v=${publishVersion}"></script>
<script type="text/javascript" src="${ctx}/js/modules/customer/contract/contract.js?v=${publishVersion}"></script>

<div id="printDiv" style="display: none">
    <div style="text-align: center;font-size: 25px;font-weight: bold">业务合同归档签收表</div><br/><br/>
    <table width="720px" style="border-collapse:collapse;font-size: 10px;text-align: center;margin:0 auto;" class="printTable">
        <tr style="height: 30px;">
            <td  style="width:25%;font-weight: bold">合同编号</td>
            <td  style="width:30%;" colspan="2" id="contractNoTd"></td>
            <td  style="width:20%;font-weight: bold" colspan="2">归档编号</td>
            <td  style="width:25%;"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">定山签约主体</td>
            <td colspan="2" id="signNameTd"></td>
            <td style="font-weight: bold" colspan="2">销售/经办人</td>
            <td id="saleNameTd"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">客户公司名称</td>
            <td colspan="2" id="customerNameTd"></td>
            <td style="font-weight: bold" colspan="2">所属大集团</td>
            <td id="groupNameTd">沪铁保安</td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">产品类型</td>
            <td colspan="2" id="productTypeTd"></td>
            <td style="font-weight: bold" colspan="2">产品方案</td>
            <td id="productSubTypeTd"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">合同开始日期</td>
            <td colspan="2" id="startMonthTd"></td>
            <td style="font-weight: bold" colspan="2">合同结束日期</td>
            <td id="endMonthTd"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">签约单价</td>
            <td colspan="2"  id="priceTd"></td>
            <td style="font-weight: bold" colspan="2">签约人数</td>
            <td id="numTd"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">单价是否含税</td>
            <td colspan="2" id="taxTd"></td>
            <td style="font-weight: bold" colspan="2">新签/续签/改签</td>
            <td id="contractTypeTd"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold">其他信息</td>
            <td colspan="5"></td>
        </tr>
        <tr style="height: 60px;">
            <td style="font-weight: bold">印章保管人<br/>盖印并签署</td>
            <td colspan="5" style="text-align: right">
                <font style="font-weight: bold">□公章&emsp;&emsp;&emsp;&emsp;□法人章&emsp;&emsp;&emsp;&emsp;□合同专用章&emsp;&emsp;&emsp;&emsp;□财务专用章&emsp;&emsp;&emsp;&emsp;□其他章&emsp;&emsp;&emsp;&emsp;</font>
                <br/><br/>签字：&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;年&emsp;&emsp;&emsp;&emsp;月&emsp;&emsp;&emsp;&emsp;日</td>
        </tr>
        <tr style="height: 30px;">
            <td style="width:25%; font-weight: bold">销售/经办人合同签收</td>
            <td style="width:15%;"></td>
            <td style="width:15%;font-weight: bold">合同归档份数</td>
            <td style="width:8%;"></td>
            <td style="width:12%;font-weight: bold">归档日期</td>
            <td style="width:25%;"></td>
        </tr>
        <tr style="height: 30px;">
            <td style="font-weight: bold" rowspan="2">备注</td>
            <td colspan="5" style="text-align: left">1、该合同已通过REON系统完成线上审批；</td>
        </tr>
        <tr style="height: 30px;">
            <td colspan="5" style="text-align: left">2、该合同需各部门完成签批后，由销售人员及时提交合同保管人归档。</td>
        </tr>
    </table>
</div>
<div style="display: none" id="uplocadFinalHtml">
    <form class="layui-form" method="post">
        <input type="hidden" id="id" name = "id">
        <input type="hidden" name="custNo">
        <input type="hidden" name="fileId" id="fileId">
        <table class="layui-table" lay-skin="nob" style="width: 65%;">
            <tr>
                <td align="right"><i style="color: red; font-weight: bolder;"></i>附件：</td>
                <td>
                    <div class="layui-upload" style=" margin-left: 40px; margin-right: 40px;">
                        <button type="button" id="customerUpload" class="layui-btn layui-btn-normal">选择文件</button>
                        <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                            附件：
                            <div class="layui-upload-list" id="upload"></div>
                        </blockquote>
                    </div>
                </td>
            </tr>
        </table>
        <div style="float: right; margin-right: 40%;">
            <button class="layui-btn" lay-submit lay-filter="save" id="save" type="button" authURI="/customer/customer/save">上传</button>
            <button class="layui-btn" type="button" id="cancelBtn">取消</button>
        </div>
    </form>
</div>
</body>
</html>
