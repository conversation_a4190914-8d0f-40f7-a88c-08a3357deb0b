package com.reon.hr.api.bill.vo.salary;

import com.reon.hr.api.bill.vo.check.BillCheckVo;
import com.reon.hr.api.customer.vo.salary.TaxDateRangeVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class SalaryPayBatchVo  implements Serializable {
    private Long id;
    private Integer salaryMonth;//工资所属月
    private Integer billMonth ;//客户账单月
    private Long custId;
    private String custName;
    private Long payId;
    private List<Long> payIdList;
    private String batchNo;
    private String batchName;
    private BigDecimal totalActApy;
    private BigDecimal totalTax;
    private Integer applyCnt;
    private Date genTime;
    private Byte genFlag;
    private Integer anewPayFlag;
    private String paymentContentSummary;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String delFlag;
    private List<Long> salaryIdList;
    List<Map<String,Object>> payBatchEmpSalaryList;
    private String withholdingAgentNo;
    private BigDecimal totalSalaryFee;
    private BigDecimal totalSupplierDisFund;
    private BigDecimal totalSupplierCrossBankHandlingFees;
    private BigDecimal totalSupplierUnionFees;
    private BigDecimal totalSupplierSalarySaleTax;
    /**
     * 补偿金
     */
    private BigDecimal totalCompensation;//补偿金
    /**
     * 补偿金个税
     */
    private BigDecimal totalCompensationTax;//补偿金个税
    /**
     * 年终奖
     */
    private BigDecimal totalAnnualBonus;//年终奖
    /**
     * 年终奖个税
     */
    private BigDecimal totalAnnualBonusTax;//年终奖个税
    /**
     * 劳务工资
     */
    private BigDecimal totalLaborWages;//劳务工资
    /**
     * 劳务工资个税
     */
    private BigDecimal totalLaborWagesTax;//劳务工资个税
    private List<SalaryPayBatchReceivedVo> salaryPayBatchReceivedVoList;
    private List<BillCheckVo> billCheckVoList;
    private List<SalaryPayBatchInfoVo> salaryPayBatchInfoVoList;
    private String billCheckId;
    private Long paymentId;
    private Long templetId;
    private String subTypes;
    private String contractNo;
    private Integer contractType;
    private String commOrg;
    private String commPos;
    private String commissioner;
    private List<TaxDateRangeVo> taxDateRangeVoList;
    private String salaryCommOrg;
    private String salaryCommPos;
    private String salaryCommissioner;
    private Integer competitionFlag;
}