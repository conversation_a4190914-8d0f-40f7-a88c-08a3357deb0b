<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.bill.dao.bill.InsurancePracticeDisComPayMapper">


    <delete id="deleteInsurancePracticeDisComPayByPayId">
        delete from insurance_practice_dis_com_pay
        WHERE pay_id = #{payId}
    </delete>


    <insert id="saveInsurancePracticeDisComPayBatch" parameterType="java.util.List" useGeneratedKeys="true">
        INSERT INTO insurance_practice_dis_com_pay
        (pay_id,yurref, dis_com, dis_com_app, pay_amt,service_amt,act_pay_amt, creator, create_time, updater, update_time, del_flag)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.payId},#{item.yurref}, #{item.disCom}, #{item.disComApp}, #{item.payAmt},#{item.serviceAmt},#{item.actPayAmt}, #{item.creator}, NOW(), #{item.updater}, NOW(), 'N')
        </foreach>
    </insert>


    <select id="getInsurancePracticeDisComListByPayId" resultType="com.reon.hr.api.bill.vo.InsurancePracticeDisComPayVo">
        select
        *
        from insurance_practice_dis_com_pay
        where pay_id = #{payId}
          and del_flag = 'N'
    </select>

    <select id="getPayAmtGroupByDisComByPayIdList" resultType="com.reon.hr.api.bill.vo.InsurancePracticeDisComPayVo">
        SELECT
        dis_com, SUM(pay_amt) AS pay_amt,SUM(act_pay_amt) AS act_pay_amt
        FROM
        insurance_practice_dis_com_pay
        WHERE
        pay_id IN
        <foreach collection="payIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND del_flag = 'N'
        GROUP BY
        dis_com
    </select>

    <select id="getPaymentApprovalListByDisCom" resultType="com.reon.hr.api.bill.vo.PaymentApplyVo">
        select pa.id, ip.dis_com,
        ip.pay_amt,
        pa.pid,
        pa.pay_method,
        pa.pay_com,
        pa.list_cnt,
        pa.bank_type,
        pa.depoist,
        pa.bank_no,
        pa.opening_place,
        pa.remark,
        pa.last_date,
        pa.pay_month,
        pa.purpose,
        pa.applicant,
        pa.app_com,
        pa.pay_desc,
        pa.pay_detail_type,
        pa.payee,
        pa.apply_time,
        pa.pay_type,
        ip.dis_com_app,
        ip.id as payMentId,
        ip.service_amt,
        ip.act_pay_amt
        from insurance_practice_dis_com_pay ip
        left join payment_apply pa on pa.id = ip.pay_id
        where pa.pid in
        <foreach collection="vo.pidList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and  ip.dis_com_app in
        <foreach collection="vo.disComAppList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="vo.startApplyTime!=null and vo.startApplyTime!=''">
            and pa.apply_time <![CDATA[>=]]> #{vo.startApplyTime,jdbcType=TIMESTAMP}
            and pa.apply_time <![CDATA[<]]> DATE_ADD(#{vo.startApplyTime,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
        </if>
        <if test="vo.lastDate!=null and vo.lastDate!=''">
            and pa.last_date <![CDATA[>=]]> #{vo.lastDate,jdbcType=TIMESTAMP}
            and pa.last_date <![CDATA[<]]> DATE_ADD(#{vo.lastDate,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
        </if>

        <if test="vo.payCom!=null and vo.payCom!=''">
            and pa.pay_com = #{vo.payCom}
        </if>
        <if test="vo.payMethod!=null and vo.payMethod!=''">
            and pa.pay_method = #{vo.payMethod}
        </if>
        <if test="vo.payMonth!=null and vo.payMonth!=''">
            and pa.pay_month = #{vo.payMonth}
        </if>

        ORDER BY pa.last_date
    </select>

    <select id="getDetailById" resultType="com.reon.hr.api.bill.vo.InsurancePracticeDisComPayVo">
        select * from insurance_practice_dis_com_pay where id = #{id}
    </select>


    <update id="updatePayFlagById" >
        update insurance_practice_dis_com_pay set pay_flag = 2,pay_time=now() where id = #{id}
    </update>

    <update id="updateOnlineFlagById" >
        update insurance_practice_dis_com_pay set online_flag = 1 where id = #{id}
    </update>


    <select id="getPrintApplicationFromPage" resultType="com.reon.hr.api.bill.vo.PaymentApplyVo">
        select pa.id, ip.id as payMentId, ip.dis_com,
        ip.pay_amt,
        pa.pay_com,
        ip.pay_time,
        pa.pay_type,
        pa.bank_type,
        pa.depoist,
        pa.bank_no,
        pa.pay_month,
        pa.applicant,
        pa.pay_method,
        pa.apply_time,
        ip.service_amt,
        pa.last_date,
        ip.act_pay_amt,
        ip.print_type,
        pa.pass_time,
        pa.app_status
        from insurance_practice_dis_com_pay ip
        left join payment_apply pa on pa.id = ip.pay_id
        where ip.pay_flag = 2
          and ip.dis_com in
        <foreach collection="vo.disComAppList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="vo.startApplyTime != null and vo.startApplyTime != ''">
            and pa.apply_time <![CDATA[>=]]> #{vo.startApplyTime,jdbcType=TIMESTAMP}
            and pa.apply_time <![CDATA[<]]> DATE_ADD(#{vo.startApplyTime,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
        </if>
        <if test="vo.lastDate != null and vo.lastDate != ''">
            and pa.last_date <![CDATA[>=]]> #{vo.lastDate,jdbcType=TIMESTAMP}
            and pa.last_date <![CDATA[<]]> DATE_ADD(#{vo.lastDate,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
        </if>
        <if test="vo.printType!=null and vo.printType!=''">
            and ip.print_type = #{vo.printType}
        </if>
        <if test="vo.payCom!=null and vo.payCom!=''">
            and pa.pay_com = #{vo.payCom}
        </if>
        <if test="vo.payMonth!=null and vo.payMonth!=''">
            and pa.pay_month = #{vo.payMonth}
        </if>
        order by pa.last_date
    </select>


    <select id="getReserveInquiryListByDisCom" resultType="com.reon.hr.api.bill.vo.PaymentApplyVo">
        select ip.dis_com, ip.dis_com_app, ip.service_amt + ip.pay_amt as payAmt,ip.act_pay_amt, pa.last_date
        from insurance_practice_dis_com_pay ip
                 left join payment_apply pa on ip.pay_id = pa.id
        where ip.pay_flag = 1
          and pa.app_status = 5
          and pa.last_date > now()
        and ip.dis_com in
        <foreach collection="vo.disComAppList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="vo.startApplyTime != null and vo.startApplyTime != ''">
            and pa.last_date <![CDATA[>=]]> #{vo.startApplyTime,jdbcType=TIMESTAMP}
            and pa.last_date <![CDATA[<]]> DATE_ADD(#{vo.startApplyTime,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
        </if>
        <if test="vo.disCom!=null and vo.disCom!=''">
            and ip.dis_com = #{vo.disCom}
        </if>
    </select>

    <update id="batchUpdatePrintTypeByIdList">
        update insurance_practice_dis_com_pay set print_type = 2 where id in
        <foreach item="item" collection="idList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="checkProgressByPayId" resultType="com.reon.hr.api.bill.vo.InsurancePracticeDisComPayVo">
        select dis_com,dis_com_app,pay_flag,online_flag from insurance_practice_dis_com_pay where pay_id=#{payId}
    </select>

    <select id="getDisComPayAmtHGroupByPayComAndDisComByDisComAndLastDate"
            resultType="com.reon.hr.api.bill.vo.InsurancePracticeDisComPayVo">
        select ip.dis_com,
               pa.pay_com,
               sum(ip.pay_amt)     as pay_amt,
               sum(ip.service_amt) as service_amt,
               sum(ip.act_pay_amt) as act_pay_amt
        from insurance_practice_dis_com_pay ip
                 left join payment_apply pa on pa.id = ip.pay_id
        where ip.dis_com = #{disCom}
          and last_date <![CDATA[>=]]> #{lastDate,jdbcType=TIMESTAMP}
          and last_date <![CDATA[<]]> DATE_ADD(#{lastDate,jdbcType=TIMESTAMP}, INTERVAL 1 DAY)
          and app_status = 5
          and pay_type = 1
          and ip.pay_flag = 1
        group by ip.dis_com, pa.pay_com
    </select>
</mapper>
