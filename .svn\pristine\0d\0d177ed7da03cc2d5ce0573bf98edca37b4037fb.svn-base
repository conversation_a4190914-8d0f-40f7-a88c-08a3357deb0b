<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
            http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
            http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- 读取数据库配置文件 -->
    <context:property-placeholder location="classpath:test-jdbc.properties"
                                  ignore-unresolvable="true"></context:property-placeholder>

    <context:annotation-config/>
    <context:component-scan base-package="com.reon.hr.sp.dao"></context:component-scan>
    <aop:aspectj-autoproxy/>

    <!-- 引入系统其他配置文件 -->
    <bean id="propertiesReader" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <value>classpath*:*.properties</value>
        </property>
    </bean>

    <!-- 扩展propertiesReader为 placeholder -->
    <context:property-placeholder properties-ref="propertiesReader" ignore-unresolvable="true"/>
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="driverClassName" value="${jdbc.driverClassName}"/>
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${druid.initialSize}"/>
        <property name="minIdle" value="${druid.minIdle}"/>
        <property name="maxActive" value="${druid.maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${druid.maxWait}"/>
    </bean>

    <!--    <jdbc:initialize-database data-source="dataSource" ignore-failures="DROPS">-->
    <!--        <jdbc:script location="classpath:data/init-table.sql" />-->
    <!--        <jdbc:script location="classpath:data/import-data.sql" />-->
    <!--    </jdbc:initialize-database>-->

    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="mapperLocations" value="classpath:/mysql/**/*.xml"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
    </bean>

    <!-- DAO接口所在包名，Spring会自动查找其下的类 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.reon.hr.sp.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"></property>
    </bean>

    <!-- 事务管理 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="add*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="insert*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="save*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="delete*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="generate*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="update*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="handle*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="inner*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="batchInner*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="*" propagation="SUPPORTS" read-only="true"/>
        </tx:attributes>
    </tx:advice>
    <aop:config>
        <aop:pointcut id="interceptorPointCuts" expression="execution(* com.reon.hr.sp.service..*.*(..))"/>
        <aop:advisor advice-ref="txAdvice" pointcut-ref="interceptorPointCuts"/>
    </aop:config>

</beans>