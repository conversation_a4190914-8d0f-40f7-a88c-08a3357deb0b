layui.use(['jquery','form', 'layer', 'element', 'laydate', 'table','upload'], function () {
    var table = layui.table,
        upload = layui.upload,
        $ = layui.$,
        form = layui.form,
        laydate = layui.laydate,
        layer = layui.layer,
        layer = parent.layer === undefined ? layui.layer : parent.layer;
         var uploadIds = [];
         var flag = true;
    //同时渲染多格laydate

        laydate.render({
            elem: '#meetingTime'
            ,trigger: 'click'
            ,min:'2010-01-01'
            ,max:'2099-12-12'
            ,theme: 'grid'
            ,calendar: true
            ,format: 'yyyyMMdd'
        });

        //保存
    form.on("submit(save)", function (data) {
        if ( uploadIds == false){
            return layer.msg('请上传文件内容后再提交哦！');
        }
        ML.layuiButtonDisabled($('#save'));// 禁用

        /*上传id*/
        var uploadIdList = [];
        uploadIds.forEach(function (obj) {
            var ids = {};
            ids['fileId'] = obj.fileId;
            uploadIdList.push(ids);
        });
        data.field['uploadIdList'] = uploadIdList;
        $.ajax({
            url:ML.contextPath+"/customer/contract/saveMeeting",
            type:'POST',
            dataType:'json',
            contentType: 'application/json',
            data:JSON.stringify(data.field),
            success:function (result) {
                layer.closeAll('iframe');
                layer.msg(result.msg);
            },
            error:function (data) {
                layer.msg("系统繁忙，请稍后重试!");
                ML.layuiButtonDisabled($('#save'),'true');
            }
        });
        return false;
    });

    //上传

    if (!flag){
        return layer.msg("只允许上传一个文件");
    }

    if (flag) {
        upload.render({
            elem: '#meetUpload' //绑定元素
            , url: ML.contextPath + '/sys/file/upload' //上传接口
            , accept: 'file'
            , headers: {contentType: false, processData: false}
            , method: 'POST'
            , exts: 'zip|rar|jpg|png|gif|bmp|jpeg|doc|xls|ppt|txt|pdf|tiff|docx|xlsx|pptx|tif|avi|swf|ceb'
            , choose: function (obj) {
                flag = false;

            }
            , before: function (obj) {
                obj.preview(function (index, file, result) {
                    if (file.type == 'image/png') {
                        $('#upload').append('<img width="50px" height="50px" src="' + result + '" alt="' + file.name + '" class="layui-upload-img">')
                    } else {
                        $('#upload').append('  <input style="outline:none;border:0" value="' + file.name + '" readonly>')
                    }
                });
            }
            , done: function (res) {
                //上传完毕回调
                if (res.code == 0) {
                    uploadIds.push({'fileId': res.data});
                    layer.msg('上传成功', {icon: 1});
                    ML.layuiButtonDisabled($('#meetUpload'));
                }
            }
            , error: function () {
                //请求异常回调
                console.log("error");
                layer.msg('上传失败', {icon: 5});
            }
        });
    }

    //关闭弹窗
    $(document).on('click', '#cancel', function () {
        layer.closeAll('iframe');
    });
});
