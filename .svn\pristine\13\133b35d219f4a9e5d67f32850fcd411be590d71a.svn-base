package com.reon.hr.api.customer.vo.batchImport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class BatchAdjustImportTextVo extends BatchAdjustImportVo{
    @ExcelProperty(value = "订单编号*")
    private String orderNo;
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名*")
    private String name;
    /**
     * 订单号
     */
//    @ExcelProperty(value = "订单编号*")
//    private String orderNo;
    @ExcelProperty(value = "证件号*")
    private String certNo;

    @ExcelProperty(value = "证件类型*")
    private String certTypeStr;

    @ExcelProperty(value = "接单方*")
    private String receivingName;

    /**
     * 调整类型
     */
    @ExcelProperty(value = "调整类型*")
    private String adjustTypeStr;

    /**
     * 调整类型
     */
    @ExcelProperty(value = "是否调整非连续的个人订单*")
    private String adjustAllStr;

    /**
     * 调整起始月
     */
    @ExcelProperty(value = "调整起始月*")
    private Integer startMonth;

    /***
     * 企业基数列
     * */
    @ExcelProperty(value = "企业基数")
    private String comCol;
    /***
     * 个人基数列
     */
    @ExcelProperty(value = "个人基数")
    private String indCol;
    /**
     * 城市
     */
    @ExcelProperty(value = "城市*")
    private String cityName;
    /**
     * 社保公积金组
     */
    @ExcelProperty(value = "社保公积金组*")
    private String groupName;


    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称*")
    private String prodName;
    /***
     * 个人比例
     */
    @ExcelProperty(value = "旧比例")
    private String oldRatioName;

    @ExcelProperty(value = "新比例")
    private String newRatioName;

    /**
     * 操作对象和读取到的export对象进行关联
     */
    @ExcelIgnore
    private String uuid;

}
