<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reon.hr.sp.customer.dao.salary.SalaryPaymentComparisonMapper">
    <resultMap id="BaseResultMap" type="com.reon.hr.sp.customer.entity.salary.SalaryPaymentComparison">
        <result column="salary_batch_detail_ids" jdbcType="BIGINT" property="salaryBatchDetailIds"/>
        <result column="emp_id" jdbcType="BIGINT" property="empId"/>
        <result column="offline_data_ids" jdbcType="BIGINT" property="offlineDataIds"/>
        <result column="salary_comparison_type" jdbcType="INTEGER" property="salaryComparisonType"/>
        <result column="difference" jdbcType="DECIMAL" property="difference"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="payment_date" jdbcType="INTEGER" property="paymentDate"/>
        <result column="tax_month" jdbcType="INTEGER" property="taxMonth"/>
        <result column="emp_excel_add" jdbcType="VARCHAR" property="empExcelAdd"/>
        <result column="ind_tax_apply_info" jdbcType="VARCHAR" property="indTaxApplyInfo"/>
        <result column="tax_comparison_type" jdbcType="INTEGER" property="taxComparisonType"/>
        <result column="feedback_status" jdbcType="INTEGER" property="feedbackStatus"/>
        <result column="must_feedback_flag" jdbcType="INTEGER" property="mustFeedbackFlag"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        salary_batch_detail_ids,emp_id,offline_data_ids,salary_comparison_type,difference,remark,payment_date,tax_month,
        emp_excel_add,ind_tax_apply_info,tax_comparison_type,feedback_status,must_feedback_flag,
        withholding_agent_no,creator, create_time, updater, update_time, del_flag
    </sql>
    <insert id="insertByVoList">
        insert into salary_payment_comparison
        (salary_batch_detail_ids,emp_id,offline_data_ids,salary_comparison_type,difference,remark,payment_date,tax_month,
        emp_excel_add,ind_tax_apply_info,tax_comparison_type,must_feedback_flag,
        withholding_agent_no,creator)
        values
        <foreach collection="list" item="item" separator=",">
            (
             #{item.salaryBatchDetailIds},#{item.empId},#{item.offlineDataIds},#{item.salaryComparisonType},
             #{item.difference},#{item.remark},#{item.paymentDate},#{item.taxMonth},
             #{item.empExcelAdd},#{item.indTaxApplyInfo},#{item.taxComparisonType},#{item.mustFeedbackFlag},
             #{item.withholdingAgentNo},#{item.creator}
            )
        </foreach>
    </insert>
    <update id="updateFeedbackStatus">
        update salary_payment_comparison
        set feedback_status = #{feedbackStatus},updater=#{updater}
        where
        <foreach collection="list" item="item" separator=" or " open="(" close=")">
            (
            <if test="item.salaryBatchDetailIds != null and item.salaryBatchDetailIds != '' ">
                salary_batch_detail_ids = #{item.salaryBatchDetailIds} and
            </if>
            <if test="item.empId != null and item.empId != '' ">
                emp_id = #{item.empId} and
            </if>
            <if test="item.offlineDataIds != null and item.offlineDataIds != '' ">
                offline_data_ids = #{item.offlineDataIds} and
            </if>
            salary_comparison_type=#{item.salaryComparisonType} and payment_date=#{item.paymentDate} and tax_month=#{item.taxMonth} and
            withholding_agent_no=#{item.withholdingAgentNo} and tax_comparison_type=#{item.taxComparisonType}
            )
        </foreach>
    </update>
    <delete id="deleteByPaymentDate">
        delete from salary_payment_comparison
               where payment_date=#{paymentDate} and salary_comparison_type=1
    </delete>
    <delete id="deleteByTaxMonth">
        delete from salary_payment_comparison
        where tax_month=#{taxMonth} and tax_comparison_type=#{taxComparisonType} and salary_comparison_type=2
        <if test="withholdingAgentNo!=null and withholdingAgentNo!=''">
            and withholding_agent_no = #{withholdingAgentNo}
        </if>
        <if test="certNoList != null and certNoList.size > 0">
            and (
                TRIM(REPLACE(IFNULL(JSON_EXTRACT(ind_tax_apply_info,'$.certNo'),''),'"','')) in
                <foreach collection="certNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                or TRIM(REPLACE(IFNULL(JSON_EXTRACT(emp_excel_add,'$.certNo'),''),'"','')) in
                <foreach collection="certNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            )
        </if>
        <if test="staffNameList != null and staffNameList.size > 0">
            and (
            TRIM(REPLACE(IFNULL(JSON_EXTRACT(ind_tax_apply_info,'$.staffName'),''),'"','')) in
            <foreach collection="staffNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            or TRIM(REPLACE(IFNULL(JSON_EXTRACT(emp_excel_add,'$.name'),''),'"','')) in
            <foreach collection="staffNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </delete>
    <select id="getSalaryPaymentComparisonList"
            resultType="com.reon.hr.api.customer.vo.export.salary.SalaryPaymentComparisonExportVo">
        SELECT
        e.`name` emp_name,e.cert_no,spc.emp_id,spc.difference,spc.offline_data_ids,spc.salary_batch_detail_ids,
        spc.salary_comparison_type,spc.remark,spc.emp_excel_add,spc.ind_tax_apply_info,spc.tax_comparison_type,spc.feedback_status,
        spc.must_feedback_flag,
        spc.payment_date,spc.tax_month,wa.withholding_agent_name,wa.withholding_agent_type
        FROM
        salary_payment_comparison spc
        LEFT JOIN employee e on spc.emp_id=e.id
        left join withholding_agent wa on wa.withholding_agent_no=spc.withholding_agent_no
        <where>
            <if test="vo.empName != null and vo.empName !='' ">
                and e.`name` = #{vo.empName}
            </if>
            <if test="vo.certNo != null and vo.certNo !='' ">
                and e.cert_no = #{vo.certNo}
            </if>
            <if test="vo.paymentDate != null and vo.paymentDate !='' ">
                and spc.payment_date = #{vo.paymentDate}
            </if>
            <if test="vo.withholdingAgentNo != null and vo.withholdingAgentNo !='' ">
                and spc.withholding_agent_no = #{vo.withholdingAgentNo}
            </if>
            <if test="vo.feedbackStatus != null and vo.feedbackStatus !='' ">
                and spc.feedback_status = #{vo.feedbackStatus}
            </if>
            <if test="vo.mustFeedbackFlag != null and vo.mustFeedbackFlag !='' ">
                and spc.must_feedback_flag = #{vo.mustFeedbackFlag}
            </if>
            <if test="vo.salaryComparisonType != null and vo.salaryComparisonType !='' ">
                and spc.salary_comparison_type = #{vo.salaryComparisonType}
            </if>
            <if test="vo.taxComparisonType != null and vo.taxComparisonType !='' ">
                and spc.tax_comparison_type = #{vo.taxComparisonType}
            </if>
        </where>
    </select>
    <select id="getSalaryPaymentComparisonTaxList"
            resultType="com.reon.hr.api.customer.dto.customer.salary.IncomeTaxDifferencesImportDto">
        SELECT
        e.employee_no,e.`name` employeeName,e.cert_no,spc.emp_id,spc.difference,spc.offline_data_ids,spc.salary_batch_detail_ids,
        spc.salary_comparison_type,spc.remark,spc.emp_excel_add,spc.ind_tax_apply_info,spc.tax_comparison_type,spc.feedback_status,
        spc.must_feedback_flag,
        spc.payment_date,spc.tax_month,wa.withholding_agent_name,wa.withholding_agent_type,
        spc.withholding_agent_no,
        si.creator salaryCreator,c.contract_no,c.contract_name,c.dist_com,c.commissioner dist_com_man,c.salary_commissioner,sp.pay_name salary_pay_name,
        cust.cust_no,cust.cust_name,sc.category_name salary_category_name,e.cert_type,sp.bill_month,sp.salary_month,
        c.salary_comm_org,c.salary_comm_pos
        FROM
        salary_payment_comparison spc
        LEFT JOIN employee e on spc.emp_id=e.id
        left join withholding_agent wa on wa.withholding_agent_no=spc.withholding_agent_no
        left join salary_pay sp on sp.id=IFNULL(JSON_EXTRACT(spc.emp_excel_add,'$.payId'),0)
        left join salary_category sc on sp.salary_category_id=sc.id
        left join salary_info si on si.emp_id=e.id and si.pay_id=sp.id
        LEFT JOIN contract c on c.contract_no=sc.contract_no
        LEFT JOIN customer cust on cust.id=c.cust_id
        <where>
            spc.salary_comparison_type=2
            <if test="vo.employeeName != null and vo.employeeName !='' ">
                and (e.`name` = #{vo.employeeName} or REPLACE(IFNULL(JSON_EXTRACT(spc.ind_tax_apply_info,'$.staffName'),''),'"','')=#{vo.employeeName})
            </if>
            <if test="vo.certNo != null and vo.certNo !='' ">
                and (e.cert_no = #{vo.certNo} or REPLACE(IFNULL(JSON_EXTRACT(spc.ind_tax_apply_info,'$.certNo'),''),'"','')=#{vo.certNo})
            </if>
            <if test="vo.paymentDate != null and vo.paymentDate !='' ">
                and spc.payment_date = #{vo.paymentDate}
            </if>
            <if test="vo.remark != null and vo.remark !='' ">
                and spc.remark like concat('%',#{vo.remark},'%')
            </if>
            <if test="vo.findDifference != null and vo.findDifference ">
                and spc.remark is not null
            </if>
            <if test="vo.taxMonth != null and vo.taxMonth !='' ">
                and spc.tax_month = #{vo.taxMonth}
            </if>
            <if test="vo.withholdingAgentNo != null and vo.withholdingAgentNo !='' ">
                and spc.withholding_agent_no = #{vo.withholdingAgentNo}
            </if>
            <if test="vo.feedbackStatus != null and vo.feedbackStatus !='' ">
                and spc.feedback_status = #{vo.feedbackStatus}
            </if>
            <if test="vo.mustFeedbackFlag != null and vo.mustFeedbackFlag !='' ">
                and spc.must_feedback_flag = #{vo.mustFeedbackFlag}
            </if>
            <if test="vo.custName != null and vo.custName != '' ">
                and cust.cust_name like concat(#{vo.custName},'%')
            </if>
            <if test="vo.custNo != null and vo.custNo != '' ">
                and cust.cust_no = #{vo.custNo}
            </if>
            <if test="vo.contractNo != null and vo.contractNo != '' ">
                and c.contract_no = #{vo.contractNo}
            </if>
            <if test="vo.contractName != null and vo.contractName != '' ">
                and c.contract_name like concat(#{vo.contractName},'%')
            </if>
            <if test="vo.distCom != null and vo.distCom != '' ">
                and c.dist_com = #{vo.distCom}
            </if>
            <if test="vo.distComMan != null and vo.distComMan != '' ">
                and c.commissioner = #{vo.distComMan}
            </if>
            <if test="vo.salaryCommissioner != null and vo.salaryCommissioner != '' ">
                and c.salary_commissioner = #{vo.salaryCommissioner}
            </if>
            <if test="vo.userOrgPositionDtoList != null and vo.userOrgPositionDtoList.size > 0">
                and ( c.contract_no is null
                or
                <foreach collection="vo.userOrgPositionDtoList" item="userOrgPositionDto" open="(" separator="or"
                         close=")">
                    (
                    (
                    ( c.comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%')) or
                    ( c.salary_comm_pos like concat(#{userOrgPositionDto.posCode,jdbcType=VARCHAR},'%') and c.salary_comm_org like
                    concat(#{userOrgPositionDto.orgCode,jdbcType=VARCHAR},'%'))
                    )
                    <if test="userOrgPositionDto.loginName != null">
                        and (
                        ( c.commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR}) or
                        ( c.salary_commissioner = #{userOrgPositionDto.loginName,jdbcType=VARCHAR})
                        )
                    </if>
                    )
                </foreach>
                )
            </if>
        </where>
    </select>
    <select id="getSalaryPaymentComparisonListByList"
            resultType="com.reon.hr.api.customer.vo.salary.salaryComparison.TaxComparisonFeedbackRecordVo">
        SELECT
        e.`name` employeeName,e.cert_no,
        spc.salary_batch_detail_ids,spc.emp_id,spc.offline_data_ids,spc.salary_comparison_type,spc.difference,spc.remark,
        spc.payment_date,spc.tax_month,spc.emp_excel_add,spc.ind_tax_apply_info,spc.tax_comparison_type,spc.feedback_status,
        spc.must_feedback_flag,
        spc.withholding_agent_no,spc.creator, spc.create_time, spc.updater, spc.update_time, spc.del_flag
        FROM
        salary_payment_comparison spc
        LEFT JOIN employee e on spc.emp_id=e.id
        where
        <foreach collection="list" item="item" separator=" or " open="(" close=")">
            (
            <if test="item.salaryBatchDetailIds != null and item.salaryBatchDetailIds != '' ">
                spc.salary_batch_detail_ids = #{item.salaryBatchDetailIds} and
            </if>
            <if test="item.empId != null and item.empId != '' ">
                spc.emp_id = #{item.empId} and
            </if>
            <if test="item.offlineDataIds != null and item.offlineDataIds != '' ">
                spc.offline_data_ids = #{item.offlineDataIds} and
            </if>
            spc.salary_comparison_type=#{item.salaryComparisonType} and spc.payment_date=#{item.paymentDate} and spc.tax_month=#{item.taxMonth} and
            spc.withholding_agent_no=#{item.withholdingAgentNo} and spc.tax_comparison_type=#{item.taxComparisonType}
            )
        </foreach>
    </select>
</mapper>
